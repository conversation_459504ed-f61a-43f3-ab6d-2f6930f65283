package com.accesscorporate.app.wms.server.dal.repository.goodspackage;

import com.accesscorporate.app.wms.server.dal.entity.goodspackage.MdPackageHDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * Author: tian<PERSON>un
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-02-19 11:07
 * Description: 商品包装主标数据库操作
 */
public interface MdPackageHRepository extends IService<MdPackageHDO> {

    /**
     * 根据货品主键id查询货品包装信息是否已经存在
     */
    MdPackageHDO queryMdPackageHeaderBySkuId(Long skuId);



}
