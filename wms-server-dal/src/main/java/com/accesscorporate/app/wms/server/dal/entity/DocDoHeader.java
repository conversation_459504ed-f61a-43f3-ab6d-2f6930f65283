package com.accesscorporate.app.wms.server.dal.entity;

import com.accesscorporate.app.wms.server.dal.entity.base.WhBaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 发货单头表实体类
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("doc_do_header")
public class DocDoHeader extends WhBaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("do_no")
    private String doNo; // 发货单号

    @TableField("status")
    private String status; // 订单状态；00-初始化，30-分配中，40-分配完成，50-拣货中，60-拣货完成，64-分拣中，65-分拣完成，66-装箱中，67装箱完成，68-交接中，69-交接完成，80-已出库，90-已取消

    @TableField("pcs_status")
    private String pcsStatus; // 零散状态

    @TableField("unit_status")
    private String unitStatus; // 整件状态

    @TableField("ship_time")
    private LocalDateTime shipTime; // 发运时间

    @TableField("do_type")
    private String doType; // 订单类型；1-正常出库，2-调拨，3-RTV，5-RMA

    @TableField("release_status")
    private String releaseStatus; // 订单冻结标识；RL-释放，HD-冻结

    @TableField("priority")
    private Integer priority; // 优先级(ref: PRIORITY)

    @TableField("ref_no1")
    private String refNo1; // 参考编号1

    @TableField("ref_no2")
    private String refNo2; // 参考编号2

    @TableField("post_code")
    private String postCode; // 邮编

    @TableField("spec_flag")
    private Integer specFlag; // 半日达普通/半日达大件/普通/普通大件/团购/一日三送/自提（约定数值分别是：1/2/3/4/5/6/7）

    @TableField("invoice_flag")
    private Integer invoiceFlag; // 有无发票

    @TableField("expected_qty")
    private BigDecimal expectedQty; // 订货数量

    @TableField("ship_qty")
    private BigDecimal shipQty; // 发货数量

    @TableField("expected_qty_each")
    private BigDecimal expectedQtyEach; // 订货数量

    @TableField("ship_qty_each")
    private BigDecimal shipQtyEach; // 发货数量

    @TableField("gross_wt")
    private BigDecimal grossWt; // 毛重

    @TableField("country")
    private String country; // 国家

    @TableField("province")
    private Integer province; // 省

    @TableField("city")
    private Integer city; // 市

    @TableField("county")
    private Integer county; // 县

    @TableField("disctrict")
    private Integer disctrict; // 区

    @TableField("consignee_name")
    private String consigneeName; // 收货方

    @TableField("address")
    private String address; // 收货地址

    @TableField("contact")
    private String contact; // 联系人

    @TableField("telephone")
    private String telephone; // 电话

    @TableField("mobile")
    private String mobile; // 手机

    @TableField("auto_flag")
    private Integer autoFlag; // 自动标识

    @TableField("email")
    private String email; // 邮件

    @TableField("carrier_id")
    private Long carrierId; // 配送公司ID

    @TableField("tracking_no")
    private String trackingNo; // 运单号

    @TableField("invoice_qty")
    private Integer invoiceQty; // 发票数量

    @TableField("payment_method_name")
    private String paymentMethodName; // 支付方式

    @TableField("payment_type")
    private Integer paymentType; // 支付类型

    @TableField("packed_qty")
    private BigDecimal packedQty; // 装箱数量

    @TableField("do_create_time")
    private LocalDateTime doCreateTime; // DO创建时间

    @TableField("expected_arrive_time1")
    private LocalDateTime expectedArriveTime1; // 期望配送时间1

    @TableField("expected_arrive_time2")
    private LocalDateTime expectedArriveTime2; // 期望配送时间2

    @TableField("pk_time_start")
    private LocalDateTime pkTimeStart; // 拣货开始时间

    @TableField("pk_time_end")
    private LocalDateTime pkTimeEnd; // 拣货完成时间

    @TableField("pack_time_start")
    private LocalDateTime packTimeStart; // 包装开始时间

    @TableField("pack_time_end")
    private LocalDateTime packTimeEnd; // 包装完成时间

    @TableField("sort_time")
    private LocalDateTime sortTime; // 分拣结束时间

    @TableField("alloc_time")
    private LocalDateTime allocTime; // 分配时间

    @TableField("cancel_time")
    private LocalDateTime cancelTime; // 取消时间

    @TableField("wave_flag")
    private Integer waveFlag; // 是否已跑波次

    @TableField("wave_id")
    private Long waveId; // 波次ID

    @TableField("sort_grid_no")
    private Integer sortGridNo; // 分拣格ID

    @TableField("hold_who")
    private String holdWho; // 冻结人

    @TableField("hold_time")
    private LocalDateTime holdTime; // 冻结时间

    @TableField("hold_code")
    private String holdCode; // 冻结原因代码

    @TableField("hold_reason")
    private String holdReason; // 冻结原因

    @TableField("userdefine1")
    private String userdefine1; // DO：是否首次购买调拨：调入仓库联系人

    @TableField("userdefine2")
    private String userdefine2; // 是否分期付款

    @TableField("userdefine3")
    private String userdefine3; // 用户自定义3

    @TableField("userdefine4")
    private String userdefine4; // 父DO

    @TableField("notes")
    private String notes; // 备注

    @TableField("edi_1")
    private String edi1; // EDI_1

    @TableField("edi_2")
    private String edi2; // EDI_2

    @TableField("need_cancel")
    private Integer needCancel; // 0：不需要取消1：需要取消

    @TableField("expected_receive_time")
    private String expectedReceiveTime; // 期望收货时间

    @TableField("is_half_day_delivery")
    private Integer isHalfDayDelivery; // 1是半日达，2是一日三送，3是准时达，4是指定日期

    @TableField("print_num")
    private Integer printNum; // 发货单打印份数  主要供企业订单使用

    @TableField("order_amount")
    private BigDecimal orderAmount; // 订单总额（PRODUCT_AMOUNT+ORDER_DELIVERY_FEE)该字段临时迁移用

    @TableField("product_amount")
    private BigDecimal productAmount; // 货品总额

    @TableField("account_payable")
    private BigDecimal accountPayable; // 已收款

    @TableField("order_frost_rebate")
    private BigDecimal orderFrostRebate; // 总返利金额

    @TableField("order_delivery_fee")
    private BigDecimal orderDeliveryFee; // 运费

    @TableField("third_party_bill")
    private Integer thirdPartyBill; // 是否要代收货款 1：是 2：否

    @TableField("receivable")
    private BigDecimal receivable; // 应收款

    @TableField("exchange_flag")
    private Integer exchangeFlag; // DO_TYPE = 0时，0-非换货 1-换货

    @TableField("station_id")
    private Long stationId; // 配送站ID

    @TableField("edi_send_flag1")
    private Integer ediSendFlag1; // 订单同步标记（Backend）

    @TableField("edi_send_flag2")
    private Integer ediSendFlag2; // 订单同步标记（TMS）

    @TableField("edi_send_flag3")
    private Integer ediSendFlag3; // 订单同步标记（BACKEND UNLOCK）

    @TableField("ORIG_ID")
    private String origId; // 原始ID

    @TableField("payment_method")
    private String paymentMethod; // 支付方式（不用了）

    @TableField("supplier_id")
    private Long supplierId; // 供应商ID

    @TableField("is_temp_carton")
    private Integer isTempCarton; // 临时纸箱标识

    @TableField("repl_status")
    private Integer replStatus; // 补货状态(0: 不需要补货, 1:等待补货, 2: 已完成补)

    @TableField("total_gross_wt")
    private BigDecimal totalGrossWt; // 称重毛重

    @TableField("volume")
    private BigDecimal volume; // 体积

    @TableField("cycle_class")
    private Integer cycleClass; // 商品等级，DO明细ABC分类标识； Do明细全A类时为1，全B类时为2，全C为3，全D为4，混合为5

    @TableField("userdefine5")
    private String userdefine5; // 父SO

    @TableField("userdefine6")
    private String userdefine6; // 父SO包含子SO的数量

    @TableField("ordersource")
    private Integer orderSourceOld; // 订单来源（QQ网购：4）

    @TableField("exception_status")
    private Integer exceptionStatus; // 异常状态；0-待通知客服，1-待反馈，2-待回退，3-待返拣，4-待补货，5-已完成

    @TableField("display_price")
    private Integer displayPrice; // 打印时是否显示金额，0：不显示，1：显示

    @TableField("flow_flag")
    private Integer flowFlag; // 流程引擎

    @TableField("service_type")
    private Integer serviceType; // 0是自营，1是1号商城

    @TableField("last_dc_name")
    private String lastDcName; // 销售商家(订单的下单地)

    @TableField("tran_type")
    private Integer tranType; // 0主动调拨，1被动调拨

    @TableField("is_valuable")
    private Integer isValuable; // 是否贵重

    @TableField("first_hold_code")
    private String firstHoldCode; // 初始冻结原因代码

    @TableField("lack_status")
    private String lackStatus; // 订单缺货状态；0-不缺货，1-缺货

    @TableField("sorting_bin_id")
    private Long sortingBinId; // 分拣柜

    @TableField("do_lack_location_code")
    private String doLackLocationCode; // 缺货暂存位号

    @TableField("is_fresh")
    private Integer isFresh; // 生鲜标识

    @TableField("do_finish_time")
    private LocalDateTime doFinishTime; // 首次预计出库时间

    @TableField("in_wine")
    private Integer inWine; // 是否包含酒类随附单 0 不包含、1 包含

    @TableField("wine_no")
    private String wineNo; // 酒类随附单单据号

    @TableField("aisles")
    private String aisles; // DO通道集

    @TableField("plan_ship_time")
    private LocalDateTime planShipTime; // 预计出库时间

    @TableField("plan_ship_time_end")
    private LocalDateTime planShipTimeEnd; // 预计出库时间止

    @TableField("plan_ship_time_std")
    private LocalDateTime planShipTimeStd; // 标准预计出库时间

    @TableField("source_system")
    private String sourceSystem; // 订单来源系统，药网订单：YW

    @TableField("check_flag")
    private Integer checkFlag; // 登记标识，1：合约机订单，2:实名制网卡订单 ，3：选号入网订单

    @TableField("packed_by")
    private String packedBy; // 最近一次核检操作人

    @TableField("sorted_by")
    private String sortedBy; // 最近一次分拣操作人

    @TableField("sort_start_time")
    private LocalDateTime sortStartTime; // 分拣开始时间

    @TableField("departure_time")
    private String departureTime; // 预计发车时间 标签用

    @TableField("print_code")
    private String printCode; // 打印代码

    @TableField("is_auto_wave")
    private Integer isAutoWave; // 是否自动波次标识(0：否；1：是)

    @TableField("is_group")
    private Integer isGroup; // 团购订单标识(0：否；1：是)

    @TableField("delevery_feature")
    private String deleveryFeature; // 配送属性（3：易漏液

    @TableField("discount_amount")
    private BigDecimal discountAmount; // 优惠金额

    @TableField("repl_start_time")
    private LocalDateTime replStartTime; // 补货开始时间

    @TableField("repl_end_time")
    private LocalDateTime replEndTime; // 补货完成时间

    @TableField("station_name")
    private String stationName; // 3PL站点名称

    @TableField("have_cfy")
    private Integer haveCfy; // 是否有处方药(1-有,0-没有)

    @TableField("recheck_type")
    private Integer recheckType; // 核拣方式0/null:普通核拣1:按商品总数核拣

    @TableField("orig_id_hc")
    private Long origIdHc; // 原始ID_HC

    @TableField("shop_id")
    private Long shopId; // 店铺ID

    @TableField("order_source")
    private Integer orderSource; // 订单来源

    @TableField("original_so_code")
    private String originalSoCode; // 原始SO编码

    @TableField("buyer_remark")
    private String buyerRemark; // 买家备注

    @TableField("seller_remark")
    private String sellerRemark; // 卖家备注

    @TableField("platform_remark")
    private String platformRemark; // 平台备注

    @TableField("volume_type")
    private Integer volumeType; // 体积类型

    @TableField("business_customer_id")
    private Long businessCustomerId; // 企业客户ID

    @TableField("city_name")
    private String cityName; // 城市名称

    @TableField("county_name")
    private String countyName; // 区县名称

    @TableField("province_name")
    private String provinceName; // 省份名称

    @TableField("pay_time")
    private LocalDateTime payTime; // 支付时间

    @TableField("failed_number")
    private Integer failedNumber; // 失败次数

    @TableField("LOAD_CONFIM_TIME")
    private LocalDateTime loadConfimTime; // 装载确认时间

    @TableField("last_weigh_time")
    private LocalDateTime lastWeighTime; // 最后称重时间

    @TableField("order_check_state")
    private Integer orderCheckState; // 发货审核状态，默认1

    @TableField("order_sub_type")
    private String orderSubType; // 订单子类型: 0-普通订单 1-O2O订单

    @TableField("ignore_expiry_date")
    private Integer ignoreExpiryDate; // 忽略失效日期

    @TableField("delivery_service_fee")
    private BigDecimal deliveryServiceFee; // 配送服务费

    @TableField("is_direct")
    private Integer isDirect; // 是否直接出库

    @TableField("failed_count")
    private Integer failedCount; // 失败计数

    @TableField("failed_type")
    private Integer failedType; // 失败类型

    @TableField("need_crossstock")
    private Integer needCrossstock; // 是否越库

    @TableField("source_asn_id")
    private Long sourceAsnId; // ERP系统原入库单ID

    @TableField("emergency_flag")
    private Integer emergencyFlag; // 紧急标记

    @TableField("merchant_id")
    private Long merchantId; // 商家ID

    @TableField("print_flag")
    private Integer printFlag; // 打印标记

    @TableField("lack_ship_flag")
    private Integer lackShipFlag; // 是否允许缺发

    @TableField("transport_wendy")
    private Integer transportWendy; // 运输温度

    @TableField("similarity")
    private String similarity; // 相似度明文

    @TableField("similarity_sign")
    private String similaritySign; // 相似度密文

    @TableField("store_code")
    private String storeCode; // 店铺编码

    @TableField("weight_flag")
    private Integer weightFlag; // 称重标识 0未称重 1已称重

    @TableField("channel_code")
    private String channelCode; // 渠道编码

    @TableLogic
    @TableField("is_deleted")
    private Boolean isDeleted; // 该记录是否已逻辑删除，0:否 1:是
}
