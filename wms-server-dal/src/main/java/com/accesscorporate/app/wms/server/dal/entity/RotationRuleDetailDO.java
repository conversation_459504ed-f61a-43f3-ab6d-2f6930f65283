package com.accesscorporate.app.wms.server.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 补货规则明细-Detail
 *
 * <AUTHOR>
 * 2025/2/18  15:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName(value = "rul_rotation_d")
public class RotationRuleDetailDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 周转规则头ID
     */
    private Long rHId;

    /**
     * 行号
     */
    private Long lineno;

    /**
     * 批次属性名称
     */
    @TableField(value = "lotattname")
    private String lotAttName;

    /**
     * 排序方式
     */
    private String sortby;

    /**
     * 创建时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人ID
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 最后修改时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 修改人ID
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 是否删除（0: no, 1: yes）
     */
    private Integer isDeleted;

    /**
     * 版本锁
     */
    private Long version;

    /**
     * 租户ID
     */
    private Long tenantId;

}
