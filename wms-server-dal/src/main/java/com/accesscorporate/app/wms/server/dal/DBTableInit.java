package com.accesscorporate.app.wms.server.dal;

import com.idanchuang.support.mysql.table.helper.TableInitializer;
import com.idanchuang.support.mysql.table.helper.config.DataConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 自动更新表结构
 * <AUTHOR>
 * Created at 2022/8/2 12:54 下午
 */
@Component
public class DBTableInit implements CommandLineRunner {

    @Value("${spring.datasource.url}")
    private String url;
    @Value("${spring.datasource.username}")
    private String username;
    @Value("${spring.datasource.password}")
    private String password;

    @Override
    public void run(String[] args) {
        String[] packages = {
                // 你的实体包路径
                "com.accesscorporate.app.wms.server.dal.entity"
        };
        DataConfig dataConfig = new DataConfig(packages, url, username, password);

        dataConfig.setType("update");
        TableInitializer.init(dataConfig);
    }

}
