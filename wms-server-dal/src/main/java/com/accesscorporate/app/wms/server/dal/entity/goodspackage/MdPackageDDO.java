package com.accesscorporate.app.wms.server.dal.entity.goodspackage;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-02-19 11:24
 * Description: 商品包装明细表
 */
@Data
@TableName("md_package_d")
public class MdPackageDDO {

    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    @TableField("PACKAGE_ID")
    private Long packageId;

    @TableField("PACK_UOM")
    private String packUom;

    @TableField("QTY")
    private BigDecimal qty;

    @TableField("UOM_DESCR")
    private String uomDescr;

    @TableField("LENGTH")
    private BigDecimal length;

    @TableField("WIDTH")
    private BigDecimal width;

    @TableField("HEIGHT")
    private BigDecimal height;

    @TableField("VOLUME")
    private BigDecimal volume;

    @TableField("WEIGHT")
    private BigDecimal weight;

    @TableField("main_flag")
    private String mainFlag;

    @TableField("carton_flg")
    private Integer cartonFlg;

    @TableField(value = "CREATE_TIME")
    private Date createTime;

    @TableField(value = "CREATE_BY")
    private String createBy;

    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    @TableField(value = "UPDATE_BY")
    private String updateBy;

    @TableField("IS_DELETED")
    @TableLogic
    private Boolean isDeleted;

    //@Version
    @TableField("VERSION")
    private Long version;

    @TableField("tenant_id")
    private Long tenantId;


}
