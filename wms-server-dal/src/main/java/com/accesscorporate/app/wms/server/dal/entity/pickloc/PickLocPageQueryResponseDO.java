package com.accesscorporate.app.wms.server.dal.entity.pickloc;

import com.accesscorporate.app.wms.server.common.config.CommonApolloConfig;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-24 10:12
 * Description: 分页查询列表字段响应
 */
@Data
public class PickLocPageQueryResponseDO {

    /**
     * 商品捡货位记录行主键id
     */
    private Long pickLocId;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 补货单位
     */
    private String uom;

    /**
     * 产品名称
     */
    private String productCname;

    /**
     * 产品英文名
     */
    private String productEname;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 商品条码
     */
    private String ean13;

    /**
     * 库位编码
     */
    private String locCode;

    /**
     * 库区
     */
    private String partionCode;

    /**
     * 库位类型
     */
    private String locType;

    /**
     * 下架方式
     */
    private String packageType;

    /**
     * 补货上限
     */
    private BigDecimal upLimit;

    /**
     * 补货下限
     */
    private BigDecimal lowerLimit;

    /**
     * 最小补货量
     */
    private BigDecimal minimumRplQty;

    /**
     * 获取货品英文名称
     */
    public String getProductCnameForI18n(){
        boolean englishWareHouse = CommonApolloConfig.isEnglishWareHouse(warehouseId);
        if (englishWareHouse && StringUtils.isNotBlank(productEname)){
            return productEname;
        }
        return productCname;
    }



}
