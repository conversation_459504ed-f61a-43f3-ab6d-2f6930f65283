package com.accesscorporate.app.wms.server.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

@Data
@TableName("doc_asn_header")
public class ExpAsnHeaderDO {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("original_id")
    private String originalId;
    
    @TableField("asn_no")
    private String asnNo;
    
    @TableField("asn_type")
    private String asnType;
    
    @TableField("asn_status")
    private String asnStatus;
    
    @TableField("cargo_owner_id")
    private Long cargoOwnerId;
    
    @TableField("supplier_id")
    private Long supplierId;
    
    @TableField("from_wh_id")
    private Long fromWhId;
    
    @TableField("expt_arrive_time")
    private Date exptArriveTime;
    
    @TableField("start_received_time")
    private Date startReceivedTime;
    
    @TableField("received_time")
    private Date receivedTime;
    
    @TableField("close_time")
    private Date closeTime;
    
    @TableField("lbx_no")
    private String lbxNo;
    
    @TableField("business_type")
    private Integer businessType;
    
    @TableField("remark")
    private String remark;
    
    @TableField("asn_ref_no1")
    private String asnRefNo1;
    
    @TableField("asn_ref_no2")
    private String asnRefNo2;
    
    @TableField("asn_ref_no3")
    private String asnRefNo3;
    
    @TableField("book_arr_time_from")
    private Date bookArrTimeFrom;
    
    @TableField("book_arr_time_to")
    private Date bookArrTimeTo;
    
    @TableField("priority")
    private Integer priority;
    
    @TableField("release_status")
    private String releaseStatus;
    
    @TableField("expt_qty")
    private BigDecimal exptQty;
    
    @TableField("recv_qty")
    private BigDecimal recvQty;
    
    @TableField("expt_qty_each")
    private BigDecimal exptQtyEach;
    
    @TableField("recv_qty_each")
    private BigDecimal recvQtyEach;
    
    @TableField("userdefine01")
    private String userdefine01;
    
    @TableField("userdefine02")
    private String userdefine02;
    
    @TableField("userdefine03")
    private String userdefine03;
    
    @TableField("userdefine04")
    private String userdefine04;
    
    @TableField("userdefine05")
    private String userdefine05;
    
    @TableField("notes")
    private String notes;
    
    @TableField("is_missing_product")
    private Integer isMissingProduct;
    
    @TableField("hold_who")
    private String holdWho;
    
    @TableField("hold_time")
    private Date holdTime;
    
    @TableField("hold_code")
    private String holdCode;
    
    @TableField("hold_reason")
    private String holdReason;
    
    @TableField("edi_1")
    private String edi1;
    
    @TableField("edi_2")
    private String edi2;
    
    @TableField("need_cancel")
    private Integer needCancel;
    
    @TableField("edi_send_time1")
    private Date ediSendTime1;
    
    @TableField("edi_send_time2")
    private Date ediSendTime2;
    
    @TableField("create_by")
    private String createBy;
    
    @TableField("create_time")
    private Timestamp createTime;
    
    @TableField("update_by")
    private String updateBy;
    
    @TableField("update_time")
    private Timestamp updateTime;
    
    @TableField("is_deleted")
    private Integer isDeleted;
    
    @TableField("version")
    private Long version;
    
    @TableField("need_crossstock")
    private Integer needCrossstock;
    
    @TableField("payment_name")
    private String paymentName;
    
    @TableField("receive_type")
    private String receiveType;
    
    @TableField("valid_period_standard_id")
    private Long validPeriodStandardId;
    
    @TableField("is_direct_purchase")
    private Integer isDirectPurchase;
    
    @TableField("warehouse_id")
    private Long warehouseId;
    
    @TableField("is_from_cross")
    private Integer isFromCross;
    
    @TableField("expired_date")
    private Date expiredDate;
    
    @TableField("is_diff_places_trans")
    private Integer isDiffPlacesTrans;
    
    @TableField("source_system")
    private String sourceSystem;
    
    @TableField("GRF_ID")
    private String grfId;
    
    @TableField("tran_type")
    private Integer tranType;
    
    @TableField("special_flag")
    private Integer specialFlag;
    
    @TableField("business_customer_id")
    private String businessCustomerId;
    
    @TableField("is_from_slc")
    private Integer isFromSlc;
    
    @TableField("is_special")
    private Integer isSpecial;
    
    @TableField("entry_registration_code")
    private String entryRegistrationCode;
    
    @TableField("return_reason")
    private String returnReason;
    
    @TableField("return_mode")
    private String returnMode;
    
    @TableField("is_thunder_buy")
    private Integer isThunderBuy;
    
    @TableField("in_warehouse_no")
    private String inWarehouseNo;
    
    @TableField("recheck_time")
    private Timestamp recheckTime;
    
    @TableField("receive_by")
    private String receiveBy;
    
    @TableField("recheck_by")
    private String recheckBy;
    
    @TableField("verify_by")
    private String verifyBy;
    
    @TableField("print_flag")
    private Integer printFlag;
    
    @TableField("is_direct")
    private Integer isDirect;
    
    @TableField("purchaser")
    private String purchaser;
    
    @TableField("confirmed_flag")
    private Integer confirmedFlag;
    
    @TableField("is_gift")
    private Integer isGift;
    
    @TableField("auto_flag")
    private Integer autoFlag;
    
    @TableField("crossstock_type")
    private Integer crossstockType;
    
    @TableField("failed_type")
    private Integer failedType;
    
    @TableField("failed_number")
    private Integer failedNumber;
    
    @TableField("asn_sub_type")
    private String asnSubType;
    
    @TableField("sign_time")
    private String signTime;
}