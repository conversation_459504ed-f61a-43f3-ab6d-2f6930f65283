package com.accesscorporate.app.wms.server.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Blob;
import java.util.Date;

@Data
@TableName("s_exp_srv_log")
public class SExpSrvLogDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("exp_name")
    private String expName;

    @TableField("exp_args")
    private String expArgs;

    @TableField("exp_data")
    private Blob expData;

    @TableField("ref_id")
    private Long refId;

    @TableField("ref_no")
    private String refNo;

    @TableField("status")
    private Integer status;

    @TableField("result_msg")
    private String resultMsg;

    @TableField("result_cause")
    private String resultCause;

    @TableField("flag")
    private Integer flag;

    @TableField("create_time")
    private Date createTime;

    @TableField("invoke_time")
    private Date invokeTime;

    @TableField("invoke_count")
    private Integer invokeCount;

    @TableField("fail_count")
    private Integer failCount;

    @TableField("req_base")
    private String reqBase;

    @TableField("req_path")
    private String reqPath;

    @TableField("req_cost")
    private Long reqCost;

    @TableField("invoke_cost")
    private Long invokeCost;

    @TableField("invoke_his")
    private String invokeHis;

    @TableField("total_req_cost")
    private Long totalReqCost;

    @TableField("total_invoke_cost")
    private Long totalInvokeCost;

    @TableField("sys_id")
    private Integer sysId;

    @TableField("srv_msg_id")
    private Long srvMsgId;

    @TableField("is_error")
    private Integer isError;
}
