package com.accesscorporate.app.wms.server.dal.entity.rule;

import com.accesscorporate.app.wms.server.dal.entity.base.TenantBaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rul_allocation")
public class RulAllocationDO extends TenantBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则编码
     */
    private String allocationCode;

    /**
     * 规则描述
     */
    private String allocationDescr;

    /**
     * 是否激活0 未激活 1 激活
     */
    private String activeFlag;

    /**
     * 是否删除
     */
    private Integer isDeleted;



}
