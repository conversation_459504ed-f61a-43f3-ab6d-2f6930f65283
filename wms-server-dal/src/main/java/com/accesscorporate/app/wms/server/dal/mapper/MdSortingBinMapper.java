package com.accesscorporate.app.wms.server.dal.mapper;

import com.accesscorporate.app.wms.server.dal.dto.MdSortingBinQuery;
import com.accesscorporate.app.wms.server.dal.entity.MdSortingBinDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * MdSortingBinDO 的 Mapper 接口
 * 分拣柜管理
 *
 * <AUTHOR>
 */
@Repository
public interface MdSortingBinMapper {

    /**
     * 根据主键ID查询分拣柜信息
     *
     * @param id 分拣柜主键ID
     * @return 分拣柜实体对象
     */
    MdSortingBinDO selectById(Long id);

    /**
     * 根据分拣柜编号查询分拣柜信息
     *
     * @param sortingBinNo 分拣柜编号
     * @return 分拣柜实体对象
     */
    MdSortingBinDO selectBySortingBinNo(String sortingBinNo);

    /**
     * 根据分拣柜编号和仓库ID查询分拣柜列表
     *
     * @param sortingBinNo 分拣柜编号
     * @param warehouseId 仓库ID
     * @return 分拣柜实体对象列表
     */
    List<MdSortingBinDO> selectSortingBinByNo(String sortingBinNo, Long warehouseId);

    /**
     * 查询指定类型以外的空闲分拣柜ID
     *
     * @param sortBinType 要排除的分拣柜类型
     * @param warehouseId 仓库ID
     * @return 空闲分拣柜ID
     */
    Long selectIdleSortingBin(String sortBinType, Long warehouseId);

    /**
     * 查询指定类型的空闲分拣柜ID
     *
     * @param sortBinType 分拣柜类型
     * @param warehouseId 仓库ID
     * @return 空闲分拣柜ID
     */
    Long selectIdleSortingBinByType(String sortBinType, Long warehouseId);

    /**
     * 根据分拣柜编号查询分拣柜ID(排除指定编号)
     *
     * @param sortingBinNo 要排除的分拣柜编号
     * @param warehouseId 仓库ID
     * @return 分拣柜ID
     */
    Long selectSortingBinIdByNo(String sortingBinNo, Long warehouseId);

    /**
     * 批量更新分拣柜可用状态
     *
     * @param ids 分拣柜ID列表
     * @return 更新记录数
     */
    int updateAvailableStatusByIds(@Param("ids") List<Long> ids);

    /**
     * 查询所有分拣柜信息
     *
     * @return 分拣柜实体对象列表
     */
    List<MdSortingBinDO> selectAll();

    /**
     * 根据条件分页查询分拣柜信息
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    Page<MdSortingBinDO> selectByCondition(@Param("page") Page<MdSortingBinDO> page,
                                           @Param("query") MdSortingBinQuery query);

    /**
     * 新增分拣柜信息
     *
     * @param mdSortingBinDO 要插入的对象
     * @return 插入成功的记录数
     */
    int insert(MdSortingBinDO mdSortingBinDO);

    /**
     * 根据主键ID更新MdSortingBinDO对象
     *
     * @param mdSortingBinDO 要更新的对象
     * @return 更新成功的记录数
     */
    int updateById(MdSortingBinDO mdSortingBinDO);

    /**
     * 根据主键ID删除MdSortingBinDO对象
     *
     * @param id 主键ID
     * @return 删除成功的记录数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 根据sortingZoneNbr、warehouseId和id查询MdSortingBinDO对象
     * @param sortingZoneNbr 分拣区编号
     * @param warehouseId 仓库ID
     * @param id 主键ID
     * @return MdSortingBinDO对象
     */
    MdSortingBinDO selectBySortingZoneNbrAndWarehouseIdAndId(
            @Param("sortingZoneNbr") String sortingZoneNbr,
            @Param("warehouseId") Long warehouseId,
            @Param("id") Long id);

}
