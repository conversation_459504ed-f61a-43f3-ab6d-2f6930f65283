package com.accesscorporate.app.wms.server.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.idanchuang.support.mysql.table.helper.comment.Entity;

import java.io.Serializable;
import java.time.LocalDateTime;

@TableName("md_region")
@Entity(tableName = "md_region", check = true, comment = "仓库区域表")

public class Region implements Serializable {

    @TableId
    private Integer id;

    /** 仓库ID */
    private Long warehouseId;

    /** 区域编码 */
    private String regionCode;

    /** 区域名称 */
    private String regionName;

    /** 创立日期 */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /** 创建人ID */
    private String createBy;

    /** 最后修改日期 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /** 修改人ID */
    private String updateBy;

    /** 是否删除 0:no 1:yes */
    private Integer isDeleted;

    @Version
    /** 版本 */
    private Integer version;

    /** 接力拣货区域顺序 */
    private Short pickOrder;

    /** 管道类型标记 */
    private Byte pipeline;

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Short getPickOrder() {
        return pickOrder;
    }

    public void setPickOrder(Short pickOrder) {
        this.pickOrder = pickOrder;
    }

    public Byte getPipeline() {
        return pipeline;
    }

    public void setPipeline(Byte pipeline) {
        this.pipeline = pipeline;
    }
}