package com.accesscorporate.app.wms.server.dal.entity.rule;

import com.accesscorporate.app.wms.server.dal.entity.base.TenantBaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rul_allocation_d")
public class RulAllocationDDO extends TenantBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则头id
     */
    private Long rHId;

    /**
     * 序号
     */
    private Long lineno;

    private String lotattname;

    private String sortby;

    private Integer isDeleted;

}
