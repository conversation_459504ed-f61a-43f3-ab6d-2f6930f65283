package com.accesscorporate.app.wms.server.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("md_partition")
public class Partition {
    
    @TableId(type = IdType.AUTO)
    private Long id;

    private String description;
    private String partitionType;
    @TableField("partion_code")
    private String partitionCode;
    private Long paToLoc; // 上架位置ID
    private Long pickToLoc; // 拣货位置ID
    private Long regionId; // 区域ID
    private Integer specialFlag; // 特殊标识
    @TableField("p_partition_id")
    private Long physicalPartitionId; // 物理分区ID
    private Long warehouseId; // 仓库ID

    @TableField("create_by")
    private String createdBy; // 创建者
    @TableField("create_time")
    private Timestamp createdAt; // 创建时间
    @TableField("update_by")
    private String updatedBy; // 更新者
    @TableField("update_time")
    private Timestamp updatedAt; // 更新时间
    @Version
    private Integer version; // 版本号
    @TableLogic // 表示逻辑删除
    private Integer isDeleted; // 删除标识
}