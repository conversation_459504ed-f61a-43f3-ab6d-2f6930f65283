package com.accesscorporate.app.wms.server.dal.repository;

import com.accesscorporate.app.wms.server.dal.entity.pickloc.MdPickLocDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-20 10:31
 * Description: 捡货位
 */
public interface MdPickLocRepository extends IService<MdPickLocDO> {

    /**
     * 通过skuId查询捡货位信息
     */
    MdPickLocDO queryPickLocBySkuIdAndWarehouseId(Long skuId,Long locId,Long warehouseId);

    /**
     * 批量保存
     */
    Integer batchInsertMdPickLoc(List<MdPickLocDO> list);

}
