package com.accesscorporate.app.wms.server.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 货主-DO
 *
 * <AUTHOR>
 * 2025/2/8  16:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName(value = "md_merchant")
public class CargoOwnerDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 原始ID
     */
    private String originalId;

    /**
     * 商家编码
     */
    private String merchantCode;

    /**
     * 商家中文名称
     */
    private String descrC;

    /**
     * 商家英文名称
     */
    private String descrE;

    /**
     * 联系人email
     */
    private String contactEmail;

    /**
     * 联系人电话
     */
    private String contactTel;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 状态
     */
    private String status;

    /**
     * 备注
     */
    private String note;

    /**
     * 联系人传真
     */
    private String contactFax;

    /**
     * 联系人手机
     */
    private String contactMo;

    /**
     * 邮政编码
     */
    private String postcode;

    /**
     * 地址
     */
    private String address;

    /**
     * 国家ID
     */
    private Long countryId;

    /**
     * 省份ID
     */
    private Long provinceId;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 创立日期
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private String createBy;

    /**
     * 最后修改日期
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 修改人ID
     */
    private String updateBy;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 版本锁
     */
    private Integer version;

    /**
     * 纳税人名称
     */
    private String taxPayerName;

    /**
     * 发票请求接口地址
     */
    private String invoiceInterfaceUrl;

    /**
     * 商家平台编码
     */
    private String invoicePlatformCode;

    /**
     * 纳税人识别码
     */
    private String taxPayerCode;

    /**
     * 租户ID
     */
    private Long tenantId;


}
