package com.accesscorporate.app.wms.server.dal.entity.rule;

import com.accesscorporate.app.wms.server.dal.entity.base.TenantBaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rul_allocation_h")
public class RulAllocationHDO extends TenantBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 序号
     */
    private Long lineno;

    /**
     * 分配规则id
     */
    private Long allocationId;

    /**
     * 规则描述
     */
    private String allocationDescr;

    /**
     * 订单类型
     */
    private String ordertype;

    /**
     * 是否优先分配RETURN库区库存，0：不优先分配，1：优先分配
     */
    private Integer partitionCtrl;

    /**
     * 是否允许分配存储库区库存，0：不允许，1：允许
     */
    private Integer storageCtrl;

    private Integer isDeleted;

    /**
     * 调拨类型，0：主动调拨，1：被动调拨
     */
    private Integer allotType;

    /**
     * 库存策略 1清空库位优先 2最少拣货点，满足订单需求数优先
     */
    private Integer stockStrategy;

    /**
     * 是否支持存储位
     */
    private Integer supportRs;

    /**
     * 是否效期控制
     */
    private Integer expireControl;

}
