package com.accesscorporate.app.wms.server.dal.config;

import com.idanchuang.component.sequence.id.service.GeneralSeqIdService;
import org.springframework.boot.CommandLineRunner;

/**
 * 用来初始化ID序列服务中的id值, 否则nextId获取到的id是从0开始的
 *
 * 注意: 在run方法中添加你要初始化id的表, 并设置当前id值
 *
 * 关于这个类的使用有任何问题可以联系飞书 基础服务台 进行咨询
 *
 * <AUTHOR>
 */
public class HistoryTableIdInit implements CommandLineRunner {

    private final String appId;
    private final GeneralSeqIdService generalSeqIdService;

    public HistoryTableIdInit(String appId, GeneralSeqIdService generalSeqIdService) {
        this.appId = appId;
        this.generalSeqIdService = generalSeqIdService;
    }

    /**
     * 此方法会在应用启动完成后, 执行1次
     * @param args ..
     * @throws Exception e
     */
    @Override
    public void run(String... args) throws Exception {
        // 设置 指定表名 当前的 id , 如下:
//        this.generalSeqIdService.setMinId(appId, "table_name1", 3000);
//        this.generalSeqIdService.setMinId(appId, "table_name2", 5000);
    }

}
