package com.accesscorporate.app.wms.server.dal.entity.rule;

import com.accesscorporate.app.wms.server.dal.entity.base.WhBaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rul_putaway_detail")
public class RulPutawayDetailDO extends WhBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 上架规则头
     */
    private Long rulHeaderId;

    /**
     * 任务适用单据类型，逗号分隔
     */
    private String docTypes;

    /**
     * P,C库位划分原则  0：按体积 1：按数量  2：按TIHI
     */
    private Integer locTypeOrigin;

    /**
     * 任务适用商品类别，逗号分隔
     */
    private String skuCatgIds;

    /**
     * 任务适用供应商id，逗号分隔
     */
    private String supplierIds;

    /**
     * 任务适用商家id，逗号分隔
     */
    private String merchantIds;

    /**
     * 查找的库位类型，逗号分隔
     */
    private String locTypes;

    /**
     * 是否只查询空库位
     */
    private Integer emptyLocOnly;

    /**
     * 1:同类商品 2：同批次
     */
    private String concentrateType;

    /**
     * 上架规则code，sku默认库位01 默认库区02  目标库位03  目标库区04   品类默认的库区05   最近操作的库位06  最近操作的库位附近的货位07
     */
    private String directionRule;

    /**
     * 目标库位，逗号分隔
     */
    private String targetLocs;

    /**
     * 目标库区，逗号分隔
     */
    private String targetPartitionts;

    /**
     * 最近操作类型，逗号分隔 PA 上架 MV 移库 PK 拣货 ST 分拣 RK 返拣 RP 补货 CC 盘点 SO 发货
     */
    private String latestOperTypes;

    /**
     * 是否启用
     */
    private Integer activeFlag;

    /**
     * 行号
     */
    private Integer lineNo;

    /**
     * 0:no1:yes
     */
    private Integer isDeleted;

    private Long partitionRuleId;


}
