package com.accesscorporate.app.wms.server.dal.entity.pickloc;

import com.accesscorporate.app.wms.server.dal.entity.base.WhBaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-19 17:52
 * Description: 产品拣货位信息实体
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("md_pick_loc")
public class MdPickLocDO extends WhBaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * Sku ID
     */
    private Long skuId;
    /**
     * 拣货位ID
     */
    private Long locId;
    /**
     * 补货上限
     */
    private BigDecimal uplimit;
    /**
     * 补货下限
     */
    private BigDecimal lowerlimit;
    /**
     * 最小补货数量
     */
    private BigDecimal minimumRplQty;
    /**
     * 补货单位
     */
    private String uom;

    /**
     *
     */
    private Long rpUom;

    @TableLogic
    private Boolean isDeleted;




}
