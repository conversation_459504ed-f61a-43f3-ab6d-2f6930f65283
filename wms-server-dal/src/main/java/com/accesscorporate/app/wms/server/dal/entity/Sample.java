package com.accesscorporate.app.wms.server.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.idanchuang.support.mysql.table.helper.comment.*;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

/**
 * 案例表
 *
 * @ Entity、Id、Field 注解可用来自动更新开发环境表结构（使用 DBTableInit 工具类）
 *
 * <AUTHOR>
 * @date 2021/10/08 15:58
 **/
@Entity(tableName = "sample", check = true, comment = "案例表")
@TableName("sample")
@Schema(description = "案例")
public class Sample {

    @Id(autoIncrease = true)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Field(field = "title", type = FieldType.VARCHAR, nullable = false, comment = "标题", index = @Index(unique = true, name = "uk_title"))
    @Schema(description = "标题")
    private String title;

    @Field(nullable = false, comment = "创建时间")
    @TableField("created_at")
    @Schema(description = "创建时间")
    private Date createdAt;

    @Field(nullable = false, comment = "更新时间")
    @TableField("updated_at")
    @Schema(description = "更新时间")
    private Date updatedAt;

    @Field(nullable = false, defaultValue = "0", comment = "是否删除 0:否 1:是")
    @Schema(description = "是否删除 0:否 1:是")
    private Integer deleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }
}
