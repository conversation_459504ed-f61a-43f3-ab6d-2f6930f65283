package com.accesscorporate.app.wms.server.dal.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> liudongliang
 * @project : wms-server
 * @company : 浙江单创品牌管理有限公司
 * @desc :
 * Copyright @ 2024 danchuang Technology Co.Ltd. – Confidential and Proprietary
 */
@Data
public class SkuCombiListQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 6328027147546517902L;

    /**
     * 组合条码集合
     */
    private List<String> combiBarcodeList;

    /**
     * 商品条码集合
     */
    private List<String> barcodeList;

    /**
     * 库位编码集合
     */
    private List<String> locCodeList;

    /**
     * 创建时间-起始(时间戳)
     */
    private Date createTimeFm;

    /**
     * 创建时间-截止(时间戳)
     */
    private Date createTimeTo;

    /**
     * 仓库ID
     */
    private Long warehouseId;
}
