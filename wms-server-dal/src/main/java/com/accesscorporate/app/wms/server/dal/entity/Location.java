package com.accesscorporate.app.wms.server.dal.entity;

import com.accesscorporate.app.wms.server.dal.entity.base.WhBaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("md_location")
public class Location extends WhBaseEntity {
    
    @TableId(type = IdType.AUTO)
    private Long id; // 主键，库位唯一标识

    private String locCode; // 库位编码
    private Long partitionId; // 关联的分区 ID
    private String aisle; // 行【通道】
    private String bay; // 贝【货架】
    private String lev; // 层【货架层】
    private String position; // 位置
    private String locType; // 库位类型
    private String circleClass; // 周转需求类型
    private String packageType; // 下架方式
    private Integer canMixProduct; // 是否允许混放商品
    private Integer notAutoRecmd; // 是否在上架计算临近货位时包括在内
    private Integer maxLpnQyt; // 最大 LPN 数量
    private BigDecimal volume; // 体积
    private BigDecimal weight; // 重量
    private BigDecimal length; // 长度
    private BigDecimal width; // 宽度
    private BigDecimal height; // 高度
    private BigDecimal axisX; // X 轴坐标
    private BigDecimal axisY; // Y 轴坐标
    private BigDecimal axisZ; // Z 轴坐标
    private BigDecimal fraction; // 容积率
    private Integer ignoreLpn; // 忽略 LPN
    private BigDecimal maxProdQty; // 最大产品数量
    private BigDecimal maxMixQty; // 最大混放数量
    private String putawaySeq; // 上架顺序
    private String pickSeq; // 拣货顺序
    private String countSeq; // 盘点顺序
    private Long merchantId; // 货主
    private Integer lockStatus; // 库位锁定状态
    private String chute; // 滑道号
    private Integer volumeType; // 体积类型
    private Integer lackFlag; // 疑似缺货标识
    @TableField(exist = false)
    private Long distance; // 库位距离
    private Integer useStatus; // 库位使用状态
    private Integer isEmpty; // 是否为空
    private Integer canMixBatch; // 批次混放
    // 关联的分区
    @TableField(exist = false)
    private Partition partition;

    @TableLogic
    private Boolean isDeleted;


} 