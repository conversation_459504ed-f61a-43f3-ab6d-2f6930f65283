package com.accesscorporate.app.wms.server.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("wms_address")
public class AddressDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("address_code")
    private String addressCode;

    @TableField("name")
    private String name;

    @TableField("parent_code")
    private String parentCode;

    @TableField("type")
    private Integer type;

    @TableField("sort_name")
    private String sortName;

    /**
     * 字段: is_oversea, 0国内地址 1国外地址
     */
    @TableField("is_oversea")
    private Integer isOversea;

    @TableField("transport_wendy")
    private Integer transportWendy;

    @TableField("create_time")
    private Date createTime;

    @TableField("create_by")
    private String createBy;

    @TableField("update_time")
    private Date updateTime;

    @TableField("update_by")
    private String updateBy;

    @TableField("version")
    private Long version;
}
