package com.accesscorporate.app.wms.server.dal.entity;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * md_sku_combi_header
 *
 * <AUTHOR>
@Data
public class MdSkuCombiHeaderDO implements Serializable {

    @Serial
    private static final long serialVersionUID = -3131035578457105605L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 组合条码
     */
    private String combiBarcode;

    /**
     * 组合特征 所有条码*数量*等级签名 用于确认组合唯一性
     */
    private String sign;

    /**
     * 品项数
     */
    private Integer skuCount;

    /**
     * 件数
     */
    private Integer skuTotal;

    /**
     * 库位编码
     */
    private String locCode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 是否删除 0:no 1:yes
     */
    private Integer isDeleted;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 最后修改日期
     */
    private Date updateTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 版本锁
     */
    private Integer version;

}