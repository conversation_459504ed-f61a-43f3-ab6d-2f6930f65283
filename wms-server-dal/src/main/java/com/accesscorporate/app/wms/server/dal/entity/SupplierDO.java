package com.accesscorporate.app.wms.server.dal.entity;

import com.accesscorporate.app.wms.server.common.enums.SupplierCanInvoiceEnum;
import com.accesscorporate.app.wms.server.common.enums.SupplierNoticeEnum;
import com.accesscorporate.app.wms.server.common.enums.SupplierSourceEnum;
import com.accesscorporate.app.wms.server.common.enums.SupplierStatusEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 供应商 DO
 *
 * <AUTHOR>
 * 2025/2/8  16:22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName(value = "md_supplier")
public class SupplierDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 原始ID
     */
    @TableField(value = "original_id")
    private String originalId;

    /**
     * 供应商编码
     */
    @TableField(value = "SUPPLIER_CODE")
    private String supplierCode;

    /**
     * 商家下单时是否短信通知 0：不通知；1：通知
     *
     * @see SupplierNoticeEnum
     */
    @TableField(value = "IS_SMS_NOTICE_FOR_PO")
    private Integer isSmsNoticeForPo;

    /**
     * 商家下单时是否邮件通知 0：不通知；1：通知
     *
     * @see SupplierNoticeEnum
     */
    @TableField(value = "IS_EAMIL_NOTICE_FOR_PO")
    private Integer isEmailNoticeForPo;

    /**
     * 条码打印费
     */
    @TableField(value = "BARCODE_PRINT_FEE")
    private BigDecimal barcodePrintFee;

    /**
     * 订货周期
     */
    @TableField(value = "PO_DAYS")
    private Integer poDays;

    /**
     * 是否提供发票
     * 0:否 1:是
     *
     * @see SupplierCanInvoiceEnum
     */
    @TableField(value = "CAN_INVOICE")
    private Integer canInvoice;

    /**
     * 供应商来源（0：后台添加；1：供应商自己在网站注册）
     *
     * @see SupplierSourceEnum
     */
    @TableField(value = "SUPPLIER_SOURCE")
    private Integer supplierSource;

    /**
     * 供应商类型
     */
    @TableField(value = "SUPPLIER_TYPE")
    private Integer supplierType;

    /**
     * 最小订货金额
     */
    @TableField(value = "MIN_ORDER_AMOUNT")
    private BigDecimal minOrderAmount;

    /**
     * 付款日
     */
    @TableField(value = "PAYMENT_DAY")
    private Integer paymentDay;

    /**
     * 税号
     */
    @TableField(value = "TAX_NUMBER")
    private String taxNumber;

    /**
     * 采购人员
     */
    @TableField(value = "SUPPLIER_PUSERCHASER_ID")
    private Long supplierPurchaserId;

    /**
     * 采购员
     */
    @TableField(value = "PUSERCHASER_NAME")
    private String purchaserName;

    /**
     * 采购员电话
     */
    @TableField(value = "PUSERCHASER_TEL")
    private String purchaserTel;

    /**
     * 0:申请中 1：正常
     *
     * @see SupplierStatusEnum
     */
    @TableField(value = "SUPPLIER_STATUS")
    private Integer supplierStatus;

    /**
     * 联系人
     */
    @TableField(value = "SUPPLIER_CONTACT_MAN_NAME")
    private String supplierContactManName;

    /**
     * 联系人手机
     */
    @TableField(value = "SUPPLIER_CONTACT_MOBILE")
    private String supplierContactMobile;

    /**
     * 联系人email
     */
    @TableField(value = "SUPPLIER_CONTACT_EMAIL")
    private String supplierContactEmail;

    /**
     * 送货方式
     */
    @TableField(value = "SUPPLIER_DELIVERY_METHOD")
    private Integer supplierDeliveryMethod;

    /**
     * 供应商公司名
     */
    @TableField(value = "SUPPLIER_COMPANY_NAME")
    private String supplierCompanyName;

    /**
     * 供应商区县id
     */
    @TableField(value = "SUPPLIER_COUNTY")
    private Long supplierCounty;

    /**
     * 供应商市id
     */
    @TableField(value = "SUPPLIER_CITY")
    private Long supplierCity;

    /**
     * 供应商省id
     */
    @TableField(value = "SUPPLIER_PROVINCE")
    private Long supplierProvince;

    /**
     * 供应商国家id
     */
    @TableField(value = "SUPPLIER_COUNTRY")
    private Long supplierCountry;

    /**
     * 企业在药监系统编码
     */
    @TableField(value = "corp_id")
    private Long corpId;

    /**
     * 创立日期
     */
    @TableField(value = "CREATE_TIME")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * ref_id
     */
    @TableField(value = "ref_id")
    private Long refId;

    /**
     * specimen_url
     */
    @TableField(value = "specimen_url")
    private String specimenUrl;

    /**
     * 供应商仓库地址
     */
    @TableField(value = "warehouse_address")
    private String warehouseAddress;

    /**
     * 修改人ID
     */
    @TableField(value = "CREATE_BY")
    private String createBy;

    /**
     * 最后修改日期
     */
    @TableField(value = "UPDATE_TIME")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 修改人ID
     */
    @TableField(value = "UPDATE_BY")
    private String updateBy;

    /**
     * 是否删除
     */
    @TableField(value = "IS_DELETED")
    private Integer isDeleted;

    /**
     * 版本
     */
    @TableField(value = "VERSION")
    private Long version;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 拼音名称
     */
    @TableField(value = "pinyin_name")
    private String pinyinName;
}
