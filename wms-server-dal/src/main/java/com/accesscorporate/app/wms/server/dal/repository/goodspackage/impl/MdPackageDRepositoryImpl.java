package com.accesscorporate.app.wms.server.dal.repository.goodspackage.impl;

import com.accesscorporate.app.wms.server.dal.entity.goodspackage.MdPackageDDO;
import com.accesscorporate.app.wms.server.dal.mapper.goodspackage.MdPackageDMapper;
import com.accesscorporate.app.wms.server.dal.repository.goodspackage.MdPackageDRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-02-19 11:29
 * Description: 商品包装明细表数据库操作
 */
@Repository
public class MdPackageDRepositoryImpl extends
        ServiceImpl<MdPackageDMapper, MdPackageDDO> implements MdPackageDRepository {


    @Override
    public List<MdPackageDDO> queryPackageDetailListByPackageId(Long packageId) {
        return lambdaQuery()
                .eq(MdPackageDDO::getPackageId,packageId)
                .eq(MdPackageDDO::getIsDeleted, Boolean.FALSE)
                .list();
    }


}
