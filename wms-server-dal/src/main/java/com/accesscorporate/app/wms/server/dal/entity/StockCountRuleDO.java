package com.accesscorporate.app.wms.server.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 库存盘点规则 DO
 *
 * <AUTHOR>
 * 2025/2/14 16:22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName(value = "rul_count")
public class StockCountRuleDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 编码
     */
    @TableField(value = "`code`")
    private String code;

    /**
     * 名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 优先级
     */
    @TableField(value = "`priority`")
    private Integer priority;

    /**
     * 生产日期
     */
    @TableField(value = "`lotatt01`")
    private Boolean lotatt01;

    /**
     * 失效日期
     */
    @TableField(value = "`lotatt02`")
    private Boolean lotatt02;

    /**
     * 入库日期
     */
    @TableField(value = "`lotatt03`")
    private Boolean lotatt03;

    /**
     * 供应商
     */
    @TableField(value = "`lotatt04`")
    private Boolean lotatt04;

    /**
     * 批号
     */
    @TableField(value = "`lotatt05`")
    private Boolean lotatt05;

    /**
     * 商家
     */
    @TableField(value = "`lotatt06`")
    private Boolean lotatt06;

    /**
     * 包装
     */
    @TableField(value = "`lotatt07`")
    private Boolean lotatt07;

    /**
     * 生产厂家
     */
    @TableField(value = "`lotatt08`")
    private Boolean lotatt08;

    /**
     * 进价
     */
    @TableField(value = "`lotatt09`")
    private Boolean lotatt09;

    /**
     * 采购单号
     */
    @TableField(value = "`lotatt10`")
    private Boolean lotatt10;

    /**
     * 批次11
     */
    @TableField(value = "`lotatt11`")
    private Boolean lotatt11;

    /**
     * 坏品备注
     */
    @TableField(value = "`lotatt12`")
    private Boolean lotatt12;

    /**
     * 版本号
     */
    @TableField(value = "`version`")
    private Integer version;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    @TableField(value = "`is_deleted`")
    private Integer isDeleted;

    /**
     * 租户ID
     */
    @TableField(value = "`tenant_id`")
    private Long tenantId;

    /**
     * 仓库ID
     */
    @TableField(value = "`warehouse_id`")
    private Long warehouseId;

    /**
     * 创建者
     */
    @TableField(value = "`CREATE_BY`")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "`CREATE_TIME`")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "`UPDATE_BY`")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "`UPDATE_TIME`")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

}