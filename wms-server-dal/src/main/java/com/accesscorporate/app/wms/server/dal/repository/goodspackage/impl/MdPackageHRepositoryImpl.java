package com.accesscorporate.app.wms.server.dal.repository.goodspackage.impl;

import com.accesscorporate.app.wms.server.dal.entity.goodspackage.MdPackageHDO;
import com.accesscorporate.app.wms.server.dal.mapper.goodspackage.MdPackageHMapper;
import com.accesscorporate.app.wms.server.dal.repository.goodspackage.MdPackageHRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-02-19 11:08
 * Description: 商品包装主标数据库操作
 */
@Repository
public class MdPackageHRepositoryImpl extends
        ServiceImpl<MdPackageHMapper, MdPackageHDO> implements MdPackageHRepository {


    @Override
    public MdPackageHDO queryMdPackageHeaderBySkuId(Long skuId) {
        return lambdaQuery()
                .eq(MdPackageHDO::getSkuId,skuId)
                .eq(MdPackageHDO::getIsDeleted,Boolean.FALSE)
                .one();
    }

}
