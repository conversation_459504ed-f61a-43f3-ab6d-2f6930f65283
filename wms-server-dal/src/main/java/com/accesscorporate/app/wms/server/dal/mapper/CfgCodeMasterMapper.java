package com.accesscorporate.app.wms.server.dal.mapper;

import com.accesscorporate.app.wms.server.dal.entity.CfgCodeMasterDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cfg_code_master】的数据库操作Mapper
* @createDate 2025-02-05 13:26:06
* @Entity com.accesscorporate.app.wms.server.dal.entity.CfgCodeMasterDO
*/
public interface CfgCodeMasterMapper extends BaseMapper<CfgCodeMasterDO> {

    List<String> findCodeCategoryGroupNames();

    List<CfgCodeMasterDO> getCodeMasters(@Param("groupName") String groupName);
}




