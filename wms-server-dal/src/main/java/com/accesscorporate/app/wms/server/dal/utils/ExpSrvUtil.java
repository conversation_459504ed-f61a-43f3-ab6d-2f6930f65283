package com.accesscorporate.app.wms.server.dal.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import java.io.IOException;

@lombok.extern.slf4j.Slf4j
public class ExpSrvUtil {


    public final static String SPLIT_STR = "&,";

    public final static String SPLIT_STR_ENCODE = "/,";

    public final static String EMPTY_STR = "";

    public static final String INTERFACE_INVOKE_INTERVAL = "interface.invoke.interval";

    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
    }

    public static String encodeAsJson(Object bean) throws JsonProcessingException {
        return objectMapper.writer().writeValueAsString(bean);
    }

    public static <T> T decodeJson(String json, Class<T> clazz) throws JsonProcessingException, IOException {
        return objectMapper.reader(clazz).readValue(json);
    }

    //使用&,间隔所有对象
    //输入：1, "1,1", 3，输出：1&,1/,1&,3
    public static String encodeArgs(Object... args) {
        if (null == args) {
            return null;
        }
        StringBuffer sb = new StringBuffer();
        for (Object arg : args) {
            sb.append(encodeArg(arg)).append(SPLIT_STR);
        }
        return sb.substring(0, sb.lastIndexOf(SPLIT_STR));
    }

    public static String[] decodeArgs(String msgText, int argCount) {
        String[] rs = new String[argCount];
        if (null != msgText) {
            String[] rst = msgText.split(SPLIT_STR);
            int i = 0;
            for (String r : rst) {
                rs[i++] = decodeArg(r);
            }
        }
        return rs;
    }

    public static String encodeArg(Object obj) {
        return null == obj ? EMPTY_STR : obj.toString().replaceAll(",", SPLIT_STR_ENCODE);
    }

    public static String decodeArg(String objString) {
        return null == objString ? null : EMPTY_STR.equals(objString) ? null : objString.replaceAll(SPLIT_STR_ENCODE, ",");
    }



}