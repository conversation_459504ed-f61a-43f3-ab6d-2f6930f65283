package com.accesscorporate.app.wms.server.dal.repository.impl;

import com.accesscorporate.app.wms.server.dal.entity.pickloc.MdPickLocDO;
import com.accesscorporate.app.wms.server.dal.mapper.pickloc.MdPickLocMapper;
import com.accesscorporate.app.wms.server.dal.repository.MdPickLocRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-20 10:35
 * Description: 捡货位
 */
@Repository
public class MdPickLocRepositoryImpl extends
        ServiceImpl<MdPickLocMapper, MdPickLocDO> implements MdPickLocRepository {

    @Override
    public MdPickLocDO queryPickLocBySkuIdAndWarehouseId(Long skuId,Long LocId, Long warehouseId) {
        return lambdaQuery()
                .eq(MdPickLocDO::getSkuId,skuId)
                .eq(MdPickLocDO::getWarehouseId,warehouseId)
                .eq(MdPickLocDO::getLocId,LocId)
                .eq(MdPickLocDO::getIsDeleted,Boolean.FALSE)
                .one();
    }

    @Override
    public Integer batchInsertMdPickLoc(List<MdPickLocDO> list) {
        return super.getBaseMapper().batchInsertMdPickLoc(list);
    }


}
