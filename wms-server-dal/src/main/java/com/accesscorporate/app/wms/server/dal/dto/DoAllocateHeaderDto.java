package com.accesscorporate.app.wms.server.dal.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 发货单头DTO
 */
@lombok.extern.slf4j.Slf4j
public class DoAllocateHeaderDto implements Serializable {

    private static final long serialVersionUID = 8316182654323875023L;

    private Long id;
    /**
     * DO单号
     */
    private String doNo;
    /**
     * 发货单状态
     */
    private String status;
    /**
     * 冻结状态 HOLD：冻结 RELEASE：释放
     */
    private String releaseStatus;
    /**
     * 订货数量 EXPECTED_QTY_EACH
     */
    private BigDecimal expectedQty;
    /**
     * 发货数量
     */
    private BigDecimal shipQty;
    /**
     * DO创建时间
     */
    private Date doCreateTime;
    /**
     * 发货日期
     */
    private Date shipTime;
    /**
     * DO类型
     */
    private String doType;
    /**
     * 是否半日达
     */
    private Integer isHalfDayDelivery;
    /**
     * 配送类型
     */
    private Integer deliveryLimitType;
    /**
     * 国家
     */
    private String country;
    /**
     * 省份
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区县
     */
    private String county;
    /**
     * 波次号
     */
    private String waveNo;
    /**
     * 分拣格号
     */
    private String sortGridNo;
    /**
     * 客户姓名
     */
    private String consigneeName;
    /**
     * 收货地址
     */
    private String address;
    /**
     * 邮编
     */
    private String postCode;
    /**
     * 电话
     */
    private String telephone;
    /**
     * 手机
     */
    private String mobile;
    /**
     * 配送公司名
     */
    private String distSuppCompName;
    /**
     * 发票数量
     */
    private Long invoiceQty;
    /**
     * 是否第一次购买
     */
    private String userDeffine1;
    /**
     * 冻结原因代码
     */
    private String holdCode;
    /**
     * 冻结原因
     */
    private String holdReason;
    /**
     * 冻结人
     */
    private String holdWho;
    /**
     * 冻结时间
     */
    private Date holdTime;
    /**
     * 代收货款金额
     */
    private BigDecimal productAmount;
    /**
     * 客户支付配送费
     */
    private BigDecimal orderDeliveryFee;
    /**
     * 退换货标识
     */
    private Integer exchangeFlag;
    /**
     * 重量
     */
    private BigDecimal grossWt;
    /**
     * 应收款
     */
    private BigDecimal receivable;

    private BigDecimal volume;

    /**
     * 配送公司Id
     */
    private Long carrierId;

    private String notes;

    private Date createdAt;

    /**
     * 调拨单类型
     *
     * @return
     */
    private Integer tranType;

    /**
     * 预计出库时间（标准）
     */
    private Date doFinishTime;

    /**
     * 补货状态
     */
    private Integer replStatus;

    private String businessCustomerName;

    public DoAllocateHeaderDto() {

    }

    /**
     * 分配查询 @see com.daxia.wms.delivery.deliveryorder.dao.DoAllocateHeaderDAO.findDoHeaderPageInfo
     *
     * @param id
     * @param doNo
     * @param status
     * @param releaseStatus
     * @param expectedQty
     * @param shipQty
     * @param doCreateTime
     * @param shipTime
     * @param doType
     * @param isHalfDayDelivery
     * @param deliveryLimitType
     * @param country
     * @param province
     * @param city
     * @param county
     * @param waveNo
     * @param sortGridNo
     * @param consigneeName
     * @param address
     * @param postCode
     * @param telephone
     * @param mobile
     * @param distSuppCompName
     * @param invoiceQty
     * @param userDeffine1
     * @param holdCode
     * @param holdReason
     * @param holdWho
     * @param holdTime
     * @param productAmount
     * @param orderDeliveryFee
     * @param exchangeFlag
     * @param grossWt
     * @param receivable
     * @param notes
     * @param createdAt
     * @param tranType
     * @param replStatus
     */
    public DoAllocateHeaderDto(Long id, String doNo, String status, String releaseStatus, BigDecimal expectedQty, BigDecimal shipQty, Date doCreateTime, Date shipTime,Date planShipTime, String doType, Integer isHalfDayDelivery,
                               Integer deliveryLimitType, String country, String province, String city, String county, String waveNo, String sortGridNo, String consigneeName, String address, String postCode, String telephone,
                               String mobile, String distSuppCompName, Long invoiceQty, String userDeffine1, String holdCode, String holdReason, String holdWho, Date holdTime, BigDecimal productAmount, BigDecimal orderDeliveryFee,
                               Integer exchangeFlag, BigDecimal grossWt, BigDecimal receivable, String notes, Date createdAt, Integer tranType, Integer replStatus, String businessCustomerName) {
        this.id = id;
        this.doNo = doNo;
        this.status = status;
        this.releaseStatus = releaseStatus;
        this.expectedQty = expectedQty;
        this.shipQty = shipQty;
        this.doCreateTime = doCreateTime;
        this.shipTime = shipTime;
        this.doType = doType;
        this.isHalfDayDelivery = isHalfDayDelivery;
        this.deliveryLimitType = deliveryLimitType;
        this.country = country;
        this.province = province;
        this.city = city;
        this.county = county;
        this.waveNo = waveNo;
        this.sortGridNo = sortGridNo;
        this.consigneeName = consigneeName;
        this.address = address;
        this.postCode = postCode;
        this.telephone = telephone;
        this.mobile = mobile;
        this.distSuppCompName = distSuppCompName;
        this.invoiceQty = invoiceQty;
        this.userDeffine1 = userDeffine1;
        this.holdCode = holdCode;
        this.holdReason = holdReason;
        this.holdWho = holdWho;
        this.holdTime = holdTime;
        this.productAmount = productAmount;
        this.orderDeliveryFee = orderDeliveryFee;
        this.exchangeFlag = exchangeFlag;
        this.grossWt = grossWt;
        this.receivable = receivable;
        this.notes = notes;
        this.createdAt = createdAt;
        this.tranType = tranType;
        this.replStatus = replStatus;
        this.businessCustomerName = businessCustomerName;
        this.doFinishTime = planShipTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReleaseStatus() {
        return releaseStatus;
    }

    public void setReleaseStatus(String releaseStatus) {
        this.releaseStatus = releaseStatus;
    }

    public BigDecimal getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(BigDecimal expectedQty) {
        this.expectedQty = expectedQty;
    }

    public BigDecimal getShipQty() {
        return shipQty;
    }

    public void setShipQty(BigDecimal shipQty) {
        this.shipQty = shipQty;
    }

    public Date getDoCreateTime() {
        return doCreateTime;
    }

    public void setDoCreateTime(Date doCreateTime) {
        this.doCreateTime = doCreateTime;
    }

    public Date getShipTime() {
        return shipTime;
    }

    public void setShipTime(Date shipTime) {
        this.shipTime = shipTime;
    }

    public String getDoType() {
        return doType;
    }

    public void setDoType(String doType) {
        this.doType = doType;
    }

    public Integer getIsHalfDayDelivery() {
        return isHalfDayDelivery;
    }

    public void setIsHalfDayDelivery(Integer isHalfDayDelivery) {
        this.isHalfDayDelivery = isHalfDayDelivery;
    }

    public Integer getDeliveryLimitType() {
        return deliveryLimitType;
    }

    public void setDeliveryLimitType(Integer deliveryLimitType) {
        this.deliveryLimitType = deliveryLimitType;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getDistSuppCompName() {
        return distSuppCompName;
    }

    public void setDistSuppCompName(String distSuppCompName) {
        this.distSuppCompName = distSuppCompName;
    }

    public Long getInvoiceQty() {
        return invoiceQty;
    }

    public void setInvoiceQty(Long invoiceQty) {
        this.invoiceQty = invoiceQty;
    }

    public String getUserDeffine1() {
        return userDeffine1;
    }

    public void setUserDeffine1(String userDeffine1) {
        this.userDeffine1 = userDeffine1;
    }

    public String getHoldCode() {
        return holdCode;
    }

    public void setHoldCode(String holdCode) {
        this.holdCode = holdCode;
    }

    public String getHoldReason() {
        return holdReason;
    }

    public void setHoldReason(String holdReason) {
        this.holdReason = holdReason;
    }

    public String getHoldWho() {
        return holdWho;
    }

    public void setHoldWho(String holdWho) {
        this.holdWho = holdWho;
    }

    public Date getHoldTime() {
        return holdTime;
    }

    public void setHoldTime(Date holdTime) {
        this.holdTime = holdTime;
    }

    public BigDecimal getProductAmount() {
        return productAmount;
    }

    public void setProductAmount(BigDecimal productAmount) {
        this.productAmount = productAmount;
    }

    public BigDecimal getOrderDeliveryFee() {
        return orderDeliveryFee;
    }

    public void setOrderDeliveryFee(BigDecimal orderDeliveryFee) {
        this.orderDeliveryFee = orderDeliveryFee;
    }

    public void setGrossWt(BigDecimal grossWt) {
        this.grossWt = grossWt;
    }

    public BigDecimal getGrossWt() {
        return grossWt;
    }

    public Integer getExchangeFlag() {
        return exchangeFlag;
    }

    public void setExchangeFlag(Integer exchangeFlag) {
        this.exchangeFlag = exchangeFlag;
    }

    public BigDecimal getReceivable() {
        return receivable;
    }

    public void setReceivable(BigDecimal receivable) {
        this.receivable = receivable;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public Long getCarrierId() {
        return carrierId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public Integer getTranType() {
        return tranType;
    }

    public void setTranType(Integer tranType) {
        this.tranType = tranType;
    }

    public Date getDoFinishTime() {
        return doFinishTime;
    }

    public void setDoFinishTime(Date doFinishTime) {
        this.doFinishTime = doFinishTime;
    }

    public Integer getReplStatus() {
        return replStatus;
    }

    public void setReplStatus(Integer replStatus) {
        this.replStatus = replStatus;
    }

    public String getBusinessCustomerName() {
        return businessCustomerName;
    }

    public void setBusinessCustomerName(String businessCustomerName) {
        this.businessCustomerName = businessCustomerName;
    }
}
