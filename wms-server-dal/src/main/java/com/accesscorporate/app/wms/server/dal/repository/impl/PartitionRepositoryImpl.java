package com.accesscorporate.app.wms.server.dal.repository.impl;

import com.accesscorporate.app.wms.server.common.enums.WhetherEnum;
import com.accesscorporate.app.wms.server.dal.entity.Partition;
import com.accesscorporate.app.wms.server.dal.mapper.PartitionMapper;
import com.accesscorporate.app.wms.server.dal.repository.PartitionRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-23 17:57
 * Description: 库区信息
 */
@Repository
public class PartitionRepositoryImpl extends
        ServiceImpl<PartitionMapper, Partition> implements PartitionRepository {

    @Override
    public List<Partition> queryPartitionByWarehouseId(Long warehouseId) {
        return lambdaQuery()
                .eq(Partition::getWarehouseId,warehouseId)
                .eq(Partition::getIsDeleted, WhetherEnum.NO.getCode())
                .list();
    }



}
