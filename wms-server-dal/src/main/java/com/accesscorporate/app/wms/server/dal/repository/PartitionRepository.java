package com.accesscorporate.app.wms.server.dal.repository;

import com.accesscorporate.app.wms.server.dal.entity.Partition;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-23 17:55
 * Description: 库区信息
 */
public interface PartitionRepository extends IService<Partition> {

    List<Partition> queryPartitionByWarehouseId(Long warehouseId);


}
