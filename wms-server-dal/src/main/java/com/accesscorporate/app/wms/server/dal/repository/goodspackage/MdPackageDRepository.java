package com.accesscorporate.app.wms.server.dal.repository.goodspackage;

import com.accesscorporate.app.wms.server.dal.entity.goodspackage.MdPackageDDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-02-19 11:28
 * Description: 商品包装明细表数据库操作
 */
public interface MdPackageDRepository extends IService<MdPackageDDO> {

    /**
     * 通过packageId查询明细信息
     */
    List<MdPackageDDO> queryPackageDetailListByPackageId(Long packageId);

}
