package com.accesscorporate.app.wms.server.dal.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("md_printer")
public class PrintDO {
    private Long id;
    private String code;
    private Long warehouseId;
    private String name;
    private String type;
    private String invoiceNoFrom;
    private String invoiceNoTo;
    private String invoiceNoCurrent;
    private String invoiceCode;
    private Integer isDeleted;
    private Integer status = 1;
    private Boolean isPrintByName;

}