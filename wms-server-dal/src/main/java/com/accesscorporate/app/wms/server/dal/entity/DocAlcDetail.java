package com.accesscorporate.app.wms.server.dal.entity;

import com.accesscorporate.app.wms.server.dal.entity.base.WhBaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 订单分配明细表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("doc_alc_detail")
public class DocAlcDetail extends WhBaseEntity {
    
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long doHeaderId; // 订单头ID
    private String linestatus; // 明细状态
    private Long skuId; // 商品ID
    private BigDecimal expectedQty; // 期望发货数量
    private BigDecimal expectedQtyEach; // 期望发货数量EACH
    private BigDecimal expectedQtyPcs; // 期望件数量（拆解后）
    private BigDecimal expectedQtyUnit; // 期望箱数量（拆解后）
    private Long packageId; // 包装ID
    private Long packDetailId; // 包装明细ID
    private String uom; // 单位
    private String lotNo; // 批次号
    private String lotatt01; // 生产日期
    private String lotatt02; // 失效日期
    private String lotatt03; // 入库日期
    private String lotatt04; // 供应商ID
    private String lotatt05; // 批号
    private String lotatt06; // 货主ID（商家）
    private String lotatt07; // 批次包装
    private String lotatt08; // 制造商
    private String lotatt09; // 单价
    private String lotatt10; // po号
    private String lotatt11; // 批次属性11
    private String lotatt12; // 坏品原因
    private String lotatt13; // 容器号
    private String lotatt14; // 容器类型
    private String lotatt15; // 批次属性15
    private String lotatt16; // 批次属性16
    private String lotatt17; // 批次属性17
    private Long allocationRule; // 分配规则ID
    private BigDecimal volume; // 体积
    private BigDecimal netweight; // 净重
    private BigDecimal grossweight; // 毛重
    private String pickzone; // 拣货区
    private BigDecimal price; // 商品价值
    private BigDecimal allocatedQty; // 分配数量
    private BigDecimal allocatedQtyUnit; // 分配包装数量
    private BigDecimal allocatedQtyPcs; // 分配件数
    private BigDecimal allocatedQtyEach; // 分配数量EACH
    private BigDecimal packedQty; // 核检数量EACH
    private BigDecimal pickedQty; // 拣货数量
    private BigDecimal pickedQtyEach; // 拣货数量EACH
    private BigDecimal sortedQty; // 分拣数量
    private BigDecimal sortedQtyEach; // 分拣数量EACH
    private BigDecimal packedQtyEach; // 核检数量EACH
    private BigDecimal shippedQty; // 发货数量
    private BigDecimal shippedQtyEach; // 发货数量EACH
    private String userdefine1; // 用户自定义1
    private String userdefine2; // 用户自定义2
    private String userdefine3; // 用户自定义3
    private String userdefine4; // 用户自定义4
    private String notes; // 备注
    private Integer isPromote; // 是否是促销产品
    private BigDecimal orderItemRebate; // 本项商品返利金额
    private BigDecimal retailPrice; // 出库单价
    private String parentId; // 父级ID
    private Long isDoLeaf; // 是否是叶子结点
    private String dEdi1; // D_EDI_1
    private String dEdi2; // D_EDI_2
    private String origHeaderId; // 原始头ID
    private String origDetailId; // 原始明细ID
    private BigDecimal needReplQty; // 需补货数量
    private BigDecimal needReplQtyPcs; // 需要补货件数
    private BigDecimal needReplQtyUnit; // 需要补货箱数
    private Integer isDamaged; // 是否坏品
    private Integer isValueables; // 是否贵重品
    private Integer wineFlag; // 是否为酒类随附单
    private Integer noStockFlag; // 无库存标识
    private Integer leastAvailableDay; // 最少可用天数
    private Integer salesGrade; // 销售等级
    private Integer goodsGrade; // 货品等级
    private LocalDate minExp; // 最小失效日期
    private LocalDate maxExp; // 最大失效日期

    @TableLogic
    private Boolean isDeleted;
}
