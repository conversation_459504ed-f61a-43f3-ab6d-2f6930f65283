package com.accesscorporate.app.wms.server.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 仓 DO
 *
 * <AUTHOR>
 * 2025/2/7  17:25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName(value = "md_warehouse")
public class WarehouseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 原始ID
     */
    private String originalId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 省份ID
     */
    private Long provinceId;

    /**
     * 区县ID
     */
    private Long countyId;

    /**
     * 国家ID
     */
    private Long countryId;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 地址（交货地址）
     */
    private String addressName;

    /**
     * 负责人ID
     */
    private Long managerId;

    /**
     * 传真
     */
    private String fax;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 备注
     */
    private String referenceInfo;

    /**
     * 仓库群组email
     */
    private String groupEmail;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 联系人
     */
    private String contactor;

    /**
     * 仓库描述信息
     */
    private String warehouseDesc;

    /**
     * 缩写
     */
    private String shortName;

    /**
     * 仓库类型
     */
    private Short warehouseType;

    /**
     * 仓库面积
     */
    private BigDecimal square;

    /**
     * 容积
     */
    private BigDecimal volume;

    /**
     * 是否当前仓库
     */
    private Integer runFlag;

    /**
     * 类型：1 仓库，2 门店
     */
    private Integer type;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人ID
     */
    private String createBy;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 修改人ID
     */
    private String updateBy;

    /**
     * 是否删除：0-no, 1-yes
     */
    private Integer isDeleted;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 仓库分组
     */
    private String warehouseGroup;

    /**
     * 企业编码
     */
    private Long corpId;

    /**
     * 站点代码
     */
    private String stationCode;

    /**
     * 租户ID
     */
    private Integer tenantId;
}
