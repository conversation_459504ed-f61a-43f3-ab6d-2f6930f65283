package com.accesscorporate.app.wms.server.dal.intercept;



import cn.hutool.core.util.ReflectUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.select.Join;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.SetOperationList;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.util.Properties;

/**
 * @author: gaohao
 * @date: 2025-02-17 14:58
 * @desc: 数据隔离拦截器
 */
@Intercepts({
        @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
@Slf4j
@Component
public class SelectDataAuthInterceptor implements Interceptor {

    private static final String TENANT_COLUMN = "tenant_id"; // 租户字段
    private static final String WAREHOUSE_COLUMN = "warehouse_id"; // 仓库字段
    private static final ThreadLocal<Integer> TENANT_ID = new ThreadLocal<>(); // 当前租户ID
    private static final ThreadLocal<Integer> WAREHOUSE_ID = new ThreadLocal<>(); // 当前仓库ID

    /**
     * 根据父类判断数据隔离级别，实现隔离
     *
     * @param invocation
     * @return
     * @throws Throwable
     */
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
        BoundSql boundSql = statementHandler.getBoundSql();
        // 获取原始SQL
        String originalSql = boundSql.getSql();
        Integer tenantId = 6;
        Integer warehouseId = 1;
        // 生成数据隔离的SQL
        String newSql = processSql(originalSql, tenantId, warehouseId);
        ReflectUtil.setFieldValue(boundSql, "sql", newSql);
        return invocation.proceed();
    }

    private String processSql(String sql, Integer tenantId, Integer warehouseId) {
        try {
            Statement stmt = CCJSqlParserUtil.parse(sql);
            if (stmt instanceof Select) {
                Select select = (Select) stmt;
                processSelect(select, tenantId, warehouseId);
                return select.toString();
            } else if (stmt instanceof Update) {
                Update update = (Update) stmt;
                processUpdate(update, tenantId, warehouseId);
                return update.toString();
            } else if (stmt instanceof Delete) {
                Delete delete = (Delete) stmt;
                processDelete(delete, tenantId, warehouseId);
                return delete.toString();
            } else if (stmt instanceof Insert) {
                Insert insert = (Insert) stmt;
                processInsert(insert, tenantId, warehouseId);
                return insert.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sql;
    }

    private void processSelect(Select select, Integer tenantId, Integer warehouseId) {
        Select selectBody = select.getSelectBody();
        if (selectBody instanceof PlainSelect) {
            processPlainSelect((PlainSelect) selectBody, tenantId, warehouseId);
        } else if (selectBody instanceof SetOperationList) {
            for (Select body : ((SetOperationList) selectBody).getSelects()) {
                if (body instanceof PlainSelect) {
                    processPlainSelect((PlainSelect) body, tenantId, warehouseId);
                }
            }
        }
    }

    private void processPlainSelect(PlainSelect plainSelect, Integer tenantId, Integer warehouseId) {
        if (plainSelect.getFromItem() instanceof Table) {
            Table table = (Table) plainSelect.getFromItem();
            appendConditions(plainSelect, table, tenantId, warehouseId);
        }
        if (plainSelect.getJoins() != null) {
            for (Join join : plainSelect.getJoins()) {
                if (join.getRightItem() instanceof Table) {
                    Table table = (Table) join.getRightItem();
                    appendConditions(plainSelect, table, tenantId, warehouseId);
                }
            }
        }
    }

    private void processUpdate(Update update, Integer tenantId, Integer warehouseId) {
        appendConditions(update, update.getTable(), tenantId, warehouseId);
    }

    private void processDelete(Delete delete, Integer tenantId, Integer warehouseId) {
        appendConditions(delete, delete.getTable(), tenantId, warehouseId);
    }

    private void processInsert(Insert insert, Integer tenantId, Integer warehouseId) {
        // INSERT should ensure tenant_id / warehouse_id are present in the VALUES.
    }

    private void appendConditions(PlainSelect select, Table table, Integer tenantId, Integer warehouseId) {
        Expression newCondition = buildCondition(table, tenantId, warehouseId);
        if (newCondition != null) {
            select.setWhere(select.getWhere() == null ? newCondition : new AndExpression(select.getWhere(), newCondition));
        }
    }

    private void appendConditions(Update update, Table table, Integer tenantId, Integer warehouseId) {
        Expression newCondition = buildCondition(table, tenantId, warehouseId);
        if (newCondition != null) {
            update.setWhere(update.getWhere() == null ? newCondition : new AndExpression(update.getWhere(), newCondition));
        }
    }

    private void appendConditions(Delete delete, Table table, Integer tenantId, Integer warehouseId) {
        Expression newCondition = buildCondition(table, tenantId, warehouseId);
        if (newCondition != null) {
            delete.setWhere(delete.getWhere() == null ? newCondition : new AndExpression(delete.getWhere(), newCondition));
        }
    }

    private Expression buildCondition(Table table, Integer tenantId, Integer warehouseId) {
        String tableName = table.getName();
        Expression condition = null;

        // 通过反射获取 DO 类的父类来判断是否有租户/仓库隔离
        try {

//            condition = CCJSqlParserUtil.parseCondExpression(tableName + "." + TENANT_COLUMN + " = " + tenantId);
//            Class<?> clazz = Class.forName("com.accesscorporate.app.wms.server.dal.entity.rule" + capitalize(tableName) + "DO"); // 这里假设表名和 DO 类名一一对应
//            Class<?> superclass = clazz.getSuperclass();
//
//            if (superclass != null) {
//                // 如果父类是 TenantBaseEntity，则需要添加 tenant_id
//                if (superclass.getSimpleName().equals("TenantBaseEntity") && tenantId != null) {
//                    condition = CCJSqlParserUtil.parseCondExpression(tableName + "." + TENANT_COLUMN + " = " + tenantId);
//                }
//                // 如果父类是 WhBaseEntity，则需要添加 warehouse_id
//                else if (superclass.getSimpleName().equals("WhBaseEntity") && warehouseId != null) {
//                    Expression warehouseCondition = CCJSqlParserUtil.parseCondExpression(tableName + "." + WAREHOUSE_COLUMN + " = " + warehouseId);
//                    condition = condition == null ? warehouseCondition : new AndExpression(condition, warehouseCondition);
//                }
//            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return condition;
    }

    // 大写表名的首字母（如：user -> User）
    private String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }





    @Override
    public Object plugin(Object target) {
        return Interceptor.super.plugin(target);
    }

    @Override
    public void setProperties(Properties properties) {
        Interceptor.super.setProperties(properties);
    }
}
