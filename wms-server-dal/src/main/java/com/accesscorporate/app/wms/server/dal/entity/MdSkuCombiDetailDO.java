package com.accesscorporate.app.wms.server.dal.entity;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * md_sku_combi_detail
 *
 * <AUTHOR>
@Data
public class MdSkuCombiDetailDO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7132122113363188056L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 组合条码
     */
    private String combiBarcode;

    /**
     * 商品id
     */
    private Long skuId;

    /**
     * 商品条码
     */
    private String barcode;

    /**
     * 货品等级
     */
    private String goodsGrade;

    /**
     * 件数
     */
    private Integer total;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 是否删除 0:no 1:yes
     */
    private Integer isDeleted;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 最后修改日期
     */
    private Date updateTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 版本锁
     */
    private Integer version;

}