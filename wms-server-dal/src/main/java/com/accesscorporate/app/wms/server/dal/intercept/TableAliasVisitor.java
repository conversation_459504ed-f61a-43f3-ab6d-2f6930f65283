//package com.accesscorporate.app.wms.server.dal.intercept;
//
//import com.alibaba.druid.sql.SQLUtils;
//import com.alibaba.druid.sql.ast.SQLExpr;
//import com.alibaba.druid.sql.ast.SQLStatement;
//import com.alibaba.druid.sql.ast.expr.SQLIdentifierExpr;
//import com.alibaba.druid.sql.ast.statement.SQLExprTableSource;
//import com.alibaba.druid.sql.ast.statement.SQLInsertStatement;
//import com.alibaba.druid.sql.dialect.mysql.ast.statement.MySqlInsertStatement;
//import com.alibaba.druid.sql.dialect.mysql.visitor.MySqlSchemaStatVisitor;
//import com.alibaba.druid.stat.TableStat;
//import lombok.Getter;
//import lombok.Setter;
//
//import java.util.LinkedHashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * 别名分析
// *
// * <AUTHOR>
// * @date 2020 /10/27 15:01:18
// */
//@Setter
//@Getter
//public class TableAliasVisitor extends MySqlSchemaStatVisitor {
//    private Map<String, String> tablesInfo = new LinkedHashMap();
//    private String filterTableAlias;
//    private List<SQLExpr> insertColumns;
//    private List<SQLInsertStatement.ValuesClause> values;
//
//    @Override
//    public boolean visit(SQLExprTableSource x) {
//        tablesInfo.put(x.getTableName(), x.getAlias());
//        return true;
//    }
//    @Override
//    public boolean visit(MySqlInsertStatement x) {
//        if (repository != null
//            && x.getParent() == null) {
//            repository.resolve(x);
//        }
//
//        setMode(x, TableStat.Mode.Insert);
//
//        TableStat stat = getTableStat(x.getTableSource());
//
//        if (stat != null) {
//            stat.incrementInsertCount();
//        }
//
//        List<SQLExpr> columns = x.getColumns();
//        accept(columns);
//        List<SQLInsertStatement.ValuesClause> valuesList = x.getValuesList();
//        accept(valuesList);
//        accept(x.getQuery());
//        accept(x.getDuplicateKeyUpdate());
//        insertColumns= columns;
//        values=valuesList;
//        return true;
//    }
//
//    public static void main(String[] args) {
//        String sql="insert into test t (a,b,c) values (1,2,3)";
//        sql = "select * from test t left join test2 t2 on t.id=t2.id";
//        SQLStatement sqlStatement = SQLUtils.parseSingleMysqlStatement(sql);
//        TableAliasVisitor av = new TableAliasVisitor();
//        sqlStatement.accept(av);
//        System.out.println(av.getColumns());
//        List<SQLExpr> insertColumns = av.getInsertColumns();
//        List<SQLInsertStatement.ValuesClause> values = av.getValues();
//        for (int i=0;i<insertColumns.size();i++){
//            SQLIdentifierExpr x = (SQLIdentifierExpr)insertColumns.get(i);
//            System.out.println(x.getName());
//            System.out.println(values.get(0).getValues().get(i));
//        }
//    }
//}
