package com.accesscorporate.app.wms.server.dal.mapper;

import com.accesscorporate.app.wms.server.dal.dto.AddressFilterQuery;
import com.accesscorporate.app.wms.server.dal.entity.AddressDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AddressMapper {

    /**
     * 分页查询地址
     */
    IPage<AddressDO> queryByFilter(IPage<AddressDO> page, @Param("filter") AddressFilterQuery filter);

    /**
     * 根据ID查询地址
     */
    AddressDO selectById(@Param("id") Long id);

    /**
     * 根据父编码查询地址列表（原getListByParentCode逻辑）
     */
    List<AddressDO> getListByParentCode(@Param("parentCode") String parentCode);

    /**
     * 根据名称查询地址（原getByName逻辑）
     */
    AddressDO getByName(@Param("name") String name);

    /**
     * 根据地址编码查询地址（原getByAddressCode逻辑）
     */
    AddressDO getByAddressCode(@Param("addressCode") String addressCode);

    /**
     * 插入地址信息
     * @param addressDO 地址实体对象
     * @return 插入成功的记录数
     */
    int insert(AddressDO addressDO);

    /**
     * 根据主键ID更新地址信息
     * @param addressDO 地址实体对象（需包含id字段）
     * @return 更新成功的记录数
     */
    int updateById(AddressDO addressDO);

    /**
     * 根据主键ID逻辑删除地址信息
     * @param id 地址主键ID
     * @return 删除成功的记录数（1表示成功，0表示无匹配记录）
     */
    int deleteById(@Param("id") Long id);

    int recoverById(@Param("id") Long id);

}
