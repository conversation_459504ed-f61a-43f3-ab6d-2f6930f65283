package com.accesscorporate.app.wms.server.dal.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 商品表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("md_sku")
public class MdSku implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String originalId;

    private Long cargoOwnerId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productCname;

    /**
     * 产品英文名
     */
    private String productEname;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * ������
     */
    private String sku;

    /**
     * 商品条码
     */
    private String ean13;

    /**
     * 二维码
     */
    private String qrCode;

    /**
     * 单品长度 cm
     */
    private BigDecimal length;

    /**
     * 单品宽度 cm
     */
    private BigDecimal width;

    /**
     * 单品高度 cm
     */
    private BigDecimal height;

    private BigDecimal volume;

    /**
     * 重量（净重）kg
     */
    private BigDecimal neightweight;

    private BigDecimal grossweight;

    /**
     * 重量计量单位
     */
    private String weightUnit;

    /**
     * 高度计量单位
     */
    private String heightUnit;

    /**
     * 宽度计量单位
     */
    private String widthUnit;

    /**
     * 长度计量单位
     */
    private String lengthUnit;

    /**
     * 体积计量单位
     */
    private String volumeUnit;

    private Long receiveUnit;

    private Long shippmentUnit;

    private Long stockUnit;

    /**
     * ����
     */
    private Long defaultLocationId;

    /**
     * 缺省库区ID
     */
    private Long defaultPartionId;

    /**
     * 分配规则
     */
    private Long allocationRuleId;

    /**
     * 补货规则
     */
    private Long replenishmentRuleId;

    /**
     * 周转规则
     */
    private Long rotationRuleId;

    /**
     * 上架规则
     */
    private Long putawayRuleId;

    /**
     * 出入库时需要匹配序列号数量
     */
    private Integer snQty;

    /**
     * 是否需要批次控制
     */
    private Integer batchCtlFlag;

    /**
     * 缺省批次属性
     */
    private Long defLotId;

    /**
     * 缺省ID
     */
    private Long packageId;

    /**
     * 备注
     */
    private String notes;

    /**
     * 循环级别
     */
    private String circleClass;

    /**
     * 允许混放商品
     */
    private Integer canMixProduct;

    /**
     * 允许混放批次
     */
    private Integer canMixBatch;

    /**
     * 贵重品标识
     */
    private Integer valuableFlag;

    /**
     * 保质期管理标识
     */
    private Integer shelfLifeFlag;

    /**
     * 保质天数
     */
    private Integer shelfLife;

    /**
     * 最低安全库存
     */
    private BigDecimal lowSafeStockNum;

    /**
     * 最高安全库存
     */
    private BigDecimal highSafeStockNum;

    /**
     * 赠品标识
     */
    private Integer giftFlag;

    /**
     * 3C标识
     */
    private Integer cccFlag;

    /**
     * 剂型
     */
    private String dosage;

    /**
     * 批准文号
     */
    private String registerNo;

    /**
     * 允许小数
     */
    private Integer dotFlag;

    /**
     * 温度范围
     */
    private String keepTemperature;

    /**
     * ��������
     */
    private String keepHumidity;

    /**
     * 特殊要求
     */
    private String keepSpecCondition;

    /**
     * 预留字段1
     */
    private String udf1;

    /**
     * 预留字段1
     */
    private String udf2;

    /**
     * 预留字段1
     */
    private String udf3;

    /**
     * 箱规
     */
    private String udf4;

    /**
     * ��������1
     */
    private String udf5;

    /**
     * ��������1
     */
    private String udf6;

    /**
     * ��������1
     */
    private String udf7;

    /**
     * ��������1
     */
    private String udf8;

    /**
     * 创立日期
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private String createBy;

    /**
     * 最后修改日期
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * ������ID
     */
    private String updateBy;

    /**
     * 0:no1:yes
     */
    private Integer isDeleted;

    /**
     * 版本锁
     */
    private Long version;

    /**
     * 业务更新版本
     */
    private Long itVersion;

    private Integer checkGrossweight;

    private Integer checkVolume;

    private Integer checkNetweight;

    private Integer checkLength;

    private Integer checkWidth;

    private Integer checkHeight;

    private Integer productType;

    private BigDecimal rsMinQty;

    private String subtitle;

    /**
     * 有效期规则ID
     */
    private Long validPeriodRuleId;

    /**
     * 有效期类型
     */
    private Long validPeriodType;

    /**
     * 生鲜标识
     */
    private Integer freshFlag;

    private String pickMaxPercent;

    private String pickMaxQty;

    private String pickSecPercent;

    private String pickSecQty;

    /**
     * 码托规格TI，一层几箱
     */
    private Integer tiQty;

    /**
     * 码托规格HI，一共几层
     */
    private Integer hiQty;

    private Long qaDayRuleId;

    private String validPeriodRuleNo;

    /**
     * 产品规格
     */
    private String specification;

    private Integer tenantId;

    private String spvsnPrefix;

    private Integer needSpvsnFlag;

    private Integer maintainType;

    /**
     * 存贮条件
     */
    private String storageCondition;

    private Integer validityDays;

    private LocalDate instructionsValidityPeriod;

    private Integer isSpecialCheck;

    private Integer limitCountFlg;

    private String helpCode;

    /**
     * 税率
     */
    private String taxRate;

    private String productionLicense;

    /**
     * 盘点规则
     */
    private Long countRuleId;

    private Long recheckHandleType;

    private Long brandId;

    /**
     * 扩展字段1
     */
    private String ext1;

    /**
     * 扩展字段2
     */
    private String ext2;

    /**
     * 扩展字段3
     */
    private String ext3;

    /**
     * 扩展字段4
     */
    private String ext4;

    /**
     * 扩展字段5
     */
    private String ext5;

    /**
     * 生产厂家
     */
    private String manufacturerName;

    /**
     * 器械注册证号
     */
    private String registrationCertificateNo;

    /**
     * 厂家id
     */
    private String manufacturerId;

    private String storageConditionKey;

    /**
     * 运输温度 1-防高温 2-防冻 3-常温
     */
    private Integer transportWendy;

    /**
     * 耗材类型 1-气泡膜 2-气泡柱
     */
    private String materialsNo;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 商品价格
     */
    private BigDecimal price;


}
