package com.accesscorporate.app.wms.server.dal.entity.goodspackage;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-02-19 10:51
 * Description: 商品包装主表
 */
@Data
@TableName("md_package_h")
public class MdPackageHDO {

    /**
     * 主键ID，自增
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    /**
     * 商品ID
     */
    @TableField("SKU_ID")
    private Long skuId;

    /**
     * 包装代码
     */
    @TableField("PKG_CODE")
    private String pkgCode;

    /**
     * 包装描述
     */
    @TableField("PKG_DESC")
    private String pkgDesc;

    /**
     * 创立日期
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATE_BY", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 最后修改日期
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 最后修改人ID
     */
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 逻辑删除标记（0: no, 1: yes）
     */
    @TableField("IS_DELETED")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 版本锁，用于乐观锁
     */
    @TableField("VERSION")
    //@Version
    private Long version;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;


}
