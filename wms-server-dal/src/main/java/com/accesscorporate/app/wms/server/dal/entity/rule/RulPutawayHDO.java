package com.accesscorporate.app.wms.server.dal.entity.rule;

import com.accesscorporate.app.wms.server.dal.entity.base.WhBaseEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 上架规则主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rul_putaway_h")
public class RulPutawayHDO extends WhBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则code
     */
    private String putawayCode;

    /**
     * 规则描述
     */
    private String descr;

    /**
     * 是否激活0 未激活 1 激活
     */
    private String activeFlag;

    /**
     * 逻辑删除
     */
    private Integer isDeleted;

}
