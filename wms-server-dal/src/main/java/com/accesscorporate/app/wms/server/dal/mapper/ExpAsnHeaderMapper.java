package com.accesscorporate.app.wms.server.dal.mapper;

import com.accesscorporate.app.wms.server.dal.entity.ExpAsnHeaderDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ExpAsnHeaderMapper extends BaseMapper<ExpAsnHeaderDO> {

    ExpAsnHeaderDO selectByIdAndWarehouseId(
        @Param("id") Long id,
        @Param("warehouseId") Long warehouseId);
}