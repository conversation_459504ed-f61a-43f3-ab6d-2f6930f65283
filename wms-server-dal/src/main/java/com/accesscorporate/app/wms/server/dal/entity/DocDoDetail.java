package com.accesscorporate.app.wms.server.dal.entity;

import com.accesscorporate.app.wms.server.dal.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 发货单明细表实体类
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("doc_do_detail")
public class DocDoDetail extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("do_header_id")
    private Long doHeaderId; // 订单头ID

    @TableField("linestatus")
    private String linestatus; // 明细状态；00-初始化，30-分配中，40-分配完成，50-拣货中，60-拣货完成，64-分拣中，65-分拣完成，66-装箱中，67装箱完成，68-交接中，69-交接完成，80-已出库，90-已取消

    @TableField("sku_id")
    private Long skuId; // 商品ID

    @TableField("expected_qty")
    private BigDecimal expectedQty; // 期望发货数量

    @TableField("expected_qty_each")
    private BigDecimal expectedQtyEach; // 期望发货数量EACH

    @TableField("package_id")
    private Long packageId; // 包装ID

    @TableField("pack_detail_id")
    private Long packDetailId; // 包装明细ID

    @TableField("uom")
    private String uom; // 单位

    @TableField("lotatt01")
    private String lotatt01; // 生产日期

    @TableField("lotatt02")
    private String lotatt02; // 失效日期

    @TableField("lotatt03")
    private String lotatt03; // 入库日期

    @TableField("lotatt04")
    private String lotatt04; // 供应商ID

    @TableField("lotatt05")
    private String lotatt05; // 批号

    @TableField("lotatt06")
    private String lotatt06; // 货主ID（商家）

    @TableField("lotatt07")
    private String lotatt07; // 批次包装

    @TableField("lotatt08")
    private String lotatt08; // 制造商

    @TableField("lotatt09")
    private String lotatt09; // 单价

    @TableField("lotatt10")
    private String lotatt10; // po号

    @TableField("lotatt11")
    private String lotatt11; // 批次属性11

    @TableField("lotatt12")
    private String lotatt12; // 坏品原因

    @TableField("lotatt13")
    private String lotatt13; // 容器号

    @TableField("lotatt14")
    private String lotatt14; // 容器类型

    @TableField("lotatt15")
    private String lotatt15; // 批次属性15

    @TableField("lotatt16")
    private String lotatt16; // 批次属性16

    @TableField("lotatt17")
    private String lotatt17; // 批次属性17

    @TableField("allocation_rule")
    private Long allocationRule; // 分配规则ID

    @TableField("volume")
    private BigDecimal volume; // 体积

    @TableField("netweight")
    private BigDecimal netweight; // 净重

    @TableField("grossweight")
    private BigDecimal grossweight; // 毛重

    @TableField("pickzone")
    private String pickzone; // 拣货区

    @TableField("price")
    private BigDecimal price; // 商品价值

    @TableField("allocated_qty")
    private BigDecimal allocatedQty; // 分配数量

    @TableField("allocated_qty_each")
    private BigDecimal allocatedQtyEach; // 分配数量EACH

    @TableField("picked_qty")
    private BigDecimal pickedQty; // 拣货数量

    @TableField("pick_qty")
    private BigDecimal pickQty; // 原始拣货数量

    @TableField("picked_qty_unit")
    private BigDecimal pickedQtyUnit; // 拣货箱数量

    @TableField("picked_qty_each")
    private BigDecimal pickedQtyEach; // 拣货数量EACH

    @TableField("sorted_qty")
    private BigDecimal sortedQty; // 分拣数量

    @TableField("sorted_qty_unit")
    private BigDecimal sortedQtyUnit; // 分拣包装数量

    @TableField("packed_qty")
    private BigDecimal packedQty; // 核检数量

    @TableField("sorted_qty_each")
    private BigDecimal sortedQtyEach; // 分拣数量EACH

    @TableField("packed_qty_each")
    private BigDecimal packedQtyEach; // 核检数量EACH

    @TableField("shipped_qty")
    private BigDecimal shippedQty; // 发货数量

    @TableField("shipped_qty_each")
    private BigDecimal shippedQtyEach; // 发货数量EACH

    @TableField("userdefine1")
    private String userdefine1; // 用户自定义1

    @TableField("userdefine2")
    private String userdefine2; // 用户自定义2

    @TableField("userdefine3")
    private String userdefine3; // 用户自定义3

    @TableField("userdefine4")
    private String userdefine4; // 用户自定义4

    @TableField("notes")
    private String notes; // 备注

    @TableField("is_promote")
    private Integer isPromote; // 是否是促销产品，0:否 1:是

    @TableField("order_item_rebate")
    private BigDecimal orderItemRebate; // 本项商品返利金额

    @TableField("retail_price")
    private BigDecimal retailPrice; // 出库单价

    @TableField("PARENT_ID")
    private String parentId; // 父ID

    @TableField("is_do_leaf")
    private Long isDoLeaf; // 是否是叶子结点，0:否 1:是

    @TableField("d_edi_1")
    private String dEdi1; // D_EDI_1

    @TableField("d_edi_2")
    private String dEdi2; // D_EDI_2

    @TableField("ORIG_HEADER_ID")
    private String origHeaderId; // 原始头ID

    @TableField("ORIG_DETAIL_ID")
    private String origDetailId; // 原始明细ID

    @TableField("need_repl_qty")
    private BigDecimal needReplQty; // 需补货数量

    @TableField("is_damaged")
    private Integer isDamaged; // 是否坏品，0:否 1:是

    @TableField("is_valueables")
    private Integer isValueables; // 是否贵重品，0:否 1:是

    @TableField("warehouse_id")
    private Long warehouseId; // 仓库id

    @TableField("wine_flag")
    private Integer wineFlag; // 是否为酒类随附单 0 否、1 是

    @TableField("cargo_total_price")
    private BigDecimal cargoTotalPrice; // 商品行邮税总价

    @TableField("lot_no")
    private String lotNo; // 批次编码

    @TableField("cargototalprice")
    private BigDecimal cargoTotalPriceOld; // 商品总价（旧字段）

    @TableField("is_validation_required")
    private Integer isValidationRequired; // 药网药品监管码是否需要扫码 0:不用 1:可选 2:必扫

    @TableField("have_cfy")
    private Integer haveCfy; // 是否有处方药

    @TableField("cfy_handle_flag")
    private Integer cfyHandleFlag; // 处方药处理标识

    @TableField("least_available_day")
    private Integer leastAvailableDay; // 最低有效天数

    @TableField("recheck_message")
    private String recheckMessage; // 核拣装箱弹出提示

    @TableField("sales_grade")
    private Integer salesGrade; // 销售等级，2C订单使用

    @TableField("goods_grade")
    private Integer goodsGrade; // 货品等级，2B订单使用

    @TableField("min_exp")
    private LocalDate minExp; // 最小失效日期

    @TableField("max_exp")
    private LocalDate maxExp; // 最大失效日期

    @TableLogic
    @TableField("is_deleted")
    private Boolean isDeleted; // 该记录是否已逻辑删除，0:否 1:是
}
