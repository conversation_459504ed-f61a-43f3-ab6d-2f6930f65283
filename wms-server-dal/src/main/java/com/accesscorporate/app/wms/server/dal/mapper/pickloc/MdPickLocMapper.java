package com.accesscorporate.app.wms.server.dal.mapper.pickloc;

import com.accesscorporate.app.wms.server.dal.dto.PickLocQueryParam;
import com.accesscorporate.app.wms.server.dal.entity.pickloc.MdPickLocDO;
import com.accesscorporate.app.wms.server.dal.entity.pickloc.PickLocPageQueryResponseDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-20 10:27
 * Description: 商品捡货位数据库操作
 */
@Mapper
public interface MdPickLocMapper extends BaseMapper<MdPickLocDO> {


    IPage<PickLocPageQueryResponseDO> pickLocPageQuery(Page<?> page, @Param("param") PickLocQueryParam param);


    Integer batchInsertMdPickLoc(List<MdPickLocDO> list);


}
