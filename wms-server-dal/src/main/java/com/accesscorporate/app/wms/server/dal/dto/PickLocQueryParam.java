package com.accesscorporate.app.wms.server.dal.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-24 10:37
 * Description: 捡货位列表查询
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PickLocQueryParam extends BaseQueryParam {

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 商品中文名称
     */
    private String productCname;

    /**
     * 商品条码
     */
    private String barCode;

    /**
     * 库位编码
     */
    private String locCode;

    /**
     * 库区主键ID
     */
    private String partitionId;



}
