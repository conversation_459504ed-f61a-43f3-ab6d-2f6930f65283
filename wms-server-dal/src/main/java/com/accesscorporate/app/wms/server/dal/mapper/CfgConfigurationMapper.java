package com.accesscorporate.app.wms.server.dal.mapper;

import com.accesscorporate.app.wms.server.dal.entity.CfgConfigurationDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统参数配置 Mapper 接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface CfgConfigurationMapper extends BaseMapper<CfgConfigurationDO> {

    /**
     * 根据配置编号和仓库ID查询配置
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @return 配置信息
     */
    CfgConfigurationDO selectByConfigNoAndWarehouseId(@Param("configNo") String configNo, 
                                                       @Param("warehouseId") Long warehouseId);

    /**
     * 根据配置编号和租户ID查询配置
     * 
     * @param configNo 配置编号
     * @param tenantId 租户ID
     * @return 配置信息
     */
    CfgConfigurationDO selectByConfigNoAndTenantId(@Param("configNo") String configNo, 
                                                    @Param("tenantId") Long tenantId);

    /**
     * 根据配置编号查询全局配置
     * 
     * @param configNo 配置编号
     * @return 配置信息
     */
    CfgConfigurationDO selectGlobalByConfigNo(@Param("configNo") String configNo);

    /**
     * 根据配置级别查询配置列表
     * 
     * @param configLevel 配置级别
     * @param warehouseId 仓库ID（可选）
     * @param tenantId 租户ID（可选）
     * @return 配置列表
     */
    List<CfgConfigurationDO> selectByConfigLevel(@Param("configLevel") Integer configLevel,
                                                  @Param("warehouseId") Long warehouseId,
                                                  @Param("tenantId") Long tenantId);

    /**
     * 根据配置类型查询配置列表
     * 
     * @param configType 配置类型
     * @param warehouseId 仓库ID（可选）
     * @param tenantId 租户ID（可选）
     * @return 配置列表
     */
    List<CfgConfigurationDO> selectByConfigType(@Param("configType") String configType,
                                                 @Param("warehouseId") Long warehouseId,
                                                 @Param("tenantId") Long tenantId);

    /**
     * 根据一级分类查询配置列表
     * 
     * @param levelOne 一级分类
     * @param warehouseId 仓库ID（可选）
     * @param tenantId 租户ID（可选）
     * @return 配置列表
     */
    List<CfgConfigurationDO> selectByLevelOne(@Param("levelOne") String levelOne,
                                               @Param("warehouseId") Long warehouseId,
                                               @Param("tenantId") Long tenantId);

    /**
     * 根据二级分类查询配置列表
     * 
     * @param levelTwo 二级分类
     * @param warehouseId 仓库ID（可选）
     * @param tenantId 租户ID（可选）
     * @return 配置列表
     */
    List<CfgConfigurationDO> selectByLevelTwo(@Param("levelTwo") String levelTwo,
                                               @Param("warehouseId") Long warehouseId,
                                               @Param("tenantId") Long tenantId);

    /**
     * 检查配置编号是否存在
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 存在的数量
     */
    int countByConfigNoAndWarehouseId(@Param("configNo") String configNo,
                                      @Param("warehouseId") Long warehouseId,
                                      @Param("excludeId") Long excludeId);

    /**
     * 批量插入配置
     * 
     * @param configList 配置列表
     * @return 插入数量
     */
    int batchInsert(@Param("configList") List<CfgConfigurationDO> configList);

    /**
     * 根据配置标签查询配置列表
     * 
     * @param configTag1 配置标签1
     * @param configTag2 配置标签2
     * @param warehouseId 仓库ID（可选）
     * @param tenantId 租户ID（可选）
     * @return 配置列表
     */
    List<CfgConfigurationDO> selectByConfigTags(@Param("configTag1") String configTag1,
                                                 @Param("configTag2") String configTag2,
                                                 @Param("warehouseId") Long warehouseId,
                                                 @Param("tenantId") Long tenantId);
}
