package com.accesscorporate.app.wms.server.dal.mapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;

import com.accesscorporate.app.wms.server.dal.entity.SequenceRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【cfg_sn_rule】的数据库操作Mapper
* @createDate 2025-02-05 17:37:56
* @Entity com.accesscorporate.app.wms.server.dal.entity.CfgSnRuleDO
*/
public interface CfgSnRuleMapper extends BaseMapper<SequenceRule> {

    List<SequenceRule> selectByRuleCode(@Param("ruleCode") String ruleCode);

    List<SequenceRule> selectByRuleName(@Param("ruleName") String ruleName);

    SequenceRule selectOneByRuleCode(@Param("ruleCode") String ruleCode);
}




