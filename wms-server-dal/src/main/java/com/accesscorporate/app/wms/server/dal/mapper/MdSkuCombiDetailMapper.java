package com.accesscorporate.app.wms.server.dal.mapper;

import com.accesscorporate.app.wms.server.dal.entity.MdSkuCombiDetailDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MdSkuCombiDetailMapper {

    int batchInsert(@Param("list") List<MdSkuCombiDetailDO> record);

    MdSkuCombiDetailDO selectByPrimaryKey(Long id);

    List<MdSkuCombiDetailDO> selectByCombiBarcode(@Param("combiBarcode") String combiBarcode);

    int deleteByCombiBarcode(@Param("combiBarcode") String combiBarcode);
}