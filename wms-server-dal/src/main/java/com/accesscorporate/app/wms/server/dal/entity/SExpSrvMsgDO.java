package com.accesscorporate.app.wms.server.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Date;

@Data
@TableName("s_exp_srv_msg")
public class SExpSrvMsgDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 字段: type, 类型
     */
    @TableField("type")
    private Long type;

    /**
     * 字段: exp_name, 接口名称
     */
    @TableField("exp_name")
    private String expName;

    /**
     * 字段: ref_id, 单据ID
     */
    @TableField("ref_id")
    private Long refId;

    /**
     * 字段: ref_no, 单据编号
     */
    @TableField("ref_no")
    private String refNo;

    /**
     * 字段: exp_args, 调用数据参数
     */
    @TableField("exp_args")
    private String expArgs;

    /**
     * 字段: status, 状态
     */
    @TableField("status")
    private Long status;

    /**
     * 字段: invoke_count, 调用次数
     */
    @TableField("invoke_count")
    private Long invokeCount;

    /**
     * 字段: fail_count, 失败次数
     */
    @TableField("fail_count")
    private Long failCount;

    /**
     * 字段: result_msg, 调用结果
     */
    @TableField("result_msg")
    private String resultMsg;

    /**
     * 字段: result_cause, 调用结果出错原因
     */
    @TableField("result_cause")
    private String resultCause;

    /**
     * 字段: create_time, 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 字段: invoke_time, 调用时间
     */
    @TableField("invoke_time")
    private Date invokeTime;

    /**
     * 字段: invoke_his, 调用历史记录
     */
    @TableField("invoke_his")
    private String invokeHis;

    /**
     * 字段: srv_log_id, LOG表ID
     */
    @TableField("srv_log_id")
    private Long srvLogId;

    /**
     * 字段: is_error, 是否有错误
     */
    @TableField("is_error")
    private Integer isError;

    /**
     * 字段: warehouse_id, 仓库ID
     */
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 字段: exp_data, 调用数据(持久化的DTO)
     */
    @TableField("exp_data")
    private String expData;

    @TableField("next_invoke_time")
    private Date nextInvokeTime;
}
