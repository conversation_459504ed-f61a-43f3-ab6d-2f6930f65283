package com.accesscorporate.app.wms.server.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 补货规则-Header
 *
 * <AUTHOR>
 * 2025/2/18  15:22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName(value = "rul_rotation_d")
public class RotationRuleHeaderDO {


    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 周转规则编码
     */
    private String rotationCode;

    /**
     * 周转规则描述
     */
    @TableField(value = "`rotation_descr`")
    private String rotationDesc;

    /**
     * 订单类型
     */
    @TableField(value = "`ordertype`")
    private String orderType;

    /**
     * 激活标志
     */
    private String activeFlag;

    /**
     * 创建时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人ID
     */
    private String createBy;

    /**
     * 最后修改时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 修改人ID
     */
    private String updateBy;

    /**
     * 是否删除（0: no, 1: yes）
     */
    private Integer isDeleted;

    /**
     * 版本锁
     */
    private Long version;

    /**
     * 是否优先分配RETURN库区库存（0：不优先分配，1：优先分配）
     */
    private Integer partitionCtrl;

    /**
     * 是否效期控制
     */
    private Integer expireControl;

    /**
     * 租户ID
     */
    private Long tenantId;


}
