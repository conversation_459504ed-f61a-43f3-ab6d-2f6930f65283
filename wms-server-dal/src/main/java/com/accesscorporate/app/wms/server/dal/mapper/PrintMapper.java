package com.accesscorporate.app.wms.server.dal.mapper;

import com.accesscorporate.app.wms.server.dal.dto.PrinterFilterQuery;
import com.accesscorporate.app.wms.server.dal.entity.PrintDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface PrintMapper {

    PrintDO selectById(Long id);

    PrintDO selectByCode(String code, Long warehouseId);

    List<PrintDO> selectByType(String type, Long warehouseId);

    List<PrintDO> selectAll();

    int insert(PrintDO printDO);

    int deleteByPrimaryKey(Long id);

    int updateById(PrintDO printDO);

    /**
     * 分页查询打印机信息
     * @param page 分页参数（current: 当前页, size: 每页数量）
     * @param filterQuery 查询条件（支持name/model/location模糊查询）
     * @return 分页结果
     */
    IPage<PrintDO> selectByPage(Page<PrintDO> page, @Param("params") PrinterFilterQuery filterQuery);
}