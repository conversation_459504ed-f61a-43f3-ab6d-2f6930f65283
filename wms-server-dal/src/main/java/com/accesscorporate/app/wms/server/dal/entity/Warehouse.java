package com.accesscorporate.app.wms.server.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.idanchuang.support.mysql.table.helper.comment.Entity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 仓库
 */
@Entity(tableName = "md_warehouse", check = true, comment = "仓库表")
@TableName("md_warehouse")
@Data
public class Warehouse implements Serializable {

    @TableId
    private Long id;

    /** 原始ID */
    private String originalId;

    /** 仓库名称 */
    private String warehouseName;

    /** 昵称 */
    private String nickName;

    /** 仓库编码 */
    private String warehouseCode;

    /** 省份ID */
    private Long provinceId;

    /** 区县ID */
    private Long countyId;

    /** 国家ID */
    private Long countryId;

    /** 城市ID */
    private Long cityId;

    /** 地址（交货地址） */
    private String addressName;

    /** 负责人ID */
    private Long managerId;

    /** 传真 */
    private String fax;

    /** 联系电话 */
    private String phone;

    /** 备注 */
    private String referenceInfo;

    /** 仓库群组email */
    private String groupEmail;

    /** 手机 */
    private String mobile;

    /** 联系人 */
    private String contactor;

    /** 仓库描述信息, po导出用 */
    private String warehouseDesc;

    /** 缩写，并用于系统间交互 */
    private String shortName;

    /** 仓库类型 0:正常, 1:药网, 3:SLC, 4:美特好, 5:跨境通, 6:保正天戈, 7:小雷补货车, 8:卓志 */
    private Integer warehouseType;

    /** 仓库面积 */
    private BigDecimal square;

    /** 容积 */
    private BigDecimal volume;

    /** 是否当前仓库 */
    private Integer runFlag;

    /** 1仓库 2门店 */
    private Integer type;

    /** 创立日期 */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /** 创建人ID */
    private String createBy;

    /** 最后修改日期 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /** 修改人ID */
    private String updateBy;

    /** 是否删除 0:no 1:yes */
    private Integer isDeleted;

    @Version
    /** 版本 */
    private Integer version;

    /** 仓库分组 */
    private String warehouseGroup;

    /** 企业在药监系统编码 */
    private Long corpId;

    /** 站点代码 */
    private String stationCode;

    /** 租户ID */
    private Integer tenantId;

    // Getters and Setters

    // 省略 getters 和 setters，您可以使用IDE自动生成
}