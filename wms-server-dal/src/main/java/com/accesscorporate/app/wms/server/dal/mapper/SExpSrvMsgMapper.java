package com.accesscorporate.app.wms.server.dal.mapper;

import com.accesscorporate.app.wms.server.dal.entity.SExpSrvMsgDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SExpSrvMsgMapper extends BaseMapper<SExpSrvMsgDO> {

    List<SExpSrvMsgDO> queryByTypeAndInvokeCount(
            @Param("typeList") List<Long> typeList,
            @Param("invokeCount") Long invokeCount,
            @Param("maxRows") Integer maxRows);
}
