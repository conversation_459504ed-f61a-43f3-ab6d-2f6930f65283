package com.accesscorporate.app.wms.server.dal.mapper;


import com.accesscorporate.app.wms.server.dal.dto.SkuCombiListQuery;
import com.accesscorporate.app.wms.server.dal.entity.MdSkuCombiHeaderDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MdSkuCombiHeaderMapper {

    Page<MdSkuCombiHeaderDO> list(@Param("page") Page<MdSkuCombiHeaderDO> page,
                                  @Param("query") SkuCombiListQuery query);

    List<MdSkuCombiHeaderDO> querySkuCombiByCombiBarcode(@Param("combiBarcode") String combiBarcode,
                                                         @Param("warehouseId") Long warehouseId);

    List<MdSkuCombiHeaderDO> querySkuCombiBySign(@Param("sign") String sign,
                                                 @Param("warehouseId") Long warehouseId);

    int insert(MdSkuCombiHeaderDO record);

    MdSkuCombiHeaderDO selectByPrimaryKey(Long id);

    int deleteByPrimaryKey(@Param("id") Long id);
}