package com.accesscorporate.app.wms.server.dal.entity;

import com.accesscorporate.app.wms.server.common.enums.SLoopEnum;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @TableName cfg_sn_rule
 */
@TableName(value = "cfg_sn_rule")
@Data
public class SequenceRule {
    /**
     * 规则代码
     */
    private String ruleCode;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 后缀
     */
    private String postfix;

    /**
     * 例子：WAVE{year}{month}{day}
     */
    private String prefix;

    /**
     * 长度
     */
    private Integer sLength;

    /**
     * 循环方式 0 日 1 月 2 年 3 不循环
     */
    private Integer sLoop;

    /**
     * 取最大单号的sql
     */
    private String sqlText;

    /**
     * 全局标记
     */
    private Integer isGlobal;

    /**
     * 逻辑删除
     */
    private Integer isDeleted;

    /**
     * 乐观锁控制
     */
    private Long version;
}