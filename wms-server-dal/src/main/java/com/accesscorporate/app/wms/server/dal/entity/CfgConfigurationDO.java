package com.accesscorporate.app.wms.server.dal.entity;

import com.accesscorporate.app.wms.server.dal.entity.base.TenantBaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统参数配置实体类
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cfg_configuration")
public class CfgConfigurationDO extends TenantBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 参数类型
     */
    private String configType;

    /**
     * 参数编号
     */
    private String configNo;

    /**
     * 参数名称
     */
    private String descrC;

    /**
     * 参数值
     */
    private Integer valueInt;

    /**
     * 参数字符值
     */
    private String valueString;

    /**
     * 参数时间值
     */
    private LocalDateTime valueDate;

    /**
     * 是否删除 0:否 1:是
     */
    @TableLogic
    private Integer isDeleted;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 配置项编码，生成代码试用
     */
    private String configCode;

    /**
     * 配置级别 0:库房，1:租户 2:全局
     */
    private Integer configLevel;

    /**
     * 值类型：0：邮箱 1：数字 2：网址 3：是否 4：字符串
     */
    private Integer valueType;

    /**
     * 配置标签1
     */
    private String configTag1;

    /**
     * 配置标签2
     */
    private String configTag2;

    /**
     * 一级分类
     */
    private String levelOne;

    /**
     * 二级分类
     */
    private String levelTwo;

    /**
     * 配置级别枚举
     */
    public enum ConfigLevel {
        WAREHOUSE(0, "库房"),
        TENANT(1, "租户"),
        GLOBAL(2, "全局");

        private final Integer value;
        private final String description;

        ConfigLevel(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static ConfigLevel fromValue(Integer value) {
            for (ConfigLevel level : values()) {
                if (level.getValue().equals(value)) {
                    return level;
                }
            }
            return null;
        }
    }

    /**
     * 值类型枚举
     */
    public enum ValueType {
        EMAIL(0, "邮箱"),
        NUMBER(1, "数字"),
        URL(2, "网址"),
        BOOLEAN(3, "是否"),
        STRING(4, "字符串");

        private final Integer value;
        private final String description;

        ValueType(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static ValueType fromValue(Integer value) {
            for (ValueType type : values()) {
                if (type.getValue().equals(value)) {
                    return type;
                }
            }
            return null;
        }
    }
}
