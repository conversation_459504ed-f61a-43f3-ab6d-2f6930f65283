package com.accesscorporate.app.wms.server.dal.mapper.goodspackage;

import com.accesscorporate.app.wms.server.dal.dto.GoodsPackageQueryParam;
import com.accesscorporate.app.wms.server.dal.entity.goodspackage.PackageSkuDO;
import com.accesscorporate.app.wms.server.dal.entity.goodspackage.MdPackageHDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-02-19 11:01
 * Description: 商品包装主标数据库操作
 */
@Repository
public interface MdPackageHMapper extends BaseMapper<MdPackageHDO> {

    /**
     * 分页查询商品包装信息
     */
    IPage<PackageSkuDO> pageQueryPackageSku(Page<?> page, @Param("param") GoodsPackageQueryParam param);


}
