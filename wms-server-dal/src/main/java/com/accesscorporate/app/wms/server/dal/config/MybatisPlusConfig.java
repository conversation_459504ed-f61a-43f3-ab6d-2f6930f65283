package com.accesscorporate.app.wms.server.dal.config;

import com.accesscorporate.app.wms.server.dal.intercept.SelectDataAuthInterceptor;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.toolkit.GlobalConfigUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.idanchuang.component.sequence.id.service.GeneralSeqIdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.plugin.Interceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 如需接入分布式自增ID序列服务配置, 请打开Configuration注解
 *
 * 如存在历史数据需要初始化id, 请看下方{@link MybatisPlusConfig#historyTableIdInit(GeneralSeqIdService)}注释
 * <AUTHOR>
 */
@Configuration
@MapperScan("com.accesscorporate.app.wms.server.dal.mapper")
@EnableTransactionManagement
@Slf4j
public class MybatisPlusConfig {

    @Value("${spring.application.name}")
    private String appId;


    @Bean
    public Interceptor mybatisPlusInterceptor() {
        return new SelectDataAuthInterceptor();
    }


    /**
     * @return 基于sequence服务的id生成器
     */
    @Bean
    public IdentifierGenerator identifierGenerator(GeneralSeqIdService generalSeqIdService) {
        return entity -> {
            String name;
            Class<?> clazz = entity.getClass();
            TableName tableName = clazz.getAnnotation(TableName.class);
            while (tableName == null) {
                clazz = clazz.getSuperclass();
                if (clazz == null || clazz.equals(Object.class)) {
                    break;
                }
                tableName = clazz.getAnnotation(TableName.class);
            }
            if (tableName != null) {
                name = tableName.value();
            } else {
                name = entity.getClass().getSimpleName();
            }
            return generalSeqIdService.nextId(appId, name);
        };
    }

    /**
     * 指定id生成策略
     * @param identifierGenerator 策略实现
     * @return ..
     */
    @Bean
    public GlobalConfig globalConfig(IdentifierGenerator identifierGenerator) {
        GlobalConfig conf = new GlobalConfig();
        conf.setIdentifierGenerator(identifierGenerator);
        IdWorker.setIdentifierGenerator(identifierGenerator);
        GlobalConfig defaults = GlobalConfigUtils.defaults();
        defaults.setIdentifierGenerator(identifierGenerator);
        return conf;
    }

    /**
     * 当存在历史数据时用来初始化ID序列服务中的id值, 否则nextId获取到的id是从0开始的
     *
     * 使用方式: 修改{@link HistoryTableIdInit}类中的run方法, 按注释编写要初始化的表与id值
     *
     * @param generalSeqIdService sequence服务RPC接口
     * @return 历史数据id初始化
     */
    @Bean
    public HistoryTableIdInit historyTableIdInit(GeneralSeqIdService generalSeqIdService) {
        return new HistoryTableIdInit(appId, generalSeqIdService);
    }

}

