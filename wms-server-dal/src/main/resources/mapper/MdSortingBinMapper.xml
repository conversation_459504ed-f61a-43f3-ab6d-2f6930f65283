<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.accesscorporate.app.wms.server.dal.mapper.MdSortingBinMapper">

    <update id="deleteByPrimaryKey" parameterType="com.accesscorporate.app.wms.server.dal.entity.MdSortingBinDO">
        update md_sorting_bin
        set is_deleted = 1
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据主键ID查询MdSortingBinDO对象 -->
    <select id="selectById" resultType="com.accesscorporate.app.wms.server.dal.entity.MdSortingBinDO">
        SELECT *
        FROM md_sorting_bin
        WHERE id = #{id}
    </select>

    <!-- 根据sortingBinNo查询MdSortingBinDO对象 -->
    <select id="selectBySortingBinNo" resultType="com.accesscorporate.app.wms.server.dal.entity.MdSortingBinDO">
        SELECT *
        FROM md_sorting_bin
        WHERE sortingZoneNbr = #{sortingBinNo}
    </select>

    <select id="selectSortingBinByNo" resultType="com.accesscorporate.app.wms.server.dal.entity.MdSortingBinDO">
        SELECT *
        FROM md_sorting_bin
        WHERE sortingZoneNbr = #{sortingBinNo} and warehouseId = #{warehouseId}
    </select>

    <select id="selectIdleSortingBin" resultType="java.lang.Long">
        SELECT o.id
        FROM md_sorting_bin o
        WHERE o.sortBinType != #{sortBinType}
            and o.isAvailable = 1
            and o.warehouseId = #{warehouseId}
        order by o.waveQty, o.sortingZoneNbr asc
    </select>

    <select id="selectIdleSortingBinByType" resultType="java.lang.Long">
        SELECT o.id
        FROM md_sorting_bin o
        WHERE o.sortBinType = #{sortBinType}
          and o.isAvailable = 1
          and o.warehouseId = #{warehouseId}
        order by o.waveQty, o.sortingZoneNbr asc
    </select>

    <select id="selectSortingBinIdByNo" resultType="java.lang.Long">
        SELECT o.id
        FROM md_sorting_bin o
        WHERE o.sortingZoneNbr != #{sortingBinNo}
          and o.isAvailable = 1
          and o.warehouseId = #{warehouseId}
    </select>

    <!-- 查询所有MdSortingBinDO对象 -->
    <select id="selectAll" resultType="com.accesscorporate.app.wms.server.dal.entity.MdSortingBinDO">
        SELECT *
        FROM md_sorting_bin
    </select>

    <!-- TODO 根据条件查询MdSortingBinDO对象列表 -->
    <select id="selectByCondition" resultType="com.accesscorporate.app.wms.server.dal.entity.MdSortingBinDO">
        select v1.*
        from md_sorting_bin v1
        where v1.is_deleted = 0
        <if test="query.sortingZoneName != null">
            and v1.sorting_zone_name =#{query.sortingZoneName}
        </if>
        <if test="query.sortingZoneNbr != null">
            and v1.sorting_zone_nbr =#{query.sortingZoneNbr}
        </if>
        <if test="query.isAvailable != null">
            and v1.is_available =#{query.isAvailable}
        </if>
        <if test="query.sortBinType != null">
            and v1.sort_bin_type = #{query.sortBinType}
        </if>
        <!--创建时间-倒序排序-->
        order by v1.create_time desc
    </select>

    <!-- 插入MdSortingBinDO对象 -->
    <insert id="insert" parameterType="com.accesscorporate.app.wms.server.dal.entity.MdSortingBinDO">
        INSERT INTO md_sorting_bin (id,
                                    warehouse_id,
                                    sorting_zone_name,
                                    sorting_bin_qty,
                                    is_available,
                                    sorting_zone_nbr,
                                    sorting_zone_status,
                                    create_time,
                                    create_by,
                                    update_time,
                                    update_by,
                                    is_deleted,
                                    version,
                                    wave_qty,
                                    sort_bin_type,
                                    invoice_printer_id,
                                    support_wcs)
        VALUES (#{id},
                #{warehouseId},
                #{sortingZoneName},
                #{sortingBinQty},
                #{isAvailable},
                #{sortingZoneNbr},
                #{sortingZoneStatus},
                #{createTime},
                #{createBy},
                #{updateTime},
                #{updateBy},
                #{isDeleted},
                #{version},
                #{waveQty},
                #{sortBinType},
                #{invoicePrinterId},
                #{supportWcs})
    </insert>

    <!-- TODO 根据主键ID更新MdSortingBinDO对象 -->
    <update id="updateById" parameterType="com.accesscorporate.app.wms.server.dal.entity.MdSortingBinDO">
        UPDATE md_sorting_bin
        SET name= #{name},
            sorting_zone_name=#{sortingZoneName},
            sorting_bin_qty=#{sortingBinQty},
            is_available=#{isAvailable},
            sorting_zone_nbr=#{sortingZoneNbr},
            sorting_zone_status=#{sortingZoneStatus},
            update_time=now(),
            update_by=#{updateBy},
            is_deleted=#{isDeleted},
            version=#{version},
            wave_qty=#{waveQty},
            sort_bin_type= #{sortBinType},
            invoice_printer_id=#{invoicePrinterId},
            support_wcs=#{supportWcs}
        WHERE id = #{id}
    </update>

    <!-- 根据sortingZoneNbr、warehouseId和id查询MdSortingBinDO对象 -->
    <select id="selectBySortingZoneNbrAndWarehouseIdAndId" resultType="com.accesscorporate.app.wms.server.dal.entity.MdSortingBinDO">
        SELECT
            id,
            warehouse_id,
            sorting_zone_name,
            sorting_bin_qty,
            is_available,
            sorting_zone_nbr,
            sorting_zone_status,
            create_time,
            create_by,
            update_time,
            update_by,
            is_deleted,
            version,
            wave_qty,
            sort_bin_type,
            invoice_printer_id,
            support_wcs
        FROM
        md_sorting_bin
        WHERE
        sorting_zone_nbr = #{sortingZoneNbr}
        AND warehouse_id = #{warehouseId}
        AND id = #{id}
    </select>

    <update id="updateAvailableStatusByIds">
        UPDATE md_sorting_bin
        SET is_available = 0
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_available = 1
        AND is_deleted = 0
    </update>

</mapper>