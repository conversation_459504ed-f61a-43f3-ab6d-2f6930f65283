<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.accesscorporate.app.wms.server.dal.mapper.MdSkuCombiDetailMapper">
    <resultMap id="BaseResultMap" type="com.accesscorporate.app.wms.server.dal.entity.MdSkuCombiDetailDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="combi_barcode" jdbcType="VARCHAR" property="combiBarcode"/>
        <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
        <result column="barcode" jdbcType="VARCHAR" property="barcode"/>
        <result column="goods_grade" jdbcType="VARCHAR" property="goodsGrade"/>
        <result column="total" jdbcType="INTEGER" property="total"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , combi_barcode, sku_id, barcode, goods_grade, total, create_time, create_by, is_deleted,
    warehouse_id, update_time, update_by, version
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from md_sku_combi_detail
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByCombiBarcode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from md_sku_combi_detail
        where is_deleted = 0 and combi_barcode = #{combiBarcode,jdbcType=VARCHAR}
    </select>

    <insert id="batchInsert" keyColumn="id" keyProperty="id"
            parameterType="com.accesscorporate.app.wms.server.dal.entity.MdSkuCombiDetailDO" useGeneratedKeys="true">
        insert into md_sku_combi_detail (combi_barcode, sku_id, barcode,
        goods_grade, total, create_time,
        create_by, is_deleted, warehouse_id,
        update_time, update_by, version)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.combiBarcode,jdbcType=VARCHAR},
            #{item.skuId,jdbcType=BIGINT},
            #{item.barcode,jdbcType=VARCHAR},
            #{item.goodsGrade,jdbcType=VARCHAR},
            #{item.total,jdbcType=INTEGER},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createBy,jdbcType=VARCHAR},
            #{item.isDeleted,jdbcType=INTEGER},
            #{item.warehouseId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=VARCHAR},
            #{item.version,jdbcType=INTEGER})
        </foreach>
    </insert>

    <update id="deleteByCombiBarcode" parameterType="com.accesscorporate.app.wms.server.dal.entity.MdSkuCombiDetailDO">
        update md_sku_combi_detail
        set is_deleted = 1
        where combi_barcode = #{combiBarcode,jdbcType=VARCHAR}
    </update>

</mapper>