<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.accesscorporate.app.wms.server.dal.mapper.CfgCodeMasterMapper">

    <resultMap id="BaseResultMap" type="com.accesscorporate.app.wms.server.dal.entity.CfgCodeMasterDO">
            <id property="masterCode" column="master_code" />
            <result property="groupName" column="group_name" />
            <result property="masterName" column="master_name" />
            <result property="codeType" column="code_type" />
            <result property="description" column="description" />
            <result property="displayInd" column="display_ind" />
            <result property="insertInd" column="insert_ind" />
            <result property="updateInd" column="update_ind" />
    </resultMap>

    <sql id="Base_Column_List">
        master_code,group_name,master_name,code_type,description,display_ind,
        insert_ind,update_ind
    </sql>
    <select id="findCodeCategoryGroupNames" resultType="java.lang.String">
        select distinct group_name from cfg_code_master
    </select>
    <select id="getCodeMasters" resultType="com.accesscorporate.app.wms.server.dal.entity.CfgCodeMasterDO">
        select
        <include refid="Base_Column_List"/>
        from cfg_code_master
        where group_name = #{groupName}
    </select>
</mapper>
