<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.accesscorporate.app.wms.server.dal.mapper.PrintMapper">

    <select id="selectById" resultType="com.accesscorporate.app.wms.server.dal.entity.PrintDO">
        SELECT * FROM print WHERE id = #{id}
    </select>

    <select id="selectByCode" resultType="com.accesscorporate.app.wms.server.dal.entity.PrintDO">
        SELECT * FROM print
        WHERE code = #{code} and warehouseId = #{warehouseId}
    </select>

    <select id="selectByType" resultType="com.accesscorporate.app.wms.server.dal.entity.PrintDO">
        SELECT * FROM print
        WHERE status = 1 and type = #{type} and warehouseId = #{warehouseId}
    </select>

    <select id="selectAll" resultType="com.accesscorporate.app.wms.server.dal.entity.PrintDO">
        SELECT * FROM print
    </select>

    <insert id="insert" parameterType="com.accesscorporate.app.wms.server.dal.entity.PrintDO">
        INSERT INTO print (name, model, location) VALUES (#{name}, #{model}, #{location})
    </insert>

    <update id="updateById" parameterType="com.accesscorporate.app.wms.server.dal.entity.PrintDO">
        UPDATE print SET name = #{name}, model = #{model}, location = #{location} WHERE id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="long">
        DELETE FROM print WHERE id = #{id}
    </delete>

    <!-- 分页查询打印机信息 -->
    <select id="selectByPage" resultType="com.accesscorporate.app.wms.server.dal.entity.PrintDO">
        SELECT * FROM print
        <where>
            <if test="params.status != null and params.status != ''">
                AND status = #{params.name}
            </if>
            <if test="params.code != null and params.code != ''">
                AND code = #{params.code}
            </if>
            <if test="params.warehouseId != null and params.warehouseId != ''">
                AND warehouseId = #{params.warehouseId}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>