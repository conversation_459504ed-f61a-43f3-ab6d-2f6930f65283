<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.accesscorporate.app.wms.server.dal.mapper.AddressMapper">
    <!-- 分页查询地址（原getByAddCodeAndPId逻辑） -->

    <select id="queryByFilter" resultType="com.accesscorporate.app.wms.server.dal.entity.AddressDO">
        SELECT *
        FROM wms_address
        WHERE is_deleted = 0
        and parent_code != 0
        <if test="filter.provinceCode != null and  filter.provinceCode != '' ">
            AND address_code = #{filter.provinceCode}
        </if>
        <if test="filter.countryCode != null and filter.countryCode != '' ">
            AND parent_code = #{filter.countryCode}
        </if>
        ORDER BY CONVERT(address_code, SIGNED)
    </select>

    <!-- 根据父编码查询地址列表 -->
    <select id="getListByParentCode" resultType="com.accesscorporate.app.wms.server.dal.entity.AddressDO">
        SELECT *
        FROM wms_address
        WHERE parent_code = #{parentCode}
          and is_deleted = 0
    </select>

    <!-- 根据名称查询地址 -->
    <select id="getByName" resultType="com.accesscorporate.app.wms.server.dal.entity.AddressDO">
        SELECT *
        FROM wms_address
        WHERE name = #{name}
          and is_deleted = 0 LIMIT 1
    </select>


    <select id="selectById" resultType="com.accesscorporate.app.wms.server.dal.entity.AddressDO">
        SELECT *
        FROM wms_address
        WHERE id = #{id}
          and is_deleted = 0
    </select>

    <!-- 根据地址编码查询地址 -->
    <select id="getByAddressCode" resultType="com.accesscorporate.app.wms.server.dal.entity.AddressDO">
        SELECT *
        FROM wms_address
        WHERE address_code = #{addressCode}
          and is_deleted = 0 LIMIT 1
    </select>

    <!-- 插入地址信息 -->
    <insert id="insert" parameterType="com.accesscorporate.app.wms.server.dal.entity.AddressDO">
        INSERT INTO wms_address
        (address_code, name, parent_code, create_time, update_time, version,
         create_by, is_deleted, is_oversea, sort_name, tenant_id, transport_wendy, type, update_by)
        VALUES (#{addressCode}, #{name}, #{parentCode}, #{createTime}, #{updateTime}, 1,
                #{createBy}, #{isDeleted}, #{isOversea}, #{sortName}, #{tenantId}, #{transportWendy}, #{type},
                #{updateBy})
    </insert>

    <!-- 根据主键ID更新地址信息 -->
    <update id="updateById" parameterType="com.accesscorporate.app.wms.server.dal.entity.AddressDO">
        UPDATE wms_address
        SET address_code    = #{addressCode},
            name            = #{name},
            parent_code     = #{parentCode},
            transport_wendy = #{transportWendy},
            update_time= now(),
            version         = version + 1
        WHERE id = #{id}
    </update>

    <!-- 根据主键ID逻辑删除地址信息 -->
    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE wms_address
        SET is_deleted = 1,
            update_time=now(),
            version    = version + 1
        WHERE id = #{id}
    </update>

    <!-- 根据主键ID恢复逻辑删除的地址信息 -->
    <update id="recoverById" parameterType="java.lang.Long">
        UPDATE wms_address
        SET is_deleted = 0,
            update_time=now(),
            version    = version + 1
        WHERE id = #{id}
    </update>

</mapper>