<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.accesscorporate.app.wms.server.dal.mapper.MdSkuCombiHeaderMapper">
    <resultMap id="BaseResultMap" type="com.accesscorporate.app.wms.server.dal.entity.MdSkuCombiHeaderDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="combi_barcode" jdbcType="VARCHAR" property="combiBarcode"/>
        <result column="sign" jdbcType="VARCHAR" property="sign"/>
        <result column="sku_count" jdbcType="INTEGER" property="skuCount"/>
        <result column="sku_total" jdbcType="INTEGER" property="skuTotal"/>
        <result column="loc_code" jdbcType="VARCHAR" property="locCode"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
        <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , combi_barcode, sign, sku_count, sku_total, loc_code, create_time, create_by,
    is_deleted, warehouse_id, update_time, update_by, version
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from md_sku_combi_header
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="list" resultType="com.accesscorporate.app.wms.server.dal.entity.MdSkuCombiHeaderDO">
        select
        v1.*
        from md_sku_combi_header v1
        inner join md_sku_combi_detail v2 on v1.combi_barcode = v2.combi_barcode
        where v1.is_deleted = 0
        and v2.is_deleted = 0
        <!--组合条码批量查询-->
        <if test="query.combiBarcodeList != null and query.combiBarcodeList.size > 0">
            and v1.combi_barcode in
            <foreach collection="query.combiBarcodeList" separator="," index="index" item="combiBarcode"
                     open="(" close=")">
                #{combiBarcode}
            </foreach>
        </if>
        <!--条码批量查询-->
        <if test="query.barcodeList != null and query.barcodeList.size > 0">
            and v2.barcode in
            <foreach collection="query.barcodeList" separator="," index="index" item="barcode"
                     open="(" close=")">
                #{barcode}
            </foreach>
        </if>
        <!--库位编码批量查询-->
        <if test="query.locCodeList != null and query.locCodeList.size > 0">
            and v1.loc_code in
            <foreach collection="query.locCodeList" separator="," index="index" item="locCode"
                     open="(" close=")">
                #{locCode}
            </foreach>
        </if>
        <!--创建时间-起始查询-->
        <if test="query.createTimeFm != null">
            and v1.create_time <![CDATA[>=]]> #{query.createTimeFm,jdbcType=TIMESTAMP}
        </if>
        <!--创建时间-截止查询-->
        <if test="query.createTimeTo != null">
            and v1.create_time <![CDATA[<=]]> #{query.createTimeTo,jdbcType=TIMESTAMP}
        </if>
        <!--创建时间-倒序排序-->
        order by v1.create_time desc
    </select>

    <select id="querySkuCombiByCombiBarcode"
            resultType="com.accesscorporate.app.wms.server.dal.entity.MdSkuCombiHeaderDO">
        select
        <include refid="Base_Column_List"/>
        from md_sku_combi_header
        where is_deleted = 0
        and warehouse_id = #{warehouseId}
        and combi_barcode = #{combiBarcode}
    </select>

    <select id="querySkuCombiBySign" resultType="com.accesscorporate.app.wms.server.dal.entity.MdSkuCombiHeaderDO">
        select
        <include refid="Base_Column_List"/>
        from md_sku_combi_header
        where is_deleted = 0
        and warehouse_id = #{warehouseId}
        and sign = #{sign}
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.accesscorporate.app.wms.server.dal.entity.MdSkuCombiHeaderDO" useGeneratedKeys="true">
        insert into md_sku_combi_header (combi_barcode, sign, sku_count,
                                         sku_total, loc_code, create_time,
                                         create_by, is_deleted, warehouse_id,
                                         update_time, update_by, version)
        values (#{combiBarcode,jdbcType=VARCHAR}, #{sign,jdbcType=VARCHAR}, #{skuCount,jdbcType=INTEGER},
                #{skuTotal,jdbcType=INTEGER}, #{locCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{createBy,jdbcType=VARCHAR}, #{isDeleted,jdbcType=INTEGER}, #{warehouseId,jdbcType=BIGINT},
                #{updateTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER})
    </insert>

    <update id="deleteByPrimaryKey" parameterType="com.accesscorporate.app.wms.server.dal.entity.MdSkuCombiHeaderDO">
        update md_sku_combi_header
        set is_deleted = 1
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>