<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.accesscorporate.app.wms.server.dal.mapper.goodspackage.MdPackageHMapper">

    <select id="pageQueryPackageSku"
            parameterType="com.accesscorporate.app.wms.server.dal.dto.GoodsPackageQueryParam"
            resultType="com.accesscorporate.app.wms.server.dal.entity.goodspackage.PackageSkuDO">
        SELECT
        mph.id AS packageId,
        mph.PKG_CODE AS pkgCode,
        mph.PKG_DESC AS pkgDesc,
        ms.product_code AS productCode,
        ms.ean13 AS barCode,
        ms.product_cname AS productCname
        FROM md_package_h mph
        JOIN md_sku ms ON mph.SKU_ID = ms.id
        WHERE 1 = 1

        <!-- 按商品编码查询 -->
        <if test="param.productCode != null and param.productCode != ''">
            AND ms.product_code = #{param.productCode}
        </if>

        <!-- 按商品名称模糊查询 -->
        <if test="param.productCname != null and param.productCname != ''">
            AND ms.product_cname LIKE CONCAT('%', #{param.productCname}, '%')
        </if>

        <!-- 按条形码查询 -->
        <if test="param.barCode != null and param.barCode != ''">
            AND ms.ean13 = #{param.barCode}
        </if>
    </select>
</mapper>
