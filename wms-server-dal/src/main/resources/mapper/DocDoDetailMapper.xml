<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.accesscorporate.app.wms.server.dal.mapper.DocDoDetailMapper">

    <!-- 根据订单头ID和仓库ID查询明细 -->
    <select id="selectByDoHeaderIdAndWarehouseId" resultType="com.accesscorporate.app.wms.server.dal.entity.DocDoDetail">
        SELECT *
        FROM doc_do_detail
        WHERE do_header_id = #{doHeaderId}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据商品ID和仓库ID查询明细 -->
    <select id="selectBySkuIdAndWarehouseId" resultType="com.accesscorporate.app.wms.server.dal.entity.DocDoDetail">
        SELECT *
        FROM doc_do_detail
        WHERE sku_id = #{skuId}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据明细状态统计数量 -->
    <select id="countByLinestatus" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM doc_do_detail
        WHERE linestatus = #{linestatus}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
    </select>

    <!-- 根据包装ID查询明细 -->
    <select id="selectByPackageId" resultType="com.accesscorporate.app.wms.server.dal.entity.DocDoDetail">
        SELECT *
        FROM doc_do_detail
        WHERE package_id = #{packageId}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据原始头ID查询明细 -->
    <select id="selectByOrigHeaderId" resultType="com.accesscorporate.app.wms.server.dal.entity.DocDoDetail">
        SELECT *
        FROM doc_do_detail
        WHERE ORIG_HEADER_ID = #{origHeaderId}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据批次编码查询明细 -->
    <select id="selectByLotNo" resultType="com.accesscorporate.app.wms.server.dal.entity.DocDoDetail">
        SELECT *
        FROM doc_do_detail
        WHERE lot_no = #{lotNo}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据供应商ID查询明细 -->
    <select id="selectBySupplier" resultType="com.accesscorporate.app.wms.server.dal.entity.DocDoDetail">
        SELECT *
        FROM doc_do_detail
        WHERE lotatt04 = #{supplierId}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据货主ID查询明细 -->
    <select id="selectByCargoOwner" resultType="com.accesscorporate.app.wms.server.dal.entity.DocDoDetail">
        SELECT *
        FROM doc_do_detail
        WHERE lotatt06 = #{cargoOwnerId}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据拣货区查询明细 -->
    <select id="selectByPickzone" resultType="com.accesscorporate.app.wms.server.dal.entity.DocDoDetail">
        SELECT *
        FROM doc_do_detail
        WHERE pickzone = #{pickzone}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据多个明细状态查询 -->
    <select id="selectByLinestatusList" resultType="com.accesscorporate.app.wms.server.dal.entity.DocDoDetail">
        SELECT *
        FROM doc_do_detail
        WHERE warehouse_id = #{warehouseId}
          AND is_deleted = 0
          AND linestatus IN
        <foreach collection="linestatusList" item="linestatus" open="(" separator="," close=")">
            #{linestatus}
        </foreach>
        ORDER BY create_time DESC
    </select>

    <!-- 根据时间范围查询明细 -->
    <select id="selectByTimeRange" resultType="com.accesscorporate.app.wms.server.dal.entity.DocDoDetail">
        SELECT *
        FROM doc_do_detail
        WHERE warehouse_id = #{warehouseId}
          AND is_deleted = 0
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据数量范围查询明细 -->
    <select id="selectByQtyRange" resultType="com.accesscorporate.app.wms.server.dal.entity.DocDoDetail">
        SELECT *
        FROM doc_do_detail
        WHERE warehouse_id = #{warehouseId}
          AND is_deleted = 0
        <if test="minExpectedQty != null">
            AND expected_qty >= #{minExpectedQty}
        </if>
        <if test="maxExpectedQty != null">
            AND expected_qty &lt;= #{maxExpectedQty}
        </if>
        <if test="minPickedQty != null">
            AND picked_qty >= #{minPickedQty}
        </if>
        <if test="maxPickedQty != null">
            AND picked_qty &lt;= #{maxPickedQty}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 批量更新明细状态 -->
    <update id="updateLinestatusByDoHeaderId">
        UPDATE doc_do_detail
        SET linestatus = #{newLinestatus},
            update_time = NOW(),
            update_by = #{updateBy}
        WHERE do_header_id = #{doHeaderId}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
    </update>

    <!-- 批量更新明细数量 -->
    <update id="updateQtyByIds">
        UPDATE doc_do_detail
        SET picked_qty = #{pickedQty},
            sorted_qty = #{sortedQty},
            packed_qty = #{packedQty},
            shipped_qty = #{shippedQty},
            update_time = NOW(),
            update_by = #{updateBy}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
    </update>

    <!-- 统计明细汇总信息 -->
    <select id="selectSummaryByDoHeaderId" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_count,
            SUM(expected_qty) as total_expected_qty,
            SUM(allocated_qty) as total_allocated_qty,
            SUM(picked_qty) as total_picked_qty,
            SUM(sorted_qty) as total_sorted_qty,
            SUM(packed_qty) as total_packed_qty,
            SUM(shipped_qty) as total_shipped_qty,
            SUM(price * expected_qty) as total_amount
        FROM doc_do_detail
        WHERE do_header_id = #{doHeaderId}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
    </select>

</mapper>
