<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.accesscorporate.app.wms.server.dal.mapper.ExpAsnHeaderMapper">

    <select id="selectByIdAndWarehouseId" resultType="com.accesscorporate.app.wms.server.dal.entity.ExpAsnHeaderDO">
        SELECT *
        FROM doc_asn_header
        WHERE id = #{id}
          AND warehouse_id = #{warehouseId}
    </select>


</mapper>
