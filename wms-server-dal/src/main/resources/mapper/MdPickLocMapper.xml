<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.accesscorporate.app.wms.server.dal.mapper.pickloc.MdPickLocMapper">

    <select id="pickLocPageQuery"
            parameterType="com.accesscorporate.app.wms.server.dal.dto.PickLocQueryParam"
            resultType="com.accesscorporate.app.wms.server.dal.entity.pickloc.PickLocPageQueryResponseDO">
        SELECT
        mdp.id pickLocId,
        mdp.uplimit upLimit,
        mdp.lowerlimit lowerLimit,
        mdp.minimum_rpl_qty,
        mdp.warehouse_id,
        mdp.uom,
        ms.product_cname,
        ms.product_ename,
        ms.product_code,
        ms.ean13,
        ml.loc_code,
        ml.loc_type,
        ml.package_type,
        mp.partion_code
        FROM md_pick_loc mdp
        JOIN md_sku ms ON mdp.sku_id=ms.id
        JOIN md_location ml ON mdp.loc_id=ml.id
        JOIN md_partition mp ON mp.id=ml.partition_id
        WHERE mdp.warehouse_id=#{param.warehouseId} and mdp.is_deleted=0

        <!-- 按商品编码查询 -->
        <if test="param.productCode != null and param.productCode != ''">
            AND ms.product_code = #{param.productCode}
        </if>

        <!-- 按商品名称模糊查询 -->
        <if test="param.productCname != null and param.productCname != ''">
            AND ms.product_cname LIKE CONCAT('%', #{param.productCname}, '%')
        </if>

        <!-- 按条形码查询 -->
        <if test="param.barCode != null and param.barCode != ''">
            AND ms.ean13 = #{param.barCode}
        </if>

        <!-- 按库位编码查询 -->
        <if test="param.locCode != null and param.locCode != ''">
            AND ml.loc_code = #{param.locCode}
        </if>

        <!-- 按库区搜索 -->
        <if test="param.partitionId != null and param.partitionId != ''">
            AND mp.id = #{param.partitionId}
        </if>

    </select>


    <!-- 批量新增-->
    <insert id="batchInsertMdPickLoc" parameterType="java.util.List">
        INSERT INTO md_pick_loc
        (sku_id,
        loc_id,
        uplimit,
        lowerlimit,
        minimum_rpl_qty,
        uom,
        create_by,
        warehouse_id)
        VALUES
        <foreach collection="list" item="detail" index="index" separator=",">
            (#{detail.skuId},
            #{detail.locId},
            #{detail.uplimit},
            #{detail.lowerlimit},
            #{detail.minimumRplQty},
            #{detail.uom},
            #{detail.createBy},
            #{detail.warehouseId})
        </foreach>
    </insert>


</mapper>
