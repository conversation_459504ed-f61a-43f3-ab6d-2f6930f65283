<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.accesscorporate.app.wms.server.dal.mapper.DocDoHeaderMapper">

    <!-- 根据ID和仓库ID查询 -->
    <select id="selectByIdAndWarehouseId" resultType="com.accesscorporate.app.wms.server.dal.entity.DocDoHeader">
        SELECT *
        FROM doc_do_header
        WHERE id = #{id}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
    </select>

    <!-- 根据发货单号和仓库ID查询 -->
    <select id="selectByDoNoAndWarehouseId" resultType="com.accesscorporate.app.wms.server.dal.entity.DocDoHeader">
        SELECT *
        FROM doc_do_header
        WHERE do_no = #{doNo}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
    </select>

    <!-- 根据原始ID和仓库ID查询 -->
    <select id="selectByOrigIdAndWarehouseId" resultType="com.accesscorporate.app.wms.server.dal.entity.DocDoHeader">
        SELECT *
        FROM doc_do_header
        WHERE ORIG_ID = #{origId}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
    </select>

    <!-- 根据状态统计数量 -->
    <select id="countByStatus" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM doc_do_header
        WHERE status = #{status}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
    </select>

    <!-- 根据订单类型统计数量 -->
    <select id="countByDoType" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM doc_do_header
        WHERE do_type = #{doType}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
    </select>

    <!-- 根据时间范围查询 -->
    <select id="selectByTimeRange" resultType="com.accesscorporate.app.wms.server.dal.entity.DocDoHeader">
        SELECT *
        FROM doc_do_header
        WHERE warehouse_id = #{warehouseId}
          AND is_deleted = 0
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据多个状态查询 -->
    <select id="selectByStatusList" resultType="com.accesscorporate.app.wms.server.dal.entity.DocDoHeader">
        SELECT *
        FROM doc_do_header
        WHERE warehouse_id = #{warehouseId}
          AND is_deleted = 0
          AND status IN
        <foreach collection="statusList" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
        ORDER BY create_time DESC
    </select>

    <!-- 根据波次ID批量更新状态 -->
    <update id="updateStatusByWaveId">
        UPDATE doc_do_header
        SET status = #{newStatus},
            update_time = NOW(),
            update_by = #{updateBy}
        WHERE wave_id = #{waveId}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
    </update>

</mapper>
