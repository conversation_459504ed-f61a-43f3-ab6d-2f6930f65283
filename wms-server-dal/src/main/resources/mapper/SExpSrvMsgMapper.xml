<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.accesscorporate.app.wms.server.dal.mapper.SExpSrvMsgMapper">

    <select id="queryByTypeAndInvokeCount" resultType="com.accesscorporate.app.wms.server.dal.entity.SExpSrvMsgDO">
        SELECT *
        FROM s_exp_srv_msg
        WHERE type IN
        <foreach collection="typeList" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        AND invoke_count = #{invokeCount}
        <if test="maxRows != null">
            LIMIT #{maxRows}
        </if>
    </select>

</mapper>
