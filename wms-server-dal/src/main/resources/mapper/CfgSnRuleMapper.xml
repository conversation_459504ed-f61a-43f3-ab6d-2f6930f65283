<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.accesscorporate.app.wms.server.dal.mapper.CfgSnRuleMapper">

    <resultMap id="BaseResultMap" type="com.accesscorporate.app.wms.server.dal.entity.SequenceRule">
            <id property="ruleCode" column="rule_code" />
            <result property="ruleName" column="rule_name" />
            <result property="postfix" column="postfix" />
            <result property="prefix" column="prefix" />
            <result property="sLength" column="s_length" />
            <result property="sLoop" column="s_loop" />
            <result property="sqlText" column="sql_text" />
            <result property="isGlobal" column="is_global" />
            <result property="isDeleted" column="is_deleted" />
            <result property="version" column="version" />
    </resultMap>

    <sql id="Base_Column_List">
        rule_code,rule_name,postfix,prefix,s_length,s_loop,
        sql_text,is_global,is_deleted,version
    </sql>
    <select id="selectByRuleCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cfg_sn_rule
        where
        rule_code = #{ruleCode,jdbcType=VARCHAR}
    </select>
    <select id="selectByRuleName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cfg_sn_rule
        where
        rule_name = #{ruleName,jdbcType=VARCHAR}
    </select>
    <select id="selectOneByRuleCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cfg_sn_rule
        where
        rule_code = #{ruleCode,jdbcType=VARCHAR}
    </select>
</mapper>
