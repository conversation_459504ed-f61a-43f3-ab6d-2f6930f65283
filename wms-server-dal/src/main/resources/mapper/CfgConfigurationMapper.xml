<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.accesscorporate.app.wms.server.dal.mapper.CfgConfigurationMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="com.accesscorporate.app.wms.server.dal.entity.CfgConfigurationDO">
        <id column="id" property="id" />
        <result column="config_type" property="configType" />
        <result column="config_no" property="configNo" />
        <result column="descr_c" property="descrC" />
        <result column="value_int" property="valueInt" />
        <result column="value_string" property="valueString" />
        <result column="value_date" property="valueDate" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="version" property="version" />
        <result column="is_deleted" property="isDeleted" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="config_code" property="configCode" />
        <result column="config_level" property="configLevel" />
        <result column="value_type" property="valueType" />
        <result column="config_tag1" property="configTag1" />
        <result column="config_tag2" property="configTag2" />
        <result column="level_one" property="levelOne" />
        <result column="level_two" property="levelTwo" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用SQL片段 -->
    <sql id="Base_Column_List">
        id, config_type, config_no, descr_c, value_int, value_string, value_date,
        create_time, create_by, update_time, update_by, version, is_deleted,
        warehouse_id, config_code, config_level, value_type, config_tag1,
        config_tag2, level_one, level_two, tenant_id
    </sql>

    <!-- 根据配置编号和仓库ID查询配置 -->
    <select id="selectByConfigNoAndWarehouseId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cfg_configuration
        WHERE config_no = #{configNo}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
        ORDER BY update_time DESC
        LIMIT 1
    </select>

    <!-- 根据配置编号和租户ID查询配置 -->
    <select id="selectByConfigNoAndTenantId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cfg_configuration
        WHERE config_no = #{configNo}
          AND tenant_id = #{tenantId}
          AND warehouse_id IS NULL
          AND is_deleted = 0
        ORDER BY update_time DESC
        LIMIT 1
    </select>

    <!-- 根据配置编号查询全局配置 -->
    <select id="selectGlobalByConfigNo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cfg_configuration
        WHERE config_no = #{configNo}
          AND warehouse_id IS NULL
          AND tenant_id IS NULL
          AND is_deleted = 0
        ORDER BY update_time DESC
        LIMIT 1
    </select>

    <!-- 根据配置级别查询配置列表 -->
    <select id="selectByConfigLevel" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cfg_configuration
        WHERE config_level = #{configLevel}
          AND is_deleted = 0
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
        <if test="tenantId != null">
            AND tenant_id = #{tenantId}
        </if>
        ORDER BY config_no, update_time DESC
    </select>

    <!-- 根据配置类型查询配置列表 -->
    <select id="selectByConfigType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cfg_configuration
        WHERE config_type = #{configType}
          AND is_deleted = 0
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
        <if test="tenantId != null">
            AND tenant_id = #{tenantId}
        </if>
        ORDER BY config_no, update_time DESC
    </select>

    <!-- 根据一级分类查询配置列表 -->
    <select id="selectByLevelOne" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cfg_configuration
        WHERE level_one = #{levelOne}
          AND is_deleted = 0
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
        <if test="tenantId != null">
            AND tenant_id = #{tenantId}
        </if>
        ORDER BY level_two, config_no, update_time DESC
    </select>

    <!-- 根据二级分类查询配置列表 -->
    <select id="selectByLevelTwo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cfg_configuration
        WHERE level_two = #{levelTwo}
          AND is_deleted = 0
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
        <if test="tenantId != null">
            AND tenant_id = #{tenantId}
        </if>
        ORDER BY config_no, update_time DESC
    </select>

    <!-- 检查配置编号是否存在 -->
    <select id="countByConfigNoAndWarehouseId" resultType="int">
        SELECT COUNT(1)
        FROM cfg_configuration
        WHERE config_no = #{configNo}
          AND warehouse_id = #{warehouseId}
          AND is_deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 批量插入配置 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO cfg_configuration (
            config_type, config_no, descr_c, value_int, value_string, value_date,
            create_time, create_by, update_time, update_by, version, is_deleted,
            warehouse_id, config_code, config_level, value_type, config_tag1,
            config_tag2, level_one, level_two, tenant_id
        ) VALUES
        <foreach collection="configList" item="item" separator=",">
            (
                #{item.configType}, #{item.configNo}, #{item.descrC}, #{item.valueInt},
                #{item.valueString}, #{item.valueDate}, #{item.createTime}, #{item.createBy},
                #{item.updateTime}, #{item.updateBy}, #{item.version}, #{item.isDeleted},
                #{item.warehouseId}, #{item.configCode}, #{item.configLevel}, #{item.valueType},
                #{item.configTag1}, #{item.configTag2}, #{item.levelOne}, #{item.levelTwo},
                #{item.tenantId}
            )
        </foreach>
    </insert>

    <!-- 根据配置标签查询配置列表 -->
    <select id="selectByConfigTags" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cfg_configuration
        WHERE is_deleted = 0
        <if test="configTag1 != null and configTag1 != ''">
            AND config_tag1 = #{configTag1}
        </if>
        <if test="configTag2 != null and configTag2 != ''">
            AND config_tag2 = #{configTag2}
        </if>
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
        <if test="tenantId != null">
            AND tenant_id = #{tenantId}
        </if>
        ORDER BY config_no, update_time DESC
    </select>

</mapper>
