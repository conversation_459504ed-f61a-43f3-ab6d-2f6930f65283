package com.accesscorporate.app.wms.server.common.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR> liudongliang
 * @project : wms-server
 * @company : 浙江单创品牌管理有限公司
 * @desc :
 * Copyright @ 2024 danchuang Technology Co.Ltd. – Confidential and Proprietary
 */
public class MD5Util {

    public static String getMD5Hash(String input) {
        try {
            // 创建 MD5 消息摘要对象
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 计算哈希值
            byte[] messageDigest = md.digest(input.getBytes());

            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }
}
