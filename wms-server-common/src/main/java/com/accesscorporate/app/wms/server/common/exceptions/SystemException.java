package com.accesscorporate.app.wms.server.common.exceptions;



@lombok.extern.slf4j.Slf4j
public class SystemException extends BusinessException {
	private static final long serialVersionUID = 1631749498044372018L;
	public static final String USER_LOGIN_NAME_DUPLICATED = "error.system.userAccount.loginNameDuplicated";//用户登录名重复
	public static final String USER_LOGIN_NAME_NOT_EXISTS = "error.system.userAccount.loginNameNotExists";//用户登录名不存在
	public static final String OLD_PASSWORD_INVALID = "error.system.userAccount.oldPasswordInvalid";//原密码错误
	public static final String PASSWORD_EQ_LOGIN_NAME = "error.system.userAccount.password.eq.loginName";//密码不能和用户名相同
	public static final String PASSWORD_INVALID = "error.system.userAccount.password.invalid";//密码不能和用户名相同
	public static final String PASSWORD_REPEAT = "error.system.userAccount.password.repeat";//禁止使用前4次密码

	public static final String PASSWORD_LENGTH_ERROR = "error.system.userAccount.passwordLengthError";//密码长度不小于6位，不超过16位
	public static final String OLD_PASSWORD_EQ_NEW_PASSWORD = "error.system.userAccount.newPasswordEqNewPassword";//新密码与原密码相同
	public static final String ROLE_NAME_DUPLICATED = "error.system.role.roleNameDuplicated";//角色名称重复
	public static final String ROLE_CODE_DUPLICATED = "error.system.role.roleCodeDuplicated";//角色编码重复
	public static final String PASSWORD_TOO_SIMPLE = "error.system.userAccount.passwordTooSimple";//密码过于简单 
	public static final String SEQUENCE_RULECODE_EXISTS = "error.system.sequenceRuleCode.exists";//业务编号规则代码已存在
	public static final String SEQUENCE_RULENAME_EXISTS = "error.system.sequenceRuleName.exists";//业务编号规则名称已存在
	public static final String ROLE_EXISTS_USER_LIST = "error.system.role.userExists"; //存在分配的用户，不能删除
	public static final String CONFIG_CANNOT_ADD_EXISTS = "error.system.config.cannotAdd.exists";//此配置信息已经存在，添加失败
	public static final String PRINT_TEMPLATE_NOT_EXISTS = "error.system.printTemplate.notExists";//配置模板不存在
	public static final String PRINT_TEMPLATE_ERROR = "error.system.printTemplateError";//打印模板异常
    public static final String ERROR_CLIENT_VERSION = "error.client.version"; // 版本错误，请升级应用版本!
	public static final String PRINT_TEMPLATE_PATH_ERROR = "error.system.printTemplatePathError"; // 打印模板路径配置异常
    public static final String WAREHOUSE_IS_NULL = "error.system.warehouseIsNull"; // 仓库信息不存在
	public static final String TENANG_IS_NULL = "error.system.IsNull"; // 租户信息不存在
    public static final String CAN_NOT_ADD_GLOBAL_CONFIG = "can.not.add.global.config"; // 您不能添加全局参数，请联系管理员    public SystemException() {
	public static final String PERMISSION_HAS_CHILD = "permission.has.child";//角色名称重复
	public SystemException() {
		super();
	}

	public SystemException(String message, Object... params) {
		super(message, params);
	}

	public SystemException(String message, Throwable cause) {
		super(message, cause);
	}

	public SystemException(String message) {
		super(message);
	}

	public SystemException(Throwable cause) {
		super(cause);
	}

}
