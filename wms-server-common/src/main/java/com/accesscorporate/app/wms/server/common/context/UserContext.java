package com.accesscorporate.app.wms.server.common.context;

import com.alibaba.ttl.TransmittableThreadLocal;

/**
 * User Context 用户上下文信息
 * 「『「禁止其他业务代码直接调用, 业务代码调用！！！！
 *  业务代码调用参考：UserContextAssistant！！！！」』」
 *
 * <AUTHOR>
 * 2025/1/13 14:32
 */
public class UserContext {

    /**
     * 用户ID
     */
    private static final TransmittableThreadLocal<Long> USER_ID = new TransmittableThreadLocal<>();
    /**
     * 账号
     */
    private static final TransmittableThreadLocal<String> ACCOUNT = new TransmittableThreadLocal<>();
    /**
     * 用户名
     */
    private static final TransmittableThreadLocal<String> USERNAME = new TransmittableThreadLocal<>();
    /**
     * 租户ID
     */
    private static final TransmittableThreadLocal<Long> TENANT_ID = new TransmittableThreadLocal<>();
    /**
     * 当前仓库ID
     */
    private static final TransmittableThreadLocal<Long> CURRENT_WAREHOUSE_ID = new TransmittableThreadLocal<>();

    public static Long getUserId() {
        return USER_ID.get();
    }

    public static void setUserId(Long userId) {
        USER_ID.set(userId);
    }


    public static String getAccount() {
        return ACCOUNT.get();
    }

    public static void setAccount(String account) {
        ACCOUNT.set(account);
    }

    public static String getUserName() {
        return USERNAME.get();
    }

    public static void setUsername(String username) {
        USERNAME.set(username);
    }

    public static Long getTenantId() {
        return TENANT_ID.get();
    }

    public static void setTenantId(Long tenantId) {
        TENANT_ID.set(tenantId);
    }

    public static Long getCurrentWarehouseId() {
        return CURRENT_WAREHOUSE_ID.get();
    }

    public static void setCurrentWarehouseId(Long currentWarehouseId) {
        CURRENT_WAREHOUSE_ID.set(currentWarehouseId);
    }


    public static void removeAll() {
        USER_ID.remove();
        ACCOUNT.remove();
        USERNAME.remove();
        TENANT_ID.remove();
        CURRENT_WAREHOUSE_ID.remove();
    }
}
