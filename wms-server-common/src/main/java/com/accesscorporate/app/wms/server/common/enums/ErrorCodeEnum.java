package com.accesscorporate.app.wms.server.common.enums;


import com.idanchuang.component.base.exception.core.ExType;
import com.idanchuang.component.base.exception.core.IErrorEnum;


/**
 * <AUTHOR>
 * @version JDK 17
 * @date 2025/3/24
 * @description
 */
public enum ErrorCodeEnum implements IErrorEnum {
    /**
     * 异常枚举定义
     */
    SYSTEM_ERROR(ExType.SYSTEM_ERROR, 2000, "{}"),
    REQUEST_TIMEOUT(ExType.SYSTEM_ERROR, 2001, "请求超时: {}"),
    REQUEST_FAIL(ExType.SYSTEM_ERROR, 2002, "请求失败: {}"),
    REPEAT_REQUEST(ExType.SYSTEM_ERROR, 2003, "重复提交: {}"),
    SERVICE_UNAVAILABLE(ExType.SYSTEM_ERROR, 2004, "服务不可用: {}"),


    BUSINESS_ERROR(ExType.BUSINESS_ERROR, 2100, "{}"),
    PARAM_ERROR(ExType.BUSINESS_ERROR, 2101, "参数错误: {}"),
    DATA_NOT_EXIST(ExType.BUSINESS_ERROR, 2102, "数据不存在: {}"),
    REDIS_LOCK_FAIL(ExType.BUSINESS_ERROR, 2103, "redis加锁失败: {}"),


    ;

    private final ExType exType;
    private final int code;
    private final String msg;

    ErrorCodeEnum(ExType exType, int code, String msg) {
        this.exType = exType;
        this.code = code;
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }

    @Override
    public ExType getExType() {
        return exType;
    }

    @Override
    public String toString() {
        return "ErrorCode{extType=" + this.exType + ", code=" + this.code + ", msg='" + this.msg + '\'' + '}';
    }
}
