package com.accesscorporate.app.wms.server.common.enums;

/**
 * 自动波次类型枚举
 */
public enum AutoWaveTypeEnum {
    NORMAL("普通", 1),
    BIG_VOLUME("大体积", 12),
    BATCH_GROUP("批量团购", 20),
    MILA("MILA", 30),
    BOND_GIFT("非保赠品", 40);

    private final String displayName;
    private final Integer value;

    AutoWaveTypeEnum(String displayName, Integer value) {
        this.displayName = displayName;
        this.value = value;
    }

    public String getDisplayName() {
        return displayName;
    }

    public Integer getValue() {
        return value;
    }

    public static AutoWaveTypeEnum of(Integer value) {
        for (AutoWaveTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Cannot find enum type with value " + value);
    }

    public static String dictionaryCode() {
        return "WAVE_TYPE_SUBSECTION";
    }
}
