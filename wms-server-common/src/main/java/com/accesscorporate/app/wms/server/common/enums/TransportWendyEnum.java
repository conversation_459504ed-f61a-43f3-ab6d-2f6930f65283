package com.accesscorporate.app.wms.server.common.enums;

import org.apache.commons.collections4.CollectionUtils;

import java.util.Objects;
import java.util.Set;

/**
 * 运输温度
 *
 */
public enum TransportWendyEnum {
    HEAT(1, "防高温"),
    COLD(2, "防冻"),
    NORMAL(3, "常温"),
    HEATANDCOLD(4, "防冻防高温");

    private final Integer code;
    private final String desc;

    TransportWendyEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static TransportWendyEnum of(Integer code) {
        for (TransportWendyEnum transportWendyEnum : TransportWendyEnum.values()) {
            if (transportWendyEnum.getCode().equals(code)) {
                return transportWendyEnum;
            }
        }
        throw new IllegalArgumentException("Cannot find enum type with code " + code);
    }

    public static TransportWendyEnum getTransportWendyEnum(Integer code) {
        for (TransportWendyEnum transportWendyEnum : TransportWendyEnum.values()) {
            if (transportWendyEnum.getCode().equals(code)) {
                return transportWendyEnum;
            }
        }
        return null;
    }

    public static TransportWendyEnum getBySkuAndAddress(Integer addressCode, Integer skuCode) {

        // 商品/地址 任意一个未配置则取常温
        if (Objects.isNull(addressCode) || Objects.isNull(skuCode)) {
            return TransportWendyEnum.NORMAL;
        }

        // 两个配置相同 取地址配置
        if (Objects.equals(addressCode, skuCode)) {
            return TransportWendyEnum.of(addressCode);
        }

        // 不相同 且存在常温的 取常温
        Integer normalCode = TransportWendyEnum.NORMAL.getCode();
        if (normalCode.equals(addressCode) || normalCode.equals(skuCode)) {
            return TransportWendyEnum.NORMAL;
        }

        // 不相同 地址防冻防高温的 取商品配置
        Integer heatAndCold = TransportWendyEnum.HEATANDCOLD.getCode();
        if (heatAndCold.equals(addressCode)) {
            return TransportWendyEnum.of(skuCode);
        }

        // 不相同 商品防冻防高温的 取地址配置
        if (heatAndCold.equals(skuCode)) {
            return TransportWendyEnum.of(addressCode);
        }

        // 均不相同
        return TransportWendyEnum.NORMAL;
    }

    /**
     * 根据运输温度优先级设置运输温度
     * @param wendyEnumSet
     * @return
     */
    public static TransportWendyEnum getTransportWendyEnumPriority(Set<TransportWendyEnum> wendyEnumSet) {
        if (CollectionUtils.isEmpty(wendyEnumSet)) {
            return TransportWendyEnum.NORMAL;
        }
        if (wendyEnumSet.contains(HEATANDCOLD)) {
            return TransportWendyEnum.HEATANDCOLD;
        }
        if (wendyEnumSet.contains(HEAT) && wendyEnumSet.contains(COLD)) {
            return TransportWendyEnum.HEATANDCOLD;
        }
        if (wendyEnumSet.contains(HEAT)) {
            return TransportWendyEnum.HEAT;
        }
        if (wendyEnumSet.contains(COLD)) {
            return TransportWendyEnum.COLD;
        }
        return TransportWendyEnum.NORMAL;
    }

}
