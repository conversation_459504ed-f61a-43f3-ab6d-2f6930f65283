package com.accesscorporate.app.wms.server.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商是否能提供发票 - Enum
 * 0:否 1:是
 *
 * <AUTHOR>
 * 2025/2/17  16:46
 */
@Getter
@AllArgsConstructor
public enum SupplierCanInvoiceEnum {

    NO_INVOICE(0, "否"),

    YES_INVOICE(1, "是"),

    ;

    private final Integer code;
    private final String description;

    public static String getDescByCode(Integer code) {
        for (SupplierCanInvoiceEnum value : SupplierCanInvoiceEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDescription();
            }
        }
        return null;
    }
}
