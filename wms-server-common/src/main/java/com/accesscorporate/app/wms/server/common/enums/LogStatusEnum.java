package com.accesscorporate.app.wms.server.common.enums;

public enum LogStatusEnum {
    SUCCESS(20, "成功"),
    IGNORE(10, "忽略不调"),
    INIT(0, "初始化"),
    SYS_ERR(-10, "系统错误"),
    ARGS_ERROR(-20, "参数错误"),
    VALIDATE_ERROR(-30, "验证出错"),
    DATA_ERR(-40, "数据出错"),
    SEND_ERR(-50, "请求响应出错");


    private Integer value;
    private String decs;

    private LogStatusEnum(Integer value, String decs) {
        this.value = value;
        this.decs = decs;
    }

    public Integer getValue() {
        return value;
    }

    public String getDecs() {
        return decs;
    }

    public static LogStatusEnum ofValue(Integer value) {
        LogStatusEnum[] ls = LogStatusEnum.values();
        for (LogStatusEnum l : ls) {
            if (l.value.equals(value)) {
                return l;
            }
        }
        return null;
    }
}
