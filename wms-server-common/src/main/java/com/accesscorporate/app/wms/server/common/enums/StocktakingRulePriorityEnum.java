package com.accesscorporate.app.wms.server.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 盘点规则优先级--枚举
 *
 * <AUTHOR>
 * 2025/2/14  16:47
 */
@Getter
@AllArgsConstructor
public enum StocktakingRulePriorityEnum {


    HIGHEST(1, "最高"),
    HIGH(2, "高"),
    MEDIUM(3, "中"),
    LOW(4, "低"),
    LOWEST(5, "最低"),

    ;

    private final Integer code;
    private final String description;


    public static String getDescByCode(Integer code) {
        for (StocktakingRulePriorityEnum value : StocktakingRulePriorityEnum.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value.getDescription();
            }
        }
        return null;
    }





}
