package com.accesscorporate.app.wms.server.common.utils;

import com.accesscorporate.app.wms.server.common.context.UserContext;

/**
 * 用户信息控制--小助手
 *
 * <AUTHOR>
 * 2025/2/13  11:05
 */
public class UserContextAssistant {

    /**
     * 获取用户ID
     * @return 用户ID
     */
    public static Long getUserId() {
        return UserContext.getUserId();
    }

    /**
     * 获取账号
     * @return 账号
     */
    public static String getAccount() {
        return UserContext.getAccount();
    }

    /**
     * 获取用户名
     * @return 用户名
     */
    public static String getUsername() {
        return UserContext.getUserName();
    }

    /**
     * 获取租户ID
     * @return 租户ID
     */
    public static Long getTenantId() {
        return UserContext.getTenantId();
    }

    /**
     * 获取当前仓库ID
     * @return 当前仓库ID
     */
    public static Long getCurrentWarehouseId() {
        return UserContext.getCurrentWarehouseId();
    }

}
