package com.accesscorporate.app.wms.server.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 供应商状态
 * 0:申请中 1：正常
 *
 * <AUTHOR>
 * 2025/2/17  16:39
 */
@Getter
@AllArgsConstructor
public enum SupplierStatusEnum {

    APPLYING(0, "申请中"),
    NORMAL(1, "正常");

    private final Integer code;
    private final String description;

    public static String getDescByCode(Integer code) {
        for (SupplierStatusEnum value : SupplierStatusEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getDescription();
            }
        }
        return null;
    }

    public static Integer getCodeByDesc(String description) {
        for (SupplierStatusEnum value : SupplierStatusEnum.values()) {
            if (Objects.equals(value.getDescription(), description)) {
                return value.getCode();
            }
        }
        return null;
    }

}
