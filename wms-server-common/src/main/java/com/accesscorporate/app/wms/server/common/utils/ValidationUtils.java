package com.accesscorporate.app.wms.server.common.utils;

import com.accesscorporate.app.wms.server.common.annotation.NonNullField;
import com.idanchuang.component.base.exception.core.ExFactory;
import com.idanchuang.component.base.exception.core.asserts.ExBusinessAssert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.Objects;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-08-04 16:23
 * Description: 非空校验
 */
@Slf4j
public class ValidationUtils {

    /**
     * 校验对象中所有标记了@NonNullField的字段是否为空
     *
     * @param obj    需要校验的对象
     * @param rowNum 行号（用于Excel导入场景）
     * @throws IllegalAccessException 反射访问异常
     */
    public static void validate(Object obj, int rowNum) {
        if (obj == null) {
            ExBusinessAssert.isTrue(false, "校验对象不能为空!");
        }

        // 获取对象所有字段
        Field[] fields = obj.getClass().getDeclaredFields();

        for (Field field : fields) {
            // 检查字段是否标记了NonNullField注解
            if (field.isAnnotationPresent(NonNullField.class)) {
                NonNullField annotation = field.getAnnotation(NonNullField.class);
                field.setAccessible(true); // 允许访问私有字段
                Object fieldValue = null;
                try {
                    fieldValue = field.get(obj);
                } catch (IllegalAccessException e) {
                    ExFactory.throwBusiness("通过反射校验字段为空异常，异常信息:{}",e);
                }
                // 执行非空校验
                boolean isValid = false;
                if (fieldValue instanceof String) {
                    isValid = StringUtils.isNotBlank((String) fieldValue);
                } else {
                    isValid = Objects.nonNull(fieldValue);
                }
                // 校验失败抛出异常
                ExBusinessAssert.isTrue(isValid,
                        String.format("导入文件第[%d]行,%s不可为空!", rowNum, annotation.desc()));
            }
        }
    }
}
