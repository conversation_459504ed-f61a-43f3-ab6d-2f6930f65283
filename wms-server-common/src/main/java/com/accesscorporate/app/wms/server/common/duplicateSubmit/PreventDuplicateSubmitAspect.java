package com.accesscorporate.app.wms.server.common.duplicateSubmit;

import com.alibaba.fastjson.JSON;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-19 14:23
 * Description: 防止重复提交切面
 */
@Aspect
@Component
public class PreventDuplicateSubmitAspect {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Pointcut("@annotation(preventDuplicateSubmit)")
    public void pointcut(PreventDuplicateSubmit preventDuplicateSubmit) {
    }

    @Around(value = "pointcut(preventDuplicateSubmit)", argNames = "joinPoint,preventDuplicateSubmit")
    public Object around(ProceedingJoinPoint joinPoint, PreventDuplicateSubmit preventDuplicateSubmit) throws Throwable {
        // 获取请求参数
        Object[] args = joinPoint.getArgs();
        // 序列化为字符串
        String paramJson = JSON.toJSONString(args);

        // 取方法名拼接作为key
        String method = joinPoint.getSignature().toShortString();
        String md5 = DigestUtils.md5DigestAsHex((method + paramJson).getBytes(StandardCharsets.UTF_8));

        String key = "dup_submit:" + md5;

        // redis setnx
        Boolean success = redisTemplate.opsForValue().setIfAbsent(key, "1", preventDuplicateSubmit.expireSeconds(), TimeUnit.SECONDS);
        if (success != null && success) {
            // 首次提交
            try {
                // 正常执行业务代码
                return joinPoint.proceed();
            } finally {
                // 始终删除该key，无论是否异常
                redisTemplate.delete(key);
            }
        } else {
            // 重复提交
            throw new RuntimeException(preventDuplicateSubmit.errMessage());
        }
    }

}
