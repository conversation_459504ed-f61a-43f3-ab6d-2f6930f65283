package com.accesscorporate.app.wms.server.common.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.metadata.property.DateTimeFormatProperty;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.idanchuang.component.base.exception.common.ErrorCode;
import com.idanchuang.component.base.exception.core.ExFactory;
import com.idanchuang.component.base.exception.core.asserts.ExBusinessAssert;
import com.idanchuang.component.base.exception.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.EncryptedDocumentException;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> liudongliang
 * @project : wms-server
 * @company : 浙江单创品牌管理有限公司
 * @desc :
 * Copyright @ 2024 danchuang Technology Co.Ltd. – Confidential and Proprietary
 */
@Slf4j
public class EasyExcelUtils {

    public static <T> List<T> reader(MultipartFile file, Class<T> importFileJavaType) {
        List<T> importResultList = null;
        try {
            importResultList = EasyExcel.read(file.getInputStream())
                    .head(importFileJavaType)
                    .autoCloseStream(true)
                    .autoTrim(true).sheet().doReadSync();
        } catch (ExcelDataConvertException | BusinessException dce) {
            if (dce instanceof ExcelDataConvertException) {
                ExFactory.throwBusiness("");
                throwExcelDataConverter((ExcelDataConvertException) dce);
            } else {
                throw dce;
            }
        } catch (ExcelAnalysisException ease) {
            Throwable cause = ease.getCause();
            if (cause instanceof ExcelDataConvertException) {
                throwExcelDataConverter((ExcelDataConvertException) cause);
            } else {
                throw ease;
            }
        } catch (EncryptedDocumentException encrypted) {
            ExFactory.throwBusiness("当前导入文件是加密文件,请先移除密码保护,再执行导入");
        } catch (Exception e) {
            log.error("导入失败, e:", e);
            throw ExFactory.throwWith(ErrorCode.PARAM_ERROR, String.format("导入失败 原因:%s", e.getMessage()));
        }
        return importResultList;
    }

    public static void throwExcelDataConverter(ExcelDataConvertException convertException) {
        ExcelContentProperty contentProperty = convertException.getExcelContentProperty();
        List<String> headNameList = contentProperty.getHead().getHeadNameList();
        String excelColumnName = findRequiredAny(headNameList);
        DateTimeFormatProperty dateTimeFormatProperty = contentProperty.getDateTimeFormatProperty();
        if (Objects.nonNull(dateTimeFormatProperty)) {
            String format = contentProperty.getDateTimeFormatProperty().getFormat();
            if (StringUtils.isBlank(format)) {
                ExFactory.throwBusiness(String.format("%s,格式错误,请输入正确的格式;若不能确认格式,请咨询系统技术同学答疑", excelColumnName));
            } else {
                ExFactory.throwBusiness(String.format("%s,格式错误,请输入正确的格式(%s)", excelColumnName, format));
            }
        } else {
            ExFactory.throwBusiness(String.format("%s,格式错误,请先检查格式", excelColumnName));
        }
    }

    public static <T> T findRequiredAny(List<T> dataList) {
        Optional<T> any = dataList.stream().findAny();
        ExBusinessAssert.isTrue(any.isPresent(), "Excel识别文件头失败!");
        return any.get();
    }

}
