package com.accesscorporate.app.wms.server.common.enums;

import lombok.Getter;

@Getter
public enum SLoopEnum {

    DAY(0, "日"),
    MONTH(1, "月"),
    YEAR(2, "年"),
    NON_CIRCULATION(3, "不循环"),
    ;


    private Integer key;
    private String value;

    SLoopEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }


    public static String getValue(Integer key) {
        for (SLoopEnum statusEnum : SLoopEnum.values()) {
            if (statusEnum.getKey().equals(key)) {
                return statusEnum.getValue();
            }
        }
        return null;
    }

}
