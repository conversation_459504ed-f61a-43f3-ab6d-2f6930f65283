package com.accesscorporate.app.wms.server.common;

import com.idanchuang.component.base.exception.core.ExType;
import com.idanchuang.component.base.exception.core.IErrorEnum;

/**
 * 错误码
 * <AUTHOR>
 */
public enum ErrorCode implements IErrorEnum {

    LOG_FAILED(ExType.SYSTEM_ERROR, 1000, "日志记录失败: {}"),
    UNSUPPORTED_TYPE(ExType.BUSINESS_ERROR, 1002, "不支持的类型: {}"),

    /* ---------------------  认证&鉴权「异常码」 --------------------- */
    ILLEGAL_HEADER(ExType.SYSTEM_ERROR, 3001, "不合法请求头:{}"),

    FORBIDDEN_REQUEST(ExType.SYSTEM_ERROR, 3002, "非法请求:{}"),

    ;

    ErrorCode(ExType exType, int code, String msg) {
        this.exType = exType;
        this.code = code;
        this.msg = msg;
    }

    private ExType exType;

    private int code;

    private String msg;

    @Override
    public ExType getExType() {
        return exType;
    }

    public void setExType(ExType exType) {
        this.exType = exType;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
