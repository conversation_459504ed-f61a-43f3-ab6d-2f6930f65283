package com.accesscorporate.app.wms.server.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 订单类型枚举
 */
public enum DoTypeEnum {
    SELL("1"), // 销售
    ALLOT("2"), // 调拨
    RTV("3"), // RTV
    WHOLESALE("6"), // 批发
    DIRECT("7"), // 直配
    WHOLESALE_SALE("60"), // 批发销售，生成波次专用
    MD_SALE("70"), // 门店销售，生成波次专用
    MPS_OUT("80"); // 仓内加工

    private String value;
    private String backendValue;

    DoTypeEnum(String value) {
        this.value = value;
    }

    DoTypeEnum(String value, String backendValue) {
        this.value = value;
        this.backendValue = backendValue;
    }

    public String getValue() {
        return value;
    }

    public String getBackendValue() {
        return backendValue;
    }

    public static String dictionaryCode() {
        return "ODO_TYPE";
    }

    private static final Map<String, DoTypeEnum> stringToEnum = new HashMap<>();

    static {
        for (DoTypeEnum op : values()) {
            stringToEnum.put(op.getValue(), op);
        }
    }

    public static DoTypeEnum fromString(String value) {
        return stringToEnum.get(value);
    }
}
