package com.accesscorporate.app.wms.server.common.enums;

import lombok.Getter;

@Getter
public enum StatusEnum {

    DISABLE("0", "失效"),
    ENABLE("1", "生效");


    private String key;
    private String value;

    StatusEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }


    public static String getValue(String key) {
        for (StatusEnum statusEnum : StatusEnum.values()) {
            if (statusEnum.getKey().equals(key)) {
                return statusEnum.getValue();
            }
        }
        return null;
    }

}
