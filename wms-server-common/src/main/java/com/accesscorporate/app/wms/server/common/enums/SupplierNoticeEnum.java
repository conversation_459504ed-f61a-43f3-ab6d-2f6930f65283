package com.accesscorporate.app.wms.server.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 供应商通知 枚举
 * 0：不通知；1：通知
 *
 * <AUTHOR>
 * 2025/2/17  16:42
 */
@Getter
@AllArgsConstructor
public enum SupplierNoticeEnum {

    NO_NOTICE(0, "不通知"),

    NOTICE(1, "通知"),

    ;

    private final Integer code;
    private final String description;

    public static String getDescByCode(Integer code) {
        for (SupplierNoticeEnum value : SupplierNoticeEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getDescription();
            }
        }
        return null;
    }
}
