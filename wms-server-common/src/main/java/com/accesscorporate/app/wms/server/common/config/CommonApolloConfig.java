package com.accesscorporate.app.wms.server.common.config;

import com.idanchuang.component.config.apollo.util.SpringContextUtil;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-24 13:57
 * Description: 基础配置
 */
@Data
@Component
public class CommonApolloConfig {

    /**
     * 英文仓集合
     */
    @Value("${i18n.warehouseIds}")
    private List<Long> englishWareHouseIds;

    /**
     * 判断仓库是否属于英文仓
     */
    public static boolean isEnglishWareHouse(Long warehouseId){
        List<Long> englishWareHouseIds = SpringContextUtil.getBean(CommonApolloConfig.class).getEnglishWareHouseIds();
        return englishWareHouseIds.contains(warehouseId);
    }



}
