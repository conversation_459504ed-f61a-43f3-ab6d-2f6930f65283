package com.accesscorporate.app.wms.server.common.duplicateSubmit;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface PreventDuplicateSubmit {

    /**
     * 过期时间,单位秒
     */
    int expireSeconds() default 10;


    /**
     * 错误提示信息
     */
    String errMessage() default "请勿重复提交，请稍后再试!!!";
}
