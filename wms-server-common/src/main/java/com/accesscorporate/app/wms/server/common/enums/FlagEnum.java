package com.accesscorporate.app.wms.server.common.enums;

import lombok.Getter;

@Getter
public enum FlagEnum {
    LOG_RETRY(1),// 01
    MSG_JOB_RETRY(2)//10
    ;

    private Integer value;

    private FlagEnum(Integer value) {
        this.value = value;
    }

    public static boolean isFlag(FlagEnum eflag, Integer vflag) {
        return (eflag.getValue() & (null == vflag ? 0 : vflag)) == eflag.getValue();
    }
}
