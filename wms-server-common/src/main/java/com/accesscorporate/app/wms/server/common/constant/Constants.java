package com.accesscorporate.app.wms.server.common.constant;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * WMS常量定义类
 */
@lombok.extern.slf4j.Slf4j
public class Constants {

    /**
     * 全国统一的工种编号
     */
    public static final String JOB_CODE_PICK = "JOB006";
    public static final String JOB_CODE_SORT = "JOB012";
    public static final String JOB_CODE_RECHECK = "JOB010";
    public static final String JOB_CODE_RECEIVE = "JOB003";
    public static final String JOB_CODE_PUTAWAY = "JOB005";
    public static final String ZERO = "0";

    public static final String DM_REASON_KEY = "basic.settings.failed-reason-map";

    /**
     * 通用状态定义
     */
    public static enum CommonStatus {
        ACTIVE(1), // 有效
        INACTIVE(0);// 无效

        private final Integer value;

        CommonStatus(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "COMMON_STATUS";
        }
    }

    // 批次策略
    public static enum LotStrategy {
        NULL(0), // 无策略
        ONE_BATCH_NO(1);// 同一批号

        private final Integer value;

        LotStrategy(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 是否
     */
    public enum YesNo {
        YES(1), NO(0);

        private final Integer value;

        YesNo(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "YES_NO";
        }
    }

    public enum CheckResult {
        PASS(1), NG(0);

        private final Integer value;

        CheckResult(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "CHECK_RESULT";
        }
    }

    /**
     * 任务状态 说明: 00 初始化 40 已发布 50 任务挂起 99 已完成 90 已取消
     */
    public enum TaskStatus {
        INITIALIZED("00"), RELEASED("40"), SUSPENDED("50"), COMPLETED("99"), CANCELED("90");

        private String value;

        TaskStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "TASK_STATUS";
        }

        private static final Map<String, TaskStatus> stringToEnum = new HashMap<String, TaskStatus>();

        static {
            for (TaskStatus op : values()) {
                stringToEnum.put(op.getValue(), op);
            }
        }

        public static TaskStatus fromString(String symbol) {
            return stringToEnum.get(symbol);
        }
    }

    /**
     * 任务类型
     * <p>
     * 说明: PA 上架 MV 移库 PK 拣货 ST 分拣 RK 返拣 RP 补货 CC 盘点 SO 发货 CONVERT 库存转换
     */
    public enum TaskType {
        PA("PA"), MV("MV"), PK("PK"), ST("ST"), RK("RK"), RP("RP"), CC("CC"), SO("SO"), TRANS_OUT("TRANS_OUT"),
        CONVERT("CONVERT"), ALL("ALL");

        private String value;

        TaskType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "TASK_TYPE";
        }
    }

    /**
     * 单据类型，QA: QualityApprove；
     */
    public enum DocType {
        ASN("ASN"), SO("SO"), MOVE("MOVE"), AD("AD"), SD("SD"), // stockDestory
        TRANS("TRANS"), CONVERT("CONVERT"), QA("QA"), BOM("BOM"), OTHER_ASN("OTHER_ASN"), OTHER_SO("OTHER_SO");

        private String value;

        DocType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "DOC_TYPE";
        }
    }

    /**
     * 单据类型，QA: QualityApprove；
     */
    public enum SpvsnDocType {
        PURCHASE_IN("PURCHASE_IN"), RETURN_IN("RETURN_IN"), RETAIL_OUT("RETAIL_OUT"), STORE_OUT("STORE_OUT"),
        VENDOR_OUT("VENDOR_OUT");

        private String value;

        SpvsnDocType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "SPVSN_DOC_TYPE";
        }
    }

    /**
     * <pre>
     * Description:序列号代码常量
     * </pre>
     * <p>
     * 说明 波次编号 WAVENO 盘点单号 COUNTNO 拣货单号 PKTNO 反拣任务号 RK_TASKNO 补货任务号 RP_TASKNO 上架任务号 PA_TASKNO 移库任务号 MV_TASKNO 搬仓出库明细的任务号
     * TR_TASKNO 箱号 CARTONNO 交接单号 LOADNO 移库单号 MOVENO 损溢单号 GAL_NO 批号 LOT_NO 补货单号 REPLENISHMENT_NO 返拣单号 REVERSEPICK_NO
     * 时间片号 SLICE_NO 预约号 RESERVATION_NO 搬仓出库单号 TRANS_OUT 搬仓入库单号 TRANS_IN 虚拟PO VIRTUALPO 逆向交接单 REVERSALHANDOVERNO SEED_NO
     * 越库分播单 SEED_CONTAINER_NO 越库分播单容器号 MC 合单打印号 UNFREEZE_NO 解冻单号 FREEZE_NO 冻结单号
     */
    public enum SequenceName {
        WAVENO("WAVE_NO"), COUNTNO("COUNT_NO"), PKTNO("PICK_NO"), CARTONNO("CARTON_NO"), LOADNO("LOAD_NO"),
        MOVENO("MOVE_NO"), PUTAWAYNO("PUTAWAY_NO"), HOLDNO("INV_HOLD_NO"), VALLPNNO("VAL_LPN_NO"),
        PAWLPNNO("PAW_LPN_NO"), REPLENISHMENTNO("REPLENISHMENT_NO"), GALNO("GAL_NO"), LOTNO("LOT_NO"),
        REVERSEPICKNO("REVERSEPICK_NO"), PALLETNO("PALLET_NO"), CDOCKNO("CDOCKNO"), SLICE_NO("SLICE_NO"),
        RESERVATION_NO("RESERVATION_NO"), TRANS_NO("TRANS_NO"), LOCATIONCOUNTNO("LOCATION_COUNT_NO"),
        RK_TASKNO("RK_TASK_NO"), RP_TASKNO("RP_TASK_NO"), PA_TASKNO("PA_TASK_NO"), MV_TASKNO("MV_TASK_NO"),
        TR_TASKNO("TR_TASK_NO"), VIRTUALPO("VIRTUAL_PO"), REVERSALHANDOVERNO("REVERSALHANDOVER_NO"),
        MATERIALS_DOC_IN("MATERIALS_DOC_IN"), MATERIALS_OUT_NO("MATERIALS_DOC_OUT"), DO_NO("DO_NO"), ASN_NO("ASN_NO"),
        FIND_LACK_HEADER("FIND_LACK_HEADER"), SHELF_LIFT("SHELF_LIFT"), WAVE_CRITERIA_NO("WAVE_CRITERIA_NO"),
        WAVE_NO_CX("WAVE_NO_CX"), JOB_NO("JOB_NO"), RTV_RESERVATION_NO("RTV_RESERVATION_NO"), CD_PACKING("CD_PACKING"),
        VIRUAL_PO("VIRUAL_PO"), MAINTAINNO("MAINTAIN_NO"), ARRIVE_NOTIFY_NO("ARRIVE_NOTIFY_NO"),
        RECEIVE_RECHECK_NO("RECEIVE_RECHECK_NO"), SUB_RECEIVE_NO("SUB_RECEIVE_NO"), TRSNAPORT_NO("TRSNAPORT_NO"),
        STOCK_CONVERT_NO("STOCK_CONVERT_NO"), WAVENO_2B("WAVE_NO_2B"), BOM_RULE_NO("BOM_RULE_NO"), BOM_NO("BOM_NO"),
        CONTAINER_NO("CONTAINER_NO"), OTHER_ASN_NO("OTHER_ASN_NO"), OTHER_DO_NO("OTHER_DO_NO"), SEED_NO("SEED_NO"),
        SEED_CONTAINER_NO("SEED_CONTAINER_NO"), MC("MC"), TR_NO("TR_NO"), LOCATION_PICK("LOCATION_PICK"),
        UNFREEZE_NO("UNFREEZE_NO"), FREEZE_NO("FREEZE_NO"), BATCH_WAVENO("BATCH_WAVE_NO"), MILA_WAVENO("MILA_WAVE_NO"),
        GIFT_WAVENO("GIFT_WAVE_NO");

        private String value;

        SequenceName(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "SEQUENCE_NAME";
        }
    }

    /**
     * 分拣柜状态
     */
    public enum SortingBinStatus {
        FREE(0), BUSSY(1);

        private Integer value;

        SortingBinStatus(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "SORTING_ZONE_STATUS";
        }
    }

    /**
     * 加工单状态 0初始化，10已分配，20领料中，30领料完成，40成品完成，50上架中，60已完成，-10已取消
     */
    public enum BomStatus {
        INIT(0), YFP(10), LLZ(20), LLWC(30), CPWC(40), SJZ(50), YWC(60), YQX(-10);

        private Integer value;

        BomStatus(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "BOM_STATUS";
        }
    }

    /**
     * <pre>
     * Description:wave状态
     * </pre>
     * <p>
     * 说明 初始化 00 部分分配 30 分配完成 40 部分拣货 50 拣货完成 60 部分分拣 64 分拣完成 65
     */
    public enum WaveStatus {
        INITIAL("00"), PARTALLOCATED("30"), ALLALLOCATED("40"), PARTPICKED("50"), ALLPICKED("60"), PARTSORTED("64"),
        ALLSORTED("65");

        private String value;

        WaveStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "WAVE_STATUS";
        }
    }

    /**
     * 发货单的补货状态
     */
    public enum DoReplStatus {
        /**
         * 不需要补货
         */
        NONE(0),
        /**
         * 等待补货
         */
        WAIT(1),
        /**
         * 补货完成
         */
        COMPLETE(2);

        private int value;

        DoReplStatus(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * <pre>
     * Description:do状态
     * </pre>
     * <p>
     * 说明 初始化 00 部分分配 30 分配完成 40 部分拣货 50 拣货完成 60 部分分拣 64 分拣完成 65 部分核检装箱 66 核检装箱完成 67 部分交接 68 交接完成 69 部分发货 70 发货完成 80
     * 订单关闭 99 订单取消 90
     */
    public enum DoStatus {
        UNCHECKED("-10"),
        /**
         * 初始化 00
         */
        INITIAL("00"),
        /**
         * 部分分配 30
         */
        PARTALLOCATED("30"),
        /**
         * 分配完成 40
         */
        ALLALLOCATED("40"),
        /**
         * 部分拣货 50
         */
        PARTPICKED("50"),
        /**
         * 拣货完成 60
         */
        ALLPICKED("60"),
        /**
         * 部分分拣 64
         */
        PARTSORTED("64"),
        /**
         * 分拣完成 65
         */
        ALLSORTED("65"),
        /**
         * 部分核检装箱 66
         */
        PART_CARTON("66"),
        /**
         * 核检装箱完成 67
         */
        ALL_CARTON("67"),
        /**
         * 部分交接 68
         */
        PART_LOAD("68"),
        /**
         * 交接完成 69
         */
        ALL_LOAD("69"),
        /**
         * 部分发货 70
         */
        LOAD_LOCKED("70"),
        /**
         * 发货中 75
         */
        PART_DELIVER("75"),
        /**
         * 发货完成 80
         */
        ALL_DELIVER("80"),
        /**
         * 订单关闭 99
         */
        CLOSED("99"),
        /**
         * 订单取消 90
         */
        CANCELED("90");

        private String value;

        DoStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "DO_STATUS";
        }

        private static final Map<String, DoStatus> stringToEnum = new HashMap<String, DoStatus>();

        static {
            for (DoStatus op : values()) {
                stringToEnum.put(op.getValue(), op);
            }
        }

        public static DoStatus fromString(String symbol) {
            return stringToEnum.get(symbol);
        }
    }

    /**
     * 订单作业报表中那些单纯用{枚举DO_STATUS}无法表示的列名
     * <p>
     *
     * <pre>
     * Description:do状态
     * </pre>
     * <p>
     * 说明 :<br/>
     * 分配完成且在波次里 40W W-Wave <br/>
     * 初始化To交接中 00268 2-To <br/>
     * 发货完成且未延迟 80G G-Good <br/>
     * 发货完成且延迟 80B B-Bad <br/>
     * 总计列 <br/>
     */
    public enum DoReportColName {
        NO_OUT_TIME("NO_OUT_TIME"), JUST_IN_WAVE("40W"), INITIAL_2_HANDOVERING("00268"), DELIVERED("80G"),
        DELAY_DELIVERED("80B"), TOTAL("TOTAL");

        private String value;

        DoReportColName(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum CartonStatus {
        /**
         * 包装完成
         */
        PACK_OVER("40"),
        /**
         * 已装车
         */
        ALL_LOAD("69"),
        /**
         * 已取消
         */
        CANCEL("90");

        private String value;

        CartonStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "DO_STATUS";
        }
    }

    /**
     * Description:冻结状态 说明 冻结 HOLD 释放 RELEASE
     */
    public enum ReleaseStatus {
        HOLD("HD"), RELEASE("RL");

        private String value;

        ReleaseStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "RELEASE_STATUS";
        }

        private static final Map<String, ReleaseStatus> stringToEnum = new HashMap<String, ReleaseStatus>();

        static {
            for (ReleaseStatus op : values()) {
                stringToEnum.put(op.getValue(), op);
            }
        }

        public static ReleaseStatus fromString(String symbol) {
            return stringToEnum.get(symbol);
        }
    }

    /**
     * Description:ASN状态常量 说明 初始化 00 部分收货 10 收货完成 40 收货确认 45， 验收确认 50，审核中 51 审核失败 52 上架完成 60, 订单关闭 99 订单取消 90
     */
    public enum AsnStatus {
        INITIAL("00"), PARTRECEIVE("10"), RECEIVED("40"), RECHECKED("50"), IN_RECHECKED("51"), RECHECKED_FAILED("52"),  ORDER_CLOSED("99"), COMPLETE_PUTAWAY("60"),
        ORDER_CANCEL("90");

        private String value;

        AsnStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "ASN_STATUS";
        }

        private static final Map<String, AsnStatus> stringToEnum = new HashMap<String, AsnStatus>();

        static {
            for (AsnStatus op : values()) {
                stringToEnum.put(op.getValue(), op);
            }
        }

        public static AsnStatus fromString(String symbol) {
            return stringToEnum.get(symbol);
        }
    }

    /**
     * Description:交易类型常量
     * <p>
     * 入库 IN 上架 PA 盘点 CC 损溢 AD 移库 MV 转移 TR 拣货 PK 分拣 SS 发货 SO 采购入库 PO 退货入库 RS 调拨入库 TI 冻结 HD 释放 RL 返拣 RK 补货 RP 补货上架 取消收货
     * CI 补货下架 RO ASN转仓明细收货CR_IN ASN转仓明细取消收货CR_CI ASN转仓调拨发运CR_SO 越库转本地CR_LOCAL 越库转本地取消CR_LOCAL_CI 礼品卡退货入GIFTCARD_RTV_IN
     * 礼品卡退货入取消GIFTCARD_RTV_IN_CI 库存转换 CONVERT 期初 BI 原料加工 BOM_SRC 成品加工 BOM_DES 查验损耗 RECHECK_LOSS
     */
    public enum TrsType {
        IN("IN"), PA("PA"), CC("CC"), AD("AD"), MV("MV"), TR("TR"), PK("PK"), SS("SS"), SO("SO"), PO("PO"), RS("RS"),
        TI("TI"), HD("HD"), RL("RL"), RK("RK"), RP("RP"), CI("CI"), RO("RO"), CR_IN("CR_IN"), CR_CI("CR_CI"),
        CR_SO("CR_SO"), CR_LOCAL("CR_LOCAL"), CR_LOCAL_CI("CR_LOCAL_CI"), GIFTCARD_RTV_IN("GIFTCARD_RTV_IN"),
        GIFTCARD_RTV_IN_CI("GIFTCARD_RTV_IN_CI"), CONVERT("CONVERT"), BOM_SRC("BOM_SRC"), BOM_DES("BOM_DES"),
        OTHER_ASN("OTHER_ASN"), RECHECK_LOSS("RECHECK_LOSS"), OTHER_SO("OTHER_SO");

        private String value;

        TrsType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "TRS_TYPE";
        }
    }

    /**
     * 订单类型
     */
    public enum DoType {
        SELL("1"), // 销售
        ALLOT("2"), // 调拨
        RTV("3"), // RTV
        WHOLESALE("6"), // 批发
        DIRECT("7"), // 直配
        WHOLESALE_SALE("60"), // 批发销售，生成波次专用
        MD_SALE("70"),// 门店销售，生成波次专用
        /**
         * 仓内加工
         */
        MPS_OUT("80");

        private String value;
        private String backendValue;

        DoType(String value) {
            this.value = value;
        }

        DoType(String value, String backendValue) {
            this.value = value;
            this.backendValue = backendValue;
        }

        public String getValue() {
            return value;
        }

        public String getBackendValue() {
            return backendValue;
        }

        public static final String dictionaryCode() {
            return "ODO_TYPE";
        }

        private static final Map<String, DoType> stringToEnum = new HashMap<String, DoType>();

        static {
            for (DoType op : values()) {
                stringToEnum.put(op.getValue(), op);
            }
        }

        public static DoType fromString(String value) {
            return stringToEnum.get(value);
        }
    }

    /**
     * 订单子类型
     */
    public enum DoSubType {
        NORMAL("NORMAL"), // 正常
        CTN("CTN"), // 整箱出
        NMB("NMB");

        private String value;

        DoSubType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 订单子类型
     */
    public enum AsnSubType {
        NORMAL("NORMAL"), // 正常
        CTN("CTN"), // 整箱出
        NMB("NMB"); // 整箱出

        private String value;

        AsnSubType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum OrderSubType {
        /**
         * 交接锁定
         */
        NORMAL("0"),

        /**
         * 已取消
         */
        O2O("1"),
        ;

        private String value;

        OrderSubType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "ORDER_SUB_TYPE";
        }
    }

    /**
     * 拣货单状态 说明: 00 初始化 40 已发布 80 发货完成 99 已完成 90 已取消
     */
    public enum PktStatus {
        INITIALIZED("00"), RELEASED("40"), DELIVERIED("80"), COMPLETED("99"), CANCELED("90");

        private String value;

        PktStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "TASK_STATUS";
        }
    }

    /**
     * <pre>
     * 交接单类型
     * </pre>
     */
    public enum LoadType {
        /**
         * 正常出库
         */
        DO("DO"),
        /**
         * RTV
         */
        RTV("RTV"),
        /**
         * 调拨
         */
        TT("TT"),
        /**
         * 仓库发货
         */
        WL("WL"),
        /**
         * 批发发货
         */
        WHOLESALE("WHOLESALE");

        private String value;

        LoadType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "LOAD_TYPE";
        }
    }

    /**
     * <pre>
     * 交接单状态
     * </pre>
     */
    public enum LoadStatus {
        INITIAL("00"), LOADING("40"), DELIVERYED("80"), COMPLETED("99"), CANCELED("90");

        private String value;

        LoadStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "LOAD_STATUS";
        }
    }

    /**
     * <pre>
     * 库位类型
     * </pre>
     * <p>
     * CS: 箱拣货库位; EA: 件拣货库位; PC: 箱件合并拣货位; WB: 组装工作区; SS: 理货站; RS: 存储位; DM: 坏品库位; ST: 过渡库位;
     * DEA: 定向拣货位 EACB: 组合零拣
     */
    public enum LocType {
        CS("CS"), EA("EA"), WB("WB"), RS("RS"), DM("DM"), ST("ST"),
        DEA("DEA"), EACB("EACB");

        private String value;

        LocType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "LOC_TYPE";
        }

        public static boolean isExist(String value) {
            for (LocType type : LocType.values()) {
                if (type.value.equals(value)) {
                    return true;
                }
            }
            return false;
        }

    }

    /**
     * <pre>
     * 中文库位类型
     * </pre>
     * <p>
     * EA 件拣货库位 RS 存储位 DM 坏品库位 ST 过渡库位
     */
    public enum CnLocType {

        EA("零拣"), RS("存储"), DM("坏品"), ST("过渡"),EACB("组合零拣");

        private String value;

        CnLocType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static String valueFrom(String value) {
            for (CnLocType locType : CnLocType.values()) {
                if (locType.getValue().equals(value)) {
                    return locType.toString();
                }
            }
            return null;
        }
    }

    /**
     * <pre>
     * 中文库位类型
     * </pre>
     * <p>
     * P托盘 C整箱 B散件
     */
    public enum CnPackageType {

        P("托盘"), C("整箱"), B("散件");

        private String value;

        CnPackageType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static String valueFrom(String value) {
            for (CnPackageType locType : CnPackageType.values()) {
                if (locType.getValue().equals(value)) {
                    return locType.toString();
                }
            }
            return null;
        }
    }

    /**
     * 移库单状态, 说明: 00 初始化 40 已发布 99 已完成 90 已取消
     */
    public enum MoveStatus {
        INITIALIZED("00"), RELEASED("40"), COMPLETED("99"), CANCELED("90");

        private String value;

        MoveStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "MOVE_STATUS";
        }
    }

    /**
     * 任务优先级
     */
    public enum TaskPriority {
        ONE(1L), TWO(2L), THREE(3L), FOUR(4L), FIVE(5L), SIX(6L), SEVEN(7L), EIGHT(8L), NINE(9L);

        private Long value;

        TaskPriority(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "PRIORITY_TSK";
        }
    }

    public enum PriorityType {
        /**
         * 最高
         */
        TOP(1L),
        /**
         * 高
         */
        HIGT(2L),
        /**
         * 中等
         */
        MID(3L),
        /**
         * 低
         */
        LOW(4L),
        /**
         * 最低
         */
        BOTTOM(5L);

        private Long value;

        PriorityType(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "PRIORITY";
        }
    }

    /**
     * <pre>
     * 单位
     * </pre>
     */
    public enum Uom {
        EA("EA"), IP("IP"), PL("PL"), CS("CS"), OT("OT");

        private String value;

        Uom(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "";
        }
    }

    /**
     * <pre>
     * 冻结原因
     * 00 等待补货
     * 01 客服取消
     * 02 分配缺货
     * 03 拣货缺货
     * 04 分拣缺货
     * 05 手动缺货
     * 06 疑似恶意订单
     * 07 货物质量问题
     * 08 拣货破损
     * 09 核拣缺货
     * 10 删除缺货商品
     * 11 分拣破损
     * 12 核拣破损
     * 13 PO取消
     * 30 上层系统锁定
     * </pre>
     */
    public enum Reason {
        WAIT_REPL("00"), CS_CANCLE("01"), ALLOC_LACK("02"), PICK_LACK("03"), SORT_LACK("04"), MANUAL_LACK("05"),
        MALICE_DO("06"), BAD_QUALITY("07"), PICK_DAMAGE("08"), RECHECK_LACK("09"), DELETE_LACK_DETAIL("10"),
        SORT_DM("11"), CHECK_DM("12"), PO_CANCLE("13"), HIGHER_HOLD("30");

        private String value;

        Reason(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "REASON_HDD";
        }
    }

    /**
     * <pre>
     * 初始冻结原因
     * 01 客服取消
     * 02 分配缺货
     * 03 拣货缺货
     * 04 分拣缺货
     * 05 手动缺货
     * 06 疑似恶意订单
     * 07 货物质量问题
     * 08 拣货破损
     * </pre>
     */
    public enum FirstReason {
        CS_CANCLE("01"), ALLOC_LACK("02"), PICK_LACK("03"), SORT_LACK("04"), MANUAL_LACK("05"), MALICE_DO("06"),
        BAD_QUALITY("07"), PICK_DAMAGE("08");

        private String value;

        FirstReason(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "FIRST_REASON_HDD";
        }
    }

    /**
     * <pre>
     * Description:波次类型状态常量
     * </pre>
     * <p>
     * 普通波次 3 RTV波次 6 调拨波次 7 批发波次 9 批发销售波次 10 11 门店销售波次 [数据转换用]
     */
    public enum WaveType {
        WAVE_NORMAL("3"), WAVE_RTV("6"), WAVE_TT("7"), WAVE_2B("9"), WAVE_2B_PF("10"), WAVE_2B_MD("11");

        private String value;

        WaveType(String value) {
            this.value = value;
        }

        public static final String dictionaryCode() {
            return "WAVE_TYPE";
        }

        public String getValue() {
            return value;
        }

    }

    /**
     * <pre>
     * 移库原因
     * 正常：00
     * 缺货：01
     * 其他：02
     * </pre>
     */
    public enum ReasonMove {
        NORMAL("00"), ERROR("01"), OT("02");

        private String value;

        ReasonMove(String value) {
            this.value = value;
        }

        public static final String dictionaryCode() {
            return "REASON_MOVE";
        }

        public String getValue() {
            return value;
        }

    }

    /**
     * <pre>
     * 损溢原因
     * 盘点差异             01
     * 可保存报废         02
     * 不可保存报废      03
     * 二手售卖             04
     * 调拨差异             05
     * 收货错误             06
     * 仓库错发             07
     * 赔付平台已审批  08
     * </pre>
     */
    public enum ReasonGal {
        REASON_GAL1("01"), REASON_GAL2("02"), REASON_GAL3("03"), REASON_GAL4("04"), REASON_GAL5("05"),
        REASON_GAL6("06"), REASON_GAL7("07"), REASON_GAL8("08");

        private String value;

        ReasonGal(String value) {
            this.value = value;
        }

        public static final String dictionaryCode() {
            return "REASON_AD";
        }

        public String getValue() {
            return value;
        }

    }

    /**
     * 损益单默认差异原因
     */
    public static final String DEFAULT_GAL_REASON_DESC = "盘点差异";

    /**
     * 损溢状态: {00: 初始化; 20: 已发布; 60: 审核未通过; 70: 上级审批中; 90: 已取消; 99: 完成}
     */
    public enum GalStatus {
        INITIALIZED("00"), RELEASED("20"), UNCHECKED("60"), HIGHER_CHECKING("70"), CANCELED("90"), COMPLETED("99");

        private String value;

        GalStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "GAL_STATUS";
        }
    }

    /**
     * 盘点状态: 0 初始化 1 发布 2 进行中 3 完成 4 取消 5 终盘
     */
    public enum CouStatus {
        INITIALIZED("0"), RELEASED("1"), COUNTTING("2"), COMPLETED("3"), CANCELED("4"), FINALSET("5");

        private String value;

        CouStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "COU_STATUS";
        }
    }

    /**
     * 盘点类型
     * <p>
     * 缺货(LACK)、动碰（TOUCH）、物理（PHYSICAL）、滞销(OVERSTORAGE)、差异（DIFF）
     */
    public enum CountMode {
        LACK("1"), TOUCH("2"), PHYSICAL("3"), OVERSTORAGE("4"), DIFF("5");

        private String value;

        CountMode(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "COUNT_MODE";
        }
    }

    /**
     * <pre>
     * Description:冻结方式
     * </pre>
     * <p>
     * BY_LOT 按批次 BY_SKU 按产品 BY_LOC 按库位 BY_LOTLOCID 按批次库位托盘号
     */
    public enum HoldType {
        BYLOT("BY_LOT"), BYSKU("BY_SKU"), BYLOC("BY_LOC"), BYLOTLOCID("BY_LOTLOCID");

        private String value;

        HoldType(String value) {
            this.value = value;
        }

        public static final String dictionaryCode() {
            return "HOLD_TYPE";
        }

        public String getValue() {
            return value;
        }

    }

    /**
     * 盘点状态 说明: 00 正常 01 缺货 02 货物质量问题 03 其它 7 新增锁定库位 8 移入锁定
     */
    public enum ReasonHd {
        NORMAL("00"), SHORT("01"), BAD("02"), OT("03"), CHL("7"), MOVEIN("8");

        private String value;

        ReasonHd(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "REASON_HD";
        }
    }

    /**
     * 上架单状态
     * <p>
     * 说明: 00 初始化 40 已发布 50 进行中 99 已完成 90 已取消
     */
    public enum DocPaStatus {
        INITIAL("00"), RELEASED("40"), INPROGRESS("50"), CLOSED("99"), CANCELLED("90");

        private String value;

        DocPaStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "DOC_PA_STATUS";
        }
    }

    // 状态（00：初始化；40：已发布；99：完成；90：取消）
    public enum ReplStatus {
        INITIAL("00"), RELEASED("40"), INPROGRESS("50"), CLOSED("99"), CANCELLED("90");

        private String value;

        ReplStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "DOC_PA_STATUS";
        }
    }

    /**
     * 补货任务状态 00：初始化； 40：已发布； 60: 已下架； 99：已完成； 90：已取消；
     */
    public enum ReplTaskStatus {
        INITIAL("00"), RELEASED("40"), INPROGRESS("50"), OFFSHELF("60"), CLOSED("99"), CANCELLED("90");

        private String value;

        ReplTaskStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "TASK_STATUS";
        }
    }

    /**
     * 入库单类型: 1: 正品, 3: DO退货, 5：调拨，7: 门店退货 8: 换货do退货, 10: SLC导入（不与调用其他接口） 11：批发退货 12 仓内加工
     */
    public enum AsnType {
        NORMAL("1"), DORTV("3"), MOVE("5"), STORE_RTV("7"), DORTV2("8"), SUPPLIER("10"), PF_RTV("11"),MPS_IN("12");

        private String value;

        AsnType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "ASN_TYPE";
        }

        public static AsnType of(String value) {
            if (StringUtils.isBlank(value)) {
                return null;
            }
            return Arrays.stream(AsnType.values()).filter(type -> type.getValue().equals(value)).findFirst().orElse(null);
        }
    }


    public enum GoodsSourceConvertEnum {
        NORMAL(0, "采购入库", "1"),
        DORTV(2, "DO销退", "3"),
        MOVE_IN(4, "调拨入库", "5"),
        TALL(6, "巡仓理货", null),
        WORK_DEFECTIVE(8, "作业残次", null),
        MATURE_WARN(10, "临期预警", null),
        MPS_IN(12,"仓内加工","12")
        ;

        public static GoodsSourceConvertEnum ofByAsnType(String asnType) {
            if (Objects.isNull(asnType)) {
                return null;
            }
            for (GoodsSourceConvertEnum value : GoodsSourceConvertEnum.values()) {
                if(Objects.equals(value.getAsnType(),asnType)){
                    return value;
                }
            }
            return null;
        }

        GoodsSourceConvertEnum(Integer code, String desc, String asnType) {
            this.code = code;
            this.desc = desc;
            this.asnType = asnType;
        }

        private Integer code;
        private String desc;
        private String asnType;

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public String getAsnType() {
            return asnType;
        }

        public void setAsnType(String asnType) {
            this.asnType = asnType;
        }

        public static final String dictionaryCode() {
            return "SOURCE_TYPE";
        }
    }


    public enum SerialImportDocType {
        PO_IN("PO_IN"), RN_IN("RN_IN"), TR_IN("TR_IN"), SO_OUT("SO_OUT"), RTV_OUT("RTV_OUT"), TR_OUT("TR_OUT");

        private String value;

        SerialImportDocType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 说明: 0 没有定义 1 可选 2 必填 3 禁用
     */
    public enum BatchControlType {
        NOT_DEFINE("0"), OPTIONAL("1"), MANDATORY("2"), DISABLE("3");

        private String value;

        BatchControlType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "ASN_TYPE";
        }
    }

    /**
     * 说明: 0 没有定义 1 字符串 2 数字 3 日期
     */
    public enum BatchDataType {
        NOT_DEFINE("0"), STRING("1"), NUMBER("2"), DATE("3");

        private String value;

        BatchDataType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "ASN_TYPE";
        }
    }

    /**
     * 说明:周转级别，价值级别 A B C
     */
    public enum CycleClass {
        A_CLASS("A"), B_CLASS("B"), C_CLASS("C"), D_CLASS("D");

        private String value;

        CycleClass(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "CYCLE_CLASS";
        }
    }

    public enum PackageType {
        P("P"), C("C"), B("B");

        private String value;

        PackageType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 接口中有使用 INITIAL("00") 初始化
     */
    public enum LineStatus {
        INITIAL("00");

        private String value;

        LineStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "LINE_STATUS";
        }
    }

    /**
     * 接口中有使用 半日达普通/半日达大件/普通/普通大件/团购/一日三送/自提（约定数值分别是：1/2/3/4/5/6/7）
     */
    public enum DoSpecialFlag {
        HALFDAY_NORMAL(1L), HALFDAY_BIG_VOLUME(2L), NORMAL(3L), NORMAL_BIG_VOLUME(4L), GROUP_BUY(5L), THREETIMES(6L),
        ZITI(7L);

        private Long value;

        DoSpecialFlag(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "DO_SPECIAL_FLAG";
        }
    }

    /**
     * 普通/半日达/一日三送/准时送/指定日期（0/1/2/3/4）
     */
    public enum DeliveryLimitType {
        NORMAL(0), HALFDAY(1), THREETIMES(2), ONTIME(3), APPOINTDATE(4), SLOW_SHIP(5);

        private Integer value;

        DeliveryLimitType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "DELIVERY_LIMIT_TYPE";
        }

        private static final Map<Integer, DeliveryLimitType> integerToEnum = new HashMap<Integer, DeliveryLimitType>();

        static {
            for (DeliveryLimitType op : values()) {
                integerToEnum.put(op.getValue(), op);
            }
        }

        public static DeliveryLimitType fromInteger(Integer integer) {
            return integerToEnum.get(integer);
        }
    }

    public enum CartonPrintPoint {
        WAVE(1), SORT(2), RECHECK(3);

        private Integer value;

        CartonPrintPoint(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 拣货原因确认 NORMAL(0) 正常 ,LACK(1) 缺货 ,BROKEN(2) 破损
     */
    public enum StockStatus {
        NORMAL("0"), LACK("1"), BROKEN("2");

        private String value;

        StockStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * CrossDock状态 初始化：00 收货中：10 收货完成：40 已审核：60 待出库：70 装箱中:79 已发货：80 已取消：90
     */
    public enum CrossDockStatus {
        INITIAL("00"), RECEIVING("10"), RECEIVED("40"), VERIFIED("60"), WAIT_FOR_SHIP("70"), PACKING("79"),
        SHIPED("80"), CANCELED("90");

        private String value;

        CrossDockStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "CDOCK_STATUS";
        }
    }

    /**
     * CorssDock状态 成功 ：SUCCESS 失败 ：FAILED 处理中 ：HANDLING
     */
    public enum LoadHeaderHandlingStatus {
        SUCCESS("Success"), FAILED("Failed"), HANDLING("Handling");

        private String value;

        LoadHeaderHandlingStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 预约状态 待审核：00 审核通过：10 已拒绝：90 已取消：99
     */
    public enum ResState {
        FORCHECK("00"), CHECKEDPASS("10"), REJECTED("90"), CANCELLED("99");

        private String value;

        ResState(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "RES_STATE";
        }
    }

    /**
     * cfgConfigration记录的编码
     * <p>
     * RESERVATION_DATE_NUMBER：时间片默认生成天数 RESERVATION_MAX_NUM:时间片最大可预约数 RESERVATION_TIME_DURATION：时间片单位长度（分）
     * RESERVATION_START_TIME：时间片默认开始时间 RESERVATION_END_TIME：时间片默认终止时间
     */
    public enum ConfigCode {
        RESERVATION_DATE_NUMBER("Reservation.DateNumber"), RESERVATION_TIME_DURATION("Reservation.TimeDuration"),
        RESERVATION_END_TIME("Reservation.EndTime");

        private String value;

        ConfigCode(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "CONFIG_COEDE";
        }
    }

    /**
     * cfgConfigration记录的时间片编码
     * <p>
     * RESERVATION_DATE_NUMBER：时间片默认生成天数 RESERVATION_TIME_DURATION：时间片单位长度（分） RESERVATION_START_TIME：时间片默认开始时间
     * RESERVATION_END_TIME：时间片默认终止时间
     */
    public enum ReservationNewCode {
        RESERVATION_DATE_NUMBER("Reservation.DateNumber"), RESERVATION_TIME_DURATION("reservation.timeSliceSpan"),
        RESERVATION_START_TIME("reservation.timeSliceStartTime"), RESERVATION_END_TIME("reservation.timeSliceEndTime");

        private String value;

        ReservationNewCode(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum StockType {
        RECEIVE_STAGE("RS"), PICK_STAGE("PS"), STOCK("STOCK"), STOCK_ALLOC("SA"), STOCK_PENDING("SP"),
        STOCK_ALLOCING("SAI"), RECEIVE_CROSSDOCK("RC"), STOCK_BATCH_ATT("SBA");

        private String value;

        StockType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 0 未集货 1 部分集货 2 集货入区 3 集货出区 4 复核完成
     */
    public enum WaveMergeStatus {
        NO_MERGE("0"), PART_MERGE("1"), ALL_MERGE("2"), OUT_MERGE("3"), CHECKED("4");

        private String value;

        WaveMergeStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum PktMergeStatus {
        NO_MERGE("0"), MERGED("1"), OUT_MERGE("2");

        private String value;

        PktMergeStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum CountLevel {
        LOC_SKU(0), BATCH(1), OTHER(2);

        private Integer value;

        CountLevel(Integer value) {
            this.value = value;
        }

        public String getValue() {
            return value.toString();
        }

        public Integer getIntValue() {
            return value;
        }
    }

    /**
     * NORMAL：普通盘点 SHELF_LIFE：效期盘点 SHELF_LIFE_QTY：效期(数量)盘点 RULE：规则盘点
     */
    public enum CountType {
        NORMAL("1"), SHELF_LIFE("2"), SHELF_LIFE_QTY("3"), RULE("4");

        private String value;

        CountType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "COUNT_TYPE";
        }
    }

    /**
     * DO异常状态
     * <p>
     * TO_BE_ROLLBACK 待回退 TO_BE_REPICK 待返拣 TO_BE_REPL 待补货 COMPLETE 完成 TO_BE_ANNOUNCE 待通知客服 TO_BE_FEEDBACK 待回馈
     */
    public enum DoExpStatus {
        TO_BE_ANNOUNCE("0"), TO_BE_ROLLBACK("1"), TO_BE_REPICK("2"), TO_BE_REPL("3"), COMPLETE("4"),
        TO_BE_FEEDBACK("5"), TO_BE_PRINT("6");

        private String value;

        DoExpStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 客服反馈结果
     * <p>
     * CANCEL 取消 RELEASE 释放 WAIT 等货 DELETELACK 删除缺货商品
     */
    public enum FeedBackStatus {
        CANCEL("0"), RELEASE("1"), WAIT("2"), DELETELACK("3");

        private String value;

        FeedBackStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 发票薄状态
     * <p>
     * INITIAL 初始化 USING 使用中
     */
    public enum InvoiceBookStatus {
        INITIAL("0"), USING("1");

        private String value;

        InvoiceBookStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "INVOICE_BOOK_STATUS";
        }
    }

    /**
     * 发票号状态
     * <p>
     * INITIAL 未打印 PRINT 已打印 DES 已作废 LOST 已丢失
     */
    public enum InvoiceNoStatus {
        INITIAL("0"), PRINT("1"), DES("2"), LOST("3");

        private String value;

        InvoiceNoStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "INVOICE_STATUS";
        }
    }

    public enum LockStatus {
        NORMAL(0), LOCKED(1);

        private Integer value;

        LockStatus(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "LOCK_STATUS";
        }
    }

    /**
     * 小库位（轻型货架） 阈值≤177840 SMALL 中库位（轻型货架） 177840<阈值≤265050 MIDSIZE 大库位（轻型货架） 265050<阈值≤444600 LARGE 托盘库位（重型货架）
     * 阈值˃444600 HEAVY
     */
    public enum VolumeType {
        SMALL(0), MIDSIZE(1), LARGE(2), HEAVY(3);

        private Integer value;

        VolumeType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "VOLUME_TYPE";
        }
    }

    // 单据体积类型 0：小件 2：中件 1：大件
    public enum DocVolumeType {
        SMALL(0), MIDSIZE(2), LARGE(1);

        private Integer value;

        DocVolumeType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "DOC_VOLUME_TYPE";
        }
    }

    /**
     * 移库单类型
     * 01位置转移 03良转残 02残转良
     */
    public enum MoveType {
        MOVE("01"), TOBAD("03"), TOGOOD("02"),BTOB("04");
        private String value;

        MoveType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static boolean isExist(String value) {
            for (MoveType type : MoveType.values()) {
                if (type.value.equals(value)) {
                    return true;
                }
            }
            return false;
        }

        public static String getMoveType(String fromType, String toType) {
            if(fromType.equals(toType)
                    ||Constants.MoveStockType.CB.getValue().equals(fromType)
                    ||Constants.MoveStockType.CB.getValue().equals(toType)){
                return MOVE.getValue();
            }
            if (MoveStockType.GOOD.getValue().equals(fromType)){
                return TOBAD.getValue();
            }
            if (MoveStockType.GOOD.getValue().equals(toType)){
                return TOGOOD.getValue();
            }
            return BTOB.getValue();
        }

        public static final String dictionaryCode() {
            return "MOVE_TYPE";
        }
    }
    /**
     * 移库单库存类型
     * 10 良品,  20 轻微残次, 21 中度残次, 22 报废 01 组合品
     */
    public enum MoveStockType {
        CB("01"),GOOD("10"), B1("20"), B2("21"),C("22");
        private String value;

        MoveStockType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static boolean isExist(String value) {
            for (MoveStockType type : MoveStockType.values()) {
                if (type.value.equals(value)) {
                    return true;
                }
            }
            return false;
        }

        public static final String dictionaryCode() {
            return "MOVE_STOCK_TYPE";
        }
    }

    /**
     * 收货类型
     * <p>
     * RECEIVE 必须入库 UDF 自定义
     */
    public enum ReceiveType {
        RECEIVE("0"), UDF("2");

        private String value;

        ReceiveType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "RECEIVE_TYPE";
        }
    }

    /**
     * 商品性质
     * <p>
     * OK 好品 DAMAGED 坏品
     */
    public enum SkuNature {
        OK(0), DAMAGED(1);

        private Integer value;

        SkuNature(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "SKU_NATURE";
        }
    }

    /**
     * 补货类型
     * <p>
     * JP 即时补货 XP 闲时补货
     */
    public enum ReplType {
        JP("JP"), XP("XP");

        private String value;

        ReplType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "REPL_TYPE";
        }
    }

    /**
     * 波次优先级
     */
    public enum WavePriority {
        FIRST(1), SECOND(2), THIRD(3), FORTH(4), FIFTH(5);

        private Integer value;

        WavePriority(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "WAVE_PRIORITY";
        }
    }

    /**
     * 容器绑定单据类型
     * <p>
     * WAVE-------绑定波次单据 PICK-------拣货（3.6.6删除此功能，全部改绑波次） RECHECK ---绑定核捡 DELIVERYORDER ---发货单号
     */
    public enum BindDocType {
        WAVE("0"), PICK("1"), RECHECK("2"), DELIVERYORDER("3");

        private String value;

        BindDocType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "BIND_DOC_TYPE";
        }
    }

    /**
     * 容器业务状态{ IDLE: 闲置，PICKING：拣货，MERGED：集货入区， OUT_MERGE：集货出区， SORT：分拣，RECHECK：绑定核捡，CHECKED：分拣复核 }
     */
    public enum ContainerBusinessStatus {
        IDLE("0"), PICKING("1"), MERGED("2"), OUT_MERGE("3"), SORT("4"), RECHECK("5"), CHECKED("6");

        private String value;

        ContainerBusinessStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public static final String dictionaryCode() {
            return "CONTAINER_BUSINESS_TYPE";
        }
    }

    /**
     * 容器使用类型（容器操作类型）
     * <p>
     * BIND-------绑定 RELEASE----释放 MERGEIN-----集货入区 MERGEOUT----集货出区
     */
    public enum ContainerOperationType {
        BIND("0"), RELEASE("1"), MERGEIN("2"), MERGEOUT("3");

        private String value;

        ContainerOperationType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public static final String dictionaryCode() {
            return "CONTAINER_OPERATION_TYPE";
        }
    }

    /**
     * 容器类型 WAVE_CONTAINER------波次容器 PACKAGE_CONTAINER---包裹容器 LPN_CONTAINER-------托盘 SORT_CONTAINER-------分拣筐
     */
    public enum ContainerType {
        WAVE_CONTAINER("0"), PACKAGE_CONTAINER("1"), LPN_CONTAINER("2"), SORT_CONTAINER("3");

        private String value;

        ContainerType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public static final String dictionaryCode() {
            return "CONTAINER_TYPE";
        }
    }

    /**
     * 0 普通分拣柜 1 超小体积分拣柜
     */
    public enum SortBinType {
        NORMAL("0"), SMALL("1"), BIG("2");

        private String value;

        SortBinType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "SORT_BIN_TYPE";
        }
    }

    /**
     * 配送商类型
     */
    public enum CarrierType {
        THRPL(0), SELF(1);

        private Integer value;

        CarrierType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "CARRIER_TYPE";
        }
    }

    /**
     * 0 人工交接 1 自动交接 2 流水交接
     */
    public enum LoadMode {
        MANUAL(0), AUTO(1), FLOW(2);

        private final Integer value;

        LoadMode(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "LOAD_MODE";
        }
    }

    /**
     * 超小体积do标识
     */
    public enum FlowFlag {
        SPECIAL(1), NOMAL(0);

        private final Integer value;

        FlowFlag(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "LOAD_MODE";
        }
    }

    /**
     * 调拨类型 1:主动调拨, 2:被动调拨, 3:转仓调拨, 5:待退货商品调拨
     */
    public enum TranType {
        ZD(1), BD(2), CR(3), TH(5);

        private final Integer value;

        TranType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "TRAN_TYPE";
        }
    }

    /**
     * 付款方式 1 网上支付 2 货到付现金 3 邮局汇款 4 银行转账 5 货到刷卡 6 万里通 8 合同账期 9 货到转账 10 货到支票
     */
    public enum PayType {
        ONLINE(1), COD_CASH(2), MAIL_REMITTANCE(3), BANK_REMITTANCE(4), COD_POS(5), WLT(6), CONTRACT_ACCOUNT(8),
        COD_REMITTANCE(9), COD_CHECk(10);

        private final Integer value;

        PayType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    public enum PayName {
        COD_CASH("货到付现金"), COD_POS("货到刷卡");

        private final String value;

        PayName(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 备注：主要是在分配规则部分单据类型需要一个"所有"类型，故添加此枚举 订单类型：所有
     */
    public enum AllType {
        ALL(0);// 所有

        private Integer value;

        AllType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "All_TYPE";
        }
    }

    /**
     * 上架，返拣原因代码 00 正常 01 库位已满 02 计算错误 03 坏品
     */
    public enum ReasonCode {
        DAMAGE("03"), NORMAL("00"), FULL("01"), FAULT("02");// 所有

        private String value;

        ReasonCode(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "REASON_PA";
        }
    }

    /**
     * 上架不采纳推荐库位的原因：<br/>
     * 推荐库位被占用—00<br/>
     * 推荐库位放不下—01<br/>
     * 推荐库位跨仓—02<br/>
     * 推荐库位不存在—03<br/>
     * 临时原因库位无法上架—04<br/>
     * 特殊商品要求—05<br/>
     * 大数量推荐零拣—06<br/>
     * 小数量推荐存储—07<br/>
     * 特殊业务需要—08<br/>
     * 坏品—09
     */
    public enum UnAdoptedReason {
        OCCUPIED("00"), FILLED("01"), CROSS_WAREHOUSE("02"), LOC_NOT_EXIST("03"), UNAVAILABLE_TEMP("04"),
        SPECIAL_SKU("05"), NUMEROUS_TO_EA("06"), FEW_TO_RS("07"), SPECIAL_BUSINESS("08"), DAMAGED("09");

        private String value;

        UnAdoptedReason(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "UN_ADOPTED_REASON";
        }
    }

    /**
     * 即时补货业务类型 1 RPL_DO——DO补货 2 RPL_ALLOT——调拨补货 6 RPL_HALF——半日达补货 7 RPL_ON_TIME——准时达补货 8 RPL_APPOINT_DATE——指定日期补货
     */
    public enum ReplDoType {
        RPL_DO("1"), RPL_ALLOT("2"), RPL_HALF("6"), RPL_ON_TIME("7"), RPL_APPOINT_DATE("8");

        private String value;

        ReplDoType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "REPL_DO_TYPE";
        }
    }

    /**
     * LPN NO 前缀 1. PAW 普通 LPN 2. VAL 贵重品 LPN
     */
    public enum LpnNoPrefix {
        PAW("PAW"), VAL("VAL");

        private String value;

        LpnNoPrefix(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 库位疑似缺货标识 NORMAL(0) 正常 ,LACK(1) 疑似缺货 ,INVENTORIED(2) 已生成盘点单
     */
    public enum LocLackFlag {
        NORMAL(Integer.valueOf(0)), LACK(Integer.valueOf(1)), INVENTORIED(Integer.valueOf(2));

        private Integer value;

        LocLackFlag(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 预约单状态 INITIAL("00"), 初始化 ARRIVED("10"), 已到货 PARTRECEIVE("40"), 收货中 RECEIVED("80"), 收货完成
     * RENEGED("90"),已违约REMOVE("98"), 已移除 CANCEL("99"); 已取消
     */
    public enum ReservationStatus {
        INITIAL("00"), ARRIVED("10"), PARTRECEIVE("40"), RECEIVED("80"), REMOVE("98"), CANCEL("99"), RENEGED("90");

        private String value;

        ReservationStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "RESERVATION_STATUS";
        }
    }

    /**
     * 预约时间片状态
     */
    public static enum ResTimeStatus {
        FORBID("0"), IS_AVAILABLE("1"), USED("2"), ARRIVED("3"), PART_RECEIVE("4"), ALL_RECEIVED("5");

        private final String value;

        ResTimeStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "RES_TIME_STATUS";
        }
    }

    /**
     * 月台类型 说明: 00 收货月台
     */
    public enum DockType {
        RECEIVE("0");

        private String value;

        DockType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "DOCK_TYPE";
        }
    }

    /**
     * 暂存位类型
     */
    public enum TempLocationType {
        LACK(0), RTV(1);

        private final Integer value;

        TempLocationType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "TEMP_LOC_TYPE";
        }
    }

    /**
     * 供应商预约返回错误代码 NORMAL("00"),正常 PONOTEXIST("10"),PO不存在 POTYPEERROR("11"),PO类型错误 POSTATUSERROR("12"),PO状态错误
     * POISRES("13"),PO已存在预约 PORESTIMElATE("14"),预约日期晚于PO过期日期 DOCKNOTEXIST("20"),月台不存在 DOCKSTATUSERROR("21"),月台状态错误
     * DOCKOVERLOADMAXUNITS("22"),月台超过unit上限 TIMENOTEXIST("30"),时间片不存在 TIMESTATUSERROR("31"),时间片状态错误
     * TIMEERROR("32"),时间片异常 NOFIXEDRESERVATION("35"),仓库未做预约设置 ASNNOTEXIST("36"),调拨单号不存在 RESSYNPMSFAILD("37"),预约同步pms失败
     * PHONENUMBERISNULL("40"),手机号为空 OTHERS("50"),其他错误（并发等等）
     */
    public enum ReservationErrorCode {
        NORMAL("00"), PONOTEXIST("10"), POTYPEERROR("11"), POSTATUSERROR("12"), POISRES("13"), PORESTIMElATE("14"),
        DOCKNOTEXIST("20"), DOCKSTATUSERROR("21"), DOCKOVERLOADMAXUNITS("22"), TIMENOTEXIST("30"),
        TIMESTATUSERROR("31"), TIMEERROR("32"), NOFIXEDRESERVATION("35"), ASNNOTEXIST("36"), RESSYNPMSFAILD("37"),
        PHONENUMBERISNULL("40"), OTHERS("50");

        private String value;

        ReservationErrorCode(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 波次分配分拣柜时机 0 生成波次时 1 拣货绑定容器、换箱、拆箱时 2 RF重新分配分拣柜页面
     */
    public enum WaveAllocateSortingBin {
        WAVE(0), PICK(1), REALLOCATE(2);

        private final Integer value;

        WaveAllocateSortingBin(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 操作类型 0代表取消, 1代表新增, 2代表修改
     */
    public enum OperationType {
        OPERATION_CANCEL(0), OPERATION_INSERT(1), OPERATION_UPDATE(2);

        private final Integer value;

        OperationType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 预约类型 SELF_RES("0"),自主预约 TEL_RES("1"),电话预约 TT_RES("2"),调拨预约 LOGISTIC_PROVIDER_RES("3"),物流商预约
     */
    public enum ResType {
        SELF_RES("0"), TEL_RES("1"), TT_RES("2"), LOGISTIC_PROVIDER_RES("3");

        private String value;

        ResType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "RES_TYPE";
        }
    }

    /**
     * 越库库存状态 INITIAL("0") 初始化 CANCELD("1")已取消 ALL_TO_LOCAL("2") 已转本地 SHIPPED("3") 已发货 ,PACKING("4") 装箱中
     */
    public enum StockCDStatus {
        INITIAL("0"), CANCELD("1"), ALL_TO_LOCAL("2"), SHIPPED("3"), PACKING("4");

        private String value;

        StockCDStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "STOCK_CD_STATUS";
        }
    }

    /**
     * STAGE-CD-DM 越库坏品暂存库位 STAGE-CD 越库暂存库位 STAGE STAGE-DM STAGE_JG 加工暂存位
     */
    public enum SpecialLocCode {
        STAGE_CD_DM("STAGE-CD-DM"), STAGE_CD("STAGE-CD"), STAGE("STAGE"), STAGE_DM("STAGE-DM"),
        STAGE_LACK("STAGE-LACK"), STAGE_JG("STAGE-JG"), ZP("ZP");

        private String value;

        SpecialLocCode(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * PARTITION_RETURN RETURN库区
     */
    public enum SpecialPartitionCode {
        PARTITION_RETURN("RETURN");

        private String value;

        SpecialPartitionCode(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 预约周期
     */
    public static enum LoopMode {
        DAY(0), // 每天
        WEEK(1), // 每周
        MONTH(2);// 每月

        private final Integer value;

        LoopMode(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "LOOP_MODE";
        }
    }

    /**
     * 固定预约类型
     */
    public static enum FixedResType {
        SUPPLIER(0), WAREHOUSE(1), LOGISTICPROVIDER(2);

        private final Integer value;

        FixedResType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "FIXED_RES_TYPE";
        }
    }

    /**
     * 特殊仓库名称
     */
    public static enum SpecialWarehouseName {
        ALLWAREHOUSE("全部仓库");

        private final String value;

        SpecialWarehouseName(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 星期
     */
    public static enum Week {
        MONDAY(1), TUESDAY(2), WEDNESDAY(3), THURSDAY(4), FRIDAY(5), SATURDAY(6), SUNDAY(7);

        private final Integer value;

        Week(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "WEEK";
        }
    }

    /**
     * 订单【登记】标识: CONTRACT_PHONE(1) 合约机订单 REAL_NAME_NIC(2) 实名制网卡订单 PHONE_NO_NET_PURCHASING(3) 选号入网订单 ,COMBI_SKU(4) 组合订单
     */
    public static enum OrderRegisterFlag {
        CONTRACT_PHONE(1), REAL_NAME_NIC(2), PHONE_NO_NET_PURCHASING(3),COMBI_SKU(4);

        private final Integer value;

        OrderRegisterFlag(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "ORDER_REGISTER_FLAG";
        }
    }

    /**
     * 库区特殊标识： 普通 NORMAL —— 0 券类品 COUPONS —— 1
     */
    public static enum PartitionSpecialFlag {
        NORMAL(0), COUPONS(1);

        private final Integer value;

        PartitionSpecialFlag(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "PARTITION_SPECIAL_FLAG";
        }
    }

    /**
     * 预约收货更新标识： NOTRESERVATION —— 非预约类型 RESSWITCHON —— 预约开关开 RESSWITCHOFF —— 预约开关关
     */
    public static enum ResUpdateType {
        NOTRESERVATION("非预约类型"), RESSWITCHON("预约开关开"), RESSWITCHOFF("预约开关关");

        private final String value;

        ResUpdateType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 预约单元格样式枚举： cell-red —— 禁约 cell-green —— 可约 cell-yellow —— 已约 cell-arrived —— 已到货 cell-partreceive —— 收货中
     * cell-allreceived —— 收货完成 cell-supplier —— G 供应商固定预约 cell-warehouse —— K 仓库固定预约 cell-logisticProvider —— L 物流商固定预约
     */
    public static enum ResCellStyle {
        CELL_RED("cell-red"), CELL_GREEN("cell-green"), CELL_YELLOW("cell-yellow"), CELL_ARRIVED("cell-arrived"),
        CELL_PARTREVEIVE("cell-partreceive"), CELL_ALLREVEIVED("cell-allreceived"), CELL_SUPPLIER("cell-supplier"),
        CELL_WAREHOUSE("cell-warehouse"), CELL_LOGISTICPROVIDER("cell-logisticProvider");

        private final String value;

        ResCellStyle(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 预约单元格选择枚举： UI_SELECTED —— 已选择 UI_UNSELECTED —— 未选择
     */
    public static enum ResCellSelect {
        UI_SELECTED("ui-selected"), UI_UNSELECTED("");

        private final String value;

        ResCellSelect(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 预约单元格选择枚举： SUPPLIER_FIXEDRES —— 供应商固定预约 WAREHOUSE_FIXEDRES —— 调出仓库固定预约 NOT_FEXEDRES —— 非固定预约
     */
    public static enum ResFixedResType {
        SUPPLIER_FIXEDRES("G"), WAREHOUSE_FIXEDRES("K"), NOT_FEXEDRES("");

        private final String value;

        ResFixedResType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * ASN退货类型
     * <p>
     * NORMAL(0) 普通退货ASN GIFT_CARD_RTV(1) 礼品卡退货ASN
     * <p>
     */
    public static enum RtvType {
        NORMAL(0), GIFT_CARD_RTV(1);

        private final Integer value;

        RtvType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * ASN退货类型
     * <p>
     * OPTIONAL(1) 可选 MUST(2) 必须输入 MUSTNOT(3) 禁止输入
     * <p>
     */
    public static enum BatchCfgControl {
        OPTIONAL(1L), MUST(2L), MUSTNOT(3L);

        private final Long value;

        BatchCfgControl(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }

    /**
     * 未打印 0 已打印 1 打印中 2
     */
    public static enum WavePrintFlag {
        NOT_PRINT(0L), PRINTED(1L), PRINTING(2L);

        private final Long value;

        WavePrintFlag(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }

    /**
     * 批次数据类型
     * <p>
     * STRING("1") 字符串 NUMBER("2") 数字 DATE("3") 日期
     * <p>
     */
    public static enum BatchDateType {
        STRING("1"), NUMBER("2"), DATE("3");

        private final String value;

        BatchDateType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * ONLINE: 网站自营; CALL: 电话购买; POS：门店购买; QQMAIL：QQ网购; TMALL: 天猫; YIYUAN: 掌上医院; YHD: 一号店; JD: 京东; GUOMEI：国美
     */
    public static enum OrderSource {
        ONLINE(0), CALL(1), POS(2), QQMALL(3), TMALL(4), YIYUAN(5), YHD(11), JD(12), GUOMEI(13);

        private final Integer value;

        OrderSource(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 需要校验批次的类型 NORMAL_IN(1) 普通类型收货, RTV_IN(2) 退货入库, TT_IN(3) 调拨入库， TRANS_IN(4) 搬仓入库, OVERAGE_IN(5) 损益溢入库;
     */
    public static enum BatchOrgType {
        NORMAL_IN(1), RTV_IN(2), TT_IN(3), TRANS_IN(4), OVERAGE_IN(5);

        private final Integer value;

        BatchOrgType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 库存转换状态 INITIAL(00) 转换中ALLOCATING(30) 初始化 ALLOCATED(40) 分配完成 CONVERTING(60) 转换中 COMPLETE(99) 已完成 CANCEL(90) 已取消
     */
    public enum StockConvertStatus {
        INITIAL("00"), ALLOCATING("30"), ALLOCATED("40"), CONVERTING("60"), COMPLETE("99"), CANCEL("90");

        private String value;

        StockConvertStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "STOCK_CONERT_STATUS";
        }
    }

    /**
     * 通知客服的方式
     */
    public enum NotifyCSType {
        MANNUAL(0), AUTO(1);

        private final Integer value;

        NotifyCSType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 逆向交接单状态
     * <p>
     * WAIT_RECEIVE("10") 待收 RECEIVED("80") 已收
     */
    public enum ReversalStatus {
        WAIT_RECEIVE("10"), RECEIVED("80");

        private final String value;

        ReversalStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "REVERSAL_HANDOVER_STATUS";
        }
    }

    /**
     * cubi测量后上架任务发布状态
     */
    public enum CubiPublishStatus {
        ERROR(-1), INITIAL(0), PUBLISHED(1);

        private final Integer value;

        CubiPublishStatus(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    public enum PrintStatus {
        YES(1), NO(0), PARTIAL(2);

        private final int value;

        PrintStatus(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    // 自动波次类型
    public enum AutoWaveType {
        NORMAL("普通", 1), BIG_VOLUME("大体积", 12), BATCH_GROUP("批量团购", 20),
        MILA("MILA", 30), BOND_GIFT("非保赠品", 40);

        private final Integer value;

        private String displayName;

        AutoWaveType(String displayName, Integer value) {
            this.displayName = displayName;
            this.value = value;
        }

        public String getDisplayName() {
            return displayName;
        }

        public Integer getValue() {
            return value;
        }

        public static AutoWaveType of(Integer value) {
            for (AutoWaveType type : values()) {
                if (type.getValue().equals(value)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Cannot find enum type with value " + value);
        }

        public static final String dictionaryCode() {
            return "WAVE_TYPE_SUBSECTION";
        }
    }

    /*
     * 打印机类型
     */
    public enum PrinterType {
        A4A5("A4/A5"), LABEL("标签"), TRIPLICATE("三联发票"), ROLL("卷式发票");

        private String displayName;

        PrinterType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return this.displayName;
        }
    }

    public enum MaterialDocInStatus {
        INITIAL("00"), PARTRECEIVE("10"), RECEIVED("40"), CHECKED("99"), CANCLE("90");

        private final String value;

        MaterialDocInStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "MATERIAL_DOC_IN_STATUS";
        }
    }

    /**
     * BUY 采购入库 INVENTORY 盘盈入库 RECYLE 二手回收 RTV调拨入库 OTHER 其他
     */
    public enum MaterialDocInType {
        BUY("1"), INVENTORY("2"), RECYLE("3"), RTV("4"), OTHER("5");

        private final String value;

        MaterialDocInType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "MATERIAL_DOC_IN_TYPE";
        }
    }

    /**
     * 仓库类型
     */
    public enum WarehouseType {
        /**
         * 保税仓库
         */
        BONDED(1),
        /**
         * 大贸仓库
         */
        TRADE(2),
        /**
         * 中心仓
         */
        CDC(3)
        ;

        private final Integer value;

        WarehouseType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

    }

    /**
     * CANCEL取消 CS_CANCEL客服取消 ALLOC分配冻结 MANUAL_HOLD手动缺货 PICK_LACK拣货缺货 PICK_DAMAGE拣货破损 SORT_HD分拣冻结 SORT_LACK分拣缺货
     * CHECK_LACK核拣缺货 SORT_DM分拣破损 CHECK_DM 核拣破损 IN_LACK_LOC入暂存位，OUT_LACK_LOC出暂存位 SKIP_CS跳过客服 CALL_CS 通知客服
     * CS_RL客服反馈释放，CS_WAIT客服反馈等货，CS_DELETE客服反馈删除缺货明细 ROLL_BACK状态回退，REPICK反拣，REPRINT重打发货单 FORCE_PICK强制拣货，RF_RLRF释放RL释放
     */
    public enum ExOpType {
        CANCEL("00"), CS_CANCEL("10"), ALLOC("11"), MANUAL_HOLD("12"), PICK_LACK("13"), PICK_DAMAGE("14"),
        SORT_HD("15"), SORT_LACK("16"), CHECK_LACK("17"), SORT_DM("18"), CHECK_DM("19"), IN_LACK_LOC("20"),
        OUT_LACK_LOC("21"), SKIP_CS("30"), CALL_CS("40"), CS_RL("50"), CS_WAIT("51"), CS_DELETE("52"), ROLL_BACK("60"),
        REPICK("61"), REPRINT("62"), FORCE_PICK("70"), RF_RL("71"), RL("72"), ARRIVAL_RL("73"),
        COMPLETE_LACK_PICK("74");

        private final String value;

        ExOpType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "EX_OP_TYPE";
        }
    }

    /**
     * 预约适用类型
     * <p>
     * SPREAD_RESVATION(0) 零散预约 FOCUS_RESVATION(1) 集中预约
     */
    public enum ResApplyType {
        SPREAD_RESVATION(0), FOCUS_RESVATION(1);

        private final Integer value;

        ResApplyType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "RES_APPLY_TYPE";
        }
    }

    /**
     * 预警级别
     */
    public enum AlertLevel {
        LEVEL0(0), LEVEL1(1), LEVEL2(2), LEVEL3(3), LEVEL4(4);

        private final Integer value;

        AlertLevel(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "ALERT_LEVEL";
        }
    }

    /**
     * 发票模板版本
     * <p>
     * NORMAL-----------0 三联发票 NORMAL_ROLL------1 普通卷式发票 MEET_ALL_ROLL----2 美特好卷式发票
     */
    public enum InvoiceVersion {
        NORMAL(0), NORMAL_ROLL(1), MEET_ALL_ROLL(2);

        private final Integer value;

        InvoiceVersion(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 找货任务生成状态
     */
    public enum FindLackCreateStatus {
        NOT_CREATED("0"), CREATED("1"), COMPLETED("2");

        private String value;

        FindLackCreateStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "FIND_LACK_CREATE_STATUS";
        }
    }

    public enum FindLackPrintStatus {
        NO_PRINT("0"), PRINTED("1");

        private String value;

        FindLackPrintStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "FIND_LACK_PRINT_STATUS";
        }
    }

    /**
     * 统计自动波次kpi的方式 WAVE---0-----按波次数量统计 DO ---1-----按订单数量统计
     */
    public enum StatisticAutoWavePattern {
        WAVE(1), DO(2);

        private final Integer value;

        StatisticAutoWavePattern(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "STA_WAVE_KPI_PATTERN";
        }
    }

    public enum PartitionType {
        NORMAL("0"), RVS("1");

        private final String value;

        PartitionType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 指定类型（按照枚举所列维度制定规则，订单匹配规则，在生成波次时按照规则划分订单并单独波次）
     * <p>
     * SKU —— 商品 PARTITION —— 库区
     */
    public enum SpecializedDoPropType {
        SKU("商品"), PARTITION("库区");

        private final String displayName;

        SpecializedDoPropType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * 关系枚举 EQ —— 等于 HAVE —— 包含 ONLY —— 仅限于 NOT_HAVE —— 不包含
     */
    public enum Relation {
        EQ("等于"), HAVE("包含"), ONLY("仅限于"), NOT_HAVE("不包含");

        private final String displayName;

        Relation(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * 劳动力管理模式 0：关闭 1：并行拣货 2：接力拣货
     */
    public enum LaborForceSwitch {
        OFF(0), PARALLEL(1), RELAY(2);

        private final Integer value;

        LaborForceSwitch(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "LABOR_FORCE_SWITCH";
        }
    }

    /**
     * 核拣方式 DO:发货单 SC:分拣筐 SKU:分拣筐+SKU
     */
    public enum RecheckType {
        DO("DO"), SC("SC"), SKU("SKU");

        private final String value;

        RecheckType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 团购订单划分模式
     * <p>
     * CARRIER ——按配送商 WEIGHT ——按重量 UNITS ——按units
     */
    public enum GroupOrderDivideMode {
        CARRIER(0), WEIGHT(1), UNITS(2);

        private final Integer value;

        GroupOrderDivideMode(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 重測指令状态 00:未測 10:已測
     */
    public enum RetestStatus {
        INITIAL("00"), TESTED("10");

        private String value;

        RetestStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 特殊用户
     */
    public enum SpecialUser {
        CUBI_SCAN("cubiscan"), SYSTEM("system");

        private String value;

        SpecialUser(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 业务类型 PO_IN : PO入库 MOVE_OUT : 调拨出库 MOVE_IN : 调拨入库 DO_RTV : DO退货
     */
    public enum BusType {
        PO_IN(21), MOVE_OUT(22), MOVE_IN(23), DO_RTV(101);

        private Integer value;

        BusType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 规则类型 PERCENT : 百分比 SURPLUS_DAYS : 剩余天数
     */
    public enum RuleType {
        PERCENT("P"), SURPLUS_DAYS("D");

        private String value;

        RuleType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    // 定义配送商公司
    public enum CarrierCorp {
        SF(1L), // 顺丰
        ZJB(2L), // 宅急便
        ZJS(3L), // 宅急送
        JD(4L), // 京东
        YOUPAI(5L), // 优派
        POSTB(6L), // 邮政小包
        YTO(7L), // 圆通;
        YUNDA(8L), // 韵达
        STO(9L), // 申通
        HTKY(10L), // 百世汇通
        ZTO(11L);// 中通

        private Long value;

        CarrierCorp(Long value) {
            this.value = value;
        }

        public String getSFName() {
            return "顺丰";
        }

        public String getZJBName() {
            return "宅急便";
        }

        public String getZJSName() {
            return "宅急送";
        }

        public String getJDName() {
            return "京东";
        }

        public String getYOUPAIName() {
            return "优配";
        }

        public String getPOSTBName() {
            return "邮政小包";
        }

        public Long getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "CARRIER_CORP";
        }
    }

    // 定义电子面单类型
    public enum WaybillType {
        // 顺丰
        SF,
        // 菜鸟
        CAINIAO,
        // 拼多多
        PDD,
        // 京东
        JD,
        // 京配
        JP,
        // 喜鹊
        XQ,
        // 圆通
        YTO,
        // 增益
        ZENY,
        // 宅急送
        ZJS,
        // 城市100
        CITY100,
        // 韵达
        YUNDA,
        // 优派
        YOUPAI,
        // EMS
        EMS,
        // 申通
        STO,
        // 百世汇通
        HTKY,
        // 中通
        ZTO,
        //图片
        IMAGE,
        PDF,
        // 邮政小包,导入号段方式
        POSTB_IMPORT,
        // 方舟国际速递
        FZGJ,
        // 顺丰仓-跨越速运
        KYE,
        //丰网快递
        FWSY,
        // 京东重货
        JDZH,
        // 极兔
        JT

        ;
    }

    // 订单申报状态
    public enum DoAuthorizeStatus {
        NOT_SEND(0), SEND(1), AUTHORIZED(2);

        private Integer value;

        DoAuthorizeStatus(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    public enum MedicineCodeValidationType {
        NO_NEED(0), OPTIONAL(1), REQUIRED(2);

        private Integer value;

        private MedicineCodeValidationType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 测量状态 NO_MEASURE 未测量 MEASURED 已测量 RETEST 重测
     */
    public enum MeasureStatus {
        NO_MEASURE(0), MEASURED(1), RETEST(2);

        private Integer value;

        private MeasureStatus(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

    }

    /**
     * 是否需要XXX<br/>
     * NEED_NOT 不需要 OPTIONAL 可选 MUST 必须要
     */
    public enum NeedOrNot {
        NEED_NOT(0), OPTIONAL(1), MUST(2);

        private Integer value;

        private NeedOrNot(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 优先级：0、1、2、3<br/>
     * 目前用到的地方如下：<br/>
     * 销量预测数据的处理优先级（预测当天优先级1；明天2；后天3）
     */
    public enum Priority {
        ZERO(0), FIRST(1), SECOND(2), THIRD(3);

        private Integer value;

        private Priority(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 工种类型：间接劳动+直接劳动
     */
    public enum JobType {
        DIRECT("DIRECT"), INDIRECT("INDIRECT");

        private String value;

        private JobType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 签到+签退+扫工种
     */
    public enum OperateType {
        CHECK_IN("CHECK_IN"), CHECK_OUT("CHECK_OUT"), JOB("JOB");

        private String value;

        private OperateType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum IndexContentType {
        NEW_FEATURES("newFeatures"), NEWS("news"), LINKS("links");

        private String value;

        private IndexContentType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum JobKind {
        RECEIVE("receive"), PUTARAY("putAway"), PICK("pick"), SORT("sort"), RECHECK("recheck");

        private String value;

        private JobKind(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum JobKindCh {
        PICK("拣货"), SORT("分拣"), RECHECK("包装");

        private String value;

        private JobKindCh(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum WorkingShift {
        Morning("早班"), Noon("中班"), Evening("晚班");

        private String value;

        private WorkingShift(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 上/下午
     */
    public enum PeriodType {
        AM("AM"), PM("PM");

        private String value;

        PeriodType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "AM_PM_STATUS";
        }
    }

    /**
     * 预约类型 SELF_RES("0"),自主预约 TEL_RES("1"),电话预约 TT_RES("2")
     */
    public enum RtvResType {
        SELF_RES("0"), TEL_RES("1"),
        ;

        private String value;

        RtvResType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "RTV_RES_TYPE";
        }
    }

    /**
     * 项目kpi统计类型
     */
    public enum KpiType {
        AUTO_WAVE("自动波次占比"), LOC_RECOMMEND("库位推荐采纳率"), REPL("闲时补货占比"), REPL_NEW("闲时补货占比(新)"), LABOR("劳动力使用率"),
        PACK_MATERIAL("包材推荐采纳率");

        private final String displayName;

        KpiType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * 位移类型
     *
     * @description COMMON_MOVE 普通移库 <br />
     * BATCH_MOVE 按照批次移库
     */
    public enum RFMoveType {
        COMMON_MOVE, BATCH_MOVE;
    }

    /**
     * <pre>
     * Description:人力在职状态  0：离职  1： 在职
     * </pre>
     */
    public enum EmployeeStatus {
        NORMAL("1"), RESIGNED("0");

        private String value;

        EmployeeStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "EMPLOYEE_STATUS";
        }
    }

    /**
     * <pre>
     * Description:逾期退单处理方式
     * 	保留售卖:KEEP_SELL 报废:SCRAP 供应商提货:SUP_DELIVERY
     * </pre>
     */
    public enum OverdueRtvDealWay {
        KEEP_SELL("1"), SCRAP("2"), SUP_DELIVERY("3");

        private String value;

        OverdueRtvDealWay(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "OVERDUE_RTV_DEALWAY";
        }
    }

    /**
     * <pre>
     * Description:逾期退单状态
     * 	初始化:INITIAL 处理中:HANDLING 处理完成:DONE
     * </pre>
     */
    public enum OverdueRtvStatus {
        INITIAL("00"), HANDLING("40"), DONE("80");

        private String value;

        OverdueRtvStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "OVERDUE_RTV_STATUS";
        }
    }

    /**
     * 正常退货:INITIAL 逾期处理:HANDLING 弃货处理:DONE
     */
    public enum dealType {
        NORMAL_RTV("1"), OVERDUE_RTV("2"), JETTISON_RTV("3");

        private String value;

        dealType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "DEAL_TYPE";
        }
    }

    /**
     * 波次类型细分 名字-波次类型-自动波次类型
     */
    public static enum RoleTaskType {
        COMMON("普通", 3, null), AUTO_COMMON("普通", 3, 1), HNAD_WAVE("手动波次", 3, -1), BATCH_GROUP("批量团购", 3, 20), //
        RTV("RTV", 6, null), //
        ALLOT("调拨", 7, null), //
        WAVE_2B("批发", 9, null);//

        private String name;

        private Integer type1;

        private Integer type2;

        RoleTaskType(String name, Integer type1, Integer type2) {
            this.name = name;
            this.type1 = type1;
            this.type2 = type2;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getType1() {
            return type1;
        }

        public void setType1(Integer type1) {
            this.type1 = type1;
        }

        public Integer getType2() {
            return type2;
        }

        public void setType2(Integer type2) {
            this.type2 = type2;
        }

        /**
         * 获得父节点
         *
         * @return
         */
        public static List<RoleTaskType> getParent() {
            List<RoleTaskType> types = new ArrayList<RoleTaskType>();
            for (RoleTaskType taskType : values()) {
                if (taskType.getType2() == null) {
                    types.add(taskType);
                }
            }
            return types;
        }

        /**
         * 获得子节点
         *
         * @param id
         * @return
         */
        public static List<RoleTaskType> getChildren(Integer id) {
            List<RoleTaskType> types = new ArrayList<RoleTaskType>();
            for (RoleTaskType taskType : RoleTaskType.values()) {
                if (id.equals(taskType.getType1()) && taskType.getType2() != null) {
                    types.add(taskType);
                }
            }
            return types;
        }

        public static String getTypeName(Integer type1, Integer type2) {
            List<RoleTaskType> parentTypes = getParent();
            if (type2 == null) {
                for (RoleTaskType roleTaskType : parentTypes) {
                    if (roleTaskType.getType1().equals(type1)) {
                        return roleTaskType.getName();
                    }
                }
            } else {
                for (RoleTaskType parentType : parentTypes) {
                    if (parentType.getType1().equals(type1)) {
                        List<RoleTaskType> childTypes = getChildren(type1);
                        for (RoleTaskType roleTaskType : childTypes) {
                            if (roleTaskType.getType2().equals(type2)) {
                                return parentType.getName() + "-" + roleTaskType.getName();
                            }
                        }
                    }
                }
            }
            return null;
        }

        /**
         * <pre>
         * 	上架快照类型
         * Description:
         * 	收货时推荐上架库位状态:PLAN_TO_LOC 实际上架时目标库位状态:ACT_TO_LOC 实际上架时推荐库位状态:ACT_PLAN_LOC
         * </pre>
         */
        public enum snapShortType {
            PLAN_TO_LOC(1), ACT_TO_LOC(2), ACT_PLAN_LOC(3);

            private Integer value;

            snapShortType(Integer value) {
                this.value = value;
            }

            public Integer getValue() {
                return value;
            }

            public static final String dictionaryCode() {
                return "SNAP_SHORT_TYPE";
            }
        }

        public enum PacketMaterial {
            /**
             * 纸箱
             */
            CARTON(2L);

            private Long value;

            PacketMaterial(Long value) {
                this.value = value;
            }

            public Long getValue() {
                return value;
            }
        }
    }

    /* TMS派车单 箱实发虚发标记 */
    public enum TmsShipType {
        NOT_DELIVERED(0), DELIVERED(1);

        private Integer value;

        TmsShipType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "TMS_SHIP_TYPE";
        }
    }

    /**
     * <pre>
     * Description:派车单批量收货状态
     * 	初始化:INITIAL 已提交:SUBMITED 处理中:HANDLING 处理完成:DONE
     * </pre>
     */
    public enum ShipmentReceiveStatus {
        INITIAL("00"), SUBMITED("10"), HANDLING("40"), DONE("80");

        private String value;

        ShipmentReceiveStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "SHIPMENT_RECEIVE_STATUS";
        }
    }

    /**
     * NO_RVS 可保存 RVS 不可保存
     */
    public enum GalSkuType {
        NO_RVS(1), RVS(2);

        private Integer value;

        GalSkuType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * <pre>
     * 任务获取类型
     * </pre>
     */
    public enum TaskObtainType {

        /**
         * 被主动索取的
         */
        OBTAINED_ACTIVELY(1),

        /**
         * 被人工指派的
         */
        DISPATCHED(2);

        private Integer value;

        TaskObtainType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 补打发票类型
     */
    public enum PrintInvoiceType {
        /**
         * 按波次补打
         */
        WAVE(0),
        /**
         * 按发货单补打
         */
        DO(1);

        private Integer value;

        PrintInvoiceType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 补打发票类型
     */
    public enum InvoicePrintFlg {
        /**
         * 初始化
         */
        INIT(0),
        /**
         * 已发送
         */
        SENDED(1),

        /**
         * 已绑定
         */
        BINDED(2);

        private Integer value;

        InvoicePrintFlg(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 存储条件 1:常温 2:冷藏 3:冷冻
     */
    public enum KeepCondition {
        NORMAL(1), COOL(2), REFRIGERATION(3), FROZEN(4);

        private Integer value;

        KeepCondition(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "STORAGE_CONDITION";
        }
    }

    /**
     * 存储条件 0:质检员
     */
    public enum SpvRole {
        QC(0);

        private Integer value;

        SpvRole(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 电子发票类型 1：普通开具 2：整单冲红 3：换票冲红
     */
    public enum EInvoiceIssueType {
        NORMAL(1), WHOLE_CH(2), EXCHANGE_CH(3);

        private Integer value;

        EInvoiceIssueType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * AISINO_GZ 青岛电子发票平台
     */
    public enum EInvoicePlateform {
        QD_CHINAEINV("QD_CHINAEINV"), AISINO_GZ("AISINO_GZ");

        private String value;

        EInvoicePlateform(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum LimitedOperType {
        STOCK_EXPORT("1"), DO_EXPORT("2");

        private String value;

        LimitedOperType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 首页权限编码
     */
    public enum WorkBenchCode {
        CURRENT_DO("home.work.benck.current.do"), CURRENT_ASN("home.work.benck.current.asn"),
        NOT_PICK("home.work.benck.not.pick.do"), NOT_REPL("home.work.benck.not.repl.do"),
        EXCEPTION_DO("home.work.benck.exception.do"), NOT_SHIP("home.work.benck.not.ship.do"),
        DO_STATUS("home.work.benck.do.status"), PERIOD("home.work.benck.period"),
        HIGH_STOCK("home.work.benck.high.stock"), UPH("home.work.benck.uph"), TPH("home.work.benck.tph"),
        ONLINE_USER("home.work.benck.online.user"), EXCEPTION_INTERFACE("home.work.benck.exception.interface"),
        DO_TYPE("home.work.benck.do.type"), DO_SOURCE("home.work.benck.do.source"),
        DO_LOGISTICS("home.work.benck.do.logistics"), ASN_AND_DO("home.work.benck.doAndAsn"),
        REC_DELIVERY("home.work.benck.receiveAndDelivery"), STOCK_VOLUME("home.work.benck.stockVolumn");

        private String value;

        WorkBenchCode(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static WorkBenchCode getWorkBenchCode(String code) {
            WorkBenchCode[] codes = WorkBenchCode.values();
            for (WorkBenchCode workBenchCode : codes) {
                if (workBenchCode.getValue().equalsIgnoreCase(code)) {
                    return workBenchCode;
                }
            }
            return null;
        }
    }

    /**
     * sku默认库位01 默认库区02 目标库位03 目标库区04 品类默认的库区05 最近操作的库位06 最近操作的库位附近的货位07
     */
    public enum DirectionRule {
        SKU_DEFAULT_LOC("01"), SKU_DEFAULT_PARTITION("02"), TARGET_LOCATION("03"), SKU_TARGET_PARTITION("04"),
        CATG_DEFAULT_PARTITION("05"), LATEST_OPERATE_LOCATION("06"), LATEST_OPERATE_NEAR_BY_LOCATION("07"),
        DEFAULT_PICK_LOC("08"), DEFAULT_PICK_LOC_NEARBY("09"), REPLENISH_ROUTE("10");

        private String value;

        DirectionRule(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "DIRECTION_RULE";
        }
    }

    /**
     * 返拣原因 数据字典：REVERSE_PICK_REASON
     */
    public enum ReversePickReason {
        /**
         * 核拣缺发
         */
        HJQF("15");

        private String value;

        ReversePickReason(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 耗材交易日志类型
     */
    public enum TrsMaterialsType {
        IN("1"), OUT("2"), SCAN("3");

        private String value;

        TrsMaterialsType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "TRS_MATERIALS_TYPE";
        }
    }

    public enum LocRecConcentrateType {
        SAME_SKU("1"), SAME_BATCH("2");

        private String value;

        LocRecConcentrateType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "LOC_REC_CONCENTRATE_TYPE";
        }
    }

    public enum NoticeOperateType {
        PICK, SORT, RECHECK;
    }

    public enum DisposeType {
        REJECT(0), TEMP_IN_DM(1);

        private Integer value;

        DisposeType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "DISPOSE_TYPE";
        }
    }

    public enum CrossDockType {
        WAREHOUSE(0), CUSTOMER(1);

        private Integer value;

        CrossDockType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "CROSSDOCK_TYPE";
        }
    }

    public enum LocTypeOrigin {
        VOLUME(0), QUANTITY(1), TIHI(2);

        private Integer value;

        LocTypeOrigin(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "LOC_TYPE_ORIGIN";
        }
    }

    public enum WaveRuleType {
        SEARCH_TYPE(1), MATCH_TYPE(2);

        private Integer value;

        WaveRuleType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    public enum SyncDoNode {
        PACK("10"), // 装箱10
        WEIGHT("20"), // 称重20
        LOAD("30"), // 交接30
        NONE("99"); // 99不回单

        private String value;

        SyncDoNode(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum AutoLoadAndDeliverNode {
        PACK("10"), // 装箱10
        WEIGHT("20"), // 称重20
        NONE("99"); // 99不自动交接发货

        private String value;

        AutoLoadAndDeliverNode(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 商品无规则类型
     */
    public enum SkuNoRuleType {
        PA("1"), // 上架
        REPT("2"), // 补货
        ALC("3"), // 分配
        COUNT("4");// 盘点

        private String value;

        SkuNoRuleType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "SKU_NORULE_TYPE";
        }

    }

    /**
     * 说明 初始化00，分配中10， 预分配40，进行中50，已完成65
     */
    public enum CrossSeedStatus {
        INITIAL("00"), PARTALLOCATED("10"), ALLALLOCATED("40"), SORTING("50"), SORTED("65");

        private String value;

        CrossSeedStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "ASN_STATUS";
        }

        private static final Map<String, CrossSeedStatus> stringToEnum = new HashMap<String, CrossSeedStatus>();

        static {
            for (CrossSeedStatus op : values()) {
                stringToEnum.put(op.getValue(), op);
            }
        }

        public static CrossSeedStatus fromString(String symbol) {
            return stringToEnum.get(symbol);
        }
    }

    /**
     * 发票信息回写类型
     */
    public enum InvoiceWriteBackType {
        MERGE("0"), INVOICE("1");

        private String value;

        InvoiceWriteBackType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

    }

    public enum DoPrintInfoType {
        /**
         * 面单图片
         */
        WAYBILL_IMAGE_URL("WAYBILL_IMAGE_URL"),
        /**
         * 面单json信息
         */
        WAYBILL_JSON("WAYBILL_JSON"),
        /**
         * 二维码流
         */
        QR_STREAM("QR_STREAM"),

        /**
         * PDF文件流
         */
        PDF_STREAM("PDF_STREAM"),

        /**
         * 面单图片文件流
         */
        WAYBILL_IMAGE_STREAM("WAYBILL_IMAGE_STREAM");

        private String value;

        DoPrintInfoType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

    }

    /**
     * 冻结原因
     */
    public enum FreezeReason {
        INNER_WORK("质量问题-仓作业"), CHECK("质量问题-巡仓"), FEEDBACK("质量问题-客诉"), ALMOST_EXPIRED("临期");

        FreezeReason(String value) {
            this.value = value;
        }

        private String value;

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "FREEZE_REASON";
        }

        public static String getReasonDesc(String name) {
            for (FreezeReason reason : FreezeReason.values()) {
                if (reason.name().equals(name)) {
                    return reason.value;
                }
            }

            return null;
        }

    }

    /**
     * 冻结状态
     */
    public enum FreezeStatus {
        /**
         * 初始化
         */
        INIT("0"),
        /**
         * 冻结
         */
        FROZEN("1"),
        /**
         * 部分解冻
         */
        PART_UNFROZEN("2"),
        /**
         * 完成
         */
        COMPLETE("3");

        FreezeStatus(String value) {
            this.value = value;
        }

        private String value;

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "FREEZE_STATUS";
        }
    }

    /**
     * 解冻状态
     */
    public enum UnfreezeStatus {
        /**
         * 完成
         */
        COMPLETE("1");

        UnfreezeStatus(String value) {
            this.value = value;
        }

        private String value;

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "UNFREEZE_STATUS";
        }
    }

    /**
     * 商品类型数据字典
     */
    public enum ProductType {
        /**
         * 普通商品
         */
        NORMAL(10),
        /**
         * 非报关品之赠品
         */
        GIFT(20),
        /**
         * 非报关品之周边包材
         */
        MATERIALS(30);

        private Integer value;

        private ProductType(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 商品类型数据字典
     */
    public enum GdsStatus {

        // 1 保税
        // 2 非保
        // 3 展示
        // 4 先入区后报关
        BS(1), FB(2), ZS(3), XYG(4);

        private Integer value;

        private GdsStatus(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 商品类型数据字典
     */
    public enum SpecialLabelCode {
        /**
         * 纯Mila 订单
         */
        MILA("1"),
        /**
         * 混Mila 订单
         */
        MIX_MILA("2"),
        /**
         * 非报关品之赠品订单
         */
        GIFT("3"),
        /**
         * 重物订单
         */
        WEIGHT("4"),
        /**
         * 残次订单
         */
        CP("5");

        private String code;

        private SpecialLabelCode(String code) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public static final String dictionaryCode() {
            return "SPECIAL_LABEL_CODE";
        }

    }

    public enum MarkLogStatus {
        INIT(0),
        PROCESSING(1),
        SUCCESS(2),
        FAIL(3);

        private MarkLogStatus(Integer code) {
            this.code = code;
        }

        private Integer code;

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public static final String dictionaryCode() {
            return "MARK_LOG_STATUS";
        }

    }

    public enum TransportWendyEnum {
        HEAT(1, "防高温"),
        COLD(2, "防冻"),
        NORMAL(3, "常温"),
        HEATANDCOLD(4, "防冻防高温");

        private final Integer code;
        private final String desc;

        TransportWendyEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static TransportWendyEnum of(Integer code) {

            for (TransportWendyEnum transportWendyEnum : TransportWendyEnum.values()) {
                if (transportWendyEnum.getCode().equals(code)) {
                    return transportWendyEnum;
                }
            }

            throw new IllegalArgumentException("Cannot find enum type with code " + code);
        }

        public static TransportWendyEnum getBySkuAndAddress(Integer addressCode, Integer skuCode) {

            // 商品/地址 任意一个未配置则取常温
            if (Objects.isNull(addressCode) || Objects.isNull(skuCode)) {
                return TransportWendyEnum.NORMAL;
            }

            // 两个配置相同 取地址配置
            if (Objects.equals(addressCode, skuCode)) {
                return TransportWendyEnum.of(addressCode);
            }

            // 不相同 且存在常温的 取常温
            Integer normalCode = TransportWendyEnum.NORMAL.getCode();
            if (normalCode.equals(addressCode) || normalCode.equals(skuCode)) {
                return TransportWendyEnum.NORMAL;
            }

            // 不相同 地址防冻防高温的 取商品配置
            Integer heatAndCold = TransportWendyEnum.HEATANDCOLD.getCode();
            if (heatAndCold.equals(addressCode)) {
                return TransportWendyEnum.of(skuCode);
            }

            // 不相同 商品防冻防高温的 取地址配置
            if (heatAndCold.equals(skuCode)) {
                return TransportWendyEnum.of(addressCode);
            }

            // 均不相同
            return TransportWendyEnum.NORMAL;
        }

        /**
         * 根据运输温度优先级设置运输温度
         * @param wendyEnumSet
         * @return
         */
        public static TransportWendyEnum getTransportWendyEnumPriority(Set<TransportWendyEnum> wendyEnumSet) {
            if (CollectionUtils.isEmpty(wendyEnumSet)) {
                return TransportWendyEnum.NORMAL;
            }
            if (wendyEnumSet.contains(HEATANDCOLD)) {
                return TransportWendyEnum.HEATANDCOLD;
            }
            if (wendyEnumSet.contains(HEAT) && wendyEnumSet.contains(COLD)) {
                return TransportWendyEnum.HEATANDCOLD;
            }
            if (wendyEnumSet.contains(HEAT)) {
                return TransportWendyEnum.HEAT;
            }
            if (wendyEnumSet.contains(COLD)) {
                return TransportWendyEnum.COLD;
            }
            return TransportWendyEnum.NORMAL;
        }

    }

    public enum TraceabilityCodeOrderTypeEnum {
        IN(1, "入库"),
        OUT(2, "出库"),
        SALE_OUT(3, "销售出库");

        private final Integer code;
        private final String desc;

        TraceabilityCodeOrderTypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode(){
            return code;
        }

        public String getDesc(){
            return desc;
        }

    }
    public enum MpsOrderStatusEnum {
        DRAFT(0,"草稿"),
        SENT(1,"服务下发"),
        FINISHED(2,"服务完成"),
        CANCELED(3,"取消");

        private final Integer code;
        private final String desc;

        MpsOrderStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode(){
            return code;
        }

        public String getDesc(){
            return desc;
        }

    }
    public enum MpsProcessStatusEnum {
        UNSTART(0,"未开始"),
        WAITING_OUT(1,"待出库"),
        IN_PROCESS(2, "执行中"),
        PART_OUT(3,"部分出库"),
        ALL_OUT(4,"全部出库"),
        WAITING_IN(5,"待入库"),
        PART_FINISH(6,"部分完工"),
        FINISHED(7,"全部完工");
        private final Integer code;
        private final String desc;

        MpsProcessStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode(){
            return code;
        }

        public String getDesc(){
            return desc;
        }

    }
    public enum MpsOrderTypeEnum {
        PACKAGE(0,"增值组装服务"),
        BASE(1,"增值基础服务");
        private final Integer code;
        private final String desc;

        MpsOrderTypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
        public static MpsOrderTypeEnum getByCode(Integer code){
            for (MpsOrderTypeEnum value : MpsOrderTypeEnum.values()) {
                if(value.getCode().equals(code)){
                    return value;
                }
            }
            return null;
        }
        public Integer getCode(){
            return code;
        }

        public String getDesc(){
            return desc;
        }

    }
    public enum MpsProcessTypeEnum {


        /*****************************************************
         *                        增值组装服务
         ****************************************************/

        GOODS_ASSEMBLY(0, "库内商品组装"),
        GOODS_TRADE(1, "库内商品溯源"),

        GOODS_ASSEMBLY_TRADE(2, "库内商品组合"),

        GOODS_SPLIT(3, "库内商品拆套"),

        /*****************************************************
         *                        增值基础服务
         ****************************************************/
        BASE_SERVICE_4(4, "装/卸-卸货"),
        BASE_SERVICE_5(5, "装/卸-装货"),
        BASE_SERVICE_6(6, "报废-商品报废"),
        BASE_SERVICE_7(7, "盘点-额外盘点"),
        BASE_SERVICE_8(8, "打托-普通打托"),
        BASE_SERVICE_9(9, "打托-木架包装"),
        BASE_SERVICE_10(10, "打托-木箱包装"),
        BASE_SERVICE_11(11, "质检-商品全检"),
        BASE_SERVICE_12(12, "质检-其他质检"),
        BASE_SERVICE_13(13, "贴码-贴条码"),
        BASE_SERVICE_14(14, "贴码-贴溯源码"),
        BASE_SERVICE_15(15, "贴码-贴中文标"),
        BASE_SERVICE_16(16, "贴码-贴封口贴"),
        BASE_SERVICE_17(17, "添加或除去标识物-撕码"),
        BASE_SERVICE_18(18, "组装-单品单件组装"),
        BASE_SERVICE_19(19, "组装-单品多件组装"),
        BASE_SERVICE_20(20, "组装-多品单件组装"),
        BASE_SERVICE_21(21, "组装-多品多件组装"),
        BASE_SERVICE_22(22, "更换包装-更换包装-小件"),
        BASE_SERVICE_23(23, "更换包装-更换包装-中件"),
        BASE_SERVICE_24(24, "更换包装-更换包装-大件"),
        BASE_SERVICE_25(25, "更换包装-更换包装-超大件"),
        BASE_SERVICE_26(26, "添加或除去标识物-添加或去除配件"),
        BASE_SERVICE_27(27, "添加或除去标识物-添加或去除其他标识物"),
        BASE_SERVICE_28(28, "整理-商品擦拭"),
        BASE_SERVICE_29(29, "整理-商品整理"),
        BASE_SERVICE_30(30, "拆包-运营活动拆包"),
        BASE_SERVICE_31(31, "组装-预包装"),
        BASE_SERVICE_32(32, "拆包-拆预包"),
        BASE_SERVICE_33(33, "赠品配发-赠品配发-单件"),
        BASE_SERVICE_34(34, "赠品配发-赠品配发-多件"),
        BASE_SERVICE_35(35, "组装-撕膜溯源烟封"),
        BASE_SERVICE_36(36, "添加或除去标识物-撕膜"),
        BASE_SERVICE_37(37, "特殊增值-用车"),
        BASE_SERVICE_38(38, "特殊增值-电费"),
        BASE_SERVICE_39(39, "修复-修复"),
        BASE_SERVICE_40(40, "加工-溯源"),
        BASE_SERVICE_41(41, "加工-扫EV码"),
        BASE_SERVICE_42(42, "加工-组装"),
        BASE_SERVICE_43(43, "加工-撕膜溯源烟封"),
        BASE_SERVICE_44(44, "贴码-贴标签"),
        BASE_SERVICE_45(45, "贴码-贴宁波溯源码"),

        ;
        private final Integer code;
        private final String desc;

        MpsProcessTypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
        public static List<MpsProcessTypeEnum> getAddedService() {
            return Lists.newArrayList(GOODS_SPLIT, GOODS_TRADE, GOODS_ASSEMBLY_TRADE, GOODS_ASSEMBLY);
        }

        public static List<MpsProcessTypeEnum> getBaseService() {
            List<MpsProcessTypeEnum> mdsProcessTypeEnums = Lists.newArrayList(values());
            mdsProcessTypeEnums.removeAll(getAddedService());
            return mdsProcessTypeEnums;
        }
        public Integer getCode(){
            return code;
        }

        public String getDesc(){
            return desc;
        }

    }
    /**
     * 抽检状态：00 待抽检，01 抽检中，02 抽检异常，03 抽检通过，04 抽检取消
     */
    public enum RandomCheckStatus {

        /**
         * 待抽检 01
         */
        WAIT_CHECK("00"),

        /**
         * 抽检中 01
         */
        CHECKING("01"),
        /**
         * 抽检异常 02
         */
        CHECK_ERROR("02"),
        /**
         * 抽检通过 03
         */
        CHECKED("03"),
        /**
         * 抽检取消 04
         */
        CANCELED("04");
        private String value;

        RandomCheckStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "CHECK_STATUS";
        }

        private static final Map<String, RandomCheckStatus> stringToEnum = new HashMap<String, RandomCheckStatus>();

        static {
            for (RandomCheckStatus op : values()) {
                stringToEnum.put(op.getValue(), op);
            }
        }

        public static RandomCheckStatus fromString(String symbol) {
            return stringToEnum.get(symbol);
        }
        public static boolean isFinalStatus(String status){
            return Sets.immutableEnumSet(CHECKED,CANCELED).contains(fromString(status));
        }
    }

}