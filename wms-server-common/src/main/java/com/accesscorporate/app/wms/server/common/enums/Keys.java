package com.accesscorporate.app.wms.server.common.enums;

@lombok.extern.slf4j.Slf4j
public class Keys {
    
    public static enum System {
        /**
         * Android app：android
         **/
        android_updateUrl,//升级地址  system.android.updateUrl
        android_updateVersionCode, //升级版本  system.android.updateVersionCode
        android_updateMessage, //升级版本  system.android.updateMessage
        show_amount, //发货统计是否显示金额  system.show.amount


        /** Windows客户端：cs **/
    
    
        /** 服务器：server **/
    
        /** 规则：rul **/
    
        /** 基本：base **/
        base_environment,//系统环境名：比如百洋、主干线上、测试环境主干 system.base.environment
        base_isDemo, //是否演示环境                                    system.base.isDemo
        base_icpNo, //备案号                                           system.base.icpNo
        base_alterEmailReceiver,                                     //system.base.alterEmailReceiver
        base_uploadAllowFlash,  //上传是否允许是否Flash               system.base.uploadAllowFlash
        excel_data_url,                                               //system.excel.data.url
        excel_path,                                                    //system.excel.path
        excel_fileName_pwd,                                           //system.excel.fileName.pwd
        normal_maxNum_export,//导出最大行数                           //system.normal.maxNum.export
        /**
         * 同步执行导出
         * system.sync.do.export
         */
        sync_do_export,
        /** 劳动力：laborForce **/
    
        /** Windsows CE rf：rf **/
    
        /** 安全：security **/
        alert_mail_switch,//system.alert.mail.switch 报警邮件开关
        /** 自定义属性**/
        sku_ext_cfg,
        /**
         * system.report.batch.size
         */
        report_batch_size,
        /**
         * 订单池看板天数限制上限
         * system.order.kanban.days
         */
        order_kanban_days;
    }
    
    public static enum Print {
        /** 送货单：do **/
        do_yaoToSuffix,                                         //print.do.yaoToSuffix
        do_printByBatch,                                        //print.do.printByBatch
        do_yaoToPrintByFtl,//随货同行单打印是否使用ftl           //print.do.yaoToPrintByFtl
        /** 箱标签：carton **/                                    //print.product.name.sub.length
        product_name_sub_length,
        sub_rows,//print.sub.rows
        confrere_cfg, //print.confrere.cfg
        carton_diy_script, //print.carton.diy.script
		carton_split_by_package, // print.carton.split.by.package 箱标签打印是否按B/C分离显示当前箱的index；
        pick_task_not_merge, // print.pick.task.not.merge 拣货任务打印是否合并
        is_print_confrere_check_status, // print.is.print.confrere.check.status 打印随货同行单是否控制状态
		/** 基本：base **/		        /** 发票：invoice **/
        
        /** 拣货单：pick **/
        
        /** 收货：receive **/
    
        /** 补货：repl **/
        // print.sf.jibao.products 需要集货的sku barcode集合
        sf_jibao_products
        ;
    }
    
    public static enum  Stock{
        locRecommend_newModel,                            //stock.locRecommend.newModel
        locRecommend_TimeOutMiSeconds,                   //stock.locRecommend.TimeOutMiSeconds
        refreshEmptyLoc_whIds,                            //stock.refreshEmptyLoc.whIds
        check_merchant,                                   //stock.check.merchant
        destroy_auto,                                     //stock.destroy.auto
        mul_level_repl,                                   //stock.mul.level.repl
        repl_task_update_do_repl_status,               //stock.repl.task.update.do.repl.status
        do_repl_lotatt05,               //stock.do.repl.lotatt05
        /** 基本：base **/
        
        /** gsp：gsp **/
        
        /** 盘点&损溢：count **/
        gal_recheck_async,                              //stock.gal.recheck.async
        /** 需要备份库存的仓库 **/
        backup_stock_warehouses, // stock.backup.stock.warehouses
        /** 搬仓：trans **/
        base_lot12Type,                                  //stock.base.lot12Type
        /** 养护回写 **/
        maintain_rewrite,
        /**
         * 效期倒挂移库开关
         * stock.better.in.ea.need.move.switch
         */
        better_in_ea_need_move_switch,
        ;
    }
    
    public static enum Interface {
        
        /** 基本：base **/
        base_skuAutoSetMainFlg,                           //interface.base.skuAutoSetMainFlg
        dubhe_serverUrl,                                   //interface.dubhe.serverUrl
        ctms_serverUrl,                                   //interface.dubhe.serverUrl
        ctms_token,                                   //interface.dubhe.serverUrl
        imp_check_switch,                                  //interface.imp.check.switch
        is_sync_transport_record,//同步到货通知单开关      //interface.is.sync.transport.record
        change_address_check_sync_waybill,//同步到货通知单开关  //interface.change.address.check.sync.waybill
        change_carrier_check_sync_waybill,//同步到货通知单开关  //interface.change.carrier.check.sync.waybill
        send_ship_do_msg_tms,//出库时更新发货信息给tms  //interface.send.ship.do.msg.tms
        send_convert_msg_switch, // interface.send.convert.msg.switch,转移单是否回写消息
        stock_convert_not_allow_d2m_switch,//interface.stock.convert.not.allow.d2m.switch,是否不允许好品转坏品
        is_oppo_warehouse,//interface.is.oppo.warehouse,是否为欧珀仓库
        oppo_config,//interface.oppo.config,oppo调用appKey
        is_oppo_snrewrite_use_new,//interface.is.oppo.snrewrite.use.new,oppo序列号回写是否使用新方式
        is_hold_exp_use_new,//interface.is.hold.exp.use.new，冻结是否使用2条交易
        tms_rewrite_switch,//interface.tms.rewrite.switch，是否回写tms
        do_finishTime,//interface.do.finishTime，截单时间脚本
        mail_fetch_error_days, //interface.mail.fetch.error.days;回调错误数据抓取的天数，默认30天
        is_sync_vbp,//interface.is.sync.vbp,是否回单到vbp
        vbp_server_config//interface.vbp.server.config,vbp服务器配置
        /** 调入：imp **/
        
        /** 调出：exp **/
    }

    public static enum Task {
        cross_seed_create_whIds,// task.cross.seed.create.whIds
        cross_create_lack_detail, // task.cross.create.lack.detail
        /** 补货：repl **/

        ;
    }
    
    public static enum Exception {
        
        /** 取消：cancel **/
    
        /** 基本：base **/
        ;
    }
    
    public static enum Master {
    
        waveRule_genAuto,                              //mastere.waverule.genauto
        /** sku：sku **/
        update_sku_gift_event,                           //master.update.sku.gift.event
        /** 容器：container **/
    
        /** 库位：location **/
    
        /** 分拣柜：sortingBin **/
    
        /** 配送商：carrier **/

        /** 电子面单：carrier **/
        waybill_cainiao_cfg, //菜鸟电子面单配置  //master.waybill.cainiao.cfg
//        waybill_cainiaoAuthorizeUrl, //菜鸟验证地址       //master.waybill.cainiaoAuthorizeUrl
//        waybill_cainiaoServerUrl, //菜鸟验证地址          //master.waybill.cainiaoServerUrl
//        waybill_cainiaoRedirectUrl, //菜鸟验证回调地址    //master.waybill.cainiaoRedirectUrl
//        waybill_cainiaoClientId, //菜鸟Appkey            //master.waybill.cainiaoClientId
//        waybill_cainiaoClientSecret, //菜鸟appSecret     //master.waybill.cainiaoClientSecret
        location_volumeType, //master.location.volumeType
        dot_flag_package, //master.dot.flag.package
        waybill_cainiao_carrier_cfg, //菜鸟配送商面单配置  //master.waybill.cainiao.carrier.cfg
        //顺丰是否保价,master.sf.waybill.is.insure
        sf_waybill_is_insure,
        //顺丰报价是否使用默认保价金额,master.sf.waybill.use.default.insure.amount
        sf_waybill_use_default_insure_amount,
        //京东业务类型,master.jd.business.type
        jd_business_type,
        ;
    }
    
    public static enum Receive{
        autoGenerateLpnNo,                             //receive.autoGenerateLpnNo
        recheck_recheckBy,                             //receive.recheck.recheckBy
        rewrite_recheck,                               //receive.rewrite.recheck
        /** 预约：reservation **/
        
        /** 收货：receive **/
        receive_enableAuto,                          //receive.receive.enableAuto
        cross_asn_rewrite,                           //receive.cross.asn.rewrite

        match_batch_need_check,  //  receive.match.batch.need.check
        match_batch_need_check_all,  //  receive.match.batch.need.check.all
        exp_sendAsn2OmsStatus,  //  receive.exp.sendAsn2OmsStatus asn 状态回写时机。
        ignore_sku_nature,  //  receive.ignore.sku.nature 状态回写时机。
        is_query_batchInfo_by_detail,  //  receive.is.query.batchInfo.buy.detail 收货是否通过asnDetail校验批次
        disable_lotatt05_asnType,     //receive.disable.lotatt05.asnType   收货批号禁用订单类型
        can_import, // receive.can.import 允许导入收货
        import_template_url, // receive.import.template.url 导入模板
        auto_fail_max_count , // receive.auto.fail.max.count 收货失败最大次数
        ;
    }
    
    public static enum Delivery{
        auto_allocate_job_allocate_fail_num,
        auto_allocate_job_allocate_check_num,
        repl_allocateNow,                   // delivery.repl.allocateNow
        base_groupExcludeShopIds,          //delivery.base.groupExcludeShopIds

        /** 发货单：do **/
        batchGroupCanNotCancel,                       //delivery.batchGroupCanNotCancel
        create_tpl_whIds,                               //delivery.create.tpl.whIds

        //调拨单自动分配
        tt_autoAllocate,                              //delivery.tt.autoAllocate
        allocateDay,                                   //delivery.allocateDay
        allocate_splitRegions,                        //delivery.allocate.splitRegions，订单按区域分配，配置区域的ID

        /** 发票：invoice **/
        invoice_einvoice_plateform,                    // delivery.invoice.einvoice.plateform
        invoice_hangXinUrl,                              //delivery.invoice.hangXinUrl
        invoice_hangXinSwitch,                          //delivery.invoice.hangXinSwitch
        no_sku_validityRuleMinDays,                     //delivery.no.sku.validityRuleMinDays
        
        allocate_ingoreDate,//分配忽略效期控制                  //delivery.allocate.ingoreDate
        allocate_rtvIngoreSupplier,//RTV效期忽略供应商         //delivery.allocate.rtvIngoreSupplier
        cancel_allocate_release_do,//取消分配时是否释放订单     //delivery.cancel.allocate.release_do

        /** 分配：allocate **/
        /**
         * 自动分配数量
         * delivery.allocate.batch.num
         */
        allocate_batch_num,
        /**
         * 防冻订单校验
         * delivery.freeze.order.weight.check
         */
        freeze_order_weight_check,
        /**
         * 订单最大重量误差
         * delivery.order.weight.max.gap
         */
        order_weight_max_gap,
        /** 波次：wave **/
        wave_enableAuto,                                             //delivery.wave.enableAuto
        generate_wave_by_one_do,                                             //delivery.generate.wave.by.one.do 是否一单一波次
        insertCartonRewriteMsg,//发货时回写箱信息                     //delivery.insertCartonRewriteMsg
        wave_priorityScrit, //波次优先级策略                 //delivery.wave.priorityScrit
        wave_needAllSortBin,//波次是否需要分配分拣柜   //delivery.wave.needAllSortBin

        wave_emergency_not_spit_region,//紧急波次不按区域分拣货单           //delivery.wave.emergency.not.spit.region
        /** 不打印拣货单的区域id**/
        pick_notPrintRegions,                                               //delivery.pick.notPrintRegions
        /** 整件拣货单是否一单一个拣货单 **/
        pick_splitRegions,                                                     //delivery.pick.splitRegions
        /** 分拣完是否自动关分拣 **/
        pick_autoSort,                                                       //delivery.pick.autoSort
        pick_canRequestWaveType,                                             //delivery.pick.canRequestWaveType //拣货能索取的波次类型
        
        /** 拣货：pick **/
        //WCS容器拣货，是否忽略批次
        pick_by_container_ignore_batch,                                    //pick.by.container.ignore.batch

        /** RF按波次拣货，支持输入订单号后几位 **/
        pick_useDPS,                                                          //delivery.pick.useDPS  //是否启用电子标签
        pick_by_part_doNo,                                                   //delivery.pick.by.part.doNo
        sync_do_node,//哪个操作节点回单                                 //delivery.sync.do.node 装箱10，称重20，交接30，99不回单

        /** 分拣：sorting **/
        sort_isWithPick,  //边拣边分                                         //delivery.sort.isWithPick
        do_reAllocate,  //刷新或清空团购标记重分配                             //delivery.do.reAllocate
        check_expected_qty,  //                                              //delivery.check.expected.qty
        checkByBtn,  //                                                           //delivery.checkByBtn

        /** 核检：recheck **/
        sound_shop_flag,                                                             //delivery.sound.shop.flag
        sound_tok,                                                                     //delivery.sound.tok
        change_carrier_check_sync_waybill,//修改配送商是否校验已回单，已回单则不允许修改                //delivery.change.carrier.check.sync.waybill
        check_change_do_after_print_carton,//打印后修改                                              //delivery.check.change.do.after.print.carton
        do_checked_all,//打印后修改                                              //delivery.do.checked.all
        do_can_hold_status,//订单可冻结的最小状态                                              //delivery.do.can.hold.status
        rechck_auto_allocate, //自动分配包装台       //delivery.rechck.auto.allocate
        obs_before_url,       // obs 原始需要替换的地址      delivery.obs.before.url
        obs_after_url,       // obs 替换后的地址      delivery.obs.after.url

        before_sync_order_cfg,  //delivery.before.sync.order.cfg //提前回单配置
        recheck_show_recommend_material,//delivery.rechck.show.recommend.material

        /**
         * 装箱按钮控制开关
         * delivery.confirm.recheck.enable
         */
        confirm_recheck_enable,
        /**
         * 支持子母件配送商类型列表
         * delivery.support.sub.carton.waybill.types
         */
        support_sub_carton_waybill_types,
        /** 集货：merge **/
        /**
         * 批量更新称重标数量
         * delivery.update.weight.flag.batch.num
         */
        update_weight_flag_batch_num,
        /** 交接：delivery **/
        auto_load_and_deliver_cfg,  //delivery.auto.load.and.deliver.cfg //自动交接发货配置
        auto_temp_carton_skuCodes,  //delivery.auto.temp.carton.skuCodes //自动下单回单like的商品
        auto_temp_carton_doTypes,    //delivery.auto.temp.carton.doTypes //自动下单的订单类型
        hold_up_do_hour,  //delivery.hold.up.do.hour //订单滞留小时
        seed_execute_can_less,  //delivery.seed.execute.can.less //分播是否允许缺分

        ctn_recheck_enableAuto,                                             //delivery.ctn.recheck.enableAuto 按箱发货时是否自动装箱
        jp_sf_type,                                             //delivery.jp.sf.type 京东配送顺丰电子面单类型
        use_merge_print,                                             //delivery.use.merge.print 启用批发合单打印
        cross_ship_do_status,                                             //delivery.cross.ship.do.status 越库可自动发货的状态，默认装箱完成
        recheck_check_sync_waybill,                                             //delivery.recheck.check.sync.waybill 装箱时是否校验回平台
        recheck_show_inspection_print,                                             //delivery.recheck.show.inspection.print 装箱时是否显示质检报告打印
        cross_ship_delay_time, //delivery.cross.ship.delay.time 越库自动发货延迟时间，分钟
        recheck_by_lot05_do_types, //delivery.recheck.by.lot05.do.types 按批号核拣的订单类型
        cainiao_way_bill_need_city, //delivery.cainiao.way.bill.need.city 菜鸟下单时市和区是否必填
        replace_print_product_info,//替换商品敏感信息  //delivery.replace.print.product.info
        spvsn_code_check_switch,//出库扫描药监码开关  //delivery.spvsn.code.check.switch
        spvsn_code_recheck_check_switch,//核拣装箱药监码校验开关  //delivery.spvsn.code.recheck.check.switch
        group_wave_spvsn_check_switch,//团购交接药监码校验开关  //delivery.group.wave.spvsn.check.switch
        group_wave_record_materials_switch,//团购记录耗材开关  //delivery.group.wave.record.materials.switch
        random_get_labor_task,//随机索取拣货任务  //delivery.random.get.labor.task
        recheck_print_user_script,//核检打印自定义脚本 //delivery.recheck.print.user.script
        temp_carton_before_day,//下单查询天数 //delivery.temp.carton.before.day
        order_goods_coincidence_factor,//订单商品重合率 //delivery.order.goods.coincidence.factor
        order_goods_coincidence_count,//订单商品重合数量//delivery.order.goods.coincidence.count
        coincidence_factor_refresh_time,//重合率刷新时间 //delivery.coincidence.factor.refresh.time
        daily_sales_statistics_day,//销量排行统计天数 //delivery.daily.sales.statistics.day
        max_ship_count,//hold单开启仓库最大作业单量 //delivery.max.ship.count
        max_publish_count_half_hour,//hold单开启30分钟的最大下发速率 //delivery.max.publish.count.half.hour
        temp_do_publish_mode,//hold单下发的模式 //delivery.temp.do.publish.mode
        publish_temp_do_fetch_count,//每次执行定时任务抓取的订单数 //delivery.publish.temp.do.fetch.count
        cancel_do_remove_temp_carton,//订单取消时自动取消电子面单，默认是 //delivery.cancel.do.remove.temp.carton
        line_location_take_rate,//流水线库位默认使用率  //delivery.line.location.take.rate
        max_line_stranded_box_num,//线上箱子最大滞留数 //delivery.max.line.stranded.box.num
        is_use_line,//线上箱子最大滞留数 //delivery.is.use.line
        max_chute_stranded_box_num,//滑道口箱子最大滞留数 //delivery.max.chute.stranded.box.num
        max_location_all_qty_num,//滑道最大商品数量总数 //delivery.max.location.all.qty.num
        max_location_picking_qty_num,//滑道最大待拣货商品数量总数 //delivery.max.location.picking.qty.num
        line_exception_chute_set,//流水线异常口设定 delivery.line.exception.chute.set,
        recheckFor2B_check_pcs,//批发业务核拣装箱是否复核散件 //delivery.recheckFor2B.check.pcs
        do_record_query_interval_day,//销售记录查询间隔天数//delivery.do.record.query.interval.day
        do_distribute_query_interval_day,//订单分布查询间隔天数//delivery.do.distribute.query.interval.day
        detail_export_url, // 订单明细导出url delivery.detail.export.url
        pdd_way_bill_need_encrypt,//拼多多下单返回报文是否加密//delivery.pdd.way.bill.need.encrypt
        cainiao_way_bill_need_encrypt,//菜鸟下单返回报文是否加密//delivery.cainiao.way.bill.need.encrypt
        order_cut_off_time,//截单时间delivery.order.cut.off.time
        order_monitor_report_mail_receiver,//发货单监控邮件收件人delivery.order.monitor.report.mail.receiver
        //箱号拼接字符串 delivery.carton.split.value
        carton_split_value,
        group_wave_number,
        not_serial_sku,
        random_check_switch,//抽检开关 delivery.random.check.switch
        random_check_percent //抽检比例 0-1 之间的小数 delivery.random.check.percent
        ,
        gene_temp_carton
        ;
    }
    public enum Asn{
        ro_auto_receive_switch,// asn.ro.auto.receive.switch
    }
}