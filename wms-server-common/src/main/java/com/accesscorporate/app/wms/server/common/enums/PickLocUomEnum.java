package com.accesscorporate.app.wms.server.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-24 15:33
 * Description: 补货单位
 */
@Getter
public enum PickLocUomEnum {

    EA,

    IP,

    CS,

    PL,

    OT;

    /**
     * 获取指定补货单位枚举
     */
    public static PickLocUomEnum getPickLocUomEnum(String uom){
        return Arrays.stream(PickLocUomEnum.values())
                .filter(uomEnum->Objects.equals(uomEnum.name(),uom))
                .findAny().orElse(null);
    }

}
