package com.accesscorporate.app.wms.server.common.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.util.TimeZone;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-02-21 16:56
 * Description: Jackson 配置类，用于注册自定义的 String 反序列化器
 */
@Configuration
public class JacksonConfig {

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jacksonCustomizer() {
        return builder -> {
            // 创建一个 SimpleModule 并添加自定义的 String 反序列化器
            SimpleModule module = new SimpleModule();
            module.addDeserializer(String.class, new StringTrimmingDeserializer());
            module.addSerializer(LocalDateTime.class, new LocalDateTimeToTimestampSerializer());
            // 注册模块到 Jackson
            builder.modules(module);
        };
    }

}
