package com.accesscorporate.app.wms.server.common.exceptions;

@lombok.extern.slf4j.Slf4j
public class BusinessException extends RuntimeException {

    private static final long serialVersionUID = 2817356867906293460L;

    private Object[] params;

    private String code;

    public BusinessException() {
        super();
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
    }

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(Throwable cause) {
        super(cause);
    }

    public BusinessException(String message, Object... params) {
        super(message);
        this.params = params;
    }

    public BusinessException(String code, String message, Object... params) {
        super(message);
        this.params = params;
        this.code = code;
    }

    public Object[] getParams() {
        return params;
    }

    public void setParams(Object[] params) {
        this.params = params;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
