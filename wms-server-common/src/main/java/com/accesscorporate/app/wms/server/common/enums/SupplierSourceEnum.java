package com.accesscorporate.app.wms.server.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 供应商来源
 * （0：后台添加；1：供应商自己在网站注册）
 *
 * <AUTHOR>
 * 2025/2/17  16:35
 */
@Getter
@AllArgsConstructor
public enum SupplierSourceEnum {

    ADD_IN_BACKGROUND(0, "后台添加"),

    REGISTER_IN_WEBSITE(1, "供应商自己在网站注册");

    private final Integer code;

    private final String description;


    public static String getDescByCode(Integer code) {
        for (SupplierSourceEnum value : SupplierSourceEnum.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value.getDescription();
            }
        }
        return null;
    }

    public static Integer getCodeByDesc(String description) {
        for (SupplierSourceEnum value : SupplierSourceEnum.values()) {
            if (Objects.equals(description, value.getDescription())) {
                return value.getCode();
            }
        }
        return null;
    }

}
