package com.accesscorporate.app.wms.server.common.enums;

import lombok.Getter;

/**
 * 消息类型枚举
 */
@Getter
public enum MsgTypeEnum {
    DO_TMSV2(1, "TMS V2 出库单"),
    ALL_DO_OMS(6, "OMS 所有出库单"),
    BATCHQTY_WMS(9, "WMS 批次数量"),
    RES_PMS(11, "PMS 预约"),
    TO_CD_PMS(12, "PMS 转CD"),
    TO_CD_TMSV2(13, "TMS V2 转CD"),

    TRS_LOG_OMS(14, "OMS 物流日志"),
    TRS_CDSHIP_OMS(16, "OMS CD发货"),
    GAL_RECKECK_ERP(17, "ERP 复检"),
    RES_RENEGUE_TMS(22, "TMS 预约取消"),
    RES_RENEGUE_SP(23, "SP 预约取消"),
    DO_RELEASE_SCS(27, "SCS 出库单释放"),
    DO_CANCEL_OMS(28, "OMS 出库单取消"),
    RTV_RESERVATION_PMS(29, "PMS RTV预约"),
    STOCK_DESTORY_APPLIY(34, "库存销毁申请"),
    STOCK_CONVERT(35, "库存转换"),
    ASN_OMS(37, "OMS ASN"),
    ASN_OMS_AUDIT(38, "OMS ASN审核"),
    CALL_CS(40, "客服呼叫"),
    SN_OMS(41, "OMS 序列号"),
    DO_BAIYANG(42, "百洋出库单"),
    WAVE_INVOICE_OMS(44, "OMS 波次发票"),
    ORDER_TMS(45, "TMS 订单"),
    RECHECK_OMS(46, "OMS 复检"),
    BOM_OMS(47, "OMS BOM"),
    CARTON_OMS(48, "OMS 箱号"),
    SHIP_INFO_TMS(49, "TMS 发货信息"),
    ASN_TRANSPORT_EXP(50, "运输ASN异常"),
    DO_TRANSPORT_EXP(51, "运输出库单异常"),
    TMS_EXP(52, "TMS 异常"),
    MAINTAIN_EXP(53, "维护异常"),
    INVOICE_ERP(54, "ERP 发票"),
    BIZ_REWRITE(55, "业务重写"),
    CALL_GOODS_PASS(56, "货物放行呼叫"),
    MPS_OMS(57, "OMS MPS");

    private final Integer value;
    private final String desc;

    MsgTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static Integer[] getValues(MsgTypeEnum[] msgTypes) {
        Integer[] rs = new Integer[msgTypes.length];
        for (int i = 0; i < msgTypes.length; i++) {
            rs[i] = msgTypes[i].getValue();
        }
        return rs;
    }

    public static MsgTypeEnum ofValue(Integer value) {
        MsgTypeEnum[] ls = MsgTypeEnum.values();
        for (MsgTypeEnum l : ls) {
            if (l.value.equals(value)) {
                return l;
            }
        }
        return null;
    }


}