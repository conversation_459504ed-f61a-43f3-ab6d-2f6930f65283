package com.accesscorporate.app.wms.server.common.utils;

import com.idanchuang.component.base.exception.core.ExFactory;
import com.idanchuang.component.base.exception.core.IErrorEnum;
import com.idanchuang.component.base.exception.exception.AbstractException;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.util.StringUtils;


/**
 * <AUTHOR>
 * @version JDK 17
 * @date 2025/3/24
 * @description
 */
public class ThrowFactory extends ExFactory {

    /**
     * 抛出指定异常、支持占位符
     *
     * @param code           异常枚举
     * @param messagePattern 异常信息模板
     * @param messages       异常信息占位符填充信息
     * @return 格式化后异常
     */
    public static AbstractException throwWith(IErrorEnum code, String messagePattern, Object... messages) {
        if (StringUtils.isEmpty(messagePattern)) {
            throw throwWith(code);
        }
        if (messages != null && messages.length > 0) {
            messagePattern = MessageFormatter.arrayFormat(messagePattern, messages).getMessage();
        }
        return throwWith(of(code, messagePattern));
    }

}
