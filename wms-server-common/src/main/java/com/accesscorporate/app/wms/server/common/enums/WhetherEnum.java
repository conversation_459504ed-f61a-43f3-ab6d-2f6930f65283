package com.accesscorporate.app.wms.server.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是否状态枚举
 *
 * <AUTHOR>
 * 2025/2/17  16:39
 */
@Getter
@AllArgsConstructor
public enum WhetherEnum {

    YES(1, "是"),
    NO(0, "否");

    private final Integer code;

    private final String desc;


    public static String getDescByCode(Integer code) {
        for (WhetherEnum value : WhetherEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }

    public static Integer getCodeByDesc(String desc) {
        for (WhetherEnum value : WhetherEnum.values()) {
            if (value.getDesc().equals(desc)) {
                return value.getCode();
            }
        }
        return null;
    }


}
