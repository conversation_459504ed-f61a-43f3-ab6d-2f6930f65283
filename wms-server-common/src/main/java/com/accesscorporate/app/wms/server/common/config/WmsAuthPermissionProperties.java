package com.accesscorporate.app.wms.server.common.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * WMS 认证&权限相关配置类
 *
 * <AUTHOR>
 * 2025/2/8  13:42
 */

@Slf4j
@Data
@Component
@ConfigurationProperties(
        prefix = "wms.auth.permission"
)
public class WmsAuthPermissionProperties {


    /**
     * WMS 权限系统应用Code
     */
    private String applicationCode;


    /**
     * 仓数据权限--数据角色Code
     */
    private String dataRoleCodeWh;


    /**
     * 租户权限点--数据角色Code
     */
    private String dataRoleCodeTenant;


    /**
     * 租户ID & 租户名称映射关系
     */
    private Map<Long, String> tenantMap = new HashMap<>();


}
