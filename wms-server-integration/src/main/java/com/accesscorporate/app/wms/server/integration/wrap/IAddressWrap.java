package com.accesscorporate.app.wms.server.integration.wrap;

import com.danchuang.global.scm.address.client.dto.BaseAddressDTO;
import com.danchuang.global.scm.address.client.request.address.AddressQueryRequest;

import java.util.List;

public interface IAddressWrap {

    BaseAddressDTO getByAddressCode(String addressCode);

    List<BaseAddressDTO> queryByParentCode(AddressQueryRequest addressQueryRequest);

}
