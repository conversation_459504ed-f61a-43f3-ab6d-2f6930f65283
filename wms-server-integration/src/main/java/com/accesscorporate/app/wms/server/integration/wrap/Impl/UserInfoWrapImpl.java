package com.accesscorporate.app.wms.server.integration.wrap.Impl;

import com.accesscorporate.app.wms.server.integration.dto.UserDetailResponse;
import com.accesscorporate.app.wms.server.integration.wrap.IUserInfoWrap;
import com.idanchuang.sso.external.facade.ExternalAuthServiceFacade;
import com.idanchuang.sso.external.facade.ExternalUserServiceFacade;
import com.idanchuang.sso.model.dto.JwtTokenDTO;
import com.idanchuang.sso.model.dto.system.UserDetailDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户信息获取--防腐层实现
 *
 * <AUTHOR>
 * 2025/2/5  17:26
 */
@Component
@RequiredArgsConstructor
public class UserInfoWrapImpl implements IUserInfoWrap {

    private final ExternalUserServiceFacade userServiceFacade;

    private final ExternalAuthServiceFacade authServiceFacade;

    public List<UserDetailResponse> getUserByIdList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        Long[] longArr = new Long[userIdList.size()];
        List<UserDetailDTO> list = userServiceFacade.getUserDTOByIds(userIdList.toArray(longArr)).mustSuccess().getData();
        return list.stream().map(user -> {
            UserDetailResponse res = new UserDetailResponse();
            res.setUserId(user.getId());
            res.setUserName(user.getRealName());
            res.setAccount(user.getAccount());
            return res;
        }).collect(Collectors.toList());
    }


    /**
     * 根据token获取用户信息
     *
     * @param token 登录标识
     * @return vo
     */
    public UserDetailResponse getUserInfoByToken(String token) {
        JwtTokenDTO jwtTokenDTO = authServiceFacade.checkToken(token).mustSuccess().getData();
        UserDetailDTO claims = jwtTokenDTO.getClaims();
        UserDetailResponse vo = new UserDetailResponse();
        vo.setUserId(claims.getId());
        vo.setUserName(claims.getRealName());
        vo.setAccount(claims.getAccount());
        return vo;
    }
}
