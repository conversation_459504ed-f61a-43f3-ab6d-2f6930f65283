package com.accesscorporate.app.wms.server.integration.wrap;

import com.accesscorporate.app.wms.server.integration.dto.TenantResponse;

import java.util.List;

/**
 * 用户权限-Wrap
 *
 * <AUTHOR>
 * 2025/2/8  13:24
 */
public interface IUserPermissionWrap {

    /**
     * 根据用户ID 获取对应的仓权限列表「仓编码」
     *
     * @param userId: 用户ID
     * @return List<String>
     * <AUTHOR>
     * 2025/2/8 14:31
     */
    List<String> queryUserWarehousePermission(Long userId);


    /**
     * 根据用户ID 获取对应关联的租户列表「租户」
     *
     * @param userId: 用户ID
     * @return List<String>
     * <AUTHOR>
     * 2025/2/8 18:26
     */
    List<TenantResponse> queryUserTenantPermission(Long userId);

}
