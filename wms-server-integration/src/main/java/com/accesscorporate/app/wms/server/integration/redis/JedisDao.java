package com.accesscorporate.app.wms.server.integration.redis;


import com.alibaba.fastjson.JSON;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import com.google.common.collect.Lists;
import com.idanchuang.component.config.apollo.ConfigService;
import com.idanchuang.component.redis.util.RedisUtil;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.SerializationUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Component
@lombok.extern.slf4j.Slf4j
public class JedisDao {
    @Resource
    private RedisUtil redisUtils;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private ConfigService configService;

    public static final String NAMESPACE_PREFIX = "dc_chain_wms:" ;
    public static final String SHARE_PREFIX = "global:" ;
    public static final String COMMA = ",";
    public static final String SPLIT = ":";
    // key超时时间,3天过期.
    public static final int EXPIRE = 60 * 60 * 24 * 3;

    /**
     * 批量获取对象
     * @param keys
     * @return
     */
    public List<Object> batchGetObject(List<String> keys) {
        if(CollectionUtils.isEmpty(keys)){
            return null;
        }
        try {
            List<String> redisKeys = keys.stream().map(this::getKey).collect(Collectors.toList());
            List<String> results = redisUtils.multiGet(redisKeys);

            if (results == null || results.size() != keys.size()) {
                return new ArrayList<>();
            }

            return results.stream()
                    .filter(Objects::nonNull)
                    .map(obj -> {
                        if (obj instanceof String) {
                            // 如果RedisUtil返回的是字符串，需要转换为字节数组再反序列化
                            return bytesToObject(((String) obj).getBytes(StandardCharsets.UTF_8));
                        }
                        return obj;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("批量获取对象失败", e);
            return new ArrayList<>();
        }
    }

//    private boolean needCache(){
//        return Objects.nonNull(ParamUtil.getCurrentTenantId());
//    }

    private String getKey(String key){
        String[] ns = key.replace("*","").split(SPLIT);
        String globalCache = configService.getString("redis.global.cache", "");
        if(ns.length>1 && globalCache.contains(ns[1])) {
            return NAMESPACE_PREFIX +SHARE_PREFIX+ key;
        }
        Long tenantId = UserContextAssistant.getTenantId();
        Long warehouseId = UserContextAssistant.getCurrentWarehouseId();
        return NAMESPACE_PREFIX+ tenantId + SPLIT + warehouseId + SPLIT + key;
    }

    private String getKeyForTenant(String key){
        Long tenantId = UserContextAssistant.getTenantId();
        return NAMESPACE_PREFIX+ tenantId + SPLIT + key;
    }

    private String getBatchRemoveKey(String key){
        return "*:" + key;
    }

    private String[] getKey(String[] keys){
        for (int i = 0; i < keys.length; i++) {
            keys[i]=getKey(keys[i]);
        }
        return keys;
    }


    /**
     * 设置单个值
     *
     * @param key
     * @param value
     * @return
     */
    public String set(String key, Object value) {
        return set(key, value, EXPIRE);
    }

    /**
     * 设置单个值，并设置超时时间
     *
     * @param key     键
     * @param value   值
     * @param timeout 超时时间（秒）
     * @return
     * @see [类、类#方法、类#成员]
     */
    public String set(String key, Object value, Integer timeout) {
        try {
            String redisKey = getKey(key);
            byte[] serializedValue = objectToBytes(value);

            if (timeout != null && timeout > 0) {
                redisUtils.setObj(redisKey, serializedValue, timeout);
            } else {
                redisUtils.setObj(redisKey, serializedValue);
            }
            return "OK";
        } catch (Exception e) {
            log.error("设置缓存失败, key: {}", key, e);
            return null;
        }
    }

    public String setString(String key, String value, Integer timeout) {
        try {
            String redisKey = getKey(key);
            if (timeout != null && timeout > 0) {
                redisUtils.set(redisKey, value, timeout);
            } else {
                redisUtils.set(redisKey, value);
            }
            return "OK";
        } catch (Exception e) {
            log.error("设置字符串缓存失败, key: {}", key, e);
            return null;
        }
    }

    public String getString(String key) {
        try {
            return redisUtils.get(getKey(key));
        } catch (Exception e) {
            log.error("获取字符串缓存失败, key: {}", key, e);
            return null;
        }
    }

    public Object get(String key) {
        try {
            Object result = redisUtils.get(getKey(key));
            if (result instanceof byte[]) {
                return bytesToObject((byte[]) result);
            } else if (result instanceof String) {
                // 如果RedisUtil返回的是字符串，尝试转换为字节数组再反序列化
                return bytesToObject(((String) result).getBytes(StandardCharsets.UTF_8));
            }
            return result;
        } catch (Exception e) {
            log.error("获取对象缓存失败, key: {}", key, e);
            return null;
        }
    }

    /**
     * 集合是否包含元素
     * @param key
     * @return
     */
    public Boolean sismember(String key, Object member) {
        try {
            return redisUtils.isMemberSetObj(getKey(key), objectToBytes(member));
        } catch (Exception e) {
            log.error("检查集合成员失败, key: {}", key, e);
            return false;
        }
    }

    public void setObj(String key, byte[] value) {
        try {
            redisUtils.setObj(getKey(key), value);
        } catch (Exception e) {
            log.error("设置对象缓存失败, key: {}", key, e);
        }
    }

    /**
     * 获取jedis对象
     */
    public Object getObj(String key) {
        try {
            Object result = redisUtils.get(getKey(key));
            if (result instanceof byte[]) {
                return SerializationUtils.deserialize((byte[]) result);
            } else if (result instanceof String) {
                return SerializationUtils.deserialize(((String) result).getBytes(StandardCharsets.UTF_8));
            }
            return result;
        } catch (Exception e) {
            log.error("获取对象缓存失败, key: {}", key, e);
            return null;
        }
    }

    public long del(String key) {
        return del(new String[]{key});
    }

    public long del(String... keys) {
        try {
            List redisKeys =Arrays.asList( getKey(keys));
            return redisUtils.del(redisKeys);
        } catch (Exception e) {
            log.error("删除缓存失败, keys: {}", Arrays.toString(keys), e);
            return 0L;
        }
    }

    public String flushAll() {
        try {
            redisTemplate.getConnectionFactory().getConnection().flushAll();
            return "OK";
        } catch (Exception e) {
            log.error("清空所有缓存失败", e);
            return null;
        }
    }

    public Set<String> keys(String pattern) {
        try {
            return redisTemplate.keys(getKey(pattern));
        } catch (Exception e) {
            log.error("获取keys失败, pattern: {}", pattern, e);
            return new HashSet<>();
        }
    }

    public long incr(String key) {
        try {
            String redisKey = getKey(key);
            long result = redisUtils.increment(redisKey,1);
            redisUtils.expire(redisKey, EXPIRE);
            return result;
        } catch (Exception e) {
            log.error("递增操作失败, key: {}", key, e);
            return 0L;
        }
    }

    public long decr(String key) {
        try {
            return redisUtils.increment(getKey(key),-1);
        } catch (Exception e) {
            log.error("递减操作失败, key: {}", key, e);
            return 0L;
        }
    }

    public Long incrBy(String key, Long value) {
        try {
            String redisKey = getKey(key);
            long result = redisUtils.increment(redisKey, value);
            redisUtils.expire(redisKey, EXPIRE);
            return result;
        } catch (Exception e) {
            log.error("递增指定值操作失败, key: {}, value: {}", key, value, e);
            return null;
        }
    }

    public Long decrBy(String key, Long value) {
        try {
            return redisUtils.increment(getKey(key), Math.negateExact(value));
        } catch (Exception e) {
            log.error("递减指定值操作失败, key: {}, value: {}", key, value, e);
            return null;
        }
    }

    public void sadd(String key, Object ele) {
        sadd(key, ele, 60 * 60 * 24);
    }

    public void sadd(String key, Object ele, int expire) {
        try {
            String redisKey = getKey(key);
            redisUtils.addSetObj(redisKey, objectToBytes(ele));
            redisUtils.expire(redisKey, expire);
        } catch (Exception e) {
            log.error("添加集合元素失败, key: {}", key, e);
        }
    }

    public void sadd(String key, Object[] eles, int expire) {
        try {
            String redisKey = getKey(key);
            byte[][] serializedElements = objectToBytes(eles);
            redisUtils.addSetObj(redisKey, serializedElements);
            redisUtils.expire(redisKey, expire);
        } catch (Exception e) {
            log.error("批量添加集合元素失败, key: {}", key, e);
        }
    }

    public Set<Object> sget(String key) {
        try {
            Set<Object> members = redisUtils.membersSetObj(getKey(key));
            Set<Object> returnSet = new HashSet<>();
            for (Object member : members) {
                if (member instanceof byte[]) {
                    returnSet.add(bytesToObject((byte[]) member));
                } else {
                    returnSet.add(member);
                }
            }
            return returnSet;
        } catch (Exception e) {
            log.error("获取集合成员失败, key: {}", key, e);
            return new HashSet<>();
        }
    }

    public void lpush(String key, Object eleOne) {
        try {
            String redisKey = getKey(key);
            redisUtils.leftPushList(redisKey, JSON.toJSONString(eleOne));
            // 1天过期
            redisUtils.expire(redisKey, 60 * 60 * 24);
        } catch (Exception e) {
            log.error("左侧推入列表失败, key: {}", key, e);
        }
    }

    public void rpush(String key, Object eleOne) {
        try {
            String redisKey = getKey(key);
            redisUtils.rightPushList(redisKey, JSON.toJSONString(eleOne));
            // 1天过期
            redisUtils.expire(redisKey, 60 * 60 * 24);
        } catch (Exception e) {
            log.error("右侧推入列表失败, key: {}", key, e);
        }
    }

    public <T> T lpop(String key, Class<T> clazz) {
        try {
            String result = redisUtils.leftPopList(getKey(key));
            if (StringUtils.isNotEmpty(result)) {
                return JSON.parseObject(result, clazz);
            }
            return null;
        } catch (Exception e) {
            log.error("左侧弹出列表失败, key: {}", key, e);
            return null;
        }
    }

    public <T> List<T> lrange(String key, int start, int stop, Class<T> clazz) {
        try {
            List<String> results = redisUtils.rangeList(getKey(key), start, stop);
            List<T> tList = new ArrayList<>();
            for (String result : results) {
                tList.add(JSON.parseObject(result, clazz));
            }
            return tList;
        } catch (Exception e) {
            log.error("获取列表范围失败, key: {}, start: {}, stop: {}", key, start, stop, e);
            return new ArrayList<>();
        }
    }

    public Long llen(String key) {
        try {
            return redisUtils.getListSize(getKey(key));
        } catch (Exception e) {
            log.error("获取列表长度失败, key: {}", key, e);
            return 0L;
        }
    }

    /**
     * SET if Not exists
     */
    public boolean setnx(String key, String value) {
        return setnx(key, value, EXPIRE);
    }

    public boolean setnx(String key, Object value, int timeout) {
        try {
            String redisKey = getKey(key);
            boolean flag = redisUtils.setObjIfAbsent(redisKey, objectToBytes(value));
            if (flag) {
                redisUtils.expire(redisKey, timeout);
            }
            return flag;
        } catch (Exception e) {
            log.error("setnx操作失败, key: {}", key, e);
            return false;
        }
    }

    public String setex(String key, String value, int timeout) {
        try {
            redisUtils.setObj(getKey(key), objectToBytes(value), timeout);
            return "OK";
        } catch (Exception e) {
            log.error("setex操作失败, key: {}", key, e);
            return null;
        }
    }

    public String setex(String key, String value) {
        return setex(key, value, EXPIRE);
    }

    public String getSet(String key, String value) {
        return getSet(key, value, EXPIRE);
    }

    public String getSet(String key, String value, int timeout) {
        try {
            String redisKey = getKey(key);
            String oldValue = redisUtils.getAndSet(redisKey, value);
            redisUtils.expire(redisKey, timeout);
            return oldValue;
        } catch (Exception e) {
            log.error("getSet操作失败, key: {}", key, e);
            return null;
        }
    }

    public Long hdel(final String key, final String field) {
        try {
            String redisKey = getKey(key);
            if (redisUtils.existHashKey(redisKey, field)) {
                return redisUtils.delHash(redisKey, field);
            }
            return 0L;
        } catch (Exception e) {
            log.error("删除hash字段失败, key: {}, field: {}", key, field, e);
            return 0L;
        }
    }

    /**
     * map 减操作,避免调用2次jedis 资源
     * @param key
     * @param field
     * @return
     */
    public Long hsub(final String key, final String field, final long increment) {
        try {
            String redisKey = getKey(key);
            if (redisUtils.existHashKey(redisKey, field)) {
                return redisUtils.incrHash(redisKey, field, increment);
            }
            return null;
        } catch (Exception e) {
            log.error("hash字段递增失败, key: {}, field: {}, increment: {}", key, field, increment, e);
            return null;
        }
    }

    /**
     * 对象转数组
     *
     * @param obj
     * @return
     */
    private byte[] objectToBytes(Object obj) {
        byte[] bytes;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            ObjectOutputStream oos = new ObjectOutputStream(bos);
            oos.writeObject(obj);
            oos.flush();
            bytes = bos.toByteArray();
            oos.close();
            bos.close();
        } catch (IOException ex) {
            throw new RuntimeException(ex);
        }
        return bytes;
    }

    /**
     * 数组转对象
     *
     * @param bytes
     * @return
     */
    public Object bytesToObjectTo(byte[] bytes) {
        try (ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
            ObjectInputStream ois = new ObjectInputStream(bis)) {
            Object o = ois.readObject();
            return o;
        } catch (IOException | ClassNotFoundException ex) {
            log.error("反序列化失败", ex);
            return null;
        }
    }
    /**
     * 对象数组转二维数组
     *
     * @param objs
     * @return
     */
    @SneakyThrows
    private byte[][] objectToBytes(Object[] objs) {
        byte[][] result = new byte[objs.length][];
        for (int i = 0; i < objs.length; i++) {
            Object obj = objs[i];
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(bos);
            oos.writeObject(obj);
            oos.flush();
            result[i] = bos.toByteArray();
            oos.close();
            bos.close();

        }
        return result;
    }

    /**
     * 数组转对象
     *
     * @param bytes
     * @return
     */
    private Object bytesToObject(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        Object obj;
        try {
            ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
            ObjectInputStream ois = new ObjectInputStream(bis);
            obj = ois.readObject();
            ois.close();
            bis.close();
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
        return obj;
    }

    public Long hset(String key, String field, Object value) {
        try {
             redisUtils.setHashObj(getKey(key), field, objectToBytes(value));
             return 1L;
        } catch (Exception e) {
            log.error("设置hash字段失败, key: {}, field: {}", key, field, e);
            return 0L;
        }
    }

    public Object hget(String key, String field) {
        try {
            Object result = redisUtils.getHashObj(getKey(key), field);
            if (result instanceof byte[]) {
                return bytesToObject((byte[]) result);
            } else if (result instanceof String) {
                return bytesToObject(((String) result).getBytes(StandardCharsets.UTF_8));
            }
            return result;
        } catch (Exception e) {
            log.error("获取hash字段失败, key: {}, field: {}", key, field, e);
            return null;
        }
    }



    public List<String> getKeys(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(getKey(pattern));
            return new ArrayList<>(keys);
        } catch (Exception e) {
            log.error("获取匹配keys失败, pattern: {}", pattern, e);
            return new ArrayList<>();
        }
    }


    /**
     * 正则匹配批量删除
     * @param pattern
     */
    public void delByPattern(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(getBatchRemoveKey(pattern));
            if (CollectionUtils.isEmpty(keys)) {
                return;
            }

            // 分批删除，避免一次删除太多key
            List<String> keyList = new ArrayList<>(keys);
            List<List<String>> partitions = Lists.partition(keyList, 100);
            for (List<String> partition : partitions) {
                redisUtils.del(partition);
            }
        } catch (Exception e) {
            log.error("按模式批量删除失败, pattern: {}", pattern, e);
        }
    }

    /**
     * 正则匹配批量删除
     * @param pattern
     */
    public void delByPatternForTenant(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(getKeyForTenant(pattern));
            if (CollectionUtils.isEmpty(keys)) {
                return;
            }

            // 分批删除，避免一次删除太多key
            List<String> keyList = new ArrayList<>(keys);
            List<List<String>> partitions = Lists.partition(keyList, 100);
            for (List<String> partition : partitions) {
                redisUtils.del(partition);
            }
        } catch (Exception e) {
            log.error("按租户模式批量删除失败, pattern: {}", pattern, e);
        }
    }

    /**
     * 批量插入 K-V
     * @param entityMap
     */
    public void batchSetString(Map<String, Object> entityMap) {
        if (MapUtils.isEmpty(entityMap)) {
            return;
        }
        try {
            Map<String, Object> redisMap = new HashMap<>();
            entityMap.forEach((k, v) -> {
                redisMap.put(getKey(k), objectToBytes(v));
            });

            // 分批处理，避免一次处理太多数据
            List<Map.Entry<String, Object>> entries = new ArrayList<>(redisMap.entrySet());
            List<List<Map.Entry<String, Object>>> partitions = Lists.partition(entries, 100);

            for (List<Map.Entry<String, Object>> partition : partitions) {
                Map<Object, Object> batchMap = new HashMap<>();
                for (Map.Entry<String, Object> entry : partition) {
                    batchMap.put(entry.getKey(), entry.getValue());
                }
                redisUtils.multiSetObj(batchMap);

                // 设置过期时间
                for (Object key : batchMap.keySet()) {
                    redisUtils.expireObj(key, EXPIRE);
                }
            }
        } catch (Exception e) {
            log.error("批量设置缓存失败", e);
        }
    }
}