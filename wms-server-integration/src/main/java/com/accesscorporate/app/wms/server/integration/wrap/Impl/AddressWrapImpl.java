package com.accesscorporate.app.wms.server.integration.wrap.Impl;

import com.accesscorporate.app.wms.server.integration.wrap.IAddressWrap;
import com.danchuang.global.scm.address.client.api.AddressManageFeignClient;
import com.danchuang.global.scm.address.client.api.BaseAddressDataFeignClient;
import com.danchuang.global.scm.address.client.dto.BaseAddressDTO;
import com.danchuang.global.scm.address.client.request.address.AddressQueryRequest;
import com.idanchuang.component.base.JsonResult;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class AddressWrapImpl implements IAddressWrap {

    @Resource
    private BaseAddressDataFeignClient baseAddressDataFeignClient;
    @Resource
    private AddressManageFeignClient addressManageFeignClient;

    @Override
    public BaseAddressDTO getByAddressCode(String addressCode) {
        JsonResult<BaseAddressDTO> result = baseAddressDataFeignClient.getByAddressCode(addressCode).mustSuccess();
        return result.getData();
    }

    @Override
    public List<BaseAddressDTO> queryByParentCode(AddressQueryRequest addressQueryRequest) {
        JsonResult<List<BaseAddressDTO>> result = addressManageFeignClient.queryByParentCode(addressQueryRequest).mustSuccess();
        return result.getData();
    }


}
