package com.accesscorporate.app.wms.server.integration.wrap;

import com.accesscorporate.app.wms.server.integration.dto.UserDetailResponse;

import java.util.List;

/**
 * 用户信息获取--防腐层
 *
 * <AUTHOR>
 * 2025/2/5 18:09
 */
public interface IUserInfoWrap {

    /**
     * 根据用户ID、获取用户详情
     *
     * @param userIdList: 用户ID列表
     * @return List<UserDetailResponse> 用户信息详情Response
     * <AUTHOR>
     * 2025/2/5 17:59
     */
    List<UserDetailResponse> getUserByIdList(List<Long> userIdList);


    /**
     * 根据用户Token 获取用户详情
     *
     * @param token: 用户Token
     * @return UserDetailResponse
     * <AUTHOR>
     * 2025/2/6 16:22
     */
    UserDetailResponse getUserInfoByToken(String token);


}
