package com.accesscorporate.app.wms.server.integration.wrap.Impl;

import com.accesscorporate.app.wms.server.common.config.WmsAuthPermissionProperties;
import com.accesscorporate.app.wms.server.integration.dto.TenantResponse;
import com.accesscorporate.app.wms.server.integration.wrap.IUserPermissionWrap;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.core.json.Jackson;
import com.idanchuang.sso.external.facade.ExternalBrandPermissionServiceFacade;
import com.idanchuang.sso.model.dto.system.GroupBroupDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 用户权限-Wrap
 *
 * <AUTHOR>
 * 2025/2/8  13:26
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UserPermissionWrapImpl implements IUserPermissionWrap {

    private final ExternalBrandPermissionServiceFacade brandPermissionServiceFacade;

    private final WmsAuthPermissionProperties wmsAuthPermissionProperties;

    @Override
    public List<String> queryUserWarehousePermission(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        JsonResult<List<GroupBroupDTO>> dataPermissionWhRes = brandPermissionServiceFacade.getDataPermissionInTypeCodesAndRoleCodes(
                userId,
                wmsAuthPermissionProperties.getApplicationCode(),
                Collections.emptySet(),
                Collections.singleton(wmsAuthPermissionProperties.getDataRoleCodeWh())
        ).mustSuccess();
        log.info("查询用户的仓权限点信息,userId:{}, result:{}", userId, Jackson.toJsonString(dataPermissionWhRes));
        List<GroupBroupDTO> data = dataPermissionWhRes.getData();
        return data.stream().filter(groupBroupDTO ->
                        groupBroupDTO.getGroupCode().equals(wmsAuthPermissionProperties.getDataRoleCodeWh())
                ).flatMap(x -> x.getBrandCodeList().stream())
                .distinct().toList();
    }

    @Override
    public List<TenantResponse> queryUserTenantPermission(Long userId) {

        if (userId == null) {
            return Collections.emptyList();
        }
        JsonResult<List<GroupBroupDTO>> dataPermissionWhRes = brandPermissionServiceFacade.getDataPermissionInTypeCodesAndRoleCodes(
                userId,
                wmsAuthPermissionProperties.getApplicationCode(),
                Collections.emptySet(),
                Collections.singleton(wmsAuthPermissionProperties.getDataRoleCodeTenant())
        ).mustSuccess();
        log.info("查询用户的租户信息,userId:{}, result:{}", userId, Jackson.toJsonString(dataPermissionWhRes));
        List<GroupBroupDTO> data = dataPermissionWhRes.getData();
        return data.stream()
                .filter(groupBroupDTO -> groupBroupDTO.getGroupCode().equals(wmsAuthPermissionProperties.getDataRoleCodeTenant()))
                .flatMap(x -> x.getBrandCodeList().stream())
                .distinct()
                .map(brandCode -> new TenantResponse(Long.parseLong(brandCode)))
                .toList();

    }
}
