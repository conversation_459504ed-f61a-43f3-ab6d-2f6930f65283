package com.accesscorporate.app.wms.server.integration.config;

import com.accesscorporate.app.wms.server.common.constant.MqConstant;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class MqConfig {


    @Bean
    public Queue mpsOrderFinishMessageQueue() {
        return new Queue(MqConstant.ALLOCATE_MESSAGE_QUEUE);
    }

    @Bean
    public FanoutExchange mpsOrderFinishMessageExchange() {
        return new FanoutExchange(MqConstant.ALLOCATE_MESSAGE_EXCHANGE);
    }

    @Bean
    public Binding orderReturnBinding() {
        return BindingBuilder
                .bind(mpsOrderFinishMessageQueue())
                .to(mpsOrderFinishMessageExchange());
    }


}
