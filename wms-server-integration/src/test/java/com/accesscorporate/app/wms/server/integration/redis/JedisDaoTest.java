package com.accesscorporate.app.wms.server.integration.redis;

import com.idanchuang.component.config.apollo.ConfigService;
import com.idanchuang.component.redis.util.RedisUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * JedisDao 测试类
 * 验证使用RedisUtil重写后的功能
 * 
 * <AUTHOR>
 */
class JedisDaoTest {

    @Mock
    private RedisUtil redisUtils;

    @Mock
    private ConfigService configService;

    @InjectMocks
    private JedisDao jedisDao;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSetAndGet() {
        // 准备测试数据
        String key = "test:key";
        String value = "test_value";
        
        // Mock UserContextAssistant
        try (MockedStatic<com.accesscorporate.app.wms.server.common.utils.UserContextAssistant> mockedStatic = 
             mockStatic(com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.class)) {
            
            mockedStatic.when(() -> com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.getTenantId())
                       .thenReturn(1L);
            mockedStatic.when(() -> com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.getCurrentWarehouseId())
                       .thenReturn(1L);
            
            when(configService.getString("redis.global.cache", "")).thenReturn("");
            
            // 执行set操作
            String result = jedisDao.set(key, value);
            
            // 验证RedisUtil的setEx方法被调用
            verify(redisUtils, times(1)).setEx(anyString(), any(byte[].class), eq(JedisDao.EXPIRE));
            assertEquals("OK", result);
        }
    }

    @Test
    void testSetString() {
        // 准备测试数据
        String key = "test:string:key";
        String value = "test_string_value";
        int timeout = 3600;
        
        try (MockedStatic<com.accesscorporate.app.wms.server.common.utils.UserContextAssistant> mockedStatic = 
             mockStatic(com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.class)) {
            
            mockedStatic.when(() -> com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.getTenantId())
                       .thenReturn(1L);
            mockedStatic.when(() -> com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.getCurrentWarehouseId())
                       .thenReturn(1L);
            
            when(configService.getString("redis.global.cache", "")).thenReturn("");
            
            // 执行setString操作
            String result = jedisDao.setString(key, value, timeout);
            
            // 验证RedisUtil的setEx方法被调用
            verify(redisUtils, times(1)).setEx(anyString(), eq(value), eq(timeout));
            assertEquals("OK", result);
        }
    }

    @Test
    void testGetString() {
        // 准备测试数据
        String key = "test:get:key";
        String expectedValue = "test_get_value";
        
        try (MockedStatic<com.accesscorporate.app.wms.server.common.utils.UserContextAssistant> mockedStatic = 
             mockStatic(com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.class)) {
            
            mockedStatic.when(() -> com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.getTenantId())
                       .thenReturn(1L);
            mockedStatic.when(() -> com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.getCurrentWarehouseId())
                       .thenReturn(1L);
            
            when(configService.getString("redis.global.cache", "")).thenReturn("");
            when(redisUtils.get(anyString())).thenReturn(expectedValue);
            
            // 执行getString操作
            String result = jedisDao.getString(key);
            
            // 验证结果
            assertEquals(expectedValue, result);
            verify(redisUtils, times(1)).get(anyString());
        }
    }

    @Test
    void testIncr() {
        // 准备测试数据
        String key = "test:incr:key";
        long expectedValue = 1L;
        
        try (MockedStatic<com.accesscorporate.app.wms.server.common.utils.UserContextAssistant> mockedStatic = 
             mockStatic(com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.class)) {
            
            mockedStatic.when(() -> com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.getTenantId())
                       .thenReturn(1L);
            mockedStatic.when(() -> com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.getCurrentWarehouseId())
                       .thenReturn(1L);
            
            when(configService.getString("redis.global.cache", "")).thenReturn("");
            when(redisUtils.incr(anyString())).thenReturn(expectedValue);
            
            // 执行incr操作
            long result = jedisDao.incr(key);
            
            // 验证结果
            assertEquals(expectedValue, result);
            verify(redisUtils, times(1)).incr(anyString());
            verify(redisUtils, times(1)).expire(anyString(), eq(JedisDao.EXPIRE));
        }
    }

    @Test
    void testDel() {
        // 准备测试数据
        String key = "test:del:key";
        
        try (MockedStatic<com.accesscorporate.app.wms.server.common.utils.UserContextAssistant> mockedStatic = 
             mockStatic(com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.class)) {
            
            mockedStatic.when(() -> com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.getTenantId())
                       .thenReturn(1L);
            mockedStatic.when(() -> com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.getCurrentWarehouseId())
                       .thenReturn(1L);
            
            when(configService.getString("redis.global.cache", "")).thenReturn("");
            when(redisUtils.del(any(String[].class))).thenReturn(1L);
            
            // 执行del操作
            long result = jedisDao.del(key);
            
            // 验证结果
            assertEquals(1L, result);
            verify(redisUtils, times(1)).del(any(String[].class));
        }
    }

    @Test
    void testBatchGetObject() {
        // 准备测试数据
        List<String> keys = Arrays.asList("key1", "key2", "key3");
        List<Object> mockResults = Arrays.asList("value1", "value2", "value3");
        
        try (MockedStatic<com.accesscorporate.app.wms.server.common.utils.UserContextAssistant> mockedStatic = 
             mockStatic(com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.class)) {
            
            mockedStatic.when(() -> com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.getTenantId())
                       .thenReturn(1L);
            mockedStatic.when(() -> com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.getCurrentWarehouseId())
                       .thenReturn(1L);
            
            when(configService.getString("redis.global.cache", "")).thenReturn("");
            when(redisUtils.multiGet(anyList())).thenReturn(mockResults);
            
            // 执行batchGetObject操作
            List<Object> results = jedisDao.batchGetObject(keys);
            
            // 验证结果
            assertNotNull(results);
            assertEquals(3, results.size());
            verify(redisUtils, times(1)).multiGet(anyList());
        }
    }

    @Test
    void testBatchSetString() {
        // 准备测试数据
        Map<String, Object> entityMap = new HashMap<>();
        entityMap.put("key1", "value1");
        entityMap.put("key2", "value2");
        
        try (MockedStatic<com.accesscorporate.app.wms.server.common.utils.UserContextAssistant> mockedStatic = 
             mockStatic(com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.class)) {
            
            mockedStatic.when(() -> com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.getTenantId())
                       .thenReturn(1L);
            mockedStatic.when(() -> com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.getCurrentWarehouseId())
                       .thenReturn(1L);
            
            when(configService.getString("redis.global.cache", "")).thenReturn("");
            
            // 执行batchSetString操作
            jedisDao.batchSetString(entityMap);
            
            // 验证RedisUtil的multiSet方法被调用
            verify(redisUtils, times(1)).multiSet(anyMap());
            verify(redisUtils, times(2)).expire(anyString(), eq(JedisDao.EXPIRE));
        }
    }

    @Test
    void testGetKeyGeneration() {
        // 测试key生成逻辑
        try (MockedStatic<com.accesscorporate.app.wms.server.common.utils.UserContextAssistant> mockedStatic = 
             mockStatic(com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.class)) {
            
            mockedStatic.when(() -> com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.getTenantId())
                       .thenReturn(1L);
            mockedStatic.when(() -> com.accesscorporate.app.wms.server.common.utils.UserContextAssistant.getCurrentWarehouseId())
                       .thenReturn(2L);
            
            when(configService.getString("redis.global.cache", "")).thenReturn("");
            
            // 执行操作来触发getKey方法
            jedisDao.getString("test");
            
            // 验证生成的key格式
            verify(redisUtils, times(1)).get(eq("dc_chain_wms:1:2:test"));
        }
    }
}
