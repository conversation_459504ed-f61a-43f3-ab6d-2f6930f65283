<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>wms-server</artifactId>
        <groupId>com.accesscorporate.app</groupId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>wms-server-integration</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.idanchuang.mqlistener</groupId>
            <artifactId>danchuang-base-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.idanchuang.component</groupId>
            <artifactId>component-mq-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.accesscorporate.app</groupId>
            <artifactId>wms-server-common</artifactId>
        </dependency>
        <dependency>
            <!-- (SpringCloud消费者强制依赖)消费者组件, 组件文档: http://git.acg.team/arch/spring-cloud-parent-all/tree/master/components/component-consumer -->
            <groupId>com.idanchuang.component</groupId>
            <artifactId>component-consumer</artifactId>
        </dependency>


        <dependency>
            <groupId>com.idanchuang</groupId>
            <artifactId>sso-external-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.idanchuang</groupId>
            <artifactId>sso-client-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danchuang.global.scm.address</groupId>
            <artifactId>address-client</artifactId>
            <version>${address.client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.abmau</groupId>
            <artifactId>abmau-service-open-warehouse-api</artifactId>
            <version>${abmau.service.open.warehouse.api}</version>
        </dependency>
        <dependency>
            <groupId>com.idanchuang.component</groupId>
            <artifactId>component-redis</artifactId>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <!-- deploy 时排除此模块 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <!--开启filtering功能  -->
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>