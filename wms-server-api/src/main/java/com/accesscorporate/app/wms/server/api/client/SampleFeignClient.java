package com.accesscorporate.app.wms.server.api.client;

import com.accesscorporate.app.wms.server.api.dto.MessageDTO;
import com.idanchuang.component.base.JsonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * RPC接口样例
 *
 * 注意:
 * 1.FeignClient注解的value必须与 spring.application.name 配置相同
 * 2.FeignClient注解必须指定contextId属性(bean的name), 如果不指定的话会引起(bean with that name has already been defined)问题
 * 3.不要在FeignClient接口上添加RequestMapping注解, 否则可能会出现bean#method冲突的问题, 可以在实现类上加@RequestMapping并在接口上添加path属性来实现此需求
 *
 * <AUTHOR>
 * Created at 2021/4/8 16:45
 **/
@FeignClient(value = "wms-server", contextId = "sampleFeignClient")
public interface SampleFeignClient {

    @PostMapping("hello")
    JsonResult<String> hello(@RequestBody MessageDTO msg);

}
