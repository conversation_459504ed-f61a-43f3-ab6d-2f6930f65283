<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>wms-server</artifactId>
        <groupId>com.accesscorporate.app</groupId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>wms-server-api</artifactId>
    <!-- 协议包有改动时请务必升级此版本号哦 -->
    <version>${revision}</version>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.idanchuang.component</groupId>
            <artifactId>component-provider-api</artifactId>
        </dependency>

        <!-- 尽量不要在这里添加其他依赖哦, 否则会对消费者添加额外负担 -->
    </dependencies>

    <build>
        <plugins>
            <!-- 加入此插件, deploy时将源码一并发布至nexus, 这样调用方就可以下载源码看到接口注释啦 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>