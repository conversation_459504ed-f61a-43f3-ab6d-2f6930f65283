# 脚手架说明

## 前置依赖
1. Java17
2. Maven3.6+

## 快速开始

* 编辑启动配置，添加JVM参数：
```shell
--add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED --add-opens java.base/java.math=ALL-UNNAMED --add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/java.nio=ALL-UNNAMED --add-opens java.base/java.security=ALL-UNNAMED --add-opens java.base/java.text=ALL-UNNAMED --add-opens java.base/java.time=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/jdk.internal.access=ALL-UNNAMED --add-opens java.base/jdk.internal.misc=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.security=ALL-UNNAMED --add-opens java.base/sun.net=ALL-UNNAMED --add-opens java.base/sun.net.util=ALL-UNNAMED --add-opens java.base/sun.reflect.annotation=ALL-UNNAMED --add-opens java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED --add-exports java.base/sun.security.action=ALL-UNNAMED --add-opens java.base/java.util.concurrent=ALL-UNNAMED --add-reads java.base=ALL-UNNAMED
```
* 运行 Application
* 访问 http://localhost:8080/doc.html (账号密码：swagger/swagger)

## 模块说明
* wms-server              ：项目parent
* wms-server-api          ：提供RPC接口与入参出参实体类（注意不要在此模块中引入无关的依赖）
* wms-server-biz          ：核心业务逻辑工程，包含Service
* wms-server-common       ：通用代码，主要是通用的跟业务不相关的代码，工具类、实体类等
* wms-server-dal          ：数据库处理工程，包含数据库表实体、Mapper
* wms-server-integration  ：外部调用整合工程，一般用来封装对第三方的调用
* wms-server-web          ：web代码工程，包含controller，job，listener等
* 备注：除了以上这些工程模块，非常复杂的工程可以根据需要再追加子模块，但是不要跟以上模块的功能重合

## 依赖说明
### 父pom
**所有微服务项目统一将 com.idanchuang:spring-cloud-starter-parent 作为父pom，不可更改！后续升级组件以父pom为准！**

### 基础组件
以下提供的基础组件均以官方spring-boot-starter为基础，并在此基础上进行增强，两者完全兼容，不用担心使用习惯改变的问题！
> 以下为脚手架默认依赖的组件，默认为web应用
1. component-base: 统一接口响应体结构JsonResult等
2. component-logback: 组件实现Java应用日志规范、指标日志、异步日志等
3. component-config-apollo: 组件实现对接apollo配置中心，配置动态生效、配置加密等
4. component-just-web: 组件实现WEB服务统一的全局接口异常处理、访问日志、接口文档等
5. component-sentinel: 组件实现动态流控降级(在Sentinel流控后台配置规则, 后台地址见组件文档)
6. component-provider: 组件使服务拥有服务提供者角色，可提供RPC接口
7. component-consumer: 组件使服务拥有消费者角色(附带直连服务功能)
8. component-mybatisplus: 基于mybatisplus，提供字段加密、SQL监控、慢日志等

> 以下为按需添加的依赖组件
1. component-redis: 提供Redis基础能力，并提供分布式锁、分布式限流、RedisUtil等
2. component-mq-amqp: 提供对接RabbitMQ的能力，兼容开源RabbitMQ与阿里云AMQP服务
4. component-businesslog-es: 提供对接ElasticSearch的能力
5. component-feishu: 提供对接飞书开放平台的能力，多维表格读写等
6. component-i18n: 提供国际化能力，语言包、机器翻译等
7. component-gauss: 提供对接幂等服务
8. component-sequence: 提供对接分布式ID生成服务
9. component-xxl-job: 提供零配置对接xxljob的能力

> 关于组件的详细文档，请查阅：[飞书组件文档](https://access.feishu.cn/wiki/wikcn8SccjEccH0Y1apw1prKWXe)

## 关于配置
脚手架默认对接了Apollo配置中心，除了application.yml中必要的初始配置以外，所有的配置应全部在Apollo中添加
> 为了便于演示，初始化的脚手架项目在application.yml添加一些用于演示的数据库配置，请自行删除，并将真实的数据库配置信息添加到Apollo中

## 关于注册发现
脚手架默认依赖了consumer与provider组件，无缝对接了自研的Sun注册中心（配置项存在于Apollo的公共命名空间），开发者无需关注注册中心的相关配置，只需正常使用FeignClient接口即可。

## 关于日志
脚手架已经在web模块的resources目录下自动生成 logback.spring.xml 文件，应用无需任何配置，直接打印日志即可，如下案例：
```java
@Slf4j // 例子1：使用lombok插件
public class LogExample {
    
    public void example(String name) {
        log.info("日志 : {}", name);
    }
}
```
```java
public class LogExample {
    
    private static final Logger logger = LoggerFactory.getLogger(LogExample.class);
    
    public void example(String name) {
        logger.info("日志 : {}", name);
    }
}
```

## 接口响应体规范
所有REST接口的响应体都必须是JsonResult对象
```java
JsonResult<Void> // 接口无返回值
JsonResult<String> // 接口仅返回一个字符串，其他基础类型雷同
JsonResult<List<xx>> // 接口返回一个列表
JsonResult<PageData<xx>> // 接口返回一个分页对象
```

## 全局异常处理
> 基础组件中已经包含了一个统一的全局异常处理，业务开发无需再编写定制的异常处理类 <br/>

推荐直接使用ExFactory抛出异常，基础组件会对异常统一拦截并封装为JsonResult对象响应给前端，如下：
```java
public void doSomething(Long id) {
    if (id == null) {
        // 抛出一个参数异常
        throw ExFactory.throwParamError("id 不能为空");
    }
    String name = getNameById(id);
    if (StringUtils.isBlank(name)) {
        // 抛出一个业务异常
        throw ExFactory.throwBusiness("业务异常，找不到相关信息，id: {}", id);
    }
}
```

## 接口文档
脚手架默认引入了Springdoc文档框架（swagger），开发者无需依赖其他文档框架，访问地址是：http://localhost:8080/doc.html , 默认账号密码是：swagger/swagger

## 发布协议包
```shell
mvn clean deploy -DskipTests
```

## 升级协议包版本
修改项目主pom.xml中的 revision 变量即可（如果加上 -SNAPSHOT 后缀则表示快照版本，可重复deploy）

## 本地直连调试
* 第一步: 修改项目根目录下的 ribbon-resolve.properties 文件, 配置好需要直连的服务名以及其地址
* 第二步: 添加jvm参数: -Dribbon.resolve.file=<文件绝对路径>/ribbon-resolve.properties

