package com.daxia.wms.delivery.print.service.carton;

import com.daxia.dubhe.api.wms.dto.LogisticsDTO;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoPrintDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoPrint;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.delivery.deliveryorder.service.DoWaveExService;
import com.daxia.wms.delivery.print.dto.BaseCartonPrintDTO;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.helper.CurrencyConverter;
import com.daxia.wms.delivery.print.helper.PrintCartonHelper;
import com.daxia.wms.delivery.print.service.PrintWaybillService;
import com.daxia.wms.print.dto.PrintCfg;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.print.utils.PrintHelper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by yangzhipei on 2017-9-25 19:10:10
 */
@Name("printZTWaybillService")
@lombok.extern.slf4j.Slf4j
public class PrintZTWaybillServiceImpl extends AbstractPrintWaybillService implements PrintWaybillService {

    @In
    private DoWaveExService doWaveExService;
    @In
    private DoPrintDAO doPrintDAO;
    private ObjectMapper objectMapper = new ObjectMapper();

    @Create
    public void init () {
        this.setWaybillType(Constants.WaybillType.ZTO);
    }

    @Override
    protected void buildDTO(List<PrintReportDto> printReportDtos, DeliveryOrderHeader doHeader, BaseCartonPrintDTO carton,
                            int index, int count) {
        CartonPrintDTO cartonPrintDTO = new CartonPrintDTO();
        cartonPrintDTO.setIsPrinted(carton.getIsPrinted());
        cartonPrintDTO.setCartonId(carton.getId());
        cartonPrintDTO.setCartonNo(carton.getCartonNo());
        cartonPrintDTO.setDoNo(doHeader.getDoNo());
        cartonPrintDTO.setOutRefNo(doHeader.getRefNo1());
        cartonPrintDTO.setIsCOD(PrintCartonHelper.isCOD(doHeader));
        cartonPrintDTO.setSortGridNo(doHeader.getSortGridNo());
        cartonPrintDTO.setSortGridCount(doHeader.getWaveHeader().getDoHeaders().size());
        cartonPrintDTO.setWaveNo(doHeader.getWaveHeader().getWaveNo());

        // 设置篮号（波次顺序）
        cartonPrintDTO.setBasketNo(String.valueOf(index));
        cartonPrintDTO.setOriginalSoCode(doHeader.getOriginalSoCode());
        // 设置收货人地址
        cartonPrintDTO.setClientAddress(PrintCartonHelper.buildAddress(doHeader));
        cartonPrintDTO.setClientName(doHeader.getConsigneeName());
        cartonPrintDTO.setClientPhone(PrintCartonHelper.buildTelOrMobile(doHeader));


        // 设置寄件人地址信息
        setSendAddressInfo(doHeader,cartonPrintDTO);
        // 设置始发地代码编号和目的地代码编号
        DoWaveEx doWaveEx = doWaveExService.findByDoHeaderId(doHeader.getId());
        if (null != doWaveEx) {
            //大头笔信息
            cartonPrintDTO.setDestAddressCode(doWaveEx.getDestinationCode());
            cartonPrintDTO.setDestAddressName(doWaveEx.getDestinationName());
            cartonPrintDTO.setStartAddressName(doWaveEx.getOriginName());
        }
        // 运单号
        cartonPrintDTO.setWayBill(carton.getCartonNo());
        // 是否需要待收货款
        boolean receivable = doHeader.getReceivable() != null
                && doHeader.getReceivable().compareTo(BigDecimal.ZERO) > 0;
        cartonPrintDTO.setNeedReceivable(receivable);
        if (receivable) {
            // 设置代收金额
            cartonPrintDTO.setServiceCodAmount(doHeader.getReceivable().toString());
            cartonPrintDTO.setCodCurrency(CurrencyConverter.convert(doHeader.getReceivable().toString()));
            cartonPrintDTO.setProductSalesType("COD");
        }
        // 设置图片路径
        cartonPrintDTO.setBasePrintImgPath(PrintHelper.getBasePrintImgPath());

        cartonPrintDTO.setRefNo1(doHeader.getOriginalSoCode());

        // 设置配送商的信息
        setProductInfo(doHeader, cartonPrintDTO);
        printReportDtos.add(cartonPrintDTO);
        this.processWayBillInfo(doHeader.getId(), cartonPrintDTO);
    }

    @Override
    protected PrintData genPrintData(List<PrintReportDto> dtoList, DeliveryOrderHeader doHeader) {
        PrintData printData = new PrintData();
        printData.setDtoList(dtoList);
        printData.setPrintCfg(new PrintCfg("wayBillZTO", "76", "130"));
        return printData;
    }

    @SneakyThrows
    private void processWayBillInfo(Long doId, CartonPrintDTO cartonPrintDTO) {
        DoPrint doPrint = doPrintDAO.findByDoHeaderId(doId, Constants.DoPrintInfoType.WAYBILL_JSON.getValue());
        if (null == doPrint) {
            throw new DeliveryException(DeliveryException.WAYBILL_IMAGE_NOT_EXIST);
        }
        LogisticsDTO logisticsDTO = objectMapper.readValue(doPrint.getContent(), LogisticsDTO.class);

        cartonPrintDTO.setDestRouteLabel(logisticsDTO.getDestRouteLabel());
        cartonPrintDTO.setDestTeamCode(logisticsDTO.getDestTeamCode());
        cartonPrintDTO.setCodingMapping(logisticsDTO.getCodingMapping());
        cartonPrintDTO.setTwoDimensionCode(logisticsDTO.getTwoDimensionCode());
        cartonPrintDTO.setDestAddressCode(logisticsDTO.getDestRouteLabel());
        cartonPrintDTO.setDestAddressName(logisticsDTO.getBagAddr());
    }
}
