package com.daxia.wms.delivery.deliveryorder.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.dao.operation.ChainClient;
import com.daxia.framework.common.dao.operation.Handler;
import com.daxia.framework.common.util.*;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.*;
import com.daxia.wms.delivery.deliveryorder.dto.DoAllocateHeaderDto;
import com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateHeader;
import com.daxia.wms.delivery.deliveryorder.filter.DoAlcHeaderFilter;
import com.daxia.wms.delivery.wave.dto.AutoWaveDTO;
import com.daxia.wms.delivery.wave.dto.OverAllocateDto;
import com.daxia.wms.master.rule.filter.BigWaveFilter;
import com.daxia.wms.wave.vo.DoRecommendDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.hibernate.Hibernate;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.*;

@Name("com.daxia.wms.delivery.doAllocateHeaderDAO")
@lombok.extern.slf4j.Slf4j
public class DoAllocateHeaderDAO extends HibernateBaseDAO<DoAllocateHeader, Long> {
    private static final long serialVersionUID = 3320667226589218283L;

    private static final String AISLES_SPLIT_SIGN = ",";

    /**
     * filter查询订单分配信息，订单分配界面查询
     *
     * @param filter
     * @param startIndex
     * @param pageSize
     * @return
     */
    @SuppressWarnings("unchecked")
    public DataPage<DoAllocateHeaderDto> findDoHeaderPageInfo(DoAlcHeaderFilter filter, int startIndex, int pageSize) {
        Long whId = ParamUtil.getCurrentWarehouseId();
        StringBuilder hql = new StringBuilder(
                "select new com.daxia.wms.delivery.deliveryorder.dto.DoAllocateHeaderDto(");
        hql.append(" o.id,o.doNo,o.status,o.releaseStatus,o.expectedQty,o.shipQty,o.doCreateTime,");
        hql.append(" o.shipTime,o.planShipTime,o.doType,o.isHalfDayDelivery,o.station.deliveryLimitType,o.country, ");
        hql.append(" (case when o.provinceName is null then pi.provinceCname else o.provinceName end), ");
        hql.append(" (case when o.cityName is null then cii.cityCname else o.cityName end), ");
        hql.append(" (case when o.countyName is null then cti.countyCname else o.countyName end), ");
        hql.append(" wh.waveNo,o.sortGridNo,");
        hql.append(" o.consigneeName,o.address,o.postCode,o.telephone,o.mobile,c.distSuppCompName,");
        hql.append(" o.invoiceQty,o.userDeffine1,o.holdCode,o.holdReason,o.holdWho,o.holdTime,");
        hql.append(
                " o.productAmount,o.orderDeliveryFee,o.exchangeFlag, o.grossWt,o.receivable,o.notes,o.createdAt,o.tranType,o.replStatus,bc.customerName ");
        hql.append(" )");
        hql.append(" from DoAllocateHeader o ");
        hql.append(" left join o.provinceInfo pi");
        hql.append(" left join o.cityInfo cii");
        hql.append(" left join o.countyInfo cti");
        hql.append(" left join o.waveHeader wh");
        hql.append(" left join o.carrier c");
        hql.append(" left join o.station st");
        hql.append(" left join o.businessCustomer bc");
        hql.append(" where o.warehouseId  = " + whId);

        StringBuilder countHql = new StringBuilder("select count(o.id) ");
        countHql.append(" from DoAllocateHeader o ");
        countHql.append(" left join o.station st");
        countHql.append(" where o.warehouseId  = " + whId);
        // if (filter.getTransSkuFlag() != null && filter.getTransSkuFlag() && modeFlag
        // && StringUtils.equals(filter.getDoType(), Constants.DoType.SELL.getValue()))
        // {
        // countHql
        // .append(" and exists (select 1 from DoAllocateDetail alcd,TransSku tts where
        // alcd.skuId = tts.id and alcd.doHeaderId = o.id and alcd.warehouseId = "
        // + whId + " and tts.warehouseId = alcd.warehouseId)");
        // hql.append(" and exists (select 1 from DoAllocateDetail alcd,TransSku tts
        // where alcd.skuId = tts.id and alcd.doHeaderId = o.id and alcd.warehouseId = "
        // + whId + " and tts.warehouseId = alcd.warehouseId)");
        // }
        // Map<String, String> orderByMap = new HashMap<String, String>();
        // orderByMap.put("o.planShipTime", "asc");
        // filter.setOrderByMap(orderByMap);

        return (DataPage<DoAllocateHeaderDto>) this.executeQueryByFilter(hql.toString(), countHql.toString(),
                startIndex, pageSize, filter);
    }

    /**
     * 批量更新分配定单释放状态，释放定单或锁定定单时可以调用该方法
     *
     * @param releaseStauts
     * @param ids
     */
    public void updateReleaseStatus(String releaseStauts, List<Long> ids) {
        String hql = "update DoAllocateHeader o set o.releaseStatus = :status " +
                "where o.id in (:ids) and o.warehouseId = :warehouseId";
        Query query = createUpdateQuery(hql);
        query.setString("status", releaseStauts);
        query.setParameterList("ids", ids);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 批量更新 分配定单 为 释放状态，且清空其 分配时间
     *
     * @param ids
     */
    public void rlsDoAndClearAlcTime(List<Long> ids) {
        String hql = "update DoAllocateHeader o set o.releaseStatus = :status, o.allocTime = :allocTime "
                + "where o.id in (:ids) and o.warehouseId = :warehouseId";
        Query query = createUpdateQuery(hql);
        query.setString("status", Constants.ReleaseStatus.RELEASE.getValue());
        query.setString("allocTime", null);
        query.setParameterList("ids", ids);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 删除订单分配头
     *
     * @param ids
     */
    public void remove(List<Long> ids) {
        String sqlString = "delete from doc_alc_header where id in (:ids) and warehouse_id = :warehouseId";
        Query query = this.createSQLQuery(sqlString);
        query.setParameterList("ids", ids);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    @SuppressWarnings("unchecked")
    public List<Long> queryNeedAllocateDoHeaderIds(int batchSize, Integer failMaxNum,
            Integer failCheck, Long warehouseId) {
        StringBuilder hql = new StringBuilder();
        hql.append(
                "select o.id from DoAllocateHeader o where o.status in (:status) and o.releaseStatus = :rs and o.doType in (:doTypes)"
                        +
                        " and (o.allocTime = '0000-00-00 00:00:00' or o.allocTime is null  or o.replStatus = :replStatus) ");
        // 判断是否配置最大失败数，并且大于0
        if (failCheck != null && failCheck.intValue() == 1 && failMaxNum != null && failMaxNum.intValue() > 0) {
            hql.append(" and o.allocateFailNum < :allocateFailNum");
        }
        if (warehouseId != null) {
            hql.append(" and o.warehouseId = :warehouseId");
        }
        // 按照订单导入时间排序
        hql.append(" order by o.doCreateTime ");

        Query query = createQuery(hql.toString());
        query.setParameterList("status",
                new String[] { DoStatus.INITIAL.getValue(), DoStatus.PARTALLOCATED.getValue() });
        query.setString("rs", ReleaseStatus.RELEASE.getValue());

        List<String> doTypes = Lists.newArrayList(DoType.SELL.getValue(), DoType.WHOLESALE.getValue());
        if (SystemConfig.configIsOpen("delivery.allocate.ttAutoAllocate", warehouseId)) {
            doTypes.add(DoType.ALLOT.getValue());
        }
        query.setParameterList("doTypes", doTypes);
        query.setInteger("replStatus", Constants.DoReplStatus.COMPLETE.getValue());
        // 判断是否配置最大失败数，并且大于0
        if (failCheck != null && failCheck.intValue() == 1 && failMaxNum != null && failMaxNum.intValue() > 0) {
            query.setParameter("allocateFailNum", Long.valueOf(failMaxNum));
        }
        if (warehouseId != null) {
            query.setParameter("warehouseId", warehouseId);
        }
        query.setMaxResults(batchSize);
        return query.list();
    }

    @SuppressWarnings("unchecked")
    public List<Long> queryNeedAllocateRtvIds(int batchSize, Integer failMaxNum, Integer failCheck) {
        StringBuilder hql = new StringBuilder();
        hql.append("select o.id from DoAllocateHeader o "
                + "where o.status in (:status) and o.releaseStatus = :rs and o.doType = :doType and o.allocTime is null ");
        // 判断是否配置最大失败数，并且大于0
        if (failCheck != null && failCheck.intValue() == 1 && failMaxNum != null && failMaxNum.intValue() > 0) {
            hql.append(" and o.allocateFailNum < :allocateFailNum");
        }
        hql.append(" order by o.doCreateTime ");
        Query query = createQuery(hql.toString());
        query.setParameterList("status",
                new String[] { DoStatus.INITIAL.getValue(), DoStatus.PARTALLOCATED.getValue() });
        query.setString("rs", ReleaseStatus.RELEASE.getValue());
        query.setString("doType", DoType.RTV.getValue());
        // 判断是否配置最大失败数，并且大于0
        if (failCheck != null && failCheck.intValue() == 1 && failMaxNum != null && failMaxNum.intValue() > 0) {
            query.setParameter("allocateFailNum", Long.valueOf(failMaxNum));
        }
        query.setMaxResults(batchSize);
        return query.list();
    }

    @SuppressWarnings("unchecked")
    public List<DoAllocateHeader> queryByIds(List<Long> alcheaderIds) {
        Query query = createQuery("from DoAllocateHeader o where o.id in (:ids)").setParameterList("ids", alcheaderIds);
        return query.list();
    }

    /**
     * 生成波次界面查询，不带推荐
     */
    @SuppressWarnings("unchecked")
    public DataPage<DoHeaderDto> findBigWaveDoList(BigWaveFilter bigWaveFilter, int startIndex, int pageSize) {
        Long whId = ParamUtil.getCurrentWarehouseId();
        StringBuilder hql = new StringBuilder("select new com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto(");
        hql.append(" o.id,o.doNo,o.expectedQty,o.doCreateTime,");
        hql.append(" o.doType,o.isHalfDayDelivery,st.deliveryLimitType,o.country, ");
        hql.append(" o.provinceName, ");
        hql.append(" o.cityName, ");
        hql.append(" o.countyName, ");
        hql.append("  o.consigneeName,o.address,");
        hql.append(" o.postCode,o.telephone,o.mobile,c.distSuppCompName,o.invoiceQty,o.volume, o.receivable,");
        hql.append(
                " o.lastDcName,o.tranType,o.isFresh,o.isValuable,o.planShipTime,o.grossWt,st.stationName,o.orderAmount, o.volumeType, o.buyerRemark, o.sellerRemark,"
                        +
                        " doWaveEx.groupTimes,o.payTime ,bc.customerName,o.deliveryOrderHeader.emergencyFlag,groupRule"
                        +
                        ".ruleName,o.originalSoCode,o.createdAt, o.deliveryOrderHeader.channelCode, " +
                        "o.deliveryOrderHeader.storeCode,o.cycleClass,o.aisles,o.deliveryOrderHeader.userDeffine5 ");
        hql.append(" )");
        hql.append(" from DoAllocateHeader o ");
        hql.append(" left join o.carrier c");
        hql.append(" left join o.station st");
        hql.append(" left join o.doWaveEx doWaveEx");
        hql.append(" left join o.businessCustomer bc");
        hql.append(" left join doWaveEx.groupRule groupRule");
        hql.append(" where o.warehouseId  = " + whId);

        StringBuilder countHql = new StringBuilder("select count(o.id) ");
        countHql.append(" from DoAllocateHeader o ");
        countHql.append(" where o.warehouseId  = " + whId);

        // Map<String,String> orderByMap = new HashMap<String, String>();
        // orderByMap.put("o.planShipTime","asc");
        // bigWaveFilter.setOrderByMap(orderByMap);

        return (DataPage<DoHeaderDto>) this.executeQueryByFilter(hql.toString(), countHql.toString(), startIndex,
                pageSize, bigWaveFilter);
    }

    /**
     * 生成波次界面查询，带推荐
     */
    @SuppressWarnings("unchecked")
    public List<DoHeaderDto> findBigWaveDoList(List<Long> doIds) {
        StringBuilder hql = new StringBuilder("select new com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto(");
        hql.append(" o.id,o.doNo,o.expectedQty,o.doCreateTime,");
        hql.append(" o.doType,o.isHalfDayDelivery,st.deliveryLimitType,o.country, ");
        hql.append(" (case when o.provinceName is null then pi.provinceCname else o.provinceName end), ");
        hql.append(" (case when o.cityName is null then cii.cityCname else o.cityName end), ");
        hql.append(" (case when o.countyName is null then cti.countyCname else o.countyName end), ");
        hql.append("  o.consigneeName,o.address,");
        hql.append(" o.postCode,o.telephone,o.mobile,c.distSuppCompName,o.invoiceQty,o.volume, o.receivable,");
        hql.append(
                " o.lastDcName,o.tranType,o.isFresh,o.isValuable,o.planShipTime,o.grossWt,st.stationName,o.orderAmount,o.volumeType, o.buyerRemark, o.sellerRemark, doWaveEx.groupTimes, o.payTime, businessCustomer"
                        +
                        ".customerName,o.deliveryOrderHeader.emergencyFlag,groupRule.ruleName,o.originalSoCode,o.aisles,o.deliveryOrderHeader.userDeffine5 ");
        hql.append(" )");
        hql.append(" from DoAllocateHeader o ");
        hql.append(" left join o.provinceInfo pi");
        hql.append(" left join o.cityInfo cii");
        hql.append(" left join o.countyInfo cti");
        hql.append(" left join o.carrier c");
        hql.append(" left join o.station st");
        hql.append(" left join o.doWaveEx doWaveEx");
        hql.append(" left join o.businessCustomer businessCustomer");
        hql.append(" left join doWaveEx.groupRule groupRule");
        hql.append(" where o.id  in (:doIds)");

        return this.createQuery(hql.toString()).setParameterList("doIds", doIds).list();
    }

    @SuppressWarnings("unchecked")
    public String getAislesById(Long id) {
        String hql = "SELECT distinct CONCAT(loc.locType,'-', loc.aisle) as aisle "
                + "FROM PickTask pt, Location loc "
                + "where pt.doHeaderId = :id and pt.locId = loc.id";
        List<String> aisleList = this.getSession().createQuery(hql).setParameter("id", id).list();
        return ListUtil.collection2String(aisleList, AISLES_SPLIT_SIGN);
    }

    @SuppressWarnings("unchecked")
    public List<DoRecommendDTO> findDoRecommends(BigWaveFilter bigWaveFilter, Integer resultSize) {
        bigWaveFilter.setWarehouseId(ParamUtil.getCurrentWarehouseId());

        StringBuilder hql = new StringBuilder(
                "select o.aisles as aisles, o.id as doHeaderId, o.volume, o.grossWt, o.expectedQty");
        hql.append(" from DoAllocateHeader o ");
        // hql.append(" left join o.provinceInfo pi");
        // hql.append(" left join o.cityInfo cii");
        // hql.append(" left join o.countyInfo cti");
        hql.append(" left join o.carrier c");
        hql.append(" left join o.station st");

        if (StringUtil.isNotEmpty(bigWaveFilter.getBusinessCustomerName())) {
            hql.append(" left join o.businessCustomer bc");
        }

        hql.append(" where 1 = 1 ");

        List<Object> paramList = new ArrayList<Object>();
        hql.append(buildConditionHql(bigWaveFilter, paramList)).append(buildOrderCondition(bigWaveFilter, false));

        Query query = createQuery(hql.toString());
        query.setMaxResults(resultSize);
        this.prepareParameter(paramList, query);

        List<Object[]> results = query.list();
        List<DoRecommendDTO> dtos = new ArrayList<DoRecommendDTO>(results.size());
        for (Object[] objs : results) {
            DoRecommendDTO dto = new DoRecommendDTO();
            String aisles = (String) objs[0];
            if (aisles != null && aisles.length() > 1) {
                dto.getAisles().addAll(Arrays.asList(aisles.split(AISLES_SPLIT_SIGN)));
            }
            dto.setDoHeaderId((Long) objs[1]);
            dto.setExpectedQty((BigDecimal) objs[4]);
            dto.setVolume((BigDecimal) objs[2]);
            dto.setWeight((BigDecimal) objs[3]);
            dtos.add(dto);
        }

        return dtos;
    }

    public void updateDoNotAutoWave(List<Long> ids, List<String> nos) {
        String hql = "UPDATE DoAllocateHeader o SET o.waveFailNum = o.waveFailNum + 1 WHERE o.warehouseId = :warehouseId";
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("warehouseId", ParamUtil.getCurrentWarehouseId());
        if (ListUtil.isNotEmpty(ids)) {
            hql += " AND o.id in (:ids)";
            map.put("ids", ids);
        }
        if (ListUtil.isNotEmpty(nos)) {
            hql += " AND o.doNo in (:doNos)";
            map.put("doNos", nos);
        }
        Query query = this.createUpdateQuery(hql);
        this.prepareParameter(map, query);
        query.executeUpdate();
    }

    /**
     * 删除SLC分配头
     */
    public void deleteAlcHeader(Long doHeaderId) {
        String sql = "delete from doc_alc_header where id = :doHeaderId and warehouse_id = :whId";
        Query query = createSQLQuery(sql);
        query.setParameter("doHeaderId", doHeaderId);
        query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 更新订单订货数量及是否贵重品
     */
    public void updateHeaderTotalQtyAndValuable(Long doId, BigDecimal totalQty, boolean isValuableFlag) {
        String sql = "update doc_alc_header set expected_qty = :totalQty, is_valuable = :valuable where id = :doId and warehouse_id = :whId";
        Query query = createSQLQuery(sql);
        query.setParameter("totalQty", totalQty);
        query.setParameter("valuable", isValuableFlag ? YesNo.YES.getValue() : YesNo.NO.getValue());
        query.setParameter("doId", doId);
        query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    public BigDecimal sumExpectedQty(List<Long> doIds) {
        Query query = this.createQuery("SELECT SUM(o.expectedQty) FROM DoAllocateHeader o WHERE o.id IN (:ids)");
        query.setParameterList("ids", doIds);
        Object obj = query.uniqueResult();
        return obj == null ? BigDecimal.ZERO : (BigDecimal) obj;
    }

    /**
     * 设置订单需要取消分配，且将订单自动波次失败次数置为最大
     *
     * @param ids
     */
    public void setAlcNeedCancelFlag(List<Long> ids) {
        String hql = "update DoAllocateHeader o set o.needCancelAlloc = :needCancel, o.waveFailNum = :maxFailNum where o.id in (:ids)";
        Query query = this.createUpdateQuery(hql);
        query.setParameterList("ids", ids);
        query.setParameter("needCancel", YesNo.YES.getValue());
        Integer maFailNum = SystemConfig.getConfigValueInt("delivery.autoWave.failNum",
                ParamUtil.getCurrentWarehouseId());
        query.setParameter("maxFailNum", maFailNum + 1);// 使得不再被自动波次，避免再次失败
        query.executeUpdate();
    }

    /**
     * 查询需要自动取消分配的订单（按预计出库时间、订单转do时间排序）
     *
     * @param batchSize
     * @param failMaxNum
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Long> queryNeedCancelAllocIds(int batchSize, Integer failMaxNum) {
        StringBuilder hql = new StringBuilder();
        hql.append("select o.id from DoAllocateHeader o ");
        hql.append("	where o.status = :status and o.releaseStatus = :rs ");
        hql.append("	and o.needCancelAlloc = :needCancel ");
        hql.append("	and o.doType = :doType");
        hql.append("	and o.waveFlag = :isWaved");
        // 判断是否配置最大失败数，并且大于0
        if (failMaxNum != null && failMaxNum.intValue() > 0) {
            hql.append(" and o.cancelAllocFailNum < :failMaxNum");
        }
        hql.append(" order by o.planShipTime, o.doCreateTime ");
        Query query = createQuery(hql.toString());
        query.setString("status", DoStatus.ALLALLOCATED.getValue());
        query.setString("rs", ReleaseStatus.RELEASE.getValue());
        query.setInteger("needCancel", YesNo.YES.getValue());
        query.setParameter("doType", DoType.SELL.getValue());
        query.setParameter("isWaved", YesNo.NO.getValue());
        // 判断是否配置最大失败数，并且大于0
        if (failMaxNum != null && failMaxNum.intValue() > 0) {
            query.setParameter("failMaxNum", Long.valueOf(failMaxNum));
        }
        query.setMaxResults(batchSize);
        return query.list();
    }

    @SuppressWarnings("unchecked")
    public List<Long> findNotLacks() {
        String hql = " select o.id from DoAllocateHeader o where o.noStockFlag = :notLack and o.releaseStatus = :holdStatus "
                + " and o.needCancel = :needNotCancel and  o.deliveryOrderHeader.holdCode in (:allocLack) and o.deliveryOrderHeader.exceptionStatus in (:canReleaseStatus) and o.warehouseId = :warehouseId ";
        Query query = this.createQuery(hql);
        List<String> allocLack = new ArrayList<String>();
        allocLack.add(Reason.ALLOC_LACK.getValue());
        allocLack.add(Reason.WAIT_REPL.getValue());
        query.setParameterList("allocLack", allocLack);

        query.setInteger("notLack", YesNo.NO.getValue());
        query.setInteger("needNotCancel", YesNo.NO.getValue());
        query.setParameter("holdStatus", ReleaseStatus.HOLD.getValue());
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        List<String> canReleaseStatus = new ArrayList<String>();
        canReleaseStatus.add(DoExpStatus.TO_BE_REPL.getValue());
        canReleaseStatus.add(DoExpStatus.TO_BE_ROLLBACK.getValue());
        canReleaseStatus.add(DoExpStatus.TO_BE_ANNOUNCE.getValue());
        query.setParameterList("canReleaseStatus", canReleaseStatus);
        return (List<Long>) query.list();
    }

    public List<Long> findIdForWave(BigWaveFilter bigWaveFilter) {
        Long whId = ParamUtil.getCurrentWarehouseId();
        StringBuilder hql = new StringBuilder("SELECT o.id");
        hql.append(" from DoAllocateHeader o ");
        hql.append(" left join o.provinceInfo pi");
        hql.append(" left join o.cityInfo cii");
        hql.append(" left join o.countyInfo cti");
        hql.append(" left join o.carrier c");
        hql.append(" left join o.station st");
        hql.append(" left join o.doWaveEx doWaveEx");

        hql.append(" where o.warehouseId  = " + whId);

        return (List<Long>) this.executeQueryByFilter(hql.toString(), bigWaveFilter);
    }

    public void updateCarrier(List<Long> idList, Long carrierId) {
        String hql = "update DoAllocateHeader o set o.carrierId = :carrierId " +
                "where o.id in (:ids) and o.warehouseId = :warehouseId";
        Query query = createUpdateQuery(hql);
        query.setLong("carrierId", carrierId);
        query.setParameterList("ids", idList);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    public List<OverAllocateDto> queryOverAllocate() {
        String sql = "SELECT allocating.qty - o.qty_available AS overQty, o.id AS stkId, sku.product_cname AS productName, loc.loc_code AS locCode "
                +
                "FROM stk_batch_loc_lpn o LEFT JOIN (" +
                "   SELECT sum(allocating.qty_allocating) qty, stk_lpn_id FROM " +
                "   stk_allocating allocating WHERE allocating.is_deleted = 0 AND allocating.warehouse_id = :warehouseId"
                +
                "   GROUP BY allocating.stk_lpn_id" +
                ") allocating ON o.id = allocating.stk_lpn_id " +
                "INNER JOIN md_location loc ON o.loc_id = loc.id " +
                "INNER JOIN md_sku sku ON sku.id = o.sku_id " +
                "WHERE o.warehouse_id = :warehouseId AND o.is_deleted = 0 AND allocating.stk_lpn_id IS NOT NULL AND o.qty_available < allocating.qty";
        Map<String, Object> params = Maps.newHashMap();
        params.put("warehouseId", ParamUtil.getCurrentWarehouseId());
        SQLQuery sqlQuery = this.createSQLQuery(sql, params);
        sqlQuery.addScalar("overQty", Hibernate.BIG_DECIMAL).addScalar("stkId", Hibernate.LONG)
                .addScalar("productName", Hibernate.STRING)
                .addScalar("locCode", Hibernate.STRING)
                .setResultTransformer(Transformers.aliasToBean(OverAllocateDto.class));
        return sqlQuery.list();
    }

    public List<Pair<Long, BigDecimal>> getStkAllocateQty(Long stkId) {
        String sql = "SELECT o.doc_id AS doId, o.qty_allocating AS overQty FROM stk_allocating o WHERE o.stk_lpn_id = :stkId AND o.is_deleted = 0 ORDER by o.create_time DESC";
        Map<String, Object> params = Maps.newHashMap();
        params.put("stkId", stkId);
        SQLQuery sqlQuery = this.createSQLQuery(sql, params).addScalar("doId", Hibernate.LONG).addScalar("overQty",
                Hibernate.BIG_DECIMAL);
        List<Object[]> results = sqlQuery.setMaxResults(10).list();
        List<Pair<Long, BigDecimal>> overQtys = ListUtil.convert(results,
                new ListUtil.Convertor<Object[], Pair<Long, BigDecimal>>() {
                    @Override
                    public Pair<Long, BigDecimal> convert(Object[] objects) {
                        return new MutablePair<Long, BigDecimal>((Long) objects[0], (BigDecimal) objects[1]);
                    }
                });
        return overQtys;
    }

    public List<Long> findReAllocateId() {
        String sql = " select distinct do_id id from temp_alloc order by do_id desc limit 10 ";
        SQLQuery sqlQuery = this.createSQLQuery(sql);
        sqlQuery.addScalar("id", Hibernate.LONG);
        List<Long> doIdList = sqlQuery.list();
        return doIdList;
    }

    public void removeTempAlloc(List<Long> ids) {
        SQLQuery sqlQuery = createSQLQuery("delete from temp_alloc where do_id in (:ids)");
        sqlQuery.setParameterList("ids", ids);
        sqlQuery.executeUpdate();
    }

    public List<OverAllocateDto> queryMoreAllocate() {
        String sql = "SELECT o.id doId, o.do_no AS doNo, o.expected_qty AS expectedQty, allocating.qty AS qty " +
                "FROM doc_alc_header o INNER JOIN (" +
                "   SELECT sum(allocating.qty_allocating) qty, allocating.doc_id FROM " +
                "   stk_allocating allocating WHERE allocating.is_deleted = 0 AND allocating.warehouse_id = :warehouseId"
                +
                "   GROUP BY allocating.doc_id " +
                ") allocating ON o.id = allocating.doc_id " +
                "WHERE o.warehouse_id = :warehouseId AND o.is_deleted = 0 AND o.expected_qty < allocating.qty AND o.status='40' ";

        Map<String, Object> params = Maps.newHashMap();
        params.put("warehouseId", ParamUtil.getCurrentWarehouseId());
        SQLQuery sqlQuery = this.createSQLQuery(sql, params);
        sqlQuery.addScalar("expectedQty", Hibernate.BIG_DECIMAL).addScalar("doId", Hibernate.LONG)
                .addScalar("doNo", Hibernate.STRING)
                .addScalar("qty", Hibernate.BIG_DECIMAL)
                .setResultTransformer(Transformers.aliasToBean(OverAllocateDto.class));
        return sqlQuery.list();
    }

    public List<AutoWaveDTO> getAutoWaveInfo(Integer lowerLimit) {
        String sql = " select * from (select temp1.itemInfo,temp1.carrierId,count(1) as totalDo from ( select temp.do_header_id,GROUP_CONCAT( CONCAT( temp.sku_id, '* ', ROUND( temp.expected_qty ) ) ORDER BY temp.sku_id ASC ) AS itemInfo,"
                +
                " temp.carrier_id as carrierId from ( " +
                " select d.do_header_id,sum(d.expected_qty) as expected_qty, d.sku_id,h.carrier_id from doc_alc_detail d "
                +
                " inner join doc_do_header h on d.do_header_id = h.id and h.transport_wendy = 3 " +
                " inner join doc_do_wave_ex ex on ex.do_h_id = h.id and ex.special_label_code is null " +
                " where h.warehouse_id = :warehouseId and h.status = :status and h.do_type = :doType and h.release_status =:releaseStatus and "
                +
                " h.wave_flag = :waveFlag and h.is_deleted =0 and d.is_deleted = 0 and h.carrier_id is not null group by d.do_header_id,d.sku_id,h.carrier_id) temp group by temp.do_header_id) "
                +
                " temp1 group by temp1.itemInfo,temp1.carrierId) temp2 where temp2.totalDo >= :lowerLimit ";
        Map<String, Object> params = Maps.newHashMap();
        params.put("warehouseId", ParamUtil.getCurrentWarehouseId());
        params.put("status", Constants.DoStatus.ALLALLOCATED.getValue());
        params.put("doType", Constants.DoType.SELL.getValue());
        params.put("releaseStatus", Constants.ReleaseStatus.RELEASE.getValue());
        params.put("waveFlag", Long.valueOf(Constants.YesNo.NO.getValue().intValue()));
        params.put("lowerLimit", lowerLimit);
        SQLQuery sqlQuery = this.createSQLQuery(sql, params);
        sqlQuery.addScalar("itemInfo", Hibernate.STRING).addScalar("carrierId", Hibernate.LONG)
                .addScalar("totalDo", Hibernate.LONG)
                .setResultTransformer(Transformers.aliasToBean(AutoWaveDTO.class));
        return sqlQuery.list();
    }

    public List<Long> getAutoWaveDoIds(AutoWaveDTO dto) {
        String sql = " select distinct temp1.do_header_id as id from " +
                "(select temp.do_header_id,GROUP_CONCAT( CONCAT( temp.sku_id, '* ', ROUND( temp.expected_qty ) ) ORDER BY temp.sku_id ASC ) AS itemInfo from ( "
                +
                " select d.do_header_id,sum(d.expected_qty) as expected_qty, d.sku_id from doc_alc_detail d " +
                " inner join doc_do_header h on d.do_header_id = h.id and h.transport_wendy = 3 " +
                " where h.warehouse_id = :warehouseId and h.status = :status and h.do_type = :doType and h.release_status =:releaseStatus and "
                +
                " h.wave_flag = :waveFlag and h.is_deleted =0 and d.is_deleted = 0 and h.carrier_id = :carrierId group by d.do_header_id,d.sku_id) temp group by temp.do_header_id) "
                +
                " temp1 where temp1.itemInfo = :itemInfo ";
        Map<String, Object> params = Maps.newHashMap();
        params.put("warehouseId", ParamUtil.getCurrentWarehouseId());
        params.put("status", Constants.DoStatus.ALLALLOCATED.getValue());
        params.put("doType", Constants.DoType.SELL.getValue());
        params.put("releaseStatus", Constants.ReleaseStatus.RELEASE.getValue());
        params.put("waveFlag", Long.valueOf(Constants.YesNo.NO.getValue().intValue()));
        params.put("carrierId", dto.getCarrierId());
        params.put("itemInfo", dto.getItemInfo());
        SQLQuery sqlQuery = this.createSQLQuery(sql, params);
        sqlQuery.addScalar("id", Hibernate.LONG);
        return sqlQuery.list();
    }

    public List<AutoWaveDTO> getAutoWaveInfoByFilter(BigWaveFilter filter, Integer lowerLimit) {
        filter.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        String sql = "select count(*) as totalDo,o.deliveryOrderHeader.carrierId as carrierId,"
                + "o.deliveryOrderHeader.similaritySign as itemInfo from "
                + "DoAllocateHeader o where o.deliveryOrderHeader.waveFlag = ? "
                + "and o.deliveryOrderHeader.status = ?  and o.deliveryOrderHeader.doType = ? "
                + "and o.deliveryOrderHeader.releaseStatus = ? and  o.deliveryOrderHeader.similaritySign is not null";
        // 根据filter 的信息组合查询条件
        List<Object> paramList = new ArrayList<Object>();
        // String condition = this.buildConditionHql(filter, paramList);
        // 创建链，并获得根结点
        Handler root = ChainClient.invoke(filter, this.getEntityClass());
        String condition = root.handleHql();
        root.handleParam(paramList);
        // 组合hql语句
        if (condition != null && !condition.trim().isEmpty()) {
            sql += condition;
        }

        sql += " group by o.deliveryOrderHeader.similaritySign,o.deliveryOrderHeader.carrierId,o.checkFlag having count(*)>= ? ";
        Query query = this.createQuery(sql);
        query.setParameter(0, Constants.YesNo.NO.getValue().intValue());
        query.setParameter(1, Constants.DoStatus.ALLALLOCATED.getValue());
        query.setParameter(2, Constants.DoType.SELL.getValue());
        query.setParameter(3, Constants.ReleaseStatus.RELEASE.getValue());
        int startPos = 4;
        for (int i = 0; i < paramList.size(); i++) {
            query.setParameter(startPos++, paramList.get(i));
        }
        // 设置 having参数
        query.setParameter(startPos, lowerLimit.longValue());
        query.setResultTransformer(Transformers.aliasToBean(AutoWaveDTO.class));
        return query.list();
    }

    public List<Long> getAutoWaveDoIdsByFilter(BigWaveFilter filter, AutoWaveDTO autoWaveDTO) {
        filter.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        String sql = "select o.deliveryOrderHeader.id as id from "
                + "DoAllocateHeader o where o.deliveryOrderHeader.waveFlag = ? "
                + "and o.deliveryOrderHeader.status = ? " + "and o.deliveryOrderHeader.doType = ? "
                + "and o.deliveryOrderHeader.releaseStatus = ? "
                + "and o.deliveryOrderHeader.similaritySign = ? and  o.deliveryOrderHeader.similaritySign is not null "
                + "and o.carrierId = ? ";
        // 根据filter 的信息组合查询条件
        List<Object> paramList = new ArrayList<Object>();
        // String condition = this.buildConditionHql(filter, paramList);
        // 创建链，并获得根结点
        Handler root = ChainClient.invoke(filter, this.getEntityClass());
        String condition = root.handleHql();
        root.handleParam(paramList);
        // 组合hql语句
        if (condition != null && !condition.trim().isEmpty()) {
            sql += condition;
        }

        Query query = this.createQuery(sql);
        query.setParameter(0, Constants.YesNo.NO.getValue().intValue());
        query.setParameter(1, Constants.DoStatus.ALLALLOCATED.getValue());
        query.setParameter(2, Constants.DoType.SELL.getValue());
        query.setParameter(3, Constants.ReleaseStatus.RELEASE.getValue());
        query.setParameter(4, autoWaveDTO.getItemInfo());
        query.setParameter(5, autoWaveDTO.getCarrierId());
        int startPos = 6;
        for (int i = 0; i < paramList.size(); i++) {
            query.setParameter(startPos + i, paramList.get(i));
        }
        String selectMax = AppConfig.getProperty("batch.genWave.groupSize.max", "9000");
        return query.setMaxResults(Integer.valueOf(selectMax)).list();
    }

    public List<Long> getAutoWaveDoIdsByFilter(BigWaveFilter filter) {
        filter.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        String sql = "select o.deliveryOrderHeader.id as id from "
                + "DoAllocateHeader o where o.deliveryOrderHeader.waveFlag = ? "
                + "and o.deliveryOrderHeader.status = ? "
                + "and o.deliveryOrderHeader.releaseStatus = ? ";
        // 根据filter 的信息组合查询条件
        List<Object> paramList = new ArrayList<Object>();
        // 创建链，并获得根结点
        Handler root = ChainClient.invoke(filter, this.getEntityClass());
        String condition = root.handleHql();
        root.handleParam(paramList);
        // 组合hql语句
        if (condition != null && !condition.trim().isEmpty()) {
            sql += condition;
        }

        Query query = this.createQuery(sql);
        query.setParameter(0, Constants.YesNo.NO.getValue().intValue());
        query.setParameter(1, Constants.DoStatus.ALLALLOCATED.getValue());
        query.setParameter(2, Constants.ReleaseStatus.RELEASE.getValue());
        int startPos = 3;
        for (int i = 0; i < paramList.size(); i++) {
            query.setParameter(startPos + i, paramList.get(i));
        }
        Integer c = filter.getScanOrderCount();
        // 未指定条数 限制1000条
        if (c == null) {
            c = 1000;
        }
        return query.setMaxResults(c).list();
    }
}