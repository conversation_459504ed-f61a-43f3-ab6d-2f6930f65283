package com.daxia.wms.delivery.load.service;

import java.io.IOException;
import java.util.List;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.load.entity.OverdueRtv;
import com.daxia.wms.delivery.load.filter.OverdueRtvFilter;

/**
 * <pre>
 * Description:逾期退单管理Service接口
 * </pre>
 * 
 */
public interface OverdueRtvService {

	public DataPage<OverdueRtv> findOverdueRtvs(OverdueRtvFilter filter,
			int startIndex, int pageSize);
	
	public void saveOverdueRtv(OverdueRtv overdueRtv);
	
	public void createOverdueRtvs(Long doHeaderId);
	
	public void batchAuditOverdueRtvs(List<Long> overdueRtvIds,String notes);
	
	public byte[] exportExcel(List<Long> ids) throws IOException;
}
