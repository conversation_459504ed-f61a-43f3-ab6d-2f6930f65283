package com.daxia.wms.delivery.task.replenish.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoDetailDAO;
import com.daxia.wms.delivery.task.replenish.dto.LocationReplInfoDTO;
import com.daxia.wms.delivery.task.replenish.dto.ReplGenterateDTO;
import com.daxia.wms.delivery.task.replenish.dto.ReplenishSkuDTO;
import com.daxia.wms.master.dto.SkuDTO;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.entity.PackageInfoDetail;
import com.daxia.wms.master.entity.RotationHead;
import com.daxia.wms.master.service.PackageInfoDetailService;
import com.daxia.wms.master.service.PartitionRuleService;
import com.daxia.wms.master.service.RotationService;
import com.daxia.wms.master.service.SkuCache;
import com.daxia.wms.stock.stock.dto.Stock2AllocateDTO;
import com.daxia.wms.stock.stock.service.StockService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 生成补货任务
 */
@Name("com.daxia.wms.delivery.xpReplenishTaskGenerator")
@lombok.extern.slf4j.Slf4j
public class XPReplenishTaskGenerator extends AbstractReplenishTaskGenerator {
    public static final int SCALE = 5;
    
    @In
    private StockService stockService;
    @In
    private SkuCache skuCache;
    @In
    private PackageInfoDetailService packageInfoDetailService;
    @In
    private PartitionRuleService partitionRuleService;
    @In
    private RotationService rotationService;
    @In
    private DoDetailDAO doDetailDAO;
    
    /**
     * 生成闲时补货任务。返回 系统已经生成的task总数量
     *
     * @param locationReplInfos
     * @return
     */
    @Transactional
    public int createReplenishTask(List<LocationReplInfoDTO> locationReplInfos, Boolean isAuto, int taskMadeCount) throws Exception {
        int taskCount = taskMadeCount;
        //对需要补货的记录循环生成补货任务。
        for (LocationReplInfoDTO needReplLoc : locationReplInfos) {
            Boolean isPackage = false;
            if (needReplLoc.getPackageType().equals(Constants.PackageType.C.getValue())) {
                isPackage = true;
            }
            SkuDTO sku = skuCache.getSku(needReplLoc.getSkuId());
            //取得商品补货规则
            RotationHead head = rotationService.get(sku.getReplenishmentRuleId());
            if (head == null || !"1".equals(head.getActiveFlag())) {
                throw new DeliveryException("error.sku.noReplRule", sku.getProductCode());
            }
            //得到可以补货的库存
            List<Stock2AllocateDTO> stockList = stockService.findAllRsStock(needReplLoc.getSkuId(), head, isPackage);
            //如果当前sku存储位没有库存，跳到下一条记录
            if (CollectionUtils.isEmpty(stockList)) {
                continue;
            }
            BigDecimal replQty = needReplLoc.getUplimit().subtract(needReplLoc.getActQty());

            int tempCount = this.createReplTask(new ReplGenterateDTO(stockList, replQty, sku, needReplLoc.getLocId(), Constants.ReplType.XP.getValue(), null, isAuto, taskCount, isPackage, null));
            taskCount = taskCount + tempCount;
            doDetailDAO.getSession().flush();
            doDetailDAO.getSession().clear();
        }
        return taskCount;
    }
}