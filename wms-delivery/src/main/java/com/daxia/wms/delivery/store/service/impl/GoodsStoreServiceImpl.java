package com.daxia.wms.delivery.store.service.impl;


import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.Constants;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.delivery.deliveryorder.dao.DoHeaderDAO;
import com.daxia.wms.delivery.pick.dao.PickDAO;
import com.daxia.wms.delivery.pick.dao.PickTaskDAO;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.store.dto.StoreInTaskDTO;
import com.daxia.wms.delivery.store.dto.StoreOutTaskDTO;
import com.daxia.wms.delivery.store.service.GoodsStoreService;
import com.daxia.wms.master.dao.ContainerDAO;
import com.daxia.wms.master.dao.MergeLocDAO;
import com.daxia.wms.master.dto.MergeBoardDTO;
import com.daxia.wms.master.entity.Container;
import com.daxia.wms.master.entity.MergeLoc;
import com.daxia.wms.master.filter.MergeBoardFilter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Name("com.daxia.wms.delivery.goodsStoreService")
@lombok.extern.slf4j.Slf4j
public class GoodsStoreServiceImpl implements GoodsStoreService {

    @In
    private DoHeaderDAO doHeaderDAO;

    @In
    private PickDAO pickDAO;
    @In
    private PickTaskDAO pickTaskDAO;

    @In
    private MergeLocDAO mergeLocDAO;
    @In
    private ContainerDAO containerDAO;
    @In
    private ContainerMgntService containerMgntService;


    @Override
    public List<StoreInTaskDTO> findStoreInTask(String boxNo) {
        return doHeaderDAO.findStoreInTask(boxNo);
    }

    @Override
    @Transactional
    public Boolean flushMergeLoc(MergeLoc mergeLoc) {
        pickDAO.getSession().flush();
        Boolean pickFinished = pickDAO.isPickFinished(mergeLoc);//1、订单关联的所有整散拣货任务都已拣货完成
        if (pickFinished) {
            Boolean mergeInFinished = pickDAO.isMergeInFinished(mergeLoc);//2、订单关联的容器都已经集货入区
            if (mergeInFinished) {
                mergeLoc.setStatus(MergeLoc.MergeStatus.DONE.getValue());
                mergeLocDAO.update(mergeLoc);
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public List<StoreOutTaskDTO> findStoreOutTask(String mergePartition) {
        return pickDAO.findStoreOutTask(mergePartition);
    }

    @Override
    public DataPage<MergeBoardDTO> queryBoard(MergeBoardFilter mergeBoardFilter, int startIndex, int pageSize) {
        if (Integer.valueOf(3).equals(mergeBoardFilter.getDocType())) {
            Map<String, Object> paramMap = new HashMap<String, Object>();
            String sql = doHeaderDAO.builderHqlByAll(mergeBoardFilter, paramMap);
            StringBuilder countSql = new StringBuilder("select count(*) from (");
            countSql.append(sql).append(") a");
            DataPage<MergeBoardDTO> tlist = doHeaderDAO.queryBoard(sql, countSql.toString(), paramMap,
                    startIndex, pageSize);
            List<MergeBoardDTO> stls = tlist.getDataList();
            int totalcount = Integer.parseInt(String.valueOf(tlist.getTotalCount()));
            return new DataPage<MergeBoardDTO>(totalcount, tlist.getStartIndex(), tlist.getPageSize(), stls);
        } else {
            return new DataPage<MergeBoardDTO>();
        }
    }

    @Override
    @Transactional
    public void flushContainer4PickLack(String pktNo, String pktType, Long pktId) {
        containerDAO.getSession().flush();
        containerDAO.getSession().clear();
        PickHeader pickHeader = pickDAO.get(pktId);
        Map<String, Object> params = new HashedMap();
        params.put("pktHeaderId", pktId);
        List<PickTask> pickTasks = pickTaskDAO.find(params);
        Set<String> canNotReleaseDoNos = new HashSet<String>();//当前拣货单，不能释放的订单（容器）
        if (pickHeader != null) {
            for (PickTask task : pickTasks) {
                if (task.getPickedQty().compareTo(BigDecimal.ZERO) > 0) {
                    canNotReleaseDoNos.add(task.getDoHeader().getDoNo());
                    continue;
                }
                if (Constants.DoStatus.ALLPICKED.getValue().compareTo(task.getStatus()) > 0) {
                    canNotReleaseDoNos.add(task.getDoHeader().getDoNo());
                    continue;
                }
            }
        }
        List<Container> containers = containerDAO.findByPktNo(pktNo);
        //边拣边分
        if (Config.isDefaultFalse(Keys.Delivery.sort_isWithPick, Config.ConfigLevel.WAREHOUSE)) {
            for (Container container : containers) {
                String docNo = container.getDocNo();
                String docType = container.getDocType();
                if (!canNotReleaseDoNos.contains(container.getDocNo())) {//先释放容器，在更新集货位信息
                    containerMgntService.release(container);
                }
                String mergeLocCode = mergeLocDAO.findMergeLoc(docNo, docType, pktType);
                MergeLoc mergeLoc = mergeLocDAO.findMergeLocByCode(mergeLocCode);
                if (mergeLoc != null) {
                    flushMergeLoc(mergeLoc);
                }
            }
        } else {
            //非边拣边分，如果是整单缺拣，才释放容器
            if (CollectionUtils.isEmpty(canNotReleaseDoNos)) {
                for (Container container : containers) {
                    containerMgntService.release(container);
                }
            }
        }
    }
}
