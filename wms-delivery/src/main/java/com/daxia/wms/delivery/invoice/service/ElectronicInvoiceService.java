package com.daxia.wms.delivery.invoice.service;

import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.invoice.entity.EinvResponseData;

import java.util.List;

/**
 * 电子发票
 */
public interface ElectronicInvoiceService {
    /**
     * 冲红发票
     *
     * @param invoiceHeader
     */
    public void writeBack(InvoiceHeader invoiceHeader);

    /**
     * 开票
     *
     * @param invoiceHeader
     */
    public void billing(InvoiceHeader invoiceHeader);

    /**
     * 查询并绑定发票信息
     *
     * @param invoiceHeader
     */
    public void bind(InvoiceHeader invoiceHeader);

    /**
     * 加载需要绑定的发票信息
     * @param default_batch_num
     * @return
     */
    List<InvoiceHeader> loadInvoiceToBind(int default_batch_num, int dafalutFailedNum);
    
    /**
     * 加载需要重新上传的发票信息
     * @param default_batch_num
     * @return
     */
    List<InvoiceHeader> loadInvoiceToBilling(int default_batch_num, int dafalutFailedNum);

    void writeBackInvoice(DeliveryOrderHeader doHeader);
    
    public EinvResponseData uploadInvoice(InvoiceHeader invoiceHeader,String orgSequenceNo);
    
    public EinvResponseData chInvoice(Long invoiceId,String reason,String chedSequenceNo);
    
    public EinvResponseData downloadInvoice(Long invoiceId, String type,String cxParam);
    
    public List<InvoiceHeader> loadInvoiceToCancel(int batchNum, int dafalutFailedNum);
    
    public void test();
    
    public void writeBackInvoiceByDoNo(String doNo);
}
