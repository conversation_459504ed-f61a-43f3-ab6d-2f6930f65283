package com.daxia.wms.delivery.invoice.job;

import com.alibaba.druid.pool.DruidDataSource;
import com.daxia.framework.common.util.SystemConfig;

import java.sql.*;
import java.util.Collection;

@lombok.extern.slf4j.Slf4j
public class JRDBConnectionManager {
    //SQLServer
    private String driverName = "com.microsoft.sqlserver.jdbc.SQLServerDriver";//加载驱动程序
//    private String url = "*******************************************************";//设置数据库连接串
//    private String user = "JS_user";//数据库登录用户名
//    private String password = "ZSWQtfd$#@gtk";//数据库登录密码
    public void setDriverName(String newDriverName) {
        driverName = newDriverName;
    }
    public String getDriverName() {
        return driverName;
    }
    
    
    private static DruidDataSource druidDataSource;
    
    public Connection getConnection() {
        if (druidDataSource == null) {
            synchronized (JRDBConnectionManager.class) {
                if (druidDataSource == null) {
                    String dbParams = SystemConfig.getConfigValue("invoiceJinren.dbParams", null);
                    String[] params = dbParams.split(",");
                    druidDataSource = new DruidDataSource();
                    druidDataSource.setUrl(params[0]);
                    druidDataSource.setUsername(params[1]);
                    druidDataSource.setPassword(params[2]);
        
                    druidDataSource.setMaxActive(5);
                    druidDataSource.setInitialSize(1);
                    druidDataSource.setMaxWait(60000); //获取连接时最大等待时间
                    druidDataSource.setMinIdle(1); //最小连接池数量
    
                    //1) Destroy线程会检测连接的间隔时间，如果连接空闲时间大于等于minEvictableIdleTimeMillis则关闭物理连接。
                    //2) testWhileIdle的判断依据，详细看testWhileIdle属性的说明
                    druidDataSource.setTimeBetweenEvictionRunsMillis(90000);
                    druidDataSource.setMinEvictableIdleTimeMillis(60000); //连接保持空闲而不被驱逐的最长时间
        
                    druidDataSource.setTestWhileIdle(true); //申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
                    druidDataSource.setTestOnBorrow(false); //申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
                    druidDataSource.setTestOnReturn(false); //归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
    
                    druidDataSource.setPoolPreparedStatements(false); //是否缓存preparedStatement，也就是PSCache,PSCache对支持游标的数据库性能提升巨大，比如说oracle。在mysql下建议关闭
                    //druidDataSource.setMaxOpenPreparedStatements(5); //要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。在Druid中，不会存在Oracle下PSCache占用内存过多的问题，可以把这个数值配置大一些，比如说100
                }
            }
        }
        try {
            return druidDataSource.getConnection();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }
    
//    public Connection getConnection() {
//        try {
//        	// 查找金任数据库连接配置。
//			String dbParams = SystemConfig.getConfigValue(
//					"invoiceJinren.dbParams", null);
//			String[] params = dbParams.split(",");
//            Class.forName(driverName);
//            return DriverManager.getConnection(params[0], params[1], params[2]);
//        } catch (Exception e) {
//            e.printStackTrace();
//            return null;
//        }
//    }
    public static void main(String[] args) {
        try{
        	PreparedStatement ps = null;
        	ResultSet rs = null;
            JRDBConnectionManager dcm = new JRDBConnectionManager();
            Connection  conn = dcm.getConnection();
            String sql = "select * from T_FPInfo ";
                         ps = conn.prepareStatement(sql);
                         rs = ps.executeQuery();// 执行查询
                         while (rs.next()) {// 判断是否还有下一个数据
                             System.out.println("FP_id:" + rs.getString("FP_id") + "\tNAME:"
                                    + rs.getString("FP_num")+ "\t FP_fpnum:"
                                            + rs.getString("FP_fpnun")+ "\t FP_time:"
                                                    + rs.getString("FP_time"));
                         }
//            List<Invoice4JinrenDTO> ak = new ArrayList<Invoice4JinrenDTO>();
//            Invoice4JinrenDTO jinrenDTO = new Invoice4JinrenDTO();
//            jinrenDTO.setDocNo("1234e");
//			jinrenDTO.setTitle("title");
//			jinrenDTO.setProductName("肾宝");
//			jinrenDTO.setSkuType("1");
//			jinrenDTO.setUnit("1");
//			jinrenDTO.setQty("2");
//			jinrenDTO.setTaxRate("0.45");
//			jinrenDTO.setCreateTime(DateUtil.getNowTime()
//					.toString());
//			jinrenDTO.setPrice("100");
//			jinrenDTO.setAmount("12");
//			jinrenDTO.setRemarks("bbs"); // TODO
//															// 需要确认
//															// 备用字段需要确认。
//			ak.add(jinrenDTO);
//            String sql = "INSERT T_FPInfo (fp_num,fp_fpNun,fp_title,fp_comName,fp_guige,fp_unit,fp_count,fp_shuilv,fp_time,"
//            		+ "fp_hsPrice,fp_hsmoney,fp_remarks,fp_diy1,fp_diy2,fp_diy3,fp_id) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,NEWID())";     
//            PreparedStatement prest = conn.prepareStatement(sql,ResultSet.TYPE_SCROLL_SENSITIVE,ResultSet.CONCUR_READ_ONLY);     
//            for(Invoice4JinrenDTO invoice4JinrenDTO : ak){  
//               prest.setString(1, StringUtil.isEmpty(invoice4JinrenDTO.getDocNo()) ? "" : invoice4JinrenDTO.getDocNo());     
//               prest.setString(2, StringUtil.isEmpty(invoice4JinrenDTO.getInvoiceNo()) ? "" : invoice4JinrenDTO.getInvoiceNo());     
//               prest.setString(3, StringUtil.isEmpty(invoice4JinrenDTO.getTitle() ) ? "" : invoice4JinrenDTO.getTitle());     
//               prest.setString(4, StringUtil.isEmpty(invoice4JinrenDTO.getProductName()) ? "" :  invoice4JinrenDTO.getProductName());     
//               prest.setString(5, StringUtil.isEmpty(invoice4JinrenDTO.getUnit()) ? "" :  invoice4JinrenDTO.getUnit());    
//               prest.setString(6, StringUtil.isEmpty(invoice4JinrenDTO.getUnit()) ? "" :  invoice4JinrenDTO.getUnit());     
//               prest.setString(7, StringUtil.isEmpty(invoice4JinrenDTO.getQty()) ? "" :  invoice4JinrenDTO.getQty());     
//               prest.setString(8, StringUtil.isEmpty(invoice4JinrenDTO.getTaxRate() ) ? "" : invoice4JinrenDTO.getTaxRate());     
//               prest.setString(9, StringUtil.isEmpty(invoice4JinrenDTO.getCreateTime()) ? "" :  invoice4JinrenDTO.getCreateTime());     
//               prest.setString(10, StringUtil.isEmpty(invoice4JinrenDTO.getPrice()) ? "" :  invoice4JinrenDTO.getPrice());     
//               prest.setString(11, StringUtil.isEmpty(invoice4JinrenDTO.getAmount()) ? "" :  invoice4JinrenDTO.getAmount());     
//               prest.setString(12, StringUtil.isEmpty(invoice4JinrenDTO.getRemarks()) ? "" : invoice4JinrenDTO.getRemarks());     
//               prest.setString(13, StringUtil.isEmpty(invoice4JinrenDTO.getDiy1()) ? "" :  invoice4JinrenDTO.getDiy1());     
//               prest.setString(14, StringUtil.isEmpty(invoice4JinrenDTO.getDiy2()) ? "" :  invoice4JinrenDTO.getDiy2());     
//               prest.setString(15, StringUtil.isEmpty(invoice4JinrenDTO.getDiy3()) ? "" :  invoice4JinrenDTO.getDiy3());  
//               prest.addBatch();     
//            }     
//            prest.executeBatch();     
//            conn.commit();     
//            conn.close();     
        }catch (SQLException ex) {  
        	//rollback
        	
        } catch(Exception e){
            e.printStackTrace();
        }
    }
}
