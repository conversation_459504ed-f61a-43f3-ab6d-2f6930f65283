package com.daxia.wms.delivery.deliveryorder.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

@lombok.extern.slf4j.Slf4j
public class OtherDoFilter extends WhBaseQueryFilter {

    private static final long serialVersionUID = -8161171226949691369L;
    /**
     * DO编号
     */
    private String doNo;
    /**
     * DO类型
     */
    private String doType;

    /**
     * DO状态From
     */
    private String doStatusFrom;
    /**
     * DO状态To
     */
    private String doStatusTo;

    /**
     * 参考编号1
     */
    private String refNo1;
    /**
     * 参考编号2
     */
    private String refNo2;
    /**
     * 订单关闭时间From
     */
    private Date closeTimeFm;
    /**
     * 订单关闭时间To
     */
    private Date closeTimeTo;

    /**
     * 备注
     */
    private String notes;
    /**
     * 创建时间From
     */
    private Date createdAtFrom;
    /**
     * 创建时间To
     */
    private Date createdAtTo;
    /**
     * 商品
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuCname;

    /**
     * 商品条码
     */
    private String barCode;

    @Operation(fieldName = "o.doNo", operationType = OperationType.EQUAL)
    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    @Operation(fieldName = "o.doType", operationType = OperationType.EQUAL)
    public String getDoType() {
        return doType;
    }

    public void setDoType(String doType) {
        this.doType = doType;
    }

    @Operation(fieldName = "o.doStatus", operationType = OperationType.NOT_LESS_THAN)
    public String getDoStatusFrom() {
        return doStatusFrom;
    }

    public void setDoStatusFrom(String doStatusFrom) {
        this.doStatusFrom = doStatusFrom;
    }

    @Operation(fieldName = "o.doStatus", operationType = OperationType.NOT_GREAT_THAN)
    public String getDoStatusTo() {
        return doStatusTo;
    }

    public void setDoStatusTo(String doStatusTo) {
        this.doStatusTo = doStatusTo;
    }

    @Operation(fieldName = "o.refNo1", operationType = OperationType.EQUAL)
    public String getRefNo1() {
        return refNo1;
    }

    public void setRefNo1(String refNo1) {
        this.refNo1 = refNo1;
    }

    @Operation(fieldName = "o.refNo2", operationType = OperationType.EQUAL)
    public String getRefNo2() {
        return refNo2;
    }

    public void setRefNo2(String refNo2) {
        this.refNo2 = refNo2;
    }

    @Operation(fieldName = "o.notes", operationType = OperationType.EQUAL)
    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    @Operation(operationType = OperationType.CLAUSE, clause =
            "o.id in ( select detail.doHeaderId from OtherDoDetail detail,ProductBarcode barCode where  detail.sku.id = barCode.skuId and barCode.barcodeLevel1 = ? and detail.warehouseId =:warehouseId) ")
    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    @Operation(operationType = OperationType.CLAUSE, clause =
            "o.id in ( select detail.doHeaderId from OtherDoDetail detail,Sku sku  where  detail.skuId = sku.id and sku.productCode = ? and detail.warehouseId =:warehouseId ) ")
    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    @Operation(operationType = OperationType.CLAUSE, clause =
            "o.id in ( select detail.doHeaderId from OtherDoDetail detail,Sku sku  where  detail.skuId = sku.id and sku.productCname like ? and detail.warehouseId =:warehouseId ) ")
    public String getSkuCname() {
        if (StringUtils.isEmpty(skuCname)) {
            return null;
        }
        return "%" + skuCname + "%";
    }

    public void setSkuCname(String skuCname) {
        this.skuCname = skuCname;
    }

    @Operation(fieldName = "o.closeTime", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")
    public Date getCloseTimeFm() {
        return closeTimeFm;
    }

    public void setCloseTimeFm(Date closeTimeFm) {
        this.closeTimeFm = closeTimeFm;
    }

    @Operation(fieldName = "o.closeTime", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
    public Date getCloseTimeTo() {
        return closeTimeTo;
    }

    public void setCloseTimeTo(Date closeTimeTo) {
        this.closeTimeTo = closeTimeTo;
    }

    @Operation(fieldName = "o.createdAt", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")
    public Date getCreatedAtFrom() {
        return createdAtFrom;
    }

    public void setCreatedAtFrom(Date createdAtFrom) {
        this.createdAtFrom = createdAtFrom;
    }

    @Operation(fieldName = "o.createdAt", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
    public Date getCreatedAtTo() {
        return createdAtTo;
    }

    public void setCreatedAtTo(Date createdAtTo) {
        this.createdAtTo = createdAtTo;
    }
}
