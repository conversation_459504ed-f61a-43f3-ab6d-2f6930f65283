package com.daxia.wms.delivery.sort.action;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.security.Identity;

import com.daxia.framework.common.action.ActionBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;

import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.sort.dto.SortingDoDetailDTO;
import com.daxia.wms.delivery.sort.service.SortingService;
/**
 * 强制分拣action
 */
@Name("com.daxia.wms.delivery.forceSortingAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ForceSortingAction extends ActionBean {
	private static final long serialVersionUID = -919135520372419959L;
	private String doNo;
	private String staffNo;
	private List<SortingDoDetailDTO> sortingDoDetailDTOs;
	//分拣柜号
	private String sotringBinNo;
	
	@In
	private SortingService sortingService;
	
	public void init(){
		if(CollectionUtils.isEmpty(sortingDoDetailDTOs)){
			sortingDoDetailDTOs = new ArrayList<SortingDoDetailDTO>();
		}
		if (StringUtil.isEmpty(this.staffNo)) {
			Identity identity = (Identity) Component.getInstance("org.jboss.seam.security.identity");
			this.staffNo = identity.getCredentials().getUsername();
		}
	}
	
	/**
	 * 根据发货单号查询该发货单
	 */
	public void getSortingDoHeader() {
	    sortingDoDetailDTOs = new ArrayList<SortingDoDetailDTO>();
		this.sortingDoDetailDTOs = sortingService.getDetails4ForceSorting(doNo.trim());
	}
	
	/**
	 * 强制分拣
	 */
	@Loggable
	public void forceSorting() {
		List<SortingDoDetailDTO> secectedDTOs = getSelectedData();
		List<Long> doDetailIds = new ArrayList<Long>();
		for (SortingDoDetailDTO dto : secectedDTOs) {
			doDetailIds.add(dto.getId());
		}
		sortingService.forceSorting(doDetailIds, doNo, staffNo, sotringBinNo);
		sortingDoDetailDTOs.removeAll(secectedDTOs);
		if (ListUtil.isNullOrEmpty(sortingDoDetailDTOs)) {
			this.sayMessage("该发货单强制分拣成功！");
		} else {
			this.sayMessage("该发货明细强制分拣成功");
		}
	}
	
	/**
	 * 获取页面选中的dto
	 * @return
	 * @throws DeliveryException
	 */
	private List<SortingDoDetailDTO> getSelectedData() throws DeliveryException {
		List<SortingDoDetailDTO> secectedDTOs = new ArrayList<SortingDoDetailDTO>();
		for(SortingDoDetailDTO dto : sortingDoDetailDTOs) {
			if (dto.isSelected()) {
				secectedDTOs.add(dto);
			}
		}
		return secectedDTOs;
	}
	public String getDoNo() {
		return doNo;
	}
	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}
	public List<SortingDoDetailDTO> getSortingDoDetailDTOs() {
		return sortingDoDetailDTOs;
	}
	public void setSortingDoDetailDTOs(List<SortingDoDetailDTO> sortingDoDetailDTOs) {
		this.sortingDoDetailDTOs = sortingDoDetailDTOs;
	}
	public String getStaffNo() {
		return staffNo;
	}
	public void setStaffNo(String staffNo) {
		this.staffNo = staffNo;
	}

	public String getSotringBinNo() {
		return sotringBinNo;
	}

	public void setSotringBinNo(String sotringBinNo) {
		this.sotringBinNo = sotringBinNo;
	}

}
