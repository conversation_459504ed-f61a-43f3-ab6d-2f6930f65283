package com.daxia.wms.delivery.pick.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.*;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.PktStatus;
import com.daxia.wms.Constants.StockStatus;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.delivery.constant.DeliveryConstant;
import com.daxia.wms.delivery.pick.dto.BatchPickDTO;
import com.daxia.wms.delivery.pick.dto.PickTaskDto;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.print.dto.SlcTaskDTO;
import com.daxia.wms.master.entity.Container;
import com.daxia.wms.master.entity.Partition;
import com.daxia.wms.stock.stock.entity.StockBatchAtt;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.hibernate.Hibernate;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 拣货任务DAO
 */
@Name("com.daxia.wms.delivery.pickTaskDAO")
@lombok.extern.slf4j.Slf4j
public class PickTaskDAO extends HibernateBaseDAO<PickTask, Long> {

    private static final long serialVersionUID = -4083143539668100810L;

    /**
     * 关联拣货任务和波次
     *
     * @param waveHeaderId
     * @param pktHeaderId
     * @param doHeaderIds
     * @param regionId
     */
    public void relate(Long waveHeaderId, Long pktHeaderId, List<Long> doHeaderIds, Long regionId, boolean isEaTask) {
        String sql = "UPDATE tsk_pick tsk, md_location loc, md_partition par SET tsk.status = :doStatus, tsk.wave_h_id = :waveHeaderId, tsk.pkt_h_id = :pktHeaderId "
                + " WHERE tsk.doc_id IN (:doHeaderIds) AND tsk.warehouse_id = :warehouseId AND tsk.is_deleted = 0 AND tsk.fm_loc_id = loc.id "
                + " AND loc.partition_id = par.id AND par.region_id = :regionId "
                + " AND loc.warehouse_id = :warehouseId AND loc.is_deleted = 0 AND par.warehouse_id = :warehouseId AND par.is_deleted = 0 "
                + " AND " + (isEaTask ? "tsk.qty_unit = tsk.qty " : "tsk.qty_unit != tsk.qty ");
        Query query = this.createSQLQuery(sql);
        query.setParameter("doStatus", Constants.DoStatus.ALLALLOCATED.getValue());
        query.setParameter("waveHeaderId", waveHeaderId);
        query.setParameter("pktHeaderId", pktHeaderId);
        query.setParameterList("doHeaderIds", doHeaderIds);
        query.setParameter("regionId", regionId);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * <pre>
     * Description:根据DoHeaderId删除相应拣货任务，并修改任务状态为取消
     * </pre>
     *
     * @param doHeaderId
     */
    public void cancelPickTaskByDoHeaderId(Long doHeaderId) {
        String hql = "update PickTask o set o.isDeleted =:isDeleted , o.status =:status where o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId";
        Query query = this.createUpdateQuery(hql);
        query.setLong("isDeleted", Long.valueOf(YesNo.YES.getValue().longValue()));
        query.setString("status", DoStatus.CANCELED.getValue());
        query.setLong("doHeaderId", doHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 根据任务号和任务状态获取拣货任务
     *
     * @param taskNum
     * @param status
     * @return
     */
    public PickTask getPickTask(String taskNum, String status) {
        String hql = " from PickTask o where o.id = :taskNo and o.status = :taskStatus and o.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setString("taskNo", taskNum);
        query.setString("taskStatus", status);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (PickTask) query.uniqueResult();
    }

    /**
     * 根据do单头id和do单明细id获取拣货任务
     *
     * @param doId
     * @param doDetailId
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<PickTask> getPickTasks(Long doId, Long doDetailId) {
        String hql = " from PickTask o where o.doHeaderId = :doId and o.doDetailId = :doDetailId and o.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setLong("doId", doId);
        query.setLong("doDetailId", doDetailId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 根据波次id和状态获取拣货任务
     *
     * @param waveId
     * @param status
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<PickTask> getPickTasksByWaveId(Long waveId, String status) {
        String hql = "from PickTask o where o.waveHeaderId = :waveId and o.status = :status and o.warehouseId = :warehouseId";
        Query query = this.getSession().createQuery(hql);
        query.setLong("waveId", waveId);
        query.setString("status", status);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 根据拣货单id和拣货任务状态获取拣货任务
     *
     * @param pktId
     * @param status
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<PickTask> getPickTasksByPktId(Long pktId, String status) {
        String hql = "from PickTask o where o.pktHeaderId = :pktId and o.status = :status and o.warehouseId = :warehouseId";
        Query query = this.getSession().createQuery(hql);
        query.setLong("pktId", pktId);
        query.setString("status", status);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 根据波次id、库位id、skuId、状态获取拣货任务
     *
     * @param waveId
     * @param locId
     * @param skuId
     * @param status
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<PickTask> getPickTasksByWave(Long waveId, Long locId, Long skuId, String status, Long packageDetailId) {
        String hql = "from PickTask o where o.waveHeaderId =:waveId and o.status = :status " +
                "and o.locId = :locId and o.skuId = :skuId and o.warehouseId = :warehouseId";
        if (packageDetailId != null) {
            hql += " and o.packDetailId = :packDetailId ";
        }
        Query query = this.getSession().createQuery(hql);
        query.setLong("waveId", waveId);
        query.setString("status", status);
        query.setLong("locId", locId);
        query.setLong("skuId", skuId);
        if (packageDetailId != null) {
            query.setLong("packDetailId", packageDetailId);
        }
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 根据拣货单id、库位id、skuId和状态获取拣货任务
     *
     * @param pktId
     * @param locId
     * @param skuId
     * @param status
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<PickTask> getPktTasksByPkt(Long pktId, Long locId, Long skuId, String status, Long packageDetailId) {
        String hql = "from PickTask o where o.pktHeaderId = :pktId and o.status = :status " +
                "and o.locId = :locId and o.skuId = :skuId and o.warehouseId = :warehouseId";
        if (packageDetailId != null) {
            hql += " and o.packDetailId = :packDetailId ";
        }
        Query query = this.getSession().createQuery(hql);
        query.setLong("pktId", pktId);
        query.setString("status", status);
        query.setLong("locId", locId);
        query.setLong("skuId", skuId);
        if (packageDetailId != null) {
            query.setLong("packDetailId", packageDetailId);
        }
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 查找出拣货单头下所有的拣货明细(分配完成和部分拣货状态)
     *
     * @param pktHeaderId
     * @param statusAlloc
     * @param statusPartPick
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<PickTask> getPickTasksByPktHeaderIdAndStatus(Long pktHeaderId, String statusAlloc, String statusPartPick) {
        String hql = " from PickTask o where o.pktHeaderId =:pktHeaderId " +
                " and (o.status =:statusAlloc or o.status =:statusPartPick ) and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setLong("pktHeaderId", pktHeaderId);
        query.setString("statusAlloc", statusAlloc);
        query.setString("statusPartPick", statusPartPick);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * @param pktHeaderId
     * @return 各种状态及对应的数量，总数量（total）
     */
    @SuppressWarnings("unchecked")
    public Map<String, Long> findPickTaskStatusCountByPktHeader(Long pktHeaderId) {
        Map<String, Long> map = new HashMap<String, Long>();
        String hql = "select o.status from PickTask o where o.pktHeaderId = :pktHeaderId and o.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setLong("pktHeaderId", pktHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        List<Object> objList = query.list();
        Long total = Long.valueOf(0);
        for (Object obj : objList) {
            String status = (String) obj;
            Long count = map.get(status);
            if (count == null) {
                count = 1L;
            } else {
                count++;
            }
            total++;
            map.put(status, count);
        }
        map.put("total", total);
        return map;
    }

    /**
     * @param doDetailId
     * @return 各种状态及对应的数量，总数量（total）
     */
    @SuppressWarnings("unchecked")
    public Map<String, Long> findPickTaskStatusCountByDoDetail(Long doDetailId) {
        Map<String, Long> map = new HashMap<String, Long>();
        String hql = "select o.status from PickTask o where o.doDetailId = :doDetailId and o.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setLong("doDetailId", doDetailId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        List<Object> objList = query.list();
        Long total = Long.valueOf(0);
        for (Object obj : objList) {
            String status = (String) obj;
            Long count = map.get(status);
            if (count == null) {
                count = 1L;
            } else {
                count++;
            }
            total++;
            map.put(status, count);
        }
        map.put("total", total);
        return map;
    }

    /**
     * 根据波次主键更新其下所有pickTask的状态为分拣完成
     */
    public void updatePickTaskSortedByWaveId(Long waveId) {
        String hql = " update PickTask o set o.status =:status, o.qtySorted = o.pickedQty, o.qtySortedUnit = o.qtyPickedUnit "
                + " where o.waveHeaderId = :waveId and o.warehouseId = :warehouseId";
        Query query = this.createUpdateQuery(hql);
        query.setString("status", DoStatus.ALLPICKED.getValue());
        query.setLong("waveId", waveId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 根据发货单Id清空拣货任务和波次，拣货单头的关联
     *
     * @param doHeaderId 发货单Id
     */
    public void unrelate(Long doHeaderId) {
        String hql = "update PickTask o set o.waveHeaderId = null ,o.pktHeaderId = null where o.doHeader = :doHeaderId and o.warehouseId = :warehouseId";
        Query query = this.createUpdateQuery(hql);
        query.setLong("doHeaderId", doHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 根据do头id发货
     *
     * @param doHeaderId
     * @param shipWho
     */
    public void deliveryByDoHeader(Long doHeaderId, String shipWho) {
        String hql = "update PickTask o set o.status = :status ,o.qtyShipped = o.pickedQty ,o.shipTime = :nowTime,o.shipWho = :shipWho where o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId";
        Query query = createUpdateQuery(hql);
        query.setLong("doHeaderId", doHeaderId);
        query.setString("status", DoStatus.ALL_DELIVER.getValue());
        query.setString("shipWho", shipWho);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("nowTime", DateUtil.getNowTime());
        query.executeUpdate();
    }

    @SuppressWarnings("unchecked")
    public List<Long> findRegionId(List<Long> doIdList) {
        String hql = "SELECT DISTINCT region.id"
                + " FROM PickTask pickTask , Location location , Partition partition, Region region WHERE  "
                + "  pickTask.locId = location.id AND location.partitionId = partition.id AND partition.regionId = region.id"
                + " AND pickTask.doHeaderId IN (:ids) AND pickTask.warehouseId = :warehouseId AND location.warehouseId = :warehouseId AND partition.warehouseId = :warehouseId AND region.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setParameterList("ids", doIdList);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 根据波次号和状态获取拣货任务的sku
     *
     * @param status
     * @param docNo
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Long> getPickTaskSkusByWaveNo(String status, String docNo) {
        String hql = "select o.skuId from PickTask o "
                + "where o.status = :status and o.wave.waveNo = :waveNo and o.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setString("status", status);
        query.setString("waveNo", docNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 根据拣货单号和拣货任务状态获取sku
     *
     * @param status
     * @param docNo
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Long> getPickTaskSkusByPktNo(String status, String docNo) {
        String hql = "select o.skuId from PickTask o "
                + "where o.status = :status and o.pickHeader.pktNo = :pktNo and o.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setString("status", status);
        query.setString("pktNo", docNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 根据do单头获取缺货明细
     *
     * @param doId
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<PickTask> findLackPickTasksByDoId(Long doId) {
        String hql = " from PickTask o where o.doHeaderId = :doId and o.stockStatus in ('1', '2') and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setLong("doId", doId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 查询指定波次下已发布的拣货任务，用以绑定波次分页
     *
     * @param waveHeaderId
     * @param status
     * @param startIndex
     * @param pageSize
     * @return
     */
    @SuppressWarnings("unchecked")
    public DataPage<PickTask> findPickTask4BindList(Long waveHeaderId, PktStatus status, int startIndex, int pageSize) {
        String hql = "from PickTask o where o.waveHeaderId = ? and o.status = ? and o.warehouseId = ?";
        Object[] params = {waveHeaderId, status.getValue(), ParamUtil.getCurrentWarehouseId()};
        return (DataPage<PickTask>) this.executeQuery(hql, null, startIndex, pageSize, params);
    }

    /**
     * 判断容器是否和波次下的拣货任务绑定
     *
     * @param containerNo 容器号
     * @return true 绑定；false 未绑定
     */
    public boolean isContainerBind2PickTask(String containerNo, Long waveId) {
        String hql = "select 1 from PickTask o where o.containerNo = :containerNo and o.warehouseId = :warehouseId";
        if (waveId != null) {
            hql += " and o.waveHeaderId = :waveId";
        }
        Query query = this.createQuery(hql);
        query.setString("containerNo", containerNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        if (waveId != null) {
            query.setLong("waveId", waveId);
        }
        query.setMaxResults(1);
        return query.uniqueResult() == null ? false : true;
    }


    /**
     * 统计波次下拣货任务的SKU的数量，按波次ID分组
     *
     * @param waveIds     波次ID范围
     * @param statusList  任务状态
     * @param stockStatus 缺货状态，可以为null
     * @return
     */
    @SuppressWarnings("unchecked")
    public Map<Long, BigDecimal> getPickTaskSum(List<Long> waveIds, List<String> statusList, List<String> stockStatus) {
        Map<Long, BigDecimal> map = new HashMap<Long, BigDecimal>();

        String hql = "select o.waveHeaderId, sum(o.qty) from PickTask o where o.waveHeaderId in (:waveIds) and o.status in (:stats) and o.warehouseId = :warehouseId";
        if (!ListUtil.isNullOrEmpty(stockStatus)) {
            hql = hql + " and o.stockStatus in (:stockStatus)";
        }
        hql = hql + " group by o.waveHeaderId";

        Query query = this.createQuery(hql).setParameterList("waveIds", waveIds).setParameterList("stats", statusList).setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        if (!ListUtil.isNullOrEmpty(stockStatus)) {
            query.setParameterList("stockStatus", stockStatus);
        }

        List<Object[]> numList = query.list();
        for (Object[] obj : numList) {
            map.put((Long) obj[0], (BigDecimal) obj[1]);
        }
        return map;
    }

    /**
     * 根据波次id获取已绑定容器的拣货任务
     *
     * @param waveHeaderId
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<PickTask> findBindPickTasksByWave(Long waveHeaderId) {
        String hql = "from PickTask o where o.containerNo is not null and o.status=60 and o.waveHeaderId = :waveHeaderId and o.warehouseId = :warehouseId";
        Query query = this.getSession().createQuery(hql);
        query.setLong("waveHeaderId", waveHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 根据容器号，获取已绑定该容器的相关拣货任务
     *
     * @param waveId
     * @param containerNo
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<PickTask> findBindPickTasksByContainerNo(String containerNo, Long waveId) {
        String hql = "from PickTask o where o.containerNo = :containerNo and o.waveHeaderId = :waveId and o.warehouseId = :warehouseId";
        Query query = this.getSession().createQuery(hql);
        query.setString("containerNo", containerNo);
        query.setLong("waveId", waveId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 波次下，拣货单的拣货任务的SKU数量
     *
     * @param waveId      波次ID
     * @param statusList  任务状态
     * @param stockStatus 任务缺货状态，可以为null
     * @return
     */
    @SuppressWarnings("unchecked")
    public Map<Long, BigDecimal> getPktPickTaskSum(Long waveId, List<String> statusList, List<String> stockStatus) {
        Map<Long, BigDecimal> map = new HashMap<Long, BigDecimal>();
        String hql = "select o.pktHeaderId, sum(o.qty) from PickTask o where o.waveHeaderId = :waveId and o.status in (:stats) and o.warehouseId = :warehouseId";
        if (!ListUtil.isNullOrEmpty(stockStatus)) {
            hql = hql + " and o.stockStatus in (:stockStatus)";
        }
        hql = hql + " group by o.pktHeaderId";

        Query query = this.createQuery(hql).setParameter("waveId", waveId).setParameterList("stats", statusList).setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        if (!ListUtil.isNullOrEmpty(stockStatus)) {
            query.setParameterList("stockStatus", stockStatus);
        }

        List<Object[]> numList = query.list();
        for (Object[] obj : numList) {
            map.put((Long) obj[0], (BigDecimal) obj[1]);
        }
        return map;
    }

    /**
     * 波次下，拣货单的拣货任务的SKU总数量
     *
     * @param waveId 波次ID
     * @return
     */
    @SuppressWarnings("unchecked")
    public Map<Long, BigDecimal> getPktPickTaskNumMap(Long waveId) {
        Map<Long, BigDecimal> map = new HashMap<Long, BigDecimal>();
        String hql = "select o.pktHeaderId, sum(o.qty) from PickTask o where o.waveHeaderId = :waveId and o.warehouseId = :warehouseId group by o.pktHeaderId ";
        Query query = this.createQuery(hql).setParameter("waveId", waveId).setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        List<Object[]> numList = query.list();
        for (Object[] obj : numList) {
            map.put((Long) obj[0], ((BigDecimal) obj[1]));
        }
        return map;
    }

    /**
     * 根据拣货单Id查询拣货单的拣货人
     *
     * @param pickHeaderId
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<String> findPickWhoByPickHeaderId(Long pickHeaderId) {
        String sql = "select distinct o.pick_who from tsk_pick o where o.pkt_h_id = :pickHeaderId and o.pick_who is not null and o.warehouse_id = :warehouseId and o.is_deleted = 0";
        Query query = this.createSQLQuery(sql).setParameter("pickHeaderId", pickHeaderId).setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * @param doHeaderId 判断订单有么有缺货的拣货任务
     */
    public boolean isExistLackPickTask(Long doHeaderId) {
        String sql = "select 1 from tsk_pick t where t.doc_id = :doHeaderId and t.stock_status != :stockStatus and t.warehouse_id = :warehouseId and t.is_deleted = 0";
        Query query = this.createSQLQuery(sql).setParameter("doHeaderId", doHeaderId).setParameter("stockStatus", StockStatus.NORMAL.getValue()).setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        Object object = query.uniqueResult();
        return object == null ? false : true;
    }

    /**
     * @param doHeaderId 判断订单有么有非缺货的拣货任务
     */
    public boolean isExistNotLackPickTask(Long doHeaderId) {
        String sql = "select 1 from tsk_pick t where t.doc_id = :doHeaderId and t.stock_status = :stockStatus and t.status = :status and t.warehouse_id = :warehouseId and t.is_deleted = 0";
        Query query = this.createSQLQuery(sql).setParameter("doHeaderId", doHeaderId).setParameter("stockStatus", StockStatus.NORMAL.getValue()).
                setParameter("status", DoStatus.ALLPICKED.getValue()).setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        Object object = query.uniqueResult();
        return object == null ? false : true;
    }

    /**
     * 查询拣货单下拣货、未拣货、缺货units数量
     *
     * @param pktHeaderIds
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Object> getPktPickTaskSumByPktHId(List<Long> pktHeaderIds) {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("select sum(case when tp.STATUS in ('60', '80') then tp.QTY end) as pickedNum,");
        stringBuffer.append("sum(case when tp.STATUS in ('00', '40') and tp.STOCK_STATUS = 0 then tp.QTY end) as unPickedNum,");
        stringBuffer.append("sum(case when tp.STATUS in ('00', '40') and tp.STOCK_STATUS in (1,2) then tp.QTY end) as lackUnpickedNum ");
        stringBuffer.append("from tsk_pick tp where tp.IS_DELETED = 0 and tp.PKT_H_ID in (:pktHeaderIds) and tp.warehouse_id = :warehouseId");
        Query query = this.createSQLQuery(stringBuffer.toString()).setParameterList("pktHeaderIds", pktHeaderIds).setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 根据波次和容器查询拣货任务明细（合并同一商品）
     */
    @SuppressWarnings("unchecked")
    public List<Object> findPickTasksByWaveContainer(String containerNo, Long waveId) {
        StringBuilder sqlBuilder = new StringBuilder("select ms.product_code,ms.ean13,ms.product_cname,sum(t.qty_picked) from tsk_pick t,md_sku ms");
        sqlBuilder.append(" where t.sku_id = ms.id and t.is_deleted = 0 and ms.is_deleted = 0 and t.container_no = :containerNo and t.wave_h_id = :waveId and t.warehouse_id = :warehouseId");
        sqlBuilder.append(" group by ms.product_code,ms.ean13,ms.product_cname");
        Query query = createSQLQuery(sqlBuilder.toString()).setParameter("containerNo", containerNo).setParameter("waveId", waveId).setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 查询拣货单绑定的空容器
     *
     * @param pickHeader
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<String> getPHBindEmptyContainber(PickHeader pickHeader) {
        StringBuffer sqlSb = new StringBuffer();
        sqlSb.append("(select c.container_no from md_container c ")
                .append(" where c.doc_no = :pktNo and c.is_deleted = 0 and c.business_status in (1,2) and c.warehouse_id = :warehouseId) ")
                .append(" minus ")
                .append(" (select distinct t.container_no from tsk_pick t where  t.pkt_h_id = :pktId and t.warehouse_id = :warehouseId and t.is_deleted = 0) ");
        Query query = this.createSQLQuery(sqlSb.toString());
        query.setParameter("pktNo", pickHeader.getPktNo());
        query.setParameter("pktId", pickHeader.getId());
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (List<String>) query.list();
    }

    /**
     * 根据do头Id和拣货情况查询拣货任务
     *
     * @param doId
     * @param stockStatus
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<PickTask> getPickTasksByDoIdAndStockStatus(Long doId, List<String> stockStatus) {
        String hql = " from PickTask o where o.doHeaderId = :doId and o.stockStatus in (:stockStatus) and o.warehouseId = :warehouseId ";
        Query query = createQuery(hql);
        query.setLong("doId", doId);
        query.setParameterList("stockStatus", stockStatus);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 批量逻辑删除拣货任务
     *
     * @param ids
     */
    public void logicDeleteByIds(List<Long> ids) {
        String hql = "update PickTask o set o.isDeleted = 1 where o.id in (:ids)";
        Query query = this.createQuery(hql).setParameterList("ids", ids);
        query.executeUpdate();
    }

    /**
     * 根据发货明细查询拣货任务 并按照拣货数倒序排序
     *
     * @param doDetailId
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<PickTask> queryByDoDetail(Long doDetailId) {
        String hql = "from PickTask o where o.warehouseId = :warehouseId and o.doDetailId = :doDetailId order by o.qty desc";
        Query query = this.createQuery(hql)
                .setLong("warehouseId", ParamUtil.getCurrentWarehouseId())
                .setLong("doDetailId", doDetailId);
        return query.list();
    }

    @SuppressWarnings("unchecked")
    public List<PickTask> getPickTasksByWaveId(Long waveId) {
        String hql = "from PickTask o where o.waveHeaderId = :waveId and o.warehouseId = :warehouseId";
        Query query = this.getSession().createQuery(hql);
        query.setLong("waveId", waveId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 查询拣货单下的库区
     *
     * @param pktHeaderId
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Partition> queryPartitionByPktHId(Long pktHeaderId) {
        String hql = "select distinct(o.location.partition) from PickTask o where o.pktHeaderId = :pktHeaderId and o.warehouseId = :warehouseId";
        Query query = this.getSession().createQuery(hql);
        query.setLong("pktHeaderId", pktHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (List<Partition>) query.list();
    }

    /**
     * 查询发货明细的拣货任务数最大的拣货任务
     *
     * @param doDetailId
     * @return
     */
    public PickTask queryMaxCountByDetail(Long doDetailId) {
        String hql = "select o from PickTask o where o.doDetailId = :doDetailId and o.warehouseId = :warehouseId order by o.qty desc o.id desc";
        Query query = this.createQuery(hql)
                .setLong("doDetailId", doDetailId)
                .setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (PickTask) query.uniqueResult();
    }

    @SuppressWarnings("unchecked")
    public List<Long> getPartitionIdsByAlcHeaderId(Long alcHeaderId) {
        String hql = " select distinct p.id from PickTask o " +
                " left join o.location l " +
                " left join l.partition p " +
                " where o.doHeaderId = :alcHeaderId and o.warehouseId = :warehouseId " +
                " order by p.id asc ";
        Query query = this.createQuery(hql);
        query.setParameter("alcHeaderId", alcHeaderId);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 查询拣货单下的库区
     *
     * @param waveHeaderId
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Partition> queryPartitionByWaveIdConNo(Long waveHeaderId, String containerNo) {
        String hql = "select distinct(o.location.partition) from PickTask o where o.waveHeaderId = :waveHeaderId and o.containerNo = :containerNo and o.warehouseId = :warehouseId";
        Query query = this.getSession().createQuery(hql);
        query.setLong("waveHeaderId", waveHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("containerNo", containerNo);
        return (List<Partition>) query.list();
    }

    /**
     * 显示拣货单号
     *
     * @param waveHeaderId
     * @param containerNo
     * @return
     */
    @SuppressWarnings("rawtypes")
    public String queryPktHNosByWaveIdConNo(Long waveHeaderId, String containerNo) {
        String hql = "select distinct(o.pickHeader.pktNo) from PickTask o where o.waveHeaderId = :waveHeaderId and o.containerNo = :containerNo and o.warehouseId = :warehouseId";
        Query query = this.getSession().createQuery(hql);

        query.setLong("waveHeaderId", waveHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("containerNo", containerNo);
        List list = query.list();
        if (ListUtil.isNullOrEmpty(list)) {
            return "";
        }
        return list.toString().substring(1, list.toString().length() - 1);
    }

    /**
     * 根据doHeaderId联表查询Slc出库模板所需数据
     *
     * @param doHeaderId
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<SlcTaskDTO> getSlcPrintInfoByDoHeaderId(Long doHeaderId) {
        StringBuilder hql = new StringBuilder("select new com.daxia.wms.delivery.print.dto.SlcTaskDTO(ml.locCode,mc.categoryName, tp.qty,dod.price,mk.productCode,mk.productCname,mk.ean13 ,tp.skuId,tp.locId) ");
        hql.append(" from PickTask tp ");
        hql.append(" left join tp.location ml  ");
        hql.append(" left join tp.doDetail dod ");
        hql.append(" left join tp.sku mk  ");
        hql.append(" left join tp.sku.category mc ");
        hql.append(" where tp.doHeaderId =:doHeaderId and tp.warehouseId =:warehouseId");
        Query query = this.createQuery(hql.toString());
        query.setLong("doHeaderId", doHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (List<SlcTaskDTO>) query.list();
    }

    /**
     * 更新拣货任务的拣货人信息
     *
     * @param waveHeaderId
     * @param updateBy
     * @return
     */
    public void updatePickTaskInfo(Long waveHeaderId, String updateBy) {
        String updateQuery = "update tsk_pick o set o.update_by = :updateBy, o.update_time = sysdate() where o.wave_h_id = :waveHeaderId and o.warehouse_id = :warehouseId";
        Query query = createSQLQuery(updateQuery);
        query.setString("updateBy", updateBy);
        query.setLong("waveHeaderId", waveHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 查询发货明细已拣货数
     *
     * @param doDetailId
     * @return
     */
    public BigDecimal getDoDetailPickedCount(Long doDetailId) {
        String hql = "select sum(o.pickedQty) from PickTask o where "
                + "o.doDetailId = :doDetailId and o.isDeleted =0 and o.warehouseId = :warehouseId group by o.doDetailId";
        Query query = this.createQuery(hql)
                .setParameter("doDetailId", doDetailId)
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        Object obj = query.uniqueResult();
        if (null == obj) {
            return BigDecimal.ZERO;
        } else {
            return (BigDecimal) obj;
        }
    }

    public String[] getLastPickedLocOfWave(Long waveId) {
        String sql = "  Select loc.Loc_Code, Pp.Partition_Code From tsk_pick t, md_location loc, md_partition p, md_physical_partition Pp " +
                "  Where t.Fm_Loc_Id = loc.Id " +
                "    And loc.Partition_Id = p.Id " +
                "    And p.p_Partition_Id = Pp.Id " +
                "    And t.Wave_h_Id = :waveId " +
                "    And t.Status = :taskStatus " +
                "    And t.Is_Deleted = 0 " +
                "    And loc.Is_Deleted = 0 " +
                "    And p.Is_Deleted = 0 " +
                "    And Pp.Is_Deleted = 0 " +
                "    And t.Warehouse_Id = :warehouseId " +
                "    And loc.Warehouse_Id = :warehouseId " +
                "    And p.Warehouse_Id = :warehouseId " +
                "    And Pp.Warehouse_Id = :warehouseId " +
                "    Order By t.update_time Desc ";
        Query query = this.createSQLQuery(sql);
        query.setParameter("waveId", waveId);
        query.setParameter("taskStatus", DoStatus.ALLPICKED.getValue());
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        Object[] objArr = (Object[]) query.uniqueResult();
        if (objArr == null) {
            return null;
        }
        String[] strArr = new String[objArr.length];
        for (int i = 0; i < objArr.length; i++) {
            strArr[i] = (String) objArr[i];
        }
        return strArr;
    }

    public String[] getLastPickedLocOfPktH(Long pickHId) {
        String sql = "  Select loc.Loc_Code, Pp.Partition_Code From tsk_pick t, md_location loc, md_partition p, md_physical_partition Pp " +
                "  Where t.Fm_Loc_Id = loc.Id " +
                "    And loc.Partition_Id = p.Id " +
                "    And p.p_Partition_Id = Pp.Id " +
                "    And t.Pkt_h_Id = :pickHId " +
                "    And t.Status = :taskStatus " +
                "    And t.Is_Deleted = 0 " +
                "    And loc.Is_Deleted = 0 " +
                "    And p.Is_Deleted = 0 " +
                "    And Pp.Is_Deleted = 0 " +
                "    And t.Warehouse_Id = :warehouseId " +
                "    And loc.Warehouse_Id = :warehouseId " +
                "    And p.Warehouse_Id = :warehouseId " +
                "    And Pp.Warehouse_Id = :warehouseId " +
                "    Order By t.update_time Desc ";
        Query query = this.createSQLQuery(sql);
        query.setParameter("pickHId", pickHId);
        query.setParameter("taskStatus", DoStatus.ALLPICKED.getValue());
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        Object[] objArr = (Object[]) query.uniqueResult();
        if (objArr == null) {
            return null;
        }
        String[] strArr = new String[objArr.length];
        for (int i = 0; i < objArr.length; i++) {
            strArr[i] = (String) objArr[i];
        }
        return strArr;
    }

    @SuppressWarnings("unchecked")
    public List<PickTask> getPickTasksIOrderByBatch(Long doId, Long doDetailId) {
        String hql = " from PickTask o where o.doHeaderId = :doId and o.doDetailId = :doDetailId and o.warehouseId = :warehouseId "
                + " order by o.stockBatchAtt.lotatt02 asc, o.stockBatchAtt.lotatt01 asc";
        Query query = createQuery(hql);
        query.setLong("doId", doId);
        query.setLong("doDetailId", doDetailId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    public List<Object[]> getPickSortDetails(List<Long> pickTaskList) {
        String hql = "SELECT IFNULL(c.grid_no,dh.sort_grid_no), " +
                " dh.do_no, " +
                " sum(p.qty_picked_unit), " +
                " pd.UOM_DESCR," +
                " c.container_no " +
                " FROM " +
                " tsk_pick p " +
                " INNER JOIN doc_do_header dh ON p.doc_id = dh.id " +
                " INNER JOIN doc_pkt_header ph ON ph.id = p.pkt_h_id " +
                " LEFT JOIN (SELECT doc_no,REF_NO_1,grid_no,GROUP_CONCAT(container_no) as container_no from md_container " +
                " WHERE warehouse_id = :warehouseId AND doc_no is not null GROUP BY doc_no,REF_NO_1,grid_no) c ON c.doc_no = dh.do_no AND c.REF_NO_1 = ph.pkt_no " +
                " LEFT JOIN md_package_d pd on pd.ID = p.pack_detail_id " +
                " WHERE " +
                " p.warehouse_id = :warehouseId " +
                " AND p.id IN (:pickTaskList) " +
                " GROUP BY " +
                " dh.sort_grid_no, " +
                " dh.do_no " +
                " HAVING " +
                " sum(p.qty_picked_unit) > 0";
        Query query = createSQLQuery(hql);
        query.setParameterList("pickTaskList", pickTaskList);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    public boolean existUnitPickTask(Long regionId, List<Long> doIds, boolean isEaTask) {
        String sql = "SELECT 1 from tsk_pick tsk WHERE tsk.doc_id IN (:doIds) AND tsk.warehouse_id = :warehouseId AND tsk.is_deleted = 0 AND tsk.fm_loc_id IN ( "
                + "SELECT loc.id FROM md_location loc, md_partition par WHERE loc.partition_id = par.id AND par.region_id = :regionId "
                + " AND loc.warehouse_id = :warehouseId AND loc.is_deleted = 0 AND par.warehouse_id = :warehouseId AND par.is_deleted = 0 "
                + ") AND " + (isEaTask ? "tsk.qty_unit = tsk.qty " : "tsk.qty_unit != tsk.qty ");

        return this.createSQLQuery(sql).setParameterList("doIds", doIds).setParameter("regionId", regionId).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).setMaxResults(1).uniqueResult() != null;
    }

    public boolean existUnitPickTask(List<Long> regionIdList, List<Long> doIds, boolean isEaTask) {
        String sql = "SELECT 1 from tsk_pick tsk WHERE tsk.doc_id IN (:doIds) AND tsk.warehouse_id = :warehouseId AND tsk.is_deleted = 0 AND tsk.fm_loc_id IN ( "
                + "SELECT loc.id FROM md_location loc, md_partition par WHERE loc.partition_id = par.id AND par.region_id in (:regionIdList) "
                + " AND loc.warehouse_id = :warehouseId AND loc.is_deleted = 0 AND par.warehouse_id = :warehouseId AND par.is_deleted = 0 "
                + ") AND " + (isEaTask ? "tsk.qty_unit = tsk.qty " : "tsk.qty_unit != tsk.qty ");

        return this.createSQLQuery(sql).setParameterList("doIds", doIds).setParameterList("regionIdList", regionIdList).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).setMaxResults(1).uniqueResult() != null;
    }

    public BigDecimal sumQtyUnit(Long pktHeaderId) {
        String hql = "SELECT SUM(pt.qtyUnit) FROM PickTask pt WHERE pt.pktHeaderId = :pktHeaderId AND pt.warehouseId = :warehouseId";
        return (BigDecimal) this.createQuery(hql).setParameter("pktHeaderId", pktHeaderId).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).setMaxResults(1).uniqueResult();
    }

    public List<PickTask> findByPktId(Long pktHeaderId) {
        String hql = "FROM PickTask pt WHERE pt.pktHeaderId = :pktHeaderId AND pt.warehouseId = :warehouseId";
        return (List<PickTask>) this.createQuery(hql).setParameter("pktHeaderId", pktHeaderId).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).list();
    }

    public List<Long> queryPickHeaderByDoId(Long waveId) {
        String hql = "select distinct(o.pktHeaderId) from PickTask o where o.doHeader.waveId = :waveHeaderId and o.warehouseId = :warehouseId";
        Query query = this.getSession().createQuery(hql);
        query.setLong("waveHeaderId", waveId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (List<Long>) query.list();
    }

    public Long isStockIsLock(Long doHeaderId) {
        String sql = "SELECT o.sku_id FROM tsk_pick o, stock_batch_att sb WHERE " +
                "o.is_deleted = 0 AND o.warehouse_id = :warehouseId AND o.lot_id = sb.id AND o.doc_id = :doHeaderId AND sb.status < " + StockBatchAtt.STATUS_NORMAL;
        Query query = this.getSession().createSQLQuery(sql);
        query.setLong("doHeaderId", doHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());

        BigInteger skuId = (BigInteger) query.setMaxResults(1).uniqueResult();
        return skuId == null ? null : skuId.longValue();
    }

    public List<String> findLotNoByDoDetail(Long doDetailId) {
        String hql = "SELECT DISTINCT o.stockBatchAtt.lotatt05 FROM PickTask o WHERE o.doDetailId = :doDetailId AND o.warehouseId = :warehouseId";
        return this.createQuery(hql).setParameter("doDetailId", doDetailId).setLong("warehouseId", ParamUtil.getCurrentWarehouseId()).list();
    }

    /**
     * doDetailId：[qtyPcs, qtyUnit]
     *
     * @param doHeaderId
     * @return
     */
    public Map<Long, ImmutablePair<BigDecimal, BigDecimal>> countTask(Long doHeaderId) {
        String sql = "SELECT tsk.doc_line_id, sum(IF(pd.QTY = 1, tsk.qty, 0)) as pcs, sum(IF(pd.QTY > 1, tsk.qty_unit, 0)) as unit FROM tsk_pick tsk, md_package_d pd WHERE " + "tsk.pack_detail_id = pd.id AND tsk.doc_id = :doHeaderId" +
                " AND "
                + "tsk.warehouse_id = :warehouseId AND tsk.is_deleted = 0 AND pd.is_deleted = 0 GROUP BY tsk.doc_line_id";
        List<Object[]> objs = this.createSQLQuery(sql).setParameter("doHeaderId", doHeaderId).setLong("warehouseId", ParamUtil.getCurrentWarehouseId()).list();
        Map<Long, ImmutablePair<BigDecimal, BigDecimal>> qtyMap = Maps.newHashMap();
        for (Object[] o : objs) {
            qtyMap.put(((BigInteger) o[0]).longValue(), new ImmutablePair((BigDecimal) o[1], (BigDecimal) o[2]));
        }
        return qtyMap;
    }

    public List<Object[]> findCompletedTaskByWaveNo(String waveNo) {
        String hql = "SELECT o.sku.productCname, o.skuId FROM PickTask o WHERE o.wave.waveNo = :waveNo AND o.status = :status AND o.warehouseId = :warehouseId GROUP BY o.skuId";

        return this.createQuery(hql).setParameter("status", DoStatus.ALLPICKED.getValue()).setParameter("waveNo", waveNo).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).list();
    }

    public List<Object[]> findCompletedTaskByPktNo(String pktNo) {
        String hql = "SELECT o.sku.productCname, o.skuId FROM PickTask o WHERE o.pickHeader.pktNo = :pktNo AND o.status = :status AND o.warehouseId = :warehouseId GROUP BY o.skuId";

        return this.createQuery(hql).setParameter("status", DoStatus.ALLPICKED.getValue()).setParameter("pktNo", pktNo).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).list();
    }

    public List<Object[]> getPickSortDetails(String docNum, Integer docType, Long skuId) {
        if (docType == 1) {
            String sql = "SELECT " +
                    " dh.sort_grid_no, " +
                    " dh.do_No, " +
                    " sum(p.qty_picked_unit), " +
                    " pd.UOM_DESCR," +
                    " null " +
                    " FROM " +
                    " tsk_pick p " +
                    " INNER JOIN doc_do_header dh ON dh.id = p.doc_id " +
                    " INNER JOIN doc_wave_header wh ON wh.id = dh.wave_id " +
                    " LEFT JOIN md_package_d pd ON pd.id = p.pack_detail_id " +
                    " WHERE " +
                    " wh.wave_No = :waveNo " +
                    " AND p.sku_id = :skuId " +
                    " AND p.warehouse_id = :warehouseId " +
                    " AND p.`status` =  :status " +
                    " GROUP BY " +
                    " dh.sort_grid_no, " +
                    " dh.do_no";

            Query query = createSQLQuery(sql);
            query.setParameter("waveNo", docNum);
            query.setParameter("status", DoStatus.ALLPICKED.getValue());
            query.setParameter("skuId", skuId);
            query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
            return query.list();
        } else {
            String sql = "SELECT " +
                    " IFNULL(c.grid_no,dh.sort_grid_no), " +
                    " dh.do_no, " +
                    " sum(p.qty_picked_unit), " +
                    " pd.UOM_DESCR," +
                    " c.container_no " +
                    " FROM " +
                    " tsk_pick p " +
                    " INNER JOIN doc_do_header dh on dh.id = p.doc_id " +
                    " INNER JOIN doc_pkt_header pkt on pkt.id = p.pkt_h_id " +
                    " LEFT JOIN md_package_d pd on pd.id = p.pack_detail_id " +
                    " LEFT JOIN (SELECT doc_no,REF_NO_1,grid_no,GROUP_CONCAT(container_no) as container_no from md_container " +
                    " WHERE warehouse_id = :warehouseId AND doc_no is not null GROUP BY doc_no,REF_NO_1,grid_no) c on c.doc_no = dh.do_no AND c.ref_no_1 = pkt.pkt_no " +
                    " WHERE pkt.pkt_no = :pktNo " +
                    " AND p.sku_id = :skuId " +
                    " AND p.`status` = :status " +
                    " AND p.warehouse_id = :warehouseId " +
                    " GROUP BY " +
                    " dh.sort_grid_no, " +
                    " dh.do_no";

            Query query = createSQLQuery(sql);
            query.setParameter("pktNo", docNum);
            query.setParameter("status", DoStatus.ALLPICKED.getValue());
            query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
            query.setParameter("skuId", skuId);
            return query.list();
        }
    }

    public void batchUpdate4Pick(List<BatchPickDTO> batchIdList, Container container, String pickerNo, String reason) {
        Map<String, Object> params = Maps.newHashMap();
        String sql = "update tsk_pick t SET status = :status, to_lpn_no = :toLpnNo, pick_who = :pickerNo, stock_status = :reason, pick_time = now() ";
        if (null != container) {
            sql = sql + ",container_no = :containerNo ";
            params.put("containerNo", container.getContainerNo());
        }
        params.put("status", DoStatus.ALLPICKED.getValue());
        params.put("toLpnNo", DeliveryConstant.LPN_DEFAULT_NO);
        params.put("pickerNo", pickerNo);
        params.put("reason", reason);

        StringBuilder sbQty = new StringBuilder(",qty_picked =(CASE id");
        StringBuilder sbQtyUnit = new StringBuilder(",qty_picked_unit =(CASE id");
        StringBuilder toStockId = new StringBuilder(",to_stock_id =(CASE id");
        List<Long> idList = Lists.newArrayList();
        for (BatchPickDTO batchPickDTO : batchIdList) {
            sbQty.append(" WHEN ").append(batchPickDTO.getPickTask().getId()).append(" THEN ").append(batchPickDTO.getPickedQty().toString());
            sbQtyUnit.append(" WHEN ").append(batchPickDTO.getPickTask().getId()).append(" THEN ").append(batchPickDTO.getPickedQtyUnit().toString());
            toStockId.append(" WHEN ").append(batchPickDTO.getPickTask().getId()).append(" THEN ").append(batchPickDTO.getPickTask().getToStockId().toString());
            idList.add(batchPickDTO.getPickTask().getId());
        }
        sbQty.append(" END)");
        sbQtyUnit.append(" END)");
        toStockId.append(" END)");
        sql = sql + sbQty.toString() + sbQtyUnit.toString() + toStockId.toString() + " WHERE id IN (:idList) AND is_deleted = 0 AND warehouse_id = :warehouseId";
        params.put("idList", idList);
        params.put("warehouseId", ParamUtil.getCurrentWarehouseId());
        SQLQuery sqlQuery = this.createSQLQuery(sql);
        this.prepareParameter(params, sqlQuery);
        sqlQuery.executeUpdate();
    }

    public List<Integer> countQtyByDoc4Group(Long pkHeaderId, Long locId, Long skuId) {
        String sql = " select DISTINCT(qty) as qty from (select sum(qty) AS qty from tsk_pick t " +
                " where t.pkt_h_id = :pkHeaderId and fm_loc_id = :locId and sku_id = :skuId GROUP BY doc_id) temp ";
        SQLQuery query = this.createSQLQuery(sql);
        query.setParameter("pkHeaderId", pkHeaderId)
                .setParameter("locId", locId)
                .setParameter("skuId", skuId);
        query.addScalar("qty", Hibernate.INTEGER);
        return query.list();
    }

    public List<PickTask> findPickTaskByDoIdAndSkuId(Long doId, Long skuId, String lotatt05, Integer pkType) {
        String hql = "select pt FROM PickTask pt, StockBatchAtt att WHERE pt.skuId = :skuId AND pt.lotId = att.id AND pt.doHeaderId = :doHeaderId and pt.warehouseId = :warehouseId";
        if (PickHeader.PKT_TYPE_PCS.equals(pkType)) {
            hql += " and pt.qty = pt.qtyUnit ";
        } else {
            hql += " and pt.qty != pt.qtyUnit ";
        }
        if (lotatt05 != null) {
            hql += " and ifnull(att.lotatt05,'') = :lotatt05 ";
        }
        Query query = this.createQuery(hql);
        query.setParameter("skuId", skuId);
        query.setParameter("doHeaderId", doId);
        if (lotatt05 != null) {
            query.setParameter("lotatt05", lotatt05);
        }
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (List<PickTask>) query.list();
    }

    public String getDoPickStatus(Long doId, String packageType) {
        String hql = "select o.status from PickTask o where o.doHeaderId = :doId and o.warehouseId = :warehouseId ";
        if (packageType.equals(Constants.PackageType.B.getValue())) {
            hql = hql + " and o.qty = o.qtyUnit ";
        } else {
            hql = hql + " and o.qty != o.qtyUnit ";
        }
        Query query = createQuery(hql);
        query.setLong("doId", doId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        List<Object> objList = query.list();
        if (CollectionUtils.isEmpty(objList)) {
            return null;
        }
        boolean allAllocatedFlag = false;
        boolean allPickedFlag = false;
        for (Object obj : objList) {
            String status = (String) obj;
            if (StringUtil.isIn(status, DoStatus.ALLPICKED.getValue(), DoStatus.CANCELED.getValue())) {
                allPickedFlag = true;
            } else if (StringUtil.equals(status,DoStatus.ALLALLOCATED.getValue())) {
                allAllocatedFlag = true;
            }
        }
        if (allAllocatedFlag && allPickedFlag) {
            //分配完成+拣货完成 = 部分拣货
            return DoStatus.PARTPICKED.getValue();
        } else if (allAllocatedFlag && !allPickedFlag) {
            //分配完成
            return DoStatus.ALLALLOCATED.getValue();
        } else if (!allAllocatedFlag && allPickedFlag) {
            //拣货完成
            return DoStatus.ALLPICKED.getValue();
        }

        return null;
    }
    
    public boolean existCBType(Long doId) {
        String sql = "SELECT IF(tp.qty = tp.qty_unit, 1, 0) FROM tsk_pick tp WHERE tp.doc_id = :doId AND tp.is_deleted = 0 AND tp.warehouse_id = :warehouseId " +
                "GROUP BY IF(tp.qty = tp.qty_unit, 1, 0)";
        Query query = this.createSQLQuery(sql).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).setParameter("doId", doId);
        List list = query.list();
        return ListUtil.isNotEmpty(list) && list.size() == 2;
    }
    
    public List<PickTask> findPickTaskByDoId(Long doId){
        String hql = "FROM PickTask pt WHERE  pt.doHeaderId = :doHeaderId and warehouse_id = :warehouseId";
        Query query = this.createQuery(hql);
        query.setParameter("doHeaderId",doId);
        query.setParameter("warehouseId",ParamUtil.getCurrentWarehouseId());
        return (List<PickTask>) query.list();
    }

    public Map<String,Long> findStatusCountByWave(Long waveHeadId) {
        Map<String, Long> map = new HashMap<String, Long>();
        String hql = "select o.status from PickTask o where o.waveHeaderId = :waveHeadId and o.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setLong("waveHeadId", waveHeadId)
                .setLong("warehouseId", ParamUtil.getCurrentWarehouseId());

        List<Object> objList = query.list();
        Long total = Long.valueOf(0);
        for (Object obj : objList) {
            String status = (String) obj;
            Long count = map.get(status);
            if (count == null) {
                count = 1L;
            } else {
                count++;
            }
            total++;
            map.put(status, count);
        }
        map.put("total", total);
        return map;
    }

    public String getDoPickStatus(Long doId) {
        String hql = "select o.status from PickTask o where o.doHeaderId = :doId and o.warehouseId = :warehouseId ";
        Query query = createQuery(hql);
        query.setLong("doId", doId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        List<Object> objList = query.list();
        boolean allAllocatedFlag = false;
        for (Object obj : objList) {
            String status = (String) obj;
            if (StringUtil.equals(status,DoStatus.ALLALLOCATED.getValue())) {
                return DoStatus.PARTPICKED.getValue();
            }
        }
        return DoStatus.ALLPICKED.getValue();
    }
    
    public boolean existTaskByDo(List<Long> genDoIds, Long regionId, boolean isEa) {
        String sql = "SELECT 1 FROM tsk_pick tp, md_location loc, md_partition mp WHERE " +
                "tp.fm_loc_id = loc.id AND tp.doc_id IN (:doId) AND tp.is_deleted = 0 AND tp.warehouse_id = :warehouseId " +
                "AND loc.partition_id = mp.id AND mp.region_id = :regionId AND loc.warehouse_id = :warehouseId " +
                "AND loc.is_deleted = 0 AND mp.warehouse_id = :warehouseId AND mp.is_deleted = 0 " +
                (isEa ? "AND tp.qty_unit = tp.qty " : "AND tp.qty_unit != tp.qty ");
        Query query = this.createSQLQuery(sql).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).
                setParameterList("doId", genDoIds).setParameter("regionId", regionId);
        return ListUtil.isNotEmpty(query.list());
    }
    
    public List<PickTaskDto> getForPreContainer(Long waveId) {
        String sql  = "SELECT tp.id, tp.lot_id as lotId, tp.fm_loc_id as locId, tp.qty, tp.qty_unit as qtyUnit, sku.id as skuId, " +
                " sku.volume as volume,region.id as regionId FROM tsk_pick tp, md_sku sku,md_location loc,md_partition par,md_region region " +
                " WHERE tp.fm_loc_id = loc.id and loc.partition_id = par.id and region.id = par.region_id " +
                " AND tp.sku_id = sku.id AND tp.is_deleted = 0 AND tp.warehouse_id = :warehouseId AND tp.wave_h_id = :waveId  ORDER BY sku.volume DESC";
        Map<String, Object> params = Maps.newHashMap();
        params.put("waveId", waveId);
        params.put("warehouseId", ParamUtil.getCurrentWarehouseId());
        
        SQLQuery sqlQuery = this.createSQLQuery(sql,params);
        sqlQuery.addScalar("id", Hibernate.LONG).addScalar("lotId",Hibernate.LONG).addScalar("regionId",Hibernate.LONG).addScalar("locId",Hibernate.LONG).addScalar("skuId",Hibernate.LONG);
        sqlQuery.addScalar("qty", Hibernate.BIG_DECIMAL).addScalar("qtyUnit", Hibernate.BIG_DECIMAL).addScalar("volume", Hibernate.BIG_DECIMAL);
        return sqlQuery.setResultTransformer(Transformers.aliasToBean(PickTaskDto.class)).list();
    }

    public List<PickTask> findByContainer(Long skuId, Long locId, String containerNo, String taskStatus, String docNo) {
        String hql = "FROM PickTask pt WHERE  pt.id in ( " +
                " select taskId from PktContainerDetail pd where pd.skuId = :skuId and pd.locId = :locId and " +
                " pd.pktContainerHeader.containerNo = :containerNo and pktContainerHeader.docNo = :docNo " +
                " and pd.warehouseId = :warehouseId) and pt.warehouseId = :warehouseId and pt.status = :taskStatus ";
        Query query = this.createQuery(hql);
        query.setParameter("skuId",skuId);
        query.setParameter("locId",locId);
        query.setParameter("containerNo",containerNo);
        query.setParameter("docNo",docNo);
        query.setParameter("taskStatus",taskStatus);
        query.setParameter("warehouseId",ParamUtil.getCurrentWarehouseId());
        return (List<PickTask>) query.list();
    }

    public BigDecimal findPickTaskByDoIdAndSkuIdForQty(Long doId, Long skuId, String lotatt05) {
        String hql = "select sum(pt.qty) FROM PickTask pt ";
        if (lotatt05 != null) {
            hql += ", StockBatchAtt att ";
        }
        hql += "WHERE pt.skuId = :skuId AND pt.doHeaderId = :doHeaderId and pt.warehouseId = :warehouseId";
        if (lotatt05 != null) {
            hql += " AND pt.lotId = att.id and ifnull(att.lotatt05,'') = :lotatt05 ";
        }
        Query query = this.createQuery(hql);
        query.setParameter("skuId", skuId);
        query.setParameter("doHeaderId", doId);
        if (lotatt05 != null) {
            query.setParameter("lotatt05", lotatt05);
        }
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        BigDecimal result = (BigDecimal) query.uniqueResult();
        return null == result ? BigDecimal.ZERO : result;
    }
}