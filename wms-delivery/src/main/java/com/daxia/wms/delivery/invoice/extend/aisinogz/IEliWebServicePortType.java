package com.daxia.wms.delivery.invoice.extend.aisinogz;


import com.daxia.wms.invoice.entity.ElectroniceInfo;
import com.daxia.wms.invoice.entity.ElectroniceStock;
import com.daxia.wms.invoice.entity.ReturnElectronice;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by the JAX-WS RI. JAX-WS RI 2.1.3-hudson-390-
 * Generated source version: 2.0
 * 
 */
@WebService(name = "IEliWebServicePortType", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com")
public interface IEliWebServicePortType {

	/**
	 * 
	 * @param in0
	 * @return returns
	 *         com.aisinogd.fp.dzfp.companyinterface.hessian.pojo.ReturnElectronice
	 */
	@WebMethod
	@WebResult(name = "out", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com")
	@RequestWrapper(localName = "queryEliData", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com", className = "com.aisinogd.fp.dzfp.companyinterface.webservice.QueryEliData")
	@ResponseWrapper(localName = "queryEliDataResponse", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com", className = "com.aisinogd.fp.dzfp.companyinterface.webservice.QueryEliDataResponse")
	public ReturnElectronice queryEliData(
            @WebParam(name = "in0", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com") ElectroniceInfo in0);

	/**
	 *
	 * @param in0
	 * @return returns
	 *         com.aisinogd.fp.dzfp.companyinterface.hessian.pojo.ReturnElectronice
	 */
	@WebMethod
	@WebResult(name = "out", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com")
	@RequestWrapper(localName = "sendToInvEli", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com", className = "com.aisinogd.fp.dzfp.companyinterface.webservice.SendToInvEli")
	@ResponseWrapper(localName = "sendToInvEliResponse", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com", className = "com.aisinogd.fp.dzfp.companyinterface.webservice.SendToInvEliResponse")
	public ReturnElectronice sendToInvEli(
            @WebParam(name = "in0", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com") ElectroniceInfo in0);

	/**
	 *
	 * @param in0
	 * @return returns
	 *         com.aisinogd.fp.dzfp.companyinterface.hessian.pojo.ElectroniceStock
	 */
	@WebMethod
	@WebResult(name = "out", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com")
	@RequestWrapper(localName = "queryEliStock", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com", className = "com.aisinogd.fp.dzfp.companyinterface.webservice.QueryEliStock")
	@ResponseWrapper(localName = "queryEliStockResponse", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com", className = "com.aisinogd.fp.dzfp.companyinterface.webservice.QueryEliStockResponse")
	public ElectroniceStock queryEliStock(
            @WebParam(name = "in0", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com") String in0);

	/**
	 *
	 * @param in0
	 * @return returns
	 *         com.aisinogd.fp.dzfp.companyinterface.hessian.pojo.ReturnElectronice
	 */
	@WebMethod
	@WebResult(name = "out", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com")
	@RequestWrapper(localName = "invEli", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com", className = "com.aisinogd.fp.dzfp.companyinterface.webservice.InvEli")
	@ResponseWrapper(localName = "invEliResponse", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com", className = "com.aisinogd.fp.dzfp.companyinterface.webservice.InvEliResponse")
	public ReturnElectronice invEli(
            @WebParam(name = "in0", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com") ElectroniceInfo in0);

}
