package com.daxia.wms.delivery.deliveryorder.job;

import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.PageConfig;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateService;
import com.daxia.wms.delivery.deliveryorder.service.impl.DoAllocateServiceImpl;
import com.daxia.wms.master.job.AbstractJob;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * 自动分配Do的定时任务
 */
@Name("autoAllocateSingleJob2")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class AutoAllocateSingleJob2 extends AbstractJob {

    // 一次分配订单的数量
    private static final int DEFAULT_BATCH_ALLOCATE_NUM = 20;

    private List<Long> getAlcHeaderIds(int batchAllocateNum,Long warehouseId) {
        DoAllocateService service = ((DoAllocateService) Component.getInstance(DoAllocateServiceImpl.class));
        return service.queryNeedAllocateDoHeaderIds(this.getMultiThreadNum() * batchAllocateNum,warehouseId);
    }

    /**
     * 分配线程数
     */
    private int getMultiThreadNum() {
        Integer nThreads = SystemConfig.getConfigValueInt("delivery.allocateJob.multiThreadNum", null);
        if (nThreads == null || nThreads < 1 || nThreads > 10) {
            nThreads = 1;
        }

        return nThreads;
    }

    /**
     * 筛选满足条件的DO进行自动分配
     */
    @Override
    protected void doRun() throws InterruptedException {
        List<Long> whIdList = new ArrayList<Long>();
        String cfg = PageConfig.get("job.except.wh.id", Config.ConfigLevel.GLOBAL.getValue());
        if (StringUtil.isNotEmpty(cfg)){
            String[] kk = cfg.split(",");
            if(kk.length>1){
                whIdList.add(Long.valueOf(cfg.split(",")[1]));
            }
        }

        int batchAllocateNum = DEFAULT_BATCH_ALLOCATE_NUM;
        if (!ListUtil.isNullOrEmpty(whIdList)) {
            for (Long whId : whIdList) {
                // 需要分配的alcHeaderId
                List<Long> alcHeaderIds = getAlcHeaderIds(batchAllocateNum,whId);
                if (CollectionUtils.isNotEmpty(alcHeaderIds)) {
                    log.debug("alc header id size:" + alcHeaderIds.size());
                    // 实际线程数
                    // (int)Math.ceil((double)dividend/divisor)
                    int threadNum = (alcHeaderIds.size() + batchAllocateNum - 1) / batchAllocateNum;

                    if (threadNum == 1) {
                        // 单线程分配
                        log.debug("Single thread allocation");

                        AutoAllocateExecutor executor = ((AutoAllocateExecutor) Component.getInstance(AutoAllocateExecutor.class));
                        executor.doAllocate(alcHeaderIds,null);
                    } else {
                        // 多线程分配
                        log.debug("Multiple threads allocation with threadNum: " + threadNum);
                        CountDownLatch threadSignal = new CountDownLatch(threadNum);
                        List<List<Long>> partitions = Lists.partition(alcHeaderIds, batchAllocateNum);
                        for (int i = 0; i < partitions.size(); i++) {
                            new AutoAllocateTask(i, partitions.get(i), threadSignal,whId).start();
                        }
                        threadSignal.await();
                    }
                }
            }
        }

    }
}