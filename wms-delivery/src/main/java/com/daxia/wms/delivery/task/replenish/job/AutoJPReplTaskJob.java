package com.daxia.wms.delivery.task.replenish.job;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.util.List;

import com.daxia.wms.delivery.task.replenish.service.ReplenishTaskGenerateContext;
import com.daxia.wms.util.AlarmUtil;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.jboss.seam.Component;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.log.Log;


import com.daxia.framework.common.util.MailUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.delivery.task.replenish.service.ReplHeaderService;
import com.daxia.wms.Constants;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.master.service.impl.WarehouseServiceImpl;
import com.daxia.wms.stock.StockException;

@Name("autoJPReplTaskJob")
@AutoCreate
@lombok.extern.slf4j.Slf4j
public class AutoJPReplTaskJob {
	@In
    private ReplHeaderService replHeaderService;

    @In
    private ReplenishTaskGenerateContext replenishTaskGenerateContext;
    
    private static volatile boolean running = false;



    public void run() {
        if (running) {
            log.debug("AutoJPReplTaskJob Run Already. ");

            return;
        }

        running();
        log.debug("Begin Run AutoJPReplTaskJob, Please Waiting ............... ");

        List<Warehouse> whList = loadRunningWh();
        if (whList != null) {
            for (Warehouse wh : whList) {
                try {
                    ParamUtil.setCurrentWarehouseId(wh.getId());
                    replenishTaskGenerateContext.createJPReplenishTask(null,null,Boolean.TRUE);
                    replHeaderService.addReplTask(Constants.ReplType.JP.getValue(),null,Boolean.TRUE);
                } catch (Exception e) {
                        boolean notIgnoreException = !ignoreException(e);
                  
                        if(notIgnoreException){
                            log.error("Auto Publish JPRepl Task Error:", e);
                        }
                        
                        if (e instanceof HibernateException) {
						    try {
						        log.debug("Auto Publish JPRepl error,rollback begin, warehouse Id is:"
						        + ParamUtil.getCurrentWarehouseId());
						        Session session = (Session) Component.getInstance("hibernateSession");
						        session.getTransaction().rollback();
						        log.debug("Auto Publish JPRepl error,rollback end,warehouse Id is:"
						              + ParamUtil.getCurrentWarehouseId());
						    } catch (Exception e2) {
						    	log.error("Auto Publish JPRepl error,rollback failed,warehouse Id is:"
						                + ParamUtil.getCurrentWarehouseId(), e2);
						    }
						}
    
                    if (notIgnoreException) {
                        ByteArrayOutputStream bs = new ByteArrayOutputStream();
                        PrintStream ps = new PrintStream(bs);
                        try {
                            e.printStackTrace(ps);
            
                            String content = "自动即时补货异常：" + bs.toString();
                            AlarmUtil.sendEmail("自动即时补货失败", content);
                        } catch (Exception e1) {
                            log.error("Send mail error: ", e1);
                        } finally {
                            ps.close();
            
                        }
                    }
                }
                Session session = (Session) Component.getInstance("hibernateSession");
                session.clear();
                
                ParamUtil.setCurrentWarehouseId(null);
            }
        }

        unRunning();
	}

    private boolean ignoreException(Exception e) {
        return e instanceof StockException && StockException.NO_INIT_REPL_TASK_EXIST.equals(e.getMessage());
    }

    //加载所有运行状态的仓库信息，并catch异常
    private List<Warehouse> loadRunningWh() {
        try {
            WarehouseService warehouseService = ((WarehouseService) Component.getInstance(WarehouseServiceImpl.class));
            return warehouseService.getRunningWh();
        } catch (Exception e) {
            log.error("Component Instance Error:", e);
        }
        return null;
    }

    private static void running() {
        running = true;
    }

    private static void unRunning() {
        running = false;
    }
}