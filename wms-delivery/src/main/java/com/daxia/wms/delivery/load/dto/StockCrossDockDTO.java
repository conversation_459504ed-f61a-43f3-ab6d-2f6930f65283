package com.daxia.wms.delivery.load.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.daxia.wms.stock.stock.entity.StockCrossDock;

@lombok.extern.slf4j.Slf4j
public class StockCrossDockDTO implements Serializable {

	private static final long serialVersionUID = -6993029847380298728L;
	
	private Long id ;
	
	private String productCode;//商品编码

	private String ean13;//商品条码
	
	private String productName;//商品条码
	
	private BigDecimal qty;//数量
	
	private String lotatt01;//生产日期
	
	private String lotatt02;//失效日期
	
	private String lotatt05;//批号
	
	private String asnferNo1;//PO
	
	private String lotNo;//批次号
	
	private String lpnNo;//LPN号
	
	private Long sn_qty;//是否需要序列号
	
	private String prodcutType;//产品类型
	
	private Long skuId;//产品ID
	
	private List<String> serialNos;//序列号

	private Long lotId; //批次
	
	private Long cddid; //CrossDockDetail ID
	
	private String locType; //库位类型
	
	private String status; //状态
	
	private Long locId; //库位

	private int localStatus; //状态
	
	private String asnNo; //ASN编号
	
	private StockCrossDock stockCrossDock;//越库库存记录，用于保存校验时用
	
	private Integer isScanned;

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	public String getProductCode() {
		return productCode;
	}
	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}
	
	public String getEan13() {
		return ean13;
	}
	public void setEan13(String ean13) {
		this.ean13 = ean13;
	}
	
	public BigDecimal getQty() {
		return qty;
	}
	public void setQty(BigDecimal qty) {
		this.qty = qty;
	}

	public String getProductName() {
		return productName;
	}
	public void setProductName(String productName) {
		this.productName = productName;
	}
	
	public String getLotatt01() {
		return lotatt01;
	}
	public void setLotatt01(String lotatt01) {
		this.lotatt01 = lotatt01;
	}
	
	public String getLotatt02() {
		return lotatt02;
	}
	public void setLotatt02(String lotatt02) {
		this.lotatt02 = lotatt02;
	}
	
	public String getLotatt05() {
		return lotatt05;
	}
	public void setLotatt05(String lotatt05) {
		this.lotatt05 = lotatt05;
	}
	
	public String getAsnferNo1() {
		return asnferNo1;
	}
	public void setAsnferNo1(String asnferNo1) {
		this.asnferNo1 = asnferNo1;
	}
	
	public String getLotNo() {
		return lotNo;
	}
	public void setLotNo(String lotNo) {
		this.lotNo = lotNo;
	}
	
	public String getLpnNo() {
		return lpnNo;
	}
	public void setLpnNo(String lpnNo) {
		this.lpnNo = lpnNo;
	}
	
	public Long getSn_qty() {
		return sn_qty;
	}
	public void setSn_qty(Long sn_qty) {
		this.sn_qty = sn_qty;
	}
	
	public String getProdcutType() {
		return prodcutType;
	}
	public void setProdcutType(String prodcutType) {
		this.prodcutType = prodcutType;
	}
	
	public Long getSkuId() {
		return skuId;
	}
	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}
	
	public List<String> getSerialNos() {
		return serialNos;
	}
	public void setSerialNos(List<String> serialNos) {
		this.serialNos = serialNos;
	}
	
	public Long getLotId() {
		return lotId;
	}
	public void setLotId(Long lotId) {
		this.lotId = lotId;
	}
	
	public Long getCddid() {
		return cddid;
	}
	public void setCddid(Long cddid) {
		this.cddid = cddid;
	}
	
	public String getLocType() {
		return locType;
	}
	public void setLocType(String locType) {
		this.locType = locType;
	}
	
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	
	public Long getLocId() {
		return locId;
	}
	public void setLocId(Long locId) {
		this.locId = locId;
	}
	
	public int getLocalStauts() {
		return localStatus;
	}
	public void setLocalStatus(int localStatus) {
		this.localStatus = localStatus;
	}
	
	public String getAsnNo() {
		return asnNo;
	}
	public void setAsnNo(String asnNo) {
		this.asnNo = asnNo;
	}
	
	public StockCrossDock getStockCrossDock() {
		return stockCrossDock;
	}
	public void setStockCrossDock(StockCrossDock stockCrossDock) {
		this.stockCrossDock = stockCrossDock;
	}
	public Integer getIsScanned() {
		return isScanned;
	}
	public void setIsScanned(Integer isScanned) {
		this.isScanned = isScanned;
	}
}
