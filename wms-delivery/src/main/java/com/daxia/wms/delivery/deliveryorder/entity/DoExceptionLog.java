package com.daxia.wms.delivery.deliveryorder.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;

/**
 * DO异常日志记录实体
 */
@Entity
@Table(name = "doc_exception_log")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@lombok.extern.slf4j.Slf4j
public class DoExceptionLog extends WhBaseEntity  {

	private static final long serialVersionUID = -5515062042653375859L;
	private Long id; 
	private Long doHeaderId;
	private String doNo;
	private Long carrierId;
	private String doType; 
	private String fmDoStatus;
	private String toDoStatus;
	private Date holdTime;
	private String holdReason;
	private String fmReleaseStatus;
	private String toReleaseStatus;
	private String operator  ;//操作人
	private String fmExceptionStatus;
	private String toExceptionStatus;
	private String notes;
	private String operationType;
	
	@Id
    @Column(name = "ID")
	@GeneratedValue(strategy = GenerationType.AUTO) 
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	@Column(name = "DO_HEADER_ID")
	public Long getDoHeaderId() {
		return doHeaderId;
	}
	public void setDoHeaderId(Long doHeaderId) {
		this.doHeaderId = doHeaderId;
	}
	
	@Column(name = "DO_NO")
	public String getDoNo() {
		return doNo;
	}
	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}
	
	@Column(name = "CARRIER_ID")
	public Long getCarrierId() {
		return carrierId;
	}
	public void setCarrierId(Long carrierId) {
		this.carrierId = carrierId;
	}
	
	@Column(name = "DO_TYPE")
	public String getDoType() {
		return doType;
	}
	public void setDoType(String doType) {
		this.doType = doType;
	}
	
	@Column(name = "FROM_DOSTATUS")
	public String getFmDoStatus() {
		return fmDoStatus;
	}
	public void setFmDoStatus(String fmDoStatus) {
		this.fmDoStatus = fmDoStatus;
	}
	
	@Column(name = "TO_DOSTATUS")
	public String getToDoStatus() {
		return toDoStatus;
	}
	public void setToDoStatus(String toDoStatus) {
		this.toDoStatus = toDoStatus;
	}
	
	@Column(name = "HOLD_TIME")
	public Date getHoldTime() {
		return holdTime;
	}
	public void setHoldTime(Date holdTime) {
		this.holdTime = holdTime;
	}
	
	@Column(name = "HOLD_REASON")
	public String getHoldReason() {
		return holdReason;
	}
	public void setHoldReason(String holdReason) {
		this.holdReason = holdReason;
	}
	
	@Column(name = "FROM_RELEASESTATUS")
	public String getFmReleaseStatus() {
		return fmReleaseStatus;
	}
	public void setFmReleaseStatus(String fmReleaseStatus) {
		this.fmReleaseStatus = fmReleaseStatus;
	}
	
	@Column(name = "TO_RELEASESTATUS")
	public String getToReleaseStatus() {
		return toReleaseStatus;
	}
	public void setToReleaseStatus(String toReleaseStatus) {
		this.toReleaseStatus = toReleaseStatus;
	}
	
	@Column(name = "OPERATION_ID")
	public String getOperator() {
		return operator;
	}
	public void setOperator(String operator) {
		this.operator = operator;
	}
	
	@Column(name = "FROM_EXCEPTION_STATUS")
	public String getFmExceptionStatus() {
		return fmExceptionStatus;
	}
	public void setFmExceptionStatus(String fmExceptionStatus) {
		this.fmExceptionStatus = fmExceptionStatus;
	}
	
	@Column(name = "TO_EXCEPTIONSTATUS")
	public String getToExceptionStatus() {
		return toExceptionStatus;
	}
	public void setToExceptionStatus(String toExceptionStatus) {
		this.toExceptionStatus = toExceptionStatus;
	}
	
	@Column(name = "NOTES")
	public String getNotes() {
		return notes;
	}
	public void setNotes(String notes) {
		this.notes = notes;
	}
    
    @Column(name = "OPERATION_TYPE")
    public String getOperationType() {
        return operationType;
    }
    
    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }
}