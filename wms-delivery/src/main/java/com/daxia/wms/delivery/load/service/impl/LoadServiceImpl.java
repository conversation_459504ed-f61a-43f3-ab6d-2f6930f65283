package com.daxia.wms.delivery.load.service.impl;

import com.alibaba.fastjson.JSON;
import com.daxia.dubhe.api.ApiException;
import com.daxia.dubhe.api.DubheResponse;
import com.daxia.dubhe.api.erp.entity.DeliveryEventBatchInfo;
import com.daxia.dubhe.api.erp.entity.DeliveryEventSyncInfo;
import com.daxia.dubhe.api.erp.request.DeliveryEventSyncRequest;
import com.daxia.dubhe.api.internal.util.NumberUtils;
import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.cfg.AutoLoadAndDeliverCfg;
import com.daxia.framework.common.log.LogUtil;
import com.daxia.framework.common.log.StopWatch;
import com.daxia.framework.common.service.ReportGenerator;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.security.ExtendedIdentity;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.framework.system.service.TenantService;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.ContainerType;
import com.daxia.wms.Constants.*;
import com.daxia.wms.Keys;
import com.daxia.wms.OrderLogConstants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.delivery.deliveryorder.dao.DoDetailDAO;
import com.daxia.wms.delivery.deliveryorder.dao.MergePrintHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.filter.DoHeaderFilter;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.OrderLogService;
import com.daxia.wms.delivery.load.dao.LoadDAO;
import com.daxia.wms.delivery.load.dao.LoadDetailDAO;
import com.daxia.wms.delivery.load.dao.LoadHisDAO;
import com.daxia.wms.delivery.load.dao.ReShipDoDAO;
import com.daxia.wms.delivery.load.dto.DoLoadReportDTO;
import com.daxia.wms.delivery.load.dto.LoadScanDTO;
import com.daxia.wms.delivery.load.dto.PrintLoadDTO;
import com.daxia.wms.delivery.load.dto.PrintLoadDetailDTO;
import com.daxia.wms.delivery.load.entity.LoadDetail;
import com.daxia.wms.delivery.load.entity.LoadHeader;
import com.daxia.wms.delivery.load.entity.LoadHeaderHis;
import com.daxia.wms.delivery.load.entity.ReShipDo;
import com.daxia.wms.delivery.load.filter.LoadDetailFilter;
import com.daxia.wms.delivery.load.filter.LoadFilter;
import com.daxia.wms.delivery.load.service.FlowLoadService;
import com.daxia.wms.delivery.load.service.LoadService;
import com.daxia.wms.delivery.load.service.OverdueRtvService;
import com.daxia.wms.delivery.pick.service.PickTaskService;
import com.daxia.wms.delivery.print.dto.LoadPrint;
import com.daxia.wms.delivery.print.dto.LoadPrintSub;
import com.daxia.wms.delivery.recheck.dao.CartonHeaderDAO;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.filter.CartonHeaderFilter;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.recheck.service.EmsWayBillUpdateService;
import com.daxia.wms.exp.delivery.srv.TmsRewriteExpSrv;
import com.daxia.wms.exp.receive.handler.DeliveryEventDubheHandler;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.exp.stock.srv.ShipInfo4TmsExpSrv;
import com.daxia.wms.exp.stock.srv.StockExpSrv;
import com.daxia.wms.exp.sys.base.util.ExpSrvUtil;
import com.daxia.wms.master.MasterException;
import com.daxia.wms.master.dao.CarrierDAO;
import com.daxia.wms.master.dao.ShopInfoDAO;
import com.daxia.wms.master.entity.*;
import com.daxia.wms.master.service.CarrierService;
import com.daxia.wms.master.service.MerchantService;
import com.daxia.wms.master.service.SupplierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.print.PrintConstants;
import com.daxia.wms.stock.stock.dao.TrsTransactionLogDAO;
import com.daxia.wms.stock.stock.entity.StockBatchAtt;
import com.daxia.wms.stock.stock.entity.TrsPickLog;
import com.daxia.wms.stock.stock.service.TrsPickLogService;
import com.daxia.wms.util.AlarmUtil;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.daxia.framework.system.util.FileExportUtil.exportCSV;

/**
 * Description:交接业务Service实现类
 */
@Name("com.daxia.wms.delivery.loadService")
@lombok.extern.slf4j.Slf4j
public class LoadServiceImpl implements LoadService {
    @In
    private LoadDAO loadDAO;
    @In
    private LoadHisDAO loadHisDAO;
    @In
    private ReShipDoDAO reShipDoDAO;
    @In
    private LoadDetailDAO loadDetailDAO;
    @In
    private DeliveryOrderService deliveryOrderService;
    @In
    private CartonService cartonService;
    @In
    private FlowLoadService flowLoadService;
    @In
    protected StockExpSrv stockExpSrv;
    @In
    protected ShipInfo4TmsExpSrv shipInfo4TmsExpSrv;

    @In
    private ContainerMgntService containerMgntService;
    @In
    private SequenceGeneratorService sequenceGeneratorService;
    @In
    private CartonHeaderDAO cartonHeaderDAO;

    @In(create = true, required = true)
    private TrsTransactionLogDAO trsTransactionLogDAO;
    @In
    private ReportGenerator reportGenerator;
    @In
    private PickTaskService pickTaskService;
    @In
    private ExpFacadeService expFacadeService;
    @In
    private WarehouseService warehouseService;
    @In
    CarrierService carrierService;
    @In
    private ExtendedIdentity identity;
    @In
    private OverdueRtvService overdueRtvService;

    @In
    private OrderLogService orderLogService;

    @In
    private TenantService tenantService;

    @In
    private DeliveryEventDubheHandler deliveryEventDubheHandler;

    @In
    private ShopInfoDAO shopInfoDAO;

    @In
    private CarrierDAO carrierDAO;

    @In
    private TrsPickLogService trsPickLogService;

    @In
    private EmsWayBillUpdateService emsWayBillUpdateService;
    @In
    private TmsRewriteExpSrv tmsRewriteExpSrv;
    @In
    private MergePrintHeaderDAO mergePrintHeaderDAO;

    @In
    private SupplierService supplierService;

    @In
    private MerchantService merchantService;

    @In
    private DoDetailDAO doDetailDAO;

    public static enum LoadReport {
        NAME("load"), SUB_NAME("loadDetail");

        private String value;

        LoadReport(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public LoadServiceImpl() {
    }

    /**
     * 保存或更新
     */
    @Override
    @Transactional
    public LoadHeader saveOrUpdate(LoadHeader loadHeader) throws DeliveryException {
        //交接状态不能手动修改,因为新增预处理时候必须给一个初始化的状态
        if (StringUtil.isEmpty(loadHeader.getStatus())) {
            loadHeader.setStatus(Constants.LoadStatus.INITIAL.getValue());
        }

        loadHeader = (LoadHeader) loadDAO.getSession().merge(loadHeader);
    
        if (StringUtil.isIn(loadHeader.getLoadType(), LoadType.WHOLESALE.getValue(), LoadType.WL.getValue())) {
            if (LoadHeader.DivideType.CARRIER.getV().equals(loadHeader.getDivideType()) && loadHeader.getCarrierId() == null) {
                throw new DeliveryException(DeliveryException.CARRIER_IS_NOT_EXIST);
            } else if (LoadHeader.DivideType.LINE.getV().equals(loadHeader.getDivideType()) && loadHeader.getLineId() == null) {
                throw new DeliveryException(MasterException.LINE_NAME_IS_NULL);
            }
        }

        //交接单已有交接明细则不允许修改
        if (loadHeader.getId() != null) {
            List<LoadDetail> loadDetails = loadHeader.getLoadDetails();
            if (!ListUtil.isNullOrEmpty(loadDetails)) {
                throw new DeliveryException(DeliveryException.IS_LOADING_CANNOT_UPDATE);
            }
        }
        if (loadHeader.getIsAuto() == null) {
            loadHeader.setIsAuto(LoadMode.MANUAL.getValue());
        }
        this.loadDAO.saveOrUpdate(loadHeader);
        return loadHeader;
    }

    /**
     * 取消交接单,页面的取消按钮
     */
    @Override
    @Transactional
    public void remove(LoadHeader loadHeader) throws DeliveryException {
        // 如果交接单为空,则不能取消,抛出异常
        if (loadHeader == null) {
            throw new DeliveryException(DeliveryException.LOAD_HEADER_NOTEXIST);
        }
        // 交接单不为初始化状态不能取消
        if (!loadHeader.getStatus().equalsIgnoreCase(Constants.LoadStatus.INITIAL.getValue())) {
            throw new DeliveryException(DeliveryException.CANCLE_HEADER_FAILED);
        }
        this.loadDAO.remove(loadHeader);
    }

    /**
     * 分页查询
     */
    @Override
    public DataPage<LoadHeader> query(LoadFilter loadFilter, int startIndex, int pageSize) {
        return loadDAO.findRangeByFilter(loadFilter, startIndex, pageSize);
    }

    /**
     * 分布查询历史数据
     */
    @Override
    public DataPage<LoadHeaderHis> queryHis(LoadFilter loadFilter, int startIndex, int pageSize) {
        List<LoadHeaderHis> loadHeaderHis = loadHisDAO.findLoadHeaderHis(loadFilter, startIndex, pageSize, true);
        DataPage<LoadHeaderHis> dataPage = new DataPage<LoadHeaderHis>(loadHeaderHis.size(), startIndex, pageSize, loadHeaderHis);
        return dataPage;
    }

    /**
     * 通过主键获取实体
     */
    @Override
    public LoadHeader getLoadHeader(Long loadHeaderId) {
        return loadDAO.get(loadHeaderId);
    }
    
    @Override
    public LoadHeader getLoadHeader(String loadNo) {
        if (StringUtil.isEmpty(loadNo)) {
            return null;
        }
    
        List list = loadDAO.find(ImmutableMap.<String, Object>of("loadNo", loadNo));
        if (ListUtil.isNotEmpty(list) && list.size() == 1) {
            return (LoadHeader) list.get(0);
        }
    
        return null;
    }
    
    /**
     * 通过主键获取实体
     */
    @Override
    public LoadHeader getLoadHeaderHis(Long loadHeaderId) {
        LoadHeaderHis loadHeaderHis = loadHisDAO.get(loadHeaderId);
        if (loadHeaderHis == null) {
            return null;
        }
        LoadHeader loadHeader = new LoadHeader();
        loadHeader.setCarrier(loadHeaderHis.getCarrier());
        loadHeader.setCarrierId(loadHeaderHis.getCarrierId());
        loadHeader.setCartonQty(loadHeaderHis.getCartonQty());
        loadHeader.setContact(loadHeaderHis.getContact());
        loadHeader.setContactId(loadHeaderHis.getContactId());
        loadHeader.setContactMobile(loadHeaderHis.getContactMobile());
        loadHeader.setCreatedAt(loadHeaderHis.getCreatedAt());
        loadHeader.setCreatedBy(loadHeaderHis.getCreatedBy());
        loadHeader.setDeliveryTime(loadHeaderHis.getDeliveryTime());
        loadHeader.setDocQty(loadHeaderHis.getDocQty());
        loadHeader.setDriverName(loadHeaderHis.getDriverName());
        loadHeader.setId(loadHeaderHis.getId());
        loadHeader.setIsAuto(loadHeaderHis.getIsAuto());
        loadHeader.setLoadNo(loadHeaderHis.getLoadNo());
        loadHeader.setLoadType(loadHeaderHis.getLoadType());
        loadHeader.setLockTime(loadHeaderHis.getLockTime());
        loadHeader.setNotes(loadHeaderHis.getNotes());
        loadHeader.setStatus(loadHeaderHis.getStatus());
        loadHeader.setSupplierId(loadHeaderHis.getSupplierId());
        loadHeader.setTotalCartonQty(loadHeaderHis.getTotalCartonQty());
        loadHeader.setTotalGrossWeight(loadHeaderHis.getTotalGrossWeight());
        loadHeader.setTotalNetWeight(loadHeaderHis.getTotalNetWeight());
        loadHeader.setTotalVolume(loadHeaderHis.getTotalVolume());
        loadHeader.setTranInWhID(loadHeaderHis.getTranInWhID());
        loadHeader.setUpdatedAt(loadHeaderHis.getUpdatedAt());
        loadHeader.setUpdatedBy(loadHeaderHis.getUpdatedBy());
        loadHeader.setVechileNo(loadHeaderHis.getVechileNo());
        loadHeader.setVersion(loadHeaderHis.getVersion());
        return loadHeader;

    }

    /**
     * 更新交接单
     */
    @Transactional
    public void update(LoadHeader loadHeader) {
        this.loadDAO.update(loadHeader);
    }

    /**
     * 验证交接单已扫描的箱数是否大于配置的最大交接数
     *
     * @param loadHeader
     */
    private void checkMaxLoadCount(LoadHeader loadHeader,int cartonNum) {
        Integer configValue = Config.getIntFmJson(Keys.Delivery.auto_load_and_deliver_cfg, Config.ConfigLevel.WAREHOUSE, AutoLoadAndDeliverCfg.cartonCount);
        if (configValue == null) {
            configValue = 100;
        }
        if (loadHeader.getCartonQty() + cartonNum > configValue) { // 已扫描的箱数大于XX，不允许继续
            throw new DeliveryException(DeliveryException.TOO_MANY_CARTONS_IN_A_CAR, configValue);
        }
    }

    /**
     * <pre>
     * do单的类型必须与交接单的类型匹配
     * </pre>
     *
     * @param doHeader
     * @param loadHeader
     */
    @Override
    public void checkDoType(LoadHeader loadHeader, DeliveryOrderHeader doHeader, String cartonNo) {
        String loadType = loadHeader.getLoadType();
        String doType = doHeader.getDoType();
        if (Constants.LoadType.DO.getValue().equals(loadType)) {
            if (!(Constants.DoType.SELL.getValue().equals(doType))) {
                throw new DeliveryException(DeliveryException.DO_TYPE_DONOT_MATCH_LOAD_TYPE, cartonNo);
            }
        } else if (Constants.LoadType.RTV.getValue().equals(loadType)) {
            if (!Constants.DoType.RTV.getValue().equals(doType)) {
                throw new DeliveryException(DeliveryException.DO_TYPE_DONOT_MATCH_LOAD_TYPE, cartonNo);
            }
            //核对供应商
            if (!CompareUtil.compare(doHeader.getSupplierId(), loadHeader.getSupplierId())) {
                throw new DeliveryException(DeliveryException.DO_SUPPLIER_DONOT_MATCH_LOAD_SUPPLIER, cartonNo);
            }
        } else if (Constants.LoadType.TT.getValue().equals(loadType)) {
            if (!Constants.DoType.ALLOT.getValue().equals(doType)) {
                throw new DeliveryException(DeliveryException.DO_TYPE_DONOT_MATCH_LOAD_TYPE, cartonNo);
            }
            //核对目标仓库
            if (!CompareUtil.compare(Long.valueOf(doHeader.getEdi2()), loadHeader.getTranInWhID())) {
                throw new DeliveryException(DeliveryException.DO_TRAN_IN_WH_DONOT_MATCH_LOAD_TRAN_IN_WH, cartonNo);
            }
        } else if (Constants.LoadType.WL.getValue().equals(loadType)) {
            //仓库发货，校验发货单类型、配送商是否一致
            if (!(Constants.DoType.SELL.getValue().equals(doType))) {
                throw new DeliveryException(DeliveryException.DO_TYPE_DONOT_MATCH_LOAD_TYPE, cartonNo);
            }
    
            if (LoadHeader.DivideType.CARRIER.getV().equals(loadHeader.getDivideType())) {
                if (!doHeader.getCarrierId().equals(loadHeader.getCarrierId())) {
                    throw new DeliveryException(DeliveryException.CARRIER_DONOT_MATCH_LOAD_CARRIER);
                }
            } else {
                Long lineId = deliveryOrderService.getLineId(doHeader.getId());
                if (lineId == null || !lineId.equals(loadHeader.getLineId())) {
                    throw new DeliveryException(DeliveryException.LINE_DONOT_MATCH_LOAD_LINE);
                }
            }
        } else if (Constants.LoadType.WHOLESALE.getValue().equals(loadType)) {
            //批发发货，校验发货单类型、配送商是否一致
            if (!(DoType.WHOLESALE.getValue().equals(doType))) {
                throw new DeliveryException(DeliveryException.DO_TYPE_DONOT_MATCH_LOAD_TYPE, cartonNo);
            }
    
            if (LoadHeader.DivideType.CARRIER.getV().equals(loadHeader.getDivideType())) {
                if (!doHeader.getCarrierId().equals(loadHeader.getCarrierId())) {
                    throw new DeliveryException(DeliveryException.CARRIER_DONOT_MATCH_LOAD_CARRIER);
                }
            } else {
                Long lineId = deliveryOrderService.getLineId(doHeader.getId());
                if (lineId == null || !lineId.equals(loadHeader.getLineId())) {
                    throw new DeliveryException(DeliveryException.LINE_DONOT_MATCH_LOAD_LINE);
                }
            }
        }
    }

    /**
     * 交接锁定
     */
    @Override
    @Transactional
    public LoadHeader lockLoadOrder(LoadHeader loadHeader) throws DeliveryException {
        //判断loadHeader状态，交接中状态可以锁定。
        loadHeader = loadDAO.get(loadHeader.getId());
        if (!Constants.LoadStatus.LOADING.getValue().equals(loadHeader.getStatus())) {
            throw new DeliveryException(DeliveryException.STATUS_ERROR_CAN_NOT_LOCK_LOAD_ORDER);
        }
        loadHeader.setStatus(Constants.LoadStatus.COMPLETED.getValue());
        loadHeader.setLockTime(DateUtil.getNowTime());
        loadDAO.saveOrUpdate(loadHeader);
        return loadHeader;
    }

    /**
     * 锁定后发货
     */
    @Override
    @Transactional
    public LoadHeader completeLoad(LoadHeader loadHeader) throws DeliveryException {
        //判断loadHeader状态，交接中状态可以锁定。
        loadHeader = loadDAO.get(loadHeader.getId());
        if (!Constants.LoadStatus.COMPLETED.getValue().equals(loadHeader.getStatus())) {
            throw new DeliveryException(DeliveryException.STATUS_ERROR_CAN_NOT_LOCK_LOAD_ORDER);
        }
        loadHeader.setStatus(Constants.LoadStatus.DELIVERYED.getValue());
        loadHeader.setDeliveryTime(DateUtil.getNowTime());
        loadDAO.saveOrUpdate(loadHeader);
        return loadHeader;
    }

    /**
     * do发货wms的逻辑
     *
     * @param doHeader
     * @param autoParam 配置 是否自动发货 及 发货时间。<br/>配置自动发货：<br/>
     *                  autoParam.put(LoadService.IS_AUTO, YesNo.YES.getValue());<br/>
     *                  autoParam.put(LoadService.SHIP_TIME, WCS调用DTS执行自动发货的时间-Date类型);
     */
    @Override
    @Transactional
    @Loggable
    public void deliver(DeliveryOrderHeader doHeader, Map<String, Object> autoParam) throws DeliveryException {
        String updateUser = (String) autoParam.get(LoadService.OPERATOR);
        log.debug("deliver: doHeader = " + doHeader.getId() + ",updateUser = " + updateUser);
        // 扣除库存；
        this.changeStock(doHeader.getId(), updateUser);
        pickTaskService.deliveryPktTaskByDoHeader(doHeader.getId(), updateUser);
        // 更新明细
        loadDAO.updateDoDetailByDoHeaderId(doHeader.getId(), Constants.DoStatus.ALL_DELIVER.getValue());
        Integer updateDoCounts;
        // 更新头，自动发货需记录 发货时间 为 WCS调用DTS执行自动发货的时间
        if (checkAutoParam(autoParam)) {
            updateDoCounts = loadDAO.autoDeliverDo(doHeader, (Date) autoParam.get(SHIP_TIME));
        } else {
            updateDoCounts = loadDAO.updateDoHeaderById(doHeader, Constants.DoStatus.ALL_DELIVER.getValue());
        }
        if (!YesNo.YES.getValue().equals(updateDoCounts)) {
            throw new DeliveryException(DeliveryException.DO_ALREADY_MODIFIED_BY_OTHER);
        }

        //RTV更细预约信息。
        if (DoType.RTV.getValue().equals(doHeader.getDoType())) {
            if (loadDAO.isOverDueRtvLoadHeaer(doHeader.getId())) {
                overdueRtvService.createOverdueRtvs(doHeader.getId());
            }
        }
        // 基于pickTask写发货交易
        trsTransactionLogDAO.createDeliveryTransaction(updateUser, doHeader.getId(), doHeader.getDoNo());
        if(Config.isDefaultFalse(Keys.Delivery.insertCartonRewriteMsg,Config.ConfigLevel.WAREHOUSE)){
            cartonHeaderDAO.insertCartonRewriteMsg(doHeader.getId());
        }
        //发货时tms订单更新接口
        doHeader = deliveryOrderService.getDoHeaderById(doHeader.getId());
        // 调用库存回写接口
        stockExpSrv.createDoShipOmsMsg(doHeader.getId());

        expFacadeService.sendDo2OmsCreateDatas(doHeader.getId(), Constants.DoStatus.ALL_DELIVER.getValue(), 1, null, doHeader.getDoType());
        expFacadeService.sendDoSnLog2OmsCreateDatas(doHeader.getId(), doHeader.getDoNo());
        expFacadeService.sendBatchQty2WmsCreateDatas(doHeader.getId(), doHeader.getDoType());
    }

    /**
     * 检查配置自动发货这个特殊参数的正确性
     *
     * @param autoParam
     * @return
     */
    private boolean checkAutoParam(Map<String, Object> autoParam) {
        if (autoParam != null && YesNo.YES.getValue().equals(autoParam.get(IS_AUTO))) {
            if (autoParam.get(SHIP_TIME) instanceof Date) {
                return true;
            }
        }
        return false;
    }

    /**
     * do发货；自动发货
     *
     * @param doHeader
     * @param autoParam 配置 是否自动发货 及 发货时间。<br/>配置自动发货：<br/>
     *                  autoParam.put(LoadService.IS_AUTO, YesNo.YES.getValue());<br/>
     *                  autoParam.put(LoadService.SHIP_TIME, WCS调用DTS执行自动发货的时间-Date类型);
     */
    @Override
    @Transactional
    public void invokeAutoDeliver(DeliveryOrderHeader doHeader, Map<String, Object> autoParam)
            throws DeliveryException {
        this.deliver(doHeader, autoParam);
    }

    /**
     * 接口调用:
     * do状态,箱信息,实体卡,do发送到tms,发票回写
     */
    @Override
    public void callInterfaceWhenDeliver(DeliveryOrderHeader doHeader) {
        try {
            //-----------向后台系统传递接口数据,异步方式------------
            expFacadeService.sendDo2Oms(doHeader.getId(), Constants.DoStatus.ALL_DELIVER.getValue(), 1, null, doHeader.getDoType());
//            expFacadeService.send2Ost(doHeader.getId(), OstOrderStatus.OST_DO_SHIPOUT, loadDAO.getOperateUser());
            if (DoType.SELL.getValue().equals(doHeader.getDoType())) {
                expFacadeService.sendDoSnLog2Oms(doHeader.getId());//序列号
            }
            if (Config.isDefaultFalse(Keys.Interface.tms_rewrite_switch, Config.ConfigLevel.WAREHOUSE)) {
                tmsRewriteExpSrv.createMsg(doHeader.getId());
            }
//            if(DoType.ALLOT.getValue().equals(doHeader.getDoType())) {
//                //调拨出库需要将序列号传给dts写入目标仓库；调拨出库回写商品批次对应的数量到目标仓库；
//                expFacadeService.sendBatchQty2WMS(doHeader.getId(), doHeader.getDoType());
//            }
            //expFacadeService.sendSerialNo2WMS(doHeader.getId(), doHeader.getDoType(), DocType.SO.getValue(), doHeader.getRefNo1(), doHeader.getEdi2(), doHeader.getWarehouseId());
        } catch (Exception e) {
            log.error("Error while callInterfaceWhenDeliver:" + doHeader.getDoNo(), e);
        }
    }

    @Override
    public void sendCartonAndDOTogether2TMS(Long doId, String doNo, String currentCartonNo, boolean isFirstCarton) {
        expFacadeService.send2TmsV2(doId, doNo, currentCartonNo, isFirstCarton);
    }

    /**
     * 打印
     */
    @Override
    public List<String> print(Long id) {
        List<LoadHeader> dataList = new ArrayList<LoadHeader>();
        dataList.add(this.loadDAO.get(id));

        return reportGenerator.builtPrintData(LoadReport.NAME.getValue(), LoadReport.SUB_NAME.getValue(),
                new HashMap<String, Object>(), getReportData(dataList));
    }

    /**
     * 导出PDF
     */
    @Override
    public byte[] exportPDF(Long id) {
        List<LoadHeader> dataList = new ArrayList<LoadHeader>();
        dataList.add(this.loadDAO.get(id));

        return reportGenerator.generatePDF(LoadReport.NAME.getValue(), LoadReport.SUB_NAME.getValue(),
                new HashMap<String, Object>(), getReportData(dataList));
    }

    /**
     * 导出Excel
     */
    @Override
    public byte[] exportExcel(Long id) {
        List<DoLoadReportDTO> list = loadDetailDAO.findCartonNumsGroupByDoNoInACar(id);
        return reportGenerator.generateExcel("doCountInCar", null, new HashMap<String, Object>(), list);
    }

    @Override
    public void exportLoadDetailExcel(Long id) {
        try {
            List<Object[]> detailList = loadDetailDAO.findLoadDetailByLoadId(id);
            String dtoString = makeCsvForLoadDetail(detailList); // 构造输出字符串。
            exportCSV(new String("出库交接明细.csv".getBytes("GBK"), "ISO-8859-1"), dtoString);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private String makeCsvForLoadDetail(List<Object[]> dtoList) {
        StringBuilder strBuilder = new StringBuilder();
        addCsvChar(strBuilder, "收件人");
        addCsvChar(strBuilder, "电话");
        addCsvChar(strBuilder, "地址");
        addCsvChar(strBuilder, "运单号");
        addCsvChar(strBuilder, "耗材编码");
        addCsvChar(strBuilder, "长");
        addCsvChar(strBuilder, "宽");
        addCsvChar(strBuilder, "高");
        addCsvChar(strBuilder, "重量");
        addCsvChar(strBuilder, "代收货款");
        strBuilder.append("\r\n");
        for (Object[] detail : dtoList) {
            addCsvChar(strBuilder, detail[0]);
            addCsvChar(strBuilder, detail[1]);
            addCsvChar(strBuilder, detail[2]);
            addCsvChar(strBuilder, detail[3]);
            addCsvChar(strBuilder, detail[4]);
            addCsvChar(strBuilder, detail[5]);
            addCsvChar(strBuilder, detail[6]);
            addCsvChar(strBuilder, detail[7]);
            addCsvChar(strBuilder, detail[8]);
            addCsvChar(strBuilder, detail[9]);
            strBuilder.append("\r\n");
        }
        return strBuilder.toString();
    }

    /**
     * csv文字处理(csv按照","分割来生成excel的列，故需替换掉文本中原有的逗号),并去掉文字中的空格和换行符
     *
     * @param strBd 文字处理
     * @param value 处理内容
     */
    private void addCsvChar(final StringBuilder strBd, final Object value) {
        strBd.append(
                value == null ? "" : value.toString().trim().replace(",", "、").replace("\\s", "").replace("\n", ""))
                .append(",");
    }

    /**
     * 获取导出数据
     */
    @Override
    public List<LoadPrint> getReportData(List<LoadHeader> dataList) {
        Map<Long, Integer> doPagedQty = loadDetailDAO.getDoPagedQty(dataList);
        Warehouse wh = warehouseService.getLocalWarehouse();

        List<LoadPrint> loadPrints = new ArrayList<LoadPrint>(dataList.size());
        for (LoadHeader loadHeader : dataList) {
            LoadPrint loadPrint = new LoadPrint();
            loadPrint.setCartonQty(loadHeader.getCartonQty().intValue());
            loadPrint.setLoadNo(loadHeader.getLoadNo());
            // 设置do总数量为交接单明细的size
            loadPrint.setDoQty(loadHeader.getDocQty());
            loadPrint.setDriverName(loadHeader.getDriverName());
            loadPrint.setVechileNo(loadHeader.getVechileNo());
            loadPrint.setPrintTime(DateUtil.getNowTime());
            loadPrint.setShipTime(loadHeader.getDeliveryTime());
            loadPrint.setShipper(wh.getWarehouseName());

            List<LoadDetail> details = loadHeader.getLoadDetails();
            Map<Long, LoadPrintSub> subMap = new HashMap<Long, LoadPrintSub>();
            for (LoadDetail detail : details) {
                DeliveryOrderHeader doHeader = detail.getDeliveryOrderHeader();

                LoadPrintSub sub = new LoadPrintSub();
                sub.setDoNo(doHeader.getDoNo());
                sub.setPackedQty(doPagedQty.get(doHeader.getId()));

                if (StringUtil.isEmpty(loadPrint.getCarrierName()) && null != doHeader.getCarrierId()) {
                    loadPrint.setCarrierName(doHeader.getCarrier().getDistSuppCompName());
                }
                if (subMap.get(doHeader.getId()) == null) {
                    subMap.put(doHeader.getId(), sub);
                } else {
                    continue;
                }
            }
            loadPrint.setSubs(new ArrayList<LoadPrintSub>(subMap.values()));
            loadPrints.add(loadPrint);
        }

        return loadPrints;
    }

    /**
     * 查找doId对应的没有交接的箱子
     */
    @Override
    public CartonHeader findANotLoadedCartonHeaderByDoId(Long doId) {
        return cartonService.findANotLoadedCartonHeaderByDoId(doId);
    }


    /**
     * 根据交接单一次性获得所有交接信息
     */
    @Override
    public LoadHeader findLoadDetailsInAcar(Long loadHeaderId, Boolean queryHistory) {
        LoadHeader loadHeader;
        List<Object[]> objectList;
        if (!queryHistory) {
            loadHeader = this.getLoadHeader(loadHeaderId);
            objectList = loadDetailDAO.findLoadDetailsInAcarNew(loadHeaderId);
        } else {
            loadHeader = this.getLoadHeaderHis(loadHeaderId);
            objectList = loadDetailDAO.findLoadDetailsHisInAcarNew(loadHeaderId);
        }
        List<LoadDetail> loadDetailList = new ArrayList<LoadDetail>(objectList.size());
        for (Object[] object : objectList) {
            LoadDetail loadDetail = new LoadDetail();
            String address = (String) object[0];
            String consigneeName = (String) object[1];
            String provinceCname = (String) object[2];
            String cityCname = (String) object[3];
            String countyCname = (String) object[4];
            loadDetail.setAddress(address);
            loadDetail.setConsigneeName(consigneeName);
            loadDetail.setProvinceCname(provinceCname);
            loadDetail.setCityCname(cityCname);
            loadDetail.setCountyCname(countyCname);
            loadDetail.setId(Long.valueOf(object[5].toString()));
            loadDetail.setLoadHeaderId(Long.valueOf(object[6].toString()));
            loadDetail.setCartonHeaderId(Long.valueOf(object[7].toString()));
            loadDetail.setDoNo((String) object[8]);
            loadDetail.setTrackingNo((String) object[9]);
            loadDetail.setCarrierNo((String) object[10]);
            loadDetail.setDoHeaderId(Long.valueOf(object[11].toString()));
            loadDetail.setCreatedAt((Timestamp) object[12]);
            loadDetail.setCreatedBy((String) object[13]);
            loadDetail.setUpdatedAt((Timestamp) object[14]);
            loadDetail.setUpdatedBy((String) object[15]);
            loadDetailList.add(loadDetail);
        }
        loadHeader.setLoadDetails(loadDetailList);

        return loadHeader;
    }

    @Transactional
    public void changeStock(Long doHeaderId, String updateUser) {
        loadDetailDAO.changePickStockByDoHeaderId(doHeaderId, updateUser);
    }

    @Override
    @Loggable
    public void manualDelivery(CartonHeader cartonHeader, String loadType) {
        log.debug("manualDelivery: cartonNo = " + cartonHeader.getCartonNo() + ",loadType = " + loadType);
        StopWatch ascShipStopWatch = new StopWatch("箱：'" + cartonHeader.getCartonNo() + "'查找ship");
        // 自动发货。
        //仓库发货时，发运后才自动交接发货
        if (Constants.LoadType.WL.getValue().equals(loadType) || LoadType.WHOLESALE.getValue().equals(loadType)) {
            return;
        }
        ReShipDo reShipDo = findReShipDoByDoId(cartonHeader.getDoHeaderId(), LoadMode.MANUAL.getValue());
        log.debug(ascShipStopWatch.stop());
        if (reShipDo != null) {
            flowLoadService.autoDeliver(cartonHeader,
                    LoadMode.MANUAL.getValue(), reShipDo,
                    ParamUtil.getCurrentLoginName());
            // 调拨类型的订单在发货的时候回传tms。
            if (LoadType.TT.getValue().equals(loadType) && SystemConfig.configIsOpen(ConfigKeys.GLOBAL_TMS_IS_ENABLE, ParamUtil.getCurrentWarehouseId())) {
                sendCartonAndDOTogether2TMS(cartonHeader.getDoHeaderId(), cartonHeader.getDoHeader().getDoNo(), null, true);
            }
        }
    }

    /**
     * 按箱交接
     *
     * @param cartonHeaderList
     * @param loadHeader
     * @return 交接结果list中第一条是交接的包裹明细，第二条是“是否需要同步包裹到tms”标志
     */
    @Override
    @Transactional
    @Loggable
    public List<List<Object>> loadByCarton(List<CartonHeader> cartonHeaderList, LoadHeader loadHeader, List<DeliveryOrderHeader> doHeaderList, Integer isForceDeliver) {
        List<List<Object>> resultList = new ArrayList<List<Object>>();
        // 验证最大交接箱数
        checkMaxLoadCount(loadHeader,cartonHeaderList.size());
        BigDecimal totalGrossWeight = BigDecimal.ZERO;
        BigDecimal totalNetWeight = BigDecimal.ZERO;
        BigDecimal totalVolume = BigDecimal.ZERO;
        Map<Long,DeliveryOrderHeader> headerMap = new HashMap<Long, DeliveryOrderHeader>();
        for (DeliveryOrderHeader header : doHeaderList) {
            headerMap.put(header.getId(),header);
        }
        List<Long> cartonIds = new ArrayList<Long>(cartonHeaderList.size()+1);
        for (CartonHeader cartonHeader : cartonHeaderList) {
            cartonIds.add(cartonHeader.getId());
            DeliveryOrderHeader doHeader = headerMap.get(cartonHeader.getDoHeaderId());
            StopWatch loadValidateStopWatch = new StopWatch("箱：'" + cartonHeader.getCartonNo() + "'交接验证最大交接箱数到释放容器");
            // 生成交接明细
            LoadDetail loadDetail = createLoadDetail(loadHeader, cartonHeader, doHeader);
            // 更新箱的运单号
            cartonHeader.setStatus(CartonStatus.ALL_LOAD.getValue());
            cartonService.update(cartonHeader);

            // 释放容器
            List<Container> containers = containerMgntService.findContainerByDoc(cartonHeader.getCartonNo(), ContainerType.PACKAGE_CONTAINER.getValue(), BindDocType.RECHECK.getValue());
            if (ListUtil.isNotEmpty(containers)) {
                for (Container c : containers) {
                    containerMgntService.release(c);
                }
            }
            log.debug(loadValidateStopWatch.stop());
            //是否初次交接
            Boolean isFirstLoaded = Boolean.FALSE;
            // 如果订单是
            if (Constants.DoStatus.ALL_CARTON.getValue().equals(doHeader.getStatus())) {
                // 初次交接的do需要给tms同步包裹信息。
                isFirstLoaded = Boolean.TRUE;
            }
            StopWatch loadUpdateStopWatch = new StopWatch("箱：'" + cartonHeader.getCartonNo() + "'交接更新交接单，订单，准备数据等");
            // 是否已经交接完成,
            Boolean allLoad = cartonHeaderDAO.isAllLoadedOfDO(doHeader.getId());
            totalGrossWeight = totalGrossWeight.add(cartonHeader.getActualGrossWeight());
            totalNetWeight = totalNetWeight.add(cartonHeader.getNetWeight());
            totalVolume = totalVolume.add(cartonHeader.getActualVolume());

            //更新订单头和明细状态
            updateDo4Load(doHeader, allLoad);

            //交接时为接口准备数据
            createInterfaceData4Load(cartonHeader, loadHeader, doHeader, isFirstLoaded, allLoad);

            log.debug(loadUpdateStopWatch.stop());
            StopWatch loadShipDataStopWatch = new StopWatch("箱：'" + cartonHeader.getCartonNo() + "'交接准备发货数据，构造返回值");

            //记录日志，装车扫描
            orderLogService.saveLog(doHeader, OrderLogConstants.OrderLogType.LOAD_SCAN.getValue(), ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_MODIFY_LOAD_SCAN, null, loadHeader.getLoadNo(), cartonHeaderList.get(0).getCartonNo()));

            //为发货准备数据。
            if (allLoad) {
                //实时调用平台发货接口
                if (Config.get(Keys.Delivery.sync_do_node, Config.ConfigLevel.WAREHOUSE, Constants.SyncDoNode.NONE.getValue()).compareTo(SyncDoNode.LOAD.getValue()) <= 0) {
                    syncToPlatform(cartonHeader, doHeader, loadHeader.getLoadType(), isForceDeliver);
                }

                //记录日志，装车完成
                orderLogService.saveLog(doHeader,
                        OrderLogConstants.OrderLogType.LOAD_COMPLETE.getValue(),
                        ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_MODIFY_LOAD_COMPLETE,null,loadHeader.getLoadNo()));
                if (!YesNo.YES.getValue().equals(doHeader.getNeedCrossStock())) {
                    ReShipDo reShipDo = findReShipDoByDoId(doHeader.getId(), LoadMode.MANUAL.getValue());
                    if (null == reShipDo) {
                        reShipDo = new ReShipDo();
                        reShipDo.setDocId(doHeader.getId());
                        reShipDo.setCount(0L);
                        reShipDo.setOpTime(DateUtil.getNowTime());
                        reShipDo.setIsAuto(LoadMode.MANUAL.getValue());
                        reShipDoDAO.saveOrUpdate(reShipDo);
                    }
                }
            }
            //构造返回值
            List<Object> result = new ArrayList<Object>(4);
            List<LoadScanDTO> loadScanDTOs = new ArrayList<LoadScanDTO>();
            loadScanDTOs.add(assemble(loadDetail, cartonHeader.getLpnNo()));
            result.add(loadScanDTOs);
            result.add(isFirstLoaded);
            result.add(allLoad);
            result.add(cartonHeader);
            resultList.add(result);
            log.debug(loadShipDataStopWatch.stop());
        }
        //更新交接单头
        updateLoadHeader4Load(loadHeader.getId(),totalGrossWeight,totalNetWeight,totalVolume,Long.valueOf(cartonHeaderList.size()));
        return resultList;
    }

    /**
     * 实时调用平台发货
     *
     * @param cartonHeader
     * @param doHeader
     * @param loadType
     */
    @Override
    public void syncToPlatform(CartonHeader cartonHeader, DeliveryOrderHeader doHeader,
                               String loadType, Integer isForceDeliver) {
        //非仓库发货，返回
        if (!LoadType.WL.getValue().equals(loadType)) {
            return;
        }
        //强制发货，不调用实时同步接口
        if (YesNo.YES.getValue().equals(isForceDeliver)) {
            //记录日志，装车完成
            orderLogService.saveLog(doHeader,
                    OrderLogConstants.OrderLogType.FORCE_DELIVER.getValue(),
                    ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_FORCE_DELIVER));
            return;
        }
        //实时回单接口
        try {
            sync(cartonHeader.getWayBill(), doHeader);
        } catch (DeliveryException de) {
            throw de;
        } catch (ApiException e) {
            throw new DeliveryException(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage());
            //同步失败,未知异常
            throw new DeliveryException(DeliveryException.DELIVERY_SYNC_UNKNOWN_ERROR);
        }
    }

    @Override
    public void sync(String wayBill, DeliveryOrderHeader doHeader) throws Exception {
        DeliveryEventSyncInfo syncInfo = new DeliveryEventSyncInfo();
//        Platform platform = platformDAO.get(NumberUtils.object2Long(doHeader.getOrderSource()));
//        syncInfo.setPlatformId(platform.getOriginalId());
        syncInfo.setPlatformId(NumberUtils.object2Long(doHeader.getOrderSource()));
        ShopInfo shop = shopInfoDAO.get(doHeader.getShopId());
        if (shop != null) {
            syncInfo.setShopId(shop.getOriginalId());
        }
        Carrier carrier = carrierDAO.get(doHeader.getCarrierId());
        if (carrier != null) {
            syncInfo.setCarrierId(carrier.getOriginalId());
        }
        if (StringUtil.isBlank(wayBill)) {
            throw new DeliveryException(DeliveryException.ERROR_WAYBILL_IS_NULL);
        }
        syncInfo.setPlatformOrderId(doHeader.getOriginalSoCode());
        syncInfo.setLogisticsNo(wayBill);
        syncInfo.setPayType(doHeader.getPaymentType());
        syncInfo.setSoCode(doHeader.getRefNo1());
        syncInfo.setDoNo(doHeader.getDoNo());
        DeliveryEventSyncRequest request = new DeliveryEventSyncRequest();
        request.setTenantCode(tenantService.getCodeByWarehouseId(ParamUtil.getCurrentWarehouseId()));
        request.setDeliveryEventSyncInfo(syncInfo);

        processDeiverBatchInfo(doHeader, request);

        DubheResponse response = deliveryEventDubheHandler.send(request, doHeader.getWarehouseId(), doHeader.getSourceSystem());
        if (response == null || !com.daxia.dubhe.api.internal.util.Constants.FLAG_SUCCESS.equals(response.getFlag())) {
            //接口调用失败，抛异常
            throw new DeliveryException(response.getMessage());
        }
        //记录物流同步日志
        boolean waybillSync = orderLogService.existsLog(doHeader.getId(), OrderLogConstants.OrderLogType.WAYBILL_SYNC.getValue());
        if (!waybillSync) {
            orderLogService.saveLog(doHeader,
                    OrderLogConstants.OrderLogType.WAYBILL_SYNC.getValue(),
                    ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_WAYBILL_SYNC));
        }
    }

    private void processDeiverBatchInfo(DeliveryOrderHeader doHeader, DeliveryEventSyncRequest request) {
        if (!Config.isFmJson(Keys.Delivery.auto_load_and_deliver_cfg, Config.ConfigLevel.WAREHOUSE, AutoLoadAndDeliverCfg.syncPlatFormIsNeedBatch)) {
            return;
        }

        List<TrsPickLog> pickLogs = trsPickLogService.findByDocId(doHeader.getId());

        if (ListUtil.isNullOrEmpty(pickLogs)) {
            return;
        }

        Set<Long> doItemIds = Sets.newHashSet();
        for (TrsPickLog pickLog : pickLogs) {
            doItemIds.add(pickLog.getDocLineId());
        }

        List<DeliveryOrderDetail> deliveryOrderDetails = doDetailDAO.getByKeys(Lists.<Long>newArrayList(doItemIds));
        Map<Long,DeliveryOrderDetail> doDetaiMap = Maps.newHashMap();
        for (DeliveryOrderDetail deliveryOrderDetail : deliveryOrderDetails) {
            doDetaiMap.put(deliveryOrderDetail.getId(),deliveryOrderDetail);
        }


        List<DeliveryEventBatchInfo> batchInfos = Lists.newArrayList();

        for (TrsPickLog pickLog : pickLogs) {
            DeliveryEventBatchInfo batchInfo = new DeliveryEventBatchInfo();
            batchInfo.setProductId(pickLog.getSku().getOriginalId());
            batchInfo.setQty(pickLog.getToQty());
            batchInfo.setDocItemId(doDetaiMap.get(pickLog.getDocLineId()).getOrigDetailId());
            StockBatchAtt batchAtt = pickLog.getToStkBatchAtt();
            batchInfo.setWmsBatchCode(batchAtt.getLotNo());
            batchInfo.setProductionTime(DateUtil.valueOf(batchAtt.getLotatt01()));
            batchInfo.setExpireTime(DateUtil.valueOf(batchAtt.getLotatt02()));
            batchInfo.setWarehouseTime(DateUtil.valueOf(batchAtt.getLotatt03()));

            if(StringUtil.isNotEmpty(batchAtt.getLotatt04())){
                Supplier supplier = supplierService.getSupplier(Long.valueOf(batchAtt.getLotatt04()));
                batchInfo.setSupplierId(supplier.getOriginalId());
            }

            batchInfo.setLotNo(batchAtt.getLotatt05());

            if(StringUtil.isNotEmpty(batchAtt.getLotatt06())){
                Merchant merchant = merchantService.getMerchant(Long.valueOf(batchAtt.getLotatt06()));
                batchInfo.setMerchantId(merchant.getOriginalId());
            }

            batchInfo.setPurchasePrice(NumberUtils.object2BigDecimal(batchAtt.getLotatt09()));
            batchInfo.setPoCode(batchAtt.getLotatt10());
            batchInfo.setIsDamaged(StringUtil.isEmpty(batchAtt.getLotatt12()) ? YesNo.NO.getValue() : YesNo.YES.getValue());
            batchInfos.add(batchInfo);
        }
        request.getDeliveryEventSyncInfo().setBatchInfos(batchInfos);
    }

    /**
     * @param cartonHeader
     * @param loadHeader
     * @param doHeader
     * @param isFirstLoaded
     * @param allLoad
     */
    private void createInterfaceData4Load(CartonHeader cartonHeader, LoadHeader loadHeader, DeliveryOrderHeader doHeader, Boolean isFirstLoaded, Boolean allLoad) {
        //do每次交接时回写
        if (LoadType.DO.getValue().equals(loadHeader.getLoadType())) {
            expFacadeService.createDo2TmsData(doHeader.getId(), doHeader.getDoType(), doHeader.getDoNo(), cartonHeader.getCartonNo(), isFirstLoaded);
        }
        // 调拨类型的只在发货的时候回传一次信息给tms
        if (LoadType.TT.getValue().equals(loadHeader.getLoadType()) && allLoad) {
            expFacadeService.createDo2TmsData(doHeader.getId(), doHeader.getDoType(), doHeader.getDoNo(), null, true);
        }
        if (allLoad) {
            //推出库单信息至TMS系统
            expFacadeService.sendOrder2Tms(doHeader.getId(), doHeader.getDoNo(), doHeader.getWarehouseId(), doHeader.getSourceSystem());
        }
    }

    /**
     * @param doHeader
     * @param allLoad
     */
    private void updateDo4Load(DeliveryOrderHeader doHeader, boolean allLoad) {
        // 更新DO单的状态
        boolean doHeaderIsChange = false;
        if (allLoad) {
            doHeader.setStatus(Constants.DoStatus.ALL_LOAD.getValue());
            doHeaderIsChange = true;
        } else if (Constants.DoStatus.ALL_CARTON.getValue().equals(doHeader.getStatus())) {
            doHeader.setStatus(Constants.DoStatus.PART_LOAD.getValue());
            doHeaderIsChange = true;
        }
        //防止并发 ，每次交接都要更新订单头。
        doHeader.setUpdatedAt(DateUtil.getNowTime());
        doHeader.setUpdatedBy(identity.getCredentials().getUsername());
        this.deliveryOrderService.updateDoHeader(doHeader);
        if (doHeaderIsChange) {
            this.deliveryOrderService.updateDoDetailStatusByDoId(doHeader.getId(), doHeader.getStatus());
        }
    }

    /**
     * @param totalGrossWeight
     * @param loadHeaderId
     */
    private void updateLoadHeader4Load(Long loadHeaderId, BigDecimal totalGrossWeight, BigDecimal totalNetWeight, BigDecimal totalVolume,Long cartonNum) {
        loadDAO.updateLoadHeaderForAuto(loadHeaderId,totalGrossWeight, totalNetWeight, totalVolume,cartonNum);
        loadDAO.getSession().flush();
        loadDAO.getSession().clear();
    }

    /**
     * @param cartonHeader
     * @param loadHeaderId
     */
    private void updateLoadHeader4Unload(CartonHeader cartonHeader, Long loadHeaderId) {
        LoadHeader loadHeader = loadDAO.get(loadHeaderId);
        String status;
        if (loadHeader.getCartonQty().equals(1L)) {
            status = Constants.LoadStatus.INITIAL.getValue();
        } else {
            status = Constants.LoadStatus.LOADING.getValue();
        }
        loadDAO.updateLoadHeaderForUnload(loadHeaderId, status);
        loadDAO.getSession().flush();
        loadDAO.getSession().clear();
    }


    /**
     * LoadDetail值设置到DTO中
     *
     * @param loadDetail
     * @param LpnNo
     * @return
     */
    private LoadScanDTO assemble(LoadDetail loadDetail, String LpnNo) {
        LoadScanDTO result = null;
        if (loadDetail != null) {
            result = new LoadScanDTO();
            result.setAddress(loadDetail.getAddress());
            result.setCartonNo(loadDetail.getCarrierNo());
            result.setCityCname(loadDetail.getCityCname());
            result.setConsigneeName(loadDetail.getConsigneeName());
            result.setCountyCname(loadDetail.getCountyCname());
            result.setDoNo(loadDetail.getDoNo());
            result.setLoadDetailId(loadDetail.getId());
            result.setDoHeaderId(loadDetail.getDoHeaderId());
            result.setLpnNo(LpnNo);
            result.setProvinceCname(loadDetail.getProvinceCname());
            result.setTrackingNo(loadDetail.getTrackingNo());
        }
        return result;
    }

    /**
     * 自动交接中的 交接步骤
     *
     * @return List中的第一个结果是操作后的交接单，第二个是"是否需要调用TMS接口"标志。
     */
    @Override
    @Transactional
    @Loggable
    public LoadHeader autoLoad(CartonHeader ch, Integer loadMode, ReShipDo reShipDo, Date newOpTime) throws DeliveryException {
        StopWatch autoLoad1StopWatch = new StopWatch("箱：'" + ch.getCartonNo() + "'交接流水交接,取得reship，交接单");
        //传入箱时间如果大于记录的时间，则记录传入箱时间
        if (null == reShipDo.getId()) {
            reShipDoDAO.saveOrUpdate(reShipDo);
        } else if (reShipDo.getOpTime().compareTo(newOpTime) < 0) {
            reShipDo.setOpTime(newOpTime);
            reShipDoDAO.saveOrUpdate(reShipDo);
        }

        // 取得交接单
        LoadHeader lh = generateLoadHeader(loadMode);

        DeliveryOrderHeader doHeader = ch.getDoHeader();
        Boolean needSend2Tms = false;
        if (Constants.DoStatus.ALL_CARTON.getValue().equals(doHeader.getStatus())) {
            needSend2Tms = true;
        }
        log.debug(autoLoad1StopWatch.stop());
        // 将包裹装入交接单。
        loadCarton(lh, ch);
        StopWatch autoLoad2StopWatch = new StopWatch("箱：'" + ch.getCartonNo() + "'交接插入接口数据");
        String newStatus = deliveryOrderService.getDoHeaderById(doHeader.getId()).getStatus();
        // 流水交接需要给tms合并回传do和包裹信息。
        expFacadeService.createDo2TmsData(doHeader.getId(), doHeader.getDoType(), doHeader.getDoNo(),
                ch.getCartonNo(), needSend2Tms);
        log.debug(autoLoad2StopWatch.stop());
        return lh;
    }


    /**
     * 完成自动交接单
     *
     * @param lh
     */
    @Override
    @Transactional
    public void completeLoadHeader(LoadHeader lh) throws DeliveryException {
        // 取得自动完成发货单数量系统参数
        Integer loadHeaderCapacity = Config.getIntFmJson(Keys.Delivery.auto_load_and_deliver_cfg, Config.ConfigLevel.WAREHOUSE, AutoLoadAndDeliverCfg.cartonCount);
        // 系统参数未配置
        if (loadHeaderCapacity == null) {
            loadHeaderCapacity = Integer.valueOf(100);
        }
        // 达到指定数量自动完成发货单。
        if (lh.getCartonQty() >= loadHeaderCapacity.intValue() - 1) {
            loadDAO.updateLoadHeaderForAutoClose(lh.getId(), loadHeaderCapacity);
        }
    }

    /**
     * 将包裹装入交接单。
     *
     * @param lh 装车单
     * @param ch 包裹
     * @return 是否需要同步包裹和订单信息到tms。
     */
    @Loggable
    private Boolean loadCarton(LoadHeader lh, CartonHeader ch) {
        StopWatch loadValidateStopWatch = new StopWatch("箱：'" + ch.getCartonNo() + "'交接生成交接明细到释放容器");
        DeliveryOrderHeader doHeader = ch.getDoHeader();
        // 生成交接明细。
        createLoadDetail(lh, ch, doHeader);

        // 更新包裹状态。
//        ch.setWayBill(doHeader.getDoNo());
        ch.setStatus(CartonStatus.ALL_LOAD.getValue());
        cartonHeaderDAO.saveOrUpdate(ch);
        cartonHeaderDAO.getSession().flush();
        cartonHeaderDAO.getSession().clear();

        // 释放容器
        List<Container> containers = containerMgntService.findContainerByDoc(ch.getCartonNo(), ContainerType.PACKAGE_CONTAINER.getValue(), BindDocType.RECHECK.getValue());
        if (ListUtil.isNotEmpty(containers)) {
            for (Container c : containers) {
                containerMgntService.release(c);
            }
        }
        log.debug(loadValidateStopWatch.stop());
        StopWatch loadUpdateStopWatch = new StopWatch("箱：'" + ch.getCartonNo() + "'交接更新交接单，订单等");
        loadDAO.updateLoadHeaderForAuto(lh.getId(), ch.getActualGrossWeight(), ch.getNetWeight(), ch.getActualVolume(),Long.valueOf(1));

        boolean doHeaderIsChange = false;
        boolean needSend2Tms = false;

        // 订单装入第一个包裹时向tms同步包裹与订单信息
        if (Constants.DoStatus.ALL_CARTON.getValue().equals(doHeader.getStatus())) {
            needSend2Tms = true;
        }

        // 包裹对应的do是否都已经交接完成
        Boolean isAllLoaded = cartonService.isDoCartonsAllLoaded(ch.getDoHeaderId());
        // 如果订单对应包裹已经全部交接，则完成订单。
        if (isAllLoaded) {
            doHeader.setStatus(Constants.DoStatus.ALL_LOAD.getValue());
            doHeaderIsChange = true;
        } else if (Constants.DoStatus.ALL_CARTON.getValue().equals(doHeader.getStatus())) {
            doHeader.setStatus(Constants.DoStatus.PART_LOAD.getValue());
            doHeaderIsChange = true;
        }
        if (doHeaderIsChange) {
            this.deliveryOrderService.updateDoHeader(doHeader);
            this.deliveryOrderService.updateDoDetailStatusByDoId(doHeader.getId(), doHeader.getStatus());
        }
        log.debug(loadUpdateStopWatch.stop());
        return needSend2Tms;
    }

    /**
     * 生成交接单明细
     *
     * @param lh
     * @param ch
     * @param doHeader
     */
    private LoadDetail createLoadDetail(LoadHeader lh, CartonHeader ch, DeliveryOrderHeader doHeader) {
        LoadDetail loadDetail = new LoadDetail();
        loadDetail.setLoadHeaderId(lh.getId());
        loadDetail.setDoHeaderId(ch.getDoHeaderId());
        loadDetail.setDoNo(doHeader.getDoNo());
        loadDetail.setCartonHeaderId(ch.getId());
        loadDetail.setCarrierNo(ch.getCartonNo());
        loadDetail.setConsigneeName(doHeader.getConsigneeName());
        String provinceCname = doHeader.getProvinceInfo() == null ? null : doHeader.getProvinceInfo()
                .getProvinceCname();
        String cityCname = doHeader.getCityInfo() == null ? null : doHeader.getCityInfo().getCityCname();
        String countyCname = doHeader.getCountyInfo() == null ? null : doHeader.getCountyInfo().getCountyCname();
        loadDetail.setProvinceCname(provinceCname);
        loadDetail.setCityCname(cityCname);
        loadDetail.setCountyCname(countyCname);
        loadDetail.setAddress(doHeader.getAddress());
        if (!DoType.RTV.getValue().equals(doHeader.getDoType())) {
            loadDetail.setTrackingNo(doHeader.getDoNo());
        }
        loadDetailDAO.save(loadDetail);
        return loadDetail;
    }

    /**
     * 取得交接单
     *
     * @return
     */
    private LoadHeader generateLoadHeader(Integer loadMode) {
        // 次日完成今日交接单
        loadDAO.updateLoadHeaderForAutoClose(getTodayStartTime());
        // 取得系统中初始化或者交接中的自动交接单。
        LoadHeader lh = loadDAO.findAutoLoadLoadH(loadMode);
        // 不存在的时候生成自动交接单。
        if (lh == null) {
            lh = createAutoLoadHeader(loadMode);
        }
        return lh;
    }
    private Timestamp getTodayStartTime(){
        // 获取今天的日期
        LocalDate today = LocalDate.now();
        // 将今天的日期与00:00:00结合，得到今天的零时
        LocalDateTime todayMidnight = today.atStartOfDay();
        // 指定时区（可以替换为你需要的时区）
        ZoneId zoneId = ZoneId.systemDefault();

        // 将 LocalDateTime 转换为 Instant
        Instant instant = todayMidnight.atZone(zoneId).toInstant();

        // 将 Instant 转换为 Timestamp
        return Timestamp.from(instant);
    }
    /**
     * 生成自动交接单。类型为do，状态为初始化。
     *
     * @return
     */
    private LoadHeader createAutoLoadHeader(Integer loadMode) {
        LoadHeader lh = new LoadHeader();
        lh.setLoadNo(sequenceGeneratorService.generateSequenceNo(Constants.SequenceName.LOADNO.getValue(), ParamUtil.getCurrentWarehouseId()));
        lh.setIsAuto(loadMode);
        lh.setLoadType(LoadType.DO.getValue());
        lh.setStatus(LoadStatus.LOADING.getValue());
        lh.setDocQty(0L);
        lh.setTotalCartonQty(0L);
        lh.setCartonQty(0L);
        lh.setTotalGrossWeight(BigDecimal.ZERO);
        lh.setTotalNetWeight(BigDecimal.ZERO);
        lh.setTotalVolume(BigDecimal.ZERO);
        loadDAO.saveOrUpdate(lh);
        return lh;
    }

    /**
     * 校验包裹和订单状态。
     *
     * @param cartonNo
     */
    @Override
    public CartonHeader validateBeforeAutoDelivery(String cartonNo) throws DeliveryException {
        // 取得包裹
        CartonHeader ch = cartonService.getCartonByNo(cartonNo);
        // 包裹不存在。
        if (ch == null) {
            throw new DeliveryException(DeliveryException.CARTON_NO_EXIST, cartonNo);
        }
        // 包裹已经交接，则不允许交接。
        if (CartonStatus.ALL_LOAD.getValue().equals(ch.getStatus())) {
            throw new DeliveryException(DeliveryException.CARTON_ALREADY_LOADED, ch.getCartonNo());
        }
        // 取得订单
        DeliveryOrderHeader doHeader = ch.getDoHeader();
        // 订单已经冻结，不允许交接。
        if (ReleaseStatus.HOLD.getValue().equals(doHeader.getReleaseStatus())) {
            throw new DeliveryException(DeliveryException.DO_ALREADY_FROZEN);
        }
        //订单需要取消，不允许交接
        if (doHeader.getNeedCancel()) {
            throw new DeliveryException(DeliveryException.DO_NEED_CANCEL);
        }
        // 订单不在（装箱完成，交接中）状态，则不允许自动交接
        if (StringUtil.isNotIn(doHeader.getStatus(), DoStatus.ALL_CARTON.getValue(),
                DoStatus.PART_LOAD.getValue())) {
            throw new DeliveryException(DeliveryException.DO_STATUS_CANNOT_LOAD, cartonNo);
        }
        return ch;
    }

    @Override
    public List<ReShipDo> findToReShipDo(Integer failCeiling, Integer maxCount, Long warehouseId) {
        return reShipDoDAO.findNeedReShipDo(failCeiling, maxCount, warehouseId);
    }

    /**
     * 根据DoId查找ReShipDo
     *
     * @param doId
     * @param loadMode
     * @return
     */
    @Override
    public ReShipDo findReShipDoByDoId(Long doId, Integer loadMode) {
        return reShipDoDAO.findReShipDoByDoId(doId, loadMode);
    }

    /**
     * 手动发货失败发邮件。
     *
     * @param docNo
     * @param msgKey
     * @param ex
     */
    @Override
    public void sendNoticeMail(String docNo, String msgKey, Exception ex) {
        //读取异常信息
        String exStr = "";
        if (null != ex) {
            exStr = ex.getMessage();
        }
    
        AlarmUtil.sendEmail(ResourceUtils.getDispalyString(msgKey), docNo + "\r\n" + exStr);
    }

    @Override
    public void evictReShipDo(ReShipDo reShipDo) {
        reShipDoDAO.getSession().evict(reShipDo);
    }

    /**
     * 发货失败次数+1
     *
     * @param reShipDo
     */
    @Override
    @Transactional
    public void addShipCount(ReShipDo reShipDo) {
        if (reShipDo.getCount() == 0L) {
            reShipDo.setNextInvokeTime(DateUtil.getNowTime());
        } else {
            reShipDo.setNextInvokeTime(DateUtil.dateAdd("mi", DateUtil.getNowTime(), ExpSrvUtil.getReinvokeInterval(reShipDo.getCount().intValue(), ParamUtil.getCurrentWarehouseId())));
        }
        reShipDo.setCount(reShipDo.getCount() + 1);
        reShipDoDAO.saveOrUpdate(reShipDo);
    }

    @Override
    @Transactional
    public void removeReShipByDoId(Long doId) {
        reShipDoDAO.removeReShipDoByDoId(doId);
    }

    @Override
    public Boolean isCartonLoaded(Long cartonHeaderId) {
        Map<String, Object> params = new HashMap<String, Object>(2);
        params.put("cartonHeaderId", cartonHeaderId);
        params.put("warehouseId", ParamUtil.getCurrentWarehouseId());
        return this.loadDetailDAO.isExists(params, "id", null);
    }

    /**
     * 锁定装车单并发货
     *
     * @param loadHeader
     */
    @Override
    @Transactional
    public void closeLoadOrder(LoadHeader loadHeader) {
        String status = getLoadHeader(loadHeader.getId()).getStatus();
        //锁定交接单
        if (LoadStatus.LOADING.getValue().equals(status)) {
            lockLoadOrder(loadHeader);
            completeLoad(loadHeader);
        } else if (LoadStatus.COMPLETED.getValue().equals(status)) {
            completeLoad(loadHeader);
        }
    }

    @Override
    @Transactional
    public void removeCartonFromLoadHeader(Long cartonId, Long loadHeaderId) {
        LoadHeader loadHeader = loadDAO.get(loadHeaderId);
        if (loadHeader == null ||
                !loadHeader.getStatus().equals(Constants.LoadStatus.LOADING.getValue())) {
            throw new DeliveryException(DeliveryException.LOAD_HEADER_STATUS_ERROR_WHEN_UNLOAD);
        }

        this.loadDetailDAO.removeByCartonIdAndLoadHeaderId(cartonId, loadHeaderId);
        CartonHeader cartonHeader = cartonHeaderDAO.get(cartonId);
        //还原箱状态
        cartonHeader.setStatus(Constants.CartonStatus.PACK_OVER.getValue());
        cartonService.update(cartonHeader);

        //还原订单状态
        LoadDetail loadDetail = new LoadDetail();
        loadDetail.setDoHeaderId(cartonHeader.getDoHeaderId());
        DeliveryOrderHeader orderHeader =
                deliveryOrderService.getDoHeaderById(cartonHeader.getDoHeaderId());
        List<LoadDetail> list = loadDetailDAO.findByFilter(loadDetail);
        if (CollectionUtils.isEmpty(list)) {
            orderHeader.setStatus(Constants.DoStatus.ALL_CARTON.getValue());
        } else {
            orderHeader.setStatus(Constants.DoStatus.PART_LOAD.getValue());
        }
        deliveryOrderService.saveOrUpdateDoHeader(orderHeader);
        //记录日志,取消装车
        orderLogService.saveLog(orderHeader,
                OrderLogConstants.OrderLogType.LOAD_CANCEL.getValue(),
                ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_MODIFY_LOAD_CANCEL,null,cartonHeader.getCartonNo(),loadHeader.getLoadNo()));

        reShipDoDAO.removeReShipDoByDoId(orderHeader.getId());

        //更新交接单箱数、状态
        updateLoadHeader4Unload(cartonHeaderDAO.get(cartonId), loadHeaderId);
    }

    @Override
    public DataPage<LoadDetail> queryDetail(LoadDetailFilter loadDetailFilter, int startIndex, int pageSize) {
        DataPage<LoadDetail> result =
                loadDetailDAO.findRangeByFilter(loadDetailFilter, startIndex, pageSize);
        DeliveryOrderHeader orderHeader = null;
        for (LoadDetail detail : result.getDataList()) {
            orderHeader = deliveryOrderService.getDoHeaderById(detail.getDoHeaderId());
            if (orderHeader != null) {
                detail.setConsigneeName(orderHeader.getConsigneeName());
                detail.setCityCname(orderHeader.getCityInfo() == null ? "" : orderHeader.getCityInfo().getCityCname());
                detail.setCountyCname(orderHeader.getCountyInfo() == null ? "" : orderHeader.getCountyInfo().getCountyCname());
                detail.setAddress(orderHeader.getAddress());
            }
        }
        return result;
    }

    @Override
    @Transactional
    public LoadHeader shipConfirm(Long loadHeaderId) {
        LoadHeader loadHeader = loadDAO.get(loadHeaderId);
        //验证交接单状态
        if (loadHeader == null ||
                !loadHeader.getStatus().equals(Constants.LoadStatus.LOADING.getValue())) {
            throw new DeliveryException(DeliveryException.SHIP_CONFIRM_STATUS_ERROR);
        }
        //验证冻结
        String fzOrders = deliveryOrderService.findFrozenOrdersByLoadHeaderId(loadHeaderId);
        if (StringUtils.isNotBlank(fzOrders)) {
            throw new DeliveryException(DeliveryException.ERROR_SHIP_CONFIRM_ORDER_HD, fzOrders);
        }

        //验证多箱是否有遗漏
        String unLoadCartons = cartonService.findUnLoadCartonsByLoadHeaderId(loadHeaderId);
        if (StringUtils.isNotBlank(unLoadCartons)) {
            throw new DeliveryException(DeliveryException.ERROR_SHIP_CONFIRM_MULTI_CARTON, unLoadCartons);
        }

        if (LoadType.RTV.getValue().equals(loadHeader.getLoadType()) || LoadType.TT.getValue().equals(loadHeader.getLoadType())) {
            closeLoadOrder(loadHeader);
            return loadHeader;
        }

        //更新交接单状态
        loadHeader.setStatus(Constants.LoadStatus.DELIVERYED.getValue());
        loadHeader.setDeliveryTime(DateUtil.getNowTime());
        //交接单do_qty
        loadHeader.setDocQty(loadDetailDAO.getDocQtyByLoadId(loadHeader.getId()));
        loadDAO.update(loadHeader);
        //订单状态更新为发货中
        deliveryOrderService.updateStateByLoadHeaderId(loadHeader.getId(), Constants.DoStatus.PART_DELIVER.getValue());
        //增加reShipDo.callCount
        reShipDoDAO.addCallCountByLoadHeaderId(loadHeader.getId());
        return loadHeader;
    }

    /**
     * 合单发货，获取下个待扫描箱号
     *
     * @param cartonHeader
     * @return
     */
    @Override
    public String getNextCartonNo4CombineShip(CartonHeader cartonHeader) {
        //1.系统参数盘点是否允许合单发货
        Integer isCombineShip = SystemConfig.getConfigValueInt("delivery.ship.combineShip", ParamUtil
                .getCurrentWarehouseId());
        if (!YesNo.YES.getValue().equals(isCombineShip)) {
            return null;
        }
        //2.验证carton是否已绑定了运单号
        if (StringUtils.isBlank(cartonHeader.getWayBill())) {
            return null;
        }

        //3.查询运单号相同，状态为【装箱完成】的下一个箱子
        CartonHeaderFilter filter = new CartonHeaderFilter();
        filter.setWayBill(cartonHeader.getWayBill());
        filter.setStatus(CartonStatus.PACK_OVER.getValue());
        List<CartonHeader> list = cartonHeaderDAO.findByFilter(filter);
        //4.返回箱号
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0).getCartonNo();
    }
    
    @Override
    public List<LoadHeader> findUnFinished(int size) {
        return this.loadDAO.findUnFinished(size);
    }
    /**
     * 校验并查询装箱所需参数
     * @param loadId
     * @param cartonNo 箱号/出库单号/快递单号/板号
     * @return
     */
    @Override
    public Triple<LoadHeader, List<DeliveryOrderHeader>, List<CartonHeader>> validateBeforeCartonLoad(Long loadId, String cartonNo) {
        LoadHeader loadHeader = this.getLoadHeader(loadId);
        if (loadHeader == null || !(CompareUtil.compare(Constants.LoadStatus.INITIAL.getValue(), loadHeader.getStatus()) || CompareUtil.compare(Constants.LoadStatus.LOADING.getValue(), loadHeader.getStatus()))) {
            throw new DeliveryException(DeliveryException.LOAD_STATUS_CANNOT_LOAD1);
        }
        List<CartonHeader> cartonHeaderList = new ArrayList<CartonHeader>();
        CartonHeader header = cartonService.getCartonByNo(cartonNo);
        if (header == null) {
            //板号查询
            CartonHeaderFilter cartonHeaderFilter = new CartonHeaderFilter();
            cartonHeaderFilter.setLpnNo(cartonNo);
            List<CartonHeader> cartonList = cartonService.query(cartonHeaderFilter);
            if(CollectionUtils.isNotEmpty(cartonList)) {
                cartonHeaderList=cartonList;
            }else{
                //运单号转箱号，兼容扫运单号,订单号
                cartonHeaderList = cartonService.findByDoNo(cartonNo);
                if (CollectionUtils.isEmpty(cartonHeaderList)) {
                    cartonHeaderList = cartonService.findByWaybill(cartonNo);
                    if (CollectionUtils.isEmpty(cartonHeaderList)) {
                        throw new DeliveryException(DeliveryException.CARTON_NO_EXIST, cartonNo);
                    } else if (cartonHeaderList.size() > 1) {
                        CartonHeader c = cartonHeaderList.get(0);
                        cartonHeaderList = new ArrayList<CartonHeader>();
                        cartonHeaderList.add(c);
                    }
                }
            }
        } else {
            cartonHeaderList.add(header);
        }
        Set<Long> doIdList = new HashSet<Long>();
        Iterator<CartonHeader> cartonHeaderIterator = cartonHeaderList.iterator();
        while (cartonHeaderIterator.hasNext()){
            CartonHeader carton=cartonHeaderIterator.next();
            // 过滤移除已经出库单据
            if (carton.getStatus().equals(Constants.CartonStatus.ALL_LOAD.getValue())) {
                cartonHeaderIterator.remove();
            }else{
                doIdList.add(carton.getDoHeaderId());
            }
        }
        // 已全部装箱 则异常返回
        if(CollectionUtils.isEmpty(doIdList)){
            throw new DeliveryException(DeliveryException.CARRIER_NO_IS_LOADED,cartonNo);
        }
        List<DeliveryOrderHeader> doHeaderList = deliveryOrderService.findDoByIds(new ArrayList<Long>(doIdList));
        // 过滤移除已经出库单据
        Iterator<DeliveryOrderHeader> iterator = doHeaderList.iterator();
        while (iterator.hasNext()){
            DeliveryOrderHeader x = iterator.next();
            if(DoStatus.ALL_DELIVER.getValue().equals( x.getStatus())){
                iterator.remove();
            }
        }
        validateCartonAndDo4Load(loadHeader, doHeaderList, cartonHeaderList);
        
        return Triple.of(loadHeader,doHeaderList,cartonHeaderList);
    }

    private void validateCartonAndDo4Load(LoadHeader loadHeader, List<DeliveryOrderHeader> doHeaderList, List<CartonHeader> cartonHeaderList) {
        //对于按照波次来交接,拿一个波次号出来验证
        CartonHeader modelCartonHeader = cartonHeaderList.get(0);
        // 判断该箱号是否已存在于交接单明细中
        if (this.isCartonLoaded(modelCartonHeader.getId())) {
            throw new DeliveryException(DeliveryException.CARRIER_NO_IS_LOADED, modelCartonHeader.getCartonNo());
        }
        // 检查do与交接单类型是否匹配
        checkDoType(loadHeader, modelCartonHeader.getDoHeader(), modelCartonHeader.getCartonNo());
    
        if (StringUtil.isIn(loadHeader.getLoadType(), LoadType.DO.getValue(), LoadType.WL.getValue(), LoadType.WHOLESALE.getValue())) {
            String loadNo = loadDetailDAO.getLoadNoByDo(loadHeader.getId(), doHeaderList.get(0).getId());
            if (StringUtil.isNotEmpty(loadNo)) {
                throw new DeliveryException(DeliveryException.DO_CAN_NOT_MIX_LOAD, loadNo);
            }
        }
        
        for (CartonHeader carton : cartonHeaderList) {
            if (!carton.getStatus().equals(Constants.CartonStatus.PACK_OVER.getValue())) {
                throw new DeliveryException(DeliveryException.CARTON_STATUS_ERR_FOR_LOAD, carton.getCartonNo());
            }
            check3PLWeightAndBind(carton.getDoHeader(), carton);
        }
        for (DeliveryOrderHeader header : doHeaderList) {
            if (header == null) {
                throw new DeliveryException(DeliveryException.NO_DO_NO_MATCH_CARRIER_NO, modelCartonHeader.getCartonNo());
            } else if (Constants.ReleaseStatus.HOLD.getValue().equals(header.getReleaseStatus())) {
                throw new DeliveryException(DeliveryException.DO_ALREADY_FROZEN);
            } else if (header.getNeedCancel()) {
                throw new DeliveryException(DeliveryException.DO_NEED_CANCEL);
            }
            if (StringUtil.isNotIn(header.getStatus(), Constants.DoStatus.ALL_CARTON.getValue(), Constants.DoStatus.PART_LOAD.getValue())) {
                throw new DeliveryException(DeliveryException.DO_STATUS_CANNOT_LOAD1, header.getDoNo());
            }
        }
    }
    
    private void check3PLWeightAndBind(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        if (!(Constants.DoType.SELL.getValue().equals(doHeader.getDoType()))) {
            return;
        }
        Carrier carrier = carrierService.getCarrier(doHeader.getCarrierId());
        // 若配送商不需要称重和打印面单，则无需后面的校验
        if (!Constants.YesNo.YES.getValue().equals(carrier.getIsNeedWeightAndPrint())) {
            return;
        }
        // 判断是否已经绑定面单。
        if (StringUtil.isEmpty(cartonHeader.getWayBill())) {
            throw new DeliveryException(DeliveryException.CARTON_NOT_BIND, cartonHeader.getCartonNo());
        }
        
        if (SystemConfig.configIsOpen("delivery.load.need.weight", cartonHeader.getWarehouseId()) && Constants.YesNo.NO.getValue().intValue() == cartonHeader.getWeightFlag()) {
            throw new DeliveryException(DeliveryException.CARTON_NOT_WEIGHTED, cartonHeader.getCartonNo());
        }
    }
    
    @Override
    public List<LoadDetail> getLastDetails(Long loadId, int size) {
        return loadDAO.getLastDetails(loadId, size);
    }
    
    @Override
    public void handleAfterLoaded(List<List<Object>> resultDto, LoadHeader loadHeader) {
        for (List<Object> objects : resultDto) {
            Boolean isFirstLoaded = (Boolean) objects.get(1);
            Boolean isAllLoaded = (Boolean) objects.get(2);
            CartonHeader cartonHeader = (CartonHeader) objects.get(3);
            DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(cartonHeader.getDoHeaderId());
            // 如果订单是初次交接则需要调用OMS接口。
            if (isFirstLoaded) {
                //3.2.7改为每扫描一次调用一次。
                expFacadeService.sendDo2Oms(doHeader.getId(), doHeader.getStatus(), 1, Constants.DoStatus.ALL_CARTON.getValue().toString(), doHeader.getDoType());
            }
            //do类型的交接的时候回传tms，调拨类型的在发货的时候回传 ，rma和退货的不回传tms。
            if (Constants.LoadType.DO.getValue().equals(loadHeader.getLoadType())) {
                this.sendCartonAndDOTogether2TMS(doHeader.getId(), doHeader.getDoNo(), cartonHeader.getCartonNo(), isFirstLoaded);
            }
            if (isAllLoaded) {
                //交接后自动发货。
                this.manualDelivery(cartonHeader, loadHeader.getLoadType());
            }
        }
    }

    @Override
    public List<String> findCanLoadDo(Long loadId, String doNo) {
        DoHeaderFilter doHeaderFilter = new DoHeaderFilter();
        LoadHeader loadHeader = getLoadHeader(loadId);
        if (loadHeader != null) {
            doHeaderFilter.setDoType(convert2DoType(loadHeader.getLoadType()));
            if (LoadHeader.DivideType.CARRIER.getV().equals(loadHeader.getDivideType())) {
                doHeaderFilter.setCarrierId(loadHeader.getCarrierId());
            }
            if (LoadHeader.DivideType.LINE.getV().equals(loadHeader.getDivideType())) {
                doHeaderFilter.setLineId(loadHeader.getLineId());
            }
            if (DoType.RTV.getValue().equals(doHeaderFilter.getDoType())) {
                doHeaderFilter.setSupplierId(loadHeader.getSupplierId());
            }
            if (DoType.ALLOT.getValue().equals(doHeaderFilter.getDoType())) {
                doHeaderFilter.setTranInWhID(StringUtil.notNullString(loadHeader.getTranInWhID(), null));
            }
            doHeaderFilter.setLoadHeaderId(loadId);
            doHeaderFilter.setRightDoNo(doNo);
            doHeaderFilter.setStatusFrom(Constants.DoStatus.ALL_CARTON.getValue());
            doHeaderFilter.setStatusTo(Constants.DoStatus.ALL_CARTON.getValue());
        }
        DataPage<DoHeaderDto> dataPage = deliveryOrderService.query(doHeaderFilter, 0, 20);
        List<String> doNoList = new ArrayList<String>();
        for (DoHeaderDto dto : dataPage.getDataList()) {
            doNoList.add(dto.getDoNo());
        }
        return doNoList;
    }

    @Override
    public String printLoadDo(Long id) {
        List<PrintLoadDTO> dtos = getLoadPrintData(Arrays.asList(id));
        Map<String, Object> originalUnitProps = new HashMap<String, Object>();
        originalUnitProps.put("dtos", JSON.toJSONString(dtos));
        return PrintTemplateUtil.process(PrintConstants.PrintTemplate.LOAD.name(), originalUnitProps);
    }

    @Override
    @Transactional
    public void batchSetVechileNo(List<Long> ids, String vechileNo) {
        loadDAO.batchSetVechileNo(ids,vechileNo);
    }

    private List<PrintLoadDTO> getLoadPrintData(List<Long> ids) {
        List<PrintLoadDTO> dtos = new ArrayList<PrintLoadDTO>();
        for (Long loadId : ids) {
            LoadHeader header = loadDAO.get(loadId);
            PrintLoadDTO dto = new PrintLoadDTO();
            dto.setCreateBy(header.getUpdatedBy());
            dto.setDriverName(header.getDriverName());
            dto.setLoadNo(header.getLoadNo());
            dto.setVechileNo(header.getVechileNo());
            dto.setNotes(header.getNotes());
            dto.setWarehouseName(warehouseService.getLocalWarehouse().getShortName());
            List<LoadDetail> loadDetails = header.getLoadDetails();
            List<PrintLoadDetailDTO> detailList = new ArrayList<PrintLoadDetailDTO>();
            dto.setDetailList(detailList);
            for (LoadDetail loadDetail : loadDetails) {
                PrintLoadDetailDTO loadDetailDTO = new PrintLoadDetailDTO();
                DeliveryOrderHeader deliveryOrderHeader = loadDetail.getDeliveryOrderHeader();
                loadDetailDTO.setDoNo(deliveryOrderHeader.getOriginalSoCode());
                loadDetailDTO.setConsigneeName(deliveryOrderHeader.getConsigneeName());
                loadDetailDTO.setAddress(deliveryOrderHeader.getAddress());
                DecimalFormat df = new DecimalFormat("###0.00");
                String productAmount = StringUtil.convertToString(df.format(deliveryOrderHeader.getProductAmount() == null
                        ? BigDecimal.ZERO : deliveryOrderHeader.getProductAmount()), 18, "");
                loadDetailDTO.setAmount(productAmount);
                detailList.add(loadDetailDTO);
            }
            Collections.sort(detailList,new Comparator<PrintLoadDetailDTO>() {
                @Override
                public int compare(PrintLoadDetailDTO o1, PrintLoadDetailDTO o2) {
                    return o1.getAddress().compareTo(o2.getAddress());
                }
            });
            dtos.add(dto);
        }
        return dtos;
    }

    private String convert2DoType(String loadType) {
        if (Constants.LoadType.WL.getValue().equals(loadType)) {
            return Constants.DoType.SELL.getValue();
        }
        if (Constants.LoadType.TT.getValue().equals(loadType)) {
            return Constants.DoType.ALLOT.getValue();
        }
        if (Constants.LoadType.RTV.getValue().equals(loadType)) {
            return Constants.DoType.RTV.getValue();
        }
        if (Constants.LoadType.WHOLESALE.getValue().equals(loadType)) {
            return Constants.DoType.WHOLESALE.getValue();
        }
        return null;
    }
}