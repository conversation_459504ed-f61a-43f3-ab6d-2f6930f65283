package com.daxia.wms.delivery.deliveryorder.dto;

import java.io.Serializable;

@lombok.extern.slf4j.Slf4j
public class DoExStatusOpDto implements Serializable {

	private static final long serialVersionUID = -7161994210954280337L;
	private String status;
	private String releaseStatus;
	private String exceptionStatus;
	private String oprator;
	private String opType;
	
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getReleaseStatus() {
		return releaseStatus;
	}
	public void setReleaseStatus(String releaseStatus) {
		this.releaseStatus = releaseStatus;
	}
	public String getExceptionStatus() {
		return exceptionStatus;
	}
	public void setExceptionStatus(String exceptionStatus) {
		this.exceptionStatus = exceptionStatus;
	}
	public String getOprator() {
		return oprator;
	}
	public void setOprator(String oprator) {
		this.oprator = oprator;
	}
    
    public String getOpType() {
        return opType;
    }
    
    public void setOpType(String opType) {
        this.opType = opType;
    }
}
