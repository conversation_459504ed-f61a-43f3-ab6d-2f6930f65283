package com.daxia.wms.delivery.deliveryorder.job;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.recheck.entity.TempCarton;
import com.daxia.wms.delivery.recheck.service.TempCartonService;
import com.daxia.wms.delivery.recheck.service.impl.TempCartonServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.Component;
import org.jboss.seam.contexts.Lifecycle;

import java.util.List;
import java.util.concurrent.CountDownLatch;

@lombok.extern.slf4j.Slf4j
public class TempCartonTask extends Thread {

    private List<Long> doIdList; // 待分配订单

    private CountDownLatch threadSignal;
    private Long warehouseId;

    public TempCartonTask(int i, List<Long> doIdList, CountDownLatch threadSignal, Long warehouseId) {
        super("TempCartonTask" + i);
        this.doIdList = doIdList;
        this.threadSignal = threadSignal;
        this.warehouseId = warehouseId;
    }

    private void setup() {
        Lifecycle.beginCall();
    }

    @Override
    public void run() {
        try {
            setup();
            TempCartonService tempCartonService = ((TempCartonService) Component.getInstance(TempCartonServiceImpl.class));
            if (CollectionUtils.isNotEmpty(doIdList)) {
                ParamUtil.setCurrentWarehouseId(warehouseId);
                TempCarton tc;
                for (Long doId : doIdList) {
                    tc = tempCartonService.generateTempCarton(doId);
                    if (tc != null) {
                        tempCartonService.saveOrUpdate(tc);
                    }
                }
            }
        } finally {
            threadSignal.countDown();
            cleanup();
        }
    }

    private void cleanup() {
        Lifecycle.endCall();
    }
}