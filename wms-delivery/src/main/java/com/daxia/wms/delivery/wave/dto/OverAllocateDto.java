package com.daxia.wms.delivery.wave.dto;

import java.math.BigDecimal;

@lombok.extern.slf4j.Slf4j
public class OverAllocateDto {
    private Long stkId;
    
    private BigDecimal overQty;
    
    private String locCode;
    
    private String productName;


    private Long doId;

    private String doNo;

    private BigDecimal expectedQty;

    private BigDecimal qty;

    public Long getStkId() {
        return stkId;
    }
    
    public void setStkId(Long stkId) {
        this.stkId = stkId;
    }
    
    public BigDecimal getOverQty() {
        return overQty;
    }
    
    public void setOverQty(BigDecimal overQty) {
        this.overQty = overQty;
    }
    
    public String getLocCode() {
        return locCode;
    }
    
    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }


    public Long getDoId() {
        return doId;
    }

    public void setDoId(Long doId) {
        this.doId = doId;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public BigDecimal getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(BigDecimal expectedQty) {
        this.expectedQty = expectedQty;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }
}
