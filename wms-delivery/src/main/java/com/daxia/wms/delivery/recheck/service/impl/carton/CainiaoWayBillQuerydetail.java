package com.daxia.wms.delivery.recheck.service.impl.carton;

import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.master.entity.Carrier;
import com.daxia.wms.master.entity.CarrierCainiaoEx;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.CarrierCainiaoExService;
import com.daxia.wms.master.service.ShopInfoService;
import com.daxia.wms.master.service.SkuCache;
import com.daxia.wms.waybill.cainiao.CainiaoConstants;
import com.google.common.collect.Lists;
import com.taobao.api.ApiException;
import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.domain.WaybillDetailQueryInfo;
import com.taobao.api.domain.WaybillDetailQueryRequest;
import com.taobao.api.request.WlbWaybillIQuerydetailRequest;
import com.taobao.api.response.WlbWaybillIQuerydetailResponse;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.util.List;

@Name("cainiaoWayBillQuerydetail")
@lombok.extern.slf4j.Slf4j
public class CainiaoWayBillQuerydetail extends CainiaoWayBillBase {
    @In
    CarrierCainiaoExService carrierCainiaoExService;

    @In
    SkuCache skuCache;

    @In
    ShopInfoService shopInfoService;



    public List<WaybillDetailQueryInfo> reqeust(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        Carrier carrier = doHeader.getCarrier();
        CarrierCainiaoEx carrierCainiaoEx = carrierCainiaoExService.getByCarrier(carrier.getId());
        WarehouseCarrier warehouseCarrier = loadWarehouseCarrier(carrier.getId());

        WlbWaybillIQuerydetailRequest req = new WlbWaybillIQuerydetailRequest();
        req.setWaybillDetailQueryRequest(genWlbWaybillIQuerydetailRequest(doHeader, cartonHeader, carrierCainiaoEx));

        TaobaoClient client = new DefaultTaobaoClient(CainiaoConstants.getCainiaoConfig().getServerUrl(), warehouseCarrier.getAppKey(), warehouseCarrier.getAppSecret());
        try {
            WlbWaybillIQuerydetailResponse rsp = client.execute(req, warehouseCarrier.getAppToken());

            if (!rsp.isSuccess()) {
                log.error("Cainiao querydetail error, request:" + req.getWaybillDetailQueryRequest() + ", response: " + rsp.getBody());
                throw new DeliveryException(DeliveryException.WAYBILL_CAINIO_QUERYDETAIL_ERROR, rsp.getMsg() + ", " + rsp.getSubCode());
            }
            return rsp.getWaybillDetails();
        } catch (ApiException e) {
            log.error("Cainiao querydetail error! ", e);
            throw new DeliveryException(DeliveryException.WAYBILL_CAINIO_QUERYDETAIL_ERROR);
        }
    }

    private WaybillDetailQueryRequest genWlbWaybillIQuerydetailRequest(DeliveryOrderHeader doHeader, CartonHeader cartonHeader, CarrierCainiaoEx carrierCainiaoEx) {
        WaybillDetailQueryRequest waybillDetailQueryRequest = new WaybillDetailQueryRequest();
        waybillDetailQueryRequest.setQueryBy(1L);
        waybillDetailQueryRequest.setTradeOrderList(Lists.newArrayList(doHeader.getDoNo()));

        return waybillDetailQueryRequest;
    }
}
