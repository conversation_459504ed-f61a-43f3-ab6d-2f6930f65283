package com.daxia.wms.delivery.recheck.action;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSONObject;
import com.daxia.dubhe.api.internal.util.NumberUtils;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.cfg.AutoLoadAndDeliverCfg;
import com.daxia.framework.common.entity.CfgConfiguration;
import com.daxia.framework.common.log.LogUtil;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.service.UserAccountService;
import com.daxia.framework.system.service.impl.CacheService;
import com.daxia.framework.system.service.impl.CfgConfigurationServiceImpl;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoType;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.OrderFrozenException;
import com.daxia.wms.delivery.OrderNotInReCheckStatusException;
import com.daxia.wms.delivery.container.service.ContainerLogService;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.delivery.deliveryorder.dao.DoPrintDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoNotice;
import com.daxia.wms.delivery.deliveryorder.entity.DoPrint;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.DoNoticeService;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.service.InvoiceService;
import com.daxia.wms.delivery.invoice.service.PrintInvoiceService;
import com.daxia.wms.delivery.load.service.LoadService;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.service.PickHeaderService;
import com.daxia.wms.delivery.print.dto.PrintCfg;
import com.daxia.wms.delivery.print.service.carton.PrintCartonDispatcher;
import com.daxia.wms.delivery.recheck.dto.PackMaterialInfoDto;
import com.daxia.wms.delivery.recheck.dto.ReCheckCartonInfo;
import com.daxia.wms.delivery.recheck.dto.ReCheckRecord;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.entity.PackMaterialLog;
import com.daxia.wms.delivery.recheck.filter.CartonHeaderFilter;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.recheck.service.PackMaterialLogService;
import com.daxia.wms.delivery.recheck.service.ReCheckService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.invoice.util.HttpClientUtil;
import com.daxia.wms.master.MasterException;
import com.daxia.wms.master.component.BusinessCenterComponent;
import com.daxia.wms.master.dto.AutoCompleteDTO;
import com.daxia.wms.master.entity.*;
import com.daxia.wms.master.entity.dto.StoreInfoDTO;
import com.daxia.wms.master.service.*;
import com.daxia.wms.print.PrintConstants;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.stock.stock.service.TrsPickLogService;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.HttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.assertj.core.util.Lists;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.security.Restrict;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

import static com.daxia.wms.Constants.DoType.MPS_OUT;

/**
 * 对核拣装箱进行操作
 */
@Name("com.daxia.wms.delivery.reCheckAction")
@Restrict("#{identity.hasPermission('delivery.recheck')}")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ReCheckAction implements Serializable {

    @In
    ReCheckService reCheckService;
    @In
    TrsPickLogService trsPickLogService;
    @In
    CartonService cartonService;
    @In
    LoadService loadService;
    @In
    DeliveryOrderService deliveryOrderService;
    @In
    InvoiceService invoiceService;
    @In
    ExpFacadeService expFacadeService;
    @In
    ContainerService containerService;
    @In
    ContainerLogService containerLogService;
    @In
    PackMaterialLogService packMaterialLogService;
    @In
    DoNoticeService doNoticeService;
    @In
    LaborService laborService;
    @In
    PrintCartonDispatcher printCartonDispatcher;
    @In
    SkuScanService skuScanService;
    @In(value = "com.daxia.wms.delivery.invoice.printInvoiceService")
    PrintInvoiceService printInvoiceService;
    @In
    MaterialsService materialsService;
    @In
    PackingDeskService packingDeskService;
    @In
    PickHeaderService pickHeaderService;
    @In
    WaveService waveService;
    @In
    DoPrintDAO doPrintDAO;

    @In
    private UserAccountService userAccountService;
    @In
    private BusinessCenterComponent businessCenterComponent;

    private PrintData wayBillPrintData = new PrintData();
    private String printContent = "";
    private String printInvoiceContent = "";

    private CartonHeaderFilter cartonHeaderFilter;
    /**
     * 当前操作的发货单号
     */
    private String orderNo = "";

    private Long orderId;

    private String orderStatus;
    private String pcsStatus;

    private String doType = "";

    private String sortGridNo;

    private String sortContainerNo;// 分拣筐号

    private WaveHeader waveHeader;

    /**
     * 包装台号
     */
    private String packingDeskNo;

    /**
     * 确认装箱操作码
     */
    private String packConfirmCode;

    /**
     * 已完成核拣的箱数
     */
    private int finishedNo;
    /**
     * 已完成核减的箱号
     */
    private List<String> finishedCartonNoList;

    /**
     * 容器列表
     */
    private List<String> containerList;

    /**
     * 当前订单所有的商品的核拣记录，包括已核拣了多少，未核拣多少，及对应的商品信息
     */
    private List<ReCheckRecord> reCheckRecords;

    /**
     * 操作失败时，与do有关的错误信息
     */
    private String doErrorMessage;

    /**
     * 核拣某一商品失败时，与该商品有关的错误信息
     */
    private String reCheckErrorMessage;

    /**
     * 在核拣过程中，用户删除的已完成装箱的Id号，Id号为-1时表示为还没有存储的装箱数据
     */
    private Long clearedCartonId;

    /**
     * 要核拣过程中，用户删除的某一个商品的Id
     */
    private Long clearedProductId;

    private ReCheckCartonInfo currentReCheckCartonInfo;

    /**
     * 正在操作（增加或删除）的核拣记录，格式为： 1含序列号 ：skuId1/number1/12,232;skuId2/number2/12,212
     * 2不含序列号：skuId1/number1;skuId2/number2
     */
    private String operateCartonInfo;

    /**
     * 是否需要发票
     */
    private boolean needInvoice = false;

    //发票类型
    private Integer invoiceType = null;

    private boolean contentShow = false;

    private boolean departFlag = false;

    private boolean isRtv = false;

    private boolean dataError = false;

    private Integer isLackRecheck = YesNo.NO.getValue();

    private String invoiceNum;

    private String invoiceSeq;

    /**
     * 1----旧发票模式 2----3.1.9版本发票模式 3----3.2.0最新发票模式
     */
    private String isNewInvoice;

    /**
     * 3.2.0发票模式新增变量
     */
    private Long orderNeedInvoiceQty;// 订单所需发票数量

    private String containerNo;

    private String deliverDoNo;

    /**
     * 核拣装箱完成需要拆箱的数量(需开启配置项有效)
     */
    private Integer departCartonQty;

    /**
     * 包装时是否自动交接DO(配置项)
     */
    private Boolean isAutoDeliver = Boolean.FALSE;

    /**
     * 核拣装箱完成是否需要确认装箱数量标记
     */
    private Boolean confirmCartonQtyFlag = Boolean.FALSE;

    private BigDecimal doActWeight;// DO实际称重重量

    private BigDecimal doPlanWeight;// DO计算重量

    private BigDecimal packWeightGap;// DO包裹差距标准

    /**
     * 是否自动打印普通箱标签
     */
    private String isAutoPrintCfg;

    /**
     * 是否自动打印RTV标签
     */
    private String isAutoPrintRTV;

    /**
     * 是否自动打印调拨标签
     */
    private String isAutoPrintAllot;

    /**
     * 是否逐箱打印箱标签
     */
    private String isPrintCartonByCarton;

    /**
     * 是否自动打印箱标签
     */
    private String isAutoPrintCarton;

    private String isAutoPrintAfterPack;

    /**
     * 复核触发方式：DO_NO-发货单号触发，TRACKING_NO-物流单号触发
     */
    private String recheckTriggerType;

    private BigDecimal totalSkuQty;// 按商品总数核拣 商品总数

    private String recommendMaterialCfg;

    private String showRecommendMaterial;

    /**
     * 包材推荐
     */
    private String recommendMaterial = "";

    private String actPackMaterial;

    /**
     * 是否已扫描包材编码
     */
    private Boolean recommendFlag = Boolean.FALSE;

    /**
     * 复核同时称重weight.when.recheck
     */
    private Integer weightWhenRecheckFlag = 0;

    /**
     * 需要扫描包材个数
     */
    private Integer needScanPackQty;
    /**
     * 单次扫描包材号
     */
    private String scanPackMaterialNo;
    /**
     * 自动交接模式
     */
    private Integer autoDeliveryMode;


    /**
     * 打印参数
     */
    private PrintCfg printCfg;

    /**
     * 按商品核拣一码多品选择
     */
    private List<Sku> skuList = new ArrayList<Sku>();
    private String carrierName;
    private String carrierCode;
    /**
     * 店铺名称名称
     */
    private String shopName;

    private Integer isprint = 0;// 是否有购物清单 无纸化需求---从接口那边获取是否需要自动打印

    private String recheckType; // 实际核拣方式 DO:按发货单 SC:分拣筐 SKU:商品

    /**
     * 原始打印机信息
     */
    private Printer originalPrinter;

    private Boolean needSpvsnCode = false;

    /**
     * 恒温耗材扫描信息
     */
    @Setter
    @Getter
    private String innerMaterial;
    /**
     * 订单的全部商品
     */
    @Setter
    @Getter
    private String barcodes;

    private String recheckBy;

    private String noticeContent;

    private Integer useSerialManage;

    private String productBarCode;

    private String productSerial;

    private List<Materials> materialsList;

    private String skuHelperData;

    private String currentCartonNo;

    private BigDecimal currentCartonWeight;

    private Integer packDeskManageFlag;

    private Integer soundNoticeFlag;

    private String soundNoticeDesc;

    private String udfPrintContent;

    private String udfPrintJson;

    private String notesJson;

    private Boolean isSpecialCheck = false;

    private String checkByBtn = "1";

    private String loginName;

    private Integer inputBarCodeByScan = 0;

    Map<String, String> noteMap = new HashMap<String, String>();

    private Integer soundShopFlag;
    private String soundTok;
    private String password;
    /**
     * 录像文件名
     */
    private String recordFileName;

    public String getRecordFileName() {
        return recordFileName;
    }

    public void setRecordFileName(String recordFileName) {
        this.recordFileName = recordFileName;
    }

    @In
    CfgConfigurationServiceImpl cfgConfigurationService;
    @In
    CacheService cacheService;
    @In
    SkuService skuService;
    @In
    ContainerMgntService containerMgntService;

    private String serialSkuJson;
    private Integer needSplitBarcode;
    private String doStoreCode;


    public Boolean getNeedRecord() {
        return AppConfig.getProperty("camera.record.warehouseIds","").contains(ParamUtil.getCurrentWarehouseId()+"");
    }

    public ReCheckAction() {
        cartonHeaderFilter = new CartonHeaderFilter();
    }

    /**
     * 初始化参数，获取系统当前发票的模式
     */
    @Create
    public void init() {
        isNewInvoice = invoiceService.getInvoiceLevel();
        isAutoPrintCfg = SystemConfig.getConfigValue("ATUO_PRINT_CARTON", ParamUtil.getCurrentWarehouseId());
        isAutoPrintRTV = SystemConfig.getConfigValue("print.carton.autoPrintRTV", ParamUtil.getCurrentWarehouseId());
        isAutoPrintAllot = SystemConfig.getConfigValue("print.carton.autoPrintAllot", ParamUtil.getCurrentWarehouseId());
        isPrintCartonByCarton = SystemConfig.getConfigValue("print.carton.isPrintCartonByCarton", ParamUtil.getCurrentWarehouseId());
        isAutoPrintCarton = "0";
        isAutoPrintAfterPack = "1";
        this.packWeightGap = reCheckService.getPackWeightGap();
        recommendMaterialCfg = SystemConfig.getConfigValue("recheck.recommondMaterial", ParamUtil.getCurrentWarehouseId());
        showRecommendMaterial = Config.get(Keys.Delivery.recheck_show_recommend_material, Config.ConfigLevel.WAREHOUSE, YesNo.NO.getValue().toString());
        String autoNode = Config.getFmJson(Keys.Delivery.auto_load_and_deliver_cfg, Config.ConfigLevel.WAREHOUSE, AutoLoadAndDeliverCfg.type);
        //是否装箱和称重自动交接
        autoDeliveryMode = StringUtil.equals(autoNode, Constants.AutoLoadAndDeliverNode.PACK.getValue()) ? YesNo.YES.getValue() : YesNo.NO.getValue();
        noticeContent = null;
        needSplitBarcode = StringUtils.isBlank(SystemConfig.getConfigValue("barCode.split.script", ParamUtil.getCurrentWarehouseId())) ? YesNo.NO.getValue() : YesNo.YES.getValue();
        invoiceType = 0;
        useSerialManage = SystemConfig.getConfigValueInt("warehouse.serial.manage", ParamUtil.getCurrentWarehouseId());
        packConfirmCode = SystemConfig.getConfigValue("recheck.pack.confirm.code", ParamUtil.getCurrentWarehouseId());
        weightWhenRecheckFlag = SystemConfig.getConfigValueInt("weight.when.recheck", ParamUtil.getCurrentWarehouseId());
        packDeskManageFlag = SystemConfig.getConfigValueInt(ConfigKeys.RECHCEK_IS_MANAGE_PACK_DESK, ParamUtil.getCurrentWarehouseId());
        soundShopFlag = Config.getInt(Keys.Delivery.sound_shop_flag, Config.ConfigLevel.WAREHOUSE, 0);
        soundTok = Config.get(Keys.Delivery.sound_tok, Config.ConfigLevel.GLOBAL);
        materialsList = materialsService.queryPackMaterials();
        isSpecialCheck = false;
        inputBarCodeByScan = SystemConfig.getConfigValueInt("input.barCode.byScan.time", ParamUtil.getCurrentWarehouseId());
        checkByBtn = Config.isDefaultTrue(Keys.Delivery.checkByBtn, Config.ConfigLevel.WAREHOUSE) || userAccountService.havePermission(ParamUtil.getCurrentLoginName(), "delivery.recheck.checkByBtn") ? "1" : "0";
    }

    private void validateSoundTok() {
        if (YesNo.YES.getValue().equals(soundShopFlag) && StringUtil.isNotBlank(soundTok)) {
            Long time = NumberUtils.object2Long(soundTok.split("\\.")[3], 0L);
            if (time * 1000 < System.currentTimeMillis()) {
                HttpClient httpclient = new DefaultHttpClient();
                try {
                    String s = HttpClientUtil.excutePost(httpclient, "https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_id=zFuqo5SIS6nt4qnYlm24QuPa&client_secret=38b08773f620c8d8130b07fb31b8d726", new HashMap<String, String>());
                    String token = JsonUtil.getField(s, "access_token");
                    CfgConfiguration cfg = Config.getConfig("delivery.sound.tok", Config.ConfigLevel.GLOBAL);
                    cfg.setValueString(token);
                    cfgConfigurationService.update(cfg);
                    cacheService.clearCacheRegion("sys_config", "sys_cfg");
                    soundTok = token;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 获取当前核拣产品的错误信息
     *
     * @return 当前核拣产品的错误信息
     */
    public String getReCheckErrorMessage() {
        return reCheckErrorMessage;
    }

    /**
     * 重置参数
     */
    private void reset() {
        reCheckErrorMessage = "";
        doErrorMessage = "";
        sortGridNo = "";
        orderStatus = "";
        orderId = null;
        currentReCheckCartonInfo = null;
        reCheckRecords = null;
        operateCartonInfo = "";
        finishedNo = 0;
        finishedCartonNoList = null;
        containerList = null;
        needInvoice = false;
        invoiceType = 0;
        contentShow = false;
        isRtv = false;
        dataError = false;
        skuList.clear();
        carrierName = "";
        carrierCode = "";
        shopName = "";
        doActWeight = BigDecimal.ZERO;
        doPlanWeight = BigDecimal.ZERO;
        recommendFlag = Boolean.FALSE;
        actPackMaterial = "";
        clearPrintData();
        innerMaterial=null;
        isprint = 0;
        this.noticeContent = "";
        orderNeedInvoiceQty = 0L;
        currentCartonNo = null;
        soundNoticeFlag = YesNo.NO.getValue();
        soundNoticeDesc = "";
        isSpecialCheck = false;
        recheckBy = "";
        pcsStatus = null;
    }
    DeliveryOrderHeader doHeader;
    /**
     * 根据Do号查询发货单核拣
     *
     * @throws Exception
     */
    public void startCheckByDoNo() throws Exception {
        // 记录工时
        laborService.logWorkTime(Constants.JOB_CODE_RECHECK);

        recheckType = Constants.RecheckType.DO.getValue();
        reset();
        sortContainerNo = null;
        doHeader = deliveryOrderService.findDoHeaderByDoNoWithoutException(orderNo);
        recheckTriggerType = "DO_NO"; // 默认为发货单号触发

        if (doHeader == null) {
            doHeader = deliveryOrderService.getByTempCartonNo(orderNo);
            if (doHeader != null) {
                orderNo = doHeader.getDoNo();
            }
        }

        // 如果按发货单号没找到，尝试按物流单号查询
        if (doHeader == null) {
            doHeader = deliveryOrderService.findDoHeaderByTrackingNo(orderNo);
            if (doHeader != null) {
                // 找到订单后，将orderNo设置为发货单号，后续复用发货单号的校验逻辑
                orderNo = doHeader.getDoNo();
                recheckTriggerType = "TRACKING_NO"; // 标记为物流单号触发
            }
        }

        if(getNeedRecord()){
            // 设置文件名
            recordFileName =orderNo+"_"+System.currentTimeMillis();
            log.info("复核设置的录像文件名称为{}",recordFileName);
        }
        //支持兼容容器号核减
        Container c = null;
        if (doHeader == null) {
            c = containerService.getContainerByNo(orderNo);
            // TODO 待优化（如果启用DPS，那么需要判断是否需要人工分拣，如果人工）
            if (Config.isDefaultFalse(Keys.Delivery.pick_useDPS, Config.ConfigLevel.WAREHOUSE)) {
                String waveNo;
                if (c != null) {//支持录入容器号、容器关联的波次号开始复核
                    waveNo = c.getDocNo();
                    if (waveNo == null) { //如果容器已经释放，尝试从容器日志查找最近的单据进行操作
                        waveNo = containerLogService.getLaskBindDocNo(c.getContainerNo(), Constants.BindDocType.WAVE.getValue());
                    }
                } else {
                    waveNo = orderNo;
                }
                WaveHeader waveHeader = waveService.getWaveHeaderByWaveNum(waveNo);
                if (waveHeader == null) {//支持录入容器号、容器关联的波次号开始复核
                    throw new DeliveryException(MasterException.CONTAINER_NOT_EXIST);
                }
            
                List<DeliveryOrderHeader> deliveryOrderHeaderList = waveHeader.getDoHeaders();
                if (ListUtil.isNotEmpty(deliveryOrderHeaderList) && deliveryOrderHeaderList.size() == 1) {
                    doHeader = deliveryOrderHeaderList.get(0);
                    this.orderNo = doHeader.getDoNo();
                } else {
                    throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
                }
            } else if (c != null) {
                String pktNo = c.getRefNo1();
                PickHeader header = pickHeaderService.getPktHeaderByPktNo(pktNo);
                if (header == null || !PickHeader.PKT_TYPE_PCS.equals(header.getPktType())) {
                    throw new DeliveryException(DeliveryException.CONTAINER_TYPE_NOT_MATCH);
                }
                this.orderNo = c.getDocNo();
                doHeader = deliveryOrderService.findDoHeaderByDoNoWithoutException(orderNo);
            }
            recheckType = Constants.RecheckType.SC.getValue();
        }
    
        if (doHeader == null) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        
        //do取消拦截
        if(deliveryOrderService.doCancelIntercept(doHeader.getId())){
            this.doErrorMessage = DeliveryException.RECHECK_DO_IS_FROZEN;
            return;
        }

        doSomethingBeforeReCheck(doHeader);
    
        // TODO 待优化（如果启用DPS，那么需要判断是否需要人工分拣，如果人工）
        if (c != null && Config.isDefaultFalse(Keys.Delivery.pick_useDPS, Config.ConfigLevel.WAREHOUSE)) {
            // TODO  启用流水线的话，开始复核就自动释放周转箱
            containerMgntService.release(c);
        }
        
        if (doHeader != null && StringUtil.isEmpty(doHeader.getPackedBy())) {
            deliveryOrderService.logPackedBy(doHeader);
        }
        validateSoundTok();
    }

    public void getProductBarCodeBySerial() {
        this.productBarCode = "";

        if (StringUtil.isNotEmpty(this.getProductSerial())) {
            this.productBarCode = skuScanService.transformScanCode(this.getProductSerial());
        }
    }

    /**
     * 执行一些核减装箱的前置工作
     *
     * @param doHeader
     * @throws Exception
     */
    public void doSomethingBeforeReCheck(DeliveryOrderHeader doHeader) throws Exception {
        setDoStoreCode(doHeader.getStoreCode());
        setUdfPrintContent(null);
        setUdfPrintJson(null);
        if (validateReCheck(doHeader)) {
            DoNotice doNotice = this.doNoticeService.getLast(doHeader.getId());
            if (doNotice != null) {
                this.noticeContent = doNotice.getNoticeContent();
            } else {
                this.noticeContent = "";
            }

            //注意提示音标记及提示内容
            soundNoticeDesc = deliveryOrderService.getNoticeDesc(doHeader, Constants.NoticeOperateType.RECHECK.name());
            soundNoticeFlag = StringUtils.isNotEmpty(soundNoticeDesc) ? YesNo.YES.getValue() : YesNo.NO.getValue();

            orderNeedInvoiceQty = doHeader.getInvoiceQty();
            // 避免session 关闭，重新加载doHeader
            doHeader = deliveryOrderService.getDoHeaderById(doHeader.getId());
            setNomalInfo(doHeader);
            loadSkuHelperData(doHeader.getId());

            //toBtoC订单，未开启耗材开关的，按商家控制是否扫描耗材
            if (StringUtil.isIn(doHeader.getDoType(), DoType.SELL.getValue(), DoType.WHOLESALE.getValue()) &&
                    !YesNo.YES.getValue().toString().equals(recommendMaterialCfg)) {
                String merchantIds = SystemConfig.getConfigValue("recheck.recommondMaterial.merchantIds", ParamUtil.getCurrentWarehouseId());
                if (StringUtil.isNotEmpty(merchantIds) && doHeader.getMerchantId() != null &&
                        Arrays.asList(merchantIds.split(",")).contains(doHeader.getMerchantId().toString())) {
                    recommendMaterialCfg = YesNo.YES.getValue().toString();
                }
            }

            DoPrint doPrint = doPrintDAO.findByDoHeaderId(doHeader.getId(),"1");
            if (doPrint != null) {
                setUdfPrintContent(doPrint.getContent());
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("doNo",doHeader.getDoNo());
                jsonObject.put("originalSoCode",doHeader.getOriginalSoCode());
                jsonObject.put("waveNo",doHeader.getWaveHeader().getWaveNo());
                jsonObject.put("operator",ParamUtil.getCurrentLoginName());
                setUdfPrintJson(jsonObject.toJSONString());

                Map<String, Object> originalUnitProps = new HashMap<String, Object>();
                originalUnitProps.put("data", doPrint.getContent());
                setPrintContent(PrintTemplateUtil.process(PrintConstants.PrintTemplate.UDFPRINT.name(), originalUnitProps));
            }

        }
    }

    /**
     * 清空打印数据
     */
    public void clearPrintData() {
        printContent = "";
        printInvoiceContent = "";
        if (wayBillPrintData != null) {
            wayBillPrintData.clear();
        }
    }

    /**
     * 是否要包材推荐
     * 1.普通非团购订单需要包材推荐
     * 2.如果是团购订单需要看配置项
     */
    private void needRecommendMaterial(DeliveryOrderHeader doHeader) {
        recommendMaterialCfg = SystemConfig.getConfigValue("recheck.recommondMaterial", ParamUtil.getCurrentWarehouseId());
        if (YesNo.YES.getValue().toString().equals(recommendMaterialCfg)) {
            if (StringUtil.isNotIn(doType, DoType.SELL.getValue(), DoType.WHOLESALE.getValue())) {
                this.recommendMaterialCfg = YesNo.NO.getValue().toString();
            }
        }
    }

    private void setNomalInfo(DeliveryOrderHeader doHeader) {
        this.doType = doHeader.getDoType();
        this.orderId = doHeader.getId();
        this.sortGridNo = doHeader.getSortGridNo();
        Carrier carrier = doHeader.getCarrier();

        carrierName = carrier == null ? "" : carrier.getDistSuppCompName();
        carrierCode = carrier == null ? "" : carrier.getDistSuppCode();
        String storeCode = doHeader.getStoreCode();
        if(Objects.nonNull(storeCode)){
            // 避免获取店铺信息异常引起界面异常
            try{
                List<StoreInfoDTO> infoDTOS = businessCenterComponent.queryStoreListByStoreCodes(Lists.newArrayList(storeCode));
                if(CollectionUtils.isNotEmpty(infoDTOS)){
                   shopName= infoDTOS.get(0).getStoreName();
                }
            }catch (Exception e){
                log.warn("获取店铺名称异常{}",deliverDoNo);
            }
        }
        this.isRtv = (DoType.ALLOT.getValue().equals(doHeader.getDoType()) || DoType.RTV.getValue().equals(doHeader.getDoType()));
        // 设置订单需要发票数量、实际绑定发票成功数量等信息
        needInvoice(doHeader);
        // 获取核拣完成是否需要确认装箱数量配置项
        this.confirmCartonQtyFlag = false;
        //是否要包材推荐
        needRecommendMaterial(doHeader);
        // 获取已核拣箱数
        finishedCartonNoList = reCheckService.findCartonNoByDoId(doHeader.getId(), Constants.PackageType.B.getValue());
        finishedNo = finishedCartonNoList.size();
        // 商品毛重KJT仓库需要和称重核对
        doPlanWeight = doHeader.getGrossWt() == null ? BigDecimal.ZERO : doHeader.getGrossWt();

        //是否需要药监码
        needSpvsnCode = Boolean.FALSE;
        List<DeliveryOrderDetail> deliveryOrderDetails = doHeader.getDoDetails();
        JSONObject serialSkuObj = new JSONObject();
        HashSet<String> barcodesSet = Sets.newHashSet();
        for (DeliveryOrderDetail deliveryOrderDetail : deliveryOrderDetails) {
            Sku sku = deliveryOrderDetail.getSku();
            barcodesSet.add(sku.getEan13());
            serialSkuObj.put(sku.getProductCode(), sku.getSnQty());
            if (Integer.valueOf(1).equals(sku.getIsSpecialCheck())) {
                isSpecialCheck = true;
            }
            if ((DoType.ALLOT.getValue().equals(doHeader.getDoType()) || DoType.RTV.getValue().equals(doHeader.getDoType()))
                    && sku.getNeedSpvsnFlag()) {
                this.needSpvsnCode = Boolean.TRUE;
                continue;
            }
        }
        // 条码集合 用于简单判断溯源码扫描是否正确
        this.barcodes = StringUtils.join(barcodesSet, ",");
        setSerialSkuJson(serialSkuObj.toJSONString());

        //核捡装箱弹出提示系统参数
        String recheckMessageCfg = SystemConfig.getConfigValue("do.reCheck.alert.recheckMessage", ParamUtil.getCurrentWarehouseId());
        if(StringUtil.isNotEmpty(recheckMessageCfg) && YesNo.YES.getValue().toString().equals(recheckMessageCfg)){
            this.noteMap = reCheckService.getDetailNotes(doHeader.getId());
            setNotesJson(JSONUtils.toJSONString(noteMap));
        }

        // 获取发货单的已核拣和待核拣的信息
        getRecheckRecord();

        // isprint:1打 0不打
        isprint = 1;
        // 设置是否自动打印箱标签
        setIsAutoPrintCarton(doHeader);
        remarkPackBy(doHeader);
    }

    /**
     * 记录发货单的核拣人
     *
     * @param doHeader
     */
    private void remarkPackBy(DeliveryOrderHeader doHeader) {
        if (StringUtil.isEmpty(doHeader.getPackedBy())) {
            doHeader.setPackedBy(ParamUtil.getCurrentLoginName());
            deliveryOrderService.updateDoHeader(doHeader);
        }
    }

    /**
     * 根据订单类型决定是否自动打印箱标签
     *
     * @param doHeader
     */
    private void setIsAutoPrintCarton(DeliveryOrderHeader doHeader) {
        if (DoType.SELL.getValue().equals(doHeader.getDoType()) || DoType.WHOLESALE.getValue().equals(doHeader.getDoType())) {
            isAutoPrintCarton = isAutoPrintCfg == "" ? "0" : isAutoPrintCfg;
        } else if (DoType.RTV.getValue().equals(doHeader.getDoType())) {
            isAutoPrintCarton = isAutoPrintRTV == "" ? "0" : isAutoPrintRTV;
        } else if (DoType.ALLOT.getValue().equals(doHeader.getDoType())) {
            isAutoPrintCarton = isAutoPrintAllot == "" ? "0" : isAutoPrintAllot;
        } else {
            isAutoPrintCarton = "0";
        }

        //核拣完成后，是否自动打印箱标签
        isAutoPrintAfterPack = "1";
        String doNotPrintScript = SystemConfig.getConfigValue("delivery.doNotPrintAfterPack.script", ParamUtil.getCurrentWarehouseId());
        if (StringUtil.isNotEmpty(doNotPrintScript) && Boolean.TRUE.equals(MvelUtil.eval(doNotPrintScript, ImmutableMap.of("doHeader", (Object) doHeader)))) {
            isAutoPrintAfterPack = "0";
        }

        // 根据复核触发方式判断是否生成物流面单
        if ("TRACKING_NO".equals(recheckTriggerType)) {
            // 用物流单触发复核流程，不需要打印物流面单
            isAutoPrintAfterPack = "0";
        } else if ("DO_NO".equals(recheckTriggerType)) {
            // 用发货单触发复核流程，需判断是否有"打印购物清单"标记
            WaveHeader waveHeader = doHeader.getWaveHeader();
            if (waveHeader != null && FlagUtil.has(waveHeader.getPrintFlag().intValue(), WaveHeader.FLAG_PRINT_CARTON)) {
                // 有标记，不需要打印物流面单
                isAutoPrintAfterPack = "0";
            }
            // 无标记，需要打印物流面单（保持原有逻辑）
        }

    }

    /**
     * 校验订单是否能核拣。若不能，返回false，同时设置操作失败提示信息；若能，返回true
     *
     * @param doHeader
     * @return
     */
    private boolean validateReCheck(DeliveryOrderHeader doHeader) {
        if (doHeader == null) {
            doErrorMessage = "recheck.do.notexist";
            return false;
        }
        if (isNeedCheck(doHeader)) {
            if (reCheckService.isFrozen(doHeader)) {
                if (YesNo.YES.getValue().equals(trsPickLogService.checkIsPicked(doHeader.getId()))) {
                    doErrorMessage = "recheck.do.recheck.frozen.you";
                } else {
                    doErrorMessage = "recheck.do.recheck.frozen.wu";
                }
                return false;
            }
            if (doHeader.getNeedCancel()) {
                doErrorMessage = "recheck.do.recheck.cancel";
                return false;
            }
        }

        if (YesNo.YES.getValue().equals(doHeader.getLackStatus())) {
            doErrorMessage = "recheck.do.recheck.lack";
            return false;
        }
        this.orderStatus = doHeader.getStatus();
        boolean canReCheck = reCheckService.isOrderInReCheckStatus(doHeader, recheckType, PickHeader.PKT_TYPE_PCS);
        //如果是按照容器核减需要校验所有拣货任务是否已完成
        if (recheckType.equals(Constants.RecheckType.SC.getValue())) {
            if (Config.isDefaultFalse(Keys.Delivery.pick_useDPS, Config.ConfigLevel.WAREHOUSE)) {
                containerList = containerService.findContainerNosByWaveHeaderId(doHeader.getWaveHeader().getId());
            } else {
                containerList = containerService.findContainerNosByDoIdAndPktType(doHeader.getDoNo(), PickHeader.PKT_TYPE_PCS);
            }
        }
        if (!canReCheck) {
            if (Constants.DoStatus.ALL_DELIVER.getValue().equals(doHeader.getStatus())) {
                doErrorMessage = "recheck.invalid.do_status.all.deliver";
            } else if (Constants.DoStatus.ALL_CARTON.getValue().equals(doHeader.getStatus())) {
                doErrorMessage = "recheck.invalid.do_status.all.carton";
            } else {
                doErrorMessage = "recheck.invalid.do_status";
            }
            contentShow = true;
            return false;
        }
//        if (!validateCheckFlag(doHeader)) {
//            doErrorMessage = "recheck.do.userInfoNotFinish";
//            return false;
//        }
        return true;
    }

    private boolean isNeedCheck(DeliveryOrderHeader doHeader) {
        if (recheckType.equals(Constants.RecheckType.DO.getValue())) {
            return true;
        }
        if (!reCheckService.isPacked(doHeader, Constants.PackageType.B.getValue())) {
            return true;
        }
        return false;
    }

    private boolean validateCheckFlag(DeliveryOrderHeader doHeader) {
        // 3G%实名制订单&&已确认填写客户信息
        if (Constants.OrderRegisterFlag.CONTRACT_PHONE.getValue().equals(doHeader.getCheckFlag())
                || Constants.OrderRegisterFlag.PHONE_NO_NET_PURCHASING.getValue().equals(doHeader.getCheckFlag())
                || Constants.OrderRegisterFlag.REAL_NAME_NIC.getValue().equals(doHeader.getCheckFlag())) {
            return true;
        }
        return true;
    }

    /**
     * 根据do是否需要发票，设置发票的数量参数
     *
     * @param doHeader
     */
    private void needInvoice(DeliveryOrderHeader doHeader) {
        List<InvoiceHeader> invoiceHeadList = doHeader.getInvoiceHeaders();
        orderNeedInvoiceQty = doHeader.getInvoiceQty();

        if (ListUtil.isNotEmpty(invoiceHeadList)) {
            needInvoice = true;

            InvoiceHeader invoiceHeader = invoiceHeadList.get(0);
            if (invoiceHeader.getInvoiceType().equals(InvoiceHeader.InvoiceType.ELECTRONIC.getValue())) {
                invoiceType = Integer.valueOf(InvoiceHeader.InvoiceType.ELECTRONIC.getValue());
            } else if (invoiceHeader.getInvoiceType().equals(InvoiceHeader.InvoiceType.NORMAL.getValue())) {
                invoiceType = Integer.valueOf(InvoiceHeader.InvoiceType.NORMAL.getValue());
            }
        }
    }

    /**
     * 获取发货单的已核拣和待核拣的信息
     */
    public void getRecheckRecord() {
        // 获取核减记录信息之前，清除发票打印数据
        this.clearPrintData();
        innerMaterial= null;
        contentShow = false;
        recommendMaterial = null;
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(orderId);
        if (validateReCheck(doHeader)) {
            try {
                this.reCheckRecords = reCheckService.getReCheckRecord(orderId, Constants.PackageType.B.getValue());
                recommendMaterial(doHeader);
            } catch (Exception e) {
                log.error("GetRecheckRecord error", e);
                if (this.reCheckRecords != null) {
                    // 有异常清空核检记录，页面不显示核检记录
                    this.reCheckRecords.clear();
                }
                if (StringUtil.isEmpty(e.getMessage())) {
                    doErrorMessage = DeliveryException.SYSTEM_ERROR;
                } else {
                    doErrorMessage = e.getMessage();
                }
                return;
            }
            contentShow = true;
        }
    }

    /**
     * 包材推荐
     *
     * @param doHeader
     */
    private void recommendMaterial(DeliveryOrderHeader doHeader) {
        recommendMaterial = null;
        if (!Constants.YesNo.YES.getValue().toString().equals(recommendMaterialCfg) &&
                !Constants.YesNo.YES.getValue().toString().equals(showRecommendMaterial)) {
            return;
        }

        BigDecimal notPackVolume = BigDecimal.ZERO;
        if (Constants.DoStatus.ALLSORTED.getValue().equals(doHeader.getStatus())) {
            notPackVolume = doHeader.getVolume();
        } else {
            for (ReCheckRecord r : reCheckRecords) {
                BigDecimal need = r.getPendingCheckNumber();
                BigDecimal skuVolume = r.getVolume();
                notPackVolume = notPackVolume.add((need.multiply(skuVolume)));
            }
        }
        Map<String, Integer> recommendMap = reCheckService.recommendMaterial(notPackVolume);

        PackMaterialInfoDto dto = reCheckService.buildPackMaterialDto(recommendMap);
        recommendMaterial = dto.getRecommendStr();
        if (Constants.DoStatus.ALLSORTED.getValue().equals(doHeader.getStatus())) {
            PackMaterialLog packMaterialLog = packMaterialLogService.getByDoId(doHeader.getId());
            if (null != packMaterialLog) {
                packMaterialLog.setRecommendCount(dto.getTotalCount());
                packMaterialLog.setRecommendMaterial(recommendMaterial);
                packMaterialLogService.update(packMaterialLog);
            } else {
                packMaterialLog = new PackMaterialLog();
                packMaterialLog.setDoId(doHeader.getId());
                packMaterialLog.setRecommendMaterial(recommendMaterial);
                packMaterialLog.setRecommendCount(dto.getTotalCount());
                packMaterialLogService.savePackMaterialLog(packMaterialLog);
            }
        }
    }

    /**
     * 验证扫描的包材
     */
    public void doScanPackMaterial() {
        recommendFlag = Boolean.FALSE;
        reCheckService.checkPackMaterial(scanPackMaterialNo, needScanPackQty);
        actPackMaterial = scanPackMaterialNo;
        recommendFlag = Boolean.TRUE;
    }

    /**
     * 打印箱标签
     */
    public void printFace() {
        if(isAutoPrintAfterPack =="0" && Objects.equals(currentReCheckCartonInfo.getCartonNo(),doHeader.getTrackingNo())){
            return;
        }
        printContent = "";
        if (null != wayBillPrintData) {
            wayBillPrintData.clear();
        }

        this.wayBillPrintData = printCartonDispatcher.print(orderId, new ArrayList<Long>());
        if (wayBillPrintData != null) {
            printContent = wayBillPrintData.getTemplateJs();
        }
    }

    public void printOnFinish() {
        if(isAutoPrintAfterPack =="0"){
            return;
        }
        clearPrintData();
        innerMaterial= null;

        this.wayBillPrintData = printCartonDispatcher.print(orderId, new ArrayList<Long>());
        if (wayBillPrintData != null) {
            printContent = wayBillPrintData.getTemplateJs();
        }

        DeliveryOrderHeader deliveryOrderHeader = this.deliveryOrderService.getDoHeaderById(orderId);
        if (YesNo.YES.getValue().intValue() == deliveryOrderHeader.getInvoiceFlag().intValue() && SystemConfig.configIsOpen("print.invoice.autoPrint", ParamUtil.getCurrentWarehouseId())) {
            printInvoiceContent = printInvoiceService.printByDo(orderId);
        }
    }

    public void printCarton1By1() {
        if(isAutoPrintAfterPack =="0" && Objects.equals(currentReCheckCartonInfo.getCartonNo(),doHeader.getTrackingNo())){
            return;
        }
        innerMaterial= null;
        clearPrintData();
        Long cartonId = cartonService.getCartonByNo(currentReCheckCartonInfo.getCartonNo()).getId();
        this.wayBillPrintData = printCartonDispatcher.print(orderId, Arrays.asList(cartonId));
        if (wayBillPrintData != null) {
            printContent = wayBillPrintData.getTemplateJs();
        }
    }

    /**
     * 清空箱标签打印数据
     */
    public void cleareCartonPrintData() {
        printContent = "";
        if (wayBillPrintData != null) {
            wayBillPrintData.clear();
        }
    }

    /**
     * 客户端调用此方法创建新的箱号
     */
    public void createCartonNumber() {
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(orderId);
        if (doHeader == null) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        currentReCheckCartonInfo = createCarton();
    }

    /**
     * 创新装箱，并将其进行缓存
     *
     * @return
     */
    private ReCheckCartonInfo createCarton() {
        DeliveryOrderHeader doHeader = new DeliveryOrderHeader();
        doHeader.setId(orderId);
        ReCheckCartonInfo currentReCheckCartonInfo = new ReCheckCartonInfo(doHeader);
        currentReCheckCartonInfo.setDoNo(orderNo);
        return currentReCheckCartonInfo;
    }

    /**
     * 完成当前箱的包装，需要将当前箱的数据进行存储，这里还需要检查订单是否在操作期间被冻结
     */
    @Loggable
    public void completeCurrentPack() {
        printContent = "";
        if (wayBillPrintData != null) {
            wayBillPrintData.clear();
        }
        doErrorMessage = "";
        DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(orderNo);
        if (currentReCheckCartonInfo == null || operateCartonInfo == null) {
            //空箱时的缺发发货
            if (isLackRecheck.equals(YesNo.YES.getValue())) {
                List<CartonHeader> cartonHeaders = cartonService.findByDoNo(orderNo);
                if (CollectionUtils.isEmpty(cartonHeaders)) {
                    throw new DeliveryException(DeliveryException.DO_HAVE_NO_CARTON, orderNo);
                }
                //检查是否允许缺发
                if(YesNo.NO.getValue().equals(doHeader.getLackShipFlag())){
                    throw new DeliveryException(DeliveryException.DO_CAN_NOT_LACK_SHIP);
                }
                loadRecheckLack();
                orderStatus = reCheckService.resetDoStatusForEmpty(orderNo, Constants.PackageType.B.getValue(), recheckType);
                if(deliveryOrderService.doCancelIntercept(doHeader.getId())){
                    throw new DeliveryException(DeliveryException.RECHECK_DO_IS_FROZEN);
                }
                if (orderStatus.equals(Constants.DoStatus.ALL_CARTON.getValue())) {
//                    expFacadeService.sendDo2Oms(orderId, orderStatus, 1, Constants.DoStatus.ALL_CARTON.getValue(), doHeader.getDoType());
                }
                Boolean pscIsFinishedPack = reCheckService.isFinishedPack(doHeader, Constants.PackageType.B.getValue());
                pcsStatus = pscIsFinishedPack ? Constants.DoStatus.ALL_CARTON.getValue() : null;//判断散件是否装箱完成
                return;
            }
            return;
        }

        if (StringUtil.isBlank(getRecheckBy()) && isSpecialCheck) {
            doErrorMessage = "recheck.isSpecialCheck.RecheckBy.none";
            return;
        }

        if (StringUtil.isNotEmpty(this.packingDeskNo)) {
            currentReCheckCartonInfo.setPackingDeskNo(this.packingDeskNo);
        }

        currentReCheckCartonInfo.setPackMaterialNo(this.getActPackMaterial());

        // 核拣装箱完成，获取是否开启自动装箱拆箱功能配置项
        this.confirmCartonQtyFlag = false;

        boolean autoDelivery = YesNo.YES.getValue().equals(autoDeliveryMode)
                || Objects.equals(doHeader.getDoType(),MPS_OUT.getValue());
        currentReCheckCartonInfo.setNeedAutoDelivery(autoDelivery);

        currentReCheckCartonInfo.setRecheckBy(this.getRecheckBy());
        currentReCheckCartonInfo.setRecheckType(recheckType);
        currentReCheckCartonInfo.setPackageType(Constants.PackageType.B.getValue());
        // 构建装箱明细
        buildCurrentReCheckCartonInfo(currentReCheckCartonInfo);
        // 对核减记录按未核减数降序排序
        reCheckService.sortRecordsByNotPackedQty(reCheckRecords);
        //缺发发货
        if (isLackRecheck.equals(YesNo.YES.getValue())) {
            //检查是否允许缺发
            if(YesNo.NO.getValue().equals(doHeader.getLackShipFlag())){
                throw new DeliveryException(DeliveryException.DO_CAN_NOT_LACK_SHIP);
            }
            loadRecheckLack();
            if (StringUtil.isBlank(operateCartonInfo)) {//当前箱明细为空
                orderStatus = reCheckService.resetDoStatusForEmpty(orderNo, Constants.PackageType.B.getValue(), recheckType);
                if (orderStatus.equals(Constants.DoStatus.ALL_CARTON.getValue())) {
//                    expFacadeService.sendDo2Oms(orderId, orderStatus, 1, Constants.DoStatus.ALL_CARTON.getValue(), doHeader.getDoType());
                }
                Boolean pscIsFinishedPack = reCheckService.isFinishedPack(doHeader, Constants.PackageType.B.getValue());
                pcsStatus = pscIsFinishedPack ? Constants.DoStatus.ALL_CARTON.getValue() : null;//判断散件是否装箱完成
                return;
            }
        }
        String cartonNo = null;
        try {
            currentReCheckCartonInfo.setRecordFileName(recordFileName);
            currentReCheckCartonInfo.setInnerMaterial(innerMaterial);
            cartonNo = reCheckService.saveOneCartonReCheckRecord(currentReCheckCartonInfo);
        } catch (OrderFrozenException e) {
            doErrorMessage = "recheck.do.forzen.cartonfail";
            return;
        } catch (OrderNotInReCheckStatusException e) {
            orderStatus = e.getStatus();
            doErrorMessage = "recheck.invalid.do_status";
            return;
        } catch (BusinessException e) {
            if (DeliveryException.RECHECK_LOGIN_INFO_EXCEPTION.equals(e.getMessage())) {
                throw new DeliveryException(DeliveryException.RECHECK_LOGIN_INFO_EXCEPTION, e.getParams());
            }
            log.error(e.getMessage(), e);

            doErrorMessage = ResourceUtils.getDispalyString(e.getMessage(), e.getClass().getName(), e.getParams());
            if (StringUtil.isBlank(doErrorMessage)) {
                doErrorMessage = e.getMessage();
            }
            return;
        } catch (RuntimeException e) {
            log.error(e.getMessage(), e);
            throw e;
        }
        orderStatus = doHeader.getStatus();
        Boolean pscIsFinishedPack = reCheckService.isFinishedPack(doHeader, Constants.PackageType.B.getValue());
        pcsStatus = pscIsFinishedPack ? Constants.DoStatus.ALL_CARTON.getValue() : null;//判断散件是否装箱完成
        log.debug("completeCurrentPack: doHeaderId = " + doHeader.getId());

        // 核检装箱完成
        if (orderStatus.equals(Constants.DoStatus.ALL_CARTON.getValue())) {
            // 调用接口回写数据
            // expFacadeService.sendDo2Oms(orderId, orderStatus, 1, Constants.DoStatus.ALL_CARTON.getValue(), doHeader.getDoType());
//			expFacadeService.send2Ost(orderId, OstOrderStatus.OST_DO_CHECK, ParamUtil.getCurrentLoginName());
        }
        actPackMaterial = null;
        recommendFlag = Boolean.FALSE;
        finishedCartonNoList = reCheckService.findCartonNoByDoId(currentReCheckCartonInfo.getDoHeader().getId(), Constants.PackageType.B.getValue());
        finishedNo = finishedCartonNoList.size();
        this.currentCartonNo = currentReCheckCartonInfo.getCartonNo();
        if (YesNo.YES.getValue().equals(weightWhenRecheckFlag)) {
            saveCartonWeight(cartonNo);
        }
        // 逐箱打印箱标签，确认装箱同时，获取打印数据
        if (YesNo.YES.getValue().toString().equals(isAutoPrintCarton) && null != isPrintCartonByCarton && "1".equals(isPrintCartonByCarton)) {
            if (Constants.DoType.ALLOT.getValue().equals(doHeader.getDoType())) {
            }
        }

    }

    @Loggable
    public void saveCartonWeightClean() {
        doErrorMessage = null;
        saveCartonWeight(currentCartonNo);
    }

    @Loggable
    public void saveCartonWeight(String cartonNo) {
        //校验箱子状态
        CartonHeader ch = validateCarton(cartonNo);
        if (currentCartonWeight == null || currentCartonWeight.compareTo(BigDecimal.ZERO) <= 0
                || currentCartonWeight.compareTo(BigDecimal.valueOf(1000)) > 0) {
            doErrorMessage = DeliveryException.RECHECK_DO_WEIGHT_ERR;
            throw new DeliveryException(DeliveryException.RECHECK_DO_WEIGHT_ERR);
        }
        try {
            //称重并自动交接，实时同步状态到平台
            if (Config.get(Keys.Delivery.sync_do_node, Config.ConfigLevel.WAREHOUSE, Constants.SyncDoNode.NONE.getValue()).compareTo(Constants.SyncDoNode.WEIGHT.getValue()) <= 0) {
                if (StringUtils.isEmpty(ch.getWayBill())) {
                    throw new DeliveryException(DeliveryException.ERROR_WAYBILL_IS_NULL);
                }
                loadService.syncToPlatform(ch, ch.getDoHeader(), Constants.LoadType.WL.getValue(), Constants.YesNo.NO.getValue());
                ch.setAutoDeliveryFlag(Constants.YesNo.YES.getValue());
                cartonService.update(ch);
            }
            cartonService.saveCartonWeight(ch, currentCartonWeight, ParamUtil.getCurrentLoginName());
            //更新总重量到do中
            deliveryOrderService.upddateDoWeight(ch.getDoHeader().getId());
            //更新重量到菜鸟
            cartonService.syncWeightToCainiao(ch, currentCartonWeight);
        } catch (DeliveryException e) {
            log.error("",e);
            doErrorMessage = e.getMessage();
//            throw new DeliveryException(e.getMessage());
        } catch (Exception e) {
            log.error("",e);
            doErrorMessage = DeliveryException.DO_WEIGHT_ERR;
//            throw new DeliveryException(DeliveryException.DO_WEIGHT_ERR);
        }
        currentCartonNo = null;
        currentCartonWeight = null;
    }

    /**
     * 校验箱子
     *
     * @param cartonNo
     */
    private CartonHeader validateCarton(String cartonNo) {
        CartonHeader ch = cartonService.getCartonByNo(cartonNo);
        if (ch == null) {
            doErrorMessage = DeliveryException.CARTON_NO_EXIST;
            throw new DeliveryException(DeliveryException.CARTON_NO_EXIST, cartonNo);
        }
        if (Constants.ReleaseStatus.HOLD.getValue().equals(ch.getDoHeader().getReleaseStatus())) {
            doErrorMessage = DeliveryException.DO_ALREADY_FROZEN;
            throw new DeliveryException(DeliveryException.DO_ALREADY_FROZEN);
        }
        return ch;
    }

    /**
     * 核拣完成自动拆箱
     *
     * @throws Exception
     */
    public void departNewCartons() throws Exception {
        if (!this.confirmCartonQtyFlag) {
            return;
        }
        departFlag = false;

        int unitsQty = 0;
        for (ReCheckRecord record : reCheckRecords) {
            unitsQty = unitsQty + record.getNeedCheckNumber().intValue();
        }

        Boolean flag = YesNo.YES.getValue().equals(autoDeliveryMode);

        if (this.departCartonQty> 0) {
            try {
                if ((departCartonQty + finishedNo) > 2 * unitsQty) {
                    throw new DeliveryException(DeliveryException.CARTON_QTY_BEYOND_DOUBLE_UNITS);
                }
                reCheckService.departNewCartons(orderId, departCartonQty, actPackMaterial, flag, this.recheckBy);
            } catch (Exception e) {
                departFlag = false;
                throw e;
            }
        } else if (flag) {
            reCheckService.updateCartonAutoDeliveryFlagByDoId(orderId);
        }
        // 拆箱后京东的重设箱号。
        reCheckService.resetJDCartonNo(orderId);

        // 重新获取当前do的已装箱数，页面显示用
        finishedCartonNoList = reCheckService.findCartonNoByDoId(orderId, Constants.PackageType.B.getValue());
        finishedNo = finishedCartonNoList.size();

        currentReCheckCartonInfo = null;
        actPackMaterial = null;
        departFlag = true;
    }

    public void updateCartonAutoDeliveryFlag() {
        Boolean flag = YesNo.YES.getValue().equals(autoDeliveryMode);
        if (flag) {
            reCheckService.updateCartonAutoDeliveryFlagByDoId(orderId);
        }
    }

    /**
     * 给字符串加双引号
     *
     * @param s
     * @return
     */
    public String jsonString(String s) {
        if (s == null || s.length() == 0) {
            return "\"\"";
        }
        s = StringEscapeUtils.escapeJavaScript(s);
        return "\"" + s + "\"";
    }

    /**
     * 根据客户端传来的装箱信息构建装箱明细
     */
    private void buildCurrentReCheckCartonInfo(ReCheckCartonInfo currentReCheckCartonInfo) {
        Map<String, ClientCartonRecord> newCartonInfo = getNewCartonInfo();
        for (ReCheckRecord record : reCheckRecords) {
            ClientCartonRecord c = newCartonInfo.get(record.getUid());
            if (c == null) {
                continue;
            }
            currentReCheckCartonInfo.addReCheckRecord(record, c.getNumber());
            if (c.getSerials() == null) {
                continue;
            }
            for (String serial : c.getSerials()) {
                currentReCheckCartonInfo.addSerial(record.getProductId(),serial);
            }
        }
    }

    public void checkPackingDesk() {
        doErrorMessage = "";
        PackingDesk packingDesk = packingDeskService.getByNo(packingDeskNo);
        if (packingDesk == null) {
            doErrorMessage = "error.delivery.packDesk.not.exist";
        }
    }

    /**
     * 获取装箱信息
     *
     * @return
     */
    private Map<String, ClientCartonRecord> getNewCartonInfo() {
        if (operateCartonInfo == null || operateCartonInfo.length() == 0) {
            return Collections.emptyMap();
        }
        Map<String, ClientCartonRecord> result = new HashMap<String, ClientCartonRecord>();
        String[] skuInfos = operateCartonInfo.split(";");
        for (String skuInfo : skuInfos) {
            ClientCartonRecord c = ClientCartonRecord.parse(skuInfo);
            result.put(c.getId() + "_" + StringUtil.notNullString(c.getLotatt05()), c);
        }
        return result;
    }

    public String getOperateCartonInfo() {
        return operateCartonInfo;
    }

    /**
     * 设置当前正在操作的装箱信息。格式为：skuId/number;skuId/number。多个sku间用";"分隔，
     * 每个sku的装箱信息由sku的id和其在该箱中的数量两部分组成
     *
     * @param operateCartonInfo
     */
    public void setOperateCartonInfo(String operateCartonInfo) {
        this.operateCartonInfo = operateCartonInfo;
    }

    /**
     * 核拣缺货
     */
    public void lack() {
        String msg = "操作成功！缺货商品:";
        msg += reCheckService.recheckLack(orderNo);
        DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(orderNo);
        /** 冻结do时调scs重新计算预计出库时间接口 */
        expFacadeService.sendDoReleaseOrHold2Scs(doHeader.getId());
        throw new DeliveryException(msg);
    }

    public void validateUser() {
        this.recheckBy = "";
        this.recheckBy = userAccountService.verifyPassword(this.loginName, this.password).getLoginName();
    }

    public void loadRecheckLack() {
        //修改拣货数量
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(orderId);
        if (StringUtil.isNotIn(doHeader.getDoType(), DoType.WHOLESALE.getValue(), DoType.ALLOT.getValue(), DoType.RTV.getValue())) {
            throw new DeliveryException(DeliveryException.DO_TYPE_ERROR);
        }
        reCheckService.loadRecheckLack(reCheckRecords, PickHeader.PKT_TYPE_PCS);
    }

    /**
     * 封装客户端发送来的核拣记录
     */
    private static class ClientCartonRecord {

        private Long id;

        private BigDecimal number;

        private String lotatt05;

        private String[] serials;

        private String invoice;

        static ClientCartonRecord parse(String info) {
            ClientCartonRecord instance = new ClientCartonRecord();
            int invoiceIndex = info.indexOf("||");
            if (invoiceIndex != -1) {
                instance.invoice = info.substring(invoiceIndex + 2);
                info = info.substring(0, invoiceIndex);
            }
            String pair[] = info.split("\\?");
            String[] skuLot5 = pair[0].split("_");
            instance.id = Long.valueOf(skuLot5[0]);
            instance.lotatt05 = skuLot5.length <= 1 ? null : skuLot5[1];
            instance.number = NumberUtils.object2BigDecimal(pair[1]);
            if (pair.length < 3) {
                return instance;
            }
            Set<String> serials = Sets.newHashSet(pair[2].split(","));
            instance.serials = new String[serials.size()];
            int i = 0;
            for (String serial : serials) {
                instance.serials[i++] = serial;
            }
            return instance;
        }

        @SuppressWarnings("unused")
        public String getInvoice() {
            return invoice;
        }

        public Long getId() {
            return id;
        }

        public BigDecimal getNumber() {
            return number;
        }

        public String getLotatt05() {
            return lotatt05;
        }

        public String[] getSerials() {
            return serials;
        }
    }

    private void loadSkuHelperData(Long doId) {
        List<AutoCompleteDTO> autoCompleteDTOS = deliveryOrderService.findSkuHelperData(doId);
        this.skuHelperData = JsonUtil.objToString(autoCompleteDTOS);
    }

    public String getSkuHelperData() {
        return skuHelperData;
    }

    public void setSkuHelperData(String skuHelperData) {
        this.skuHelperData = skuHelperData;
    }

    public String getDoType() {
        return doType;
    }

    public void setDoType(String doType) {
        this.doType = doType;
    }

    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    public String getSortContainerNo() {
        return sortContainerNo;
    }

    public void setSortContainerNo(String sortContainerNo) {
        this.sortContainerNo = sortContainerNo;
    }

    public WaveHeader getWaveHeader() {
        return waveHeader;
    }

    public void setWaveHeader(WaveHeader waveHeader) {
        this.waveHeader = waveHeader;
    }

    public boolean isNeedInvoice() {
        return needInvoice;
    }

    public void setNeedInvoice(boolean needInvoice) {
        this.needInvoice = needInvoice;
    }

    public String getInvoiceNum() {
        return invoiceNum;
    }

    public void setInvoiceNum(String invoiceNum) {
        this.invoiceNum = invoiceNum;
    }

    public void setInvoiceSeq(String invoiceSeq) {
        this.invoiceSeq = invoiceSeq;
    }

    public String getInvoiceSeq() {
        return invoiceSeq;
    }

    public void setContentShow(boolean contentShow) {
        this.contentShow = contentShow;
    }

    public boolean getContentShow() {
        return this.contentShow;
    }

    public String getIsNewInvoice() {
        return isNewInvoice;
    }

    public void setIsNewInvoice(String isNewInvoice) {
        this.isNewInvoice = isNewInvoice;
    }

    public void setIsRtv(boolean isRtv) {
        this.isRtv = isRtv;
    }

    public boolean getIsRtv() {
        return this.isRtv;
    }

    public void setDataError(boolean dataError) {
        this.dataError = dataError;
    }

    public boolean getDataError() {
        return this.dataError;
    }

    public Long getOrderNeedInvoiceQty() {
        return orderNeedInvoiceQty;
    }

    public void setOrderNeedInvoiceQty(Long orderNeedInvoiceQty) {
        this.orderNeedInvoiceQty = orderNeedInvoiceQty;
    }

    public String getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }

    public String getDeliverDoNo() {
        return deliverDoNo;
    }

    public void setDeliverDoNo(String deliverDoNo) {
        this.deliverDoNo = deliverDoNo;
    }

    public PrintCfg getPrintCfg() {
        return printCfg;
    }

    public void setPrintCfg(PrintCfg printCfg) {
        this.printCfg = printCfg;
    }

    public Boolean getIsAutoDeliver() {
        return isAutoDeliver;
    }

    public void setIsAutoDeliver(Boolean isAutoDeliver) {
        this.isAutoDeliver = isAutoDeliver;
    }

    public BigDecimal getDoActWeight() {
        return doActWeight;
    }

    public void setDoActWeight(BigDecimal doActWeight) {
        this.doActWeight = doActWeight;
    }

    public BigDecimal getDoPlanWeight() {
        return doPlanWeight;
    }

    public void setDoPlanWeight(BigDecimal doPlanWeight) {
        this.doPlanWeight = doPlanWeight;
    }

    public BigDecimal getPackWeightGap() {
        return packWeightGap;
    }

    public void setPackWeightGap(BigDecimal packWeightGap) {
        this.packWeightGap = packWeightGap;
    }

    public BigDecimal getTotalSkuQty() {
        return totalSkuQty;
    }

    public void setTotalSkuQty(BigDecimal totalSkuQty) {
        this.totalSkuQty = totalSkuQty;
    }

    public List<Sku> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<Sku> skuList) {
        this.skuList = skuList;
    }

    public String getCarrierName() {
        return carrierName;
    }

    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName;
    }

    public String getCarrierCode() {
        return carrierCode;
    }

    public void setCarrierCode(String carrierCode) {
        this.carrierCode = carrierCode;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getRecheckType() {
        return recheckType;
    }

    public void setRecheckType(String recheckType) {
        this.recheckType = recheckType;
    }

    public boolean isDepartFlag() {
        return departFlag;
    }

    public void setDepartFlag(boolean departFlag) {
        this.departFlag = departFlag;
    }

    public String getRecommendMaterialCfg() {
        return recommendMaterialCfg;
    }

    public void setRecommendMaterialCfg(String recommendMaterialCfg) {
        this.recommendMaterialCfg = recommendMaterialCfg;
    }

    public String getActPackMaterial() {
        return actPackMaterial;
    }

    public void setActPackMaterial(String actPackMaterial) {
        this.actPackMaterial = actPackMaterial;
    }

    public String getRecommendMaterial() {
        return recommendMaterial;
    }

    public void setRecommendMaterial(String recommendMaterial) {
        this.recommendMaterial = recommendMaterial;
    }

    public Boolean getRecommendFlag() {
        return recommendFlag;
    }

    public void setRecommendFlag(Boolean recommendFlag) {
        this.recommendFlag = recommendFlag;
    }

    public Integer getNeedScanPackQty() {
        return needScanPackQty;
    }

    public void setNeedScanPackQty(Integer needScanPackQty) {
        this.needScanPackQty = needScanPackQty;
    }

    public String getScanPackMaterialNo() {
        return scanPackMaterialNo;
    }

    public void setScanPackMaterialNo(String scanPackMaterialNo) {
        this.scanPackMaterialNo = scanPackMaterialNo;
    }

    public String getIsPrintCartonByCarton() {
        return isPrintCartonByCarton;
    }

    public void setIsPrintCartonByCarton(String isPrintCartonByCarton) {
        this.isPrintCartonByCarton = isPrintCartonByCarton;
    }

    public Printer getOriginalPrinter() {
        return originalPrinter;
    }

    public void setOriginalPrinter(Printer originalPrinter) {
        this.originalPrinter = originalPrinter;
    }

    public Integer getAutoDeliveryMode() {
        return autoDeliveryMode;
    }

    public void setAutoDeliveryMode(Integer autoDeliveryMode) {
        this.autoDeliveryMode = autoDeliveryMode;
    }

    public String getIsAutoPrintCarton() {
        return isAutoPrintCarton;
    }

    public Integer getIsprint() {
        return isprint;
    }

    public void setIsprint(Integer isprint) {
        this.isprint = isprint;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public int getFinishedNo() {
        return finishedNo;
    }

    public void setFinishedNo(int finishedNo) {
        this.finishedNo = finishedNo;
    }

    public Long getClearedCartonId() {
        return clearedCartonId;
    }

    public void setClearedCartonId(Long clearedCartonId) {
        this.clearedCartonId = clearedCartonId;
    }

    public Long getClearedProductId() {
        return clearedProductId;
    }

    public void setClearedProductId(Long clearedProductId) {
        this.clearedProductId = clearedProductId;
    }

    public List<ReCheckRecord> getReCheckRecords() {
        return this.reCheckRecords;
    }

    public String getDoErrorMessage() {
        return doErrorMessage;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getDepartCartonQty() {
        return departCartonQty;
    }

    public void setDepartCartonQty(Integer departCartonQty) {
        this.departCartonQty = departCartonQty;
    }

    public Boolean getConfirmCartonQtyFlag() {
        return confirmCartonQtyFlag;
    }

    public void setConfirmCartonQtyFlag(Boolean confirmCartonQtyFlag) {
        this.confirmCartonQtyFlag = confirmCartonQtyFlag;
    }

    public PrintData getWayBillPrintData() {
        return wayBillPrintData;
    }

    public void setWayBillPrintData(PrintData wayBillPrintData) {
        this.wayBillPrintData = wayBillPrintData;
    }

    public Boolean getNeedSpvsnCode() {
        return needSpvsnCode;
    }

    public void setNeedSpvsnCode(Boolean needSpvsnCode) {
        this.needSpvsnCode = needSpvsnCode;
    }

    public String getRecheckBy() {
        return recheckBy;
    }

    public void setRecheckBy(String recheckBy) {
        this.recheckBy = recheckBy;
    }

    public String getNoticeContent() {
        return noticeContent;
    }

    public void setNoticeContent(String noticeContent) {
        this.noticeContent = noticeContent;
    }

    public Integer getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(Integer invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getPrintContent() {
        return printContent;
    }

    public void setPrintContent(String printContent) {
        this.printContent = printContent;
    }

    public Integer getUseSerialManage() {
        return useSerialManage;
    }

    public void setUseSerialManage(Integer useSerialManage) {
        this.useSerialManage = useSerialManage;
    }

    public String getProductBarCode() {
        return productBarCode;
    }

    public void setProductBarCode(String productBarCode) {
        this.productBarCode = productBarCode;
    }

    public String getProductSerial() {
        return productSerial;
    }

    public void setProductSerial(String productSerial) {
        this.productSerial = productSerial;
    }

    public String getPrintInvoiceContent() {
        return printInvoiceContent;
    }

    public void setPrintInvoiceContent(String printInvoiceContent) {
        this.printInvoiceContent = printInvoiceContent;
    }

    public String getIsAutoPrintAfterPack() {
        return isAutoPrintAfterPack;
    }

    public void setIsAutoPrintAfterPack(String isAutoPrintAfterPack) {
        this.isAutoPrintAfterPack = isAutoPrintAfterPack;
    }

    public List<Materials> getMaterialsList() {
        return materialsList;
    }

    public void setMaterialsList(List<Materials> materialsList) {
        this.materialsList = materialsList;
    }

    public String getPackConfirmCode() {
        return packConfirmCode;
    }

    public void setPackConfirmCode(String packConfirmCode) {
        this.packConfirmCode = packConfirmCode;
    }

    public String getCurrentCartonNo() {
        return currentCartonNo;
    }

    public void setCurrentCartonNo(String currentCartonNo) {
        this.currentCartonNo = currentCartonNo;
    }

    public BigDecimal getCurrentCartonWeight() {
        return currentCartonWeight;
    }

    public void setCurrentCartonWeight(BigDecimal currentCartonWeight) {
        this.currentCartonWeight = currentCartonWeight;
    }

    public Integer getWeightWhenRecheckFlag() {
        return weightWhenRecheckFlag;
    }

    public void setWeightWhenRecheckFlag(Integer weightWhenRecheckFlag) {
        this.weightWhenRecheckFlag = weightWhenRecheckFlag;
    }

    public String getPackingDeskNo() {
        return packingDeskNo;
    }

    public void setPackingDeskNo(String packingDeskNo) {
        this.packingDeskNo = packingDeskNo;
    }

    public Integer getPackDeskManageFlag() {
        return packDeskManageFlag;
    }

    public void setPackDeskManageFlag(Integer packDeskManageFlag) {
        this.packDeskManageFlag = packDeskManageFlag;
    }

    public Integer getSoundNoticeFlag() {
        return soundNoticeFlag;
    }

    public void setSoundNoticeFlag(Integer soundNoticeFlag) {
        this.soundNoticeFlag = soundNoticeFlag;
    }

    public String getSoundNoticeDesc() {
        return soundNoticeDesc;
    }

    public void setSoundNoticeDesc(String soundNoticeDesc) {
        this.soundNoticeDesc = soundNoticeDesc;
    }

    public List<String> getFinishedCartonNoList() {
        return finishedCartonNoList;
    }

    public void setFinishedCartonNoList(List<String> finishedCartonNoList) {
        this.finishedCartonNoList = finishedCartonNoList;
    }

    public List<String> getContainerList() {
        return containerList;
    }

    public void setContainerList(List<String> containerList) {
        this.containerList = containerList;
    }

    public Integer getIsLackRecheck() {
        return isLackRecheck;
    }

    public void setIsLackRecheck(Integer isLackRecheck) {
        this.isLackRecheck = isLackRecheck;
    }


    public Boolean getSpecialCheck() {
        return isSpecialCheck;
    }

    public void setSpecialCheck(Boolean specialCheck) {
        isSpecialCheck = specialCheck;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPcsStatus() {
        return pcsStatus;
    }

    public void setPcsStatus(String pcsStatus) {
        this.pcsStatus = pcsStatus;
    }

    public String getCheckByBtn() {
        return checkByBtn;
    }

    public void setCheckByBtn(String checkByBtn) {
        this.checkByBtn = checkByBtn;
    }

    public Integer getSoundShopFlag() {
        return soundShopFlag;
    }

    public void setSoundShopFlag(Integer soundShopFlag) {
        this.soundShopFlag = soundShopFlag;
    }

    public String getSoundTok() {
        return soundTok;
    }

    public void setSoundTok(String soundTok) {
        this.soundTok = soundTok;
    }

    public String getUdfPrintContent() {
        return udfPrintContent;
    }

    public void setUdfPrintContent(String udfPrintContent) {
        this.udfPrintContent = udfPrintContent;
    }

    public String getUdfPrintJson() {
        return udfPrintJson;
    }

    public void setUdfPrintJson(String udfPrintJson) {
        this.udfPrintJson = udfPrintJson;
    }

    public Map<String, String> getNoteMap() {
        return noteMap;
    }

    public void setNoteMap(Map<String, String> noteMap) {
        this.noteMap = noteMap;
    }

    public String getNotesJson() {
        return notesJson;
    }

    public void setNotesJson(String notesJson) {
        this.notesJson = notesJson;
    }

    public String getSerialSkuJson() {
        return serialSkuJson;
    }

    public void setSerialSkuJson(String serialSkuJson) {
        this.serialSkuJson = serialSkuJson;
    }

    public Integer getInputBarCodeByScan() {
        return inputBarCodeByScan;
    }

    public void setInputBarCodeByScan(Integer inputBarCodeByScan) {
        this.inputBarCodeByScan = inputBarCodeByScan;
    }
    
    public Integer getNeedSplitBarcode() {
        return needSplitBarcode;
    }
    
    public void setNeedSplitBarcode(Integer needSplitBarcode) {
        this.needSplitBarcode = needSplitBarcode;
    }

    public String getShowRecommendMaterial() {
        return showRecommendMaterial;
    }

    public void setShowRecommendMaterial(String showRecommendMaterial) {
        this.showRecommendMaterial = showRecommendMaterial;
    }

    /** 分箱按钮显示控制
     * 仓库配制子母件能力 且该订单的配送商支持分箱能力 则展示
     * @return
     */
    public boolean enableConfirmRecheck() {
        List<String> carrierSubCodes =
            Config.getByDelimit(Keys.Delivery.support_sub_carton_waybill_types, Config.ConfigLevel.GLOBAL);
        if (Objects.isNull(orderId) || CollectionUtils.isEmpty(carrierSubCodes)) {
            return false;
        }
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(orderId);
        Carrier carrier = doHeader.getCarrier();
        if(Objects.isNull(carrier)){
            return false;
        }
        Constants.WaybillType waybillType = carrier.getWaybillType();
        if (Objects.isNull(waybillType)) {
            return false;
        }
        return carrierSubCodes.contains(waybillType.name())
            && Config.isDefaultFalse(Keys.Delivery.confirm_recheck_enable, Config.ConfigLevel.WAREHOUSE);
    }

    public String getDoStoreCode() {
        return doStoreCode;
    }

    public void setDoStoreCode(String doStoreCode) {
        this.doStoreCode = doStoreCode;
    }
}