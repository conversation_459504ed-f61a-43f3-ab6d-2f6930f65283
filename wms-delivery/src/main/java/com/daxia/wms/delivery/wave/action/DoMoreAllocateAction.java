package com.daxia.wms.delivery.wave.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateHeader;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateService;
import com.daxia.wms.delivery.wave.dto.OverAllocateDto;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.tuple.Pair;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

@Name("com.daxia.wms.delivery.doMoreAllocateAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class DoMoreAllocateAction extends PagedListBean<OverAllocateDto> {
    
    private List<OverAllocateDto> dtoList = Lists.newArrayList();
    
    private Long doId;

    @In
    DoAllocateService doAllocateService;

    @Override
    public void query() {
        dtoList = doAllocateService.queryMoreAllocate();
    }

    public void cancelAllocate() {
        DoAllocateHeader header = doAllocateService.getHeader(doId);
        doAllocateService.cancelAssign(header, true, false);
        this.query();
        this.sayMessage(MESSAGE_SUCCESS);
    }
    
    public List<OverAllocateDto> getDtoList() {
        return dtoList;
    }
    
    public void setDtoList(List<OverAllocateDto> dtoList) {
        this.dtoList = dtoList;
    }

    public Long getDoId() {
        return doId;
    }

    public void setDoId(Long doId) {
        this.doId = doId;
    }
}
