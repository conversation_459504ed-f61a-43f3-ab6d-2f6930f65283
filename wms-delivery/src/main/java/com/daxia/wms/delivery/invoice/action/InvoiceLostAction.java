package com.daxia.wms.delivery.invoice.action;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.invoice.entity.InvoiceBook;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;
import com.daxia.wms.delivery.invoice.service.InvoiceBookService;
import com.daxia.wms.delivery.invoice.service.InvoiceNoService;
import com.daxia.wms.exp.service.ExpFacadeService;

/**
 
 * 发票遗失Action
 */
@Name("com.daxia.wms.delivery.invoiceLostAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class InvoiceLostAction extends PagedListBean<InvoiceNo> {

	private static final long serialVersionUID = -7098575504147556818L;
	//发票薄id
	private Long invoiceBookId;
	//发票薄
	private InvoiceBook invoiceBook;
	private boolean initialized = false;
	private String invoiceNoFm;
	private String invoiceNoTo;
	
	@In
	private InvoiceNoService invoiceNoService;
	@In
	private InvoiceBookService invoiceBookService;
	@In
	private ExpFacadeService expFacadeService;
	
    /**
     * 查询发票信息
     */
	@Override
	public void query() {
		if (null != invoiceBookId) {
			DataPage<InvoiceNo> dataPage = invoiceNoService.findInvoiceNoByInvoiceBookId(invoiceBookId, getStartIndex(), getPageSize());
			this.populateValues(dataPage);
		}
	}
	
    /**
     * 初始化页面显示信息
     */
	public void initPage() {
		if (!initialized) {
			invoiceBook = invoiceBookService.getInvoiceBookById(invoiceBookId);
			query();
			initialized = true;
		}
	}
	
    /**
     * 发票遗失
     */
	public void lost() {
		invoiceBook = invoiceNoService.invoiceNoLost(invoiceBookId, invoiceNoFm, invoiceNoTo);
		//同步丢失的发票号到后台
		this.invoiceNoFm = "";
		this.invoiceNoTo = "";
		query();
		this.sayMessage(MESSAGE_SUCCESS);
	}
	
	
	public Long getInvoiceBookId() {
		return invoiceBookId;
	}

	public void setInvoiceBookId(Long invoiceBookId) {
		this.invoiceBookId = invoiceBookId;
	}

	public InvoiceBook getInvoiceBook() {
		return invoiceBook;
	}

	public void setInvoiceBook(InvoiceBook invoiceBook) {
		this.invoiceBook = invoiceBook;
	}

	public String getInvoiceNoFm() {
		return invoiceNoFm;
	}

	public void setInvoiceNoFm(String invoiceNoFm) {
		this.invoiceNoFm = invoiceNoFm;
	}

	public String getInvoiceNoTo() {
		return invoiceNoTo;
	}

	public void setInvoiceNoTo(String invoiceNoTo) {
		this.invoiceNoTo = invoiceNoTo;
	}
}
