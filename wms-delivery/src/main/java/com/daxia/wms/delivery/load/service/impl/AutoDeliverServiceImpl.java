package com.daxia.wms.delivery.load.service.impl;


import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.cfg.AutoLoadAndDeliverCfg;
import com.daxia.framework.common.log.LogUtil;
import com.daxia.framework.common.log.StopWatch;
import com.daxia.framework.common.util.*;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.DoType;
import com.daxia.wms.Keys;
import com.daxia.wms.OrderLogConstants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.OrderLogService;
import com.daxia.wms.delivery.load.entity.LoadHeader;
import com.daxia.wms.delivery.load.entity.ReShipDo;
import com.daxia.wms.delivery.load.service.AutoDeliverService;
import com.daxia.wms.delivery.load.service.LoadService;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.exp.util.SysConfigHelper;
import com.daxia.wms.stock.stock.service.TransactionService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigInteger;
import java.util.*;

@Name("com.daxia.wms.delivery.autoDeliverService")
@lombok.extern.slf4j.Slf4j
public class AutoDeliverServiceImpl implements AutoDeliverService {

    @In
    private LoadService loadService;
    @In
    private OrderLogService orderLogService;
    @In
    private CartonService cartonService;
    @In
    private ExpFacadeService expFacadeService;
    @In
    private DeliveryOrderService deliveryOrderService;
    @In
    private TransactionService transactionService;

    /**
     * 自动发货定时任务。
     */
    @Override
    public boolean reShip(ReShipDo reShipDo) {
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(reShipDo.getDocId());
        if (doHeader == null) {
            return Boolean.FALSE;
        }
        // 过滤需要取消和未货物放行的单据
        String targetWarehouseIds = AppConfig.getProperty("auto.ship.check.customs.goods.warehouseIds", "");
        if(doHeader.getNeedCancel()||(Objects.equals(doHeader.getCartonHeaders().get(0).getGoodPass(),
                Constants.YesNo.NO)&& targetWarehouseIds.contains(String.valueOf(doHeader.getWarehouseId())))){
            return Boolean.FALSE;
        }
        //如果订单已经发货 ，则发货失败。
        if (!DoStatus.ALL_LOAD.getValue().equals(doHeader.getStatus()) && !DoStatus.PART_DELIVER.getValue().equals(doHeader.getStatus())) {
            return Boolean.FALSE;
        }
        // 自动发货
        Map<String, Object> autoParam = new HashMap<String, Object>();
        autoParam.put(LoadService.IS_AUTO, reShipDo.getIsAuto());
        autoParam.put(LoadService.SHIP_TIME, reShipDo.getOpTime());
        log.debug("DELIVER invokeAutoDeliver START DO {}", doHeader.getId());
        loadService.invokeAutoDeliver(doHeader, autoParam);
        log.debug("DELIVER invokeAutoDeliver END DO {}", doHeader.getId());

        if (log.isDebugEnabled()) {
        	BigInteger count = transactionService.getShipTransactionCount(doHeader.getId());
            log.debug("DELIVER invokeAutoDeliver transaction count {}", count.toString());
        }
        
        // 调用接口
//        loadService.callInterfaceWhenDeliver(doHeader);
        //记录日志，已出库
        orderLogService.saveLog(doHeader,
                OrderLogConstants.OrderLogType.SHIP_COMPLETE.getValue(),
                ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_MODIFY_SHIP_COMPLETE));
        return Boolean.TRUE;
    }
    
    /**
     * 自动交接发货
     * 
     * @param cartonNo
     * @param shipTime
     * @throws DeliveryException
     */
    @Override
    @Loggable
    public void autoLoadAndDeliver(String cartonNo, Integer loadMode, Date shipTime) throws DeliveryException {
    	StopWatch validateLoadStopWatch = new StopWatch("箱：'"+cartonNo+"'交接,流水交接前验证,插入或取得reship信息");
    	// 校验包裹和订单状态。
        CartonHeader ch = loadService.validateBeforeAutoDelivery(cartonNo);
        ParamUtil.setCurrentWarehouseId(ch.getWarehouseId());
        log.debug(validateLoadStopWatch.stop());
        ReShipDo reShipDo = loadService.findReShipDoByDoId(ch.getDoHeaderId(), loadMode);
        if (null == reShipDo) {
            reShipDo = new ReShipDo();
            reShipDo.setDocId(ch.getDoHeaderId());
			reShipDo.setCount(0L);
            reShipDo.setOpTime(shipTime);
            reShipDo.setIsAuto(loadMode);
        }
        // 自动交接
        Boolean needSend2Tms = Boolean.FALSE;
        StopWatch autoLoadStopWatch = new StopWatch("箱：'"+cartonNo+"'交接,流水交接");
        // 订单装入第一个包裹时向tms同步包裹与订单信息
        DeliveryOrderHeader doHeader = ch.getDoHeader();
        if (Constants.DoStatus.ALL_CARTON.getValue().equals(doHeader.getStatus())) {
            needSend2Tms = true;
        }
        log.debug(autoLoadStopWatch.stop());
        LoadHeader lh = loadService.autoLoad(ch, loadMode, reShipDo, shipTime);
        log.debug(autoLoadStopWatch.stop());
        StopWatch callInterfaceStopWatch = new StopWatch("箱：'"+cartonNo+"'交接,流水交接后回传接口");
        String newStatus = deliveryOrderService.getDoHeaderById(doHeader.getId()).getStatus();
        log.debug(callInterfaceStopWatch.stop());
		if (needSend2Tms) {
			// 给tms合并回传do和包裹信息。
            loadService.sendCartonAndDOTogether2TMS(ch.getDoHeaderId(), ch.getDoHeader().getDoNo(), ch.getCartonNo(), needSend2Tms);
//            expFacadeService.sendDo2Oms(doHeader.getId(), newStatus, 1, Constants.DoStatus.ALL_CARTON.getValue(), doHeader.getDoType());
        } else {
            if (!DoType.ALLOT.getValue().toString().equals(doHeader.getDoType())) {
                // 给tms合并回传do和包裹信息。
                loadService.sendCartonAndDOTogether2TMS(ch.getDoHeaderId(), ch.getDoHeader().getDoNo(), ch.getCartonNo(), needSend2Tms);
            }
		}
        
		// 给tms合并回传do和包裹信息。
		loadService.sendCartonAndDOTogether2TMS(ch.getDoHeaderId(), ch
				.getDoHeader().getDoNo(), ch.getCartonNo(), needSend2Tms);
		// 给wcs发送箱数据。自动交接不调用接口。
		log.debug(callInterfaceStopWatch.stop());
        StopWatch autoDeliveryStopWatch = new StopWatch("箱：'" + cartonNo + "'交接,流水交接后自动发货");
        // 包裹对应的do是否都已经交接完成
        Boolean isAllLoaded = cartonService.isDoCartonsAllLoaded(ch.getDoHeaderId());
        if (isAllLoaded) {
            boolean doAutoShip = (doHeader.getAutoFlag() & WaveHeader.FLAG_AUTO_SHIP) != 0;
            if (doAutoShip) {
                loadService.addShipCount(reShipDo);
            } else if(DoType.SELL.getValue().equals(ch.getDoHeader().getDoType()) && check4AutoShip(ch)){
                loadService.addShipCount(reShipDo);
            }else if(!DoType.SELL.getValue().equals(ch.getDoHeader().getDoType())) {
                String autoNode = Config.getFmJson(Keys.Delivery.auto_load_and_deliver_cfg, Config.ConfigLevel.WAREHOUSE, AutoLoadAndDeliverCfg.type);
                if (StringUtil.isInArrays(autoNode, Constants.AutoLoadAndDeliverNode.PACK.getValue(), Constants.AutoLoadAndDeliverNode.WEIGHT.getValue())) {
                    loadService.addShipCount(reShipDo);
                }
            }
            syncSerialNo(ch.getDoHeaderId(), doHeader.getDoNo());
            //记录日志，自动交接
            orderLogService.saveLog(doHeader,
                    OrderLogConstants.OrderLogType.LOAD_COMPLETE.getValue(),
                    ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_MODIFY_AUTO_LOAD));
        }
        
        // 完成装车单
        loadService.completeLoadHeader(lh);
        log.debug(autoDeliveryStopWatch.stop());
    }

    private boolean check4AutoShip(CartonHeader c) {
        DeliveryOrderHeader doHeader = c.getDoHeader();
        List<CartonHeader> cartonList = doHeader.getCartonHeaders();
        String carrierId = doHeader.getCarrierId().toString();
        String carrierBySelf = SystemConfig.getConfigValue("carrier.by.self", c.getWarehouseId());
        for(CartonHeader cc: cartonList){
            if (StringUtil.isEmpty(cc.getWayBill())) {
                if (StringUtil.isEmpty(carrierBySelf)) {
                    return false;
                }
                String[] carrierBySelfs = carrierBySelf.split(",");
                if (!Arrays.asList(carrierBySelfs).contains(carrierId)) {
                    return false;
                }
            }
        }
        return true;
    }


    private void syncSerialNo(Long doHeaderId, String doNo) {
        //发货后同步序列号
        if (!SysConfigHelper.getSwitchDefalutClosed("SW_SN_AFTER_DELIVER", ParamUtil.getCurrentWarehouseId())) {
            return;
        }
//        doSn2OmsExpSrv.sendNow(doHeaderId);
        expFacadeService.sendDoSnLog2OmsCreateDatas(doHeaderId, doNo);
    }

//    /**
//     * 是否自提订单或已经绑定了运单号
//     * @param c
//     * @return
//     */
//    private boolean isBySelfOrderOrHaveWaybill(CartonHeader c) {
//        //已绑定了运单，可以出库
//        if (StringUtil.isNotEmpty(c.getWayBill())) {
//            return true;
//        }
//
//        String carrierBySelf = SystemConfig.getConfigValue(
//                "carrier.by.self", c.getWarehouseId());
//
//        if (StringUtil.isEmpty(carrierBySelf)) {
//            return false;
//        }
//        String[] carrierBySelfs = carrierBySelf.split(",");
//        List kk = Arrays.asList(carrierBySelfs);
//        if (c.getDoHeader().getCarrierId() == null) {
//            return false;
//        }
//        return kk.contains(c.getDoHeader().getCarrierId().toString());
//    }
}
