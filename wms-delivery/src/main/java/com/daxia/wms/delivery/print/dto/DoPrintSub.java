package com.daxia.wms.delivery.print.dto;

import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.master.entity.Sku;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

@lombok.extern.slf4j.Slf4j
public class DoPrintSub {

	private Long doDetailId;

	private Sku sku;

	private BigDecimal price;

	private BigDecimal qty;

	private String lotNo;

	private Boolean isDoLeaf;

	private Boolean isPromote;

	private String parentId;

	private String category;

	private BigDecimal allocatedQty;

	private Long skuId;

	private String origDetailId;

	private BigDecimal expectedQty;
	// 供应商名称
	private String supplierName;
	// 失效日期
	private String validate;
	// 批次ID
	private Long supplierId;

	/**
	 * DO明细的商品在不同库位上的数量分布
	 */
	private Map<String, BigDecimal> qtyDistribution;

	public DoPrintSub() {
	}

	public DoPrintSub(BigDecimal qty, Long doDetailId, String lotNo) {
		this.qty = qty;
		this.setDoDetailId(doDetailId);
		this.lotNo = lotNo;
	}

	public DoPrintSub(DeliveryOrderDetail doDetail) {
		this.doDetailId = doDetail.getId();
		this.sku = doDetail.getSku();
		this.price = doDetail.getPrice();
		this.isDoLeaf = doDetail.getIsDoLeaf() == 1;
		this.isPromote = doDetail.getIsPromote() == 1;
		this.parentId = doDetail.getParentId();

		this.qty = doDetail.getExpectedQty();
		// 2012-08-27 added
		this.setAllocatedQty(doDetail.getAllocatedQty());
		this.setCategory(doDetail.getSku().getCategory().getCategoryName());
	}

	public DoPrintSub(BigDecimal qty, Long doDetailId, String lotNo, Long skuId, BigDecimal price, Integer isDoLeaf,
			Integer isPromote, String parentId, String origDetailId, BigDecimal expectedQty) {
		this.qty = qty;
		this.setDoDetailId(doDetailId);
		this.lotNo = lotNo;
		this.skuId = skuId;
		this.price = price;
		this.isDoLeaf = isDoLeaf == 1;
		this.isPromote = isPromote == 1;
		this.parentId = parentId;
		this.origDetailId = origDetailId;
		this.expectedQty = expectedQty;
	}

	public Boolean getIsDoLeaf() {
		return isDoLeaf;
	}

	public void setIsDoLeaf(Boolean isDoLeaf) {
		this.isDoLeaf = isDoLeaf;
	}

	public Boolean getIsPromote() {
		return isPromote;
	}

	public void setIsPromote(Boolean isPromote) {
		this.isPromote = isPromote;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public Sku getSku() {
		return sku;
	}

	public void setSku(Sku sku) {
		this.sku = sku;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getQty() {
		return qty;
	}

	public void setQty(BigDecimal qty) {
		this.qty = qty;
	}

	public void setLotNo(String lotNo) {
		this.lotNo = lotNo;
	}

	public String getLotNo() {
		return lotNo;
	}

	public void setDoDetailId(Long doDetailId) {
		this.doDetailId = doDetailId;
	}

	public Long getDoDetailId() {
		return doDetailId;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public BigDecimal getAllocatedQty() {
		return allocatedQty;
	}

	public void setAllocatedQty(BigDecimal allocatedQty) {
		this.allocatedQty = allocatedQty;
	}

	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	public String getOrigDetailId() {
		return origDetailId;
	}

	public void setOrigDetailId(String origDetailId) {
		this.origDetailId = origDetailId;
	}

	public BigDecimal getExpectedQty() {
		return expectedQty;
	}

	public void setExpectedQty(BigDecimal expectedQty) {
		this.expectedQty = expectedQty;
	}

	public String getSupplierName() {
		return supplierName;
	}

	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}

	public String getValidate() {
		return validate;
	}

	public void setValidate(String validate) {
		this.validate = validate;
	}

	public Long getSupplierId() {
		return supplierId;
	}

	public void setSupplierId(Long supplierId) {
		this.supplierId = supplierId;
	}

	/**
	 * 获取DO明细的商品在不同库位上的数量分布
	 * 
	 * @return 非Null的一个Map
	 */
	public Map<String, BigDecimal> getQtyDistribution() {
		if (qtyDistribution == null) {
			qtyDistribution = new HashMap<String, BigDecimal>();
		}
		return qtyDistribution;
	}

	/**
	 * 设置DO明细的商品在不同库位上的数量分布
	 * 
	 * @param qtyDistributionParam
	 */
	public void setQtyDistribution(Map<String, BigDecimal> qtyDistributionParam) {
		if (qtyDistributionParam != null) {
			for (Entry<String, BigDecimal> entry : qtyDistributionParam.entrySet()) {
				qtyDistribution.put(entry.getKey(), entry.getValue());
			}
		}
	}

}
