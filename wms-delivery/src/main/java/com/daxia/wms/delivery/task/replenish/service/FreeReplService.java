package com.daxia.wms.delivery.task.replenish.service;

import java.util.List;
import java.util.Set;

import com.daxia.wms.delivery.task.replenish.dto.LocationReplInfoDTO;

public interface FreeReplService {

	/**
	 * 生成一张补货单
	 * @param replType
	 * @param maximumPerDoc
	 * @param partitionId
	 * @param partitionType
	 * @param pId2
	 * @param doType
	 * @param isAuto
	 */
	public void createOneRplHeader(String replType, int maximumPerDoc, Long partitionId, String partitionType, Long pId2, String doType, Boolean isAuto);
}
