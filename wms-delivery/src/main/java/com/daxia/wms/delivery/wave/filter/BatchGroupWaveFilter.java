package com.daxia.wms.delivery.wave.filter;

import java.util.Date;

import com.daxia.framework.common.filter.WhBaseQueryFilter;

@lombok.extern.slf4j.Slf4j
public class BatchGroupWaveFilter extends WhBaseQueryFilter {

	private static final long serialVersionUID = -5296902803945738966L;

	/**
	 * 波次号from
	 */
	private String waveNoFrom;
	
	/**
	 * 波次号to
	 */
	private String waveNoTo;
	
	/**
	 * 波次箱标签打印状态
	 */
	private Integer catornPrintStatus;
	
	/**
	 * 波次创建时间from
	 */
	private Date fromDate;
	
	/**
	 * 波次创建时间to
	 */
	private Date toDate;

	public String getWaveNoFrom() {
		return waveNoFrom;
	}

	public void setWaveNoFrom(String waveNoFrom) {
		this.waveNoFrom = waveNoFrom;
	}

	public String getWaveNoTo() {
		return waveNoTo;
	}

	public void setWaveNoTo(String waveNoTo) {
		this.waveNoTo = waveNoTo;
	}

	public Integer getCatornPrintStatus() {
		return catornPrintStatus;
	}

	public void setCatornPrintStatus(Integer catornPrintStatus) {
		this.catornPrintStatus = catornPrintStatus;
	}

	public Date getFromDate() {
		return fromDate;
	}

	public void setFromDate(Date fromDate) {
		this.fromDate = fromDate;
	}

	public Date getToDate() {
		return toDate;
	}

	public void setToDate(Date toDate) {
		this.toDate = toDate;
	}
}
