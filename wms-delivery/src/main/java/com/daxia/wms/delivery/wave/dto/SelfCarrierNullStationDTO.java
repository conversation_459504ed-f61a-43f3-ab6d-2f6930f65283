package com.daxia.wms.delivery.wave.dto;

import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@lombok.extern.slf4j.Slf4j
public class SelfCarrierNullStationDTO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = -5054373949412038618L;
    /**
     * DO单号
     */
    private String doNo;
    /**
     * 发货单状态
     */
    private String status;

    /**
     * 订货数量 EXPECTED_QTY_EACH
     */
    private BigDecimal expectedQty;

    /**
     * DO创建时间
     */
    private Date doCreateTime;

    /**
     * DO类型
     */
    private String doType;
    /**
     * 是否半日达
     */
    private Integer isHalfDayDelivery;

    /**
     * 发票数量
     */
    private Long invoiceQty;

    /**
     * 应收款
     */
    private BigDecimal receivable;

    /**
     * 发票号码
     */
    private Integer sortBy;

    //发票状态
    private InvoiceHeader.InvoiceStatus invoiceStatus;
    //发票头
    private Long invoiceId;
    // do 头
    private Long doHeadId;

    private String reqErrorCode;
    //电子发票请求序列号
    private String reqSequenceNo;

    public SelfCarrierNullStationDTO(String doNo, String status, BigDecimal expectedQty, Date doCreateTime,
                                     String doType, Integer isHalfDayDelivery, Long invoiceQty, BigDecimal receivable) {
        this.doNo = doNo;
        this.status = status;
        this.expectedQty = expectedQty;
        this.doCreateTime = doCreateTime;
        this.doType = doType;
        this.isHalfDayDelivery = isHalfDayDelivery;
        this.invoiceQty = invoiceQty;
        this.receivable = receivable;
    }

    public SelfCarrierNullStationDTO(String doNo, String status, BigDecimal expectedQty, Date doCreateTime,
                                     String doType, Integer isHalfDayDelivery, Long invoiceQty, BigDecimal receivable, Integer sortBy, InvoiceHeader.InvoiceStatus invoiceStatus, Long invoiceId, String reqErrorCode,String reqSequenceNo) {
        this.doNo = doNo;
        this.status = status;
        this.expectedQty = expectedQty;
        this.doCreateTime = doCreateTime;
        this.doType = doType;
        this.isHalfDayDelivery = isHalfDayDelivery;
        this.invoiceQty = invoiceQty;
        this.receivable = receivable;
        this.sortBy = sortBy;
        this.invoiceStatus = invoiceStatus;
        this.invoiceId = invoiceId;
        this.reqErrorCode = reqErrorCode;
        this.reqSequenceNo = reqSequenceNo;
    }

    public String getDoNo() {
        return doNo;
    }


    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }


    public String getStatus() {
        return status;
    }


    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getExpectedQty() {
        return expectedQty;
    }


    public void setExpectedQty(BigDecimal expectedQty) {
        this.expectedQty = expectedQty;
    }

    public Date getDoCreateTime() {
        return doCreateTime;
    }


    public void setDoCreateTime(Date doCreateTime) {
        this.doCreateTime = doCreateTime;
    }


    public String getDoType() {
        return doType;
    }


    public void setDoType(String doType) {
        this.doType = doType;
    }


    public Integer getIsHalfDayDelivery() {
        return isHalfDayDelivery;
    }


    public void setIsHalfDayDelivery(Integer isHalfDayDelivery) {
        this.isHalfDayDelivery = isHalfDayDelivery;
    }


    public Long getInvoiceQty() {
        return invoiceQty;
    }


    public void setInvoiceQty(Long invoiceQty) {
        this.invoiceQty = invoiceQty;
    }


    public BigDecimal getReceivable() {
        return receivable;
    }


    public void setReceivable(BigDecimal receivable) {
        this.receivable = receivable;
    }

    public Integer getSortBy() {
        return sortBy;
    }

    public void setSortBy(Integer sortBy) {
        this.sortBy = sortBy;
    }

    public InvoiceHeader.InvoiceStatus getInvoiceStatus() {
        return invoiceStatus;
    }

    public void setInvoiceStatus(InvoiceHeader.InvoiceStatus invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }

    public Long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public Long getDoHeadId() {
        return doHeadId;
    }

    public void setDoHeadId(Long doHeadId) {
        this.doHeadId = doHeadId;
    }


    public String getReqErrorCode() {
        return reqErrorCode;
    }

    public void setReqErrorCode(String reqErrorCode) {
        this.reqErrorCode = reqErrorCode;
    }

	public String getReqSequenceNo() {
		return reqSequenceNo;
	}

	public void setReqSequenceNo(String reqSequenceNo) {
		this.reqSequenceNo = reqSequenceNo;
	}
    
}
