package com.daxia.wms.delivery.deliveryorder.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.master.entity.*;
import com.daxia.wms.stock.stock.entity.StockBatchAtt;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * DO单明细
 */
@Entity
@Table(name = "doc_do_detail")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_do_detail set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class DeliveryOrderDetail extends WhBaseEntity {

    private static final long   serialVersionUID = -2682108677551739904L;

    /**
    * 主键
    */
    private Long id;
    
    /**
     * 发货单Id
     */
    private Long doHeaderId;
    
    /**
    * 发货单行状态
    */
    private String lineStatus;

    /**
    * 产品Id
    */
    private Long skuId;
 
    /**
    * 期望发货数量
    */
    private BigDecimal expectedQty;

    /**
    * 包装
    */
    private Long packageId;

    /**
    * 单位
    */
    private String uom;

    /**
    * 生产日期
    */
    private String lotatt01;

    /**
    * 失效日期
    */
    private String lotatt02;

    /**
    * 
    */
    private String lotatt03;

    /**
    * 供应商ID
    */
    private String lotatt04;

    /**
    * 
    */
    private String lotatt05;

    /**
    * 货主ID、商家
    */
    private String lotatt06;

    /**
    * 
    */
    private String lotatt07;

    /**
    * 制造商
    */
    private String lotatt08;

    /**
    * 
    */
    private String lotatt09;

    /**
    * 
    */
    private String lotatt10;

    /**
    * 
    */
    private String  lotatt11;

    /**
    * 
    */
    private String lotatt12;

    /**
     * 批次属性13
     */
    private String lotatt13;
    /**
     * 批次属性14
     */
    private String lotatt14;
    /**
     * 批次属性15
     */
    private String lotatt15;
    /**
     * 批次属性16
     */
    private String lotatt16;
    /**
     * 批次属性17
     */
    private String lotatt17;
    
    /**
     * 分配规则
     */
    private Long allocationRule;

    /**
     * 体积
     */
    private BigDecimal volume;
    
    /**
     * 净重
     */
    private BigDecimal netweight;
    
    /**
     * 毛重
     */
    private BigDecimal grossweight;
    
    /**
     * 拣货区
     */
    private String pickzone;
    
    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
    * 分配数量
    */
    private BigDecimal allocatedQty;
    
	/**
	 * 拣货总数量(拣货主包装数量 + 拣货散件装数量)
	 */
    private BigDecimal pickedQty;
	
	/**
	 * 拣货主包装数量
	 */
	private BigDecimal pickedQtyUnit;

    /**
    * 分拣总数量(拣货主包装数量 + 分拣散件装数量)
    */
    private BigDecimal sortedQty;
	
	/**
	 * 分拣主包装数量
	 */
	private BigDecimal sortedQtyUnit;

    /**
     * 
     */
    private BigDecimal packedQty;
    
    /**
     * 发货数量
     */
    private BigDecimal shippedQty;
    
    /**
     * 用户自定义属性1
     */
    private String  userdefine1;
    
    /**
     * 用户自定义属性2
     */
    private String  userdefine2;
    
    /**
     * 用户自定义属性3
     */
    private String userdefine3;
    
    /**
     * 用户自定义属性4
     */
    private String userdefine4;
    
    /**
     * 备注
     */
    private String notes;

	private String reCheckMessage;
    /**
     * PACK_DETAIL_ID
     */
    private Long packDetailId;
    
    private PackageInfoDetail packageInfoDetail;
    
    /**
    * DoHeader对象
    */
    private DeliveryOrderHeader doHeader;
    
    private List<PickTask> pickTasks;
    
    private Sku  sku;
    
    private Supplier supplier;
    
    private Merchant merchant;
    
    private Manufacturer manufacturer;
    
    private PackageInfoHeader packageInfoHeader;
    
//    private Long origId;//原始单据ID
    private String origHeaderId;//原始单据头信息ID
    private String origDetailId;//原始单据明细信息ID
	private Integer isDoLeaf;
	private String parentId;//组合商品父商品ID
	private Integer isPromote;//是否是促销
	
	private Integer lineNo;
	/**
	 * 需要补货的数量
	 */
	private BigDecimal needReplQty;
	
	private Long targetWarehouseId;//目标仓库ID
	
	private Integer isValueables; //是否是贵重品
	
	private Integer isDamaged; //是否坏品
	
	private Integer wineFlag; //酒类随附单标记
	
	private String lotNo;//批次号
	
	private Integer isValidationRequired; //药网药品监管码是否需要扫描

	/**是否有处方药(1-有,0-没有)**/
	private Integer haveCfy;
	/** 处方药调拨单信息生成打标，1－已生成，0－未生成*/
	private Integer cfyHandleFlag;
	// 分配最少有效期天数。
	private Integer leastAvailableDay;

    private BigDecimal pickQty;

    private BigDecimal printExceptedQty;

	/**
	 * 销售等级 1 = 良品（A1、A2） 10 = 效期品（A3） 2 = 轻微残次（B1） 20=中度残次（B2）
	 */
	private Integer salesGrade;
	/**
	 * 货品等级 10 = 良品-优质效期（A1） 11 = 良品-告警期（A2） 12 = 良品-警告期（A3）
	 * 20 = 轻度残次（B1） 21 = 中度残次（B2） 22 = 销毁期（C） 23 = 未分类（D）
	 */
	private Integer goodsGrade;
	/**
	 * 最小失效日期
	 */
	private Date minExp;
	/**
	 * 最大失效日期
	 */
	private Date maxExp;
	@Column(name = "max_exp")
	public Date getMaxExp() {
		return maxExp;
	}

	public void setMaxExp(Date maxExp) {
		this.maxExp = maxExp;
	}

	@Column(name = "min_exp")
	public Date getMinExp() {
		return minExp;
	}
	@Column(name = "sales_grade")
	public Integer getSalesGrade() {
		return salesGrade;
	}

	public void setSalesGrade(Integer salesGrade) {
		this.salesGrade = salesGrade;
	}
	@Column(name = "goods_grade")
	public Integer getGoodsGrade() {
		return goodsGrade;
	}

	public void setGoodsGrade(Integer goodsGrade) {
		this.goodsGrade = goodsGrade;
	}

	public void setMinExp(Date minExp) {
		this.minExp = minExp;
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY, generator = "autoIdGenerator")
    @GenericGenerator(name = "autoIdGenerator", strategy = "com.daxia.framework.common.dao.AutoIdentityGenerator")  
    public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "DO_HEADER_ID", insertable = false, updatable = false)
	public Long getDoHeaderId() {
		return doHeaderId;
	}

	public void setDoHeaderId(Long doHeaderId) {
		this.doHeaderId = doHeaderId;
	}

	@Column(name = "LINESTATUS")
	public String getLineStatus() {
		return lineStatus;
	}

	public void setLineStatus(String lineStatus) {
		this.lineStatus = lineStatus;
	}

	/**
     * Getter method for property <tt>skuId</tt>.
     * 
     * @return property value of skuId
     */
	@Column(name = "SKU_ID")
    public Long getSkuId() {
        return skuId;
    }

    /**
     * Setter method for property <tt>skuId</tt>.
     * 
     * @param skuId value to be assigned to property skuId
     */
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    @Column(name = "EXPECTED_QTY")
	public BigDecimal getExpectedQty() {
		return expectedQty;
	}

	public void setExpectedQty(BigDecimal expectedQty) {
		this.expectedQty = expectedQty;
	}

	@Column(name = "PACKAGE_ID")
	public Long getPackageId() {
		return packageId;
	}

	public void setPackageId(Long packageId) {
		this.packageId = packageId;
	}

	@Column(name = "UOM")
	public String getUom() {
		return uom;
	}

	public void setUom(String uom) {
		this.uom = uom;
	}

	@Column(name = "LOTATT01")
	public String getLotatt01() {
		return lotatt01;
	}

	public void setLotatt01(String lotatt01) {
		this.lotatt01 = lotatt01;
	}

	@Column(name = "LOTATT02")
	public String getLotatt02() {
		return lotatt02;
	}

	public void setLotatt02(String lotatt02) {
		this.lotatt02 = lotatt02;
	}

	@Column(name = "LOTATT03")
	public String getLotatt03() {
		return lotatt03;
	}

	public void setLotatt03(String lotatt03) {
		this.lotatt03 = lotatt03;
	}

	@Column(name = "LOTATT04")
	public String getLotatt04() {
		return lotatt04;
	}

	public void setLotatt04(String lotatt04) {
		this.lotatt04 = lotatt04;
	}

	@Column(name = "LOTATT05")
	public String getLotatt05() {
		return lotatt05;
	}

	public void setLotatt05(String lotatt05) {
		this.lotatt05 = lotatt05;
	}

	@Column(name = "LOTATT06")
	public String getLotatt06() {
		return lotatt06;
	}

	public void setLotatt06(String lotatt06) {
		this.lotatt06 = lotatt06;
	}

	@Column(name = "LOTATT07")
	public String getLotatt07() {
		return lotatt07;
	}

	public void setLotatt07(String lotatt07) {
		this.lotatt07 = lotatt07;
	}

	@Column(name = "LOTATT08")
	public String getLotatt08() {
		return lotatt08;
	}

	public void setLotatt08(String lotatt08) {
		this.lotatt08 = lotatt08;
	}

	@Column(name = "LOTATT09")
	public String getLotatt09() {
		return lotatt09;
	}

	public void setLotatt09(String lotatt09) {
		this.lotatt09 = lotatt09;
	}

	@Column(name = "LOTATT10")
	public String getLotatt10() {
		return lotatt10;
	}

	public void setLotatt10(String lotatt10) {
		this.lotatt10 = lotatt10;
	}

	@Column(name = "LOTATT11")
	public String getLotatt11() {
		return lotatt11;
	}

	public void setLotatt11(String lotatt11) {
		this.lotatt11 = lotatt11;
	}

	@Column(name = "LOTATT12")
	public String getLotatt12() {
		return lotatt12;
	}

	public void setLotatt12(String lotatt12) {
		this.lotatt12 = lotatt12;
	}

    @Column(name="LOTATT13")
    public String getLotatt13() {
        return lotatt13;
    }

    public void setLotatt13(String lotatt13) {
        this.lotatt13 = lotatt13;
    }

    @Column(name="LOTATT14")
    public String getLotatt14() {
        return lotatt14;
    }

    public void setLotatt14(String lotatt14) {
        this.lotatt14 = lotatt14;
    }

    @Column(name="LOTATT15")
    public String getLotatt15() {
        return lotatt15;
    }

    public void setLotatt15(String lotatt15) {
        this.lotatt15 = lotatt15;
    }

    @Column(name="LOTATT16")
    public String getLotatt16() {
        return lotatt16;
    }

    public void setLotatt16(String lotatt16) {
        this.lotatt16 = lotatt16;
    }

    @Column(name="LOTATT17")
    public String getLotatt17() {
        return lotatt17;
    }

    public void setLotatt17(String lotatt17) {
        this.lotatt17 = lotatt17;
    }

	@Column(name = "ALLOCATION_RULE")
	public Long getAllocationRule() {
		return allocationRule;
	}

	public void setAllocationRule(Long allocationRule) {
		this.allocationRule = allocationRule;
	}

	@Column(name = "VOLUME")
	public BigDecimal getVolume() {
		return volume;
	}

	public void setVolume(BigDecimal volume) {
		this.volume = volume;
	}

	@Column(name = "NETWEIGHT")
	public BigDecimal getNetweight() {
		return netweight;
	}

	public void setNetweight(BigDecimal netweight) {
		this.netweight = netweight;
	}

	@Column(name = "GROSSWEIGHT")
	public BigDecimal getGrossweight() {
		return grossweight;
	}

	public void setGrossweight(BigDecimal grossweight) {
		this.grossweight = grossweight;
	}

	@Column(name = "PICKZONE")
	public String getPickzone() {
		return pickzone;
	}

	public void setPickzone(String pickzone) {
		this.pickzone = pickzone;
	}

	@Column(name = "PRICE")
	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	@Column(name = "ALLOCATED_QTY")
	public BigDecimal getAllocatedQty() {
		return allocatedQty;
	}

	public void setAllocatedQty(BigDecimal allocatedQty) {
		this.allocatedQty = allocatedQty;
	}

	@Column(name = "PICKED_QTY")
	public BigDecimal getPickedQty() {
		return pickedQty;
	}

	public void setPickedQty(BigDecimal pickedQty) {
		this.pickedQty = pickedQty;
	}
	
	@Column(name = "picked_qty_unit")
	public BigDecimal getPickedQtyUnit() {
		return pickedQtyUnit;
	}

	public void setPickedQtyUnit(BigDecimal pickedQtyUnit) {
		this.pickedQtyUnit = pickedQtyUnit;
	}

	@Column(name = "SORTED_QTY")
	public BigDecimal getSortedQty() {
		return sortedQty;
	}

	public void setSortedQty(BigDecimal sortedQty) {
		this.sortedQty = sortedQty;
	}
	
	@Column(name = "sorted_qty_unit")
	public BigDecimal getSortedQtyUnit() {
		return sortedQtyUnit;
	}

	public void setSortedQtyUnit(BigDecimal sortedQtyUnit) {
		this.sortedQtyUnit = sortedQtyUnit;
	}

	@Column(name = "PACKED_QTY")
	public BigDecimal getPackedQty() {
		return packedQty;
	}

	public void setPackedQty(BigDecimal packedQty) {
		this.packedQty = packedQty;
	}

	@Column(name = "SHIPPED_QTY")
	public BigDecimal getShippedQty() {
		return shippedQty;
	}

	public void setShippedQty(BigDecimal shippedQty) {
		this.shippedQty = shippedQty;
	}

	@Column(name = "USERDEFINE1")
	public String getUserdefine1() {
		return userdefine1;
	}

	public void setUserdefine1(String userdefine1) {
		this.userdefine1 = userdefine1;
	}

	@Column(name = "USERDEFINE2")
	public String getUserdefine2() {
		return userdefine2;
	}

	public void setUserdefine2(String userdefine2) {
		this.userdefine2 = userdefine2;
	}

	@Column(name = "USERDEFINE3")
	public String getUserdefine3() {
		return userdefine3;
	}

	public void setUserdefine3(String userdefine3) {
		this.userdefine3 = userdefine3;
	}

	@Column(name = "USERDEFINE4")
	public String getUserdefine4() {
		return userdefine4;
	}

	public void setUserdefine4(String userdefine4) {
		this.userdefine4 = userdefine4;
	}

	@Column(name = "NOTES")
	public String getNotes() {
		return notes;
	}

	public void setNotes(String notes) {
		this.notes = notes;
	}
	@Transient
	public BigDecimal getUnSortedQty() {
		return allocatedQty.subtract(sortedQty);
	}

	public void setUnSortedQty(BigDecimal unSortedQty) {
	}

	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DO_HEADER_ID")
    @Where(clause = " IS_DELETED = 0 ")
    public DeliveryOrderHeader getDoHeader() {
        return doHeader;
    }

    public void setDoHeader(DeliveryOrderHeader doHeader) {
        this.doHeader = doHeader;
    }

	@OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SKU_ID",insertable=false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public Sku getSku() {
        return sku;
    }

    public void setSku(Sku sku) {
        this.sku = sku;
    }
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "LOTATT04",insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public Supplier getSupplier() {
        return supplier;
    }
    public void setSupplier(Supplier supplier) {
        this.supplier = supplier;
    }
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "LOTATT06",insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public Merchant getMerchant() {
        return merchant;
    }
    public void setMerchant(Merchant merchant) {
        this.merchant = merchant;
    }

    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = " PACKAGE_ID",insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public PackageInfoHeader getPackageInfoHeader() {
        return packageInfoHeader;
    }

    public void setPackageInfoHeader(PackageInfoHeader packageInfoHeader) {
        this.packageInfoHeader = packageInfoHeader;
    }

	/**
	 * @return the origHeaderId
	 */
	@Column(name = "ORIG_HEADER_ID")
	public String getOrigHeaderId() {
		return origHeaderId;
	}

	/**
	 * @param origHeaderId the origHeaderId to set
	 */
	public void setOrigHeaderId(String origHeaderId) {
		this.origHeaderId = origHeaderId;
	}

	@Column(name = "PACK_DETAIL_ID")
	public Long getPackDetailId() {
		return packDetailId;
	}

	public void setPackDetailId(Long packDetailId) {
		this.packDetailId = packDetailId;
	}

	@Column(name = "ORIG_DETAIL_ID")
    public String getOrigDetailId() {
        return origDetailId;
    }

    public void setOrigDetailId(String origDetailId) {
        this.origDetailId = origDetailId;
    }
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PACK_DETAIL_ID",insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public PackageInfoDetail getPackageInfoDetail() {
        return packageInfoDetail;
    }
    public void setPackageInfoDetail(PackageInfoDetail packageInfoDetail) {
        this.packageInfoDetail = packageInfoDetail;
    }
    
	@Column(name = "IS_DO_LEAF")
	public Integer getIsDoLeaf() {
		return isDoLeaf;
	}

	public void setIsDoLeaf(Integer isDoLeaf) {
		this.isDoLeaf = isDoLeaf;
	}

	/**
	 *组合商品父商品ID
 	 *关联关系到doDTL的origId
	 * @return
	 */
	@Column(name = "PARENT_ID")
	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}
	 
	@Column(name = "IS_PROMOTE")
	public Integer getIsPromote() {
		return isPromote;
	}

	public void setIsPromote(Integer isPromote) {
		this.isPromote = isPromote;
	}


	@Transient
	public Integer getLineNo() {
		return lineNo;
	}

	public void setLineNo(Integer lineNo) {
		this.lineNo = lineNo;
	}
	
    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "DOC_LINE_ID")
    @Where(clause = " IS_DELETED = 0 ")
	public List<PickTask> getPickTasks() {
		return pickTasks;
	}
	
	public void setPickTasks(List<PickTask> pickTasks) {
		this.pickTasks = pickTasks;
	}

	/**
	 * @return 需要补货的数量
	 */
	@Column(name="NEED_REPL_QTY")
	public BigDecimal getNeedReplQty() {
		return needReplQty;
	}

	public void setNeedReplQty(BigDecimal needReplQty) {
		this.needReplQty = needReplQty;
	}
	@Transient
	public Long getTargetWarehouseId() {
		return targetWarehouseId;
	}

	public void setTargetWarehouseId(Long targetWarehouseId) {
		this.targetWarehouseId = targetWarehouseId;
	}

	@Column(name="IS_VALUEABLES")
    public Integer getIsValueables() {
        return isValueables;
    }

    public void setIsValueables(Integer isValueables) {
        this.isValueables = isValueables;
    }

    @Column(name="IS_DAMAGED")
    public Integer getIsDamaged() {
        return isDamaged;
    }

    public void setIsDamaged(Integer isDamaged) {
        this.isDamaged = isDamaged;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "LOTATT08",insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public Manufacturer getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(Manufacturer manufacturer) {
        this.manufacturer = manufacturer;
    }

    @Column(name="WINE_FLAG")
	public Integer getWineFlag() {
		return wineFlag;
	}

	public void setWineFlag(Integer wineFlag) {
		this.wineFlag = wineFlag;
	}

    @Column(name="LOT_NO")
    public String getLotNo() {
        return lotNo;
    }

    public void setLotNo(String lotNo) {
        this.lotNo = lotNo;
    }

    @Column(name="IS_VALIDATION_REQUIRED")
	public Integer getIsValidationRequired() {
		return isValidationRequired;
	}

	public void setIsValidationRequired(Integer isValidationRequired) {
		this.isValidationRequired = isValidationRequired;
	}

	@Column(name = "have_cfy")
	public Integer getHaveCfy() {
		return haveCfy;
	}

	public void setHaveCfy(Integer haveCfy) {
		this.haveCfy = haveCfy;
	}

	@Column(name = "cfy_handle_flag")
	public Integer getCfyHandleFlag() {
		return cfyHandleFlag;
	}

	public void setCfyHandleFlag(Integer cfyHandleFlag) {
		this.cfyHandleFlag = cfyHandleFlag;
	}
	
	@Column(name="LEAST_AVAILABLE_DAY")
	public Integer getLeastAvailableDay() {
		return leastAvailableDay;
	}

	public void setLeastAvailableDay(Integer leastAvailableDay) {
		this.leastAvailableDay = leastAvailableDay;
	}

    @Column(name="pick_qty")
    public BigDecimal getPickQty() {
        return pickQty;
    }

    public void setPickQty(BigDecimal pickQty) {
        this.pickQty = pickQty;
    }

	@Column(name="recheck_message")
	public String getReCheckMessage() {
		return reCheckMessage;
	}

	public void setReCheckMessage(String reCheckMessage) {
		this.reCheckMessage = reCheckMessage;
	}

	@Transient
    public BigDecimal getPrintExceptedQty() {
        return printExceptedQty == null ? getExpectedQty():printExceptedQty;
    }

    public void setPrintExceptedQty(BigDecimal printExceptedQty) {
        this.printExceptedQty = printExceptedQty;
    }

    public void setBatchAttrs(StockBatchAtt stockBatchAtt) {
    	if (stockBatchAtt == null) {
    		return;
    	}
    	
    	this.setLotatt01(stockBatchAtt.getLotatt01());
    	this.setLotatt02(stockBatchAtt.getLotatt02());
    	this.setLotatt03(stockBatchAtt.getLotatt03());
    	this.setLotatt04(stockBatchAtt.getLotatt04());
    	this.setLotatt05(stockBatchAtt.getLotatt05());
    	this.setLotatt06(stockBatchAtt.getLotatt06());
    	this.setLotatt07(stockBatchAtt.getLotatt07());
    	this.setLotatt08(stockBatchAtt.getLotatt08());
    	this.setLotatt09(stockBatchAtt.getLotatt09());
    	this.setLotatt10(stockBatchAtt.getLotatt10());
    	this.setLotatt11(stockBatchAtt.getLotatt11());
    	this.setLotatt12(stockBatchAtt.getLotatt12());
    }
	
}