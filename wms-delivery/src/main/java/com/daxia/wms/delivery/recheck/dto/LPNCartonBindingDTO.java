package com.daxia.wms.delivery.recheck.dto;

import java.io.Serializable;
import java.util.Date;

@lombok.extern.slf4j.Slf4j
public class LPNCartonBindingDTO implements Serializable{

	private static final long serialVersionUID = -158006633948161718L;
	
	private String lpn;//LPN流水号
	private Long stationId;//配送站ID
	
	private String currentDoNo;//当前DO号
	private int currentDoTotalCartonNum;//当前DO箱数量
	
	private Long carrierIdOf1stCarton;//第一个箱子的承运商
	//private Long stationIdOf1stCarton;//第一个箱子的配送站
	private String doTypeOf1stCarton;//第一个箱子的DO 类型
	private Long supplierIdOf1stCarton;//第一个箱子的 supplier Id类型, 仅在DO 类型为TRV时才记录此值。
	private String warehouseOf1stCarton;//第一个箱子的DO 类型, 仅在DO 类型为TT时才记录此值。对应DO edi2字段
	
	private Long cartonHeaderId;//箱号头信息
	private String cartonNo;//箱号
	private String doNo;//发货单单号
	private String trackingNo;//运单号
	
	private Long lpnCartonNum;//LPN 箱数量
	private Date bindingDateTime;//绑定时间
	
	public String getLpn() {
		return lpn;
	}

	public void setLpn(String lpn) {
		this.lpn = lpn;
	}

	public Long getStationId() {
		return stationId;
	}

	public void setStationId(Long stationId) {
		this.stationId = stationId;
	}

	public String getCurrentDoNo() {
		return currentDoNo;
	}

	public void setCurrentDoNo(String currentDoNo) {
		this.currentDoNo = currentDoNo;
	}

	public int getCurrentDoTotalCartonNum() {
		return currentDoTotalCartonNum;
	}

	public void setCurrentDoTotalCartonNum(int currentDoTotalCartonNum) {
		this.currentDoTotalCartonNum = currentDoTotalCartonNum;
	}

	public Long getCarrierIdOf1stCarton() {
		return carrierIdOf1stCarton;
	}

	public void setCarrierIdOf1stCarton(Long carrierIdOf1stCarton) {
		this.carrierIdOf1stCarton = carrierIdOf1stCarton;
	}

	public String getDoTypeOf1stCarton() {
		return doTypeOf1stCarton;
	}

	public void setDoTypeOf1stCarton(String doTypeOf1stCarton) {
		this.doTypeOf1stCarton = doTypeOf1stCarton;
	}

	public Long getSupplierIdOf1stCarton() {
		return supplierIdOf1stCarton;
	}

	public void setSupplierIdOf1stCarton(Long supplierIdOf1stCarton) {
		this.supplierIdOf1stCarton = supplierIdOf1stCarton;
	}

	public String getWarehouseOf1stCarton() {
		return warehouseOf1stCarton;
	}

	public void setWarehouseOf1stCarton(String warehouseOf1stCarton) {
		this.warehouseOf1stCarton = warehouseOf1stCarton;
	}

	public Long getCartonHeaderId() {
		return cartonHeaderId;
	}

	public void setCartonHeaderId(Long cartonHeaderId) {
		this.cartonHeaderId = cartonHeaderId;
	}

	public String getCartonNo() {
		return cartonNo;
	}

	public void setCartonNo(String cartonNo) {
		this.cartonNo = cartonNo;
	}

	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}

	public String getTrackingNo() {
		return trackingNo;
	}

	public void setTrackingNo(String trackingNo) {
		this.trackingNo = trackingNo;
	}

	public Long getLpnCartonNum() {
		return lpnCartonNum;
	}

	public void setLpnCartonNum(Long lpnCartonNum) {
		this.lpnCartonNum = lpnCartonNum;
	}

	public Date getBindingDateTime() {
		return bindingDateTime;
	}

	public void setBindingDateTime(Date bindingDateTime) {
		this.bindingDateTime = bindingDateTime;
	}
}
