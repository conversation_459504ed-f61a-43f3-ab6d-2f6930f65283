package com.daxia.wms.delivery.task.repick.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.xml.bind.annotation.XmlAttribute;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.wms.stock.task.entity.TrsTask;

/**
* 返拣任务
*/
@Entity
@Table(name = "trs_reverse_pick_task")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = "IS_DELETED = 0")
@SQLDelete(sql = "update trs_reverse_pick_task set is_deleted = 1 where id = ? and version = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class ReversePickTask extends TrsTask {

    private static final long serialVersionUID = -5948208186940394908L;
   
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)  
    @Column(name = "ID")
    @XmlAttribute
    @Override
    public Long getId() {
        return id;
    }
}
