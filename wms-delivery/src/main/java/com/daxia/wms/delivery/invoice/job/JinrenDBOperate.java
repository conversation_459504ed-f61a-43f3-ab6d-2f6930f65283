package com.daxia.wms.delivery.invoice.job;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

import com.daxia.framework.common.util.*;
import org.jboss.seam.annotations.Name;

import com.daxia.wms.delivery.invoice.dto.Invoice4JinrenDTO;

@Name("jinrenDBOperate")
 @lombok.extern.slf4j.Slf4j
public class JinrenDBOperate {
     /**
      * 获取数据库链接
      * 
     * @return 数据库链接
      */
     public boolean writeInvoiceInfo(List<Invoice4JinrenDTO> invoice4JinrenDTOs,List<String> invoiceHeaderIds) {
    	 Connection  conn = null;
         boolean result = true;
    	 try{
             JRDBConnectionManager dcm = new JRDBConnectionManager();
             conn = dcm.getConnection();
             conn.setAutoCommit( false );
           //删除已经存在的发票信息
             
             String temp = "?";
 			if(ListUtil.isNotEmpty(invoiceHeaderIds)){
 				for(int i = 1 ; i< invoiceHeaderIds.size(); i++) {
 	 	            temp = temp + ",?";
 	 	        }
 	 			String sql = "delete from T_FPInfo where fp_num in ( "+temp+")";
 	 			PreparedStatement prest = conn
 	 					.prepareStatement(sql, ResultSet.TYPE_SCROLL_SENSITIVE,
 	 							ResultSet.CONCUR_READ_ONLY);
 	 			for(int i = 0 ; i< invoiceHeaderIds.size(); i++) {
 	 				prest.setString(i + 1, invoiceHeaderIds.get(i));
 	 	        }
 	 			prest.executeUpdate();
 			}
             
             String insertSql = "INSERT into T_FPInfo (fp_num,fp_fpNun,fp_title,fp_comName,fp_guige,fp_unit,fp_count,fp_shuilv,fp_time,"
             		+ "fp_hsPrice,fp_hsmoney,fp_remarks,fp_diy1,fp_diy2,fp_diy3,fp_batchNum,fp_id,FP_CategoryCode,FP_BuyerTaxNo) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,NEWID(),?,?)";
             PreparedStatement prest = conn.prepareStatement(insertSql,ResultSet.TYPE_SCROLL_SENSITIVE,ResultSet.CONCUR_READ_ONLY);     
             for(Invoice4JinrenDTO invoice4JinrenDTO : invoice4JinrenDTOs){  
                prest.setString(1, StringUtil.isEmpty(invoice4JinrenDTO.getDocNo()) ? "" : invoice4JinrenDTO.getDocNo());     
                prest.setString(2, StringUtil.isEmpty(invoice4JinrenDTO.getInvoiceNo()) ? "" : invoice4JinrenDTO.getInvoiceNo());     
                prest.setString(3, StringUtil.isEmpty(invoice4JinrenDTO.getTitle() ) ? "" : invoice4JinrenDTO.getTitle());     
                prest.setString(4, StringUtil.isEmpty(invoice4JinrenDTO.getProductName()) ? "" :  invoice4JinrenDTO.getProductName());     
                prest.setString(5, StringUtil.isEmpty(invoice4JinrenDTO.getSkuType()) ? "" :  invoice4JinrenDTO.getSkuType());    
                prest.setString(6, StringUtil.isEmpty(invoice4JinrenDTO.getUnit()) ? "" :  invoice4JinrenDTO.getUnit());

                 BigDecimal qty = new BigDecimal(0);
                if(!StringUtil.isEmpty(invoice4JinrenDTO.getQty())){
             	   qty = new BigDecimal(invoice4JinrenDTO.getQty());
                }
                prest.setBigDecimal(7,qty);     
                BigDecimal taxRate = new BigDecimal(0);
                if(!StringUtil.isEmpty(invoice4JinrenDTO.getTaxRate())){
             	   taxRate = new BigDecimal(invoice4JinrenDTO.getTaxRate());
                }
                prest.setBigDecimal(8,taxRate);     
                prest.setTimestamp(9, DateUtil.getNowTime()); 
                BigDecimal price = new BigDecimal(0);
                if(!StringUtil.isEmpty(invoice4JinrenDTO.getPrice())){
             	   price = new BigDecimal(invoice4JinrenDTO.getPrice());
                }
                prest.setBigDecimal(10, price);   
                BigDecimal amount = new BigDecimal(0);
                if(!StringUtil.isEmpty(invoice4JinrenDTO.getAmount())){
                	amount = new BigDecimal(invoice4JinrenDTO.getAmount());
                }
                prest.setBigDecimal(11,amount);     
                prest.setString(12, StringUtil.isEmpty(invoice4JinrenDTO.getRemarks()) ? "" : invoice4JinrenDTO.getRemarks());     
                prest.setString(13, StringUtil.isEmpty(invoice4JinrenDTO.getDiy1()) ? "" :  invoice4JinrenDTO.getDiy1());     
                prest.setString(14, StringUtil.isEmpty(invoice4JinrenDTO.getDiy2()) ? "" :  invoice4JinrenDTO.getDiy2());
                Integer jinrenDiy3 = SystemConfig.getConfigValueInt("delivery.invoice.jinrenDiy3", ParamUtil.getCurrentWarehouseId());
                prest.setBigDecimal(15,jinrenDiy3 == null ? BigDecimal.ONE : BigDecimal.valueOf(jinrenDiy3));//默认为1 
                prest.setString(16, StringUtil.isEmpty(invoice4JinrenDTO.getWaveNo()) ? "" :  invoice4JinrenDTO.getWaveNo());
                 prest.setString(17, StringUtil.isEmpty(invoice4JinrenDTO.getTaxCategoryCode()) ? "" :  invoice4JinrenDTO.getTaxCategoryCode());
                 prest.setString(18, StringUtil.isEmpty(invoice4JinrenDTO.getBuyerTaxNo()) ? "" :  invoice4JinrenDTO.getBuyerTaxNo());

                 prest.addBatch();
             }     
             prest.executeBatch();     
             
             
             conn.commit();     
         }catch (Exception e) {
             e.printStackTrace();
             result = false;
             // 回滚事务
             try {
                conn.rollback();
             } catch ( Exception e2 ) {
            	 e.printStackTrace();
             }
         } finally {
             try {
                conn.close();
             } catch (Exception e) {
            	 e.printStackTrace();
             }
             return result;
         }
     }
     
	public List<Invoice4JinrenDTO> readInvoiceInfo(List<String> invoiceIds) {
		List<Invoice4JinrenDTO> invoice4JinrenDTOs = new ArrayList<Invoice4JinrenDTO>();
		Connection conn = null;
		try {
			ResultSet rs = null;
			JRDBConnectionManager dcm = new JRDBConnectionManager();
			conn = dcm.getConnection();
			String temp = "?";
			if(ListUtil.isNullOrEmpty(invoiceIds)){
				return invoice4JinrenDTOs;
			}
	        for(int i = 1 ; i< invoiceIds.size(); i++) {
	            temp = temp + ",?";
	        }
			String sql = "select * from T_FPInfo where fp_num in ( "+temp+") and len(fp_fpNun) !=0 ";
			PreparedStatement prest = conn
					.prepareStatement(sql, ResultSet.TYPE_SCROLL_SENSITIVE,
							ResultSet.CONCUR_READ_ONLY);
			for(int i = 0 ; i< invoiceIds.size(); i++) {
				prest.setString(i + 1, invoiceIds.get(i));
	        }
			rs = prest.executeQuery();
			while (rs.next()) {
				Invoice4JinrenDTO invoice4JinrenDTO = new Invoice4JinrenDTO();
				invoice4JinrenDTO.setDocNo(rs.getString("FP_Num"));
				invoice4JinrenDTO.setInvoiceNo(rs.getString("FP_FpNun"));
				invoice4JinrenDTOs.add(invoice4JinrenDTO);
			}
			conn.close();
		}catch (Exception e) {
			e.printStackTrace();
		}
		return invoice4JinrenDTOs;
	}
}