package com.daxia.wms.delivery.recheck.action;

import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.BusinessException;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ResourceUtils;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoType;
import com.daxia.wms.Constants.DocType;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.pick.service.PickTaskService;
import com.daxia.wms.delivery.recheck.service.ReCheckService;
import com.daxia.wms.master.entity.Sku;
import com.daxia.wms.master.entity.Supplier;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.serial.entity.SerialSku;
import com.daxia.wms.master.serial.service.SerialSkuService;
import com.daxia.wms.master.service.SkuScanService;
import com.daxia.wms.master.service.SkuService;
import com.daxia.wms.master.service.SupplierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.serial.entity.TrsSpvsnCodeLog;
import com.daxia.wms.serial.service.SpvsnCodeService;
import com.daxia.wms.stock.stock.entity.StockSerial;
import com.google.common.collect.Lists;
import org.apache.commons.collections.map.HashedMap;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 核拣时的序列号管理。
 * 核拣商品时如果录入序列号，通过该类实现序列号的验证。
 */
@Name("com.daxia.wms.delivery.reCheckSerialAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ReCheckSerialAction implements Serializable {
    private static final long serialVersionUID = 1L;
    @In
    private ReCheckService reCheckService;

    @In
    DeliveryOrderService deliveryOrderService;

    @In
    SkuScanService skuScanService;

    private Long orderId;

    private String doNo;

    private String barcode;
    private String errorSerialNoStrs;

    private Long productId;

    private String errorMessage;

    private Long serialId;
    private Long supervisionId;

    private String supervisionCode;
    private String productCode;

    private String lotatt05;

    private Boolean inputed = Boolean.FALSE;

    @In
    private SpvsnCodeService spvsnCodeService;
    @In
    private SupplierService supplierService;
    @In
    private WarehouseService warehouseService;
    @In
    SerialSkuService serialSkuService;
    @In
    SkuService skuService;

    @In
    private PickTaskService pickTaskService;

    /**
     * 新录入的序列号
     */
    private String serial;
    private String serialIds;
    private String serialNos;

    public void scanSerial() {
        errorMessage = "";
        this.barcode = "";

        if (serial == null || serial.trim().length() == 0) {
            // 新录入序列号的序列号是否为空或null
            errorMessage = "recheck.serial.required";
            return;
        }

        List<SerialSku> serialSkus = serialSkuService.findBySerialOrGroup(this.getSerial());
        if (ListUtil.isNullOrEmpty(serialSkus)) {//序列号查到不到，按商品编码、条码查找；
            Sku s = skuService.getSkuByCodeOrBarcode(serial, serial);
            if (s != null) {
                this.productId = s.getId();
                this.barcode = s.getProductCode();
            } else {
                errorMessage = "recheck.serial.invalid";
            }
            return;
        }

        List<String> serialNos = parse(serialSkus, this.getSerial());

        if (ListUtil.isNullOrEmpty(serialNos)) {
            if (StringUtil.isEmpty(errorMessage)) {
                errorMessage = "recheck.serial.invalid";
            }
            return;
        }

        this.productId = serialSkus.get(0).getSkuId();
        this.barcode = serialSkus.get(0).getSku().getEan13().split(",")[0];

        DeliveryOrderHeader header = deliveryOrderService.getDoHeaderById(orderId);
        List<DeliveryOrderDetail> details = header.getDoDetails();
        // 根据录入的序列号，获取序列号库存信息
        List<StockSerial> stockSerials = this.reCheckService.findStockSerial(serialNos, StringUtil.isEmpty(details.get(0).getLotatt06()) ? null : Long.valueOf(details.get(0).getLotatt06()));
    
        if (ListUtil.isNullOrEmpty(stockSerials) || stockSerials.size() != serialNos.size()) {
            // 序列号库存是否存在
            errorMessage = "recheck.serial.invalid";
            return;
        }

        //查找当前产品对应的订单是否是坏品
        DeliveryOrderDetail serialDoDetail = null;
        for (DeliveryOrderDetail detail : details) {
            if (detail.getSkuId().equals(productId)) {
                serialDoDetail = detail;
                break;
            }
        }
    
        //没有找到匹配的do明细
        if (serialDoDetail == null) {
            errorMessage = "recheck.serial_product.notmatch";
            return;
        }

        for (StockSerial stockSerial : stockSerials) {
            if (serialDoDetail.getMerchant() == null || !serialDoDetail.getMerchant().getId().equals(stockSerial.getMerchantId())) {
                // 序列号和商品是否匹配
                errorMessage = "recheck.serial_merchant.notmatch";
                return;
            }

            if (!productId.equals(stockSerial.getSkuId())) {
                // 序列号和商品是否匹配

                errorMessage = "recheck.serial_product.notmatch";
                return;
            }

            boolean isValideSku = reCheckService.isDoContainsSku(orderId, stockSerial.getSkuId());
            if (!isValideSku) {
                // 订单是否包含序列号对应的商品

                errorMessage = "recheck.serial_do.notmatch";
                return;
            }
        }
        List<Long> serialIds = ListUtil.convert(stockSerials, new ListUtil.Convertor<StockSerial, Long>() {
            @Override
            public Long convert(StockSerial stockSerial) {
                return stockSerial.getId();
            }
        });
        this.serialIds =  StringUtil.combine(serialIds, ",");
        this.serialNos = StringUtil.combine(serialNos, ",");
    }

    public void batchScanSerial() {
        errorMessage = "";
        if (serial == null || serial.trim().length() == 0) {
            errorMessage = "recheck.serial.required";
            return;
        }
        List<String> serialNoList = Arrays.asList(this.getSerial().replaceAll("(\r\n|\r|\n|\n\r)", ",").replaceAll("(\\s)", "").split(","));
        Sku sku = skuService.getSku(barcode);
        List<SerialSku> serialSkus = serialSkuService.findBySerialOrGroup(serialNoList);

        if (ListUtil.isNullOrEmpty(serialSkus)) {
            errorSerialNoStrs = StringUtil.combine(serialNoList, ",");
            errorMessage = "recheck.serial.invalid";
            return;
        }
        Map<String, SerialSku> serialSkuMap = new HashedMap();
        for (SerialSku serialSku : serialSkus) {
            serialSkuMap.put(serialSku.getSerialNo(), serialSku);
        }
        List<String> serialNos = new ArrayList<String>();
        List<String> errorSerialNos = new ArrayList<String>();
        for (String s : serialNoList) {
            SerialSku serialSku = serialSkuMap.get(s);
            if (serialSku != null && serialSku.getSkuId().equals(sku.getId())) {
                serialNos.add(serialSku.getSerialNo());
            } else {
                errorSerialNos.add(s);
            }
        }
        errorSerialNoStrs = StringUtil.combine(errorSerialNos,",");
        if (serialNoList.size() != serialNos.size()) {
            errorMessage = "recheck.serial.invalid";
            return;
        }
        this.productId = serialSkus.get(0).getSkuId();
        this.barcode = serialSkus.get(0).getSku().getEan13().split(",")[0];

        DeliveryOrderHeader header = deliveryOrderService.getDoHeaderById(orderId);
        List<DeliveryOrderDetail> details = header.getDoDetails();
        // 根据录入的序列号，获取序列号库存信息
        List<StockSerial> stockSerials = this.reCheckService.findStockSerial(serialNos, StringUtil.isEmpty(details.get(0).getLotatt06()) ? null : Long.valueOf(details.get(0).getLotatt06()));
        if (ListUtil.isNullOrEmpty(stockSerials) || stockSerials.size() != serialNos.size()) {
            // 序列号库存是否存在
            errorMessage = "recheck.serial.invalid";
            return;
        }
        //查找当前产品对应的订单是否是坏品
        DeliveryOrderDetail serialDoDetail = null;
        for (DeliveryOrderDetail detail : details) {
            if (detail.getSkuId().equals(productId)) {
                serialDoDetail = detail;
                break;
            }
        }
        //没有找到匹配的do明细
        if (serialDoDetail == null) {
            errorMessage = "recheck.serial_product.notmatch";
            return;
        }
        for (StockSerial stockSerial : stockSerials) {
            if (serialDoDetail.getMerchant() == null || !serialDoDetail.getMerchant().getId().equals(stockSerial.getMerchantId())) {
                // 序列号和商品是否匹配
                errorMessage = "recheck.serial_merchant.notmatch";
                return;
            }

            if (!productId.equals(stockSerial.getSkuId())) {
                // 序列号和商品是否匹配

                errorMessage = "recheck.serial_product.notmatch";
                return;
            }

            boolean isValideSku = reCheckService.isDoContainsSku(orderId, stockSerial.getSkuId());
            if (!isValideSku) {
                // 订单是否包含序列号对应的商品

                errorMessage = "recheck.serial_do.notmatch";
                return;
            }
        }
        List<Long> serialIds = ListUtil.convert(stockSerials, new ListUtil.Convertor<StockSerial, Long>() {
            @Override
            public Long convert(StockSerial stockSerial) {
                return stockSerial.getId();
            }
        });
        this.serialIds =  StringUtil.combine(serialIds, ",");
        this.serialNos = StringUtil.combine(serialNos, ",");
    }

    private List<String> parse(List<SerialSku> serialSkus, String serial) {
        SerialSku singleSerial = null;
        Long groupSkuId = null;
        List<String> groupSerial = Lists.newArrayList();
        for (SerialSku serialSku : serialSkus) {
            if (serialSku.getSerialNo().equals(serial)) {
                if (singleSerial == null) {
                    singleSerial = serialSku;
                } else {
                    errorMessage = "recheck.serial.isRepetition";
                    return null;
                }
            } else if (serialSku.getGroupNo().equals(serial)) {
                if (groupSkuId == null) {
                    groupSkuId = serialSku.getSkuId();
                    groupSerial.add(serialSku.getSerialNo());
                } else {
                    if (serialSku.getSkuId().equals(groupSkuId)) {
                        groupSerial.add(serialSku.getSerialNo());
                    } else {
                        errorMessage = "recheck.serial.groupIsRepetition";
                        return null;
                    }
                }
            } else {
                errorMessage = "recheck.serial.invalid";
                return null;
            }
        }
        if (singleSerial != null) {
            return Lists.newArrayList(singleSerial.getSerialNo());
        } else {
            return groupSerial;
        }
    }

    private List<String> parse(List<SerialSku> serialSkus, List<String> serials) {
        SerialSku singleSerial = null;
        Long groupSkuId = null;
        List<String> groupSerial = Lists.newArrayList();
        for (SerialSku serialSku : serialSkus) {
            if ((serials.contains(serialSku.getSerialNo()))) {
                if (singleSerial == null) {
                    singleSerial = serialSku;
                } else {
                    errorMessage = "recheck.serial.isRepetition";
                    return null;
                }
            } else {
                errorMessage = "recheck.serial.invalid";
                return null;
            }
        }
        if (singleSerial != null) {
            return Lists.newArrayList(singleSerial.getSerialNo());
        } else {
            return groupSerial;
        }
    }


    public void getProductBarCodeBySerial() {
        this.barcode = "";
        if (StringUtil.isNotEmpty(this.getSerial())) {
            this.barcode = skuScanService.transformScanCode(this.getSerial());
        }
    }
    /**
     * 校验新录入的序列号信息
     */
    public void validateSerial() {
        if (serial == null || serial.trim().length() == 0) {
            // 新录入序列号的序列号是否为空或null
            errorMessage = "recheck.serial.required";
            return;
        }

        DeliveryOrderHeader header = deliveryOrderService.getDoHeaderById(orderId);
        List<DeliveryOrderDetail> details = header.getDoDetails();
        // 根据录入的序列号，获取序列号库存信息
        StockSerial stockSerial = this.reCheckService.findStockSerial(this.serial,StringUtil.isEmpty(
                details.get(0).getLotatt06())?null:Long.valueOf(details.get(0).getLotatt06()));

        //查找当前产品对应的订单是否是坏品
        DeliveryOrderDetail serialDoDetail = null;
        for (DeliveryOrderDetail detail : details) {
            if (detail.getSkuId().equals(productId)) {
                serialDoDetail = detail;
                break;
            }
        }
        //标识:礼品卡 坏品 调拨出库 扫描的序列号若系统有数据就序列号出库，若没有就不处理，也能通过
        Boolean giftCardAllotCheck = true;
        if (DoType.ALLOT.getValue().equals(header.getDoType()) && Constants.YesNo.YES.getValue().equals(serialDoDetail.getIsDamaged())) {
            giftCardAllotCheck = false;
        }
        if (giftCardAllotCheck) {
            if (stockSerial == null) {
                // 序列号库存是否存在

                errorMessage = "recheck.serial.invalid";
                return;
            }
            if (!productId.equals(stockSerial.getSkuId())) {
                // 序列号和商品是否匹配

                errorMessage = "recheck.serial_product.notmatch";
                return;
            }
            boolean isValideSku = reCheckService.isDoContainsSku(orderId, stockSerial.getSkuId());
            if (!isValideSku) {
                // 订单是否包含序列号对应的商品

                errorMessage = "recheck.serial_do.notmatch";
                return;
            }
            serialId = stockSerial.getId();
        } else {
            if (stockSerial != null) {
                if (!productId.equals(stockSerial.getSkuId())) {
                    // 序列号和商品是否匹配
                    errorMessage = "recheck.serial_product.notmatch";
                    return;
                }
                if (!serialDoDetail.getMerchant().getId().equals(stockSerial.getMerchantId())) {
                    // 序列号和商品是否匹配
                    errorMessage = "recheck.serial_merchant.notmatch";
                    return;
                }
                boolean isValideSku = reCheckService.isDoContainsSku(orderId, stockSerial.getSkuId());
                if (!isValideSku) {
                    // 订单是否包含序列号对应的商品

                    errorMessage = "recheck.serial_do.notmatch";
                    return;
                }
                serialId = stockSerial.getId();
            }
        }
    }
    /**
     * 校验新录入的药监码
     */
    public void validateSupervisionCode() {
        //校验格式
        //保存。
        try {
            this.errorMessage = null;
            DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(orderId);
            TrsSpvsnCodeLog trsSpvsnCodeLog = new TrsSpvsnCodeLog();
            trsSpvsnCodeLog.setDocId(orderId);
            trsSpvsnCodeLog.setDocNo(doHeader.getDoNo());
            trsSpvsnCodeLog.setDocType(DocType.SO.getValue());
            trsSpvsnCodeLog.setSpvsnDocType(doHeader.getDoType());
            //退货记录目标供应商
            if (doHeader.getSupplierId() != null) {
                Supplier supplier = supplierService.getSupplier(doHeader.getSupplierId());
                if (supplier != null) {
                    trsSpvsnCodeLog.setToCorpID(supplier.getCorpId());
                }
                // 调拨记录目标门店
            } else if (DoType.ALLOT.getValue().equals(doHeader.getDoType())
                    && StringUtil.isNotEmpty(doHeader.getEdi2())) {
                Warehouse warehouse = warehouseService.getWarehouse(Long.valueOf(doHeader.getEdi2()));
                if (warehouse != null) {
                    trsSpvsnCodeLog.setToCorpID(warehouse.getCorpId());
                }
            }
            trsSpvsnCodeLog.setSpvsnCode(supervisionCode);

            if(StringUtil.isNotEmpty(productCode)){
                Sku sku = skuService.getSku(productCode);
                if(null != sku){
                    trsSpvsnCodeLog.setSkuId(sku.getId());
                }
            }
            trsSpvsnCodeLog.setLotatt05(lotatt05);

            int needSpvsnCount = pickTaskService.findPickTaskByDoIdAndSkuIdForQty(
                    trsSpvsnCodeLog.getDocId(),
                    trsSpvsnCodeLog.getSkuId(),
                    trsSpvsnCodeLog.getLotatt05()
            ).intValue();

            spvsnCodeService.saveSpvsnCode(trsSpvsnCodeLog,needSpvsnCount);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                errorMessage = ResourceUtils.getDispalyString(e.getMessage(), e.getClass().getName(),
                                                              ((BusinessException) e).getParams());
            } else {
                errorMessage = "系统异常，请联系管理员";
            }
        }
        supervisionCode = null;
    }

    public String getSerialIds() {
        return serialIds;
    }

    public void setSerialIds(String serialIds) {
        this.serialIds = serialIds;
    }

    public String getSerialNos() {
        return serialNos;
    }

    public void setSerialNos(String serialNos) {
        this.serialNos = serialNos;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getSerial() {
        return serial;
    }

    public void setSerial(String serial) {
        this.serial = serial;
    }

    public void validateInputed() {
        inputed = spvsnCodeService.validateInputed(orderId, DocType.SO.getValue());
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getSerialId() {
        return serialId;
    }

    public void setSerialId(Long serialId) {
        this.serialId = serialId;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getSupervisionCode() {
        return supervisionCode;
    }

    public void setSupervisionCode(String supervisionCode) {
        this.supervisionCode = supervisionCode;
    }

    public Long getSupervisionId() {
        return supervisionId;
    }

    public void setSupervisionId(Long supervisionId) {
        this.supervisionId = supervisionId;
    }

    public Boolean getInputed() {
        return inputed;
    }

    public void setInputed(Boolean inputed) {
        this.inputed = inputed;
    }

    public String getErrorSerialNoStrs() {
        return errorSerialNoStrs;
    }

    public void setErrorSerialNoStrs(String errorSerialNoStrs) {
        this.errorSerialNoStrs = errorSerialNoStrs;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getLotatt05() {
        return lotatt05;
    }

    public void setLotatt05(String lotatt05) {
        this.lotatt05 = lotatt05;
    }

}
