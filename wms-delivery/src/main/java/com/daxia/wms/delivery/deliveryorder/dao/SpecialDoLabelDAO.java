package com.daxia.wms.delivery.deliveryorder.dao;

import java.math.BigDecimal;
import java.util.List;

import org.jboss.seam.annotations.Name;

import com.google.common.collect.Lists;
import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.deliveryorder.entity.SpecialDoLabel;

@Name("com.daxia.wms.master.specialDoLabelDAO")
@lombok.extern.slf4j.Slf4j
public class SpecialDoLabelDAO extends HibernateBaseDAO<SpecialDoLabel, String> {

    private static final long serialVersionUID = -5595293331008966059L;

    @SuppressWarnings("unchecked")
    public List<SpecialDoLabel> findAvailable(Long warehouseId) {
        String hql = "FROM SpecialDoLabel sdl WHERE sdl.warehouseId = :warehouseId AND sdl.available = :available ORDER BY sdl.sortNumber";

        return this.createQuery(hql).setParameter("warehouseId", warehouseId).setParameter("available", Boolean.TRUE)
            .list();
    }

    @SuppressWarnings("unchecked")
    public List<Integer> findWaveDetailType(List<Long> selectedDOIdList) {
        String hql = "SELECT DISTINCT doLabel.wave_detail_type FROM doc_do_wave_ex doWave LEFT JOIN md_special_do_label doLabel ON "
                     + "doWave.special_label_code = doLabel.label_code AND doLabel.warehouse_id = :warehouseId AND doLabel.available = :available "
                     + "WHERE doWave.do_h_id IN (:doHeaderId) ";
        List<BigDecimal> resultList = this.createSQLQuery(hql)
            .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).setParameter("available", Boolean.TRUE)
            .setParameterList("doHeaderId", selectedDOIdList).list();

        List<Integer> waveDetailTypes = Lists.newArrayList();
        for (int i = resultList.size(); i > 0; i--) {
            BigDecimal obj = resultList.get(i - 1);
            if (obj != null) {
                waveDetailTypes.add(obj.intValue());
            }
        }
        return waveDetailTypes;
    }

    @SuppressWarnings("unchecked")
    public List<SpecialDoLabel> findByWaveDetailType(Integer waveDetailType) {
        String hql = "FROM SpecialDoLabel sdl WHERE "
                     + "sdl.warehouseId = :warehouseId AND sdl.available = :available AND sdl.waveDetailType = :waveDetailType "
                     + "ORDER BY sdl.labelCode";

        return this.createQuery(hql).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId())
            .setParameter("available", Boolean.TRUE).setParameter("waveDetailType", waveDetailType).list();
    }
}