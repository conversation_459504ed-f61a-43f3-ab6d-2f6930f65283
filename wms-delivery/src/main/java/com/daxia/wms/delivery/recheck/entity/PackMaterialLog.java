package com.daxia.wms.delivery.recheck.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;

/**
 * 包装包材推荐日志
 */
@Entity
@Table(name = "doc_pack_material_log")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_pack_material_log set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class PackMaterialLog extends WhBaseEntity {
	private static final long serialVersionUID = 8870631386862785643L;
	
	private Long id;
	private Long doId;
	private Integer recommendCount;
	private String recommendMaterial;
	private Integer actCount;
	private String actMaterial;
	private Integer isDeleted;
	private Date updateTime;
	private Integer isAccept;
	
	/**
     * DoHeader对象
     */
    private DeliveryOrderHeader doHeader;
	    
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)  
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	@Column(name = "DO_ID")
	public Long getDoId() {
		return doId;
	}
	public void setDoId(Long doId) {
		this.doId = doId;
	}
	
	@Column(name = "RECOMMEND_COUNT")
	public Integer getRecommendCount() {
		return recommendCount;
	}
	public void setRecommendCount(Integer recommendCount) {
		this.recommendCount = recommendCount;
	}
	
	@Column(name = "RECOMMEND_MATERIAL")
	public String getRecommendMaterial() {
		return recommendMaterial;
	}
	public void setRecommendMaterial(String recommendMaterial) {
		this.recommendMaterial = recommendMaterial;
	}
	
	@Column(name = "ACT_COUNT")
	public Integer getActCount() {
		return actCount;
	}
	public void setActCount(Integer actCount) {
		this.actCount = actCount;
	}
	
	@Column(name = "ACT_MATERIAL")
	public String getActMaterial() {
		return actMaterial;
	}
	public void setActMaterial(String actMaterial) {
		this.actMaterial = actMaterial;
	}
	
	@Column(name = "IS_DELETED")
	public Integer getIsDeleted() {
		return isDeleted;
	}
	public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}

	@Column(name = "UPDATE_TIME",insertable = false, updatable = false)
	public Date getUpdateTime() {
		return updateTime;
	}
	
	@Column(name = "UPDATE_TIME",insertable = false, updatable = false)
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
	@Column(name = "IS_ACCEPT")
	public Integer getIsAccept() {
		return isAccept;
	}
	public void setIsAccept(Integer isAccept) {
		this.isAccept = isAccept;
	}
	
	@OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DO_ID",insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public DeliveryOrderHeader getDoHeader() {
        return doHeader;
    }

    public void setDoHeader(DeliveryOrderHeader doHeader) {
        this.doHeader = doHeader;
    }
	
}
