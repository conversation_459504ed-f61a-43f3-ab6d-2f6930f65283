package com.daxia.wms.delivery.load.filter;

import java.io.Serializable;
import java.util.Date;

import org.jboss.seam.annotations.intercept.BypassInterceptors;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

/**
 * 逆向交接单查询条件值对像
 */
@SuppressWarnings("serial")
@BypassInterceptors
@lombok.extern.slf4j.Slf4j
public class ReversalHandoverDetailFilter extends WhBaseQueryFilter implements
		Serializable {

	// 订单号
	private String doNo;
	// 箱号
	private String cartonNo;
	// 待收/已收
	private String reversalStatus;
	//逆向交接单号
	private String reversalNo;

	// 创建时间FROM
	private Date createTimeFrom;
	// 创建时间TO
	private Date createTimeTo;
	// 收货时间FROM
	private Date fmReceiveTime;
	// 收货时间TO
	private Date toReceiveTime;

	@Operation(fieldName = "o.doNo", operationType = OperationType.EQUAL)
	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}

	@Operation(fieldName = "o.cartonNo", operationType = OperationType.EQUAL)
	public String getCartonNo() {
		return cartonNo;
	}

	public void setCartonNo(String cartonNo) {
		this.cartonNo = cartonNo;
	}

	@Operation(fieldName = "o.reversalStatus", operationType = OperationType.EQUAL)
	public String getReversalStatus() {
		return reversalStatus;
	}

	public void setReversalStatus(String reversalStatus) {
		this.reversalStatus = reversalStatus;
	}

	@Operation(fieldName = "o.reversalHandoverHeader.reversalNo", operationType = OperationType.EQUAL)
	public String getReversalNo() {
		return reversalNo;
	}

	public void setReversalNo(String reversalNo) {
		this.reversalNo = reversalNo;
	}

	@Operation(fieldName = "o.createdAt", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")	
	public Date getCreateTimeFrom() {
		return createTimeFrom;
	}

	public void setCreateTimeFrom(Date createTimeFrom) {
		this.createTimeFrom = createTimeFrom;
	}

	@Operation(fieldName = "o.createdAt", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
	public Date getCreateTimeTo() {
		return createTimeTo;
	}

	public void setCreateTimeTo(Date createTimeTo) {
		this.createTimeTo = createTimeTo;
	}


	@Operation(fieldName = "o.receiveTime", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")	
	public Date getFmReceiveTime() {
		return fmReceiveTime;
	}

	public void setFmReceiveTime(Date fmReceiveTime) {
		this.fmReceiveTime = fmReceiveTime;
	}

	@Operation(fieldName = "o.receiveTime", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
	public Date getToReceiveTime() {
		return toReceiveTime;
	}

	public void setToReceiveTime(Date toReceiveTime) {
		this.toReceiveTime = toReceiveTime;
	}
}
