package com.daxia.wms.delivery.wave.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;

import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.util.*;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.AutoWaveType;
import com.daxia.wms.Constants.DoType;
import com.daxia.wms.delivery.constant.DeliveryConstant;
import com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto;
import com.daxia.wms.delivery.deliveryorder.entity.SpecialDoLabel;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateQueryService;
import com.daxia.wms.delivery.deliveryorder.service.SpecialDoLabelService;
import com.daxia.wms.delivery.wave.dto.WaveGeneContextDTO;
import com.daxia.wms.master.rule.filter.BigWaveFilter;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.delivery.wave.service.impl.WaveStrategy;
import com.daxia.wms.master.dto.SkuDTO;
import com.daxia.wms.master.entity.GroupRule;
import com.daxia.wms.master.service.GroupRuleService;
import com.daxia.wms.master.service.PartitionService;
import com.daxia.wms.master.service.SkuCache;
import com.google.common.collect.Lists;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.*;
import org.jboss.seam.annotations.security.Restrict;
import org.jboss.seam.log.Log;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Name("com.daxia.wms.delivery.batchGroupAction")
@Restrict("#{identity.hasPermission('delivery.singlePromoteWave')}")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class BatchGroupAction extends PagedListBean<DoHeaderDto> {

    private static final long serialVersionUID = -938282474511960339L;

    private BigWaveFilter bigWaveFilter;

    @In
    private WaveService waveService;
    @In
    SpecialDoLabelService specialDoLabelService;
    @In
    private PartitionService partitionService;

//    @In("singlePromoteGenerator")
//    private AutoWaveGenerateService autoWaveGenerateNewService;

    @In("waveMaunalGenerateService")
    private WaveStrategy waveStrategy;

    @In
    private DoAllocateQueryService doAllocateQueryService;
    
    @In
    private SkuCache skuCache;
    

    private List<SelectItem> specialLabelList;

    @In
    GroupRuleService groupRuleService;

    @Create
    @Loggable
    public void init() {
        this.getDataPage().setPageSize(DeliveryConstant.DEFAULT_PAGESIZE_FOR_BIGWAVE);

        this.bigWaveFilter = new BigWaveFilter();
        this.bigWaveFilter.setStatus(Constants.DoStatus.ALLALLOCATED.getValue());
        this.bigWaveFilter.setReleaseStatus(Constants.ReleaseStatus.RELEASE.getValue());
        this.bigWaveFilter.setWaveFlag(Long.valueOf(Constants.YesNo.NO.getValue().intValue()));
        if (SystemConfig.configIsOpen(ConfigKeys.INVOICE_USE_ELECTRONIC_INVOICE, ParamUtil.getCurrentWarehouseId())) {
            this.bigWaveFilter.setInvoiceIsBind(Boolean.TRUE);
        }
        this.bigWaveFilter.setDoType(String.valueOf(DoType.SELL.getValue()));
        this.bigWaveFilter.setAutoWaveType(AutoWaveType.BATCH_GROUP.getValue());
        Integer defaultCarrierId = SystemConfig.getConfigValueInt("dubhe.default.carrier.id", ParamUtil.getCurrentWarehouseId());
        if (defaultCarrierId != null) {
            this.bigWaveFilter.setNotUsedCarrierId(defaultCarrierId.longValue());
        }
        Integer cartonPrintPoint = SystemConfig.getConfigValueInt(ConfigKeys.CARTON_PRINT_POINT, ParamUtil.getCurrentWarehouseId());
        if (cartonPrintPoint != null && !Constants.CartonPrintPoint.RECHECK.getValue().equals(cartonPrintPoint)) {
            bigWaveFilter.setCheckTempCarton(Constants.YesNo.YES.getValue());
        } else {
            bigWaveFilter.setCheckTempCarton(null);
        }
        // 按指定商品标记、站点、DO包裹滑道号、配送商、预计出库时间排序
        this.bigWaveFilter.getOrderByMap().put("planShipTime", "asc");
        this.bigWaveFilter.getOrderByMap().put("carrierId", "asc");
        this.bigWaveFilter.getOrderByMap().put("stationId", "asc");
        this.bigWaveFilter.getOrderByMap().put("doWaveEx.waveCriteriaExId", "asc");
        specialLabelList = Lists.newArrayList();
        List<SpecialDoLabel> specialDoLabels = specialDoLabelService.findAvailable();
        for (SpecialDoLabel specialDoLabel : specialDoLabels) {
            SelectItem selectItem = new SelectItem();
            selectItem.setValue(specialDoLabel.getLabelCode());
            selectItem.setLabel(specialDoLabel.getLabelName());
            specialLabelList.add(selectItem);
        }
}

    @Override
    @Loggable
    public void query() {
        DataPage<DoHeaderDto> dataPage = null;

        dataPage = waveService.findBigWaveDoList(bigWaveFilter, this.getStartIndex(), this.getPageSize());

        loadSkuInfo(dataPage);

        this.populateValues(dataPage);
    }


    public List<SelectItem> getGroupRule() {
        List<GroupRule> groupRules = groupRuleService.getAllEnable(ParamUtil.getCurrentWarehouseId());
        List<SelectItem> list = new ArrayList<SelectItem>();
        for (GroupRule gr : groupRules) {
            SelectItem item = new SelectItem();
            item.setValue(gr.getId());
            item.setLabel(gr.getRuleName());
            item.setDescription(gr.getRuleName());
            list.add(item);
        }
        return list;
    }

    private void loadSkuInfo(DataPage<DoHeaderDto> dataPage) {
        if (ListUtil.isNotEmpty(dataPage.getDataList())) {
            List<Long> doIds = new ArrayList<Long>();
            for (DoHeaderDto dto : dataPage.getDataList()) {
                doIds.add(dto.getId());
            }

            Map<Long, Long> skuIds = doAllocateQueryService.getDoLeafSkuId(doIds);
            for (DoHeaderDto dto : dataPage.getDataList()) {
                Long skuId = skuIds.get(dto.getId());
                if (skuId != null) {
                    SkuDTO skuDTO = skuCache.getSku(skuId);
                    if (skuDTO != null) {
                        dto.setProductCode((StringUtil.isEmpty(dto.getProductCode()) ? "" : dto.getProductCode() + ",") + skuDTO.getProductCode());
                    }
                }
            }
        }
    }

    public void generateWave() throws Exception {
        List<Long> selectedDOIdList = new ArrayList<Long>();
        for (Object id : this.getSelectedRowList()) {
            selectedDOIdList.add((Long) id);
        }
        if (ListUtil.isNullOrEmpty(selectedDOIdList)) {
            return;
        }

        WaveGeneContextDTO context = new WaveGeneContextDTO();
        context.setDoIdList(selectedDOIdList);
        context.setMaxDOQty(Long.valueOf(selectedDOIdList.size()));
        context.setIsRecommend(Boolean.FALSE);
        context.setIsSemiAuto(Boolean.FALSE);
        context.setAutoWaveType(AutoWaveType.BATCH_GROUP);
        String waveNo = waveStrategy.generateWave(context);

        if (!StringUtil.isEmpty(waveNo)) {
            this.selectedMap.clear();
            this.query();// 生成波次成功以后，重新查询订单
            sayMessage(DeliveryConstant.GENERATE_SUCCESS, waveNo);
        } else {
            sayMessage(DeliveryConstant.GENERATE_FAILED);
        }
    }

    public BigWaveFilter getBigWaveFilter() {
        return bigWaveFilter;
    }

    public void setBigWaveFilter(BigWaveFilter bigWaveFilter) {
        this.bigWaveFilter = bigWaveFilter;
    }

    public List<SelectItem> getSpecialLabelList() {
        return specialLabelList;
    }

    public void setSpecialLabelList(List<SelectItem> specialLabelList) {
        this.specialLabelList = specialLabelList;
    }
}
