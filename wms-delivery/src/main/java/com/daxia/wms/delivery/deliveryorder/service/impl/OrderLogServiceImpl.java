package com.daxia.wms.delivery.deliveryorder.service.impl;

import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.deliveryorder.dao.OrderLogDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.OrderLog;
import com.daxia.wms.delivery.deliveryorder.service.OrderLogService;
import com.daxia.wms.master.dto.OrderLogDTO;
import com.daxia.wms.master.filter.LpnFilter;
import com.daxia.wms.master.filter.OrderLogFilter;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.jboss.seam.annotations.async.Asynchronous;
import org.springframework.beans.BeanUtils;

import java.util.*;

@Name("com.daxia.wms.delivery.orderLogService")
@lombok.extern.slf4j.Slf4j
public class OrderLogServiceImpl implements OrderLogService {

    @In
    OrderLogDAO orderLogDAO;

    @Override
    public DataPage<OrderLog> queryByFilter(LpnFilter filter, int startIndex, int pageSize) {
        return orderLogDAO.findRangeByFilter(filter, startIndex, pageSize);
    }

    @Override
    @Transactional
    public OrderLog saveLog(DeliveryOrderHeader doHeader, String operateType, String orderLog, String operator) {
        OrderLog log = new OrderLog();
        log.setDocId(doHeader.getId());
        log.setDocNo(doHeader.getDoNo());
        log.setOperateType(operateType);
        log.setOperateLog(orderLog);
        log.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        log.setOperateTime(new Date());
        log.setOperateBy(operator);
        orderLogDAO.saveOrUpdate(log);
        return log;
    }

    @Override
    @Transactional
    public void saveLog(DeliveryOrderHeader doHeader, String operateType, String orderLog) {
        saveLog(doHeader, operateType, orderLog, ParamUtil.getCurrentLoginName());
    }

    /**
     * @param doHeader
     * @param operateType
     * @param orderLog
     */
    @Override
    @Transactional
    @Asynchronous
    public void asyncSaveLog(DeliveryOrderHeader doHeader, String operateType, String orderLog,String operator) {
        ParamUtil.setCurrentWarehouseId(doHeader.getWarehouseId());
        saveLog(doHeader, operateType, orderLog, operator);
    }

    @Override
    public List<OrderLog> findLogByOrderId(Long id) {
        OrderLogFilter filter = new OrderLogFilter();
        filter.setDocId(id);
        Map<String, String> orderByMap = new HashMap<String, String>();
        orderByMap.put("o.id", "asc");
        filter.setOrderByMap(orderByMap);
        return orderLogDAO.findByFilter(filter);
    }

    @Override
    public boolean existsLog(Long id, String operateType) {
        OrderLogFilter filter = new OrderLogFilter();
        filter.setDocId(id);
        filter.setOperateType(operateType);
        Map<String, String> orderByMap = new HashMap<String, String>();
        filter.setOrderByMap(orderByMap);
        List<OrderLog> orderLogs = orderLogDAO.findByFilter(filter);
        return !CollectionUtils.isEmpty(orderLogs);
    }

    @Override
    @Transactional
    public void saveLog(Long waveId, String operateType, String orderLog) {
        orderLogDAO.save(waveId, operateType, orderLog);
    }

    @Override
    @Transactional
    public void saveLog4PickList(List<Long> pickTaskIdList, String operateType, String orderLog,String operateBy) {
        orderLogDAO.saveLog4PickList(pickTaskIdList, operateType, orderLog,operateBy);
    }

    @Override
    @Transactional
    public void batchSaveLog(List<Long> doList, String operateType, String orderLog) {
        orderLogDAO.batchSaveLog(doList, operateType, orderLog);
    }

    @Override
    public DataPage<OrderLogDTO> query(OrderLogFilter orderLogFilter, int startIndex, int pageSize) {
        DataPage<OrderLog> dataPage = orderLogDAO.findRangeByFilter(orderLogFilter, startIndex, pageSize);
        DataPage<OrderLogDTO> dtoDataPage = new DataPage<OrderLogDTO>();
        List<OrderLogDTO> dtoList = new ArrayList<OrderLogDTO>();
        for (OrderLog orderLog : dataPage.getDataList()) {
            OrderLogDTO orderLogDTO = new OrderLogDTO();
            BeanUtils.copyProperties(orderLog, orderLogDTO);
            dtoList.add(orderLogDTO);
        }
        dtoDataPage.setDataList(dtoList);
        dtoDataPage.setTotalCount(dataPage.getTotalCount());
        dtoDataPage.setPageCount(dataPage.getPageCount());
        dtoDataPage.setSumInfo(dataPage.getSumInfo());
        return dtoDataPage;
    }

    @Override
    @Transactional
    public void saveLog(DeliveryOrderHeader doHeader, String operateType, String orderLog, String updateBy, OrderLogDTO orderLogDTO) {
        OrderLog log =  saveLog(doHeader, operateType,  orderLog,  updateBy);
        log.setExt1(orderLogDTO.getExt1());
        log.setExt2(orderLogDTO.getExt2());
        log.setExt3(orderLogDTO.getExt3());
        log.setExt4(orderLogDTO.getExt4());
        log.setExt5(orderLogDTO.getExt5());
        orderLogDAO.update(log);
    }
}
