package com.daxia.wms.delivery.backup;

import com.daxia.framework.system.util.NetUtils;
import com.daxia.wms.master.job.AbstractJob;
import com.daxia.wms.util.AlarmUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.util.List;

@Name("backupTableJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class BackupTableJob extends AbstractJob {
    @Override
    protected void doRun() {
        BackupService backupService = ((BackupService) Component.getInstance(BackupService.class));

        List<CfgBackupTable> allBackupTables = backupService.findAllBackupTables();
        for (CfgBackupTable backupTable : allBackupTables) {
            try {
                backupService.backupTable(backupTable);
            } catch (RuntimeException e) {
                String title = "备份表异常" + backupTable.getTableName();
                log.error(title, e);

                AlarmUtil.sendEmail("", "服务器" + NetUtils.getLocalIp() + title, ExceptionUtils.getFullStackTrace(e));
            }
        }
    }
}
