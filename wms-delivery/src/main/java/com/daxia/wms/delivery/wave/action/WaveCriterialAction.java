package com.daxia.wms.delivery.wave.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;

import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.master.rule.entity.WaveCriterial;
import com.daxia.wms.master.rule.filter.WaveCriterialFilter;
import com.daxia.wms.master.rule.service.WaveCriterialService;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

@Name("com.daxia.wms.delivery.waveCriterialAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class WaveCriterialAction extends PagedListBean<WaveCriterial> {
    
    private Long id;
    private WaveCriterialFilter waveCriterialFilter;
    
    @In
    private WaveCriterialService waveCriterialService;
    
    @Override
    public void query() {
        if (waveCriterialFilter == null) {
            waveCriterialFilter = new WaveCriterialFilter();
        }
        waveCriterialFilter.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        DataPage<WaveCriterial> dataPage = waveCriterialService.find(waveCriterialFilter, this.getStartIndex(), this.getPageSize());
        this.populateValues(dataPage);
    }
    
    public void remove() {
        waveCriterialService.deleted(id);
        int rowNum = ListUtil.getPosFromListIdValue(dataPage.getDataList(), id, "id");
        this.dataPage.getDataList().remove(rowNum);
        this.setOperationStatus(rowNum);
        this.sayMessage(MESSAGE_SUCCESS);
    }
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public WaveCriterialFilter getWaveCriterialFilter() {
        return waveCriterialFilter;
    }
    
    public void setWaveCriterialFilter(WaveCriterialFilter waveCriterialFilter) {
        this.waveCriterialFilter = waveCriterialFilter;
    }
}