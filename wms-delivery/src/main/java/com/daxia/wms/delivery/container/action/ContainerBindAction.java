package com.daxia.wms.delivery.container.action;

import java.util.ArrayList;
import java.util.List;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.container.dto.Detail4BindDTO;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.Constants;
import com.daxia.wms.master.service.ContainerService;

/**
 *  容器绑定action，containerBind.xhtml、containerMgnt.xhtml页面使用
 */
@Name("com.daxia.wms.delivery.containerBindAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ContainerBindAction extends PagedListBean<Detail4BindDTO> {

    private static final long serialVersionUID = 1L;

    private String docType;

    private String docNo;

    private Long containerId;

    private Detail4BindDTO detail4BindDTO;
    
    @In
    private ContainerMgntService containerMgntService;

    @In
    private ContainerService containerService;
    
    public ContainerBindAction() {
        super();
    }
    
    /**
     * 新打开页面时，清空参数
     */
    public void initialize(){
        docType = null;
        docNo = null;
        dataPage = new DataPage<Detail4BindDTO>();        
        this.selectedMap.clear();
    }
    
    @Override
    public void query() {
        this.getSelectedMap().clear();
        clear();
        // 对查询到的list集合数据进行分页显示
        DataPage<Detail4BindDTO> dataPage = containerMgntService.findDetails4Bind(containerId, docNo, docType,
                this.getStartIndex(), this.getPageSize());
        this.populateValues(dataPage);        
    }

    /**
     * 清空页面数据
     * @param dataPage
     */
    private void clear() {
        this.dataPage.getDataList().clear();
        this.dataPage.setTotalCount(0);
        this.dataPage.setPageCount(0);
    }

    /**
     * 将容器绑定到波次下指定 ids的拣货任务
     */
    public void bind() {
        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }
        // 绑定到单据
        containerMgntService.bindContainer(containerId, docType, docNo, ids);
        this.sayMessage(MESSAGE_SUCCESS);
        if(!Constants.BindDocType.DELIVERYORDER.getValue().equals(docType)){
            this.query();
        }else{
            this.clear();
        }
    }

    public String getDocType() {
        return docType;
    }

    public void setDocType(String docType) {
        this.docType = docType;
    }

    public String getDocNo() {
        return docNo;
    }

    public void setDocNo(String docNo) {
        this.docNo = docNo;
    }

    public Long getContainerId() {
        return containerId;
    }

    public void setContainerId(Long containerId) {
        this.containerId = containerId;
    }

    public Detail4BindDTO getDetail4BindDTO() {
        return detail4BindDTO;
    }

    public void setDetail4BindDTO(Detail4BindDTO detail4BindDTO) {
        this.detail4BindDTO = detail4BindDTO;
    }
}
