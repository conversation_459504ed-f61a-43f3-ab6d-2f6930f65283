package com.daxia.wms.delivery.deliveryorder.job;

import com.daxia.framework.common.log.StopWatch;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateHeader;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateService;
import com.daxia.wms.delivery.deliveryorder.service.impl.DoAllocateServiceImpl;
import com.daxia.wms.master.job.AbstractJob;
import com.daxia.wms.util.AlarmUtil;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.util.*;
import java.util.Map.Entry;

/**
 * 自动取消分配定时任务
 * (注：主要针对分配抢库存，打波次失败的订单，定时查找自动取消分配)
 */
@Name("autoCancelAllocJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class AutoCancelAllocJob extends AbstractJob {
    // 一次分配订单的数量
    private static final int DEFAULT_BATCH_CANCEL_ALLOC_NUM = 10;

    private List<Long> getAlcHeaderIds() {
        DoAllocateService doAlcService = ((DoAllocateService) Component.getInstance(DoAllocateServiceImpl.class));
        return doAlcService.queryNeedCancelAllocIds(DEFAULT_BATCH_CANCEL_ALLOC_NUM);
    }

    @SuppressWarnings("unchecked")
    public void doCancelAllocate(List<Long> alcheaderIds) {
        StopWatch stopWatch = new StopWatch("AutoCancelAllocJob.doCancelAllocate");
        log.debug("start auto cancel allocate:{}", alcheaderIds);
        if (CollectionUtils.isNotEmpty(alcheaderIds)) {
            DoAllocateService doAlcService = ((DoAllocateService) Component.getInstance(DoAllocateServiceImpl.class));

            // 记录取消失败异常订单集合
            List<Long> doIdListOfFail = new ArrayList<Long>();
            // 异常上限值
            Integer failMaxNum = doAlcService.getAllocateFailNum();

            if (ListUtil.isNotEmpty(alcheaderIds)) {
                for (Long alcHeaderId : alcheaderIds) {
//                    // 每次操作执行前清空当前的warehouseId，防止被定时任务线程执行其他任务所应用
//                    ParamUtil.setCurrentWarehouseId(null);
                    try {
                    	doAlcService.autoCancelAssign(alcHeaderId);
                    } catch (Exception e) {
                        log.error("auto cancel allocate do {" + alcHeaderId + "} failed", e);

                        // 如果是HibernateException先执行事务回滚操作,避免锁表
                        if (e instanceof HibernateException) {
                            try {
                                log.debug("rollback begin");

                                Session session = (Session) Component.getInstance("hibernateSession");
                                session.getTransaction().rollback();

                                log.debug("rollback end");
                            } catch (Exception e2) {
                                log.error("rollback failed", e2);
                            }
                        }
                        doIdListOfFail.add(alcHeaderId);
                    }
                    Session session = (Session) Component.getInstance("hibernateSession");
                    session.clear();
                    // 每次操作执行后清空当前的warehouseId，防止被定时任务线程执行其他任务所应用
                    ParamUtil.setCurrentWarehouseId(null);
                }
                // 记录失败异常订单集合
                List<DoAllocateHeader> alcheadersOfFail = ListUtil.isNotEmpty(doIdListOfFail) ? doAlcService
                        .findByIds(doIdListOfFail) : Collections.EMPTY_LIST;
                // 处理分配失败的订单，失败次数加1
                if (CollectionUtils.isNotEmpty(alcheadersOfFail)) {
                    log.debug("doCancelAllocate fail list: {}", alcheadersOfFail);

                    Map<Long, List<String>> failNoMap = new HashMap<Long, List<String>>();

                    for (DoAllocateHeader alcheader : alcheadersOfFail) {
                        try {
                            ParamUtil.setCurrentWarehouseId(alcheader.getWarehouseId());

                            // failNum + 1
                            Long failNum = doAlcService.addCancelFailNum(alcheader.getId());

                            // failNum达到最大，发告警邮件
                            if (failMaxNum != null && failMaxNum.equals(failNum)) {
                                List<String> failNos = failNoMap.get(alcheader.getWarehouseId());
                                if (ListUtil.isNullOrEmpty(failNos)) {
                                    failNos = new ArrayList<String>();
                                }

                                failNos.add(alcheader.getDoNo());
                                failNoMap.put(alcheader.getWarehouseId(), failNos);
                            }
                        } catch (Exception e) {
                            log.error("failCheck  failed", e);

                            // 如果是HibernateException先执行事务回滚操作,避免锁表
                            if (e instanceof HibernateException) {
                                try {
                                    log.debug("rollback begin");

                                    Session session = (Session) Component.getInstance("hibernateSession");
                                    session.getTransaction().rollback();

                                    log.debug("rollback end");
                                } catch (Exception e2) {
                                    log.error("rollback failed", e2);
                                }
                            }
                        }
                    }

                    alcheadersOfFail.clear();

                    // 发邮件告警
                    if (!failNoMap.isEmpty()) {
                        for (Entry<Long, List<String>> e : failNoMap.entrySet()) {
                            String content = String.format("订单编号为%s的订单，自动分配失败次数已经达到%d次，请处理！", e.getValue().toString(), failMaxNum);
                            AlarmUtil.sendEmail("订单自动分配失败", content);
                        }
                    }
                }
            }
        }

        log.info(stopWatch.stop());
    }

	@Override
	protected void doRun() {
		doCancelAllocate(getAlcHeaderIds());
	}
}