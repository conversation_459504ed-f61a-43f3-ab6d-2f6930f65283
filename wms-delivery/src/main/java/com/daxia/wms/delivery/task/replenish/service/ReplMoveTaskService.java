package com.daxia.wms.delivery.task.replenish.service;

import java.math.BigDecimal;
import java.util.List;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.task.replenish.entity.ReplMoveTask;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishTask;
import com.daxia.wms.delivery.task.replenish.filter.ReplMoveTaskFilter;
import com.daxia.wms.Constants.TaskStatus;

public interface ReplMoveTaskService {
	public void save(ReplMoveTask replMoveTask);
	
	public List<ReplMoveTask> getReplMoveTasksByReplTask(Long replTaskId, TaskStatus taskStatus);
	
	public DataPage<ReplMoveTask> query(ReplMoveTaskFilter replHeaderFilter, int startIndex,
            int pageSize);
	
	/**
	 * 根据补货规则对库存排序
	 */
	public void allocate4move(ReplenishTask replTask, BigDecimal overQty,Long toLotId) throws Exception;
	
	/**
	 * 根据补货规则对补货移位任务排序
	 */
	public boolean allocateReplMoveTask(ReplenishTask replTask, Long realLocId, 
			String reasonCode, String reasonDesc, boolean changeLoc, BigDecimal overQty, boolean ignorePending,Long toLotId) throws Exception;
	
	/**
	 * 按补货任务取消补货移位任务
	 */
	public void cancelReplMoveTask(Long replTaskId, boolean needAlert);
	
	public BigDecimal getReplMoveTaskQtyUnitByReplTask(Long replTaskId, TaskStatus taskStatus);
	
}
