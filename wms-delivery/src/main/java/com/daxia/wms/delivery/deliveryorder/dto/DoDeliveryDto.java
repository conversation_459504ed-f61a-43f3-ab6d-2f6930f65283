package com.daxia.wms.delivery.deliveryorder.dto;

import java.io.Serializable;
import java.util.Date;

@lombok.extern.slf4j.Slf4j
public class DoDeliveryDto implements Serializable{
	
	private static final long serialVersionUID = -5797983760596388384L;
	
	/** DO单号 */
	private String doNo;
	/** DO类型 */
	private String doType;
	/** 发货日期 */
	private Date shipTime;
	/** 发货数量 */
	private Integer shipQty;
	/** 箱数 */
	private Integer packedQty;
	/** 重量 */
	private Double grossWt;
	/** 省份 */
	private String province;
	/** 市 */
	private String city;
	/** 客户姓名 */
	private String consigneeName;
	/** 邮编 */
	private String postCode;
	/** 代收货款金额 */
	private Double productAmount;
	/** 客户支付配送费 */
	private Double orderDeliveryFee;
	/** 是否退货 */
	private String isBack;
	/** 配送方式 */
	private String deliveryMethod;
	/** 配送公司名*/
	private String distSuppCompName;
	/** 锁车操作人 */
	private String updateBy;
	
	public String getDoNo() {
		return doNo;
	}
	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}
	public String getDoType() {
		return doType;
	}
	public void setDoType(String doType) {
		this.doType = doType;
	}
	public Date getShipTime() {
		return shipTime;
	}
	public void setShipTime(Date shipTime) {
		this.shipTime = shipTime;
	}
	public Integer getShipQty() {
		return shipQty;
	}
	public void setShipQty(Integer shipQty) {
		this.shipQty = shipQty;
	}
	public Integer getPackedQty() {
		return packedQty;
	}
	public void setPackedQty(Integer packedQty) {
		this.packedQty = packedQty;
	}
	public Double getGrossWt() {
		return grossWt;
	}
	public void setGrossWt(Double grossWt) {
		this.grossWt = grossWt;
	}
	public String getProvince() {
		return province;
	}
	public void setProvince(String province) {
		this.province = province;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getConsigneeName() {
		return consigneeName;
	}
	public void setConsigneeName(String consigneeName) {
		this.consigneeName = consigneeName;
	}
	public String getPostCode() {
		return postCode;
	}
	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}
	public Double getProductAmount() {
		return productAmount;
	}
	public void setProductAmount(Double productAmount) {
		this.productAmount = productAmount;
	}
	public Double getOrderDeliveryFee() {
		return orderDeliveryFee;
	}
	public void setOrderDeliveryFee(Double orderDeliveryFee) {
		this.orderDeliveryFee = orderDeliveryFee;
	}
	public String getIsBack() {
		return isBack;
	}
	public void setIsBack(String isBack) {
		this.isBack = isBack;
	}
	public String getDeliveryMethod() {
		return deliveryMethod;
	}
	public void setDeliveryMethod(String deliveryMethod) {
		this.deliveryMethod = deliveryMethod;
	}
	public String getDistSuppCompName() {
		return distSuppCompName;
	}
	public void setDistSuppCompName(String distSuppCompName) {
		this.distSuppCompName = distSuppCompName;
	}
	public String getUpdateBy() {
		return updateBy;
	}
	public void setUpdateBy(String updateBy) {
		this.updateBy = updateBy;
	}
}
