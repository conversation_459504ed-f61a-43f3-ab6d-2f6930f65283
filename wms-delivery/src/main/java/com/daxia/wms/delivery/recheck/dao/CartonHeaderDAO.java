package com.daxia.wms.delivery.recheck.dao;

import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.*;
import com.daxia.wms.delivery.recheck.dto.RecheckDetailInfoDTO;
import com.daxia.wms.delivery.recheck.entity.CartonDetail;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.filter.CartonHeaderFilter;
import com.daxia.wms.delivery.wave.dto.BatchGroupDoCartonInfoDTO;
import com.daxia.wms.delivery.wave.filter.BatchGroupDoCartonFilter;
import com.daxia.wms.master.entity.Materials;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.tuple.Pair;
import org.hibernate.Criteria;
import org.hibernate.Hibernate;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;

/**
 * 箱头DAO
 */
@Name("com.daxia.wms.delivery.cartonHeaderDAO")
@lombok.extern.slf4j.Slf4j
public class CartonHeaderDAO extends HibernateBaseDAO<CartonHeader, Long> {

    private static final long serialVersionUID = 7457928007465902701L;

    private static final Long DEFAULT_CARRIER = -999L;

    /**
     * 根据发货单编号查询已装箱数量
     * @param doId
     * @return
     */
    public int countByDoId(Long doId) {
        Query query = getSession().createQuery(
            "select count(ch) from CartonHeader ch where ch.doHeader.id = :id and ch.warehouseId = :warehouseId");
        return ((Long) (query.setLong("id", doId).setLong("warehouseId", ParamUtil.getCurrentWarehouseId()).uniqueResult())).intValue();
    }
	/**
	 * 根据发货单编号查询已装箱数量
	 * @param doId
	 * @return
	 */
	public List<String> findCartonNoByDoId(Long doId,String packageType) {
		Query query = getSession().createQuery(
				"select ch.cartonNo from CartonHeader ch where ch.doHeader.id = :doId and ch.warehouseId = :warehouseId and ch.ext1=:packageType ");
		query.setLong("doId", doId);
		query.setParameter("packageType", packageType);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		return (List<String>)query.list();
	}
    /**
     * 根据箱号来进行查询箱头
     * @param cartonNo
     * @return CartonHeader
     */
    public CartonHeader findByCartonNo(String cartonNo) {
        Criteria cri = getSession().createCriteria(CartonHeader.class);
        cri.add(Restrictions.eq("cartonNo", cartonNo));
        cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
        return (CartonHeader) (cri.setMaxResults(1).uniqueResult());
    }
    /**
     * 根据板号查询箱头
     * @param lpnNo
     * @return CartonHeader
     */
    public CartonHeader findOneByLpnNo(String lpnNo) {
        Criteria cri = getSession().createCriteria(CartonHeader.class);
        cri.add(Restrictions.eq("lpnNo", lpnNo));
        cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
        return (CartonHeader) (cri.setMaxResults(1).uniqueResult());
    }
    /**
     * 根据箱号来进行查询箱头
     * @param cartonNo
     * @return CartonHeader
     */
    public CartonHeader findByCartonNoForYouBei(String cartonNo) {
        Criteria cri = getSession().createCriteria(CartonHeader.class);
        cri.add(Restrictions.eq("cartonNo", cartonNo));
        return (CartonHeader) (cri.setMaxResults(1).uniqueResult());
    }
    /**
     * 根据快递单号来进行查询箱头
     * @param cartonNo
     * @return CartonHeader
     */
    public CartonHeader findByWayBillForYouBei(String cartonNo) {
        Criteria cri = getSession().createCriteria(CartonHeader.class);
        cri.add(Restrictions.eq("wayBill", cartonNo));
        return (CartonHeader) (cri.setMaxResults(1).uniqueResult());
    }
    /**
     * 优贝根据箱号来进行查询箱头
     * @param cartonNo
     * @return CartonHeader
     */
    public CartonHeader getCartonByNoForYouBei(String cartonNo) {
        Criteria cri = getSession().createCriteria(CartonHeader.class);
        cri.add(Restrictions.eq("cartonNo", cartonNo));
        return (CartonHeader) (cri.setMaxResults(1).uniqueResult());
    }

	public List<CartonHeader> findByLpnNo(String lpnNo) {
		Criteria cri = getSession().createCriteria(CartonHeader.class);
		cri.add(Restrictions.eq("lpnNo", lpnNo));
		cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
		return (List<CartonHeader>) (cri.list());
	}

	public int  clearLpnNoByLpnNo(String lpnNo) {
		String hql = "update CartonHeader o set o.lpnNo = null where o.lpnNo = :lpnNo and o.warehouseId = :warehouseId ";
		Query query = this.createQuery(hql);
		query.setParameter("lpnNo", lpnNo);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		return query.executeUpdate();
	}

	public List<CartonHeader> findByWaybill(String wayBill) {
		String hql = "from CartonHeader o where o.wayBill = :wayBill and o.warehouseId = :warehouseId ";
		Query query = this.createQuery(hql);
		query.setParameter("wayBill", wayBill);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		return (List<CartonHeader>)query.list();
	}

    /**
     * 根据箱号来进行查询
     * @param cartonNo
     * @return
     */
    public CartonHeader findByDoIdAndCartonNo(Long doId, String cartonNo) {
        Criteria cri = getSession().createCriteria(CartonHeader.class);
        cri.add(Restrictions.eq("cartonNo", cartonNo));
        cri.add(Restrictions.eq("doHeader.id", doId));
        cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
        return (CartonHeader) (cri.setMaxResults(1).uniqueResult());
    }

	/**
	 * 根据DO来进行查询
	 *
	 * @param doNum
	 * @return
	 */
    @SuppressWarnings("unchecked")
	public List<CartonHeader> findByDoNo(String doNum) {
        String hql = "from CartonHeader o where o.doHeader.doNo = :doNum and o.warehouseId = :warehouseId order by o.createdAt ";
        Query query = this.createQuery(hql);
        query.setParameter("doNum", doNum);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (List<CartonHeader>)query.list();
    }

	/**
	 * 根据DO来进行查询
	 *
	 * @param doNum
	 * @return
	 */
    @SuppressWarnings("unchecked")
	public List<CartonHeader> findByDoId(Long doId) {
        String hql = "from CartonHeader o where o.doHeader.id = :doId and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setParameter("doId", doId);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (List<CartonHeader>)query.list();
    }

    /**
     * 查找cartonId对应的箱有没有箱明细
     * @param cartonId
     * @return
     */
    public boolean isExistCartonDetail(Long cartonId) {
        Criteria cri = getSession().createCriteria(CartonDetail.class);
        cri.add(Restrictions.eq("cartonHeader.id", cartonId));
        cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
        cri.setProjection(Projections.count("id"));
        cri.setMaxResults(1);
        return 0 < (Integer) cri.uniqueResult();
    }

    /**
     * 根据cartonId删除箱头
     * @param cartonId
     */
    public void removeById(Long cartonId) {
        Query query = createDeleteSqlQuery("ID = :cartonId and WAREHOUSE_ID = :warehouseId");
        query.setLong("cartonId", cartonId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 根据箱号，将未称重的箱写上重量信息
     * @param cartonNo
     * @param weight
     * @param isWeight
     * @param operator
	 * @return
     */
    public int saveCartonWeight(CartonHeader cartonHeader, BigDecimal weight, int isWeight, String operator){
        String hqlUpdate = "update CartonHeader o set o.freight = null, o.actualGrossWeight = :weight, o.weightFlag = :isWeight," +
				"o.weighBy = :weighBy, o.weighTime = :weighTime, o.autoDeliveryFlag = :autoDeliveryFlag " +
				"where o.id = :id";
        Query query = createUpdateQuery(hqlUpdate);
        query.setBigDecimal("weight", weight);
        query.setInteger("isWeight", isWeight);
		query.setInteger("autoDeliveryFlag", cartonHeader.getAutoDeliveryFlag());
        query.setLong("id",cartonHeader.getId());
		query.setString("weighBy",operator);
		query.setTimestamp("weighTime",new Date());
        int updateCount = query.executeUpdate();
        return updateCount;
    }

    /**
     * 根据doId找到其下一个未装车的箱头
     * @param doId
     * @return
     */
    public CartonHeader findACartonListByDoId(Long doId){
        String hql = "from CartonHeader ch where ch.doHeader.id =:doId and ch.warehouseId = :warehouseId and not exists( select ld from LoadDetail ld where ld.doHeaderId =:doId and ld.cartonHeaderId = ch.id and ld.warehouseId = :warehouseId)";
        Query query = createQuery(hql);
        query.setLong("doId", doId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (CartonHeader)query.uniqueResult();
    }

    /**
     * 找到doId下有多少箱
     * @param doId
     * @return
     */
    public int countCartonNumOfADo(Long doId){
        String hql = "select count(ch) from CartonHeader ch where ch.doHeader.id=:doId and ch.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setLong("doId", doId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        Number number = (Number) (query.uniqueResult());
        return number.intValue();
    }

    /**
     * 根据doId找到其中一个箱头
     * @param doId
     * @return
     */
    public CartonHeader findCartonHeaderByDoId (Long doId) {
    	String hql = "from CartonHeader o where o.doHeader.id =:doId and o.warehouseId = :warehouseId";
    	Query query = this.createQuery(hql);
    	query.setLong("doId", doId);
    	query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
    	query.setMaxResults(1);
    	return(CartonHeader)(query.uniqueResult());
    }

    /**
     * 根据doNum找到其中未称重的箱头
     * @param doNum
     * @return
     */
    public String findUnWeighCartonHeader(String doNum){
    	String hql = "select o.cartonNo from CartonHeader o where o.doHeader.doNo = :doNum and o.weightFlag = :flag and o.warehouseId = :warehouseId";
    	Query query = this.createQuery(hql);
    	query.setParameter("doNum", doNum);
    	query.setParameter("flag", 0);
    	query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
    	query.setMaxResults(1);
    	return (String)(query.uniqueResult());
    }

    /**
     * 判断一个do是否装了多箱
     * @param doHeaderId
     * @return
     */
    public boolean isMultiCartonOfADo(Long doHeaderId){
		String hql = "select count(ch.id)from CartonHeader ch where ch.doHeader.id = :doHeaderId and ch.warehouseId = :warehouseId";
		Query query = this.createQuery(hql);
    	query.setLong("doHeaderId", doHeaderId);
    	query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
    	return ((Number)query.uniqueResult()).intValue() > 1 ? true : false ;
	}

    /**
     * 查询订单中未交接的箱
     *
     * @param lpnNo
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<CartonHeader> findNotLoadedCarton(Long doHeaderId){
        String sql = "select ch.* from doc_carton_header ch " +
                                " where ch.do_header_id = :doHeaderId  "+
                                " and ch.status <> :allLoadedStatus and ch.is_deleted = 0 and ch.warehouse_id = :warehouseId";
        SQLQuery query = createSQLQuery(sql);
        query.setLong("doHeaderId", doHeaderId);
        query.setString("allLoadedStatus", CartonStatus.ALL_LOAD.getValue());
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 查询订单中未交接的箱号
     *
     * @param lpnNo
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<String> findNotLoadedCartonNo(Long doHeaderId){
        String sql = "select ch.carton_No from doc_carton_header ch " +
                                " where ch.do_header_id = :doHeaderId  "+
                                " and ch.status <> :allLoadedStatus and ch.warehouse_id = :warehouseId and ch.is_deleted = 0 ";
        SQLQuery query = createSQLQuery(sql);
        query.setLong("doHeaderId", doHeaderId);
        query.setString("allLoadedStatus", CartonStatus.ALL_LOAD.getValue());
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 查询订单中已交接的箱号
     *
     * @param lpnNo
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<String> findLoadedCartonNo(Long doHeaderId){
        String sql = "select ch.carton_No from doc_carton_header ch " +
                                " where ch.do_header_id = :doHeaderId  "+
                                " and ch.status = :allLoadedStatus and ch.warehouse_id = :warehouseId and ch.is_deleted = 0 ";
        SQLQuery query = createSQLQuery(sql);
        query.setLong("doHeaderId", doHeaderId);
        query.setString("allLoadedStatus", CartonStatus.ALL_LOAD.getValue());
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 判断do是否已经交接完成
     * @param doHeaderId
     * @return
     */
    public boolean isAllLoadedOfDO(Long doHeaderId){
        String hql = "select 1 from CartonHeader ch where ch.status <> :allLoaded and ch.warehouseId = :warehouseId and ch.doHeader.id = :doHeaderId";
        Query query = this.createQuery(hql);
        query.setString("allLoaded", CartonStatus.ALL_LOAD.getValue());
        query.setLong("doHeaderId", doHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return null == query.uniqueResult();
    }

    /**
     * 根据id查询箱子信息
     * @param ids
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<CartonHeader> findByIds(List<Long> ids) {
        String hql = "from CartonHeader ch where ch.id in (:ids)";
        Query query = this.createQuery(hql);
        query.setParameterList("ids", ids);
        return query.list();
    }

    /**
     * 根据id查询箱子信息
     * @param ids
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<CartonHeader> findRLCartonsByCartonIds(List<Long> ids) {
        String hql = "from CartonHeader ch where ch.doHeader.releaseStatus = :releaseStatus and ch.id in (:ids)";
        Query query = this.createQuery(hql);
        query.setParameterList("ids", ids);
        query.setParameter("releaseStatus", ReleaseStatus.RELEASE.getValue());
        return query.list();
    }

    /**
     * 根据装箱单ID物理删除
     * @param id
     */
    public void physicalDeleteById(Long id) {
    	String hql = "delete from CartonHeader ch where ch.id=:id and ch.warehouseId = :warehouseId";
    	Query query = this.createQuery(hql);
    	query.setLong("id", id);
    	query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
    	query.executeUpdate();
    }

    /**
     * 根据DoId物理删除
     * @param id
     */
    public void physicalDeleteByDoId(Long doId) {
    	String hql = "delete from doc_carton_header  where do_header_id = :doId and warehouse_id = :warehouseId";
    	Query query = this.createSQLQuery(hql);
    	query.setLong("doId", doId);
    	query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
    	query.executeUpdate();
    }

    /**
	 * 根据波次号获取箱id
	 *
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<Long> findCartonIdByWaveNum(String waveNum) {
		String sql = " select ch.id from doc_carton_header ch, doc_do_header dh "
				+ " where ch.do_header_id = dh.id "
				+ " and dh.release_status = :releaseStatus "
				+ " and ch.warehouse_id = :warehouseId "
				+ " and dh.warehouse_id = :warehouseId "
				+ " and ch.is_deleted = 0 "
				+ " and dh.is_deleted = 0 "
				+ " and dh.wave_id = "
				+ " (select id from doc_wave_header wh "
				+ " where wh.wave_no = :waveNum "
				+ " and wh.warehouse_id = :warehouseId "
				+ " and wh.is_deleted = 0) ";

		SQLQuery query = createSQLQuery(sql).addScalar("id", Hibernate.LONG);
		query.setParameter("releaseStatus", ReleaseStatus.RELEASE.getValue());
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameter("waveNum", waveNum);
		return (List<Long>) query.list();
	}

	/**
	 * 根据doId获取箱id
	 *
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<Long> findCartonIdByDoId(List<Long> doIds) {
		String sql = " select ch.id from doc_carton_header ch "
				+ " where ch.warehouse_id = :warehouseId "
				+ " and ch.is_deleted = 0 "
				+ " and ch.do_header_id in (:ids)";

		SQLQuery query = createSQLQuery(sql).addScalar("id", Hibernate.LONG);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameterList("ids", doIds);
		return (List<Long>) query.list();
	}

	public List<Long> findCartonIdByDoId(List<Long> doIds,String packageType) {
		String sql = " select ch.id from doc_carton_header ch "
				+ " where ch.warehouse_id = :warehouseId "
				+ " and ch.is_deleted = 0 "
				+ " and ch.do_header_id in (:ids) and ch.ext_1 = :packageType";

		SQLQuery query = createSQLQuery(sql).addScalar("id", Hibernate.LONG);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameter("packageType", packageType);
		query.setParameterList("ids", doIds);
		return (List<Long>) query.list();
	}

	/**
	 * 根据波次号获取箱号
	 *
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<String> findCartonNoByWaveNum(String waveNum) {
		String sql = " select ch.carton_no from doc_carton_header ch, doc_do_header dh "
				+ " where ch.do_header_id = dh.id "
				+ " and ch.is_printed = :printStatus "
				+ " and ch.warehouse_id = :warehouseId "
				+ " and dh.warehouse_id = :warehouseId "
				+ " and ch.is_deleted = 0 "
				+ " and dh.is_deleted = 0 "
				+ " and dh.wave_id = "
				+ " (select id from doc_wave_header wh "
				+ " where wh.wave_no = :waveNum "
				+ " and wh.warehouse_id = :warehouseId "
				+ " and wh.is_deleted = 0) ";

		Query query = createSQLQuery(sql);
		query.setParameter("printStatus", YesNo.YES.getValue());
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameter("waveNum", waveNum);
		return (List<String>) query.list();
	}
	/**
	 * 根据波次号获取未货物放行的箱号集合
	 *
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<String> findUnGoodsPassCartonNoByWaveNum(String waveNum) {
		String sql = " select ch.carton_no from doc_carton_header ch, doc_do_header dh "
				+ " where ch.do_header_id = dh.id "
				+ " and ch.is_printed = :printStatus "
				+ " and ch.warehouse_id = :warehouseId "
				+ " and dh.warehouse_id = :warehouseId "
				+ " and ch.is_deleted = 0 "
				+ " and ch.good_pass = 0 "
				+ " and dh.is_deleted = 0 "
				+ " and dh.wave_id = "
				+ " (select id from doc_wave_header wh "
				+ " where wh.wave_no = :waveNum "
				+ " and wh.warehouse_id = :warehouseId "
				+ " and wh.is_deleted = 0) ";

		Query query = createSQLQuery(sql);
		query.setParameter("printStatus", YesNo.YES.getValue());
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameter("waveNum", waveNum);
		return (List<String>) query.list();
	}

	/**
	 * 根据波次号获取箱数
	 *
	 * @return
	 */
	public Long getCartonCountByWaveNum(String waveNum) {
		String sql = " select count(ch.carton_no) from doc_carton_header ch, doc_do_header dh "
				+ " where ch.do_header_id = dh.id "
				+ " and ch.is_printed = :printStatus "
				+ " and ch.warehouse_id = :warehouseId "
				+ " and dh.warehouse_id = :warehouseId "
				+ " and ch.is_deleted = 0 "
				+ " and dh.is_deleted = 0 "
				+ " and dh.wave_id = "
				+ " (select id from doc_wave_header wh "
				+ " where wh.wave_no = :waveNum "
				+ " and wh.warehouse_id = :warehouseId "
				+ " and wh.is_deleted = 0) ";

		Query query = createSQLQuery(sql);
		query.setParameter("printStatus", YesNo.YES.getValue());
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameter("waveNum", waveNum);
		query.setMaxResults(1);
    	return ((BigInteger)query.uniqueResult()).longValue();
	}

	/**
	 * 根据波次号获取箱号
	 *
	 * @return
	 */
	@SuppressWarnings({"rawtypes" })
	public List<BatchGroupDoCartonInfoDTO> findCartonAndDoInfoByWave(String waveNum, BatchGroupDoCartonFilter doCartonFilter) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select ch.id, ch.carton_no, ch.is_printed, dh.id, dh.do_no, dh.status, dh.release_status, date_format(dh.plan_ship_time, '%Y-%m-%d %H:%i:%S') from doc_carton_header ch, doc_do_header dh ");
		sb.append(" where ch.do_header_id = dh.id ");
		sb.append(" and ch.warehouse_id = :warehouseId ");
		sb.append(" and dh.warehouse_id = :warehouseId ");
		sb.append(" and ch.is_deleted = 0 ");
		sb.append(" and dh.is_deleted = 0 ");
		sb.append(" and dh.wave_id = ");
		sb.append(" (select id from doc_wave_header wh ");
		sb.append(" 	where wh.wave_no = :waveNum ");
		sb.append(" 	and wh.warehouse_id = :warehouseId ");
		sb.append(" 	and wh.is_deleted = 0) ");
		if (null != doCartonFilter) {
			if (StringUtil.isNotEmpty(doCartonFilter.getCartonNo())) {
				sb.append(" and ch.carton_no = :cartonNo ");
			}
			if (StringUtil.isNotEmpty(doCartonFilter.getDoNo())) {
				sb.append(" and dh.do_no = :doNo ");
			}
			if (StringUtil.isNotEmpty(doCartonFilter.getReleaseStatus())) {
				sb.append(" and dh.release_status = :releaseStatus");
			}
			if (StringUtil.isNotEmpty(doCartonFilter.getDoStatusFrom())) {
				sb.append(" and dh.status >= :doStatusFrom");
			}
			if (StringUtil.isNotEmpty(doCartonFilter.getDoStatusTo())) {
				sb.append(" and dh.status <= :doStatusTo");
			}
			if (null != doCartonFilter.getCatornPrintStatus()) {
				sb.append(" and ch.is_printed = :isPrinted");
			}
		}
		sb.append(" order by dh.sort_grid_no asc, ch.carton_no asc");
		sb.append("");
		SQLQuery query = createSQLQuery(sb.toString());
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameter("waveNum", waveNum);
		if (null != doCartonFilter) {
			if (StringUtil.isNotEmpty(doCartonFilter.getCartonNo())) {
				query.setParameter("cartonNo", doCartonFilter.getCartonNo());
			}
			if (StringUtil.isNotEmpty(doCartonFilter.getDoNo())) {
				query.setParameter("doNo", doCartonFilter.getDoNo());
			}
			if (StringUtil.isNotEmpty(doCartonFilter.getReleaseStatus())) {
				query.setParameter("releaseStatus", doCartonFilter.getReleaseStatus());
			}
			if (StringUtil.isNotEmpty(doCartonFilter.getDoStatusFrom())) {
				query.setParameter("doStatusFrom", doCartonFilter.getDoStatusFrom());
			}
			if (StringUtil.isNotEmpty(doCartonFilter.getDoStatusTo())) {
				query.setParameter("doStatusTo", doCartonFilter.getDoStatusTo());
			}
			if (null != doCartonFilter.getCatornPrintStatus()) {
				query.setParameter("isPrinted", doCartonFilter.getCatornPrintStatus());
			}
		}
		List list = query.list();
		BatchGroupDoCartonInfoDTO dto = null;
		Object[] result = null;
		List<BatchGroupDoCartonInfoDTO> dtoList = new ArrayList<BatchGroupDoCartonInfoDTO>();
		for (int i = 0; i < list.size(); i++) {
			result = (Object[])list.get(i);
			dto = new BatchGroupDoCartonInfoDTO();
			dto.setCartonId(((BigInteger)result[0]).longValue());
			dto.setCartonNo((String)result[1]);
			dto.setIsPrinted(((BigDecimal)result[2]).intValue());
			dto.setDoId(((BigInteger)result[3]).longValue());
			dto.setDoNo((String)result[4]);
			dto.setDoStatus((String)result[5]);
			dto.setDoReleaseStatus((String)result[6]);
			dto.setPlanShipTime((String)result[7]);
			dtoList.add(dto);
		}
		return dtoList;

	}

	/**
	 * 根据波次号获取箱号
	 *
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public List<BatchGroupDoCartonInfoDTO> findDoAndCartonInfoByWave(String waveNum, BatchGroupDoCartonFilter doCartonFilter) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select dh.id, dh.do_no, dh.status, dh.release_status, date_format(dh.plan_ship_time, '%Y-%m-%d %H:%i:%S'), count(ch.id), sum(ch.is_printed), mc.dist_supp_comp_name ");
		sb.append(" from doc_carton_header ch, doc_do_header dh, md_carrier mc ");
		sb.append(" where ch.do_header_id = dh.id ");
		sb.append(" and mc.id = dh.carrier_id");
		sb.append(" and ch.warehouse_id = :warehouseId ");
		sb.append(" and dh.warehouse_id = :warehouseId ");
		sb.append(" and ch.is_deleted = 0 ");
		sb.append(" and dh.wave_id = ");
		sb.append(" (select id from doc_wave_header wh ");
		sb.append(" 	where wh.wave_no = :waveNum ");
		sb.append(" 	and wh.warehouse_id = :warehouseId ");
		sb.append(" 	and wh.is_deleted = 0) ");
		if (null != doCartonFilter) {
			if (StringUtil.isNotEmpty(doCartonFilter.getDoNo())) {
				sb.append(" and dh.do_no = :doNo ");
			}
			if (StringUtil.isNotEmpty(doCartonFilter.getReleaseStatus())) {
				sb.append(" and dh.release_status = :releaseStatus");
			}
			if (StringUtil.isNotEmpty(doCartonFilter.getDoStatusFrom())) {
				sb.append(" and dh.status >= :doStatusFrom");
			}
			if (StringUtil.isNotEmpty(doCartonFilter.getDoStatusTo())) {
				sb.append(" and dh.status <= :doStatusTo");
			}
		}
		sb.append(" group by dh.id, dh.do_no, dh.status, dh.release_status, dh.plan_ship_time, dh.sort_grid_no, mc.dist_supp_comp_name");
		sb.append(" order by dh.sort_grid_no asc");

		SQLQuery query = createSQLQuery(sb.toString());
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameter("waveNum", waveNum);
		if (null != doCartonFilter) {
			if (StringUtil.isNotEmpty(doCartonFilter.getDoNo())) {
				query.setParameter("doNo", doCartonFilter.getDoNo());
			}
			if (StringUtil.isNotEmpty(doCartonFilter.getReleaseStatus())) {
				query.setParameter("releaseStatus", doCartonFilter.getReleaseStatus());
			}
			if (StringUtil.isNotEmpty(doCartonFilter.getDoStatusFrom())) {
				query.setParameter("doStatusFrom", doCartonFilter.getDoStatusFrom());
			}
			if (StringUtil.isNotEmpty(doCartonFilter.getDoStatusTo())) {
				query.setParameter("doStatusTo", doCartonFilter.getDoStatusTo());
			}
		}
		List list = query.list();
		BatchGroupDoCartonInfoDTO dto = null;
		Object[] result = null;
		List<BatchGroupDoCartonInfoDTO> dtoList = new ArrayList<BatchGroupDoCartonInfoDTO>();
		for (int i = 0; i < list.size(); i++) {
			result = (Object[])list.get(i);
			dto = new BatchGroupDoCartonInfoDTO();
			dto.setDoId(((BigInteger)result[0]).longValue());
			dto.setDoNo((String)result[1]);
			dto.setDoStatus((String)result[2]);
			dto.setDoReleaseStatus((String)result[3]);
			dto.setPlanShipTime((String)result[4]);
			dto.setTotalCartons(((BigInteger)result[5]).longValue());
			dto.setTotalPrintedCartons(((BigDecimal)result[6]).longValue());
			dto.setDoCarrierName((String)result[7]);
			if(dto.getTotalPrintedCartons() == 0 && dto.getTotalCartons() > 0) {
				dto.setCartonPrintStatus(PrintStatus.NO.getValue());
			} else if (dto.getTotalPrintedCartons() > 0 && dto.getTotalPrintedCartons() < dto.getTotalCartons()) {
				dto.setCartonPrintStatus(PrintStatus.PARTIAL.getValue());
			} else if (dto.getTotalPrintedCartons().intValue() ==  dto.getTotalCartons().intValue()) {
				dto.setCartonPrintStatus(PrintStatus.YES.getValue());
			}
			if (null == doCartonFilter
					|| null == doCartonFilter.getCatornPrintStatus()
					|| doCartonFilter.getCatornPrintStatus().equals(dto.getCartonPrintStatus())) {
				dtoList.add(dto);
			}
		}
		return dtoList;
	}

	/**
	 * 查询可供自动交接的箱
	 *
	 * @param doTypeList
	 * @param whId
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<CartonHeader> findCartons4AutoLoad(List<String> doTypeList, Long whId, Integer maxNum) {
		String hql = "from CartonHeader o where o.doHeader.status in (:statusList) and " + " o.status = :cartonStatus  and o.doHeader.needCancel = :needCancel and o.warehouseId = :whId and o.doHeader.warehouseId = :whId ";
		hql += " and o.autoDeliveryFlag = :autoDeliveryFlag ";
		if (ListUtil.isNotEmpty(doTypeList)) {
			hql += " and o.doHeader.doType in (:doTypeList) ";
		}
		
		hql += "  and o.doHeader.releaseStatus = :releaseStatus ";
		hql += "  order by o.createdAt asc ";
		Query query = createQuery(hql);
		List<String> status = new ArrayList<String>();
		status.add(DoStatus.ALL_CARTON.getValue());
		status.add(DoStatus.PART_LOAD.getValue());
		
		query.setParameterList("statusList", status);
		query.setString("cartonStatus", CartonStatus.PACK_OVER.getValue());
		query.setParameter("autoDeliveryFlag", YesNo.YES.getValue());
		
		if (ListUtil.isNotEmpty(doTypeList)) {
			query.setParameterList("doTypeList", doTypeList);
		}
		query.setString("releaseStatus", ReleaseStatus.RELEASE.getValue());
		query.setLong("whId", whId);
		query.setLong("needCancel", YesNo.NO.getValue());
		query.setMaxResults(maxNum); // 控制一次自动交接的箱子不要超过500箱。
		return (List<CartonHeader>) query.list();
	}

	/**
	 * 获取波次标签的打印状态
	 * @param waveId
	 * @return
	 */
	public PrintStatus getCartonPrintStatusInWave(Long waveId) {
		StringBuilder sb = new StringBuilder();
		sb.append("select count(*) total, ifnull(sum(ch.is_printed),0) as printed");
		sb.append("  from doc_do_header dh, doc_carton_header ch");
		sb.append(" where dh.wave_id = :waveId");
		sb.append("   and ch.do_header_id = dh.id");
		sb.append("   and dh.is_deleted = 0");
		sb.append("   and dh.release_status = :releaseStatus");
		sb.append("   and ch.is_deleted = 0");
		sb.append("   and dh.warehouse_id = :warehouseId");
		sb.append("   and ch.warehouse_id = :warehouseId");
		sb.append("   and ch.is_deleted = 0");
		SQLQuery query = this.createSQLQuery(sb.toString()).addScalar("total", Hibernate.LONG)
														.addScalar("printed", Hibernate.LONG);
		query.setLong("waveId", waveId);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setString("releaseStatus", ReleaseStatus.RELEASE.getValue());
		query.setMaxResults(1);
		Object[] results = (Object[])query.uniqueResult();
		long total = (Long)results[0];
		long printed = (Long)results[1];
		if(printed == 0 && total > 0) {
			return PrintStatus.NO;
		} else if (printed > 0 && printed < total) {
			return PrintStatus.PARTIAL;
		} else if (printed == total) {
			return PrintStatus.YES;
		}
		return null;
	}

	/**
	 * 设置箱标签为已打印
	 * @param ids
	 */
	public void setCartonPrinted(List<Long> ids) {
		String sql = "update doc_carton_header o set o.is_printed = :isPrinted where o.is_printed = :notPrinted and o.id in (:ids)";
		Query query = this.createSQLQuery(sql);
		query.setParameterList("ids", ids);
		query.setParameter("isPrinted", YesNo.YES.getValue());
		query.setParameter("notPrinted", YesNo.NO.getValue());
		query.executeUpdate();
	}

	/**
	 * 判断是否所有的箱子都未打印箱标签
	 * @param ids
	 * @param byType 统计方式：按DO或按CARTON
	 * @return
	 */
	public Boolean isAllCartonNotPrinted(List<Long> ids, String byType) {
		StringBuilder sb = new StringBuilder();
		sb.append("select 1 from doc_do_header dh, doc_carton_header ch ");
		sb.append("	where dh.id = ch.do_header_id ");
		if ("DO".equals(byType)) {
			sb.append("   and dh.id in (:ids)");
		} else if ("CARTON".equals(byType)) {
			sb.append("   and ch.id in (:ids)");
		}
		sb.append("   and dh.release_status = :releaseStatus");
		sb.append("   and dh.is_deleted = 0");
		sb.append("   and ch.is_printed = :isPrinted ");
		sb.append("   and dh.warehouse_id = :warehouseId");
		sb.append("   and ch.warehouse_id = :warehouseId");
		sb.append("   and ch.is_deleted = 0");
		SQLQuery query = this.createSQLQuery(sb.toString());
		query.setParameterList("ids", ids);
		query.setParameter("isPrinted", YesNo.YES.getValue());
		query.setParameter("releaseStatus", ReleaseStatus.RELEASE.getValue());
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		@SuppressWarnings("rawtypes")
		List list = query.list();
		if (ListUtil.isNullOrEmpty(list)) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	public void updatePackWeightByDo(Long doHeaderId, BigDecimal newWeight) {
		String hql = "update CartonHeader o set o.actualGrossWeight =:actualGrossWeight,o.weightFlag =1  where o.doHeader.id =:doId and o.warehouseId = :warehouseId";
		Query query =this.createQuery(hql)
				.setParameter("actualGrossWeight", newWeight)
				.setParameter("doId", doHeaderId)
				.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();
	}

	/**
	 * 根据发货单查询其包材类型和数量
	 * @param doId
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public Map<String,Integer> getPackMaterialInfo(Long doId) {
		Map<String, Integer> packMaterialCountMap = new HashMap<String, Integer>();
		String hql = "select  m.name,sum(1)  from CartonHeader o, Materials m "
				+ "where o.doHeader.id = :doId and o.packMaterialNo = m.materialsNo and o.warehouseId = :warehouseId group by m.name ";
		Query query = this.createQuery(hql)
				.setParameter("doId", doId)
				.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		List objs = query.list();
		if (ListUtil.isNotEmpty(objs)) {
			for (Object obj : objs) {
				Object [] objArray = (Object []) obj;
				packMaterialCountMap.put((String) objArray[0], ((Long)objArray[1]).intValue());
			}
		}
		return packMaterialCountMap;
	}

	public String getWayBillNoByDo(Long doId) {
		String sql = "select t.way_bill from doc_carton_header t where t.do_header_id = :doId and t.is_deleted = 0 and t.warehouse_id = :whId and t.way_bill is not null ";
		Query query = createSQLQuery(sql);
		query.setParameter("doId", doId);
		query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
		query.setMaxResults(1);
		return (String)query.uniqueResult();
	}

	public int updateCartonWayBill(String cartonNo ,String wayBill) {
		String hqlUpdate = "update CartonHeader o set o.wayBill = :wayBill where o.cartonNo = :cartonNo and o.warehouseId = :warehouseId";
        Query query = createUpdateQuery(hqlUpdate);
        query.setString("wayBill", wayBill);
        query.setString("cartonNo",cartonNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        int updateCount = query.executeUpdate();
		return updateCount;
	}

	public String findUnLoadCartonsByLoadHeaderId(Long loadHeaderId) {
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT GROUP_CONCAT(c.carton_no) FROM doc_carton_header c ")
				.append("WHERE c.`status` <> :cartonStatus AND c.do_header_id in (	")
				.append("SELECT ld.do_header_id FROM doc_load_detail ld WHERE ld.load_header_id = :loadHeaderId)")
				.append("AND c.is_deleted = 0");
		Query query = getSession().createSQLQuery(sql.toString());
		query.setParameter("cartonStatus", Constants.CartonStatus.ALL_LOAD.getValue());
		query.setParameter("loadHeaderId", loadHeaderId);
		return (String) query.uniqueResult();
	}

    public List<CartonHeader> loadCartonToCalculateFreght() {
		int day = 20;
		String dayStr = SystemConfig.getConfigValue(ConfigKeys.DELIVERY_FREGHT_DAY_RANGE);
		if (StringUtil.isNotEmpty(dayStr)) {
			day = Integer.valueOf(dayStr);
		}

		String hql = "FROM CartonHeader c WHERE c.createdAt >= TIMESTAMPADD(DAY,-" + day + ",NOW()) AND c.freight is null AND c.actualGrossWeight>0  AND c" +
				".doHeader.doType = :doType AND c.doHeader.status = :doStatus";
		return (List<CartonHeader>) this.createQuery(hql).setParameter("doType", DoType.SELL.getValue()).setParameter("doStatus", DoStatus.ALL_DELIVER.getValue()).setMaxResults(50).list();
	}

	public String getCartonSeqByDo(Long id) {
		return (String) this.createSQLQuery("SELECT LPAD(count(ch.id) + 1, 3, 0) FROM doc_carton_header ch WHERE do_header_id = :doHeaderId").setParameter("doHeaderId", id).setMaxResults(1).uniqueResult();
	}

	public void updateCartonFreight(Long id, BigDecimal freight) {
		this.createUpdateQuery("UPDATE CartonHeader o SET o.freight = :freight WHERE o.id = :id").setParameter("id", id).setParameter("freight", freight).executeUpdate();
	}
	
	public void updateWeightByWaveId(Long waveId, BigDecimal weight, String operator) {
		String updateSql = "UPDATE doc_carton_header ch INNER JOIN doc_do_header dh ON ch.do_header_id = dh.id AND dh.is_deleted = 0 " +
				"SET ch.freight = null, ch.actual_grossweight = :weight, ch.is_weight = :isWeight, ch.weigh_by = :weighBy, ch.weigh_time = :weighTime ";

		if (SystemConfig.configIsOpen("delivery.weight.and.deliver",ParamUtil.getCurrentWarehouseId())) {
			updateSql += " ,ch.AUTO_DELIVERY_FLAG = :autoDeliveryFlag ";
		}

		updateSql += " WHERE dh.wave_id = :waveId ";
		Query query = this.createUpdateSqlQuery(updateSql,"ch").
				setParameter("weight", weight).setParameter("isWeight", YesNo.YES.getValue()).
				setParameter("weighBy", operator).setParameter("weighTime", new Date()).
				setParameter("waveId", waveId);
		
		if (SystemConfig.configIsOpen("delivery.weight.and.deliver",ParamUtil.getCurrentWarehouseId())) {
			query.setParameter("autoDeliveryFlag",YesNo.YES.getValue());
		}
		query.executeUpdate();
	}

	public List<CartonHeader> findListByWaveId(Long waveId) {
		String hql = "from CartonHeader o where o.doHeader.waveId = :waveId";
		Query query = createQuery(hql);
		query.setLong("waveId", waveId);
		return (List<CartonHeader>) query.list();
	}
	
	public void createCartonHeader(Long waveId, Materials packMaterial, Integer autoDeliveryFlag, String operator, WaybillType waybillType) {
		String sql = "INSERT INTO doc_carton_header (`status`, carton_no, do_header_id, grossweight, netweight, " +
				"volume, actual_volume ," +
				"tracking_no, create_time, create_by, update_time, update_by, warehouse_id, pack_material, auto_delivery_flag, way_bill,ext_1) " +
				"SELECT '40', dh.tracking_no, dh.id, dh.gross_wt + :materialWeigh, dh.gross_wt + :materialWeigh, dh" +
				".volume,:volume , " +
				"dh.tracking_no, now(), :operator, now(), :operator, :warehouseId, :packmaterial, :autodeliveryflag, dh.tracking_no,:packageType " +
				"FROM doc_do_header dh WHERE  dh.wave_id = :waveId  " +
				"AND dh.release_status = :releaseStatus AND dh.status = :doStatus";


		if (WaybillType.JD.equals(waybillType)) {

			sql = "INSERT INTO doc_carton_header (`status`, carton_no, do_header_id, grossweight, netweight, " +
					"volume, actual_volume ," +
					"tracking_no, create_time, create_by, update_time, update_by, warehouse_id, pack_material, auto_delivery_flag, way_bill,ext_1) " +
					"SELECT '40', concat(dh.tracking_no, '-1-1-'), dh.id, dh.gross_wt + :materialWeigh, dh.gross_wt + :materialWeigh, dh" +
					".volume,:volume , " +
					"dh.tracking_no, now(), :operator, now(), :operator, :warehouseId, :packmaterial, :autodeliveryflag, dh.tracking_no,:packageType " +
					"FROM doc_do_header dh WHERE  dh.wave_id = :waveId  " +
					"AND dh.release_status = :releaseStatus AND dh.status = :doStatus";
		}

		this.createSQLQuery(sql)
				.setParameter("volume",packMaterial.getVolume())
				.setParameter("materialWeigh",packMaterial.getWeight())
				.setParameter("operator",operator).setParameter("packmaterial",packMaterial.getMaterialsNo()).setParameter("autodeliveryflag",autoDeliveryFlag).setParameter("waveId",waveId)
				.setParameter("warehouseId",ParamUtil.getCurrentWarehouseId()).setParameter("releaseStatus", Constants.ReleaseStatus.RELEASE.getValue()).
				setParameter("packageType",PackageType.B.getValue())
				.setParameter("doStatus", DoStatus.ALL_CARTON.getValue()).executeUpdate();
    }
	public void createCartonHeaderWithInnerMaterial(Long waveId, Materials packMaterial, Integer autoDeliveryFlag, String operator, WaybillType waybillType,BigDecimal innerMaterialWeight) {
		String sql = "INSERT INTO doc_carton_header (`status`, carton_no, do_header_id, grossweight, netweight, " +
				"volume, actual_volume ," +
				"tracking_no, create_time, create_by, update_time, update_by, warehouse_id, pack_material, auto_delivery_flag, way_bill,ext_1) " +
				"SELECT '40', dh.tracking_no, dh.id, dh.gross_wt + :materialWeigh, dh.gross_wt + :materialWeigh, dh" +
				".volume,:volume , " +
				"dh.tracking_no, now(), :operator, now(), :operator, :warehouseId, :packmaterial, :autodeliveryflag, dh.tracking_no,:packageType " +
				"FROM doc_do_header dh WHERE  dh.wave_id = :waveId  " +
				"AND dh.release_status = :releaseStatus AND dh.status = :doStatus";


		if (WaybillType.JD.equals(waybillType)) {

			sql = "INSERT INTO doc_carton_header (`status`, carton_no, do_header_id, grossweight, netweight, " +
					"volume, actual_volume ," +
					"tracking_no, create_time, create_by, update_time, update_by, warehouse_id, pack_material, auto_delivery_flag, way_bill,ext_1) " +
					"SELECT '40', concat(dh.tracking_no, '-1-1-'), dh.id, dh.gross_wt + :materialWeigh, dh.gross_wt + :materialWeigh, dh" +
					".volume,:volume , " +
					"dh.tracking_no, now(), :operator, now(), :operator, :warehouseId, :packmaterial, :autodeliveryflag, dh.tracking_no,:packageType " +
					"FROM doc_do_header dh WHERE  dh.wave_id = :waveId  " +
					"AND dh.release_status = :releaseStatus AND dh.status = :doStatus";
		}

		this.createSQLQuery(sql)
				.setParameter("volume",packMaterial.getVolume())
				.setParameter("materialWeigh",packMaterial.getWeight().add(innerMaterialWeight))
				.setParameter("operator",operator).setParameter("packmaterial",packMaterial.getMaterialsNo()).setParameter("autodeliveryflag",autoDeliveryFlag).setParameter("waveId",waveId)
				.setParameter("warehouseId",ParamUtil.getCurrentWarehouseId()).setParameter("releaseStatus", Constants.ReleaseStatus.RELEASE.getValue()).
				setParameter("packageType",PackageType.B.getValue())
				.setParameter("doStatus", DoStatus.ALL_CARTON.getValue()).executeUpdate();
    }

	public void createCartonDetail(Long waveId, String operator) {
		String sql  = "INSERT INTO doc_carton_detail (carton_header_id, do_header_id, do_detail_id, sku_id, qty_packed, create_time, create_by, update_time, update_by,warehouse_id) " +
				"SELECT ch.id, dh.id, dd.id, dd.sku_id, dd.picked_qty, now(), :operator, now(), :operator,ch.warehouse_id FROM doc_do_detail dd, doc_carton_header ch, doc_do_header dh " +
				"WHERE dd.do_header_id = dh.id AND ch.do_header_id = dh.id and dh.wave_id = :waveId AND dh.warehouse_id = :warehouseId AND dd.warehouse_id = :warehouseId " +
				"AND dh.is_deleted = 0 AND dd.is_deleted = 0 AND dh.release_status = :releaseStatus";
		this.createSQLQuery(sql).setParameter("operator", operator).setParameter("waveId", waveId).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).
				setParameter("releaseStatus", Constants.ReleaseStatus.RELEASE.getValue()).executeUpdate();
	}

	public List<Long> findIdList4PrintByOrderIds(List<Long> ids,Long carrierId) {
		String sql = " select ch.id from doc_carton_header ch " +
				" INNER JOIN doc_do_header dh on dh.id = ch.do_header_id " +
				" WHERE dh.release_status = :ReleaseStatus and dh.id in (:ids) AND dh.warehouse_id = :whId ";
		if(DEFAULT_CARRIER.equals(carrierId)){
			sql = sql +" AND dh.carrier_id is null ORDER BY dh.sort_grid_no ASC ";
		}else {
			sql = sql +" AND dh.carrier_id = :carrierId ORDER BY dh.sort_grid_no ASC ";
		}

		SQLQuery query = this.createSQLQuery(sql);
		query.setParameterList("ids",ids);
		if(!DEFAULT_CARRIER.equals(carrierId)){
			query.setParameter("carrierId",carrierId);
		}

		query.setParameter("ReleaseStatus",ReleaseStatus.RELEASE.getValue());
		query.setParameter("whId",ParamUtil.getCurrentWarehouseId());
		query.addScalar("id", Hibernate.LONG);
		return query.list();
	}

	public List<Long> findCarrierIdList4PrintByOrderIds(List<Long> doIdList) {
		String sql = " select DISTINCT(ifnull(dh.carrier_id,-999)) as carrier_id from doc_do_header dh  " +
				"     WHERE dh.release_status = :ReleaseStatus and dh.id in (:doIdList) AND dh.warehouse_id = :whId ";
		SQLQuery query = this.createSQLQuery(sql);
		query.setParameterList("doIdList",doIdList)
				.setParameter("ReleaseStatus",ReleaseStatus.RELEASE.getValue())
				.setParameter("whId",ParamUtil.getCurrentWarehouseId());
		query.addScalar("carrier_id", Hibernate.LONG);
		return query.list();
	}

	public List<RecheckDetailInfoDTO> findRecheckDetailInfo(List<Long> cartonIds) {
		StringBuilder hql = new StringBuilder("select new com.daxia.wms.delivery.recheck.dto.RecheckDetailInfoDTO(");
		hql.append("sku.productCode, sku.ean13, sku.productCname, cd.packedNumber,cd.packedNumber*ifnull(cd.packQty,1), o.volume, o.actualGrossWeight, o.grossWeight, dh.doNo, o.cartonNo, o.createdBy, o.createdAt)");
		hql.append(" from CartonHeader o right join o.cartonDetails cd left join o.doHeader dh left join cd.sku sku where o.id in (:cartonIds) ");
		Query query = this.createQuery(hql.toString());
		query.setParameterList("cartonIds",cartonIds);
		return (List<RecheckDetailInfoDTO>) query.list();
	}

    public Map<Long, Pair<BigDecimal,BigDecimal>> countCartonUnits(List<Long> cartonIds) {
        StringBuilder hql = new StringBuilder("SELECT o.id as cartonId, SUM(cd.packedNumber) as unitNum, SUM(ifnull(cd.packQty,1)*cd.packedNumber) as qty FROM  CartonHeader o RIGHT JOIN o.cartonDetails cd LEFT JOIN o.doHeader dh LEFT JOIN cd.sku sku WHERE o.id in (:cartonIds) GROUP BY o.id");
        Query query = this.createQuery(hql.toString());
        query.setParameterList("cartonIds", cartonIds);
        List<Object[]> results = query.list();
        Map<Long, Pair<BigDecimal, BigDecimal>> result1Map = Maps.newHashMap();
        for (Object[] objs : results) {
            result1Map.put((Long) objs[0], Pair.of((BigDecimal) objs[1], (BigDecimal) objs[2]));
        }
        return result1Map;
    }

	public boolean isPacked(Long doHeaderId, String packageType){
		String hql = "select count(ch.id) from CartonHeader ch where ch.doHeader.id = :doHeaderId and ch.warehouseId = :warehouseId and ch.ext1 = :packageType";
		Query query = this.createQuery(hql);
		query.setLong("doHeaderId", doHeaderId);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameter("packageType", packageType);
		return ((Number)query.uniqueResult()).intValue() > 0 ? true : false ;
	}

    public Integer findCount4Export(CartonHeaderFilter filter) {
        String sql = "select count(*) total from (SELECT ch.id";
        sql += " FROM doc_carton_header ch";
        sql += " INNER JOIN doc_do_header dh ON dh.id = ch.do_header_id";
        sql += " INNER JOIN md_carrier carrier ON carrier.id = dh.carrier_id";
        sql += " LEFT JOIN md_shop shop ON shop.id = dh.shop_id";
        sql += " LEFT JOIN md_province province ON province.id = dh.province";
        sql += " LEFT JOIN md_city city ON city.id = dh.city";
        sql += " LEFT JOIN md_county county ON county.id = dh.county";
        sql += " LEFT JOIN doc_carton_detail dcd on dcd.carton_header_id = ch.id";
        sql += " WHERE dh.id = ch.do_header_id AND ch.is_deleted = 0";
        sql += " AND dh.do_type = 1 AND dh.`status` = '80' AND ch.warehouse_id = :warehouseId";
        if (filter.getDoShipTimeFrom() != null) {
            sql += " AND dh.ship_time >= :fromDate ";
        }
        if (filter.getDoShipTimeTo() != null) {
            sql += " AND dh.ship_time <= :toDate ";
        }
        if (filter.getCarrierId() != null) {
            sql += " AND dh.carrier_id = :carrierId ";
        }
        if (StringUtil.isNotEmpty(filter.getOriginalSoCode())) {
            sql += " AND dh.original_so_code = :originalSoCode ";
        }
        if (StringUtil.isNotEmpty(filter.getWayBill())) {
            sql += " AND ch.way_bill = :wayBill ";
        }
        if (filter.getShopId() != null) {
            sql += " AND dh.shop_id = :shopId ";
        }
        if (StringUtil.isNotEmpty(filter.getDoNo())) {
            sql += " AND dh.do_no = :doNo ";
        }
		if (filter.getMerchantId() != null) {
			sql += " AND dh.merchant_id = :merchantId ";
		}
        sql += " GROUP BY ch.id ) tt ";
        SQLQuery query = createSQLQuery(sql);
        //查询条件
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        if (filter.getDoShipTimeFrom() != null) {
            query.setTimestamp("fromDate", filter.getDoShipTimeFrom());
        }
        if (filter.getDoShipTimeTo() != null) {
            query.setTimestamp("toDate", filter.getDoShipTimeTo());
        }
        if (filter.getCarrierId() != null) {
            query.setLong("carrierId", filter.getCarrierId());
        }
        if (StringUtil.isNotEmpty(filter.getOriginalSoCode())) {
            query.setString("originalSoCode", filter.getOriginalSoCode());
        }
        if (StringUtil.isNotEmpty(filter.getWayBill())) {
            query.setString("wayBill", filter.getWayBill());
        }
        if (filter.getShopId() != null) {
            query.setLong("shopId", filter.getShopId());
        }
        if (StringUtil.isNotEmpty(filter.getDoNo())) {
            query.setString("doNo", filter.getDoNo());
        }
		if (filter.getMerchantId() != null) {
			query.setLong("merchantId", filter.getMerchantId());
		}
        query.addScalar("total", Hibernate.INTEGER);
        return (Integer) query.uniqueResult();
    }


    @Loggable
    public List<Object[]> find4Export(CartonHeaderFilter filter,Integer startIndex,Integer pageSize) {
        String sql = "SELECT dh.original_so_code, shop.`name`, ch.way_bill, carrier.dist_supp_comp_name, dh.ship_time,";
        sql += " IFNULL(dh.province_name,province.province_cname) AS province,";
        sql += " IFNULL(dh.city_name,city.city_cname) AS city,";
        sql += " IFNULL(dh.county_name,county.county_cname) AS county,";
        sql += " dh.address,ch.actual_grossweight,ch.freight,";
        sql += " dh.payment_method_name,dh.receivable,";
        sql += " dh.buyer_remark,dh.seller_remark,ch.create_by," +
                "IFNULL(sum(dcd.qty_packed),0),dh.do_no,dh.consignee_name," +
				"GROUP_CONCAT(CONCAT(sku.product_cname,\"*\",round(dcd.qty_packed)))";
        sql += " FROM doc_carton_header ch";
        sql += " INNER JOIN doc_do_header dh ON dh.id = ch.do_header_id";
        sql += " INNER JOIN md_carrier carrier ON carrier.id = dh.carrier_id";
        sql += " LEFT JOIN md_shop shop ON shop.id = dh.shop_id";
        sql += " LEFT JOIN md_province province ON province.id = dh.province";
        sql += " LEFT JOIN md_city city ON city.id = dh.city";
        sql += " LEFT JOIN md_county county ON county.id = dh.county";
        sql += " LEFT JOIN doc_carton_detail dcd on dcd.carton_header_id = ch.id";
		sql += " LEFT JOIN md_sku sku on sku.id = dcd.sku_id";
        sql += " WHERE dh.id = ch.do_header_id AND ch.is_deleted = 0";
        sql += " AND dh.do_type = 1 AND dh.`status` = '80' AND ch.warehouse_id = :warehouseId";
        if (filter.getDoShipTimeFrom() != null) {
            sql += " AND dh.ship_time >= :fromDate ";
        }
        if (filter.getDoShipTimeTo() != null) {
            sql += " AND dh.ship_time <= :toDate ";
        }
        if (filter.getCarrierId() != null) {
            sql += " AND dh.carrier_id = :carrierId ";
        }
        if (StringUtil.isNotEmpty(filter.getOriginalSoCode())) {
            sql += " AND dh.original_so_code = :originalSoCode ";
        }
        if (StringUtil.isNotEmpty(filter.getWayBill())) {
            sql += " AND ch.way_bill = :wayBill ";
        }
        if (filter.getShopId() != null) {
            sql += " AND dh.shop_id = :shopId ";
        }
        if (StringUtil.isNotEmpty(filter.getDoNo())) {
            sql += " AND dh.do_no = :doNo ";
        }
		if (filter.getMerchantId() != null) {
			sql += " AND dh.merchant_id = :merchantId ";
		}

        sql += " GROUP BY ch.id ORDER BY dh.ship_time ASC limit :startIndex,:pageSize ";
        SQLQuery query = createSQLQuery(sql);
        //查询条件
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        if (filter.getDoShipTimeFrom() != null) {
            query.setTimestamp("fromDate", filter.getDoShipTimeFrom());
        }
        if (filter.getDoShipTimeTo() != null) {
            query.setTimestamp("toDate", filter.getDoShipTimeTo());
        }
        if (filter.getCarrierId() != null) {
            query.setLong("carrierId", filter.getCarrierId());
        }
        if (StringUtil.isNotEmpty(filter.getOriginalSoCode())) {
            query.setString("originalSoCode", filter.getOriginalSoCode());
        }
        if (StringUtil.isNotEmpty(filter.getWayBill())) {
            query.setString("wayBill", filter.getWayBill());
        }
        if (filter.getShopId() != null) {
            query.setLong("shopId", filter.getShopId());
        }
        if (StringUtil.isNotEmpty(filter.getDoNo())) {
            query.setString("doNo", filter.getDoNo());
        }
		if (filter.getMerchantId() != null) {
			query.setLong("merchantId", filter.getMerchantId());
		}
        query.setInteger("startIndex", startIndex);
        query.setInteger("pageSize", pageSize);
        return query.list();
    }

    public void insertCartonRewriteMsg(Long doId) {
        String sql = "insert into one_carton_rewrite_msg (carton_id) select t.id from doc_carton_header t where t.do_header_id = :doId and t.is_deleted = 0 " +
                " and exists ( select 1 from doc_carton_detail cd where cd.carton_header_id = t.id and cd.qty_packed > 0 ) ";
        SQLQuery query = createSQLQuery(sql.toString());
        query.setLong("doId",doId);
        query.executeUpdate();
    }

	public List<CartonHeader> findByWaybillForYouBei(String waybill) {
		String hql = "from CartonHeader o where o.wayBill = :wayBill ";
		Query query = this.createQuery(hql);
		query.setParameter("wayBill", waybill);
		return (List<CartonHeader>)query.list();
	}

	public int updatePackMaterialNo(String cartonNo ,String packMaterialNo) {
		String hqlUpdate = "update CartonHeader o set o.packMaterialNo = :packMaterialNo where o.cartonNo = :cartonNo and o.warehouseId = :warehouseId";
		Query query = createUpdateQuery(hqlUpdate);
		query.setString("packMaterialNo", packMaterialNo);
		query.setString("cartonNo",cartonNo);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		int updateCount = query.executeUpdate();
		return updateCount;
	}

	public Long countByDoIds(Integer  goodPass, Integer weightFlag, List<Long> doIds){
		String sql = "select count(*) from CartonHeader o where o.warehouseId = :warehouseId  and " +
				"o.doHeader.id in (:doIds)";
		if(Objects.nonNull(goodPass)){
			sql += " and o.goodPass=:goodPass";
		}
		if(Objects.nonNull(weightFlag)){
			sql += " and o.weightFlag=:weightFlag";
		}
		Query query = getSession().createQuery( sql);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameterList("doIds", doIds);
		if(Objects.nonNull(goodPass)){
			query.setInteger("goodPass", goodPass);
		}
		if(Objects.nonNull(weightFlag)){
			query.setInteger("weightFlag", weightFlag);
		}
	return (Long) query.uniqueResult();

	}

	/**
	 * 更新货物放行状态
	 * @param id
	 * @param goodsPass
	 * @return
	 */
	@Transactional
	public int updateGoodsPass(Long id,Integer goodsPass ){
		String sql = "UPDATE doc_carton_header o SET o.good_pass = :goodsPass where id = :id";
		SQLQuery query =  getSession().createSQLQuery(sql);
		query.setInteger("goodsPass", goodsPass);
		query.setLong("id", id);
		return query.executeUpdate();
	}

	/**
	 * 查找销售单中未货物放行的箱信息
	 * @param batchSize
	 * @param warehouseId
	 * @return 返回格式 [ [carton_id,do_no] ,... ]
	 */
	public List<Object[]> findNeedPassCartonNoList(Integer batchSize, Long warehouseId) {
		String sql=" select ch.id carton_id,do_no from doc_carton_header ch join doc_do_header dh on ch.do_header_id=dh.id " +
				" where good_pass = 0  and ch.warehouse_id = :warehouseId and dh.do_type = 1 AND ch.`status` = '40' ";
		SQLQuery query =getSession().createSQLQuery(sql);
		query.setLong("warehouseId",warehouseId);
		query.setMaxResults(batchSize);
		return query.list();
	}

    public CartonHeader selectOneCartonInfoByFilter(CartonHeaderFilter filter) {
        StringBuilder hql = new StringBuilder();
        hql.append(buildSelectHql(filter));
        hql.append(" where 1=1 ");
        List<Object> paramList = new ArrayList<Object>();
        String condition = buildConditionHql(filter, paramList);
        hql.append(condition);
        log.debug("hql:[{}] ", hql);
        log.debug("˙ ", condition);
        Query query = createQuery(hql.toString());
		this.prepareParameter(paramList, query);
		return (CartonHeader)query.setMaxResults(1).uniqueResult();
    }

	public List<String> likeByTrackingNo(String trackingNo) {
		String sql = "select carton_no from doc_carton_header ch where ch.warehouse_id = :warehouseId and ch.way_bill" +
				" like :trackingNo";
		Query query = this.createSQLQuery(sql);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameter("trackingNo", trackingNo + "%");

		return (List<String>)query.list();
	}
}