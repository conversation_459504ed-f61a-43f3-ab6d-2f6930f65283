package com.daxia.wms.delivery.deliveryorder.job;


import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.NotifyCSType;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateHeader;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateService;
import com.daxia.wms.delivery.deliveryorder.service.IDoAllocate;
import com.daxia.wms.delivery.deliveryorder.service.impl.DoAllocateServiceImpl;
import com.daxia.wms.exp.dto.DoExpDto;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.exp.service.impl.ExpFacadeServiceImpl;
import com.daxia.wms.master.helper.SysConfigHelper;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.jboss.seam.Component;
import org.jboss.seam.annotations.Name;

import java.util.*;

@Name("autoAllocateExecutor")
@lombok.extern.slf4j.Slf4j
public class AutoAllocateExecutor {


    @SuppressWarnings("unchecked")
    public void doAllocate(List<Long> alcheaderIds, Long warehouseId) {
        log.debug("start auto allocate:{}", alcheaderIds);
        if (warehouseId != null) {
            ParamUtil.setCurrentWarehouseId(warehouseId);
        }
        if (CollectionUtils.isEmpty(alcheaderIds)) {
            return;
        }

        DoAllocateService doAlcService = ((DoAllocateService) Component.getInstance(DoAllocateServiceImpl.class));
        ExpFacadeService expFacadeService = ((ExpFacadeService) Component.getInstance(ExpFacadeServiceImpl.class));
        // 记录失败异常订单集合
        List<Long> doIdListOfFail = new ArrayList<Long>();
        // 是否对异常订单处理配置项
        Integer failCheck = doAlcService.getFailCheckConfig();
        // 订单分配异常上限值
        Integer failMaxNum = doAlcService.getAllocateFailNum();

        for (Long doId : alcheaderIds) {
            try {
                DoAllocateHeader doAllocateHeader = doAlcService.getHeader(doId);
                if (doAllocateHeader != null) {
                    ParamUtil.setCurrentWarehouseId(doAllocateHeader.getWarehouseId());
                }

                if (doAlcService.autoAllocate(doId).get(IDoAllocate.NO_ENOUGH_STOCK_QTY) != null) {
                    // 调用接口通知客服；
                    DoExpDto dto = new DoExpDto();
                    dto.setId(doId);
                    dto.setHoldCode(Constants.Reason.ALLOC_LACK.getValue());
                    dto.setNotifyType(NotifyCSType.AUTO.getValue());
                    // 如果配置自动通知客服，则缺货自动通知客服。
                    Boolean lackAutoAnounceCs = SysConfigHelper.getSwitchDefalutOpen("alloc.lack.autoAnounceCs");
                    if (lackAutoAnounceCs) {
                        expFacadeService.callCS(dto);
                    }
                }
            } catch (Exception e) {
                log.error("auto allocate do {" + doId + "} failed", e);

                // 如果是HibernateException先执行事务回滚操作,避免锁表
                if (e instanceof HibernateException) {
                    try {
                        log.debug("rollback begin");
                        Session session = (Session) Component.getInstance("hibernateSession");
                        session.getTransaction().rollback();
                        log.debug("rollback end");
                    } catch (Exception e2) {
                        log.error("rollback failed", e2);
                    }
                }
                doIdListOfFail.add(doId);
            }
            Session session = (Session) Component.getInstance("hibernateSession");
            session.clear();
        }
        // 记录失败异常订单集合
        List<DoAllocateHeader> alcheadersOfFail = ListUtil.isNotEmpty(doIdListOfFail) ? doAlcService.findByIds(doIdListOfFail) : Collections.EMPTY_LIST;
        // 处理分配失败的订单，失败次数加1
        if (failCheck != null && failCheck.intValue() == 1 && CollectionUtils.isNotEmpty(alcheadersOfFail)) {
            log.debug("doAllocate fail list: {}", alcheadersOfFail);

            Map<Long, List<String>> failNoMap = new HashMap<Long, List<String>>();

            for (DoAllocateHeader alcheader : alcheadersOfFail) {
                try {
                    ParamUtil.setCurrentWarehouseId(alcheader.getWarehouseId());
                    // failNum + 1
                     doAlcService.addFailNum(alcheader.getId());

                } catch (Exception e) {
                    log.error("failCheck  failed", e);
                    // 如果是HibernateException先执行事务回滚操作,避免锁表
                    if (e instanceof HibernateException) {
                        try {
                            log.debug("rollback begin");

                            Session session = (Session) Component.getInstance("hibernateSession");
                            session.getTransaction().rollback();

                            log.debug("rollback end");
                        } catch (Exception e2) {
                            log.error("rollback failed", e2);
                        }
                    }
                }
            }

        }

    }
}