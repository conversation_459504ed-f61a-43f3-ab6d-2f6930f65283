package com.daxia.wms.delivery.schedule.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.util.DateUtil;
import com.daxia.wms.Constants.JobKindCh;
import com.daxia.wms.Constants.WorkingShift;
import com.daxia.wms.delivery.schedule.service.ScheduleCalculateService;
import com.daxia.wms.master.dao.SchDoCountPredictionDao;
import com.daxia.wms.master.dao.SchForecastedScheduleDao;
import com.daxia.wms.master.dao.SchJobForecastDao;
import com.daxia.wms.master.entity.SchDoCountPrediction;
import com.daxia.wms.master.entity.SchForecastedSchedule;
import com.daxia.wms.master.entity.SchJobForecast;
import com.daxia.wms.master.service.SchMasterDataService;
import com.daxia.wms.schedule.vo.DoCountPerHour;
import com.daxia.wms.schedule.vo.DoCountPerOutTime;
import com.daxia.wms.schedule.vo.ScheduleResult;
import com.daxia.wms.schedule.vo.TimeDescOfJobShift;

import jodd.datetime.JDateTime;

/**
 * 排班结果计算Service实现
 */
@Name("scheduleCalculateService")
@lombok.extern.slf4j.Slf4j
public class ScheduleCalculateServiceImpl implements ScheduleCalculateService {

	@In
	private SchJobForecastDao schJobForecastDao;
	@In
	private SchForecastedScheduleDao schForecastedScheduleDao;
	@In
	private SchDoCountPredictionDao schDoCountPredictionDao;
	@In
	private SchMasterDataService schMasterDataService;

	@Override
	public List<Integer> calculateForecastResult(Date predictAt, String jobKind) {
		List<Integer> forecastedCount = new ArrayList<Integer>();

		// 首先， 去SchForecastedSchedule表（专供页面用于显示排班预测结果）找
		List<SchForecastedSchedule> pageResults = schForecastedScheduleDao.findForecastedSchedule(predictAt, jobKind);

		// 能找到，则直接返回
		if (pageResults != null && pageResults.size() >= 3) {
			forecastedCount.add(pageResults.get(0).getArrangedCount());
			forecastedCount.add(pageResults.get(1).getArrangedCount());
			forecastedCount.add(pageResults.get(2).getArrangedCount());
			return forecastedCount;
		}

		// 没找到，则去SchJobForecast表找
		List<SchJobForecast> jobResults = schJobForecastDao.findJobForecast(predictAt, jobKind);

		// 能找到，则copy into SchForecastedSchedule，然后返回
		if (jobResults != null && jobResults.size() >= 3) {
			Integer morningCount = jobResults.get(0).getArrangedCount();
			Integer noonCount = jobResults.get(1).getArrangedCount();
			Integer eveningCount = jobResults.get(2).getArrangedCount();

			schMasterDataService.saveForecastedSchedule(predictAt, jobKind, morningCount, noonCount, eveningCount);

			forecastedCount.add(morningCount);
			forecastedCount.add(noonCount);
			forecastedCount.add(eveningCount);
			return forecastedCount;
		}

		// 没找到，则实时计算，然后copy into SchForecastedSchedule，最后返回
		DoCountPerHour[] doCountPerHour = getDoCountPerHour(predictAt);
		DoCountPerOutTime[] doCountPerOutTime = getDoCountPerOutTime(predictAt);
		TimeDescOfJobShift[] timeDescOfJobShift = getTimeDescOfJobShift(predictAt);
		ScheduleResult results = new ScheduleResult();
		boolean isNormalData = false;
		try {
			//results = PlusClientUtil.getScheduleService().findJobForecast(doCountPerHour, doCountPerOutTime, timeDescOfJobShift);
			isNormalData = true;
		} catch (Exception e) {
			e.printStackTrace();
			results.setPickerNum4Evening(-1);
			results.setPickerNum4Moring(-1);
			results.setPickerNum4Noon(-1);
			results.setSorterNum4Moring(-1);
			results.setSorterNum4Noon(-1);
			results.setSorterNum4Evening(-1);
			results.setRecheckerNum4Moring(-1);
			results.setRecheckerNum4Noon(-1);
			results.setRecheckerNum4Evening(-1);
		}

		Integer num1 = results.getPickerNum4Moring();
		Integer num2 = results.getPickerNum4Noon();
		Integer num3 = results.getPickerNum4Evening();
		Integer num4 = results.getSorterNum4Moring();
		Integer num5 = results.getSorterNum4Noon();
		Integer num6 = results.getSorterNum4Evening();
		Integer num7 = results.getRecheckerNum4Moring();
		Integer num8 = results.getRecheckerNum4Noon();
		Integer num9 = results.getRecheckerNum4Evening();
		if (isNormalData) {
			schMasterDataService.saveForecastedSchedule(predictAt, JobKindCh.PICK.getValue(), num1, num2, num3);
			schMasterDataService.saveForecastedSchedule(predictAt, JobKindCh.SORT.getValue(), num4, num5, num6);
			schMasterDataService.saveForecastedSchedule(predictAt, JobKindCh.RECHECK.getValue(), num7, num8, num9);
		}

		if (JobKindCh.PICK.getValue().equals(jobKind)) {
			forecastedCount.add(num1);
			forecastedCount.add(num2);
			forecastedCount.add(num3);
		} else if (JobKindCh.SORT.getValue().equals(jobKind)) {
			forecastedCount.add(num4);
			forecastedCount.add(num5);
			forecastedCount.add(num6);
		} else if (JobKindCh.RECHECK.getValue().equals(jobKind)) {
			forecastedCount.add(num7);
			forecastedCount.add(num8);
			forecastedCount.add(num9);
		}
		return forecastedCount;
	}

	private DoCountPerHour[] getDoCountPerHour(Date predictAt) {
		List<SchDoCountPrediction> resultList = schDoCountPredictionDao.getDoCountPerHour(predictAt);
		DoCountPerHour[] returnArr = new DoCountPerHour[resultList.size()];
		for (int i = 0; i < returnArr.length; i++) {
			SchDoCountPrediction obj = resultList.get(i);
			JDateTime date = getDateFromTimeStr(predictAt, obj.getDailyTime());
			Integer count = obj.getTotalCount();
			DoCountPerHour numDto = new DoCountPerHour(date, count);
			returnArr[i] = numDto;
		}
		return returnArr;
	}

	private DoCountPerOutTime[] getDoCountPerOutTime(Date predictAt) {
		List<SchDoCountPrediction> resultList = schDoCountPredictionDao.getDoCountPerOutTime(predictAt);
		DoCountPerOutTime[] returnArr = new DoCountPerOutTime[resultList.size()];
		for (int i = 0; i < returnArr.length; i++) {
			SchDoCountPrediction obj = resultList.get(i);
			JDateTime date = getDateFromTimeStr(predictAt, obj.getPlanShipTime());
			Integer count = obj.getTotalCount();
			DoCountPerOutTime numDto = new DoCountPerOutTime(date, count);
			returnArr[i] = numDto;
		}
		return returnArr;
	}

	/**
	 * 从 2014-09-20 和 25:30:00 这种格式的两个字符串中获取一个JDateTime，<br/>
	 * 25那里约定最大为47，所以dayInterval最多只加1
	 * 
	 * @param predictAt
	 * @param timeStr
	 * @return
	 */
	private JDateTime getDateFromTimeStr(Date predictAt, String timeStr) {
		int hour = Integer.parseInt(timeStr.substring(0, 2));
		int minute = Integer.parseInt(timeStr.substring(3, 5));
		int second = Integer.parseInt(timeStr.substring(6, 8));
		int dayInterval = 0;
		if (hour >= 24) {
			hour = hour % 24;
			dayInterval = 1;
		}
		Calendar cal = Calendar.getInstance();
		cal.setTime(predictAt);
		cal.add(Calendar.DAY_OF_MONTH, dayInterval);// 把日期往后增加一天.整数往后推,负数往前移动
		cal.set(Calendar.HOUR_OF_DAY, hour);
		cal.set(Calendar.MINUTE, minute);
		cal.set(Calendar.SECOND, second);
		return new JDateTime(cal.getTime());
	}

	public static void main(String[] args) {
		System.out.println(24 % 24);
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		cal.add(Calendar.DAY_OF_MONTH, 1);// 把日期往后增加一天.整数往后推,负数往前移动
		cal.set(Calendar.HOUR_OF_DAY, 21);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		System.out.println(DateUtil.dateToString(cal.getTime(), DateUtil.DATETIME_PATTERN));
	}

	private TimeDescOfJobShift[] getTimeDescOfJobShift(Date predictAt) {
		List<TimeDescOfJobShift> resultList = new ArrayList<TimeDescOfJobShift>();
		List<Object> objArrList = schMasterDataService.getTimeDescOfJobShift();
		TimeDescOfJobShift dtoPick = new TimeDescOfJobShift();
		TimeDescOfJobShift dtoSort = new TimeDescOfJobShift();
		TimeDescOfJobShift dtoRecheck = new TimeDescOfJobShift();
		for (Object obj : objArrList) {
			Object[] objArr = (Object[]) obj;
			int index = 0;
			String jobKind = (String) objArr[index++];
			String shiftName = (String) objArr[index++];
			Integer efficiency = ((BigDecimal) objArr[index++]).intValue();
			JDateTime startTime = getDateFromTimeStr(predictAt, (String) objArr[index++]);
			JDateTime endTime = getDateFromTimeStr(predictAt, (String) objArr[index++]);
			JDateTime restTimeFm = getDateFromTimeStr(predictAt, (String) objArr[index++]);
			JDateTime restTimeTo = getDateFromTimeStr(predictAt, (String) objArr[index++]);
			if (JobKindCh.PICK.getValue().equals(jobKind)) {
				dtoPick.setEfficiency(efficiency);
				if (WorkingShift.Morning.getValue().equals(shiftName)) {
					dtoPick.setMorningWorkBeginTime(startTime);
					dtoPick.setMorningWorkEndTime(endTime);
					dtoPick.setMorningEatBeginTime(restTimeFm);
					dtoPick.setMorningEatEndTime(restTimeTo);
				} else if (WorkingShift.Noon.getValue().equals(shiftName)) {
					dtoPick.setNoonWorkBeginTime(startTime);
					dtoPick.setNoonWorkEndTime(endTime);
					dtoPick.setNoonEatBeginTime(restTimeFm);
					dtoPick.setNoonEatEndTime(restTimeTo);
				} else if (WorkingShift.Evening.getValue().equals(shiftName)) {
					dtoPick.setEveningWorkBeginTime(startTime);
					dtoPick.setEveningWorkEndTime(endTime);
					dtoPick.setEveningEatBeginTime(restTimeFm);
					dtoPick.setEveningEatEndTime(restTimeTo);
				}
			} else if (JobKindCh.SORT.getValue().equals(jobKind)) {
				dtoSort.setEfficiency(efficiency);
				if (WorkingShift.Morning.getValue().equals(shiftName)) {
					dtoSort.setMorningWorkBeginTime(startTime);
					dtoSort.setMorningWorkEndTime(endTime);
					dtoSort.setMorningEatBeginTime(restTimeFm);
					dtoSort.setMorningEatEndTime(restTimeTo);
				} else if (WorkingShift.Noon.getValue().equals(shiftName)) {
					dtoSort.setNoonWorkBeginTime(startTime);
					dtoSort.setNoonWorkEndTime(endTime);
					dtoSort.setNoonEatBeginTime(restTimeFm);
					dtoSort.setNoonEatEndTime(restTimeTo);
				} else if (WorkingShift.Evening.getValue().equals(shiftName)) {
					dtoSort.setEveningWorkBeginTime(startTime);
					dtoSort.setEveningWorkEndTime(endTime);
					dtoSort.setEveningEatBeginTime(restTimeFm);
					dtoSort.setEveningEatEndTime(restTimeTo);
				}
			} else if (JobKindCh.RECHECK.getValue().equals(jobKind)) {
				dtoRecheck.setEfficiency(efficiency);
				if (WorkingShift.Morning.getValue().equals(shiftName)) {
					dtoRecheck.setMorningWorkBeginTime(startTime);
					dtoRecheck.setMorningWorkEndTime(endTime);
					dtoRecheck.setMorningEatBeginTime(restTimeFm);
					dtoRecheck.setMorningEatEndTime(restTimeTo);
				} else if (WorkingShift.Noon.getValue().equals(shiftName)) {
					dtoRecheck.setNoonWorkBeginTime(startTime);
					dtoRecheck.setNoonWorkEndTime(endTime);
					dtoRecheck.setNoonEatBeginTime(restTimeFm);
					dtoRecheck.setNoonEatEndTime(restTimeTo);
				} else if (WorkingShift.Evening.getValue().equals(shiftName)) {
					dtoRecheck.setEveningWorkBeginTime(startTime);
					dtoRecheck.setEveningWorkEndTime(endTime);
					dtoRecheck.setEveningEatBeginTime(restTimeFm);
					dtoRecheck.setEveningEatEndTime(restTimeTo);
				}
			}
		}
		resultList.add(dtoPick);
		resultList.add(dtoSort);
		resultList.add(dtoRecheck);
		TimeDescOfJobShift[] returnArr = new TimeDescOfJobShift[resultList.size()];
		for (int i = 0; i < returnArr.length; i++) {
			returnArr[i] = resultList.get(i);
		}
		return returnArr;
	}

}
