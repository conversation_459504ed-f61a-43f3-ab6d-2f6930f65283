package com.daxia.wms.delivery.invoice.job;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Constants.InvoicePrintFlg;
import com.daxia.wms.delivery.invoice.service.InvoiceJinrenService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.exp.job.AbstractJob;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.util.ArrayList;
import java.util.List;

@Name("com.daxia.wms.delivery.writeJinrenInvoiceJob")
@AutoCreate
@lombok.extern.slf4j.Slf4j
public class WriteJinrenInvoiceJob extends AbstractJob {
	@In
	private InvoiceJinrenService invoiceJinrenService;
	@In
    private WaveService waveService;

	@Override
	protected void doRun() {
			log.info("Begin Run WriteJinrenInvoiceWriteJob, Please Waiting ............... ");
			try {
				// 查找仓库列表，如果没有配置，默认为所有仓库的数据。
				String strWhIds = SystemConfig.getConfigValue(
						"invoiceJinren.whIds", null);

				List<Long> whIdList = new ArrayList<Long>();
				if (!StringUtil.isEmpty(strWhIds)) {
					String[] whIds = strWhIds.trim().split(",");
					for (String whId : whIds) {
						whIdList.add(Long.valueOf(whId));
					}
				}
				for (Long warehouseId : whIdList) {
					ParamUtil.setCurrentWarehouseId(warehouseId);
					// 查询波次
					List<WaveHeader> initialWaveHeaders = waveService
							.findWaveByPrintFlg(InvoicePrintFlg.INIT.getValue());
					// 事物在for 循环级别。
					for (WaveHeader initialWaveHeader : initialWaveHeaders) {
						try {
							invoiceJinrenService.writeJinrenInvoice(initialWaveHeader);
						} catch (Exception e) {
							log.error("WriteJinrenInvoiceWriteJob encounter exception: waveNo:" + initialWaveHeader.getWaveNo(), e);
						}
					}
				}
			}catch (Exception e) {
				log.error("WriteJinrenInvoiceWriteJob encounter exception: ", e);
			}
			log.info("End WriteJinrenInvoiceWriteJob. ");
	}
}
