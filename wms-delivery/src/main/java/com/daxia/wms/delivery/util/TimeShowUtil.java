package com.daxia.wms.delivery.util;

import java.util.Date;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.SystemConfig;

@lombok.extern.slf4j.Slf4j
public class TimeShowUtil {
    /**
     * 计算2个时间的差值，返回小时、分钟的间隔差值； 比如：2012-03-02 01：30：00 与 2012-03-01 01：00：00；
     * 返回：[24，30]及表示间隔24小时30分
     * 
     * @param timeFrom
     * @param timeTo
     * @return
     */
    public static Integer[] calculateTime(Date timeFrom, Date timeTo) {
        timeTo = (null == timeTo ? new Date() : timeTo);
        Long timeMi = (timeTo.getTime() - timeFrom.getTime()) / (1000 * 60);
        int hour = (int) (timeMi / 60);
        int min = (int) (timeMi % 60);
        return new Integer[] { hour, min };
    }

    /**
     * 格式化输出：times[0] + "小时" + times[1] + "分"
     * 
     * @param times
     * @return
     */
    public static String getShowTime(Integer[] times) {
        Integer hour = times[0];
        if (hour == 0) {
            return times[1] + "分";
        } else {
            return times[0] + "小时" + times[1] + "分";
        }
    }
}