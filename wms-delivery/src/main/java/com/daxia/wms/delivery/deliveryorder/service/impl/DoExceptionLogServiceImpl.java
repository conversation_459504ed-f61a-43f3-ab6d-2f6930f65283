package com.daxia.wms.delivery.deliveryorder.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import com.daxia.framework.common.service.Dictionary;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.deliveryorder.dao.DoExceptionLogDAO;
import com.daxia.wms.delivery.deliveryorder.dao.DoHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.dto.DoExStatusOpDto;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoExceptionLog;
import com.daxia.wms.delivery.deliveryorder.service.DoExceptionLogService;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoExpStatus;
import com.daxia.wms.Constants.ReleaseStatus;

/**
 * DO异常日志service实现
 */
@Name("com.daxia.wms.delivery.doExceptionLogService")
@lombok.extern.slf4j.Slf4j
public class DoExceptionLogServiceImpl implements DoExceptionLogService {

    @In
    private DoExceptionLogDAO doExceptionLogDAO;
    
    @In
    private DoHeaderDAO doHeaderDAO;

    /**
     * 保存异常日志
     */
    @Override
    public void saveDoExceptionLog(DoExceptionLog doExceptionLog) {
        doExceptionLogDAO.save(doExceptionLog);
    }

    //根据订单查询异常日志
    @Override
    public List<DoExceptionLog> findExceptionLog(Long doHeaderId) {
        return doExceptionLogDAO.findExceptionLog(doHeaderId);
    }

    //格式化日志信息
    @Override
    public List<String> formatExceptionLogs(List<DoExceptionLog> exceptionLogs) {
        Map<String, String> opMap = Dictionary.getDictionary("EX_OP_TYPE");
        List<String> formatLogsList = new ArrayList<String>();
        for (DoExceptionLog exceptionLog : exceptionLogs) {
            StringBuilder formatLogString = new StringBuilder();
            // 入/出暂存位的日志记录方式：订单[doNo]在[createAt]由[createBy]入/出暂存位[DoLackLocationCode]，备注[]
            if (StringUtil.isNotEmpty(exceptionLog.getNotes()) && StringUtil.isIn(exceptionLog.getNotes(), "入暂存位", "出暂存位")) {
                formatLogString.append("订单[").append(exceptionLog.getDoNo()).append("]在");
                formatLogString.append(DateUtil.dateToString(exceptionLog.getCreatedAt(), "[yy-MM-dd HH:mm] "));
                formatLogString.append("由[<font color='red'>").append(exceptionLog.getOperator()).append("</font>]");
                formatLogString.append(exceptionLog.getNotes() == null ? " " : exceptionLog.getNotes());
            } else {
                //操作人
                formatLogString.append(DateUtil.dateToString(exceptionLog.getCreatedAt(), "[yy-MM-dd HH:mm] "));
                formatLogString.append("由[<font color='red'>").append(exceptionLog.getOperator()).append("</font>]");
                formatLogString.append("执行[");
                formatLogString.append(opMap.get(exceptionLog.getOperationType()));
                formatLogString.append("]操作");

                //冻结状态变更
                String fmReleaseStatus = exceptionLog.getFmReleaseStatus();
                String toReleaseStatus = exceptionLog.getToReleaseStatus();
                if (!fmReleaseStatus.equals(toReleaseStatus)) {
                    formatLogString.append(",冻结状态由[")
                        .append(Dictionary.getDictionary("RELEASE_STATUS").get(fmReleaseStatus)).append("]处理为[")
                        .append(Dictionary.getDictionary("RELEASE_STATUS").get(toReleaseStatus)).append("]");
                }

                //订单状态变更
                String toDoStatus = exceptionLog.getToDoStatus();
                String fmDoStatus = exceptionLog.getFmDoStatus();
                if (!toDoStatus.equals(fmDoStatus)) {
                    formatLogString.append(",DO状态从[")
                    .append(Dictionary.getDictionary("DO_STATUS").get(fmDoStatus)).append("]处理为[")
                    .append(Dictionary.getDictionary("DO_STATUS").get(toDoStatus)).append("]");
                }
                
                //异常状态变更
                String fmExceptionStatus = exceptionLog.getFmExceptionStatus();
                String toExceptionStatus = exceptionLog.getToExceptionStatus();
                if (StringUtil.isEmpty(fmExceptionStatus)) {
                     formatLogString.append(",异常状态由[ ]").append("处理为[")
                     .append(Dictionary.getDictionary("EXCEPTION_STATUS").get(toExceptionStatus)).append("]");
                } else if (!fmExceptionStatus.equals(toExceptionStatus)) {
                    formatLogString.append(",异常状态由[")
                    .append(Dictionary.getDictionary("EXCEPTION_STATUS").get(fmExceptionStatus)).append("]处理为[")
                    .append(Dictionary.getDictionary("EXCEPTION_STATUS").get(toExceptionStatus)).append("]");
                }
                
                //冻结原因
                formatLogString.append(",冻结原因:[").append(exceptionLog.getHoldReason()).append("]");
            }
            
            
            //备注
            formatLogString.append(",备注:[").append(exceptionLog.getNotes() == null ? " " : exceptionLog.getNotes()).append("]");

            formatLogsList.add(formatLogString.toString());
        }

        return formatLogsList;
    }
    
    /**
     * 根据订单创建异常日志
     */
    @Override
    @Transactional
    public void saveReleaseExpLog(Long doId, String createBy){
    	DeliveryOrderHeader doHeader = doHeaderDAO.get(doId);
    	if(doHeader != null){
        	DoExceptionLog doExceptionLog = new DoExceptionLog();
        	doExceptionLog.setCarrierId(doHeader.getCarrierId());
        	doExceptionLog.setDoHeaderId(doId);
        	doExceptionLog.setDoNo(doHeader.getDoNo());
        	doExceptionLog.setDoType(doHeader.getDoType());
        	doExceptionLog.setFmDoStatus(doHeader.getStatus());
        	doExceptionLog.setFmExceptionStatus(doHeader.getExceptionStatus());
        	doExceptionLog.setFmReleaseStatus(ReleaseStatus.HOLD.getValue());
        	doExceptionLog.setHoldReason(doHeader.getHoldReason());
        	doExceptionLog.setHoldTime(doHeader.getHoldTime());
        	doExceptionLog.setOperator(createBy);
        	doExceptionLog.setToDoStatus(doHeader.getStatus());
        	doExceptionLog.setToExceptionStatus(DoExpStatus.COMPLETE.getValue());
        	doExceptionLog.setToReleaseStatus(ReleaseStatus.RELEASE.getValue());
        	doExceptionLog.setCreatedBy(createBy);
        	doExceptionLog.setUpdatedBy(createBy);
        	doExceptionLog.setOperationType(Constants.ExOpType.RL.getValue());//手动释放
        	saveDoExceptionLog(doExceptionLog);
    	}
    }
    
    /**
     * 保存异常日志
     */
	@Override
	@Transactional
	public void saveDoExceptionLog(DoExStatusOpDto doLogDto, DeliveryOrderHeader doHeader) {
		DoExceptionLog doExceptionLog = new DoExceptionLog();
		doExceptionLog.setDoHeaderId(doHeader.getId());
		doExceptionLog.setDoNo(doHeader.getDoNo());
		doExceptionLog.setDoType(doHeader.getDoType());
		doExceptionLog.setFmDoStatus(doLogDto.getStatus());
		doExceptionLog.setToDoStatus(doHeader.getStatus());
		doExceptionLog.setFmReleaseStatus(doLogDto.getReleaseStatus());
		doExceptionLog.setToReleaseStatus(doHeader.getReleaseStatus());
		doExceptionLog.setFmExceptionStatus(doLogDto.getExceptionStatus());
		doExceptionLog.setToExceptionStatus(doHeader.getExceptionStatus());
		doExceptionLog.setCarrierId(doHeader.getCarrierId());
		doExceptionLog.setHoldReason(doHeader.getHoldReason());
		doExceptionLog.setHoldTime(doHeader.getHoldTime());
		doExceptionLog.setOperationType(doLogDto.getOpType());
		if (StringUtil.isEmpty(doLogDto.getOprator())) {
			String userName = doExceptionLogDAO.getOperateUser();
	        doExceptionLog.setOperator(userName);
		} else {
			doExceptionLog.setOperator(doLogDto.getOprator());
		}
		this.saveDoExceptionLog(doExceptionLog);
	}

}
