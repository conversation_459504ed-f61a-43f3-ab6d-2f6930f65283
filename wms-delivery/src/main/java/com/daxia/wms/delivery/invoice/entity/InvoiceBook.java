package com.daxia.wms.delivery.invoice.entity;

import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.CascadeType;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;

/**
 * 发票薄
 */
@Entity
@Table(name = "invoice_book")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update invoice_book set is_deleted = 1 where id = ? and version = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class InvoiceBook extends WhBaseEntity {

	private static final long serialVersionUID = -6369752913677472353L;
	
    /**
     * 主键
     */
	private Long id;
	
    /**
     * 后台传过来的id
     */
	private Long origId;
	   
    /**
     * 发票代码
     */
	private String invoiceCode;
    
    /**
     * 起始发票号码
     */
	private String invoiceNoFrom;
	   
    /**
     * 截止发票号码
     */
	private String invoiceNoTo;
	   
    /**
     * 发票薄状态
     */
	private String status;
	   
    /**
     * 备注
     */
	private String notes;
	   
    /**
     * 是否删除 0:no 1:yes
     */
	private Integer isDeleted;
	   
    /**
     * 发票号码list
     */
	private List<InvoiceNo> invoiceNos;
	
	private String sourceSystem;//来源系统，药网订单：YW
	
	@Id
	@Column(name = "ID")
	@GeneratedValue(strategy = GenerationType.AUTO)  
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	@Column(name = "ORIG_ID")
	public Long getOrigId() {
		return origId;
	}
	
	public void setOrigId(Long origId) {
		this.origId = origId;
	}
	
	@Column(name = "INVOICE_CODE")
	public String getInvoiceCode() {
		return invoiceCode;
	}
	public void setInvoiceCode(String invoiceCode) {
		this.invoiceCode = invoiceCode;
	}
	
	@Column(name = "INVOICE_NO_FROM")
	public String getInvoiceNoFrom() {
		return invoiceNoFrom;
	}
	public void setInvoiceNoFrom(String invoiceNoFrom) {
		this.invoiceNoFrom = invoiceNoFrom;
	}
	
	@Column(name = "INVOICE_NO_TO")
	public String getInvoiceNoTo() {
		return invoiceNoTo;
	}
	public void setInvoiceNoTo(String invoiceNoTo) {
		this.invoiceNoTo = invoiceNoTo;
	}
	
	@Column(name = "STATUS")
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	
	@Column(name = "NOTES")
	public String getNotes() {
		return notes;
	}
	public void setNotes(String notes) {
		this.notes = notes;
	}
	
	@Column(name = "IS_DELETED")
    public Integer getIsDeleted() {
		return isDeleted;
	}
	public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "invoiceBook")
    @Where(clause = " IS_DELETED = 0 ")
    @Cascade(value={CascadeType.SAVE_UPDATE}) 
	public List<InvoiceNo> getInvoiceNos() {
		return invoiceNos;
	}
	public void setInvoiceNos(List<InvoiceNo> invoiceNos) {
		this.invoiceNos = invoiceNos;
	}
    
	@Column(name="SOURCE_SYSTEM")
    public String getSourceSystem() {
        return sourceSystem;
    }
    
    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }
}
