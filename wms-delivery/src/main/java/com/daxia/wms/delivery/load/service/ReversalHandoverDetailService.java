package com.daxia.wms.delivery.load.service;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.load.entity.ReversalHandoverDetail;
import com.daxia.wms.delivery.load.filter.ReversalHandoverDetailFilter;


/**
 * 逆向交接单接口
 */
public interface ReversalHandoverDetailService {

	public DataPage<ReversalHandoverDetail> findReversalHandoverDetails(ReversalHandoverDetailFilter filter, int startIndex, int pageSize);
	/**
	 * tms交接漏扫记入“已入配送异常包裹”
	 * @param cartonNo 箱号
	 * @param doHeader 发货单
	 */
	public void add(String cartonNo, DeliveryOrderHeader doHeader);
	/**
	 * 根据箱号和仓库id查逆向交接明细，判断是否存在
	 * @param cartonNo 箱号
	 * @param whId 仓库id
	 * @return
	 */
	public boolean isCartonInReversalHandoverDetail(String cartonNo, Long whId);
	
}
