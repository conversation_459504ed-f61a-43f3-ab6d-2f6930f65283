package com.daxia.wms.delivery.invoice.action;

import java.util.ArrayList;
import java.util.List;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.intercept.BypassInterceptors;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;
import com.daxia.wms.delivery.invoice.filter.InvoiceFilter;
import com.daxia.wms.delivery.invoice.filter.InvoiceNoFilter;
import com.daxia.wms.delivery.invoice.service.InvoiceBindService;
import com.daxia.wms.delivery.invoice.service.InvoiceNoService;
import com.daxia.wms.delivery.invoice.service.InvoiceService;
import com.daxia.wms.Constants.InvoiceNoStatus;
import com.daxia.wms.exp.service.ExpFacadeService;


/**
 * 绑定DO与发票
 */
@Name("com.daxia.wms.delivery.bindInvoiceActoin")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class BindInvoiceActoin extends PagedListBean<InvoiceHeader> {

    private static final long serialVersionUID = -6529313027336728713L;

	@In
	private DeliveryOrderService deliveryOrderService;
	@In
	private InvoiceService invoiceService;
	@In
	private InvoiceBindService invoiceBindService;
	@In
	private InvoiceNoService invoiceNoService;
	@In
    private ExpFacadeService expFacadeService;
	private InvoiceFilter invoiceFilter;
	private InvoiceNoFilter invoiceNoFilter;
	private String doStatus;
	private String shouldPrintNum;
	private String ptrintedNum;
	private String obsoleteNum;
	private String invoiceNo;
	private String billID;
	private Integer billNo;
	private String invoiceCode;
	private String status;
	private String newInvoiceNo;
	private String newInvoiceCode;
	private Long invoiceHeaderId;
	private String sortBy;
	private String oldInvoiceNo;
	private List<InvoiceNo> invoiceNoList;
	private String canStillAddBind;
	private String errNo;
	private String canShowUpdatePage;
	private String checkSortBy;
	
	private static final String INV_NOT_EXIST = "0";
	private static final String INV_WAS_USED = "1";
	private static final String BILL_NOT_EXIST = "2";
	private static final String BILL_IS_BINDED = "3";

	public BindInvoiceActoin() {
		super();
		dataPage = new DataPage<InvoiceHeader>();
		invoiceFilter = new InvoiceFilter();
		invoiceNoFilter = new InvoiceNoFilter();
		invoiceNoList = new ArrayList<InvoiceNo>();
	}
	
	/**
	 * 初始化进入页面
	 * @throws DeliveryException
	 */
	public void initPage() throws DeliveryException {
		DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(invoiceFilter.getDoNo());
		if (doHeader != null) {
			if (doHeader.getInvoiceQty().longValue() != 1) {
				checkSortBy = "MORE";
			}
		}
		
	}

	@Override
    public void query() {
    	int ptrintedNum = 0; 
    	int obsoleteNum = 0; 
    	invoiceNoFilter.setDoNo( invoiceFilter.getDoNo() );
        this.buildOrderFilterMap(invoiceNoFilter);
        invoiceNoList = invoiceBindService.findInvoiceNoByFilter(invoiceNoFilter);
        this.selectedMap.clear();
        for (InvoiceNo invoiceNo : invoiceNoList) {
			if (InvoiceNoStatus.PRINT.getValue().equals(invoiceNo.getStatus())) {
				ptrintedNum++;
			} else if (InvoiceNoStatus.DES.getValue().equals(invoiceNo.getStatus())) {
				obsoleteNum++;
			}
		}
        setPtrintedNum(String.valueOf(ptrintedNum));
        setObsoleteNum(String.valueOf(obsoleteNum));
    }
	
	/**
	 * 专供新增绑定页面调用，<br>
	 * 判断当前DO是否还能新增绑定发票， <br>
	 * 不能的话抛出对应异常信息作提示。<br>
	 * 返回true，则弹窗供用户继续操作：输入发票号
	 */
	public void canStillAddBind() throws DeliveryException {
		if (isALLBindedWithPrintedInv(invoiceFilter.getDoNo())) {
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_BIND_DO_ALL_BINDED);
		}
		DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(invoiceFilter.getDoNo());
		doStatus = doHeader.getStatus();
		invoiceBindService.checkDoForInvoiceOperate(doHeader);
		shouldPrintNum = doHeader.getInvoiceQty().toString();
		if ("0".equals(shouldPrintNum)) {
			throw new DeliveryException(
					DeliveryException.ERROR_DO_HAS_NO_INVOICE);
		}
		canStillAddBind = "YES";
	}

	/**
	 * 检查 DO号 是否存在对应DO，以及DO状态是否是【已出库】或【已取消】， 检查不通过则抛出对应异常
	 * @exception DeliveryException
	 *                两种异常信息：不存在、状态不对。
	 */
	public void checkDONo() throws DeliveryException {
		DeliveryOrderHeader doHeader = deliveryOrderService
				.findDoHeaderByDoNo(invoiceFilter.getDoNo());
		doStatus = doHeader.getStatus();
		invoiceBindService.checkDoForInvoiceOperate(doHeader);
		shouldPrintNum = doHeader.getInvoiceQty().toString();
		if ("0".equals(shouldPrintNum)) {
			throw new DeliveryException(
					DeliveryException.ERROR_DO_HAS_NO_INVOICE);
		}
		query();
	}

	/**
	 * 供 新增绑定 所用
	 * 检查该DO下所有的开票信息是否都绑定到了【已打印】状态的发票号码。<br>
	 * 若 1 、2 有一条满足，说明还可以新增绑定；若都不满足则抛异常：<br>
	 * 1、还有未绑定发票号码的<br>
	 * 2、全都有绑定发票号码，但是发票号码有的不是【已打印】状态。<br>
	 * 
	 * @param doNo
	 *            DO号
	 */
	private boolean isALLBindedWithPrintedInv(String doNo) {
		return invoiceService.isALLBindedWithPrintedInv(doNo);
	}

	/**
	 * 返回发票状态是否是【已打印】， <br>
	 * 同时 会 检查 发票号 是否存在对应发票，<br>
	 * 不存在则抛出对应异常
	 * 
	 * @param doNo
	 *            订单号
	 * @exception DeliveryException
	 */
	public boolean isInvoiceNoPrinted(String invoiceNo) throws DeliveryException {
		InvoiceNo invoiceNoObj = invoiceNoService.getInvoiceNoObjByInvNo(invoiceNo);
		if (invoiceNoObj == null) {
			setErrNo(INV_NOT_EXIST);
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_BIND_INVOICE_NOEXISTS, invoiceNo);
		}
		if (InvoiceNoStatus.PRINT.getValue().equals(invoiceNoObj.getStatus())) {
			setErrNo(INV_WAS_USED);
			return true;
		}
		return false;
	}
	
	/**
	 * 返回发票状态是否是【已打印】， 【已作废】, 【已遗失】<br>
	 * 同时 会 检查 发票号 是否存在对应发票，<br>
	 * 不存在则抛出对应异常
	 * 
	 * @param doNo
	 *            订单号
	 * @exception DeliveryException
	 */
	public boolean isInvoiceNoUsed(String invoiceNo) throws DeliveryException {
		InvoiceNo invoiceNoObj = invoiceNoService.getInvoiceNoObjByInvNo(invoiceNo);
		if (invoiceNoObj == null) {
			setErrNo(INV_NOT_EXIST);
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_BIND_INVOICE_NOEXISTS, invoiceNo);
		}
		if (InvoiceNoStatus.PRINT.getValue().equals(invoiceNoObj.getStatus())) {
			setErrNo(INV_WAS_USED);
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_BIND_INVNO_PRINTED);
		} else if (InvoiceNoStatus.DES.getValue().equals(invoiceNoObj.getStatus())) {
			setErrNo(INV_WAS_USED);
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_BIND_INVNO_DES);
		} else if (InvoiceNoStatus.LOST.getValue().equals(invoiceNoObj.getStatus())) {
			setErrNo(INV_WAS_USED);
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_BIND_INVNO_LOST);
		}
		return false;
	}
	
	/**
	 * 检查发票号码是否已绑定
	 * @throws DeliveryException
	 */
	public void checkInvNoForAddBind() throws DeliveryException {
		setErrNo(null);
		isInvoiceNoUsed(invoiceNo);
	}

	/**
	 * 检查 开票序号 是否关联着一个【已打印】状态的发票， 已绑定则抛出对应异常
	 * 
	 * @param doNo
	 *            订单号
	 * @exception DeliveryException
	 *                提示 当前开票信息已经绑定到发票号码：xxxx，不允许绑定
	 */
	public void checkBillNoForAddBind() throws DeliveryException {
		setErrNo(null);
		InvoiceHeader invHeader = invoiceService.getBill(
				invoiceFilter.getDoNo(),
				billNo == null ? null : String.valueOf(billNo));
		if (invHeader == null) {
			setErrNo(BILL_NOT_EXIST);
			throw new DeliveryException(
					DeliveryException.ERROR_INVOICE_BIND_BILLNO_NOEXISTS,
					billNo);
		}
		if (billNo == null) {
			setBillNo(invHeader.getSortBy());
		}
		String bindedPrintedInvNo = invoiceService
				.getBindedPrintedInvNo(invHeader);
		if (bindedPrintedInvNo != null) {
			setErrNo(BILL_IS_BINDED);
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_BIND_BILLNO_BINDED,
					bindedPrintedInvNo);
		}
	}

	/**
	 * 新增开票信息与发票号码的绑定：<br>
	 * 1、关联 发票号码X 与 开票信息Y；<br>
	 * 2、X修改为【已打印】状态；<br>
	 * 3、X所在发票簿修改为【使用中】状态。<br>
	 * 
	 * @param doNo
	 *            订单号
	 * @exception DeliveryException
	 *                绑定失败
	 */
	public void addBind() throws DeliveryException {
		checkInvNoForAddBind();
		checkBillNoForAddBind();
		canStillAddBind();
		invoiceNoService.bindInvoiceNoWithBill(invoiceNo, invoiceCode, invoiceFilter.getDoNo(), billNo);
		this.sayMessage(MESSAGE_SUCCESS);
	}
	
	/**
	 * 专供修改绑定页面调用，<br>
	 * 判断当前DO是否还能新增绑定发票， <br>
	 * 不能的话抛出对应异常信息作提示。<br>
	 * 返回true，则弹窗供用户继续操作：输入发票号
	 * @return boolean
	 */
	public boolean canChangeBind() throws DeliveryException {
		checkDONo();
		if (!isInvoiceNoPrinted(invoiceNo)) {
			throw new DeliveryException("asdasdada");
		}
		return true;
	}
	
	/**
	 * 修改开票信息与发票号码的绑定：<br>
	 * 1、关联 新发票号码X 与 原开票信息Y；<br>
	 * 2、X修改为【已打印】状态；老的发票号码修改为【作废】状态；<br>
	 * 3、X所在发票簿修改为【使用中】状态。<br>
	 * @exception DeliveryException
	 *                绑定失败
	 */
	public void modifyBind() throws DeliveryException {
		invoiceBindService.modifyBindInvoiceNo(invoiceHeaderId, invoiceNo, oldInvoiceNo);
		//调用作废接口
		InvoiceNo oldNo = invoiceNoService.getInvoiceNoObjByInvNoAndInvHeaderId(oldInvoiceNo,invoiceHeaderId);
		this.sayMessage(MESSAGE_SUCCESS);
	}
	
	/**
	 * 检查do状态是否允许弹出修改页面
	 * @throws DeliveryException
	 */
	public void checkCanUpdateNo() throws DeliveryException {
		DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(invoiceFilter.getDoNo());
		invoiceBindService.checkDoAndNo(doHeader, oldInvoiceNo);
		canShowUpdatePage = "YES";
	}
	
	public void initUpdatePage() {
	}

	public String getDoStatus() {
		return doStatus;
	}

	public void setDoStatus(String doStatus) {
		this.doStatus = doStatus;
	}
	
	public String getShouldPrintNum() {
		return shouldPrintNum;
	}

	public void setShouldPrintNum(String shouldPrintNum) {
		this.shouldPrintNum = shouldPrintNum;
	}

	public String getPtrintedNum() {
		return ptrintedNum;
	}

	public void setPtrintedNum(String ptrintedNum) {
		this.ptrintedNum = ptrintedNum;
	}

	public String getObsoleteNum() {
		return obsoleteNum;
	}

	public void setObsoleteNum(String obsoleteNum) {
		this.obsoleteNum = obsoleteNum;
	}

	public String getInvoiceNo() {
		return invoiceNo;
	}

	public void setInvoiceNo(String invoiceNo) {
		this.invoiceNo = invoiceNo;
	}

	public String getBillID() {
		return billID;
	}

	public void setBillID(String billID) {
		this.billID = billID;
	}

	public Integer getBillNo() {
		return billNo;
	}

	public void setBillNo(Integer billNo) {
		this.billNo = billNo;
	}

	public String getInvoiceCode() {
		return invoiceCode;
	}

	public void setInvoiceCode(String invoiceCode) {
		this.invoiceCode = invoiceCode;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@BypassInterceptors
	public InvoiceFilter getInvoiceFilter() {
		return invoiceFilter;
	}

	public void setInvoiceFilter(InvoiceFilter invoiceFilter) {
		this.invoiceFilter = invoiceFilter;
	}
	
    public String getNewInvoiceNo() {
		return newInvoiceNo;
	}

	public void setNewInvoiceNo(String newInvoiceNo) {
		this.newInvoiceNo = newInvoiceNo;
	}

	public String getNewInvoiceCode() {
		return newInvoiceCode;
	}

	public void setNewInvoiceCode(String newInvoiceCode) {
		this.newInvoiceCode = newInvoiceCode;
	}
	
	public InvoiceNoFilter getInvoiceNoFilter() {
		return invoiceNoFilter;
	}

	public void setInvoiceNoFilter(InvoiceNoFilter invoiceNoFilter) {
		this.invoiceNoFilter = invoiceNoFilter;
	}

	public Long getInvoiceHeaderId() {
		return invoiceHeaderId;
	}

	public void setInvoiceHeaderId(Long invoiceHeaderId) {
		this.invoiceHeaderId = invoiceHeaderId;
	}

	public String getSortBy() {
		return sortBy;
	}

	public void setSortBy(String sortBy) {
		this.sortBy = sortBy;
	}

	public String getOldInvoiceNo() {
		return oldInvoiceNo;
	}

	public void setOldInvoiceNo(String oldInvoiceNo) {
		this.oldInvoiceNo = oldInvoiceNo;
	}

	public List<InvoiceNo> getInvoiceNoList() {
		return invoiceNoList;
	}

	public void setInvoiceNoList(List<InvoiceNo> invoiceNoList) {
		this.invoiceNoList = invoiceNoList;
	}

	public String getErrNo() {
		return errNo;
	}

	public void setErrNo(String errNo) {
		this.errNo = errNo;
	}

	public String getCanShowUpdatePage() {
		return canShowUpdatePage;
	}

	public void setCanShowUpdatePage(String canShowUpdatePage) {
		this.canShowUpdatePage = canShowUpdatePage;
	}

	public String getCanStillAddBind() {
		return canStillAddBind;
	}

	public void setCanStillAddBind(String canStillAddBind) {
		this.canStillAddBind = canStillAddBind;
	}

	public String getCheckSortBy() {
		return checkSortBy;
	}

	public void setCheckSortBy(String checkSortBy) {
		this.checkSortBy = checkSortBy;
	}
	
}
