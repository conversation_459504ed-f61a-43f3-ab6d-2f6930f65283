package com.daxia.wms.delivery.deliveryorder.service.impl;

/**
 * 监听发货单回退事件。
 * 接口实现类在rollback方法中完成发货单回退功能。实现类需要先注册自己关注的回退事件，一个类可以关注多个回退事件。<br/>
 * 可能发生的回退事件有：
 * <ul>
 * 	<li>装箱复核完成（部分装箱复核）      ~   分拣完成    (Event: CARTON2SORTED)
 * 	<li>分拣完成（部分分拣）              ~   拣货完成    (Event: SORTED2PICKED)
 * 	<li>拣货完成（部分拣货）              ~   初始化      (Event: PICKED2INITIAL)
 * 	<li>分配完成（部分分配）              ~   初始化      (Event: ALLOCATED2INITIAL)
 * </ul>
 * @see com.daxia.wms.Constants.DoStatus
 */
public interface DoStatusRollbackListener {
	
	/**
	 * 发生发货单回退事件时，事件源将调用该方法对发货单状态进行回退。
	 * 如果一个类监听了多个回退事件，则在该方法中还需要对fromStatus和toStatus进行判断。
	 * @param doId 发货单Id号
	 * @param startStatus 回退前DO最初始的状态
	 * @param fromStatus 回退前的状态
	 * @param toStatus 回退后的状态
	 * @see com.daxia.wms.Constants.DoStatus
	 */
	public void rollback(Long doId, String startStatus, String fromStatus, String toStatus);
}
