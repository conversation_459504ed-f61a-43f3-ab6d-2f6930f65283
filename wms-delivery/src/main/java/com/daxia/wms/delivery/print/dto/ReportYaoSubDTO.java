package com.daxia.wms.delivery.print.dto;

import java.math.BigDecimal;

@lombok.extern.slf4j.Slf4j
public class ReportYaoSubDTO {
    private Long lotId;
    private String productCode;
    private String barCode;
    private String productName;
    //生产厂家
    private String manufacturer;
    //单位
    private String uom;
    //批号
    private String lotatt05;
    //生产日期
    private String lotatt01;
    //有效日期
    private String lotatt02;
    private BigDecimal qty;
    private BigDecimal actualQty;

    private BigDecimal actualQtyHY;

    //包装数量
    private BigDecimal qtyPackage;
    //进价
    private String lotatt09;

    private String lotatt12;

    private String notes;

    private String registerNo;//批准文号

    private String doSage;//剂型

    private String specification;//规格

    private String storageCondition;

    private BigDecimal totalMoney;

    private BigDecimal totalMoneyHY;

    private String productionLicense; //生产企业许可证号

    private BigDecimal sellPrice;

    //整件数
    private BigDecimal cQty;
    //散件数
    private BigDecimal bQty;

    private String registrationCertificateNo;//器械注册证号


    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getDoSage() {
        return doSage;
    }

    public void setDoSage(String doSage) {
        this.doSage = doSage;
    }

    public String getRegisterNo() {
        return registerNo;
    }

    public void setRegisterNo(String registerNo) {
        this.registerNo = registerNo;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }

    public String getLotatt05() {
        return lotatt05;
    }

    public void setLotatt05(String lotatt05) {
        this.lotatt05 = lotatt05;
    }

    public String getLotatt01() {
        return lotatt01;
    }

    public void setLotatt01(String lotatt01) {
        this.lotatt01 = lotatt01;
    }

    public String getLotatt02() {
        return lotatt02;
    }

    public void setLotatt02(String lotatt02) {
        this.lotatt02 = lotatt02;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getLotatt09() {
        return lotatt09;
    }

    public void setLotatt09(String lotatt09) {
        this.lotatt09 = lotatt09;
    }

    public BigDecimal getAmount() {
        if(lotatt09 == null || actualQty == null) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal(lotatt09).multiply(actualQty);
    }

    public Long getLotId() {
        return lotId;
    }

    public void setLotId(Long lotId) {
        this.lotId = lotId;
    }

    public BigDecimal getActualQty() {
        return actualQty;
    }

    public void setActualQty(BigDecimal actualQty) {
        this.actualQty = actualQty;
    }

    public BigDecimal getActualQtyHY() {
        return actualQtyHY;
    }

    public void setActualQtyHY(BigDecimal actualQtyHY) {
        this.actualQtyHY = actualQtyHY;
    }

    public String getStorageCondition() {
        return storageCondition;
    }

    public void setStorageCondition(String storageCondition) {
        this.storageCondition = storageCondition;
    }

    public BigDecimal getTotalMoney() {
        return totalMoney;
    }

    public void setTotalMoney(BigDecimal totalMoney) {
        this.totalMoney = totalMoney;
    }

    public BigDecimal getTotalMoneyHY() {
        return totalMoneyHY;
    }

    public void setTotalMoneyHY(BigDecimal totalMoneyHY) {
        this.totalMoneyHY = totalMoneyHY;
    }

    public BigDecimal getQtyPackage() {
        return qtyPackage;
    }

    public void setQtyPackage(BigDecimal qtyPackage) {
        this.qtyPackage = qtyPackage;
    }

    public String getProductionLicense() {
        return productionLicense;
    }

    public void setProductionLicense(String productionLicense) {
        this.productionLicense = productionLicense;
    }

    public BigDecimal getSellPrice() {
        return sellPrice;
    }

    public void setSellPrice(BigDecimal sellPrice) {
        this.sellPrice = sellPrice;
    }

    public BigDecimal getcQty() {
        return cQty;
    }

    public void setcQty(BigDecimal cQty) {
        this.cQty = cQty;
    }

    public BigDecimal getbQty() {
        return bQty;
    }

    public void setbQty(BigDecimal bQty) {
        this.bQty = bQty;
    }

    public String getLotatt12() {
        return lotatt12;
    }

    public void setLotatt12(String lotatt12) {
        this.lotatt12 = lotatt12;
    }

    public String getRegistrationCertificateNo() {
        return registrationCertificateNo;
    }

    public void setRegistrationCertificateNo(String registrationCertificateNo) {
        this.registrationCertificateNo = registrationCertificateNo;
    }
}
