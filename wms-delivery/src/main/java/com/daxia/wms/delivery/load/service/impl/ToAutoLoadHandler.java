package com.daxia.wms.delivery.load.service.impl;

import java.util.List;

import org.jboss.seam.annotations.In;

import com.daxia.framework.common.util.DateUtil;
import com.daxia.wms.delivery.deliveryorder.service.ToAutoHandler;
import com.daxia.wms.delivery.load.service.AutoDeliverService;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.Constants.LoadMode;

@lombok.extern.slf4j.Slf4j
public class ToAutoLoadHandler extends ToAutoHandler {
    @In
    private CartonService cartonService;
    
    @In
    private AutoDeliverService autoDeliverService;
	
	@Override
	protected void handle(Long doId) {
		List<CartonHeader> cartonHeaders = cartonService.findByDoId(doId);
		for (CartonHeader cartonHeader : cartonHeaders) {
			autoDeliverService.autoLoadAndDeliver(cartonHeader.getCartonNo(),
					LoadMode.AUTO.getValue(), DateUtil.getNowTime());
		}
	}

	@Override
	protected void setName() {
		this.name = "toAutoLoadHandler";
	}
}
