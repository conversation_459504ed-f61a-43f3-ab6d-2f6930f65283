package com.daxia.wms.delivery.print.service;

import java.util.List;

import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.master.service.PrintService;

/**
 *  调拨单打印
 */
public interface PrintAllotService extends PrintService<DeliveryOrderHeader> {

	/**
	 * 调拨单
	 * @param doList
	 * @param doType
	 * @return
	 */
    public List<String> printDoList(List<DeliveryOrderHeader> doList, String doType);

    /**
     * pdf格式的调拨单
     * @param doList
     * @param doType
     * @return
     */
    public byte[] generatePDFByDoList(List<DeliveryOrderHeader> doList, String doType);
}
