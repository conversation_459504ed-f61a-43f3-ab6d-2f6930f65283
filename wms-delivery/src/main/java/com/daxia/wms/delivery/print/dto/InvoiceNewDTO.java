package com.daxia.wms.delivery.print.dto;

import java.math.BigDecimal;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 发票打印DTO
 */
@XmlRootElement
@lombok.extern.slf4j.Slf4j
public class InvoiceNewDTO {
    private Long id;
    /**
     * 开票序号；
     */
    private String serialNum;
    
    /**
     * 付款人
     */
    private String payer;
    
    /**
     * 收款人 
     */
    private String receiver;
    
    /**
     * 纳税人识别码 
     */
    private String receiverCodeId;
    
    /**
     * 合计 
     */
    private BigDecimal total;
    
    /**
     * 合计人名币 
     */
    private String totalRmb;
    
    /**
     * 订单号 
     */
    private String doNo;

    /**
     * 网址 
     */
    private String website;
    
    /**
     * 开票人 
     */
    private String operator;

    /**
     * 开票日期 
     */
    private String printDate;

    /**
     * 行业类型 
     */
    private String industryType;

    /**
     * 波次号 
     */
    private String waveNo;

    /**
     * 分拣格号 
     */
    private String sortGridNo;
    
    /**
     * 发票号码
     */
    private String invoiceNo;
    
    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票详细信息list
     */
    private List<InvoiceNewDetailDTO> detailList;
    
    /**
     * 机器编号
     */
    private String machineNo;
    
    /**
     * 收款单位
     */
    private String receiveUnit;
    
    /**
     * 税务登记号
     */
    private String taxNo;
    
    /**
     * 送货单号
     */
    private String soNo;
    
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMachineNo() {
        return machineNo;
    }

    
    public void setMachineNo(String machineNo) {
        this.machineNo = machineNo;
    }

    
    public String getReceiveUnit() {
        return receiveUnit;
    }

    
    public void setReceiveUnit(String receiveUnit) {
        this.receiveUnit = receiveUnit;
    }

    
    public String getTaxNo() {
        return taxNo;
    }

    
    public void setTaxNo(String taxNo) {
        this.taxNo = taxNo;
    }

    
    public String getSoNo() {
        return soNo;
    }

    
    public void setSoNo(String soNo) {
        this.soNo = soNo;
    }
    
    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    public String getSerialNum() {
        return serialNum;
    }

    public void setSerialNum(String serialNum) {
        this.serialNum = serialNum;
    }

    public String getPayer() {
        return payer;
    }

    public void setPayer(String payer) {
        this.payer = payer;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public String getTotalRmb() {
        return totalRmb;
    }

    public void setTotalRmb(String totalRmb) {
        this.totalRmb = totalRmb;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
    
	@XmlElementWrapper(name="invocicePrintSubList")
	@XmlElement(name="invocicePrintSub")
    public List<InvoiceNewDetailDTO> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<InvoiceNewDetailDTO> detailList) {
        this.detailList = detailList;
    }

    public String getReceiverCodeId() {
        return receiverCodeId;
    }

    public void setReceiverCodeId(String receiverCodeId) {
        this.receiverCodeId = receiverCodeId;
    }

    public String getPrintDate() {
        return printDate;
    }

    public void setPrintDate(String printDate) {
        this.printDate = printDate;
    }

    public String getIndustryType() {
        return industryType;
    }

    public void setIndustryType(String industryType) {
        this.industryType = industryType;
    }
    
    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    /**
     * 静态内部类，显示发票条目详细信息
     */
    @XmlRootElement
    public static class InvoiceNewDetailDTO {
        /**
         * 项目/商品名 
         */
        private String item;
        
        /**
         * 单位 
         */
        private String uom;

        /**
         * 数量 
         */
        private BigDecimal qty;

        /**
         * 单价 
         */
        private BigDecimal unitPrice;

        /**
         * 金额 
         */
        private BigDecimal totalPrice;

        public String getItem() {
            return item;
        }

        public void setItem(String item) {
            this.item = item;
        }

        public String getUom() {
            return uom;
        }

        public void setUom(String uom) {
            this.uom = uom;
        }

        public BigDecimal getQty() {
            return qty;
        }

        public void setQty(BigDecimal qty) {
            this.qty = qty;
        }

        public BigDecimal getUnitPrice() {
            return unitPrice;
        }

        public void setUnitPrice(BigDecimal unitPrice) {
            this.unitPrice = unitPrice;
        }

        public BigDecimal getTotalPrice() {
            return totalPrice;
        }

        public void setTotalPrice(BigDecimal totalPrice) {
            this.totalPrice = totalPrice;
        }
    }
}