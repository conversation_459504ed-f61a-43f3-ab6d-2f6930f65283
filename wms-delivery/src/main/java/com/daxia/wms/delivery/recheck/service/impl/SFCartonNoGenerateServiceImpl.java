package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoPrintDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoPrint;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.DoWaveExService;
import com.daxia.wms.delivery.recheck.dao.CartonHeaderDAO;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;
import com.daxia.wms.master.integration.TmsIntegration;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.waybill.sf.service.SFOrderService;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.util.*;

@Name("sFCartonNoGenerateService")
@lombok.extern.slf4j.Slf4j
public class SFCartonNoGenerateServiceImpl implements CartonNoGenerateService {
    @In
    private DoWaveExService doWaveExService;
    @In(create = true)
    SFOrderService sFOrderService;
    @In
    CartonHeaderDAO cartonHeaderDAO;
    @In
    WarehouseCarrierService warehouseCarrierService;
    @In
    private DeliveryOrderService deliveryOrderService;
    @In
    private DoPrintDAO doPrintDAO;
    @In( value = "com.daxia.wms.master.tmsIntegration",create = true)
    private TmsIntegration tmsIntegration;

    @Override
    public void generatorCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        try {

            String orderNo = doHeader.getDoNo();
            if (StringUtil.isEmpty(orderNo)) {
                throw new DeliveryException(DeliveryException.DO_NO_IS_NULL);
            }

            String cartonNo;
            // 1、判断主箱号是否生产 未生成 走默认逻辑 已生成重新生产箱号 重新申请快递单号
            if (!isDoMainCarrierNoCartoned(doHeader.getTrackingNo())) {
                DoPrint doPrintInfo =
                    doPrintDAO.findByDoHeaderId(doHeader.getId(), Constants.DoPrintInfoType.WAYBILL_JSON.getValue());
                if (doPrintInfo == null || StringUtil.isEmpty(doHeader.getTrackingNo())) {
                    throw new DeliveryException(DeliveryException.WAYBILL_IMAGE_NOT_EXIST);
                }
                cartonNo = doHeader.getTrackingNo();
            } else {
                List<String> subLogisticsNos = tmsIntegration.applySubLogisticsNos(doHeader.getDoNo(), 1);
                if(CollectionUtils.isEmpty(subLogisticsNos)){
                    throw new DeliveryException(DeliveryException.WAYBILL_LOGISTICS_FETCH_ERROR);
                }
                cartonNo= subLogisticsNos.get(0);
            }
            cartonHeader.setCartonNo(cartonNo);
            cartonHeader.setWayBill(cartonNo);

        } catch (Exception e) {
            throw new DeliveryException(e);
        }
        if (StringUtil.isBlank(cartonHeader.getWayBill())) {
            throw new DeliveryException(DeliveryException.DO_HAS_NO_MAIN_CARTON_H_NO);
        }
    }

    /**
     * Do 是否已按配送商主单号装箱
     *
     * @param doMainCarrierNo
     * @return
     */
    private boolean isDoMainCarrierNoCartoned(String doMainCarrierNo) {
        Map<String, Object> params = new HashMap<String, Object>(2, 1);
        params.put("cartonNo", doMainCarrierNo);
        params.put("warehouseId", ParamUtil.getCurrentWarehouseId());
        return cartonHeaderDAO.isExists(params, null);
    }
}