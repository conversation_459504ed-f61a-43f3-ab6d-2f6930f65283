package com.daxia.wms.delivery.deliveryorder.service.impl;

import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants.EInvoiceIssueType;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.invoice.dao.InvoiceDao;
import com.daxia.wms.delivery.invoice.entity.InvoiceDetail;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader.InvoiceStatus;
import com.daxia.wms.delivery.invoice.service.ElectronicInvoiceService;
import com.daxia.wms.delivery.invoice.service.InvoiceService;
import com.daxia.wms.invoice.contants.Constants.EINV_QUERY_TYPE;
import com.daxia.wms.invoice.entity.*;
import com.daxia.wms.invoice.service.EinvInvoiceCallService;
import com.daxia.wms.master.entity.Merchant;
import com.daxia.wms.master.entity.MerchantInvoiceEx;
import com.daxia.wms.master.service.MerchantInvoiceExService;
import com.daxia.wms.master.service.MerchantService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Observer;
import org.jboss.seam.annotations.Transactional;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

@Name("com.daxia.wms.delivery.electronicInvoiceService")
@lombok.extern.slf4j.Slf4j
public class ElectronicInvoiceServiceImpl implements ElectronicInvoiceService {
    @In
    InvoiceDao invoiceDao;
    
    @In(create = true)
    private EinvInvoiceCallService einvInvoiceCallService;
    
    @In(create = true)
    private InvoiceService invoiceService;
    
    @In
    private MerchantService merchantService;
    
    @In
    private DeliveryOrderService deliveryOrderService;
    
    @In
    private MerchantInvoiceExService merchantInvoiceExService;
    // 日期格式
    private static final String dateFormat = "yyyy-MM-dd HH:mm:ss";
    private static final DateFormat df = new SimpleDateFormat(dateFormat);
    private static final Pattern PATTERN =  Pattern.compile("^[1](3|4|5|8)[0-9]{9}$");

    @Override
    @Transactional
	public void writeBackInvoice(DeliveryOrderHeader doHeader) {
		if (doHeader.getInvoiceFlag() == 1 && SystemConfig.configIsOpen(ConfigKeys.INVOICE_USE_ELECTRONIC_INVOICE, ParamUtil.getCurrentWarehouseId())) {
			List<InvoiceHeader> invoiceHeaders = doHeader.getInvoiceHeaders();
			for (InvoiceHeader invoiceHeader : invoiceHeaders) {
				if (invoiceHeader.getPositiveInvoiceId() == null && invoiceHeader.getInvoiceType().equals(InvoiceHeader.InvoiceType.ELECTRONIC.getValue())) {
					this.writeBack(invoiceHeader);
				}
			}
		}
	}
    
    @Override
    @Transactional
    @Observer("WRITE_BACK_INVOICE_EVENT")
	public void writeBackInvoiceByDoNo(String doNo) {
    	DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNoWithoutException(doNo);
    	if(doHeader == null){
    		return;
    	}
		if (doHeader.getInvoiceFlag() == 1 && SystemConfig.configIsOpen(ConfigKeys.INVOICE_USE_ELECTRONIC_INVOICE, ParamUtil.getCurrentWarehouseId())) {
			List<InvoiceHeader> invoiceHeaders = doHeader.getInvoiceHeaders();
			for (InvoiceHeader invoiceHeader : invoiceHeaders) {
				if (invoiceHeader.getPositiveInvoiceId() == null && invoiceHeader.getInvoiceType().equals(InvoiceHeader.InvoiceType.ELECTRONIC.getValue())) {
					this.writeBack(invoiceHeader);
				}
			}
		}
	}

	@Override
	@Transactional
	public void writeBack(InvoiceHeader invoiceHeader) {
		if (!(invoiceHeader.getPositiveInvoiceId() == null
				&& invoiceHeader.getInvoiceType().equals(
						InvoiceHeader.InvoiceType.ELECTRONIC.getValue()))) {
			return;
		}
		if(InvoiceHeader.InvoiceStatus.INIT == invoiceHeader
				.getInvoiceStatus()){
			invoiceHeader.setInvoiceStatus(InvoiceHeader.InvoiceStatus.CANCELLED);
		} else if(InvoiceHeader.InvoiceStatus.UPLOAD == invoiceHeader
				.getInvoiceStatus()){
			invoiceHeader.setInvoiceStatus(InvoiceHeader.InvoiceStatus.NEEDCANCEL);
		} else if (InvoiceHeader.InvoiceStatus.BILLED == invoiceHeader
				.getInvoiceStatus() || InvoiceHeader.InvoiceStatus.NEEDCANCEL.equals(invoiceHeader.getInvoiceStatus())) {
			EinvResponseData response = chInvoice(invoiceHeader.getId(),
					"订单取消", invoiceHeader.getChedSequenceNo());
			if (response == null) {
				Integer failedNum = invoiceHeader.getFailedNum() == null ? Integer
						.valueOf(0) : invoiceHeader.getFailedNum();
				invoiceHeader.setInvoiceStatus(InvoiceHeader.InvoiceStatus.NEEDCANCEL);		
				invoiceHeader.setReqErrorCode("请求接口异常，请联系管理员！");
				invoiceHeader.setFailedNum(failedNum + 1);
			} else {
				int code = response.getCode();
				String sequenceNo = response.getSerialNo();
				if (code == 0) {
					// 生成新的负票信息
					InvoiceHeader newInvoiceHeader = new InvoiceHeader();
					BeanUtils.copyProperties(invoiceHeader, newInvoiceHeader);
					newInvoiceHeader.setInvoiceAmount(-newInvoiceHeader.getInvoiceAmount());
					newInvoiceHeader.setId(null);
					newInvoiceHeader.setInvoiceStatus(InvoiceStatus.UPLOAD); // 红票状态
																				// 已上传
					newInvoiceHeader
							.seteInvoiceIssueType(EInvoiceIssueType.WHOLE_CH
									.getValue());
					newInvoiceHeader.setPositiveInvoiceId(invoiceHeader
							.getId());
					newInvoiceHeader.setReqSequenceNo(sequenceNo);
					newInvoiceHeader.setInvoiceUrl(null);
					newInvoiceHeader.setInvoiceCode(null);
					newInvoiceHeader.setCheckCode(null);
					newInvoiceHeader.setInvoiceNumber(null);
					newInvoiceHeader.setInvoiceDetails(null);
					invoiceService.saveOrUpdate(newInvoiceHeader);
					List<InvoiceDetail> invoiceDetails = invoiceService
							.findInvoiceDetailsByHeaderId(invoiceHeader.getId());
					for (InvoiceDetail invoiceDetail : invoiceDetails) {
						InvoiceDetail chedInvoiceDetail = new InvoiceDetail();
						BeanUtils.copyProperties(invoiceDetail, chedInvoiceDetail);
						chedInvoiceDetail.setId(null);
						chedInvoiceDetail.setInvoiceHeader(newInvoiceHeader);
						chedInvoiceDetail.setUpdatedAt(null);
						chedInvoiceDetail.setQty(chedInvoiceDetail.getQty().negate());
						chedInvoiceDetail.setAmount(chedInvoiceDetail.getAmount().negate());
						invoiceService.saveOrUpdate(chedInvoiceDetail);
						//
					}
					invoiceHeader.setInvoiceStatus(InvoiceStatus.WRITE_BACK);
					invoiceHeader.setFailedNum(0);
				} else {
					invoiceHeader.setInvoiceStatus(InvoiceHeader.InvoiceStatus.NEEDCANCEL);		
					// 记录错误code，更新失败次数
					invoiceHeader.setReqErrorCode(response.getMessage());
					invoiceHeader
							.setFailedNum(invoiceHeader.getFailedNum() + 1);
				}
				invoiceHeader.setChedSequenceNo(sequenceNo);
			}
		}
		invoiceService.saveOrUpdate(invoiceHeader);
	}
	@Override
    public void test(){
		List<DeliveryOrderHeader>  doheaders = new ArrayList<DeliveryOrderHeader>();
		doheaders.add(deliveryOrderService.findDoHeaderByDoNo("160427003885"));
		for(DeliveryOrderHeader doheader : doheaders){
			List<InvoiceHeader> invoiceHeaders = doheader.getInvoiceHeaders();
	        if (ListUtil.isNotEmpty(invoiceHeaders)) {
	            for (InvoiceHeader invoiceHeader : doheader.getInvoiceHeaders()) {
	                if (invoiceHeader.getInvoiceType().equals(InvoiceHeader.InvoiceType.ELECTRONIC.getValue())) {
	                    billing(invoiceHeader);
//	                    bind(invoiceHeader);
//	                	writeBack(invoiceHeader);
	                }
	            }
	        }
		}
		
	}

    @Override
	@Transactional
    public void billing(InvoiceHeader invoiceHeader) {
    	if (! invoiceHeader.getInvoiceType().equals(InvoiceHeader.InvoiceType.ELECTRONIC.getValue())) {
    		return;
    	}
        if (InvoiceHeader.InvoiceStatus.INIT == invoiceHeader.getInvoiceStatus()) {
        	EinvResponseData response = uploadInvoice(invoiceHeader,invoiceHeader.getReqSequenceNo());
        	if(response == null){
        		Integer failedNum = invoiceHeader.getFailedNum() == null ? Integer.valueOf(0):invoiceHeader.getFailedNum() ;
        		invoiceHeader.setReqErrorCode("请求接口异常，请联系管理员！");
        		invoiceHeader.setFailedNum(failedNum +1);
        	} else {
        		int code = response.getCode();
            	String sequenceNo = response.getSerialNo();
            	//成功修改发票状态，回写sequenceno。
            	if(code == 0){
            		invoiceHeader.setInvoiceStatus(InvoiceStatus.UPLOAD);
					invoiceHeader.setReqErrorCode("");
            		invoiceHeader.setFailedNum(0);
            	} else {
            		Integer failedNum = invoiceHeader.getFailedNum() == null ? Integer.valueOf(0):invoiceHeader.getFailedNum() ;
            		invoiceHeader.setReqErrorCode(response.getMessage());
            		invoiceHeader.setFailedNum(failedNum +1);
            	}
            	invoiceHeader.setReqSequenceNo(sequenceNo);
        	}
        	
        	invoiceService.saveOrUpdate(invoiceHeader);
        }
    }

    @Override
	@Transactional
    public void bind(InvoiceHeader invoiceHeader) {
		if (!invoiceHeader.getInvoiceType().equals(
				InvoiceHeader.InvoiceType.ELECTRONIC.getValue())) {
			return;
		}
        if (InvoiceHeader.InvoiceStatus.UPLOAD == invoiceHeader.getInvoiceStatus()) {
        	EinvResponseData response = downloadInvoice(invoiceHeader.getId(), EINV_QUERY_TYPE.SESSION_ID.getValue(),invoiceHeader.getReqSequenceNo());
			if (response == null) {
				Integer failedNum = invoiceHeader.getFailedNum() == null ? Integer
						.valueOf(0) : invoiceHeader.getFailedNum();
				invoiceHeader.setReqErrorCode("请求接口异常，请联系管理员！");
				invoiceHeader.setFailedNum(failedNum + 1);
			} else {
				int code = response.getCode();
				// 成功修改发票状态，回写sequenceno。
				if (code == 0) {
					invoiceHeader.setInvoiceStatus(InvoiceStatus.BILLED);
					EinvResultInvoice einvResultInvoice = response
							.getInvoices()[0];
					invoiceHeader.setInvoiceCode(einvResultInvoice.getCode()
							.substring(0, 12));
					invoiceHeader.setInvoiceNumber(einvResultInvoice.getCode()
							.substring(12, 20));
					invoiceHeader
							.setCheckCode(einvResultInvoice.getCheckCode());
					invoiceHeader.setInvoiceUrl(einvResultInvoice.getViewUrl());
					invoiceHeader.setReqErrorCode("");
					invoiceHeader.setFailedNum(0);
				} else {
					invoiceHeader.setReqErrorCode(response.getMessage());
					invoiceHeader
							.setFailedNum(invoiceHeader.getFailedNum() + 1);
				}
			}
        	invoiceService.saveOrUpdate(invoiceHeader);
        }
    }
    
    @Override
    public List<InvoiceHeader> loadInvoiceToBind(int batchNum, int failedNum){
        return invoiceDao.findToBind(batchNum,failedNum);
    }
    
    @Override
    public List<InvoiceHeader> loadInvoiceToBilling(int batchNum,int failedNum) {
        return invoiceDao.findToBilling(batchNum,failedNum);
    }
    @Override
    public List<InvoiceHeader> loadInvoiceToCancel(int batchNum,int failedNum) {
        return invoiceDao.findNeedCancel(batchNum,failedNum);
    }
    
    
    @Override
    public EinvResponseData uploadInvoice(InvoiceHeader ih, String orgSequenceNo){
		if(ih == null ){
			throw new DeliveryException("没有找到对应的发票信息");
    	}
		List<InvoiceDetail> details = ih.getInvoiceDetails();
		if(ListUtil.isNullOrEmpty(details)){
    		throw new DeliveryException("发票对应的开票明细信息不存在");
    	}
		Merchant mc = merchantService.getMerchant(ih.getMerchantId());
		if(mc == null){
    		throw new DeliveryException("没有找到发票对应的商家信息");
    	}
		MerchantInvoiceEx merchantInvoiceEx = merchantInvoiceExService.findByMerchantId(mc.getId());
		if(merchantInvoiceEx == null){
    		throw new DeliveryException("商家对应的发票配置信息不存在");
    	}

		EinvInvoiceUploadDto kpParams = new EinvInvoiceUploadDto();

		if (StringUtil.isEmpty(orgSequenceNo)) {
			String invoiceIdStr = ih.getId().toString();
			int length = invoiceIdStr.length();
			for (int i = 0; i < 15 - length; i++) {
				invoiceIdStr = "0" + invoiceIdStr;
			}
			invoiceIdStr = "U" + invoiceIdStr+"0001"; // 普通上传
			kpParams.setSerialNo(invoiceIdStr); // 操作流水号
		} else {
			String strIndex = orgSequenceNo.substring(orgSequenceNo.length()-4, orgSequenceNo.length());
    		int curIndex = Integer.valueOf(strIndex) + 1;
			String invoiceIdStr = String.valueOf(curIndex);
	    	int length = String.valueOf(curIndex).length();
	    	for(int j = 0; j< 4-length; j++){
	    		invoiceIdStr  =  "0" + invoiceIdStr;
	    	}
	    	orgSequenceNo = orgSequenceNo.substring(0, orgSequenceNo.length()-4) + invoiceIdStr;
			kpParams.setSerialNo(orgSequenceNo);
		}
                             
        kpParams.setPostTime(df.format(new Date()));                // 请求发送时间
        EinvDoDto order = new EinvDoDto();                                  // 订单信息
        order.setOrderNo(ih.getDeliveryOrderHeader().getOriginalSoCode());
		// 订单编号
		if (StringUtil.isNotEmpty(ih.getReceiverMobile())
				&& ih.getReceiverMobile().length() == 11) {
			order.setTel(ih.getReceiverMobile()); // 消费者电话号码
		}
        order.setAccount(ih.getReceiverName());                 // 消费者的用户名
        order.setAddress(ih.getReceiverAddr());                     // 货物配送地址或消费者的地址
        kpParams.setEinvDoDto(order);
        EinvInvoiceDto kpInvoice = new EinvInvoiceDto();                      // 发票信息
        kpInvoice.setTotalAmount(ih.getInvoiceAmount().toString());                              // 发票总金额
        kpInvoice.setCustomerCode(ih.getTaxNo());            // 付款方纳税人识别号
        kpInvoice.setCustomerName(ih.getInvoiceTitle());                           // 付款方名称，即发票抬头。
//        kpInvoice.setCustomerAddress(ih.getReceiverAddr());
		if (StringUtil.isNotEmpty(ih.getReceiverMobile()) && PATTERN.matcher(ih.getReceiverMobile()).matches()) {
//			kpInvoice.setCustomerTel(ih.getReceiverMobile());
		}
		kpInvoice.setDrawer(merchantInvoiceEx.getDrawer());                                 // 开票人
        kpInvoice.setPayee(merchantInvoiceEx.getPayee());                                 // 收款人
        kpInvoice.setReviewer(merchantInvoiceEx.getReviewer());                                 // 复核人
        kpInvoice.setTaxpayerCode(merchantInvoiceEx.getTaxPayerCode());                    // 收款方纳税人识别号
        
        kpInvoice.setRemark(ih.getNotes());                              // 发票备注
        List<EinvInvoiceDetailDto> itemList=new ArrayList<EinvInvoiceDetailDto>();// 发票项目明细列表
        for(InvoiceDetail detail : details){
            EinvInvoiceDetailDto item1 = new EinvInvoiceDetailDto();                  // 项目明细1
             item1.setAmount(detail.getAmount().toString());                                  // 金额
             item1.setName(detail.getSkuDescr());              // 商品名称
             item1.setPrice(detail.getPrice().toString());                                     // 商品单价
             item1.setQuantity(detail.getQty().toString());                                     // 数量
             item1.setTaxRate(detail.getTaxRate().divide(new BigDecimal(100)).toString());                                   // 税率
             item1.setUom(detail.getUomDescr());                                         // 单位
             item1.setSpec(detail.getSkuType());
             item1.setType("0");
			if(StringUtil.isNotEmpty(detail.getTaxCategoryCode())){
				item1.setCatalogCode(detail.getTaxCategoryCode());
			}
             itemList.add(item1);
        }

        kpInvoice.setItems(itemList);
        kpParams.setEinvInvoiceDto(kpInvoice);
        return einvInvoiceCallService.uploadInvoice(kpParams,merchantInvoiceEx);
	}
	
	@Override
    public EinvResponseData chInvoice(Long invoiceId, String reason, String orgSequenceNo){
		EinvInvoiceChDto chParams = new EinvInvoiceChDto();
		InvoiceHeader ih = invoiceService.getInvoiceById(invoiceId);
		if(ih == null ){
			throw new DeliveryException("没有找到对应的发票信息");
    	}
		List<InvoiceDetail> details = invoiceService
				.findInvoiceDetailsByHeaderId(ih.getId());
		if(ListUtil.isNullOrEmpty(details)){
    		throw new DeliveryException("发票对应的开票明细信息不存在");
    	}
		Merchant mc = merchantService.getMerchant(ih.getMerchantId());
		if(mc == null){
    		throw new DeliveryException("没有找到发票对应的商家信息");
    	}
		MerchantInvoiceEx merchantInvoiceEx = merchantInvoiceExService.findByMerchantId(mc.getId());
		if(merchantInvoiceEx == null){
    		throw new DeliveryException("商家对应的发票配置信息不存在");
    	}
		if (StringUtil.isEmpty(orgSequenceNo)) {
			String invoiceIdStr = ih.getId().toString(); 
			int length = invoiceIdStr.length();
			for (int i = 0; i < 15 - length; i++) {
				invoiceIdStr = "0" + invoiceIdStr;
			}
			invoiceIdStr = "C" + invoiceIdStr+"0001"; // 普通上传
			chParams.setSerialNo(invoiceIdStr); // 操作流水号
		} else {
			String strIndex = orgSequenceNo.substring(orgSequenceNo.length()-4, orgSequenceNo.length());
    		int curIndex = Integer.valueOf(strIndex) + 1;
			String invoiceIdStr = String.valueOf(curIndex);
	    	int length = String.valueOf(curIndex).length();
	    	for(int j = 0; j< 4-length; j++){
	    		invoiceIdStr  =  "0" + invoiceIdStr;
	    	}
	    	orgSequenceNo = orgSequenceNo.substring(0, orgSequenceNo.length()-4) + invoiceIdStr;
	    	chParams.setSerialNo(orgSequenceNo);
		}
        chParams.setPostTime(df.format(new Date()));                // 请求发送时间
        chParams.setOriginalCode(ih.getInvoiceCode()+ih.getInvoiceNumber());                      // 原发票号码
        chParams.setReason(reason);                              // 冲红原因
        
        
		return einvInvoiceCallService.chInvoice(chParams, merchantInvoiceEx);
	}
    
	
    @Override
    public EinvResponseData downloadInvoice(Long invoiceId, String type, String cxParam){
    	InvoiceHeader ih = invoiceService.getInvoiceById(invoiceId);
		if(ih == null ){
			throw new DeliveryException("没有找到对应的发票信息");
    	}
		List<InvoiceDetail> details = invoiceService
				.findInvoiceDetailsByHeaderId(ih.getId());
		if(ListUtil.isNullOrEmpty(details)){
    		throw new DeliveryException("发票对应的开票明细信息不存在");
    	}
		Merchant mc = merchantService.getMerchant(ih.getMerchantId());
		if(mc == null){
    		throw new DeliveryException("没有找到发票对应的商家信息");
    	}
		MerchantInvoiceEx merchantInvoiceEx = merchantInvoiceExService.findByMerchantId(mc.getId());
		if(merchantInvoiceEx == null){
    		throw new DeliveryException("商家对应的发票配置信息不存在");
    	}
		EinvInvoiceDownloadDto cxParams = new EinvInvoiceDownloadDto();
        cxParams.setPostTime(df.format(new Date()));                // 请求发送时间
        cxParams.setSerialNo("1000");
        List<EinvCriteriaDto> criteria = new ArrayList<EinvCriteriaDto>();               // 查询条件
        criteria.add(new EinvCriteriaDto(type,cxParam));                     // 发票编号   
        cxParams.setCriteria(criteria);
		return einvInvoiceCallService.downloadInvoice(cxParams,merchantInvoiceEx);
	}
}