package com.daxia.wms.delivery.load.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.load.dto.CrossDockDTO;
import com.daxia.wms.delivery.load.dto.StockCrossDockDTO;
import com.daxia.wms.delivery.load.entity.TOCrossDockDetail;
import com.daxia.wms.delivery.load.entity.TOCrossDockHeader;
import com.daxia.wms.stock.stock.entity.StockSerial;

public interface CrossDockDetailService {

	/**
	 * 根据LPN号获取越库发货单单头
	 * 
	 * @param lpnNo
	 *            	LPN号
	 * @param isShipped
	 * 				是否发货 
	 * @return 
	 * 				越库发货单单头
	 */
	public TOCrossDockHeader findToCDHeaderByLpnNo(String lpnNo,boolean isShipped);

	/**
	 * 通过LpnNo获取发货需要扫描的LPN号`
	 * 
	 * @param lpnNo
	 *            lpn编号
	 * @param isShipped
	 * 				是否发货 
	 * @return 需要扫描的LPN号
	 */
	public List<String> getNeedScannedLpnByLpnNo(String lpnNo,boolean isShipped);

	/**
	 * 通过越库发货单单头获取需要扫描的LPN号
	 * 
	 * @param cdHeaderId
	 *            越库发货单单头
	 * @param isShipped
	 * 				是否发货 
	 * @return 需要扫描的LPN号
	 */
	public List<String> getNeedScannedLpnByCDHeaderId(Long cdHeaderId,Integer crossStockType,boolean isShipped);

	/**
	 * 通过lpnNo组装stockCrossDockDTO
	 * 
	 * @param lpnNo
	 *            lpn编号
	 * @param isShipped
	 * 				是否发货 
	 * @return 组装后的stockCrossDockDTO
	 */
	public CrossDockDTO getStockCrossDockDTOByLpnNo(String lpnNo,boolean isShipped);

	/**
	 * 通过cdHeaderId组装stockCrossDockDTO
	 * 
	 * @param cdHeaderId
	 *            越库发货单单头ID
	 * @return 组装后的stockCrossDockDTO
	 */
	public CrossDockDTO getStockCrossDockDTOByCDHeaderId(Long cdHeaderId);
	
	
	/**
	 * 通过越库单单头及尚需发货LPN构建StockCrossDockDTO
	 * @param  toCrossDockHeader
	 * 		越库单单头
	 * @return needScannedLpns
	 * 		及尚需发货LPN
	*/
	public CrossDockDTO getStockCrossDockDTOByCDANDLPN(DeliveryOrderHeader toCrossDockHeader,List<String> needScannedLpns,
			List<String> notNeedScannedLpns);
	
	/**
	 * 越库发货
	 * 		越库单库存对象
	 * @return needScannedLpns
	 * 		及尚需发货LPN
	*/
	public void delivery(Long toCrossDockId,List<StockCrossDockDTO> stockCrossDocks,boolean isPacking);
	
	/**
	 * 通过LPN号获得发货明细
	 * 		越库单库存对象
	 * @param isShipped
	 * 				是否发货 
	 * @return needScannedLpns
	 * 		及尚需发货LPN
	*/
    public List<StockCrossDockDTO> getCrossDockDetailDTOByLPN(Long cdHeaderId, String lpnNo, boolean isShipped);

    /**
	 * 通过cdHeaderId获取越库单据头
	 * 
	 * @param cdHeaderId
	 *            			越库发货单单头ID
	 * @return 
	 * 						越库单据头
	 */
	public TOCrossDockHeader getToCrossDockHeaderByCDHeaderId(Long cdHeaderId);
	
	public void expDataToWMSANDOSM(Long toCrossDockId);
	
	/**
	 * 检查越库发货单状态，如果不为初始化则不能发货
	 * 
	 * @param cdStatus
	 *            越库单状态
	 * 
	 */
	public void checkCDStatus(String cdStatus);
	
	/**
	 * 完成发货
	 * 
	 * @param toCrossDockHeader
	 *            越库订单头
	 * @param stockCrossDocks
	 *            越库分录明细
	 * @param stockSerialMap
	 *            越库发货关联越库库存序列号
	 * 
	 */
	public void completeDelivery(DeliveryOrderHeader toCrossDockHeader,List<StockCrossDockDTO> stockCrossDocks,
			Map<Long, List<StockSerial>> stockSerialMap);
	
	/**
	 * 更新越库单单头xinx
	 * 
	 * @param toCrossDockHeader
	 *            越库库存记录状态
	 * @param deliveryAmount
	 *            越库发货数量
	 */
	public void updateCrossDockHeader(DeliveryOrderHeader toCrossDockHeader,BigDecimal deliveryAmount);
	
	/**
	 * 通过越库发货单创建序列号交易日志
	 * 
	 * @param serial
	 *            序列号库存记录
	 * @param toCrossDockHeader
	 *            越库发货单单头
	 * @param transactionId
	 *            越库发货交易日志ID
	 */
	public void createSerialLogByCrossDock(StockSerial serial,TOCrossDockHeader toCrossDockHeader, Long transactionId);
	
	/**
	 * 通过越库发货单创建发货交易日志
	 * 
	 * @param toCrossDockHeader
	 *            越库发货单单头
	 * @param toCrossDockDetail
	 *            越库发货单明细
	 * @param stockCrossDockDTO
	 *            越库发货明细，用于获取LPN等相关信息
	 */
	public Long createTrsShipLogByCrossDock(DeliveryOrderHeader toCrossDockHeader, DeliveryOrderDetail
			toCrossDockDetail,	StockCrossDockDTO stockCrossDockDTO) ;


	public List<StockCrossDockDTO> getCrossDockDetailDTOByCdHeader(Long cdHeaderId,Integer crossStockType);

	public List<StockCrossDockDTO> checkBeforeCrossdockDelivery(Long cdHeaderId);

	public CrossDockDTO getStockCrossDockDTOByCDANDLPN(TOCrossDockHeader toCrossDockHeader, List<String>
			needScannedLpns,List<String> notNeedScannedLpns);
}
