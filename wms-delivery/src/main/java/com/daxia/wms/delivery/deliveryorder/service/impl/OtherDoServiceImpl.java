package com.daxia.wms.delivery.deliveryorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.daxia.dubhe.api.internal.util.StrUtils;

import com.daxia.framework.common.service.*;
import com.daxia.framework.common.service.Dictionary;
import com.daxia.framework.common.util.*;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.OtherDoDetailDAO;
import com.daxia.wms.delivery.deliveryorder.dao.OtherDoHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.entity.OtherDoDetail;
import com.daxia.wms.delivery.deliveryorder.entity.OtherDoHeader;
import com.daxia.wms.delivery.deliveryorder.filter.OtherDoFilter;
import com.daxia.wms.delivery.deliveryorder.service.OtherDoService;
import com.daxia.wms.delivery.print.dto.PrintDoDTO;
import com.daxia.wms.delivery.print.dto.PrintDoDetailDTO;
import com.daxia.wms.exp.stock.srv.StockExpSrv;
import com.daxia.wms.master.dao.WarehouseDAO;
import com.daxia.wms.master.entity.PackageInfoDetail;
import com.daxia.wms.print.PrintConstants;
import com.daxia.wms.stock.stock.dao.StockDao;
import com.daxia.wms.stock.stock.dao.StockQueryDao;
import com.daxia.wms.stock.stock.dto.OrderStockDTO;
import com.daxia.wms.stock.stock.dto.StockDTO;
import com.daxia.wms.stock.stock.entity.*;
import com.daxia.wms.stock.stock.filter.TrsTransactionLogFilter;
import com.daxia.wms.stock.stock.service.*;
import org.apache.commons.collections.map.HashedMap;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

@Name("com.daxia.wms.delivery.otherDoService")
@lombok.extern.slf4j.Slf4j
public class OtherDoServiceImpl implements OtherDoService {
    @In
    private OtherDoHeaderDAO otherDoHeaderDAO;

    @In
    private OtherDoDetailDAO otherDoDetailDAO;

    @In
    private StockQueryService stockQueryService;

    @In("allocateOperator")
    private IOperator allocateOperator;

    @In("otherShipOperator")
    private IOperator otherShipOperator;

    @In
    private StockService stockService;
    @In
    private StockDao stockDao;

    @In
    protected StockQueryDao stockQueryDao;

    @In
    private StockBatchAttService stockBatchAttService;

    @In
    private TransactionService transactionService;

    @In
    private StockExpSrv stockExpSrv;
    @In
    private WarehouseDAO warehouseDAO;

    @Override
    public DataPage<OtherDoHeader> queryDoHeaderPageInfo(OtherDoFilter otherDoFilter, int startIndex, int pageSize) {
        return otherDoHeaderDAO.findRangeByFilter(otherDoFilter, startIndex, pageSize);
    }

    @Override
    @Transactional
    public void saveOrUpdate(OtherDoHeader otherDoHeader) {
        Boolean isExists = otherDoHeaderDAO.existsByDoCode(otherDoHeader.getDoNo(), ParamUtil.getCurrentWarehouseId());
        if (isExists) {
            throw new DeliveryException(DeliveryException.DO_NO_ALREADY_IN);
        }
        if (StringUtil.isBlank(otherDoHeader.getDoStatus())) {
            otherDoHeader.setDoStatus(Constants.DoStatus.INITIAL.getValue());
            otherDoHeader.setExptQty(BigDecimal.ZERO);
            otherDoHeader.setShipQty(BigDecimal.ZERO);
        }
        otherDoHeaderDAO.saveOrUpdate(otherDoHeader);
    }

    @Override
    public OtherDoHeader getHeaderById(Long doHeaderId) {
        return otherDoHeaderDAO.get(doHeaderId);
    }

    @Override
    public OtherDoDetail getDetailById(Long doDetailId) {
        return otherDoDetailDAO.get(doDetailId);
    }


    @Override
    @Transactional
    public void deletedDetail(Long doHeaderId, Long doDetailId) {
        OtherDoDetail otherDoDetail = otherDoDetailDAO.get(doDetailId);
        StockBatchLocLpn stockBatchLocLpn = stockQueryService.findStockBatchLocLpnById(otherDoDetail.getStkLpnId());
        OrderStockDTO dto = new OrderStockDTO();
        dto.setSkuId(stockBatchLocLpn.getSkuId());
        dto.setFmLocId(stockBatchLocLpn.getLocId());
        dto.setLotId(stockBatchLocLpn.getLotId());
        dto.setLpnNo(stockBatchLocLpn.getLpnNo());
        dto.setPlanQty(BigDecimal.ZERO);
        dto.setPlanQtyUnit(BigDecimal.ZERO);
        dto.setFmStockId(otherDoDetail.getStkAllocatedId());
        dto.setAllocatingId(otherDoDetail.getStkAllocatingId());
        allocateOperator.setStockDto(dto);
        stockService.undo(allocateOperator);

        otherDoDetail.setIsDeleted(Constants.YesNo.YES.getValue());
        otherDoDetailDAO.update(otherDoDetail);
        otherDoDetailDAO.getSession().flush();
        BigDecimal exptQty = BigDecimal.ZERO;
        OtherDoHeader header = otherDoHeaderDAO.get(doHeaderId);
        for (OtherDoDetail doDetail : header.getOtherDoDetails()) {
            exptQty = exptQty.add(doDetail.getExptQty());
        }
        header.setExptQty(exptQty);
        otherDoHeaderDAO.saveOrUpdate(header);
    }

    @Override
    @Transactional
    public void createDetail(OtherDoDetail otherDoDetail, Long stockId, BigDecimal planQty) {
        if(!Constants.DoStatus.INITIAL.getValue().equals(otherDoDetail.getOtherDoHeader().getDoStatus())){
            throw new DeliveryException(DeliveryException.DO_STATUS_ERROR_CAN_NOT_OPERATE);
        }
        StockBatchLocLpn stockBatchLocLpn = stockQueryService.findStockBatchLocLpnById(stockId);
        if (planQty.compareTo(stockBatchLocLpn.getQtyAvailable()) > 0) {
            throw new DeliveryException(DeliveryException.PLAN_QTY_ERROR);
        }
        Map<String, Object> params = new HashedMap();
        params.put("doHeaderId", otherDoDetail.getDoHeaderId());
        params.put("stkLpnId", stockId);
        if (stockBatchLocLpn.getPackQty() != null && stockBatchLocLpn.getPackQty().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal q = planQty.divideAndRemainder(stockBatchLocLpn.getPackQty())[1];
            if (q.compareTo(BigDecimal.ZERO) > 0) {
                throw new DeliveryException(DeliveryException.PLAN_QTY_ERROR);
            }
        }
        List<OtherDoDetail> detailList = otherDoDetailDAO.find(params);
        if (!CollectionUtils.isEmpty(detailList)) {
            OtherDoDetail detail = detailList.get(0);
            detail.setExptQty(detail.getExptQty().add(planQty));
            otherDoDetail = detail;
        } else {
            otherDoDetail.setExptQty(planQty);
            otherDoDetail.setSkuId(stockBatchLocLpn.getSkuId());
            otherDoDetail.setSku(stockBatchLocLpn.getSku());
            otherDoDetail.setLocId(stockBatchLocLpn.getLocId());
            otherDoDetail.setLocation(stockBatchLocLpn.getLocation());
            otherDoDetail.setLotId(stockBatchLocLpn.getLotId());
            otherDoDetail.setStockBatchAtt(stockBatchLocLpn.getStockBatchAtt());
            otherDoDetail.setLpnNo(stockBatchLocLpn.getLpnNo());
            otherDoDetail.setStkLpnId(stockBatchLocLpn.getId());
            otherDoDetail.setShipQty(BigDecimal.ZERO);
            otherDoDetail.setLineStatus(Constants.AsnStatus.INITIAL.getValue());
        }
        otherDoDetailDAO.saveOrUpdate(otherDoDetail);

        BigDecimal exptQty = BigDecimal.ZERO;
        OtherDoHeader header = otherDoHeaderDAO.get(otherDoDetail.getDoHeaderId());
        for (OtherDoDetail doDetail : header.getOtherDoDetails()) {
            exptQty = exptQty.add(doDetail.getExptQty());
        }
        header.setExptQty(exptQty);
        otherDoHeaderDAO.saveOrUpdate(header);
        OrderStockDTO dto = new OrderStockDTO();
        dto.setDocId(otherDoDetail.getDoHeaderId());
        dto.setDocLineId(otherDoDetail.getId());
        dto.setStockLpnId(stockBatchLocLpn.getId());
        dto.setFmStockId(otherDoDetail.getStkAllocatedId());
        dto.setAllocatingId(otherDoDetail.getStkAllocatingId());
        dto.setSkuId(stockBatchLocLpn.getSkuId());
        dto.setFmLocId(stockBatchLocLpn.getLocId());
        dto.setLotId(stockBatchLocLpn.getLotId());
        dto.setLpnNo(stockBatchLocLpn.getLpnNo());
        dto.setPlanQty(planQty.negate());
        if (stockBatchLocLpn.getPackQty() != null && stockBatchLocLpn.getPackQty().compareTo(BigDecimal.ONE) > 0) {
            dto.setPlanQtyUnit(planQty.divide(stockBatchLocLpn.getPackQty()).negate());
        } else {
            dto.setPlanQtyUnit(planQty.negate());
        }
        allocateOperator.setStockDto(dto);
        Map<String, Long> map = stockService.operateStock(allocateOperator);
        otherDoDetail.setStkAllocatingId(map.get(Constants.StockType.STOCK_ALLOCING.getValue()));
        otherDoDetail.setStkAllocatedId(map.get(Constants.StockType.STOCK_ALLOC.getValue()));
        otherDoDetailDAO.saveOrUpdate(otherDoDetail);
    }

    @Override
    @Transactional
    public void confirmShip(Long doHeaderId) {
        OtherDoHeader header = otherDoHeaderDAO.get(doHeaderId);
        List<Long> allocatingIds = new ArrayList<Long>();
        List<Long> allocatedIds = new ArrayList<Long>();
        for (OtherDoDetail otherDoDetail : header.getOtherDoDetails()) {
            allocatingIds.add(otherDoDetail.getStkAllocatingId());
            allocatedIds.add(otherDoDetail.getStkAllocatedId());
            StockPicked stockPicked = new StockPicked();
            stockPicked.setLocId(otherDoDetail.getLocId());
            stockPicked.setLotId(otherDoDetail.getLotId());
            stockPicked.setLpnNo(otherDoDetail.getLpnNo());
            stockPicked.setQtyPicked(otherDoDetail.getExptQty());
            PackageInfoDetail packageInfoDetail = otherDoDetail.getStockBatchAtt().getPackageInfoDetail();
            stockPicked.setQtyPickedUnit(otherDoDetail.getExptQty().divide(packageInfoDetail.getQty()));
            stockPicked.setSkuId(otherDoDetail.getSkuId());
            stockPicked.setStockLpnId(otherDoDetail.getStkLpnId());
            stockDao.save(stockPicked);
            otherDoDetail.setShipQty(otherDoDetail.getExptQty());
            otherDoDetail.setLineStatus(Constants.AsnStatus.RECEIVED.getValue());
            otherDoDetail.setStkPickedId(stockPicked.getId());
            otherDoDetailDAO.saveOrUpdate(otherDoDetail);
        }
        StockDTO stockDTO = new StockDTO();
        stockDTO.setAllocatingIds(allocatingIds);
        stockDTO.setAllocatedIds(allocatedIds);
        otherShipOperator.setStockDto(stockDTO);
        stockService.operateStock(otherShipOperator);
        header.setShipQty(header.getExptQty());
        header.setShipTime(DateUtil.getNowTime());
        header.setDoStatus(Constants.AsnStatus.RECEIVED.getValue());
        otherDoHeaderDAO.saveOrUpdate(header);
    }

    @Override
    @Transactional
    public void verify(Long doHeaderId) {
        OtherDoHeader header = otherDoHeaderDAO.get(doHeaderId);
        for (OtherDoDetail otherDoDetail : header.getOtherDoDetails()) {
            StockPicked stockPicked = stockQueryDao.getStockPickedById(otherDoDetail.getStkPickedId());
            stockPicked.setIsDeleted(Constants.YesNo.YES.getValue());
            stockDao.update(stockPicked);
            otherDoDetail.setShipQty(otherDoDetail.getShipQty());
            otherDoDetail.setLineStatus(Constants.AsnStatus.ORDER_CLOSED.getValue());
            otherDoDetail.setStkPickedId(stockPicked.getId());
            otherDoDetailDAO.saveOrUpdate(otherDoDetail);
            createTrs(otherDoDetail, Constants.TrsType.OTHER_SO.getValue());
        }
        header.setCloseTime(DateUtil.getNowTime());
        header.setDoStatus(Constants.AsnStatus.ORDER_CLOSED.getValue());
        otherDoHeaderDAO.saveOrUpdate(header);
    }

    @Override
    @Transactional
    public void cancel(Long doHeaderId) {
        OtherDoHeader header = otherDoHeaderDAO.get(doHeaderId);
        for (OtherDoDetail otherDoDetail : header.getOtherDoDetails()) {
            deletedDetail(otherDoDetail.getDoHeaderId(), otherDoDetail.getId());
        }
        header = otherDoHeaderDAO.get(doHeaderId);
        header.setDoStatus(Constants.AsnStatus.ORDER_CANCEL.getValue());
        otherDoHeaderDAO.saveOrUpdate(header);
    }

    @Override
    public String print(Long doHeaderId) {
        PrintDoDTO dto = new PrintDoDTO();
        OtherDoHeader header = otherDoHeaderDAO.get(doHeaderId);
        dto.setDoNo(header.getDoNo());
        dto.setWhNickName(warehouseDAO.get(header.getWarehouseId()).getNickName());
        JSONObject object = new JSONObject();
        dto.setObject(object);
        object.put("shipTime", header.getCloseTime() == null ? "" : DateUtil.dateToString(header.getCloseTime(),DateUtil.ISO_EXPANDED_DATE_FORMAT));
        object.put("printTime", DateUtil.dateToString(DateUtil.getNowTime(),DateUtil.DATETIME_PATTERN));
        dto.setDoType(Dictionary.getDictionaryValue("OTHER_DO_TYPE", header.getDoType()));
        dto.setPrinter(ParamUtil.getCurrentLoginName());
        List<PrintDoDetailDTO> detailDtos = new ArrayList<PrintDoDetailDTO>();
        dto.setDetailDtos(detailDtos);
        TrsTransactionLogFilter filter = new TrsTransactionLogFilter();
        filter.setDocId(doHeaderId);
        filter.setDocType(Constants.DocType.OTHER_SO.getValue());
        List<TrsTransactionLog>  logs = transactionService.findTrsTransactionLogByFilter(filter);
        for (TrsTransactionLog log : logs) {
            PrintDoDetailDTO detailDTO = new PrintDoDetailDTO();
            detailDTO.setLocCode(log.getToLocation().getLocCode());
            detailDTO.setProductName(log.getSku().getProductCname());
            detailDTO.setProductCode(log.getSku().getProductCode());
            detailDTO.setUom(log.getSku().getUdf6());
            detailDTO.setQty(log.getToQty());
            detailDtos.add(detailDTO);
        }
        Map<String, Object> originalUnitProps = new HashMap<String, Object>();
        originalUnitProps.put("dtos", JSON.toJSONString(Arrays.asList(dto)));
        return PrintTemplateUtil.process(PrintConstants.PrintTemplate.OTHERDO.name(), originalUnitProps);
    }

    @Transactional
    public void createTrs(OtherDoDetail otherDoDetail, String transactionType) {
        TrsTransactionLog trsTraLog = new TrsTransactionLog();
        StockBatchAtt stockBatchAtt = stockBatchAttService.findStockBatchAttBylotId(otherDoDetail.getLotId());
        trsTraLog.setIsDamage(StrUtils.isEmptyStr(stockBatchAtt.getLotatt12()) ? 0L : 1L);
        trsTraLog.setTaskId(otherDoDetail.getId());
        trsTraLog.setDocId(otherDoDetail.getOtherDoHeader().getId());
        trsTraLog.setDocNo(otherDoDetail.getOtherDoHeader().getDoNo());
        trsTraLog.setDocLineId(otherDoDetail.getId());
        trsTraLog.setDocType(Constants.DocType.OTHER_SO.getValue());
        trsTraLog.setTransactionType(transactionType);
        String cargoOwnerId = stockBatchAtt.getLotatt06();
        trsTraLog.setFmCargoOwnerId(StringUtil.toLong(cargoOwnerId));
        trsTraLog.setToCargoOwnerId(StringUtil.toLong(cargoOwnerId));
        String supplierIdStr = stockBatchAtt.getLotatt04();
        trsTraLog.setFmSupplierId(StringUtil.toLong(supplierIdStr));
        trsTraLog.setToSupplierId(StringUtil.toLong(supplierIdStr));
        Long skuId = otherDoDetail.getSkuId();
        trsTraLog.setFmSkuId(skuId);
        trsTraLog.setToSkuId(skuId);
        trsTraLog.setFmLocId(otherDoDetail.getLocId());
        trsTraLog.setToLocId(otherDoDetail.getLocId());
        trsTraLog.setFmLotId(stockBatchAtt.getId());
        trsTraLog.setToLotId(stockBatchAtt.getId());
        trsTraLog.setFmLpnNo("*");
        trsTraLog.setToLpnNo("*");
        BigDecimal changeStockQty = otherDoDetail.getExptQty();
        trsTraLog.setFmQty(changeStockQty);
        trsTraLog.setFmQtyUnit(changeStockQty);
        trsTraLog.setToQty(changeStockQty);
        trsTraLog.setToQtyUnit(changeStockQty);
        PackageInfoDetail packageInfoDetail = stockBatchAtt.getPackageInfoDetail();
        if (packageInfoDetail != null) {
            trsTraLog.setPackId(packageInfoDetail.getPackageId());
            trsTraLog.setFmPackDetailId(packageInfoDetail.getId());
            trsTraLog.setToPackDetailId(packageInfoDetail.getId());
            BigDecimal packQty = packageInfoDetail.getQty();
            trsTraLog.setFmUomQty(packQty);
            trsTraLog.setToUomQty(packQty);
        }
        String updateBy = ParamUtil.getCurrentLoginName();
        trsTraLog.setOperationId(updateBy);
        trsTraLog.setUpdatedBy(updateBy);
        trsTraLog.setCreatedBy(updateBy);
        trsTraLog.setOperSource(ParamUtil.getCurrentDevice());
        trsTraLog.setMerchantId(StringUtil.toLong(stockBatchAtt.getLotatt06()));
        transactionService.saveTrsTransactionLog(trsTraLog);
        stockExpSrv.createTrsLogOmsMsg(trsTraLog.getId());
    }
}
