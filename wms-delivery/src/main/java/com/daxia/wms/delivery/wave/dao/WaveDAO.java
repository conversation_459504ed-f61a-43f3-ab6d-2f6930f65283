package com.daxia.wms.delivery.wave.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.*;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.WaveAllocateSortingBin;
import com.daxia.wms.Constants.WaveStatus;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.filter.WaveHeaderFilter;
import com.daxia.wms.master.dto.AutoCompleteDTO;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import org.hibernate.Hibernate;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.criterion.CriteriaSpecification;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * 波次业务DAO
 */
@Name("com.daxia.wms.delivery.waveDAO")
@lombok.extern.slf4j.Slf4j
public class WaveDAO extends HibernateBaseDAO<WaveHeader, Long> {

    private static final long serialVersionUID = 2767292203887398558L;

    /**
     * 根据主键修改波次状态
     * 
     * @param waveId     主键
     * @param waveStatus 波次状态值
     */
    public void updateWaveStatusById(Long waveId, String waveStatus) {
        String hql = "update WaveHeader o set o.waveStatus=:waveStatus where o.id=:id and o.warehouseId = :warehouseId";
        Query query = this.createUpdateQuery(hql);
        query.setString("waveStatus", waveStatus);
        query.setLong("id", waveId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 根据波次号查询波次信息
     * 
     * @param waveNo 波次号
     * @return 波次List
     */
    @SuppressWarnings("unchecked")
    public List<WaveHeader> findWaveByNo(String waveNo) {
        StringBuilder hql = new StringBuilder(
                "from WaveHeader o where o.waveNo=:waveNo and o.warehouseId = :warehouseId");
        Query query = createQuery(hql.toString());
        query.setParameter("waveNo", waveNo);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 根据发货单Id获取其波次
     * 
     * @param doId 发货单ID
     * @return 波次头实体
     */
    public WaveHeader getWaveHeaderByDoId(Long doId) {
        String hql = " select o.waveHeader from DeliveryOrderHeader o where o.id =:id and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setLong("id", doId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (WaveHeader) query.uniqueResult();
    }

    /**
     * 根据发货单Id获取其波次
     * 
     * @param waveId 发货单ID
     * @return 波次头实体
     */
    public Boolean isWaveAllBindedInvoice(Long waveId) {
        String hql = " select 1 from doc_do_header o where o.wave_id = :waveId and o.warehouse_Id = :warehouseId and o.invoice_Flag = 1 "
                + "and exists ( select 1 from doc_invoice_header ih where ih.do_header_id = o.id and (ih.invoice_number is null or length(ih.invoice_number) =0)  and o.warehouse_Id = :warehouseId)";
        Query query = this.createSQLQuery(hql);
        query.setLong("waveId", waveId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.uniqueResult() == null ? Boolean.TRUE : Boolean.FALSE;
    }

    /**
     * 根据波次Id List批量更新波次的打印标识
     * 
     * @param ids  波次Id List
     * @param type 波次的打印标识
     */
    public void updateWavePrintFlag(List<Long> ids, Integer type) {
        if (ids.isEmpty()) {
            return;
        }

        String hql = "UPDATE doc_wave_header SET print_Flag = (print_Flag | :printFlag) WHERE id in(:ids) AND warehouse_id = :warehouseId";
        this.createUpdateSqlQuery(hql).setParameterList("ids", ids).setLong("printFlag", type)
                .setLong("warehouseId", ParamUtil.getCurrentWarehouseId())
                .executeUpdate();
    }

    /**
     * 根据波次Id List批量更新波次的打印购物清单标记
     * 
     * @param ids   波次Id List
     * @param value 打印购物清单标记值（0：已消费，1：需要打印）
     */
    public void updateDoNeedPrintFlag(List<Long> ids, Integer value) {
        if (ids.isEmpty()) {
            return;
        }

        String hql = "UPDATE doc_wave_header SET do_need_print = :doNeedPrint WHERE id in(:ids) AND warehouse_id = :warehouseId";
        this.createUpdateSqlQuery(hql).setParameterList("ids", ids).setInteger("doNeedPrint", value)
                .setLong("warehouseId", ParamUtil.getCurrentWarehouseId())
                .executeUpdate();
    }

    /**
     * 根据波次号查询波次 TODO 和 findWaveByNo 有功能重复
     * 
     * @param waveNum 波次号
     * @return 波次头实体
     */
    public WaveHeader getWaveHeaderByWaveNum(String waveNum) {
        String hql = "from WaveHeader o where o.waveNo = :waveNum and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setString("waveNum", waveNum);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (WaveHeader) query.uniqueResult();
    }

    /**
     * 查询一个可分拣的波次（波次状态为拣货完成，未同步到das即dasFlag = 0）
     * 
     * @return 波次头实体
     */
    public WaveHeader findOne2Sorting() {
        String status = Constants.WaveStatus.ALLPICKED.getValue();
        String hql = "from WaveHeader o where o.waveStatus = :status and o.dasFlag = :dasFlag and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setString("status", status);
        query.setInteger("dasFlag", Integer.valueOf(0));
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (WaveHeader) query.uniqueResult();
    }

    /**
     * 根据拣货单号查询波次
     * 
     * @param pktNo 拣货单号
     * @return 波次头实体
     */
    public WaveHeader getWaveHeaderByPktNo(String pktNo) {
        String hql = "from WaveHeader o where o.id = (select p.waveHeadId from PickHeader p where p.pktNo =:pktHeaderNo and p.warehouseId = :warehouseId) and o.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setString("pktHeaderNo", pktNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (WaveHeader) query.uniqueResult();
    }

    /**
     * 根据波次Id查询波次 TODO 可去除 直接用get
     * 
     * @param waveId
     * @return
     */
    public WaveHeader findWaveById(Long waveId) {
        String hql = "from WaveHeader o where o.id =:waveId and o.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setLong("waveId", waveId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (WaveHeader) query.uniqueResult();
    }

    /**
     * 分页查询波次单
     * 
     * @param waveHeaderFilter
     * @param startIndex
     * @param pageSize
     * @return
     */
    @SuppressWarnings("unchecked")
    public DataPage<WaveHeader> findWaveHeaderByFilter(WaveHeaderFilter waveHeaderFilter, int startIndex,
            int pageSize) {
        Long whId = ParamUtil.getCurrentWarehouseId();
        StringBuilder hql = new StringBuilder("from WaveHeader o where o.warehouseId  = ").append(whId);
        StringBuilder countHql = new StringBuilder("select count(o.id) from WaveHeader o where o.warehouseId  = ")
                .append(whId);

        return (DataPage<WaveHeader>) this.executeQueryByFilter(hql.toString(), countHql.toString(), startIndex,
                pageSize, waveHeaderFilter);
    }

    /**
     * 随机取出波次下某订单
     * 
     * @param waveId
     * @return
     */
    public DeliveryOrderHeader findOneDoInWave(Long waveId) {
        String hql = "from DeliveryOrderHeader o where o.waveId =:waveId and o.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setLong("waveId", waveId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (DeliveryOrderHeader) query.uniqueResult();
    }

    public boolean needReAllocateSortinhBin(Long waveId, boolean isPick) {
        StringBuilder hql = new StringBuilder(
                "select 1 from WaveHeader o where o.id = :waveId and o.warehouseId = :warehouseId ");
        if (isPick) {
            hql.append("and o.allocateSortingBin = :allocateSortingBin");
        } else {
            hql.append("and o.allocateSortingBin != :allocateSortingBin");
        }
        Query query = createQuery(hql.toString());
        query.setLong("waveId", waveId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        if (isPick) {
            query.setInteger("allocateSortingBin", WaveAllocateSortingBin.WAVE.getValue());
        } else {
            query.setInteger("allocateSortingBin", WaveAllocateSortingBin.REALLOCATE.getValue());
        }
        query.setMaxResults(1);
        Integer result = (Integer) query.uniqueResult();
        return result == null ? false : true;
    }

    @SuppressWarnings("unchecked")
    public List<Object> getWaveMergeInfo(Long partitionId, Timestamp createTimeFrom,
            Timestamp createTimeTo, String mergeStatusFrom, String mergeStatusTo, int startIndex, int pageSize) {
        StringBuilder sql = new StringBuilder();
        sql.append(
                " select mp.partition_code, ml.merge_code, wh.wave_no, mc.container_no, wh.merge_status, wh.e_do_finish_time from doc_wave_header wh ");
        sql.append(
                " left join md_merge_loc ml on wh.merge_loc_id = ml.id and ml.warehouse_id =:warehouseId left join md_merge_partition mp on mp.id = ml.merge_partition_id and mp.warehouse_id=:warehouseId ");
        sql.append(" left join md_sorting_bin msb on wh.sort_grid_id = msb.id and msb.warehouse_id = :warehouseId ");
        sql.append(
                " left join (select co.doc_no as wave_header_no, GROUP_CONCAT(co.container_no) As Container_No from md_container co ");
        sql.append(
                " where co.warehouse_id=:warehouseId and co.container_type ='0' and co.doc_type = '0' Group By co.doc_no Order By co.container_no)) mc on wh.wave_no = mc.wave_header_no ");
        sql.append(
                " where wh.status >= :waveStatusFrom and wh.status <=:waveStatusTo and wh.warehouse_id = :warehouseId and wh.is_deleted = 0");
        if (null != partitionId) {
            sql.append(" and mp.id = :partitionId ");
        }
        if (null != createTimeFrom) {
            sql.append(" and wh.create_time >= :createTimeFrom ");
        }
        if (null != createTimeTo) {
            sql.append(" and wh.create_time <= :createTimeTo ");
        }
        if (null != mergeStatusFrom) {
            sql.append(" and wh.merge_status >= :mergeStatusFrom ");
        }
        if (null != mergeStatusTo) {
            sql.append(" and wh.merge_status <= :mergeStatusTo ");
        }
        sql.append(" order by wh.e_do_finish_time,wh.create_time asc ");

        Query query = createSQLQuery(sql.toString());
        if (null != partitionId) {
            query.setParameter("partitionId", partitionId);
        }
        if (null != createTimeFrom) {
            query.setParameter("createTimeFrom", createTimeFrom);
        }
        if (null != createTimeTo) {
            query.setParameter("createTimeTo", createTimeTo);
        }
        if (null != mergeStatusFrom) {
            query.setParameter("mergeStatusFrom", mergeStatusFrom);
        }
        if (null != mergeStatusTo) {
            query.setParameter("mergeStatusTo", mergeStatusTo);
        }
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("waveStatusFrom", Constants.WaveStatus.ALLPICKED.getValue());
        query.setParameter("waveStatusTo", Constants.WaveStatus.ALLSORTED.getValue());

        query.setFirstResult((startIndex - 1) * pageSize);
        query.setMaxResults(pageSize);

        return query.list();
    }

    public Long getWaveMergeInfoCount(Long partitionId, Timestamp createTimeFrom,
            Timestamp createTimeTo, String mergeStatusFrom, String mergeStatusTo) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select count(*) from doc_wave_header wh ");
        sql.append(
                " left join md_merge_loc ml on ml.id = wh.merge_loc_id and ml.warehouse_id=:warehouseId left join md_merge_partition mp on mp.id = ml.merge_partition_id and mp.warehouse_id=:warehouseId ");
        sql.append(" left join md_sorting_bin msb on wh.sort_grid_id = msb.id and msb.warehouse_id = :warehouseId ");
        sql.append(
                " left join (select co.doc_no as wave_header_no, GROUP_CONCAT(co.container_no) As Container_No from md_container co ");
        sql.append(
                " where co.warehouse_id=:warehouseId and co.container_type ='0' and co.doc_type = '0' Group By co.doc_no Order By co.container_no) mc on wh.wave_no = mc.wave_header_no ");
        sql.append(
                " where wh.status >= :waveStatusFrom and wh.status <= :waveStatusTo  and wh.warehouse_id = :warehouseId and wh.is_deleted = 0");
        if (null != partitionId) {
            sql.append(" and mp.id = :partitionId ");
        }
        if (null != createTimeFrom) {
            sql.append(" and wh.create_time >= :createTimeFrom ");
        }
        if (null != createTimeTo) {
            sql.append(" and wh.create_time <= :createTimeTo ");
        }
        if (null != mergeStatusFrom) {
            sql.append(" and wh.merge_status >= :mergeStatusFrom ");
        }
        if (null != mergeStatusTo) {
            sql.append(" and wh.merge_status <= :mergeStatusTo ");
        }

        Query query = createSQLQuery(sql.toString());
        if (null != partitionId) {
            query.setParameter("partitionId", partitionId);
        }
        if (null != createTimeFrom) {
            query.setParameter("createTimeFrom", createTimeFrom);
        }
        if (null != createTimeTo) {
            query.setParameter("createTimeTo", createTimeTo);
        }
        if (null != mergeStatusFrom) {
            query.setParameter("mergeStatusFrom", mergeStatusFrom);
        }
        if (null != mergeStatusTo) {
            query.setParameter("mergeStatusTo", mergeStatusTo);
        }
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("waveStatusFrom", Constants.WaveStatus.ALLPICKED.getValue());
        query.setParameter("waveStatusTo", Constants.WaveStatus.ALLSORTED.getValue());

        return (Long) query.uniqueResult();
    }

    /**
     * 根据波次号查询仓库id
     * 
     * @param waveNo
     * @return
     */
    public BigInteger queryWarehouseIdByWaveNo(String waveNo) {
        String sql = "select wh.warehouse_id from doc_wave_header wh where wh.wave_no = :waveNo";
        Query query = createSQLQuery(sql);
        query.setString("waveNo", waveNo);
        return (BigInteger) query.uniqueResult();
    }

    /**
     * 查询空闲分拣柜
     * 
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Object> getFreeSortBins() {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append(" GROUP_CONCAT(msb.sorting_zone_nbr) as sort_grid_no ");
        sb.append(" from md_sorting_bin msb ");
        sb.append(" where not exists (select 1 ");
        sb.append(" from doc_do_header dh ");
        sb.append(" where msb.id = dh.sorting_bin_id ");
        sb.append(" and dh.warehouse_id = :whId and dh.is_deleted = 0 and dh.status = :status ) ");
        sb.append(" and msb.warehouse_id = :whId and msb.is_available = 1 order by msb.sorting_zone_nbr ");

        Query query = this.createSQLQuery(sb.toString());
        query.setParameter("status", Constants.WaveStatus.PARTSORTED.getValue());
        query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 波次是否包含酒类随附单
     */
    public boolean isWineWave(String waveNo) {
        String sql = "select count(id) from doc_wave_header t where exists (select 1 from doc_do_header where in_wine = :inWine and is_deleted = 0 and warehouse_id = :whId and wave_id = t.id) "
                + " and t.wave_no = :waveNo and t.is_deleted = 0 and t.warehouse_id = :whId";
        Query query = createSQLQuery(sql);
        query.setParameter("inWine", YesNo.YES.getValue());
        query.setParameter("waveNo", waveNo);
        query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
        BigInteger count = (BigInteger) query.uniqueResult();
        return BigInteger.ZERO.equals(count) ? false : true;
    }

    /**
     * 波次对应DO需要绑定分拣箱
     */
    @SuppressWarnings("unchecked")
    public List<String> doNeedBingContatiner(Long waveId) {
        String sql = " select ddh.do_no from doc_do_header ddh inner join doc_wave_header dwh on ddh.wave_id = dwh.id 	"
                + " left join md_container mc on mc.doc_no = ddh.do_no and mc.container_type = :BINDDOCTYPE 		"
                + " where ddh.warehouse_id = :whid and dwh.id = :WAVEID and ddh.is_deleted = 0 						"
                + " and mc.id is null and ddh.status = :STATUS order by ddh.sort_grid_no desc  						";
        Query query = createSQLQuery(sql);
        query.setParameter("BINDDOCTYPE", Constants.ContainerType.SORT_CONTAINER.getValue());
        query.setParameter("WAVEID", waveId);
        query.setParameter("STATUS", Constants.DoStatus.ALLPICKED.getValue());
        query.setParameter("WHID", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 波次对应DO需要绑定分拣箱
     */
    public String checkDoNeedBingContatiner(String doNumber) {
        String sql = " select ddh.sort_grid_no from doc_do_header ddh										"
                + " left join md_container mc on mc.doc_no = ddh.do_no and mc.container_type = :BINDDOCTYPE "
                + " where ddh.warehouse_id = :WHID and ddh.do_no = :doNumber and ddh.is_deleted = 0 		"
                + " and mc.id is null and ddh.status = :STATUS 												";
        Query query = createSQLQuery(sql);
        query.setParameter("BINDDOCTYPE", Constants.ContainerType.SORT_CONTAINER.getValue());
        query.setParameter("doNumber", doNumber);
        query.setParameter("WHID", ParamUtil.getCurrentWarehouseId());
        query.setParameter("STATUS", Constants.DoStatus.ALLPICKED.getValue());
        Object retVal = (Object) query.uniqueResult();
        return null == retVal ? null : retVal.toString();
    }

    @SuppressWarnings("unchecked")
    public List<BigDecimal> getSortNoListByWaveId(Long waveId) {
        String sql = " select ddh.sort_grid_no from doc_do_header ddh inner join doc_wave_header dwh on ddh.wave_id = dwh.id "
                + " where ddh.warehouse_id = :WHID and dwh.id = :WAVEID and ddh.is_deleted = 0 "
                + " order by ddh.sort_grid_no desc ";
        Query query = createSQLQuery(sql);
        query.setParameter("WAVEID", waveId);
        query.setParameter("WHID", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 取到指定波次类型的波次id
     */
    @SuppressWarnings("unchecked")
    public List<Long> getWaveIdsByAutoWaveType(int batchSize, int failMaxNum, Integer autoWaveType) {
        String hql = " select o.id from WaveHeader o where o.waveStatus >= :statusFm and o.waveStatus <= :statusTo and "
                + " o.autoType = :autoType and o.createdAt >= TIMESTAMPADD(DAY,-7,NOW()) and o.autoFailTime <= :failMaxNum "
                + " and exists(select 1 from DeliveryOrderHeader h where h.waveId = o.id and h.status < :doStatus) and o.warehouseId = :warehouseId ";
        Query query = createQuery(hql);
        query.setParameter("statusFm", WaveStatus.ALLALLOCATED.getValue());
        query.setParameter("statusTo", WaveStatus.ALLSORTED.getValue());
        query.setParameter("failMaxNum", failMaxNum);
        query.setParameter("doStatus", DoStatus.ALL_CARTON.getValue());
        query.setParameter("autoType", autoWaveType);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(batchSize);
        return query.list();
    }

    /**
     * 根据波次id查询波次下没有分拣的unit个数
     * 
     * @param waveId
     * @return
     */
    public BigDecimal getUnSortedQtyByWaveId(Long waveId) {
        StringBuffer hql = new StringBuffer();
        hql.append("select sum(od.allocatedQty)-sum(od.sortedQty)")
                .append(" from WaveHeader wh,DeliveryOrderDetail od,DeliveryOrderHeader oh")
                .append(" where wh.id = oh.waveId and oh.id = od.doHeaderId")
                .append(" and wh.id = :waveId")
                .append(" and wh.warehouseId = :warehouseId and od.warehouseId = :warehouseId")
                .append(" and oh.warehouseId = :warehouseId");
        Query query = this.createQuery(hql.toString());
        query.setParameter("waveId", waveId);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (BigDecimal) query.uniqueResult();
    }

    /**
     * @param printFlg
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<WaveHeader> findWaveByPrintFlg(Integer printFlg) {
        String hql = "from WaveHeader o where o.invoicePrintFlag =:printFlg and o.invoiceFlag = :invoiceFlag and o.warehouseId = :warehouseId and o.createdAt >= str_to_date(:dateFm, '%Y-%m-%d') order by createdAt";
        Query query = createQuery(hql);
        query.setInteger("printFlg", printFlg);
        query.setParameter("dateFm", DateUtil.dateToString(DateUtil.getDateAfterIDay(DateUtil.getNowDate(), -10)));
        query.setInteger("invoiceFlag", YesNo.YES.getValue());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (List<WaveHeader>) query.list();
    }

    public WaveHeader queryByDoNo(String doNo) {
        String hql = "SELECT wh FROM DeliveryOrderHeader dh, WaveHeader wh WHERE dh.waveId = wh.id AND dh.warehouseId = :warehouseId AND wh.warehouseId = :warehouseId AND dh.doNo = :doNo";
        return (WaveHeader) createQuery(hql).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId())
                .setParameter("doNo", doNo).setMaxResults(1).uniqueResult();
    }

    public List<AutoCompleteDTO> findSkuHelperData(Long waveId) {
        String hql = "SELECT DISTINCT new com.daxia.wms.master.dto.AutoCompleteDTO(sku.productCname, sku.helpCode, sku.productCode) FROM WaveHeader wh LEFT JOIN wh.pickTasks pt "
                +
                " INNER JOIN pt.sku sku WHERE wh.id = :waveId AND sku.helpCode IS NOT NULL";
        return this.createQuery(hql, ImmutableMap.<String, Object>of("waveId", waveId)).list();
    }

    public List<AutoCompleteDTO> loadSkuByWaveId(Long waveId) {
        String hql = "SELECT new com.daxia.wms.master.dto.AutoCompleteDTO(sku.id, sku.productCname, sku.helpCode, " +
                "sku.productCode,sku.specification,sum(pt.pickedQty),sum(pt.qtySorted)) FROM WaveHeader wh LEFT JOIN wh.pickTasks pt "
                +
                " INNER JOIN pt.sku sku INNER JOIN pt.doDetail dd WHERE wh.id = :waveId GROUP BY sku.id";
        return this.createQuery(hql, ImmutableMap.<String, Object>of("waveId", waveId)).list();
    }

    public boolean checkVersion(Long waveId, Integer version) {
        String sql = "select count(1) from doc_wave_header wv where wv.version = :version and wv.id = :waveId";
        Query query = this.createSQLQuery(sql);
        query.setParameter("waveId", waveId).setParameter("version", version)
                .setMaxResults(1);
        return Integer.valueOf(query.uniqueResult().toString()) > 0;
    }

    /**
     * map: [{warehouseId: 1, id: 1}]
     * 
     * @param autoFlag
     * @return
     */
    public List<Map<String, Object>> getAutoWave(Integer autoFlag) {
        Map<String, Object> params = Maps.newHashMap();
        switch (autoFlag.intValue()) {
            case WaveHeader.FLAG_AUTO_PICK:
                params.put("waveStatus", WaveStatus.ALLALLOCATED.getValue());
                break;
            case WaveHeader.FLAG_AUTO_SORT:
                params.put("waveStatus", WaveStatus.ALLPICKED.getValue());
                break;
            case WaveHeader.FLAG_AUTO_RECHECK:
                params.put("waveStatus", WaveStatus.ALLSORTED.getValue());
                break;
        }

        params.put("failedNumberMax", WaveHeader.FAILED_NUM_MAX);
        params.put("autoFlag", autoFlag);

        String sql = "SELECT wh.warehouse_id AS warehouseId, wh.id AS id FROM doc_wave_header wh " +
                "WHERE wh.`status` = :waveStatus AND wh.failed_number < :failedNumberMax AND wh.is_deleted = 0 AND (wh.auto_flag & :autoFlag) != 0 "
                +
                "AND wh.create_time >=  TIMESTAMPADD(DAY, -5, NOW())";
        if (WaveHeader.FLAG_AUTO_RECHECK == autoFlag.intValue()) {
            sql += " and not exists ( select 1 from doc_do_header dh where dh.wave_id = wh.id and dh.status > '65' and dh.status <= '80' and dh.need_cancel = 0 and dh.release_status='RL' ) ";
            sql += " and ( wh.auto_type <> '40' or wh.auto_type is null )";
        }
        SQLQuery sqlQuery = (SQLQuery) this.createSQLQuery(sql, params);
        return sqlQuery.addScalar("warehouseId", Hibernate.LONG).addScalar("id", Hibernate.LONG)
                .setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).setMaxResults(10).list();
    }

    public String getDistinctCarrierName(Long waveId) {
        List<String> carrierNames = this
                .createQuery("SELECT DISTINCT do.carrier.distSuppCompName FROM DeliveryOrderHeader do " +
                        "WHERE do.waveId = :waveId")
                .setParameter("waveId", waveId).list();
        if (ListUtil.isNullOrEmpty(carrierNames) || carrierNames.size() > 1) {
            return null;
        } else {
            return carrierNames.get(0);
        }
    }

    public List<Long> findDoMerchentIds(Long waveId) {
        String sql = "SELECT DISTINCT(merchant_id) as merchantId from doc_do_header where wave_id = :waveId";
        Query query = createSQLQuery(sql).addScalar("merchantId", Hibernate.LONG);
        query.setParameter("waveId", waveId);
        return query.list();
    }

    public long getUnpickDocNum(String unpickedRegionCodes) {
        String sql = "SELECT COUNT(1) FROM doc_wave_header wh, doc_pkt_header pkt, md_region region " +
                "WHERE pkt.wave_header_id = wh.id AND pkt.region_id = region.id " +
                "AND wh.status < :waveStatus AND wh.create_time >= ADDDATE(now(),-7) " +
                "AND wh.warehouse_id = :warehouseId AND wh.is_deleted = 0 AND pkt.warehouse_id = :warehouseId AND pkt.is_deleted = 0 AND region.warehouse_id = :warehouseId AND region.is_deleted = 0 ";

        Map<String, Object> params = Maps.newHashMap();
        if (StringUtil.isNotEmpty(unpickedRegionCodes)) {
            List<String> regionCodes = ListUtil.strSplit2List(unpickedRegionCodes, ",");
            sql = sql + " AND region_code in(:regionCodes)";
            params.put("regionCodes", regionCodes);
        }

        Query query = createSQLQuery(sql, params).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId())
                .setParameter("waveStatus", WaveStatus.ALLPICKED.getValue());
        return ((BigInteger) query.setMaxResults(1).uniqueResult()).longValue();
    }
}