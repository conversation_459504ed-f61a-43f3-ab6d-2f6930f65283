package com.daxia.wms.delivery.task.replenish.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.wms.stock.task.filter.TrsTaskFilter;

/**
 * 补货任务查询过滤器
 */
@lombok.extern.slf4j.Slf4j
public class ReplTaskFilter extends TrsTaskFilter {
   
    private static final long serialVersionUID = -918605295585591361L;

    private String productBarCode;
    private String productCode;

    @Operation(operationType = OperationType.CLAUSE ,clause = " o.id in ( select task.id from ReplenishTask task,Sku sku,ProductBarcode barCode  where  barCode.skuId = sku.id and task.skuId = sku.id and barCode.barcodeLevel1 = ?  )")
	public String getProductBarCode() {
		return productBarCode;
	}

	public void setProductBarCode(String productBarCode) {
		this.productBarCode = productBarCode;
	}

	@Operation(fieldName = "sku.productCode", operationType = OperationType.EQUAL)
	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}
    
}
