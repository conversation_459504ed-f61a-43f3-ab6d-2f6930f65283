package com.daxia.wms.delivery.recheck.service.impl.carton;

import com.daxia.framework.common.util.NullUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.master.MasterException;
import com.daxia.wms.master.entity.CarrierCainiaoEx;
import com.daxia.wms.master.entity.ShopInfo;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.google.common.collect.Lists;
import com.taobao.api.request.CainiaoWaybillIiGetRequest;
import com.taobao.api.request.CainiaoWaybillIiUpdateRequest;
import com.taobao.api.request.WlbWaybillIPrintRequest;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;

import java.math.BigDecimal;
import java.util.List;

public abstract class CainiaoWayBillBase {
    @In
    WarehouseCarrierService warehouseCarrierService;
    
    WarehouseCarrier loadWarehouseCarrier(Long carrierId) {
        WarehouseCarrier warehouseCarrier = warehouseCarrierService.getCarrierInfoByWarehouseIdAndCarrierIdAndType(ParamUtil.getCurrentWarehouseId(), carrierId, Constants.WaybillType.CAINIAO.name());
        if (warehouseCarrier == null) {
            throw new MasterException(MasterException.ERROR_WAYBILL_CONFIG_IS_NOT_EXIST);
        }
        
//        if (!warehouseCarrier.getType().equals(Constants.WaybillType.CAINIAO.name())) {
//            throw new MasterException(MasterException.ERROR_WAYBILL_CONFIG_IS_NOT_MATCH);
//        }
        return warehouseCarrier;
    }
    
    // 发货地址
    protected CainiaoWaybillIiGetRequest.UserInfoDto genShippingAddress4Get(CarrierCainiaoEx carrierCainiaoEx, DeliveryOrderHeader doHeader) {
        // 发货地址从Search接口获取;
        CainiaoWaybillIiGetRequest.UserInfoDto userInfoDto = new CainiaoWaybillIiGetRequest.UserInfoDto();
        CainiaoWaybillIiGetRequest.AddressDto waybillAddress = new CainiaoWaybillIiGetRequest.AddressDto();
        // 省,市,区
        waybillAddress.setProvince(carrierCainiaoEx.getProvince());
        waybillAddress.setCity(carrierCainiaoEx.getCity());
        waybillAddress.setDistrict(carrierCainiaoEx.getArea());
        // 详细地址
        waybillAddress.setDetail(carrierCainiaoEx.getAddressDetail());

        ShopInfo shopInfo = doHeader.getShopInfo();

        if (Constants.YesNo.YES.getValue().equals(doHeader.getHaveCfy())) {
            userInfoDto.setName(shopInfo != null ? shopInfo.getNickName() : SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_NAME_CFY, ParamUtil.getCurrentWarehouseId()));
//            waybillAddress.setDetail(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_ADDRESS_CFY, ParamUtil.getCurrentWarehouseId()));
            userInfoDto.setPhone(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_PHONE_CFY, ParamUtil.getCurrentWarehouseId()));
        } else {
            userInfoDto.setName(shopInfo != null ? shopInfo.getNickName() : SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_NAME, ParamUtil.getCurrentWarehouseId()));
//            waybillAddress.setDetail(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_ADDRESS, ParamUtil.getCurrentWarehouseId()));
            String sendPhone = NullUtil.notEmpty(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_PHONE + "_" + (shopInfo == null ? "-999" : shopInfo.getId()), ParamUtil.getCurrentWarehouseId()),
                    SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_PHONE, ParamUtil.getCurrentWarehouseId()));
            userInfoDto.setPhone(sendPhone);
        }
        String sendName = SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_NAME_COVER, ParamUtil.getCurrentWarehouseId());
        if (StringUtil.isNotBlank(sendName)) {
            userInfoDto.setName(sendName);
        }
        userInfoDto.setAddress(waybillAddress);
        return userInfoDto;
    }

    // 收件人地址
    protected CainiaoWaybillIiGetRequest.UserInfoDto genConsigneeInfo4Get(DeliveryOrderHeader doHeader) {
        CainiaoWaybillIiGetRequest.UserInfoDto userInfoDto = new CainiaoWaybillIiGetRequest.UserInfoDto();
        // 收货人
        userInfoDto.setName(doHeader.getConsigneeName());
        // 收货人联系方式
        userInfoDto.setMobile(DoUtil.decryptPhone(StringUtils.defaultIfEmpty(doHeader.getMobile(), doHeader.getTelephone()).trim()));
        if (userInfoDto.getMobile().indexOf(",") > 0) {
            userInfoDto.setMobile(userInfoDto.getMobile().split(",")[0]);
        }
        userInfoDto.setPhone(DoUtil.decryptPhone(StringUtils.defaultIfEmpty(doHeader.getMobile(), doHeader.getTelephone()).trim()));
        if (userInfoDto.getPhone().indexOf(",") > 0) {
            userInfoDto.setPhone(userInfoDto.getPhone().split(",")[0]);
        }
        //收货人地址
        userInfoDto.setAddress(genConsigneeAddress4Get(doHeader));
        return userInfoDto;
    }

    // 收件人地址
    protected CainiaoWaybillIiUpdateRequest.UserInfoDto genConsigneeInfo4Update(DeliveryOrderHeader doHeader) {
        CainiaoWaybillIiUpdateRequest.UserInfoDto userInfoDto = new CainiaoWaybillIiUpdateRequest.UserInfoDto();
        // 收货人
        userInfoDto.setName(doHeader.getConsigneeName());
        // 收货人联系方式
        userInfoDto.setMobile(DoUtil.decryptPhone(StringUtils.defaultIfEmpty(doHeader.getMobile(), doHeader.getTelephone()).trim()));
        if (userInfoDto.getMobile().indexOf(",") > 0) {
            userInfoDto.setMobile(userInfoDto.getMobile().split(",")[0]);
        }
        userInfoDto.setPhone(DoUtil.decryptPhone(StringUtils.defaultIfEmpty(doHeader.getMobile(), doHeader.getTelephone()).trim()));
        if (userInfoDto.getPhone().indexOf(",") > 0) {
            userInfoDto.setPhone(userInfoDto.getPhone().split(",")[0]);
        }
        //收货人地址
        userInfoDto.setAddress(genConsigneeAddress4Update(doHeader));
        return userInfoDto;
    }

    // 收件人地址
    protected CainiaoWaybillIiGetRequest.AddressDto genConsigneeAddress4Get(DeliveryOrderHeader doHeader) {
        CainiaoWaybillIiGetRequest.AddressDto waybillAddress = new CainiaoWaybillIiGetRequest.AddressDto();
        if (null != doHeader.getProvinceInfo() || StringUtils.isNotEmpty(doHeader.getProvinceName())) {
            // 省
            waybillAddress.setProvince(StringUtils.isNotEmpty(doHeader.getProvinceName()) ? doHeader.getProvinceName() :
                    doHeader.getProvinceInfo().getProvinceCname());
        } else {
            throw new DeliveryException(DeliveryException.ERROR_ADDRESS_ERROR);
        }
        if (null != doHeader.getCityInfo() || StringUtils.isNotEmpty(doHeader.getCityName())) {
            waybillAddress.setCity(StringUtils.isNotEmpty(doHeader.getCityName()) ? doHeader.getCityName() :
                    doHeader.getCityInfo().getCityCname());
        } else {
            throw new DeliveryException(DeliveryException.ERROR_ADDRESS_ERROR);
        }
        if (null != doHeader.getCountyInfo() || StringUtils.isNotEmpty(doHeader.getCountyName())) {
            waybillAddress.setDistrict(StringUtils.isNotEmpty(doHeader.getCountyName()) ? doHeader.getCountyName() :
                    doHeader.getCountyInfo().getCountyCname());
        } else {
            throw new DeliveryException(DeliveryException.ERROR_ADDRESS_ERROR);
        }
        // 详细地址
        waybillAddress.setDetail(doHeader.getAddress());
        return waybillAddress;
    }

    // 收件人地址
    protected CainiaoWaybillIiUpdateRequest.AddressDto genConsigneeAddress4Update(DeliveryOrderHeader doHeader) {
        CainiaoWaybillIiUpdateRequest.AddressDto waybillAddress = new CainiaoWaybillIiUpdateRequest.AddressDto();
        if (null != doHeader.getProvinceInfo() || StringUtils.isNotEmpty(doHeader.getProvinceName())) {
            // 省
            waybillAddress.setProvince(StringUtils.isNotEmpty(doHeader.getProvinceName()) ? doHeader.getProvinceName() :
                    doHeader.getProvinceInfo().getProvinceCname());
        } else {
            throw new DeliveryException(DeliveryException.ERROR_ADDRESS_ERROR);
        }
        if (null != doHeader.getCityInfo() || StringUtils.isNotEmpty(doHeader.getCityName())) {
            waybillAddress.setCity(StringUtils.isNotEmpty(doHeader.getCityName()) ? doHeader.getCityName() :
                    doHeader.getCityInfo().getCityCname());
        } else {
            throw new DeliveryException(DeliveryException.ERROR_ADDRESS_ERROR);
        }
        if (null != doHeader.getCountyInfo() || StringUtils.isNotEmpty(doHeader.getCountyName())) {
            waybillAddress.setDistrict(StringUtils.isNotEmpty(doHeader.getCountyName()) ? doHeader.getCountyName() :
                    doHeader.getCountyInfo().getCountyCname());
        } else {
            throw new DeliveryException(DeliveryException.ERROR_ADDRESS_ERROR);
        }
        // 详细地址
        waybillAddress.setDetail(doHeader.getAddress());
        return waybillAddress;
    }

    protected String genLogisticsServiceList4Get(DeliveryOrderHeader doHeader) {
        if (doHeader.getReceivable() != null && doHeader.getReceivable().compareTo(BigDecimal.ZERO) > 0) {
            //payment_type: {CASH: 现金, CARD: 刷卡}; currency: 币种，参考维基百科: "CNY","USD","HKD","EUR","RUB"
            return "{\"SVC-COD\": {\"value\": " + doHeader.getReceivable().setScale(2, BigDecimal.ROUND_HALF_UP) + "}}";
        }
        return null;
    }

    // 发货地址
    protected WlbWaybillIPrintRequest.WaybillAddress genShippingAddress4Print(CarrierCainiaoEx carrierCainiaoEx) {
        // 发货地址从Search接口获取;
        WlbWaybillIPrintRequest.WaybillAddress waybillAddress = new WlbWaybillIPrintRequest.WaybillAddress();
        // 省
        waybillAddress.setProvince(carrierCainiaoEx.getProvince());
        // 详细地址
        waybillAddress.setAddressDetail(carrierCainiaoEx.getAddressDetail());
        waybillAddress.setCity(carrierCainiaoEx.getCity());
        waybillAddress.setArea(carrierCainiaoEx.getArea());
        return waybillAddress;
    }

    // 收件人地址
    protected WlbWaybillIPrintRequest.WaybillAddress genConsigneeAddress4Print(DeliveryOrderHeader doHeader) {
        WlbWaybillIPrintRequest.WaybillAddress waybillAddress = new WlbWaybillIPrintRequest.WaybillAddress();
        if (null != doHeader.getProvinceInfo() || StringUtils.isNotEmpty(doHeader.getProvinceName())) {
            // 省
            waybillAddress.setProvince(StringUtils.isNotEmpty(doHeader.getProvinceName()) ? doHeader.getProvinceName() :
                    doHeader.getProvinceInfo().getProvinceCname());
        } else {
            throw new DeliveryException(DeliveryException.ERROR_ADDRESS_ERROR);
        }
        if (null != doHeader.getCityInfo() || StringUtils.isNotEmpty(doHeader.getCityName())) {
            waybillAddress.setCity(StringUtils.isNotEmpty(doHeader.getCityName()) ? doHeader.getCityName() :
                    doHeader.getCityInfo().getCityCname());
        } else {
            throw new DeliveryException(DeliveryException.ERROR_ADDRESS_ERROR);
        }
        if (null != doHeader.getCountyInfo() || StringUtils.isNotEmpty(doHeader.getCountyName())) {
            waybillAddress.setArea(StringUtils.isNotEmpty(doHeader.getCountyName()) ? doHeader.getCountyName() :
                    doHeader.getCountyInfo().getCountyCname());
        } else {
            throw new DeliveryException(DeliveryException.ERROR_ADDRESS_ERROR);
        }
        // 详细地址
        waybillAddress.setAddressDetail(doHeader.getAddress());
        return waybillAddress;
    }

    protected List<WlbWaybillIPrintRequest.LogisticsService> genLogisticsServiceList4Print(DeliveryOrderHeader doHeader) {
        List<WlbWaybillIPrintRequest.LogisticsService> logisticsServiceList = Lists.newArrayList();
        if (doHeader.getReceivable() != null && doHeader.getReceivable().compareTo(BigDecimal.ZERO) > 0) {
            WlbWaybillIPrintRequest.LogisticsService logisticsService = new WlbWaybillIPrintRequest.LogisticsService();
            logisticsService.setServiceCode("SVC-COD");
            //payment_type: {CASH: 现金, CARD: 刷卡}; currency: 币种，参考维基百科: "CNY","USD","HKD","EUR","RUB"
            logisticsService.setServiceValue4Json("{\"value\": \"" + doHeader.getReceivable().setScale(2, BigDecimal.ROUND_HALF_UP).toString() + "\",\"currency\": \"CNY\",\"payment_type\": \"CASH\"}");

            logisticsServiceList.add(logisticsService);
        }

        return logisticsServiceList;
    }

    // 使用者ID(发货时商家 ID一致)
    // 淘系订单【使用者 id】可以使用查询卖家用户信息接口taobao.user.seller.get获取
    // 非淘系订单【使用者id】直接使用【申请者id】， 风控只针对淘系订单
    protected Long genRealUserId(DeliveryOrderHeader doHeader, CarrierCainiaoEx carrierCainiaoEx) {
        return carrierCainiaoEx.getSellId().longValue();
    }


    protected Long getWeight(CartonHeader cartonHeader) {
        return cartonHeader.getActualGrossWeight().multiply(BigDecimal.valueOf(1000L)).longValue();
    }
}
