package com.daxia.wms.delivery.recheck.dto;

import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeaderHis;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

/**
 * 表示核拣时一个箱子装箱信息
 */
@lombok.extern.slf4j.Slf4j
public class ReCheckCartonInfo {
	private DeliveryOrderHeader doHeader;
	private DeliveryOrderHeaderHis doHeaderHis;//发货单历史查询用
	private Long cartonId;
	private String cartonNo;
	private String wayBill;//运单号（宅急便）
	private String packMaterialNo;
	private List<ReCheckCartonDetail> details;
	private Set<Pair> serials;

	private BigDecimal volume;
	private BigDecimal grossWeight;
	private String doNo;
	private boolean weight;
	private BigDecimal actualWeight;
	/**
	 * 分拣格号
	 */
	private String sortGridNo;
	
	private Timestamp createdAt;
	private String createBy;

	//称重人
	private String weighBy;
	//称重时间
	private Date weighTime;

	//核检人
	private String recheckBy;

	private String packingDeskNo;
	
	/**
	 * 已装箱的数量
	 */
	private BigDecimal unitsQty;
	/**
	 * 是否需要自动发运
	 */
	private boolean needAutoDelivery = false;

	private String recheckType;

	private String packageType;

	private String containerNo;
	/**
	 * 核拣装箱录像地址
	 */
	private String recordFileName;
	/**
	 *  内部耗材
	 */
	@Setter
	@Getter
	private String innerMaterial;


	public String getRecordFileName() {
		return recordFileName;
	}

	public void setRecordFileName(String recordFileName) {
		this.recordFileName = recordFileName;
	}

	private boolean checkMaterial = true;

	public ReCheckCartonInfo(DeliveryOrderHeader doHeader) {
		this.doHeader = doHeader;
		details = new ArrayList<ReCheckCartonDetail>();
	}

	public void setDoHeader(DeliveryOrderHeader doHeader) {
		this.doHeader = doHeader;
	}

	/**
	 * 增加当前箱内的核拣记录
	 * 
	 * @param record
	 * @param number
	 */
	public void addReCheckRecord(ReCheckRecord record, BigDecimal number) {
		if (record == null) {
			return;
		}
        ReCheckCartonDetail detail = null;
        for (ReCheckCartonDetail d : details) {
            if (d.getRecord().getProductId().equals(record.getProductId())
                    && StringUtil.equals(d.getRecord().getLotatt05(), record.getLotatt05())) {
                detail = d;
                break;
            }
        }
        if (detail == null) {
            detail = new ReCheckCartonDetail(this, record, number);
            detail.setPackQty(record.getPackQty());
            details.add(detail);
        } else {
            detail.increaseNumberInCarton(number);
        }
        record.addCheckNumber(number);
	}

	/**
	 * 按商品id删除当前箱内的核拣记录
	 * @param productId
	 */
	public void removeReCheckRecordByProductId(Long productId) {
		for (int index = 0; index < details.size(); index++) {
			ReCheckCartonDetail d = details.get(index);
			if (!productId.equals(d.getRecord().getProductId())) {
				continue;
			}
			d.setNumberInCarton(d.getNumberInCarton().subtract(BigDecimal.ONE));
			d.getRecord().decreaseCheckNumber(BigDecimal.ONE);
			if (d.getNumberInCarton().compareTo(BigDecimal.ZERO) == 0) {
				details.remove(index);
			}
			return;
		}
	}

	/**
	 * 按商品条码查询 一个箱子里面某一个商品的详细装箱记录
	 * @param barCode
	 * @return
	 */
	public List<ReCheckCartonDetail> findDetailByProductBarCode(String barCode) {
		if (barCode == null) {
			return null;
		}
		List<ReCheckCartonDetail> results = new ArrayList<ReCheckCartonDetail>();
		for (ReCheckCartonDetail d : details) {
			if (barCode.equals(d.getRecord().getProductBarCode())) {
				results.add(d);
			}
		}
		return results;
	}

	public List<ReCheckCartonDetail> getDetails() {
		return details;
	}

	public void setDetails(List<ReCheckCartonDetail> details) {
		this.details = details;
	}

	public Long getCartonId() {
		return cartonId;
	}

	public void setCartonId(Long cartonId) {
		this.cartonId = cartonId;
	}

	public String getWayBill() {
		return wayBill;
	}

	public void setWayBill(String wayBill) {
		this.wayBill = wayBill;
	}

	public String getCartonNo() {
		return cartonNo;
	}

	public void setCartonNo(String cartonNo) {
		this.cartonNo = cartonNo;
	}
	

	public String getPackMaterialNo() {
		return packMaterialNo;
	}

	public void setPackMaterialNo(String packMaterialNo) {
		this.packMaterialNo = packMaterialNo;
	}

	public DeliveryOrderHeader getDoHeader() {
		return doHeader;
	}

	/**
	 * 清除箱中所有已装入的商品
	 */
	public void clear() {
		for (ReCheckCartonDetail d : details) {
			if (d == null) {
				continue;
			}
			ReCheckRecord record = d.getRecord();
			record.setFinishedCheckNumber(record.getFinishedCheckNumber()
					.subtract(d.getNumberInCarton()));
		}
	}

	public String getRecheckBy() {
		return recheckBy;
	}

	public void setRecheckBy(String recheckBy) {
		this.recheckBy = recheckBy;
	}

	public BigDecimal getVolume() {
		return volume;
	}

	public void setVolume(BigDecimal volume) {
		this.volume = volume;
	}

	public BigDecimal getGrossWeight() {
		return grossWeight;
	}

	public void setGrossWeight(BigDecimal grossWeight) {
		this.grossWeight = grossWeight;
	}

	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}

	public boolean isWeight() {
		return weight;
	}

	public void setWeight(boolean weight) {
		this.weight = weight;
	}

	public BigDecimal getActualWeight() {
		return actualWeight;
	}

	public void setActualWeight(BigDecimal actualWeight) {
		this.actualWeight = actualWeight;
	}

	public void addSerial(Long productId,String serialNo) {
		if (serials == null) {
			serials = new HashSet<Pair>();
		}
		serials.add(Pair.of(productId, serialNo));
	}

	public Set<Pair> getSerials() {
		return serials;
	}

	public void setCreateAt(Timestamp createdAt) {
		this.createdAt = createdAt;
	}

	public Timestamp getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Timestamp createdAt) {
		this.createdAt = createdAt;
	}

	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}
    
    public BigDecimal getUnitsQty() {
        return unitsQty;
    }
    
    public void setUnitsQty(BigDecimal unitsQty) {
        this.unitsQty = unitsQty;
    }

    public DeliveryOrderHeaderHis getDoHeaderHis() {
        return doHeaderHis;
    }
    
    public void setDoHeaderHis(DeliveryOrderHeaderHis doHeaderHis) {
        this.doHeaderHis = doHeaderHis;
    }

	public boolean isNeedAutoDelivery() {
		return needAutoDelivery;
	}

	public void setNeedAutoDelivery(boolean needAutoDelivery) {
		this.needAutoDelivery = needAutoDelivery;
	}

	public String getWeighBy() {
		return weighBy;
	}

	public void setWeighBy(String weighBy) {
		this.weighBy = weighBy;
	}

	public Date getWeighTime() {
		return weighTime;
	}

	public void setWeighTime(Date weighTime) {
		this.weighTime = weighTime;
	}

	public String getSortGridNo() {
		return sortGridNo;
	}

	public void setSortGridNo(String sortGridNo) {
		this.sortGridNo = sortGridNo;
	}

	public String getPackingDeskNo() {
		return packingDeskNo;
	}

	public void setPackingDeskNo(String packingDeskNo) {
		this.packingDeskNo = packingDeskNo;
	}

	public String getRecheckType() {
		return recheckType;
	}

	public void setRecheckType(String recheckType) {
		this.recheckType = recheckType;
	}

	public String getPackageType() {
		return packageType;
	}

	public void setPackageType(String packageType) {
		this.packageType = packageType;
	}

	public String getContainerNo() {
		return containerNo;
	}

	public void setContainerNo(String containerNo) {
		this.containerNo = containerNo;
	}

	public boolean isCheckMaterial() {
		return checkMaterial;
	}

	public void setCheckMaterial(boolean checkMaterial) {
		this.checkMaterial = checkMaterial;
	}
}
