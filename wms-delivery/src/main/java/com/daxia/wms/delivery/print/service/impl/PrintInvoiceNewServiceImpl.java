package com.daxia.wms.delivery.print.service.impl;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.jboss.seam.security.Identity;

import com.daxia.framework.common.service.ReportGenerator;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.invoice.dao.InvoiceDao;
import com.daxia.wms.delivery.invoice.entity.InvoiceDetail;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;
import com.daxia.wms.delivery.invoice.service.InvoiceNoService;
import com.daxia.wms.delivery.invoice.service.InvoiceService;
import com.daxia.wms.delivery.print.dto.InvoiceNewDTO;
import com.daxia.wms.delivery.print.dto.InvoiceNewDTO.InvoiceNewDetailDTO;
import com.daxia.wms.delivery.print.helper.PrintInvoiceHelper;
import com.daxia.wms.delivery.print.service.PrintInvoiceNewService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.master.service.impl.AbstractPrintService;

/**
 * 
 * <pre>
 * 新模式打印发票
 * </pre>
 */
@Name("com.daxia.wms.delivery.printInvoiceNewService")
@lombok.extern.slf4j.Slf4j
public class PrintInvoiceNewServiceImpl extends AbstractPrintService<InvoiceNewDTO> implements PrintInvoiceNewService {

    @In
    private InvoiceService invoiceService;

    @In
    private InvoiceDao invoiceDao;
    
    @In
    private DeliveryOrderService deliveryOrderService;
    
    @In
    private WaveService waveService;
    
    @In
    private ReportGenerator reportGenerator;
    
    @In
    private Identity identity;
    
    @In
    private InvoiceNoService invoiceNoService;
    
    private static String EXPORTTYPE = "export";

    /**
     * 从invoiceList获取打印数据
     * @param invoiceList
     * @param invoiceCode
     * @return
     */
    private List<InvoiceNewDTO> getPrintDataFromList(List<InvoiceHeader> invoiceList,String invoiceCode) {
        if (invoiceList.isEmpty()) {
        	throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        return buildDTO(invoiceList, invoiceCode);
    }

    /**
     * 获取发票打印数据
     */
    @Override
    public List<InvoiceNewDTO> getPrintDataOnly(List<Long> ids, String invoiceCode) {
        if (ids.isEmpty()) {
        	throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        
        List<InvoiceHeader> invoiceList = invoiceService.getInvoiceListForPrint(ids);

        return getPrintDataFromList(invoiceList, invoiceCode);
    }

    /**
     * 设置发票为已打印
     */
    @Override
    @Transactional
    public void setInvoicePrinted(List<Long> ids) {
        invoiceDao.setInvoicePrinted(ids);
        //由于后面要根据要根据发票是否打印来做判断，需先提交
        invoiceDao.getSession().flush();
        invoiceDao.getSession().clear();
        
        List<WaveHeader> waves = invoiceDao.getWaveByInvoiceId(ids);
        if (ListUtil.isNotEmpty(waves)) {
            for (WaveHeader waveHeader : waves) {
                deliveryOrderService.updateWaveInvoiceInfo(waveHeader, waveHeader.getDoHeaders(), null);
                waveService.updateWaveHeader(waveHeader);
            }
        }
    }

    /**
     * 根据发票头和发票代码得到发票打印的数据
     * @param invoiceList
     * @param invoiceCode
     * @return
     */
    private List<InvoiceNewDTO> buildDTO(List<InvoiceHeader> invoiceList, String invoiceCode) {
        String operator = this.getOperateUser();
        String webSite = SystemConfig.getConfigValue("system.comSite", ParamUtil.getCurrentWarehouseId());

        List<InvoiceNewDTO> dtoList = new ArrayList<InvoiceNewDTO>(invoiceList.size());

        WaveHeader waveHeader = null;
        for (InvoiceHeader invoice : invoiceList) {
            DeliveryOrderHeader doHeader = invoice.getDeliveryOrderHeader();

            InvoiceNewDTO dto = new InvoiceNewDTO();
            dto.setOperator(operator);
            dto.setDoNo(doHeader.getDoNo());
            dto.setPayer(invoice.getInvoiceTitle());
            dto.setTotal(new BigDecimal(invoice.getInvoiceAmount()));
            dto.setTotalRmb(StringUtil.numToRMBStr(invoice.getInvoiceAmount()));
            dto.setWebsite(webSite);
            List<InvoiceDetail> details = invoiceService.findInvoiceDetailsByHeaderId(invoice.getId());
            dto.setDetailList(buildDTODetail(details));
            dto.setReceiverCodeId(invoice.getTaxNo());
            dto.setReceiver(invoice.getBranchName());
            dto.setPrintDate(DateUtil.dateToString(DateUtil.getTodayDate(), "yyyy-MM-dd"));
            dto.setIndustryType("商业");
            if (invoice.getSortBy() != null) {
            	dto.setSerialNum(invoice.getSortBy() + "/" + doHeader.getInvoiceQty());
            }

            if (InvoiceService.INVOICE_LEVEL_3.equals(invoiceService.getInvoiceLevel())
                    || InvoiceService.INVOICE_LEVEL_4.equals(invoiceService.getInvoiceLevel())
                    || InvoiceService.INVOICE_LEVEL_5.equals(invoiceService.getInvoiceLevel())) {
                dto.setInvoiceCode("机打代码 " + invoiceCode);
                dto.setInvoiceNo("机打号码 " + invoice.getInvoiceNumber());
            }

            waveHeader = doHeader.getWaveHeader();
            if (waveHeader != null) {
            	dto.setWaveNo(waveHeader.getWaveNo());
            }

            dto.setSortGridNo(doHeader.getSortGridNo());

            dtoList.add(dto);
        }
        return dtoList;
    }

    /**
     * 发票明细
     * @param invoiceDetails
     * @return
     */
    private List<InvoiceNewDetailDTO> buildDTODetail(List<InvoiceDetail> invoiceDetails) {
        List<InvoiceNewDetailDTO> detailDTOs = new ArrayList<InvoiceNewDetailDTO>(invoiceDetails.size());

        for (InvoiceDetail detail : invoiceDetails) {
            InvoiceNewDetailDTO detailDTO = new InvoiceNewDetailDTO();

            detailDTO.setItem(detail.getSkuDescr());
            if (detail.getQty() != null) {
            	detailDTO.setQty(detail.getQty());
            }
            detailDTO.setUnitPrice(detail.getPrice());
            detailDTO.setTotalPrice(detail.getAmount());
            detailDTO.setUom(detail.getUomDescr());

            detailDTOs.add(detailDTO);
        }
        return detailDTOs;
    }

    /**
     * 报表名称
     * @return
     */
    protected String[] buildReportName() {
        return new String[] { "invoice/invoice", null };
    }

    /**
     * 设置发票为已打印
     */
    @Override
    @Transactional
    public List<String> print(List<Long> ids, String invoiceCode) {
        List<InvoiceNewDTO> printDataList = this.getPrintDataOnly(ids, invoiceCode);

        this.setInvoicePrinted(ids);

        return this.printData(printDataList, this.getEmptyParams(), buildReportName()[0], buildReportName()[1]);
    }
    
    @Override
    public List<InvoiceNewDTO> getPrintData4RollInvoice(List<Long> idList, String invoiceCode,String operationType) {

        if (idList.isEmpty()) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        
        List<InvoiceHeader> invoiceList = invoiceService.getInvoiceListForPrint(idList);

        return getPrintDataFromList4RollInvoice(invoiceList, invoiceCode,operationType);
    
    }

    /**
     * 构建卷式发票的数据
     * @param invoiceList
     * @param invoiceCode
     * @return
     */
    @Override
    public List<InvoiceNewDTO> getPrintDataFromList4RollInvoice(List<InvoiceHeader> invoiceList, String invoiceCode,String operationType) {

        if (invoiceList.isEmpty()) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        return buildDTO4RollInvoice(invoiceList, invoiceCode,operationType);
    
    }

    /**
     * 构造卷式发票的DTO
     * @param invoiceList
     * @param invoiceCode
     * @return
     */
    private List<InvoiceNewDTO> buildDTO4RollInvoice(List<InvoiceHeader> invoiceList, String invoiceCode,String operationType) {

        String operator = this.getOperateUser();
        String webSite = SystemConfig.getConfigValue("system.comSite", ParamUtil.getCurrentWarehouseId());

        List<InvoiceNewDTO> dtoList = new ArrayList<InvoiceNewDTO>(invoiceList.size());

        WaveHeader waveHeader = null;
        for (InvoiceHeader invoice : invoiceList) {
            DeliveryOrderHeader doHeader = invoice.getDeliveryOrderHeader();

            InvoiceNewDTO dto = new InvoiceNewDTO();
            dto.setId(invoice.getId());
            dto.setOperator(operator);
            //送货单号
            dto.setDoNo(doHeader.getDoNo());
            
            //付款单位（个人）
            if(invoice.getInvoiceTitle().length() > 40){
                dto.setPayer(invoice.getInvoiceTitle().substring(0, 40));
            }else{
                dto.setPayer(invoice.getInvoiceTitle());
            }
            
            dto.setTotal(BigDecimal.valueOf(invoice.getInvoiceAmount()));
            dto.setTotalRmb(StringUtil.numToRMBStr(invoice.getInvoiceAmount()));
            dto.setWebsite(webSite);
            List<InvoiceDetail> details = invoiceService.findInvoiceDetailsByHeaderId(invoice.getId());
            dto.setDetailList(buildDTODetail4Roll(details,operationType));
            dto.setReceiverCodeId(invoice.getTaxNo());
            //收款员
            dto.setReceiver(operator);
            
            dto.setPrintDate(DateUtil.dateToString(DateUtil.getTodayDate(), "yyyy-MM-dd"));
            dto.setIndustryType("商业");
            
            //TODO
            //打印机编号
            dto.setMachineNo("Printer001");
            //收款单位
            dto.setReceiveUnit(invoice.getBranchName());
            
            // 税务登记号
            dto.setTaxNo(invoice.getTaxNo());
            
            // 订单编号
            dto.setSoNo(invoice.getSoCode());
            
            if (invoice.getSortBy() != null) {
                dto.setSerialNum(invoice.getSortBy() + "/" + doHeader.getInvoiceQty());
            }

            //发票号码
            dto.setInvoiceNo(invoice.getInvoiceNumber());

            waveHeader = doHeader.getWaveHeader();
            if (waveHeader != null) {
                dto.setWaveNo(waveHeader.getWaveNo());
            }
            
            //分拣格号
            dto.setSortGridNo(doHeader.getSortGridNo());
            
            if(EXPORTTYPE.equals(operationType))
            {
            	// 导出
            	InvoiceNo invoiceNo = invoiceNoService.getInvoiceNoObjByInvNo(invoice.getInvoiceNumber());
            	if(invoiceNo != null)
            	{
            		dto.setPrintDate(DateUtil.dateToString(invoiceNo.getUpdatedAt(), "yyyy-MM-dd"));
                	dto.setInvoiceCode(invoiceNo.getInvoiceCode());
            	}
            }
            
            dtoList.add(dto);
        }
        return dtoList;
    
    }

    private List<InvoiceNewDetailDTO> buildDTODetail4Roll(List<InvoiceDetail> invoiceDetails,String operationType) {

        List<InvoiceNewDetailDTO> detailDTOs = new ArrayList<InvoiceNewDetailDTO>(invoiceDetails.size());
        String item;
        String uom;

        if(EXPORTTYPE.equals(operationType)){
            //导出
            String regEx = "^\\w+$";
            Pattern p = Pattern.compile(regEx);
            for (InvoiceDetail detail : invoiceDetails) {
            	if(StringUtil.isEmpty(detail.getSkuDescr())){
            		continue;
            	}
                InvoiceNewDetailDTO detailDTO = new InvoiceNewDetailDTO();
                item = "";
                uom = "";
                int pos1 = 0;
                if (detail.getSkuDescr() != null) {
                    pos1 = PrintInvoiceHelper.cal(detail.getSkuDescr(), 230);

                    if (pos1 == detail.getSkuDescr().length()) {
                        item = detail.getSkuDescr();
                    } else {
                        item = detail.getSkuDescr().substring(0, pos1);
                    }
                }

                // 项目
                detailDTO.setItem(item);

                // 单价 数量 金额
                if (pos1 == detail.getSkuDescr().length()) {
                    uom = "          ";
                } else {
                    uom = PrintInvoiceHelper.calAndFillWithBlank(detail.getSkuDescr().substring(pos1), 60);
                }
                DecimalFormat df = new DecimalFormat("###0.00");
                String blankFirst = "  ";
                String preSpace = "    ";
                String defaultChar = " ";
                String unitPrice = StringUtil.convertToString(df.format(detail.getPrice() == null ? BigDecimal.ZERO : detail.getPrice()), 8, defaultChar);// detailDTO.getUnitPrice();
                String qty = StringUtil.convertToString(df.format(detail.getQty() == null ? BigDecimal.ZERO : detail.getQty()), 8, defaultChar);// detailDTO.getQty();
                String totalPrice = StringUtil.convertToString(df.format(detail.getAmount() == null ? BigDecimal.ZERO : detail.getAmount()), 10, defaultChar);// detailDTO.getTotalPrice();
                
                if(pos1 == detail.getSkuDescr().length()){
                    uom = uom + preSpace + unitPrice + preSpace + qty + preSpace + totalPrice;
                }else{
                    if(p.matcher(uom).find()){
                        uom = uom + blankFirst + blankFirst + blankFirst + blankFirst + unitPrice + preSpace + qty + preSpace + totalPrice;
                    }else{
                        uom = uom + blankFirst + unitPrice + preSpace + qty + preSpace + totalPrice;
                    }
                }

                detailDTO.setUom(uom);

                detailDTOs.add(detailDTO);
            }
        }else{
            for (InvoiceDetail detail : invoiceDetails) {
            	if(StringUtil.isEmpty(detail.getSkuDescr())){
            		continue;
            	}
                InvoiceNewDetailDTO detailDTO = new InvoiceNewDetailDTO();
                item = "";
                uom = "";
                int pos1 = 0;
                if (detail.getSkuDescr() != null) {
                    pos1 = PrintInvoiceHelper.cal(detail.getSkuDescr(), 230);

                    if (pos1 == detail.getSkuDescr().length()) {
                        item = detail.getSkuDescr();
                    } else {
                        item = detail.getSkuDescr().substring(0, pos1);
                    }
                }

                // 项目
                detailDTO.setItem(item);

                // 单价 数量 金额
                if (pos1 == detail.getSkuDescr().length()) {
                    // uom = "            ";
                    uom = "                  ";
                } else {
                    uom = PrintInvoiceHelper.calAndFillWithBlank(detail.getSkuDescr().substring(pos1), 60);
                }
                DecimalFormat df = new DecimalFormat("###0.00");
                /*
                 * String blankFirst = "    "; String preSpace = "        "; String
                 * defaultChar = " ";
                 */
                String unitPrice = df.format(detail.getPrice() == null ? BigDecimal.ZERO : detail.getPrice());// detailDTO.getUnitPrice();
                String qty = df.format(detail.getQty() == null ? BigDecimal.ZERO : detail.getQty());// detailDTO.getQty();
                String totalPrice = df.format(detail.getAmount() == null ? BigDecimal.ZERO : detail.getAmount());// detailDTO.getTotalPrice();

                uom = uom + PrintInvoiceHelper.formatStr(unitPrice, qty, totalPrice);

                detailDTO.setUom(uom);

                detailDTOs.add(detailDTO);
            }
        }

        return detailDTOs;
    
    }

    @Override
    public List<String> printTest() {
        // TODO Auto-generated method stub
        return null;
    }
    
    @Override
    public String getOperateUser() {
    	String userName = null;
        if (identity != null && identity.getCredentials() != null) {
            userName = identity.getCredentials().getUsername();
        }
        
        if (StringUtil.isEmpty(userName)) {
        	userName = ParamUtil.getCurrentLoginName();
        }
        
        if (StringUtil.isEmpty(userName)){
        	throw new DeliveryException(DeliveryException.GET_CUR_OPERATE_USER_FAILURE);
        }
        return userName;
    }
}