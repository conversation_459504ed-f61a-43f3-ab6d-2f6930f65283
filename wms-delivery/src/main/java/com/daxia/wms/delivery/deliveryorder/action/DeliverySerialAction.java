package com.daxia.wms.delivery.deliveryorder.action;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.load.entity.TOCrossDockDetail;
import com.daxia.wms.delivery.load.entity.TOCrossDockHeader;
import com.daxia.wms.delivery.load.service.TOCrossDockService;
import com.daxia.wms.stock.stock.entity.StockSerial;
import com.daxia.wms.stock.stock.service.StockSerialService;

/**
 * 补刷序列号Action
 */
@Name("com.daxia.wms.delivery.deliverySerialAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class DeliverySerialAction extends PagedListBean<StockSerial> {
	private static final long serialVersionUID = 4685694973056410603L;
	private String serialNo; // 商品扫描的序列号
    private List<StockSerial> serialList;
    private Long doHeaderId;
    private Long doDetailId;
    private Long skuId;
    private DeliveryOrderHeader doHeader;
    private DeliveryOrderDetail doDetail;
    private TOCrossDockHeader crossDockHeader;
    private TOCrossDockDetail crossDockDetail;
    
    //序列号入库是否成功
    private String successFlag;
    
    @In
    private DeliveryOrderService deliveryOrderService;
    @In
    private StockSerialService stockSerialService;
    
    @In
    private TOCrossDockService toCrossDockService;
    
    public DeliverySerialAction() {
    	this.serialList = new ArrayList<StockSerial>();
    }
    @Override
    public void query() {
    }
    
    /**
     * 获取序列号刷新弹出的stockSerialPopup.xhtml
     */
    public void findSerialList() {
    	this.doHeader = this.deliveryOrderService.getDoHeaderById(this.doHeaderId);
        this.doDetail = deliveryOrderService.getDoDetail(doDetailId);
        this.skuId = doDetail.getSkuId();
    	serialList = stockSerialService.findLackSupplierIdStockSerialBySku(skuId);
    	Collections.sort(serialList, new Comparator<StockSerial>() {
	      @Override
	      public int compare(StockSerial o1, StockSerial o2) {
	    	  if(o1.getUpdatedAt() == null){
  	    		return 1;
  	    	  }else if(o2.getUpdatedAt() == null ){
  	    		return -1;
  	    	  }else{
  	    		return -(o1.getUpdatedAt().compareTo(o2.getUpdatedAt()));
  	    	  }
	      	}
    	 });
    	this.serialNo = null;
    }
    
    /**
     * 获取序列号刷新弹出的stockSerialPopup.xhtml
     */
    public void findCDSerialList() {
    	this.crossDockHeader = this.toCrossDockService.get(this.doHeaderId);
        this.crossDockDetail = toCrossDockService.getCrossDockDetail(doDetailId);
        this.skuId = crossDockDetail.getSkuId();
    	serialList = stockSerialService.findLackSupplierIdStockSerialBySku(skuId);
    	Collections.sort(serialList, new Comparator<StockSerial>() {
	      @Override
	      public int compare(StockSerial o1, StockSerial o2) {
	    	  if(o1.getUpdatedAt() == null){
  	    		return 1;
  	    	  }else if(o2.getUpdatedAt() == null ){
  	    		return -1;
  	    	  }else{
  	    		return -(o1.getUpdatedAt().compareTo(o2.getUpdatedAt()));
  	    	  }
	      	}
    	 });
    	this.serialNo = null;
    }
    
    /**
     * 补刷序列号操作
     */
    public void reAddSerialNo() {		
    	//序列号入库是否成功标识符初始化
    	successFlag = "0";
		if (serialNo == null || serialNo.trim().length() == 0) {
			throw new DeliveryException(DeliveryException.SERIAL_NO_NOT_EXIST);
		}		
		StockSerial stockSerial = stockSerialService.findBySerialNo(this.serialNo);
		if (stockSerial == null) {
			//增加序列号库存
			stockSerial = stockSerialService.createStockSerial(skuId, serialNo);
			this.getSerialList().add(stockSerial);
			//逆序排列
			Collections.sort(serialList, new Comparator<StockSerial>() {
		      @Override
		      public int compare(StockSerial o1, StockSerial o2) {
		    	  if(o1.getUpdatedAt() == null){
	    	    		return 1;
	    	    	  }else if(o2.getUpdatedAt() == null ){
	    	    		return -1;
	    	    	  }else{
	    	    		return -(o1.getUpdatedAt().compareTo(o2.getUpdatedAt()));
	    	    	  }
		      	  }
	    	 });
		    this.serialNo = null;
		}else if (!skuId.equals(stockSerial.getSkuId())) {
			throw new DeliveryException(DeliveryException.SERIAL_NO_NOT_MATCH);  
		}else if (stockSerial.getQty().compareTo(0) == 0){
			stockSerial.setQty(1);
			stockSerialService.saveSerialNo(stockSerial);
			this.getSerialList().add(stockSerial);
			//逆序排列
			Collections.sort(serialList, new Comparator<StockSerial>() {
		      @Override
		      public int compare(StockSerial o1, StockSerial o2) {
		    	  if(o1.getUpdatedAt() == null){
	    	    		return 1;
	    	    	  }else if(o2.getUpdatedAt() == null ){
	    	    		return -1;
	    	    	  }else{
	    	    		return -(o1.getUpdatedAt().compareTo(o2.getUpdatedAt()));
	    	    	  }
		      	  }
	    	 });
		    this.serialNo = null;
		}else{
			throw new DeliveryException(DeliveryException.SERIAL_NO_DUPLICATED); 
		}
		//序列号补刷业务逻辑执行完成，标识符置为 “1”
		successFlag = "1";
		this.sayMessage(MESSAGE_SUCCESS);
	}
    
    /**
     * 删除序列号库存
     */
	public void reRemoveSerialNo(){
		if (serialNo == null || serialNo.trim().length() == 0) {
			throw new DeliveryException(DeliveryException.SERIAL_NO_NOT_EXIST);
		}		
		StockSerial stockSerial = stockSerialService.findBySerialNo(this.serialNo);
		if (stockSerial == null || stockSerial.getQty().compareTo(0) == 0) {
			throw new DeliveryException(DeliveryException.SERIAL_NO_NOT_EXIST);
		}else{
			stockSerial.setQty(0);
			stockSerialService.saveSerialNo(stockSerial);
		}
    	Iterator<StockSerial> it = this.getSerialList().iterator();
        while (it.hasNext()) {
        	StockSerial stockserial = it.next();
            if (this.getSerialNo().equals(stockserial.getSerialNo())) {
                it.remove();
                break;
            }
        }
        this.sayMessage(MESSAGE_SUCCESS);
	}
	public String getSerialNo() {
		return serialNo;
	}
	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}
	public List<StockSerial> getSerialList() {
		return serialList;
	}
	public void setSerialList(List<StockSerial> serialList) {
		this.serialList = serialList;
	}
	public Long getDoHeaderId() {
		return doHeaderId;
	}
	public void setDoHeaderId(Long doHeaderId) {
		this.doHeaderId = doHeaderId;
	}
	public Long getDoDetailId() {
		return doDetailId;
	}
	public void setDoDetailId(Long doDetailId) {
		this.doDetailId = doDetailId;
	}
	public DeliveryOrderHeader getDoHeader() {
		return doHeader;
	}
	public void setDoHeader(DeliveryOrderHeader doHeader) {
		this.doHeader = doHeader;
	}
	public DeliveryOrderDetail getDoDetail() {
		return doDetail;
	}
	public void setDoDetail(DeliveryOrderDetail doDetail) {
		this.doDetail = doDetail;
	}
	public String getSuccessFlag() {
		return successFlag;
	}
	public void setSuccessFlag(String successFlag) {
		this.successFlag = successFlag;
	}
	public Long getSkuId() {
		return skuId;
	}
	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}
	public TOCrossDockHeader getCrossDockHeader() {
		return crossDockHeader;
	}
	public void setCrossDockHeader(TOCrossDockHeader crossDockHeader) {
		this.crossDockHeader = crossDockHeader;
	}
	public TOCrossDockDetail getCrossDockDetail() {
		return crossDockDetail;
	}
	public void setCrossDockDetail(TOCrossDockDetail crossDockDetail) {
		this.crossDockDetail = crossDockDetail;
	}
	
}