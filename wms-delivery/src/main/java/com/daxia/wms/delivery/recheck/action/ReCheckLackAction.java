package com.daxia.wms.delivery.recheck.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.pick.dto.PickLackDTO;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.pick.filter.PickTaskFilter;
import com.daxia.wms.delivery.pick.service.PickService;
import com.daxia.wms.delivery.pick.service.PickTaskService;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 核捡缺发处理
 */
@Name("com.daxia.wms.delivery.reCheckLackAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ReCheckLackAction extends PagedListBean<PickLackDTO> {

    private static final long serialVersionUID = -8277272791377861759L;

    private PickTaskFilter pickTaskFilter;
    @In
    private PickTaskService pickTaskService;

    @In
    private PickService pickService;

    public ReCheckLackAction() {
        pickTaskFilter = new PickTaskFilter();
    }

    public void initReCheckLackPage() {
        if (this.pickTaskFilter == null) {
            this.pickTaskFilter = new PickTaskFilter();
        }
    }

    @Override
    public void query() {
        if (this.pickTaskFilter == null) {
            this.pickTaskFilter = new PickTaskFilter();
        }

        DataPage<PickTask> dataPage = this.pickTaskService.query(this.pickTaskFilter, this.getStartIndex(), this.getPageSize());
        List<PickTask> taskList = dataPage.getDataList();
        List<PickLackDTO> dataList = new ArrayList<PickLackDTO>();
        for (PickTask pickTask : taskList) {
            PickLackDTO dto = new PickLackDTO();
            BeanUtils.copyProperties(pickTask, dto);
            dto.setRecheckQty(pickTask.getPickedQty());
            dataList.add(dto);
        }
        DataPage<PickLackDTO> pickLackDTODataPage = new DataPage();
        pickLackDTODataPage.setDataList(dataList);
        pickLackDTODataPage.setPageCount(dataPage.getPageCount());
        pickLackDTODataPage.setTotalCount(dataPage.getTotalCount());
        pickLackDTODataPage.setSumInfo(dataPage.getSumInfo());
        this.populateValues(pickLackDTODataPage);
    }

    public void updateRecheckQty() {
        List<Object> taskList = getSelectedRowList();
        if (taskList.isEmpty()) {
            throw new DeliveryException("拣货明细为空!");
        }
        for (Object object : taskList) {
            PickLackDTO t = (PickLackDTO) object;
            PickTask task = pickTaskService.getTaskById(t.getId());
            if (null == task) {
                throw new DeliveryException("拣货明细不存在!");
            }
            DeliveryOrderHeader header = task.getDoHeader();
            if (!header.getStatus().equals(Constants.DoStatus.ALLSORTED.getValue()) && !task.getStatus().equals(Constants.DoStatus.ALLPICKED.getValue())) {
                throw new DeliveryException("拣货单状态不正确,无法修改数量!");
            }
            if(t.getRecheckQty().compareTo(task.getPickedQty())>0){
                throw new DeliveryException("修改数量不能大于已拣货数量!");
            }else if(t.getRecheckQty().compareTo(task.getPickedQty())==0){
                continue;
            }
            pickService.updatePickQty(task,t.getRecheckQty(),null);
        }
        query();
    }

    public PickTaskFilter getPickTaskFilter() {
        return pickTaskFilter;
    }

    public void setPickTaskFilter(PickTaskFilter pickTaskFilter) {
        this.pickTaskFilter = pickTaskFilter;
    }


}