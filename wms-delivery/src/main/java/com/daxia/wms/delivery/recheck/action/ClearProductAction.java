package com.daxia.wms.delivery.recheck.action;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.OrderFrozenException;
import com.daxia.wms.delivery.recheck.dto.ReCheckCartonDetail;
import com.daxia.wms.delivery.recheck.dto.ReCheckRecord;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.ClearReCheckRecordService;

/**
 * 对已装箱的商品逐件进行清除，clearProduct.xhtml页面调用
 */
@Name("com.daxia.wms.delivery.clearProductAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ClearProductAction implements Serializable{
	private static final long serialVersionUID = 1L;
	@In
	private ClearReCheckRecordService clearReCheckRecordService;
	
	private Long orderId;

	private String orderNo;

	private String cartonNo;

	private Long cartonId;

	private String productBarCode;

	/**
	 * 商品序列号，有些商品需要，是否需要录入需要进么判断
	 */
	private String serialNo;
	
	/**
	 * 商品是否需要录入序列号
	 */
	private boolean needSerial;

	/**
	 * 是否清除成功
	 */
	private boolean clearSuccess;

	/**
	 * 是否级联删除了装箱头信息。如果删除商品时发现该箱中已无其它数据，则删除装箱头信息
	 */
	private boolean cascadeDeleteCarton;

	private String errorMessage;

	/**
	 * 商品对应的装箱明细，用于在界面中显示
	 */
	private ReCheckCartonDetail reCheckCartonDetail;
	
	/**
	 * 商品对应的装箱明细，用于缓存以便进行一些操作
	 */
	private List<ReCheckCartonDetail> tmpCheckCartonDetails;

	/**
	 * 序列号Id，删除商品时如果存在序列号，则同时删除该序列号
	 */
	private Long trsSerialLogId;

	/**
	 * 待删除的商品Id
	 */
	private Long clearedProductId;
	
	/**
	 * 是否存在一码多品
	 */
	private boolean muitiProduct;
	
	/**
	 * 具有指定的相同条码的产品列表，一码多品时显示该列表供用户选择
	 */
	private List<ReCheckRecord> productListWithOneBarCode;
	
	/**
	 * 一码多品的情况下选中的商品id
	 */
	private Long selectedProductId;

	public boolean isClearSuccess() {
		return clearSuccess;
	}

	public void setClearSuccess(boolean clearSuccess) {
		this.clearSuccess = clearSuccess;
	}
	
	public boolean isCascadeDeleteCarton() {
		return cascadeDeleteCarton;
	}

	public void setCascadeDeleteCarton(boolean cascadeDeleteCarton) {
		this.cascadeDeleteCarton = cascadeDeleteCarton;
	}

	public Long getClearedProductId() {
		return clearedProductId;
	}

	public void setClearedProductId(Long clearedProductId) {
		this.clearedProductId = clearedProductId;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public Long getCartonId() {
		return cartonId;
	}

	public void setCartonId(Long cartonId) {
		this.cartonId = cartonId;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getCartonNo() {
		return cartonNo;
	}

	public void setCartonNo(String cartonNo) {
		this.cartonNo = cartonNo;
	}

	public String getProductBarCode() {
		return productBarCode;
	}

	public void setProductBarCode(String productBarCode) {
		this.productBarCode = productBarCode;
	}

	public String getSerialNo() {
		return serialNo;
	}

	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}

	public boolean isNeedSerial() {
		return needSerial;
	}

	public void setNeedSerial(boolean needSerial) {
		this.needSerial = needSerial;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public ReCheckCartonDetail getReCheckCartonDetail() {
		return reCheckCartonDetail;
	}

	public boolean isMuitiProduct() {
		return muitiProduct;
	}

	public void setMuitiProduct(boolean muitiProduct) {
		this.muitiProduct = muitiProduct;
	}

	public List<ReCheckRecord> getProductListWithOneBarCode() {
		return productListWithOneBarCode;
	}

	public void setProductListWithOneBarCode(
			List<ReCheckRecord> productListWithOneBarCode) {
		this.productListWithOneBarCode = productListWithOneBarCode;
	}

	public Long getSelectedProductId() {
		return selectedProductId;
	}

	public void setSelectedProductId(Long selectedProductId) {
		this.selectedProductId = selectedProductId;
	}

	/**
	 * 根据定单号和cartonNo，初始化装箱头信息Id，如果为null，则表示录入的cartonNo无效。
	 * 
	 * @param orderId
	 * @param cartonNo
	 * @return 返回cartonNo对应的装箱头信息Id，此外:null表示无对应的数据
	 */
	private Long initCartonId(String cartonNo) {
		CartonHeader cartonHeader = clearReCheckRecordService.findCartonHeader(cartonNo);
		if(cartonHeader != null) {
			cartonId = cartonHeader.getId();
			orderId = cartonHeader.getDoHeader().getId();
			orderNo = cartonHeader.getDoHeader().getDoNo();
		}
		return cartonId;
	}

	/**
	 * 根据装箱头信息Id和商品条码返回商品对应的核拣记录
	 * 
	 * @param cartonId
	 * @param productBarCode
	 * @return
	 */
	private List<ReCheckCartonDetail> initReCheckCartonDetail(Long cartonId,
			String productBarCode) {
		List<ReCheckCartonDetail> result;
		result = clearReCheckRecordService.findReCheckCartonDetail(cartonId,
				productBarCode);
		return result;
	}
	
	/**
	 * 判断商品是否需要进么序列号扫描
	 * 
	 * @param orderId
	 * @param cartonId
	 * @param productId
	 * @return
	 */
	private boolean initNeedSerial(Long orderId, Long cartonId, ReCheckCartonDetail cartonDetail){		
		return clearReCheckRecordService.needScanSerial(orderId, cartonId, cartonDetail.getProductId());
	}
	
	/**
	 * 对输入的cartonNo, productBarCode进行检验，判断是否合法，并且是否存在一码多品的情况
	 */
	public void checkProduct(){
	    muitiProduct = false;
		reCheckCartonDetail = null;
		productListWithOneBarCode = null;
		orderId = null;
		orderNo = "";
		cartonId = initCartonId(cartonNo);
		if (cartonId == null) {
			errorMessage = "recheck.carton.notexist";
			return;
		}
		tmpCheckCartonDetails = initReCheckCartonDetail(cartonId, productBarCode);
		if (tmpCheckCartonDetails == null || tmpCheckCartonDetails.isEmpty()) {
			errorMessage = "recheck.product_carton.notmatch";
			return;
		}
		if(tmpCheckCartonDetails.size() > 1){
			muitiProduct = true;
			productListWithOneBarCode = new ArrayList<ReCheckRecord>(tmpCheckCartonDetails.size());
			for(ReCheckCartonDetail cartonDetail: tmpCheckCartonDetails){
				productListWithOneBarCode.add(cartonDetail.getRecord());
			}
		}else{
			selectedProductId = tmpCheckCartonDetails.get(0).getProductId();
		}
		errorMessage = "";
	}
	
	/**
	 * 存在一码多品时，用户从列表中选择商品后，调用此方法确定选择
	 */
	public void selectProduct(){
		errorMessage = "";
		if(getReCheckCartonDetailByProductId(selectedProductId) == null){
			errorMessage = "recheck.product.notexist";
		}
	}

	/**
	 * 检查是否需要进行扫描序列号
	 */
	public void checkSerial() {
		reCheckCartonDetail = null;
		ReCheckCartonDetail tmpCheckCartonDetail = getReCheckCartonDetailByProductId(selectedProductId);
		needSerial = initNeedSerial(orderId,
				cartonId, tmpCheckCartonDetail);
	}
	
	/**
	 * 根据商品找到商品对应的核检明细
	 * 
	 * @param productId
	 * @return
	 */
	private ReCheckCartonDetail getReCheckCartonDetailByProductId(Long productId){//TODO
		for(ReCheckCartonDetail cartonDetail: tmpCheckCartonDetails){
			if(cartonDetail.getProductId().equals(selectedProductId)){
				return cartonDetail;
			}
		}
		return null;
	}

	/**
	 * 录入序列号后（如果需要的话），调用此方法查询Product
	 */
	public void listProduct() {
		ReCheckCartonDetail tmpCheckCartonDetail = getReCheckCartonDetailByProductId(selectedProductId);
		if (!needSerial) {
			reCheckCartonDetail = tmpCheckCartonDetail;
			tmpCheckCartonDetails = null;
			return;
		}
		if (serialNo == null) {
			errorMessage = "recheck.serial.required";
			return;
		}
		trsSerialLogId = getTrsSerialLogId(tmpCheckCartonDetail.getProductId(), serialNo);
		if (trsSerialLogId == null) {
			errorMessage = "recheck.serial_carton.notmatch";
			return;
		}
		reCheckCartonDetail = tmpCheckCartonDetail;
		tmpCheckCartonDetails = null;
		errorMessage = "";
	}
	
	/**
	 * 查找序列号交易日志id
	 * @param productId
	 * @param serialNo
	 * @return
	 */
	private Long getTrsSerialLogId(Long productId, String serialNo){
		return  clearReCheckRecordService.findTrsSerialLogId(orderId,
				cartonId, productId, serialNo);	
		
	}

	/**
	 * 流程的最后一步，调用此方法进行清除
	 */
	public void doClear() {
	    errorMessage = null;
		clearSuccess = false;
		clearedProductId = null;
		if (reCheckCartonDetail == null) {
			return;
		}
		try {
			cascadeDeleteCarton = clearReCheckRecordService.clearProduct(orderId, cartonId,
				reCheckCartonDetail.getProductId(), trsSerialLogId);
		}catch(OrderFrozenException e){
			errorMessage = "recheck.do.frozen.error";
			return;
		}catch(DeliveryException e){
			errorMessage = e.getMessage();
			return;
		}catch (Exception e) {
			log.error(e.getMessage(), e);
			errorMessage = "recheck.syserror";
			return;
		}
		clearedProductId = reCheckCartonDetail.getProductId();
		cartonNo = "";
		productBarCode = "";
		serialNo = "";
		orderNo = "";
		reCheckCartonDetail = null;
		muitiProduct = false;
		productListWithOneBarCode = null;
		errorMessage = "";
		clearSuccess = true;
	}

}
