package com.daxia.wms.delivery.load.action;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.faces.FacesMessages;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.ResourceUtils;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.framework.system.constants.Constants.YesNo;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.load.dto.CrossDockDTO;
import com.daxia.wms.delivery.load.dto.CrossDockPackingDTO;
import com.daxia.wms.delivery.load.dto.StockCrossDockDTO;
import com.daxia.wms.delivery.load.entity.CrossDockPacking;
import com.daxia.wms.delivery.load.entity.TOCrossDockHeader;
import com.daxia.wms.delivery.load.filter.CrossDockPackingFilter;
import com.daxia.wms.delivery.load.service.CrossDockDetailService;
import com.daxia.wms.delivery.load.service.CrossDockPackingService;
import com.daxia.wms.delivery.load.service.TOCrossDockService;
import com.daxia.wms.delivery.print.service.PrintCrossDockCartonService;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.CrossDockStatus;
import com.daxia.wms.Constants.DocType;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.stock.stock.entity.StockSerial;
import com.daxia.wms.stock.stock.service.StockSerialService;

/**
 * 功能说明：越库装箱
 */
@SuppressWarnings("serial")
@Name("com.daxia.wms.delivery.crossDockPackingAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class CrossDockPackingAction extends PagedListBean<CrossDockPacking> {

    @In
    private StockSerialService stockSerialService;

    @In
    private CrossDockPackingService crossDockPackingService;

    @In
    private CrossDockDetailService crossDockDetailService;

    @In
    private PrintCrossDockCartonService printCrossDockCartonService;

    private CrossDockPackingFilter crossDockPackingFilter;

    private CrossDockPacking crossDockPacking;

    private List<CrossDockPacking> crossDockPackingList;

    // LPN单号
    private String lpnNo;

    // 越库发货DTO
    private CrossDockDTO crossDockDTO;

    // 商品条码
    private String productCode;

    private String scannedSerialNo;

    // 未扫描序列号产品数量
    private int notScannedSerialNum;

    // 扫描序列号产品数量
    private int scannedSerialNum;

    private String deleteSerialNo;

    private String serialIdNo;

    //按商品保存序列号数据
    private Map<Long, List<String>> totalSerials;

    private int boxNum;

    private int isMultiLpn;

    private List<CrossDockPackingDTO> crossDockPackingDTOList;

    private CrossDockPackingDTO crossDockPackingDTO;

    @In
    private TOCrossDockService toCrossDockService;

    @In
    private ExpFacadeService expFacadeService;

    private List<String> scannLpnList;

    private StockCrossDockDTO stockCrossDockDTO;

    //箱标签打印数据
    private PrintData printData = new PrintData();
    //越库装箱实体id
    private List<Long> ids = new ArrayList<Long>();


    //是否自动打印箱标签
    private String isAutoPrintCarton;

    @Override
    public void query() {
        crossDockPackingFilter.getOrderByMap().put("createdAt", "desc");
        this.buildOrderFilterMap(crossDockPackingFilter);
        DataPage<CrossDockPacking> dataPage = crossDockPackingService
                .queryCDHeaderPageInfo(crossDockPackingFilter, getStartIndex(), getPageSize());
        this.populateValues(dataPage);
    }

    /**
     * 初始化页面
     */
    public void initPage() {
        this.crossDockPackingFilter = new CrossDockPackingFilter();
    }

    /**
     * 检查LPN状态并初始化及装载分录数据
     */
    public void doCheckLpnStatus() {
        crossDockPackingDTO = null;
        if (scannLpnList == null) {
            scannLpnList = new ArrayList<String>();
        }
        if (scannLpnList.contains(lpnNo)) {
            this.lpnNo = null;
            throw new DeliveryException(DeliveryException.NOTALLOW_LPN_HASSCANNED);
        }
        // 检查LPN是否存在
        List<StockCrossDockDTO> stockCrossDockList = crossDockDetailService.getCrossDockDetailDTOByLPN(null, lpnNo, false);
        // 如果LPN不存则则给予提示
        if (stockCrossDockList.size() == 0) {
            this.lpnNo = null;
            throw new DeliveryException(DeliveryException.LPN_NOT_SUITFOR_CROSSDOCK);
        }
        //检查装箱数量，如果为0则不予发送
        BigDecimal totalQty = BigDecimal.ZERO;
        for (StockCrossDockDTO curStockCrossDockDTO : stockCrossDockList) {
            totalQty = totalQty.add(curStockCrossDockDTO.getQty());
        }
        if (totalQty.intValue() == 0) {
            this.lpnNo = null;
            throw new DeliveryException(DeliveryException.ERROR_LPN_HASDUMPNOTPACKING);
        }
        if (Constants.StockCDStatus.SHIPPED.getValue().equals(stockCrossDockList.get(0).getStatus())) {
            this.lpnNo = null;
            throw new DeliveryException(DeliveryException.NOTALLOW_DELIVERY_BYSCANNEDLPN);
        }
        if (crossDockDTO == null) {
            crossDockDTO = crossDockDetailService.getStockCrossDockDTOByLpnNo(lpnNo, false);
            if (this.lpnNo.startsWith("CV")) {
                crossDockDTO.setValuableFlag(YesNo.YES.getValue());
            } else {
                crossDockDTO.setValuableFlag(YesNo.NO.getValue());
            }
        } else {
            //若存在，则检查贵品表示和目的仓库是否一致
            CrossDockDTO newCrossDockDTO = crossDockDetailService.getStockCrossDockDTOByLpnNo(lpnNo, false);
            if (this.lpnNo.startsWith("CV")) {
                newCrossDockDTO.setValuableFlag(YesNo.YES.getValue());
            } else {
                newCrossDockDTO.setValuableFlag(YesNo.NO.getValue());
            }
            if (!newCrossDockDTO.getToWhId().equals(crossDockDTO.getToWhId())) {
                this.lpnNo = null;
                throw new DeliveryException(DeliveryException.LPN_ERROR_WAREHOUSENOTMATCH);
            } else if (!newCrossDockDTO.getValuableFlag().equals(crossDockDTO.getValuableFlag())) {
                this.lpnNo = null;
                throw new DeliveryException(DeliveryException.LPN_ERROR_VALUEABLEFLAGNOTMATCH);
            }
            crossDockDTO = newCrossDockDTO;
        }
        if (ListUtil.isNullOrEmpty(crossDockPackingDTOList)) {
            crossDockPackingDTOList = new ArrayList<CrossDockPackingDTO>();
        }
        CrossDockPackingDTO crossDockPackingDTO = new CrossDockPackingDTO();
        CrossDockPacking crossDockPacking = new CrossDockPacking();
        crossDockPacking.setLpnNo(this.lpnNo);
        crossDockPacking.setCdHeaderNo(crossDockDTO.getDoNo());
        crossDockPacking.setCdHeaderId(crossDockDTO.getToCrossDockId());
        crossDockPacking.setAsnNo(crossDockDTO.getAsnNo());
        crossDockPacking.setRefNo1(crossDockDTO.getRefNo1());
        crossDockPacking.setPoNo(stockCrossDockList.get(0).getAsnferNo1());
        if (StringUtil.isNotEmpty(crossDockDTO.getToWhId())) {
            crossDockPacking.setToWarehouseId(Long.valueOf(crossDockDTO.getToWhId()));
        }
        crossDockPacking.setToWarehouseName(crossDockDTO.getToWarehouseName());
        crossDockPacking.setValuableFlag(crossDockDTO.getValuableFlag());
        // 添加到已扫描序列号记录中
        crossDockPackingDTO.setStockCrossDockDTOList(stockCrossDockList);
        crossDockPackingDTO.setCrossDockPacking(crossDockPacking);
        this.crossDockPackingDTO = crossDockPackingDTO;
        //判断已缓存到页面中的扫描LPN信息，是否在当前越库发货单DTO中
        for (String strLpn : this.scannLpnList) {
            for (String needScanLpn : crossDockDTO.getNeedScannedLpnNo().split(",")) {
                if (strLpn.equals(needScanLpn)) {
                    if (StringUtil.isEmpty(crossDockDTO.getScannedLpnNo())) {
                        crossDockDTO.setScannedLpnNo(strLpn);
                    } else {
                        crossDockDTO.setScannedLpnNo(crossDockDTO.getScannedLpnNo() + "," + strLpn);
                    }
                }
            }
        }
    }

    /**
     * 删除LPN并清除序列号缓存数据
     */
    public void deleteByLpn() {
        String newLpn = "";
        for (CrossDockPackingDTO crossDockPackingDTO : crossDockPackingDTOList) {
            if (crossDockPackingDTO.getCrossDockPacking().getLpnNo().equals(this.lpnNo)) {
                crossDockPackingDTOList.remove(crossDockPackingDTO);
                //如果存在序列号则予以删除
                for (StockCrossDockDTO stockCrossDockDTO : crossDockPackingDTO.getStockCrossDockDTOList()) {
                    if (stockCrossDockDTO.getSerialNos() != null) {
                        for (String strLpn : stockCrossDockDTO.getSerialNos()) {
                            totalSerials.get(stockCrossDockDTO.getSkuId()).remove(strLpn);
                        }
                    }
                }
                break;
            }
            if (newLpn == "") {
                newLpn = crossDockPackingDTO.getCrossDockPacking().getLpnNo();
            } else {
                newLpn = newLpn + "," + crossDockPackingDTO.getCrossDockPacking().getLpnNo();

            }
        }
        if (ListUtil.isNullOrEmpty(crossDockPackingDTOList)) {
            crossDockDTO = null;
        } else {
            crossDockDTO.setScannedLpnNo(newLpn);
        }
        scannLpnList.remove(this.lpnNo);
        this.lpnNo = null;
    }

    /**
     * 通过LPN修改具体装箱数据
     */
    public void editDetailInfo() {
        for (CrossDockPackingDTO curCrossDockPackingDTO : crossDockPackingDTOList) {
            if (curCrossDockPackingDTO.getCrossDockPacking().getLpnNo().equals(lpnNo)) {
                this.crossDockPackingDTO = curCrossDockPackingDTO;
                break;
            }
        }
    }

    /**
     * 通过LPN修改具体装箱数据
     */
    public void viewDetailInfo() {
        List<StockCrossDockDTO> stockCrossDockList = crossDockDetailService.getCrossDockDetailDTOByLPN(null,lpnNo, true);
        this.crossDockPackingDTO = new CrossDockPackingDTO();
        this.crossDockPackingDTO.setStockCrossDockDTOList(stockCrossDockList);
        this.crossDockPackingDTO.setCrossDockPacking(crossDockPacking);

    }

    /**
     * 退出当前扫描LPN
     */
    public void exitDetailInfo() {
        this.crossDockPackingDTO = null;
        if (ListUtil.isNullOrEmpty(this.crossDockPackingDTOList)) {
            crossDockDTO = null;
        }
        this.lpnNo = null;
    }

    /**
     * 输入商品条码
     */
    public void queryProductCode() {
        boolean isExist = false;
        Long skuId = null;
        List<StockCrossDockDTO> editStockCrossDockDTO = new ArrayList<StockCrossDockDTO>();
        for (StockCrossDockDTO stockCrossDockDTO : crossDockPackingDTO.getStockCrossDockDTOList()) {
            if (StringUtil.isNotEmpty(stockCrossDockDTO.getEan13())) {
                for (String productBarCode : stockCrossDockDTO.getEan13().split(",")) {
                    if (this.productCode.equals(productBarCode)) {
                        if (YesNo.YES.getValue().equals(stockCrossDockDTO.getIsScanned())) {
                            this.productCode = null;
                            throw new DeliveryException(DeliveryException.ERROR_PRODUCTCODE_HASSCANNED);
                        }
                        isExist = true;
                        stockCrossDockDTO.setIsScanned(YesNo.YES.getValue());
                        editStockCrossDockDTO.add(stockCrossDockDTO);
                        if (skuId == null) {
                            skuId = stockCrossDockDTO.getSkuId();
                        } else if (!skuId.equals(stockCrossDockDTO.getSkuId())) {
                            this.productCode = null;
                            for (StockCrossDockDTO editStockCrossDock : editStockCrossDockDTO) {
                                editStockCrossDock.setIsScanned(YesNo.NO.getValue());
                            }
                            throw new DeliveryException(DeliveryException.ERROR_CODE_MATCHMAYPRODUCT);
                        }
                    }
                }
            }
        }
        if (!isExist) {
            for (StockCrossDockDTO stockCrossDockDTO : crossDockPackingDTO.getStockCrossDockDTOList()) {
                if (this.productCode.equals(stockCrossDockDTO.getProductCode())) {
                    if (YesNo.YES.getValue().equals(stockCrossDockDTO.getIsScanned())) {
                        this.productCode = null;
                        throw new DeliveryException(DeliveryException.ERROR_PRODUCTCODE_HASSCANNED);
                    }
                    isExist = true;
                    stockCrossDockDTO.setIsScanned(YesNo.YES.getValue());
                }
            }
        }
        this.productCode = null;
        if (!isExist) {
            throw new DeliveryException(DeliveryException.PRODUCTCODE_ERROR_PLEASERINPUTAGAIN);
        }
        validateLpn();
    }

    /**
     * 检查LPN数据是否全部输入完成
     */
    public void validateLpn() {
        if (null == this.crossDockPackingDTO) {
            return;
        }
        for (StockCrossDockDTO stockCrossDockDTO : this.crossDockPackingDTO.getStockCrossDockDTOList()) {
            //处理脏数据，正常应该不存在
            if (stockCrossDockDTO.getQty().intValue() == 0) {
                continue;
            }
            //判断是否扫描
            if (!YesNo.YES.getValue().equals(stockCrossDockDTO.getIsScanned())) {
                return;
            }
            //判断是否需要序列号
            if (stockCrossDockDTO.getSn_qty().intValue() == YesNo.YES.getValue()
                    && (ListUtil.isNullOrEmpty(stockCrossDockDTO.getSerialNos()) ||
                    stockCrossDockDTO.getSerialNos().size() != stockCrossDockDTO.getQty().intValue())) {
                return;
            }
        }
        //若缓存LPN为空，则新增一个，若缓存LPN包含当前LPN数据，则直接退出
        if (ListUtil.isNullOrEmpty(crossDockPackingList)) {
            crossDockPackingList = new ArrayList<CrossDockPacking>();
        } else {
            if (crossDockPackingList.contains(this.crossDockPacking)) {
                return;
            }
        }
        if (!crossDockPackingDTOList.contains(this.crossDockPackingDTO)) {
            crossDockPackingDTOList.add(this.crossDockPackingDTO);
        }
        crossDockPackingDTO = null;
        if (StringUtil.isEmpty(crossDockDTO.getScannedLpnNo())) {
            crossDockDTO.setScannedLpnNo(lpnNo);
        } else {
            boolean isNotContent = true;
            for (String strLpn : crossDockDTO.getScannedLpnNo().split(",")) {
                if (strLpn.equals(lpnNo)) {
                    isNotContent = false;
                }
            }
            if (isNotContent) {
                crossDockDTO.setScannedLpnNo(crossDockDTO.getScannedLpnNo() + "," + lpnNo);
            }
        }
        if (!scannLpnList.contains(lpnNo)) {
            scannLpnList.add(lpnNo);
        }
        this.lpnNo = null;
    }

    /**
     * 初始化序列号信息
     */
    public void initSerialInfo() {
        // 初始化序列号到序列号录入界面
        for (StockCrossDockDTO curStockCrossDockDTO : crossDockPackingDTO.getStockCrossDockDTOList()) {
            if (curStockCrossDockDTO.getId().toString().equals(serialIdNo)) {
                this.stockCrossDockDTO = curStockCrossDockDTO;
                this.stockCrossDockDTO.setAsnNo(crossDockDTO.getAsnNo());
            }
        }
        // 设置ASN编号属性，供序列号录入界面使用
        if (stockCrossDockDTO != null) {
            scannedSerialNum = (stockCrossDockDTO.getSerialNos() == null) ? 0 : stockCrossDockDTO.getSerialNos().size();
            notScannedSerialNum = stockCrossDockDTO.getQty().intValue() - scannedSerialNum;
        }
    }

    /**
     * 检查序列号是否可用，然后添加到当前需要扫描的序列号中
     */
    public void validateSerial() {
        if (notScannedSerialNum == 0) {
            scannedSerialNo = null;
            throw new DeliveryException(DeliveryException.SERIAL_ALL_HASSCANNED);
        } else if (StringUtil.isEmpty(scannedSerialNo)) {
            throw new DeliveryException(DeliveryException.NOTALLOW_SERIAL_NULL);
        }
        if (totalSerials == null) {
            totalSerials = new HashMap<Long, List<String>>();
        }
        if (ListUtil.isNotEmpty(totalSerials.get(this.stockCrossDockDTO.getSkuId()))) {
            for (String serialNo : totalSerials.get(this.stockCrossDockDTO.getSkuId())) {
                if (serialNo.equals(scannedSerialNo)) {
                    throw new DeliveryException(DeliveryException.NOTALLOW_SCAN_BYSANNEDSERIAL);
                }
            }
        } else {
            totalSerials.put(this.stockCrossDockDTO.getSkuId(), new ArrayList<String>());
        }
        StockSerial stockSerial = stockSerialService.findBySerialNoAndSku(scannedSerialNo, this.stockCrossDockDTO.getSkuId(), null);
        if (stockSerial == null || stockSerial.getQty() == 0) {
            scannedSerialNo = null;
            throw new DeliveryException(DeliveryException.SERIAL_NOTEXIST_OR_ISALREADYUSED);
        }
        if (ListUtil.isNullOrEmpty(this.stockCrossDockDTO.getSerialNos())) {
            this.stockCrossDockDTO.setSerialNos(new ArrayList<String>(0));
        }
        this.stockCrossDockDTO.getSerialNos().add(stockSerial.getSerialNo());
        totalSerials.get(this.stockCrossDockDTO.getSkuId()).add(stockSerial.getSerialNo());
        updateSerialDTO();
        scannedSerialNo = null;
    }

    /**
     * 更新该条记录DTO数据，绑定对应的序列号
     */
    private void updateSerialDTO() {
        scannedSerialNum = (this.stockCrossDockDTO.getSerialNos() == null) ?
                0 : this.stockCrossDockDTO.getSerialNos().size();
        notScannedSerialNum = stockCrossDockDTO.getQty().intValue() - scannedSerialNum;
        validateLpn();
    }

    public void validatePackingInfo() {
        String checkLpn = "";
        boxNum = 1;
        isMultiLpn = 0;
        for (int i = 0, len = crossDockPackingDTOList.size(); i < len; i++) {
            CrossDockPacking curCrossDockPacking = crossDockPackingDTOList.get(i).getCrossDockPacking();
            if (checkLpn == "") {
                checkLpn = curCrossDockPacking.getLpnNo();
            } else if (!checkLpn.equals(curCrossDockPacking.getLpnNo())) {
                isMultiLpn = 1;
            }
            for (StockCrossDockDTO stockCrossDockDTO : crossDockPackingDTOList.get(i).getStockCrossDockDTOList()) {
                if (YesNo.NO.getValue().equals(stockCrossDockDTO.getIsScanned())) {
                    throw new DeliveryException(DeliveryException.ERROR_LPN_NOTSCANNED, (i + 1));
                }
                if (stockCrossDockDTO.getSn_qty().intValue() == YesNo.YES.getValue()
                        && (ListUtil.isNullOrEmpty(stockCrossDockDTO.getSerialNos()) ||
                        stockCrossDockDTO.getSerialNos().size() != stockCrossDockDTO.getQty().intValue())) {
                    throw new DeliveryException(DeliveryException.ERROR_SERIAL_NOTSCANNED, (i + 1));
                }
            }
        }

    }

    public void completePacking() {
        if (!ListUtil.isNullOrEmpty(ids)) {
            ids.clear();
        }
        crossDockPackingService.delivery(this.crossDockPackingDTOList, boxNum);
        List<Long> toCrossDockHeaderIdList = new ArrayList<Long>();
        for (CrossDockPackingDTO crossDockPackingDTO : crossDockPackingDTOList) {
            this.ids.add(crossDockPackingDTO.getCrossDockPacking().getId());
            toCrossDockHeaderIdList.add(crossDockPackingDTO.getCrossDockPacking().getCdHeaderId());
        }
        for (Long toCrossDockHeaderId : toCrossDockHeaderIdList) {
            TOCrossDockHeader toCrossDockHeader = crossDockDetailService.getToCrossDockHeaderByCDHeaderId(toCrossDockHeaderId);
            if (toCrossDockHeader != null && CrossDockStatus.SHIPED.getValue().equals(toCrossDockHeader.getStatus())) {
                crossDockDetailService.expDataToWMSANDOSM(toCrossDockHeader.getId());
                expFacadeService.sendSerialNo2WMS(toCrossDockHeader.getId(),
                        toCrossDockHeader.getDoType(), DocType.SO.getValue(),
                        toCrossDockHeader.getRefNo1(), toCrossDockHeader.getTargetWarehouseId(),
                        ParamUtil.getCurrentWarehouseId());
                // 序列号
                expFacadeService.sendDoSnLog2Oms(toCrossDockHeader.getId());
            }
        }
        this.sayMessage("global.hint.operation.success");

        this.isAutoPrintCarton = SystemConfig.getConfigValue("print.carton.autoPrintCrossDock", ParamUtil.getCurrentWarehouseId());
        if (YesNo.YES.getValue().toString().equals(isAutoPrintCarton)) {
            autoPrint();
        }
    }

    /**
     * 删除该条记录关联的指定编号的序列号
     */
    public void deleteSerialNo() {
        this.stockCrossDockDTO.getSerialNos().remove(deleteSerialNo);
        totalSerials.get(this.stockCrossDockDTO.getSkuId()).remove(deleteSerialNo);
        updateSerialDTO();
    }

    /**
     * 删除该条记录关联的全部序列号
     */
    public void deleteAllSerial() {
        if (ListUtil.isNullOrEmpty(this.stockCrossDockDTO.getSerialNos())) {
            return;
        }
        for (String removeSerialNo : this.stockCrossDockDTO.getSerialNos()) {
            totalSerials.get(this.stockCrossDockDTO.getSkuId()).remove(removeSerialNo);
        }
        this.stockCrossDockDTO.getSerialNos().clear();
        updateSerialDTO();
    }

    /**
     * 显示消息
     *
     * @param msg
     */
    @Override
    protected void sayMessage(String msg) {
        FacesMessages.instance().add(ResourceUtils.getDispalyString(msg));
    }

    /**
     * 自动打印越库箱标签
     */
    public void autoPrint() {
        if (null != printData) {
            printData.clear();
        }

        if (ListUtil.isNullOrEmpty(ids)) {
            this.sayMessage(DeliveryException.PRINT_DATA_IS_EMPTY);
        }

        this.printData = printCrossDockCartonService.getCrossDockCartonPrintDTO(ids);
    }

    /**
     * 打印越库箱标签
     */
    public void print() {
        if (null != printData) {
            printData.clear();
        }

        if (!ListUtil.isNullOrEmpty(ids)) {
            ids.clear();
        }

        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }
        selectedMap.clear();

        this.printData = printCrossDockCartonService.getCrossDockCartonPrintDTO(ids);
    }

    public CrossDockPackingFilter getCrossDockPackingFilter() {
        return crossDockPackingFilter;
    }

    public void setCrossDockPackingFilter(
            CrossDockPackingFilter crossDockPackingFilter) {
        this.crossDockPackingFilter = crossDockPackingFilter;
    }

    public String getLpnNo() {
        return lpnNo;
    }

    public void setLpnNo(String lpnNo) {
        this.lpnNo = lpnNo;
    }

    public CrossDockPacking getCrossDockPacking() {
        return crossDockPacking;
    }

    public void setCrossDockPacking(
            CrossDockPacking crossDockPacking) {
        this.crossDockPacking = crossDockPacking;
    }

    public List<CrossDockPacking> getCrossDockPackingList() {
        return crossDockPackingList;
    }

    public void setCrossDockPackingList(
            List<CrossDockPacking> crossDockPackingList) {
        this.crossDockPackingList = crossDockPackingList;
    }

    public CrossDockDTO getCrossDockDTO() {
        return crossDockDTO;
    }

    public void setCrossDockDTO(CrossDockDTO crossDockDTO) {
        this.crossDockDTO = crossDockDTO;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getScannedSerialNo() {
        return scannedSerialNo;
    }

    public void setScannedSerialNo(String scannedSerialNo) {
        this.scannedSerialNo = scannedSerialNo;
    }

    public int getNotScannedSerialNum() {
        return notScannedSerialNum;
    }

    public void setNotScannedSerialNum(int notScannedSerialNum) {
        this.notScannedSerialNum = notScannedSerialNum;
    }

    public int getScannedSerialNum() {
        return scannedSerialNum;
    }

    public void setScannedSerialNum(int scannedSerialNum) {
        this.scannedSerialNum = scannedSerialNum;
    }

    public String getDeleteSerialNo() {
        return deleteSerialNo;
    }

    public void setDeleteSerialNo(String deleteSerialNo) {
        this.deleteSerialNo = deleteSerialNo;
    }

    public String getSerialIdNo() {
        return serialIdNo;
    }

    public void setSerialIdNo(String serialIdNo) {
        this.serialIdNo = serialIdNo;
    }

    public int getBoxNum() {
        return boxNum;
    }

    public void setBoxNum(int boxNum) {
        this.boxNum = boxNum;
    }

    public int getIsMultiLpn() {
        return isMultiLpn;
    }

    public void setIsMultiLpn(int isMultiLpn) {
        this.isMultiLpn = isMultiLpn;
    }

    public List<CrossDockPackingDTO> getCrossDockPackingDTOList() {
        return crossDockPackingDTOList;
    }

    public void setCrossDockPackingDTOList(List<CrossDockPackingDTO> crossDockPackingDTOList) {
        this.crossDockPackingDTOList = crossDockPackingDTOList;
    }

    public Map<Long, List<String>> getTotalSerials() {
        return totalSerials;
    }

    public void setTotalSerials(Map<Long, List<String>> totalSerials) {
        this.totalSerials = totalSerials;
    }

    public CrossDockPackingDTO getCrossDockPackingDTO() {
        return crossDockPackingDTO;
    }

    public void setCrossDockPackingDTO(CrossDockPackingDTO crossDockPackingDTO) {
        this.crossDockPackingDTO = crossDockPackingDTO;
    }

    public StockCrossDockDTO getStockCrossDockDTO() {
        return stockCrossDockDTO;
    }

    public void setStockCrossDockDTO(StockCrossDockDTO stockCrossDockDTO) {
        this.stockCrossDockDTO = stockCrossDockDTO;
    }

    public PrintData getPrintData() {
        return printData;
    }

    public void setPrintData(PrintData printData) {
        this.printData = printData;
    }

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    public String getIsAutoPrintCarton() {
        return isAutoPrintCarton;
    }

    public void setIsAutoPrintCarton(String isAutoPrintCarton) {
        this.isAutoPrintCarton = isAutoPrintCarton;
    }

}
