package com.daxia.wms.delivery.task.replenish.service.impl;

import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoDetailDAO;
import com.daxia.wms.delivery.task.replenish.dto.LocationReplInfoDTO;
import com.daxia.wms.delivery.task.replenish.dto.ReplenishSkuDTO;
import com.daxia.wms.delivery.task.replenish.service.ReplenishTaskGenerateContext;
import com.daxia.wms.stock.stock.service.StockService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

@Name("com.daxia.wms.stock.replenishTaskGenerateContext")
@lombok.extern.slf4j.Slf4j
public class ReplenishTaskGenerateContextImpl implements ReplenishTaskGenerateContext {
    @In
    StockService stockService;
    @In
    DoDetailDAO doDetailDAO;
    @In
    XPReplenishTaskGenerator xpReplenishTaskGenerator;
    @In
    JPReplenishTaskGenerator jpReplenishTaskGenerator;
    
    @Override
    public int createXPReplenishTask(String partition, String productCode, String aisle, Boolean isAuto) throws Exception {
        // 根据库区和sku取得需要补货的记录。
        List<Object[]> results = stockService.queryNeedReplLocs(partition, productCode, aisle);
        if (CollectionUtils.isEmpty(results)) {
            throw new DeliveryException("error.delivery.noLocNeedFreeTimeRepl");
        }
        
        List<LocationReplInfoDTO> locationReplInfos = new ArrayList<LocationReplInfoDTO>();
        int splitSize = 100;
        int size = results.size();
        int taskMadeCount = 0;
        for (int i = 1; i <= size; i++) {
            Object[] result = results.get(i - 1);
            LocationReplInfoDTO dto = new LocationReplInfoDTO();
            
            // 如果实际库存 > 补货上限则不需要补货
            BigDecimal uplimit = (BigDecimal) result[6];
            BigDecimal actQty = (BigDecimal) result[5];
            if (uplimit.compareTo(actQty) <= 0) {
                continue;
            }
            // 设置DTO
            dto.setLocId(((Number) result[0]).longValue());
            dto.setSkuId(((Number) result[1]).longValue());
            dto.setUplimit(uplimit);
            dto.setLowerlimit((BigDecimal) result[7]);
            dto.setActQty(actQty);
            dto.setPackageType((String) result[2]);
//            dto.setPackageQty((BigDecimal) result[3]);
            dto.setPartitionId(((Number) result[4]).longValue());
            locationReplInfos.add(dto);
            
            // 每splitSize个记录集中生成一次闲时补货。
            if (i % splitSize == 0 && i > 1) {
                // 生成闲时补货任务。
                taskMadeCount = xpReplenishTaskGenerator.createReplenishTask(locationReplInfos, isAuto, taskMadeCount);
                // 清空待补货信息集合
                locationReplInfos.clear();
            }
        }
        // 循环结束剩余需要补货的记录存在且个数数少于splitSize的，集中生成一次闲时补货。
        if (size % splitSize != 0) {
            taskMadeCount = xpReplenishTaskGenerator.createReplenishTask(locationReplInfos, isAuto, taskMadeCount);
        }
        
        return taskMadeCount;
    }
    
    @Override
    public Map<String, Set<String>> createJPReplenishTask(Long carrierId, String doType, Boolean isAuto) throws Exception {
        List<ReplenishSkuDTO> skus = doDetailDAO.findNeedReplSku(carrierId, doType);
        return jpReplenishTaskGenerator.createTask(skus, doType, isAuto);
    }
}
