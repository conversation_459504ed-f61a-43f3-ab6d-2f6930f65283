package com.daxia.wms.delivery.invoice.dao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;
import com.daxia.wms.Constants;

@Name("com.daxia.wms.delivery.invoiceNoDAO")
@lombok.extern.slf4j.Slf4j
public class InvoiceNoDAO extends HibernateBaseDAO<InvoiceNo, Long> {

	private static final long serialVersionUID = 7918202074571516220L;
	
    /**
     * 根据发票薄号分页查询发票号
     * 
     * @param invoiceBookId
     * @param startIndex
     * @param pageSize
     * @return
     */
	@SuppressWarnings("unchecked")
	public DataPage<InvoiceNo> findInvoiceNoByInvoiceBookId(Long invoiceBookId, int startIndex, int pageSize) {
		String hql = "from InvoiceNo o where o.invoiceBookId = ? and o.isDeleted = 0 and o.warehouseId = ? order by o.invoiceNo";
		String countHql = "select count(*) from InvoiceNo o where o.invoiceBookId = ? and o.isDeleted = 0 and o.warehouseId = ? order by o.invoiceNo";
		
		List<Object> params = new ArrayList<Object>();
		params.add(invoiceBookId);
		params.add(ParamUtil.getCurrentWarehouseId());
		return  (DataPage<InvoiceNo>) this.executeQuery(hql, countHql, startIndex, pageSize, params.toArray());
	}
 
    /**
     * 查询发票薄中发票号从段从invoiceNoFm至invoiceNoTo不是未打印状态的发票号集合
     * 
     * @param invoiceBookId
     * @param invoiceNoFm
     * @param invoiceNoTo
     * @param status
     * @return
     */
	@SuppressWarnings("unchecked")
	public List<InvoiceNo> findInvoiceBooksInvoiceNoNotPrint(Long invoiceBookId, String invoiceNoFm, String invoiceNoTo, String status) {
		String hql = "from InvoiceNo o where o.invoiceBookId = :invoiceBookId and o.invoiceNo >= :invoiceNoFm " +
				"and o.invoiceNo <= :invoiceNoTo  and o.status != :status and o.warehouseId = :warehouseId";
		
		Query query = this.createQuery(hql);
		query.setParameter("invoiceBookId", invoiceBookId);
		query.setParameter("invoiceNoFm", invoiceNoFm);
		query.setParameter("invoiceNoTo", invoiceNoTo);
		query.setParameter("status", status);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		return query.list();
	}
	
    /**
     * 根据发票薄id更新发票号从invoiceNoFrom至invoiceNoTo状态为status
     * 
     * @param invoiceBookId
     * @param invoiceNoFrom
     * @param invoiceNoTo
     * @param status
     */
	public void updateInvoiceBookInvoiceNos(Long invoiceBookId, String invoiceNoFrom, String invoiceNoTo, String status, Date loseDate, String loseBy) {
		String hqlQuery = "update InvoiceNo o set o.status = :status, o.loseDate = :loseDate, o.loseBy = :loseBy where o.invoiceBookId = :invoiceBookId and o.invoiceNo >= :invoiceNoFrom and o.invoiceNo <= :invoiceNoTo and o.warehouseId = :warehouseId";
		Query updateQuery = this.createUpdateQuery(hqlQuery);
		updateQuery.setParameter("status", status);
		updateQuery.setParameter("loseDate", loseDate);
		updateQuery.setParameter("loseBy", loseBy);
		updateQuery.setParameter("invoiceBookId", invoiceBookId);
		updateQuery.setParameter("invoiceNoFrom", invoiceNoFrom);
		updateQuery.setParameter("invoiceNoTo", invoiceNoTo);
		updateQuery.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		updateQuery.executeUpdate();
	}
	
    /**
     * 根据发票号码查找发票信息
     * @param invoiceNo
     * @return
     */	
	public InvoiceNo findInvoiceNoByNo(String invoiceNo){
		String hql = " from InvoiceNo o where o.invoiceNo = :invoiceNo and o.warehouseId = :warehouseId";
		Query query = this.createQuery(hql);
		query.setParameter("invoiceNo", invoiceNo);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		InvoiceNo no = null;
		try {
			no = (InvoiceNo)query.uniqueResult();
		} catch (org.hibernate.NonUniqueResultException ex) {
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_NUMBER_DUPLICATE, invoiceNo); // 发票号:{0}在系统中有重复，请联系系统管理员！
		}
		
		return no;
	}
	
	
    /**
     * 根据发票号码与发票代码查找发票信息
     */
    public InvoiceNo findInvoiceNoByNo(String invoiceNo, String invoiceCode){
        String hql = " from InvoiceNo o where o.invoiceNo = :invoiceNo and o.invoiceCode = :invoiceCode and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setParameter("invoiceNo", invoiceNo);
        query.setParameter("invoiceCode", invoiceCode);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (InvoiceNo)query.uniqueResult();
    }
    
	/**
	 * 根据发票号码查找发票信息
	 * 
	 * @param invoiceNo
	 * @return
	 */
	public InvoiceNo findInvoiceNoByInvNoAndDoNo(String invoiceNo, String doNo) {
		String hql = " from InvoiceNo o where o.invoiceNo = :invoiceNo and doNo = :doNo and o.warehouseId = :warehouseId";
		Query query = this.createQuery(hql);
		query.setParameter("invoiceNo", invoiceNo);
		query.setParameter("doNo", doNo);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		InvoiceNo no = null;
		try {
			no = (InvoiceNo) query.uniqueResult();
		} catch (org.hibernate.NonUniqueResultException ex) {
			throw new DeliveryException(
					DeliveryException.ERROR_INVOICE_NUMBER_DUPLICATE, invoiceNo); // 发票号:{0}在系统中有重复，请联系系统管理员！
		}

		return no;
	}

	/**
	 * 根据发票号码查找发票信息
	 * 
	 * @param invoiceNo
	 * @return
	 */
	public InvoiceNo findInvoiceNoObjByInvNoAndInvHeaderId(String invoiceNo,Long invHeaderId) {
		String hql = " from InvoiceNo o where o.invoiceNo = :invoiceNo and o.invoiceHeaderId = :invHeaderId and o.warehouseId = :warehouseId";
		Query query = this.createQuery(hql);
		query.setParameter("invoiceNo", invoiceNo);
		query.setParameter("invHeaderId", invHeaderId);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		InvoiceNo no = null;
		try {
			no = (InvoiceNo) query.uniqueResult();
		} catch (org.hibernate.NonUniqueResultException ex) {
			throw new DeliveryException(
					DeliveryException.ERROR_INVOICE_NUMBER_DUPLICATE, invoiceNo); // 发票号:{0}在系统中有重复，请联系系统管理员！
		}

		return no;
	}
	
    
    /**
     * 通过InvoiceHeader 的ID查询“已打印”的发票号
     * 
     * @param headId
     * @return
     */
	public InvoiceNo findInvoiceNoHeadId(Long headId) {
		String hql = " from InvoiceNo o where invoiceHeaderId = :headId and status = :status and o.warehouseId = :warehouseId";
		Query query = this.createQuery(hql);
		query.setParameter("headId", headId);
		query.setParameter("status", Constants.InvoiceNoStatus.PRINT.getValue());
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		return (InvoiceNo)query.uniqueResult();
	}
	
	/**
	 * 根据发票代码、发票起止号码查询发票实体信息
	 * @param invoiceCode
	 * @param invoiceNoFrom
	 * @param invoiceNoTo
	 * @return
	 */
	@SuppressWarnings("unchecked")
    public List<InvoiceNo> getInvoiceNoList(String invoiceCode, String invoiceNoFrom, String invoiceNoTo) {
        String hql = "from InvoiceNo o where o.invoiceCode = :invoiceCode and o.invoiceNo >= :invoiceNoFrom and o.invoiceNo <= :invoiceNoTo and o.warehouseId = :warehouseId order by o.invoiceNo asc";
        Query query = this.createQuery(hql);
        query.setString("invoiceCode", invoiceCode);
        query.setString("invoiceNoFrom", invoiceNoFrom);
        query.setString("invoiceNoTo", invoiceNoTo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }
	/**
	 * 根据订单号查询发票实体信息
	 * @param doNo
	 * @return
	 */
	@SuppressWarnings("unchecked")
    public List<InvoiceNo> getInvoiceNoByDoNo(String doNo){
	    String hql = "from InvoiceNo o where o.doNo = :doNo and o.warehouseId = :warehouseId  and o.status in (1,0)  order by o.invoiceNo asc";
        Query query = this.createQuery(hql);
        query.setString("doNo", doNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
	}
	
	@SuppressWarnings("unchecked")
    public List<InvoiceNo> findInvoiceByHeaderIds(List<Long> ids) {
	    String hql = " from InvoiceNo o where invoiceHeaderId in (:ids) and status = :status and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setParameterList("ids", ids);
        query.setParameter("status", Constants.InvoiceNoStatus.PRINT.getValue());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
	}
}
