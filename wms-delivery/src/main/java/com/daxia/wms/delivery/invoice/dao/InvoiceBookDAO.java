package com.daxia.wms.delivery.invoice.dao;

import java.util.ArrayList;
import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.invoice.entity.InvoiceBook;
import com.daxia.wms.delivery.invoice.filter.InvoiceBookFilter;

/**
 * 发票薄DAO
 */
@Name("com.daxia.wms.delivery.invoiceBookDAO")
@lombok.extern.slf4j.Slf4j
public class InvoiceBookDAO extends HibernateBaseDAO<InvoiceBook, Long> {

	private static final long serialVersionUID = 1138039305681399962L;

    /**
     * 分页查询发票薄信息
     * 
     * @param invoiceBookFilter
     * @param startIndex
     * @param pageSize
     * @return
     */
	@SuppressWarnings("unchecked")
	public DataPage<InvoiceBook> queryInvoiceBookByFilter(InvoiceBookFilter invoiceBookFilter, int startIndex, int pageSize) {
		StringBuilder hqlSb = new StringBuilder("from  InvoiceBook o where 1 = 1 and o.warehouseId = ? ");
		List<Object> params = new ArrayList<Object>();
        params.add(ParamUtil.getCurrentWarehouseId());
		if (StringUtil.isNotEmpty(invoiceBookFilter.getInvoiceBookStatus())) {
			hqlSb.append(" and o.status = ?");
			params.add(invoiceBookFilter.getInvoiceBookStatus());
		}
		if (StringUtil.isNotEmpty(invoiceBookFilter.getInvoiceCode())) {
			hqlSb.append(" and o.invoiceCode = ?");
			params.add(invoiceBookFilter.getInvoiceCode());
		}
		if (StringUtil.isNotEmpty(invoiceBookFilter.getInvoiceNoFm()) && StringUtil.isNotEmpty(invoiceBookFilter.getInvoiceNoTo())) {
			hqlSb.append("and ((o.invoiceNoFrom <= ? and o.invoiceNoTo >= ?) ");
			hqlSb.append(" or (o.invoiceNoFrom <= ? and o.invoiceNoTo >= ?) ");
			hqlSb.append(" or (o.invoiceNoFrom >= ? and o.invoiceNoTo <= ? ))");
			params.add(invoiceBookFilter.getInvoiceNoFm());
			params.add(invoiceBookFilter.getInvoiceNoFm());
			params.add(invoiceBookFilter.getInvoiceNoTo());
			params.add(invoiceBookFilter.getInvoiceNoTo());
			params.add(invoiceBookFilter.getInvoiceNoFm());
			params.add(invoiceBookFilter.getInvoiceNoTo());
		}
		hqlSb.append("order by o.id");
		String countHql = "select count(*) " + hqlSb.toString();
		return (DataPage<InvoiceBook>) this.executeQuery(hqlSb.toString(), countHql, startIndex, pageSize, params.toArray());
	}

    /**
     * 根据发票起止号码及发票代码查找发票薄
     */
	@SuppressWarnings("unchecked")
	public long queryInvoiceBookByNo(String invoiceNoFrom, String invoiceNoTo, String invoiceCode) {
	    String hql = "from InvoiceBook o where o.invoiceNoFrom <= :invoiceNoFrom " +
	    "and o.invoiceNoTo >= :invoiceNoTo and o.invoiceCode = :invoiceCode " +
	    " and o.warehouseId = :warehouseId ";
	    Query query = this.createQuery(hql);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
	    query.setParameter("invoiceNoFrom", invoiceNoFrom);
	    query.setParameter("invoiceNoTo", invoiceNoTo);
	    query.setParameter("invoiceCode", invoiceCode);
	    List<InvoiceBook> list = query.list();
	    if (list.size() > 1 || list.isEmpty()) {
	        return -1;
	    } 
	    return list.get(0).getId();
	}
}
