package com.daxia.wms.delivery.container.service;

import java.util.List;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.container.entity.ContainerLog;
import com.daxia.wms.delivery.container.filter.ContainerLogFilter;
import com.daxia.wms.master.entity.Container;

/**
 *  容器使用日志service接口
 */
public interface ContainerLogService {

    /**
     * 新增保存容器使用日志
     * 
     * @param containerLog
     */
    public void save(ContainerLog containerLog);

    /**
     * 新增一条容器操作日志
     * @param container
     * @param mergeLocCode 集货位号，集货入出区需要该参数
     * @param operType 操作类型
     * @param operator 操作人
     */
    public void addContainerLog(Container container, String mergeLocCode, String operType, String operator);

    /**
     * 查询指定波次下的容器日志
     * @param waveId
     * @return
     */
    public List<ContainerLog> queryLogsByWave(Long waveId);
    
    /**
     * 获取容器最后绑定的单据号
     * @param containerNo
     * @return
     */
    public String getLaskBindDocNo(String containerNo, String docType);
    
    /**
     * 
     * @param docNo
     * @param opType
     * @param docType
     * @return
     */
    public List<ContainerLog> queryLogsByDocNo(String docNo, String opType, String docType);
    
    DataPage<ContainerLog> query(ContainerLogFilter containerLogFilter, int startIndex, int pageSize);
}
