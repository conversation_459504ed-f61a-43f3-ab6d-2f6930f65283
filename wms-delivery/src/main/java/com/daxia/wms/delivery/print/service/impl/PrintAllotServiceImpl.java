package com.daxia.wms.delivery.print.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.util.DateUtil;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.print.service.PrintAllotService;
import com.daxia.wms.Constants.DoType;
import com.daxia.wms.master.service.impl.AbstractPrintService;
/**
 * 调拨单打印实现类
 * 
 * </pre>
 */
@Name("com.daxia.wms.delivery.printAllotService")
@lombok.extern.slf4j.Slf4j
public class PrintAllotServiceImpl extends AbstractPrintService<DeliveryOrderHeader> implements PrintAllotService {

    public static final String REPORT_NAME = "allot";

    public static final String REPORT_SUB_NAME = "allotDetail";

        
    /**
     * 获取调拨单打印参数
     * @param doType
     * @return
     */
    private Map<String, Object> getParams(String doType) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("warehouseName", this.getCurrentWarehouseName());
        params.put("nowDate", DateUtil.getNowDate());
        params.put("operateUser", this.getOperateUser());

        return params;
    }

    /**
     * 调拨单
     */
    @Override
    public List<String> printDoList(List<DeliveryOrderHeader> doList, String doType) {
        Map<String, Object> params = this.getParams(doType);

        List<String> printData = new ArrayList<String>();
        List<DeliveryOrderHeader> dataList = null;
        for (DeliveryOrderHeader doHeader : doList) {
            dataList = new ArrayList<DeliveryOrderHeader>();
            dataList.add(doHeader);

            printData.addAll(this.printData(dataList, params, REPORT_NAME, REPORT_SUB_NAME));
        }

        return printData;
    }

    /**
     * 生成pdf格式的调拨单
     */
    @Override
    public byte[] generatePDFByDoList(List<DeliveryOrderHeader> doList, String doType) {
        Map<String, Object> params = this.getParams(doType);
        return this.export(doList, params, REPORT_NAME, REPORT_SUB_NAME);
    }
}