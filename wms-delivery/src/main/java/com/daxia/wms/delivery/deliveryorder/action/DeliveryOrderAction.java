package com.daxia.wms.delivery.deliveryorder.action;

import com.danchuang.global.scm.address.client.dto.BaseAddressDTO;
import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.action.UiRender;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.entity.CfgCodeDetail;
import com.daxia.framework.common.service.Dictionary;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.util.WmsUtil;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants;
import com.daxia.wms.Keys;
import com.daxia.wms.PageConfig;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dto.DoAlcDetailInfoDto;
import com.daxia.wms.delivery.deliveryorder.dto.DoDetailDto;
import com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto;
import com.daxia.wms.delivery.deliveryorder.dto.DoTimeInfoDto;
import com.daxia.wms.delivery.deliveryorder.entity.*;
import com.daxia.wms.delivery.deliveryorder.filter.DoDetailFilter;
import com.daxia.wms.delivery.deliveryorder.filter.DoHeaderFilter;
import com.daxia.wms.delivery.deliveryorder.service.*;
import com.daxia.wms.delivery.load.service.AutoDeliverService;
import com.daxia.wms.delivery.print.dto.PrintCfg;
import com.daxia.wms.delivery.print.helper.WaybillPrintHelper;
import com.daxia.wms.delivery.print.service.PrintDoService;
import com.daxia.wms.delivery.print.service.PrintService;
import com.daxia.wms.delivery.print.service.carton.PrintCartonDispatcher;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeaderHis;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.delivery.util.ExportDeliveryOrderUtil;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.master.component.BusinessCenterComponent;
import com.daxia.wms.master.entity.*;
import com.daxia.wms.master.rule.filter.BigWaveFilter;
import com.daxia.wms.master.service.*;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.stock.stock.entity.StockBatchLocLpn;
import com.daxia.wms.util.FileImportTemplate;
import com.daxia.wms.util.FileImportTemplate.ImportFileReader;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import org.apache.commons.beanutils.BeanComparator;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.comparators.ComparatorChain;
import org.apache.commons.collections.comparators.FixedOrderComparator;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.*;
import org.jboss.seam.annotations.security.Restrict;
import org.json.JSONArray;
import org.richfaces.event.UploadEvent;

import javax.faces.model.SelectItem;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 发运订单头信息业务Action
 */
@Name("com.daxia.wms.delivery.deliveryOrderAction")
@Restrict("#{identity.hasPermission('delivery.do')}")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@Synchronized(timeout = 30000)
@lombok.extern.slf4j.Slf4j
public class DeliveryOrderAction extends PagedListBean<DoHeaderDto> {

    private static final long serialVersionUID = 970831196985906224L;
    private DeliveryOrderHeader doHeader;
    private DeliveryOrderHeaderHis doHeaderHis;
    private DeliveryOrderDetail doDetail;
    private boolean initialized = false;
    private boolean judge = true;
    private boolean frozenOrNot = false;
    private Long doHeaderId;
    private Long doDetailId;
    private String doNo;
    private Double assignNumber;
    private DoHeaderFilter doHeaderFilter;
    private DoHeaderFilter doHeaderFilterForDoMonitor;
    private DoDetailFilter doDetailFilter;
    private CartonHeader cartonHeader;
    private CartonHeaderHis cartonHeaderHis;
    private WaveHeader waveHeader;
    private List<StockBatchLocLpn> stockBatchLocLpns;
    private String printData = "[]";
    private String printContent = "";
    private DoHeaderDto doHeaderDto;
    private DoTimeInfoDto doTimeInfoDto;
    private List<OrderLog> orderLogList;

    private List<SelectItem> provinces = new ArrayList<SelectItem>();
    private List<SelectItem> cites = new ArrayList<SelectItem>();
    private List<SelectItem> counties = new ArrayList<SelectItem>();
    private List<SelectItem> frozenCodes = new ArrayList<SelectItem>();
    private Long countryId;
    private Long provinceId;
    private Long cityId;
    private int cartonCount;
    private String fromDate;
    private String toDate;
    private String sortGridNo;
    private String waveNo;
    private String holdCode;
    private String holdNotes;

    private String uploadFileMsg;
    private String errorCodes;

    private String pageFm;
    private String doIdStrs;
    private Long carrierId;

    private List<Carrier> carrierList;

    private String udf1Name;

    private String udf2Name;

    private String udf3Name;

    private String udf4Name;

    private String udf5Name;

    private String udf1Dict;

    private String udf2Dict;

    private String udf3Dict;

    private String udf4Dict;

    private String udf5Dict;

    /**
     * 物流单号
     */
    private String wayBill;

    /**
     * 发货单打印配置参数
     */
    private PrintCfg doPrintCfg;

    /**
     * 打印标志，用于区分不同的打印类型
     */
    private String printFlag;

    private List<SelectItem> replTrucks = new ArrayList<SelectItem>();

    private List<DoNotice> doNotices;

    private List<DoAlcDetailInfoDto> doAlcDetailInfoDtos;

    private String operateType;

    private String operateLog;

    @In
    private CartonService cartonService;
    @In
    private OrderLogService orderLogService;
    @In
    private PrintService printService;
    @In
    private DeliveryOrderService deliveryOrderService;
    @In
    private WaveService waveService;
    @In
    private AreaService areaService;
    @In(create = true)
    private PrintDoService printDoService;
    @In
    private ExpFacadeService expFacadeService;
    @In
    private DeliveryOrderImportService deliveryOrderImportService;
    @In
    private WarehouseService warehouseService;
    @In
    private AutoDeliverService autoDeliverService;
    @In
    private DoAllocateService doAllocateService;
    @In
    private ShopInfoService shopInfoService;
    @In
    private DoNoticeService doNoticeService;
    @In
    private PrintCartonDispatcher printCartonDispatcher;
    @In
    private CarrierService carrierService;
    @In
    private GroupRuleService groupRuleService;
    @In
    private BusinessCustomerService businessCustomerService;
    @In
    private UiRender uiRender;
    @In
    private SpecialDoLabelService specialDoLabelService;

    @In
    private BusinessCenterComponent businessCenterComponent;

    private String shopName;

    private List<SelectItem> shopInfos;
    /**
     * 面单打印节点
     */
    private Integer cartonPrintPoint;

    private String customerName;

    private Integer doCount;
    private List<UiRender.TdInfo> tdInfoList;
    private UiRender.TdHtml tdHtml;
    private List<SelectItem> specialLabelList;
    private List<SelectItem> storeList;
    public Boolean needPrintCarton() {
        return Config.isDefaultFalse(Keys.Delivery.gene_temp_carton, Config.ConfigLevel.WAREHOUSE) ;
    }
    public DeliveryOrderAction() {
        super();
    }

    @Create
    public void initialize() {
        tdInfoList = Lists.newArrayList();
        tdInfoList.add(new UiRender.TdInfo("货品等级", "cycleClass", "DO_GRADE_VIEW", null, false));
        tdInfoList.add(new UiRender.TdInfo("订单标记", "specialLabelCode", "SPECIAL_LABEL_CODE", null, false));
        tdInfoList.add(new UiRender.TdInfo("发货单类型", "doType", "ODO_TYPE", null, false));
        tdInfoList.add(new UiRender.TdInfo("发货单状态", "status", "DO_STATUS", null, false));
        tdInfoList.add(new UiRender.TdInfo("冻结/释放", "releaseStatus", "RELEASE_STATUS", null, false));
        tdInfoList.add(new UiRender.TdInfo("配送商", "distSuppCompName"));
        tdInfoList.add(new UiRender.TdInfo("波次号", "waveNo", null, null, true, "waveNo"));
        tdInfoList.add(new UiRender.TdInfo("创建时间", "doCreateTime", null, "yyyy-MM-dd HH:mm:ss", false));
        tdInfoList.add(new UiRender.TdInfo("导入时间", "createdAt", null, "yyyy-MM-dd HH:mm:ss", true));
        tdInfoList.add(new UiRender.TdInfo("装箱完成时间", "packEndTime", null, "yyyy-MM-dd HH:mm:ss", false));
        tdInfoList.add(new UiRender.TdInfo("发货时间", "shipTime", null, "yyyy-MM-dd HH:mm:ss", false));
        // tdInfoList.add(new UiRender.TdInfo("预计出库时间", "planShipTime", null,
        // "yyyy-MM-dd HH:mm:ss",true));
        tdInfoList.add(new UiRender.TdInfo("渠道", "channelName"));
        tdInfoList.add(new UiRender.TdInfo("店铺", "storeName"));

        tdInfoList.add(new UiRender.TdInfo("订单子类型", "orderSubType", "ORDER_SUB_TYPE", null, false));
        // tdInfoList.add(new UiRender.TdInfo("最早配送时间(O2O)", "expectedArriveTime1",
        // null, "yyyy-MM-dd HH:mm:ss",false));
        // tdInfoList.add(new UiRender.TdInfo("最晚配送时间(O2O)", "expectedArriveTime2",
        // null, "yyyy-MM-dd HH:mm:ss",false));
        tdInfoList.add(new UiRender.TdInfo("订货商品种数", "userDeffine5Formatted"));
        tdInfoList.add(new UiRender.TdInfo("订货件数", "expectedQty", null, null, true, "countUnit"));
        tdInfoList.add(new UiRender.TdInfo("发货商品种数", "shipskuCount"));
        tdInfoList.add(new UiRender.TdInfo("发货件数", "shipQty"));
        // tdInfoList.add(new UiRender.TdInfo("配送商", "distSuppCompName"));
        tdInfoList.add(new UiRender.TdInfo("发票数量", "invoiceQty"));
        // tdInfoList.add(new UiRender.TdInfo("创建时间", "doCreateTime",null,"yyyy-MM-dd
        // HH:mm:ss",false));
        // tdInfoList.add(new UiRender.TdInfo("支付时间", "payTime",null,"yyyy-MM-dd
        // HH:mm:ss",false));
        // tdInfoList.add(new UiRender.TdInfo("波次号", "waveNo"));
        // tdInfoList.add(new UiRender.TdInfo("发货时间", "shipTime",null,"yyyy-MM-dd
        // HH:mm:ss",false));
        tdInfoList.add(new UiRender.TdInfo("应收金额", "receivable", null, null, true));
        tdInfoList.add(new UiRender.TdInfo("重量", "grossWt"));
        tdInfoList.add(new UiRender.TdInfo("分拣格号", "sortGridNo", null, null, true));
        tdInfoList.add(new UiRender.TdInfo("省", "province"));
        tdInfoList.add(new UiRender.TdInfo("市", "city"));
        tdInfoList.add(new UiRender.TdInfo("区", "county"));
        tdInfoList.add(new UiRender.TdInfo("收货方", "consigneeName"));
        tdInfoList.add(new UiRender.TdInfo("收货地址", "address"));
        // tdInfoList.add(new UiRender.TdInfo("电话", "telephone"));
        // tdInfoList.add(new UiRender.TdInfo("手机", "mobile"));
        tdInfoList.add(new UiRender.TdInfo("冻结原因", "holdReason"));
        tdInfoList.add(new UiRender.TdInfo("冻结人", "holdWho"));
        tdInfoList.add(new UiRender.TdInfo("冻结时间", "holdTime", null, "yyyy-MM-dd HH:mm:ss", false));
        tdInfoList.add(new UiRender.TdInfo("买家留言", "buyerRemark", null, null, true));
        tdInfoList.add(new UiRender.TdInfo("卖家留言", "sellerRemark", null, null, true));
        tdInfoList.add(new UiRender.TdInfo("备注", "notes"));
        // 使用自定义方法渲染打印标识，确保HTML正确显示
        tdInfoList.add(new UiRender.TdInfo("打印标识", "showPrintFlagHtml", null, null, false, "showPrintFlagHtml"));
        tdInfoList.add(new UiRender.TdInfo("是否越库", "needCrossStock", "YES_NO", null, false));
        tdInfoList.add(new UiRender.TdInfo("是否自提", "userDeffine6", "YES_NO", null, false));
        // 自定义字段1~5
        String udfNames = SystemConfig.getConfigValue("delivery.order.udf.names", ParamUtil.getCurrentWarehouseId());
        if (StringUtil.isNotEmpty(udfNames)) {
            for (String s : udfNames.split(";")) {
                if (s.split(":").length >= 2 && StringUtil.isInArrays(s.split(":")[0], "1", "2", "3", "4", "5")) {
                    if (s.split(":").length == 3) {
                        // 数据字典转换
                        tdInfoList.add(
                                new UiRender.TdInfo(s.split(":")[1], "userDeffine" + s.split(":")[0], s.split(":")[2]));
                    } else {
                        tdInfoList.add(new UiRender.TdInfo(s.split(":")[1], "userDeffine" + s.split(":")[0]));
                    }
                }
            }
        }

        String attributeSort = SystemConfig.getConfigValue("delivery.order.doListFiledSortArray",
                ParamUtil.getCurrentWarehouseId());
        if (StringUtil.isNotEmpty(attributeSort)) {
            ComparatorChain compChain = new ComparatorChain();
            FixedOrderComparator fixedOrderComparator = new FixedOrderComparator(attributeSort.split(","));
            fixedOrderComparator.setUnknownObjectBehavior(FixedOrderComparator.UNKNOWN_AFTER);
            compChain.addComparator(new BeanComparator("attribute", fixedOrderComparator));
            Collections.sort(tdInfoList, compChain);
        }

        this.doHeaderFilter = new DoHeaderFilter();
        this.doHeaderFilterForDoMonitor = new DoHeaderFilter();
        // 设置默认查询条件 订单类型为 DO
        if (StringUtil.isEmpty(doHeaderFilter.getDoType())) {
            doHeaderFilter.setDoType(Constants.DoType.SELL.getValue());
        }
        // 设置默认查询条件 冻结/释放为 释放
        if (StringUtil.isEmpty(doHeaderFilter.getReleaseStatus())) {
            doHeaderFilter.setReleaseStatus(Constants.ReleaseStatus.RELEASE.getValue());
        }
        this.setStockBatchLocLpns(new ArrayList<StockBatchLocLpn>());

        shopInfos = Lists.newArrayList();
        storeList = Lists.newArrayList();
        List<ShopInfo> shopInfoList = shopInfoService.getAll();
        for (ShopInfo shopInfo : shopInfoList) {
            SelectItem selectItem = new SelectItem();
            selectItem.setValue(shopInfo.getId());
            selectItem.setLabel(shopInfo.getName());
            shopInfos.add(selectItem);
        }
        doHeaderFilter.getOrderByMap().put("doCreateTime", "desc");

        JsonUtil.autoSetAttr(doHeaderFilter,
                SystemConfig.getConfigValue("deliveryOrderAction.doHeaderFilter", ParamUtil.getCurrentWarehouseId()));

        Map<String, String> resonMap = Dictionary.getDictionary("REASON_HDD");
        EnumSet<Constants.Reason> manualFrozenCodes = EnumSet.range(Constants.Reason.MANUAL_LACK,
                Constants.Reason.BAD_QUALITY);

        for (Constants.Reason temp : manualFrozenCodes) {
            String keyValue = temp.getValue();
            String labelValue = resonMap.get(keyValue);
            SelectItem item = new SelectItem();
            item.setValue(keyValue);
            item.setLabel(labelValue);
            frozenCodes.add(item);
        }

        cartonPrintPoint = PageConfig.getInt(ConfigKeys.CARTON_PRINT_POINT, Config.ConfigLevel.WAREHOUSE.getValue());

        this.carrierList = carrierService.getAll();

        specialLabelList = Lists.newArrayList();
        List<SpecialDoLabel> specialDoLabels = specialDoLabelService.findAvailable();
        for (SpecialDoLabel specialDoLabel : specialDoLabels) {
            SelectItem selectItem = new SelectItem();
            selectItem.setValue(specialDoLabel.getLabelCode());
            selectItem.setLabel(specialDoLabel.getLabelName());
            specialLabelList.add(selectItem);
        }
        SelectItem selectItem = new SelectItem();
        selectItem.setValue("0");
        selectItem.setLabel("无标记订单");
        specialLabelList.add(selectItem);
    }

    public List<SelectItem> getGroupRule() {
        List<GroupRule> groupRules = groupRuleService.getAllEnable(ParamUtil.getCurrentWarehouseId());
        List<SelectItem> list = new ArrayList<SelectItem>();
        for (GroupRule gr : groupRules) {
            SelectItem item = new SelectItem();
            item.setValue(gr.getId());
            item.setLabel(gr.getRuleName());
            item.setDescription(gr.getRuleName());
            list.add(item);
        }
        return list;
    }

    public void getOrderDetail() {
        this.doDetail = deliveryOrderService.getDoDetail(doDetailId);
    }

    public void showBatchModifyCarrier() {
        if (StringUtil.isBlank(doIdStrs)) {
            throw new BusinessException("请选择发货单");
        }
    }

    public void showBatchFrozen() {
        if (StringUtil.isBlank(doIdStrs)) {
            throw new BusinessException("请选择发货单");
        }
        setDoCount(doIdStrs.split(",").length);
    }

    public void doBatchModifyCarrier() {
        String[] ids = doIdStrs.split(",");
        List<Long> idList = new ArrayList<Long>();
        for (String id : ids) {
            if (StringUtil.isNotBlank(id)) {
                idList.add(Long.valueOf(id));
            }
        }
        deliveryOrderService.batchModifyCarrier(idList, carrierId);
        this.sayMessage(MESSAGE_SUCCESS);
    }

    public void batchRemoveWave() {
        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            Long doId = (Long) id;
            try {
                deliveryOrderService.remove(doId);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        query();
        this.sayMessage(MESSAGE_SUCCESS);
    }

    public void autoLoad() {
        for (Object id : getSelectedRowList()) {
            Long doId = (Long) id;
            try {
                deliveryOrderService.autoLoad(doId);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        query();
        this.sayMessage(MESSAGE_SUCCESS);
    }

    /**
     * 批量生成发票
     */
    public void geneInvoices() {
        List<Long> ids = new ArrayList(getSelectedRowList());
        deliveryOrderService.geneInvoices(ids);

    }

    /**
     * deliveryOrderList.xhtml
     */
    public void initialize4SortedPage() {
        if ("workbench".equals(this.pageFm)) {
            initialize4Wrokbench();
        } else {
            if (StringUtil.isEmpty(waveNo) || StringUtil.isEmpty(sortGridNo)) {
                return;
            }
            doHeaderFilter.setWaveNo(waveNo);
            doHeaderFilter.setSortGridNo(sortGridNo);
            doHeaderFilter.setReleaseStatus(null);
            doHeaderFilter.setDoType(null);
            DataPage<DoHeaderDto> dataPage = deliveryOrderService.query(doHeaderFilter, getStartIndex(), getPageSize());
            this.populateValues(dataPage);
            this.selectedMap.clear();
        }
    }

    private void initialize4Wrokbench() {
        doHeaderFilter.setReleaseStatus(null);
        doHeaderFilter.setDoImportTimeFrom(DateUtil.dateAdd("dd", new Timestamp(System.currentTimeMillis()), -30));
        doHeaderFilter.setDoCreateTimeTo(DateUtil.dateAdd("dd", new Timestamp(System.currentTimeMillis()), -2));
        doHeaderFilter.setStatusTo(Constants.DoStatus.PART_DELIVER.getValue());
        doHeaderFilter.setNeedCancel(Boolean.FALSE);
        doHeaderFilter.setDoType(Constants.DoType.SELL.getValue());
        DataPage<DoHeaderDto> dataPage = deliveryOrderService.query(doHeaderFilter, getStartIndex(), getPageSize());
        this.populateValues(dataPage);
        this.selectedMap.clear();
    }

    /**
     * <pre>
     * Description:载入Frozen页面初始化,deliveryOrderFrozen.xhtml
     * </pre>
     */
    public void initializeFrozenPage() {
        if (!initialized) {
            this.doHeader = this.deliveryOrderService.getDoHeaderById(this.doHeaderId);
            if (this.doHeader == null) {
                this.doHeader = new DeliveryOrderHeader();
            }
            initialized = true;
        }
    }

    /**
     * 进入修改配送商页面时初始化
     */
    public void initializeModifyCarrierPage() {
        if (!initialized) {
            this.doHeader = this.deliveryOrderService.getDoHeaderById(this.doHeaderId);
            if (this.doHeader == null) {
                this.doHeader = new DeliveryOrderHeader();
            }

            Province province = areaService.getProvinceByName(doHeader.getProvinceName());
            if (null != province) {
                this.doHeader.setProvince(province.getId());
            }

            City city = areaService.getCityByName(doHeader.getCityName());
            if (null != city) {
                this.doHeader.setCity(city.getId());
            }

            County county = areaService.getCountyByName(doHeader.getCountyName());
            if (null != county) {
                this.doHeader.setCounty(county.getId());
            }

            this.provinceId = doHeader.getProvince();
            this.cityId = doHeader.getCity();
            findProvinceList();
            changeListeForCity();
            changeListeForCounty();
            initialized = true;
        }
    }

    public void initializeAddLogPage() {
        if (!initialized) {
            this.doHeader = this.deliveryOrderService.getDoHeaderById(this.doHeaderId);
            if (this.doHeader == null) {
                this.doHeader = new DeliveryOrderHeader();
            }
            initialized = true;
        }
    }

    /**
     * 订单冻结页面（deliveryOrderFrozen.xhtml）获取订单信息
     */
    public void viewDo() {
        String inputNo = doHeader.getDoNo();
        doHeader.setDoNo(DoUtil.decryptDoNo(inputNo));

        // 先尝试按发货单号查询
        DeliveryOrderHeader foundHeader = null;
        try {
            foundHeader = deliveryOrderService.findDoHeaderByDoNo(doHeader.getDoNo());
        } catch (DeliveryException e) {
            // 如果按发货单号没找到，尝试按物流单号查询
            foundHeader = deliveryOrderService.findDoHeaderByTrackingNo(inputNo);
            if (foundHeader == null) {
                // 如果都没找到，抛出原始异常
                throw e;
            }
        }

        doHeader = foundHeader;
        initialized = true;
    }

    /**
     * <pre>
     * Description:查看出货单明细
     * </pre>
     */
    public void view() {
        if (!initialized) {// 确保初始化动作只执行一次
            if (this.doHeaderFilter.getQueryHis() != null && Boolean.TRUE.equals(this.doHeaderFilter.getQueryHis())) {
                this.doHeaderHis = this.deliveryOrderService.getHeaderHisById(this.doHeaderId);
                List<CartonHeaderHis> cartonHeaders = this.doHeaderHis.getCartonHeaders();
                if (!ListUtil.isNullOrEmpty(cartonHeaders)) {
                    this.cartonHeaderHis = cartonHeaders.get(0);
                    this.cartonCount = cartonHeaders.size();
                }

                this.doHeaderHis.setDoDetails(groupDetailHis(this.doHeaderHis.getDoDetails()));
            } else {
                if (this.doHeaderId != null) {
                    this.doHeader = this.deliveryOrderService.getDoHeaderById(this.doHeaderId);
                } else if (StringUtil.isNotEmpty(this.getDoNo())) {
                    this.doHeader = deliveryOrderService.findDoHeaderByDoNo(this.doNo);
                    this.doHeaderId = this.doHeader.getId();
                }
                this.cartonHeader = this.cartonService.findCartonHeaderByDoId(this.doHeaderId);
                this.cartonCount = this.cartonService.countCartonNumOfADo(this.doHeaderId);

                this.doHeader.setDoDetails(groupDetail(this.doHeader.getDoDetails()));
                this.doHeader.setMobile(DoUtil.decryptPhone(this.doHeader.getMobile()));
                this.doHeader.setTelephone(DoUtil.decryptPhone(this.doHeader.getTelephone()));
                this.wayBill = cartonService.getWayBillByDoId(this.doHeader);

                // 自定义字段
                // 自定义字段1~5
                String udfNames = SystemConfig.getConfigValue("delivery.order.udf.names",
                        ParamUtil.getCurrentWarehouseId());
                if (StringUtil.isNotEmpty(udfNames)) {
                    for (String s : udfNames.split(";")) {
                        if (s.split(":").length >= 2
                                && StringUtil.isInArrays(s.split(":")[0], "1", "2", "3", "4", "5")) {
                            if (StringUtil.equals(s.split(":")[0], "1")) {
                                setUdf1Name(s.split(":")[1]);
                                if (s.split(":").length == 3 && StringUtil.isNotEmpty(s.split(":")[2])) {
                                    setUdf1Dict(s.split(":")[2]);
                                }
                            }
                            if (StringUtil.equals(s.split(":")[0], "2")) {
                                setUdf2Name(s.split(":")[1]);
                                if (s.split(":").length == 3 && StringUtil.isNotEmpty(s.split(":")[2])) {
                                    setUdf2Dict(s.split(":")[2]);
                                }
                            }
                            if (StringUtil.equals(s.split(":")[0], "3")) {
                                setUdf3Name(s.split(":")[1]);
                                if (s.split(":").length == 3 && StringUtil.isNotEmpty(s.split(":")[2])) {
                                    setUdf3Dict(s.split(":")[2]);
                                }
                            }
                            if (StringUtil.equals(s.split(":")[0], "4")) {
                                setUdf4Name(s.split(":")[1]);
                                if (s.split(":").length == 3 && StringUtil.isNotEmpty(s.split(":")[2])) {
                                    setUdf4Dict(s.split(":")[2]);
                                }
                            }
                            if (StringUtil.equals(s.split(":")[0], "5")) {
                                setUdf5Name(s.split(":")[1]);
                                if (s.split(":").length == 3 && StringUtil.isNotEmpty(s.split(":")[2])) {
                                    setUdf5Dict(s.split(":")[2]);
                                }
                            }
                        }
                    }
                }

            }

            this.initialized = true;
        }
    }

    public void viewAlcDetailInfo() {
        if (!initialized) {// 确保初始化动作只执行一次

            if (this.doHeaderFilter.getQueryHis() != null && Boolean.TRUE.equals(this.doHeaderFilter.getQueryHis())) {
                this.doHeaderHis = this.deliveryOrderService.getHeaderHisById(this.doHeaderId);

            } else {
                this.doHeader = this.deliveryOrderService.getDoHeaderById(this.doHeaderId);
                this.doHeader.setMobile(DoUtil.decryptPhone(doHeader.getMobile()));
                this.doHeader.setTelephone(DoUtil.decryptPhone(doHeader.getTelephone()));
            }
            doAlcDetailInfoDtos = doAllocateService.getAlcDetailInfosByDo(doHeaderId);
            this.initialized = true;
        }
    }

    @SuppressWarnings("unchecked")
    private List<DeliveryOrderDetailHis> groupDetailHis(List<DeliveryOrderDetailHis> details) {
        BigDecimal totalAllocatEa = BigDecimal.ZERO;

        // 将明细排序 组合产品的父产品与其下子产品放一起显示
        // 用来放组合产品父产品
        List<DeliveryOrderDetailHis> parentList = new ArrayList<DeliveryOrderDetailHis>();
        // 用来放组合产品子产品
        List<DeliveryOrderDetailHis> childList = new ArrayList<DeliveryOrderDetailHis>();
        // 用来放非组合产品
        List<DeliveryOrderDetailHis> oterList = new ArrayList<DeliveryOrderDetailHis>();
        if (details == null || details.isEmpty()) {
            this.doHeaderHis.setTotalAllocatEa(totalAllocatEa);
        } else {
            for (DeliveryOrderDetailHis deliveryOrderDetail : details) {
                if (deliveryOrderDetail.getIsDoLeaf().intValue() == 0) {
                    parentList.add(deliveryOrderDetail);
                } else {
                    totalAllocatEa = totalAllocatEa.add(deliveryOrderDetail.getAllocatedQty());
                    if (deliveryOrderDetail.getParentId() == null) {
                        oterList.add(deliveryOrderDetail);
                    } else {
                        childList.add(deliveryOrderDetail);
                    }
                }
            }
            this.doHeaderHis.setTotalAllocatEa(totalAllocatEa);

            // 排序发货单明细
            details.clear();
            int lineNo = 1;
            for (DeliveryOrderDetailHis parent : parentList) {
                parent.setLineNo(Integer.valueOf(lineNo));
                details.add(parent);
                for (DeliveryOrderDetailHis child : childList) {
                    // if(parent.getId().longValue() == child.getParentId() ){
                    // shizhiyuan 2011-7 -30 注释
                    if (parent.getOrigDetailId().longValue() == child.getParentId()) {
                        details.add(child);
                    }
                }
                lineNo++;
            }
            for (DeliveryOrderDetailHis other : oterList) {
                other.setLineNo(lineNo);
                details.add(other);
                lineNo++;
            }
            return details;
        }
        return Collections.EMPTY_LIST;
    }

    @SuppressWarnings("unchecked")
    public List<DeliveryOrderDetail> groupDetail(List<DeliveryOrderDetail> details) {
        BigDecimal totalAllocatEa = BigDecimal.ZERO;

        // 将明细排序 组合产品的父产品与其下子产品放一起显示
        // 用来放组合产品父产品
        List<DeliveryOrderDetail> parentList = new ArrayList<DeliveryOrderDetail>();
        // 用来放组合产品子产品
        List<DeliveryOrderDetail> childList = new ArrayList<DeliveryOrderDetail>();
        // 用来放非组合产品
        List<DeliveryOrderDetail> oterList = new ArrayList<DeliveryOrderDetail>();
        if (details == null || details.isEmpty()) {
            this.doHeader.setTotalAllocatEa(totalAllocatEa);
        } else {
            for (DeliveryOrderDetail deliveryOrderDetail : details) {
                if (deliveryOrderDetail.getIsDoLeaf().intValue() == 0) {
                    parentList.add(deliveryOrderDetail);
                } else {
                    totalAllocatEa = totalAllocatEa.add(deliveryOrderDetail.getAllocatedQty());
                    if (deliveryOrderDetail.getParentId() == null) {
                        oterList.add(deliveryOrderDetail);
                    } else {
                        childList.add(deliveryOrderDetail);
                    }
                }
            }
            this.doHeader.setTotalAllocatEa(totalAllocatEa);

            // 排序发货单明细
            details.clear();
            int lineNo = 1;
            for (DeliveryOrderDetail parent : parentList) {
                parent.setLineNo(Integer.valueOf(lineNo));
                details.add(parent);
                for (DeliveryOrderDetail child : childList) {
                    if (parent.getOrigDetailId().equals(child.getParentId())) {
                        details.add(child);
                    }
                }
                lineNo++;
            }
            for (DeliveryOrderDetail other : oterList) {
                other.setLineNo(lineNo);
                details.add(other);
                lineNo++;
            }
            return details;
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * <pre>
     * Description:取消发货单
     * </pre>
     *
     * @throws Exception
     */
    public void cancel() throws Exception {
        deliveryOrderService.cancel(doHeader.getId());
        this.initialized = false;
        this.view();
        this.sayMessage(MESSAGE_SUCCESS);
    }

    /*
     * @see com.daxia.framework.common.action.PagedListBean#query()
     */
    @Override
    public void query() {
        this.buildOrderFilterMap(doHeaderFilter);
        convert2AddressName();
        String releaseStatus = doHeaderFilter.getReleaseStatus();
        Date createTimeFrom = doHeaderFilter.getDoCreateTimeFrom();
        String doType = doHeaderFilter.getDoType();

        if (StringUtil.isNotEmpty(doHeaderFilter.getDoNos()) ||
                StringUtil.isNotEmpty(doHeaderFilter.getOriginalSoCode()) ||
                StringUtil.isNotEmpty(doHeaderFilter.getRefNo1()) ||
                StringUtil.isNotEmpty(doHeaderFilter.getUserDeffine1())) {
            doHeaderFilter.setReleaseStatus(null);
            doHeaderFilter.setDoCreateTimeFrom(null);
            doHeaderFilter.setDoType(null);
        }
        DoHeaderFilter condition = new DoHeaderFilter();
        try {
            condition = (DoHeaderFilter) BeanUtils.cloneBean(doHeaderFilter);
            if (StringUtil.isNotEmpty(condition.getDoNos()) ||
                    StringUtil.isNotEmpty(condition.getOriginalSoCode())) {
                // 如果查询条件包含订单号或平台单号，去掉冻结状态、创建时间的过滤
                condition.setReleaseStatus(null);
                condition.setDoCreateTimeFrom(null);
            }
        } catch (Exception e) {
            condition = doHeaderFilter;
        }
        prepare(condition);
        DataPage<DoHeaderDto> dataPage = deliveryOrderService.query(doHeaderFilter, getStartIndex(), getPageSize());
        this.populateValues(dataPage);
        this.populateSelectedMap(dataPage);
        doHeaderFilter.setReleaseStatus(releaseStatus);
        doHeaderFilter.setDoCreateTimeFrom(createTimeFrom);
        doHeaderFilter.setDoType(doType);

        tdHtml = uiRender.renderColumn(dataPage.getDataList(), tdInfoList, this, doHeaderFilter);
    }

    private void populateSelectedMap(DataPage<DoHeaderDto> dataPage) {
        this.selectedMap.clear();
        if (Config.isDefaultFalse(Keys.Delivery.do_checked_all, Config.ConfigLevel.WAREHOUSE)) {
            List<DoHeaderDto> dataList = dataPage.getDataList();
            int dataListSize = dataList.size();
            if (!ListUtil.isNullOrEmpty(dataList)) {
                for (int i = 0; i < dataListSize; i++) {
                    this.selectedMap.put(dataList.get(i).getId(), Boolean.TRUE);
                }
            }
        }
    }

    private void prepare(DoHeaderFilter condition) {
        condition.setBusinessCustomerType(null);
        if (com.daxia.wms.Constants.DoType.WHOLESALE_SALE.getValue().toString().equals(condition.getDoType())) {
            condition.setDoType(com.daxia.wms.Constants.DoType.WHOLESALE.getValue().toString());
            condition.setBusinessCustomerType(Integer.valueOf(1));
        }
        if (com.daxia.wms.Constants.DoType.MD_SALE.getValue().toString().equals(condition.getDoType())) {
            condition.setDoType(com.daxia.wms.Constants.DoType.WHOLESALE.getValue().toString());
            condition.setBusinessCustomerType(Integer.valueOf(2));
        }
    }

    public boolean isToB() {
        return SystemConfig.configIsOpen(ConfigKeys.GLOBAL_WAREHOUSE_TO_B, ParamUtil.getCurrentWarehouseId());
    }

    /**
     * <pre>
     * Description:冻结订单
     * </pre>
     */
    public void frozen() throws Exception {
        // 防止手输不回车直接点确认，这里需要再次解密一下
        doHeader.setDoNo(DoUtil.decryptDoNo(doHeader.getDoNo()));
        this.deliveryOrderService.manualFrozen(this.doHeader.getId(), this.holdCode, this.holdNotes,
                WmsUtil.getOperateBy());
        /** 冻结do时调scs重新计算预计出库时间接口 */
        expFacadeService.sendDoReleaseOrHold2Scs(doHeader.getId());
        this.holdCode = "";
        this.holdNotes = "";
        this.sayMessage(MESSAGE_SUCCESS);
    }

    public void batchFrozen() {
        String[] ids = doIdStrs.split(",");
        List<Long> idList = new ArrayList<Long>();
        for (String id : ids) {
            if (StringUtil.isNotBlank(id)) {
                idList.add(Long.valueOf(id));
            }
        }
        deliveryOrderService.batchManualFrozen(idList, this.holdCode, this.holdNotes, WmsUtil.getOperateBy());
        this.sayMessage(MESSAGE_SUCCESS);
    }

    /**
     * 修改配送商
     */
    public void modifyCarrier() {
        DeliveryOrderHeader deliveryOrderHeader = deliveryOrderService.getDoHeaderById(this.doHeader.getId());

        Long fmCarrierId = deliveryOrderHeader.getCarrierId();

        // 修改前后，配送商不一致，则执行修改配送商逻辑
        if (isNeedModify(deliveryOrderHeader)) {
            // 修改配送信息,包涵修改配送商,修改地址
            deliveryOrderService.modifyCarrier(fmCarrierId, deliveryOrderHeader);
        }
        this.sayMessage(MESSAGE_SUCCESS);
    }

    /**
     * 修改配送商
     */
    public void addDoLog() {
        DeliveryOrderHeader deliveryOrderHeader = deliveryOrderService.getDoHeaderById(this.doHeader.getId());
        orderLogService.saveLog(deliveryOrderHeader, this.operateType, this.operateLog);
        this.sayMessage(MESSAGE_SUCCESS);
    }

    private Boolean isNeedModify(DeliveryOrderHeader deliveryOrderHeader) {
        Boolean flag = Boolean.FALSE;
        if (null == deliveryOrderHeader) {
            return flag;
        }
        Long carrierId = this.doHeader.getCarrierId();
        Long provinceId = this.doHeader.getProvince();
        Long cityId = this.doHeader.getCity();
        Long countyId = this.doHeader.getCounty();
        String address = this.doHeader.getAddress();
        if (carrierId != null && !carrierId.equals(deliveryOrderHeader.getCarrierId())) {
            deliveryOrderHeader.setCarrierId(carrierId);
            flag = true;
        }
        if (provinceId != null && !provinceId.equals(deliveryOrderHeader.getProvince())) {
            deliveryOrderHeader.setProvince(provinceId);
            Province province = areaService.getProvince(provinceId);
            if (null != province) {
                deliveryOrderHeader.setProvinceName(province.getProvinceCname());
            }
            flag = true;
        }
        if (cityId != null && !cityId.equals(deliveryOrderHeader.getCity())) {
            deliveryOrderHeader.setCity(cityId);
            City city = areaService.getCity(cityId);
            if (null != city) {
                deliveryOrderHeader.setCityName(city.getCityCname());
            }
            flag = true;
        }
        if (countyId != null && !countyId.equals(deliveryOrderHeader.getCounty())) {
            deliveryOrderHeader.setCounty(countyId);
            County county = areaService.getCounty(countyId);
            if (null != county) {
                deliveryOrderHeader.setCountyName(county.getCountyCname());
            }
            flag = true;
        }
        if (StringUtils.isNotBlank(address) && !address.equals(deliveryOrderHeader.getAddress())) {
            deliveryOrderHeader.setAddress(address);
            flag = true;
        }
        return flag;
    }

    /**
     * 渠道店铺二级联动
     */
    public void changeStoreListByChannelCode() {
        storeList.clear();
        List<SelectItem> selectItems = businessCenterComponent
                .changeStoreListByChannelCode(doHeaderFilter.getChannelCode());
        storeList.addAll(selectItems);
    }

    /**
     * <pre>
     * Description:获取单位
     * </pre>
     *
     * @param id
     * @return
     */
    public String getUomByDoHeaderId(Long id) {
        return deliveryOrderService.getUomByHeaderId(id);
    }

    public void findProvinceList() {
        List<Province> list = this.areaService.findAllProvince();
        for (Province province : list) {
            SelectItem item = new SelectItem();
            item.setValue(province.getId());
            item.setLabel(province.getProvinceCname());
            provinces.add(item);
        }
    }

    /**
     * <pre>
     * Description: 国家下拉列表发生变动事件
     * </pre>
     */
    public void changeListeForProvince() {
        provinces.clear();
        this.doHeaderFilter.setProvince(null);
        this.doHeaderFilter.setCity(null);
        this.doHeaderFilter.setCounty(null);
        if (this.countryId != null && this.countryId.longValue() != -1) {
            List<Province> list = this.areaService.findProvinceByCountry(this.countryId);
            for (Province province : list) {
                SelectItem item = new SelectItem();
                item.setValue(province.getId());
                item.setLabel(province.getProvinceCname());
                provinces.add(item);
            }
        }
        this.countryId = null;
        cites.clear();
        counties.clear();
    }

    /**
     * <pre>
     * Description: 省份下拉列表发生变动事件
     * </pre>
     */
    public void changeListeForCity() {
        cites.clear();
        this.doHeaderFilter.setCity(null);
        this.doHeaderFilter.setCounty(null);
        if (this.provinceId != null && this.provinceId.longValue() != -1) {
            List<City> list = this.areaService.findCityByProvince(this.provinceId);
            for (City city : list) {
                SelectItem item = new SelectItem();
                item.setValue(city.getId());
                item.setLabel(city.getCityCname());
                cites.add(item);
            }
        }
        this.provinceId = null;
        counties.clear();
    }

    /**
     * <pre>
     * Description:城市下拉列表发生变动事件
     * </pre>
     */
    public void changeListeForCounty() {
        counties.clear();
        this.doHeaderFilter.setCounty(null);
        if (this.cityId != null && this.cityId.longValue() != -1) {
            List<County> list = this.areaService.findCountyByCity(this.cityId);
            for (County county : list) {
                SelectItem item = new SelectItem();
                item.setValue(county.getId());
                item.setLabel(county.getCountyCname());
                counties.add(item);
            }
        }
        this.cityId = null;
    }

    /**
     * 获取选中的doid
     *
     * @return 选中的DOid lit
     */
    private List<Long> getPrintDoIds() {
        List<Long> ids = new ArrayList<Long>();

        if (this.doHeaderId == null) {
            for (Object id : getSelectedRowList()) {
                ids.add((Long) id);
            }
        } else {
            ids.add(this.doHeaderId);
        }
        return ids;
    }

    /**
     * 订单打印、预览 - 根据打印标志分发到不同的打印方法
     */
    public void print() {
        this.printData = "[]";
        printContent = "";
         List<Long> ids = this.getPrintDoIds();
        if (ListUtil.isNullOrEmpty(ids)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }

        DeliveryOrderHeader deliveryOrderHeader = deliveryOrderService.getDoHeaderById(ids.get(0));
        if (deliveryOrderHeader == null || deliveryOrderHeader.getWaveId() == null) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        if ("printDo".equals(this.printFlag)) {
            printDo();
        } else if ("previewDo".equals(this.printFlag)) {
            previewDo();
        } else if ("printWaybill".equals(this.printFlag)) {
            printWaybill();
        } else if ("previewWaybill".equals(this.printFlag)) {
            previewWaybill();
        } else {
            // 默认行为，保持向后兼容
            printDo();
        }
    }

    /**
     * 打印发货单
     */
    private void printDo() {
        this.printData = "[]";
        printContent = "";

        List<Long> ids = this.getPrintDoIds();
        if (ListUtil.isNullOrEmpty(ids)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }

        DeliveryOrderHeader deliveryOrderHeader = deliveryOrderService.getDoHeaderById(ids.get(0));
        if (deliveryOrderHeader == null) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }

        if (StringUtil.isIn(deliveryOrderHeader.getDoType(), Constants.DoType.SELL.getValue(),
                Constants.DoType.WHOLESALE.getValue())) {
            printContent = printDoService.genData(this.getPrintDoIds());
        } else {
            List<String> pages = printService.getDoReportByDoHeaderIds(ids);

            this.doPrintCfg = printDoService.setDoPrintCfg(ids.get(0), null);
            this.printData = new JSONArray(pages).toString();
        }
    }

    /**
     * 预览发货单
     */
    private void previewDo() {
        // 预览和打印使用相同的逻辑，只是调用方式不同
        printDo();
    }

    /**
     * 打印运单
     */
    private void printWaybill() {
        this.printData = "[]";
        printContent = "";

        List<Long> ids = this.getPrintDoIds();
        if (ListUtil.isNullOrEmpty(ids)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }

        // 使用面单打印功能
        List<PrintData> resultList = printCartonDispatcher.printTempCarton(ids);
        printData = new Gson().toJson(resultList);
        printContent = WaybillPrintHelper.getPrintJs(resultList);
    }

    /**
     * 预览运单
     */
    private void previewWaybill() {
        // 预览和打印使用相同的逻辑，只是调用方式不同
        printWaybill();
    }

    public void printTempCarton() {
        List<PrintData> resultList = printCartonDispatcher.printTempCarton(getPrintDoIds());
        printData = new Gson().toJson(resultList);
        printContent = WaybillPrintHelper.getPrintJs(resultList);
    }

    @SuppressWarnings("unchecked")
    public void importDo(UploadEvent event) {
        errorCodes = "";
        uploadFileMsg = ResourceUtils.getDispalyString(MESSAGE_SUCCESS);

        Set<String> errorCodeSet = null;
        try {
            errorCodeSet = (Set<String>) FileImportTemplate.importFile(event, new ImportFileReader() {

                @Override
                public Object readFileList(List<String> fileList) {
                    return deliveryOrderImportService.importDo(fileList);
                }
            });
        } catch (Exception e) {
            uploadFileMsg = FileImportTemplate.getExceptionDispalyMsg(e, MESSAGE_FAILED);
        }

        if (errorCodeSet != null && !errorCodeSet.isEmpty()) {
            errorCodes = ListUtil.collection2String(errorCodeSet, ",");
            uploadFileMsg = ResourceUtils.getDispalyString(MESSAGE_FAILED);
        }
    }

    /**
     * 发运确认 - 导出明细
     * 
     * @throws IOException
     */
    public void export() throws IOException {
        this.buildOrderFilterMap(doHeaderFilter);
        convert2AddressName();
        String releaseStatus = doHeaderFilter.getReleaseStatus();
        Date createTimeFrom = doHeaderFilter.getDoCreateTimeFrom();
        String doType = doHeaderFilter.getDoType();

        if (StringUtil.isNotEmpty(doHeaderFilter.getDoNos()) ||
                StringUtil.isNotEmpty(doHeaderFilter.getOriginalSoCode()) ||
                StringUtil.isNotEmpty(doHeaderFilter.getRefNo1())) {
            doHeaderFilter.setReleaseStatus(null);
            doHeaderFilter.setDoCreateTimeFrom(null);
            doHeaderFilter.setDoType(null);
        }
        BigDecimal totalNum = deliveryOrderService.countAllDetailResults(doHeaderFilter);
        Integer totalMax = SystemConfig.getConfigValueInt("normal.maxNum.export", ParamUtil.getCurrentWarehouseId());
        BigDecimal totalMaxLong = BigDecimal.valueOf(totalMax.longValue());
        if (totalMaxLong.compareTo(totalNum) < 0) {
            throw new DeliveryException("导出数据超过最大数量限制");
        }
        if (totalNum.compareTo(BigDecimal.ZERO) == 0) {
            throw new DeliveryException(DeliveryException.NO_RESULT);
        }
        byte[] bytes;
        List<DoDetailDto> dtos = deliveryOrderService.getAllDetailResults(doHeaderFilter);
        // 收货人和详细地址脱敏
        dtos.forEach(r -> {
            if (StringUtil.isNotEmpty(r.getConsigneeName())) {
                r.setConsigneeName(
                        StringUtil.rightPad(r.getConsigneeName().substring(0, 1), r.getConsigneeName().length(), '*'));
            }
            if (StringUtil.isNotEmpty(r.getAddress())) {
                r.setAddress(StringUtil.rightPad("", r.getAddress().length(), '*'));
            }
        });
        try {
            bytes = ExportDeliveryOrderUtil.generateForDoDetail(dtos);
        } catch (IOException e) {
            throw new DeliveryException("导出失败");
        }
        this.selectedMap.clear();
        doHeaderFilter.setReleaseStatus(releaseStatus);
        doHeaderFilter.setDoCreateTimeFrom(createTimeFrom);
        doHeaderFilter.setDoType(doType);
        boolean isEnglish = BooleanUtils.isTrue(AppConfig.useEnglish());
        DownloadUtil.writeToResponse(bytes, DownloadUtil.EXCEL, isEnglish ? "DeliveryOrderDetails.xls" : "发运订单明细.xls");
    }

    /**
     * 发运确认 - 导出
     */
    @Loggable
    public void exportDo() throws IOException {
        this.buildOrderFilterMap(doHeaderFilter);
        convert2AddressName();
        String releaseStatus = doHeaderFilter.getReleaseStatus();
        Date createTimeFrom = doHeaderFilter.getDoCreateTimeFrom();
        String doType = doHeaderFilter.getDoType();

        if (StringUtil.isNotEmpty(doHeaderFilter.getDoNos()) ||
                StringUtil.isNotEmpty(doHeaderFilter.getOriginalSoCode()) ||
                StringUtil.isNotEmpty(doHeaderFilter.getRefNo1())) {
            doHeaderFilter.setReleaseStatus(null);
            doHeaderFilter.setDoCreateTimeFrom(null);
            doHeaderFilter.setDoType(null);
        }
        BigDecimal totalNum = deliveryOrderService.countAllResults(doHeaderFilter);
        Integer totalMax = SystemConfig.getConfigValueInt("normal.maxNum.export", ParamUtil.getCurrentWarehouseId());
        BigDecimal totalMaxLong = BigDecimal.valueOf(totalMax.longValue());
        if (totalMaxLong.compareTo(totalNum) < 0) {
            throw new DeliveryException("导出数据超过最大数量限制");
        }
        if (totalNum.compareTo(BigDecimal.ZERO) == 0) {
            throw new DeliveryException(DeliveryException.NO_RESULT);
        }
        byte[] bytes;
        List<DoHeaderDto> dtos = deliveryOrderService.getAllResults(doHeaderFilter);
        // 收货人和详细地址脱敏
        dtos.forEach(r -> {
            if (StringUtil.isNotEmpty(r.getConsigneeName())) {
                r.setConsigneeName(
                        StringUtil.rightPad(r.getConsigneeName().substring(0, 1), r.getConsigneeName().length(), '*'));
            }
            if (StringUtil.isNotEmpty(r.getAddress())) {
                r.setAddress(StringUtil.rightPad("", r.getAddress().length(), '*'));
            }
        });
        try {
            bytes = ExportDeliveryOrderUtil.generateForDo(dtos);
        } catch (IOException e) {
            throw new DeliveryException("导出失败");
        }
        this.selectedMap.clear();
        doHeaderFilter.setReleaseStatus(releaseStatus);
        doHeaderFilter.setDoCreateTimeFrom(createTimeFrom);
        doHeaderFilter.setDoType(doType);
        boolean isEnglish = BooleanUtils.isTrue(AppConfig.useEnglish());
        DownloadUtil.writeToResponse(bytes, DownloadUtil.EXCEL, isEnglish ? "DeliveryOrders.xls" : "发运订单.xls");
    }

    /**
     * 渠道放大镜
     * 
     * @param shopId
     */
    public void receiveSelectShop(Long shopId) {
        if (shopId == null) {
            shopName = "";
        } else {
            ShopInfo shopInfo = shopInfoService.get(shopId);
            shopName = shopInfo.getName();
            this.doHeaderFilter.setShopId(shopId);
        }
    }

    public void clearSelectShop() {
        shopName = "";
        this.doHeaderFilter.setShopId(null);
    }

    /**
     * 药网RTV打印
     */
    public void printRTV() {
        this.printData = "[]";

        List<Long> ids = this.getPrintDoIds();

        List<String> pages = printService.getRTVReportByDoHeaderIds(ids);

        this.printData = new JSONArray(pages).toString();
    }

    /**
     * 药网出库单打印，3联
     */
    public void printYaoTo() {
        this.printData = "[]";
        this.printContent = "";
        List<Long> ids = this.getPrintDoIds();
        if (Config.isDefaultFalse(Keys.Print.do_yaoToPrintByFtl, Config.ConfigLevel.TENANT)) {
            printContent = printService.genYaoToData(ids);
        } else {
            List<String> pages = printService.getYaoToReportByDoHeaderIds(ids);
            this.printData = new JSONArray(pages).toString();
        }
    }

    /**
     * 药网出库单打印，3联
     */
    public void printYaoRtv() {
        this.printData = "[]";
        this.printContent = "";
        List<Long> ids = this.getPrintDoIds();

        List<String> pages = printService.getYaoRtvReportByDoHeaderIds(ids);

        this.printData = new JSONArray(pages).toString();
    }

    public void getDoTimeInfo() {
        // doTimeInfoDto = deliveryOrderService.getDoTimeInfo(this.doHeaderId,
        // this.doHeaderFilter.getQueryHis() != null
        // && Boolean.TRUE.equals(this.doHeaderFilter.getQueryHis()));
    }

    public void getDoLogInfo() {
        orderLogList = orderLogService.findLogByOrderId(doHeader.getId());
    }

    public void getDoNotice() {
        doNotices = doNoticeService.getNotices(this.doHeaderId);
    }

    public ShopInfo getShopInfo(Long shopId) {
        ShopInfo shopInfo = shopInfoService.get(shopId);
        return shopInfo;
    }

    public void setDoDetail(DeliveryOrderDetail doDetail) {
        this.doDetail = doDetail;
    }

    public DeliveryOrderDetail getDoDetail() {
        return doDetail;
    }

    public void setDoHeader(DeliveryOrderHeader doHeader) {
        this.doHeader = doHeader;
    }

    public DeliveryOrderHeader getDoHeader() {
        return doHeader;
    }

    public Long getDoDetailId() {
        return doDetailId;
    }

    public void setDoDetailId(Long doDetailId) {
        this.doDetailId = doDetailId;
    }

    public void setStockBatchLocLpns(List<StockBatchLocLpn> stockBatchLocLpns) {
        this.stockBatchLocLpns = stockBatchLocLpns;
    }

    public List<StockBatchLocLpn> getStockBatchLocLpns() {
        return stockBatchLocLpns;
    }

    public void setAssignNumber(Double assignNumber) {
        this.assignNumber = assignNumber;
    }

    public Double getAssignNumber() {
        return assignNumber;
    }

    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }

    public Long getDoHeaderId() {
        return doHeaderId;
    }

    public DoHeaderFilter getDoHeaderFilter() {
        return doHeaderFilter;
    }

    public void setDoHeaderFilter(DoHeaderFilter doHeaderFilter) {
        this.doHeaderFilter = doHeaderFilter;
    }

    public DeliveryOrderService getDeliveryOrderService() {
        return deliveryOrderService;
    }

    public void setDeliveryOrderService(DeliveryOrderService deliveryOrderService) {
        this.deliveryOrderService = deliveryOrderService;
    }

    public void setDoDetailFilter(DoDetailFilter doDetailFilter) {
        this.doDetailFilter = doDetailFilter;
    }

    public DoDetailFilter getDoDetailFilter() {
        return doDetailFilter;
    }

    public void setJudge(boolean judge) {
        this.judge = judge;
    }

    public boolean isJudge() {
        return judge;
    }

    public void setFrozenOrNot(boolean frozenOrNot) {
        this.frozenOrNot = frozenOrNot;
    }

    public boolean isFrozenOrNot() {
        return frozenOrNot;
    }

    public WaveHeader getWaveHeader() {
        return waveHeader;
    }

    public void setWaveHeader(WaveHeader waveHeader) {
        this.waveHeader = waveHeader;
    }

    public WaveService getWaveService() {
        return waveService;
    }

    public void setWaveService(WaveService waveService) {
        this.waveService = waveService;
    }

    public List<SelectItem> getProvinces() {
        return provinces;
    }

    public void setProvinces(List<SelectItem> provinces) {
        this.provinces = provinces;
    }

    public List<SelectItem> getCites() {
        return cites;
    }

    public void setCites(List<SelectItem> cites) {
        this.cites = cites;
    }

    public List<SelectItem> getCounties() {
        return counties;
    }

    public void setCounties(List<SelectItem> counties) {
        this.counties = counties;
    }

    public List<SelectItem> getFrozenCodes() {
        return frozenCodes;
    }

    public void setFrozenCodes(List<SelectItem> frozenCodes) {
        this.frozenCodes = frozenCodes;
    }

    public Long getCountryId() {
        return countryId;
    }

    public void setCountryId(Long countryId) {
        this.countryId = countryId;
    }

    public Long getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Long provinceId) {
        this.provinceId = provinceId;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public void setPrintData(String printData) {
        this.printData = printData;
    }

    public String getPrintData() {
        return printData;
    }

    public void setInitialized(boolean initialized) {
        this.initialized = initialized;
    }

    public CartonHeader getCartonHeader() {
        return cartonHeader;
    }

    public void setCartonHeader(CartonHeader cartonHeader) {
        this.cartonHeader = cartonHeader;
    }

    public int getCartonCount() {
        return cartonCount;
    }

    public void setCartonCount(int cartonCount) {
        this.cartonCount = cartonCount;
    }

    public DoHeaderDto getDoHeaderDto() {
        return doHeaderDto;
    }

    public void setDoHeaderDto(DoHeaderDto doHeaderDto) {
        this.doHeaderDto = doHeaderDto;
    }

    public DoHeaderFilter getDoHeaderFilterForDoMonitor() {
        return doHeaderFilterForDoMonitor;
    }

    public void setDoHeaderFilterForDoMonitor(
            DoHeaderFilter doHeaderFilterForDoMonitor) {
        this.doHeaderFilterForDoMonitor = doHeaderFilterForDoMonitor;
    }

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public String getHoldCode() {
        return holdCode;
    }

    public void setHoldCode(String holdCode) {
        this.holdCode = holdCode;
    }

    public String getHoldNotes() {
        return holdNotes;
    }

    public void setHoldNotes(String holdNotes) {
        this.holdNotes = holdNotes;
    }

    public DoTimeInfoDto getDoTimeInfoDto() {
        return doTimeInfoDto;
    }

    public void setDoTimeInfoDto(DoTimeInfoDto doTimeInfoDto) {
        this.doTimeInfoDto = doTimeInfoDto;
    }

    public PrintCfg getDoPrintCfg() {
        return doPrintCfg;
    }

    public void setDoPrintCfg(PrintCfg doPrintCfg) {
        this.doPrintCfg = doPrintCfg;
    }

    public DeliveryOrderHeaderHis getDoHeaderHis() {
        return doHeaderHis;
    }

    public void setDoHeaderHis(DeliveryOrderHeaderHis doHeaderHis) {
        this.doHeaderHis = doHeaderHis;
    }

    public CartonHeaderHis getCartonHeaderHis() {
        return cartonHeaderHis;
    }

    public void setCartonHeaderHis(CartonHeaderHis cartonHeaderHis) {
        this.cartonHeaderHis = cartonHeaderHis;
    }

    public String getUploadFileMsg() {
        return uploadFileMsg;
    }

    public void setUploadFileMsg(String uploadFileMsg) {
        this.uploadFileMsg = uploadFileMsg;
    }

    public String getErrorCodes() {
        return errorCodes;
    }

    public void setErrorCodes(String errorCodes) {
        this.errorCodes = errorCodes;
    }

    public List<SelectItem> getReplTrucks() {
        return replTrucks;
    }

    public void setReplTrucks(List<SelectItem> replTrucks) {
        this.replTrucks = replTrucks;
    }

    public String getPrintContent() {
        return printContent;
    }

    public void setPrintContent(String printContent) {
        this.printContent = printContent;
    }

    /**
     * 获取发货单状态（当为卓志仓库时，去除待审核状态）
     *
     * @throws Exception
     */
    public List<SelectItem> getStatus(String dictName) {
        String STATUS_ACTIVE = "1";// 有效
        List<SelectItem> list = new ArrayList<SelectItem>();
        List<CfgCodeDetail> codeList = Dictionary.findByCategory(dictName);
        for (CfgCodeDetail c : codeList) {
            if (!STATUS_ACTIVE.equals(c.getStatus())) {
                continue;
            }
            SelectItem item = new SelectItem();
            item.setValue(c.getCodeValue().trim());
            String msgKey = c.getMessageKey();
            String text = null;
            if (msgKey == null || msgKey.trim().length() == 0) {
                text = c.getCodeName();
            } else {
                text = ResourceUtils.getDispalyString(msgKey);
            }
            item.setLabel(text);
            item.setDescription(text);
            list.add(item);
        }
        return list;
    }

    public void receiveSelectBusinessCustomer(Long businessCustomerId) {
        BusinessCustomer businessCustomer = null;
        if (businessCustomerId == null) {
            businessCustomer = new BusinessCustomer();
        } else {
            businessCustomer = businessCustomerService.getBusinessCustomer(businessCustomerId);
        }
        this.doHeaderFilter.setBusinessCustomerId(businessCustomerId);
        this.setCustomerName(businessCustomer.getCustomerName());

    }

    public void clearBusinessCustomer() {
        this.doHeaderFilter.setBusinessCustomerId(null);
        this.setCustomerName(null);
    }

    public List<DoNotice> getDoNotices() {
        return doNotices;
    }

    public void setDoNotices(List<DoNotice> doNotices) {
        this.doNotices = doNotices;
    }

    public String getWayBill() {
        return wayBill;
    }

    public String getPageFm() {
        return pageFm;
    }

    public void setPageFm(String pageFm) {
        this.pageFm = pageFm;
    }

    public void setWayBill(String wayBill) {
        this.wayBill = wayBill;
    }

    public List<OrderLog> getOrderLogList() {
        return orderLogList;
    }

    public void setOrderLogList(List<OrderLog> orderLogList) {
        this.orderLogList = orderLogList;
    }

    public List<DoAlcDetailInfoDto> getDoAlcDetailInfoDtos() {
        return doAlcDetailInfoDtos;
    }

    public void setDoAlcDetailInfoDtos(List<DoAlcDetailInfoDto> doAlcDetailInfoDtos) {
        this.doAlcDetailInfoDtos = doAlcDetailInfoDtos;
    }

    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public String getDoIdStrs() {
        return doIdStrs;
    }

    public void setDoIdStrs(String doIdStrs) {
        this.doIdStrs = doIdStrs;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Integer getCartonPrintPoint() {
        return cartonPrintPoint;
    }

    public void setCartonPrintPoint(Integer cartonPrintPoint) {
        this.cartonPrintPoint = cartonPrintPoint;
    }

    public List<Carrier> getCarrierList() {
        return carrierList;
    }

    public void setCarrierList(List<Carrier> carrierList) {
        this.carrierList = carrierList;
    }

    public List<SelectItem> getShopInfos() {
        return shopInfos;
    }

    public void setShopInfos(List<SelectItem> shopInfos) {
        this.shopInfos = shopInfos;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public Integer getDoCount() {
        return doCount;
    }

    public void setDoCount(Integer doCount) {
        this.doCount = doCount;
    }

    public String getUdf1Name() {
        return udf1Name;
    }

    public void setUdf1Name(String udf1Name) {
        this.udf1Name = udf1Name;
    }

    public String getUdf2Name() {
        return udf2Name;
    }

    public void setUdf2Name(String udf2Name) {
        this.udf2Name = udf2Name;
    }

    public String getUdf3Name() {
        return udf3Name;
    }

    public void setUdf3Name(String udf3Name) {
        this.udf3Name = udf3Name;
    }

    public String getUdf4Name() {
        return udf4Name;
    }

    public void setUdf4Name(String udf4Name) {
        this.udf4Name = udf4Name;
    }

    public String getUdf5Name() {
        return udf5Name;
    }

    public void setUdf5Name(String udf5Name) {
        this.udf5Name = udf5Name;
    }

    public String getUdf1Dict() {
        return udf1Dict;
    }

    public void setUdf1Dict(String udf1Dict) {
        this.udf1Dict = udf1Dict;
    }

    public String getUdf2Dict() {
        return udf2Dict;
    }

    public void setUdf2Dict(String udf2Dict) {
        this.udf2Dict = udf2Dict;
    }

    public String getUdf3Dict() {
        return udf3Dict;
    }

    public void setUdf3Dict(String udf3Dict) {
        this.udf3Dict = udf3Dict;
    }

    public String getUdf4Dict() {
        return udf4Dict;
    }

    public void setUdf4Dict(String udf4Dict) {
        this.udf4Dict = udf4Dict;
    }

    public String getUdf5Dict() {
        return udf5Dict;
    }

    public void setUdf5Dict(String udf5Dict) {
        this.udf5Dict = udf5Dict;
    }

    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType;
    }

    public String getOperateLog() {
        return operateLog;
    }

    public void setOperateLog(String operateLog) {
        this.operateLog = operateLog;
    }

    public List<UiRender.TdInfo> getTdInfoList() {
        return tdInfoList;
    }

    public void setTdInfoList(List<UiRender.TdInfo> tdInfoList) {
        this.tdInfoList = tdInfoList;
    }

    public UiRender.TdHtml getTdHtml() {
        return tdHtml;
    }

    public void setTdHtml(UiRender.TdHtml tdHtml) {
        this.tdHtml = tdHtml;
    }

    public List<SelectItem> getSpecialLabelList() {
        return specialLabelList;
    }

    public void setSpecialLabelList(List<SelectItem> specialLabelList) {
        this.specialLabelList = specialLabelList;
    }

    public List<SelectItem> getStoreList() {
        return storeList;
    }

    public void setStoreList(List<SelectItem> storeList) {
        this.storeList = storeList;
    }

    private String countryCode;
    private String provinceCode;
    private String cityCode;
    private String countyCode;

    private String countryName;
    private String provinceName;
    private String cityName;
    private String countyName;

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCountyCode() {
        return countyCode;
    }

    public void setCountyCode(String countyCode) {
        this.countyCode = countyCode;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCountyName() {
        return countyName;
    }

    public void setCountyName(String countyName) {
        this.countyName = countyName;
    }

    @In
    private AddressService addressService;

    /**
     * 得到所有的国家信息
     *
     * @return 国家信息
     */
    public List<SelectItem> findCountryList() {
        List<BaseAddressDTO> baseAddressList = addressService.findAllCountryByAddressLibrary(Constants.ZERO);
        return baseAddressList.stream().map(baseAddress -> {
            SelectItem selectItem = new SelectItem();
            selectItem.setLabel(baseAddress.getName());
            selectItem.setValue(baseAddress.getAddressCode());
            return selectItem;
        }).collect(Collectors.toList());
    }

    /**
     * 国家下拉列表发生变动事件 省份联动变更
     */
    public void changeListForProvince() {
        provinces.clear();
        this.doHeaderFilter.setProvinceName(null);
        this.doHeaderFilter.setCityName(null);
        this.doHeaderFilter.setCountyName(null);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(countryCode)) {
            List<BaseAddressDTO> list = addressService.findAllCountryByAddressLibrary(countryCode);
            for (BaseAddressDTO province : list) {
                SelectItem item = new SelectItem();
                item.setLabel(province.getName());
                item.setValue(province.getAddressCode());
                provinces.add(item);
            }
        }
        this.countryCode = null;
        cites.clear();
        counties.clear();
    }

    /**
     * 省份下拉列表发生变动事件
     */
    public void changeListForCity() {
        cites.clear();
        this.doHeaderFilter.setCityName(null);
        this.doHeaderFilter.setCountyName(null);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(provinceCode)) {
            List<BaseAddressDTO> list = addressService.findAllCountryByAddressLibrary(provinceCode);
            for (BaseAddressDTO city : list) {
                SelectItem item = new SelectItem();
                item.setLabel(city.getName());
                item.setValue(city.getAddressCode());
                cites.add(item);
            }
        }
        this.provinceCode = null;
        counties.clear();
    }

    /**
     * 城市下拉列表发生变动事件
     */
    public void changeListForCounty() {
        counties.clear();
        this.doHeaderFilter.setCountyName(null);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(cityCode)) {
            List<BaseAddressDTO> list = addressService.findAllCountryByAddressLibrary(cityCode);
            for (BaseAddressDTO county : list) {
                SelectItem item = new SelectItem();
                item.setValue(county.getAddressCode());
                item.setLabel(county.getName());
                counties.add(item);
            }
        }
        this.cityCode = null;
    }

    private void convert2AddressName() {

        if (org.apache.commons.lang3.StringUtils.isNotBlank(countryCode)) {
            BaseAddressDTO addressDTO = addressService.findByAddressCode(countryCode);
            doHeaderFilter.setCountry(addressDTO == null ? null : addressDTO.getName());
        } else {
            doHeaderFilter.setCountry(null);
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(provinceCode)) {
            BaseAddressDTO addressDTO = addressService.findByAddressCode(provinceCode);
            doHeaderFilter.setProvinceName(addressDTO == null ? null : addressDTO.getName());
        } else {
            doHeaderFilter.setProvinceName(null);
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(cityCode)) {
            BaseAddressDTO addressDTO = addressService.findByAddressCode(cityCode);
            doHeaderFilter.setCityName(addressDTO == null ? null : addressDTO.getName());
        } else {
            doHeaderFilter.setCityName(null);
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(countyCode)) {
            BaseAddressDTO addressDTO = addressService.findByAddressCode(countyCode);
            doHeaderFilter.setCountyName(addressDTO == null ? null : addressDTO.getName());
        } else {
            doHeaderFilter.setCountyName(null);
        }

    }

    public String getPrintFlag() {
        return printFlag;
    }

    public void setPrintFlag(String printFlag) {
        this.printFlag = printFlag;
    }


}