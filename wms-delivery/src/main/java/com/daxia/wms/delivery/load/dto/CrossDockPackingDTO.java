package com.daxia.wms.delivery.load.dto;

import java.io.Serializable;
import java.util.List;

import com.daxia.wms.delivery.load.entity.CrossDockPacking;

/**
 * 越库发货时CrossDockPackingDTO的Dto
 */
@lombok.extern.slf4j.Slf4j
public class CrossDockPackingDTO implements Serializable {
	
	private static final long serialVersionUID = 607074415833938158L;

	private String virtualId;

	private CrossDockPacking crossDockPacking;
	
	private List<StockCrossDockDTO> stockCrossDockDTOList;
	
	public String getVirtualId() {
		return virtualId;
	}

	public void setVirtualId(String virtualId) {
		this.virtualId = virtualId;
	}

	public CrossDockPacking getCrossDockPacking() {
		return crossDockPacking;
	}

	public void setCrossDockPacking(CrossDockPacking crossDockPacking) {
		this.crossDockPacking = crossDockPacking;
	}

	public List<StockCrossDockDTO> getStockCrossDockDTOList() {
		return stockCrossDockDTOList;
	}

	public void setStockCrossDockDTOList(List<StockCrossDockDTO> stockCrossDockDTOList) {
		this.stockCrossDockDTOList = stockCrossDockDTOList;
	}
}
