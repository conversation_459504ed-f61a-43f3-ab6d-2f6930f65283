package com.daxia.wms.delivery.crossorder.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.crossorder.dto.CrossSeedDTO;
import com.daxia.wms.delivery.crossorder.entity.CrossSeedHeader;
import com.daxia.wms.delivery.crossorder.filter.CrossSeedFilter;
import com.google.common.collect.ImmutableMap;
import org.hibernate.Hibernate;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.jboss.seam.annotations.Name;

import java.util.ArrayList;
import java.util.List;

@Name("com.daxia.wms.delivery.crossSeedHeaderDAO")
@lombok.extern.slf4j.Slf4j
public class CrossSeedHeaderDAO extends HibernateBaseDAO<CrossSeedHeader, Long> {

    private static final long serialVersionUID = 1440279412821645173L;

    public List<String> findAsnForCreateAndAllocate() {
        String sql = " SELECT aa.asn_no ";
        sql += " FROM ( ";
        sql += " SELECT LENGTH(ah.userdefine04)- LENGTH( ";
        sql += " REPLACE(ah.userdefine04,',',''))+1 asnCount, COUNT(dh.id) doCount,ah.asn_no,ah.warehouse_id,ah.create_time,ah.asn_status ";
        sql += " FROM doc_asn_header ah inner join doc_do_header dh on ah.id = dh.source_asn_id AND ah.warehouse_id = dh.warehouse_id ";
        sql += " WHERE ah.warehouse_id = :warehouseId ";
        sql += " AND ah.need_crossstock = 1 AND dh.need_crossstock = 1 AND LENGTH(ah.userdefine04)>0 ";
        sql += " GROUP BY ah.id) aa ";
        sql += " LEFT JOIN doc_cross_seed_header ch ON aa.asn_no = ch.asn_no AND aa.warehouse_id = ch.warehouse_id ";
        sql += " WHERE aa.asnCount = aa.doCount AND (ch.id IS NULL OR ch.status = :status) and aa.asn_status = :asnStatus ";
        sql += " ORDER BY aa.create_time limit 100 ";
        Query query = this.createSQLQuery(sql);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("status", Constants.CrossSeedStatus.INITIAL.getValue());
        query.setParameter("asnStatus", Constants.AsnStatus.ORDER_CLOSED.getValue());
        return query.list();
    }

    public CrossSeedHeader getCrossSeedByAsnNo(String asnNo) {
        String hql = "from CrossSeedHeader ch where ch.asnNo = :asnNo and ch.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setString("asnNo", asnNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (CrossSeedHeader) query.uniqueResult();
    }

    public void updateAllocate(Long crossSeedHeaderId) {
        String hql = " UPDATE CrossSeedHeader ch SET ch.status = :toStatus ";
        hql += " WHERE ch.id =:crossSeedHeaderId AND NOT EXISTS ( ";
        hql += " SELECT 1 ";
        hql += " FROM CrossSeedDetail cd ";
        hql += " WHERE cd.headerId = ch.id AND cd.expectedQty > cd.allocatedQty) AND ch.status=: fmStatus ";
        Query query = this.createQuery(hql);
        query.setParameter("crossSeedHeaderId", crossSeedHeaderId);
        query.setParameter("toStatus", Constants.CrossSeedStatus.ALLALLOCATED.getValue());
        query.setParameter("fmStatus", Constants.CrossSeedStatus.INITIAL.getValue());
        query.executeUpdate();
    }

    public DataPage<CrossSeedDTO> findCrossSeedInfo(CrossSeedFilter crossSeedFilter, Integer startIndex, Integer pageSize) {
        Long whId = ParamUtil.getCurrentWarehouseId();
        StringBuilder hql = new StringBuilder("select new com.daxia.wms.delivery.crossorder.dto.CrossSeedDTO(" +
        "o.id, o.seedNo, o.status, o.asnNo, o.doNum, o.units,o.actualUnits, o.isPrinted, o.seedFromTime, o.seedToTime,o.createdBy, o.createdAt,o.updatedBy,o.updatedAt,d.doNo,d.containerNo" +
                ")");
//        StringBuilder hql = new StringBuilder(" from CrossSeedHeader o");
        hql.append(" from CrossSeedHeader o");
        hql.append(" left join o.crossSeedDetails d");
        hql.append(" left join d.sku s ");
        hql.append(" where o.warehouseId = " + whId.toString());

        List<Object> paramList = new ArrayList<Object>();
        StringBuilder hqt = new StringBuilder();
        if (StringUtil.isNotEmpty(crossSeedFilter.getSeedNo())) {
            hqt.append("and o.seedNo = ? ");
            paramList.add(crossSeedFilter.getSeedNo());
        }
        if (StringUtil.isNotEmpty(crossSeedFilter.getAsnNo())) {
            hqt.append("and o.asnNo = ? ");
            paramList.add(crossSeedFilter.getAsnNo());
        }
        if (StringUtil.isNotEmpty(crossSeedFilter.getContainerNo())) {
            hqt.append("and d.containerNo = ? ");
            paramList.add(crossSeedFilter.getContainerNo());
        }
        if (StringUtil.isNotEmpty(crossSeedFilter.getDoNo())) {
            hqt.append("and d.doNo = ? ");
            paramList.add(crossSeedFilter.getDoNo());
        }
        if (StringUtil.isNotEmpty(crossSeedFilter.getStatus())) {
            hqt.append("and o.status = ? ");
            paramList.add(crossSeedFilter.getStatus());
        }
        if (StringUtil.isNotEmpty(crossSeedFilter.getEan13())) {
            hqt.append("and ( s.productCode = ? or s.ean13 = ? ) ");
            paramList.add(crossSeedFilter.getEan13());
            paramList.add(crossSeedFilter.getEan13());
        }
        if (null != crossSeedFilter.getIsPrinted()) {
            hqt.append("and o.isPrinted = ? ");
            paramList.add(crossSeedFilter.getIsPrinted());
        }
//        hql.append(" GROUP BY o.id");

        Query query = createQuery(hql.append(hqt).toString() + " GROUP BY o.id");

        StringBuilder countHql = new StringBuilder("select count(distinct o.id) from CrossSeedHeader o left join o.crossSeedDetails d left join d.sku s " +
                "where o.warehouseId = " + whId.toString() + " ");
        Query countQuery = createQuery(countHql.append(hqt).toString());

        for (int i = 0; i < paramList.size(); i++) {
            Object object = paramList.get(i);
            if (object instanceof String) {
                object = ((String) object).trim();
            }
            query.setParameter(i, object);
            countQuery.setParameter(i, object);
        }

        if (pageSize > 0) {
            query.setFirstResult(startIndex);
            query.setMaxResults(pageSize);
        }

        DataPage<CrossSeedDTO> dataPage = new DataPage<CrossSeedDTO>(((Long) countQuery.uniqueResult()).intValue(),
                startIndex, pageSize, query.list());

        return dataPage;
    }

    public CrossSeedDTO getCrossHeaderDTO(Long crossSeedHeaderId) {
        StringBuilder hql = new StringBuilder("select new com.daxia.wms.delivery.crossorder.dto.CrossSeedDTO(" +
                "o.id, o.seedNo, o.status, o.asnNo, o.doNum, o.units, o.actualUnits, o.isPrinted, o.seedFromTime, o.seedToTime,o.createdBy, o.createdAt,o.updatedBy,o.updatedAt" +
                ")");
        hql.append(" from CrossSeedHeader o ");
        hql.append(" where o.id = :crossSeedHeaderId");

        return (CrossSeedDTO) this.createQuery(hql.toString(), ImmutableMap.<String, Object>of("crossSeedHeaderId", crossSeedHeaderId)).setMaxResults(1).uniqueResult();
    }

    public CrossSeedHeader getByNo(String seedNo) {
        String hql = "from CrossSeedHeader ch where ch.seedNo = :seedNo and ch.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setString("seedNo", seedNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (CrossSeedHeader) query.uniqueResult();
    }


    public List<Long> findDoIdsByCrossSeedHeaderId(Long crossSeedHeaderId) {
        String sql = " select dh.id from doc_do_header dh,doc_asn_header ah,doc_cross_seed_header sh ";
        sql += " where dh.source_asn_id = ah.id and sh.asn_no = ah.asn_no and dh.warehouse_id = :warehouseId  ";
        sql += " and ah.warehouse_id = :warehouseId and sh.warehouse_id = :warehouseId and dh.is_deleted = 0 ";
        sql += " and ah.is_deleted = 0 and sh.is_deleted = 0 and sh.id = :crossSeedHeaderId ";
        SQLQuery query = this.createSQLQuery(sql);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("crossSeedHeaderId", crossSeedHeaderId);
        query.addScalar("id", Hibernate.LONG);
        return query.list();
    }

    public CrossSeedHeader getByContainerNo(String containerNo) {
        String hql = "from CrossSeedHeader ch where exists ( select 1 from CrossSeedDetail cd where cd.headerId = ch.id and cd.containerNo = :containerNo ) and ch.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setString("containerNo", containerNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (CrossSeedHeader) query.uniqueResult();
    }

}
