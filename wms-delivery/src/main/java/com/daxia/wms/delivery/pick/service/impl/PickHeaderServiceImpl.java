package com.daxia.wms.delivery.pick.service.impl;


import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.log.LogUtil;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.PktMergeStatus;
import com.daxia.wms.Constants.PktStatus;
import com.daxia.wms.Constants.SequenceName;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.delivery.pick.dao.PickDAO;
import com.daxia.wms.delivery.pick.dao.PickTaskDAO;
import com.daxia.wms.delivery.pick.dto.PickInfoDTO;
import com.daxia.wms.delivery.pick.dto.PickTaskDto;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.pick.filter.PickHeaderFilter;
import com.daxia.wms.delivery.pick.service.PickHeaderService;
import com.daxia.wms.delivery.util.LaborForceHelper;
import com.daxia.wms.delivery.wave.service.impl.ContainerGenerateDispatcher;
import com.daxia.wms.master.entity.ContainerOrderRef;
import com.daxia.wms.master.entity.LaborHumanRegion;
import com.daxia.wms.master.entity.MergeLoc;
import com.daxia.wms.master.entity.Partition;
import com.daxia.wms.master.service.ContainerOrderRefService;
import com.daxia.wms.master.service.TempLaborTaskService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 出库部分PKT业务Service实现类
 */
@Name("com.daxia.wms.delivery.pickHeaderService")
@lombok.extern.slf4j.Slf4j
public class PickHeaderServiceImpl implements PickHeaderService {
	@In 
	private PickTaskDAO pickTaskDAO;
    @In
    private PickDAO pickDAO;
    @In
    private SequenceGeneratorService sequenceGeneratorService;
    @In
    private TempLaborTaskService tempLaborTaskService;
    @In
    private ContainerMgntService containerMgntService;
    /**
     * Description:分页查询拣货单头
     * @param filter
     * @return
     */
	@Override
	public DataPage<PickHeader> findPktHeaders(PickHeaderFilter filter , int startIndex , int pageSize) throws DeliveryException {
		return this.pickDAO.findRangeByFilter(filter, startIndex, pageSize);
	}
    
    @Override
    public Pagination<PickInfoDTO> findPickInfo(PickHeaderFilter filter , int startIndex , int pageSize) throws DeliveryException {
        return new Pagination<PickInfoDTO>(this.pickDAO.findPickInfo(filter, startIndex, pageSize));
    }
    
    @Override
    public PickInfoDTO getPickInfoDTO(Long pktId) {
        return this.pickDAO.getPickInfoDTO(pktId);
    }
	
	/**
     * Description:获取拣货单头
     * @param pktHeaderId
     * @return
     */
    @Override
    public PickHeader getPktHeader(Long pktHeaderId) {
        return pickDAO.get(pktHeaderId);
    }

    /**
     * 更新拣货单头
     */
    @Override
    @Transactional
    public void updatePktHeader(PickHeader pktHeader) {
        this.pickDAO.saveOrUpdate(pktHeader);
    }

    /**
     * 根据拣货单号获取拣货单头
     */
	@Override
	public PickHeader getPktHeaderByPktNo(String pktNo) {
		return pickDAO.getPktHeaderByPktNo(pktNo);
	}

    /** 
     * @param pktFilter
     * @return
     * @Description:不分页查询拣货单头
     */
    @SuppressWarnings("deprecation")
	@Override
    public List<PickHeader> query(PickHeaderFilter pktFilter) {
        return pickDAO.findByFilter(pktFilter);
    }

    /**
     * @Description:根据波次id获取拣货单
     */
	@Override
	public List<PickHeader> getPktHeadersByWaveId(Long waveId) {
		return pickDAO.getPktHeadersByWaveId(waveId);
	}
	
	/**
	 * Description:根据拣货单的波次id和区域id创建拣货单
	 */
	@Override
    @Transactional
    public PickHeader createPktHeader(Long waveId, Long regionId, Boolean isAvailable, Integer pktType) {
        PickHeader pktHeader = new PickHeader();
        pktHeader.setWaveHeadId(waveId);
        pktHeader.setStatus(PktStatus.RELEASED.getValue());
        pktHeader.setRegionId(regionId);
        pktHeader.setIsAvailable(isAvailable);
        pktHeader.setPktType(pktType);
        String pktNo = sequenceGeneratorService.generateSequenceNo(SequenceName.PKTNO.getValue(), ParamUtil.getCurrentWarehouseId());
        pktHeader.setPktNo(pktNo);
        pickDAO.saveOrUpdate(pktHeader);
        return pktHeader;
    }

    /**
     * 根据集货状态获取波次的拣货单数量
     */
    @Override
    public Map<? extends Long, ? extends Integer> getPktMergeCount(List<Long> waveIds, PktMergeStatus merged) {
        return pickDAO.getPktMergeCount(waveIds, merged);
    }
    
    /**
     * 根据波次主键查询拣货单记录数
     */
    @Override
    public Long findPktHeaderCount(Long waveHeadId){
    	return pickDAO.findPktHeaderCount(waveHeadId);
    }
    
    /**
     * 将波次id为waveId的拣货单集货出库
     */
    @Override
    @Transactional
    public void completeMergeOut(Long waveId){
        pickDAO.completeMergeOut(waveId);
    }
    
    /**
     * 根据do获取拣货单
     */
    public List<PickHeader> getPktHeadersByDo(Long doId){
    	List<Long> pktHeaderIdList = pickDAO.getPktHeadersByDo(doId);
    	Set<Long> set = new HashSet<Long>();
    	List<PickHeader> pktHeaderList = new ArrayList<PickHeader>();
    	for(Long id : pktHeaderIdList){
    		if(id != null){
        		if(!set.contains(id)){
        			set.add(id);
        			PickHeader pktHeader = pickDAO.get(id);
        			pktHeaderList.add(pktHeader);
        		}
    		}
    	}
    	return pktHeaderList;
    }
    
    /**
     * 改变拣货单状态
     */
    @Override
    @Transactional
    @Loggable
    public void changePktHeaderStatus(PickHeader pktHeader, boolean updateFromTime, String updateBy) {
        log.debug("changePktHeaderStatus : pktHeader = " + pktHeader.getId());
        // 根据拣货单号获取拣货单
        Map<String, Long> map = pickTaskDAO.findPickTaskStatusCountByPktHeader(pktHeader.getId());
        Long allCount = NumberUtil.coverNull(map.get("total"));
        Long allPickedcount = NumberUtil.coverNull(map.get(DoStatus.ALLPICKED.getValue()));
        Long canceldCount = NumberUtil.coverNull(map.get(DoStatus.CANCELED.getValue()));
        String pktType = String.valueOf(pktHeader.getPktType());
		if (updateFromTime && pktHeader.getPickTimeFrom() == null) {
        	//第一次更新拣货单
        	
        	pktHeader.setPickTimeFrom(new Date());
        }
        if (allCount == 0L) {// 没有拣货明细，取消拣货头
            cancelAndDelete(pktHeader.getId());
        	// 删除劳动力临时表记录
        	tempLaborTaskService.deleteByTaskNo(pktHeader.getPktNo());

            //释放周转箱
            containerMgntService.releaseByPkt(pktHeader.getPktNo(), pktType);
        } else if ((allPickedcount + canceldCount) == allCount) {
            //拣货完成记录拣货完成时间
            if (!PktStatus.COMPLETED.getValue().equals(pktHeader.getStatus()) && null == pktHeader.getPickTimeTo()) {
                pktHeader.setPickTimeTo(new Date());
            }
            
            pktHeader.setStatus(PktStatus.COMPLETED.getValue());
        	// 删除劳动力临时表记录
        	tempLaborTaskService.deleteByTaskNo(pktHeader.getPktNo());
            pktHeader.setPickBy(updateBy);
            updatePktHeader(pktHeader);
        }

    }

    private void cancelAndDelete(Long pktId) {
        PickHeader pktHeader = getPktHeader(pktId);
        pktHeader.setStatus(PktStatus.CANCELED.getValue());
        updatePktHeader(pktHeader);
        pickDAO.remove(pktId);
    }

    @Override
    @Transactional
    public void batchChangePktHeaderStatus(List<Long> batchIdList, String updateBy,Long waveId) {
        //批量更新拣货单头
        pickDAO.batchUpdatePickHeader4Pick(batchIdList,updateBy,waveId);
        //批量更新劳动力管理
        tempLaborTaskService.batchUpdateTempLaborTaskr4Pick(batchIdList);
    }

    @Override
    public Map<String, BigDecimal> getPickHeaderPicGrossweightInfo(PickHeader pktHeader) {
        Map<String, BigDecimal> pickInfoMap = new HashMap<String, BigDecimal>();
        List<PickTask> pickTaskList = pktHeader.getPickTasks();
        for (PickTask tempTask : pickTaskList) {
            if (Constants.PktStatus.CANCELED.getValue().equals(tempTask.getStatus())) {
                continue;
            }
            String containerNo = tempTask.getContainerNo();
            if (StringUtil.isEmpty(containerNo)) {
                containerNo = PickHeaderService.NOT_BIND_CONTAINER;
            }
            Double skuGrossweight = tempTask.getSku().getGrossweight();
            //sku如果没有设置毛重默认为0
            BigDecimal skuGrossweightB = BigDecimal.ZERO;
            if (null != skuGrossweight) {
                skuGrossweightB = BigDecimal.valueOf(skuGrossweight);
            }
            //需要数量乘每个sku毛重 
            BigDecimal taskGrossweight = tempTask.getQty().multiply(skuGrossweightB);
            BigDecimal pickInfo = pickInfoMap.get(containerNo);
            if (null == pickInfo) {
                pickInfoMap.put(containerNo, taskGrossweight);
            } else {
                pickInfoMap.put(containerNo, pickInfo.add(taskGrossweight));
            }
        }
        return pickInfoMap;
    }
    
    /**
     * 查询波次下的拣货单及对应的所有库区code信息
     * @param waveId
     * @return map 其中key存放拣货单id，value存放库区codes
     */
    @Override
    public Map<Long, List<String>> findPktPartitionCodesInWave(Long waveId) {
        return this.pickDAO.findPktPartitionCodesInWave(waveId);
    }

    /**
     * 根据拣货单查询拣货人
     * @param pickHeaderId
     * @return
     */
    @Override
    public List<String> findPickWhoByPickHeaderId(Long pickHeaderId) {
        return pickTaskDAO.findPickWhoByPickHeaderId(pickHeaderId);
    }
    
    /**
     * 根据拣货单查找其所包含的库区
     */
    @Override
    public List<String> findPartitionsByPktNo(String pktNo) {
        return pickDAO.findPartitionsByPktNo(pktNo);
    }

    /**
     * 设置拣货单为已打印状态
     */
    @Transactional
    @Override
    public void setPickHeaderPrinted(List<Long> ids) {
        pickDAO.setPickHeaderPrinted(ids);
    }
    
    /**
     * 计算拣货单的units总数
     * @param pktHeaderId
     * @return
     */
    public BigDecimal calUnitsInPktHeader(Long pktHeaderId) {
        return pickDAO.calUnitsInPktHeader(pktHeaderId);
    }
    
    /**
     * 计算拣货单的汇总信息，包括总units数，总毛重，总体积
     */
    @Override
    public Map<String, BigDecimal> calcTotalInfo(Long pktHeaderId) {
        return pickDAO.calcTotalInfo(pktHeaderId);
    }

    /**
     * 根据波次及拣货单状态查区域
     * @param waveIds
     * @param status 拣货单状态
     * @return
     */
    @Override
    @SuppressWarnings({ "rawtypes" })
    public List<List> findAreaByWaveIdAndStatus(List<Long> waveIds, List<String> status) {
    	return pickDAO.findAreaByWaveIdAndStatus(waveIds, status);
    }
    
    /**
     * 查询拣货单对应的所有库区信息
     * @param pktHeaderId
     * @return
     */
    @Override
    public List<Partition> findPartitionsInPktHeader(Long pktHeaderId) {
        return pickDAO.findPartitionsInPktHeader(pktHeaderId);
    }

    /**
     * 查询DO的波次下的指定状态拣货单号（微便利使用）
     */
    @Override
    public List<String> queryPktNoInDosWave(Long doId, String status) {
        return pickDAO.queryPktNoInDosWave(doId, status);
    }

    /**
     * 获取全仓未指派的拣货单（状态为已发布）
     */
    @Override
    public Long countAllPktHeader() {
    	return pickDAO.countAllPktHeader();
    }
    
	@Override
    public Long countOwnPktHeader(List<LaborHumanRegion> regionList, Map<String, List<String>> typeMap) {
		if (LaborForceHelper.enableCross(regionList)) {
			return pickDAO.countOwnPktHeader(null, typeMap);
		} else {
			List<Long> regionIdList = LaborForceHelper.getIdOfRegionList(regionList);
			return pickDAO.countOwnPktHeader(regionIdList, typeMap);
		}
	}
    
    /**
     * 人工索取一条可指派的拣货任务
     * @return
     */
    @Override
    public PickHeader findPktHeaderForDemand(List<Long> regionIdList, Map<String, List<String>> typeMap,Long waveId) {
        Integer laborSwitch = SystemConfig.getConfigValueInt("LABOR_FORCE_MANAGE_SWITCH", ParamUtil.getCurrentWarehouseId());
    	return pickDAO.findPktHeaderForDemand(regionIdList, typeMap, laborSwitch, waveId);
    }
    
    @Override
    public void saveOrUpdate(PickHeader pickHeader) {
    	pickDAO.saveOrUpdate(pickHeader);
    }
    
    /**
     * 根据库位与波次查找拣货单
     * @param locId
     * @param waveId
     * @return
     */
    @Override
    public PickHeader findPktHeaderByLocAndWave(Long locId, Long waveId) {
    	return pickDAO.findPktHeaderByLocAndWave(locId, waveId);
    }
    
    /**
     * 根据波次和状态查找拣货单
     */
    @Override
    public List<PickHeader> getPktHeadersByWaveIdAndStatus(Long waveId, String status) {
    	return pickDAO.getPktHeadersByWaveIdAndStatus(waveId, status);
    }

    @Override
    public void clearCartonStatus(Long waveId, Long cartonId) {
        pickDAO.clearCartonStatus(waveId, cartonId);
    }

    @Override
    public Boolean isPickFinishedByDpNoAndPkType(String doNo, Integer pktType) {
        MergeLoc mergeLoc = new MergeLoc();
        mergeLoc.setDocNo(doNo);
        mergeLoc.setPktType(pktType);
        return pickDAO.isPickFinished(mergeLoc);
    }
    
    @Override
    public List<PickTaskDto> findPickTask(Long pktId) {
        return pickDAO.findPickTask(pktId);
    }
}