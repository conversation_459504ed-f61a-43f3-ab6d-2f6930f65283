package com.daxia.wms.delivery.recheck.action;

import com.daxia.wms.delivery.recheck.dto.CombineBindingInfo;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.wms.delivery.recheck.dto.ReCheckCartonInfo;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.recheck.service.ReCheckService;

/**
 *显示装箱明细信息, cartonDetailListView.xhtml页面使用
 */
@Name("com.daxia.wms.delivery.cartonAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class CartonAction extends PagedListBean<CartonHeader> {
	
	private static final long serialVersionUID = -8590281122184966434L;
	private boolean initialized = false;
	private Long cartonHeaderId;
	private CartonHeader cartonHeader;
	private CombineBindingInfo combineBindingInfo;
	private String cartonNo;
	private String trackingNo;
	private ReCheckCartonInfo reCheckCartonInfo;
	private String carrierName;
	private boolean handleFlag = false;

	private Long carrierId;
	
	//是否查询历史数据
	private Boolean queryHistory;

	private Long orderCount;

	private Long cartonCount;
	
	@In
	private CartonService cartonService;
	@In
	private ReCheckService reCheckService;

	private String cartonCodes;

	public CartonAction() {
		super();
		combineBindingInfo = new CombineBindingInfo();
	}

	/**
     * 
     * <pre>
     * Description:初始化页面
     * </pre>
     *
     */
    public void initializePage(){
        if(!initialized){
        	this.reCheckCartonInfo = this.reCheckService.selectCarton(cartonNo, queryHistory);
        }
    }
    
	@Override
	public void query() {
		// TODO Auto-generated method stub
		
	}
	
	/**
	 * 绑定运单号时查询箱信息
	 */
	public void findCartonInfo4Binding(){
		handleFlag = false;
		this.cartonHeader = cartonService.findCartonInfo4Binding(cartonNo);
		handleFlag = true;
	}

	/**
	 * 绑定运单号时查询箱信息
	 */
	public void findCarton4Combine(){
		handleFlag = false;
		this.combineBindingInfo = cartonService.findCartonInfo4Combine(cartonNo);
		handleFlag = true;
	}

	
	public void bindTrackingNo(){
		handleFlag = false;
		this.cartonService.updateCartonWayBill(cartonNo, trackingNo);
		handleFlag = true;
	}

	public void combineBind(){
		handleFlag = false;
		this.cartonService.combineBind(cartonNo, trackingNo);
		handleFlag = true;
	}

	
	public boolean isInitialized() {
		return initialized;
	}

	public void setInitialized(boolean initialized) {
		this.initialized = initialized;
	}

	public Long getCartonHeaderId() {
		return cartonHeaderId;
	}

	public void setCartonHeaderId(Long cartonHeaderId) {
		this.cartonHeaderId = cartonHeaderId;
	}

	public CartonHeader getCartonHeader() {
		return cartonHeader;
	}

	public void setCartonHeader(CartonHeader cartonHeader) {
		this.cartonHeader = cartonHeader;
	}

	public String getCartonNo() {
		return cartonNo;
	}

	public void setCartonNo(String cartonNo) {
		this.cartonNo = cartonNo;
	}

	public ReCheckCartonInfo getReCheckCartonInfo() {
		return reCheckCartonInfo;
	}

	public void setReCheckCartonInfo(ReCheckCartonInfo reCheckCartonInfo) {
		this.reCheckCartonInfo = reCheckCartonInfo;
	}

    public Boolean getQueryHistory() {
        return queryHistory;
    }
    
    public void setQueryHistory(Boolean queryHistory) {
        this.queryHistory = queryHistory;
    }

	public String getTrackingNo() {
		return trackingNo;
	}

	public void setTrackingNo(String trackingNo) {
		this.trackingNo = trackingNo;
	}

	public String getCarrierName() {
		return carrierName;
	}

	public void setCarrierName(String carrierName) {
		this.carrierName = carrierName;
	}

	public boolean isHandleFlag() {
		return handleFlag;
	}

	public void setHandleFlag(boolean handleFlag) {
		this.handleFlag = handleFlag;
	}

	public Long getCarrierId()
	{
		return carrierId;
	}

	public void setCarrierId(Long carrierId)
	{
		this.carrierId = carrierId;
	}

	public String getCartonCodes()
	{
		return cartonCodes;
	}

	public void setCartonCodes(String cartonCodes)
	{
		this.cartonCodes = cartonCodes;
	}

	public Long getOrderCount()
	{
		return orderCount;
	}

	public void setOrderCount(Long orderCount)
	{
		this.orderCount = orderCount;
	}

	public Long getCartonCount()
	{
		return cartonCount;
	}

	public void setCartonCount(Long cartonCount)
	{
		this.cartonCount = cartonCount;
	}

	public CombineBindingInfo getCombineBindingInfo()
	{
		return combineBindingInfo;
	}

	public void setCombineBindingInfo(CombineBindingInfo combineBindingInfo)
	{
		this.combineBindingInfo = combineBindingInfo;
	}
}
