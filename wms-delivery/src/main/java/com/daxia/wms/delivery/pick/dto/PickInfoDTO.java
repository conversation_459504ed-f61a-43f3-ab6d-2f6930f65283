package com.daxia.wms.delivery.pick.dto;

import com.daxia.framework.common.service.Dictionary;
import com.daxia.wms.delivery.merge.dto.AbstractPickedNumDTO;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

@lombok.extern.slf4j.Slf4j
public class PickInfoDTO extends AbstractPickedNumDTO {

    private Long pickHeaderId;

    private String pickNo;

    // 区域CODE
    private String regionNo;

    // 拣货单状态
    private String pickStatus;
    
    // 拣货单状态
    private String pickStatusName;

    // 总拣货数量
    private BigDecimal pickNum;

    //拣货时间From
    
    private Date pickTimeFrom;

    private Date pickTimeTo;

    // 拣货使用的时间
    private String pickUsedTime;

    private String pickUsedTimeColor;

    private String pickBy;
    
    private String waveNo;
    
    public PickInfoDTO(Long pktId, String status, String pktNo, String regionCode, Date pickTimeFrom, Date pickTimeTo, String pickBy, String waveNo) {
        this.pickHeaderId = pktId;
        this.pickStatus = status;
        this.pickNo = pktNo;
        this.regionNo = regionCode;
        this.pickTimeFrom = pickTimeFrom;
        this.pickTimeTo = pickTimeTo;
        this.pickBy = pickBy;
        this.waveNo = waveNo;
    
        this.pickStatusName = Dictionary.getDictionaryValue("TASK_STATUS", this.getPickStatus());
    }
    
    public String getWaveNo() {
        return waveNo;
    }
    
    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }
    
    public String getPickStatusName() {
        return this.pickStatusName;
    }
    
    public String getPickNo() {
        return pickNo;
    }

    public void setPickNo(String pickNo) {
        this.pickNo = pickNo;
    }

    public String getRegionNo() {
        return regionNo;
    }

    public void setRegionNo(String regionNo) {
        this.regionNo = regionNo;
    }

    public String getPickStatus() {
        return pickStatus;
    }

    public void setPickStatus(String pickStatus) {
        this.pickStatus = pickStatus;
    }

    public BigDecimal getPickNum() {
        return pickNum;
    }

    public void setPickNum(BigDecimal pickNum) {
        this.pickNum = pickNum;
    }
    
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    public Date getPickTimeFrom() {
        return pickTimeFrom;
    }

    public void setPickTimeFrom(Date pickTimeFrom) {
        this.pickTimeFrom = pickTimeFrom;
    }
    
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    public Date getPickTimeTo() {
        return pickTimeTo;
    }

    public void setPickTimeTo(Date pickTimeTo) {
        this.pickTimeTo = pickTimeTo;
    }

    public String getPickUsedTime() {
        return pickUsedTime;
    }

    public void setPickUsedTime(String pickUsedTime) {
        this.pickUsedTime = pickUsedTime;
    }

    public String getPickUsedTimeColor() {
        return pickUsedTimeColor;
    }

    public void setPickUsedTimeColor(String pickUsedTimeColor) {
        this.pickUsedTimeColor = pickUsedTimeColor;
    }

    public String getPickBy() {
        return pickBy;
    }

    public void setPickBy(String pickBy) {
        this.pickBy = pickBy;
    }

    public Long getPickHeaderId() {
        return pickHeaderId;
    }

    public void setPickHeaderId(Long pickHeaderId) {
        this.pickHeaderId = pickHeaderId;
    }
}