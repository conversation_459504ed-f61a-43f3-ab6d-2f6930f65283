package com.daxia.wms.delivery.load.dao;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.load.entity.CrossDockPacking;
import com.daxia.wms.delivery.print.dto.CrossDockCartonPrintDTO;

@Name("com.daxia.wms.delivery.crossDockPackingDAO")
@lombok.extern.slf4j.Slf4j
public class CrossDockPackingDAO  extends HibernateBaseDAO<CrossDockPacking, Long> {

	private static final long serialVersionUID = -3570517033784099391L;

	public List<CrossDockCartonPrintDTO> getCrossDockCartonPrintDTOById(List<Long> ids) {
		Long warehouseId = ParamUtil.getCurrentWarehouseId();
		String hql = "select carton_no,max(cd_header_id),max(cd_header_no),count(lpn_no),max(lpn_no),min(ref_no1), "
					+ "max(ref_no1),to_wh_id,valueable_flag,sum(sku_qty),sum(unit_qty) " 
					+ "from doc_crdock_packing "
					+ "where id in (:ids) "
					+ "and warehouse_Id = :warehouseId " 
					+ "and is_deleted = 0 "
					+ "group by carton_no,to_wh_id,valueable_flag";
		Query query = this.createSQLQuery(hql);
		query.setParameterList("ids", ids);
		if(warehouseId != null) {
			query.setLong("warehouseId", warehouseId);
		}
		List<?> list = query.list();
	    List<CrossDockCartonPrintDTO> dtoList = new ArrayList<CrossDockCartonPrintDTO>();
	    for (int i = 0; i < list.size(); i++) {
	    	Object[] objs = (Object[]) list.get(i);
	    	CrossDockCartonPrintDTO dto = new CrossDockCartonPrintDTO();
	        dto.setCartonNos(objs[0] == null ? null :(String)objs[0]);
	        dto.setCdHeaderId(objs[1] == null ? null : Long.valueOf(objs[1].toString()));
	        dto.setCdHeaderNo(objs[2] ==null ? null : (String)objs[2]);
	        dto.setLpnQty(objs[3] == null ? null : BigDecimal.valueOf(Long.valueOf(objs[3].toString())));
	        dto.setLpnNo(objs[4] == null ? null : (String)objs[4]);
	        dto.setIsSingleRefNo1(objs[5].toString().equals(objs[6].toString()) ? 1 : 0);
	        dto.setRefNo1(objs[6] == null ? null : (String)objs[6]);
	        dto.setToWhId(objs[7] == null ? null : Long.valueOf(objs[7].toString()));
	        dto.setValuableFlag(objs[8] == null ? null : Integer.valueOf(objs[8].toString()));
	        dto.setSkuQty(objs[9] == null ? null : BigDecimal.valueOf(Long.valueOf(objs[9].toString())));
	        dto.setUnitsQty(objs[10] == null ? null : BigDecimal.valueOf(Long.valueOf(objs[10].toString())));
	        dtoList.add(dto);
	    }
	    return dtoList;
	}

}
