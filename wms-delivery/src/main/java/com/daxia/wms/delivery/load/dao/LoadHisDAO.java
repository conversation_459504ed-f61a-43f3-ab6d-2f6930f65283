package com.daxia.wms.delivery.load.dao;

import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.load.entity.LoadHeaderHis;
import com.daxia.wms.delivery.load.filter.LoadFilter;

/**
 * Description:交接历史业务DAO
 */
@Name("com.daxia.wms.delivery.loadHisDAO")
@lombok.extern.slf4j.Slf4j
public class LoadHisDAO extends HibernateBaseDAO<LoadHeaderHis, Long> {

    private static final long serialVersionUID = -4593097398374099405L;

    /**
     * 查询交接单历史数据
     * 
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<LoadHeaderHis> findLoadHeaderHis(LoadFilter filter, int startIndex, int pageSize, boolean needPaging){
    	Long whId = ParamUtil.getCurrentWarehouseId();
        StringBuilder hql = new StringBuilder("");
        hql.append(" from LoadHeaderHis o where o.warehouseId = " + whId);
        if(!StringUtil.isEmpty(filter.getStatus())) {
            hql.append(" and o.status = :status "); 
        }
        
        if(!StringUtil.isEmpty(filter.getDoNo())) {
            hql.append(" and o.id in (select o1.loadHeaderId from LoadDetailHis o1,DeliveryOrderHeaderHis o2 ");
            hql.append(" where o1.doHeaderId = o2.id ");
            hql.append(" and o2.doNo = :doNo and o1.warehouseId = " + whId + " and o2.warehouseId = " + whId + ") ");
        } 
        if(null != filter.getCreateTimeFrom()) {
            hql.append(" and o.createdAt >= :createTimeFm ");
        }
        if(null != filter.getCreateTimeTo()) {
            hql.append(" and o.createdAt <= :createTimeTo ");
        }
        if(null != filter.getCarrierId()) {
            hql.append(" and o.carrierId = :carrierId ");
        }
        if(!StringUtil.isEmpty(filter.getCartonNo())) {
            hql.append(" and (o.id in (select o3.loadHeaderId from LoadDetailHis o3 ");
            hql.append(" where o3.carrierNo = :carrierNo and o3.warehouseId = " + whId + ")) ");
        }
        if(!StringUtil.isEmpty(filter.getLoadType())) {
            hql.append(" and o.loadType = :loadType ");
        }
        if(null != filter.getIsAuto()) {
            hql.append(" and o.isAuto = :isAuto ");
        }
        if(!StringUtil.isEmpty(filter.getLoadNo())) {
            hql.append(" and o.loadNo = :loadNo ");
        }
        hql.append(" order by o.createdAt desc");
        
        Query query = this.createQuery(hql.toString());
        if(!StringUtil.isEmpty(filter.getStatus())) {
            query.setParameter("status", filter.getStatus());
        }
        if(!StringUtil.isEmpty(filter.getDoNo())) {
            query.setParameter("doNo", filter.getDoNo());
        }
        if(null != filter.getCreateTimeFrom()) {
            query.setParameter("createTimeFm", filter.getCreateTimeFrom());
        }
        if(null != filter.getCreateTimeTo()) {
            query.setParameter("createTimeTo", filter.getCreateTimeTo());
        }
        if(null != filter.getCarrierId()) {
            query.setParameter("carrierId", filter.getCarrierId());
        }
        if(!StringUtil.isEmpty(filter.getCartonNo())) {
            query.setParameter("carrierNo", filter.getCartonNo());
        }
        if(!StringUtil.isEmpty(filter.getLoadType())) {
            query.setParameter("loadType", filter.getLoadType());
        }
        if(null != filter.getIsAuto()) {
            query.setParameter("isAuto", filter.getIsAuto());
        }
        if(!StringUtil.isEmpty(filter.getLoadNo())) {
            query.setParameter("loadNo", filter.getLoadNo());
        }
        if (needPaging && pageSize > 0) {
            query.setFirstResult(startIndex);
            query.setMaxResults(pageSize);
        }
        return query.list();
    }
}
