package com.daxia.wms.delivery.deliveryorder.service;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dto.DoAlcDetailInfoDto;
import com.daxia.wms.delivery.deliveryorder.dto.DoAllocateHeaderDto;
import com.daxia.wms.delivery.deliveryorder.dto.ManualAllocDTO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateHeader;
import com.daxia.wms.delivery.deliveryorder.filter.DoAlcHeaderFilter;
import com.daxia.wms.delivery.wave.dto.AutoWaveDTO;
import com.daxia.wms.delivery.wave.dto.OverAllocateDto;
import com.daxia.wms.master.rule.filter.BigWaveFilter;
import org.apache.commons.lang3.tuple.Pair;
import org.jboss.seam.annotations.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface DoAllocateService {

    /**
     * doAlcHeaderFilter分页查询DoAllocateHeaderDto
     *
     * @param doAlcHeaderFilter
     * @param startIndex
     * @param pageSize
     * @return
     */
    DataPage<DoAllocateHeaderDto> query(DoAlcHeaderFilter doAlcHeaderFilter, int startIndex, int pageSize);

    /**
     * 主键查询订单分配头
     *
     * @param allocateHeaderId
     * @return
     */
    DoAllocateHeader getHeader(Long allocateHeaderId);

    /**
     * 根据doAllocateHeader,doAllocateDetail查询doAllocateDetail可分配的库存信息(商品对应库存可用数量>0或其拣货任务数量>0)
     *
     * @param doAllocateHeader
     * @param doAllocateDetail
     * @param startRow
     * @param pageSize
     * @param locType
     * @param locCode
     * @return
     */
    public DataPage<ManualAllocDTO> queryManualAllocInfo(DoAllocateHeader doAllocateHeader,
                                                         DoAllocateDetail doAllocateDetail, int startRow, int pageSize, String locType, String locCode);

    /**
     * 分配审核
     *
     * @param allocateHeaderId
     */
    public void checkAlloc(Long allocateHeaderId);


    /**
     * 清空订单所有明细效期范围限制
     * @param allocateHeaderId
     */
    @Transactional
    void cleanExpireLimit(Long allocateHeaderId);
    /**
     * 清空订单指定明细效期范围限制
     * @param allocateDetailIds
     */
    @Transactional
    void cleanExpireLimit(Long allocateHeaderId,List<Long> allocateDetailIds);

    /**
     * 手动分配
     *
     * @param doAllocateDetailId
     * @param assignDetailInfoMap
     */
    public void manualAlloc(Long doAllocateDetailId, List<ManualAllocDTO> dtoList, Boolean isForce, Integer isException);

    /**
     * 根据DeliveryOrderHeader创建订单分配头
     *
     * @param doHeader
     * @return
     */
    public DoAllocateHeader createDoAllocateHeader(DeliveryOrderHeader doHeader);

    /**
     * 根据DeliveryOrderDetail列表创建订单分配明细列表
     *
     * @param doDetails
     * @return
     */
    public List<DoAllocateDetail> createDoAllocateDetail(List<DeliveryOrderDetail> doDetails);

    public void checkDoStatusAfterAllocate(DoAllocateHeader alcHeader);

    /**
     * 批量移出订单分配信息
     *
     * @param doIdList
     */
    void removeAllocate(List<Long> doIdList);

    /**
     * 移出订单分配信息
     *
     * @param id
     */
    void removeAllocate(Long id);

    /**
     * 根据Id查询DoAllocateDetail
     *
     * @param doAllocateDetailId
     * @return
     */
    public DoAllocateDetail getDoAllocateDetailById(Long doAllocateDetailId);

    /**
     * 取消分配
     *
     * @param doHeaderId
     * @param doDetailIds
     * @param isAuto
     * @throws DeliveryException
     */
    public void cancelAssign(Long doHeaderId, List<Long> doDetailIds, boolean isAuto, boolean checkReleaseStatus) throws DeliveryException;

    /**
     * 取消分配
     *
     * @param header
     * @param isAuto
     */
    void cancelAssign(DoAllocateHeader header, boolean isAuto, boolean checkReleaseStatus);

    /**
     * 取消分配——定时器调用
     *
     * @param id
     * @param isAuto
     */
    public void autoCancelAssign(Long id);

    /*
     * 同步订单信息到分配信息，没有则会创建
     */
    void sycDo2Alc(Long id);

    /**
     * 根据ID列表获取订单分配明细头
     *
     * @param ids
     * @return
     */
    List<DoAllocateHeader> getHeaderByIds(List<Long> ids);

    /**
     * 释放已分配库存/补货任务
     *
     * @param doHeaderId
     * @param doDetailIds
     */
    public void releaseAssignedStock(Long doHeaderId, List<Long> doDetailIds);

    /**
     * 保存分配订单头信息
     */

    public void updateDoAllocateHeader(DoAllocateHeader header);

    /**
     * 获取是否对分配异常订单处理的配置参数
     *
     * @return
     */
    public Integer getFailCheckConfig();

    /**
     * 获取订单自动分配失败上限值
     *
     * @return
     */
    public Integer getAllocateFailNum();

    List<Long> queryNeedAllocateDoHeaderIds(int i, Long warehouseId);

    List<Long> queryNeedAllocateRtvIds(int size);

    List<DoAllocateHeader> findByIds(List<Long> alcheaderIds);

    Long addFailNum(Long id);

    void updateDoNotAutoWave(List<Long> ids, List<String> nos);

    /**
     * 设置订单需要取消分配标记，并将自动波次失败次数置为最大
     *
     * @param ids
     */
    public void setAlcNeedCancelFlag(List<Long> ids);

    /**
     * 获取需要自动取消分配的订单id
     *
     * @param size
     * @return
     */
    public List<Long> queryNeedCancelAllocIds(int size);

    /**
     * 订单自动取消分配失败次数加1
     *
     * @param id
     * @return
     */
    public Long addCancelFailNum(Long id);

    public void releaseAssignedStock(List<Long> doDetailIds);

    public void releaseAssignedStock(Long doHeaderId, Long doDetailId, BigDecimal diffQty);

    public List<Long> findNotLacks();

    public void autoFrozenOnAllocate(Long doHeaderId, List<DoAllocateDetail> needFrozenDoDetails, String reasonCode, String holdWho);

    public void saveOrUpdate(DoAllocateDetail detail);

    public List<DoAlcDetailInfoDto> getAlcDetailInfosByDo(Long doId);

    Map<String, List<String>> autoAllocate(Long id, List<Long> detailIds) throws Exception;

    Map<String, List<String>> autoAllocate(Long doId) throws Exception;

    List<Long> findIdForWave(BigWaveFilter bigWaveFilter);

    List<OverAllocateDto> queryOverAllocate();

    List<Pair<Long, BigDecimal>> getStkAllocateQty(Long stkId);

    List<Long> findReAllocateId();

    void removeTempAlloc(List<Long> ids);

    List<OverAllocateDto> queryMoreAllocate();

    /**
     * 查询订单匹配度的sku 与数量
     *
     * @param lowerLimit
     * @return
     */
    List<AutoWaveDTO> getAutoWaveInfo(Integer lowerLimit);
    /**
     * 查询满足条件的单据信息
     *
     * @param lowerLimit
     * @param filter
     * @return
     */
    List<AutoWaveDTO> getAutoWaveInfoByFilter(BigWaveFilter filter ,Integer lowerLimit);

    /**
     * 通过匹配度高的商品与数量查询匹配度高的订单
     *
     * @param dto
     * @return
     */
    List<Long> getAutoWaveDoIds(AutoWaveDTO dto);
    /**
     * 通过filter条件匹配度高的商品与数量查询匹配度高的订单
     *
     * @param bigWaveFilter
     * @param autoWaveDTO
     * @return
     */
    List<Long> getAutoWaveDoIdsByFilter(BigWaveFilter bigWaveFilter, AutoWaveDTO autoWaveDTO);

    List<Long> getAutoWaveDoIdsByFilter(BigWaveFilter bigWaveFilter);

    /**
     * 清空当前仓库 未分配完成的订单中明细包含入参品的明细分配时间限制
     * @param goodsCodes
     * @param warehouseId
     */
    void clearAlcDate(List<String> goodsCodes,Long warehouseId);
}
