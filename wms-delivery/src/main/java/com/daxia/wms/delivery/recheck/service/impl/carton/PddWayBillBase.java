//package com.daxia.wms.delivery.recheck.service.impl.carton;
//
//import com.daxia.framework.common.util.NullUtil;
//import com.daxia.framework.common.util.ParamUtil;
//import com.daxia.framework.common.util.StringUtil;
//import com.daxia.framework.common.util.SystemConfig;
//import com.daxia.wms.ConfigKeys;
//import com.daxia.wms.Constants;
//import com.daxia.wms.delivery.DeliveryException;
//import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
//import com.daxia.wms.delivery.recheck.entity.CartonHeader;
//import com.daxia.wms.delivery.util.DoUtil;
//import com.daxia.wms.master.MasterException;
//import com.daxia.wms.master.entity.CarrierCainiaoEx;
//import com.daxia.wms.master.entity.ShopInfo;
//import com.daxia.wms.master.entity.WarehouseCarrier;
//import com.daxia.wms.master.service.WarehouseCarrierService;
//import com.pdd.pop.sdk.http.api.request.PddWaybillGetRequest;
//import org.apache.commons.lang.StringUtils;
//import org.jboss.seam.annotations.In;
//
//import java.math.BigDecimal;
//
//public abstract class PddWayBillBase {
//    @In
//    WarehouseCarrierService warehouseCarrierService;
//
//    WarehouseCarrier loadWarehouseCarrier(Long carrierId) {
//        WarehouseCarrier warehouseCarrier = warehouseCarrierService.getCarrierInfoByWarehouseIdAndCarrierIdAndType(ParamUtil.getCurrentWarehouseId(), carrierId, Constants.WaybillType.PDD.name());
//        if (warehouseCarrier == null) {
//            throw new MasterException(MasterException.ERROR_WAYBILL_CONFIG_IS_NOT_EXIST);
//        }
//
//        return warehouseCarrier;
//    }
//
//    // 发货地址
//    protected PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestSender genShippingAddress4Get(CarrierCainiaoEx carrierCainiaoEx, DeliveryOrderHeader doHeader) {
//        // 发货地址从Search接口获取;
//        PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestSender userInfoDto = new PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestSender();
//        PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestSenderAddress waybillAddress = new PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestSenderAddress();
//        // 省,市,区
//        waybillAddress.setProvince(carrierCainiaoEx.getProvince());
//        waybillAddress.setCity(carrierCainiaoEx.getCity());
//        waybillAddress.setDistrict(carrierCainiaoEx.getArea());
//        // 详细地址
//        waybillAddress.setDetail(carrierCainiaoEx.getAddressDetail());
//
//        ShopInfo shopInfo = doHeader.getShopInfo();
//
//        if (Constants.YesNo.YES.getValue().equals(doHeader.getHaveCfy())) {
//            userInfoDto.setName(shopInfo != null ? shopInfo.getNickName() : SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_NAME_CFY, ParamUtil.getCurrentWarehouseId()));
////            waybillAddress.setDetail(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_ADDRESS_CFY, ParamUtil.getCurrentWarehouseId()));
//            userInfoDto.setPhone(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_PHONE_CFY, ParamUtil.getCurrentWarehouseId()));
//        } else {
//            userInfoDto.setName(shopInfo != null ? shopInfo.getNickName() : SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_NAME, ParamUtil.getCurrentWarehouseId()));
////            waybillAddress.setDetail(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_ADDRESS, ParamUtil.getCurrentWarehouseId()));
//            String sendPhone = NullUtil.notEmpty(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_PHONE + "_" + (shopInfo == null ? "-999" : shopInfo.getId()), ParamUtil.getCurrentWarehouseId()),
//                    SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_PHONE, ParamUtil.getCurrentWarehouseId()));
//            userInfoDto.setPhone(sendPhone);
//        }
//        String sendName = SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_NAME_COVER, ParamUtil.getCurrentWarehouseId());
//        if (StringUtil.isNotBlank(sendName)) {
//            userInfoDto.setName(sendName);
//        }
//        userInfoDto.setAddress(waybillAddress);
//        return userInfoDto;
//    }
//
//
//    // 收件人地址
//    protected PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItemRecipient genConsigneeInfo4Get(DeliveryOrderHeader doHeader) {
//        PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItemRecipient userInfoDto = new PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItemRecipient();
//        // 收货人
//        userInfoDto.setName(doHeader.getConsigneeName());
//        // 收货人联系方式
//        String mobile = DoUtil.decryptPhone(StringUtils.defaultIfEmpty(doHeader.getMobile(), doHeader.getTelephone()).trim());
//        userInfoDto.setMobile(mobile);
//        if (mobile.indexOf(",") > 0) {
//            userInfoDto.setMobile(mobile.split(",")[0]);
//        }
//        String phone = DoUtil.decryptPhone(StringUtils.defaultIfEmpty(doHeader.getMobile(), doHeader.getTelephone()).trim());
//        userInfoDto.setPhone(phone);
//        if (phone.indexOf(",") > 0) {
//            userInfoDto.setPhone(phone.split(",")[0]);
//        }
//        //收货人地址
//        userInfoDto.setAddress(genConsigneeAddress4Get(doHeader));
//        return userInfoDto;
//    }
//
//    // 收件人地址
//    protected PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItemRecipientAddress genConsigneeAddress4Get(DeliveryOrderHeader doHeader) {
//        PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItemRecipientAddress waybillAddress = new PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItemRecipientAddress();
//        if (null != doHeader.getProvinceInfo() || StringUtils.isNotEmpty(doHeader.getProvinceName())) {
//            // 省
//            waybillAddress.setProvince(StringUtils.isNotEmpty(doHeader.getProvinceName()) ? doHeader.getProvinceName() :
//                    doHeader.getProvinceInfo().getProvinceCname());
//        } else {
//            throw new DeliveryException(DeliveryException.ERROR_ADDRESS_ERROR);
//        }
//        if (null != doHeader.getCityInfo() || StringUtils.isNotEmpty(doHeader.getCityName())) {
//            waybillAddress.setCity(StringUtils.isNotEmpty(doHeader.getCityName()) ? doHeader.getCityName() :
//                    doHeader.getCityInfo().getCityCname());
//        } else {
//            throw new DeliveryException(DeliveryException.ERROR_ADDRESS_ERROR);
//        }
//        if (null != doHeader.getCountyInfo() || StringUtils.isNotEmpty(doHeader.getCountyName())) {
//            waybillAddress.setDistrict(StringUtils.isNotEmpty(doHeader.getCountyName()) ? doHeader.getCountyName() :
//                    doHeader.getCountyInfo().getCountyCname());
//        } else {
//            throw new DeliveryException(DeliveryException.ERROR_ADDRESS_ERROR);
//        }
//        // 详细地址
//        waybillAddress.setDetail(doHeader.getAddress());
//        return waybillAddress;
//    }
//
//    protected String genLogisticsServiceList4Get(DeliveryOrderHeader doHeader) {
//        if (doHeader.getReceivable() != null && doHeader.getReceivable().compareTo(BigDecimal.ZERO) > 0) {
//            //payment_type: {CASH: 现金, CARD: 刷卡}; currency: 币种，参考维基百科: "CNY","USD","HKD","EUR","RUB"
//            return "{\"SVC-COD\": {\"value\": " + doHeader.getReceivable().setScale(2, BigDecimal.ROUND_HALF_UP) + "}}";
//        }
//        return null;
//    }
//
//    // 使用者ID(发货时商家 ID一致)
//    // 淘系订单【使用者 id】可以使用查询卖家用户信息接口taobao.user.seller.get获取
//    // 非淘系订单【使用者id】直接使用【申请者id】， 风控只针对淘系订单
//    protected Long genRealUserId(DeliveryOrderHeader doHeader, CarrierCainiaoEx carrierCainiaoEx) {
//        return carrierCainiaoEx.getSellId().longValue();
//    }
//
//
//    protected Long getWeight(CartonHeader cartonHeader) {
//        return cartonHeader.getActualGrossWeight().multiply(BigDecimal.valueOf(1000l)).longValue();
//    }
//}
