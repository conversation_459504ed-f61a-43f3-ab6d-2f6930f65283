package com.daxia.wms.delivery.deliveryorder.dto;

import java.io.Serializable;
import java.math.BigDecimal;

@lombok.extern.slf4j.Slf4j
public class DoLackDetailDto implements Serializable {
	private static final long serialVersionUID = 7067262592060602473L;

	private String productName;
	
	private String productCode;
	
	private String barCode;
	
	private BigDecimal lackQty;
	
	private String locCode;
	
	private String stockStatus;

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getBarCode() {
		return barCode;
	}

	public void setBarCode(String barCode) {
		this.barCode = barCode;
	}

	public BigDecimal getLackQty() {
		return lackQty;
	}

	public void setLackQty(BigDecimal lackQty) {
		this.lackQty = lackQty;
	}

	public String getLocCode() {
		return locCode;
	}

	public void setLocCode(String locCode) {
		this.locCode = locCode;
	}
	
	public String getStockStatus() {
		return stockStatus;
	}
	
	public void setStockStatus(String stockStatus) {
		this.stockStatus = stockStatus;
	}
}
