package com.daxia.wms.delivery.container.service.impl;

import com.daxia.dubhe.api.wms.request.PickContainerRequest;
import com.daxia.dubhe.api.wms.response.PickContainerResponse;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.container.dao.PktContainerDetailDAO;
import com.daxia.wms.delivery.container.dao.PktContainerHeaderDAO;
import com.daxia.wms.delivery.container.entity.PktContainerDetail;
import com.daxia.wms.delivery.container.entity.PktContainerHeader;
import com.daxia.wms.delivery.container.filter.PktContainerDetailFilter;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.delivery.container.service.PktContainerService;
import com.daxia.wms.delivery.pick.dto.PickTaskDto;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.pick.service.PickTaskService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.master.entity.Container;
import com.daxia.wms.master.entity.ContainerType;
import com.daxia.wms.master.service.ContainerTypeService;
import com.daxia.wms.master.service.SkuCache;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Name("com.daxia.wms.delivery.pktContainerService")
@lombok.extern.slf4j.Slf4j
public class PktContainerServiceImpl implements PktContainerService {
    @In
    private PktContainerDetailDAO pktContainerDetailDAO;
    @In
    private PktContainerHeaderDAO pktContainerHeaderDAO;
    @In
    PickTaskService pickTaskService;
    @In
    ContainerMgntService containerMgntService;
    @In
    ContainerTypeService containerTypeService;
    @In
    SkuCache skuCache;
    
    @Override
    @Transactional
    public void delByContainerNo(String containerNo) {
        PktContainerHeader pktContainerHeader = getHeaderByContainerNo(containerNo);
        if (pktContainerHeader != null) {
            pktContainerDetailDAO.delByHeaderId(pktContainerHeader.getId());
            pktContainerHeaderDAO.remove(pktContainerHeader);
        }
    }
    
    @Override
    public DataPage<PktContainerDetail> queryByFilter(PktContainerDetailFilter containerDetailFilter, int startIndex, int pageSize) {
        return pktContainerDetailDAO.findRangeByFilter(containerDetailFilter, startIndex, pageSize);
    }
    
    @Override
    @Transactional
    public void create(Container container, PickTask pickTask, BigDecimal pickedQty, BigDecimal pickedQtyUnit,Long regionId) {
        PktContainerHeader pktContainerHeader = getHeaderByContainerNo(container.getContainerNo());
        if (pktContainerHeader == null) {
            pktContainerHeader = new PktContainerHeader();
            pktContainerHeader.setContainerNo(container.getContainerNo());
            pktContainerHeader.setDocNo(container.getDocNo());
            pktContainerHeader.setDocType(container.getDocType());
            pktContainerHeader.setRegionId(regionId);
            pktContainerHeaderDAO.save(pktContainerHeader);
        }
        
        PktContainerDetail containerDetail = new PktContainerDetail();
        containerDetail.setSkuId(pickTask.getSkuId());
        containerDetail.setLocId(pickTask.getLocId());
        containerDetail.setTaskId(pickTask.getId());
        containerDetail.setLotId(pickTask.getLotId());
        containerDetail.setQty(pickedQty);
        containerDetail.setHeaderId(pktContainerHeader.getId());
        containerDetail.setQtyUnit(pickedQtyUnit);
        containerDetail.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        if (StringUtil.isEmpty(containerDetail.getCreatedBy())) {
            containerDetail.setCreatedBy(ParamUtil.getCurrentLoginName());
        }
        if (StringUtil.isEmpty(containerDetail.getUpdatedBy())) {
            containerDetail.setUpdatedBy(ParamUtil.getCurrentLoginName());
        }
        pktContainerDetailDAO.saveOrUpdate(containerDetail);
    }
    
    @Override
    @Transactional
    //不支持整箱的算容器；
    public void createTemp(WaveHeader waveHeader) {
        List<PickTaskDto> pickTasks = pickTaskService.getTasksPreContainer(waveHeader.getId());
        
        ContainerType containerType = containerTypeService.getByType(Constants.ContainerType.WAVE_CONTAINER);
        if (containerType == null) {
            return;
        }
    
        createTemp(waveHeader, pickTasks, containerType, pickTasks.get(0).getRegionId());
    }
    
    @Override
    public List<PickContainerResponse.Container> findList4Wcs(PickContainerRequest request) {
        return pktContainerHeaderDAO.findList4Wcs(request);
    }
    
    @Override
    public List<PickContainerResponse.PickTask> findPickTaskList4Wcs(List<Long> idList, Boolean ignoreBatchFlag) {
        return pktContainerHeaderDAO.findPickTaskList4Wcs(idList,ignoreBatchFlag);
    }
    
    @Override
    @Transactional
    public void createDetail(PktContainerDetail pktContainerDetail) {
        pktContainerDetailDAO.saveOrUpdate(pktContainerDetail);
    }

    @Override
    public List<PktContainerDetail> findListByFilter(PktContainerDetailFilter containerDetailFilter) {
        return pktContainerDetailDAO.findByFilter(containerDetailFilter);
    }

    @Override
    @Transactional
    public void updateDetail(PktContainerDetail pktContainerDetail) {
        pktContainerDetailDAO.save(pktContainerDetail);
    }

    @Override
    @Transactional
    public void updateHeader(PktContainerHeader pktContainerHeader) {
        pktContainerHeaderDAO.saveOrUpdate(pktContainerHeader);
    }

    @Override
    public PktContainerHeader getPktContainerHeader4Bind(List<Long> regionIds) {
        return pktContainerHeaderDAO.getPktContainerHeader4Bind(regionIds);
    }

    @Override
    public List<String> findChuteListByHeaderId(Long id) {
        return pktContainerDetailDAO.findChuteListByHeaderId(id);
    }

    @Override
    public Integer countPickTaskSize(String regionCode) {
        return pktContainerHeaderDAO.countPickTaskSize(regionCode);
    }

    @Override
    public Integer checkContainerChute(String containerNo, String chute) {
        Integer count = pktContainerHeaderDAO.countPickTask4ChuteCheck(containerNo, chute);
        return count == 0 ? 0 : 1;
    }
    
    @Override
    @Transactional
    public void delByWave(WaveHeader waveHeader) {
        PktContainerHeader pktContainerHeader = getHeaderByWaveNo(waveHeader.getWaveNo());
        if (pktContainerHeader != null) {
            pktContainerDetailDAO.delByHeaderId(pktContainerHeader.getId());
            pktContainerHeaderDAO.remove(pktContainerHeader);
        }
    }
    
    private PktContainerHeader getHeaderByWaveNo(String waveNo) {
        Map<String, Object> params = ImmutableMap.<String, Object>of("docType", Constants.BindDocType.WAVE.getValue(), "docNo", waveNo, "warehouseId", ParamUtil.getCurrentWarehouseId());
        List<PktContainerHeader> pktContainerHeaders = pktContainerHeaderDAO.find(params);
        if (ListUtil.isNotEmpty(pktContainerHeaders)) {
            return pktContainerHeaders.get(0);
        }
        return null;
    }
    
    private void createTemp(WaveHeader waveHeader, List<PickTaskDto> pickTasks, ContainerType containerType, Long regionId) {
        BigDecimal maxVolume = containerType.getMaxVolume();//容积上限；
        BigDecimal leftVolumn = maxVolume;
        List<Pair<PickTaskDto, BigDecimal>> tempTask = Lists.newArrayList();
        for (PickTaskDto pickTaskDto : pickTasks) {
            ////不支持整箱的算容器；
            if (pickTaskDto.getQty().compareTo(pickTaskDto.getQtyUnit()) != 0) {
                throw new DeliveryException(DeliveryException.PACKAGE_TYPE_ERROR);
            }
            
            BigDecimal skuVolume = pickTaskDto.getVolume();
            BigDecimal taskVolume = pickTaskDto.getQty().multiply(skuVolume);
            if (taskVolume.compareTo(leftVolumn) <= 0) {
                tempTask.add(ImmutablePair.of(pickTaskDto, pickTaskDto.getQty()));
                leftVolumn = leftVolumn.subtract(taskVolume);
            } else { //任务拆分
                BigDecimal qty = pickTaskDto.getQty();
                BigDecimal tempVolume = BigDecimal.ZERO;
                BigDecimal tempQty = BigDecimal.ZERO;
                for (BigDecimal i = BigDecimal.ONE; i.compareTo(qty) <= 0; i = i.add(BigDecimal.ONE)) {
                    if (tempVolume.add(skuVolume).compareTo(leftVolumn) > 0) {
                        if (tempQty.compareTo(BigDecimal.ZERO) > 0) {
                            tempTask.add(ImmutablePair.of(splitPickTask(pickTaskDto, tempQty), tempQty));
                            
                            tempVolume = skuVolume;
                            tempQty = BigDecimal.ONE;
                        } else if (tempQty.compareTo(BigDecimal.ZERO) == 0) {
                            //如果已计算的拣货任务为空，且单一unit的体积大于容器体积；
                            if (ListUtil.isNullOrEmpty(tempTask)) {
                                tempTask.add(ImmutablePair.of(splitPickTask(pickTaskDto, BigDecimal.ONE), BigDecimal.ONE));
        
                                tempVolume = BigDecimal.ZERO;
                                tempQty = BigDecimal.ZERO;
                            } else if (ListUtil.isNotEmpty(tempTask)) {
                                tempVolume = skuVolume;
                                tempQty = BigDecimal.ONE;
                            }
                        }
                        create(tempTask, Constants.BindDocType.WAVE, waveHeader.getWaveNo(), regionId, waveHeader.getPriority(), waveHeader.getEstDoFinishTime());
                        tempTask.clear();
                        
                        leftVolumn = maxVolume;
                    } else if (tempVolume.add(skuVolume).compareTo(leftVolumn) <= 0) {
                        tempVolume = tempVolume.add(skuVolume);
                        tempQty = tempQty.add(BigDecimal.ONE);
                    }
                }
                
                if (tempVolume.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal detailQty = tempQty.add(qty.subtract(qty.setScale(0, BigDecimal.ROUND_DOWN)));
                    tempTask.add(ImmutablePair.of(splitPickTask(pickTaskDto, detailQty), detailQty));
    
                    leftVolumn = leftVolumn.subtract(tempVolume);
                }
            }
        }
        if (ListUtil.isNotEmpty(tempTask)) {
            create(tempTask, Constants.BindDocType.WAVE, waveHeader.getWaveNo(), regionId, waveHeader.getPriority(), waveHeader.getEstDoFinishTime());
        }
    }
    
    @Transactional
    protected PickTaskDto splitPickTask(PickTaskDto pickTaskDto, BigDecimal newQty) {
        if (pickTaskDto.getQty().compareTo(newQty) > 0) {
            Long newTaskId = pickTaskService.splitPickTask(pickTaskDto.getId(), newQty);
        
            PickTaskDto newPickTaskDto = new PickTaskDto();
            newPickTaskDto.setId(newTaskId);
            newPickTaskDto.setLotId(pickTaskDto.getLotId());
            newPickTaskDto.setLocId(pickTaskDto.getLocId());
            newPickTaskDto.setSkuId(pickTaskDto.getSkuId());
            newPickTaskDto.setQty(newQty);
            newPickTaskDto.setQtyUnit(newQty);
            newPickTaskDto.setVolume(pickTaskDto.getVolume());
        
            pickTaskDto.setQty(pickTaskDto.getQty().subtract(newQty));
            pickTaskDto.setQtyUnit(pickTaskDto.getQty().subtract(newQty));
            return newPickTaskDto;
        } else if (pickTaskDto.getQty().compareTo(newQty) == 0) {
            return pickTaskDto;
        } else {
            throw new IllegalArgumentException("Split Qty is over");
        }
    }
    
    protected void create(List<Pair<PickTaskDto, BigDecimal>> tempTask, Constants.BindDocType docType, String docNo, Long regionId, Integer priority, Date estDoFinishTime) {
        PktContainerHeader pktContainerHeader = new PktContainerHeader();
        pktContainerHeader.setContainerNo(UUID.randomUUID().toString());
        pktContainerHeader.setDocNo(docNo);
        pktContainerHeader.setDocType(docType.getValue());
        pktContainerHeader.setRegionId(regionId);
        pktContainerHeader.setPriority(priority);
        pktContainerHeader.setEstDoFinishTime(estDoFinishTime);
        pktContainerHeaderDAO.save(pktContainerHeader);
        
        for (Pair<PickTaskDto, BigDecimal> pair : tempTask) {
            PickTaskDto pickTask = pair.getLeft();
            BigDecimal qty = pair.getRight();

            PktContainerDetail containerDetail = new PktContainerDetail();
            containerDetail.setSkuId(pickTask.getSkuId());
            containerDetail.setLocId(pickTask.getLocId());
            containerDetail.setTaskId(pickTask.getId());
            containerDetail.setLotId(pickTask.getLotId());
            containerDetail.setQty(qty);
            containerDetail.setHeaderId(pktContainerHeader.getId());
            containerDetail.setQtyUnit(qty);
            containerDetail.setWarehouseId(ParamUtil.getCurrentWarehouseId());
            if (StringUtil.isEmpty(containerDetail.getCreatedBy())) {
                containerDetail.setCreatedBy(ParamUtil.getCurrentLoginName());
            }
            if (StringUtil.isEmpty(containerDetail.getUpdatedBy())) {
                containerDetail.setUpdatedBy(ParamUtil.getCurrentLoginName());
            }
            pktContainerDetailDAO.saveOrUpdate(containerDetail);
        }
    }
    
    private PktContainerHeader getHeaderByContainerNo(String containerNo) {
        Map<String, Object> params = ImmutableMap.<String, Object>of("containerNo", containerNo, "warehouseId", ParamUtil.getCurrentWarehouseId());
        List<PktContainerHeader> pktContainerHeaders = pktContainerHeaderDAO.find(params);
        if (ListUtil.isNotEmpty(pktContainerHeaders)) {
            return pktContainerHeaders.get(0);
        }
        return null;
    }
    
    public static void main(String[] args) {
        PktContainerServiceImpl pktContainerService = new PktContainerServiceImpl() {
            protected void create(List<Pair<PickTaskDto, BigDecimal>> tempTask, Constants.BindDocType docType, String docNo, Long regionId) {
                BigDecimal volume = BigDecimal.ZERO;
                for (Pair<PickTaskDto, BigDecimal> pair : tempTask) {
                    System.out.println("skuId: " + pair.getLeft().getSkuId() + ",qty: " + pair.getRight() + ", volume: " + pair.getLeft().getVolume());
            
                    volume = volume.add(pair.getRight().multiply(pair.getLeft().getVolume()));
                }
        
                System.out.println("Create container" + ", volume: " + volume);
            }
            
            @Override
            protected PickTaskDto splitPickTask(PickTaskDto pickTaskDto, BigDecimal newQty) {
                if (pickTaskDto.getQty().compareTo(newQty) > 0) {
                    System.out.println("new task:" + newQty);
                    Long newTaskId = -1L;
    
                    PickTaskDto newPickTaskDto = new PickTaskDto();
                    newPickTaskDto.setId(newTaskId);
                    newPickTaskDto.setLotId(pickTaskDto.getLotId());
                    newPickTaskDto.setLocId(pickTaskDto.getLocId());
                    newPickTaskDto.setSkuId(pickTaskDto.getSkuId());
                    newPickTaskDto.setQty(newQty);
                    newPickTaskDto.setVolume(pickTaskDto.getVolume());
    
                    pickTaskDto.setQty(pickTaskDto.getQty().subtract(newQty));
                    
                    return newPickTaskDto;
                }
                return pickTaskDto;
            }
        };
        
        WaveHeader waveHeader = new WaveHeader();
        waveHeader.setWaveNo("sssssssss");
        
        ContainerType containerType = new ContainerType();
        containerType.setMaxVolume(new BigDecimal("1.2"));
        System.out.println("====Max Volume: " + containerType.getMaxVolume() + "=========");
        
        List<PickTaskDto> pickTaskDtos = Lists.newArrayList();
        
        PickTaskDto pickTaskDto = new PickTaskDto();
        pickTaskDto.setSkuId(1L);
        pickTaskDto.setLocId(1L);
        pickTaskDto.setLotId(1L);
        pickTaskDto.setVolume(new BigDecimal("0.5"));
        pickTaskDto.setQty(new BigDecimal("3.2"));
        pickTaskDto.setQtyUnit(new BigDecimal("3.2"));
        pickTaskDtos.add(pickTaskDto);
    
        pktContainerService.createTemp(waveHeader, pickTaskDtos, containerType, 1L);
    
        System.out.println("====Max Volume: " + containerType.getMaxVolume() + "=========");
        pickTaskDtos.clear();
        
        pickTaskDto = new PickTaskDto();
        pickTaskDto.setSkuId(1L);
        pickTaskDto.setLocId(1L);
        pickTaskDto.setLotId(1L);
        pickTaskDto.setVolume(new BigDecimal("1.3"));
        pickTaskDto.setQty(new BigDecimal("3"));
        pickTaskDto.setQtyUnit(new BigDecimal("3"));
        pickTaskDtos.add(pickTaskDto);
        
        pktContainerService.createTemp(waveHeader, pickTaskDtos, containerType,1L);
    
    
        System.out.println("====Max Volume: " + containerType.getMaxVolume() + "=========");
        pickTaskDtos.clear();
        
        PickTaskDto pickTaskDto1 = new PickTaskDto();
        pickTaskDto.setSkuId(1L);
        pickTaskDto.setLocId(1L);
        pickTaskDto.setLotId(1L);
        pickTaskDto.setVolume(new BigDecimal("0.2"));
        pickTaskDto.setQty(new BigDecimal("3"));
        pickTaskDto.setQtyUnit(new BigDecimal("3"));
        pickTaskDtos.add(pickTaskDto);
        
        PickTaskDto pickTaskDto2 = new PickTaskDto();
        pickTaskDto2.setSkuId(2L);
        pickTaskDto2.setLocId(2L);
        pickTaskDto2.setLotId(2L);
        pickTaskDto2.setVolume(new BigDecimal("0.3"));
        pickTaskDto2.setQty(new BigDecimal("3"));
        pickTaskDto2.setQtyUnit(new BigDecimal("3"));
        pickTaskDtos.add(pickTaskDto2);
        
        pktContainerService.createTemp(waveHeader, pickTaskDtos, containerType,1L);
    
    
        containerType = new ContainerType();
        containerType.setMaxVolume(new BigDecimal("160"));
        System.out.println("====Max Volume: " + containerType.getMaxVolume() + "=========");
        pickTaskDtos.clear();
        
        pickTaskDto1 = new PickTaskDto();
        pickTaskDto1.setSkuId(1L);
        pickTaskDto1.setLocId(1L);
        pickTaskDto1.setLotId(1L);
        pickTaskDto1.setVolume(new BigDecimal("176.4"));
        pickTaskDto1.setQty(new BigDecimal("2"));
        pickTaskDto1.setQtyUnit(new BigDecimal("2"));
        pickTaskDtos.add(pickTaskDto1);
    
        pickTaskDto2 = new PickTaskDto();
        pickTaskDto2.setSkuId(2L);
        pickTaskDto2.setLocId(2L);
        pickTaskDto2.setLotId(2L);
        pickTaskDto2.setVolume(new BigDecimal("690"));
        pickTaskDto2.setQty(new BigDecimal("5"));
        pickTaskDto2.setQtyUnit(new BigDecimal("5"));
        pickTaskDtos.add(pickTaskDto2);
    
        pktContainerService.createTemp(waveHeader, pickTaskDtos, containerType,1L);
    
        containerType = new ContainerType();
        containerType.setMaxVolume(new BigDecimal("3000"));
        System.out.println("====Max Volume: " + containerType.getMaxVolume() + "=========");
        pickTaskDtos.clear();
    
       
    
        pickTaskDto1 = new PickTaskDto();
        pickTaskDto1.setSkuId(1L);
        pickTaskDto1.setLocId(1L);
        pickTaskDto1.setLotId(1L);
        pickTaskDto1.setVolume(new BigDecimal("690"));
        pickTaskDto1.setQty(new BigDecimal("1"));
        pickTaskDto1.setQtyUnit(new BigDecimal("1"));
        pickTaskDtos.add(pickTaskDto1);
    
        pickTaskDto2 = new PickTaskDto();
        pickTaskDto2.setSkuId(1L);
        pickTaskDto2.setLocId(1L);
        pickTaskDto2.setLotId(1L);
        pickTaskDto2.setVolume(new BigDecimal("690"));
        pickTaskDto2.setQty(new BigDecimal("1"));
        pickTaskDto2.setQtyUnit(new BigDecimal("1"));
        pickTaskDtos.add(pickTaskDto2);
    
        PickTaskDto pickTaskDto3 = new PickTaskDto();
        pickTaskDto3.setSkuId(1L);
        pickTaskDto3.setLocId(1L);
        pickTaskDto3.setLotId(1L);
        pickTaskDto3.setVolume(new BigDecimal("690"));
        pickTaskDto3.setQty(new BigDecimal("1"));
        pickTaskDto3.setQtyUnit(new BigDecimal("1"));
        pickTaskDtos.add(pickTaskDto3);
    
        PickTaskDto pickTaskDto4 = new PickTaskDto();
        pickTaskDto4.setSkuId(1L);
        pickTaskDto4.setLocId(1L);
        pickTaskDto4.setLotId(1L);
        pickTaskDto4.setVolume(new BigDecimal("690"));
        pickTaskDto4.setQty(new BigDecimal("1"));
        pickTaskDto4.setQtyUnit(new BigDecimal("1"));
        pickTaskDtos.add(pickTaskDto4);
    
        PickTaskDto pickTaskDto5 = new PickTaskDto();
        pickTaskDto5.setSkuId(1L);
        pickTaskDto5.setLocId(1L);
        pickTaskDto5.setLotId(1L);
        pickTaskDto5.setVolume(new BigDecimal("690"));
        pickTaskDto5.setQty(new BigDecimal("1"));
        pickTaskDto5.setQtyUnit(new BigDecimal("1"));
        pickTaskDtos.add(pickTaskDto5);
    
        PickTaskDto pickTaskDto6 = new PickTaskDto();
        pickTaskDto6.setSkuId(2L);
        pickTaskDto6.setLocId(2L);
        pickTaskDto6.setLotId(2L);
        pickTaskDto6.setVolume(new BigDecimal("176.4"));
        pickTaskDto6.setQty(new BigDecimal("2"));
        pickTaskDto6.setQtyUnit(new BigDecimal("2"));
        pickTaskDtos.add(pickTaskDto6);
    
    
        PickTaskDto pickTaskDto7 = new PickTaskDto();
        pickTaskDto7.setSkuId(2L);
        pickTaskDto7.setLocId(2L);
        pickTaskDto7.setLotId(2L);
        pickTaskDto7.setVolume(new BigDecimal("176.4"));
        pickTaskDto7.setQty(new BigDecimal("2"));
        pickTaskDto7.setQtyUnit(new BigDecimal("2"));
        pickTaskDtos.add(pickTaskDto7);
    
        pktContainerService.createTemp(waveHeader, pickTaskDtos, containerType,1L);
    
        pickTaskDtos.clear();
        containerType = new ContainerType();
        containerType.setMaxVolume(new BigDecimal("1500"));
        System.out.println("====Max Volume: " + containerType.getMaxVolume() + "=========");
        
        pickTaskDto1 = new PickTaskDto();
        pickTaskDto1.setSkuId(1L);
        pickTaskDto1.setLocId(1L);
        pickTaskDto1.setLotId(1L);
        pickTaskDto1.setVolume(new BigDecimal("690"));
        pickTaskDto1.setQty(new BigDecimal("3"));
        pickTaskDto1.setQtyUnit(new BigDecimal("3"));
        pickTaskDtos.add(pickTaskDto1);
    
        pickTaskDto2 = new PickTaskDto();
        pickTaskDto2.setSkuId(1L);
        pickTaskDto2.setLocId(1L);
        pickTaskDto2.setLotId(1L);
        pickTaskDto2.setVolume(new BigDecimal("690"));
        pickTaskDto2.setQty(new BigDecimal("2"));
        pickTaskDto2.setQtyUnit(new BigDecimal("2"));
        pickTaskDtos.add(pickTaskDto2);
    
        pickTaskDto4 = new PickTaskDto();
        pickTaskDto4.setSkuId(1L);
        pickTaskDto4.setLocId(1L);
        pickTaskDto4.setLotId(1L);
        pickTaskDto4.setVolume(new BigDecimal("176.4"));
        pickTaskDto4.setQty(new BigDecimal("2"));
        pickTaskDto4.setQtyUnit(new BigDecimal("2"));
        pickTaskDtos.add(pickTaskDto4);
    
        pktContainerService.createTemp(waveHeader, pickTaskDtos, containerType,1L);
    }
}