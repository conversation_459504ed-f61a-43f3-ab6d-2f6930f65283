package com.daxia.wms.delivery.util;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.ProtocolException;
import java.net.URL;

@lombok.extern.slf4j.Slf4j
public class XmlUtil {

    public static String processRequest(String requestUrl, String requestXmlStr) throws Exception {

        // 请求URL
        URL url = new URL(requestUrl);
        HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
        byte[] buf = setConnectionProperty(requestXmlStr, httpConn);
        OutputStream out = httpConn.getOutputStream();
        out.write(buf);

        // 解析数据
        InputStream is = httpConn.getInputStream();
        String responseStr = inputStream2String(is);
        out.close();
        is.close();
        return responseStr;
    }

    private static byte[] setConnectionProperty(String xmlStr, HttpURLConnection httpConn) throws UnsupportedEncodingException, ProtocolException {
        byte[] buf = xmlStr.getBytes("UTF-8");
        httpConn.setRequestProperty("Content-Length", String.valueOf(buf.length));
        httpConn.setRequestProperty("Content-Type", "text/xml; charset=utf-8");
        httpConn.setRequestMethod("POST");
        httpConn.setDoOutput(true);
        httpConn.setDoInput(true);
        return buf;
    }

    private static String inputStream2String(InputStream is) throws IOException {
        BufferedReader in = in = new BufferedReader(new InputStreamReader(is));
        String result = "";
        // 定义BufferedReader输入流来读取URL的响应
        String line;
        while ((line = in.readLine()) != null) {
            result += line;
        }
        return result;
    }
}
