package com.daxia.wms.delivery.print.dto;

import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.master.entity.*;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@lombok.extern.slf4j.Slf4j
public class DoPrint {

    private List<DoPrintSub> printSubList;

    private String doTitle;

    /**
     * 发货单号
     */
    private String doNo;

    /**
     * 父发货单号
     */
    private String parentDoNo;

    /**
     * 发运时间
     */
    private Date shipTime;
    
    /**
     * 标准预计出库时间
     */
    private Date planShipTime;

    /**
     * 发货单类型 DO、调拨、RTV
     */
    private String doType;

    /**
     * 参考编号1(SO单号/调拨指令号)
     */
    private String refNo1;

    /**
     * 参考编号2
     */
    private String refNo2;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 配送说明 半日达/一日三送/团购
     */
    private Long specFlag;

    /**
     * 有无发票
     */
    private Long invoiceFlag;

    /**
     * 订货数量 EXPECTED_QTY_EACH
     */
    private BigDecimal expectedQty;

    /**
     * 发货数量
     */
    private BigDecimal shipQty;

    /**
     * 收货方
     */
    private String consigneeName;

    /**
     * 收货地址
     */
    private String address;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 发票数量
     */
    private Long invoiceQty;

    /**
     * 是否第一次购买
     */
    private String userDeffine1;

    /**
     * 是否分期付款
     */
    private String userDeffine2;

    /**
     * 期望到货时间1
     */
    private Date expectedArriveTime1;

    /**
     * 期望到货时间2
     */
    private Date expectedArriveTime2;

    /**
     * 备注
     */
    private String notes;

    /**
     * 分拣格号
     */
    private String sortGridNo;

    // 订单总额
    private BigDecimal orderAmount;

    // 货品总额
    private BigDecimal productAmount;

    // 已收款
    private BigDecimal amountPayable;

    // 总返利金额
    private BigDecimal orderFrostRebate;

    // 运费
    private BigDecimal orderDeliveryFee;

    // 毛重
    private BigDecimal grossWt;

    // 称重毛重
    private BigDecimal totalGrossWt;

    /**
     * 配送方式
     */
    private Integer paymentType;

    private Date doCreateTime; // DO创建时间

    /**
     * 配送商
     */
    private Carrier carrier;

    private Country countryInfo;

    private Province provinceInfo;

    private City cityInfo;

    private County countyInfo;

    // 应收款
    private BigDecimal receivable;

    // 是否半日达
    private Integer isHalfDayDelivery;

    private Long origId;// 原始单据ID

    private String contact;

    // 付款方式
    private String paymentMethod;

    // 付款方式(弃用paymentMethod)
    private String paymentMethodName;

    private Integer printNum;

    private String expectedReceiveTime;

    private String edi1;

    private String edi2;

    private Timestamp createdAt;

    // 2012-08-27 added 制单人(调拨)
    private String createdBy;

    // 2012-08-27 调拨/RMA仓库联系人
    private String userDeffine3;

    // 退换货标识
    private Integer exchangeFlag;

    /**
     * 箱内单号 箱内配送单条码=DO编号+
     * "."+DO编号的倒数第3位，例：原DO号是" 0120032704"，则箱内面单的配送单条码为" 0120032704.7"
     */
    private String cartionInNo;

    private Boolean isB2B = Boolean.FALSE;

    /**
     * 控制打印时是否显示金额，0：不显示，1：显示
     */
    private Integer displayPrice;

    /**
     * 在配送要求栏：是否添加“午”字标识
     */
    private Integer noonFlag;

    /**
     * 服务类型 0:普通，1：SBY,2：LBY 客户端打印用
     */
    private Integer serviceType;

    // 公司网站
    private String comSite;

    // 联系电话
    private String comPhone;

    // 微博
    private String comWeibo;

    /**
     * 委托方(客户端显示用)
     */
    private String merchantName;
    
    /**
     * 限时达类型标识：
     * 半日达         ——  半
     * 一日三送     ——  三
     * 准时达         ——  准
     */
    private String deliveryLimitTypeFlag;
    
    /**
     * 打印员
     */
    private String printor;
    
    /**
     * 发货单上评论提示
     */
    private String deliveryCommentTip;
    
    /**
     * 登记标识
     */
    private String registerFlag;
    
    /**
     * 父SO号
     */
    private String parentSoNo;
    
    /**
     * 配送商Id
     */
    private Long carrierId;
    
    /**
     * 特殊标识
     */
    private String specialFlags;
    
    /**
     * 优惠金额
     */
    private BigDecimal disCountAmount;
    
    /**
     * 波次号
     */
    private String waveNo;
    
    // 送货单底部的一些服务展示类信息
 	private String bottomLine1;
 	private String bottomLine2;
 	private String bottomLine3;
 	private String bottomLine4;
    
    /**
     * 集货柜名称
     */
    private String sortingZoneName;
    
    private String comLogo;
    
    public DoPrint(DeliveryOrderHeader doHeader) {
        BeanUtils.copyProperties(doHeader, this);
        this.setParentDoNo(doHeader.getUserDeffine3());

        String doNo = doHeader.getDoNo();
        int len = doNo.length();
        this.setCartionInNo(doNo + "." + doNo.substring(len - 3, len - 2));
        this.setServiceType(doHeader.getServiceType());
    }

    public Integer getExchangeFlag() {
        return exchangeFlag;
    }

    public void setExchangeFlag(Integer exchangeFlag) {
        this.exchangeFlag = exchangeFlag;
    }

    public String getAddress() {
        return address;
    }

    public BigDecimal getAmountPayable() {
        return amountPayable;
    }

    public Carrier getCarrier() {
        return carrier;
    }

    public City getCityInfo() {
        return cityInfo;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public String getContact() {
        return contact;
    }

    public Country getCountryInfo() {
        return countryInfo;
    }

    public County getCountyInfo() {
        return countyInfo;
    }

    public Timestamp getCreatedAt() {
        return createdAt;
    }

    public Date getDoCreateTime() {
        return doCreateTime;
    }

    public String getDoNo() {
        return doNo;
    }

    public String getDoType() {
        return doType;
    }

    public String getEdi1() {
        return edi1;
    }

    public String getEdi2() {
        return edi2;
    }

    public Date getExpectedArriveTime1() {
        return expectedArriveTime1;
    }

    public Date getExpectedArriveTime2() {
        return expectedArriveTime2;
    }

    public BigDecimal getExpectedQty() {
        return expectedQty;
    }

    public String getExpectedReceiveTime() {
        return expectedReceiveTime;
    }

    public BigDecimal getGrossWt() {
        return grossWt;
    }

    public Long getInvoiceFlag() {
        return invoiceFlag;
    }

    public Long getInvoiceQty() {
        return invoiceQty;
    }

    public Integer getIsHalfDayDelivery() {
        return isHalfDayDelivery;
    }

    public String getMobile() {
        return mobile;
    }

    public String getNotes() {
        return notes;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public BigDecimal getOrderDeliveryFee() {
        return orderDeliveryFee;
    }

    public BigDecimal getOrderFrostRebate() {
        return orderFrostRebate;
    }

    public Long getOrigId() {
        return origId;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public String getPaymentMethodName() {
        return paymentMethodName;
    }

    public Integer getPaymentType() {
        return paymentType;
    }

    public String getPostCode() {
        return postCode;
    }

    public Integer getPrintNum() {
        return printNum;
    }

    public List<DoPrintSub> getPrintSubList() {
        return printSubList;
    }

    public BigDecimal getProductAmount() {
        return productAmount;
    }

    public Province getProvinceInfo() {
        return provinceInfo;
    }

    public BigDecimal getReceivable() {
        return receivable;
    }

    public String getRefNo1() {
        return refNo1;
    }

    public String getRefNo2() {
        return refNo2;
    }

    public BigDecimal getShipQty() {
        return shipQty;
    }

    public Date getShipTime() {
        return shipTime;
    }
    
    public String getSortGridNo() {
        return sortGridNo;
    }

    public Long getSpecFlag() {
        return specFlag;
    }

    public String getTelephone() {
        return telephone;
    }

    public BigDecimal getTotalGrossWt() {
        return totalGrossWt;
    }

    public String getUserDeffine1() {
        return userDeffine1;
    }

    public String getUserDeffine2() {
        return userDeffine2;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public void setAmountPayable(BigDecimal amountPayable) {
        this.amountPayable = amountPayable;
    }

    public void setCarrier(Carrier carrier) {
        this.carrier = carrier;
    }

    public void setCityInfo(City cityInfo) {
        this.cityInfo = cityInfo;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public void setCountryInfo(Country countryInfo) {
        this.countryInfo = countryInfo;
    }

    public void setCountyInfo(County countyInfo) {
        this.countyInfo = countyInfo;
    }

    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }

    public void setDoCreateTime(Date doCreateTime) {
        this.doCreateTime = doCreateTime;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public void setDoType(String doType) {
        this.doType = doType;
    }

    public void setEdi1(String edi1) {
        this.edi1 = edi1;
    }

    public void setEdi2(String edi2) {
        this.edi2 = edi2;
    }

    public void setExpectedArriveTime1(Date expectedArriveTime1) {
        this.expectedArriveTime1 = expectedArriveTime1;
    }

    public void setExpectedArriveTime2(Date expectedArriveTime2) {
        this.expectedArriveTime2 = expectedArriveTime2;
    }

    public void setExpectedQty(BigDecimal expectedQty) {
        this.expectedQty = expectedQty;
    }

    public void setExpectedReceiveTime(String expectedReceiveTime) {
        this.expectedReceiveTime = expectedReceiveTime;
    }

    public void setGrossWt(BigDecimal grossWt) {
        this.grossWt = grossWt;
    }

    public void setInvoiceFlag(Long invoiceFlag) {
        this.invoiceFlag = invoiceFlag;
    }

    public void setInvoiceQty(Long invoiceQty) {
        this.invoiceQty = invoiceQty;
    }

    public void setIsHalfDayDelivery(Integer isHalfDayDelivery) {
        this.isHalfDayDelivery = isHalfDayDelivery;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public void setOrderDeliveryFee(BigDecimal orderDeliveryFee) {
        this.orderDeliveryFee = orderDeliveryFee;
    }

    public void setOrderFrostRebate(BigDecimal orderFrostRebate) {
        this.orderFrostRebate = orderFrostRebate;
    }

    public void setOrigId(Long origId) {
        this.origId = origId;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public void setPaymentMethodName(String paymentMethodName) {
        this.paymentMethodName = paymentMethodName;
    }

    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public void setPrintNum(Integer printNum) {
        this.printNum = printNum;
    }

    public void setPrintSubList(List<DoPrintSub> printSubList) {
        this.printSubList = printSubList;
    }

    public void setProductAmount(BigDecimal productAmount) {
        this.productAmount = productAmount;
    }

    public void setProvinceInfo(Province provinceInfo) {
        this.provinceInfo = provinceInfo;
    }

    public void setReceivable(BigDecimal receivable) {
        this.receivable = receivable;
    }

    public void setRefNo1(String refNo1) {
        this.refNo1 = refNo1;
    }

    public void setRefNo2(String refNo2) {
        this.refNo2 = refNo2;
    }

    public void setShipQty(BigDecimal shipQty) {
        this.shipQty = shipQty;
    }

    public void setShipTime(Date shipTime) {
        this.shipTime = shipTime;
    }
    
    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    public void setSpecFlag(Long specFlag) {
        this.specFlag = specFlag;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public void setTotalGrossWt(BigDecimal totalGrossWt) {
        this.totalGrossWt = totalGrossWt;
    }

    public void setUserDeffine1(String userDeffine1) {
        this.userDeffine1 = userDeffine1;
    }

    public void setUserDeffine2(String userDeffine2) {
        this.userDeffine2 = userDeffine2;
    }

    public void setDoTitle(String doTitle) {
        this.doTitle = doTitle;
    }

    public String getDoTitle() {
        return doTitle;
    }

    public String getParentDoNo() {
        return parentDoNo;
    }

    public void setParentDoNo(String parentDoNo) {
        this.parentDoNo = parentDoNo;
    }

    public String getCartionInNo() {
        return cartionInNo;
    }

    public void setCartionInNo(String cartionInNo) {
        this.cartionInNo = cartionInNo;
    }

    public Integer getDisplayPrice() {
        return displayPrice;
    }

    public void setDisplayPrice(Integer displayPrice) {
        this.displayPrice = displayPrice;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUserDeffine3() {
        return userDeffine3;
    }

    public void setUserDeffine3(String userDeffine3) {
        this.userDeffine3 = userDeffine3;
    }

    public Integer getNoonFlag() {
        return noonFlag;
    }

    public void setNoonFlag(Integer noonFlag) {
        this.noonFlag = noonFlag;
    }

    public Integer getServiceType() {
        return serviceType;
    }

    public void setServiceType(Integer serviceType) {
        this.serviceType = serviceType;
    }

    public String getComSite() {
        return comSite;
    }

    public void setComSite(String comSite) {
        this.comSite = comSite;
    }

    public String getComPhone() {
        return comPhone;
    }

    public void setComPhone(String comPhone) {
        this.comPhone = comPhone;
    }

    public String getComWeibo() {
        return comWeibo;
    }

    public void setComWeibo(String comWeibo) {
        this.comWeibo = comWeibo;
    }

    public String getMerchantName() {
        return merchantName;
    }
    
    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }
    
    public String getDeliveryLimitTypeFlag() {
        return deliveryLimitTypeFlag;
    }
    
    public void setDeliveryLimitTypeFlag(String deliveryLimitTypeFlag) {
        this.deliveryLimitTypeFlag = deliveryLimitTypeFlag;
    }
    
    public String getPrintor() {
        return printor;
    }
    
    public void setPrintor(String printor) {
        this.printor = printor;
    }
    
    public String getDeliveryCommentTip() {
        return deliveryCommentTip;
    }
    
    public void setDeliveryCommentTip(String deliveryCommentTip) {
        this.deliveryCommentTip = deliveryCommentTip;
    }
    
    public String getRegisterFlag() {
        return registerFlag;
    }
    
    public void setRegisterFlag(String registerFlag) {
        this.registerFlag = registerFlag;
    }

    public String getParentSoNo() {
        return parentSoNo;
    }

    public void setParentSoNo(String parentSoNo) {
        this.parentSoNo = parentSoNo;
    }
    
    public Date getPlanShipTime() {
        return planShipTime;
    }
    
    public void setPlanShipTime(Date planShipTime) {
        this.planShipTime = planShipTime;
    }
    
    /**
     * @param carrierId the carrierId to set
     */
    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    /**
     * @return the carrierId
     */
    public Long getCarrierId() {
        return carrierId;
    }

    public String getSpecialFlags() {
        return specialFlags;
    }

    public void setSpecialFlags(String specialFlags) {
        this.specialFlags = specialFlags;
    }

    public BigDecimal getDisCountAmount() {
        return disCountAmount;
    }

    public void setDisCountAmount(BigDecimal disCountAmount) {
        this.disCountAmount = disCountAmount;
    }

	public String getWaveNo() {
		return waveNo;
	}

	public void setWaveNo(String waveNo) {
		this.waveNo = waveNo;
	}

	public String getSortingZoneName() {
		return sortingZoneName;
	}

	public void setSortingZoneName(String sortingZoneName) {
		this.sortingZoneName = sortingZoneName;
	}

	public String getBottomLine1() {
		return bottomLine1;
	}

	public void setBottomLine1(String bottomLine1) {
		this.bottomLine1 = bottomLine1;
	}

	public String getBottomLine2() {
		return bottomLine2;
	}

	public void setBottomLine2(String bottomLine2) {
		this.bottomLine2 = bottomLine2;
	}

	public String getBottomLine3() {
		return bottomLine3;
	}

	public void setBottomLine3(String bottomLine3) {
		this.bottomLine3 = bottomLine3;
	}

	public String getBottomLine4() {
		return bottomLine4;
	}

	public void setBottomLine4(String bottomLine4) {
		this.bottomLine4 = bottomLine4;
	}

	public String getComLogo() {
		return comLogo;
	}

	public void setComLogo(String comLogo) {
		this.comLogo = comLogo;
	}
}
