package com.daxia.wms.delivery.crossorder.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.crossorder.dto.CrossSeedDTO;
import com.daxia.wms.delivery.crossorder.dto.CrossSeedDetailDTO;
import com.daxia.wms.delivery.crossorder.entity.CrossSeedDetail;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Name("com.daxia.wms.delivery.crossSeedDetailDAO")
@lombok.extern.slf4j.Slf4j
public class CrossSeedDetailDAO extends HibernateBaseDAO<CrossSeedDetail, Long> {

    public void deleteInit(Long crossSeedHeaderId) {
        String hql = " delete cd from doc_cross_seed_detail cd ";
        hql += " WHERE cd.header_id =:crossSeedHeaderId AND cd.lot_no is null and cd.warehouse_id = :warehouseId ";
        SQLQuery query = this.createSQLQuery(hql);
        query.setParameter("crossSeedHeaderId", crossSeedHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    public DataPage<CrossSeedDetailDTO> findCrossDetailByHeaderId(Long crossSeedHeaderId) {
        Long whId = ParamUtil.getCurrentWarehouseId();
        StringBuilder hql = new StringBuilder("select new com.daxia.wms.delivery.crossorder.dto.CrossSeedDetailDTO(" +
                "o.id, o.doNo, o.sortGridNo, o.containerNo, o.consigneeName,o.lotNo, o.lotatt05, o.lotatt01, o.lotatt02," +
                "o.expectedQty, o.allocatedQty, o.sortedQty, o.packedQty, s.productCode, s.ean13, s.productCname, s.udf4" +
                ")");
        hql.append(" from CrossSeedDetail o");
        hql.append(" left join o.sku s ");
        hql.append(" where o.warehouseId = " + whId.toString());
        hql.append(" and o.headerId = " + crossSeedHeaderId);
        hql.append(" order by o.sortGridNo");

        Query query = createQuery(hql.toString());

        StringBuilder countHql = new StringBuilder("select count(distinct o.id) from CrossSeedDetail o " +
                "where o.warehouseId = " + whId.toString() + "and o.headerId = " + crossSeedHeaderId);
        Query countQuery = createQuery(countHql.toString());

        DataPage<CrossSeedDetailDTO> dataPage = new DataPage<CrossSeedDetailDTO>(((Long) countQuery.uniqueResult()).intValue(),
                0, 1000, query.list());

        return dataPage;

    }

    public List<CrossSeedDetail> findCrossSeedDetails(String seedNo, List<Long> skuIds, String lotatt05, String lotatt01, String lotatt02, Integer sortedFlag) {
        Criteria cri = getSession().createCriteria(CrossSeedDetail.class);
        cri.createAlias("header", "header");
        cri.add(Restrictions.eq("header.seedNo", seedNo));
        cri.add(Restrictions.in("header.status", new String[]{Constants.CrossSeedStatus.ALLALLOCATED.getValue(), Constants.CrossSeedStatus.SORTING.getValue()}));
        cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
        if (StringUtil.isNotBlank(lotatt05)) {
            cri.add(Restrictions.eq("lotatt05", lotatt05));
        }
        if (StringUtil.isNotBlank(lotatt01)) {
            cri.add(Restrictions.eq("lotatt01", lotatt01));
        }
        if (StringUtil.isNotBlank(lotatt02)) {
            cri.add(Restrictions.eq("lotatt02", lotatt02));
        }
        if (sortedFlag != null) {
            cri.add(Restrictions.eq("sortedFlag", sortedFlag));
        }
        if (CollectionUtils.isNotEmpty(skuIds)) {
            cri.add(Restrictions.in("skuId", skuIds));
        }
        cri.setFetchMode("header", FetchMode.JOIN);
        cri.addOrder(Order.asc("id"));
        return cri.list();
    }

    public boolean checkHeadIsNotDone(Long crossSeedHeaderId) {
        Map<String, Object> params = new HashedMap();
        params.put("headerId", crossSeedHeaderId);
        params.put("sortedFlag", 0);
        return isExists(params, null);
    }

    public boolean checkDoDetailDone(Long crossSeedHeaderId, Long doDetailId) {
        Map<String, Object> params = new HashedMap();
        params.put("headerId", crossSeedHeaderId);
        params.put("doDetailId", doDetailId);
        params.put("sortedFlag", 0);
        return isExists(params, null);
    }

    public boolean checkDoHeadDone(Long crossSeedHeaderId, Long doHeaderId) {
        Map<String, Object> params = new HashedMap();
        params.put("headerId", crossSeedHeaderId);
        params.put("doHeaderId", doHeaderId);
        params.put("sortedFlag", 0);
        return isExists(params, null);
    }

    public void updateSortedFlag(Long crossSeedHeaderId) {
        String hql = " UPDATE CrossSeedDetail cd SET cd.sortedFlag = 1 ";
        hql += " WHERE cd.headerId =:crossSeedHeaderId and cd.warehouseId = :warehouseId ";
        Query query = this.createQuery(hql);
        query.setParameter("crossSeedHeaderId", crossSeedHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    public List<CrossSeedDetail> getCrossDetailsBycontainerNo(String containerNo) {
        String hql = "from CrossSeedDetail o WHERE o.containerNo = :containerNo and o.warehouseId = :warehouseId";
        Query query = this.getSession().createQuery(hql);
        query.setParameter("containerNo", containerNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    public BigDecimal queryQtyByDoHeaderId(Long doHeaderId) {
        String hql = "select sum(cd.packedQty) - sum(cd.sortedQty)"
                + "from CrossSeedDetail cd where cd.doHeaderId =:doHeaderId and cd.warehouseId = :warehouseId  ";
        Query query = this.createQuery(hql)
                .setParameter("doHeaderId", doHeaderId)
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        Object obj = query.uniqueResult();
        if (null == obj) {
            return null;
        } else {
            return (BigDecimal) obj;
        }
    }
}
