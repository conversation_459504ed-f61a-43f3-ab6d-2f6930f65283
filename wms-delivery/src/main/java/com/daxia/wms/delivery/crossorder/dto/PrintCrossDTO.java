package com.daxia.wms.delivery.crossorder.dto;

import com.daxia.framework.common.service.Dictionary;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

@lombok.extern.slf4j.Slf4j
public class PrintCrossDTO {

    /**
     * 分播单号
     */
    private String seedNo;
    /**
     * 订单号
     */
    private String doNo;

    /**
     * 分播区格号
     */
    private String sortGridNo;

    /**
     * 分播区每个DO的容器号
     */
    private String containerNo;

    /**
     * 客户名
     */
    private String consigneeName;

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    public String getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public String getSeedNo() {
        return seedNo;
    }

    public void setSeedNo(String seedNo) {
        this.seedNo = seedNo;
    }
}