package com.daxia.wms.delivery.load.entity;


import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;

/**
 * 逆向交接单
 */
@Entity
@Table(name = "doc_reversal_detail")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = "IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_reversal_detail set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class ReversalHandoverDetail extends WhBaseEntity {
	
	private static final long serialVersionUID = 5246518269003749908L;
	
    /**
     * 主键
     */
    private Long id;
    
	/**
     * 订单号
     */
    private String doNo;
    
    /**
     * 箱号
     */
    private String cartonNo;

    /**
     * 逆向交接单ID
     */
    private Long reversalHandoverId;
    
    /**
     * 逆向交接单
     */
    private ReversalHandoverHeader reversalHandoverHeader;
    
    /**
     * 待收/已收 (枚举)
     */
    private String reversalStatus;
    
    /**
     * 订单状态
     */
    private String doStatus;
    
    /**
     * 冻结状态 HOLD：冻结 RELEASE：释放
     */
     private String releaseStatus;    
     
     /**
      * 收货人
      */
      private String receiveBy;

	/**
       * 收货时间
       */
       private Timestamp receiveTime;

	/**
     * Getter method for property <tt>id</tt>.
     * 
     * @return property value of id
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.AUTO)  
    public Long getId() {
        return id;
    }

    /**
     * Setter method for property <tt>id</tt>.
     * 
     * @param id value to be assigned to property id
     */
    public void setId(Long id) {
        this.id = id;
    }

	@Column(name = "DO_NO")
    public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}

	@Column(name = "CARTON_NO")
	public String getCartonNo() {
		return cartonNo;
	}

	public void setCartonNo(String cartonNo) {
		this.cartonNo = cartonNo;
	}

	@Column(name = "DO_REVERSAL_ID")
	public Long getReversalHandoverId() {
		return reversalHandoverId;
	}

	public void setReversalHandoverId(Long reversalHandoverId) {
		this.reversalHandoverId = reversalHandoverId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DO_REVERSAL_ID", insertable = false, updatable = false)
	public ReversalHandoverHeader getReversalHandoverHeader() {
		return reversalHandoverHeader;
	}

	public void setReversalHandoverHeader(
			ReversalHandoverHeader reversalHandoverHeader) {
		this.reversalHandoverHeader = reversalHandoverHeader;
	}

	@Column(name = "REVERSAL_STATUS")
	public String getReversalStatus() {
		return reversalStatus;
	}

	public void setReversalStatus(String reversalStatus) {
		this.reversalStatus = reversalStatus;
	}

	@Column(name = "DO_STATUS")
	public String getDoStatus() {
		return doStatus;
	}

	public void setDoStatus(String doStatus) {
		this.doStatus = doStatus;
	}

	@Column(name = "RELEASE_STATUS")
	public String getReleaseStatus() {
		return releaseStatus;
	}

	public void setReleaseStatus(String releaseStatus) {
		this.releaseStatus = releaseStatus;
	}

	@Column(name="RECEIVE_BY")
    public String getReceiveBy() {
		return receiveBy;
	}

	public void setReceiveBy(String receiveBy) {
		this.receiveBy = receiveBy;
	}
	
	@Column(name="RECEIVE_TIME")
    public Timestamp getReceiveTime() {
		return receiveTime;
	}

	public void setReceiveTime(Timestamp receiveTime) {
		this.receiveTime = receiveTime;
	}
}