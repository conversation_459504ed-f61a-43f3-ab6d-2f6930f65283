package com.daxia.wms.delivery.print.dto;

import java.util.List;

import com.daxia.wms.print.dto.PrintReportDto;

@lombok.extern.slf4j.Slf4j
public class PrintHoldDoLabelDTO extends PrintReportDto {
	private static final long serialVersionUID = 3357104967438100982L;

	/**
	 * 冻结订单号
	 */
	private String doNo;
	
	/**
	 * 页冻结商品明细
	 */
	private List<PrintHoldDoLabelDetailDTO> detailList;
	
	/**
	 * 当前页
	 */
	private Long curPage;
	
	/**
	 * 总页数
	 */
	private Long totalPage;

	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}

	public List<PrintHoldDoLabelDetailDTO> getDetailList() {
		return detailList;
	}

	public void setDetailList(List<PrintHoldDoLabelDetailDTO> detailList) {
		this.detailList = detailList;
	}

	public Long getCurPage() {
		return curPage;
	}

	public void setCurPage(Long curPage) {
		this.curPage = curPage;
	}

	public Long getTotalPage() {
		return totalPage;
	}

	public void setTotalPage(Long totalPage) {
		this.totalPage = totalPage;
	}
}
