package com.daxia.wms.delivery.backup;

import org.jboss.seam.annotations.*;
import org.jboss.seam.log.Log;

import java.util.List;

/**
 * Created by qicen on 2016/9/22.
 */
@Name("backupService")
@AutoCreate
@lombok.extern.slf4j.Slf4j
public class BackupService {

    @In
    private BackupTableDAO backupTableDAO;

    @Logger
    protected Log log;

    public List<CfgBackupTable> findAllBackupTables() {
        return backupTableDAO.findAllBackupTables();
    }

    @Transactional
    public void backupTable(CfgBackupTable cfgBackupTable) {
        backupTableInternal(cfgBackupTable);
    }

    private void backupTableInternal(CfgBackupTable cfgBackupTable) {
        String table = cfgBackupTable.getTableName();
        String where = cfgBackupTable.getBackupWhere();
        log.info("开始备份表：" + table);
        backupTableDAO.batchDeleteTemp();
        int insertCount = backupTableDAO.batchInsertTemp(table, where);
        if (insertCount > 0) {
            insertCount = backupTableDAO.batchInsert(table);
            backupTableDAO.batchDelete(table);
        }
        backupTableDAO.batchDeleteTemp();
        log.info("完成备份表：" + table + ", 备份记录数：" + insertCount);
    }


}
