package com.daxia.wms.delivery.deliveryorder.action;

import com.daxia.dubhe.api.internal.util.StrUtils;
import com.daxia.framework.common.action.ActionBean;
import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.OtherDoDetail;
import com.daxia.wms.delivery.deliveryorder.entity.OtherDoHeader;
import com.daxia.wms.delivery.deliveryorder.service.OtherDoService;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.entity.Merchant;
import com.daxia.wms.master.service.LocationService;
import com.daxia.wms.master.service.MerchantService;
import com.daxia.wms.master.service.SkuService;
import com.daxia.wms.stock.StockException;
import com.daxia.wms.stock.move.dto.StockMoveDTO;
import com.daxia.wms.stock.stock.dto.StockQueryDTO;
import com.daxia.wms.stock.stock.entity.StockBatchLocLpn;
import com.daxia.wms.stock.stock.filter.StockBatchLocLpnFilter;
import com.daxia.wms.stock.stock.filter.StockQueryFilter;
import com.daxia.wms.stock.stock.service.StockQueryService;
import com.google.common.collect.Lists;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Name("com.daxia.wms.receive.otherDoDetailAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class OtherDoDetailAction extends PagedListBean<StockMoveDTO> {

    private static final long serialVersionUID = -5022298427321759096L;
    @In
    private OtherDoService otherDoService;

    @In
    private MerchantService merchantService;

    private boolean initialized = false;

    private OtherDoHeader otherDoHeader;

    private Long doHeaderId;

    private OtherDoDetail otherDoDetail;

    private Long doDetailId;

    private StockBatchLocLpnFilter stockQueryFilter;

    private String merchantName;

    private List<StockMoveDTO> stockMoveList;

    @In
    private StockQueryService stockQueryService;

    private Long stockId;

    private int addDetailFlag = 0;

    private String printContent = "";

    public OtherDoDetailAction() {
        stockQueryFilter = new StockBatchLocLpnFilter();
    }

    @Override
    public void query() {

    }

    public void query4Add() {
        if (addDetailFlag == 1) {
            return;
        }
        stockQueryFilter.setQtyAvailable(BigDecimal.ZERO);
        DataPage<StockQueryDTO> dp = stockQueryService.queryFreeStock(stockQueryFilter, getStartIndex(), getPageSize());
        DataPage<StockMoveDTO> movePage = new DataPage<StockMoveDTO>();
        stockMoveList = Lists.newArrayList();
        for (StockQueryDTO stockQueryDTO : dp.getDataList()) {
            StockMoveDTO dto = new StockMoveDTO();
            dto.setFmLocCode(stockQueryDTO.getLocCode());
            dto.setStockDTO(stockQueryDTO);
            stockMoveList.add(dto);
        }
        movePage.setDataList(stockMoveList);
        movePage.setPageCount(dp.getPageCount());
        movePage.setTotalCount(dp.getTotalCount());
        movePage.setSumInfo(dp.getSumInfo());
        populateValues(movePage);
    }

    public void initializePage() {
        if (!initialized) {
            if (null == stockQueryFilter) {
                stockQueryFilter = new StockBatchLocLpnFilter();
            }
            reload();
            this.initialized = true;
        }
    }

    public void reload() {
        this.otherDoHeader = this.otherDoService.getHeaderById(this.doHeaderId);
        List<OtherDoDetail> otherAsnDetails = otherDoHeader.getOtherDoDetails();
        if (CollectionUtils.isEmpty(otherAsnDetails)) {
            otherDoHeader.setOtherDoDetails(otherAsnDetails);
        }
    }


    public void preAdd() {
        otherDoHeader = this.otherDoService.getHeaderById(this.doHeaderId);
        otherDoDetail = new OtherDoDetail();
        otherDoDetail.setDoHeaderId(doHeaderId);
        otherDoDetail.setOtherDoHeader(otherDoHeader);
    }

    public void addDetail() {
        addDetailFlag = 1;
        for (StockMoveDTO stockMoveDTO : stockMoveList) {
            if (stockMoveDTO.getStockDTO().getStockId().equals(stockId)) {
                if (null == stockMoveDTO.getPlanQty()
                        || BigDecimal.ZERO.compareTo(stockMoveDTO.getPlanQty())==0) {
                    continue;
                }
                otherDoService.createDetail(otherDoDetail, stockMoveDTO.getStockDTO().getStockId(), stockMoveDTO.getPlanQty());
                addDetailFlag = 0;
            }
        }
        reload();
        this.sayMessage(MESSAGE_SUCCESS);
    }

    public void edit() {
        otherDoDetail = otherDoService.getDetailById(doDetailId);
    }

    public void deletedDetail() {
        otherDoService.deletedDetail(doHeaderId, doDetailId);
        reload();
    }

    public void cancel() {
        otherDoService.cancel(doHeaderId);
        reload();
    }

    public void confirmShip() {
        otherDoService.confirmShip(doHeaderId);
        reload();
    }

    public void verify() {
        otherDoService.verify(doHeaderId);
        reload();
    }

    public void print(){
        this.printContent = "";
        this.printContent = otherDoService.print(doHeaderId);
    }

    public void receiveSelectMerchant(Long merchantId) {
        if (merchantId == null) {
            clearSelectMerchant();
        } else {
            Merchant merchant = merchantService.getMerchant(merchantId);
            stockQueryFilter.setMerchantId(StrUtils.object2String(merchantId));
            merchantName = merchant.getDescrC();
        }
    }

    public void clearSelectMerchant() {
        stockQueryFilter.setMerchantId(null);
        merchantName = "";
    }

    public OtherDoHeader getOtherDoHeader() {
        return otherDoHeader;
    }

    public void setOtherDoHeader(OtherDoHeader otherDoHeader) {
        this.otherDoHeader = otherDoHeader;
    }

    public Long getDoHeaderId() {
        return doHeaderId;
    }

    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }

    public OtherDoDetail getOtherDoDetail() {
        return otherDoDetail;
    }

    public void setOtherDoDetail(OtherDoDetail otherDoDetail) {
        this.otherDoDetail = otherDoDetail;
    }

    public Long getDoDetailId() {
        return doDetailId;
    }

    public void setDoDetailId(Long doDetailId) {
        this.doDetailId = doDetailId;
    }

    public StockBatchLocLpnFilter getStockQueryFilter() {
        return stockQueryFilter;
    }

    public void setStockQueryFilter(StockBatchLocLpnFilter stockQueryFilter) {
        this.stockQueryFilter = stockQueryFilter;
    }

    public List<StockMoveDTO> getStockMoveList() {
        return stockMoveList;
    }

    public void setStockMoveList(List<StockMoveDTO> stockMoveList) {
        this.stockMoveList = stockMoveList;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public Long getStockId() {
        return stockId;
    }

    public void setStockId(Long stockId) {
        this.stockId = stockId;
    }

    public int getAddDetailFlag() {
        return addDetailFlag;
    }

    public void setAddDetailFlag(int addDetailFlag) {
        this.addDetailFlag = addDetailFlag;
    }

    public String getPrintContent() {
        return printContent;
    }

    public void setPrintContent(String printContent) {
        this.printContent = printContent;
    }
}
