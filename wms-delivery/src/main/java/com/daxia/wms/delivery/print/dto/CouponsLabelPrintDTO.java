package com.daxia.wms.delivery.print.dto;

import java.math.BigDecimal;

@lombok.extern.slf4j.Slf4j
public class CouponsLabelPrintDTO {

    private String doNo;

    private String pickerNo;

    private String sortGridNo;

    private String waveNo;

    private String barcode;

    private String productName;

    private BigDecimal qtyEach;

    private Long skuId;

    private String barcodeA;

    private String productNameA;

    private BigDecimal qtyEachA;

    private Long skuIdA;

    private Integer totalCount;

    private Integer currentIndex;

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getPickerNo() {
        return pickerNo;
    }

    public void setPickerNo(String pickerNo) {
        this.pickerNo = pickerNo;
    }

    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getQtyEach() {
        return qtyEach;
    }

    public void setQtyEach(BigDecimal qtyEach) {
        this.qtyEach = qtyEach;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getBarcodeA() {
        return barcodeA;
    }

    public void setBarcodeA(String barcodeA) {
        this.barcodeA = barcodeA;
    }

    public String getProductNameA() {
        return productNameA;
    }

    public void setProductNameA(String productNameA) {
        this.productNameA = productNameA;
    }

    public BigDecimal getQtyEachA() {
        return qtyEachA;
    }

    public void setQtyEachA(BigDecimal qtyEachA) {
        this.qtyEachA = qtyEachA;
    }

    public Long getSkuIdA() {
        return skuIdA;
    }

    public void setSkuIdA(Long skuIdA) {
        this.skuIdA = skuIdA;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getCurrentIndex() {
        return currentIndex;
    }

    public void setCurrentIndex(Integer currentIndex) {
        this.currentIndex = currentIndex;
    }
}
