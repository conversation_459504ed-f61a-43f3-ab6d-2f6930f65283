package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.delivery.deliveryorder.service.DoWaveExService;
import com.daxia.wms.delivery.recheck.dao.CartonHeaderDAO;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.waybill.WaybillException;
import com.daxia.wms.waybill.zto.dto.*;
import com.daxia.wms.waybill.zto.service.ZtoWaybillService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;

@Name("ztoCartonNoGenerateService")
@lombok.extern.slf4j.Slf4j
public class ZTOCartonNoGenerateServiceImpl implements CartonNoGenerateService {
	@In
	private DoWaveExService doWaveExService;
	@In(create = true)
	ZtoWaybillService ztoWaybillService;
	@In
	CartonHeaderDAO cartonHeaderDAO;
	@In
	WarehouseCarrierService warehouseCarrierService;
	@In
	WarehouseService warehouseService;
	@In
	private SequenceGeneratorService sequenceGeneratorService;

	@Override
    public void generatorCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
		DoWaveEx doWaveEx = doWaveExService.findByDoHeaderId(doHeader.getId());
		if (null == doWaveEx) {
			throw new WaybillException(WaybillException.ZTO_WAYBILL_ERROR);
		}

		WarehouseCarrier warehouseCarrier = warehouseCarrierService.getCarrierInfoByWarehouseIdAndCarrierIdAndType(doHeader
				.getWarehouseId(),
				doHeader
				.getCarrierId(),Constants.WaybillType.ZTO.name());

		if (warehouseCarrier == null) {
			throw new WaybillException(WaybillException.ZTO_WAYBILL_ERROR);
		}

		Warehouse warehouse = warehouseService.getWarehouse(doHeader.getWarehouseId());

		//生成一个箱号作为业务单号
		String txLogisticId = doHeader.getDoNo() + sequenceGeneratorService.generateSequenceNo(Constants.SequenceName
				.CARTONNO.getValue(), ParamUtil.getCurrentWarehouseId());
		cartonHeader.setTrackingNo(txLogisticId);

		ZtoBillNoRequest billNoRequest = new ZtoBillNoRequest();
		//测试时只能用xfs101100111011
		billNoRequest.setId(txLogisticId);
		if (StringUtil.isNotEmpty(warehouseCarrier.getExt2())) {
			billNoRequest.setId("xfs101100111011");
		}
		//发件人信息
		Sender sender = new Sender();
		sender.setName(warehouse.getContactor());
		sender.setCompany(warehouse.getNickName());
		sender.setMobile(warehouse.getMobile());
		sender.setPhone(warehouse.getPhone());
		sender.setCity(generateSenderCity(warehouse));
		sender.setAddress(warehouse.getAddressName());
		billNoRequest.setSender(sender);

		//收件人信息
		Receiver receiver = new Receiver();
		receiver.setName(doHeader.getConsigneeName());
		receiver.setMobile(DoUtil.decryptPhone(doHeader.getMobile()));
		receiver.setPhone(DoUtil.decryptPhone(doHeader.getTelephone()));
		receiver.setCity(generateReceiverCity(doHeader));
		receiver.setAddress(doHeader.getAddress());
		billNoRequest.setReceiver(receiver);

		//COD
		if (doHeader.getReceivable().compareTo(BigDecimal.ZERO) > 0) {
			billNoRequest.setOrder_type("1");
			billNoRequest.setCollect_sum(doHeader.getReceivable());
		} else {
			billNoRequest.setOrder_type("0");
		}

		ZtoBillNoResponse billNoResponse =  ztoWaybillService.generateWabybillNo(billNoRequest,warehouseCarrier);

		if (StringUtil.isEmpty(billNoResponse.getData().getBillCode())) {
			throw new WaybillException(WaybillException.ZTO_WAYBILL_ERROR,billNoResponse.getMessage());
		}
		//运单号
		cartonHeader.setCartonNo(billNoResponse.getData().getBillCode());
		cartonHeader.setWayBill(billNoResponse.getData().getBillCode());

		doWaveEx.setOriginName(billNoResponse.getData().getSiteName());


		ZtoMarkRequest markRequest = new ZtoMarkRequest();
		markRequest.setUnionCode(String.valueOf(DateUtil.getNowTime().getTime()));
		//发货地址
		markRequest.setSend_province(warehouse.getProvince().getProvinceCname());
		markRequest.setSend_city(warehouse.getCity().getCityCname());
		markRequest.setSend_district(warehouse.getCounty().getCountyCname());
		markRequest.setSend_address(warehouse.getAddressName());
		//收货地址
		markRequest.setReceive_province(doHeader.getProvinceInfo() != null ? doHeader.getProvinceInfo().getProvinceCname() : doHeader.getProvinceName());
		markRequest.setReceive_city(doHeader.getCityInfo() != null ? doHeader.getCityInfo().getCityCname() : doHeader.getCityName());
		markRequest.setReceive_district(doHeader.getCountyInfo() != null ? doHeader.getCountyInfo().getCountyCname() : doHeader.getCountyName());
		markRequest.setReceive_address(doHeader.getAddress());
		try {
			ZtoMarkResponse markResponse = ztoWaybillService.generateMarkInfo(markRequest,warehouseCarrier);
			if (markResponse.getResult() != null) {
				//大头笔信息
				doWaveEx.setDestinationCode(markResponse.getResult().getMark());
				doWaveEx.setDestinationName(markResponse.getResult().getBagAddr());
			}
		} catch (Exception e) {
			//异常不作处理
		}

		doWaveExService.update(doWaveEx);

	}

	private String generateSenderCity(Warehouse warehouse) {
		StringBuffer buffer = new StringBuffer();
		buffer.append(warehouse.getProvince().getProvinceCname()).append(",").append(warehouse.getCity().getCityCname())
				.append(",").append(warehouse.getCounty().getCountyCname());
		return buffer.toString();
	}

	private String generateReceiverCity(DeliveryOrderHeader doHeader) {
		StringBuffer buffer = new StringBuffer();
		buffer.append(doHeader.getProvinceInfo() != null ? doHeader.getProvinceInfo().getProvinceCname() : doHeader.getProvinceName())
				.append(",")
				.append(doHeader.getCityInfo() != null ? doHeader.getCityInfo().getCityCname() : doHeader.getCityName())
				.append(",")
				.append(doHeader.getCountyInfo() != null ? doHeader.getCountyInfo().getCountyCname() : doHeader.getCountyName());
		return buffer.toString();
	}
}