package com.daxia.wms.delivery.print.service.impl;

import com.alibaba.fastjson.JSON;
import com.daxia.dubhe.api.internal.util.NumberUtils;
import com.daxia.dubhe.api.internal.util.StrUtils;
import com.daxia.framework.common.entity.CfgConfiguration;
import com.daxia.framework.common.service.Dictionary;
import com.daxia.framework.common.service.ReportGenerator;
import com.daxia.framework.common.util.*;
import com.daxia.framework.common.util.ListUtil.ListMegareOpr;
import com.daxia.framework.system.dao.UserAccountDAO;
import com.daxia.framework.system.entity.UserAccount;
import com.daxia.framework.system.service.CfgWarehouseService;
import com.daxia.framework.system.util.WmsUtil;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoType;
import com.daxia.wms.Constants.TransportWendyEnum;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.context.DeliveryContext;
import com.daxia.wms.delivery.crossorder.dto.PrintCrossDTO;
import com.daxia.wms.delivery.crossorder.entity.CrossSeedDetail;
import com.daxia.wms.delivery.crossorder.entity.CrossSeedHeader;
import com.daxia.wms.delivery.crossorder.service.CrossSeedService;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.filter.DoHeaderFilter;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.load.entity.TOCrossDockHeader;
import com.daxia.wms.delivery.pick.dto.PickTaskDto;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.pick.filter.PickTaskFilter;
import com.daxia.wms.delivery.pick.service.PickTaskService;
import com.daxia.wms.delivery.print.dto.*;
import com.daxia.wms.delivery.print.helper.SFPrintCartonHelper;
import com.daxia.wms.delivery.print.service.PrintAllotService;
import com.daxia.wms.delivery.print.service.PrintDoService;
import com.daxia.wms.delivery.print.service.PrintService;
import com.daxia.wms.delivery.recheck.dao.CartonHeaderDAO;
import com.daxia.wms.delivery.task.repick.entity.ReversePickHeader;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.master.component.BusinessCenterComponent;
import com.daxia.wms.master.dto.SkuDTO;
import com.daxia.wms.master.entity.*;
import com.daxia.wms.master.service.*;
import com.daxia.wms.print.PrintConstants;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.print.utils.PrintHelper;
import com.daxia.wms.receive.entity.InspectionReport;
import com.daxia.wms.receive.service.InspectionReportService;
import com.daxia.wms.stock.stock.entity.StockBatchAtt;
import com.daxia.wms.stock.stock.service.StockBatchAttService;
import com.daxia.wms.stock.task.entity.TrsTask;
import com.daxia.wms.stock.util.BatchPropertyUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.Component;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.security.Identity;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Name("com.daxia.wms.delivery.printService")
@lombok.extern.slf4j.Slf4j
public class PrintServiceImpl implements PrintService {

    public static final String SUB_REPORT_KEY = "subReport";
    public static final String BREAK_PAGE_KEY = "breakPage";

    // FACE有2个版本face,face_1.0
    public enum ReportName {
        RTV("rtv/rtv", "rtv/rtvSub"), STT("stt", "sttSub"), CARTON("carton/gz_0.1", null), REPKT("rePkt", null), CDOCKALLOT("crossDockAllot", "crossDockAllotDetail"),
        PKTLABEL("pktLabel", null), MASTERABEL("masterLabel", null), SLC("slc", "slcDetail");

        private String name;
        private String subName;

        ReportName(String name, String subName) {
            this.name = name;
            this.subName = subName;
        }

        public String getName() {
            return buildPath(this.name);
        }

        public String getSubName() {
            return buildPath(this.subName);
        }

        /**
         * 路径
         *
         * @param name
         * @return
         */
        public String buildPath(String name) {
            String returnName = name;

            CfgConfiguration cfg = SystemConfig.getConfig("PRINT_JASPER_" + this.name(),
                    ParamUtil.getCurrentWarehouseId());
            if (cfg != null) {
                String configName = cfg.getValueString();
                if (StringUtil.isNotEmpty(configName)) {
                    returnName = name + configName;
                }
            }

            return returnName;
        }
    }


    @In
    private ReportGenerator reportGenerator;

    @In
    private WarehouseService warehouseService;

    @In
    private DeliveryOrderService deliveryOrderService;

    @In
    private PickTaskService pickTaskService;

    @In
    private GroupRuleService groupRuleService;

    @In(create = true)
    private PrintAllotService printAllotService;

    @In(create = true)
    private PrintDoService printDoService;

    @In
    private CfgWarehouseService cfgWarehouseService;

    @In
    private SkuCache skuCache;

    @In
    private LocationService locationService;

    @In
    private StockBatchAttService stockBatchAttService;

    @In
    private SupplierService supplierService;

    @In
    private CartonHeaderDAO cartonHeaderDAO;

    @In
    private CrossSeedService crossSeedService;
    @In
    private UserAccountDAO userAccountDAO;

    @In
    private InspectionReportService inspectionReportService;

    @In
    private AddressService addressService;

//    @In
//    private SkuGroupService skuGroupService;

    @In
    private Dictionary dictionary;
    @In
    private BusinessCenterComponent businessCenterComponent;

    /**
     * 获取发货单的手机号码或电话号码
     */
    @Override
    public String getPhone(DeliveryOrderHeader doHeader) {
        String phone = DoUtil.decryptPhone(doHeader.getTelephone());
        String mobile = DoUtil.decryptPhone(doHeader.getMobile());

        return (phone != null ? phone : "")
                + (mobile != null ? " " + mobile : "");
    }

    /**
     * 将SortGridInfo集合合并成string
     *
     * @param gridInfos 分拣格详情列表
     * @return 合并结果
     */
    private String toNoteString(List<SortGridInfoDTO> gridInfos) {
        if (CollectionUtils.isEmpty(gridInfos)) {
            return "";
        }

        List<String> strings = gridInfos.stream().map(it -> "S" + it.getSortGrid() + ":" + it.getQty() + " "
                + it.getTransportWendyDesc()).collect(Collectors.toList());
        return String.join("\r\n", strings);
    }

    /**
     * 打印拣货单
     *
     * @param pkts
     * @return
     */
    private List<PickPrint> getPickPrint(List<PickHeader> pkts, boolean isGroup) {
        List<PickPrint> pktPrints = new ArrayList<PickPrint>();

        Collections.sort(pkts, new Comparator<PickHeader>() {
            @Override
            public int compare(PickHeader o1, PickHeader o2) {
                String waveNo1 = o1.getWaveHeader().getWaveNo();
                String waveNo2 = o2.getWaveHeader().getWaveNo();
                if (waveNo1.equals(waveNo2)) {
                    return o1.getRegion().getRegionCode().compareTo(o2.getRegion().getRegionCode());
                } else {
                    return waveNo1.compareTo(waveNo2);
                }
            }
        });

        int pktSize = pkts.size();

        Map<String, String> waveTypeMap = Dictionary.getDictionary(Constants.WaveType.dictionaryCode());
        Map<String, List<SortGridInfoDTO>> skuSortGridInfoMap = new HashMap<>();
        Map<String, BigDecimal> skuQtyMap = new HashMap<>();
        for (int i = 0; i < pktSize; i++) {
            skuSortGridInfoMap.clear();
            skuQtyMap.clear();
            PickHeader pkt = pkts.get(i);
            WaveHeader waveHeader = pkt.getWaveHeader();

            PickPrint pktPrint = new PickPrint();
            String customerName = null;
            //一单一波次显示商户信息
            DeliveryOrderHeader header = waveHeader.getDoHeaders().get(0);
            if (waveHeader.getDoHeaders().size() == 1) {
                BusinessCustomer customer = waveHeader.getDoHeaders().get(0).getBusinessCustomer();
                if (null != customer) {
                    customerName = StringUtil.notNullString(customer.getCustomerName());
                }
                pktPrint.setDoNo(header.getDoNo());
                String address = header.getCountyName() + header.getAddress();
                pktPrint.setAddress(address);
                pktPrint.setBuyerRemark(header.getBuyerRemark());
                pktPrint.setPhone(header.getMobile());
            }
            Carrier carrier = header.getCarrier();
            if (carrier != null) {
                pktPrint.setCarrierShortName(carrier.getShortName());
            }
//            Map<String, String> channelMap = businessCenterComponent.getChannelMap();
//            if(Objects.nonNull(channelMap)&& Objects.nonNull(header.getChannelCode())) {
//                pktPrint.setChannelName(channelMap.get(header.getChannelCode()));
//            }
//            boolean[] wendySetted={false};
            String channelName = null;
            if(Config.getConfigValue("UB002.warehouseId", Config.ConfigLevel.GLOBAL).equals(ParamUtil.getCurrentWarehouseId().toString())){
                channelName="大";
                if(Config.getConfigValue("ASF.code", Config.ConfigLevel.GLOBAL).equals(header.getChannelCode())){
                    channelName="大SF";
                }
                if(Lists.newArrayList(Config.getConfigValue("public.channel.codes", Config.ConfigLevel.GLOBAL).split(",")).contains(header.getChannelCode())){
                    channelName="G";
                }
                pktPrint.setChannelName(channelName);
            }else if(Config.getConfigValue("UB001.warehouseId", Config.ConfigLevel.GLOBAL).equals(ParamUtil.getCurrentWarehouseId().toString())){
                if(Config.getConfigValue("ASF.code", Config.ConfigLevel.GLOBAL).equals(header.getChannelCode())){
                    channelName="SF";
                }
                pktPrint.setChannelName(channelName);
            }
            pktPrint.setCustomerName(customerName);
            pktPrint.setCreateTime(pkt.getCreatedAt());
            pktPrint.setRegionCode(pkt.getRegion().getRegionCode());
            pktPrint.setPktNo(pkt.getPktNo());
            pktPrint.setWaveNo(waveHeader.getWaveNo());
            pktPrint.setPktIndex(i + 1);
            pktPrint.setPktCount(pktSize);
            pktPrint.setWaveType(isGroup ? "团购波次" : waveTypeMap.get(waveHeader.getWaveType()));
            pktPrint.setDocType(isGroup ? "团购" : "杂品单");
            //设置波次体积类型
            List<Integer> volumeTypeList = waveHeader.getDoHeaders().stream().
                    map(DeliveryOrderHeader::getVolumeType).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(volumeTypeList)) {
                if (volumeTypeList.size() > 1) {
                    pktPrint.setVolumeType("混体积");
                } else {
                    pktPrint.setVolumeType(Dictionary.getDictionaryValue("DOC_VOLUME_TYPE",
                            volumeTypeList.get(0).toString()));
                }
            }


            List<PickTask> pickTasks = pkt.getPickTasks();

            List<Long> skuIds = pickTasks.stream().map(PickTask::getSkuId).collect(Collectors.toList());
//            List<SkuGroup> skuGroups = skuGroupService.listSkuGroupBySkuIds(skuIds);
//
//            Map<Long, SkuGroup> skuGroupMap = skuGroups.stream()
//                    .collect(Collectors.toMap(SkuGroup::getSkuId, Function.identity()));
//            DeliveryContext.setSkuGroup(skuGroupMap);

//            DeliveryContext.setSkuId(new HashMap<>());
            pickTasks.stream()
                    // 按照 分拣格排序
                    .sorted(Comparator.comparing(a -> a.getDoHeader().getSortGridNo().length() < 2 ?
                            "0" + a.getDoHeader().getSortGridNo() : a.getDoHeader().getSortGridNo()))
                    .forEach(task -> {
                        Long skuId = task.getSkuId();
//                        Boolean checkGroupRes = checkGroup(task.getSkuId(), task.getQty().intValue());
//                        if (checkGroupRes) {
//                            skuId = Long.MAX_VALUE - skuId;
//                        }


                        String key = skuId + "_" + task.getLocId()+ "_" + task.getStockBatchAtt().getLotatt14();
                        BigDecimal skuQty = skuQtyMap.computeIfAbsent(key, v -> BigDecimal.ZERO);
                        // 描述中增加 温度标识和分拣格标识
                        TransportWendyEnum transportWendy = addressService
                                .getTransportWendy(task.getDoHeader().getCountry(), task.getDoHeader().getProvinceName());
                        Integer addressTWCode = Optional.ofNullable(transportWendy)
                                .map(TransportWendyEnum::getCode).orElse(null);
                        Integer skuTWCode = task.getSku().getTransportWendy();

                        TransportWendyEnum transportWendyEnum = TransportWendyEnum.getBySkuAndAddress(addressTWCode, skuTWCode);

                        BigDecimal qty = task.getQty();
//                        if (checkGroupRes) {
//                            Integer groupQty = DeliveryContext.getSkuGroup().get(task.getSkuId()).getGroupQty();
//                            qty = BigDecimal.valueOf(task.getQty().intValue() / groupQty);
//                        }
                        // 累加同sku同库位的 数量
                        skuQty = skuQty.add(qty);
                        skuQtyMap.put(key, skuQty);

                        // 初始化+get 分拣格汇总信息
                        List<SortGridInfoDTO> sortGridInfoList = skuSortGridInfoMap.computeIfAbsent(key, v -> Lists.newArrayList());
                        SortGridInfoDTO sortGridInfo = new SortGridInfoDTO();
                        sortGridInfo.setSortGrid(task.getDoHeader().getSortGridNo());
                        // 英文环境不需要此标识
                        if (!AppConfig.useEnglish()){
                            String wendyDesc = transportWendyEnum.getDesc();
                            sortGridInfo.setTransportWendyDesc(wendyDesc);
                            // 整体增加温度标识
//                            if(!wendySetted[0] && TransportWendyEnum.NORMAL != transportWendyEnum){
//                                wendySetted[0] =true;
//                                pktPrint.setChannelName(pktPrint.getChannelName()+" "+wendyDesc);
//
//                            }

                        } else {
                            sortGridInfo.setTransportWendyDesc("");
                        }

                        // 同单同sku拆分成了多条拣货任务 此处为了 为合并此种记录做准备工作
                        int index = sortGridInfoList.indexOf(sortGridInfo);
                        if (index > -1) {
                            SortGridInfoDTO gridInfo = sortGridInfoList.get(index);
                            gridInfo.setQty(gridInfo.getQty().add(qty));
                        } else {
                            sortGridInfo.setQty(qty);
                            sortGridInfoList.add(sortGridInfo);
                        }
                    });
            //对拣货任务进行合并、排序、并调用拣货路径优化算法获取新的路径
            List<PickTaskDto> pickTaskDtos = pickTaskService.setPickTaskDtosGrouped(pickTasks);
            List<PickPrintSub> pktPrintSubs = new ArrayList<PickPrintSub>();
            Set<Long> doCountSet = new HashSet<Long>();
            for (PickTaskDto pickTask : pickTaskDtos) {
                //团购拣货不打印冻结的商品
                if (isGroup && deliveryOrderService.getHeaderById(pickTask.getDoHeaderId()).getReleaseStatus().equals(Constants.ReleaseStatus.HOLD.getValue())) {
                    continue;
                }

                SkuDTO sku;
//                Long skuId = DeliveryContext.getSkuId().get(pickTask.getSkuId());
//                if (skuId != null) {
//                    sku = skuCache.getSku(skuId);
//                    // 有预包 且 是非溯源码预包
//                    if ("0".equals(DeliveryContext.getSkuGroup().get(skuId).getTraceGoods())){
//                        sku.setEan13(DeliveryContext.getSkuGroup().get(skuId).getBarcode());
//                    }
//                } else {
                    sku = skuCache.getSku(pickTask.getSkuId());
//                }

                Location location = locationService.get(pickTask.getLocId());
                PickPrintSub pktPrintSub = new PickPrintSub();
                pktPrintSub.setLocationCode(location.getLocCode());
                pktPrintSub.setPickSeq(location.getPickSeq());
                pktPrintSub.setProductCode(sku.getProductCode());
                pktPrintSub.setSkuBarcode(sku.getEan13());
                pktPrintSub.setSpecification(sku.getSpecification());
                if (pktPrintSub.getSkuBarcode() != null) {
                    pktPrintSub.setSkuBarcode(pktPrintSub.getSkuBarcode().replaceAll(",", ", "));
                }
                pktPrintSub.setQty(pickTask.getQty());
                pktPrintSub.setSkuName(sku.getProductCname());
                pktPrintSub.setProductEname(sku.getProductEname());

                pktPrintSub.setSkuId(pickTask.getSkuId());
                pktPrintSub.setLocId(pickTask.getLocId());
                pktPrintSub.setLotId(pickTask.getLotId());
                pktPrintSub.setLpnNo(pickTask.getLpnNo());
                pktPrintSub.setToLocId(pickTask.getToLocId());
                pktPrintSub.setLocation(location);
                //若dto进行过拣货任务合并则，不设置批次信息
                boolean isGrouped = pickTask.getIsGrouped();
                if (!isGrouped) {
                    StockBatchAtt stockBatchAtt = stockBatchAttService.findStockBatchAttBylotId(pickTask.getLotId());
                    if (stockBatchAtt != null) {
                        pktPrintSub.setLotNo(stockBatchAtt.getLotatt05());
                        pktPrintSub.setLotatt01(stockBatchAtt.getLotatt01());
                        pktPrintSub.setLotatt02(stockBatchAtt.getLotatt02());
                    }
                }
                pktPrintSub.setLotatt14(Dictionary.getDictionaryValue("GOODS_GRADE_VIEW",pickTask.getLotatt14()) );
                pktPrintSub.setSortInfo(pickTask.getSortInfo());
                String key = pickTask.getSkuId() + "_" + pickTask.getLocId()+ "_" + pickTask.getLotatt14();
                if (!isGroup) {
                    pktPrintSub.setNotes(toNoteString(skuSortGridInfoMap.get(key)));
                }else{
                    pktPrintSub.setNotes(skuSortGridInfoMap.get(key).get(0).getTransportWendyDesc());
                }
                pktPrintSub.setQty(skuQtyMap.get(key));
                pktPrintSub.setCaseQty(sku.getPackingQty().intValue());
//                pktPrintSub.setCaseQty(16);
                pktPrintSubs.add(pktPrintSub);
            }
//            DeliveryContext.removeSkuId();
//            DeliveryContext.removeSkuGroup();
            // 团购去重
            if (isGroup) {
                List<PickPrintSub> resultList = pktPrintSubs.stream()
                        .collect(
                                Collectors.collectingAndThen(
                                        Collectors.toCollection(
                                                () -> new TreeSet<>(Comparator.comparing(
                                                        sub -> sub.getSkuBarcode() + sub.getLocationCode()+sub.getLotId()
                                                )
                                                )
                                        ), ArrayList::new
                                ));
                pktPrintSubs = resultList;
            }
            pktPrint.setDocCount(pkt.getWaveHeader().getDoHeaders().size());
            pktPrint.setDoNeedPrint(pkt.getWaveHeader().getDoNeedPrint());
            //计算团购拣货每单数量
            if (isGroup) {
                // 团购设置每单商品数量
                pktPrint.setPerDocSkuCount(pickTasks.get(0).getDoHeader().getExpectedQty().intValue());
                //团购拣货单需要显示团购明细
                Long groupRuleId = pkt.getPickTasks().get(0).getDoHeader().getDoWaveEx().getWaveCriteriaExId();
                String groupRuleDesc = "";
                if (groupRuleId != null && groupRuleId > 0) {
                    GroupRule groupRule = groupRuleService.selectById(groupRuleId);
                    if (groupRule != null && StringUtil.isNotEmpty(groupRule.getRuleValue())) {
                        String s = groupRule.getRuleValue();
                        for (String str : s.split(",")) {
                            SkuDTO sku = skuCache.getSku(Long.valueOf(str.split(":")[0]));
                            if (sku != null && Integer.valueOf(0).equals(sku.getProductType())) {
                                if (StringUtil.isNotEmpty(groupRuleDesc)) {
                                    groupRuleDesc += ",";
                                }
                                groupRuleDesc += sku.getProductCname();
                                groupRuleDesc += " * ";
                                groupRuleDesc += str.split(":")[1];
                            }
                        }
                    }
                }
                if (StringUtil.isNotEmpty(groupRuleDesc)) {
                    pktPrint.setGroupRuleDesc(groupRuleDesc);
                }
            }

            //按拣货顺序排序
            Collections.sort(pktPrintSubs, new Comparator<PickPrintSub>() {
                @Override
                public int compare(PickPrintSub o1, PickPrintSub o2) {
                    if (StringUtil.isEmpty(o1.getPickSeq())) {
                        return -1;
                    } else if (StringUtil.isEmpty(o2.getPickSeq())) {
                        return 1;
                    } else {
                        return o1.getPickSeq().compareTo(o2.getPickSeq());
                    }
                }
            });


            pktPrint.setPktPrintSubs(pktPrintSubs);
            pktPrints.add(pktPrint);
        }

        return pktPrints;
    }

    /**
     * 校验是否是组套商品
     *
     * @param skuId md_sku 主键id
     * @param qty   数量
     * @return 校验结果
     */
    private Boolean checkGroup(Long skuId, Integer qty) {
//        Map<Long, SkuGroup> skuGroupMap = Optional.ofNullable(DeliveryContext.getSkuGroup()).orElse(Maps.newHashMap());
//        SkuGroup skuGroup = skuGroupMap.get(skuId);
//        if (skuGroup != null) {
//            Integer groupQty = skuGroup.getGroupQty();
//            if (qty % groupQty == 0 && "0".equals(skuGroup.getTraceGoods())) {
//                return Boolean.TRUE;
//            }
//        }
        return Boolean.FALSE;
    }

    /**
     * 反拣单
     *
     * @param rePickTasks
     * @return
     */
    private List<RepickPrint> getRepickPrint(List<TrsTask> rePickTasks) {
        List<RepickPrint> repickPrints = new ArrayList<RepickPrint>();
        for (TrsTask trsTask : rePickTasks) {
            RepickPrint repick = new RepickPrint();
            repick.setBarCode(trsTask.getSku().getEan13());
            repick.setDoNo(trsTask.getDocNo());
            repick.setLocCode(trsTask.getPlanLocation().getLocCode());
            repick.setProductCode(trsTask.getSku().getProductCode());
            repick.setProductName(trsTask.getSku().getProductCname());
            repick.setQty(trsTask.getQty());

            repick.setSkuId(trsTask.getSkuId());
            repick.setToLocId(trsTask.getPlanLocId());
            repick.setFmLocId(trsTask.getFmLocId());
            repick.setToLpnNo(trsTask.getToLpnNo());
            repickPrints.add(repick);
        }

        ListUtil.megareList(repickPrints, new ListMegareOpr<RepickPrint>() {
            @Override
            public boolean isNeedMegare(RepickPrint t1, RepickPrint t2) {
                return CompareUtil.compare(t1.getSkuId(), t2.getSkuId())
                        && CompareUtil.compare(t1.getFmLocId(),
                        t2.getFmLocId())
                        && CompareUtil.compare(t1.getToLocId(),
                        t2.getToLocId());
            }

            @Override
            public void megareOpr(RepickPrint t1, RepickPrint t2) {
                t1.setQty(t1.getQty().add(t2.getQty()));
            }
        });

        Collections.sort(repickPrints, new Comparator<RepickPrint>() {
            @Override
            public int compare(RepickPrint o1, RepickPrint o2) {
                return o1.getLocCode().compareTo(o2.getLocCode());
            }
        });
        return repickPrints;
    }

    /**
     * 分拣单
     *
     * @param waves
     * @return
     */
    private List<SttPrint> getSttPrint(List<WaveHeader> waves) {
        List<SttPrint> sttPrints = new ArrayList<SttPrint>();

        for (WaveHeader wave : waves) {
            SttPrint sttPrint = new SttPrint();

            sttPrint.setWaveNo(wave.getWaveNo());
            List<SttPrintSub> sttPrintSubs = new ArrayList<SttPrintSub>();
            List<PickTask> pickTasks = wave.getPickTasks();
            for (PickTask pickTask : pickTasks) {
                SttPrintSub sttPrintSub = new SttPrintSub();
                sttPrintSub.setDoDetailId(pickTask.getDoDetailId());
                sttPrintSub.setQty(pickTask.getQty());
                sttPrintSub.setSkuBarCode(pickTask.getSku().getEan13());
                if (sttPrintSub.getSkuBarCode() != null) {
                    sttPrintSub.setSkuBarCode(sttPrintSub.getSkuBarCode().replaceAll(",", ", "));
                }
                sttPrintSub.setSkuCname(pickTask.getSku().getProductCname());
                sttPrintSub.setSkuEname(pickTask.getSku().getProductEname());
                sttPrintSub.setSkuCode(pickTask.getSku().getProductCode());
                sttPrintSub.setSortGridNo(pickTask.getDoHeader().getSortGridNo());

                Location location = pickTask.getLocation();
                sttPrintSub.setPickLocCode(location.getLocCode());
                sttPrintSub.setPickSeq(location.getPickSeq());
                sttPrintSub.setPartitionCode(location.getPartition().getPartitionCode());
                sttPrintSub.setDoNo(pickTask.getDoHeader().getDoNo());
                sttPrintSubs.add(sttPrintSub);
            }

            ListUtil.megareList(sttPrintSubs, new ListMegareOpr<SttPrintSub>() {
                @Override
                public boolean isNeedMegare(SttPrintSub t1, SttPrintSub t2) {
                    return CompareUtil.compare(t1.getSortGridNo(),
                            t2.getSortGridNo())
                            && CompareUtil.compare(t1.getSkuCode(),
                            t2.getSkuCode())
                            && CompareUtil.compare(t1.getSkuBarCode(),
                            t2.getSkuBarCode());
                }

                @Override
                public void megareOpr(SttPrintSub t1, SttPrintSub t2) {
                    t1.setQty(t1.getQty().add(t2.getQty()));
                }
            });

            Collections.sort(sttPrintSubs, new Comparator<SttPrintSub>() {

                @Override
                public int compare(SttPrintSub o1, SttPrintSub o2) {
                    //库区第一排序
                    int result = CompareUtil.compare(o1.getPartitionCode(), o2.getPartitionCode(), Boolean.TRUE);

                    //分拣格第二排序(注意分拣格是字符串，要想得到数字顺序的排序结果，需转型)
                    if (result == 0) {
                        return CompareUtil.compare(Integer.valueOf(o1.getSortGridNo()), Integer.valueOf(o2.getSortGridNo()), Boolean.TRUE);
                    }
                    return result;
                }
            });

            sttPrint.setSttPrintSubs(sttPrintSubs);
            sttPrints.add(sttPrint);
        }
        return sttPrints;
    }

    @Override
    public String printPickByPkt(List<PickHeader> pkts, boolean isGroup) {
        Map<Long, List<PickHeader>> pktMap = new HashMap<Long, List<PickHeader>>();
        Long waveId = null;
        for (PickHeader pkt : pkts) {
            waveId = pkt.getWaveHeadId();

            List<PickHeader> pktList = pktMap.computeIfAbsent(waveId, k -> new ArrayList<PickHeader>());
            pktList.add(pkt);
        }

        List<String> pages = new ArrayList<String>();
        List<List<PickPrint>> resultList = new ArrayList<List<PickPrint>>();


        List<List<PickHeader>> wavePktList = new ArrayList<List<PickHeader>>(pktMap.values());
        wavePktList.sort(Comparator.comparing(o -> o.get(0).getWaveHeader().getWaveNo()));

        for (List<PickHeader> pktHeaders : wavePktList) {
            resultList.add(this.getPickPrint(pktHeaders, isGroup));
        }

        Map<String, Object> originalUnitProps = new HashMap<String, Object>();
        originalUnitProps.put("data", JSON.toJSONString(resultList));

        return PrintTemplateUtil.process(PrintConstants.PrintTemplate.PICK.name(), originalUnitProps);
    }

    @Override
    public String genYaoToData(List<Long> doIds) {
        Map<String, String> orderByMap = new LinkedHashMap<String, String>(4);
        // 设置排序
        orderByMap.put("o.waveId", "desc");// 次关键字
        orderByMap.put("o.sortGridNo", "asc");// 主关键字

        DoHeaderFilter doHeaderFilter = new DoHeaderFilter();
        doHeaderFilter.setIds(doIds);
        doHeaderFilter.setOrderByMap(orderByMap);
        List<DeliveryOrderHeader> doHeaderList = deliveryOrderService.query(doHeaderFilter);

        Boolean isCheckStatus = Config.isDefaultTrue(Keys.Print.is_print_confrere_check_status, Config.ConfigLevel.WAREHOUSE);
        for (DeliveryOrderHeader header : doHeaderList) {
            if (isCheckStatus) {
                if (StringUtil.isNotIn(header.getStatus(), Constants.DoStatus.ALL_DELIVER.getValue(), Constants.DoStatus.LOAD_LOCKED.getValue(), Constants.DoStatus.PART_DELIVER.getValue())) {
                    throw new DeliveryException(DeliveryException.PRINT_DATA_STATUS_ERROR);
                }
            }

            if (!Constants.YesNo.YES.getValue().equals(header.getWaveFlag())) {
                throw new DeliveryException(DeliveryException.PRINT_DATA_NOT_WAVE_ERROR, header.getDoNo());
            }
        }
        List<ReportYaoDTO> reportDataList = getYaoToDataByDoHeaderList(doHeaderList);
        Map<String, Object> originalUnitProps = new HashMap<String, Object>();
        originalUnitProps.put("dtos", JSON.toJSONString(reportDataList));
        return PrintTemplateUtil.process(PrintConstants.PrintTemplate.CONFRERE.name(), originalUnitProps);
    }

    @Override
    public String printCrossByHeader(CrossSeedHeader crossSeedHeader) {
        List<PrintCrossDTO> dtos = new ArrayList<PrintCrossDTO>();
        List<CrossSeedDetail> details = crossSeedHeader.getCrossSeedDetails();
        for (CrossSeedDetail detail : details) {
            PrintCrossDTO dto = new PrintCrossDTO();
            dto.setDoNo(detail.getDoNo());
            dto.setSortGridNo(detail.getSortGridNo());
            dto.setContainerNo(detail.getContainerNo());
            dto.setConsigneeName(detail.getConsigneeName());
            dtos.add(dto);
        }
        Map<String, Object> originalUnitProps = new HashMap<String, Object>();
        originalUnitProps.put("data", JSON.toJSONString(dtos));
        return PrintTemplateUtil.process(PrintConstants.PrintTemplate.CROSS.name(), originalUnitProps);
    }

    @Override
    public String printCrossByDoList(List<DeliveryOrderHeader> doList) {
        List<PrintCrossDTO> dtos = new ArrayList<PrintCrossDTO>();
        for (DeliveryOrderHeader deliveryOrderHeader : doList) {
            CrossSeedHeader crossHeader = crossSeedService.getByContainerNo(deliveryOrderHeader.getUserDeffine6());
            PrintCrossDTO dto = new PrintCrossDTO();
            dto.setDoNo(deliveryOrderHeader.getDoNo());
            dto.setSortGridNo(deliveryOrderHeader.getSortGridNo());
            dto.setContainerNo(deliveryOrderHeader.getUserDeffine6());
            dto.setConsigneeName(deliveryOrderHeader.getConsigneeName());
            dto.setSeedNo(crossHeader.getSeedNo());
            dtos.add(dto);
        }
        Map<String, Object> originalUnitProps = new HashMap<String, Object>();
        originalUnitProps.put("data", JSON.toJSONString(dtos));
        return PrintTemplateUtil.process(PrintConstants.PrintTemplate.CROSS.name(), originalUnitProps);
    }

    @Override
    public String genPrintInspectionByWave(List<Long> ids) {
        List<Long> doIds = deliveryOrderService.findDoIdByWaveId(ids);
        DoHeaderFilter doHeaderFilter = new DoHeaderFilter();
        doHeaderFilter.setIds(doIds);
        List<DeliveryOrderHeader> doHeaderList = deliveryOrderService.query(doHeaderFilter);
        List<PrintReportDto> dtos = new ArrayList<PrintReportDto>();
        for (DeliveryOrderHeader doHeader : doHeaderList) {
            PickTaskFilter filter = new PickTaskFilter();
            filter.setDoHeaderId(doHeader.getId());
            List<PickTask> pickTasks = pickTaskService.query(filter);
            for (PickTask pickTask : pickTasks) {
                StockBatchAtt stockBatchAtt = pickTask.getStockBatchAtt();
                Sku sku = pickTask.getSku();
                List<InspectionReport> reportList = inspectionReportService.getBySkuAndBatch(sku.getId(), stockBatchAtt.getLotatt05());
                for (InspectionReport inspectionReport : reportList) {
                    if (null != inspectionReport) {
                        PrintReportDto dto = new PrintReportDto();
                        dto.setDocNo(inspectionReport.getFilePath());
                        dtos.add(dto);
                    }
                }
            }
        }

        Map<String, Object> originalUnitProps = new HashMap<String, Object>();
        originalUnitProps.put("dtos", JSON.toJSONString(dtos));
        // 更新打印标记
        deliveryOrderService.updatePrintFlag(doIds);
        return PrintTemplateUtil.process(PrintConstants.PrintTemplate.INST.name(), originalUnitProps);
    }

    @Override
    public String genPrintYaoToByWave(List<Long> ids) {
        List<Long> doIds = deliveryOrderService.findDoIdByWaveId(ids);
        return this.genYaoToData(doIds);
    }

    private List<ReportYaoDTO> getYaoToDataByDoHeaderList(List<DeliveryOrderHeader> doHeaderList) {
        if (doHeaderList.isEmpty()) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
//        for (DeliveryOrderHeader doHeader : doHeaderList) {
//            if (!DoType.ALLOT.getValue().equals(doHeader.getDoType()))
//                throw new DeliveryException(DeliveryException.DO_PRINT_TYPE_ERROR);
//        }
        Set<String> doTypeSet = Sets.newHashSet();
        for (DeliveryOrderHeader doHeader : doHeaderList) {
            doTypeSet.add(doHeader.getDoType());
        }
        if (doTypeSet.size() > 1) {
            throw new DeliveryException(DeliveryException.DO_PRINT_TYPE_ERROR);
        }

        preDetailDo(doHeaderList);
        List<ReportYaoDTO> dataList = new ArrayList<ReportYaoDTO>();
        List<PickTask> pickTasks = null;
        List<ReportYaoSubDTO> rtvSubDtoList = null;
        ReportYaoDTO rtvDto = null;
        for (DeliveryOrderHeader doHeader : doHeaderList) {
            rtvDto = new ReportYaoDTO();


            if (null == doHeader.getDoCreateTime()) {
                rtvDto.setDocTime(DateUtil.dateToString(doHeader.getCreatedAt()));
            } else {
                rtvDto.setDocTime(DateUtil.dateToString(doHeader.getDoCreateTime()));
            }
            rtvDto.setOrigId(doHeader.getOrigId());

            rtvSubDtoList = new ArrayList<ReportYaoSubDTO>();
            Warehouse targetWarehouse = null;
            if (StringUtil.isNotEmpty(doHeader.getEdi2())) {
                targetWarehouse = warehouseService.getWarehouse(Long.valueOf(doHeader.getEdi2()));
            }

            rtvDto.setDoNoCodeType(PrintHelper.decide128CodeType(doHeader.getDoNo()));
            rtvDto.setRefNo(doHeader.getRefNo1());
            rtvDto.setSalesPerson(doHeader.getUserDeffine1());

            rtvDto.setShipTime(DateUtil.dateToString(doHeader.getShipTime()));
            rtvDto.setTotalPackage(String.valueOf(cartonHeaderDAO.countByDoId(doHeader.getId())));//总箱数

            Merchant merchant = doHeader.getMerchant();
            if (null != merchant) {
                rtvDto.setMerchantCode(merchant.getMerchantCode());
                rtvDto.setMerchantName(merchant.getDescrC());
            }

            BusinessCustomer businessCustomer = doHeader.getBusinessCustomer();
            if (null != businessCustomer) {
                rtvDto.setCustomerCode(businessCustomer.getOriginalId());
                rtvDto.setCustomerName(businessCustomer.getCustomerName());
            }

            if (com.daxia.wms.exp.delivery.Constants.OrderType.RTV.getValue().equals(doHeader.getDoType())) {
                rtvDto.setHuayuanNotes("采购退货");
            } else {
                rtvDto.setHuayuanNotes(doHeader.getNotes());
            }

            rtvDto.setNotes(doHeader.getNotes());
            if (StringUtil.isNotEmpty(doHeader.getAddress()) && StringUtil.isNotEmpty(doHeader.getCityName()) && doHeader.getAddress().contains(doHeader.getCityName())) {
                rtvDto.setAddressHY(doHeader.getAddress());
            } else {
                rtvDto.setAddressHY(SFPrintCartonHelper.buildProvinceAndCityAndCountyAddress(doHeader, "") + doHeader.getAddress());
            }
            rtvDto.setConsigneeName(doHeader.getConsigneeName());
            rtvDto.setConsigneeMobile(DoUtil.decryptPhone(StringUtil.isEmpty(doHeader.getMobile()) ? doHeader.getTelephone() : doHeader.getMobile()));

            UserAccount userAccount = userAccountDAO.getUserAccount(WmsUtil.getOperateBy(), ParamUtil.getCurrentTenantId());
            if (null != userAccount) {
                rtvDto.setPrintFirstName(userAccount.getFirstName());
            }
            rtvDto.setRechecker(Config.getFmJson(Keys.Print.confrere_cfg, Config.ConfigLevel.WAREHOUSE, "rechecker"));
            BigDecimal orderAmount = BigDecimal.ZERO;
            BigDecimal totalQty = BigDecimal.ZERO;

            rtvDto.setDoType(doHeader.getDoType());
            rtvDto.setTitle(SystemConfig.getConfigValue("print.confrere.title", ParamUtil.getCurrentWarehouseId()));
            rtvDto.setSubDtoList(rtvSubDtoList);
            rtvDto.setDoNo(doHeader.getDoNo());
            String defaultPrinter = SystemConfig.getConfigValue(ConfigKeys.PRINT_YAO_TO_PRINTER, ParamUtil.getCurrentWarehouseId());
            rtvDto.setPrinter(StringUtil.isEmpty(defaultPrinter) ? WmsUtil.getOperateBy() : defaultPrinter);
            rtvDto.setSender(SystemConfig.getConfigValue(ConfigKeys.PRINT_YAO_TO_SENDER, ParamUtil.getCurrentWarehouseId()));
            rtvDto.setAddress(targetWarehouse == null ? rtvDto.getAddressHY() : targetWarehouse.getAddressName());
            rtvDto.setTargetWarehouseName(targetWarehouse == null ? "" : targetWarehouse.getWarehouseName());
            rtvDto.setPrintDate(doHeader.getShipTime() != null ? DateUtil.dateToString(doHeader.getShipTime(), "yyyy-MM-dd") : DateUtil.dateToString(new Date(), "yyyy-MM-dd"));
            WaveHeader waveHeader = doHeader.getWaveHeader();
            rtvDto.setWaveNo(waveHeader == null ? null : waveHeader.getWaveNo());

            String notes = doHeader.getNotes();

            PickTaskFilter filter = new PickTaskFilter();
            filter.setDoHeaderId(doHeader.getId());
            pickTasks = pickTaskService.query(filter);
            Map<String, String> stringMap = Dictionary.getDictionary("STORAGE_CONDITION");
            if (ListUtil.isNullOrEmpty(pickTasks)) {
                List<DeliveryOrderDetail> detailList = doHeader.getDoDetails();
                for (DeliveryOrderDetail detail : detailList) {
                    ReportYaoSubDTO dto = new ReportYaoSubDTO();

                    Sku sku = detail.getSku();
                    if (sku.getEan13() != null) {
                        dto.setBarCode(sku.getEan13().replaceAll(",", ", "));
                    }
                    dto.setLotatt01(detail.getLotatt01());
                    dto.setLotatt02(detail.getLotatt02());
                    dto.setLotatt05(detail.getLotatt05());
                    dto.setLotatt09(detail.getLotatt09());
                    if (Constants.YesNo.YES.getValue().equals(detail.getIsDamaged())) {
                        dto.setLotatt12("1");
                    }
                    Manufacturer manufacturer = detail.getManufacturer();
                    if (null != manufacturer) {
                        dto.setManufacturer(manufacturer.getDescrC());
                    } else {
                        dto.setManufacturer(sku.getManufacturer() == null ? sku.getManufacturerName() : sku.getManufacturer().getDescrC());
                    }
                    dto.setProductCode(sku.getProductCode());
                    dto.setProductName(sku.getProductCname());
                    dto.setQty(detail.getExpectedQty());
                    dto.setUom(sku.getUdf6());
                    dto.setNotes(notes);
                    dto.setLotId(null);
                    dto.setActualQty(detail.getAllocatedQty());
                    dto.setRegisterNo(sku.getRegisterNo());
                    dto.setDoSage(sku.getDoSage());
                    dto.setSpecification(sku.getSpecification());
                    dto.setStorageCondition(stringMap.get(sku.getStorageCondition()) == null ? sku.getStorageCondition() : stringMap.get(sku.getStorageCondition()));
                    dto.setRegistrationCertificateNo(sku.getRegistrationCertificateNo());
                    if (StringUtil.isNotEmpty(dto.getLotatt09()) && dto.getQty() != null) {
                        BigDecimal money = dto.getQty().multiply(new BigDecimal(dto.getLotatt09()));
                        dto.setTotalMoney(money);
                    }

                    BigDecimal packQty = NumberUtils.object2BigDecimal(sku.getUdf4());
                    dto.setQtyPackage(packQty);
                    // 产地/许可证号
                    dto.setProductionLicense(StrUtils.object2String(sku.getUdf7(), "") + " / " + StrUtils.object2String(sku.getProductionLicense(), ""));
                    BigDecimal[] results = dto.getActualQty().divideAndRemainder(dto.getQtyPackage());
                    dto.setcQty(results[0]);
                    dto.setbQty(results[1]);
                    dto.setSellPrice(detail.getPrice());
                    if (dto.getSellPrice() != null && dto.getActualQty() != null) {
                        BigDecimal money = dto.getSellPrice().multiply(dto.getActualQty());
                        dto.setTotalMoneyHY(money);
                    } else {
                        dto.setTotalMoneyHY(BigDecimal.ZERO);
                    }
                    orderAmount = orderAmount.add(dto.getTotalMoneyHY());
                    totalQty = totalQty.add(dto.getActualQty());

                    rtvSubDtoList.add(dto);
                }
            } else {
                for (PickTask pickTask : pickTasks) {
                    ReportYaoSubDTO dto = new ReportYaoSubDTO();

                    StockBatchAtt stockBatchAtt = pickTask.getStockBatchAtt();
                    Sku sku = pickTask.getSku();
                    if (sku.getEan13() != null) {
                        dto.setBarCode(sku.getEan13().replaceAll(",", ", "));
                    }
                    dto.setLotatt01(stockBatchAtt.getLotatt01());
                    dto.setLotatt02(stockBatchAtt.getLotatt02());
                    dto.setLotatt05(stockBatchAtt.getLotatt05());
                    dto.setLotatt09(stockBatchAtt.getLotatt09());
                    dto.setLotatt12(stockBatchAtt.getLotatt12());
                    Manufacturer manufacturer = stockBatchAtt.getManufacturer();
                    if (null != manufacturer) {
                        dto.setManufacturer(manufacturer.getDescrC());
                    } else {
                        dto.setManufacturer(sku.getManufacturer() == null ? sku.getManufacturerName() : sku.getManufacturer().getDescrC());
                    }
                    dto.setProductCode(sku.getProductCode());
                    dto.setProductName(sku.getProductCname());
                    dto.setQty(pickTask.getQty());
                    dto.setUom(sku.getUdf6());
                    dto.setNotes(notes);
                    dto.setLotId(stockBatchAtt.getId());
                    dto.setActualQty(pickTask.getQty());
                    dto.setRegisterNo(sku.getRegisterNo());
                    dto.setDoSage(sku.getDoSage());
                    dto.setSpecification(sku.getSpecification());
                    dto.setStorageCondition(stringMap.get(sku.getStorageCondition()) == null ? sku.getStorageCondition() : stringMap.get(sku.getStorageCondition()));
                    dto.setRegistrationCertificateNo(sku.getRegistrationCertificateNo());
                    if (StringUtil.isNotEmpty(dto.getLotatt09()) && dto.getQty() != null) {
                        BigDecimal money = dto.getQty().multiply(new BigDecimal(dto.getLotatt09()));
                        dto.setTotalMoney(money);
                    } else {
                        dto.setTotalMoney(BigDecimal.ZERO);
                    }

                    //包装数量
                    BigDecimal packQty = NumberUtils.object2BigDecimal(sku.getUdf4());
                    dto.setQtyPackage(packQty);
                    // 产地/许可证号
                    dto.setProductionLicense(StrUtils.object2String(sku.getUdf7(), "") + " / " + StrUtils.object2String(sku.getProductionLicense(), ""));
                    dto.setActualQtyHY(pickTask.getPickedQty());

                    BigDecimal[] results = dto.getActualQtyHY().divideAndRemainder(dto.getQtyPackage());
                    dto.setcQty(results[0]);
                    dto.setbQty(results[1]);

                    dto.setSellPrice(pickTask.getDoDetail().getPrice());
                    if (dto.getSellPrice() != null && dto.getActualQtyHY() != null) {
                        BigDecimal money = dto.getSellPrice().multiply(dto.getActualQtyHY());
                        dto.setTotalMoneyHY(money);
                    } else {
                        dto.setTotalMoneyHY(BigDecimal.ZERO);
                    }
                    orderAmount = orderAmount.add(dto.getTotalMoneyHY());
                    totalQty = totalQty.add(dto.getActualQtyHY());

                    rtvSubDtoList.add(dto);
                }
                rtvDto.setOrderAmount(StrUtils.object2String(orderAmount));
                rtvDto.setTotalQty(StrUtils.object2String(totalQty));

                ListUtil.megareList(rtvSubDtoList, new ListMegareOpr<ReportYaoSubDTO>() {
                    @Override
                    public boolean isNeedMegare(ReportYaoSubDTO t1, ReportYaoSubDTO t2) {
                        if (CompareUtil.compare(t1.getLotId(), t2.getLotId())
                                && CompareUtil.compare(t1.getUom(), t2.getUom())) {
                            return true;
                        }
                        return false;
                    }

                    @Override
                    public void megareOpr(ReportYaoSubDTO t1, ReportYaoSubDTO t2) {
                        t1.setQty(t1.getQty().add(t2.getQty()));
                        t1.setActualQty(t1.getActualQty().add(t2.getActualQty()));
                        t1.setTotalMoney(t1.getTotalMoney().add(t2.getTotalMoney()));

                        t1.setActualQtyHY(t1.getActualQtyHY().add(t2.getActualQtyHY()));
                        t1.setTotalMoneyHY(t1.getTotalMoneyHY().add(t2.getTotalMoneyHY()));
                        t1.setbQty(t1.getbQty().add(t2.getbQty()));
                        t1.setcQty(t1.getcQty().add(t2.getcQty()));
                    }
                });
            }
            dataList.add(rtvDto);
        }
        return dataList;
    }

    @Override
    public List<String> getSttReport(List<WaveHeader> waves) {
        List<SttPrint> sttPrints = getSttPrint(waves);

        Map<String, Object> parameters = new HashMap<String, Object>();
        return reportGenerator.builtPrintData(ReportName.STT.getName(),
                ReportName.STT.getSubName(), parameters, sttPrints);
    }

    /**
     * 反拣单
     */
    @Override
    public List<String> getRepickReport(ReversePickHeader rePickHeader,
                                        List<TrsTask> rePickTasks) {
        List<RepickPrint> repickPrints = this.getRepickPrint(rePickTasks);
        return reportGenerator.builtPrintDataNoSub(ReportName.REPKT.getName(),
                getRepickParameters(rePickHeader), repickPrints);
    }

    /**
     * 反拣单参数
     *
     * @param rePickHeader
     * @return
     */
    private Map<String, Object> getRepickParameters(
            ReversePickHeader rePickHeader) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        if (rePickHeader != null) {
            paramMap.put("rePickHeaderNo", rePickHeader.getRpNo());
        }

        return paramMap;
    }

    /**
     * 根据发货单头获取发货单报表
     */
    @Override
    public List<String> getDoReportByDoHeaderIds(List<Long> doHeaderIds) {
        if (ListUtil.isNullOrEmpty(doHeaderIds)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }

        Map<String, String> orderByMap = new LinkedHashMap<String, String>(4);
        // 设置排序
        orderByMap.put("o.waveId", "desc");// 次关键字
        orderByMap.put("o.sortGridNo", "asc");// 主关键字

        DoHeaderFilter doHeaderFilter = new DoHeaderFilter();
        doHeaderFilter.setIds(doHeaderIds);
        doHeaderFilter.setOrderByMap(orderByMap);
        List<DeliveryOrderHeader> doHeaderList = deliveryOrderService.query(doHeaderFilter);

        return getDoReportByDoHeaderList(doHeaderList);
    }

    /**
     * 根据发货单头ids 获取RTV报表
     */
    @Override
    public List<String> getRTVReportByDoHeaderIds(List<Long> doHeaderIds) {
        Map<String, String> orderByMap = new LinkedHashMap<String, String>(4);
        // 设置排序
        orderByMap.put("o.waveId", "desc");// 次关键字
        orderByMap.put("o.sortGridNo", "asc");// 主关键字

        DoHeaderFilter doHeaderFilter = new DoHeaderFilter();
        doHeaderFilter.setIds(doHeaderIds);
        doHeaderFilter.setOrderByMap(orderByMap);
        List<DeliveryOrderHeader> doHeaderList = deliveryOrderService
                .query(doHeaderFilter);

        return getRTVReportByDoHeaderList(doHeaderList);
    }

    @Override
    public List<String> getYaoRtvReportByDoHeaderIds(List<Long> doHeaderIds) {
        Map<String, String> orderByMap = new LinkedHashMap<String, String>(4);
        // 设置排序
        orderByMap.put("o.waveId", "desc");// 次关键字
        orderByMap.put("o.sortGridNo", "asc");// 主关键字

        DoHeaderFilter doHeaderFilter = new DoHeaderFilter();
        doHeaderFilter.setIds(doHeaderIds);
        doHeaderFilter.setOrderByMap(orderByMap);
        List<DeliveryOrderHeader> doHeaderList = deliveryOrderService.query(doHeaderFilter);

        return getYaoRtvReportByDoHeaderList(doHeaderList);
    }

    public List<String> getYaoRtvReportByDoHeaderList(List<DeliveryOrderHeader> doHeaderList) {
        if (doHeaderList.isEmpty()) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }

        for (DeliveryOrderHeader doHeader : doHeaderList) {
            if (!DoType.RTV.getValue().equals(doHeader.getDoType())) {
                throw new DeliveryException(DeliveryException.DO_PRINT_TYPE_ERROR);
            }
        }

        preDetailDo(doHeaderList);

        List<String> printData = new ArrayList<String>();
        List<ReportYaoDTO> dataList = new ArrayList<ReportYaoDTO>();
        List<PickTask> pickTasks = null;
        List<ReportYaoSubDTO> rtvSubDtoList = null;
        ReportYaoDTO rtvDto = null;
        BigDecimal orderAmount = BigDecimal.ZERO;
        for (DeliveryOrderHeader doHeader : doHeaderList) {
            rtvSubDtoList = new ArrayList<ReportYaoSubDTO>();
            rtvDto = new ReportYaoDTO();
            Supplier supplier = supplierService.getSupplier(doHeader.getSupplierId());
            String rtvTitle = SystemConfig.getConfigValue("rtv.print.title", doHeader.getWarehouseId());
            if (StringUtil.isEmpty(rtvTitle)) {
                rtvTitle = supplier.getSupplierCompanyName();
            }
            rtvDto.setDoType(doHeader.getDoType());
            rtvDto.setTitle(rtvTitle + " 购进退货单");
            rtvDto.setSubDtoList(rtvSubDtoList);
            rtvDto.setDoNo(doHeader.getDoNo());
            rtvDto.setPrinter(WmsUtil.getOperateBy());
            rtvDto.setPrintDate(DateUtil.dateToString(new Date(), "yyyy-MM-dd"));
            rtvDto.setSupplier(supplier.getSupplierCompanyName());

            WaveHeader waveHeader = doHeader.getWaveHeader();
            rtvDto.setWaveNo(waveHeader == null ? null : waveHeader.getWaveNo());

            String notes = doHeader.getNotes();

            PickTaskFilter filter = new PickTaskFilter();
            filter.setDoHeaderId(doHeader.getId());
            pickTasks = pickTaskService.query(filter);
            if (ListUtil.isNullOrEmpty(pickTasks)) {
                List<DeliveryOrderDetail> detailList = doHeader.getDoDetails();
                for (DeliveryOrderDetail detail : detailList) {
                    ReportYaoSubDTO dto = new ReportYaoSubDTO();

                    Sku sku = detail.getSku();
                    if (sku.getEan13() != null) {
                        dto.setBarCode(sku.getEan13().replaceAll(",", ", "));
                    }
                    dto.setLotatt01(detail.getLotatt01());
                    dto.setLotatt02(detail.getLotatt02());
                    dto.setLotatt05(detail.getLotatt05());
                    dto.setLotatt09(detail.getLotatt09());
                    Manufacturer manufacturer = detail.getManufacturer();
                    dto.setManufacturer(manufacturer == null ? null : manufacturer.getDescrC());
                    dto.setProductCode(sku.getProductCode());
                    dto.setProductName(sku.getProductCname());
                    dto.setQty(detail.getExpectedQty());
                    dto.setUom(sku.getUdf6());
                    dto.setNotes(notes);
                    dto.setLotId(null);
                    dto.setActualQty(detail.getAllocatedQty());
                    dto.setRegisterNo(sku.getRegisterNo());
                    dto.setDoSage(sku.getDoSage());
                    dto.setSpecification(sku.getSpecification());

                    if (StringUtil.isNotEmpty(dto.getLotatt09())) {
                        orderAmount = orderAmount.add(new BigDecimal(dto.getLotatt09()).multiply(dto.getActualQty()));
                    }

                    rtvSubDtoList.add(dto);
                }
            } else {
                for (PickTask pickTask : pickTasks) {
                    ReportYaoSubDTO dto = new ReportYaoSubDTO();

                    StockBatchAtt stockBatchAtt = pickTask.getStockBatchAtt();
                    Sku sku = pickTask.getSku();
                    if (sku.getEan13() != null) {
                        dto.setBarCode(sku.getEan13().replaceAll(",", ", "));
                    }
                    dto.setLotatt01(stockBatchAtt.getLotatt01());
                    dto.setLotatt02(stockBatchAtt.getLotatt02());
                    dto.setLotatt05(stockBatchAtt.getLotatt05());
                    dto.setLotatt09(stockBatchAtt.getLotatt09());
                    Manufacturer manufacturer = stockBatchAtt.getManufacturer();
                    dto.setManufacturer(manufacturer == null ? null : manufacturer.getDescrC());
                    dto.setProductCode(sku.getProductCode());
                    dto.setProductName(sku.getProductCname());
                    dto.setQty(pickTask.getQty());
                    dto.setUom(sku.getUdf6());
                    dto.setNotes(notes);
                    dto.setSpecification(sku.getSpecification());
                    dto.setLotId(stockBatchAtt.getId());
                    dto.setActualQty(pickTask.getQty());
                    dto.setRegisterNo(sku.getRegisterNo());
                    dto.setDoSage(sku.getDoSage());

                    if (StringUtil.isNotEmpty(dto.getLotatt09())) {
                        orderAmount = orderAmount.add(new BigDecimal(dto.getLotatt09()).multiply(dto.getActualQty()));
                    }

                    rtvSubDtoList.add(dto);
                }

                ListUtil.megareList(rtvSubDtoList, new ListMegareOpr<ReportYaoSubDTO>() {
                    @Override
                    public boolean isNeedMegare(ReportYaoSubDTO t1, ReportYaoSubDTO t2) {
                        if (CompareUtil.compare(t1.getLotId(), t2.getLotId())
                                && CompareUtil.compare(t1.getUom(), t2.getUom())) {
                            return true;
                        }
                        return false;
                    }

                    @Override
                    public void megareOpr(ReportYaoSubDTO t1, ReportYaoSubDTO t2) {
                        t1.setQty(t1.getQty().add(t2.getQty()));
                        t1.setActualQty(t1.getActualQty().add(t2.getActualQty()));
                    }
                });
            }

            rtvDto.setOrderAmount(orderAmount.setScale(2, BigDecimal.ROUND_UP).toString());
            rtvDto.setOrderAmountRMB(StringUtil.numToRMBStr(orderAmount.setScale(2, BigDecimal.ROUND_UP).doubleValue()));

            dataList.add(rtvDto);

            printData.addAll(reportGenerator.builtPrintData("yao/yaoRtv", "yao/yaoRtvSub", this.getRtvParams(), dataList));
        }
        return printData;
    }

    @Override
    public List<String> getYaoToReportByDoHeaderIds(List<Long> doHeaderIds) {
        Map<String, String> orderByMap = new LinkedHashMap<String, String>(4);
        // 设置排序
        orderByMap.put("o.waveId", "desc");// 次关键字
        orderByMap.put("o.sortGridNo", "asc");// 主关键字

        DoHeaderFilter doHeaderFilter = new DoHeaderFilter();
        doHeaderFilter.setIds(doHeaderIds);
        doHeaderFilter.setOrderByMap(orderByMap);
        List<DeliveryOrderHeader> doHeaderList = deliveryOrderService.query(doHeaderFilter);

        return getYaoToReportByDoHeaderList(doHeaderList);
    }

    /***
     * 是否使用A5纸打印拣货单
     *
     * @return
     */
    @Override
    public Integer isPrintPickTaskByA5() {
        Integer isPrintPickTaskByA5 = SystemConfig.getConfigValueInt("print.pickTask.useA5", ParamUtil
                .getCurrentWarehouseId());
        if (isPrintPickTaskByA5 != null && Constants.YesNo.YES.getValue().equals(isPrintPickTaskByA5)) {
            return Constants.YesNo.YES.getValue();
        }
        return Constants.YesNo.NO.getValue();
    }

    public List<String> getYaoToReportByDoHeaderList(List<DeliveryOrderHeader> doHeaderList) {
        List<ReportYaoDTO> dtos = getYaoToDataByDoHeaderList(doHeaderList);
        List<String> printData = new ArrayList<String>();
        List<ReportYaoDTO> dataList = null;
        for (ReportYaoDTO rtvDto : dtos) {
            dataList = new ArrayList<ReportYaoDTO>();
            dataList.add(rtvDto);
            String tenantSuffix = Config.get(Keys.Print.do_yaoToSuffix, Config.ConfigLevel.TENANT);
            tenantSuffix = StringUtil.isNotEmpty(tenantSuffix) ? tenantSuffix : "";
            printData.addAll(reportGenerator.builtPrintData("yao/yaoTo" + tenantSuffix, "yao/yaoToSub" + tenantSuffix, this.getRtvParams(), dataList));
        }
        return printData;
    }

    /**
     * 根据发货单头实体获取RTV报表
     *
     * @param doHeaderList
     * @return
     */
    public List<String> getRTVReportByDoHeaderList(
            List<DeliveryOrderHeader> doHeaderList) {
        if (doHeaderList.isEmpty()) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }

        for (DeliveryOrderHeader doHeader : doHeaderList) {
            if (!DoType.RTV.getValue().equals(
                    doHeader.getDoType())) {
                throw new DeliveryException(
                        DeliveryException.DO_PRINT_TYPE_ERROR);
            }
        }

        preDetailDo(doHeaderList);

        List<String> printData = new ArrayList<String>();
        List<ReportRtvDto> dataList = null;
        List<PickTask> pickTasks = null;
        List<ReportRtvSubDto> rtvSubDtoList = null;
        ReportRtvDto rtvDto = null;
        for (DeliveryOrderHeader doHeader : doHeaderList) {
            dataList = new ArrayList<ReportRtvDto>();
            rtvSubDtoList = new ArrayList<ReportRtvSubDto>();

            rtvDto = new ReportRtvDto();
            rtvDto.setRtvSubDtoList(rtvSubDtoList);
            rtvDto.setDoNo(doHeader.getDoNo());
            rtvDto.setPoNo(doHeader.getRefNo1());
            rtvDto.setConsigneeName(doHeader.getConsigneeName());

            WaveHeader waveHeader = doHeader.getWaveHeader();
            rtvDto.setWaveNo(waveHeader == null ? null : waveHeader.getWaveNo());

            String notes = doHeader.getNotes();

            PickTaskFilter filter = new PickTaskFilter();
            filter.setDoHeaderId(doHeader.getId());
            pickTasks = pickTaskService.query(filter);
            if (ListUtil.isNullOrEmpty(pickTasks)) {
                List<DeliveryOrderDetail> detailList = doHeader.getDoDetails();
                for (DeliveryOrderDetail detail : detailList) {
                    ReportRtvSubDto dto = new ReportRtvSubDto();

                    Sku sku = detail.getSku();
                    if (sku.getEan13() != null) {
                        dto.setBarCode(sku.getEan13().replaceAll(",", ", "));
                    }
                    dto.setLotatt01(detail.getLotatt01());
                    dto.setLotatt02(detail.getLotatt02());
                    dto.setLotatt05(detail.getLotatt05());
                    dto.setLotatt09(detail.getLotatt09());
                    Manufacturer manufacturer = detail.getManufacturer();
                    dto.setManufacturer(manufacturer == null ? null : manufacturer.getDescrC());
                    dto.setProductCode(sku.getProductCode());
                    dto.setProductName(sku.getProductCname());
                    dto.setQty(detail.getExpectedQty());
                    dto.setUom(detail.getUom());
                    dto.setNotes(notes);
                    dto.setLotId(null);
                    dto.setActualQty(detail.getAllocatedQty());

                    rtvSubDtoList.add(dto);
                }
            } else {
                for (PickTask pickTask : pickTasks) {
                    ReportRtvSubDto dto = new ReportRtvSubDto();

                    StockBatchAtt stockBatchAtt = pickTask.getStockBatchAtt();
                    Sku sku = pickTask.getSku();
                    if (sku.getEan13() != null) {
                        dto.setBarCode(sku.getEan13().replaceAll(",", ", "));
                    }
                    dto.setLotatt01(stockBatchAtt.getLotatt01());
                    dto.setLotatt02(stockBatchAtt.getLotatt02());
                    dto.setLotatt05(stockBatchAtt.getLotatt05());
                    dto.setLotatt09(stockBatchAtt.getLotatt09());
                    Manufacturer manufacturer = stockBatchAtt.getManufacturer();
                    dto.setManufacturer(manufacturer == null ? null : manufacturer.getDescrC());
                    dto.setProductCode(sku.getProductCode());
                    dto.setProductName(sku.getProductCname());
                    dto.setQty(pickTask.getQty());
                    dto.setUom(BatchPropertyUtil.getUom(sku));
                    dto.setNotes(notes);
                    dto.setLotId(stockBatchAtt.getId());
                    dto.setActualQty(pickTask.getQty());

                    rtvSubDtoList.add(dto);
                }

                ListUtil.megareList(rtvSubDtoList, new ListMegareOpr<ReportRtvSubDto>() {
                    @Override
                    public boolean isNeedMegare(ReportRtvSubDto t1, ReportRtvSubDto t2) {
                        if (CompareUtil.compare(t1.getLotId(), t2.getLotId())
                                && CompareUtil.compare(t1.getUom(), t1.getUom())) {
                            return true;
                        }
                        return false;
                    }

                    @Override
                    public void megareOpr(ReportRtvSubDto t1, ReportRtvSubDto t2) {
                        t1.setQty(t1.getQty().add(t2.getQty()));
                    }
                });
            }

            dataList.add(rtvDto);

            printData.addAll(reportGenerator.builtPrintData("rtv/rtv_111", "rtv/rtvSub_111", this.getRtvParams(),
                    dataList));
        }
        return printData;
    }

    /**
     * 根据波次获取发货单
     */
    @Override
    public List<String> getDoReportByWaveIds(List<Long> waveIds) {
        Map<String, String> orderByMap = new LinkedHashMap<String, String>(4);
        // 设置排序
        orderByMap.put("o.sortGridNo", "asc");// 主关键字
        orderByMap.put("o.waveId", "asc");// 次关键字

        DoHeaderFilter doHeaderFilter = new DoHeaderFilter();
        doHeaderFilter.setWaveIds(waveIds);
        doHeaderFilter.setOrderByMap(orderByMap);
        List<DeliveryOrderHeader> doHeaderList = deliveryOrderService
                .query(doHeaderFilter);

        return getDoReportByDoHeaderList(doHeaderList);
    }

    /**
     * 根据波次获取TRV报表
     */
    @Override
    public List<String> getRtvReportByWaveIds(List<Long> waveIds) {
        Map<String, String> orderByMap = new LinkedHashMap<String, String>(4);
        // 设置排序
        orderByMap.put("o.sortGridNo", "asc");// 主关键字
        orderByMap.put("o.waveId", "asc");// 次关键字

        DoHeaderFilter doHeaderFilter = new DoHeaderFilter();
        doHeaderFilter.setWaveIds(waveIds);
        doHeaderFilter.setOrderByMap(orderByMap);
        List<DeliveryOrderHeader> doHeaderList = deliveryOrderService
                .query(doHeaderFilter);

        return getRTVReportByDoHeaderList(doHeaderList);
    }

    /**
     * 根据发货单头id集 获取发货单报表
     *
     * @param doHeaderList
     * @return
     */
    public List<String> getDoReportByDoHeaderList(
            List<DeliveryOrderHeader> doHeaderList) {
        if (doHeaderList.isEmpty()) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }

        List<Long> doIds = Lists.newArrayList();
        String doType = doHeaderList.get(0).getDoType();
        for (DeliveryOrderHeader doHeader : doHeaderList) {
            if (!doType.equals(doHeader.getDoType())) {
                throw new DeliveryException(DeliveryException.DO_PRINT_TYPE_ERROR);
            }
            doIds.add(doHeader.getId());
        }

        if (DoType.SELL.getValue().equals(doType)) {
            return Lists.newArrayList(printDoService.genData(doIds));
        } else {
            preDetailDo(doHeaderList);

            if (DoType.RTV.getValue().equals(doType)) {
                List<String> printData = new ArrayList<String>();
                List<DeliveryOrderHeader> dataList = null;
                for (DeliveryOrderHeader doHeader : doHeaderList) {
                    dataList = new ArrayList<DeliveryOrderHeader>();
                    dataList.add(doHeader);

                    printData.addAll(reportGenerator.builtPrintData(ReportName.RTV.getName(), ReportName.RTV.getSubName(), this.getRtvParams(), dataList));
                }
                return printData;
            } else if (DoType.ALLOT.getValue().equals(doType)) {
                return printAllotService.printDoList(doHeaderList, doType);
            } else {
                throw new DeliveryException(DeliveryException.DO_PRINT_TYPE_ERROR);
            }
        }
    }

    /**
     * 导出分拣单
     */
    @Override
    public byte[] exportSttReport(List<WaveHeader> waves) {
        List<SttPrint> sttPrints = getSttPrint(waves);

        Map<String, Object> parameters = new HashMap<String, Object>();
        return reportGenerator.generatePDF(ReportName.STT.getName(),
                ReportName.STT.getSubName(), parameters, sttPrints);
    }

    /**
     * 根据发货单头实体集导出发货单
     */
    @Override
    public byte[] exportDoReportByHeaderList(List<DeliveryOrderHeader> doHeaderList) {
        String doType = null;
        for (DeliveryOrderHeader doHeader : doHeaderList) {
            if (doType == null) {
                doType = doHeader.getDoType();
            } else if (!doType.equals(doHeader.getDoType())) {
                throw new DeliveryException(DeliveryException.DO_PRINT_TYPE_ERROR);
            }
        }

        preDetailDo(doHeaderList);

        if (DoType.RTV.getValue().equals(doType)) {
            return reportGenerator.generatePDF(ReportName.RTV.getName(), ReportName.RTV.getSubName(), getRtvParams(), doHeaderList);
        } else if (DoType.ALLOT.getValue().equals(doType)) {
            return printAllotService.generatePDFByDoList(doHeaderList, doType);
        } else {
            throw new DeliveryException(DeliveryException.DO_PRINT_TYPE_ERROR);
        }
    }

    /**
     * 预览do明细
     */
    @Override
    public void preDetailDo(List<DeliveryOrderHeader> doHeaderList) {
        for (DeliveryOrderHeader doHeader : doHeaderList) {
            List<DeliveryOrderDetail> details = doHeader.getDoDetails();
            Iterator<DeliveryOrderDetail> it = details.iterator();
            while (it.hasNext()) {
                DeliveryOrderDetail detail = it.next();
                if (detail.getParentId() != null) {
                    it.remove();
                }
            }
        }
    }

    /**
     * 设置拣货单参数：打印是否强制分页
     *
     * @return
     */
    private Map<String, Object> getPickParams() {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put(ReportGenerator.BREAK_PAGE_KEY,
                "1".equals(SystemConfig.getConfigValue("print.break", ParamUtil.getCurrentWarehouseId())));
        return params;
    }

    /**
     * 设置调拨的参数
     *
     * @return
     */
    private Map<String, Object> getAllotParams() {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("warehouseName", this.getCurrentWarehouseName());
        params.put("nowDate", DateUtil.getNowDate());
        params.put("operateUser", this.getOperateUser());

        return params;
    }

    private Map<String, Object> getRtvParams() {
        return new HashMap<String, Object>();
    }

    /**
     * 导出反拣报表
     */
    @Override
    public byte[] exportRepickReport(ReversePickHeader rePickHeader,
                                     List<TrsTask> rePickTasks) {
        if (rePickTasks.isEmpty()) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        List<RepickPrint> repickPrints = this.getRepickPrint(rePickTasks);
        return reportGenerator.generatePDF(ReportName.REPKT.getName(), null,
                getRepickParameters(rePickHeader), repickPrints, Boolean.FALSE);
    }

    /**
     * 越库单
     */
    @Override
    public List<String> getCrossDocDoReport(List<TOCrossDockHeader> toCrossDockHeaders) {
        if (toCrossDockHeaders.isEmpty()) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }

        //校验只有已出库的单子才能打印
        for (TOCrossDockHeader c : toCrossDockHeaders) {
            if (!(Constants.CrossDockStatus.SHIPED.getValue().equals(c.getStatus()))) {
                throw new DeliveryException(DeliveryException.CROSS_DOCK_PRINT_STATUS_ERROR);
            }
        }

        List<String> printData = new ArrayList<String>();
        for (TOCrossDockHeader toCrossDockHeader : toCrossDockHeaders) {
            List<TOCrossDockHeader> dataList = new ArrayList<TOCrossDockHeader>();
            dataList.add(toCrossDockHeader);

            printData.addAll(reportGenerator.builtPrintData(
                    ReportName.CDOCKALLOT.getName(),
                    ReportName.CDOCKALLOT.getSubName(), getAllotParams(),
                    dataList));
        }
        return printData;
    }

    /**
     * 导出越库单
     */
    @Override
    public byte[] exportCrossDocDoReport(List<TOCrossDockHeader> toCrossDockHeaders) {

        if (toCrossDockHeaders.isEmpty()) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }

        // 校验只有已出库的单子才能导出
        for (TOCrossDockHeader c : toCrossDockHeaders) {
            if (!(Constants.CrossDockStatus.SHIPED.getValue().equals(c.getStatus()))) {
                throw new DeliveryException(DeliveryException.CROSS_DOCK_PRINT_STATUS_ERROR);
            }
        }

        return reportGenerator.generatePDF(ReportName.CDOCKALLOT.getName(), ReportName.CDOCKALLOT.getSubName(),
                getAllotParams(), toCrossDockHeaders);
    }

    /**
     * 获取操作人
     *
     * @return
     */
    public String getOperateUser() {
        String userName = null;
        Identity identity = (Identity) Component
                .getInstance("org.jboss.seam.security.identity");
        if (identity != null && identity.getCredentials() != null) {
            userName = identity.getCredentials().getUsername();
        }
        return userName;
    }


    /**
     * 报表名称的级数
     */
    @Override
    public int getPickOrient(ReportName reportName) {
        return reportName.getName().equals(reportName.name) ? 1 : 2;
    }

    /**
     * 获取当前登录仓库名称
     *
     * @return
     */
    public String getCurrentWarehouseName() {
        return cfgWarehouseService.getCurrentWarehouseName();
    }

    /**
     * 如果父SO存在则直接返回，如果不存在则取子SO号
     *
     * @param doHeader
     * @return
     */
    public String buildParentSoNum(DeliveryOrderHeader doHeader) {
        String parentSoNum = StringUtil.trim(doHeader.getUserDeffine5());
        if (StringUtil.isNotEmpty(parentSoNum)) {
            return parentSoNum;
        } else {
            return StringUtil.trim(doHeader.getRefNo1());
        }
    }
}