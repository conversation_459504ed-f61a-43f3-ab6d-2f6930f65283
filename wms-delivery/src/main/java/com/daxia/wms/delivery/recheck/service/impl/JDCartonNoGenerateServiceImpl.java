package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoPrintDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoPrint;
import com.daxia.wms.delivery.recheck.dao.CartonHeaderDAO;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static com.daxia.wms.delivery.constant.DeliveryConstant.JD_CARTON_SPLIT;

@Name("jDCartonNoGenerateService")
@lombok.extern.slf4j.Slf4j
public class JDCartonNoGenerateServiceImpl implements CartonNoGenerateService {

    @In
    private DoPrintDAO doPrintDAO;

    @In
    private CartonHeaderDAO cartonHeaderDAO;

    @Override
    public void generatorCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        String orderNo = doHeader.getDoNo();
        if (StringUtil.isEmpty(orderNo)) {
            throw new DeliveryException(DeliveryException.DO_NO_IS_NULL);
        }

        DoPrint doPrintInfo = doPrintDAO.findByDoHeaderId(doHeader.getId(),
                Constants.DoPrintInfoType.WAYBILL_JSON.getValue());
        if (doPrintInfo == null || StringUtil.isEmpty(doHeader.getTrackingNo())) {
            throw new DeliveryException(DeliveryException.WAYBILL_IMAGE_NOT_EXIST);
        }

        StringBuilder cartonNo = new StringBuilder(doHeader.getTrackingNo());
        //JD 生成临时箱号
        List<String> cartonNoList = cartonHeaderDAO.likeByTrackingNo(doHeader.getTrackingNo());
        String newCartonNo = getCartonNo(cartonNoList, cartonNo).toString();
        cartonHeader.setCartonNo(newCartonNo);
        cartonHeader.setWayBill(doHeader.getTrackingNo());
    }

    private StringBuilder getCartonNo(List<String> cartonNoList, StringBuilder cartonNo) {
        if (CollectionUtils.isEmpty(cartonNoList)) {
            cartonNo.append(JD_CARTON_SPLIT).append("1");
            return cartonNo;
        }

        // 整箱清除特殊处理 + 排序
        // 清除总包裹数
        List<String> cartonNodes = cartonNoList.stream().map(cn -> {
                    String[] split = cn.split(JD_CARTON_SPLIT);
                    if (split.length == 3) {
                        cn = split[0] + JD_CARTON_SPLIT + split[1];
                    }
                    return cn;
                }).sorted(Comparator.comparingInt(c -> Integer.parseInt(c.split(JD_CARTON_SPLIT)[1])))
                .collect(Collectors.toList());

        // 整箱清除(清除中间箱号流程) 获取箱号  判断数组下标与箱号编号是否相同, 不相同则生成新箱号
        for (int i = 1; i < cartonNodes.size() + 1; i++) {

            // 根据排序后的数组下标按顺序判断是否是清除的箱号  清除的箱号进行重新生成
            if (i == Integer.parseInt(cartonNodes.get(i - 1).split(JD_CARTON_SPLIT)[1])) {
                continue;
            }
            return cartonNo.append(JD_CARTON_SPLIT).append(i);
        }

        // 正常装箱流程/清除最后一个箱号流程
        // 获取排序后的箱号列表 取出最后一个箱号 拿到排序数 + 1 生成新箱号
        int cartonNum = Integer.parseInt(cartonNodes.get(cartonNodes.size() - 1).split(JD_CARTON_SPLIT)[1]) + 1;
        return cartonNo.append(JD_CARTON_SPLIT).append(cartonNum);
    }
}
