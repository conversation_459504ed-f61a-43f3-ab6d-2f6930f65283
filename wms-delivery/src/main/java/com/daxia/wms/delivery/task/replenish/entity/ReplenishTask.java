package com.daxia.wms.delivery.task.replenish.entity;

import com.daxia.wms.stock.task.entity.TrsTask;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.xml.bind.annotation.XmlAttribute;
import java.math.BigDecimal;
import java.util.Date;

/**
* 功能说明：补货任务
*/
@Entity
@Table(name = "trs_replenish_task")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = "IS_DELETED = 0")
@SQLDelete(sql = "UPDATE trs_replenish_task SET IS_DELETED = 1 WHERE ID = ? AND VERSION = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class ReplenishTask extends TrsTask {

    private static final long serialVersionUID = -5948208186940394908L;
   
    private BigDecimal offShelfQty;// 下架数量   OFF_SHELF_QTY_EACH
	private BigDecimal offShelfQtyUnit;// 下架数量   OFF_SHELF_QTY_EACH
    private String offShelfBy;         // 下架人        OFF_SHELF_BY;
    private Date offShelfTime;         // 下架时间   OFF_SHELF_TIME;
    private String putAwayBy;          // 上架人        PUTAWAY_BY;
    private Date putAwayTime;          // 上架时间   PUTAWAY_TIME;
    private Integer beSplitted;          // 标记 任务是否被拆分过
    private String replType;			//补货类型
    private String doType; //即时补货业务类型
    private Date planShipTime; //预计出库时间
    private Integer isAuto; //是否自动
	private Integer isUnit; // 是否整箱补货

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)  
    @Column(name = "ID")
    @XmlAttribute
    @Override
    public Long getId() {
        return id;
    }

    @Column(name = "OFF_SHELF_QTY_EACH")
	public BigDecimal getOffShelfQty() {
		return offShelfQty;
	}
	public void setOffShelfQty(BigDecimal offShelfQty) {
		this.offShelfQty = offShelfQty;
	}
	
	@Column(name = "OFF_SHELF_BY")
	public String getOffShelfBy() {
		return offShelfBy;
	}
	public void setOffShelfBy(String offShelfBy) {
		this.offShelfBy = offShelfBy;
	}
	
	@Column(name = "OFF_SHELF_TIME")
	public Date getOffShelfTime() {
		return offShelfTime;
	}
	public void setOffShelfTime(Date offShelfTime) {
		this.offShelfTime = offShelfTime;
	}
	
	@Column(name = "PUTAWAY_BY")
	public String getPutAwayBy() {
		return putAwayBy;
	}
	public void setPutAwayBy(String putAwayBy) {
		this.putAwayBy = putAwayBy;
	}
	
	@Column(name = "PUTAWAY_TIME")
	public Date getPutAwayTime() {
		return putAwayTime;
	}
	public void setPutAwayTime(Date putAwayTime) {
		this.putAwayTime = putAwayTime;
	}
    
	@Column(name = "IS_SPLITE")
    public Integer getBeSplitted() {
		return beSplitted;
	}

	public void setBeSplitted(Integer beSplitted) {
		this.beSplitted = beSplitted;
	}

	@Column(name = "REPL_TYPE")
	public String getReplType() {
		return replType;
	}
	
	public void setReplType(String replType) {
		this.replType = replType;
	}
    
	@Column(name = "DO_TYPE")
    public String getDoType() {
        return doType;
    }

    public void setDoType(String doType) {
        this.doType = doType;
    }
    
    @Column(name = "PLAN_SHIP_TIME")
    public Date getPlanShipTime() {
        return planShipTime;
    }
    
    public void setPlanShipTime(Date planShipTime) {
        this.planShipTime = planShipTime;
    }

    @Column(name = "IS_AUTO")
    public Integer getIsAuto() {
        return isAuto;
    }

    public void setIsAuto(Integer isAuto) {
        this.isAuto = isAuto;
    }
	@Column(name = "IS_UNIT")
	public Integer getIsUnit() {
		return isUnit;
	}

	public void setIsUnit(Integer isUnit) {
		this.isUnit = isUnit;
	}

	@Column(name = "off_shelf_qty_unit")
	public BigDecimal getOffShelfQtyUnit() {
		return offShelfQtyUnit;
	}

	public void setOffShelfQtyUnit(BigDecimal offShelfQtyUnit) {
		this.offShelfQtyUnit = offShelfQtyUnit;
	}
}
