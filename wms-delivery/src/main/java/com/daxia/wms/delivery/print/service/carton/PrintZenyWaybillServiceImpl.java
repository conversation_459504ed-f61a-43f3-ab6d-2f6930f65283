package com.daxia.wms.delivery.print.service.carton;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.WaybillType;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.print.dto.BaseCartonPrintDTO;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.helper.PrintCartonHelper;
import com.daxia.wms.delivery.print.helper.SFPrintCartonHelper;
import com.daxia.wms.delivery.print.service.PrintWaybillService;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.print.PrintConstants;
import com.daxia.wms.print.dto.PrintCfg;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.print.utils.PrintHelper;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.List;

@Name("printZenyWaybillService")
@lombok.extern.slf4j.Slf4j
public class PrintZenyWaybillServiceImpl extends AbstractPrintWaybillService implements PrintWaybillService {
    @In
    WarehouseCarrierService warehouseCarrierService;
    @In
    CartonService cartonService;

    @Create
    public void init () {
        this.setWaybillType(WaybillType.ZENY);
    }

    @Override
    protected void buildDTO(List<PrintReportDto> printReportDtos, DeliveryOrderHeader doHeader, BaseCartonPrintDTO carton,
                            int index, int count) {
        CartonPrintDTO cartonPrintDTO = new CartonPrintDTO();
        cartonPrintDTO.setIsPrinted(carton.getIsPrinted());
        cartonPrintDTO.setCartonId(carton.getId());
        cartonPrintDTO.setCartonNo(carton.getCartonNo());
        cartonPrintDTO.setDoNo(doHeader.getDoNo());
        cartonPrintDTO.setOutRefNo(doHeader.getRefNo1());
        cartonPrintDTO.setIsCOD(PrintCartonHelper.isCOD(doHeader));
        cartonPrintDTO.setSortGridNo(doHeader.getSortGridNo());
        cartonPrintDTO.setWaveNo(doHeader.getWaveHeader().getWaveNo());
        cartonPrintDTO.setOriginalSoCode(doHeader.getOriginalSoCode());
        // 设置箱子里面商品个数
        BigDecimal skuUnit = cartonService.sumSkuUnit(carton.getId());
        cartonPrintDTO.setSkuUnitQty(skuUnit);
        // 设置收货人地址
        cartonPrintDTO.setClientProvinceAndCityAndCountyAddress(
                SFPrintCartonHelper.buildProvinceAndCityAndCountyAddress(doHeader,""));
        cartonPrintDTO.setClientAddress(StringUtil.notNullString(doHeader.getAddress()));
        cartonPrintDTO.setClientName(SFPrintCartonHelper.buildConsigneeName(doHeader));
        cartonPrintDTO.setClientPhone(PrintCartonHelper.buildTelOrMobile(doHeader));
        cartonPrintDTO.setShortAddress(SFPrintCartonHelper.getCityCname(doHeader) + " " + SFPrintCartonHelper
                .getCountyCname(doHeader));
        // 设置寄件人地址信息
        setSendAddressInfo(doHeader,cartonPrintDTO);
        // 运单号
        cartonPrintDTO.setWayBill(carton.getCartonNo());
        // 是否需要待收货款
        boolean receivable = doHeader.getReceivable() != null
                && doHeader.getReceivable().compareTo(BigDecimal.ZERO) > 0;
        cartonPrintDTO.setNeedReceivable(receivable);
        if (receivable) {
            // 设置代收金额
            cartonPrintDTO.setServiceCodAmount(doHeader.getReceivable().toString());
            cartonPrintDTO.setProductSalesType("COD");
        }
        // 设置图片路径
        cartonPrintDTO.setBasePrintImgPath(PrintHelper.getBasePrintImgPath());
        // 设置配送商的信息
        WarehouseCarrier warehouseCarrier = warehouseCarrierService
                .getCarrierInfoByWarehouseIdAndCarrierId(ParamUtil.getCurrentWarehouseId(), doHeader.getCarrierId());
        cartonPrintDTO.setMonthlyAccount(warehouseCarrier.getExt1());
        //设置顺丰产品类型
        cartonPrintDTO.setSuggestCode("");
        cartonPrintDTO.setProductType(warehouseCarrier.getExt3());
        if (PrintConstants.SFProductType.E.getName().equals(warehouseCarrier.getExt3())) {
            cartonPrintDTO.setSuggestCode("E");
        }
        if (Constants.DoStatus.ALL_CARTON.getValue().equals(doHeader.getStatus()) || Constants.DoStatus.ALL_CARTON.getValue().equals(doHeader.getPcsStatus())) {
            cartonPrintDTO.setCartonCount(doHeader.getCartonHeaders().size());
        }
        cartonPrintDTO.setCartonIndex(getCartonIndex(doHeader,carton.getCartonNo()));

        Integer orderNum = SystemConfig.getConfigValueInt("fee.receiverPay.unit.number", ParamUtil.getCurrentWarehouseId());
        if (orderNum != null && doHeader.getExpectedQty().intValue() < orderNum) {
            cartonPrintDTO.setPaymentMethod("运费到付");
        }

        //设置备注
        cartonPrintDTO.setRemark("");
        cartonPrintDTO.setSkuUnitQty(getUnitQtyInCarton(doHeader,carton.getCartonNo()));
        cartonPrintDTO.setNotes(doHeader.getNotes());
        printReportDtos.add(cartonPrintDTO);
    }

    @Override
    protected PrintData genPrintData(List<PrintReportDto> dtoList, DeliveryOrderHeader doHeader) {
        PrintData printData = new PrintData();
        printData.setDtoList(dtoList);
        printData.setPrintCfg(generatePrintCfg());
        return printData;
    }

    private PrintCfg generatePrintCfg() {
        PrintCfg config = new PrintCfg("wayBillZeny", "100", "180");
        this.setPrintCfg(config);
        return config;
    }
}
