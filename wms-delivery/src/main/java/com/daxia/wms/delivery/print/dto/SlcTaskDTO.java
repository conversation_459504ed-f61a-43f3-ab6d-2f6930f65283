package com.daxia.wms.delivery.print.dto;

import java.io.Serializable;
import java.math.BigDecimal;

@lombok.extern.slf4j.Slf4j
public class SlcTaskDTO implements Serializable {

    private static final long serialVersionUID = 8316182654323875023L;
    /**
     * 库位编号
     */
    private String locCode;
    /**
     * 商品名称
     */
    private String productCname;
    /**
     * 商品条码
     */
    private String ean13;
    
    /**
     * 商品编码
     */
    private String productCode;
    /**
     * 实际数量
     */
    private BigDecimal qtyEach;
    
    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
     * 类别
     */
    private String categoryName;
    
    private Long skuId;
    
    private Long locId;

    public SlcTaskDTO(String locCode,String categoryName,BigDecimal qtyEach,BigDecimal price,
            String productCode,String productCname,String ean13,Long skuId,Long locId){
        super();
        this.locCode = locCode;
        this.categoryName = categoryName;
        this.qtyEach = qtyEach;
        this.price = price;
        this.productCode =productCode;
        this.productCname = productCname;
        this.ean13 =ean13;
        this.skuId = skuId;
        this.locId = locId;
        
    }
    
    
    public String getLocCode() {
        return locCode;
    }
    
    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public BigDecimal getQtyEach() {
        return qtyEach;
    }
    
    public void setQtyEach(BigDecimal qtyEach) {
        this.qtyEach = qtyEach;
    }

    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }
    
    public String getProductCname() {
        return productCname;
    }
    
    public void setProductCname(String productCname) {
        this.productCname = productCname;
    }

    public String getEan13() {
        return ean13;
    }

    public void setEan13(String ean13) {
        this.ean13 = ean13;
    }


    
    public Long getSkuId() {
        return skuId;
    }


    
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }


    
    public Long getLocId() {
        return locId;
    }


    
    public void setLocId(Long locId) {
        this.locId = locId;
    }

}
