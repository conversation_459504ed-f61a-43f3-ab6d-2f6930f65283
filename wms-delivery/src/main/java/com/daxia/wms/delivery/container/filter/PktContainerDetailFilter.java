package com.daxia.wms.delivery.container.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

/**
 *  容器管理filter
 */
@lombok.extern.slf4j.Slf4j
public class PktContainerDetailFilter extends WhBaseQueryFilter {

	private static final long serialVersionUID = -8264810166004745332L;

	private String containerNo;
	
	private String docNo;

	private Long headerId;

	private Long taskId;
	
	@Operation(fieldName = "o.pktContainerHeader.containerNo", operationType = OperationType.EQUAL)
	public String getContainerNo() {
		return containerNo;
	}

	public void setContainerNo(String containerNo) {
		this.containerNo = containerNo;
	}
	@Operation(fieldName = "o.pktContainerHeader.docNo", operationType = OperationType.EQUAL)
	public String getDocNo() {
		return docNo;
	}

	public void setDocNo(String docNo) {
		this.docNo = docNo;
	}

	public Long getHeaderId() {
		return headerId;
	}

	public void setHeaderId(Long headerId) {
		this.headerId = headerId;
	}

	public Long getTaskId() {
		return taskId;
	}

	public void setTaskId(Long taskId) {
		this.taskId = taskId;
	}
}
