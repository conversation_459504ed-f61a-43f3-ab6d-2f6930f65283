package com.daxia.wms.delivery.recheck.service.impl.carton;

import net.sf.json.JSONObject;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;

/**
 * Created by Administrator on 2018/1/2.
 */
@lombok.extern.slf4j.Slf4j
public class PinduoduoSessionKey {
//    public static String URL = "http://open-api.pinduoduo.com/oauth/token";

/**获取access_token*/
    public static void main(String agrc[]) throws Exception{

        DefaultHttpClient httpClient = new DefaultHttpClient();
        String url = "http://open-api.pinduoduo.com/oauth/token";
        HttpPost httpPost = new HttpPost(url);

        // 设置请求的header
        httpPost.addHeader("Content-Type", "application/json;charset=utf-8");

        // 设置请求的参数
        JSONObject jsobj = new JSONObject();

        /**换取token*/
        jsobj.put("client_id", "0d59fb7f916349e0ad94bec55a4d6aaa");
        jsobj.put("client_secret", "08f85a9b04db9624c2a7e589ca92ce1f129b83a3");
        jsobj.put("grant_type", "authorization_code");
        jsobj.put("code", "caed80c33630450bb8b3fe01a6ca3f76ba9f3388");
        jsobj.put("redirect_uri", "http://tianji-erp.360jk.com/rest/shop/grantSave");

        /**刷新授权token时间*/
//        jsobj.put("client_id", "0d59fb7f916349e0ad94bec55a4d6aaa");
//        jsobj.put("client_secret", "08f85a9b04db9624c2a7e589ca92ce1f129b83a3");
//        jsobj.put("grant_type", "refresh_token");
//        jsobj.put("refresh_token", "39e2f05c9c0e4e719e15c835242659abb51f84d5");

        StringEntity entity = new StringEntity(jsobj.toString(), "utf-8");
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        httpPost.setEntity(entity);

        // 执行请求
        HttpResponse response = httpClient.execute(httpPost);
        String json2 = EntityUtils.toString(response.getEntity(), "utf-8");
        JSONObject jsonObject = JSONObject.fromObject(json2);

        // 打印执行结果
        System.out.println(jsonObject);

    }


}
