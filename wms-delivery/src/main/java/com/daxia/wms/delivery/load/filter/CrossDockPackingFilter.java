package com.daxia.wms.delivery.load.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

/**
 * CrdockHeader查询条件值对像
 */
@lombok.extern.slf4j.Slf4j
public class CrossDockPackingFilter extends WhBaseQueryFilter {

	private static final long serialVersionUID = -2588916025240916581L;

	private String lpnNo;

	private String cartonNo;

	@Operation(fieldName = "o.lpnNo", operationType = OperationType.EQUAL)
	public String getLpnNo() {
		return lpnNo;
	}

	public void setLpnNo(String lpnNo) {
		this.lpnNo = lpnNo;
	}

	@Operation(clause = " o.id in (select o.cdPackingId from CrossDockPackingCartonNo o where o.cartonNo = ? and o.warehouseId = :warehouseId)", operationType = OperationType.CLAUSE)
	public String getCartonNo() {
		return cartonNo;
	}

	public void setCartonNo(String cartonNo) {
		this.cartonNo = cartonNo;
	}

}
