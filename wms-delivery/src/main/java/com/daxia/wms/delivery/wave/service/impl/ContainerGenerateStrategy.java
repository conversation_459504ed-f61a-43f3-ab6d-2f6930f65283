package com.daxia.wms.delivery.wave.service.impl;


import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.master.entity.ContainerOrderRef;
import com.daxia.wms.master.service.ContainerOrderRefService;
import org.jboss.seam.annotations.In;

import java.util.List;

@lombok.extern.slf4j.Slf4j
public abstract class ContainerGenerateStrategy {

    @In
    protected ContainerOrderRefService containerOrderRefService;
    @In
    protected SequenceGeneratorService sequenceGeneratorService;


    protected void saveContainerRef(List<String> docNoList, String docType){
        for (String docNo : docNoList) {
            ContainerOrderRef containerOrderRef = new ContainerOrderRef();
            containerOrderRef.setContainerNo(sequenceGeneratorService.generateSequenceNo(
                    Constants.SequenceName.CONTAINER_NO.getValue(), ParamUtil.getCurrentWarehouseId()));
            containerOrderRef.setDocNo(docNo);
            containerOrderRef.setDocType(docType);
            containerOrderRef.setIsSeq(Constants.YesNo.YES.getValue());
            containerOrderRefService.save(containerOrderRef);
        }
    }

    protected void delContainerRef(List<String> docNoList, String docType){
        containerOrderRefService.delByDocNoListAndType(docNoList,docType);
    }

    public abstract void saveContainerRef(PickHeader pickHeader);

    public abstract void delContainerRef(PickHeader pickHeader);
}
