package com.daxia.wms.delivery.wave.filter;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlRootElement;

import com.daxia.wms.delivery.wave.entity.WaveHeader;
import org.apache.commons.lang.StringUtils;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;
import com.daxia.wms.Constants.AutoWaveType;

/**
 * 波次查询Filter
 */
@XmlRootElement
@lombok.extern.slf4j.Slf4j
public class WaveHeaderFilter extends WhBaseQueryFilter {

    private static final long serialVersionUID = 6114745547485752529L;

    /**
     * 波次编号
     */
    private String waveNo;

    /**
     * 波次编号从
     */
    private String waveNoFrom;

    /**
     * 波次编号到
     */
    private String waveNoTo;

    /**
     * 波次状态
     */
    private String waveStatus;

    /**
     * 波次状态从
     */
    private String waveStatusFrom;

    /**
     * 波次状态从
     */
    private String waveStatusTo;

    /**
     * 波次类型
     */
    private String waveType;

    /**
     * 发货单数量
     */
    private Long pickCount;

    /**
     * 创建时间
     */
    private Date waveCreateTime;

    /**
     * 波次创建起始时间
     */
    private Date waveCreateTimeFrom;

    /**
     * 波次创建结束时间
     */
    private Date waveCreateTimeTo;

    /**
     * 波次最早预计出库时间From
     */
    private Date estDoFinishTimeFrom;

    /**
     * 波次最早预计出库时间To
     */
    private Date estDoFinishTimeTo;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 分拣单数量
     */
    private Long sortingCount;

    /**
     * 分拣柜号
     */
    private String sortCabintNo;

    /**
     * 备注
     */
    private String notes;

    /**
     * 打印标识
     */
    private Long printFlag;

    /**
     * 波次类别 : 大体积波次、团购波次、半日达波次、普通波次、特殊波次（优先级低）
     */
    private Integer specFlag;

    /**
     * 拣货单号
     */
    private String pktNo;

    /**
     * 集货位号
     */
    private String mergeLocNo;

    /**
     * 集货区编码
     */
    private String mergeAreaCode;

    /**
     * 集货区ID
     */
    private Long mergeAreaId;

    /**
     * 集货状态
     */
    private Integer mergeStatus;

    /**
     * 波次优先级
     */
    private Integer wavePriority;

    private String doNo;

    /**
     * 容器号
     */
    private String containerNo;

    private String unpickedArea;

    private Integer autoFlag;

    private Integer invoiceFlag;

    private Integer invoicePrintFlag;

    private Integer autoType;

    private Integer volumeType;

    private Long carrierId;

    private Long groupRuleId;

    private Integer[] printFlagArray;
    private Integer printFlagDo;
    private Integer printFlagPick;
    private Integer printFlagPktLabel;
    private Integer printFlagCarton;

    private Integer[] notPrintFlagArray;
    private Integer notPrintFlagDo;
    private Integer notPrintFlagPick;
    private Integer notPrintFlagPktLabel;
    private Integer notPrintFlagCarton;

    private Integer emergencyFlag;

    private Long merchantId;

    /**
     * 打印购物清单标记
     */
    private Integer doNeedPrint;

    @Operation(operationType = OperationType.IGNORE)
    public Integer[] getPrintFlagArray() {
        return printFlagArray;
    }

    public void setPrintFlagArray(Integer[] printFlagArray) {
        setPrintFlagDo(null);
        setPrintFlagCarton(null);
        setPrintFlagPick(null);
        setPrintFlagPktLabel(null);

        this.printFlagArray = printFlagArray;
        for (Integer printFlag : printFlagArray) {
            switch (printFlag) {
                case WaveHeader.FLAG_PRINT_DO:
                    setPrintFlagDo(WaveHeader.FLAG_PRINT_DO);
                    break;
                case WaveHeader.FLAG_PRINT_CARTON:
                    setPrintFlagCarton(WaveHeader.FLAG_PRINT_CARTON);
                    break;
                case WaveHeader.FLAG_PRINT_PICK:
                    setPrintFlagPick(WaveHeader.FLAG_PRINT_PICK);
                    break;
                case WaveHeader.FLAG_PRINT_PKT_LABEL:
                    setPrintFlagPktLabel(WaveHeader.FLAG_PRINT_PKT_LABEL);
                    break;
            }
        }
    }

    @Operation(clause = " bitwise_and(o.printFlag, ?) != 0", operationType = OperationType.CLAUSE)
    public Integer getPrintFlagDo() {
        return printFlagDo;
    }

    public void setPrintFlagDo(Integer printFlagDo) {
        this.printFlagDo = printFlagDo;
    }

    @Operation(clause = " bitwise_and(o.printFlag, ?) != 0", operationType = OperationType.CLAUSE)
    public Integer getPrintFlagPick() {
        return printFlagPick;
    }

    public void setPrintFlagPick(Integer printFlagPick) {
        this.printFlagPick = printFlagPick;
    }

    @Operation(clause = " bitwise_and(o.printFlag, ?) != 0", operationType = OperationType.CLAUSE)
    public Integer getPrintFlagPktLabel() {
        return printFlagPktLabel;
    }

    public void setPrintFlagPktLabel(Integer printFlagPktLabel) {
        this.printFlagPktLabel = printFlagPktLabel;
    }

    @Operation(clause = " bitwise_and(o.printFlag, ?) != 0", operationType = OperationType.CLAUSE)
    public Integer getPrintFlagCarton() {
        return printFlagCarton;
    }

    public void setPrintFlagCarton(Integer printFlagCarton) {
        this.printFlagCarton = printFlagCarton;
    }

    @Operation(operationType = OperationType.IGNORE)
    public Integer[] getNotPrintFlagArray() {
        return notPrintFlagArray;
    }

    public void setNotPrintFlagArray(Integer[] notPrintFlagArray) {
        this.notPrintFlagArray = notPrintFlagArray;

        setNotPrintFlagDo(null);
        setNotPrintFlagCarton(null);
        setNotPrintFlagPick(null);
        setNotPrintFlagPktLabel(null);

        for (Integer printFlag : notPrintFlagArray) {
            switch (printFlag) {
                case WaveHeader.FLAG_PRINT_DO:
                    setNotPrintFlagDo(WaveHeader.FLAG_PRINT_DO);
                    break;
                case WaveHeader.FLAG_PRINT_CARTON:
                    setNotPrintFlagCarton(WaveHeader.FLAG_PRINT_CARTON);
                    break;
                case WaveHeader.FLAG_PRINT_PICK:
                    setNotPrintFlagPick(WaveHeader.FLAG_PRINT_PICK);
                    break;
                case WaveHeader.FLAG_PRINT_PKT_LABEL:
                    setNotPrintFlagPktLabel(WaveHeader.FLAG_PRINT_PKT_LABEL);
                    break;
            }
        }
    }

    @Operation(clause = " bitwise_and(o.printFlag, ?) = 0", operationType = OperationType.CLAUSE)
    public Integer getNotPrintFlagDo() {
        return notPrintFlagDo;
    }

    public void setNotPrintFlagDo(Integer notPrintFlagDo) {
        this.notPrintFlagDo = notPrintFlagDo;
    }

    @Operation(clause = " bitwise_and(o.printFlag, ?) = 0", operationType = OperationType.CLAUSE)
    public Integer getNotPrintFlagPick() {
        return notPrintFlagPick;
    }

    public void setNotPrintFlagPick(Integer notPrintFlagPick) {
        this.notPrintFlagPick = notPrintFlagPick;
    }

    @Operation(clause = " bitwise_and(o.printFlag, ?) = 0", operationType = OperationType.CLAUSE)
    public Integer getNotPrintFlagPktLabel() {
        return notPrintFlagPktLabel;
    }

    public void setNotPrintFlagPktLabel(Integer notPrintFlagPktLabel) {
        this.notPrintFlagPktLabel = notPrintFlagPktLabel;
    }

    @Operation(clause = " bitwise_and(o.printFlag, ?) = 0", operationType = OperationType.CLAUSE)
    public Integer getNotPrintFlagCarton() {
        return notPrintFlagCarton;
    }

    public void setNotPrintFlagCarton(Integer notPrintFlagCarton) {
        this.notPrintFlagCarton = notPrintFlagCarton;
    }

    @Operation(fieldName = "waveNo", operationType = OperationType.NOT_LESS_THAN)
    public String getWaveNoFrom() {
        return waveNoFrom;
    }

    public void setWaveNoFrom(String waveNoFrom) {
        this.waveNoFrom = waveNoFrom;
    }

    @Operation(fieldName = "waveNo", operationType = OperationType.NOT_GREAT_THAN)
    public String getWaveNoTo() {
        return waveNoTo;
    }

    public void setWaveNoTo(String waveNoTo) {
        this.waveNoTo = waveNoTo;
    }

    @Operation(fieldName = "waveStatus", operationType = OperationType.EQUAL)
    public String getWaveStatus() {
        return waveStatus;
    }

    public void setWaveStatus(String waveStatus) {
        this.waveStatus = waveStatus;
    }

    @Operation(fieldName = "waveStatus", operationType = OperationType.NOT_LESS_THAN)
    public String getWaveStatusFrom() {
        return waveStatusFrom;
    }

    public void setWaveStatusFrom(String waveStatusFrom) {
        this.waveStatusFrom = waveStatusFrom;
    }

    @Operation(fieldName = "waveStatus", operationType = OperationType.NOT_GREAT_THAN)
    public String getWaveStatusTo() {
        return waveStatusTo;
    }

    public void setWaveStatusTo(String waveStatusTo) {
        this.waveStatusTo = waveStatusTo;
    }

    @Operation(fieldName = "waveType", operationType = OperationType.EQUAL)
    public String getWaveType() {
        return waveType;
    }

    public void setWaveType(String waveType) {
        this.waveType = waveType;
    }

    @Operation(fieldName = "pickCount", operationType = OperationType.EQUAL)
    public Long getPickCount() {
        return pickCount;
    }

    public void setPickCount(Long pickCount) {
        this.pickCount = pickCount;
    }

    @Operation(fieldName = "sortingCount", operationType = OperationType.EQUAL)
    public Long getSortingCount() {
        return sortingCount;
    }

    public void setSortingCount(Long sortingCount) {
        this.sortingCount = sortingCount;
    }

    @Operation(fieldName = "sortCabintNo", operationType = OperationType.EQUAL)
    public String getSortCabintNo() {
        return sortCabintNo;
    }

    public void setSortCabintNo(String sortCabintNo) {
        this.sortCabintNo = sortCabintNo;
    }

    @Operation(fieldName = "notes", operationType = OperationType.EQUAL)
    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public void setWaveCreateTime(Date waveCreateTime) {
        this.waveCreateTime = waveCreateTime;
    }

    @Operation(fieldName = "createdAt", operationType = OperationType.EQUAL)
    public Date getWaveCreateTime() {
        return waveCreateTime;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Operation(fieldName = "createdBy", operationType = OperationType.EQUAL)
    public String getCreatedBy() {
        return createdBy;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    @Operation(fieldName = "waveNo", operationType = OperationType.EQUAL)
    public String getWaveNo() {
        return waveNo;
    }

    @Operation(fieldName = "o.createdAt", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")
    public Date getWaveCreateTimeFrom() {
        return waveCreateTimeFrom;
    }

    public void setWaveCreateTimeFrom(Date waveCreateTimeFrom) {
        this.waveCreateTimeFrom = waveCreateTimeFrom;
    }

    @Operation(fieldName = "o.createdAt", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
    public Date getWaveCreateTimeTo() {
        return waveCreateTimeTo;
    }

    public void setWaveCreateTimeTo(Date waveCreateTimeTo) {
        this.waveCreateTimeTo = waveCreateTimeTo;
    }

    @Operation(fieldName = "o.estDoFinishTime", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")
    public Date getEstDoFinishTimeFrom() {
        return estDoFinishTimeFrom;
    }

    public void setEstDoFinishTimeFrom(Date estDoFinishTimeFrom) {
        this.estDoFinishTimeFrom = estDoFinishTimeFrom;
    }

    @Operation(fieldName = "o.estDoFinishTime", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
    public Date getEstDoFinishTimeTo() {
        return estDoFinishTimeTo;
    }

    public void setEstDoFinishTimeTo(Date estDoFinishTimeTo) {
        this.estDoFinishTimeTo = estDoFinishTimeTo;
    }

    @Operation(fieldName = "o.printFlag", operationType = OperationType.EQUAL)
    public Long getPrintFlag() {
        return printFlag;
    }

    public void setPrintFlag(Long printFlag) {
        this.printFlag = printFlag;
    }

    @Operation(fieldName = "o.specFlag", operationType = OperationType.EQUAL)
    public Integer getSpecFlag() {
        return specFlag;
    }

    public void setSpecFlag(Integer specFlag) {
        this.specFlag = specFlag;
    }

    @Operation(clause = "exists(select t.id from PickHeader t where t.pktNo = ? and t.waveHeadId = o.id)", operationType = OperationType.CLAUSE)
    public String getPktNo() {
        return pktNo;
    }

    public void setPktNo(String pktNo) {
        this.pktNo = pktNo;
    }

    @Operation(fieldName = "o.mergeLocation.mergeCode", operationType = OperationType.EQUAL)
    public String getMergeLocNo() {
        return mergeLocNo;
    }

    public void setMergeLocNo(String mergeLocNo) {
        this.mergeLocNo = mergeLocNo;
    }

    @Operation(fieldName = "o.mergeStatus", operationType = OperationType.EQUAL)
    public Integer getMergeStatus() {
        return mergeStatus;
    }

    public void setMergeStatus(Integer mergeStatus) {
        this.mergeStatus = mergeStatus;
    }

    @Operation(fieldName = "o.priority", operationType = OperationType.EQUAL)
    public Integer getWavePriority() {
        return wavePriority;
    }

    public void setWavePriority(Integer wavePriority) {
        this.wavePriority = wavePriority;
    }

    @Operation(fieldName = "o.mergeLocation.mergeLocArea.mergeLocAreaCode", operationType = OperationType.EQUAL)
    public String getMergeAreaCode() {
        return mergeAreaCode;
    }

    public void setMergeAreaCode(String mergeAreaCode) {
        this.mergeAreaCode = mergeAreaCode;
    }

    @Operation(clause = "exists(select 1 from Container t where t.docType = 0 and t.docNo = o.waveNo  and t.containerNo = ?)", operationType = OperationType.CLAUSE)
    public String getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }

    @Operation(fieldName = "o.mergeLocation.mergeLocArea.id", operationType = OperationType.EQUAL)
    public Long getMergeAreaId() {
        return mergeAreaId;
    }

    public void setMergeAreaId(Long mergeAreaId) {
        this.mergeAreaId = mergeAreaId;
    }

    @Operation(clause = " o.id in (select ph.waveHeadId from PickHeader ph, Region r where ph.regionId = r.id and ph.status = '40' and r.regionCode in (:inPlaceHolder) )", operationType = OperationType.CLAUSE)
    public List<String> getEncludeProductCodes() {
        if (StringUtils.isEmpty(this.getUnpickedArea())) {
            return null;
        }
        String[] codes = StringUtils.split(this.getUnpickedArea(), ",");
        return Arrays.asList(codes);
    }

    @Operation(operationType = OperationType.IGNORE)
    public String getUnpickedArea() {
        return unpickedArea;
    }

    public void setUnpickedArea(String unpickedArea) {
        this.unpickedArea = unpickedArea;
    }

    @Operation(fieldName = "o.isAuto", operationType = OperationType.EQUAL)
    public Integer getAutoFlag() {
        return autoFlag;
    }

    public void setAutoFlag(Integer autoFlag) {
        this.autoFlag = autoFlag;
    }

    @Operation(fieldName = "o.invoiceFlag", operationType = OperationType.EQUAL)
    public Integer getInvoiceFlag() {
        return invoiceFlag;
    }

    public void setInvoiceFlag(Integer invoiceFlag) {
        this.invoiceFlag = invoiceFlag;
    }

    @Operation(fieldName = "o.invoicePrintFlag", operationType = OperationType.EQUAL)
    public Integer getInvoicePrintFlag() {
        return invoicePrintFlag;
    }

    public void setInvoicePrintFlag(Integer invoicePrintFlag) {
        this.invoicePrintFlag = invoicePrintFlag;
    }

    public Integer getAutoType() {
        return autoType;
    }

    public void setAutoType(Integer autoType) {
        this.autoType = autoType;
    }

    @Operation(fieldName = "o.volumeType", operationType = OperationType.EQUAL)
    public Integer getVolumeType() {
        return volumeType;
    }

    public void setVolumeType(Integer volumeType) {
        this.volumeType = volumeType;
    }

    @Operation(clause = " not exists (select 1 from DeliveryOrderHeader dh where dh.waveId = o.id and dh.carrierId != ?) ", operationType = OperationType.CLAUSE)
    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    @Operation(clause = " not exists (select 1 from DeliveryOrderHeader dh where dh.waveId = o.id and dh.doWaveEx.waveCriteriaExId != ?) ", operationType = OperationType.CLAUSE)
    public Long getGroupRuleId() {
        return groupRuleId;
    }

    public void setGroupRuleId(Long groupRuleId) {
        this.groupRuleId = groupRuleId;
    }

    @Operation(clause = "exists(select t.id from DeliveryOrderHeader t where t.waveId = o.id and t.doNo = ?)", operationType = OperationType.CLAUSE)
    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    @Operation(fieldName = "o.emergencyFlag", operationType = OperationType.EQUAL)
    public Integer getEmergencyFlag() {
        return emergencyFlag;
    }

    public void setEmergencyFlag(Integer emergencyFlag) {
        this.emergencyFlag = emergencyFlag;
    }

    @Operation(clause = "exists(select t.id from DeliveryOrderHeader t where t.waveId = o.id and t.merchantId = ?)", operationType = OperationType.CLAUSE)
    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    @Operation(fieldName = "o.doNeedPrint", operationType = OperationType.EQUAL)
    public Integer getDoNeedPrint() {
        return doNeedPrint;
    }

    public void setDoNeedPrint(Integer doNeedPrint) {
        this.doNeedPrint = doNeedPrint;
    }
}