package com.daxia.wms.delivery.load.entity;


import java.math.BigDecimal;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.CascadeType;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;

/**
 * 越库装箱实体类
 */
@Entity
@Table(name = "doc_crdock_packing")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@BatchSize(size = 20)
@Where(clause = "IS_DELETED = 0")
@lombok.extern.slf4j.Slf4j
public class CrossDockPacking extends WhBaseEntity {

	private static final long serialVersionUID = 881172018920148329L;

	// 主键
	private Long id;

	// LPN编号
	private String lpnNo;

	// 调拨出库单
	private Long cdHeaderId;
	
	// 调拨出库单
	private String cdHeaderNo;

	private String asnNo;

	private String refNo1;

	private String poNo;

	private Long toWarehouseId;

	private String toWarehouseName;

	// 是否贵重物品(1:贵重物品；0：非贵重物品)
	private Integer valuableFlag;

	// 箱号
	private String cartonNo;
	
	private BigDecimal skuQty;

    private BigDecimal unitsQty;
	
	private List<CrossDockPackingCartonNo> crossDockPackingCartonNoList;

	@Id
	@Column(name = "ID")
	@GeneratedValue(strategy = GenerationType.AUTO)  
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "LPN_NO")
	public String getLpnNo() {
		return lpnNo;
	}

	public void setLpnNo(String lpnNo) {
		this.lpnNo = lpnNo;
	}

	@Column(name = "CD_HEADER_ID")
	public Long getCdHeaderId() {
		return cdHeaderId;
	}

	public void setCdHeaderId(Long cdHeaderId) {
		this.cdHeaderId = cdHeaderId;
	}

	@Column(name = "CD_HEADER_NO")
	public String getCdHeaderNo() {
		return cdHeaderNo;
	}

	public void setCdHeaderNo(String cdHeaderNo) {
		this.cdHeaderNo = cdHeaderNo;
	}

	@Column(name = "ASN_NO")
	public String getAsnNo() {
		return asnNo;
	}

	public void setAsnNo(String asnNo) {
		this.asnNo = asnNo;
	}

	@Column(name = "REF_NO1")
	public String getRefNo1() {
		return refNo1;
	}

	public void setRefNo1(String refNo1) {
		this.refNo1 = refNo1;
	}

	@Column(name = "PO_NO")
	public String getPoNo() {
		return poNo;
	}

	public void setPoNo(String poNo) {
		this.poNo = poNo;
	}

	@Column(name = "TO_WH_ID")
	public Long getToWarehouseId() {
		return toWarehouseId;
	}

	public void setToWarehouseId(Long toWarehouseId) {
		this.toWarehouseId = toWarehouseId;
	}

	@Column(name = "TO_WH_NAME")
	public String getToWarehouseName() {
		return toWarehouseName;
	}

	public void setToWarehouseName(String toWarehouseName) {
		this.toWarehouseName = toWarehouseName;
	}

	@Column(name = "VALUEABLE_FLAG")
	public Integer getValuableFlag() {
		return valuableFlag;
	}

	public void setValuableFlag(Integer valuableFlag) {
		this.valuableFlag = valuableFlag;
	}

	@Column(name = "CARTON_NO")
	public String getCartonNo() {
		return cartonNo;
	}

	public void setCartonNo(String cartonNo) {
		this.cartonNo = cartonNo;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "crossDockPacking")
	@Where(clause = " IS_DELETED = 0 ")
	@Cascade(value={CascadeType.SAVE_UPDATE}) 
	public List<CrossDockPackingCartonNo> getCrossDockPackingCartonNoList() {
		return crossDockPackingCartonNoList;
	}

	public void setCrossDockPackingCartonNoList(List<CrossDockPackingCartonNo> crossDockPackingCartonNoList) {
		this.crossDockPackingCartonNoList = crossDockPackingCartonNoList;
	}

	@Column(name = "SKU_QTY")
	public BigDecimal getSkuQty() {
		return skuQty;
	}

	public void setSkuQty(BigDecimal skuQty) {
		this.skuQty = skuQty;
	}

	@Column(name = "UNIT_QTY")
	public BigDecimal getUnitsQty() {
		return unitsQty;
	}

	public void setUnitsQty(BigDecimal unitsQty) {
		this.unitsQty = unitsQty;
	}
}
