package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.delivery.deliveryorder.service.DoWaveExService;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.StoWayBillUpdateService;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.waybill.sto.dto.PrintData;
import com.daxia.wms.waybill.sto.dto.StoBillNoRequest;
import com.daxia.wms.waybill.sto.service.StoWaybillService;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.util.ArrayList;
import java.util.List;

@Name("stoWayBillUpdateService")
@lombok.extern.slf4j.Slf4j
public class StoWayBillUpdateServiceImpl implements StoWayBillUpdateService {
    @In
    private WarehouseCarrierService warehouseCarrierService;
    @In
    private WarehouseService warehouseService;
    @In(create = true)
    private StoWaybillService stoWaybillService;
    @In
    private DoWaveExService doWaveExService;

    @Override
    public void reqeust(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        WarehouseCarrier warehouseCarrier = warehouseCarrierService.getCarrierInfoByWarehouseIdAndCarrierId(doHeader.getWarehouseId(), doHeader.getCarrierId());
        StoBillNoRequest updateRequest = new StoBillNoRequest();
        updateRequest.setUrl(warehouseCarrier.getContentUrl());
        updateRequest.setCode("vip0007");
        updateRequest.setData_digest(warehouseCarrier.getExt1());//方法签名
        updateRequest.setCuspwd(warehouseCarrier.getExt2());//客户密码

        List<PrintData> printDatas = new ArrayList<PrintData>();
        updateRequest.setPrintDataList(printDatas);
        PrintData printData = new PrintData();
        printData.setBillno(cartonHeader.getWayBill());
        printData.setSenddate(DateUtil.dateToString(DateUtil.getTodayDate()));
        printData.setSendsite(warehouseCarrier.getExt3());//网点名称
        printData.setSendcus(warehouseCarrier.getExt4());//客户名称

        printData.setGoodsname(doHeader.getDoDetails().get(0).getSku().getProductCname());
        printData.setInputdate(DateUtil.dateToString(DateUtil.getTodayDate()));
        printData.setInputperson(ParamUtil.getCurrentLoginName());

        Warehouse warehouse = warehouseService.getWarehouse(doHeader.getWarehouseId());
        printData.setSendperson(warehouse.getContactor());
        printData.setSendtel(StringUtil.isEmpty(warehouse.getMobile())?warehouse.getPhone():warehouse.getMobile());
        printData.setSendprovince(warehouse.getProvince().getProvinceCname());
        printData.setSendcity(warehouse.getCity().getCityCname());
        printData.setSendarea(warehouse.getCounty().getCountyCname());
        printData.setSendaddress(warehouse.getAddressName());

        printData.setReceiveperson(doHeader.getConsigneeName());
        printData.setReceivetel(DoUtil.decryptPhone(StringUtil.isEmpty(doHeader.getMobile()) ? doHeader.getTelephone() : doHeader.getMobile()));
        printData.setReceiveprovince(StringUtils.isNotEmpty(doHeader.getProvinceName()) ? doHeader.getProvinceName() :
                doHeader.getProvinceInfo().getProvinceCname());
        printData.setReceivecity(StringUtils.isNotEmpty(doHeader.getCityName()) ? doHeader.getCityName() :
                doHeader.getCityInfo().getCityCname());
        printData.setReceivearea(StringUtils.isNotEmpty(doHeader.getCountyName()) ? doHeader.getCountyName() :
                doHeader.getCountyInfo().getCountyCname());
        printData.setReceiveaddress(doHeader.getAddress());
        printData.setOrderno(doHeader.getDoNo());
        printDatas.add(printData);
        String bigchar = stoWaybillService.updatePrintDatas(updateRequest);
        DoWaveEx doWaveEx = doWaveExService.findByDoHeaderId(doHeader.getId());
        if (doWaveEx != null) {
            doWaveEx.setTrackingNo(cartonHeader.getWayBill());
            doWaveEx.setDestinationCode(bigchar);
            doWaveExService.update(doWaveEx);
        }
    }

}