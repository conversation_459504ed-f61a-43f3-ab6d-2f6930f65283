package com.daxia.wms.delivery.invoice.extend.aisinogz.dto;


import com.daxia.wms.invoice.entity.ElectroniceInfo;

import javax.xml.bind.annotation.*;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="in0" type="{http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com}ElectroniceInfo"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "in0" })
@XmlRootElement(name = "sendToInvEli")
@lombok.extern.slf4j.Slf4j
public class SendToInvEli {

	@XmlElement(required = true, nillable = true)
	protected ElectroniceInfo in0;

	/**
	 * Gets the value of the in0 property.
	 * 
	 * @return possible object is {@link ElectroniceInfo }
	 * 
	 */
	public ElectroniceInfo getIn0() {
		return in0;
	}

	/**
	 * Sets the value of the in0 property.
	 * 
	 * @param value
	 *            allowed object is {@link ElectroniceInfo }
	 * 
	 */
	public void setIn0(ElectroniceInfo value) {
		this.in0 = value;
	}

}
