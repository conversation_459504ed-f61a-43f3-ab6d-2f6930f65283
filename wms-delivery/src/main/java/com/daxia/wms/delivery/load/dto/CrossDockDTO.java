package com.daxia.wms.delivery.load.dto;

import java.io.Serializable;

import com.daxia.framework.common.util.StringUtil;

/**
 * 越库发货时stockCrossDock的Dto
 */
@lombok.extern.slf4j.Slf4j
public class CrossDockDTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4672111763518933915L;

	
	private String doNo ; 				//调拨单号 
	
	private String asnNo;				//ASN单号
	
	private String refNo1;				//调拨指令号
	
	private String status;				//状态
	
	private String toWhId;				//目标仓库ID
	
	private String toWarehouseName;		//目标仓库名称

	private Long toCustomerId;				//目标商家ID

	private String toCustomerName;		//目标商家名称
	
	private String scannedLpnNo;		//已扫描LPN号

	@SuppressWarnings("unused")
	private String notScannedLpnNo;		//未扫描LPN号
	
	private String needScannedLpnNo;	//需要扫描LPN号
	
	private Long toCrossDockId; 		//越库ID
	
	private String cdDumpLpnNo;			//转储LPN

	// 是否贵重物品(1:贵重物品；0：非贵重物品)
	private Integer valuableFlag;
	
	public String getDoNo() {
		return doNo;
	}
	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}

	public String getAsnNo() {
		return asnNo;
	}
	public void setAsnNo(String asnNo) {
		this.asnNo = asnNo;
	}

	public String getRefNo1() {
		return refNo1;
	}
	public void setRefNo1(String refNo1) {
		this.refNo1 = refNo1;
	}

	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}

	public String getToWhId() {
		return toWhId;
	}
	public void setToWhId(String toWhId) {
		this.toWhId = toWhId;
	}

	public String getToWarehouseName() {
		return toWarehouseName;
	}
	public void setToWarehouseName(String toWarehouseName) {
		this.toWarehouseName = toWarehouseName;
	}

	public String getScannedLpnNo() {
		return scannedLpnNo;
	}
	public void setScannedLpnNo(String scannedLpnNo) {
		this.scannedLpnNo = scannedLpnNo;
	}

	public String getNotScannedLpnNo() {
		if(StringUtil.isEmpty(getScannedLpnNo())){
			return getNeedScannedLpnNo();
		}
		StringBuffer notScannedLpnNoStr = new StringBuffer();
		for (String needScannedLpn : needScannedLpnNo.split(",")) {
			boolean notScanned = true;
			for (String scannedLpn : scannedLpnNo.split(",")) {
				if(needScannedLpn.equals(scannedLpn)){
					notScanned = false;
				}
			}
			if(notScanned){
				if(notScannedLpnNoStr.length() > 0){
					notScannedLpnNoStr.append(",");
				}
				notScannedLpnNoStr.append(needScannedLpn);
			}
		}
		return notScannedLpnNoStr.toString();
	}
	public void setNotScannedLpnNo(String notScannedLpnNo) {
		this.notScannedLpnNo = notScannedLpnNo;
	}
	
	public String getNeedScannedLpnNo() {
		return needScannedLpnNo;
	}
	public void setNeedScannedLpnNo(String needScannedLpnNo) {
		this.needScannedLpnNo = needScannedLpnNo;
	}
	
	public Long getToCrossDockId() {
		return toCrossDockId;
	}
	public void setToCrossDockId(Long toCrossDockId) {
		this.toCrossDockId = toCrossDockId;
	}
	
	public Integer getValuableFlag() {
		return valuableFlag;
	}
	public void setValuableFlag(Integer valuableFlag) {
		this.valuableFlag = valuableFlag;
	}
	
	public String getCdDumpLpnNo() {
		return cdDumpLpnNo;
	}
	public void setCdDumpLpnNo(String cdDumpLpnNo) {
		this.cdDumpLpnNo = cdDumpLpnNo;
	}

	public Long getToCustomerId() {
		return toCustomerId;
	}

	public void setToCustomerId(Long toCustomerId) {
		this.toCustomerId = toCustomerId;
	}

	public String getToCustomerName() {
		return toCustomerName;
	}

	public void setToCustomerName(String toCustomerName) {
		this.toCustomerName = toCustomerName;
	}
}
