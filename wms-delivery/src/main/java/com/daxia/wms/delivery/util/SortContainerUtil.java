package com.daxia.wms.delivery.util;

import com.daxia.wms.delivery.DeliveryException;

@lombok.extern.slf4j.Slf4j
public class SortContainerUtil {

	/**
	 * 自动截取前8位作为实际分拣筐条码使用，不足8位时无需截位处理，提示分拣筐不存在
	 * @param sortContainer
	 * @return
	 */
	public static String subStringSortContainer(String sortContainer){
		if (sortContainer != null) {
			if (sortContainer.length() > 8) {
				sortContainer = sortContainer.substring(0,8);
			} else if (sortContainer.length() < 8) {
				throw new DeliveryException(DeliveryException.SORT_CONTAINER_NOT_EXIT);
			}
		}
		return sortContainer;
	}
}
