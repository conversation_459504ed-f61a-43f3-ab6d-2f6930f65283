package com.daxia.wms.delivery.deliveryorder.dao;


import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.deliveryorder.entity.DoReservationEx;
/**
 * DO头预约扩展表DAO
 */

@Name("com.daxia.wms.delivery.doReservationExDAO")
@lombok.extern.slf4j.Slf4j
public class DoReservationExDAO extends HibernateBaseDAO<DoReservationEx,Long>{
	
	private static final long serialVersionUID = 2270048413049692538L;

	public DoReservationEx getDoReservationExByDoId(Long doId){
		String hql = "select o from DoReservationEx o where o.doHeaderId = :doId "
				+ "and o.warehouseId = :warehouseId ";
		Query query = this.createQuery(hql);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameter("doId",doId);
		return (DoReservationEx) query.uniqueResult();
	}
}