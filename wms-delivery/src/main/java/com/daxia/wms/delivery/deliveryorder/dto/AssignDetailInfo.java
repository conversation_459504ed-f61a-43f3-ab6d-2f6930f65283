package com.daxia.wms.delivery.deliveryorder.dto;

import java.math.BigDecimal;

import com.daxia.wms.stock.stock.entity.StockBatchLocLpn;

/**
 * 分配明细的相关的
 */
@lombok.extern.slf4j.Slf4j
public class AssignDetailInfo {

    /**
     * 现分配数量
     */
    private BigDecimal assignQty;
    
    /**
     * 已分配数
     */
    private BigDecimal allocatedQty;

    /**
     * 可用分配库存
     */
    private BigDecimal valiableQty;

    private StockBatchLocLpn stockBatchLocLpn;

	public BigDecimal getAssignQty() {
		return assignQty;
	}

	public void setAssignQty(BigDecimal assignQty) {
		this.assignQty = assignQty;
	}

	public BigDecimal getAllocatedQty() {
		return allocatedQty;
	}

	public void setAllocatedQty(BigDecimal allocatedQty) {
		this.allocatedQty = allocatedQty;
	}

	public BigDecimal getValiableQty() {
		return valiableQty;
	}

	public void setValiableQty(BigDecimal valiableQty) {
		this.valiableQty = valiableQty;
	}

	public StockBatchLocLpn getStockBatchLocLpn() {
		return stockBatchLocLpn;
	}

	public void setStockBatchLocLpn(StockBatchLocLpn stockBatchLocLpn) {
		this.stockBatchLocLpn = stockBatchLocLpn;
	}
}
