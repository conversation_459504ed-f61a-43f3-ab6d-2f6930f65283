package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.Arith;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ResourceUtils;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.OrderLogConstants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.OrderFrozenException;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.delivery.crossorder.dao.CrossSeedDetailDAO;
import com.daxia.wms.delivery.crossorder.entity.CrossSeedDetail;
import com.daxia.wms.delivery.deliveryorder.dao.DoDetailDAO;
import com.daxia.wms.delivery.deliveryorder.dao.DoHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.OrderLogService;
import com.daxia.wms.delivery.deliveryorder.service.impl.DoStatusRollbackListener;
import com.daxia.wms.delivery.recheck.dao.CartonDetailDAO;
import com.daxia.wms.delivery.recheck.dao.CartonHeaderDAO;
import com.daxia.wms.delivery.recheck.dto.ReCheckCartonDetail;
import com.daxia.wms.delivery.recheck.dto.ReCheckCartonInfo;
import com.daxia.wms.delivery.recheck.dto.ReCheckRecord;
import com.daxia.wms.delivery.recheck.entity.CartonDetail;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.recheck.service.ClearReCheckRecordService;
import com.daxia.wms.delivery.recheck.service.ReCheckService;
import com.daxia.wms.delivery.task.repick.service.RePickContainerService;
import com.daxia.wms.master.dao.TrsMaterialsLogDAO;
import com.daxia.wms.master.entity.Container;
import com.daxia.wms.master.entity.Sku;
import com.daxia.wms.serial.dao.TrsSerialLogDAO;
import com.daxia.wms.serial.entity.TrsSerialLog;
import com.daxia.wms.serial.service.SerialService;
import com.daxia.wms.stock.stock.dao.StockSerialDAO;
import com.daxia.wms.stock.util.BatchPropertyUtil;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * 删除装箱数据Service
 */
@Name("com.daxia.wms.delivery.clearReCheckRecordService")
@lombok.extern.slf4j.Slf4j
public class ClearReCheckRecordServiceImpl implements ClearReCheckRecordService, DoStatusRollbackListener, Serializable {   
	private static final long serialVersionUID = 1L;
	@In
	private ReCheckService reCheckService;
	@In
	private SerialService serialService;
	@In
	private CartonHeaderDAO cartonHeaderDAO;
	@In
	private DoHeaderDAO doHeaderDAO;
	@In
	private DoDetailDAO doDetailDAO;
	@In
	private CartonDetailDAO cartonDetailDAO;
	@In
	private TrsSerialLogDAO trsSerialLogDAO;
	@In
	private StockSerialDAO stockSerialDAO;
    @In
    private ContainerMgntService containerMgntService;
    @In
    private DeliveryOrderService deliveryOrderService;
    @In
    private CartonService cartonService;
	@In
	private OrderLogService orderLogService;
	@In
	RePickContainerService rePickContainerService;
	@In
	private CrossSeedDetailDAO crossSeedDetailDAO;

	@In
	private TrsMaterialsLogDAO trsMaterialsLogDAO;

	/**
	 * 查询某一箱的装箱信息
	 * @param cartonNo 箱号
	 * @return
	 */
	@Override
	public ReCheckCartonInfo findReCheckCartonInfo(String cartonNo) {		
		return reCheckService.selectCarton(cartonNo, Boolean.FALSE);
	}
	
	/**
	 * 检查定单状态
	 * @param doId
	 * @throws DeliveryException 订单状态错误时抛出该异常
	 */
	private DeliveryOrderHeader checkDo(Long doId) throws DeliveryException{
		DeliveryOrderHeader doHeader = doHeaderDAO.get(doId);
		if (null == doHeader) {
		    throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
		}
		if (Constants.ReleaseStatus.HOLD.getValue().equals(doHeader.getReleaseStatus())) {
		    throw new OrderFrozenException(doId);
		}
		if(!(DoStatus.ALL_CARTON.getValue().equals(doHeader.getStatus()) || DoStatus.PART_CARTON.getValue().equals(doHeader.getStatus()))) {
			throw new DeliveryException(DeliveryException.DO_STATUS_ERROR_CAN_NOT_CANCEL);
		}
		return doHeader;
	}

    @Override
    @Transactional
	public void clearEmptyCarton(Long orderId, Long cartonId) {
		DeliveryOrderHeader doHeader = checkDo(orderId);
		CartonHeader cartonHeader = cartonHeaderDAO.get(cartonId);
		if (null == cartonHeader) {
			throw new DeliveryException(DeliveryException.CARTON_NO_EXIST);
		}
		//验证是否绑定分拣筐
		deliveryOrderService.checkExDoNeedSortContainer(doHeader);
		List<Long> skuIds = cartonDetailDAO.queryCartonSkus(cartonId);
		if (ListUtil.isNotEmpty(skuIds)) {
			throw new DeliveryException(DeliveryException.ERROR_CARTONNO_STATUSERROR, cartonHeader.getCartonNo());
		}

		//删除装箱明细
		cartonDetailDAO.deleteByCartonId(cartonId);

		//调用菜鸟接口
		cartonService.cancelCartonById(cartonHeader.getId());

		//物理删除装箱头信息
		cartonHeaderDAO.physicalDeleteById(cartonHeader.getId());
		cartonHeaderDAO.getSession().flush();
		resetDoStatus(cartonHeader.getDoHeader());
		//删除DO的包裹绑定的包裹容器
		List<Container> packContainers = containerMgntService.findContainerByDoc(cartonHeader.getCartonNo(),
				Constants.ContainerType.PACKAGE_CONTAINER.getValue(), Constants.BindDocType.RECHECK.getValue());
		// 判断cartonHeader是否绑定包裹容器，并释放容器
		if (ListUtil.isNotEmpty(packContainers)) {
			for (Container c : packContainers) {
				containerMgntService.release(c);
			}
		}
	}

	/**
	 * 清除定单下面指定箱的装箱信息
	 * @param orderId
	 * @param cartonId
	 * @throws OrderFrozenException 定单处于冻结状态时抛出该异常
	 */
	@Override
	@Transactional
	public void clearCrossCarton(Long orderId, Long cartonId) {
		DeliveryOrderHeader doHeader = checkDo(orderId);
		CartonHeader cartonHeader = cartonHeaderDAO.get(cartonId);
		if (null == cartonHeader) {
			throw new DeliveryException(DeliveryException.CARTON_NO_EXIST);
		}

		List<CartonDetail> cartonDetailList = cartonDetailDAO.getCartonDetailByHeaderId(cartonId);
		for (CartonDetail cartonDetail : cartonDetailList) {
			CrossSeedDetail crossDetail = crossSeedDetailDAO.get(cartonDetail.getCrossDetailId());
			if(null == crossDetail){
				throw new DeliveryException(DeliveryException.CROSS_DETAIL_NOT_EXIST);
			}
			BigDecimal packedNo = cartonDetail.getPackedNumber();   //清箱清除数
			BigDecimal packedQty =  crossDetail.getPackedQty();    //已核拣数
			BigDecimal packedNew = packedQty.subtract(packedNo);
			if(packedNew.compareTo(BigDecimal.ZERO) < 0){
				throw new DeliveryException(DeliveryException.CLEAR_CARTON_NUM_ERROR);
			}
			crossDetail.setPackedQty(packedNew);
			crossSeedDetailDAO.update(crossDetail);
		}

		List<Long> skuIds = cartonDetailDAO.queryCartonSkus(cartonId);
		//删除装箱明细
		cartonDetailDAO.deleteByCartonId(cartonId);
		doDetailDAO.updateStatusBySkus(doHeader.getId(), skuIds, Constants.DoStatus.PART_CARTON.getValue());
		//调用菜鸟接口
//		cartonService.cancelCartonById(cartonHeader.getId());

		//物理删除装箱头信息
		cartonHeaderDAO.physicalDeleteById(cartonHeader.getId());
		cartonHeaderDAO.getSession().flush();
		resetDoStatus(cartonHeader.getDoHeader());

		//记录日志，整箱清除
		orderLogService.saveLog(doHeader,
				OrderLogConstants.OrderLogType.CLEAR_CARTON.getValue(),
				ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_CLEAR_CARTON,null,cartonHeader.getCartonNo()));
	}

	/**
	 * 清除定单下面指定箱的装箱信息
	 * @param orderId
	 * @param cartonId
	 * @throws OrderFrozenException 定单处于冻结状态时抛出该异常
	 */
	@Override
	@Transactional
	public void clearCarton(Long orderId, Long cartonId) {
		DeliveryOrderHeader doHeader = checkDo(orderId);
		CartonHeader cartonHeader = cartonHeaderDAO.get(cartonId);
		if (null == cartonHeader) {
		    throw new DeliveryException(DeliveryException.CARTON_NO_EXIST);
		}
		//验证是否绑定分拣筐
		deliveryOrderService.checkExDoNeedSortContainer(doHeader);
		List<Long> skuIds = cartonDetailDAO.queryCartonSkus(cartonId);
		//删除装箱明细
		cartonDetailDAO.deleteByCartonId(cartonId);
		//查询所有已消耗掉的序列号，这时transactionId对应的值为cartonId
//		List<TrsSerialLog> trsSerials = trsSerialLogDAO.findSerialNoByDocAndTrs(orderId, Constants.DocType.SO.getValue(), cartonId);
//		//还原序列号库存。
//		for(TrsSerialLog log : trsSerials){
//			restoreStockSerial(log);
//		}
		//删除序列号信息，这时transactionId对应的值为cartonId
		trsSerialLogDAO.removeByDocAndTrs(orderId, Constants.DocType.SO.getValue(), cartonId);
		// 删除包材 耗材信息
		trsMaterialsLogDAO.removeMaterialsInfo(doHeader.getId());
		doDetailDAO.updateStatusBySkus(doHeader.getId(), skuIds, Constants.DoStatus.PART_CARTON.getValue());
		
		//调用菜鸟接口
		cartonService.cancelCartonById(cartonHeader.getId());

        //物理删除装箱头信息
		cartonHeaderDAO.physicalDeleteById(cartonHeader.getId());
		cartonHeaderDAO.getSession().flush();
		resetDoStatus(cartonHeader.getDoHeader());
		//删除DO的包裹绑定的包裹容器
		List<Container> packContainers = containerMgntService.findContainerByDoc(cartonHeader.getCartonNo(),
                Constants.ContainerType.PACKAGE_CONTAINER.getValue(), Constants.BindDocType.RECHECK.getValue());
        // 判断cartonHeader是否绑定包裹容器，并释放容器
        if (ListUtil.isNotEmpty(packContainers)) {
        	for (Container c : packContainers) {
        		 containerMgntService.release(c);
        	}
        }

		//记录日志，整箱清除
		orderLogService.saveLog(doHeader,
				OrderLogConstants.OrderLogType.CLEAR_CARTON.getValue(),
				ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_CLEAR_CARTON,null,cartonHeader.getCartonNo()));
	}
	
	/**
	 * 查询CartonId指定的装箱内，条码为productBarCode所有商品的装箱信息。因为存在一码多品的情况，因此返回值一个列表
	 * @param cartonId
	 * @param productBarCode
	 * @return 返回每一个商品对应的装箱信息
	 */
	@Override
	public List<ReCheckCartonDetail> findReCheckCartonDetail(Long cartonId, String productBarCode) {
		List<CartonDetail> cartonDetails = cartonDetailDAO.findByCartonIdAndProductBarCode(cartonId, productBarCode);
		if(cartonDetails == null || cartonDetails.isEmpty()){
			return null;
		}
		return mergeCartonDetailBySku(cartonDetails, productBarCode);
	}
	
	/**
	 * 合并相同sku的装箱记录
	 * @param details
	 * @param productBarCode
	 * @return
	 */
	private List<ReCheckCartonDetail> mergeCartonDetailBySku(List<CartonDetail> details, String productBarCode) {
		Map<Sku, ReCheckCartonDetail> reCheckCartonDetailMap = new HashMap<Sku, ReCheckCartonDetail>();
		details = new ArrayList<CartonDetail>(new LinkedHashSet<CartonDetail>(
				details));// 去除重复项
		for (CartonDetail cartonDetail : details) {// 根据sku合并数据
			Sku sku = cartonDetail.getSku();
			ReCheckCartonDetail reCheckCartonDetail = reCheckCartonDetailMap
					.get(sku);
			if (reCheckCartonDetail != null) {
				reCheckCartonDetail.increaseNumberInCarton(cartonDetail
						.getPackedNumber());
				continue;
			}
			ReCheckRecord reCheckRecord = initReCheckRecord(sku, productBarCode);
			reCheckCartonDetail = new ReCheckCartonDetail(null,	reCheckRecord, cartonDetail.getPackedNumber());
			reCheckCartonDetailMap.put(sku, reCheckCartonDetail);
		}
		return new ArrayList<ReCheckCartonDetail>(
				reCheckCartonDetailMap.values());
	}
	
	/**
	 * 初始化一条核拣记录
	 * @param sku
	 * @param productBarCode
	 * @return
	 */
	private ReCheckRecord initReCheckRecord(Sku sku, String productBarCode) {
		ReCheckRecord reCheckRecord = new ReCheckRecord();
		reCheckRecord.setProductId(sku.getId());
		reCheckRecord.setProductBarCode(productBarCode);
		reCheckRecord.setProductCode(sku.getProductCode());		
		reCheckRecord.setProductName(sku.getProductCname());
		reCheckRecord.setUnit(BatchPropertyUtil.getUom(sku));
		return reCheckRecord;
	}	
	
	/**
	 * 根据箱号来进行查询
	 * @param cartonNo
	 * @return CartonHeader
	 */
	@Override
	public CartonHeader findCartonHeader(String cartonNo) {
		CartonHeader cartonHeader = cartonHeaderDAO.findByCartonNo(cartonNo);
		if(cartonHeader != null){
			cartonHeader.getDoHeader().getDoNo();//初始化doHeader
		}
		return cartonHeader;
	}

	/**
	 * 商品是否需要进行序列号扫描
	 * @param doId
	 * @param cartonId
	 * @param productId
	 * @return 需要时返回true
	 */
	@Override
	public boolean needScanSerial(Long doId, Long cartonId, Long productId) {
		return trsSerialLogDAO.isExistTrsSerialLog(doId, Constants.DocType.SO.getValue(), cartonId, productId);
	}
	
	/**
	 * 查询序列号log的数据库Id
	 * @param orderId
	 * @param cartonId
	 * @param productId
	 * @param serialNo
	 * @return
	 */
	@Override
	public Long findTrsSerialLogId(Long orderId, Long cartonId, Long productId,
			String serialNo) {
		Map<String, Object> properties = new HashMap<String, Object>();
		properties.put("docId", orderId);
		properties.put("docType", Constants.DocType.SO.getValue());
		properties.put("transactionId", cartonId);
		properties.put("skuId", productId);
		properties.put("serialNo", serialNo);
		TrsSerialLog log = trsSerialLogDAO.getByProperty(properties);
		return log == null? null: log.getId();
	}
	
	/**
	 * 从某装箱记录中移除一个指定的商品，如果删除商品时，检查到对应的装箱中已无其它数据，则删除该箱头信息
	 * @param orderId
	 * @param cartonId
	 * @param productId
	 * @param trsSerialLogId 产品关联的序列号
	 * @return 如果级联删除了装箱头信息，则返回True，否则返回false
	 * @throws OrderFrozenException 定单处于冻结状态时抛出该异常
	 */
	@Override
	@Transactional
	public boolean clearProduct(Long orderId, Long cartonId, Long productId,
			Long trsSerialLogId) {
		checkDo(orderId);
		boolean hasCascadeDeleteCarton = false;
		//修改装箱明细
        CartonDetail cartonDetail = cartonDetailDAO.findOneByCartonIdAndProductId(cartonId, productId);
        if (cartonDetail.getPackedNumber().compareTo(BigDecimal.ONE) ==0 ) {
            // 只有一个商品装箱，清除该条记录
            cartonDetailDAO.deleteById(cartonDetail.getId());
            hasCascadeDeleteCarton = resetCartonStatus(cartonDetail);
        } else {
            // 修改商品装箱数
            cartonDetail.setPackedNumber(cartonDetail.getPackedNumber().subtract(BigDecimal.ONE));
            CartonHeader cartonHeader = cartonDetail.getCartonHeader();
            //重设箱体积毛重信息。
            reduceCartonVolumeAndWeight(cartonDetail, cartonHeader);
            cartonHeaderDAO.update(cartonHeader);
            cartonDetailDAO.update(cartonDetail);
            if (!Constants.DoStatus.PART_CARTON.getValue().equals(
                    cartonDetail.getCartonHeader().getDoHeader().getStatus())) {
                cartonDetail.getCartonHeader().getDoHeader().setStatus(Constants.DoStatus.PART_CARTON.getValue());
            }
        }
        List<Long> skuIds = new ArrayList<Long>(1);
        skuIds.add(productId);
        doDetailDAO.updateStatusBySkus(orderId, skuIds, Constants.DoStatus.PART_CARTON.getValue());
        
		if(trsSerialLogId != null) { 
		//处理序列号
			
			TrsSerialLog log = trsSerialLogDAO.get(trsSerialLogId);
			trsSerialLogDAO.deleteById(log.getId());
			//还原StockSerial
			restoreStockSerial(log);
			
		}
		return hasCascadeDeleteCarton;		
	}

	/**
	 * 取消装箱或者状态回退时恢复序列号库存的操作。
	 * 如果序列号对应库存存在则恢复库存  ，否则根据序列号出库交易重新生成库存。
	 * @param log
	 */
	private void restoreStockSerial(TrsSerialLog log) {
		int updatedRowNum = stockSerialDAO.restoreBySerialNo(log.getSerialNo(),log.getMerchantId());
		if (0 == updatedRowNum) {
			serialService.createSerialStockByTrsSerialLog(log);
		}
	}
	
	/**
	 * 检查明细对应的该箱是否还有其它数据，如果该箱无数据，则同时删除装箱头信息
	 * @param cartonDetail 装箱明细 
	 * @return 如果同时删除了对应的装箱头信息，则返回true，否则返回false
	 */
	private boolean resetCartonStatus(CartonDetail cartonDetail){
		CartonHeader cartonHeader = cartonDetail.getCartonHeader();
		if(cartonHeaderDAO.isExistCartonDetail(cartonHeader.getId())){ //该箱还有装箱明细
			reduceCartonVolumeAndWeight(cartonDetail, cartonHeader);
			cartonHeaderDAO.update(cartonHeader);
			cartonHeader.getDoHeader().setStatus(Constants.DoStatus.PART_CARTON.getValue());
			doHeaderDAO.update(cartonHeader.getDoHeader());
			return false;
		}
        
		DeliveryOrderHeader doHeader = cartonHeader.getDoHeader();
		  //验证分拣筐doHeader
		deliveryOrderService.checkExDoNeedSortContainer(doHeader);
        
	      //调用菜鸟接口
        cartonService.cancelCartonById(cartonHeader.getId());
        
		cartonHeaderDAO.physicalDeleteById(cartonHeader.getId());
		cartonHeaderDAO.getSession().flush();
		resetDoStatus(doHeader);
		// 判断cartonHeader是否绑定包裹容器，并释放容器
        List<Container> packContainers = containerMgntService.findContainerByDoc(cartonHeader.getCartonNo(),
                Constants.ContainerType.PACKAGE_CONTAINER.getValue(), Constants.BindDocType.RECHECK.getValue());
        if (ListUtil.isNotEmpty(packContainers)) {
        	for (Container c : packContainers) {
        		 containerMgntService.release(c);
        	}
        }
		return true;
	}

    /**
     * 逐件清除装箱商品时重新设值装箱单头毛重体积等信息。
     * @param cartonDetail
     * @param cartonHeader
     */
    private void reduceCartonVolumeAndWeight(CartonDetail cartonDetail, CartonHeader cartonHeader) {
        double oldVolume = cartonHeader.getVolume() == null ? 0 : cartonHeader.getVolume().doubleValue();
        double newVolume = oldVolume - Arith.nullFilter(cartonDetail.getSku().getVolume());
        cartonHeader.setVolume((newVolume < 0 ? BigDecimal.ZERO : BigDecimal.valueOf((newVolume))));
        // 把实测值设置为计算值
        cartonHeader.setActualVolume(cartonHeader.getVolume());

        double oldGrossWeight = cartonHeader.getGrossWeight() == null ? 0 : cartonHeader.getGrossWeight().doubleValue();
        double newGrossWeight = oldGrossWeight - Arith.nullFilter(cartonDetail.getSku().getGrossweight());
        cartonHeader.setGrossWeight((newGrossWeight < 0 ? BigDecimal.ZERO : BigDecimal.valueOf((newGrossWeight))));
        // 把实测值设置为计算值
        cartonHeader.setActualGrossWeight(cartonHeader.getGrossWeight());
    }
	
	/**
	 * 删除某一箱后，需要检查定单是否还有装箱记录，修改do状态
	 * @param doHeader
	 */
	private void resetDoStatus(DeliveryOrderHeader doHeader) {
		String status = null;
		if(cartonHeaderDAO.countByDoId(doHeader.getId()) == 0){
			status = Constants.DoStatus.ALLSORTED.getValue();
		}else{
			status = Constants.DoStatus.PART_CARTON.getValue();
		}
		if(doHeader.getStatus().equals(status)){
			return;
		}
		doHeader.setStatus(status);
		doHeaderDAO.update(doHeader);
		if(Constants.DoStatus.ALLSORTED.getValue().equals(status)){
			doDetailDAO.updateDoDetailStatusByDoId(doHeader.getId(), Constants.DoStatus.ALLSORTED.getValue());
			doHeaderDAO.getSession().flush();
		}
	}
	
	/**
	 * do为装箱中或者装箱完成时状态回退调用的方法
	 * @param doId 发货单Id号
	 * @param startStatus 回退前DO最初始的状态
	 * @param fromStatus 回退前的状态
	 * @param toStatus 回退后的状态
	 */
	@Override
	@Transactional
	public void rollback(Long doId, String startStatus, String fromStatus, String toStatus) {
		boolean isFromStatusMatch = fromStatus.equals(Constants.DoStatus.PART_CARTON.getValue()) || fromStatus.equals(Constants.DoStatus.ALL_CARTON.getValue());
		if (!isFromStatusMatch) {
			return;
		}
		if (!toStatus.equals(Constants.DoStatus.ALLSORTED.getValue())) {
			return;
		}
		//释放容器
		releaseContainer(doId);
		
		//回退序列号
		rollBackSerial(doId);
		
		rePickContainerService.bind4Carton(doId);
		
		//删除装箱头信息
		cartonService.phyDelDoCartonInfoByDoId(doId);
		
		//修改定单状态
		doHeaderDAO.updateStatus(doId, Constants.DoStatus.ALLSORTED.getValue());
		doDetailDAO.updateDoDetailStatusByDoId(doId, Constants.DoStatus.ALLSORTED.getValue());
	}
	
	   /**
     * 回退时处理DO序列号
     * @param doHeaderId
     */
    @Transactional
    @Override
    public void rollBackSerial(Long doHeaderId) {
            //查询所有已消耗掉的序列号
            List<TrsSerialLog> trsSerials = trsSerialLogDAO.findSerialNoByDocAndTrs(doHeaderId, Constants.DocType.SO.getValue(), null);
            //还原序列号库存。
            for(TrsSerialLog log : trsSerials){
                restoreStockSerial(log);
            }
            //删除序列号信息，这时transactionId对应的值为cartonId
            trsSerialLogDAO.removeByDoc(doHeaderId, Constants.DocType.SO.getValue());
    }
	
	/**
	 * 状态回退至分拣完成时如装箱头绑定容器则释放容器
	 * @param doId
	 */
	private void releaseContainer(Long doId) {
	    List<Container> containers = containerMgntService.getCartonedDoCantainer(doId);
	    for (Container tempContainer : containers) {
	        containerMgntService.release(tempContainer);
	    }
	}
}
