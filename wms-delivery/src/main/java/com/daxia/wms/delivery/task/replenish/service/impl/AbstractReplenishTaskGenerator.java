package com.daxia.wms.delivery.task.replenish.service.impl;


import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.log.LogUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.StockType;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.delivery.task.replenish.dto.ReplGenterateDTO;
import com.daxia.wms.delivery.task.replenish.dto.ReplenishSkuDTO;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishTask;
import com.daxia.wms.delivery.task.replenish.service.ReplHeaderService;
import com.daxia.wms.master.dto.SkuDTO;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.entity.PackageInfoDetail;
import com.daxia.wms.master.entity.SkuPickLocAssn;
import com.daxia.wms.master.service.LocationService;
import com.daxia.wms.master.service.PackageInfoDetailService;
import com.daxia.wms.master.service.SkuCache;
import com.daxia.wms.stock.stock.dto.Stock2AllocateDTO;
import com.daxia.wms.stock.stock.dto.StockDTO;
import com.daxia.wms.stock.stock.entity.StockBatchAtt;
import com.daxia.wms.stock.stock.service.IOperator;
import com.daxia.wms.stock.stock.service.StockBatchAttService;
import com.daxia.wms.stock.stock.service.StockQueryService;
import com.daxia.wms.stock.stock.service.StockService;
import com.daxia.wms.stock.task.dao.TrsTaskDAO;
import org.jboss.seam.annotations.In;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 补货任务生成器接口的部分实现
 */
@lombok.extern.slf4j.Slf4j
public abstract class AbstractReplenishTaskGenerator {

    public static final int SCALE = 5;

    @In
    protected TrsTaskDAO trsTaskDAO;

    @In
    private SequenceGeneratorService sequenceGeneratorService;

    @In
    private StockService stockService;

    @In
    private StockQueryService stockQueryService;

    @In
    private SkuCache skuCache;

    @In("replenishOperator")
    private IOperator replenishOperator;

    @In
    private PackageInfoDetailService packageInfoDetailService;

    @In
    private LocationService locationService;

    @In
    protected StockBatchAttService stockBatchAttService;

    private static final BigDecimal DEFAULT_REPL_UPLIMIT = BigDecimal.valueOf(1000);

    /**
     * 考虑箱规计算补货任务的具体补货数量
     *
     * @param supplyMost 在不超过当前库位该sku最高存货的基础上，还可以补充该sku的最大数量
     * @param actQtyUnit 当前操作欲补充的数量
     * @param sku
     * @return
     */
    protected BigDecimal countTaskQty(BigDecimal supplyMost, BigDecimal actQtyUnit, SkuDTO sku, Boolean isPackage) {
        BigDecimal supplyMostUnit = supplyMost;
        if (!isPackage) { //非包装需求数，转换成包装数量
            BigDecimal packingQty = packageInfoDetailService.findMainPackage(sku.getId()).getQty();
            if (packingQty != null && packingQty.compareTo(BigDecimal.ZERO) > 0) {
                supplyMostUnit = supplyMost.divide(packingQty, 0, BigDecimal.ROUND_UP);
                // .multiply(packingQty);
            }
        }

        return actQtyUnit.compareTo(supplyMostUnit) >= 0 ? supplyMostUnit : actQtyUnit;
    }

    /**
     * 获取指定库位某sku的可补货的最大数量=拣货位最大补货数量-在途数-当前库存
     *
     * @param pickLoc
     * @param replSku
     * @return
     */
    public BigDecimal getReplenishQty(SkuPickLocAssn pickLoc, ReplenishSkuDTO replSku) {
        //如果是需要整箱补货,获取最大补货箱数
        PackageInfoDetail packageInfoDetail = packageInfoDetailService.findMainPackage(replSku.getSkuId());

        // 可补货的最大数量 = 拣货位最大补货数量 - 在途数 - 当前库存
        BigDecimal uplimit = pickLoc.getUplimit() != null ? pickLoc.getUplimit() : DEFAULT_REPL_UPLIMIT;
        BigDecimal canReplQty = uplimit.subtract(stockQueryService.getQtyForReplenish(pickLoc.getLocId(), pickLoc.getSkuId()));
        BigDecimal canReplQtyUnit = canReplQty.divide(packageInfoDetail.getQty(), 0, BigDecimal.ROUND_CEILING);

        if (replSku.getReplQtyUnit().compareTo(BigDecimal.ZERO) > 0) {
            if (packageInfoDetail != null) {
                return canReplQtyUnit.compareTo(replSku.getReplQtyUnit()) > 0 ? canReplQtyUnit : replSku.getReplQtyUnit();
            } else {
                return BigDecimal.ZERO;
            }
        } else {
            //库位最大补货数，订单需求数取最大
            canReplQty = canReplQty.compareTo(replSku.getReplQty()) > 0 ? canReplQty : replSku.getReplQty();
            //补货数按包装整数倍来补货；
            if (packageInfoDetail != null && packageInfoDetail.getQty().compareTo(BigDecimal.ONE) > 0) {
                canReplQty = canReplQty.divide(packageInfoDetail.getQty(), 0, BigDecimal.ROUND_CEILING).multiply(packageInfoDetail.getQty());
            } else {
                canReplQty = canReplQty;
            }
            return canReplQty;
        }
    }

    /**
     * 扣去库存，生成对应补货任务，返回 调用该方法 新增的task数量
     *
     * @param replGenterateDTO
     */
    @Loggable
    protected int createReplTask(ReplGenterateDTO replGenterateDTO) {
        log.debug("Create Repl Task:[repQty: {}, skuId: #1, defaultLocId: #2]", replGenterateDTO.getSupplyMost(), replGenterateDTO.getSku().getId(), replGenterateDTO.getDefaultLocId());
        int taskCount = 0;
        if (replGenterateDTO.getSupplyMost().compareTo(BigDecimal.ZERO) <= 0) {
            return 0;
        }

        Boolean isPackage = replGenterateDTO.getPackage();

        // 遍历结果更新库存,生成任务
        for (Stock2AllocateDTO replResult : replGenterateDTO.getStockList()) {
            if (replResult == null) {
                continue;
            }
            //防止做了移库操作之后,从自身补货到自身的情况
            if (replResult.getLocId().equals(replGenterateDTO.getDefaultLocId())) {
                continue;
            }
            // 实际可分配库存
            BigDecimal actQtyUnit = replResult.getActQtyUnit();
            if (actQtyUnit.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            // 闲时补货 需要控制 任务生成的总量<= 最大单数*(最大任务数/单)
            if (Constants.ReplType.XP.getValue().equals(replGenterateDTO.getReplType()) && (taskCount + replGenterateDTO.getTaskMadeCount() >= getMaxTaskNumCfg())) {
                break;
            }
            BigDecimal packageQty = BigDecimal.ONE;
            if (Constants.ReplType.JP.getValue().equals(replGenterateDTO.getReplType())) {
                packageQty = packageInfoDetailService.findMainPackage(replGenterateDTO.getSku().getId()).getQty();
            }
            if (Constants.ReplType.XP.getValue().equals(replGenterateDTO.getReplType())) {
                isPackage = replResult.getPackageQty().compareTo(BigDecimal.ONE) > 0;
                packageQty = replResult.getPackageQty();
            }
            BigDecimal unitQty = countTaskQty(replGenterateDTO.getSupplyMost(), actQtyUnit, replGenterateDTO.getSku(), isPackage);

            // 库存变更
            StockDTO stockDto = new StockDTO();
            stockDto.setFmLocId(replResult.getLocId());
            stockDto.setToLocId(replGenterateDTO.getDefaultLocId());
            stockDto.setFmLotId(replResult.getLotId());
            stockDto.setToLotId(stockDto.getFmLotId());
            stockDto.setPlanQty(unitQty.multiply(packageQty));
            stockDto.setPlanQtyUnit(unitQty);
            stockDto.setPackQty(packageQty);
            stockDto.setSkuId(replGenterateDTO.getSku().getId());

            stockDto.setLpnNo(replResult.getLpnNo());
            // RS库存ID
            stockDto.setStockLpnId(replResult.getStockId());
            stockDto.setGenerateFlag(Boolean.TRUE);
            replenishOperator.setStockDto(stockDto);
            Map<String, Long> map = stockService.operateStock(replenishOperator);

            // 生成补货任务
            saveTask(replResult, replGenterateDTO.getDefaultLocId(), stockDto.getPlanQty(), stockDto.getPlanQtyUnit(), map.get(StockType.STOCK_ALLOC.getValue()), map.get(StockType.STOCK_PENDING.getValue()),
                    replGenterateDTO.getReplType(), replGenterateDTO.getDoType(), replGenterateDTO.getEarliestPlanShipTime(), replGenterateDTO.getAuto(), isPackage, stockDto.getFmLotId());
            taskCount++;

            // 更新已分配数量
            replGenterateDTO.setSupplyMost(isPackage ? replGenterateDTO.getSupplyMost().subtract(unitQty) : replGenterateDTO.getSupplyMost().subtract(unitQty.multiply(packageQty)));
            if (replGenterateDTO.getSupplyMost().compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
        }
        return taskCount;
    }

    private int getMaxTaskNumCfg() {
        Integer maxCountCfg = SystemConfig.getConfigValueInt(ReplHeaderService.REPLENISH_HEADER_MAXNUM, ParamUtil.getCurrentWarehouseId());
        int maxHeadNum = maxCountCfg == null ? 100 : maxCountCfg.intValue();// 默认值取100，即一个很大的值，相当于不限制数量
        Integer maxDtlCountCfg = SystemConfig.getConfigValueInt(ReplHeaderService.MAXIMUM_PER_REPL_HEADER, ParamUtil.getCurrentWarehouseId());
        int maxDtlNum = maxDtlCountCfg == null ? 100 : maxDtlCountCfg.intValue();// 默认值取1000，即一个很大的值，相当于不限制数量
        return maxHeadNum * maxDtlNum;
    }

    /**
     * 生成一个ReplenishTask实体并持久化
     *
     * @param doDetailAllocateResult
     * @param defaultLocId
     * @param qtyUnit
     * @param fmStockId
     * @param toStockId
     * @param replType
     */
    @Loggable
    protected void saveTask(Stock2AllocateDTO doDetailAllocateResult, Long defaultLocId, BigDecimal qty, BigDecimal qtyUnit, Long fmStockId, Long toStockId, String replType, String doType, Date planShipTime, Boolean isAuto, Boolean isPackage, Long lotId) {
        ReplenishTask task = new ReplenishTask();
        task.setTaskNo(sequenceGeneratorService.generateSequenceNo(Constants.SequenceName.RP_TASKNO.getValue(), ParamUtil.getCurrentWarehouseId()));
        task.setPriority(Constants.Priority.ZERO.getValue().longValue());
        StockBatchAtt sba = stockBatchAttService.findStockBatchAttBylotId(lotId);
//        task.setPackId(Long.valueOf(-1));
        task.setPackageDetailId(Long.valueOf(sba.getLotatt07()));
        PackageInfoDetail packageInfoDetail = packageInfoDetailService.get(Long.valueOf(sba.getLotatt07()));
        task.setPackQty(packageInfoDetail.getQty());
        task.setSkuId(doDetailAllocateResult.getSkuId());
        task.setMerchantId(doDetailAllocateResult.getLotAtt06() == null ? null : Long.valueOf(doDetailAllocateResult.getLotAtt06()));
        task.setLotId(lotId);
        task.setFmLocId(doDetailAllocateResult.getLocId());
        task.setPlanLocId(defaultLocId);
        task.setLpnNo(doDetailAllocateResult.getLpnNo());
        task.setToLpnNo("*");
        task.setTaskType(Constants.TaskType.RP.getValue());
        task.setTaskStatus(Constants.TaskStatus.INITIALIZED.getValue());
        // 总零散数量
        task.setQty(qty);
        task.setQtyUnit(qtyUnit);
        //总数量= 包装单位*包装数量
        //是否整箱补货
        task.setIsUnit(isPackage ? YesNo.YES.getValue() : YesNo.NO.getValue());
        task.setFmStockId(fmStockId);
        task.setToStockId(toStockId);
        task.setReplType(replType);
        task.setIsAuto(isAuto ? YesNo.YES.getValue() : YesNo.NO.getValue());
        if (StringUtil.isNotEmpty(doType)) {
            task.setDoType(doType);
            task.setBySo(Integer.valueOf(1));
        } else {
            task.setBySo(Integer.valueOf(0));
        }
        task.setPlanShipTime(planShipTime);
        trsTaskDAO.save(task);
    }

    /**
     * 判断存储区库存是否足够
     *
     * @param stockList
     * @param repQty
     * @return
     */
    protected boolean isStockEnough(List<Stock2AllocateDTO> stockList, BigDecimal repQty) {
        BigDecimal actQtySum = BigDecimal.ZERO;
        for (Stock2AllocateDTO doDetailAllocateResult : stockList) {
            actQtySum = actQtySum.add(doDetailAllocateResult.getActQty());
            if (actQtySum.compareTo(repQty) >= 0) {
                return true;
            }
        }
        return false;
    }
}