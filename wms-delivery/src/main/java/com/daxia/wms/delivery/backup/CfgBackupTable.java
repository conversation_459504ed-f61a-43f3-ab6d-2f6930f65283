package com.daxia.wms.delivery.backup;

import javax.persistence.*;

/**
 * Created by qicen on 2016/10/12.
 */
@Entity
@Table(name = "cfg_backup_table")
@MappedSuperclass
@lombok.extern.slf4j.Slf4j
public class CfgBackupTable {

    @Id
    @GeneratedValue(strategy=GenerationType.AUTO)
    @Column(name = "id", nullable = false)
    private int id;

    @Column(name = "group")
    private String group;

    @Column(name = "ordinal")
    private String ordinal;

    @Column(name = "table_name")
    private String tableName;

    @Column(name = "backup_where")
    private String backupWhere;

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getBackupWhere() {
        return backupWhere;
    }

    public void setBackupWhere(String backupWhere) {
        this.backupWhere = backupWhere;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getOrdinal() {
        return ordinal;
    }

    public void setOrdinal(String ordinal) {
        this.ordinal = ordinal;
    }
}
