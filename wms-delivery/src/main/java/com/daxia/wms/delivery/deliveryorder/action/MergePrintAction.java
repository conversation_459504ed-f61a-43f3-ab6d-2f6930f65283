package com.daxia.wms.delivery.deliveryorder.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.*;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.dto.MergePrintDTO;
import com.daxia.wms.delivery.deliveryorder.filter.MergePrintFilter;
import com.daxia.wms.delivery.deliveryorder.service.MergePrintService;
import com.daxia.wms.delivery.print.helper.WaybillPrintHelper;
import com.daxia.wms.master.entity.BusinessCustomer;
import com.daxia.wms.master.service.BusinessCustomerService;
import com.daxia.wms.print.dto.PrintData;
import com.google.gson.Gson;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.Synchronized;

import java.sql.Timestamp;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Name("com.daxia.wms.delivery.mergePrintAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@Synchronized(timeout = 30000)
@lombok.extern.slf4j.Slf4j
public class MergePrintAction extends PagedListBean<MergePrintDTO> {

    @In
    private MergePrintService mergePrintService;

    private MergePrintFilter mergePrintFilter;

    private MergePrintDTO mergePrintDTO;

    private String printData = "[]";

    private String printContent = "";

    private String customerName;

    @In
    private BusinessCustomerService businessCustomerService;

    public MergePrintAction() {
        super();
        this.mergePrintFilter = new MergePrintFilter();
        mergePrintFilter.setDoCreateTimeFm(DateUtil.dateAdd("dd", DateUtil.getNowTime(), -7));
    }

    @Override
    public void query() {
        DataPage<MergePrintDTO> dataPage = mergePrintService.queryPageInfo(mergePrintFilter,
                getStartIndex(), getPageSize());
        this.populateValues(dataPage);
    }

    public void toAdd() {
        mergePrintDTO = new MergePrintDTO();
        List<Long> selectedDOIdList = ListUtil.convert(this.getSelectedRowList(), new ListUtil.Convertor<Object, Long>() {
            @Override
            public Long convert(Object id) {
                return (Long) id;
            }
        });

        if (ListUtil.isNullOrEmpty(selectedDOIdList)) {
            return;
        }
        MergePrintFilter filter = new MergePrintFilter();
        filter.setDoIdList(selectedDOIdList);
        DataPage<MergePrintDTO> list = mergePrintService.queryPageInfo(filter, 0, 1000);
        Set<Integer> isPrintedList = new HashSet<Integer>();
        Set<Long> carrierIds = new HashSet<Long>();
        Set<String> groupNoList = new HashSet<String>();
        Integer cartonNum = 0;
        Integer groupDoNum = 0;
        for (MergePrintDTO dto : list.getDataList()) {
            isPrintedList.add(dto.getIsPrinted());
            if (dto.getCarrierId() != null) {
                carrierIds.add(dto.getCarrierId());
            }
            if (StringUtil.isNotBlank(dto.getGroupNo())) {
                groupNoList.add(dto.getGroupNo());
                groupDoNum = dto.getDoNum();
            }
            cartonNum += dto.getCartonNum();
        }
        if (isPrintedList.size() > 1) {
            throw new BusinessException("没打印和已打印不可混打!");
        }
        if (Constants.YesNo.YES.getValue().equals(isPrintedList.iterator().next())) {
            if (groupNoList.size() > 1) {
                throw new BusinessException("合单打印号必须相同才能重打!");
            }
            if (groupDoNum != selectedDOIdList.size()) {
                throw new BusinessException("重打请按合单打印号码选择!");
            }
        }
        mergePrintDTO.setIsPrinted(list.getDataList().get(0).getIsPrinted());
        mergePrintDTO.setCartonNum(cartonNum);
        mergePrintDTO.setDoNum(list.getDataList().size());
        if (carrierIds.size() == 1) {
            mergePrintDTO.setCarrierId(list.getDataList().get(0).getCarrierId());
        } else {
            mergePrintDTO.setCarrierId(null);
        }
        mergePrintDTO.setWayBillNum(cartonNum);
        mergePrintDTO.setPrintData(list.getDataList().get(0).getPrintData());
        mergePrintDTO.setDoIdList(selectedDOIdList);
    }

    private void validate() {
        List<Long> selectedDOIdList = ListUtil.convert(this.getSelectedRowList(), new ListUtil.Convertor<Object, Long>() {
            @Override
            public Long convert(Object id) {
                return (Long) id;
            }
        });

        if (ListUtil.isNullOrEmpty(selectedDOIdList)) {
            return;
        }
        MergePrintFilter filter = new MergePrintFilter();
        filter.setDoIdList(selectedDOIdList);
        DataPage<MergePrintDTO> list = mergePrintService.queryPageInfo(filter, 0, 1000);
        Set<Integer> isPrintedList = new HashSet<Integer>();
        Set<Long> carrierIds = new HashSet<Long>();
        Set<String> groupNoList = new HashSet<String>();
        Integer cartonNum = 0;
        Integer groupDoNum = 0;
        for (MergePrintDTO dto : list.getDataList()) {
            isPrintedList.add(dto.getIsPrinted());
            if (dto.getCarrierId() != null) {
                carrierIds.add(dto.getCarrierId());
            }
            if (StringUtil.isNotBlank(dto.getGroupNo())) {
                groupNoList.add(dto.getGroupNo());
                groupDoNum = dto.getDoNum();
            }
            cartonNum += dto.getCartonNum();
        }
        if (isPrintedList.size() > 1) {
            throw new BusinessException("没打印和已打印不可混打!");
        }
        if (Constants.YesNo.YES.getValue().equals(isPrintedList.iterator().next())) {
            if (groupNoList.size() > 1) {
                throw new BusinessException("合单打印号码必须相同才能重打!");
            }
            if (groupDoNum != selectedDOIdList.size()) {
                throw new BusinessException("请按合单打印号码选择数据重打!");
            }
        }
    }

    public void cofirmMergePrint() {
        printData = "[]";
        printContent = "";
        validate();
        Integer defaultCarrierId = SystemConfig.getConfigValueInt("dubhe.default.carrier.id", ParamUtil.getCurrentWarehouseId());
        if (mergePrintDTO.getCarrierId() == null) {
            throw new BusinessException("请选择配送商");
        }
        if (mergePrintDTO.getCarrierId().equals(defaultCarrierId)) {
            throw new BusinessException("不能选择默认配送商");
        }
        List<PrintData> resultList = mergePrintService.execute(mergePrintDTO);
        printData = new Gson().toJson(resultList);
        printContent = WaybillPrintHelper.getPrintJs(resultList);
    }

    public void receiveSelectBusinessCustomer(Long businessCustomerId) {
        BusinessCustomer businessCustomer = null;
        if (businessCustomerId == null) {
            businessCustomer = new BusinessCustomer();
        } else {
            businessCustomer = businessCustomerService.getBusinessCustomer(businessCustomerId);
        }
        this.mergePrintFilter.setBusinessCustomerId(businessCustomerId);
        this.setCustomerName(businessCustomer.getCustomerName());

    }

    public void clearBusinessCustomer() {
        this.mergePrintFilter.setBusinessCustomerId(null);
        this.setCustomerName(null);
    }

    public MergePrintFilter getMergePrintFilter() {
        return mergePrintFilter;
    }

    public void setMergePrintFilter(MergePrintFilter mergePrintFilter) {
        this.mergePrintFilter = mergePrintFilter;
    }

    public MergePrintDTO getMergePrintDTO() {
        return mergePrintDTO;
    }

    public void setMergePrintDTO(MergePrintDTO mergePrintDTO) {
        this.mergePrintDTO = mergePrintDTO;
    }

    public String getPrintData() {
        return printData;
    }

    public void setPrintData(String printData) {
        this.printData = printData;
    }

    public String getPrintContent() {
        return printContent;
    }

    public void setPrintContent(String printContent) {
        this.printContent = printContent;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
}
