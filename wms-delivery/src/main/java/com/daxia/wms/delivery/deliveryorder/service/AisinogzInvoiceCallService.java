package com.daxia.wms.delivery.deliveryorder.service;

import com.daxia.wms.delivery.invoice.entity.InvoiceDetail;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;

public interface AisinogzInvoiceCallService {
	/**
	 * 异步开票
	 * @return
	 */
	void sendInvEli(InvoiceHeader invoiceHeader, boolean isSync);

	/**
	 * 查询开票信息
	 * @return
	 */
	public void bind(InvoiceHeader invoiceHeader);
	

	public  Long queryEliStock();

	void chInvoice(InvoiceHeader invoiceHeader, boolean isSync);
}
