package com.daxia.wms.delivery.print.service.impl;

import com.daxia.framework.common.service.ReportGenerator;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.constants.Constants.YesNo;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.PrintInvoiceDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.invoice.dao.InvoiceDao;
import com.daxia.wms.delivery.invoice.entity.InvoiceDetail;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;
import com.daxia.wms.delivery.invoice.service.InvoiceNoService;
import com.daxia.wms.delivery.invoice.service.InvoiceService;
import com.daxia.wms.delivery.print.dto.DoInvoicePrintDTO;
import com.daxia.wms.delivery.print.dto.InvoiceHeaderDTO;
import com.daxia.wms.delivery.print.dto.RowDTO;
import com.daxia.wms.delivery.print.helper.PrintInvoiceHelper;
import com.daxia.wms.delivery.print.service.PrintInvoiceService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.master.entity.Printer;
import com.daxia.wms.master.service.PrinterService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.print.PrintConstants.PrintInvoicePos;
import com.daxia.wms.print.PrintConstants.PrintType;
import com.daxia.wms.print.dto.PrintCfg;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.print.service.PrintLogService;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.jboss.seam.log.Log;
import org.jboss.seam.security.Identity;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *  卷式发票打印业务实现类
 */
@Name("com.daxia.wms.delivery.printInvoiceService")
@lombok.extern.slf4j.Slf4j
public class PrintInvoiceServiceImpl implements PrintInvoiceService {
    private static final String BINDED_INVOICE_USED_UP = "-1";//绑定发票已用完

    @In
    private Identity identity;
    @In
    private PrinterService printerService;

    @In
    private InvoiceService invoiceService;

    @In
    private InvoiceNoService invoiceNoService;

    @In
    private WaveService waveService;

    @In
    private InvoiceDao invoiceDao;

    @In
    private ReportGenerator reportGenerator;

    @In
    private DeliveryOrderService deliveryOrderService;

    @In
    private WarehouseService warehouseService;

    @In
    private PrintInvoiceDAO printInvoiceDAO;



    @In
    private PrintLogService printLogService;

    @Override
    @Transactional
    public void bindNewInvoice(String printerCode, String invoiceCode, String firstInvoiceNo, String lastInvoiceNo) {
        Printer printer = validatePrinter(printerCode);
        invoiceService.checkInvoiceInfo(firstInvoiceNo, lastInvoiceNo, invoiceCode);
        if (null != printer) {
            printer.setInvoiceCode(invoiceCode);
            printer.setInvoiceNoCurrent(firstInvoiceNo);
            printer.setInvoiceNoFrom(firstInvoiceNo);
            printer.setInvoiceNoTo(lastInvoiceNo);
            printerService.saveOrUpdate(printer);
        }
    }

    @Override
    public Printer validatePrinter(String printerCode) {
        //校验打印机编号是否为空
        if (StringUtil.isEmpty(printerCode)) {
            throw new DeliveryException(DeliveryException.PRINTER_CODE_IS_NULL);
        }
        //校验打印机是否存在
        Printer printer =printerService.findPrinterByCode(printerCode);
        if (null == printer) {
            throw new DeliveryException(DeliveryException.PRINTER_IS_NOT_EXIST);
        }
        //校验打印机系统状态是否可用
        if (YesNo.NO.getValue().equals(printer.getStatus())) {
            throw new DeliveryException(DeliveryException.PRINTER_IS_NOT_AVALIABLE);
        }
        return printer;
    }

    @Override
    @Transactional
    public void updateCurInvoiceNo(String printerCode, String curInvoiceNo) {
        Printer printer = validatePrinter(printerCode);
        if (StringUtil.isEmpty(printer.getInvoiceNoCurrent())) {
            throw new DeliveryException(DeliveryException.PRINTER_IS_NOT_BIND_INVOICE);
        }
        if(StringUtil.isNotEmpty(curInvoiceNo)) {
            if(curInvoiceNo.compareTo(printer.getInvoiceNoFrom()) < 0 || curInvoiceNo.compareTo(printer.getInvoiceNoTo()) >0) {
                throw new DeliveryException(DeliveryException.PRINTER_CURRENT_INVOICE_NO_ERROR);
            }
        }
        printer.setInvoiceNoCurrent(curInvoiceNo);
        printerService.saveOrUpdate(printer);
    }

    @Override
    @Transactional
    public PrintData printInvoice(String waveNo, String printerCode, Boolean isDirectly) {
        //校验打印机
        Printer printer = this.validatePrinter(printerCode);
        if (StringUtil.isEmpty(printer.getInvoiceNoCurrent())) {
            throw new DeliveryException(DeliveryException.PRINTER_IS_NOT_BIND_INVOICE);
        }
        // 绑定发票已用完
        if (BINDED_INVOICE_USED_UP.equals(printer.getInvoiceNoCurrent())) {
            throw new DeliveryException(DeliveryException.BINDED_INVOICE_USED_UP_PLEANSE_CHANGE);
        }
        WaveHeader wave = waveService.getWaveHeaderByWaveNum(waveNo);
        if (null == wave) {
            throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
        }

        if (isDirectly) {
            // 波次状态 等于 拣货完成,才能打印
            if (!Constants.WaveStatus.ALLPICKED.getValue().equals(wave.getWaveStatus())) {
                throw new DeliveryException(DeliveryException.WAVE_STATUS_CAN_NOT_AUTOPRINT_INVOICE);
            }
            // 判断波次发票打印状态
            if (null != wave.getInvoicePrintFlag() && wave.getInvoiceFlag() == 1
                    && wave.getInvoicePrintFlag().intValue() != 0) {
                throw new DeliveryException(DeliveryException.WAVE_INVOICE_PRINT_STATUS_ERROR);
            }
        } else {
            // 波次状态大于等于 拣货完成
            if (Constants.WaveStatus.ALLPICKED.getValue().compareTo(wave.getWaveStatus()) > 0) {
                throw new DeliveryException(DeliveryException.WAVE_STS_ERRER);
            }
            // 判断波次发票打印状态
            if (null != wave.getInvoicePrintFlag() && wave.getInvoiceFlag() == 1
                    && wave.getInvoicePrintFlag().intValue() != 0 && wave.getInvoicePrintFlag().intValue() != 2) {
                throw new DeliveryException(DeliveryException.WAVE_INVOICE_PRINT_STATUS_ERROR);
            }
        }

        //判断是否需要发票
        if (null == wave.getInvoiceFlag()  || wave.getInvoiceFlag() == 0) {
            throw new DeliveryException(DeliveryException.WAVE_HAS_NO_INVOICE);
        }

        // 校验当前发票模式是否是卷式发票模式
        Integer version = this.getPrintInvoiceVersion();
        if(version ==0) {
            throw new DeliveryException(DeliveryException.NOW_IS_NOT_ROLL_TYPE_INVOICE);
        }

        PrintData pd = bindAndPrint(waveNo, printer, (isDirectly ? PrintInvoicePos.SORT_N : PrintInvoicePos.SORT_N_RE));
        PrintCfg printCfg = new PrintCfg();
        printCfg.setPrinter(printer);
        pd.setPrintCfg(printCfg);;
        return pd;
    }


    private PrintData bindAndPrint(String waveNo, Printer printer, PrintInvoicePos pos) {
        long start = System.currentTimeMillis();
        PrintData printData = new PrintData();
        List<Long> bindedInvoiceIdList = new ArrayList<Long>();
        List<InvoiceNo> invoiceNos = invoiceNoService.getInvoiceNoList(printer.getInvoiceCode(), printer.getInvoiceNoCurrent(), printer.getInvoiceNoTo());
        List<InvoiceHeader> invoiceList = invoiceService.getInvoiceList4PrintByWave(waveNo);

        int i = 0;
        for (InvoiceHeader invoiceHeader : invoiceList) {
            if(i >= invoiceNos.size()) {
                break;
            }
            //绑定
            invoiceNoService.bind(invoiceHeader, invoiceNos.get(i++));
            bindedInvoiceIdList.add(invoiceHeader.getId());
        }
        invoiceDao.getSession().flush();
        invoiceDao.getSession().clear();

        // 如果发票不够，打完了要提示换发票
        if (invoiceNos.size() < invoiceList.size()) {
            //设置异常提示信息
            printData.setUserDefine1(DeliveryException.INVOICE_NO_NOT_ENOUGH_PLEANSE_CHANGE);
            //设置实际打印张数
            printData.setUserDefine2(String.valueOf(bindedInvoiceIdList.size()));
            //设置未打印张数
            printData.setUserDefine3(String.valueOf(invoiceList.size() - invoiceNos.size()));
        }

        if (log.isDebugEnabled()) {
            log.debug("Print Invoice: {}", new Gson().toJson(bindedInvoiceIdList));
        }

        Boolean printInNewWay = deliveryOrderService.needPrintInNewWay();
        if (printInNewWay) {
            // 开启配置项返回 DTO
            printData.setDtoList(printAsConfigOpen(bindedInvoiceIdList, printer.getCode(), pos));
        } else {
            //打印
            List<String> data = this.print(bindedInvoiceIdList, printer.getCode(), waveNo, pos);
            printData.setDataList(data);
        }

        increaseCurrentInvoiceNo(printer, bindedInvoiceIdList.size());
        long end = System.currentTimeMillis();
        if (log.isDebugEnabled()) {
            log.debug("{} Print Roll Invoice Costs: #1 ms", (printInNewWay ? "New" : "Old"), (end - start));
        }
        return printData;
    }

    /**
     * 当前发票号码增加指定数值（超过上限则置为-1）
     * @param printer
     * @param addNum
     */
    @Override
    @Transactional
    public void increaseCurrentInvoiceNo(Printer printer,  long addNum) {
        String curStr = printer.getInvoiceNoCurrent();
        String toStr = printer.getInvoiceNoTo();
        long max = Long.parseLong(toStr);
        long cur = Long.parseLong(curStr) + addNum;
        if (cur > max) {
            cur = -1L;
            printer.setInvoiceNoCurrent(String.valueOf(cur));
        } else {
            printer.setInvoiceNoCurrent(StringUtil.convertToString(cur, toStr.length(), "0"));
        }
        printerService.saveOrUpdate(printer);
    }

    /**
     * 开关开启调用新的取发票数据方法
     * @param idList
     * @param printerCode
     * @return
     */
    @Transactional
    public List<PrintReportDto> printAsConfigOpen(List<Long> idList, String printerCode, PrintInvoicePos pos) {
        if (idList.isEmpty()) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        List<InvoiceHeader> invoiceList = invoiceService.getInvoiceList4Print(idList);
        if (invoiceList.isEmpty()) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        long start = System.currentTimeMillis();
        String waveNo = "";
        List<PrintReportDto> invoiceHeaderDTOList = new ArrayList<PrintReportDto>();

        for (InvoiceHeader invoice : invoiceList) {
            //DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(invoice.getDoHeaderId());
            DoInvoicePrintDTO invoicePrintInfo = printInvoiceDAO.getInvoicePrintInfoOfDo(invoice.getDoHeaderId());
            if (StringUtil.isEmpty(waveNo)) {
                //WaveHeader wave = invoicePrintInfo.getWaveHeader();
                waveNo = printInvoiceDAO.getWaveNoByDoId(invoice.getDoHeaderId());
                //waveNo = (wave == null ? "" : wave.getWaveNo());
            }
            List<RowDTO> rows = new ArrayList<RowDTO>();
            int index = 0;
            List<InvoiceDetail> details = invoiceService.findInvoiceDetailsByHeaderId(invoice.getId());
            for (InvoiceDetail detail : details) {
                if (StringUtil.isEmpty(detail.getSkuDescr())) {//当发票明细的商品名称描述为空时，放弃该条明细
                    continue;
                }
                rows.add(buildInvoiceDetailDataInNewWay(detail,index));
                index = index + 1;
            }

            // 发票号码
            InvoiceHeaderDTO invoiceHeaderDTO = new InvoiceHeaderDTO();
            invoiceHeaderDTO.setInvoiceNo(invoice.getInvoiceNumber());
            invoiceHeaderDTO.setSoCode(invoice.getSoCode());
            invoiceHeaderDTO.setDoNo(invoicePrintInfo.getDoNo());
            invoiceHeaderDTO.setTotal(new DecimalFormat("###0.00").format(invoice.getInvoiceAmount()));
            invoiceHeaderDTO.setTotalRmb(StringUtil.numToRMBStr(invoice.getInvoiceAmount()));
            invoiceHeaderDTO.setReceiver(this.getOperateUser());
            invoiceHeaderDTO.setMachineNo(printerCode);
            invoiceHeaderDTO.setReceiveCom(invoice.getBranchName());
            invoiceHeaderDTO.setRegisterNum(invoice.getTaxNo());
            invoiceHeaderDTO.setDate(DateUtil.dateToString(DateUtil.getTodayDate(), "yyyy-MM-dd"));
            invoiceHeaderDTO.setPayer(invoice.getInvoiceTitle());
            invoiceHeaderDTO.setSortGridNo(invoicePrintInfo.getSortGridNo());
            invoiceHeaderDTO.setWaveNo(waveNo);
            invoiceHeaderDTO.setRowDtoList(rows);
            invoiceHeaderDTO.setDocId(invoice.getId());
            invoiceHeaderDTO.setDocNo(invoice.getInvoiceNumber());
            invoiceHeaderDTO.setRefNo(invoiceHeaderDTO.getDoNo());
            invoiceHeaderDTO.setOutRefNo(invoice.getSoCode());
            invoiceHeaderDTOList.add(invoiceHeaderDTO);
            long end = System.currentTimeMillis();
            // 记录发票打印日志
            printLogService.savePrintLog(invoiceHeaderDTO.getDocId(), invoice.getInvoiceNumber(), PrintType.INVOICE,
                    pos.getValue() + "_lodop", end - start, invoicePrintInfo.getDoNo(), invoicePrintInfo.getRefNo1());
        }
        this.setInvoicePrinted(idList);

        return invoiceHeaderDTOList;
    }

    @Override
    @Transactional
    public List<String> print(List<Long> idList, String printerCode, String waveNo, PrintInvoicePos pos) {
        if (idList.isEmpty()) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        List<InvoiceHeader> invoiceList = invoiceService.getInvoiceList4Print(idList);
        if (invoiceList.isEmpty()) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }

        List<String> dataList = new ArrayList<String>();
        for (InvoiceHeader invoice : invoiceList) {
            long start = System.currentTimeMillis();
            DoInvoicePrintDTO invoicePrintInfo = printInvoiceDAO.getInvoicePrintInfoOfDo(invoice.getDoHeaderId());
            if (StringUtil.isEmpty(waveNo)) {
                waveNo = deliveryOrderService.getWaveNoByDoId(invoice.getDoHeaderId());
            }
            List<RowDTO> rows = new ArrayList<RowDTO>();
            Map<String, Object> params = new HashMap<String, Object>();
            List<InvoiceDetail> details = invoiceService.findInvoiceDetailsByHeaderId(invoice.getId());
            for (InvoiceDetail detail : details) {
                if (StringUtil.isEmpty(detail.getSkuDescr())) {//当发票明细的商品名称描述为空时，放弃该条明细
                    continue;
                }
                rows.addAll(buildInvoiceDetailData(detail));
            }
            // 发票号码
            params.put("invoiceNo", "发票号:" + invoice.getInvoiceNumber());
            params.put("soCode", "订单号:" + invoice.getSoCode());
            params.put("doNo", "发货单号:" + invoicePrintInfo.getDoNo());
            params.put("total", new DecimalFormat("###0.00").format(invoice.getInvoiceAmount()));
            params.put("totalRmb", StringUtil.numToRMBStr(invoice.getInvoiceAmount()));
            params.put("receiver", this.getOperateUser()); //invoice.getBranchName()
            params.put("machineNo", printerCode);
            params.put("receiveCom", invoice.getBranchName());
            params.put("registerNum",  invoice.getTaxNo());
            params.put("date", DateUtil.dateToString(DateUtil.getTodayDate(), "yyyy-MM-dd"));
            params.put("payer", invoice.getInvoiceTitle());
            params.put("sortGridNo", invoicePrintInfo.getSortGridNo());
            params.put("waveNo", waveNo);
            dataList.addAll(reportGenerator.builtPrintDataNoSub("invoice/invoiceRoll", params, rows));
            long end = System.currentTimeMillis();
            // 记录发票打印日志
            printLogService.savePrintLog(invoice.getId(), invoice.getInvoiceNumber(), PrintType.INVOICE,
                    pos.getValue() + "_iReport", end - start, invoicePrintInfo.getDoNo(), invoicePrintInfo.getRefNo1());
        }
        this.setInvoicePrinted(idList);
        return dataList;
    }

    public String getOperateUser() {
        String userName = null;
        if (identity != null && identity.getCredentials() != null) {
            userName = identity.getCredentials().getUsername();
        }
        if (StringUtil.isEmpty(userName)) {
            throw new DeliveryException(DeliveryException.GET_CUR_OPERATE_USER_FAILURE);
        }
        return userName;
    }

    /**
     *
     * 开启配置项 In new way（ 这里无需返回2个rowDto）
     * @param detail
     * @return
     */
    private RowDTO buildInvoiceDetailDataInNewWay(InvoiceDetail detail,int index) {
        RowDTO rowDTO = new RowDTO();
        rowDTO.setRow1(detail.getSkuDescr().length()> 26 ?
                detail.getSkuDescr().substring(0, 26) : detail.getSkuDescr());
        rowDTO.setTopOffset((49.5 + index * 2 *3.5) + "mm");
        if (detail.getSkuDescr().length() >= 26) {
            rowDTO.setRow2(detail.getSkuDescr().substring(26));
        } else {
            rowDTO.setRow2("");
        }

        if (rowDTO.getRow2().length() > 8) {
            rowDTO.setRow2(rowDTO.getRow2().substring(0, 8));
        } else if (rowDTO.getRow2().length() < 8) {
            rowDTO.setRow2(rowDTO.getRow2());
        }
        DecimalFormat df = new DecimalFormat("###0.00");
        rowDTO.setUnitPrice(df.format(detail.getPrice() == null ? BigDecimal.ZERO : detail.getPrice()));
        rowDTO.setQty(String.valueOf(detail.getQty() == null ? 0 : detail.getQty()));
        rowDTO.setTotalPrice(df.format(detail.getAmount() == null ? BigDecimal.ZERO : detail.getAmount()));
        rowDTO.setTopOffset((52.5 + index * 2 *3.5) + "mm");
        return rowDTO;
    }

    private List<RowDTO> buildInvoiceDetailData(InvoiceDetail detail) {
        List<RowDTO> itemData = new ArrayList<RowDTO>();

        Integer firstWidth = SystemConfig.getConfigValueInt("print.invoice.firstWidth", ParamUtil.getCurrentWarehouseId());
        Integer secWidth = SystemConfig.getConfigValueInt("print.invoice.secWidth", ParamUtil.getCurrentWarehouseId());
        int pos1 = PrintInvoiceHelper.cal(detail.getSkuDescr(), firstWidth == null ? 232 : firstWidth.intValue()); //232
        String row1Name = "";
        if (pos1 == detail.getSkuDescr().length()) {
            row1Name = detail.getSkuDescr();
        } else {
            row1Name = detail.getSkuDescr().substring(0, pos1);
        }
        RowDTO row1 = new RowDTO(row1Name);
        itemData.add(row1);
        String row2Name = "";
        row2Name = PrintInvoiceHelper.calAndFillWithBlank(detail.getSkuDescr().substring(pos1), secWidth == null ? 60 : secWidth.intValue(), true);//60
        DecimalFormat df = new DecimalFormat("###0.00");
        String unitPriceStr =  df.format(detail.getPrice() == null ? BigDecimal.ZERO : detail.getPrice());
        String qtyStr = String.valueOf(detail.getQty() == null ? 0 : detail.getQty());
        String totalPriceStr = df.format(detail.getAmount() == null ? BigDecimal.ZERO : detail.getAmount());
        RowDTO row2 = new RowDTO(row2Name + PrintInvoiceHelper.formatStr(unitPriceStr, qtyStr, totalPriceStr));
        itemData.add(row2);
        return itemData;
    }

    /**
     * 设置发票为已打印
     */
    @Override
    @Transactional
    public void setInvoicePrinted(List<Long> ids) {
        invoiceDao.setInvoicePrinted(ids);
        //由于后面要根据要根据发票是否打印来做判断，需先提交
        invoiceDao.getSession().flush();
        invoiceDao.getSession().clear();

        List<WaveHeader> waves = invoiceDao.getWaveByInvoiceId(ids);
        if (ListUtil.isNotEmpty(waves)) {
            for (WaveHeader waveHeader : waves) {
                deliveryOrderService.updateWaveInvoiceInfo(waveHeader, waveHeader.getDoHeaders(), null);
                waveService.updateWaveHeader(waveHeader);
            }
        }
    }

    @Override
    public Integer getPrintInvoiceVersion(){
        Integer version = SystemConfig.getConfigValueInt("print.invoice.version", ParamUtil.getCurrentWarehouseId());
        return version == null ? 0 : version;
    }

    /**
     * 判断DO需要发票&&发票未打印
     * @param doHeader
     * @return
     */
    @Override
    public Boolean isDoNeedPrintInvoice(DeliveryOrderHeader doHeader) {
        if (null == doHeader) {
            return false;
        }
        if (null == doHeader.getInvoiceFlag() || doHeader.getInvoiceFlag().intValue() != 1) {
            return false;
        }
        List<Long> notPrintedIds = invoiceDao.getInvoiceHIdsByDoIds(Lists.newArrayList(doHeader.getId()));
        if (ListUtil.isNullOrEmpty(notPrintedIds)) {
            return false;
        }
        return true;
    }
}
