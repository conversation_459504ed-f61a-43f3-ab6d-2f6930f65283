package com.daxia.wms.delivery.container.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.entity.Sku;
import com.daxia.wms.stock.stock.entity.StockBatchAtt;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * 容器日志
 */
@Entity
@Table(name = "doc_pkt_container_detail")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " is_deleted = 0 ")
@SQLDelete(sql = "update doc_pkt_container_detail set is_deleted = 1 where id = ? and version = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class PktContainerDetail extends WhBaseEntity {

    private static final long serialVersionUID = 5523366182474624592L;

    private Long id;

    private Long headerId;

    private Long taskId;

    private PickTask pickTask;

    /**
     * 商品ID
     */
    private Long skuId;

    private Sku sku;

    private Long locId;

    private Location location;

    /**
     * 批次ID
     */
    private Long lotId;

    private StockBatchAtt stockBatchAtt;

    /**
     * 数量
     */
    private BigDecimal qty;

    /**
     * 数量
     */
    private BigDecimal qtyUnit;

    /**
     * 是否删除 0:no 1:yes
     */
    private Long isDeleted;

    private PktContainerHeader pktContainerHeader;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "header_id")
    public Long getHeaderId() {
        return headerId;
    }

    public void setHeaderId(Long headerId) {
        this.headerId = headerId;
    }

    @Column(name = "task_id")
    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "task_id",insertable = false, updatable = false)
    public PickTask getPickTask() {
        return pickTask;
    }

    public void setPickTask(PickTask pickTask) {
        this.pickTask = pickTask;
    }

    @Column(name = "loc_id")
    public Long getLocId() {
        return locId;
    }

    public void setLocId(Long locId) {
        this.locId = locId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "loc_id", referencedColumnName = "ID", insertable = false, updatable = false)
    public Location getLocation() {
        return location;
    }

    public void setLocation(Location location) {
        this.location = location;
    }

    @Column(name = "IS_DELETED")
    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Column(name = "SKU_ID")
    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    @Column(name = "LOT_ID")
    public Long getLotId() {
        return lotId;
    }

    public void setLotId(Long lotId) {
        this.lotId = lotId;
    }

    @Column(name = "QTY")
    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    @Column(name = "QTY_UNIT")
    public BigDecimal getQtyUnit() {
        return qtyUnit;
    }

    public void setQtyUnit(BigDecimal qtyUnit) {
        this.qtyUnit = qtyUnit;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SKU_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    public Sku getSku() {
        return sku;
    }

    public void setSku(Sku sku) {
        this.sku = sku;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "LOT_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    public StockBatchAtt getStockBatchAtt() {
        return stockBatchAtt;
    }

    public void setStockBatchAtt(StockBatchAtt stockBatchAtt) {
        this.stockBatchAtt = stockBatchAtt;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "header_id", referencedColumnName = "ID", insertable = false, updatable = false)
    public PktContainerHeader getPktContainerHeader() {
        return pktContainerHeader;
    }

    public void setPktContainerHeader(PktContainerHeader pktContainerHeader) {
        this.pktContainerHeader = pktContainerHeader;
    }
}