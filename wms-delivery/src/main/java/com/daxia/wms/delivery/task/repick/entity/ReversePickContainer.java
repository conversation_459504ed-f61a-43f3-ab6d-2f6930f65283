package com.daxia.wms.delivery.task.repick.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Entity
@Table(name = "md_container_reverse")
@Where(clause = "is_deleted = 0 ")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@SQLDelete(sql = "update md_container_reverse set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class ReversePickContainer extends WhBaseEntity {
    
    private Long id;
    
    private Long doHeaderId;
    
    private Long repickHeaderId;
    
    private String containerNo;
    
    private String type;
    
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "id")
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    @Column(name = "repick_header_id")
    public Long getRepickHeaderId() {
        return repickHeaderId;
    }
    
    public void setRepickHeaderId(Long repickHeaderId) {
        this.repickHeaderId = repickHeaderId;
    }
    
    @Column(name ="do_header_id")
    public Long getDoHeaderId() {
        return doHeaderId;
    }
    
    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }
    
    @Column(name="container_no")
    public String getContainerNo() {
        return containerNo;
    }
    
    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }
    
    @Column(name = "type")
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
}
