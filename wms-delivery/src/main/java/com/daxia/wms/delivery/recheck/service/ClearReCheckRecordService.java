package com.daxia.wms.delivery.recheck.service;

import java.util.List;

import com.daxia.wms.delivery.OrderFrozenException;
import com.daxia.wms.delivery.recheck.dto.ReCheckCartonDetail;
import com.daxia.wms.delivery.recheck.dto.ReCheckCartonInfo;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;

/**
 * 删除装箱数据Service接口定义
 */
public interface ClearReCheckRecordService {
	
	/**
	 * 根据箱号来进行查询
	 * @param cartonNo
	 * @return CartonHeader
	 */
	public CartonHeader findCartonHeader(String cartonNo);
	
	/**
	 * 商品是否需要进行序列号扫描
	 * @param doId
	 * @param cartonId
	 * @param productBarCode
	 * @return 需要时返回true
	 */
	public boolean needScanSerial(Long doId, Long cartonId, Long productBarCode);
	
	/**
	 * 查询CartonId指定的装箱内，条码为productBarCode所有商品的装箱信息。因为存在一码多品的情况，因此返回值一个列表
	 * @param cartonId
	 * @param productBarCode
	 * @return 返回每一个商品对应的装箱信息
	 */
	public List<ReCheckCartonDetail> findReCheckCartonDetail(Long cartonId, String productBarCode);
	
	/**
	 * 查询某一箱的装箱信息
	 * @param cartonNo 箱号
	 * @return
	 */
	public ReCheckCartonInfo findReCheckCartonInfo(String cartonNo);
	
	/**
	 * 清除定单下面指定箱的装箱信息
	 * @param orderId
	 * @param cartonId
	 * @throws OrderFrozenException 定单处于冻结状态时抛出该异常
	 */
	public void clearCarton(Long orderId, Long cartonId) throws OrderFrozenException;;

	public void clearCrossCarton(Long orderId, Long cartonId) throws OrderFrozenException;

	/**
	 * 清除定单下面空箱子
	 * @param orderId
	 * @param cartonId
	 * @throws OrderFrozenException 定单处于冻结状态时抛出该异常
	 */
	public void clearEmptyCarton(Long orderId, Long cartonId);
	
	/**
	 * 从某装箱记录中移除一个指定的商品，如果删除商品时，检查到对应的装箱中已无其它数据，则删除该箱头信息
	 * @param orderId
	 * @param cartonId
	 * @param productId
	 * @param trsSerialLogId 产品关联的序列号
	 * @return 如果级联删除了装箱头信息，则返回True，否则返回false
	 * @throws OrderFrozenException 定单处于冻结状态时抛出该异常
	 */
	public boolean clearProduct(Long orderId, Long cartonId, Long productId, Long trsSerialLogId) throws OrderFrozenException;
	

	/**
	 * 查询序列号log的数据库Id
	 * @param orderId
	 * @param cartonId
	 * @param productId
	 * @param serialNo
	 * @return
	 */
	public Long findTrsSerialLogId(Long orderId, Long cartonId, Long productId,
			String serialNo);
	
	/**
     * 回退时处理DO序列号
     * @param doHeaderId
     */
    public void rollBackSerial(Long doHeaderId);
}
