package com.daxia.wms.delivery.task.replenish.service.impl;

import com.daxia.framework.common.util.*;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DocType;
import com.daxia.wms.Constants.StockType;
import com.daxia.wms.Constants.TaskStatus;
import com.daxia.wms.Constants.TrsType;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.task.replenish.dao.ReplMoveTaskDAO;
import com.daxia.wms.delivery.task.replenish.dto.ReplMoveTaskStock2AllocateDTO;
import com.daxia.wms.delivery.task.replenish.entity.ReplMoveTask;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishTask;
import com.daxia.wms.delivery.task.replenish.filter.ReplMoveTaskFilter;
import com.daxia.wms.delivery.task.replenish.service.ReplMoveTaskService;
import com.daxia.wms.master.dao.PackageInfoDetailDAO;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.entity.PackageInfoDetail;
import com.daxia.wms.master.entity.RotationDetail;
import com.daxia.wms.master.entity.RotationHead;
import com.daxia.wms.master.service.LocationService;
import com.daxia.wms.master.service.PackageInfoDetailService;
import com.daxia.wms.master.service.RotationService;
import com.daxia.wms.stock.stock.dto.Stock2AllocateDTO;
import com.daxia.wms.stock.stock.dto.StockDTO;
import com.daxia.wms.stock.stock.entity.StockBatchAtt;
import com.daxia.wms.stock.stock.entity.StockBatchLocLpn;
import com.daxia.wms.stock.stock.entity.TrsTransactionLog;
import com.daxia.wms.stock.stock.service.*;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.jboss.seam.log.Log;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Name("replMoveTaskService")
@lombok.extern.slf4j.Slf4j
public class ReplMoveTaskServiceImpl implements ReplMoveTaskService {

	@In
	private ReplMoveTaskDAO replMoveTaskDAO;
	@In
	private StockService stockService;
	@In
	private PackageInfoDetailDAO packageInfoDetailDAO;
	@In
	private TransactionService transactionService;
	@In
	private RotationService rotationService;
	@In
	private StockQueryService stockQueryService;
	@In
	protected StockBatchAttService stockBatchAttService;
	@In
	private LocationService locationService;
    @In("replMoveOperator")
    private IOperator replMoveOperator;
	@In
	private PackageInfoDetailService packageInfoDetailService;

	
	@Override
	@Transactional
	public void save(ReplMoveTask replMoveTask) {
		replMoveTaskDAO.save(replMoveTask);		
	}

	@Override
    public List<ReplMoveTask> getReplMoveTasksByReplTask(Long replTaskId, TaskStatus taskStatus) {
		return replMoveTaskDAO.getReplMoveTasksByReplTask(replTaskId, taskStatus);
	}
	
    @Override
    public DataPage<ReplMoveTask> query(ReplMoveTaskFilter replHeaderFilter, int startIndex,
                                        int pageSize) {
    	return replMoveTaskDAO.findRangeByFilter(replHeaderFilter, startIndex, pageSize);
    }
	
	@Override
    @Transactional
	public void allocate4move(ReplenishTask replTask, BigDecimal overQtyUnit, Long toLotId) throws Exception {
		BigDecimal canUseQtyUnit = stockQueryService.getAvQtyBySkuAndLoc(replTask.getSkuId(), replTask.getFmLocId(), replTask.getPackQty())[1];
		if (overQtyUnit.compareTo(canUseQtyUnit) == 1) {
			throw new DeliveryException(DeliveryException.PEP_REAL_QTY_ILLEGAL_STOCK);
		}
		List<StockBatchLocLpn> stockList = stockQueryService.findStockBySkuLoc(replTask.getSkuId(), replTask.getFmLocId(), replTask.getPackQty());
		List<Stock2AllocateDTO> stock2AllocateDTOs = new ArrayList<Stock2AllocateDTO>();
		RotationHead rotationHead = rotationService.get(replTask.getSku().getReplenishmentRuleId());
		if (rotationHead == null) {
			throw new DeliveryException(DeliveryException.REPL_NO_RULE_ERROR);
		}
		for (StockBatchLocLpn stock : stockList) {
			ReplMoveTaskStock2AllocateDTO stock2AllocateDTO = new ReplMoveTaskStock2AllocateDTO();
			stock2AllocateDTO.setActQtyUnit(stock.getCanUseQtyUnit());
			stock2AllocateDTO.setStockId(stock.getId());
			stock2AllocateDTO.setSkuId(stock.getSkuId());
			stock2AllocateDTO.setLocId(stock.getLocId());
			stock2AllocateDTO.setLotId(stock.getLotId());
			stock2AllocateDTO.setLpnNo(stock.getLpnNo());
			stock2AllocateDTO.setPutawaySeq(stock.getLocation().getPutawaySeq());
			StockBatchAtt stockBatchAtt = stock.getStockBatchAtt();
			for (int i = rotationHead.getRotaionDetails().size() - 1; i >= 0; i--) {
				try {
					// 取得排序字段对应的setter并设�?
					RotationDetail rotationDetail = rotationHead.getRotaionDetails().get(i);
					MvelUtil.setValue(stock2AllocateDTO, rotationDetail.getLotattName(), MvelUtil.getValue(stockBatchAtt, rotationDetail.getLotattName().replace("A", "a")));
				} catch (Exception e) {
					log.error("allocate4move", "setMethod failed");
					throw e;
				}
			}
			stock2AllocateDTOs.add(stock2AllocateDTO);
		}
		stockService.sortByLot4Repl(rotationHead, stock2AllocateDTOs);
		
		StockBatchAtt toBatch = stockBatchAttService.findStockBatchAttBylotId(toLotId);
		generateReplMoveTask(replTask, overQtyUnit, stock2AllocateDTOs, false, Long.valueOf(toBatch.getLotatt07()));
	}
	
	private void generateReplMoveTask(ReplenishTask replTask, BigDecimal overQtyUnit, List<Stock2AllocateDTO> stock2AllocateDTOs, boolean changeLoc, Long toPackDetailId) {
		for (Stock2AllocateDTO stock2AllocateDTO : stock2AllocateDTOs) {
			if (BigDecimal.ZERO.compareTo(overQtyUnit) < 0) {
				BigDecimal taskQtyUnit = overQtyUnit.compareTo(stock2AllocateDTO.getActQtyUnit()) > 0 ? stock2AllocateDTO.getActQtyUnit() : overQtyUnit;
				overQtyUnit = overQtyUnit.subtract(taskQtyUnit);
				
				Map<String, Long> map = changeReplMoveTaskStock(replTask, null, taskQtyUnit, taskQtyUnit, stock2AllocateDTO, true, changeLoc, genToLotId(stock2AllocateDTO.getLotId(), toPackDetailId));
				createReplMoveTask(replTask, stock2AllocateDTO, map, taskQtyUnit);
			} else {
				break;
			}
		}
	}
	
	// fmLotId：补货移库的原始库位的批次，replToLotId：补货的目标库位的批次
	private Long genToLotId(Long fmLotId, Long toPackDetailId) {
		StockBatchAtt fmBatch = stockBatchAttService.findStockBatchAttBylotId(fmLotId);
		
		StockBatchAtt toBatch = new StockBatchAtt();
		BeanUtils.copyProperties(fmBatch, toBatch);
		toBatch.setId(null);
		toBatch.setLotatt07(toPackDetailId.toString());
		
		return stockBatchAttService.existStockBatch(toBatch).getId();
	}
	
	private boolean executeReplMoveTask(ReplenishTask replTask, Long realLocId, String reasonCode, String reasonDesc, boolean changeLoc, BigDecimal overQtyUnit, List<Stock2AllocateDTO> stock2AllocateDTOs, boolean ignorePending,Long toLotId) {
		boolean seperateTask = false;
		BigDecimal replQtyUnit = overQtyUnit;
		for (Stock2AllocateDTO stock2AllocateDTO : stock2AllocateDTOs) {
			if (BigDecimal.ZERO.compareTo(overQtyUnit) < 0) {
				BigDecimal taskQtyUnit = overQtyUnit.compareTo(stock2AllocateDTO.getActQtyUnit()) > 0 ? stock2AllocateDTO.getActQtyUnit() : overQtyUnit;
				overQtyUnit = overQtyUnit.subtract(taskQtyUnit);
				
				BigDecimal planQtyUnit = stock2AllocateDTO.getActQtyUnit();
				BigDecimal actQtyUnit = taskQtyUnit;
				BigDecimal newTaskQty = taskQtyUnit;
				if (actQtyUnit.compareTo(planQtyUnit) < 0) { // 拆分补货移位任务
					seperateTask = true;
					newTaskQty = planQtyUnit.subtract(actQtyUnit);
				}
				
				validateLoc(stock2AllocateDTO, realLocId, ignorePending);
				
				Map<String, Long> map = changeReplMoveTaskStock(replTask, realLocId, planQtyUnit, actQtyUnit, stock2AllocateDTO, false, changeLoc, toLotId);
				if (seperateTask) {
					createReplMoveTask(replTask, stock2AllocateDTO, map, newTaskQty);
				}
				StockBatchAtt toBatch = stockBatchAttService.findStockBatchAttBylotId(toLotId);
				completeReplMoveTask(((ReplMoveTaskStock2AllocateDTO) stock2AllocateDTO).getReplMoveTask(), realLocId, reasonCode, reasonDesc, replTask, taskQtyUnit, this.genToLotId(stock2AllocateDTO.getLotId(), Long.valueOf
						(toBatch.getLotatt07())));
			} else {
				break;
			}
		}
		return seperateTask;
	}
	
	private void validateLoc(Stock2AllocateDTO stock2AllocateDTO, Long realLocId, boolean ignorePending) {
		Location tempLoc = locationService.checkLocTypeById(realLocId,
				this.locationService.getReplLocTypes());

		stockService.checkPutAwayMixSku(stock2AllocateDTO.getSkuId(), tempLoc, ignorePending);
		
		// 验证批次
		if (tempLoc.getCanMixBatch().intValue() == 0) {
            if (stockService.isMixLot(stock2AllocateDTO.getSkuId(), 
            		stock2AllocateDTO.getLotId(), tempLoc.getId(),null)) {
            	throw new DeliveryException(DeliveryException.LOC_CAN_NOT_MIX_BATCH);
            }
		}
		
		//不能补货到锁定库�?
		//tempLoc从缓存中查得，SQL从数据库中判断库位是否锁定，
        if (locationService.isLocked(tempLoc.getId())) {
            throw new DeliveryException(DeliveryException.REPL_LOCATION_MUST_BE_UNLOCKED, tempLoc.getLocCode());
        }
	}
	
	private Map<String, Long> changeReplMoveTaskStock(ReplenishTask replTask, Long realLocId, BigDecimal planQtyUnit, BigDecimal actQtyUnit, Stock2AllocateDTO stock2AllocateDTO, boolean isGenerate, boolean changeLoc, Long
			toLotId) {
		BigDecimal packingQty = replTask.getPackQty();
		
		StockDTO stockDTO = new StockDTO();
		stockDTO.setPackQty(packingQty);
		stockDTO.setActualQty(actQtyUnit.multiply(packingQty));
		stockDTO.setActualQtyUnit(actQtyUnit);
		stockDTO.setFmLocId(replTask.getFmLocId());
		stockDTO.setGenerateFlag(isGenerate);
		stockDTO.setFmLotId(stock2AllocateDTO.getLotId());
		stockDTO.setToLotId(toLotId);
		stockDTO.setLpnNo(stock2AllocateDTO.getLpnNo());
		stockDTO.setPlanQty(planQtyUnit.multiply(packingQty));
		stockDTO.setPlanQtyUnit(planQtyUnit);
		stockDTO.setSkuId(stock2AllocateDTO.getSkuId());
		if (isGenerate) {
			stockDTO.setToLocId(replTask.getPlanLocId());
			stockDTO.setStockLpnId(stock2AllocateDTO.getStockId());
		} else {
			if (changeLoc) {
				stockDTO.setToLocId(realLocId);
			}
			if (stock2AllocateDTO instanceof ReplMoveTaskStock2AllocateDTO) {
				stockDTO.setFmStockId(((ReplMoveTaskStock2AllocateDTO)stock2AllocateDTO).getFmStockId());
				stockDTO.setToStockId(((ReplMoveTaskStock2AllocateDTO)stock2AllocateDTO).getToStockId());
			}
		}
		replMoveOperator.setStockDto(stockDTO);
		Map<String, Long> map = stockService.operateStock(replMoveOperator);
		return map;
	}
	
	@Override
    @Transactional
	public boolean allocateReplMoveTask(ReplenishTask replTask, Long realLocId, String reasonCode, String reasonDesc, boolean changeLoc, BigDecimal overQtyUnit, boolean ignorePending, Long toLotId) throws Exception {
		BigDecimal canUseQtyUnit = getReplMoveTaskQtyUnitByReplTask(replTask.getId(), TaskStatus.RELEASED);
		if (overQtyUnit.compareTo(canUseQtyUnit) == 1) {
			throw new DeliveryException(DeliveryException.PEP_REAL_QTY_ILLEGAL_OFFSHELF);
		}
		List<ReplMoveTask> replMoveTasks = getReplMoveTasksByReplTask(replTask.getId(), TaskStatus.RELEASED);
		List<Stock2AllocateDTO> stock2AllocateDTOs = new ArrayList<Stock2AllocateDTO>();
		RotationHead rotationHead = rotationService.get(replTask.getSku().getReplenishmentRuleId());
		if (rotationHead == null) {
			throw new DeliveryException(DeliveryException.REPL_NO_RULE_ERROR);
		}
		
		for (ReplMoveTask replMoveTask : replMoveTasks) {
			replMoveTaskDAO.getSession().refresh(replMoveTask);
			ReplMoveTaskStock2AllocateDTO stock2AllocateDTO = new ReplMoveTaskStock2AllocateDTO();
			stock2AllocateDTO.setActQty(replMoveTask.getQty());
			stock2AllocateDTO.setActQtyUnit(replMoveTask.getQtyUnit());
			stock2AllocateDTO.setSkuId(replMoveTask.getSkuId());
			stock2AllocateDTO.setLocId(replMoveTask.getFmLocId());
			stock2AllocateDTO.setLotId(replMoveTask.getLotId());
			stock2AllocateDTO.setLpnNo(replMoveTask.getLpnNo());
			stock2AllocateDTO.setFmStockId(replMoveTask.getFmStockId());
			stock2AllocateDTO.setToStockId(replMoveTask.getToStockId());
			stock2AllocateDTO.setPutawaySeq(replMoveTask.getFmLocation().getPutawaySeq());
			stock2AllocateDTO.setReplMoveTask(replMoveTask);
			StockBatchAtt stockBatchAtt = replMoveTask.getStockBatchAtt();
            for(int i = rotationHead.getRotaionDetails().size() - 1; i >= 0; i--){
                try {
                    // 取得排序字段对应的setter并设�?
                    RotationDetail rotationDetail = rotationHead.getRotaionDetails().get(i);
					MvelUtil.setValue(stock2AllocateDTO, rotationDetail.getLotattName(), MvelUtil.getValue(stockBatchAtt, rotationDetail.getLotattName().replace("A", "a")));
				} catch (Exception e) {
                    log.error("allocate4move", "setMethod failed");
                    throw e;
                }
            }
			stock2AllocateDTOs.add(stock2AllocateDTO);
		}
		stockService.sortByLot4Repl(rotationHead, stock2AllocateDTOs);
		return executeReplMoveTask(replTask, realLocId, reasonCode, reasonDesc, changeLoc, overQtyUnit, stock2AllocateDTOs, ignorePending, toLotId);
	}
	
	@Transactional
	private void createReplMoveTask(ReplenishTask replTask, Stock2AllocateDTO stock2AllocateDTO, Map<String, Long> map, BigDecimal taskQtyUnit) {
		ReplMoveTask replMoveTask = new ReplMoveTask();
		replMoveTask.setDocId(replTask.getDocOperId());
		replMoveTask.setDocLineId(replTask.getId());
		replMoveTask.setDocNo(replTask.getDocOperNo());
		replMoveTask.setFmLocId(replTask.getFmLocId());
		replMoveTask.setFmStockId(map.get(StockType.STOCK_ALLOC.getValue()));
		replMoveTask.setLotId(stock2AllocateDTO.getLotId());
		replMoveTask.setLpnNo(stock2AllocateDTO.getLpnNo());
		replMoveTask.setPlanLocId(replTask.getPlanLocId());
		replMoveTask.setQty(taskQtyUnit.multiply(replTask.getPackQty()));
		replMoveTask.setQtyUnit(taskQtyUnit);
		replMoveTask.setSkuId(stock2AllocateDTO.getSkuId());
		replMoveTask.setStatus(TaskStatus.RELEASED.getValue());
		replMoveTask.setToStockId(map.get(StockType.STOCK_PENDING.getValue()));
		replMoveTask.setWarehouseId(ParamUtil.getCurrentWarehouseId());
		save(replMoveTask);
	}
	
	@Transactional
	private void completeReplMoveTask(ReplMoveTask replMoveTask, Long realLocId, String reasonCode, String reasonDesc, ReplenishTask replTask, BigDecimal taskQtyUnit, Long toLotId) {
		replMoveTask.setQty(taskQtyUnit.multiply(replTask.getPackQty()));
		replMoveTask.setQtyUnit(taskQtyUnit);
		replMoveTask.setStatus(TaskStatus.COMPLETED.getValue());
		replMoveTask.setToLocId(realLocId);
		replMoveTask.setReasonCode(reasonCode);
		replMoveTask.setReasonDesc(reasonDesc);
		save(replMoveTask);
		createReplMoveTransactionLog(replMoveTask, replTask, toLotId);
	}
	
	@Override
    @Transactional
	public void cancelReplMoveTask(Long replTaskId, boolean needAlert) {
		List<ReplMoveTask> replMoveTasks = getReplMoveTasksByReplTask(replTaskId, TaskStatus.RELEASED);
		if (ListUtil.isNullOrEmpty(replMoveTasks) && needAlert) {
			throw new DeliveryException(DeliveryException.NO_TASK_CANCEL);
		} else {
			for (ReplMoveTask replMoveTask : replMoveTasks) {
				StockDTO stockDTO = new StockDTO();
				stockDTO.setActualQty(replMoveTask.getQty());
				stockDTO.setPlanQty(replMoveTask.getQty());
				stockDTO.setFmLocId(replMoveTask.getFmLocId());
				stockDTO.setFmStockId(replMoveTask.getFmStockId());
				stockDTO.setToStockId(replMoveTask.getToStockId());
				replMoveOperator.setStockDto(stockDTO);
				stockService.undo(replMoveOperator);
			}
			replMoveTaskDAO.updateReplMoveTaskStatusByReplTask(replTaskId, TaskStatus.RELEASED, TaskStatus.CANCELED);
		}
	}
	
	@Override
    public BigDecimal getReplMoveTaskQtyUnitByReplTask(Long replTaskId, TaskStatus taskStatus) {
		return replMoveTaskDAO.getReplMoveTaskQtyUnitByReplTask(replTaskId, taskStatus);
	}
	
    private void createReplMoveTransactionLog(ReplMoveTask replMoveTask, ReplenishTask replTask,Long toLotId)
    		throws DeliveryException{

		StockBatchAtt fromStockBatchAtt = stockService.queryStockBatchAttById(replMoveTask.getLotId());
		StockBatchAtt toStockBatchAtt = stockService.queryStockBatchAttById(toLotId);
		PackageInfoDetail fromDetail = packageInfoDetailService.get(Long.valueOf(fromStockBatchAtt.getLotatt07()));
		BigDecimal fromPackQty = fromDetail.getQty();
		PackageInfoDetail toDetail = packageInfoDetailService.get(Long.valueOf(toStockBatchAtt.getLotatt07()));
		BigDecimal toPackQty = toDetail.getQty();

        TrsTransactionLog trsLog = new TrsTransactionLog();
        trsLog.setTaskId(replMoveTask.getId());
        trsLog.setDocId(replMoveTask.getDocId());
        trsLog.setDocNo(replMoveTask.getDocNo());
        trsLog.setDocLineId(replMoveTask.getDocLineId());
        trsLog.setDocType(DocType.MOVE.getValue());
        trsLog.setTransactionType(TrsType.MV.getValue());

        //SKU From、SKU  To
        Long skuId = replMoveTask.getSkuId();
        trsLog.setFmSkuId(skuId);
        trsLog.setToSkuId(skuId);
        //FmLoc、ToLocId
        trsLog.setFmLocId(replMoveTask.getFmLocId());
        trsLog.setToLocId(replMoveTask.getToLocId());
        //FmLot 、ToLot
        trsLog.setFmLotId(fromStockBatchAtt.getId());
        trsLog.setToLotId(toLotId);
        //FM_LPN_NO 、TO_LPN_NO
        trsLog.setFmLpnNo(replMoveTask.getLpnNo());
        trsLog.setToLpnNo(replMoveTask.getLpnNo());
        //FM_QTY 、TO_QTY
        trsLog.setFmQty(replMoveTask.getQty());
        trsLog.setToQty(replMoveTask.getQty());
		trsLog.setFmQtyUnit(replMoveTask.getQtyUnit());
		trsLog.setToQtyUnit(replMoveTask.getQty().divideAndRemainder(toPackQty)[0]);
        //PACK_ID
        trsLog.setPackId(replTask.getPackId());
        //FM_PACK_DETAIL_ID、TO_PACK_DETAIL_ID
        trsLog.setFmPackDetailId(fromDetail.getId());
        trsLog.setToPackDetailId(toDetail.getId());
        //FM_UOM_QTY、TO_UOM_QTY
        trsLog.setToUomQty(fromPackQty);
        trsLog.setFmUomQty(toPackQty);
		trsLog.setFmUom(fromDetail.getUomDescr());
		trsLog.setToUom(toDetail.getUomDescr());


        
        trsLog.setReasonCode(replMoveTask.getReasonCode());
        trsLog.setReasonDescr(replMoveTask.getReasonDesc());
        
        Long zero = Constants.YesNo.NO.getValue().longValue();
        trsLog.setIsDamage(zero);
        trsLog.setIsInverse(zero);
        trsLog.setOperationId(ParamUtil.getCurrentLoginName());

		trsLog.setCreatedBy(ParamUtil.getCurrentLoginName());
		trsLog.setUpdatedBy(ParamUtil.getCurrentLoginName());
		trsLog.setOperSource(ParamUtil.getCurrentDevice());    // 设置操作来源(WMS/RF)
        //供应商From、供应商To
        String supplierIdStr = fromStockBatchAtt.getLotatt04();
        if(StringUtil.isEmpty(supplierIdStr)) {
        	trsLog.setFmSupplierId(null);
        	trsLog.setToSupplierId(null);
        } else {
            Long supplierId = Long.parseLong(supplierIdStr);
            trsLog.setFmSupplierId(supplierId);
            trsLog.setToSupplierId(supplierId);
        }
        //商家id库存回写�?
        trsLog.setMerchantId(StringUtil.isEmpty(fromStockBatchAtt.getLotatt06())?null:Long.parseLong(fromStockBatchAtt.getLotatt06()));

        transactionService.saveTrsTransactionLog(trsLog);
    }
}
