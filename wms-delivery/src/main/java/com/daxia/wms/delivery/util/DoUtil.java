package com.daxia.wms.delivery.util;

import com.daxia.framework.common.util.*;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 *  DO工具类：一些DO相关的处理公用方法
 */
@lombok.extern.slf4j.Slf4j
public class DoUtil {
    

    /**
     * 判断DO是否需要【登记】标识（或者说:是否包含合约机订单、实名制网卡订单、选号入网订单） 
     * 
     * @param doHeader
     * @return
     */
    public static boolean isDoNeedRegister(DeliveryOrderHeader doHeader) {
        // 是否包含合约机订单、实名制网卡订单、选号入网订单
        return Constants.OrderRegisterFlag.CONTRACT_PHONE.getValue().equals(doHeader.getCheckFlag())
                || Constants.OrderRegisterFlag.REAL_NAME_NIC.getValue().equals(doHeader.getCheckFlag())
                || Constants.OrderRegisterFlag.PHONE_NO_NET_PURCHASING.getValue().equals(doHeader.getCheckFlag());
    }
    
    /**
     * 简单解密doNo（仅仅是去掉加密doNor的"."及以后的内容）
     * @param encrytedDoNo 待解密的doNo
     * @return 解密后的doNo。若传入的待解密doNo不包含"."，则直接返回原带解密doNo
     */
    public static String decryptDoNo(String encrytedDoNo) {
        if (StringUtil.isNotEmpty(encrytedDoNo)) {
            if(encrytedDoNo.indexOf('.') != -1) {
                return StringUtil.substring(encrytedDoNo, 0, encrytedDoNo.indexOf('.'));
            }
        }
        return encrytedDoNo;
    }
    
    /**
     * 判断list中的do是否是同一种类型
     * @param doHeaders
     * @return
     */
    public static Boolean isSameDoType(List<DeliveryOrderHeader> doHeaders) {
        for (int i = 0 ; i < doHeaders.size() ; i++){
            String doType = doHeaders.get(0).getDoType();
            if (!doType.equals(doHeaders.get(i).getDoType())) {
                return false;
            }
        }
        return true;
    }

    public static String encryptPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return phone;
        }
        try {
            return "{des}".concat(new EncryptUtil().encrypt(phone));
        } catch (Exception e) {
            return phone;
        }
    }

    public static String decryptPhone(String phone) {
        if (null == phone || !phone.startsWith("{des}")) {
            return phone;
        }
        try {
            return new EncryptUtil().decrypt(phone.substring(5));
        } catch (Exception e) {
            return phone;
        }
    }
    
    
    public static void main(String[] args) {
//        String encrytedDoNo = "DO0123456789.3";
//        System.out.println(encrytedDoNo);
//        System.out.println(DoUtil.decryptDoNo(encrytedDoNo));
        
//        DeliveryOrderHeader doHeader  = new DeliveryOrderHeader();
//        doHeader.setCheckFlag(Integer.valueOf(0));
//        doHeader.setIsGroup(YesNo.YES.getValue());
//        Boolean result = isAllowDoAutoWave(doHeader);
//        System.out.println(result);
    }
}
