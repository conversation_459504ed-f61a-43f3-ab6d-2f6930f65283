package com.daxia.wms.delivery.crossorder.service.impl;

import com.daxia.dubhe.api.internal.util.NumberUtils;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.Keys;
import com.daxia.wms.OrderLogConstants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.crossorder.dao.CrossSeedDetailDAO;
import com.daxia.wms.delivery.crossorder.dao.CrossSeedHeaderDAO;
import com.daxia.wms.delivery.crossorder.dto.CrossSeedDTO;
import com.daxia.wms.delivery.crossorder.dto.CrossSeedDetailDTO;
import com.daxia.wms.delivery.crossorder.entity.CrossSeedDetail;
import com.daxia.wms.delivery.crossorder.entity.CrossSeedHeader;
import com.daxia.wms.delivery.crossorder.filter.CrossSeedFilter;
import com.daxia.wms.delivery.crossorder.service.CrossSeedService;
import com.daxia.wms.delivery.deliveryorder.dao.DoDetailDAO;
import com.daxia.wms.delivery.deliveryorder.dao.DoHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.OrderLogService;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.stock.stock.dao.StockBatchAttDAO;
import com.daxia.wms.stock.stock.dao.StockCrossDockDAO;
import com.daxia.wms.stock.stock.entity.StockBatchAtt;
import com.daxia.wms.stock.stock.entity.StockCrossDock;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Name("com.daxia.wms.delivery.crossSeedService")
@lombok.extern.slf4j.Slf4j
public class CrossSeedServiceImpl implements CrossSeedService {
    @In
    private SequenceGeneratorService sequenceGeneratorService;
    @In
    private DoHeaderDAO doHeaderDAO;
    @In
    private DoDetailDAO doDetailDAO;
    @In
    private CrossSeedHeaderDAO crossSeedHeaderDAO;
    @In
    private CrossSeedDetailDAO crossSeedDetailDAO;
    @In
    private StockCrossDockDAO stockCrossDockDAO;
    @In
    private StockBatchAttDAO stockBatchAttDAO;
    @In
    private ExpFacadeService expFacadeService;
    @In
    private OrderLogService orderLogService;

    @Override
    public Pagination<CrossSeedDTO> findCrossSeedInfo(CrossSeedFilter crossSeedFilter, Integer startIndex, Integer pageSize) {
        return new Pagination<CrossSeedDTO>(this.crossSeedHeaderDAO.findCrossSeedInfo(crossSeedFilter, startIndex, pageSize));
    }

    @Override
    public CrossSeedDTO getCrossHeaderDTO(Long crossSeedHeaderId) {
        return this.crossSeedHeaderDAO.getCrossHeaderDTO(crossSeedHeaderId);
    }

    @Override
    public Pagination<CrossSeedDetailDTO> findCrossDetailByHeaderId(Long crossSeedHeaderId) {
        return new Pagination<CrossSeedDetailDTO>(this.crossSeedDetailDAO.findCrossDetailByHeaderId(crossSeedHeaderId));
    }

    @Override
    public CrossSeedHeader getByNo(String seedNo) {
        return crossSeedHeaderDAO.getByNo(seedNo);
    }

    @Override
    public CrossSeedHeader getCrossSeedHeaderById(Long crossSeedHeaderId) {
        return this.crossSeedHeaderDAO.get(crossSeedHeaderId);
    }

    @Override
    public List<CrossSeedDetail> findCrossSeedDetails(String seedNo, List<Long> skuIds, String lotatt05, String lotatt01, String lotatt02, Integer sortedFlag) {
        return crossSeedDetailDAO.findCrossSeedDetails(seedNo, skuIds, lotatt05, lotatt01, lotatt02, sortedFlag);
    }

    @Override
    @Transactional
    public Integer doSeed(String updateBy, BigDecimal sortQty, Long crossSeedDetailId) {
        CrossSeedDetail detail = crossSeedDetailDAO.get(crossSeedDetailId);
        if (Constants.YesNo.YES.getValue().equals(detail.getSortedFlag()) || sortQty.compareTo(detail.getAllocatedQty().subtract(detail.getSortedQty())) > 0) {
            throw new DeliveryException(DeliveryException.DETAIL_HAS_SEED);
        }
        boolean canLess = Config.isDefaultFalse(Keys.Delivery.seed_execute_can_less, Config.ConfigLevel.WAREHOUSE);
        if (canLess || sortQty.compareTo(detail.getAllocatedQty().subtract(detail.getSortedQty())) == 0) {
            detail.setSortedFlag(Constants.YesNo.YES.getValue());
        }
        detail.setSortedQty(detail.getSortedQty().add(sortQty));
        detail.setUpdatedBy(updateBy);
        crossSeedDetailDAO.update(detail);
        boolean isNotDone = crossSeedDetailDAO.checkHeadIsNotDone(detail.getHeaderId());
        CrossSeedHeader header = detail.getHeader();
        if (!isNotDone) {
            header.setStatus(Constants.CrossSeedStatus.SORTED.getValue());
            header.setSeedToTime(DateUtil.getNowTime());
        } else {
            header.setStatus(Constants.CrossSeedStatus.SORTING.getValue());
        }
        if (header.getSeedFromTime() == null) {
            header.setSeedFromTime(DateUtil.getNowTime());
        }
        header.setUpdatedBy(updateBy);
        crossSeedHeaderDAO.saveOrUpdate(header);

        DeliveryOrderDetail deliveryOrderDetail = doDetailDAO.get(detail.getDoDetailId());
        deliveryOrderDetail.setSortedQty(deliveryOrderDetail.getSortedQty().add(sortQty));
        deliveryOrderDetail.setLineStatus(Constants.DoStatus.ALLSORTED.getValue());
        doDetailDAO.update(deliveryOrderDetail);
        boolean isNotSortdDone = crossSeedDetailDAO.checkDoHeadDone(detail.getHeaderId(), detail.getDoHeaderId());
        DeliveryOrderHeader deliveryOrderHeader = doHeaderDAO.get(detail.getDoHeaderId());
        deliveryOrderHeader.setStatus(isNotSortdDone ? Constants.DoStatus.PARTSORTED.getValue() : Constants.DoStatus.ALLSORTED.getValue());
        deliveryOrderHeader.setSortedBy(updateBy);
        doHeaderDAO.update(deliveryOrderHeader);
        if (Constants.DoStatus.ALLSORTED.getValue().equals(deliveryOrderHeader.getStatus())) {
            orderLogService.saveLog(deliveryOrderHeader, OrderLogConstants.OrderLogType.SORT_COMPETE.getValue(), ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_SORT_COMPETE, null));
        }
        return isNotDone ? Constants.YesNo.NO.getValue() : Constants.YesNo.YES.getValue();
    }

    @Override
    @Transactional
    public void completeSeed(String updateBy, String seedNo, String asnNo) {
        CrossSeedHeader crossSeedHeader = crossSeedHeaderDAO.getByNo(seedNo);
        if (Constants.CrossSeedStatus.SORTED.getValue().equals(crossSeedHeader.getStatus())) {
            return;
        }
        boolean isNotSortdDone = crossSeedDetailDAO.checkHeadIsNotDone(crossSeedHeader.getId());
        if (isNotSortdDone) {
            throw new DeliveryException(DeliveryException.DETAIL_NOT_ALL_SEED);
        }
        crossSeedHeader.setSeedToTime(DateUtil.getNowTime());
        crossSeedHeader.setStatus(Constants.CrossSeedStatus.SORTED.getValue());
        crossSeedHeader.setUpdatedBy(updateBy);
        crossSeedHeaderDAO.update(crossSeedHeader);
        crossSeedDetailDAO.updateSortedFlag(crossSeedHeader.getId());
        List<Long> doIds = crossSeedHeaderDAO.findDoIdsByCrossSeedHeaderId(crossSeedHeader.getId());
        doHeaderDAO.updateStatus(doIds, updateBy, Constants.DoStatus.ALLSORTED.getValue());
        doDetailDAO.updateStatus(doIds, updateBy, Constants.DoStatus.ALLSORTED.getValue());
    }

    @Override
    public CrossSeedHeader getByContainerNo(String containerNo) {
        return crossSeedHeaderDAO.getByContainerNo(containerNo);
    }

    @Override
    public List<Long> findCanShipDoIdList() {
        return doHeaderDAO.findCanShipDoIdList();
    }

    @Override
    @Transactional
    public void ship(Long doHeaderId) {
        stockCrossDockDAO.updateStockWhenShip(doHeaderId);//重置缺发库存到主库存
        doHeaderDAO.updateStatus(Arrays.asList(doHeaderId), ParamUtil.getCurrentLoginName(), Constants.DoStatus.ALL_DELIVER.getValue());
        doDetailDAO.updateStatus(Arrays.asList(doHeaderId), ParamUtil.getCurrentLoginName(), Constants.DoStatus.ALL_DELIVER.getValue());
        orderLogService.saveLog(doHeaderDAO.get(doHeaderId), OrderLogConstants.OrderLogType.SHIP_COMPLETE.getValue(), ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_MODIFY_SHIP_COMPLETE, null));
        expFacadeService.sendDo2OmsCreateDatas(doHeaderId, Constants.DoStatus.ALL_DELIVER.getValue(), 1, null, doHeaderDAO.get(doHeaderId).getDoType());
    }

    @Override
    public List<String> findAsnForCreateAndAllocate() {
        return crossSeedHeaderDAO.findAsnForCreateAndAllocate();
    }

    @Override
    @Transactional
    public void createCrossSeedByDoList(String asnNo, List<String> doNoList) {
        String seedNo = sequenceGeneratorService.generateSequenceNo(Constants.SequenceName.SEED_NO.getValue(), ParamUtil.getCurrentWarehouseId());
        CrossSeedHeader header = new CrossSeedHeader();
        header.setSeedNo(seedNo);
        header.setAsnNo(asnNo);
        header.setStatus(Constants.CrossSeedStatus.INITIAL.getValue());
        header.setDoNum(NumberUtils.object2Long(doNoList.size()));
        header.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        crossSeedHeaderDAO.saveOrUpdate(header);
        BigDecimal units = BigDecimal.ZERO;
        Integer sortGridNo = 1;
        for (String doNo : doNoList) {
            DeliveryOrderHeader doHeader = doHeaderDAO.findByOrderCode(doNo);
            if(doHeader == null){
                Map<String, Object> params = new HashedMap();
                params.put("refNo1",doHeader.getRefNo1());
                List<DeliveryOrderHeader> doHeaders = doHeaderDAO.find(params);
                if(CollectionUtils.isNotEmpty(doHeaders)){
                    doHeader = doHeaders.get(0);
                }
            }
            String containerNo = sequenceGeneratorService.generateSequenceNo(Constants.SequenceName.SEED_CONTAINER_NO.getValue(), ParamUtil.getCurrentWarehouseId());
            for (DeliveryOrderDetail detail : doHeader.getDoDetails()) {
                CrossSeedDetail crossSeedDetail = new CrossSeedDetail();
                crossSeedDetail.setHeaderId(header.getId());
                crossSeedDetail.setExpectedQty(detail.getExpectedQty());
                crossSeedDetail.setDoNo(doHeader.getDoNo());
                crossSeedDetail.setDoDetailId(detail.getId());
                crossSeedDetail.setDoHeaderId(detail.getDoHeaderId());
                crossSeedDetail.setSortGridNo(String.valueOf(sortGridNo));
                crossSeedDetail.setContainerNo(containerNo);
                crossSeedDetail.setConsigneeName(doHeader.getConsigneeName());
                crossSeedDetail.setSkuId(detail.getSkuId());
                crossSeedDetail.setLotatt05(detail.getLotatt05());
                crossSeedDetail.setLotatt01(detail.getLotatt01());
                crossSeedDetail.setLotatt02(detail.getLotatt02());
                crossSeedDetail.setWarehouseId(ParamUtil.getCurrentWarehouseId());
                crossSeedDetailDAO.saveOrUpdate(crossSeedDetail);
                units = units.add(detail.getExpectedQty());
            }
            doHeader.setSortGridNo(String.valueOf(sortGridNo));
            doHeader.setUserDeffine6(containerNo);
            doHeaderDAO.update(doHeader);
            sortGridNo++;
        }
        header.setUnits(units);
        crossSeedHeaderDAO.saveOrUpdate(header);
    }

    @Override
    public CrossSeedHeader getCrossSeedByAsnNo(String asnNo) {
        return crossSeedHeaderDAO.getCrossSeedByAsnNo(asnNo);
    }

    @Override
    @Transactional
    public void doAllocate(Long crossSeedHeaderId, Long asnId) {
        CrossSeedHeader header = crossSeedHeaderDAO.get(crossSeedHeaderId);
        List<DeliveryOrderDetail> needAllocateDoDetails = doDetailDAO.findDetailByAsnId(asnId);
        crossSeedDetailDAO.deleteInit(crossSeedHeaderId);
        BigDecimal actualUnits = BigDecimal.ZERO;
        for (DeliveryOrderDetail needAllocateDoDetail : needAllocateDoDetails) {
            if (needAllocateDoDetail.getExpectedQty().compareTo(needAllocateDoDetail.getAllocatedQty()) > 0) {
                Map<String, Object> params = new HashedMap();
                params.put("skuId", needAllocateDoDetail.getSkuId());
                params.put("docId", asnId);
                List<StockCrossDock> stocks = stockCrossDockDAO.find(params);
                for (StockCrossDock stock : stocks) {
                    if (needAllocateDoDetail.getAllocatedQty().compareTo(needAllocateDoDetail.getExpectedQty()) >= 0) {
                        break;
                    }
                    if (stock.getQty().compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }
                    StockBatchAtt batchAtt = stockBatchAttDAO.get(stock.getLotId());
                    boolean isMatch = checkAttrMatch(needAllocateDoDetail, batchAtt);
                    if (!isMatch) {
                        continue;
                    }
                    BigDecimal allocateQty = needAllocateDoDetail.getExpectedQty().subtract(needAllocateDoDetail.getAllocatedQty());
                    if (allocateQty.compareTo(stock.getQty()) > 0) {
                        allocateQty = stock.getQty();
                    }
                    stock.setQty(stock.getQty().subtract(allocateQty));
                    actualUnits = actualUnits.add(allocateQty);
                    stockCrossDockDAO.update(stock);
                    createAllocateQty(needAllocateDoDetail, allocateQty, batchAtt, crossSeedHeaderId, stock.getId());
                    needAllocateDoDetail.setAllocatedQty(needAllocateDoDetail.getAllocatedQty().add(allocateQty));
                }
                doDetailDAO.update(needAllocateDoDetail);
                if (needAllocateDoDetail.getAllocatedQty().compareTo(needAllocateDoDetail.getExpectedQty()) < 0
                        && Config.isDefaultFalse(Keys.Task.cross_create_lack_detail, Config.ConfigLevel.WAREHOUSE)) {
                    createLack(needAllocateDoDetail, needAllocateDoDetail.getExpectedQty().subtract(needAllocateDoDetail.getAllocatedQty()), crossSeedHeaderId);
                }
            }
        }
        header.setStatus(Constants.CrossSeedStatus.ALLALLOCATED.getValue());
        header.setActualUnits(actualUnits);
        crossSeedHeaderDAO.update(header);
        doHeaderDAO.updatecCrossSeedAllocate(crossSeedHeaderId, asnId);
        List<Long> doIds = crossSeedHeaderDAO.findDoIdsByCrossSeedHeaderId(crossSeedHeaderId);
        orderLogService.batchSaveLog(doIds, OrderLogConstants.OrderLogType.ALLOCATE_COMPLETE.getValue(),
                ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_CROSS_ALLOCATE_COMPLETE, null));
    }

    private boolean checkAttrMatch(DeliveryOrderDetail detail, StockBatchAtt batchAtt) {
        if (!checktLotatt(detail.getLotatt01(), batchAtt.getLotatt01())) {
            return false;
        }
        if (!checktLotatt(detail.getLotatt02(), batchAtt.getLotatt02())) {
            return false;
        }
        if (!checktLotatt(detail.getLotatt03(), batchAtt.getLotatt03())) {
            return false;
        }
        if (!checktLotatt(detail.getLotatt04(), batchAtt.getLotatt04())) {
            return false;
        }
        if (!checktLotatt(detail.getLotatt05(), batchAtt.getLotatt05())) {
            return false;
        }
        if (!checktLotatt(detail.getLotatt06(), batchAtt.getLotatt06())) {
            return false;
        }
        if (!checktLotatt(detail.getLotatt07(), batchAtt.getLotatt07())) {
            return false;
        }
        if (!checktLotatt(detail.getLotatt08(), batchAtt.getLotatt08())) {
            return false;
        }
        if (!checktLotatt(detail.getLotatt09(), batchAtt.getLotatt09())) {
            return false;
        }
        if (!checktLotatt(detail.getLotatt10(), batchAtt.getLotatt10())) {
            return false;
        }
        if (!checktLotatt(detail.getLotatt12(), batchAtt.getLotatt12())) {
            return false;
        }
        return true;
    }

    private boolean checktLotatt(String doAttr, String stockAttr) {
        if (StringUtil.isBlank(doAttr)) {
            return true;
        }
        return StringUtil.equals(doAttr, stockAttr);
    }

    private void createAllocateQty(DeliveryOrderDetail detail, BigDecimal allocateQty, StockBatchAtt batchAtt, Long crossSeedHeaderId, Long stockId) {
        CrossSeedDetail crossSeedDetail = new CrossSeedDetail();
        DeliveryOrderHeader doHeader = detail.getDoHeader();
        crossSeedDetail.setHeaderId(crossSeedHeaderId);
        crossSeedDetail.setExpectedQty(allocateQty);
        crossSeedDetail.setAllocatedQty(allocateQty);
        crossSeedDetail.setDoNo(doHeader.getDoNo());
        crossSeedDetail.setDoDetailId(detail.getId());
        crossSeedDetail.setDoHeaderId(detail.getDoHeaderId());
        crossSeedDetail.setSortGridNo(doHeader.getSortGridNo());
        crossSeedDetail.setContainerNo(doHeader.getUserDeffine6());
        crossSeedDetail.setConsigneeName(doHeader.getConsigneeName());
        crossSeedDetail.setSkuId(detail.getSkuId());
        crossSeedDetail.setLotatt05(StringUtil.isBlank(detail.getLotatt05()) ? null : detail.getLotatt05());
        crossSeedDetail.setLotatt01(StringUtil.isBlank(detail.getLotatt01()) ? null : detail.getLotatt01());
        crossSeedDetail.setLotatt02(StringUtil.isBlank(detail.getLotatt02()) ? null : detail.getLotatt02());
        crossSeedDetail.setLotNo(batchAtt.getLotNo());
        crossSeedDetail.setStockId(stockId);
        crossSeedDetail.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        crossSeedDetailDAO.saveOrUpdate(crossSeedDetail);
    }

    private void createLack(DeliveryOrderDetail detail, BigDecimal lackQty, Long crossSeedHeaderId) {
        CrossSeedDetail crossSeedDetail = new CrossSeedDetail();
        DeliveryOrderHeader doHeader = detail.getDoHeader();
        crossSeedDetail.setHeaderId(crossSeedHeaderId);
        crossSeedDetail.setExpectedQty(lackQty);
        crossSeedDetail.setAllocatedQty(BigDecimal.ZERO);
        crossSeedDetail.setSortedFlag(1);
        crossSeedDetail.setDoNo(doHeader.getDoNo());
        crossSeedDetail.setDoDetailId(detail.getId());
        crossSeedDetail.setDoHeaderId(detail.getDoHeaderId());
        crossSeedDetail.setSortGridNo(doHeader.getSortGridNo());
        crossSeedDetail.setContainerNo(doHeader.getUserDeffine6());
        crossSeedDetail.setConsigneeName(doHeader.getConsigneeName());
        crossSeedDetail.setSkuId(detail.getSkuId());
        crossSeedDetail.setLotatt05(StringUtil.isBlank(detail.getLotatt05()) ? null : detail.getLotatt05());
        crossSeedDetail.setLotatt01(StringUtil.isBlank(detail.getLotatt01()) ? null : detail.getLotatt01());
        crossSeedDetail.setLotatt02(StringUtil.isBlank(detail.getLotatt02()) ? null : detail.getLotatt02());
        crossSeedDetail.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        crossSeedDetailDAO.saveOrUpdate(crossSeedDetail);
    }

    @Override
    public List<CrossSeedDetail> getCrossDetailsBycontainerNo(String containerNo) {
        return crossSeedDetailDAO.getCrossDetailsBycontainerNo(containerNo);
    }

    @Override
    public CrossSeedDetail getCrossDetailByDetailId(Long crossDetailId) {
        return crossSeedDetailDAO.get(crossDetailId);
    }

    @Override
    @Transactional
    public void updateByHeader(CrossSeedHeader crossSeedHeader) {
        crossSeedDetailDAO.update(crossSeedHeader);
    }

    @Override
    public BigDecimal queryQtyByDoHeaderId(Long doHeaderId) {
        return crossSeedDetailDAO.queryQtyByDoHeaderId(doHeaderId);
    }

}
