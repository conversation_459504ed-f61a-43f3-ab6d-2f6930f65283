package com.daxia.wms.delivery.invoice.service.impl;

import java.util.Date;
import java.util.List;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.jboss.seam.security.Identity;

import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.invoice.dao.InvoiceBookDAO;
import com.daxia.wms.delivery.invoice.dao.InvoiceDao;
import com.daxia.wms.delivery.invoice.dao.InvoiceNoDAO;
import com.daxia.wms.delivery.invoice.entity.InvoiceBook;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;
import com.daxia.wms.delivery.invoice.filter.InvoiceNoFilter;
import com.daxia.wms.delivery.invoice.service.InvoiceBindService;
import com.daxia.wms.delivery.invoice.service.InvoiceNoService;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.InvoiceBookStatus;
import com.daxia.wms.exp.service.ExpFacadeService;

/**
 * 发票绑定Service
 */
@Name("com.daxia.wms.delivery.invoiceBindService")
@lombok.extern.slf4j.Slf4j
public class InvoiceBindServiceImpl implements InvoiceBindService {
    @In
    private InvoiceNoDAO invoiceNoDAO;
    @In
    private InvoiceDao invoiceDao;
    @In
    private InvoiceBookDAO invoiceBookDAO;
    @In
    private Identity identity;
    @In
    private InvoiceNoService invoiceNoService;
    @In
    private ExpFacadeService expFacadeService;
    
    /**
     * 查询发票号码信息
     * 
     * @param filter
     * @return
     */
	@Override
	public List<InvoiceNo> findInvoiceNoByFilter( InvoiceNoFilter filter ) {
		List<InvoiceNo> list = (List<InvoiceNo>) invoiceNoDAO.findByFilter(filter);
		for (InvoiceNo var : list) {
			if (var.getInvoiceHeader() == null) {
				var.setInvoiceHeader(new InvoiceHeader());
			}
		}
		return list;
	}
    
    @Override
    @Transactional
    public void modifyBindInvoiceNo(Long invoiceHeaderId, String newInvoiceNo, String oldInvoiceNo) {
    	InvoiceNo newNo = invoiceNoService.getInvoiceNoObjByInvNo(newInvoiceNo);
    	if (newNo == null) {
    		throw new DeliveryException(DeliveryException.ERROR_INVOICE_BIND_INVOICE_NOEXISTS, newInvoiceNo); 
    	}
    	if (!Constants.InvoiceNoStatus.INITIAL.getValue().equals(newNo.getStatus())) {
    		throw new DeliveryException(DeliveryException.ERROR_INVOICE_BIND_NEW_INVOICENO_MUST_BE_NOPRINT, newInvoiceNo); 
    	}
    	InvoiceHeader header = invoiceDao.get(invoiceHeaderId);
    	if (header == null) {
    		throw new DeliveryException(DeliveryException.ERROR_INVOICE_BIND_HEADER_ID_NOEXISTS, invoiceHeaderId); 
    	}
    	
    	//旧开票号
    	InvoiceNo oldNo = this.checkDoAndNo(header.getDeliveryOrderHeader(),oldInvoiceNo);
    	//修改新绑定发票信息
    	newNo.setInvoiceHeaderId(header.getId());
    	newNo.setStatus(Constants.InvoiceNoStatus.PRINT.getValue());
    	newNo.setDoNo(header.getDeliveryOrderHeader().getDoNo());
    	newNo.setLockBy(identity.getCredentials().getUsername());
    	newNo.setLockDoDate(new Date());
    	//修改旧绑定发票信息
    	oldNo.setStatus(Constants.InvoiceNoStatus.DES.getValue());
    	oldNo.setInvalidBy(identity.getCredentials().getUsername());
    	oldNo.setInvalidDate(new Date());
    	//修改最新绑定发票号码
    	header.setInvoiceNumber(newNo.getInvoiceNo());
		invoiceNoDAO.update(newNo);
		invoiceNoDAO.update(oldNo);
		invoiceDao.update(header);
		//修改新发票号码所属的发票薄信息为使用中
		InvoiceBook book = invoiceBookDAO.get(newNo.getInvoiceBookId());
		if (book == null) {
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_CANCEL_NO_BOOKINFORINVOICENO, newNo.getInvoiceNo()); // 发票号{0}没有对应的发票薄信息 error.invoice.cancel.noBookInForInvoiceNo
		}
		if (InvoiceBookStatus.INITIAL.getValue().equals(book.getStatus())) {
		    // 发票薄状态：未使用
		    
			book.setStatus(InvoiceBookStatus.USING.getValue());// 改成使用中
			invoiceBookDAO.update(book);
		}
    }
    
    /**
     * 检查do状态是否可以进行发票号操作
     * 
     * @param doHeader
     */
    @Override
    public void checkDoForInvoiceOperate(DeliveryOrderHeader doHeader) {
    	if (doHeader == null) {
			throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
		}
		if (DoStatus.CANCELED.getValue().equals(doHeader.getStatus())
				|| DoStatus.ALL_DELIVER.getValue().equals(doHeader.getStatus())) {
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_BIND_DO_STATUS_NOT_BE_DEPLOY_CANCEL, doHeader.getDoNo());
		}
    }
    
    /**
     * 检查do状态是否可以进行发票号操作
     * 
     * @param doHeader
     */
    @Override
    public InvoiceNo checkDoAndNo(DeliveryOrderHeader doHeader, String oldInvoiceNo) {
    	this.checkDoForInvoiceOperate(doHeader);
		InvoiceNo oldNo = invoiceNoService.getInvoiceNoObjByInvNoAndDoNo(oldInvoiceNo,doHeader.getDoNo());
    	if (oldNo == null) {
    		throw new DeliveryException(DeliveryException.ERROR_INVOICE_BIND_OLD_INVOICENO_NOEXISTS, oldInvoiceNo); 
    	}
    	if (!Constants.InvoiceNoStatus.PRINT.getValue().equals(oldNo.getStatus())) {
    		throw new DeliveryException(DeliveryException.ERROR_INVOICE_BIND_OLD_INVOICENO_MUST_BE_PRINT, oldNo.getInvoiceNo()); 
    	}
    	return oldNo;
    }
}
