package com.daxia.wms.delivery.invoice.extend.aisinogz.dto;


import com.daxia.wms.invoice.entity.ElectroniceStock;

import javax.xml.bind.annotation.*;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="out" type="{http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com}ElectroniceStock"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "out" })
@XmlRootElement(name = "queryEliStockResponse")
@lombok.extern.slf4j.Slf4j
public class QueryEliStockResponse {

	@XmlElement(required = true, nillable = true)
	protected ElectroniceStock out;

	/**
	 * Gets the value of the out property.
	 * 
	 * @return possible object is {@link ElectroniceStock }
	 * 
	 */
	public ElectroniceStock getOut() {
		return out;
	}

	/**
	 * Sets the value of the out property.
	 * 
	 * @param value
	 *            allowed object is {@link ElectroniceStock }
	 * 
	 */
	public void setOut(ElectroniceStock value) {
		this.out = value;
	}

}
