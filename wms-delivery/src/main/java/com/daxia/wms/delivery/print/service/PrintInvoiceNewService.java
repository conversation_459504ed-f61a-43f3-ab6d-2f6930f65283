package com.daxia.wms.delivery.print.service;

import java.util.List;

import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.print.dto.InvoiceNewDTO;
import com.daxia.wms.master.service.PrintService;

/**
 * 
 * <pre>
 * 发票打印
 * </pre>
 */
public interface PrintInvoiceNewService extends PrintService<InvoiceNewDTO> {
	/**
	 * 获取发票打印数据
	 * @param ids
	 * @param invoiceCode
	 * @return
	 */
    List<InvoiceNewDTO> getPrintDataOnly(List<Long> ids, String invoiceCode);

    /**
     * 打印发票
     * @param ids
     * @param invoiceCode
     * @return
     */
    public List<String> print(List<Long> ids, String invoiceCode);

    /**
     * 设置发票为已打印
     * @param ids
     */
    void setInvoicePrinted(List<Long> ids);

    List<String> printTest();

    /**
     * 获取发票打印数据（卷式发票）
     * @param idList
     * @param invoiceCode
     * @return
     */
    List<InvoiceNewDTO> getPrintData4RollInvoice(List<Long> idList, String invoiceCode,String operationType);
    
    /**
     * 构造卷式发票打印数据
     * @param invoiceList
     * @param invoiceCode
     * @return
     */
    public List<InvoiceNewDTO> getPrintDataFromList4RollInvoice(List<InvoiceHeader> invoiceList, String invoiceCode,String operationType);
}
