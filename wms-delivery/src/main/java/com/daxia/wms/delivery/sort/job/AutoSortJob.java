package com.daxia.wms.delivery.sort.job;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.recheck.service.ReCheckService;
import com.daxia.wms.delivery.sort.service.SortingService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.master.job.AbstractJob;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.*;
import org.jboss.seam.log.Log;

import java.util.List;
import java.util.Map;


@Name("autoSortJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class AutoSortJob extends AbstractJob {
    @In
    WaveService waveService;
    @In
    SortingService sortingService;

    
    @Override
    protected void doRun() throws Exception {
        List<Map<String, Object>> waveIdList = waveService.getAutoWave(WaveHeader.FLAG_AUTO_SORT);
        for (Map<String, Object> waveIdMap : waveIdList) {
            Long warehouseId = (Long) waveIdMap.get("warehouseId");
            Long waveId = (Long) waveIdMap.get("id");
            try {
                ParamUtil.setCurrentWarehouseId(warehouseId);
            
                WaveHeader waveHeader = waveService.getWave(waveId);
                if (waveHeader != null) {
                    sortingService.compelSorting(waveHeader.getWaveNo(), null);
                }
            } catch (Exception e) {
                log.error("Auto recheck error！", e);
            
                super.processException(e);
            
                doInFailed(waveId);
            }
        }
    }
    
    private void doInFailed(Long waveId) {
        waveService.updateFailedNumber(waveId, WaveHeader.FAILED_TYPE_SORT);
    }
}
