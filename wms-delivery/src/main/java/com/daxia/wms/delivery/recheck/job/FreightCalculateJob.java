package com.daxia.wms.delivery.recheck.job;

import com.daxia.framework.common.util.NullUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.recheck.service.impl.CartonServiceImpl;
import com.daxia.wms.master.job.AbstractJob;
import com.daxia.wms.master.rule.service.FreightRuleService;
import com.daxia.wms.master.rule.service.impl.FreightRuleServiceImpl;
import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.math.BigDecimal;
import java.util.List;

@Name("freightCalculateJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class FreightCalculateJob extends AbstractJob {


    @Override
    protected void doRun() {
        FreightRuleService freightRuleService = ((FreightRuleService) Component.getInstance(FreightRuleServiceImpl.class));
        CartonService cartonService = ((CartonService) Component.getInstance(CartonServiceImpl.class));

        List<CartonHeader> cartonHeaders = cartonService.loadCartonToCalculateFreght();
        for (CartonHeader cartonHeader : cartonHeaders) {
            ParamUtil.setCurrentWarehouseId(cartonHeader.getWarehouseId());
            DeliveryOrderHeader deliveryOrderHeader = cartonHeader.getDoHeader();
            if (deliveryOrderHeader != null) {
                BigDecimal weight = NullUtil.notNull(cartonHeader.getActualGrossWeight(), cartonHeader.getGrossWeight());
                BigDecimal freight = freightRuleService.calculate(deliveryOrderHeader.getCarrierId(), deliveryOrderHeader.getProvince(), deliveryOrderHeader.getCity(), deliveryOrderHeader.getCounty(),deliveryOrderHeader.getProvinceName(),deliveryOrderHeader.getCityName(),deliveryOrderHeader.getCountyName(), weight);
                if (freight == null || freight.compareTo(BigDecimal.ZERO) <= 0) {
                    freight = BigDecimal.valueOf(-1L);
                }
                cartonService.updateCartonFreight(cartonHeader.getId(), freight);
            }
        }
    }
}
