package com.daxia.wms.delivery.deliveryorder.service.impl;

import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.AllocateException;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoAllocateDetailDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateHeader;
import com.daxia.wms.delivery.deliveryorder.service.IDoAllocate;
import com.daxia.wms.delivery.pick.service.PickTaskService;
import com.daxia.wms.delivery.util.AllocateHelper;
import com.daxia.wms.master.MasterException;
import com.daxia.wms.master.entity.AllocationRuleHeader;
import com.daxia.wms.master.entity.PackageInfoDetail;
import com.daxia.wms.master.service.AllocationRuleHeaderService;
import com.daxia.wms.master.service.PackageInfoDetailService;
import com.daxia.wms.stock.stock.dto.OrderStockDTO;
import com.daxia.wms.stock.stock.dto.Stock2AllocateDTO;
import com.daxia.wms.stock.stock.dto.StockBatchDTO;
import com.daxia.wms.stock.stock.service.IOperator;
import com.daxia.wms.stock.stock.service.StockService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.MutablePair;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Name("com.daxia.wms.delivery.wholesaleAllocate")
@lombok.extern.slf4j.Slf4j
public class WholesaleAllocateImpl implements IDoAllocate {

    @In
    AllocationRuleHeaderService allocationRuleHeaderService;
    @In
    PackageInfoDetailService packageInfoDetailService;
    @In
    StockService stockService;
    @In
    IOperator allocateOperator;
    @In
    PickTaskService pickTaskService;
    @In
    protected DoAllocateDetailDAO doAllocateDetailDAO;

    @Override
    @Transactional
    public String executeAllocate(DoAllocateDetail alcDetail, String region) throws Exception {
        if (notNeedAllocate(alcDetail)) {
            return ALC_RESULT_SUCCESS;
        }

        if (notMatchRule(alcDetail)) {
            return ALC_RESULT_NORULE;
        }

        AllocationRuleHeader allocationRule = allocationRuleHeaderService.get(alcDetail.getAllocationRule());

        // 获取分配可用库存类型
        List<String> locTypeList = AllocateHelper.buildLocTypes4Alloc(alcDetail, allocationRule);

        // 待分配的包装数量
        BigDecimal notAllocQtyUnit = alcDetail.getExpectedQtyUnit().subtract(alcDetail.getAllocatedQtyUnit());
        PackageInfoDetail mainPackageInfo = packageInfoDetailService.findMainPackage(alcDetail.getSkuId());
        if (mainPackageInfo == null) {
            throw new MasterException(MasterException.PACKAGE_HEAD_NOTEXITST);
        }
        
        PackageInfoDetail eaPackageInfo = packageInfoDetailService.findEaDetailByPackageId(mainPackageInfo.getPackageId());
        //校验全仓库存够不够
        checkStockIsEnough(alcDetail, alcDetail.getExpectedQty().subtract(alcDetail.getAllocatedQty()));
    
        // 分配整件
        if (notAllocQtyUnit.compareTo(BigDecimal.ZERO) > 0) {
            notAllocQtyUnit = doAllocate(alcDetail, locTypeList, AllocateHelper.getPackTypes(allocationRule, false, true), mainPackageInfo, notAllocQtyUnit);
        
            // 如果整件还存在未分配数
            if (notAllocQtyUnit.compareTo(BigDecimal.ZERO) > 0 && alcDetail.getDoAllocateHeader().getLotStrategy() == 0) {
                BigDecimal notAllocQty = mainPackageInfo.getQty().multiply(notAllocQtyUnit);
                if (locTypeList.contains(Constants.LocType.RS.getValue()) || replStockIsNotEnough(alcDetail, mainPackageInfo, notAllocQty)) { // 包含存储位，那么剩余的数量尝试从散件数量出；
                    //不包含存储位，尝试对比存储位数量是否足够
                    //if (replStockIsNotEnough(alcDetail, mainPackageInfo, notAllocQty)) {//补货数量不足够，尝试从散件出；足够：补货
                    //                        notAllocQty = doAllocateIngorePackage(alcDetail, locTypeList, eaPackageInfo, notAllocQty, notAllocQtyUnit);
                    //                        if (notAllocQty.compareTo(BigDecimal.ZERO) == 0)
                    //                            notAllocQtyUnit = BigDecimal.ZERO;
                    //}
                }
            }
        }
    
        // 分配散件
        BigDecimal notAllocQtyPcs = alcDetail.getExpectedQtyPcs().subtract(alcDetail.getAllocatedQtyPcs());
        if (notAllocQtyPcs.compareTo(BigDecimal.ZERO) > 0) {
            boolean ingorePackageType = false;
            if(mainPackageInfo.getPackageId().equals(eaPackageInfo.getPackageId())){
                ingorePackageType = true;
            }
    
            notAllocQtyPcs = doAllocate(alcDetail, locTypeList, AllocateHelper.getPackTypes(allocationRule, true, ingorePackageType), eaPackageInfo, alcDetail.getExpectedQtyPcs().subtract(alcDetail.getAllocatedQtyPcs()));
        }

        BigDecimal allocateQtyPcs = alcDetail.getExpectedQtyPcs().subtract(notAllocQtyPcs);
        BigDecimal allocateQtyUnit = alcDetail.getExpectedQtyUnit().subtract(notAllocQtyUnit);
        alcDetail.setAllocatedQtyPcs(allocateQtyPcs);
        alcDetail.setAllocatedQtyUnit(allocateQtyUnit);
        alcDetail.setAllocatedQty(allocateQtyUnit.multiply(mainPackageInfo.getQty()).add(allocateQtyPcs));

        //完成分配
        if (notAllocQtyUnit.compareTo(BigDecimal.ZERO) == 0 && notAllocQtyPcs.compareTo(BigDecimal.ZERO) == 0) {
            alcDetail.setLineStatus(Constants.DoStatus.ALLALLOCATED.getValue());
            return ALC_RESULT_SUCCESS;
        } else if (allocateQtyPcs.compareTo(BigDecimal.ZERO) == 0 && allocateQtyUnit.compareTo(BigDecimal.ZERO) == 0) {
            alcDetail.setLineStatus(Constants.DoStatus.INITIAL.getValue());
        } else {
            alcDetail.setLineStatus(Constants.DoStatus.PARTALLOCATED.getValue());
        }

        //没有分配完成的情况下，如果是坏品，返回null，不提示补货
        alcDetail.setNeedReplQty(alcDetail.getExpectedQty().subtract(alcDetail.getAllocatedQty()));
        alcDetail.setNeedReplQtyPcs(alcDetail.getExpectedQtyPcs().subtract(allocateQtyPcs));
        alcDetail.setNeedReplQtyUnit(alcDetail.getExpectedQtyUnit().subtract(allocateQtyUnit));

        alcDetail.getDoAllocateHeader().setReplStatus(Constants.DoReplStatus.WAIT.getValue());
        alcDetail.getDoAllocateHeader().setReplStartTime(DateUtil.getNowTime());

        return IDoAllocate.NEED_REPL;
    }

    /**
     * 组合品订单分配库存
     *
     * @param alcHeader
     * @param allocateResult
     * @return
     */

    @SneakyThrows
    @Override
    @Deprecated
    public String skuCombiStockAllocate(DoAllocateHeader alcHeader, Map<String, List<String>> allocateResult) {
        throw new AllocateException("不支持组合品订单分配库存");
    }

    private boolean replStockIsNotEnough(DoAllocateDetail alcDetail, PackageInfoDetail mainPackageInfo, BigDecimal notAllocQty) throws Exception {
        List<String> locTypeList = Lists.newArrayList(Constants.LocType.RS.getValue());
        List<String> locCodes = AllocateHelper.getLocCodes(alcDetail.getDoAllocateHeader());
        AllocationRuleHeader allocationRuleHeader = allocationRuleHeaderService.get(alcDetail.getAllocationRule());
        allocationRuleHeader.setDoType(alcDetail.getDoAllocateHeader().getDoType());
        List<Stock2AllocateDTO> stockList = stockService.queryStockForAllocate(allocationRuleHeader, null, locTypeList, Lists.<String>newArrayList(), AllocateHelper.buildBatchDTOForAllocate(alcDetail, mainPackageInfo),
                                                                               alcDetail.getDoAllocateHeader().getShopId(),  useExprise(alcDetail),locCodes);
        ImmutableTriple<List<Stock2AllocateDTO>, BigDecimal, BigDecimal> pair = sumStock(stockList);
        BigDecimal stockQtySumForRepl = pair.getMiddle();

        return stockQtySumForRepl.compareTo(notAllocQty) < 0;
    }

    private void checkStockIsEnough(DoAllocateDetail alcDetail, BigDecimal notAllocQty) throws Exception {
        List<String> locTypeList = Lists.newArrayList(Constants.LocType.RS.getValue(), Constants.LocType.ST.getValue(), Constants.LocType.EA.getValue());
        List<String> locCodes = AllocateHelper.getLocCodes(alcDetail.getDoAllocateHeader());
        AllocationRuleHeader allocationRuleHeader = allocationRuleHeaderService.get(alcDetail.getAllocationRule());
        allocationRuleHeader.setDoType(alcDetail.getDoAllocateHeader().getDoType());
        List<Stock2AllocateDTO> stockList = stockService.queryStockForAllocate(allocationRuleHeader, null, locTypeList, Lists.<String>newArrayList(), AllocateHelper.buildBatchDTOForAllocate(alcDetail, null),
                                                                               alcDetail.getDoAllocateHeader().getShopId(), useExprise(alcDetail),locCodes);

        ImmutableTriple<List<Stock2AllocateDTO>, BigDecimal, BigDecimal> pair = sumStock(stockList);
        BigDecimal stockQtySumForRepl = pair.getMiddle();

        if (stockQtySumForRepl.compareTo(notAllocQty) < 0) {//存储区的库存数量小于待补货数量，全仓缺货
            alcDetail.setNoStockFlag(Constants.YesNo.YES.getValue());
            if (AllocateHelper.needHold(alcDetail)) {
                throw new AllocateException(IDoAllocate.NO_ENOUGH_STOCK_QTY);
            }
        }
    }
    
    private boolean useExprise(DoAllocateDetail alcDetail) {
        String doType = alcDetail.getDoAllocateHeader().getDoType();
        if (StringUtil.isIn(doType, Constants.DoType.RTV.getValue())) {
            return false;
        }
        return true;
    }
    
    private BigDecimal doAllocateIngorePackage(DoAllocateDetail alcDetail, List<String> locTypeList, PackageInfoDetail eaPackageInfo, BigDecimal notAllocQty, BigDecimal notAllocQtyUnit) throws Exception {
        StockBatchDTO stockBatchDTO = AllocateHelper.buildBatchDTOForAllocate(alcDetail, null);
        if(StringUtil.isNotBlank(alcDetail.getLotatt05())){
            stockBatchDTO.setLotatt05(alcDetail.getLotatt05());
        }
        List<String> locCodes = AllocateHelper.getLocCodes(alcDetail.getDoAllocateHeader());
        AllocationRuleHeader allocationRuleHeader = allocationRuleHeaderService.get(alcDetail.getAllocationRule());
        allocationRuleHeader.setDoType(alcDetail.getDoAllocateHeader().getDoType());
        List<Stock2AllocateDTO> stockDTOs = stockService.queryStockForAllocate(allocationRuleHeader, null, locTypeList, null, stockBatchDTO, alcDetail
                .getDoAllocateHeader().getShopId(), useExprise(alcDetail), locCodes);

        ImmutableTriple<List<Stock2AllocateDTO>, BigDecimal, BigDecimal> pair = sumStock(stockDTOs);
        BigDecimal stockQtySum = pair.getMiddle();
        if (stockQtySum.compareTo(notAllocQty) < 0) { //库存不够全部库存分配则不分配
            return notAllocQty;
        }

        List<Stock2AllocateDTO> noZeroResult = pair.getLeft();

        AllocateHelper.sortStock(alcDetail.getDoAllocateHeader(), allocationRuleHeaderService.get(alcDetail.getAllocationRule()), noZeroResult);

        alcDetail.setExpectedQtyUnit(alcDetail.getExpectedQtyUnit().subtract(notAllocQtyUnit));
        alcDetail.setExpectedQtyPcs(alcDetail.getExpectedQtyPcs().add(notAllocQty));

        for (Stock2AllocateDTO allocateStock : noZeroResult) {
            // 得到的分配数量为：待分配数量与allocateStock中可分配数量的较小值
            BigDecimal allocateQty = allocateStock.getActQty().compareTo(notAllocQty) >= 0 ? notAllocQty : allocateStock.getActQty();
            if (allocateQty.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }

            // 更新库存
            OrderStockDTO stockDto = new OrderStockDTO();
            stockDto.setFmLocId(allocateStock.getLocId());
            stockDto.setLotId(allocateStock.getLotId());
            stockDto.setLpnNo(allocateStock.getLpnNo());
            stockDto.setSkuId(alcDetail.getSkuId());
            stockDto.setPlanQty(allocateQty.negate());
            stockDto.setPlanQtyUnit(allocateQty.negate());
            stockDto.setStockLpnId(allocateStock.getStockId());
            stockDto.setDocId(alcDetail.getDoHeaderId());
            stockDto.setDocLineId(alcDetail.getId());
            allocateOperator.setStockDto(stockDto);
            Map<String, Long> stockIdMap = stockService.operateStock(allocateOperator);

            // 新建拣货任务
            pickTaskService.createPickTask(alcDetail, allocateStock, allocateQty, allocateQty, eaPackageInfo.getId(), stockIdMap.get(Constants.StockType.STOCK_ALLOC.getValue()), stockIdMap.get(Constants.StockType.STOCK_ALLOCING.getValue()));

            // 更新已分配数量
            notAllocQty = notAllocQty.subtract(allocateQty);
            alcDetail.setAllocatedQtyPcs(alcDetail.getAllocatedQtyPcs().add(allocateQty));
            // 表示到此为止,库存已经满足了此订单明细,此时应该退出,不再分配
            if (notAllocQty.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }

            doAllocateDetailDAO.getSession().flush();
        }
        return notAllocQty;
    }

    //返回还剩余多少待分配
    private BigDecimal doAllocate(DoAllocateDetail alcDetail, List<String> locTypeList, List<String> packageTypes, PackageInfoDetail packageInfo, BigDecimal notAllocQtyUnit) throws Exception {
        BigDecimal notAllocQty = packageInfo.getQty().multiply(notAllocQtyUnit);

        StockBatchDTO stockBatchDTO = AllocateHelper.buildBatchDTOForAllocate(alcDetail, packageInfo);
        if(StringUtil.isNotBlank(alcDetail.getLotatt05())){
            stockBatchDTO.setLotatt05(alcDetail.getLotatt05());
        }
        List<String> locCodes = AllocateHelper.getLocCodes(alcDetail.getDoAllocateHeader());
        AllocationRuleHeader allocationRuleHeader = allocationRuleHeaderService.get(alcDetail.getAllocationRule());
        allocationRuleHeader.setDoType(alcDetail.getDoAllocateHeader().getDoType());
        List<Stock2AllocateDTO> stockDTOs = stockService.queryStockForAllocate(allocationRuleHeader, null, locTypeList, packageTypes, stockBatchDTO, alcDetail.getDoAllocateHeader().getShopId(),
                useExprise(alcDetail), locCodes);

        List<Stock2AllocateDTO> noZeroResult = sumStock(stockDTOs).getLeft();
    
        AllocateHelper.sortStock(alcDetail.getDoAllocateHeader(), allocationRuleHeaderService.get(alcDetail.getAllocationRule()), noZeroResult);
        
        //如果出的是整件,且同一批号策略
        if (alcDetail.getDoAllocateHeader().getLotStrategy().equals(Constants.LotStrategy.ONE_BATCH_NO.getValue())) {
            List<String> allocatedLotNo = this.pickTaskService.findLotNoByDoDetail(alcDetail.getId());
            if (allocatedLotNo.size() > 1) {
                throw new DeliveryException(DeliveryException.ALLOCATE_MULIT_LOT_NO);
            }
    
            String templateLotNo = allocatedLotNo.size() == 1 ? allocatedLotNo.get(0) : null;
            
            Map<String, MutablePair<List<Stock2AllocateDTO>, BigDecimal>> lotMap = Maps.newHashMap();
            List<Stock2AllocateDTO> commonLotResult = Lists.newArrayList();
            for (Stock2AllocateDTO allocateStock : noZeroResult) {
                if (StringUtil.isNotEmpty(templateLotNo) && allocateStock.getLotAtt05().compareTo(templateLotNo) != 0) {
                    continue;
                }
                MutablePair<List<Stock2AllocateDTO>, BigDecimal> pair = lotMap.get(allocateStock.getLotAtt05());
                if (pair == null) {
                    pair = new MutablePair<List<Stock2AllocateDTO>, BigDecimal>(Lists.newArrayList(allocateStock), allocateStock.getActQtyUnit());
                    lotMap.put(allocateStock.getLotAtt05(), pair);
                } else {
                    pair.getLeft().add(allocateStock);
                    pair.setRight(pair.getRight().add(allocateStock.getActQtyUnit()));
                }
            
                if (pair.getRight().compareTo(notAllocQtyUnit) >= 0) {
                    commonLotResult = pair.getLeft();
                    break;
                }
            }
            noZeroResult = commonLotResult;
        }
        
        for (Stock2AllocateDTO allocateStock : noZeroResult) {
            // 得到的分配数量为：待分配数量与allocateStock中可分配数量的较小值
            BigDecimal allocateQty = allocateStock.getActQty().compareTo(notAllocQty) >= 0 ? notAllocQty : allocateStock.getActQty();
            BigDecimal allocateQtyUnit = allocateStock.getActQtyUnit().compareTo(notAllocQtyUnit) >= 0 ? notAllocQtyUnit : allocateStock.getActQtyUnit();
            if (allocateQty.compareTo(BigDecimal.ZERO) == 0 || allocateQtyUnit.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }

            // 更新库存
            OrderStockDTO stockDto = new OrderStockDTO();
            stockDto.setFmLocId(allocateStock.getLocId());
            stockDto.setLotId(allocateStock.getLotId());
            stockDto.setLpnNo(allocateStock.getLpnNo());
            stockDto.setSkuId(alcDetail.getSkuId());
            stockDto.setPlanQty(allocateQty.negate());
            stockDto.setPlanQtyUnit(allocateQtyUnit.negate());
            stockDto.setStockLpnId(allocateStock.getStockId());
            stockDto.setDocId(alcDetail.getDoHeaderId());
            stockDto.setDocLineId(alcDetail.getId());
            allocateOperator.setStockDto(stockDto);
            Map<String, Long> stockIdMap = stockService.operateStock(allocateOperator);

            // 新建拣货任务
            pickTaskService.createPickTask(alcDetail, allocateStock, allocateQty, allocateQtyUnit, packageInfo.getId(), stockIdMap.get(Constants.StockType.STOCK_ALLOC.getValue()), stockIdMap.get(Constants.StockType.STOCK_ALLOCING.getValue()));

            // 更新已分配数量
            notAllocQty = notAllocQty.subtract(allocateQty);
            notAllocQtyUnit = notAllocQtyUnit.subtract(allocateQtyUnit);

            // 表示到此为止,库存已经满足了此订单明细,此时应该退出,不再分配
            if (allocateQty.compareTo(BigDecimal.ZERO) == 0 || allocateQtyUnit.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }

            doAllocateDetailDAO.getSession().flush();
        }
        return notAllocQtyUnit;
    }

    //如果库存足够，返回非0的库存，如果库存不足够，返回空列表
    private ImmutableTriple<List<Stock2AllocateDTO>, BigDecimal, BigDecimal> sumStock(List<Stock2AllocateDTO> stockDTOs) {
        BigDecimal actQtySum = BigDecimal.ZERO;
        BigDecimal actQtyUnitSum = BigDecimal.ZERO;
        List<Stock2AllocateDTO> noZeroResult = new ArrayList<Stock2AllocateDTO>();
        for (Stock2AllocateDTO stockDTO : stockDTOs) {
            actQtySum = actQtySum.add(stockDTO.getActQty()).add(stockDTO.getPendingQty());
            actQtyUnitSum = actQtyUnitSum.add(stockDTO.getActQtyUnit().add(stockDTO.getPendingQtyUnit()));
            if (stockDTO.getActQty().compareTo(BigDecimal.ZERO) > 0) {
                noZeroResult.add(stockDTO);
            }
        }

        return ImmutableTriple.of(noZeroResult, actQtySum, actQtyUnitSum);
    }

    private boolean notNeedAllocate(DoAllocateDetail alcDetail) {
        if (Constants.DoStatus.ALLALLOCATED.getValue().equals(alcDetail.getLineStatus())) {
            return true;
        }

        BigDecimal notAllocQty = alcDetail.getExpectedQty().subtract(alcDetail.getAllocatedQty());
        if (BigDecimal.ZERO.compareTo(notAllocQty) == 0) {   //无数量需要分配
            alcDetail.setLineStatus(Constants.DoStatus.ALLALLOCATED.getValue());
            return true;
        }

        if (!Integer.valueOf(1).equals(alcDetail.getIsDoLeaf())) {//只有isDoLeaf需要分配
            return true;
        }

        return false;
    }

    private boolean notMatchRule(DoAllocateDetail alcDetail) {
        String doType = alcDetail.getDoAllocateHeader().getDoType();
        Integer allotType = alcDetail.getDoAllocateHeader().getTranType();
        if (!allocationRuleHeaderService.isRuleMatch(alcDetail.getAllocationRule(), doType, allotType)) {
            return true;
        }
        return false;
    }
}
