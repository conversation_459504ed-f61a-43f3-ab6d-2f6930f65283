package com.daxia.wms.delivery.invoice.entity;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;

/**
 * 发票明细实体
 */
@Entity
@Table(name = "doc_invoice_detail")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_invoice_detail set is_deleted = 1 where id = ? and version = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class InvoiceDetail extends WhBaseEntity {

	private static final long serialVersionUID = 1994767465112566139L;

	/**
	 * 主键
	 */
	private Long id;
	
	/**
	 * 发票头ID
	 */
	private Long invoiceHeaderId;
	
	/**
	 * 名称
	 */
	private String skuDescr;
	
	/**
	 * 类型
	 */
	private String skuType;
	
	/**
	 * 计量单位
	 */
	private String uomDescr;
	
	/**
	 * 数量
	 */
	private BigDecimal qty;
	
	/**
	 * 单价
	 */
	private BigDecimal price;
	
	/**
	 * 总金额
	 */
	private BigDecimal amount;
	
	/**
	 * 排序
	 */
	private Integer sortBy;
	
    /**
     * 发票头对象
     */	
	private InvoiceHeader invoiceHeader;
	
	private BigDecimal taxRate;

    /**
     * 税收分类编码
     */	
    private String taxCategoryCode;

    private String preferentialPolicy;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)  
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "INVOICE_H_ID",insertable=false, updatable=false)
    public Long getInvoiceHeaderId() {
        return invoiceHeaderId;
    }

    public void setInvoiceHeaderId(Long invoiceHeaderId) {
        this.invoiceHeaderId = invoiceHeaderId;
    }

    @Column(name = "SKU_DESCR")
    public String getSkuDescr() {
        return skuDescr;
    }

    public void setSkuDescr(String skuDescr) {
        this.skuDescr = skuDescr;
    }

    @Column(name = "SKU_TYPE")
    public String getSkuType() {
        return skuType;
    }

    public void setSkuType(String skuType) {
        this.skuType = skuType;
    }

    @Column(name = "UOM_DESCR")
    public String getUomDescr() {
        return uomDescr;
    }

    public void setUomDescr(String uomDescr) {
        this.uomDescr = uomDescr;
    }

    @Column(name = "QTY")
    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    @Column(name = "PRICE")
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Column(name = "AMOUNT")
    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Column(name = "SORT_BY")
    public Integer getSortBy() {
        return sortBy;
    }

    public void setSortBy(Integer sortBy) {
        this.sortBy = sortBy;
    }

	public void setInvoiceHeader(InvoiceHeader invoiceHeader) {
		this.invoiceHeader = invoiceHeader;
	}
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "INVOICE_H_ID")
    @Where(clause = " IS_DELETED = 0 ")
	public InvoiceHeader getInvoiceHeader() {
		return invoiceHeader;
	}

    @Column(name = "tax_rate")
	public BigDecimal getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

    @Column(name = "tax_category_code")
    public String getTaxCategoryCode() {
        return taxCategoryCode;
    }

    public void setTaxCategoryCode(String taxCategoryCode) {
        this.taxCategoryCode = taxCategoryCode;
    }

    @Column(name = "preferential_policy")
    public String getPreferentialPolicy() {
        return preferentialPolicy;
    }

    public void setPreferentialPolicy(String preferentialPolicy) {
        this.preferentialPolicy = preferentialPolicy;
    }
}
