package com.daxia.wms.delivery.deliveryorder.dao;

import com.daxia.dubhe.api.internal.util.NumberUtils;
import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.util.DataList;
import com.daxia.framework.system.util.WmsUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DeliveryLimitType;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.ReplDoType;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateDetail;
import com.daxia.wms.delivery.print.dto.DoPrintSub;
import com.daxia.wms.delivery.sort.dto.SortingDoDetailDTO;
import com.daxia.wms.delivery.task.replenish.dto.ReplenishSkuDTO;
import com.daxia.wms.exp.Constants.DoType;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.hibernate.*;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Name("com.daxia.wms.delivery.doDetailDAO")
@lombok.extern.slf4j.Slf4j
public class DoDetailDAO extends HibernateBaseDAO<DeliveryOrderDetail, Long> {

	private static final long serialVersionUID = -1915956870395990287L;
	
	/**
	 * 查询需要补货的sku信息(不存在初始化、已发布、已下架的的及时补货任务)
	 * @param carrierId 配送商ID
	 * @param replDoType 即时补货业务类型
	 * @return
	 */
    @SuppressWarnings("unchecked")
	public List<ReplenishSkuDTO> findNeedReplSku(Long carrierId, String replDoType){
		List<String> regions = Config.getByDelimit(Keys.Delivery.allocate_splitRegions, Config.ConfigLevel.WAREHOUSE);
		boolean genByRegion = ListUtil.isNotEmpty(regions) && regions.size() > 1;
		Boolean replLot05 = Config.isDefaultFalse(Keys.Stock.do_repl_lotatt05, Config.ConfigLevel.WAREHOUSE);
		String sql = "SELECT DISTINCT dh.DO_NO, dh.CREATE_TIME, dd.SKU_ID, dd.LOTATT04, dd.LOTATT06, dd.LOTATT08,dd.LOTATT05, dd.NEED_REPL_QTY_PCS, date_format(dh.PLAN_SHIP_TIME, '%Y-%m-%d %H:%i:%S'), dh.shop_id,dd" +
				".need_repl_qty_unit, dh.repl_region_id,dd.min_exp,dd.max_exp " +
				"from doc_alc_detail dd inner join doc_alc_header dh on dd.do_header_id = dh.id " +
			    "and dd.warehouse_id = dh.warehouse_id " +
                "and dd.warehouse_id =:ddWhId " +
				"where dd.IS_DELETED = 0 " +
				"and not exists " +
				"(select 1 from trs_replenish_task task " +
				"inner join stock_batch_att b on task.lot_id = b.id and b.is_deleted = 0 " +
				(genByRegion ? " inner join md_location loc on task.plan_loc_id = loc.id and loc.is_deleted = 0 " +
						" inner join md_partition mp on mp.id = loc.partition_id and mp.is_deleted = 0 " :"") +
				"where (dd.LOTATT04 is null or dd.LOTATT04 = b.LOTATT04) " +
				"	and (dd.LOTATT06 is null or dd.LOTATT06 = b.LOTATT06) " +
				"	and (dd.LOTATT08 is null or dd.LOTATT08 = b.LOTATT08) " +
				(replLot05 ?
				"	and (dd.LOTATT05 is null or dd.LOTATT05 = b.LOTATT05) ":"") +
				" 	and (task.IS_DELETED = 0) " +
				"	and task.TASK_TYPE = :type " +
				"	and task.TASK_STATUS in(:status) " +
				"	and task.REPL_TYPE = :repltype " +
				"	and dd.SKU_ID = task.SKU_ID " +
				"	and task.warehouse_id = dd.warehouse_id " + (genByRegion ? " and mp.region_id = dh.repl_region_id" :"") +
				"	and b.warehouse_id = dd.warehouse_id) " +
				"and dd.NEED_REPL_QTY > 0";
	
		//设置配送商过滤条件
		if (null != carrierId) {
		    sql += " and dh.carrier_id = :carrierId";
		}
        if (StringUtils.isNotEmpty(replDoType)) {
            // 筛选普通do补货，调拨补货
			if (StringUtil.isIn(replDoType, ReplDoType.RPL_DO.getValue(), ReplDoType.RPL_ALLOT.getValue())) {
                sql += " and dh.do_type = :doRplType";
            } else if (StringUtil.isIn(replDoType, ReplDoType.RPL_HALF.getValue(), ReplDoType.RPL_ON_TIME.getValue(), ReplDoType.RPL_APPOINT_DATE.getValue())) {
                //筛选半日达、准时送、指定日期
                sql += " and dh.is_half_day_delivery = :deliveryLimitRplType";
            }
		}
	    List<String> status = getNeedReplTaskStatus();

		Query query = createSQLQuery(sql)
		    .setParameter("ddWhId", ParamUtil.getCurrentWarehouseId())
            .setParameter("type", Constants.TaskType.RP.getValue())
            .setParameterList("status", status)
            .setParameter("repltype", Constants.ReplType.JP.getValue());
		
		if (null != carrierId) {
		    query.setLong("carrierId", carrierId);
		}
        // 根据即时补货业务类型来设置过滤条件
        if (ReplDoType.RPL_DO.getValue().equals(replDoType)) {
            query.setParameter("doRplType", DoType.SELL.getValue().toString());
        } else if (ReplDoType.RPL_ALLOT.getValue().equals(replDoType)) {
            query.setParameter("doRplType", DoType.ALLOT.getValue().toString());
        } else if (ReplDoType.RPL_HALF.getValue().equals(replDoType)) {
            query.setParameter("deliveryLimitRplType", DeliveryLimitType.HALFDAY.getValue().toString());
        } else if (ReplDoType.RPL_ON_TIME.getValue().equals(replDoType)) {
            query.setParameter("deliveryLimitRplType", DeliveryLimitType.ONTIME.getValue().toString());
        } else if (ReplDoType.RPL_APPOINT_DATE.getValue().equals(replDoType)) {
            query.setParameter("deliveryLimitRplType", DeliveryLimitType.APPOINTDATE.getValue().toString());
        }
		
		List<Object[]> results = query.list();
		if(results == null){
			return null;
		}
		List<ReplenishSkuDTO> dtos = new ArrayList<ReplenishSkuDTO>(results.size());
        for (Object[] o : results) {
            Date planShipTime = null;
            try {
                //预计出库时间为空的不能转换
                if (null != o[8]) {
                    planShipTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse((String)o[8]);
                }
            } catch (ParseException e) {
                log.error("findNeedReplSku", "planShipTime parse exception!");
            }

			ReplenishSkuDTO replenishSkuDTO = new ReplenishSkuDTO((String) o[0], o[1] == null ? DateUtil.getNowTime() : (Timestamp) o[1], ((Number) o[2]).longValue(),
					(String) o[3], (String) o[4], (String) o[5], replLot05 ? (String) o[6] : null, (BigDecimal) o[7], planShipTime);
			replenishSkuDTO.setShopId(o[9] == null ? null : ((Number) o[9]).longValue());
			replenishSkuDTO.setReplQtyUnit((BigDecimal) o[10]);
			BigInteger replRegionId = (BigInteger) o[11];
			replenishSkuDTO.setReplRegionId(replRegionId == null ? null : replRegionId.longValue());
			replenishSkuDTO.setMinExp((Date) o[12]);
			replenishSkuDTO.setMaxExp((Date) o[13]);
            dtos.add(replenishSkuDTO);
        }
		return dtos;
	}
    
    /**
     * 需要补货的任务状态
     * @return
     */
    private List<String> getNeedReplTaskStatus(){
        List<String> status = new ArrayList<String>();
        status.add(Constants.TaskStatus.INITIALIZED.getValue());
        status.add(Constants.TaskStatus.RELEASED.getValue());
        status.add(Constants.ReplTaskStatus.OFFSHELF.getValue());
        
        return status;
    }
    
    /**
     * 清空DoDetail补货数量
     * @param skuId doDetail关联的sku 
     * @param lotatt04 doDetail关联的sku的批次属性
     * @param lotatt06 doDetail关联的sku的批次属性
     * @param lotatt08 doDetail关联的sku的批次属性
     */
    public void clearReplNum(Long skuId, String lotatt04, String lotatt06, String lotatt08) {
    	// 修改 死锁代码 or语句调整
    	String hql = "update DoAllocateDetail o set o.needReplQty = 0,o.needReplQtyUnit = 0,o.needReplQtyPcs = 0 where 1=1 " ;
//		if (StringUtil.isNotEmpty(lotatt04)) {
//			hql += " o.lotatt04 = :lotatt04 ";
//		}else{
//			hql += " o.lotatt04 is null ";
//		}
//		if (StringUtil.isNotEmpty(lotatt08)) {
//			hql += " and o.lotatt08 = :lotatt08 ";
//		}else{
//			hql += " and o.lotatt08 is null ";
//		}
		hql += " and o.skuId=:skuId " +
    			" and o.warehouseId = :warehouseId " +
    			" and o.needReplQty > 0 ";
//		if (StringUtil.isNotEmpty(lotatt06)) {
//			hql +=" and o.lotatt06 = :lotatt06";
//		}else{
//			hql += " and o.lotatt06 is null ";
//		}
		Query query = this.createUpdateQuery(hql);
		query.setParameter("skuId", skuId);
//		if (StringUtil.isNotEmpty(lotatt04)) {
//			query.setParameter("lotatt04", lotatt04);
//		}
//		if (StringUtil.isNotEmpty(lotatt06)) {
//			query.setParameter("lotatt06", lotatt06);
//		}
//		if (StringUtil.isNotEmpty(lotatt08)) {
//			query.setParameter("lotatt08", lotatt08);
//		}
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();
    }
    
    /**
     * 清空DoDetail缺货标记
     * @param skuId doDetail关联的sku 
     * @param lotatt04 doDetail关联的sku的批次属性
     * @param lotatt06 doDetail关联的sku的批次属性
     * @param lotatt08 doDetail关联的sku的批次属性
     */
    public void clearNoStockFlag(Long skuId, String lotatt04, String lotatt06, String lotatt08) {
    	//TODO
    	String hql = "update DoAllocateDetail o set o.noStockFlag = 0 where 1=1 " ;
//		if (StringUtil.isNotEmpty(lotatt04)) {
//			hql += " o.lotatt04 = :lotatt04 ";
//		}else{
//			hql += " o.lotatt04 is null ";
//		}
//		if (StringUtil.isNotEmpty(lotatt08)) {
//			hql += " and o.lotatt08 = :lotatt08 ";
//		}else{
//			hql += " and o.lotatt08 is null ";
//		}
		hql +="and o.skuId=:skuId " +
    			"and o.warehouseId = :warehouseId " +
    			"and o.noStockFlag = 1 ";
//		if (StringUtil.isNotEmpty(lotatt06)) {
//			hql +=" and o.lotatt06 = :lotatt06";
//		}else{
//			hql += " and o.lotatt06 is null ";
//		}
		Query query = this.createUpdateQuery(hql);
		query.setParameter("skuId", skuId);
//		query.setParameter("lotatt04", lotatt04);
//		if (StringUtil.isNotEmpty(lotatt04)) {
//			query.setParameter("lotatt04", lotatt04);
//		}
//		if (StringUtil.isNotEmpty(lotatt06)) {
//			query.setParameter("lotatt06", lotatt06);
//		}
//		if (StringUtil.isNotEmpty(lotatt08)) {
//			query.setParameter("lotatt08", lotatt08);
//		}
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();
    }
	
	/**
	 * 根据HEADERID查询订单明细的单位（UOM）
	 */
	public String getUomByHeaderId(Long doHeaderId) {
		String hql="SELECT DISTINCT dd.uom FROM DeliveryOrderDetail dd WHERE dd.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId";
		Query query = this.getSession().createQuery(hql);
		query.setLong("doHeaderId", doHeaderId);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		return (String) query.uniqueResult();
	}
	
	/**
	 * 查询订单下所有明细
	 * @see getDoDetailByDoHeaderId
	 * @param doHeaderId
	 * @return
	 */
	@SuppressWarnings("unchecked")
    public List<DeliveryOrderDetail> findDoDetailByHeaderId(Long doHeaderId){
		String hql="from DeliveryOrderDetail o WHERE o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId";
		Query query = this.getSession().createQuery(hql);
		query.setLong("doHeaderId", doHeaderId);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		return  query.list();
	}
	
	/**
	 * 查询波次下所有明细
	 * @param waveId
	 * @return
	 */
	@SuppressWarnings("unchecked")
    public List<DeliveryOrderDetail> findDoDetailByWaveId(Long waveId) {
		String hql="from DeliveryOrderDetail dd WHERE dd.doHeader.waveId = :waveId and dd.warehouseId = :warehouseId ";
		Query query = this.getSession().createQuery(hql);
		query.setLong("waveId", waveId);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		return  query.list();
	}
	
	/**
	 * 
	 * <pre>
	 * Description:根据发货单Id查询订单明细
	 * </pre>
	 *
	 * @param doHeaderId
	 * @return
	 */
	@SuppressWarnings("unchecked")
    public List<DeliveryOrderDetail> getDoLeafDetailByDoHeaderId(Long doHeaderId) {
		String hql = " from DeliveryOrderDetail o WHERE o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId and o.isDoLeaf = 1 ";
		Query query = this.getSession().createQuery(hql);
		query.setLong("doHeaderId", doHeaderId);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		return query.list();
	}
	
	/**
	 * 
	 * <pre>
	 * Description:根据订单Id 更新订单明细行状态为status
	 * </pre>
	 *
	 * @param doHeaderId 发运订单头信息Id
	 * @param status 状态值
	 */
	public void updateDoDetailStatusByDoId(Long doHeaderId, String status) {
		String hql = "update DeliveryOrderDetail o set o.lineStatus =:status  where  o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId ";
		Query query  = this.createUpdateQuery(hql);
		query.setString("status", status);
		query.setLong("doHeaderId", doHeaderId);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();
	}

	/**
	 * 根据波次Id 更新订单明细行状态为status，分拣数量为拣货数量
	 * @param WaveId 波次Id
	 * @param status 状态值
	 */
	public void updateDoDetailStatusByWaveId(Long waveId, String status) {
		String hql = " update DeliveryOrderDetail o set o.lineStatus = :status, o.sortedQty = o.pickedQty, o.sortedQty = o.pickedQty, o.sortedQtyUnit = o.pickedQtyUnit" +
				" where  o.doHeaderId =  (" +
				"    select d.id from DeliveryOrderHeader d where d.waveId =:waveId and d.warehouseId = :warehouseId ) " +
				" and o.warehouseId = :warehouseId";
		Query query = this.createUpdateQuery(hql);
		query.setString("status", status);
		query.setLong("waveId", waveId);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();
	}
	
	
	/**
	 * 
	 * <pre>
	 * Description:将do单的订单明细已拣数量归零,分配数清零,状态变为初始化,需要补货数量清零
	 * </pre>
	 *
	 * @param doHeaderId
	 */
	public void doDoDetailBackToInit(Long doHeaderId,String lineStatus) {                                                                                                
		String hql = "update DeliveryOrderDetail o set " +
				"o.needReplQty = 0, o.lineStatus = :lineStatus, o.pickedQty = 0, o.pickQty = 0, o.pickedQtyUnit = 0, o.allocatedQty = 0 " +
				"where o.doHeaderId =:doHeaderId and o.warehouseId = :owarehouseId";
		Query query = this.createUpdateQuery(hql);
		query.setString("lineStatus", lineStatus);
		query.setLong("doHeaderId", doHeaderId);
		query.setLong("owarehouseId", ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();
	}
	
	public void updateDoDetailStatusAndQty(Long doDetailId, String status, BigDecimal qty) {
		String hql = "update DeliveryOrderDetail o set o.needReplQty = 0, o.lineStatus = :lineStatus , o.pickedQty = :qty, o.pickedQty = :qty, o.allocatedQty = :qty " +
				"where o.id =:doDetailId and o.warehouseId = :warehouseId";
		Query query = this.createUpdateQuery(hql);
		query.setParameter("lineStatus", status);
		query.setParameter("qty", qty);
		query.setParameter("doDetailId", doDetailId);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();
	}
	
	/**
	 * 根据发货单编号将发货明细回退到拣货完成的状态。包括重置分拣数量为零，将状态设置为拣货完成
	 * @param doId
	 */
	public void clearSortRecordByDoId(Long doId) {
		String hql = "update DeliveryOrderDetail o set o.sortedQty = 0, o.sortedQtyUnit = 0 , o.lineStatus = :status " +
				"where o.doHeaderId = :doId and o.warehouseId = :warehouseId";
		Query query = createUpdateQuery(hql);
		query.setString("status", Constants.DoStatus.ALLPICKED.getValue());
		query.setLong("doId", doId);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();
	}
	
	/**
	 * 查找满足分拣条件的DoDetail（波次，skuid，订单状态范围）
	 * @param waveId 分拣波次号
	 * @param skuId 分拣的商品
	 * @param orderStatus 定单状态
	 * @return 
	 */
	@SuppressWarnings("unchecked")
    public List<DeliveryOrderDetail> findSortingDoDetail(Long waveId, Long skuId, String[] orderStatus) {
    	Criteria cri = getSession().createCriteria(DeliveryOrderDetail.class);
    	cri.createAlias("doHeader", "doHeader");
    	cri.add(Restrictions.eq("doHeader.waveHeader.id", waveId));
    	cri.add(Restrictions.in("doHeader.status", orderStatus));
    	cri.add(Restrictions.eq("skuId", skuId)); 
    	cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
    	cri.add(Restrictions.gtProperty("allocatedQty", "sortedQty"));
    	cri.addOrder(Order.asc("doHeader.sortGridNo"));
    	cri.setFetchMode("doHeader", FetchMode.JOIN);  
    	return cri.list();
    }
	
	@SuppressWarnings("unchecked")
	public List<DeliveryOrderDetail> findByWaveSkuStatus(Long waveId, Long skuId, String[] orderStatus) {
	    Criteria cri = getSession().createCriteria(DeliveryOrderDetail.class);
        cri.createAlias("doHeader", "doHeader");
        cri.add(Restrictions.eq("doHeader.waveHeader.id", waveId));
        cri.add(Restrictions.in("doHeader.status", orderStatus));
        cri.add(Restrictions.eq("skuId", skuId)); 
        cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
        cri.add(Restrictions.gtProperty("allocatedQty", "sortedQty"));
        cri.addOrder(Order.asc("doHeader.doCreateTime"));
        cri.addOrder(Order.asc("doHeader.id"));
        cri.setFetchMode("doHeader", FetchMode.JOIN); 
        return cri.list();
	}
	
	/**
	 * 查询订单下指定状态的指定商品的明细
	 * @param waveId
	 * @param skuId
	 * @param orderStatus
	 * @return
	 */
	@SuppressWarnings("unchecked")
    public List<DeliveryOrderDetail> findDetailBySku(Long doHeaderId, Long skuId, String[] lineStatus) {
	    String hql = "select o from DeliveryOrderDetail o where o.doHeaderId = :doHeaderId and o.skuId = :skuId and o.lineStatus in (:lineStatus) "
	            + " and o.warehouseId = :warehouseId order by o.id asc";
	    Query query = this.createQuery(hql);
	    query.setLong("doHeaderId", doHeaderId);
	    query.setLong("skuId", skuId);
	    query.setParameterList("lineStatus", lineStatus);
	    query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
	    return query.list();
	}
	
	/**
	 * 根据波次号，订单状态，是否分拣（分配数量与分拣数量是否一致）获取订单明细
	 * @param waveNo 波次号
	 * @param orderStatus 订单状态
	 * @param isSorted 是否分拣
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<DeliveryOrderDetail> findSortingDoDetailInWave(String waveNo,String orderStatus , boolean isSorted){
		Criteria cri = getSession().createCriteria(DeliveryOrderDetail.class);
    	cri.createAlias("doHeader", "doHeader");
    	cri.createAlias("doHeader.waveHeader", "waveHeader");
    	cri.add(Restrictions.eq("waveHeader.waveNo", waveNo));
    	cri.add(Restrictions.ge("doHeader.status", orderStatus));
		cri.add(Restrictions.ge("doHeader.releaseStatus", "RL"));
    	if (isSorted) {
    		cri.add(Restrictions.neProperty("allocatedQty", "sortedQty"));
    	}
    	cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
    	cri.setFetchMode("doHeader", FetchMode.JOIN);  
    	return cri.list();
	}
	
	/**
	 * 订单中没有分拣（分配余量与分拣数量不一致）的明细的数量
	 * @param doHeaderId
	 * @return
	 */
	public int countUnSortingDoDetail(Long doHeaderId) {
    	Criteria cri = getSession().createCriteria(DeliveryOrderDetail.class);
    	cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
    	cri.add(Restrictions.eq("doHeader.id", doHeaderId));
    	cri.add(Restrictions.neProperty("allocatedQty", "sortedQty"));
    	cri.setProjection(Projections.rowCount());
        return (Integer)cri.uniqueResult();
    }
	
	/**
	 * 根据波次ID，商品编码，状态获取订单明细
	 * @param waveId 波次ID
	 * @param barcode 商品编码
	 * @param statuses 状态
	 * @return
	 */
	@SuppressWarnings("unchecked")
    public List<Long> findSkuByCodeAndWaveId (Long waveId, String barcode, String[] statuses ){
	    StringBuilder hql = new StringBuilder();
	    hql.append(" select distinct o.skuId ");
        hql.append(" from DeliveryOrderDetail o ");
        hql.append(" where o.doHeader.waveId =:waveId  ");
        hql.append(" and o.warehouseId =:warehouseId  ");
        hql.append(" and o.sku.productCode =:barcode ");
        if (statuses != null && statuses.length > 0) {
            hql.append(" and o.doHeader.status in (:doStatus) ");
            hql.append(" and o.lineStatus in (:detailStatus)");
        }
        Query query = this.createQuery(hql.toString());
        query.setLong("waveId", waveId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setString("barcode", barcode);
        if (statuses != null && statuses.length > 0) {
            query.setParameterList("doStatus", statuses);
            query.setParameterList("detailStatus", statuses);
        }
        return query.list();
	}
	
	/**
	 * 根据波次ID，条码，状态获取SKU的ID列表
	 * @param waveId 波次ID
	 * @param barcode 条码
	 * @param statuses 状态
	 * @return
	 */
	@SuppressWarnings("unchecked")
    public List<Long> findSkuInWave(Long waveId, String barcode, String[] statuses ){
        StringBuilder hql = new StringBuilder();
        hql.append(" select distinct o.skuId ");
        hql.append(" from DeliveryOrderDetail o , ProductBarcode p");
        hql.append(" where o.skuId=p.skuId  ");
        hql.append(" and o.doHeader.waveId =:waveId  ");
        hql.append(" and o.warehouseId =:warehouseId  ");
        hql.append(" and p.barcodeLevel1 =:barcode ");
        if (statuses != null && statuses.length > 0) {
            hql.append(" and o.doHeader.status in (:doStatus) ");
            hql.append(" and o.lineStatus in (:detailStatus)");
        }
        Query query = this.createQuery(hql.toString());
        query.setLong("waveId", waveId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setString("barcode", barcode);
        if (statuses != null && statuses.length > 0) {
            query.setParameterList("doStatus", statuses);
            query.setParameterList("detailStatus", statuses);
        }
        return query.list();
    }
	
	 /**
	  * 更新波次中的订单明细的状态为预期值，分拣数量为分配数量；
	  * @param waveId
	  * @param status
	  */
     public void updateDoDetailByWaveId(Long waveId, String status, String filterStatus) {
         filterStatus = StringUtil.isNotBlank(filterStatus) ? filterStatus : DoStatus.UNCHECKED.getValue();
         String sql = "UPDATE doc_do_detail o INNER JOIN doc_do_header dh ON o.do_header_id = dh.id SET o.linestatus = if(o.linestatus < :filterStatus,:status,o.linestatus), o.sorted_qty = o.picked_qty, o.sorted_qty_unit = o.picked_qty_unit, o.version = o.version + 1, o.update_time = :time, o.update_by" +
                 " = :user WHERE dh.wave_id = :waveId AND o.warehouse_id = :warehouseId AND dh.warehouse_id = :warehouseId AND o.is_deleted = 0 AND dh.is_deleted = 0";
         Query query = this.createSQLQuery(sql);
         query.setString("status", status);
         query.setLong("waveId", waveId);
         query.setTimestamp("time", DateUtil.getNowTime());
         query.setString("user", getOperateUser());
         query.setString("filterStatus", filterStatus);
         query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
         query.executeUpdate();
     }

    /**
	  * 更新订单下订单明细数量为拣货完成（拣货数量=分配数量）
	  * @param doHeaderId 订单ID
	  */
	 public void updateDoDetailsQtyAllPicked(Long doHeaderId){
		 String hql = "update DeliveryOrderDetail o set o.pickedQty = o.allocatedQty, o.pickedQty = o.allocatedQty " +
		 		      "where o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId";
		 Query query = createUpdateQuery(hql);
		 query.setLong("doHeaderId", doHeaderId);
		 query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		 query.executeUpdate();
	 }
	 
	 /**
	  * 更新订单下订单明细数量为分拣完成（分拣数量=拣货数量）
	  * @param doHeaderId 订单ID
	  */
	 public void updateDoDetailsQtyAllSorted(Long doHeaderId){
		 String hql = "update DeliveryOrderDetail o set o.sortedQty = o.pickedQty, o.sortedQty = o.pickedQty " +
		 		      "where o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId";
		 Query query = createUpdateQuery(hql);
		 query.setLong("doHeaderId", doHeaderId);
		 query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		 query.executeUpdate();
	 }
	 
	 /**
	  * 更新订单下订单明细为拣货完成（拣货数量=分配数量，状态为拣货完成）
	  * @param doHeaderId 订单ID
	  */
	 public void updateDoDetailsAllPicked(Long doHeaderId) {
		 String hql = "update DeliveryOrderDetail o set o.pickedQty = o.allocatedQty, o.pickedQty = o.allocatedQty, " +
		 		      "o.lineStatus = :status where o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId";
		 Query query = createUpdateQuery(hql);
		 query.setString("status", DoStatus.ALLPICKED.getValue());
		 query.setLong("doHeaderId", doHeaderId);
		 query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		 query.executeUpdate();
	 }
	 
	 /**
	  * 根据doId查询isDoLeaf为1，部分分拣、拣货完成状态的doDetail并设置成dto
	  * @param doId
	  * @return
	  */
	 @SuppressWarnings("unchecked")
	 public List<SortingDoDetailDTO> getSortingDoDetails(Long doId) {
		 StringBuilder hql = new StringBuilder("select new com.daxia.wms.delivery.sort.dto.SortingDoDetailDTO(");
	     hql.append(" o.id,o.doHeaderId,o.sku.ean13,o.sku.productCode,");
         hql.append(" o.allocatedQty,o.sortedQty");
         hql.append(" )");
         hql.append(" from DeliveryOrderDetail o ");
         hql.append("where o.doHeaderId = :doHeaderId and o.isDoLeaf =:isDoLeaf and o.lineStatus in (:status) and o.warehouseId = :warehouseId");
         Query query = this.createQuery(hql.toString());
         query.setParameter("doHeaderId", doId);
         query.setParameter("isDoLeaf", 1);
         List<String> status = new ArrayList<String>();
         status.add(Constants.DoStatus.ALLPICKED.getValue());
         status.add(Constants.DoStatus.PARTSORTED.getValue());
         query.setParameterList("status", status);
         query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		 return (List<SortingDoDetailDTO>) query.list();
	 }
	 
    /**
     * 更新订单明细状态为目标状态
     * @param
     * @param status 目标状态
     */
    public void updateUnAllocDoDetailStatus(Long waveId, String status) {
    	String sql = " update doc_do_detail dd inner join doc_do_header d on dd.do_header_id = d.id and dd.warehouse_id = d.warehouse_id "+
				     " set dd.lineStatus =:status where d.wave_id = :waveId and dd.warehouse_id = :warehouseId and dd.allocated_qty = :allocatedQty  and dd.lineStatus = :lineStatus";
        Query query = this.createSQLQuery(sql);
        query.setParameter("status", status);
		query.setLong("waveId", waveId);
		query.setBigDecimal("allocatedQty", BigDecimal.ZERO);
		query.setString("lineStatus", DoStatus.ALLALLOCATED.getValue());
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }
	  
	/**
	 * 订单ID获取该订单的明细打印数据
	 * @param id
	 * @return
	 */
    @SuppressWarnings("unchecked")
    public List<DoPrintSub> getDoBatchDetail(Long id) {
        StringBuilder hql = new StringBuilder("select new com.daxia.wms.delivery.print.dto.DoPrintSub(");
        hql.append(" sum(o.qty),o.doDetailId,o.stockBatchAtt.lotatt05");
        hql.append(" )");
        hql.append(" from PickTask o ");
        hql.append("where o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId group by o.stockBatchAtt.lotatt05,o.doDetailId");
        Query query = this.createQuery(hql.toString());
        query.setParameter("doHeaderId", id);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (List<DoPrintSub>) query.list();
    }
    
	/**
	 * 人工分配时候更新明细信息，订单明细与订单分配明细的状态、分配数量一致
	 * @param doAllocateDetail
	 */
	public void updateManualAllocInfo(DoAllocateDetail doAllocateDetail) {
		String hqlQuery = "update DeliveryOrderDetail o set o.lineStatus = :lineStatus, o.allocatedQty = :allocatedQty where o.id = :id and o.warehouseId = :warehouseId";
		Query query = this.createUpdateQuery(hqlQuery);
		query.setParameter("lineStatus", doAllocateDetail.getLineStatus());
		query.setParameter("allocatedQty", doAllocateDetail.getAllocatedQty());
		query.setParameter("id", doAllocateDetail.getId());
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();
	}
	
	/**
	 * 根据doHeaderId更新明细状态为初始化，分配数量归零；
	 * @param doHeaderId
	 */
	public void updateDoDetail2Initial(Long doHeaderId) {
		String hqlQuery = "update DeliveryOrderDetail o set o.lineStatus = :lineStatus, o.allocatedQty = 0 where o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId";
		Query query = this.createUpdateQuery(hqlQuery);
		query.setParameter("lineStatus", Constants.DoStatus.INITIAL.getValue());
		query.setParameter("doHeaderId", doHeaderId);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();		
	}
	
	/**
	 * 根据doHeaderId查询do明细对应的打印数据
	 * @param doHeaderId
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<DoPrintSub> getDoDetails4Print(Long doHeaderId) {
        StringBuilder hql = new StringBuilder();
        hql.append(" select tsk.qty, tsk.doDetailId, tsk.lotatt05, d.sku_id, d.price, d.is_do_leaf, d.is_promote, d.parent_id, d.orig_detail_id, d.expected_qty, tsk.lotatt02,tsk.lotatt04");
        hql.append(" from doc_do_detail d left join (");
        hql.append("    select sum(p.qty) as qty, p.doc_line_id as doDetailId, att.lotatt05 as lotatt05,att.lotatt02,att.lotatt04 ");
        hql.append("    from tsk_pick p, stock_batch_att att");
        hql.append("    where p.lot_id = att.id and p.doc_id = :doHeaderId and p.warehouse_id = :warehouseId and p.is_deleted = 0");
        hql.append("    group by att.lotatt05, p.doc_line_id ) tsk");
        hql.append(" on d.id = tsk.doDetailId where d.do_header_id = :doHeaderId and d.warehouse_id = :warehouseId and d.is_deleted = 0 ");
        Query query = this.createSQLQuery(hql.toString());
        query.setParameter("doHeaderId", doHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        List<Object[]> objs =  (List<Object[]>) query.list();
        List<DoPrintSub> resultList = new ArrayList<DoPrintSub>();
        for (Object[] array : objs) {
            BigDecimal qty = (BigDecimal)array[0];
            Long doDetailId = array[1] == null ? null : ((BigInteger)array[1]).longValue();
            String lotNo = (String)array[2];
            Long skuId = array[3] == null ? null : ((BigInteger)array[3]).longValue();
            BigDecimal price = (BigDecimal)array[4];
            Integer isDoLeaf = ((BigInteger)array[5]).intValue();
            Integer isPromote = ((Byte)array[6]).intValue();
            String parentId = StringUtil.notNullString(array[7]);
            String origDetailId = StringUtil.notNullString(array[8]);
            BigDecimal expectedQty = (BigDecimal)array[9];
            DoPrintSub sub = new DoPrintSub(qty, doDetailId, lotNo, skuId, price, isDoLeaf, isPromote, parentId, origDetailId, expectedQty);
            sub.setValidate((String)array[10]);
            Long supplierId = array[11] == null ? null : Long.valueOf((String)array[11]);
            sub.setSupplierId(supplierId);
            resultList.add(sub);
        }
        return resultList;
	}
	
	/**
	 * 批量插入SLC订单的明细
	 */
	public void batchSaveDetail(DataList dataList) {
        String batchInputSQL = "insert into doc_do_detail "
                     + "       (do_header_id, linestatus, sku_id, expected_qty,   package_id, pack_detail_id, lotatt06, orig_header_id, orig_detail_id, is_valueables, allocation_rule, create_by, update_by, warehouse_id) "
                     + "SELECT  ?,            ?,          ?,      ?,                         -1,         -1,             ?,        -1,             -1,             ?,             ?,               ?,         ?,         ?           from dual "
                + " where exists (select 1 from  doc_do_header where id = ? and warehouse_id = ? and is_deleted  = 0)";

        // 取得准备批量插入数据库的数据集,定义数据集数组
        // 把上传文件的数据写入数据库
        DBUtil.batchUpdate(batchInputSQL, dataList.getInsertData());

        // 把已写入数据库的数据集清空，继续记录剩余的并写入数据库
        dataList.clear();
	}
	
	/**
	 * 删除SLC订单明细
	 */
	public void deleteDoDetail(Long doHeaderId) {
    	String sql = "delete from doc_do_detail where do_header_id = :doHeaderId  and warehouse_id = :whId";
    	Query query = createSQLQuery(sql);
    	query.setParameter("doHeaderId", doHeaderId);
    	query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
    	query.executeUpdate();
	}
	
	/**
	 * 更新DO明细状态为分拣完成 拣货数，分拣数 = 分配数
	 * @param doHeaderId
	 */
	public void updateDetails2Sorted(Long doHeaderId)  {
	    String hql = "update DeliveryOrderDetail o set "
	            + "o.pickedQty = o.allocatedQty, o.pickedQty = o.allocatedQty, o.sortedQty = o.allocatedQty, o.sortedQty = o.allocatedQty," +
                "o.lineStatus = :status where o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId";
	    
       Query query = createUpdateQuery(hql);
       query.setLong("doHeaderId", doHeaderId);
       query.setString("status", Constants.DoStatus.ALLSORTED.getValue());
       query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
       query.executeUpdate();
	}
	
	/**
	 * 根据商品条码查询制定DO商品ID
	 * @param doId
	 * @param skuCode
	 * @return
	 */
	@SuppressWarnings("unchecked")
    public List<Long> querySkuIdsInDoByBarcode(Long doId, String skuCode) {
	    StringBuilder hql = new StringBuilder();
        hql.append(" select o.skuId ");
        hql.append(" from DeliveryOrderDetail o , ProductBarcode p");
        hql.append(" where o.skuId=p.skuId  ");
        hql.append(" and o.doHeaderId =:doHeaderId  ");
        hql.append(" and o.warehouseId =:warehouseId  ");
        hql.append(" and p.barcodeLevel1 =:barcode ");
        Query query = this.createQuery(hql.toString());
        query.setLong("doHeaderId", doId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setString("barcode", skuCode);
        return query.list();
	}
	
	/**
	 * 根据商品编码查询制定DO商品ID
	 * @param doId
	 * @param skuCode
	 * @return
	 */
	@SuppressWarnings("unchecked")
    public List<Long> querySkuIdsInDoByProcode(Long doId, String skuCode) {
	        StringBuilder hql = new StringBuilder();
	        hql.append(" select o.skuId ");
	        hql.append(" from DeliveryOrderDetail o ");
	        hql.append(" where o.doHeaderId =:doHeaderId ");
	        hql.append(" and o.warehouseId =:warehouseId  ");
	        hql.append(" and o.sku.productCode =:productCode ");
	        Query query = this.createQuery(hql.toString());
	        query.setLong("doHeaderId", doId);
	        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
	        query.setString("productCode", skuCode);
	        return query.list();
	    }
	
	/**
	 * 清除装箱 明细状态状态回退
	 * @param doId
	 * @param skuIds
	 * @param status
	 */
	public void updateStatusBySkus(Long doId, List<Long> skuIds, String status) {
		if(ListUtil.isNullOrEmpty(skuIds) || doId== null || StringUtil.isEmpty(status)){
			return;
		}
		String hql = "update DeliveryOrderDetail o set o.lineStatus = :lineStatus where o.doHeaderId = :doHeaderId and o.skuId in (:skuIds) and o.warehouseId =:warehouseId";
		Query query = this.createUpdateQuery(hql)
				.setParameter("lineStatus", status)
				.setParameter("doHeaderId", doId)
				.setParameterList("skuIds", skuIds)
				.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();
	}
	/**
	 * 更新DO接口，删除缺货商品的明细
	 * @param ids
	 */
	public void deleteDoDetailByOrigIds (List<String> ids) {
	    String sql = "delete from DOC_DO_DETAIL where ORIG_DETAIL_ID in (:ids) and warehouse_id = :warehouseId";
	    Query query = createSQLQuery(sql);
	    query.setParameterList("ids", ids);
	    query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
	    query.executeUpdate();
	}

	public void removeByDoId (Long doId) {
		String sql = "delete from  doc_do_detail where do_header_id = :doId";
		Query query = createSQLQuery(sql);
		query.setLong("doId", doId);
		query.executeUpdate();
	}
	/**
	 * 更新DO接口，通过源明细id查找订单明细
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("unchecked")
    public List<DeliveryOrderDetail> findByOrigIds (List<String> ids) {
	    String hql = " from DeliveryOrderDetail o  WHERE o.origDetailId in (:ids) and o.warehouseId = :warehouseId ";
        Query query = this.getSession().createQuery(hql);
        query.setParameterList("ids", ids);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
	}

	public void updatePickQty(Long detailId,BigDecimal needReverseQty) {
		String hql = "update DeliveryOrderDetail o set " +
				" o.pickedQty = o.pickedQty - :needReverseQty, " +
				" o.sortedQty = o.sortedQty - :needReverseQty where o.id =:detailId and o.warehouseId = :warehouseId";
		Query query = this.createUpdateQuery(hql);
		query.setBigDecimal("needReverseQty", needReverseQty);
		query.setLong("detailId", detailId);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();
	}

	public void batchUpdateDoDetail4Pick(List<Long> batchIdList, String updateBy,Long waveId) {
		String sql = "update doc_do_detail dd " +
				"   inner join ( select sum(ifnull(t.qty_picked,0)) as qty_picked ,sum(ifnull(t.qty_picked_unit,0)) as qty_picked_unit, " +
				"  t.doc_line_id,t.warehouse_id from tsk_pick t " +
				"  where t.id in (:batchIdList) and t.warehouse_id =:warehouseId  and t.is_deleted=0 " +
				"  group by t.doc_line_id,t.warehouse_id ) p " +
				"  on p.doc_line_id = dd.id and dd.warehouse_id = p.warehouse_id " +
				" left join ( SELECT  count(*) as total, tp.doc_line_id,tp.warehouse_id " +
				" from tsk_pick tp where  tp.is_deleted =0 and tp.status not in (:pickTaskList) and tp.wave_h_id =:waveId " +
				" group by tp.doc_line_id,tp.warehouse_id) t" +
				" on t.doc_line_id = dd.id and  t.warehouse_id = dd.warehouse_id" +
				" set dd.linestatus = (case ifnull(t.total,0) when 0 then '60' else '50' end)," +
				" dd.update_by=:updateBy,dd.update_time=now(),dd.picked_qty=dd.picked_qty+p.qty_picked,dd.picked_qty_unit=dd.picked_qty_unit+p.qty_picked_unit,dd.pick_qty=dd.pick_qty+p.qty_picked " +
				" where dd.warehouse_id =:warehouseId and dd.is_deleted =0";

		SQLQuery sqlQuery = this.createSQLQuery(sql);
		sqlQuery.setParameter("updateBy", updateBy);
		sqlQuery.setParameter("waveId", waveId);
		sqlQuery.setParameterList("batchIdList", batchIdList);
		sqlQuery.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		List<String> pickTaskList = Lists.newArrayList();
		pickTaskList.add(DoStatus.ALLPICKED.getValue());
		pickTaskList.add(DoStatus.CANCELED.getValue());
		sqlQuery.setParameterList("pickTaskList", pickTaskList);
		sqlQuery.executeUpdate();
	}
	
	public void updateForPack(Long waveId) {
		String sql = "UPDATE doc_do_detail dd INNER JOIN doc_do_header dh ON dd.do_header_id = dh.id " +
				"SET dd.lineStatus = :lineStatus, dd.version = dd.version + 1, dd.update_time = now(), dd.update_by = :user  " +
				"WHERE dh.wave_id = :waveId AND dh.status = :status AND dh.release_status = :releaseStatus " +
				"AND dh.warehouse_id = :warehouseId AND dd.warehouse_id = :warehouseId AND dh.is_deleted = 0 AND dd.is_deleted = 0";
		Query query = this.createSQLQuery(sql);
		query.setParameter("lineStatus", DoStatus.ALL_CARTON.getValue());
		query.setParameter("status", DoStatus.ALL_CARTON.getValue());
		query.setParameter("releaseStatus", Constants.ReleaseStatus.RELEASE.getValue());
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameter("user", WmsUtil.getOperateBy());
		query.setParameter("waveId", waveId);
		query.executeUpdate();
	}

    public void clearReplNum(Long skuId) {
        String hql = "update DoAllocateDetail o set o.needReplQty = 0,o.needReplQtyUnit = 0,o.needReplQtyPcs = 0 where " +
                "o.skuId=:skuId " +
                "and o.warehouseId = :warehouseId " +
                "and o.needReplQty > 0";
        Query query = this.createUpdateQuery(hql);
        query.setParameter("skuId", skuId);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    public void clearNoStockFlag(Long skuId) {
        String hql = "update DoAllocateDetail o set o.noStockFlag = 0 where " +
                "o.skuId=:skuId " +
                "and o.warehouseId = :warehouseId " +
                "and o.noStockFlag = 1 ";
        Query query = this.createUpdateQuery(hql);
        query.setParameter("skuId", skuId);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    public Map<String, String> getDetailNotes(Long doHeaderId) {
		String hql="select tt.sku_id skuId,group_concat(tt.recheck_message) recheckMessage from ( select distinct dd.recheck_message,dd.sku_id from doc_do_detail dd where dd.do_header_id = :doHeaderId and dd.warehouse_id = :warehouseId " +
				"and dd.recheck_message is not null) tt group by tt.sku_id";
		SQLQuery query = this.createSQLQuery(hql);
		query.setLong("doHeaderId", doHeaderId);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.addScalar("skuId", Hibernate.LONG);
		query.addScalar("recheckMessage", Hibernate.STRING);
		@SuppressWarnings("unchecked")
		List<Object[]> result = (List<Object[]>) query.list();
		if (ListUtil.isNullOrEmpty(result)) {
			return null;
		}
		Map<String, String> map = new HashMap<String, String>();
		for (Object[] p : result) {
			String n = (String) p[1];
			map.put(((Long)p[0]).toString(), StringUtil.isNotBlank(n) ? n : "");
		}
		return map;
    }

    public List<DeliveryOrderDetail> findDetailByAsnId(Long asnId) {
        Criteria cri = getSession().createCriteria(DeliveryOrderDetail.class);
        cri.createAlias("doHeader", "doHeader");
        cri.add(Restrictions.eq("doHeader.sourceAsnId", asnId));
        cri.add(Restrictions.eq("doHeader.needCancel", Boolean.FALSE));
        cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
        cri.setFetchMode("doHeader", FetchMode.JOIN);
        cri.addOrder(Order.asc("id"));
        return cri.list();
    }

    public void updateStatus(List<Long> doIds, String updateBy, String lineStatus) {
        String hql = "update DeliveryOrderDetail o set o.lineStatus = :lineStatus,o.updatedBy = :updatedBy" +
                " where o.doHeaderId in (:ids) and o.warehouseId = :warehouseId";
        Query query = createUpdateQuery(hql);
        query.setString("lineStatus", lineStatus);
        query.setString("updatedBy", updateBy);
        query.setParameterList("ids", doIds);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    public void updateDetails2Packed(Long doHeaderId) {
        String hql = "update doc_do_detail o, (select sum(cd.packed_qty) packed_qty,cd.do_detail_id  "
                + "from doc_cross_seed_detail cd where cd.do_header_id =:doHeaderId and cd.warehouse_id = :warehouseId  "
                + " group by cd.do_detail_id ) tt "
                + " set o.packed_qty = tt.packed_qty, o.shipped_qty = tt.packed_qty, o.lineStatus = :status "
                + " where o.do_header_id = :doHeaderId and o.id = tt.do_detail_id and o.warehouse_id = :warehouseId";
        Query query = createSQLQuery(hql);
        query.setLong("doHeaderId", doHeaderId);
        query.setString("status", DoStatus.ALL_CARTON.getValue());
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }
}
