package com.daxia.wms.delivery.recheck.action;

import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.OrderFrozenException;
import com.daxia.wms.delivery.recheck.dto.ReCheckCartonDetail;
import com.daxia.wms.delivery.recheck.dto.ReCheckCartonInfo;
import com.daxia.wms.delivery.recheck.service.ClearReCheckRecordService;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.io.Serializable;
import java.util.List;

/**
 * 以已完成装箱的商品进行整箱删除， clearCarton.xhtml页面使用
 */
@Name("com.daxia.wms.delivery.clearCartonAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ClearCartonAction implements Serializable{
	private static final long serialVersionUID = 1L;

	private Long orderId;

	private String orderNo;

	/**
	 * 装箱编号，在界面中输入
	 */
	private String cartonNo;
	
	private Long cartonId;
	
	/**
	 * 是否清除成功
	 */
	private boolean clearSuccess;

	@In
	private ClearReCheckRecordService clearReCheckRecordService;

	/**
	 * 操作过程中的错误信息
	 */
	private String errorMessage;
	
	/**
	 * 清除的装箱中的装箱明细
	 */
	private List<ReCheckCartonDetail> resultList;
	
	/**
	 * 待清除的装箱信息，通过界面输入cartonNo来确定
	 */
	private ReCheckCartonInfo cartonInfo;

	public ReCheckCartonInfo getCartonInfo() {
		return cartonInfo;
	}

	public void setCartonInfo(ReCheckCartonInfo cartonInfo) {
		this.cartonInfo = cartonInfo;
	}

	public boolean isClearSuccess() {
		return clearSuccess;
	}

	public void setClearSuccess(boolean clearSuccess) {
		this.clearSuccess = clearSuccess;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public Long getCartonId() {
		return cartonId;
	}

	public void setCartonId(Long cartonId) {
		this.cartonId = cartonId;
	}

	public String getCartonNo() {
		return cartonNo;
	}

	public void setCartonNo(String cartonNo) {
		this.cartonNo = cartonNo;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public List<ReCheckCartonDetail> getResultList() {
		return resultList;
	}

	public void setResultList(List<ReCheckCartonDetail> resultList) {
		this.resultList = resultList;
	}

	/**
	 * 调用此方法查询Carton
	 */
	public void selectCarton() {
		orderId = null;
		orderNo = null;
		cartonId = null;
		clearSuccess = false;
		resultList = null;
		cartonInfo = null;
		
		if(cartonNo == null){
			return;
		}
		cartonNo = cartonNo.trim();
		cartonInfo = clearReCheckRecordService.findReCheckCartonInfo(cartonNo);
		if(cartonInfo == null){
			errorMessage = "recheck.carton.notexist";
			return;
		}
		orderNo = cartonInfo.getDoNo();
		orderId = cartonInfo.getDoHeader().getId();
		cartonId = cartonInfo.getCartonId();
		resultList = cartonInfo.getDetails();
		errorMessage = ""; 
	}


	public void deleteEmptyCarton(){
		errorMessage = null;
		if(resultList == null){
			return;
		}
		try{
			clearReCheckRecordService.clearEmptyCarton(orderId, cartonInfo.getCartonId());
		}catch(OrderFrozenException e){
			errorMessage = "recheck.do.frozen.error";
			return;
		}catch(DeliveryException e){
			errorMessage = e.getMessage();
			return;
		}catch(Exception e){
			log.error(e.getMessage(), e);
			errorMessage = "recheck.syserror";
			return;
		}
		clearSuccess = true;
		cartonNo = "";
		orderNo = "";
		cartonInfo = null;
		resultList = null;
	}
	
	/**
	 * 调用此方法进行整箱清除
	 */
	public void doClear(){
	    errorMessage = null;
		if(resultList == null){
			return;
		}
		try{
			clearReCheckRecordService.clearCarton(orderId, cartonInfo.getCartonId());
		}catch(OrderFrozenException e){
			errorMessage = "recheck.do.frozen.error";
			return;
		}catch(DeliveryException e){
			errorMessage = e.getMessage();
			return;
		}catch(Exception e){
			log.error(e.getMessage(), e);
			errorMessage = "recheck.syserror";
			return;
		}
		clearSuccess = true;
		cartonNo = "";
		orderNo = "";
		cartonInfo = null;
		resultList = null;
	}

	/**
	 * 调用此方法进行整箱清除
	 */
	public void doCrossClear(){
		errorMessage = null;
		if(resultList == null){
			return;
		}
		try{
			clearReCheckRecordService.clearCrossCarton(orderId, cartonInfo.getCartonId());
		}catch(OrderFrozenException e){
			errorMessage = "recheck.do.frozen.error";
			return;
		}catch(DeliveryException e){
			errorMessage = e.getMessage();
			return;
		}catch(Exception e){
			log.error(e.getMessage(), e);
			errorMessage = "recheck.syserror";
			return;
		}
		clearSuccess = true;
		cartonNo = "";
		orderNo = "";
		cartonInfo = null;
		resultList = null;
	}

}
