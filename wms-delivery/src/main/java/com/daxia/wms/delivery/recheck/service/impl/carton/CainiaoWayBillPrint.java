package com.daxia.wms.delivery.recheck.service.impl.carton;

import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.master.entity.Carrier;
import com.daxia.wms.master.entity.CarrierCainiaoEx;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.CarrierCainiaoExService;
import com.daxia.wms.master.service.ShopInfoService;
import com.daxia.wms.master.service.SkuCache;
import com.daxia.wms.waybill.cainiao.CainiaoConstants;
import com.google.common.collect.Lists;
import com.taobao.api.ApiException;
import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.request.WlbWaybillIPrintRequest;
import com.taobao.api.response.WlbWaybillIPrintResponse;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

@Name("cainiaoWayBillPrint")
@lombok.extern.slf4j.Slf4j
public class CainiaoWayBillPrint extends CainiaoWayBillBase {

    @In
    CarrierCainiaoExService carrierCainiaoExService;

    @In
    SkuCache skuCache;

    @In
    ShopInfoService shopInfoService;



    public Long reqeust(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        Carrier carrier = doHeader.getCarrier();
        CarrierCainiaoEx carrierCainiaoEx = carrierCainiaoExService.getByCarrier(carrier.getId());
        WarehouseCarrier warehouseCarrier = loadWarehouseCarrier(carrier.getId());
        WlbWaybillIPrintRequest req = new WlbWaybillIPrintRequest();
        req.setWaybillApplyPrintCheckRequest(genWaybillApplyPrintCheckRequest(doHeader, cartonHeader, carrierCainiaoEx));

        TaobaoClient client = new DefaultTaobaoClient(CainiaoConstants.getCainiaoConfig().getServerUrl(), warehouseCarrier.getAppKey(), warehouseCarrier.getAppSecret());
        try {
            WlbWaybillIPrintResponse rsp = client.execute(req, warehouseCarrier.getAppToken());

            if (!rsp.isSuccess()) {
                log.error("Cainiao print error, request:" + req.getWaybillApplyPrintCheckRequest() + ", response: " + rsp.getBody());
                throw new DeliveryException(DeliveryException.WAYBILL_CAINIO_PRINT_ERROR, rsp.getMsg() + ", " + rsp.getSubCode());
            }
            return rsp.getWaybillApplyPrintCheckInfos().get(0).getPrintQuantity();
        } catch (ApiException e) {
            log.error("Cainiao print error! ", e);
            throw new DeliveryException(DeliveryException.WAYBILL_CAINIO_PRINT_ERROR);
        }
    }

    private WlbWaybillIPrintRequest.WaybillApplyPrintCheckRequest genWaybillApplyPrintCheckRequest(DeliveryOrderHeader doHeader, CartonHeader cartonHeader,
            CarrierCainiaoEx carrierCainiaoEx) {
        WlbWaybillIPrintRequest.WaybillApplyPrintCheckRequest applyPrintCheckRequest = new WlbWaybillIPrintRequest.WaybillApplyPrintCheckRequest();
        // 物流服务商编码
        applyPrintCheckRequest.setCpCode(carrierCainiaoEx.getCpCode());
        applyPrintCheckRequest.setPrintCheckInfoCols(Lists.newArrayList(genPrintCheckInfoCols(doHeader, cartonHeader, carrierCainiaoEx)));
        return applyPrintCheckRequest;
    }

    // 面单详细信息
    private WlbWaybillIPrintRequest.PrintCheckInfo genPrintCheckInfoCols(DeliveryOrderHeader doHeader, CartonHeader cartonHeader, CarrierCainiaoEx carrierCainiaoEx) {
        WlbWaybillIPrintRequest.PrintCheckInfo printCheckInfo = new WlbWaybillIPrintRequest.PrintCheckInfo();
        printCheckInfo.setRealUserId(genRealUserId(doHeader, carrierCainiaoEx));
        // 收货人
        printCheckInfo.setConsigneeName(doHeader.getConsigneeName());
        // 电子面单单号
        printCheckInfo.setWaybillCode(cartonHeader.getWayBill());
        printCheckInfo.setWeight(getWeight(cartonHeader));
        // 收件人联系方式
        printCheckInfo.setConsigneePhone(DoUtil.decryptPhone(StringUtil.defaultIfEmpty(doHeader.getMobile(), doHeader.getTelephone())));
        if (printCheckInfo.getConsigneePhone().indexOf(",") > 0) {
            printCheckInfo.setConsigneePhone(printCheckInfo.getConsigneePhone().split(",")[0]);
        }

        printCheckInfo.setShippingAddress(genShippingAddress4Print(carrierCainiaoEx));
        printCheckInfo.setConsigneeAddress(genConsigneeAddress4Print(doHeader));
        printCheckInfo.setShortAddress(cartonHeader.getShortAddress());
        printCheckInfo.setShippingBranchCode(cartonHeader.getShippingBranchCode());
        printCheckInfo.setShippingBranchName(cartonHeader.getShippingBranchName());
        printCheckInfo.setPackageCenterCode(cartonHeader.getPackageCenterCode());
        printCheckInfo.setPackageCenterName(cartonHeader.getPackageCenterName());
        printCheckInfo.setLogisticsServiceList(genLogisticsServiceList4Print(doHeader));
        printCheckInfo.setProductType(CainiaoConstants.ProductType.STANDARD_EXPRESS.name());

        return printCheckInfo;
    }
}