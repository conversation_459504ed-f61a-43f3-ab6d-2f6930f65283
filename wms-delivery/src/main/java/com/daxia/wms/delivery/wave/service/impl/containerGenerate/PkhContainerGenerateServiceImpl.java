package com.daxia.wms.delivery.wave.service.impl.containerGenerate;

import com.daxia.wms.Constants;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.wave.service.impl.ContainerGenerateStrategy;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.util.ArrayList;
import java.util.List;

@Name("pkhContainerGenerateService")
@lombok.extern.slf4j.Slf4j
public class PkhContainerGenerateServiceImpl extends ContainerGenerateStrategy {

    @Override
    @Transactional
    public void saveContainerRef(PickHeader pickHeader) {
        List<String> docNoList = new ArrayList<String>();
        docNoList.add(pickHeader.getPktNo());
        saveContainerRef(docNoList, Constants.BindDocType.PICK.getValue());
    }

    @Override
    @Transactional
    public void delContainerRef(PickHeader pickHeader) {
        List<String> docNoList = new ArrayList<String>();
        docNoList.add(pickHeader.getPktNo());
        delContainerRef(docNoList,Constants.BindDocType.PICK.getValue());
    }
}
