package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;
import com.daxia.wms.delivery.recheck.service.EmsWayBillUpdateService;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.waybill.ems.dto.EmsBillNoRequest;
import com.daxia.wms.waybill.ems.service.EmsWaybillService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

@Name("emsCartonNoGenerateService")
@lombok.extern.slf4j.Slf4j
public class EmsCartonNoGenerateServiceImpl implements CartonNoGenerateService {
    @In
    WarehouseCarrierService warehouseCarrierService;

    @In(create = true)
    private EmsWaybillService emsWaybillService;

    @In
    WarehouseService warehouseService;
    @In
    EmsWayBillUpdateService emsWayBillUpdateService;

    @Override
    public void generatorCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        String orderNo = doHeader.getDoNo();
        if (StringUtil.isEmpty(orderNo)) {
            throw new DeliveryException(DeliveryException.DO_NO_IS_NULL);
        }
        WarehouseCarrier warehouseCarrier = warehouseCarrierService.getCarrierInfoByWarehouseIdAndCarrierId(doHeader.getWarehouseId(), doHeader.getCarrierId());
        EmsBillNoRequest emsBillNoRequest = new EmsBillNoRequest();
        emsBillNoRequest.setAppKey(warehouseCarrier.getAppKey());
        emsBillNoRequest.setSysAccount(warehouseCarrier.getExt1());
        emsBillNoRequest.setPassWord(warehouseCarrier.getExt2());
        emsBillNoRequest.setBusinessType(warehouseCarrier.getExt3());//标准快递还是经济快递
        String wayBill = emsWaybillService.getBillNumBySys(emsBillNoRequest, warehouseCarrier.getContentUrl());
        if (StringUtil.isNotBlank(wayBill)) {
            cartonHeader.setWayBill(wayBill);
            cartonHeader.setCartonNo(wayBill);
//            emsWayBillUpdateService.request(doHeader, cartonHeader, BigDecimal.ZERO);
        } else {
            throw new DeliveryException("发送运单号至EMS出错!");
        }
    }
}
