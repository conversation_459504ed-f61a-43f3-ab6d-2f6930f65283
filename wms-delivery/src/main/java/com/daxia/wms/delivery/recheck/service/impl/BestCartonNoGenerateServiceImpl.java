package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.delivery.deliveryorder.service.DoWaveExService;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.waybill.WaybillException;
import com.daxia.wms.waybill.htky.dto.BillPrintRequest;
import com.daxia.wms.waybill.htky.dto.BillPrintResponse;
import com.daxia.wms.waybill.htky.dto.PrintDetailInfo;
import com.daxia.wms.waybill.htky.service.BestWaybillService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.util.ArrayList;
import java.util.List;

@Name("bestCartonNoGenerateService")
@lombok.extern.slf4j.Slf4j
public class BestCartonNoGenerateServiceImpl implements CartonNoGenerateService {
    @In
    private DoWaveExService doWaveExService;
    @In(create = true)
    private BestWaybillService bestWaybillService;
    @In
    private WarehouseCarrierService warehouseCarrierService;
    @In
    WarehouseService warehouseService;
    @In
    private SequenceGeneratorService sequenceGeneratorService;

    @Override
    public void generatorCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        WarehouseCarrier warehouseCarrier = warehouseCarrierService.getCarrierInfoByWarehouseIdAndCarrierIdAndType(doHeader
                        .getWarehouseId(),
                doHeader
                        .getCarrierId(), Constants.WaybillType.HTKY.name());

        if (warehouseCarrier == null) {
            throw new WaybillException(WaybillException.WAREHOUSE_CARRIER_NOT_EXITS);
        }

        Warehouse warehouse = warehouseService.getWarehouse(doHeader.getWarehouseId());

        //生成一个箱号作为业务单号
        String txLogisticId = doHeader.getDoNo() + sequenceGeneratorService.generateSequenceNo(Constants.SequenceName
                .CARTONNO.getValue(), ParamUtil.getCurrentWarehouseId());
        cartonHeader.setTrackingNo(txLogisticId);

        BillPrintRequest request = new BillPrintRequest();
        PrintDetailInfo t = new PrintDetailInfo();
        t.setSendMan(warehouse.getContactor());
        t.setSendManPhone(warehouse.getPhone());
        t.setSendManAddress(warehouse.getAddressName());
        t.setSendProvince(warehouse.getProvince().getProvinceCname());
        t.setSendCity(warehouse.getCity().getCityCname());
        t.setSendCounty(warehouse.getCounty().getCountyCname());
        t.setReceiveMan(doHeader.getConsigneeName());
        t.setReceiveManPhone(DoUtil.decryptPhone(StringUtils.isNotEmpty(doHeader.getMobile()) ? doHeader.getMobile() :
                doHeader.getTelephone()));
        t.setReceiveManAddress(doHeader.getAddress());
        t.setReceiveProvince(StringUtils.isNotEmpty(doHeader.getProvinceName()) ? doHeader.getProvinceName() :
                doHeader.getProvinceInfo().getProvinceCname());
        t.setReceiveCity(StringUtils.isNotEmpty(doHeader.getCityName()) ? doHeader.getCityName() :
                doHeader.getCityInfo().getCityCname());
        t.setReceiveCounty(StringUtils.isNotEmpty(doHeader.getCountyName()) ? doHeader.getCountyName() :
                doHeader.getCountyInfo().getCountyCname());
        t.setTxLogisticID(txLogisticId);
        t.setRemark(doHeader.getPlatformRemark());
        List<PrintDetailInfo> infoList = new ArrayList<PrintDetailInfo>();
        infoList.add(t);
        request.setDetailInfoList(infoList);

        BillPrintResponse response = bestWaybillService.generateWabybillNo(request, warehouseCarrier);

        if (response == null) {
            //接口调用失败
            throw new WaybillException(WaybillException.BEST_WAYBILL_ERROR);
        }
        List<PrintDetailInfo> detailInfoList = response.getDetailInfoList();
        if (CollectionUtils.isEmpty(detailInfoList)) {
            throw new WaybillException(WaybillException.BEST_WAYBILL_ERROR);
        }
        PrintDetailInfo info = detailInfoList.get(0);
        if (StringUtils.isBlank(info.getMailNo())) {
            throw new WaybillException(WaybillException.BEST_WAYBILL_ERROR);
        }
        //运单号
        cartonHeader.setCartonNo(info.getMailNo());
        cartonHeader.setWayBill(info.getMailNo());
        DoWaveEx doWaveEx = doWaveExService.findByDoHeaderId(doHeader.getId());
        if (doWaveEx != null) {
            //大头笔信息
            doWaveEx.setShortAddress(info.getMarkDestination());
            //集包地
            doWaveEx.setPackageCenterName(info.getPkgCode());
            //始发站点名称
            doWaveEx.setOriginName(info.getBillProvideSiteName());
            doWaveEx.setOriginCode(info.getBillProvideSiteCode());
            //末端分拣号,名称
            doWaveEx.setDestinationCode(info.getSortingCode());
            doWaveEx.setDestinationName(info.getSortingSiteName());
            doWaveExService.update(doWaveEx);
        }

    }
}