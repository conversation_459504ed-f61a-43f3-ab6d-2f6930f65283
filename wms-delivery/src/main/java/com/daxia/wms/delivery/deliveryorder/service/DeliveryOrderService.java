package com.daxia.wms.delivery.deliveryorder.service;

import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.system.util.DataList;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dto.DoDetailDto;
import com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto;
import com.daxia.wms.delivery.deliveryorder.dto.DoTimeInfoDto;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeaderHis;
import com.daxia.wms.delivery.deliveryorder.filter.BackExceptionFilter;
import com.daxia.wms.delivery.deliveryorder.filter.DoHeaderFilter;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.dto.DoPrintSub;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.wave.dto.WaveDetailDTO;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.master.dto.AutoCompleteDTO;
import org.apache.commons.lang3.tuple.Pair;
import org.hibernate.criterion.DetachedCriteria;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Description:发运订单头信息业务Service接口
 */
public interface DeliveryOrderService {

    public DeliveryOrderHeader findDoHeaderByDoNo(String DoNo) throws DeliveryException;

    /**
     * 根据trackingNo查询订单DeliveryOrderHeader（不存在不抛异常）
     * @param trackingNo 物流单号
     * @return DeliveryOrderHeader
     */
    public DeliveryOrderHeader findDoHeaderByTrackingNo(String trackingNo);
    
    /**
     * 
     * <pre>
     * 分页查询送货单信息。
     * </pre>
     * 
     * @param doHeaderFilter
     *            查询条件过滤器
     * @param startIndex
     *            初始页
     * @param pageSize
     *            显示数据条数
     * @return
     */
    public DataPage<DoHeaderDto> query(DoHeaderFilter doHeaderFilter, int startIndex,int pageSize);

    /**
     * 
     * <pre>
     * Description:取消订单
     * </pre>
     *
     * @param doHeaderId 要取消的发货单对象主键
     * @throws DeliveryException 取消操作出错时抛出异常
     */
    public void cancel(Long doHeaderId) throws DeliveryException;

    /**
     * 手动冻结
     * @param doHeaderId
     * @param reasonCode
     * @param holdNotes
     */
    public void manualFrozen(Long doHeaderId, String reasonCode, String holdNotes, String frozenBy);

    public void manualFrozenForHigh(Long doHeaderId, String reasonCode, String holdNotes, String frozenBy);

    /**
     * 
     * <pre>
     * Description:更新送货头信息
     * </pre>
     *
     * @param doHeader
     */
    public void updateDoHeader(DeliveryOrderHeader doHeader);
    
    /**
     * 获取do
     * @param waveId
     */
    public List<DeliveryOrderHeader> qureyDoHeaderByWaveId(Long waveId);
    
    /**
     * 获取doIds
     * @param waveId
     */
    public List<Long> qureyDoHeaderIdsByWaveId(Long waveId);

    /**
     * 
     * <pre>
     * Description:根据Id获得发货单明细
     * </pre>
     *
     * @param doDetailId
     * @return DoDetail
     */
    public DeliveryOrderDetail getDoDetail(Long doDetailId);

    /**
     * 
     * <pre>
     * 更新送货信息
     * </pre>
     * 
     * @param doDetail
     * 
     */
    public void updateDoDetail(DeliveryOrderDetail doDetail);
    
    /**
     * 
     * <pre>
     * Description:通过headerId查询单位
     * </pre>
     *
     * @param doHeaderId
     */
    public String getUomByHeaderId(Long doHeaderId);

    /**
     * 
     *保存DoHeader
     * @param deliveryOrderHeader
     */
    public void saveOrUpdateDoHeader(DeliveryOrderHeader deliveryOrderHeader);

    /**
     * 
     * <pre>
     * Description:根据订单Id获取对应订单明细
     * </pre>
     *
     * @param doHeaderId
     * @return
     */
    public List<DeliveryOrderDetail> getDoDetailByDoHeaderId(Long doHeaderId);

    /**
     * 
     * <pre>
     * Description:根据订单Id 更新订单明细行状态为status
     * </pre>
     *
     * @param doHeaderId 发运订单头信息Id
     * @param status 状态值
     */
    public void updateDoDetailStatusByDoId(Long doHeaderId, String status);

    /**
     * 
     * @param doIds
     * @return   list<释放的订单id，不能释放的订单no>
     */
    public List<List<String>> releaseDO(List<Long> doIds);

    /**
     * 按Criteria进行查询
     * @param queryCri 包含查询条件的Criteria
     * @param countCri 包含统计行数条件的Criteria
     * @param startIndex 分页开始记录
     * @param pageSize 查询记录数
     * @return
     */
    public DataPage<DeliveryOrderHeader> query(DetachedCriteria queryCri,
                                               DetachedCriteria countCri, int startIndex,
                                               int pageSize);

    /**
     * 
     * <pre>
     * Description:将do单已经拣货数量归零,分配数归零，状态变为初始化
     * </pre>
     *
     * @param doHeaderId
     */
    public void doDoDetailBackToInit(Long doHeaderId,String lineStatus);
    
    /**
     * 更新DO明细状态和数量(分配数和拣货数都为qty)
     * @param doDetailId
     * @param status
     * @param qty
     */
    public void updateDoDetailStatusAndQty(Long doDetailId, String status, BigDecimal qty);

    /**
     * <pre>
     * 没有分页的查询doHeader
     * </pre>
     *
     * @param doHeaderFilter
     * @return
     */
    public List<DeliveryOrderHeader> query(DoHeaderFilter doHeaderFilter);
    /**
     * <pre>
     * 没有分页的查询doHeader单号
     * </pre>
     *
     * @param doHeaderFilter
     * @return
     */
    List<String> queryNos(DoHeaderFilter doHeaderFilter);

    /**
     * 
     * <pre>
     * Description:根据主键删除doHeader信息
     * </pre>
     *
     * @param doHeaderId 主键
     */
    public void remove(Long doHeaderId);

    /**
     * 查询分拣中或拣货完成状态的发货单明细
     * @param waveId
     * @param skuId
     * @return
     */
    public List<DeliveryOrderDetail> findSortingDoDetails(Long waveId, Long skuId);

    public int countUnSortingDoDetail(Long doHeaderId);

    public int countUnSortingDoHeader(Long waveId);

    public List<DeliveryOrderDetail> findSortingDoDetailInWave(String waveNo , boolean isSorted);

    public void updateDoHeaderByWaveId(Long waveId, String filterStatus);

    public void updateDoDetailByWaveId(Long waveId, String filterStatus);

    /**
     * 清空DoDetail补货数量
     * @param skuId doDetail关联的sku 
     * @param lotatt04 doDetail关联的sku的批次属性
     * @param lotatt06 doDetail关联的sku的批次属性
     * @param lotatt08 doDetail关联的sku的批次属性
     */
    public void clearDoDetailReplNum(Long skuId, String lotatt04, String lotatt06, String lotatt08);

    
    /**
     * 检查doHeader下面所有的doDetail是否已补货完毕，如果补货完毕，则修改doHeader的状态，允许再次被自动分配
     */
    public void checkDoHeaderReplStatus(List<Long> skuIds);
    
    public void upddateDoWeight(Long doId);
    
    /**
     * 通过拣货或移位导致拣货位库存变化时触发该方法
     * @param skuId
     * @param lotatt04
     * @param lotatt06
     * @param lotatt08
     */
    public void onPickLocationStockChange(Long skuId, String lotatt04,
			String lotatt06, String lotatt08);
    
	public void updateDOs4Wave(DeliveryOrderHeader doHeader, Long waveId, int sortGridNo);
	
	/**
	 * 判断发货单是否分拣完成状态
	 * @param doHeaderId
	 * @return
	 */
	public boolean isDoHeaderSorted(Long doHeaderId);

    public List<DoPrintSub> getDoBatchDetail(Long id);
    
    public void updateUnAllocDoDetails(WaveHeader waveId);
    
    public List<DeliveryOrderDetail> findDoDetailByWaveId(Long waveId);
    
    /**
     * RF拣货缺货自动冻结
     */
    public  void autoFrozenOnRFPick(Long doHeaderId, Long taskId, String holdWho, String reason);
    
    /**
     * @param doId
     * @return excStatus object[0]; releaseStatus object[1]; doNo object[2]
     */
    public Object getDoHeaderExceptionStatus(Long doId);
    
    /**
     * 根据订单头取得组装订单日志信息
     * @param doHeader
     * @return
     */
    public DoTimeInfoDto getDoTimeInfo(DeliveryOrderHeader doHeader);
    
    /**
     * 根据订单头Id取得组装订单日志信息
     * @param doHeaderId
     * @param isHis 
     * @return
     */
    public DoTimeInfoDto getDoTimeInfo(Long doHeaderId, boolean isHis);

    public List<Long> findDoIdByWaveId(List<Long> ids);
    
    public DeliveryOrderHeader getDoHeaderById(Long id);

	DeliveryOrderHeader findDoHeaderByDoNoWithoutException(String doNo);
    
    public DeliveryOrderHeader validate2Frozen(Long doHeaderId, String reasonCode, boolean b);

    public void frozen(DeliveryOrderHeader frozenDoHeader, String reasonCode, String holdNotes, String holdWho,
                       String value, String opType);
	
	/**
	 * 根据发货单Id状态回退其拣货任务
	 * 1.isDelete==true删除拣货任务，否则清空拣货任务的和波次、拣货单头、容器的关联
	 * 2.判断撤销do对应的拣货任务绑定的容器是否需要释放：若没有其他拣货任务绑定到相应的容器，则释放，否则不必释放
	 * @param doHeaderId
	 * @param isDelete 是否删除拣货任务标识
	 */
    public void unrelate(Long doHeaderId, boolean isDelete);
    
    /**
     * 获取波次下的任意一个do
     * @param waveId
     * @return
     */
    public DeliveryOrderHeader getDoHeaderByWaveId(Long waveId);

	/**
	 * 将波次waveHeaderId下的DO的分拣柜更新为指定分拣柜IdsortingBinNo
	 * @param doHeader
	 * @param waveHeaderId
	 * @param sortingBinId
	 */
	public void updateDoSortingBinByWave(DeliveryOrderHeader doHeader, Long waveHeaderId, Long sortingBinId);
    
    public DeliveryOrderHeaderHis getHeaderHisById(Long doHeaderId);

    /**
     * 查询可释放的DO集合
     * @return
     */
    public List<Long> queryAllCanReleaseDos();
    
    //移除波次、生成波次的时候更新波次的发票信息
    public void updateWaveInvoiceInfo(WaveHeader waveHeader, List<DeliveryOrderHeader> doHeaders, Long excludeId);

    /**
     * 根据do头id获取明细信息作为打印数据
     * @param doHeaderId
     * @return
     */
    public List<DoPrintSub> getDoDetails4Print(Long doHeaderId);
    
    /**
     * 根据id查询未打波次订单号
     * @param ids
     * @return
     */
    public List<String> getNotWavedDoNosByIds(List<Long> ids);
    
    /**
     * 更新DO明细状态到分拣完成,分拣数=分配数，拣货数=分配数
     * @param doHeaderId
     */
    public void updateDetails2Sorted(Long doHeaderId);
    
    /**
     * 根据dto保存SLC订单
     */
    public Long saveDoHeader(DoHeaderDto doHeaderDto);
    
    /**
     * 批量插入SLC订单的明细并验证是否全部插入
     */
    public void saveDoDetail(Long doHeaderId, DataList dataList, Integer totalNum);
    
    /**
     * 删除do及分配表
     */
    public void deleteDo(Long doHeaderId);
    
    /**
     * 更新do及分配表头的订货数量及是否贵重品
     */
    public void updateHeaderTotalQtyAndValuable(Long doId, BigDecimal totalQty, boolean isValuableFlag);
    
    /**
     * 根据商品条码查询制定DO商品ID
     * @param doId
     * @param skuCode
     * @return
     */
    public List<Long> querySkuIdsInDoByBarcode(Long doId, String skuCode);
    
    /**
     * 根据商品编码查询制定DO商品ID
     * @param doId
     * @param skuCode
     * @return
     */
    public Long querySkuIdsInDoByProcode(Long doId, String skuCode);
    
    /**
     * 根据发货单号查询发货单
     * @param doIds
     * @return
     */
    public List<DeliveryOrderHeader> findDoByIds(List<Long> doIds);
    
    /**
     * 是否需要绑定分拣箱
     * @return
     */
    public Boolean getNeedSortContainer();
    
    
    /**
     * 根据波次ID，商品编码，状态获取订单明细 并合并
     * @param waveId 波次ID
     * @param barcode 商品编码
     * @param statuses 状态
     * @return
     */
    public List<Long> findSkuByProductCodeAndWaveId(Long waveId, String barcode, String[] statuses);
    
    /**
     * 根据波次ID，条码，状态获取SKU的ID列表 并合并
     * @param waveId 波次ID
     * @param barcode 条码
     * @param statuses 状态
     * @return
     */
    public List<Long> findSkuByBarcodeAndWave(Long waveId, String barcode, String[] statuses);
    
    /**
     * 验证发货单异常时是否已绑定分拣筐
     * @param doHeader
     */
    public void checkExDoNeedSortContainer(DeliveryOrderHeader doHeader);

	public boolean needPrintInNewWay();
	
	/**
	 * 获取订单所属波次的波次号
	 * 
	 * @param doId
	 * @return
	 */
	public String getWaveNoByDoId(Long doId);
	
	/**
	 * 根据箱获取do信息
	 * @param cartonIds
	 * @return
	 */
	public List<DeliveryOrderHeader> findDoByCartonIds(List<Long> cartonIds);
	/**
	 * 状态回退到初始化
	 * @param doHeader
	 */
	public void doRollBackAllocDo2Init(DeliveryOrderHeader doHeader);
	
	/**
	 * 根据单号或来源单号查询RTV do
	 * @param  doNo
     * @param whId
	 */
	public DeliveryOrderHeader findRtvByOrderCodeOrRefNo(String doNo,Long whId);
	
	public List<DeliveryOrderDetail> getDoLeafDetailByDoHeaderId(Long doHeaderId);
	
	/**
	 * 根据波次id和分拣筐编号查找分拣格号
	 * @param waveId
	 * @param containerNo
	 * @param containerType
	 * @return
	 */
	public DoHeaderDto findByWaveIdAndConNo(Long waveId,String containerNo,String containerType);

	void logPackedBy(DeliveryOrderHeader doHeader);
	
    public  List<DeliveryOrderHeader> queryDoHeadersInWaves(List<Long> waveIds);
    
    public DeliveryOrderHeader getHeaderById(Long doHeaderId);
    
    public List<Long> releaseNotLackDos(List<Long> doIds);
    
    /**
     * 修改配送商
     * @param fmCarrierId
     * @param doHeader
     */
    public void modifyCarrier(Long fmCarrierId, DeliveryOrderHeader doHeader);

    /**
     * 查询交接单中已冻结的订单号
     * @param loadHeaderId
     * @return
     */
	public String findFrozenOrdersByLoadHeaderId(Long loadHeaderId);
	
	
	/**
	 * 获取顺丰主单号，并更新到订单上
	 * @param doHeader
	 */
	public void generateAndUpdateSfMainNo(DeliveryOrderHeader doHeader,CartonHeader cartonHeader) throws Exception;

	/**
	 * 
	 * @param id
	 * @param value
	 */
	public void updateStateByLoadHeaderId(Long id, String value);

    /**
     * 查询波次下订单的冻结、释放数量
     * @param waveId
     */
    Map<Constants.ReleaseStatus,Integer> countByReleaseStatus(Long waveId);

    /**
     * 查询波次下订单的最大状态，看是否可以快速核检
     * @param id
     * @return
     */
    String getMaxStatus(Long id);

    /**
     * 订单分配数量
     * @param doHeaderId
     * @return
     */
    BigDecimal getAllocateQty(Long doHeaderId);

    /**
     * 按波次更新订单重量
     * @param waveId
     * @param weight
     */
    void updateWeightByWaveId(Long waveId, BigDecimal weight);

    /**
     * 查询需要电子面单预下单的订单
     * @return
     */
    List<Long> findList4TempCarton(Integer rows, Long warehouseId, boolean localWaybillFlag, String jobParameter);

    /**
     * 修改地址、修改配送商记录日志
     * @param doHeader
     */
    void insertOrderLog4Modify(DeliveryOrderHeader doHeader);
    void insertOrderLog4Modify(DeliveryOrderHeader doHeader,boolean isOperatorNull);
	    public  byte[] exportDeliveryOrderHeaderReport(DoHeaderFilter doHeaderFilter);

    BigDecimal countAllResults(DoHeaderFilter doHeaderFilter);

    List<DoHeaderDto> getAllResults(DoHeaderFilter doHeaderFilter);

    List<AutoCompleteDTO> findSkuHelperData(Long doId);

    void batchModifyCarrier(List<Long> idList, Long toCarrierId);

    void sendInvoice2Oms(WaveHeader waveHeader);

    /**
     * 根据拣货任务批量更新订单明细拣货状态
     * @param batchIdList
     * @param updateBy
     */
    void batchUpdateDoDetail4Pick(List<Long> batchIdList, String updateBy,Long waveId);

    /**
     * 根据拣货任务批量更新订单头状态
     * @param batchIdList
     * @param updateBy
     */
    void batchUpdateDoHeader4Pick(List<Long> batchIdList, String updateBy,Long waveId);

    /**
     * 通过拣货任务list 获取冻结订单
     * @param batchIdList
     * @return
     */
    List<Long> getHoldDoListByPickTask(List<Long> batchIdList);
    
    List<WaveDetailDTO> qureyWaveDetail(Long waveId);

    String getNoticeDesc(DeliveryOrderHeader doHeader, String operateType);

    List<Long> getIdListByWaveIds(List<Long> ids);

    DeliveryOrderHeader getByTempCartonNo(String orderNo);

    List<Long> getIdListByWaveIdAndSrotGrid(Long waveId,String sortGridFm,String sortGridTo);

    DataPage<DeliveryOrderHeader> queryExceptionDo(BackExceptionFilter backExceptionFilter, int startIndex, int pageSize);

    void cleanGroup(Long doId) throws Exception;

    List<DoDetailDto> getAllDetailResults(DoHeaderFilter doHeaderFilter);

    BigDecimal countAllDetailResults(DoHeaderFilter doHeaderFilter);

    Long getNeedSyncSerialNoCount(Long doId);
    
    Long getLineId(Long id);

    List<CartonPrintDTO.ProductInfo> getGiftProductInfo(DeliveryOrderHeader doHeader);

    String queryProductInfo(DeliveryOrderHeader doHeader);

    /**
     * 生成波次时验证紧急订单混打问题
     * @param selectedDOIdList
     */
    void checkEmergencyOrder4GenerateWave(List<Long> selectedDOIdList);

/**
     * 根据订单号后几位查询订单号，RF波次拣货使用
     * @param doNo
     * @return
     */
    List<String> findDoNoList4WavePickByPartDoNo(String doNo);

    void clearSession();

    void batchManualFrozen(List<Long> idList, String holdCode, String holdNotes, String operateBy);

    List<String> findPrintedCartonNos(String doNo);

    List<String> findDoNos4RecheckByPartDoNo(String doNo);

    void updatePrintFlag(List<Long> doIds);

    List<DeliveryOrderHeader> getBySourceAsnIds(List<Long> asnHeaderIds);

    /**
     * 出库单取消拦截
     * @param doId
     * @return true:取消,false:不取消
     */
    boolean doCancelIntercept(Long doId);

    DeliveryOrderHeader getDoHeaderByContainerNo(String containerNo);

    void completePackForCrossDo(Long id);

    List<Long> findCtnReCheckDoIds();

    void updateFailedNumber(Long doId);

    void modifyCarrier(List<Long> doIdList, Long carrierId);

    boolean checkDoStatus(List<Long> doIdList, String fmStatus, String toStatus);

    /**
     * 根据单号查询
     * @param doNoList
     * @param warehouseId
     * @return
     */
    List<DeliveryOrderHeader> queryByNos(List<String> doNoList,Long warehouseId);

    /**
     * 根据拣货任务以及订单状态,查询满足条件的订单信息
     * @param batchIdList 拣货任务IDList
     * @param status 订单状态
     * @return 满足条件的订单信息
     */
    List<Long> getDoListByPickTaskAndStatus(List<Long> batchIdList,String status);

    /**
     * 更新订单称重标识
     */
    void autoUpdateWeightFlag();
    /**
     * 更新订单称重标识
     */
    void updateWeightFlag(List<Long> ids);

    /**
     * 自动交接
     * @param doId
     */
    void autoLoad(Long doId);

    /**
     * 马来仓生成发票
     */
    void geneInvoices(List<Long> ids);

    String fixGroupWaveDoMaterialsLog(Long waveId);
}
