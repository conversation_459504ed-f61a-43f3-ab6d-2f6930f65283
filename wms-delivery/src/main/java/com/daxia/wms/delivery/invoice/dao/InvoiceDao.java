package com.daxia.wms.delivery.invoice.dao;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.system.constants.Constants.YesNo;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.PrintStatus;
import com.daxia.wms.delivery.invoice.dto.ShowInvoiceOfDO;
import com.daxia.wms.delivery.invoice.entity.InvoiceBook;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader.InvoiceType;
import com.daxia.wms.delivery.invoice.filter.InvoiceFilter;
import com.daxia.wms.delivery.wave.entity.WaveHeader;


/**
 * 发票信息DAO
 */
@Name("com.daxia.wms.delivery.invoiceDao")
@lombok.extern.slf4j.Slf4j
public class InvoiceDao extends HibernateBaseDAO<InvoiceHeader, Long> {

	private static final long serialVersionUID = -1487519590084131915L;

	/**
	 * 更新已打印发票头的信息
	 * 
	 * @param ids
	 */
	public void setInvoicePrinted(List<Long> ids) {
		Query query = this
				.createUpdateQuery("UPDATE InvoiceHeader o SET o.invoicePrintFlag = :invoicePrintFlag WHERE o.id in (:ids) and o.warehouseId = :warehouseId");
		query.setParameterList("ids", ids);
		query.setParameter("invoicePrintFlag", 1);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());

		query.executeUpdate();
	}

	/**
	 * 查询DO和发票的绑定明细
	 * 
	 * @param doHeaderFilter
	 *            查询条件过滤器
	 * @param startIndex
	 *            查询起始位置
	 * @param pageSize
	 *            分页单位
	 * @return 发货单分页信息
	 */
	@SuppressWarnings("unchecked")
	public DataPage<ShowInvoiceOfDO> getInvoicePageInfoOfDo(
			InvoiceFilter invoiceFilter, int startIndex, int pageSize) {
		Long whId = ParamUtil.getCurrentWarehouseId();
		StringBuilder hql = new StringBuilder(
				"select new com.daxia.wms.delivery.invoice.dto.ShowInvoiceOfDO(");
		hql.append(" inv.invoiceNo, o.id, o.sortBy, inv.invoiceCode, inv.status");
		hql.append(" )");
		hql.append(" from InvoiceHeader o ");
		hql.append(" left join o.invoiceNoInfo inv where o.warehouseId = "
				+ whId);

		StringBuilder countHql = new StringBuilder("select count(o.id) ");
		countHql.append(" from InvoiceHeader o where o.warehouseId = " + whId);

		return (DataPage<ShowInvoiceOfDO>) this.executeQueryByFilter(
				hql.toString(), countHql.toString(), startIndex, pageSize,
				invoiceFilter);
	}

	/**
	 * 更新DO接口，删除之前的老发票头
	 * 
	 * @param ids
	 */
	public void deleteInvoiceHeaderByIds(List<Long> ids) {
		String sql = "delete from DOC_INVOICE_HEADER where id in (:ids) and warehouse_id = :warehouseId";
		Query query = createSQLQuery(sql);
		query.setParameterList("ids", ids);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();
	}

	/**
	 * 更新DO接口，删除之前的老发票头
	 * 
	 * @param ids
	 */
	public void deleteInvoiceDetailByHeaderIds(List<Long> ids) {
		String sql = "delete from doc_invoice_detail where INVOICE_H_ID in (:ids) and warehouse_id = :warehouseId";
		Query query = createSQLQuery(sql);
		query.setParameterList("ids", ids);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();
	}

	@SuppressWarnings("unchecked")
	public List<WaveHeader> getWaveByInvoiceId(List<Long> ids) {
		String sqlQuery = "SELECT DISTINCT wh FROM WaveHeader wh, DeliveryOrderHeader do, InvoiceHeader ih "
				+ "WHERE ih.id in (:ids) AND do.id = ih.doHeaderId AND do.waveId = wh.id AND ih.warehouseId = :warehouseId AND wh.invoicePrintFlag in (:invoicePrintFlag)";
		Query query = createQuery(sqlQuery);
		query.setParameterList("ids", ids);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		List<Integer> printStatus = new ArrayList<Integer>();
		printStatus.add(PrintStatus.NO.getValue());
		printStatus.add(PrintStatus.PARTIAL.getValue());
		query.setParameterList("invoicePrintFlag", printStatus);
		return query.list();
	}

	@SuppressWarnings("unchecked")
	public List<Long> getInvoiceHIdsByDoIds(List<Long> doIds) {
		String hql = " select t.id from InvoiceHeader t where t.doHeaderId in (:doIds) and t.invoicePrintFlag = :invoicePrintFlag and t.warehouseId = :warehouseId";
		Query query = this.createQuery(hql);
		query.setParameterList("doIds", doIds);
		query.setParameter("invoicePrintFlag", Long.valueOf(YesNo.NO.getValue()));
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		return query.list();
	}

	/**
	 * 获取波次需要的发票
	 * 
	 * @param waveNo
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<InvoiceHeader> getInvoiceList4PrintByWave(String waveNo) {
		StringBuilder hql = new StringBuilder();
		hql.append("select invoice from WaveHeader wave, DeliveryOrderHeader do, InvoiceHeader invoice ");
		hql.append("    where wave.waveNo = :waveNo  and wave.warehouseId = :warehouseId ");
		hql.append("    and do.waveId = wave.id and do.warehouseId = :warehouseId ");
		hql.append("    and invoice.doHeaderId = do.id and invoice.warehouseId= :warehouseId");
		hql.append("    and do.needCancel != :needCancel");
		hql.append("    and invoice.invoicePrintFlag = 0 order by do.sortGridNo asc, invoice.sortBy asc");
		Query query = this.createQuery(hql.toString());
		query.setString("waveNo", waveNo);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameter("needCancel", Boolean.TRUE);
		return query.list();
	}

	/**
	 * 根据id查询用以打印的发票并排序
	 * 
	 * @param idList
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<InvoiceHeader> getInvoiceList4Print(List<Long> idList) {
		String hql = "from InvoiceHeader o where o.id in (:idList) and o.warehouseId = :warehouseId order by o.deliveryOrderHeader.waveId asc, o.deliveryOrderHeader.sortGridNo asc, o.sortBy asc";
		Query query = this.createQuery(hql);
		query.setParameterList("idList", idList);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		return query.list();
	}

	/**
	 * 获取DO未打印的发票并排序
	 * 
	 * @param doId
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<InvoiceHeader> getNotPrintedInvoiceInDo(Long doId) {
		String hql = "from InvoiceHeader o where o.doHeaderId = :doId and o.invoicePrintFlag = :invoicePrintFlag and o.warehouseId = :warehouseId order by o.deliveryOrderHeader.sortGridNo asc, o.sortBy asc";
		Query query = this.createQuery(hql);
		query.setLong("doId", doId);
		query.setParameter("invoicePrintFlag", YesNo.NO.getValue());
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		return query.list();
	}

	/**
	 * 查询波次下发票数(排除取消DO)
	 * 
	 * @param waveNo
	 * @return
	 */
	public BigDecimal queryWaveInvoiceCount(String waveNo) {
		BigDecimal total = BigDecimal.ZERO;
		String sql = "select sum(ifnull(dh.invoice_qty, 0)) from doc_wave_header w,doc_do_header dh where w.id = dh.wave_id "
				+ " and w.wave_no = :waveNo and dh.need_cancel <> 1 "
				+ " and w.warehouse_id =:whId and w.is_deleted = 0 and dh.warehouse_id = :whId and w.is_deleted = 0 ";
		Query query = this.createSQLQuery(sql).setParameter("waveNo", waveNo)
				.setParameter("whId", ParamUtil.getCurrentWarehouseId());
		Object obj = query.uniqueResult();
		if (null != obj) {
			total = (BigDecimal) obj;
		}
		return total;
	}

	/**
	 * 根据波次id查询已打印的发票
	 * 
	 * @param waveId
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<Long> getInvoiceHeaderIdByWaveId(Long waveId) {
		StringBuilder hql = new StringBuilder();
		hql.append("select invoice.id from DeliveryOrderHeader do, InvoiceHeader invoice ");
		hql.append("    where do.waveId = :waveId and do.warehouseId = :warehouseId ");
		hql.append("    and invoice.doHeaderId = do.id and invoice.warehouseId= :warehouseId");
		hql.append("    order by do.sortGridNo asc, invoice.sortBy asc");
		Query query = this.createQuery(hql.toString());
		query.setLong("waveId", waveId);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		return query.list();
	}

	@SuppressWarnings("unchecked")
	public List<InvoiceBook> getInvoiceBookByInvoiceCode(String invoiceCode) {
		StringBuilder hql = new StringBuilder();
		hql.append("from InvoiceBook ib ");
		hql.append("	where ib.invoiceCode = :invoiceCode");
		Query query = this.createQuery(hql.toString());
		query.setString("invoiceCode", invoiceCode);
		return query.list();
	}

	public InvoiceBook getInvoiceBookById(Long id) {
		StringBuilder hql = new StringBuilder();
		hql.append("from InvoiceBook ib ");
		hql.append("	where ib.id = :id");
		Query query = this.createQuery(hql.toString());
		query.setLong("id", id);
		Object invoiceBook = query.uniqueResult();
		return invoiceBook == null ? null : (InvoiceBook) invoiceBook;
	}
	
	public InvoiceBook getById(Long id) {
		StringBuilder hql = new StringBuilder();
		hql.append("from InvoiceBook ib ");
		hql.append("	where ib.id = :id");
		Query query = this.createQuery(hql.toString());
		query.setLong("id", id);
		Object invoiceBook = query.uniqueResult();
		return invoiceBook == null ? null : (InvoiceBook) invoiceBook;
	}
	
	public InvoiceHeader getByReqSequenceNo(String sequenceNo) {
		StringBuilder hql = new StringBuilder();
		hql.append("from InvoiceHeader ih where ih.reqSequenceNo = :reqSequenceNo ");
		Query query = this.createQuery(hql.toString());
		query.setString("reqSequenceNo", sequenceNo);
		Object invoiceHeader = query.uniqueResult();
		return invoiceHeader == null ? null : (InvoiceHeader) invoiceHeader;
	}
	
	public List<InvoiceHeader> findToBilling(int batchNum, int failedNum) {
		String hql = "SELECT ih FROM InvoiceHeader ih, DeliveryOrderHeader dah WHERE " +
				"ih.doHeaderId = dah.id and  ih.invoiceStatus = :invoiceStatus AND dah.invoiceFlag = :invoiceFlag"
				+ " and ih.invoiceType = :invoiceType and ih.failedNum <= :failedNum";
		Query query = this.createQuery(hql);
		query.setParameter("invoiceStatus", InvoiceHeader.InvoiceStatus.INIT);
		query.setParameter("failedNum", failedNum);
		query.setParameter("invoiceType", InvoiceType.ELECTRONIC.getValue());
		query.setParameter("invoiceFlag", Long.valueOf(Constants.YesNo.YES.getValue()));
		return query.setMaxResults(batchNum).list();
	}

	public List<InvoiceHeader> findToBind(int batchNum, int failedNum) {
		String hql = "SELECT ih FROM InvoiceHeader ih, DeliveryOrderHeader dah WHERE " +
				"ih.doHeaderId = dah.id and  ih.invoiceStatus = :invoiceStatus AND dah.invoiceFlag = :invoiceFlag"
				+ " and ih.invoiceType = :invoiceType and ih.failedNum <= :failedNum ";
		Query query = this.createQuery(hql);
		query.setParameter("invoiceStatus", InvoiceHeader.InvoiceStatus.UPLOAD);
		query.setParameter("failedNum", failedNum);
		query.setParameter("invoiceType", InvoiceType.ELECTRONIC.getValue());
		query.setParameter("invoiceFlag", Long.valueOf(Constants.YesNo.YES.getValue()));
		return query.setMaxResults(batchNum).list();
	}
	
	public List<InvoiceHeader> findNeedCancel(int batchNum , int failedNum) {
		String hql = "SELECT ih FROM InvoiceHeader ih, DeliveryOrderHeader dah WHERE " +
				"ih.doHeaderId = dah.id AND ih.invoiceStatus = :invoiceStatus AND dah.invoiceFlag = :invoiceFlag"
				+ " and ih.failedNum <= :failedNum  and ih.invoiceNumber is not null";
		Query query = this.createQuery(hql);
		query.setParameter("invoiceStatus", InvoiceHeader.InvoiceStatus.NEEDCANCEL);
		query.setParameter("failedNum", failedNum);
		query.setParameter("invoiceFlag", Long.valueOf(Constants.YesNo.YES.getValue()));
		return query.setMaxResults(batchNum).list();
	}
}