package com.daxia.wms.delivery.load.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.master.entity.*;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name = "doc_crdock_detail")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@BatchSize(size = 20)
@Where(clause = "IS_DELETED = 0")
@lombok.extern.slf4j.Slf4j
public class TOCrossDockDetail extends WhBaseEntity {

    private static final long serialVersionUID = -8787450223266252631L;

        /**
	    * 主键
	    */
	    private Long id;
	    
	    /**
	     * 发货单Id
	     */
	    private Long toHeaderId;
	    
	    /**
	    * 发货单行状态
	    */
	    private String lineStatus;

	    /**
	    * 产品Id
	    */
	    private Long skuId;
	 
	    /**
	    * 期望发货数量
	    */
	    private BigDecimal expetedQty;

//	    /**
//	    * 期望发货数量EA
//	    */
//	    private BigDecimal expectedQtyEa;

	    /**
	    * 包装
	    */
	    private Long packageId;

	    /**
	    * 单位
	    */
	    private String uom;

	    /**
	    * 生产日期
	    */
	    private String lotatt01;

	    /**
	    * 失效日期
	    */
	    private String lotatt02;

	    /**
	    * 
	    */
	    private String lotatt03;

	    /**
	    * 供应商ID
	    */
	    private String lotatt04;

	    /**
	    * 
	    */
	    private String lotatt05;

	    /**
	    * 货主ID、商家
	    */
	    private String lotatt06;

	    /**
	    * 
	    */
	    private String lotatt07;

	    /**
	    * 制造商
	    */
	    private String lotatt08;

	    /**
	    * 
	    */
	    private String lotatt09;

	    /**
	    * 
	    */
	    private String lotatt10;

	    /**
	    * 
	    */
	    private String  lotatt11;

	    /**
	    * 
	    */
	    private String lotatt12;
	    
	    /**
	     * 分配规则
	     */
	    private Long allocationRule;

	    /**
	     * 体积
	     */
	    private BigDecimal volume;
	    
	    /**
	     * 净重
	     */
	    private BigDecimal netweight;
	    
	    /**
	     * 毛重
	     */
	    private BigDecimal grossweight;
	    
	    /**
	     * 拣货区
	     */
	    private String pickzone;
	    
	    /**
	     * 价格
	     */
	    private BigDecimal price;
	    
	    /**
	    * 分配数量
	    */
	    private BigDecimal allocatedQty;
	    
//	    /**
//	     * 分配数量EA
//	     */
//	    private BigDecimal allocatedQtyEa;
	     
		/**
		 * 拣货数量
		 */
	    private BigDecimal pickedQty;
	    
//	    /**
//	    * 拣货数量EA
//	    */
//	    private BigDecimal pickedQtyEa;

	    /**
	    * 分拣数量
	    */
	    private BigDecimal sortedQty;

//	    /**
//	    * 分拣数量EA
//	    */
//	    private BigDecimal sortedQtyEa;

	    /**
	     * 
	     */
	    private BigDecimal packedQtyEa;
	    
	    /**
	     * 发货数量
	     */
	    private BigDecimal shippedQty;
	    
//	    /**
//	     * 发货数量EA
//	     */
//	    private BigDecimal shippedQtyEa;
	    
	    /**
	     * 用户自定义属性1
	     */
	    private String  userdefine1;
	    
	    /**
	     * 用户自定义属性2
	     */
	    private String  userdefine2;
	    
	    /**
	     * 用户自定义属性3
	     */
	    private String userdefine3;
	    
	    /**
	     * 用户自定义属性4
	     */
	    private String userdefine4;
	    
	    /**
	     * 备注
	     */
	    private String notes;
	    /**
	     * PACK_DETAIL_ID
	     */
	    private Long packDetailId;
	    
	    private PackageInfoDetail packageInfoDetail;
	    
	    /**
	    * DoHeader对象
	    */
	    private TOCrossDockHeader toHeader;
	    
	    private List<PickTask> pickTasks;
	    
	    private Sku  sku;
	    
	    private Supplier supplier;
	    
	    private Merchant merchant;
	    
	    private Manufacturer manufacturer;
	    
	    private PackageInfoHeader packageInfoHeader;
	    
//	    private Long origId;//原始单据ID
	    private String origHeaderId;//原始单据头信息ID
	    private String origDetailId;//原始单据明细信息ID
		private Integer isDoLeaf;
		private Long parentId;//组合商品父商品ID
		private Integer isPromote;//是否是促销
		
		private Integer lineNo;
		/**
		 * 需要补货的数量
		 */
		private BigDecimal needReplQty;
		
		private Long targetWarehouseId;//目标仓库ID
		
		private Integer isValueables; //是否是贵重品
		
		private Integer isDamaged; //是否坏品
		
		@Id
		@GeneratedValue(strategy = GenerationType.AUTO)  
	    public Long getId() {
			return id;
		}
		public void setId(Long id) {
			this.id = id;
		}

		@Column(name = "DO_HEADER_ID", insertable = false, updatable = false)
		public Long getToHeaderId() {
			return toHeaderId;
		}
		public void setToHeaderId(Long toHeaderId) {
			this.toHeaderId = toHeaderId;
		}

		@Column(name = "LINESTATUS")
		public String getLineStatus() {
			return lineStatus;
		}
		public void setLineStatus(String lineStatus) {
			this.lineStatus = lineStatus;
		}

		/**
	     * Getter method for property <tt>skuId</tt>.
	     * 
	     * @return property value of skuId
	     */
		@Column(name = "SKU_ID")
	    public Long getSkuId() {
	        return skuId;
	    }
	    /**
	     * Setter method for property <tt>skuId</tt>.
	     * 
	     * @param skuId value to be assigned to property skuId
	     */
	    public void setSkuId(Long skuId) {
	        this.skuId = skuId;
	    }

	    @Column(name = "EXPECTED_QTY")
		public BigDecimal getExpetedQty() {
			return expetedQty;
		}
		public void setExpetedQty(BigDecimal expetedQty) {
			this.expetedQty = expetedQty;
		}

//		@Column(name = "EXPECTED_QTY_EACH")
//		public BigDecimal getExpectedQtyEa() {
//			return expectedQtyEa;
//		}
//		public void setExpectedQtyEa(BigDecimal expectedQtyEa) {
//			this.expectedQtyEa = expectedQtyEa;
//		}

		@Column(name = "PACKAGE_ID")
		public Long getPackageId() {
			return packageId;
		}
		public void setPackageId(Long packageId) {
			this.packageId = packageId;
		}

		@Column(name = "UOM")
		public String getUom() {
			return uom;
		}
		public void setUom(String uom) {
			this.uom = uom;
		}

		@Column(name = "LOTATT01")
		public String getLotatt01() {
			return lotatt01;
		}
		public void setLotatt01(String lotatt01) {
			this.lotatt01 = lotatt01;
		}

		@Column(name = "LOTATT02")
		public String getLotatt02() {
			return lotatt02;
		}
		public void setLotatt02(String lotatt02) {
			this.lotatt02 = lotatt02;
		}

		@Column(name = "LOTATT03")
		public String getLotatt03() {
			return lotatt03;
		}
		public void setLotatt03(String lotatt03) {
			this.lotatt03 = lotatt03;
		}

		@Column(name = "LOTATT04")
		public String getLotatt04() {
			return lotatt04;
		}
		public void setLotatt04(String lotatt04) {
			this.lotatt04 = lotatt04;
		}

		@Column(name = "LOTATT05")
		public String getLotatt05() {
			return lotatt05;
		}
		public void setLotatt05(String lotatt05) {
			this.lotatt05 = lotatt05;
		}

		@Column(name = "LOTATT06")
		public String getLotatt06() {
			return lotatt06;
		}
		public void setLotatt06(String lotatt06) {
			this.lotatt06 = lotatt06;
		}

		@Column(name = "LOTATT07")
		public String getLotatt07() {
			return lotatt07;
		}
		public void setLotatt07(String lotatt07) {
			this.lotatt07 = lotatt07;
		}

		@Column(name = "LOTATT08")
		public String getLotatt08() {
			return lotatt08;
		}
		public void setLotatt08(String lotatt08) {
			this.lotatt08 = lotatt08;
		}

		@Column(name = "LOTATT09")
		public String getLotatt09() {
			return lotatt09;
		}
		public void setLotatt09(String lotatt09) {
			this.lotatt09 = lotatt09;
		}

		@Column(name = "LOTATT10")
		public String getLotatt10() {
			return lotatt10;
		}
		public void setLotatt10(String lotatt10) {
			this.lotatt10 = lotatt10;
		}

		@Column(name = "LOTATT11")
		public String getLotatt11() {
			return lotatt11;
		}
		public void setLotatt11(String lotatt11) {
			this.lotatt11 = lotatt11;
		}

		@Column(name = "LOTATT12")
		public String getLotatt12() {
			return lotatt12;
		}
		public void setLotatt12(String lotatt12) {
			this.lotatt12 = lotatt12;
		}

		@Column(name = "ALLOCATION_RULE")
		public Long getAllocationRule() {
			return allocationRule;
		}
		public void setAllocationRule(Long allocationRule) {
			this.allocationRule = allocationRule;
		}

		@Column(name = "VOLUME")
		public BigDecimal getVolume() {
			return volume;
		}
		public void setVolume(BigDecimal volume) {
			this.volume = volume;
		}

		@Column(name = "NETWEIGHT")
		public BigDecimal getNetweight() {
			return netweight;
		}
		public void setNetweight(BigDecimal netweight) {
			this.netweight = netweight;
		}

		@Column(name = "GROSSWEIGHT")
		public BigDecimal getGrossweight() {
			return grossweight;
		}
		public void setGrossweight(BigDecimal grossweight) {
			this.grossweight = grossweight;
		}

		@Column(name = "PICKZONE")
		public String getPickzone() {
			return pickzone;
		}
		public void setPickzone(String pickzone) {
			this.pickzone = pickzone;
		}

		@Column(name = "PRICE")
		public BigDecimal getPrice() {
			return price;
		}
		public void setPrice(BigDecimal price) {
			this.price = price;
		}

		@Column(name = "ALLOCATED_QTY")
		public BigDecimal getAllocatedQty() {
			return allocatedQty;
		}
		public void setAllocatedQty(BigDecimal allocatedQty) {
			this.allocatedQty = allocatedQty;
		}

//		@Column(name = "ALLOCATED_QTY_EACH")
//		public BigDecimal getAllocatedQtyEa() {
//			return allocatedQtyEa;
//		}
//		public void setAllocatedQtyEa(BigDecimal allocatedQtyEa) {
//			this.allocatedQtyEa = allocatedQtyEa;
//		}

		@Column(name = "PICKED_QTY")
		public BigDecimal getPickedQty() {
			return pickedQty;
		}
		public void setPickedQty(BigDecimal pickedQty) {
			this.pickedQty = pickedQty;
		}

//		@Column(name = "PICKED_QTY_EACH")
//		public BigDecimal getPickedQtyEa() {
//			return pickedQtyEa;
//		}
//		public void setPickedQtyEa(BigDecimal pickedQtyEa) {
//			this.pickedQtyEa = pickedQtyEa;
//		}

		@Column(name = "SORTED_QTY")
		public BigDecimal getSortedQty() {
			return sortedQty;
		}
		public void setSortedQty(BigDecimal sortedQty) {
			this.sortedQty = sortedQty;
		}

//		@Column(name = "SORTED_QTY_EACH")
//		public BigDecimal getSortedQtyEa() {
//			return sortedQtyEa;
//		}
//		public void setSortedQtyEa(BigDecimal sortedQtyEa) {
//			this.sortedQtyEa = sortedQtyEa;
//		}

		@Column(name = "PACKED_QTY_EACH")
		public BigDecimal getPackedQtyEa() {
			return packedQtyEa;
		}
		public void setPackedQtyEa(BigDecimal packedQtyEa) {
			this.packedQtyEa = packedQtyEa;
		}

		@Column(name = "SHIPPED_QTY")
		public BigDecimal getShippedQty() {
			return shippedQty;
		}
		public void setShippedQty(BigDecimal shippedQty) {
			this.shippedQty = shippedQty;
		}

//		@Column(name = "SHIPPED_QTY_EACH")
//		public BigDecimal getShippedQtyEa() {
//			return shippedQtyEa;
//		}
//		public void setShippedQtyEa(BigDecimal shippedQtyEa) {
//			this.shippedQtyEa = shippedQtyEa;
//		}

		@Column(name = "USERDEFINE1")
		public String getUserdefine1() {
			return userdefine1;
		}
		public void setUserdefine1(String userdefine1) {
			this.userdefine1 = userdefine1;
		}

		@Column(name = "USERDEFINE2")
		public String getUserdefine2() {
			return userdefine2;
		}
		public void setUserdefine2(String userdefine2) {
			this.userdefine2 = userdefine2;
		}

		@Column(name = "USERDEFINE3")
		public String getUserdefine3() {
			return userdefine3;
		}
		public void setUserdefine3(String userdefine3) {
			this.userdefine3 = userdefine3;
		}

		@Column(name = "USERDEFINE4")
		public String getUserdefine4() {
			return userdefine4;
		}
		public void setUserdefine4(String userdefine4) {
			this.userdefine4 = userdefine4;
		}

		@Column(name = "NOTES")
		public String getNotes() {
			return notes;
		}
		public void setNotes(String notes) {
			this.notes = notes;
		}
		
		public void setUnSortedQty(BigDecimal unSortedQty) {
		}
		@Transient
		public BigDecimal getUnSortedQty() {
			return allocatedQty.subtract(sortedQty);
		}

		@ManyToOne(fetch = FetchType.LAZY)
	    @JoinColumn(name = "DO_HEADER_ID")
	    @Where(clause = " IS_DELETED = 0 ")
	    public TOCrossDockHeader getToHeader() {
	        return toHeader;
	    }
	    public void setToHeader(TOCrossDockHeader toHeader) {
	        this.toHeader = toHeader;
	    }

		@OneToOne(fetch = FetchType.LAZY)
	    @JoinColumn(name = "SKU_ID",insertable=false, updatable = false)
	    @Where(clause = " IS_DELETED = 0 ")
	    public Sku getSku() {
	        return sku;
	    }
	    public void setSku(Sku sku) {
	        this.sku = sku;
	    }
		
		@ManyToOne(fetch = FetchType.LAZY)
	    @JoinColumn(name = "LOTATT04",insertable = false, updatable = false)
	    @Where(clause = " IS_DELETED = 0 ")
	    public Supplier getSupplier() {
	        return supplier;
	    }
	    public void setSupplier(Supplier supplier) {
	        this.supplier = supplier;
	    }
	    
	    @ManyToOne(fetch = FetchType.LAZY)
	    @JoinColumn(name = "LOTATT06",insertable = false, updatable = false)
	    @Where(clause = " IS_DELETED = 0 ")
	    public Merchant getMerchant() {
	        return merchant;
	    }
	    public void setMerchant(Merchant merchant) {
	        this.merchant = merchant;
	    }

	    @ManyToOne(fetch = FetchType.LAZY)
	    @JoinColumn(name = " PACKAGE_ID",insertable = false, updatable = false)
	    @Where(clause = " IS_DELETED = 0 ")
	    public PackageInfoHeader getPackageInfoHeader() {
	        return packageInfoHeader;
	    }
	    public void setPackageInfoHeader(PackageInfoHeader packageInfoHeader) {
	        this.packageInfoHeader = packageInfoHeader;
	    }

		/**
		 * @return the origHeaderId
		 */
		@Column(name = "ORIG_HEADER_ID")
		public String getOrigHeaderId() {
			return origHeaderId;
		}
		/**
		 * @param origHeaderId the origHeaderId to set
		 */
		public void setOrigHeaderId(String origHeaderId) {
			this.origHeaderId = origHeaderId;
		}

		@Column(name = "PACK_DETAIL_ID")
		public Long getPackDetailId() {
			return packDetailId;
		}
		public void setPackDetailId(Long packDetailId) {
			this.packDetailId = packDetailId;
		}

		@Column(name = "ORIG_DETAIL_ID")
	    public String getOrigDetailId() {
	        return origDetailId;
	    }
	    public void setOrigDetailId(String origDetailId) {
	        this.origDetailId = origDetailId;
	    }
	    
	    @ManyToOne(fetch = FetchType.LAZY)
	    @JoinColumn(name = "PACK_DETAIL_ID",insertable = false, updatable = false)
	    @Where(clause = " IS_DELETED = 0 ")
	    public PackageInfoDetail getPackageInfoDetail() {
	        return packageInfoDetail;
	    }
	    public void setPackageInfoDetail(PackageInfoDetail packageInfoDetail) {
	        this.packageInfoDetail = packageInfoDetail;
	    }
	    
		@Column(name = "IS_DO_LEAF")
		public Integer getIsDoLeaf() {
			return isDoLeaf;
		}
		public void setIsDoLeaf(Integer isDoLeaf) {
			this.isDoLeaf = isDoLeaf;
		}

		/**
		 *组合商品父商品ID
	 	 *关联关系到doDTL的origId
		 * @return
		 */
		@Column(name = "PARENT_ID")
		public Long getParentId() {
			return parentId;
		}
		public void setParentId(Long parentId) {
			this.parentId = parentId;
		}
		 
		@Column(name = "IS_PROMOTE")
		public Integer getIsPromote() {
			return isPromote;
		}
		public void setIsPromote(Integer isPromote) {
			this.isPromote = isPromote;
		}

		@Transient
		public Integer getLineNo() {
			return lineNo;
		}
		public void setLineNo(Integer lineNo) {
			this.lineNo = lineNo;
		}
		
	    @OneToMany(fetch = FetchType.LAZY)
	    @JoinColumn(name = "DOC_LINE_ID")
	    @Where(clause = " IS_DELETED = 0 ")
		public List<PickTask> getPickTasks() {
			return pickTasks;
		}
		public void setPickTasks(List<PickTask> pickTasks) {
			this.pickTasks = pickTasks;
		}

		/**
		 * @return 需要补货的数量
		 */
		@Column(name="NEED_REPL_QTY")
		public BigDecimal getNeedReplQty() {
			return needReplQty;
		}
		public void setNeedReplQty(BigDecimal needReplQty) {
			this.needReplQty = needReplQty;
		}
		
		@Transient
		public Long getTargetWarehouseId() {
			return targetWarehouseId;
		}
		public void setTargetWarehouseId(Long targetWarehouseId) {
			this.targetWarehouseId = targetWarehouseId;
		}

		@Column(name="IS_VALUEABLES")
	    public Integer getIsValueables() {
	        return isValueables;
	    }
	    public void setIsValueables(Integer isValueables) {
	        this.isValueables = isValueables;
	    }

	    @Column(name="IS_DAMAGED")
	    public Integer getIsDamaged() {
	        return isDamaged;
	    }
	    public void setIsDamaged(Integer isDamaged) {
	        this.isDamaged = isDamaged;
	    }

	    @ManyToOne(fetch = FetchType.LAZY)
	    @JoinColumn(name = "LOTATT08",insertable = false, updatable = false)
	    @Where(clause = " IS_DELETED = 0 ")
	    public Manufacturer getManufacturer() {
	        return manufacturer;
	    }
	    public void setManufacturer(Manufacturer manufacturer) {
	        this.manufacturer = manufacturer;
	    }   
}