package com.daxia.wms.delivery.sort.service.impl;

import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.log.LogUtil;
import com.daxia.framework.common.service.Dictionary;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.service.UserAccountService;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.*;
import com.daxia.wms.OrderLogConstants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.delivery.deliveryorder.dao.DoDetailDAO;
import com.daxia.wms.delivery.deliveryorder.dao.DoHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.dao.DoLackDetailDAO;
import com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoLackDetail;
import com.daxia.wms.delivery.deliveryorder.filter.DoHeaderFilter;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.DoLackHeaderService;
import com.daxia.wms.delivery.deliveryorder.service.OrderLogService;
import com.daxia.wms.delivery.deliveryorder.service.impl.DoStatusRollbackListener;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.pick.service.PickService;
import com.daxia.wms.delivery.sort.dao.SortingTaskDAO;
import com.daxia.wms.delivery.sort.dto.SortedDoDetailDto;
import com.daxia.wms.delivery.sort.dto.SortingDoDetailDTO;
import com.daxia.wms.delivery.sort.entity.SortingTask;
import com.daxia.wms.delivery.sort.service.SortingService;
import com.daxia.wms.delivery.wave.dao.WaveDAO;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.master.dao.SortingBinDAO;
import com.daxia.wms.master.entity.Container;
import com.daxia.wms.master.entity.SortingBin;
import com.daxia.wms.master.service.*;
import com.daxia.wms.stock.stock.entity.TrsSortLog;
import com.daxia.wms.stock.stock.service.TrsSortLogService;
import com.daxia.wms.util.Switch;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.Component;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.jboss.seam.security.Identity;

import java.math.BigDecimal;
import java.util.*;


/**
 * 分拣业务逻辑service实现类
 */
@Name("com.daxia.wms.delivery.sortingService")
@lombok.extern.slf4j.Slf4j
public class SortingServiceImpl implements SortingService, DoStatusRollbackListener {

	@In
	SortingBinService sortingBinService;
	@In
	UserAccountService userAccountService;
	@In
	WaveService waveService;
	@In
	SkuService skuService;
	@In
	DeliveryOrderService deliveryOrderService;

	@In
	ProductBarcodeService productBarcodeService;
	@In
	private TrsSortLogService trsSortLogService;
	@In
    private DoDetailDAO doDetailDAO;
    @In
    private DoLackDetailDAO doLackDetailDAO;
	@In
	private DoHeaderDAO doHeaderDAO;
	@In
	SortingTaskDAO sortingTaskDAO;
	@In
    private WaveDAO waveDAO;
	@In
    private ContainerMgntService containerMgntService;
    @In
    private OrderLogService orderLogService;
    @In
    private PickService pickService;
    @In
    private DoLackHeaderService doLackHeaderService;

	@In
	private SortingBinDAO sortingBinDAO;

	@In
	private LaborSortBinMergePartitionService laborSortBinMergePartitionService;

	@In
	private ContainerService containerService;

	/**
     * 根据波次Id和skuId查询对应需要分拣的订单明细
     * @param waveId
     * @param skuId
     * @return
     */
	@Override
    public DeliveryOrderDetail queryDoDetail(Long waveId, Long skuId){
		List<DeliveryOrderDetail> doDetails = deliveryOrderService.findSortingDoDetails(waveId, skuId);
		if (ListUtil.isNullOrEmpty(doDetails)) {
		    throw new DeliveryException(DeliveryException.PRODUCT_NOT_NEED_SORTING);
		}

		DeliveryOrderDetail canSortDetail = null;
		for (DeliveryOrderDetail d : doDetails) {
		    DeliveryOrderHeader doHeader = d.getDoHeader();
		    if (ReleaseStatus.HOLD.getValue().equals(doHeader.getReleaseStatus())) {
		        BigDecimal lackQty = doLackDetailDAO.findTotalByDoDetailSku(d.getId(), skuId);
		        if (lackQty.add(d.getSortedQty()).compareTo(d.getAllocatedQty()) < 0) {
		            canSortDetail = d;
		            break;
		        }
		    } else {
		        canSortDetail = d;
		        break;
		    }
		}
		if (canSortDetail == null) {
		    throw new DeliveryException(DeliveryException.SORT_QTY_LH_ALLOC);
		}
		if (canSortDetail.getDoHeader().getSortGridNo() == null) {
             throw new DeliveryException(DeliveryException.SORT_GRID_NULL);
         }
		return canSortDetail;
	}

	/**
	 * 验证明细缺货数+已分拣数 需小于 分配数
	 * 如果发货单是RL 则缺货数视为0
	 * @param doDetail
	 * @param currentSortQty
	 */
	private void checkSortNum(DeliveryOrderDetail doDetail, BigDecimal currentSortQty) {
	    DeliveryOrderHeader doHeader = doDetail.getDoHeader();
	    BigDecimal totalLackQty = BigDecimal.ZERO;
	    if (ReleaseStatus.HOLD.getValue().equals(doHeader.getReleaseStatus())) {
	        totalLackQty = doLackDetailDAO.findTotalByDoDetailSku(doDetail.getId(), doDetail.getSkuId());
	    }
	    if (totalLackQty.add(currentSortQty).add(doDetail.getSortedQty()).compareTo(doDetail.getAllocatedQty()) > 0) {
	        throw new DeliveryException(DeliveryException.SORT_QTY_LH_ALLOC);
	    }
	}

    /**
     * 逐件分拣
     * @param doDetail
     * @param sortingBinId 分拣柜Id
     * @param staffNo
     * @param wave
     * @param skuId
     * @throws DeliveryException
     */
	@Override
    @Transactional
	@Loggable
    public void oneByOneSorting(DeliveryOrderDetail doDetail, Long sortingBinId, String staffNo, WaveHeader wave,
            Long skuId) throws DeliveryException {
	    log.debug("oneByOneSorting doDetail = " + doDetail.getId());
	    checkSortNum(doDetail, BigDecimal.ONE);
		validMergeStatus(wave);
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(doDetail.getDoHeaderId());
        //重新设置分拣柜
		deliveryOrderService.updateDoSortingBinByWave(doHeader, wave.getId(), sortingBinId);
        // 分拣完成以后的订单,不能再更新订单状态
        String doStatus = doHeader.getStatus();
        if (doStatus.equals(DoStatus.ALLSORTED.getValue()) || doStatus.equals(DoStatus.PART_CARTON.getValue())
                || doStatus.equals(DoStatus.ALL_CARTON.getValue()) || doStatus.equals(DoStatus.PART_LOAD.getValue())
                || doStatus.equals(DoStatus.ALL_LOAD.getValue()) || doStatus.equals(DoStatus.LOAD_LOCKED.getValue())
                || doStatus.equals(DoStatus.CANCELED.getValue()) || doStatus.equals(DoStatus.ALL_DELIVER.getValue())) {
            return;
        }
        waveService.releaseSortingBin(wave, doHeader);
        // 容器绑定波次模式下波次开始分拣时就释放波次绑定容器，即第一次分拣就释放掉容器
//        containerMgntService.releaseContainerForSorting(wave);
        doDetail.setSortedQty(doDetail.getSortedQty().add(BigDecimal.ONE));
        doDetail.setSortedQty(doDetail.getSortedQty());
        if (!doDetail.getSortedQty().equals(doDetail.getAllocatedQty())) {
            doDetail.setLineStatus(DoStatus.PARTSORTED.getValue());
            log.debug("oneByOneSorting DoStatus.PARTSORTED doDetail = " + doDetail.getId());
            if (!doHeader.getStatus().equals(DoStatus.PARTSORTED.getValue())) {
                setSortByAndStartTime(doHeader);
                doHeader.setStatus(DoStatus.PARTSORTED.getValue());
                log.debug("oneByOneSorting DoStatus.PARTSORTED do = " + doHeader.getId());
                deliveryOrderService.updateDoHeader(doHeader);
                if (!WaveStatus.PARTSORTED.getValue().toString().equals(wave.getWaveStatus())) {
                	log.debug("oneByOneSorting WaveStatus.PARTSORTED wave = " + wave.getId());
                    // 更新波次状态为"部分分拣"
                    waveService.updateWaveStatus(wave, WaveStatus.PARTSORTED.getValue().toString());
                    return;
                }
            }
            return;
        }
        doDetail.setLineStatus(DoStatus.ALLSORTED.getValue());
        deliveryOrderService.updateDoDetail(doDetail);
        // 更新交易日志
        changeTransactionLog(doDetail, staffNo);
        updateDoHeaderAndWave(doDetail, wave,sortingBinId);
    }

	private void setSortByAndStartTime(DeliveryOrderHeader doHeader) {
	    doHeader.setSortedBy(waveDAO.getOperateUser());
	    if (DoStatus.ALLPICKED.getValue().equals(doHeader.getStatus())) {
            doHeader.setSortStartTime(DateUtil.getNowTime());
        }
    }

    /**
     * 对特定的定单下的某一个商品进行批量分拣
     * @param sortingBinId 分拣格号
     * @param doId 定单号
     * @param wave 波次
     * @param skuId 分拣的商品
     * @param number 批量分拣数量
     */
	@Override
    @Transactional
	@Loggable
    public void batchSorting(Long sortingBinId, String staffNo, Long doId, WaveHeader wave, Long skuId, BigDecimal number) {
        log.debug("batchSorting doId = " + doId + " skuId = " + skuId + " number = " + number);
        BigDecimal needSortQty = number;
		validMergeStatus(wave);
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(doId);
        // 分拣完成以后的订单,不能再更新订单状态
        String doStatus = doHeader.getStatus();
        if (doStatus.equals(DoStatus.ALLSORTED.getValue()) || doStatus.equals(DoStatus.PART_CARTON.getValue())
                || doStatus.equals(DoStatus.ALL_CARTON.getValue()) || doStatus.equals(DoStatus.PART_LOAD.getValue())
                || doStatus.equals(DoStatus.ALL_LOAD.getValue()) || doStatus.equals(DoStatus.LOAD_LOCKED.getValue())
                || doStatus.equals(DoStatus.CANCELED.getValue()) || doStatus.equals(DoStatus.ALL_DELIVER.getValue())) {
            return;
        }

		//重新设置波次下DO的分拣柜
		deliveryOrderService.updateDoSortingBinByWave(doHeader, wave.getId(), sortingBinId);

        List<DeliveryOrderDetail>skuSameDetails = checkDoGetDetails(doHeader, skuId, needSortQty);
        setSortByAndStartTime(doHeader);
        waveService.releaseSortingBin(wave, doHeader);
        // 容器绑定波次模式下波次开始分拣时就释放波次绑定容器，即第一次分拣就释放掉容器
//        containerMgntService.releaseContainerForSorting(wave);
        List<DeliveryOrderDetail> doDetails = doHeader.getDoDetails();

        // 将分拣数量分配到doDetail
        for (DeliveryOrderDetail doDetail : skuSameDetails) {
            if (needSortQty.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
            BigDecimal notSortedNumber = doDetail.getAllocatedQty().subtract(doDetail.getSortedQty());
            if (ReleaseStatus.HOLD.getValue().equals(doHeader.getReleaseStatus())) {
                BigDecimal totalLackDetail = doLackDetailDAO.findTotalByDoDetailSku(doDetail.getId(), skuId);
                notSortedNumber = notSortedNumber.subtract(totalLackDetail);
            }
            if (notSortedNumber.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            BigDecimal detailSortQty = BigDecimal.ZERO;
            if (notSortedNumber.compareTo(needSortQty) >= 0) {
                detailSortQty = needSortQty;
            } else {
                detailSortQty = notSortedNumber;
            }
            needSortQty = needSortQty.subtract(detailSortQty);

            doDetail.setSortedQty(doDetail.getSortedQty().add(detailSortQty));
            doDetail.setSortedQty(doDetail.getSortedQty());
            if (doDetail.getAllocatedQty().compareTo(doDetail.getSortedQty()) == 0) {
            	log.debug("batchSorting DoStatus.ALLSORTED doDetail = " + doDetail.getId());
                doDetail.setLineStatus(DoStatus.ALLSORTED.getValue());
                deliveryOrderService.updateDoDetail(doDetail);
                changeTransactionLog(doDetail, staffNo);
            } else {
            	log.debug("batchSorting DoStatus.PARTSORTED doDetail = " + doDetail.getId());
                doDetail.setLineStatus(DoStatus.PARTSORTED.getValue());
                deliveryOrderService.updateDoDetail(doDetail);
                if (!doHeader.getStatus().equals(DoStatus.PARTSORTED.getValue().toString())) {
                    doHeader.setStatus(DoStatus.PARTSORTED.getValue().toString());
                    deliveryOrderService.updateDoHeader(doHeader);
                    if (!WaveStatus.PARTSORTED.getValue().toString().equals(wave.getWaveStatus())) {
                        // 更新波次状态为"部分分拣"
                        waveService.updateWaveStatus(wave, WaveStatus.PARTSORTED.getValue().toString());
                    }
                }
                return;
            }
        }


        // 检查是否发货单已全部分拣完毕，如果完毕，则修改定单和波次相关状态
        for (DeliveryOrderDetail doDetail : doDetails) {
            if (doDetail.getAllocatedQty().compareTo(doDetail.getSortedQty()) != 0) {
                if (!doHeader.getStatus().equals(DoStatus.PARTSORTED.getValue().toString())) {
                    doHeader.setStatus(DoStatus.PARTSORTED.getValue().toString());
                    deliveryOrderService.updateDoHeader(doHeader);
                    if (!WaveStatus.PARTSORTED.getValue().toString().equals(wave.getWaveStatus())) {
                        // 更新波次状态为"部分分拣"
                        waveService.updateWaveStatus(wave, WaveStatus.PARTSORTED.getValue().toString());
                    }
                }
                return;
            }
        }
        doHeader.setStatus(DoStatus.ALLSORTED.getValue());
		if(StringUtil.isNotEmpty(doHeader.getPcsStatus())){
			doHeader.setPcsStatus(DoStatus.ALLSORTED.getValue());
		}
		if(StringUtil.isNotEmpty(doHeader.getUnitStatus())){
			doHeader.setUnitStatus(DoStatus.ALLSORTED.getValue());
		}
        doHeader.setSortTime(DateUtil.getNowTime());
        this.deliveryOrderService.updateDoHeader(doHeader);
        updateWaveSortingStatus(wave);
    }


	/**
	 * 验证发货单指定SKU本次分拣数+已分拣数+缺货明细总数 是否大于分配总数
	 * @param doHeader
	 * @param skuId
	 * @param currentSortQty
	 */
	private List<DeliveryOrderDetail> checkDoGetDetails(DeliveryOrderHeader doHeader, Long skuId, BigDecimal currentSortQty) {
	    List<DeliveryOrderDetail> sortDetailList = new ArrayList<DeliveryOrderDetail>();
	    BigDecimal doTotalLackQty = BigDecimal.ZERO;
	    BigDecimal totalNeedQty = BigDecimal.ZERO;
	    if (ReleaseStatus.HOLD.getValue().equals(doHeader.getReleaseStatus())) {
	        doTotalLackQty = doLackDetailDAO.findTotalByDoSku(doHeader.getId(), skuId);
	    }
	    for (DeliveryOrderDetail detail : doHeader.getDoDetails()) {
	        if (detail.getSkuId().equals(skuId)) {
	            sortDetailList.add(detail);
	            totalNeedQty = totalNeedQty.add(detail.getAllocatedQty().subtract(detail.getSortedQty()));
	        }
	    }
	    if (doTotalLackQty.add(currentSortQty).compareTo(totalNeedQty) > 0) {
	        throw new DeliveryException(DeliveryException.SORT_QTY_LH_ALLOC);
	    }
	    return sortDetailList;
	}

	/**
     * 计算定单下指定商品未完成分拣的数量
     * @param doHeader
     * @return
     */
	@Override
	public BigDecimal countUnSortingNumber(DeliveryOrderHeader doHeader, Long skuId){
		if (doHeader.getStatus().equals(DoStatus.ALLSORTED.getValue())) {
			return BigDecimal.ZERO;
		}
		BigDecimal result = BigDecimal.ZERO;
		for (DeliveryOrderDetail doDetail: doHeader.getDoDetails()) {
			 if(doDetail.getSkuId().equals(skuId)){
				result = result.add(doDetail.getAllocatedQty().subtract(doDetail.getSortedQty()));
			}
		}
		return result;
	}


	/**
     * 获取一个需要进行分拣的波次
     */
	public WaveHeader getOneNeedSortingWave() {
		return waveDAO.findOne2Sorting();
	}

	/**
     * 查找wave
     * @param waveNo
     * @return
     * @throws DeliveryException
     */
	@Override
    public WaveHeader queryWave(String waveNo) throws DeliveryException{
		WaveHeader wave=waveService.queryWaveByNo(waveNo);
		if(wave==null){
			throw new DeliveryException(DeliveryException.WAVE_NO_ERRER);
		}
		else if(!(wave.getWaveStatus().equals(WaveStatus.PARTSORTED.getValue().toString()) ||
                wave.getWaveStatus().equals(WaveStatus.ALLPICKED.getValue().toString()))){
			throw new DeliveryException(DeliveryException.WAVE_STS_ERRER);
		}
		return wave;
	}

	/**
	 * 修改订单头和波次头状态
	 * @param doDetail
	 * @param sortingBinId
	 * @param wave
	 */
	@Transactional
	private void updateDoHeaderAndWave(DeliveryOrderDetail doDetail, WaveHeader wave,Long sortingBinId){
		log.debug("updateDoHeaderAndWave doDetail = " + doDetail.getId());
		//doHeader对应的所有DoDetail的未分拣数
		int unSortingNumber = deliveryOrderService.countUnSortingDoDetail(doDetail.getDoHeaderId());
        DeliveryOrderHeader doHeader = doDetail.getDoHeader();
        setSortByAndStartTime(doHeader);
		if (unSortingNumber == 0) {
			doHeader.setStatus(DoStatus.ALLSORTED.getValue());
			doHeader.setSortTime(DateUtil.getNowTime());
			deliveryOrderService.updateDoHeader(doHeader);
			//记录日志，分拣完成
			orderLogService.saveLog(doHeader,
					OrderLogConstants.OrderLogType.SORT_COMPETE.getValue(),
					ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_SORT_COMPETE));

			updateWaveSortingStatus(wave);
		} else {
			if (!doHeader.getStatus().equals(DoStatus.PARTSORTED.getValue())) {
			    doHeader.setStatus(DoStatus.PARTSORTED.getValue());
				deliveryOrderService.updateDoHeader(doHeader);
				if (!WaveStatus.PARTSORTED.getValue().toString().equals(wave.getWaveStatus())) {
					//更新波次状态为"部分分拣"
					waveService.updateWaveStatus(wave, WaveStatus.PARTSORTED.getValue().toString());
					return;
				}
			}
		}
	}

	/**
	 * 更新wave
	 * @param wave
	 */
	@Transactional
	private void updateWaveSortingStatus(WaveHeader wave) {
		log.debug("updateWaveSortingStatus wave = " + wave.getId());
		// 根据waveId查找waveHeaderId
		int unsortingNum = deliveryOrderService.countUnSortingDoHeader(wave.getId());
		// wave对应的所有doHeader都已经分拣完成
		if (unsortingNum == 0) {
			//释放容器
			containerMgntService.releaseContainerForSorting(wave);
			waveService.updateWaveStatus(wave, DoStatus.ALLSORTED.getValue().toString());
			return;
		}
		if (!WaveStatus.PARTSORTED.getValue().toString().equals(wave.getWaveStatus())) {
			// 更新波次状态为"部分分拣"
			waveService.updateWaveStatus(wave, WaveStatus.PARTSORTED.getValue().toString());
		}
	}

	/**
	 * 波次分拣完成后，释放分拣柜
	 * @param sotringBinNo
	 */
	@Transactional
	public void releaseSortingBin(String sotringBinNo){
		SortingBin sortingBin=sortingBinService.querySortingBinByNo(sotringBinNo);
		if(sortingBin==null){
			throw new DeliveryException(DeliveryException.SORTING_BIN_NO_ERRER);
		}
		//更新分拣柜状态为空闲
		sortingBinService.changeSortingBinStatus(sortingBin,SortingBinStatus.FREE.getValue().longValue());
	}

	/**
     * 根据商品条码查询商品Id
     * @param barcode
     * @return
     */
	@Override
    public List<Long> querySkuByBarcodeInWave(Long waveId, String barcode){
	    List<Long> skuIds = new ArrayList<Long>();
        List<Long> idList = this.doDetailDAO.findSkuInWave(waveId, barcode, new String[] {
                DoStatus.ALLPICKED.getValue(), DoStatus.PARTSORTED.getValue() });
        if (!ListUtil.isNullOrEmpty(idList)){
            for (Long idEach : idList) {
                if (!skuIds.contains(idEach)){
                    skuIds.add(idEach);
                }
            }
        }
		return skuIds;
	}

	/**
     * 根据波次号和商品编码查询该商品的主键
     * @param waveId 波次号
     * @param barcode 商品编码
     * @return 商品的主键
     */
	@Override
    public Long findSkyByCodeAndWaveId (Long waveId, String  barcode) {
	    Long skuId = null;
	    List<Long> skuList = this.doDetailDAO.findSkuByCodeAndWaveId(waveId, barcode, new String[] {
	                                                  DoStatus.ALLPICKED.getValue(), DoStatus.PARTSORTED.getValue() });
	    if (!ListUtil.isNullOrEmpty(skuList)) {
	        skuId = skuList.get(0);
	    }
	    return skuId;
	}

	/**
	 * 更新交易日志
	 * @param doDetail
	 * @param staffNo
	 */
	@Transactional
	public void changeTransactionLog(DeliveryOrderDetail doDetail, String staffNo){
		TrsSortLog sortLog = new TrsSortLog();
		sortLog.setTaskId(doDetail.getDoHeaderId());
		sortLog.setDocId(doDetail.getDoHeaderId());
		sortLog.setDocNo(doDetail.getDoHeader().getDoNo());
		sortLog.setDocLineId(doDetail.getId());
		sortLog.setDocType(DocType.SO.getValue().toString());
		sortLog.setTransactionType(TrsType.SS.getValue().toString());
		sortLog.setSku(doDetail.getSku());
		sortLog.setFmSkuId(doDetail.getSkuId());
		sortLog.setToSkuId(doDetail.getSkuId());
		sortLog.setFmQty(doDetail.getSortedQty());
		sortLog.setToQty(doDetail.getSortedQty());
		sortLog.setFmQtyUnit(doDetail.getSortedQtyUnit());
		sortLog.setToQtyUnit(doDetail.getSortedQtyUnit());
		sortLog.setFmQty(doDetail.getSortedQty());
		sortLog.setToQty(doDetail.getSortedQty());
		sortLog.setFmUom(doDetail.getUom());
		sortLog.setToUom(doDetail.getUom());
		sortLog.setFmGrossWeight(doDetail.getGrossweight());
		sortLog.setToGrossWeight(doDetail.getGrossweight());
		sortLog.setFmNetWeight(doDetail.getNetweight());
		sortLog.setToNetWeight(doDetail.getNetweight());
		sortLog.setOperationId(staffNo);
		sortLog.setFmVolume(doDetail.getVolume());
		sortLog.setToVolume(doDetail.getVolume());
		sortLog.setFmPackDetailId(doDetail.getPackDetailId());
		sortLog.setToPackDetailId(doDetail.getPackDetailId());
		sortLog.setPackId(doDetail.getPackageId());

		//找到do对应最新的分拣筐并记录在交易日志里
		List<Container> containers= containerMgntService.findContainerByDoc(
				doDetail.getDoHeader().getDoNo(), ContainerType.SORT_CONTAINER.getValue(),
				BindDocType.DELIVERYORDER.getValue());
		if(ListUtil.isNotEmpty(containers)){
			Container container = containers.get(0);
			sortLog.setContainerNo(container.getContainerNo());
		}
		Identity identity = (Identity) Component.getInstance("org.jboss.seam.security.identity");
		String login = identity.getCredentials().getUsername();
		if(StringUtil.isEmpty(login)){
			login = staffNo;
		}
		sortLog.setCreatedBy(login);
		sortLog.setUpdatedBy(login);
		//商家id库存回写用
		sortLog.setMerchantId(StringUtils.isEmpty(doDetail.getLotatt06())?null:Long.parseLong(doDetail.getLotatt06()));
		trsSortLogService.save(sortLog);
	}

	/**
	 * 根据单据明细id查找分拣任务
	 * @param docLineId
	 * @return
	 */
	public SortingTask queryTaskByDocLineId(Long docLineId){
		List<SortingTask> tasks=sortingTaskDAO.getSortingTaskByDocLineId(docLineId);
		if(tasks==null || tasks.size()==0 || tasks.size()>1){
				return null;
		}
		return tasks.get(0);
	}

	/**
	 * 调用接口出现异常后的回滚操作
	 */
	@Override
	@Transactional
	public void rollback(Long doId, String startStatus, String fromStatus, String toStatus) {
		/* 发货单状态回退，由于乐观锁的原因，此处必须清空缓存重新查询DO，不然会报"Row was updated or deleted by another transaction"异常
		   这是由于乐观锁得状态不一致导致 */
		this.doHeaderDAO.getSession().flush();
		this.doHeaderDAO.getSession().clear();
		DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(doId);
		doHeader.setStatus(DoStatus.ALLPICKED.getValue());
		// 如果一个波次下没有do将该波次撤销
		WaveHeader waveHeader = doHeader.getWaveHeader();

		//改订单从波次中撤销
		doHeader.setWaveId(null);
		//订单更改为未跑波次
		doHeader.setWaveFlag(YesNo.NO.getValue());
		deliveryOrderService.updateDoHeader(doHeader);

		// 发货单明细回退
		doDetailDAO.clearSortRecordByDoId(doId);
		
		if (waveHeader != null) {
			Long waveId = waveHeader.getId();
			int doCount = this.doHeaderDAO.countSortingNumberByWaveId(waveId);
			waveHeader.setDoCount(doCount);
			if (doCount == 0) {
				//如果波次被删除则释放容器(拣货箱子)
				containerMgntService.releaseContainerByWave(waveHeader, Constants.ContainerType.WAVE_CONTAINER.getValue(), Constants.BindDocType.WAVE.getValue());
				// 判断doHeader所属的波次下是否有doHeader, 如果没有则删除该波次
				this.waveService.removeWaveHeader(waveHeader);
			} else {
				// 更新波次状态
				int allSortedDoCount = doHeaderDAO.countUnSortingDoHeader(waveId, new String[]{DoStatus.ALLSORTED.getValue(), DoStatus.PART_CARTON.getValue(), DoStatus.ALL_CARTON.getValue(), DoStatus.PART_LOAD.getValue(),
						DoStatus.ALL_LOAD.getValue(), DoStatus.LOAD_LOCKED.getValue(), DoStatus.ALL_DELIVER.getValue()});
				int allPickedDoCount = doHeaderDAO.countUnSortingDoHeader(waveId, new String[]{DoStatus.ALLPICKED.getValue()});
				if (allSortedDoCount == doCount) {
					waveHeader.setWaveStatus(DoStatus.ALLSORTED.getValue().toString());
				} else if (allPickedDoCount == doCount) {
					waveHeader.setWaveStatus(DoStatus.ALLPICKED.getValue().toString());
				} else {
					waveHeader.setWaveStatus(DoStatus.PARTSORTED.getValue().toString());
				}
				this.waveService.updateWaveHeader(waveHeader);
			}
		}
	}

	/**
     * 查询波次号对应的分拣do明细
     * @param waveNo
     * @param isSorted
     * @return
     */
	@Override
    public List<DeliveryOrderDetail> querySortingDoDetailOfWave(String waveNo, boolean isSorted) {
		List<DeliveryOrderDetail> doDetailList = deliveryOrderService.findSortingDoDetailInWave(waveNo, isSorted);
		if (doDetailList == null || doDetailList.size() == 0) {
			throw new DeliveryException(DeliveryException.SORT_INFO_NULL);
		}
		return doDetailList;
	}

	/**
     * 按波次强制分拣，简化分拣逻辑，没有分拣交易
     * @param waveNo 要分拣的波次
     * @param staffNo 分拣人
     * @throws DeliveryException
     */
	@Override
	@Transactional
	@Loggable
	public void compelSorting(String waveNo,String staffNo) throws DeliveryException {
		log.debug("compelSorting waveNo = " + waveNo);
		WaveHeader wave = this.waveService.getWaveHeaderByWaveNum(waveNo);
        validMergeStatus(wave);
		if (!DoStatus.ALLPICKED.getValue().equals(wave.getWaveStatus())) {
			throw new DeliveryException(DeliveryException.WAVE_IS_WRONG_STATUS);
        }
        //waveService.releaseSortingBin(wave, null);
		// 容器绑定波次模式下波次开始分拣时就释放波次绑定容器，即第一次分拣就释放掉容器
        containerMgntService.releaseContainerForSorting(wave);
		Long waveId = wave.getId();
		
		orderLogService.saveLog(waveId, OrderLogConstants.OrderLogType.SORT_WAVE.getValue(), ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_SORT_WAVE));

        this.deliveryOrderService.updateDoDetailByWaveId(waveId, DoStatus.ALLSORTED.getValue());
        this.deliveryOrderService.updateDoHeaderByWaveId(waveId, DoStatus.ALLSORTED.getValue());
        wave.setWaveStatus(WaveStatus.ALLSORTED.getValue());
		this.waveService.updateWaveHeader(wave);
		//laborSortBinWaveService.unBindWave(waveId);
		trsSortLogService.inertSortLog(waveId, staffNo);
		
		this.doHeaderDAO.getSession().flush();
		this.doHeaderDAO.getSession().clear();
	}

	/**
     * 强制分拣
     * @param doDetailIds 要强制分拣的doDetail ID集合
     * @param doNo 要强制分拣的发货单
     * @throws DeliveryException
     */
	@Override
	@Transactional
	@Loggable
	public void forceSorting(List<Long> doDetailIds, String doNo, String staffNo, String sotringBinNo) throws DeliveryException {
	    log.debug("forceSorting doNo = " + doNo);
		if (ListUtil.isNullOrEmpty(doDetailIds)) {
	        return;
	    }
		DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(doNo);
		checkDo4ForceSorting(doHeader);
        WaveHeader wave = doHeader.getWaveHeader();
        checkWave4ForceSorting(wave);
        validMergeStatus(wave);

        List<DeliveryOrderDetail> unSortDoDetails = doDetailDAO.getByKeys(doDetailIds);
		//写交易,并更改明细状态
		for (DeliveryOrderDetail doDetail : unSortDoDetails) {
			String doDetailStatus = doDetail.getLineStatus();
			if (!DoStatus.ALLPICKED.getValue().equals(doDetailStatus) &&
			        !DoStatus.PARTSORTED.getValue().equals(doDetailStatus)) {
				throw new DeliveryException(DeliveryException.DO_DETAIL_STATUS_ERROR_CAN_NOT_FORCE_SORTING);
			}
			updateDoDetailWhenForceSorting(doDetail);
            // 调拨、RTV、RMA这三种明细可能会缺发，导致存在分配数、拣货数、分拣数都为0的明细，这样的明细不记分拣交易
            if (needSsLog(doDetail)) {
                changeTransactionLog(doDetail, staffNo);
            }
		}

        waveService.releaseSortingBin(wave, doHeader);
		// 容器绑定波次模式下波次开始分拣时就释放波次绑定容器，即第一次分拣就释放掉容器
//        containerMgntService.releaseContainerForSorting(wave);

        setSortByAndStartTime(doHeader);

		//如果该DO单全部分拣完成
		if (deliveryOrderService.isDoHeaderSorted(doHeader.getId())
				&& (!DoStatus.ALLSORTED.getValue().equals(doHeader.getStatus()))) {
			doHeader.setStatus(DoStatus.ALLSORTED.getValue());
			if(StringUtil.isNotEmpty(doHeader.getPcsStatus())){
				doHeader.setPcsStatus(DoStatus.ALLSORTED.getValue());
			}
			if(StringUtil.isNotEmpty(doHeader.getUnitStatus())){
				doHeader.setUnitStatus(DoStatus.ALLSORTED.getValue());
			}
            doHeader.setSortTime(DateUtil.getNowTime());
			doHeaderDAO.update(doHeader);
            orderLogService.saveLog(doHeader,OrderLogConstants.OrderLogType.SORT_COMPETE_FORCE.getValue(),
                    ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_SORT_COMPETE_FORCE));
			if (waveService.isWaveSorted(doHeader.getWaveId())) {
				if (!WaveStatus.ALLSORTED.getValue().equals(wave.getWaveStatus())) {
					wave.setWaveStatus(WaveStatus.ALLSORTED.getValue());
				}
				wave.setSortCabintNo(null);
				waveDAO.update(wave);
			} else if (!WaveStatus.PARTSORTED.getValue().equals(wave.getWaveStatus())) {
				wave.setWaveStatus(WaveStatus.PARTSORTED.getValue());
				waveDAO.update(wave);
			}
		} else {
			if (!DoStatus.PARTSORTED.getValue().equals(doHeader.getStatus())) {
				doHeader.setStatus(DoStatus.PARTSORTED.getValue());
				doHeaderDAO.update(doHeader);
			}
			if (!WaveStatus.PARTSORTED.getValue().equals(wave.getWaveStatus())) {
				wave.setWaveStatus(WaveStatus.PARTSORTED.getValue());
				waveDAO.update(wave);
			}
		}
	}

    /**
     * 判断该明细是否需要记录分拣交易 ：<br/>
     * 调拨、RTV、RMA这三种明细可能会缺发，导致存在分配数、拣货数、分拣数都为0的明细，这样的明细不记分拣交易
     *
     * @param doDetail
     * @return
     */
    private boolean needSsLog(DeliveryOrderDetail doDetail) {
        if (BigDecimal.ZERO.compareTo(doDetail.getAllocatedQty())!=0) {
            return true;
        }
        return false;
    }

    /**
	 * 按DO强制分拣时，更新do明细
	 * @param doDetail
	 */
	private void updateDoDetailWhenForceSorting(DeliveryOrderDetail doDetail) {
		doDetail.setLineStatus(DoStatus.ALLSORTED.getValue());
		doDetail.setSortedQty(doDetail.getAllocatedQty());
		doDetail.setSortedQty(doDetail.getAllocatedQty());
	}

	/**
     * 根据波次号和商品条码(编码)判断该商品是否属于该波次
     * @param waveId 波次号
     * @param barcode 商品条码(编码)
     * @throws DeliveryException 商品条码(编码)对应的商品如果不在波次中则抛出异常
     */
    @Override
    public void checkSkuInWave(Long waveId, String barcode) throws DeliveryException {
        List<Long> skuListForBarcode = this.doDetailDAO.findSkuInWave(waveId, barcode, null);
        List<Long> skuListForProductCode = this.doDetailDAO.findSkuByCodeAndWaveId(waveId, barcode, null);
        if (ListUtil.isNullOrEmpty(skuListForBarcode) && ListUtil.isNullOrEmpty(skuListForProductCode)) {
            throw new DeliveryException(DeliveryException.PRODUCT_NOT_NEED_SORTING);
        }
    }

	/**
	 * 分拣缺货处理
	 *
	 * @param waveNo
	 *            波次号
	 * @param holdWho
	 *            操作人
	 * @return List<String> 已做缺货处理的订单号
	 */
	@Override
	@Transactional
	public List<String> lack(String waveNo, String holdWho) throws DeliveryException {
		WaveHeader waveHeader = queryWave(waveNo);
		List<DeliveryOrderHeader> doHeaders = getDoOutOfStkInWave(waveHeader.getId());
		List<String> doNoList = new ArrayList<String>();
		if (doHeaders.isEmpty()) {
			throw new DeliveryException(DeliveryException.ERROR_WAVE_HAS_NO_LACK_DO);
		}
		for (DeliveryOrderHeader dh : doHeaders) {
			if (dh.getNeedCancel()) {
				continue;
			}
			//异常状态为非 待通知客服或者完成状态
			if (null != dh.getExceptionStatus() && StringUtil.isNotIn(
					dh.getExceptionStatus(), DoExpStatus.COMPLETE.getValue(),
					DoExpStatus.TO_BE_ANNOUNCE.getValue())) {
				continue;
			}

			// 删除异常已经完成的订单的缺货明细。
			if (DoExpStatus.COMPLETE.getValue().equals(dh.getExceptionStatus())) {
			    doLackHeaderService.deleteLackInfoByDoId(dh.getId());
			}
			String reason = "";
			// 冻结订单头
			if (StringUtil.isIn(dh.getDoType(), DoType.RTV.getValue(), DoType.ALLOT.getValue())) {
				reason = Reason.WAIT_REPL.getValue();
			} else {
				reason = Reason.SORT_LACK.getValue();
			}
			deliveryOrderService.frozen(dh, reason, null, holdWho, DoExpStatus.TO_BE_ANNOUNCE.getValue(),
					ExOpType.SORT_LACK.getValue());

			// 写缺货明细
			createLackInFo(dh, holdWho);

			doNoList.add(dh.getDoNo());
		}
		return doNoList;
	}

	private void createLackInFo(DeliveryOrderHeader dh, String holdWho) {
		Map<String, String> reasonMap = Dictionary.getDictionary("REASON_HDD");
		String holdReason = reasonMap.get(Reason.SORT_LACK.getValue());
		boolean isFrozen = ReleaseStatus.HOLD.getValue().equals(dh.getReleaseStatus());
		Set<String> lackPartitions = new TreeSet<String>();
        for (DeliveryOrderDetail detail : dh.getDoDetails()) {
            BigDecimal alreadyLackQty = BigDecimal.ZERO;
            if (isFrozen) {
                alreadyLackQty = doLackDetailDAO.findTotalByDoDetailSku(detail.getId(), detail.getSkuId());
            }
            BigDecimal nowLackQty = detail.getAllocatedQty().subtract(detail.getSortedQty().add(alreadyLackQty));
            if (nowLackQty.compareTo(BigDecimal.ZERO) > 0) {
                String lackPartition = syncLackDetailByDoDetail(detail, holdWho, holdReason, nowLackQty);
                lackPartitions.add(lackPartition);
            }
        }
        //新增或更新缺货头
	    doLackHeaderService.addOrUpdate(dh.getId(), lackPartitions);
	}

	/**
	 * 根据do明细插入缺货明细记录。
	 *
	 * @param doDetail
	 *            订单明细
	 * @param holdWho
	 *            操作人
	 * @param holdReason
	 *            冻结原因
	 */
	private String syncLackDetailByDoDetail(DeliveryOrderDetail doDetail, String holdWho, String holdReason, BigDecimal lackQty) {
		DoLackDetail lackDetail = new DoLackDetail();
		lackDetail.setDoHeaderId(doDetail.getDoHeaderId());
		lackDetail.setDoDetailId(doDetail.getId());
		lackDetail.setSkuId(doDetail.getSkuId());
		lackDetail.setHoldReason(holdReason);
		lackDetail.setCreatedBy(holdWho);
		lackDetail.setUpdatedBy(holdWho);
		lackDetail.setQty(lackQty);
		PickTask maxCountPickTask = pickService.queryMaxCountByDetail(doDetail.getId());
		lackDetail.setLocCodes(maxCountPickTask.getLocation().getLocCode());
		lackDetail.setLocId(maxCountPickTask.getLocId());
		doLackDetailDAO.saveOrUpdate(lackDetail);
		return maxCountPickTask.getLocation().getPartition().getPartitionCode();
	}



	/**
	 * 查询波次下 处于【部分分拣】或【拣货完成】 状态的 DO
	 * @param waveId 波次ID
	 * @return
	 */
	private List<DeliveryOrderHeader> getDoOutOfStkInWave(Long waveId) {
		DoHeaderFilter dhFilter = new DoHeaderFilter();
		dhFilter.setWaveFlag(YesNo.YES.getValue());
		dhFilter.setWaveId(waveId);
		dhFilter.setStatusFrom(DoStatus.ALLPICKED.getValue());
		dhFilter.setStatusTo(DoStatus.PARTSORTED.getValue());
		return doHeaderDAO.findByFilter(dhFilter);
	}

    /**
     * 根据配置项，分拣之前校验波次是否为【集货出区】
     *
     * @param wave
     */
    private void validMergeStatus(WaveHeader wave) {
        if (null == wave) {
            throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
        }
        Integer needValid = SystemConfig.getConfigValueInt(NEED_VALID_MERGE, ParamUtil.getCurrentWarehouseId());
        if (YesNo.NO.getValue().equals(needValid)) {
            return;
        }
        if (!WaveMergeStatus.NO_MERGE.getValue().equals(
                wave.getMergeStatus().toString())
                && !WaveMergeStatus.OUT_MERGE.getValue().equals(
                        wave.getMergeStatus().toString())
                && !WaveMergeStatus.CHECKED.getValue().equals(
                        wave.getMergeStatus().toString())) {
        	if (SystemConfig.configIsOpen(Switch.LABOR_FORCE_SWITCH_SORT, ParamUtil.getCurrentWarehouseId())
        			&& WaveMergeStatus.CHECKED.getValue().equals(
                            wave.getMergeStatus().toString())) {
        		return;
        	}
            throw new DeliveryException(DeliveryException.WAVE_NOT_MERGED_OUT);
        }
    }

    /**
     * 根据发货单号查询需要强制分拣的SortingDoDetailDTO
     * @param doNo
     * @return
     */
    @Override
    public List<SortingDoDetailDTO> getDetails4ForceSorting(String doNo) throws DeliveryException {
        DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(doNo.trim());
        checkDo4ForceSorting(doHeader);
        WaveHeader wave = doHeader.getWaveHeader();
        checkWave4ForceSorting(wave);
        return doDetailDAO.getSortingDoDetails(doHeader.getId());
    }

    /**
     * 按DO强制分拣验证DO存在且释放且（拣货完成或分拣中）
     * @param doHeader
     */
    private void checkDo4ForceSorting(DeliveryOrderHeader doHeader) {
        if (doHeader == null) {
            throw new DeliveryException(DeliveryException.SORTING_DO_NOT_NOEXIST_CANNOT_SORTING);
        }
        if (ReleaseStatus.HOLD.getValue().equals(doHeader.getReleaseStatus())) {
            throw new DeliveryException(DeliveryException.SORTING_DO_HOLD_CANNOT_SORTING);
        }
        if (doHeader.getNeedCancel()) {
            throw new DeliveryException(DeliveryException.SORTING_DO_NEED_CANCLE_CANNOT_SORTING);
        }
        if (StringUtil.isNotIn(doHeader.getStatus(), DoStatus.ALLPICKED.getValue(),
                DoStatus.PARTSORTED.getValue())) {
            throw new DeliveryException(DeliveryException.SORTING_DO_STATUS_WRONG);
        }
    }
    /**
     * 按DO强制分拣验证波次存在且（拣货完成或分拣中或分拣完成）
     * @param wave
     */
    private void checkWave4ForceSorting(WaveHeader wave) {
        if (null == wave) {
            throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
        }
        if (StringUtil.isNotIn(wave.getWaveStatus(), WaveStatus.ALLPICKED.getValue(),
                WaveStatus.PARTSORTED.getValue(), WaveStatus.ALLSORTED.getValue())) {
            throw new DeliveryException(DeliveryException.SORTING_WAVE_STATUS_WRONG);
        }
    }

    @Override
    @Transactional
    public Long dmBySku(Long waveId, Long skuId) {
        Long doHeaderId = null;
        DeliveryOrderHeader doHeader = null;
        List<DeliveryOrderDetail> details = this.doDetailDAO.findByWaveSkuStatus(waveId, skuId,
                new String[] {DoStatus.PARTSORTED.getValue(), DoStatus.ALLPICKED.getValue() });
        if (ListUtil.isNullOrEmpty(details)) {
            throw new DeliveryException(DeliveryException.NO_DETAIL_TO_SORT_DM);
        }
        DeliveryOrderDetail canLackDetail = null;
        for (DeliveryOrderDetail d : details) {
            doHeader = d.getDoHeader();
            BigDecimal detailTotalLackQty = BigDecimal.ZERO;
            if (!ReleaseStatus.RELEASE.getValue().equals(doHeader.getReleaseStatus())) {
                detailTotalLackQty = doLackDetailDAO.findTotalByDoDetailSku(d.getId(), skuId);
            }
             
            if (d.getSortedQty().add(detailTotalLackQty).compareTo(d.getAllocatedQty()) < 0) {
            	if ((StringUtil.isNotEmpty(doHeader.getExceptionStatus()) &&
            			StringUtil.isNotIn(doHeader.getExceptionStatus(),
            					DoExpStatus.COMPLETE.getValue(), DoExpStatus.TO_BE_ANNOUNCE.getValue()))) {
            		//非美特好订单必须无异常或异常已完成或待通知客服才可破损（避免二次通知客服）
            		continue;
            	}
            	canLackDetail = d;
                break;
            }
        }
        if (null == canLackDetail) {
            throw new DeliveryException(DeliveryException.SORT_QTY_LH_ALLOC);
        }

        //如果异常转台是已完成则删除之前缺货明细
        if (DoExpStatus.COMPLETE.getValue().equals(doHeader.getExceptionStatus())) {
            doLackHeaderService.deleteLackInfoByDoId(doHeader.getId());
        }

		String holdReason = "";
		// 冻结订单头
		if (StringUtil.isIn(doHeader.getDoType(), DoType.RTV.getValue(), DoType.ALLOT.getValue())) {
			holdReason = Reason.WAIT_REPL.getValue();
		} else {
			holdReason = Reason.SORT_DM.getValue();
		}
		deliveryOrderService.frozen(doHeader, holdReason, "分拣破损", ParamUtil.getCurrentLoginName(),
				DoExpStatus.TO_BE_ANNOUNCE.getValue(), ExOpType.SORT_DM.getValue());
		Map<String, String> reasonMap = Dictionary.getDictionary("REASON_HDD");
        DoLackDetail lackDetail = new DoLackDetail();
        PickTask maxCountPickTask = pickService.queryMaxCountByDetail(canLackDetail.getId());
        lackDetail.setLocCodes(maxCountPickTask.getLocation().getLocCode());
        lackDetail.setLocId(maxCountPickTask.getLocId());
        lackDetail.setDoDetailId(canLackDetail.getId());
        lackDetail.setDoHeader(canLackDetail.getDoHeader());
        lackDetail.setDoHeaderId(canLackDetail.getDoHeaderId());
        String reason = reasonMap.get(holdReason);
        lackDetail.setHoldReason(reason);
        lackDetail.setNotes("分拣破损");
        lackDetail.setSkuId(canLackDetail.getSkuId());
        lackDetail.setQty(BigDecimal.ONE);
        lackDetail.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        doLackDetailDAO.save(lackDetail);
        //更新或插入缺货头
        doLackHeaderService.addOrUpdate(canLackDetail.getDoHeaderId(), maxCountPickTask.getLocation().getPartition().getPartitionCode());

        return doHeaderId;
    }

    /**
     * 如果破损是最后1个unit 需要释放分拣柜和波次关系
     * @return
     */
    @Override
    public void dealWaveLastUnit(Long waveId){
    	//查询波次下已经破损的数量
    	BigDecimal lackQty = doLackDetailDAO.getDoLackQtyByWaveId(waveId);
    	if (lackQty == null) {
    		return;
    	}
    }

	@Override
    public List<String> doNeedBingContatiner(Long waveId) {
		return waveDAO.doNeedBingContatiner(waveId);
	}

    @Override
    @Transactional
	public String bindSortContainer(Container container,String waveNo,List<String> doNumbers) {
    	for (int i = doNumbers.size(); i > 0 ;i--) {
    		String sortGridNo = waveDAO.checkDoNeedBingContatiner(doNumbers.get(i-1));
    		if(sortGridNo != null){
    			containerMgntService.bindContainer(container.getId(),BindDocType.DELIVERYORDER.getValue(),doNumbers.get(i-1), null);
    			doNumbers.remove(i-1);
    			return sortGridNo;
    		}else{
    			doNumbers.remove(i-1);
    		}
		}
		return "0";
	}


    @Override
    public List<BigDecimal> getSortNoListByWaveId(Long waveId){
    	return waveDAO.getSortNoListByWaveId(waveId);
    }

    @Override
    @Transactional
    public void executeChangeSortContainer(DoHeaderDto doHeader,Container container){
    	//绑定新分拣筐操作
    	containerMgntService.bindDoc(container,BindDocType.DELIVERYORDER.getValue(),
    			doHeader.getDoNo(), ContainerBusinessStatus.SORT.getValue(),
                ContainerType.SORT_CONTAINER.getValue());

    }

    @Override
    @Transactional
    public void bindWave(SortingBin sortingBin,WaveHeader waveHeader){
       	 //索取后更新索取的波次时间 解决并发问题
   	    waveHeader.setUpdatedAt(DateUtil.getNowTime());
   	    waveHeader.setUpdatedBy(ParamUtil.getCurrentLoginName());
   	    waveDAO.update(waveHeader);
    }

    @Override
    @Transactional
	@Loggable
	public List<String> batchSortingForMultiDo(Map<Long, List<SortedDoDetailDto>> groupedByHeaderIdDetails,
			WaveHeader waveHeader, Long sortingBinId) {
    	List<String> result = new ArrayList<String>();
    	for (Long doHeaderId : groupedByHeaderIdDetails.keySet()) {
			List<SortedDoDetailDto> dtos = groupedByHeaderIdDetails
					.get(doHeaderId);
			// 调用批量分拣方法
			String warnDoNo = batchSorting(doHeaderId, dtos, sortingBinId,
					waveHeader);
			if(warnDoNo != null ){
				result.add(warnDoNo);
			}
		}
    	return result;
    }

	@Override
    @Transactional
	@Loggable
	public String batchSorting(Long doId, List<SortedDoDetailDto> dots,
			Long sortingBinId, WaveHeader waveHeader) {
		validMergeStatus(waveHeader);
		DeliveryOrderHeader doHeader = deliveryOrderService
				.getDoHeaderById(doId);
		// 重新设置波次下DO的分拣柜
		deliveryOrderService.updateDoSortingBinByWave(doHeader,
				waveHeader.getId(), sortingBinId);

		// 分拣完成以后的订单,不能再更新订单状态
		String doStatus = doHeader.getStatus();
		String releaseStatus = doHeader.getReleaseStatus();
		if(doStatus.equals(DoStatus.CANCELED.getValue())){
			throw new DeliveryException(DeliveryException.CS_SORTING_CANCELLED_DO,doHeader.getDoNo()); // TODO 需要确认
		}
		if (doStatus.equals(DoStatus.ALLSORTED.getValue())
				|| doStatus.equals(DoStatus.PART_CARTON.getValue())
				|| doStatus.equals(DoStatus.ALL_CARTON.getValue())
				|| doStatus.equals(DoStatus.PART_LOAD.getValue())
				|| doStatus.equals(DoStatus.ALL_LOAD.getValue())
				|| doStatus.equals(DoStatus.LOAD_LOCKED.getValue())
				|| doStatus.equals(DoStatus.CANCELED.getValue())
				|| ReleaseStatus.HOLD.getValue().endsWith(releaseStatus)) {
			return doHeader.getDoNo();
		}
		setSortByAndStartTime(doHeader);
		waveService.releaseSortingBin(waveHeader, doHeader);
		// 容器绑定波次模式下波次开始分拣时就释放波次绑定容器，即第一次分拣就释放掉容器
//		containerMgntService.releaseContainerForSorting(waveHeader);
		List<DeliveryOrderDetail> doDetails = doHeader.getDoDetails();

		for (SortedDoDetailDto dto : dots) {
			BigDecimal needSortQty = new BigDecimal(dto.getSortedQty());
			DeliveryOrderDetail doDetail = deliveryOrderService
					.getDoDetail(Long.valueOf(dto.getDoDetailId()));
			// 将分拣数量分配到doDetail
			if (needSortQty.compareTo(BigDecimal.ZERO) == 0) {
				continue;
			}
			BigDecimal notSortedNumber = doDetail.getAllocatedQty().subtract(
					doDetail.getSortedQty());
			if (ReleaseStatus.HOLD.getValue().equals(
					doHeader.getReleaseStatus())) {
				BigDecimal totalLackDetail = doLackDetailDAO
						.findTotalByDoDetailSku(doDetail.getId(),
								doDetail.getSkuId());
				notSortedNumber = notSortedNumber.subtract(totalLackDetail);
			}
			if (notSortedNumber.compareTo(BigDecimal.ZERO) <= 0) {
				continue;
			}

			BigDecimal detailSortQty = BigDecimal.ZERO;
			if (notSortedNumber.compareTo(needSortQty) >= 0) {
				detailSortQty = needSortQty;
			} else {
				detailSortQty = notSortedNumber;
			}
			needSortQty = needSortQty.subtract(detailSortQty);

			doDetail.setSortedQty(doDetail.getSortedQty()
					.add(detailSortQty));
			doDetail.setSortedQty(doDetail.getSortedQty());
			if (doDetail.getAllocatedQty().compareTo(
					doDetail.getSortedQty()) == 0) {
				doDetail.setLineStatus(DoStatus.ALLSORTED.getValue());
				deliveryOrderService.updateDoDetail(doDetail);
				String staffNo = dto.getStaffNo();
				changeTransactionLog(doDetail, staffNo);
			} else {
				doDetail.setLineStatus(DoStatus.PARTSORTED.getValue());
				deliveryOrderService.updateDoDetail(doDetail);
				if (!doHeader.getStatus().equals(
						DoStatus.PARTSORTED.getValue().toString())) {
					doHeader.setStatus(DoStatus.PARTSORTED.getValue()
							.toString());
					deliveryOrderService.updateDoHeader(doHeader);
					if (!WaveStatus.PARTSORTED.getValue().toString()
							.equals(waveHeader.getWaveStatus())) {
						// 更新波次状态为"部分分拣"
						waveService.updateWaveStatus(waveHeader,
								WaveStatus.PARTSORTED.getValue().toString());
					}
				}
			}
		}

		// 检查是否发货单已全部分拣完毕，如果完毕，则修改定单和波次相关状态
		for (DeliveryOrderDetail detail : doDetails) {
			if (!detail.getAllocatedQty().equals(detail.getSortedQty())) {
				if (!doHeader.getStatus().equals(
						DoStatus.PARTSORTED.getValue().toString())) {
					doHeader.setStatus(DoStatus.PARTSORTED.getValue()
							.toString());
					deliveryOrderService.updateDoHeader(doHeader);
					if (!WaveStatus.PARTSORTED.getValue().toString()
							.equals(waveHeader.getWaveStatus())) {
						// 更新波次状态为"部分分拣"
						waveService.updateWaveStatus(waveHeader,
								WaveStatus.PARTSORTED.getValue().toString());
					}
				}
				return null;
			}
		}
		doHeader.setStatus(DoStatus.ALLSORTED.getValue());
		if(StringUtil.isNotEmpty(doHeader.getPcsStatus())){
			doHeader.setPcsStatus(DoStatus.ALLSORTED.getValue());
		}
		if(StringUtil.isNotEmpty(doHeader.getUnitStatus())){
			doHeader.setUnitStatus(DoStatus.ALLSORTED.getValue());
		}
		doHeader.setSortTime(DateUtil.getNowTime());
		this.deliveryOrderService.updateDoHeader(doHeader);
		updateWaveSortingStatus(waveHeader);
		return null;
	}
}
