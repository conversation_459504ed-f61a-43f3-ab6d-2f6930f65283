package com.daxia.wms.delivery.task.replenish.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;

import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.DownloadUtil;
import com.daxia.wms.Constants.ReplStatus;
import com.daxia.wms.Constants.ReplTaskStatus;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.print.dto.PrintCfg;
import com.daxia.wms.delivery.print.service.PrintReplService;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishHeader;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishTask;
import com.daxia.wms.delivery.task.replenish.filter.ReplTaskFilter;
import com.daxia.wms.delivery.task.replenish.service.ReplHeaderService;
import com.daxia.wms.delivery.task.replenish.service.ReplTaskService;
import com.daxia.wms.stock.StockException;
import com.daxia.wms.stock.task.entity.TrsTask;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.intercept.BypassInterceptors;
import org.json.JSONArray;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 补货任务管理
 */
@Name("com.daxia.wms.delivery.repTaskAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class RepTaskAction extends PagedListBean<ReplenishTask> {

    private static final long serialVersionUID = -6529313027336728713L;
    private ReplTaskFilter replTaskFilter;
    
    private List<ReplenishTask> replTaskList;
    
    private ReplenishHeader replenishHeader;

    private Double toQty;
    private Long taskId;

    private Long replHeaderId;
    
	private String printData = "[]";
	
	//补货单打印参数设置
    private PrintCfg replPrintCfg;
	
	private Boolean canOverRepl;
	
	private boolean ignorePending;
	private String mixErr;
	private boolean queryMod = false;

    @In
    private ReplTaskService replTaskService;
    
    @In
    private ReplHeaderService replHeaderService;
    
    @In
    private PrintReplService printReplService;

    public RepTaskAction() {
        super();
        replTaskFilter = new ReplTaskFilter();   
        replenishHeader = new ReplenishHeader();
        setReplTaskList(new ArrayList<ReplenishTask>());
    }
    
    @Create
    public void init() {
        canOverRepl = replTaskService.canOverReplenish();
    }

    /**
     * 查询补货任务
     */
    @Override
    public void query() {
    	if (!queryMod) {
    		replenishHeader = replHeaderService.get(replHeaderId);
    		replTaskFilter.setDocOperId(replHeaderId);
    		setReplTaskList(replTaskService.queryReplTrs(replTaskFilter));
    	}
    }

    /**
     * 确认补货 
     * @throws Exception 
     */
    @Loggable
    public void confirmRepl() throws Exception {
    	mixErr = "";
    	queryMod = false;
		for (Object key : replTaskList) {
			ReplenishTask t = (ReplenishTask) key;
			if (t.getId().compareTo(this.taskId) == 0) {
				if (ReplTaskStatus.OFFSHELF.getValue().equals(t.getTaskStatus())) {
					if (t.getRealQty().compareTo(t.getOffShelfQty()) == 1) {
						// 实补数量不能大于已下架数量
						throw new DeliveryException(DeliveryException.PEP_REAL_QTY_ILLEGAL_OFFSHELF); 
					}
				} else {
					if (t.getRealQty().compareTo(t.getQtyUnit()) == 1) {
						 //上架数量是否>期望数量
						
						// 实补数量不能大于期望量
						throw new DeliveryException(DeliveryException.PEP_REAL_QTY_ILLEGAL_EXPECT); 
					}
				}
				try {
					replTaskService.executeRepl(t.getId(),t.getRealQty(),t.getRealLoc(), t.getReasonCode(),t.getReasonDescr(), ignorePending);
				} catch(StockException e) {
					if (StockException.LOC_PENDING_CANNOT_MIX_SKU.equals(e.getMessage())) {
						mixErr += t.getRealLoc() + ":" +  t.getSku().getProductCode();
						queryMod = true;
						return;
					} else {
						throw e;
					}
				}
			}
		}
		queryMod = false;
		ignorePending = false;
    }

    /**
     * 取消补货
     */
    @Loggable
    public void cancelRepl() {
    	replenishHeader = this.replHeaderService.get(replHeaderId);
    	if(!(ReplStatus.RELEASED.getValue().equals(replenishHeader.getStatus()) 
    			|| ReplStatus.INPROGRESS.getValue().equals(replenishHeader.getStatus()))){
    		throw new DeliveryException(DeliveryException.PEP_HEADER_STATUS_ILLEGAL); // 补货单状态不正确
    	}
    	
		for (Object key : replTaskList) {
			TrsTask t = (TrsTask) key;
			if(t.getId().compareTo(this.taskId) == 0){
				replTaskService.cancelRepl(((TrsTask)t).getId());
				break;
			}
		}
    }

    public void printTask() {
        this.printData = "[]";
        List<Long> ids = new ArrayList<Long>();
        ids.add(replHeaderId);
        List<String> printPages = printReplService.print(ids);
        this.replPrintCfg = printReplService.setDoPrintCfg();
        this.printData = new JSONArray(printPages).toString();
    }

    /**
     * 导出补货单
     * @throws IOException
     */
    public void export() throws IOException {
        this.printData = "[]";
        List<Long> ids = new ArrayList<Long>();
        ids.add(replHeaderId);
        byte[] pdfData = printReplService.generatePDF(ids);
        DownloadUtil.writeToResponse(pdfData, DownloadUtil.PDF,
            "replTask_" + DateUtil.dateToString(new Date(), "yyMMddHHmmss") + ".pdf");
    }

    /**
     * 是否显示完成按钮
     * @return
     */
    public Boolean showConfirmButton(){
        String status = replenishHeader.getStatus();
        if(ReplStatus.RELEASED.getValue().equals(status)) {
            return Boolean.TRUE;
        }
        //明细存在已下架完成按钮不可用
        if(ReplStatus.INPROGRESS.getValue().equals(status)){
            for(TrsTask task : replTaskList){
                if(ReplTaskStatus.OFFSHELF.getValue().equals(task.getTaskStatus())){
                    return Boolean.FALSE;
                }
            }
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
    
    public void confirmReplHeader() {
        replTaskService.confirmReplHeader(replHeaderId);

        sayMessage(MESSAGE_SUCCESS);
    }

    public void cancelReplHeader() {
        replTaskService.cancelReplHeader(replHeaderId);

        sayMessage(MESSAGE_SUCCESS);
    }    
    
    @BypassInterceptors
    public Long getTaskId() {
        return taskId;
    }

    public ReplTaskFilter getReplTaskFilter() {
		return replTaskFilter;
	}

	public void setReplTaskFilter(ReplTaskFilter replTaskFilter) {
		this.replTaskFilter = replTaskFilter;
	}

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    @BypassInterceptors
    public Double getToQty() {
        return toQty;
    }

    public void setToQty(Double toQty) {
        this.toQty = toQty;
    }

    public void setPrintData(String printData) {
        this.printData = printData;
    }

    public String getPrintData() {
        return printData;
    }

    public Long getReplHeaderId() {
        return replHeaderId;
    }

    public void setReplHeaderId(Long replHeaderId) {
        this.replHeaderId = replHeaderId;
    }

    public void setReplTaskList(List<ReplenishTask> replTaskList) {
        this.replTaskList = replTaskList;
    }

    public List<ReplenishTask> getReplTaskList() {
        return replTaskList;
    }

    public ReplenishHeader getReplenishHeader() {
        return replenishHeader;
    }

    public void setReplenishHeader(ReplenishHeader replenishHeader) {
        this.replenishHeader = replenishHeader;
    }
    
    public Boolean getCanOverRepl() {
		return canOverRepl;
	}
    
    public void setCanOverRepl(Boolean canOverRepl) {
		this.canOverRepl = canOverRepl;
	}

	public boolean isIgnorePending() {
		return ignorePending;
	}

	public void setIgnorePending(boolean ignorePending) {
		this.ignorePending = ignorePending;
	}

	public String getMixErr() {
		return mixErr;
	}

	public void setMixErr(String mixErr) {
		this.mixErr = mixErr;
	}

	public PrintCfg getReplPrintCfg() {
		return replPrintCfg;
	}

	public void setReplPrintCfg(PrintCfg replPrintCfg) {
		this.replPrintCfg = replPrintCfg;
	}
}