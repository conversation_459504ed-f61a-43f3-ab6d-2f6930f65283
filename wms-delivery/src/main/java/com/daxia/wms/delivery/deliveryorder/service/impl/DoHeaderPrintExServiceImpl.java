package com.daxia.wms.delivery.deliveryorder.service.impl;

import java.util.HashMap;
import java.util.Map;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.deliveryorder.dao.DoHeaderPrintExDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DoHeaderPrintEx;
import com.daxia.wms.delivery.deliveryorder.service.DoHeaderPrintExService;
import com.daxia.wms.Constants;

@Name("doHeaderPrintExService")
@lombok.extern.slf4j.Slf4j
public class DoHeaderPrintExServiceImpl implements DoHeaderPrintExService {

//	@In
//	private DoHeaderPrintExDAO doHeaderPrintExDAO;
//
//	@Override
//	@Transactional
//	public void save(DoHeaderPrintEx doHeaderPrintEx) {
//		doHeaderPrintExDAO.save(doHeaderPrintEx);
//	}
//
//	@Override
//	@Transactional
//	public void update(DoHeaderPrintEx doHeaderPrintEx) {
//		doHeaderPrintExDAO.update(doHeaderPrintEx);
//	}
//
//	@Override
//	public DoHeaderPrintEx getById(Long doId) {
//		return doHeaderPrintExDAO.get(doId);
//	}
//
//	@Override
//	public boolean isHDDoPrint(Long doId) {
//		Map<String, Object> params = new HashMap<String, Object>();
//		params.put("doHeaderId", doId);
//		params.put("isHoldLabelPrinted", Constants.YesNo.YES.getValue());
//		params.put("warehouseId", ParamUtil.getCurrentWarehouseId());
//		return doHeaderPrintExDAO.isExists(params, null);
//	}
}
