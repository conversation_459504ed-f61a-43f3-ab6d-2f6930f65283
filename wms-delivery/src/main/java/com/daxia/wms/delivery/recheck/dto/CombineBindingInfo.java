package com.daxia.wms.delivery.recheck.dto;

import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeaderHis;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 表示核拣时一个箱子装箱信息
 */
@lombok.extern.slf4j.Slf4j
public class CombineBindingInfo
{
	private Long cartonCount;

	private Long orderCount;

	private String carrierName;

	private List<String> cartonNoList;

	/**
	 * 运单号格式正则
	 */
	private String waybillRegx;
	public Long getCartonCount()
	{
		return cartonCount;
	}

	public void setCartonCount(Long cartonCount)
	{
		this.cartonCount = cartonCount;
	}

	public Long getOrderCount()
	{
		return orderCount;
	}

	public void setOrderCount(Long orderCount)
	{
		this.orderCount = orderCount;
	}

	public String getCarrierName()
	{
		return carrierName;
	}

	public void setCarrierName(String carrierName)
	{
		this.carrierName = carrierName;
	}

	public List<String> getCartonNoList()
	{
		return cartonNoList;
	}

	public void setCartonNoList(List<String> cartonNoList)
	{
		this.cartonNoList = cartonNoList;
	}

	public String getWaybillRegx()
	{
		return waybillRegx;
	}

	public void setWaybillRegx(String waybillRegx)
	{
		this.waybillRegx = waybillRegx;
	}
}
