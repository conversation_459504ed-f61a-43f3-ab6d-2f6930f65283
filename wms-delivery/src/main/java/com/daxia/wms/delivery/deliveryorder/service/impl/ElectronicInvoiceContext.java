package com.daxia.wms.delivery.deliveryorder.service.impl;

import com.daxia.framework.common.util.Config;
import com.daxia.wms.Constants;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.deliveryorder.service.AisinogzInvoiceCallService;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.service.ElectronicInvoiceService;
import com.daxia.wms.exp.receive.srv.Invoice2ErpExpSrv;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

@Name("com.daxia.wms.delivery.electronicInvoiceContext")
@lombok.extern.slf4j.Slf4j
public class ElectronicInvoiceContext {
    @In
    private AisinogzInvoiceCallService aisinogzInvoiceCallService;
    @In
    private ElectronicInvoiceService electronicInvoiceService;
    @In
    private Invoice2ErpExpSrv invoice2ErpExpSrv;

    private String einvoicePlateform;

    public ElectronicInvoiceContext() {
        //默认青岛发票平台。
        einvoicePlateform = Constants.EInvoicePlateform.QD_CHINAEINV.getValue();
        einvoicePlateform = Config.get(Keys.Delivery.invoice_einvoice_plateform, Config.ConfigLevel.WAREHOUSE);

    }

    /**
     * 冲红发票
     *
     * @param invoiceHeader
     */
    public void writeBack(InvoiceHeader invoiceHeader) {
        //青岛发票平台
        if (Constants.EInvoicePlateform.QD_CHINAEINV.getValue().equals(einvoicePlateform)) {
            electronicInvoiceService.writeBack(invoiceHeader);
        } else if (Constants.EInvoicePlateform.AISINO_GZ.getValue().equals(einvoicePlateform)) {
            aisinogzInvoiceCallService.chInvoice(invoiceHeader, true);
        }
        //发票信息回写ERP
        invoice2ErpExpSrv.createMsg(invoiceHeader.getId(),Constants.InvoiceWriteBackType.INVOICE.getValue());
    }

    /**
     * 开票
     *
     * @param invoiceHeader
     */
    public void billing(InvoiceHeader invoiceHeader) {
        //青岛发票平台
        if (Constants.EInvoicePlateform.QD_CHINAEINV.getValue().equals(einvoicePlateform)) {
            electronicInvoiceService.billing(invoiceHeader);
        } else if (Constants.EInvoicePlateform.AISINO_GZ.getValue().equals(einvoicePlateform)) {
            aisinogzInvoiceCallService.sendInvEli(invoiceHeader, true);
        }
        //发票信息回写ERP
        invoice2ErpExpSrv.createMsg(invoiceHeader.getId(),Constants.InvoiceWriteBackType.INVOICE.getValue());
    }

    /**
     * 查询并绑定发票信息
     *
     * @param invoiceHeader
     */
    public void bind(InvoiceHeader invoiceHeader) {
        //青岛发票平台
        if (Constants.EInvoicePlateform.QD_CHINAEINV.getValue().equals(einvoicePlateform)) {
            electronicInvoiceService.bind(invoiceHeader);
        } else if (Constants.EInvoicePlateform.AISINO_GZ.getValue().equals(einvoicePlateform)) {
            aisinogzInvoiceCallService.bind(invoiceHeader);
        }
    }

    public String getEinvoicePlateform() {
        return einvoicePlateform;
    }

    public void setEinvoicePlateform(String einvoicePlateform) {
        this.einvoicePlateform = einvoicePlateform;
    }
}