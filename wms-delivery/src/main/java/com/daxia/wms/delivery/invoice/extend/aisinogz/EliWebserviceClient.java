package com.daxia.wms.delivery.invoice.extend.aisinogz;

import com.daxia.framework.common.util.JsonUtil;
import com.daxia.wms.invoice.entity.*;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Marshaller;
import java.io.*;

import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;

@lombok.extern.slf4j.Slf4j
public class EliWebserviceClient {
	
	public static void main(String[] args) {
		
		// testSendToInvEli();
		//		testQueryEliData();
		        testInvEli();
		//		testQueryEliStock();
	}
	
	/**
	 * 测试发送订单开票(异步回传)
	 */
	public static void testSendToInvEli() {
		IEliWebService wb = new IEliWebService();
		// 对象工厂
		ObjectFactory of = new ObjectFactory();
		/******** 发票总体类 ********/
		ElectroniceInfo e = new ElectroniceInfo();
		e.setUserName(of.createElectroniceInfoUserName("14410101"));
		e.setPassWord(of.createElectroniceInfoPassWord("JWVFkqs7IP+++xBb1I9a/qr6/L/qYxyw=="));
		// 发票请求唯一流水号
		e.setFPQQLSH(of.createElectroniceInfoFPQQLSH("211831258112222"));
		// 业务订单号
		e.setDDH(of.createElectroniceInfoDDH("34238"));
		// 开票方纳税人识别号
		e.setKPNSRSBH(of.createElectroniceInfoKPNSRSBH("440002999999441"));
		// 开票方名称
		e.setKPNSRMC(of.createElectroniceInfoKPNSRMC("测试"));
		// 代开标志
		e.setDKBZ(of.createElectroniceInfoDKBZ("1"));
		// 销货方识别号
		e.setXHFNSRSBH(of.createElectroniceInfoXHFNSRSBH("440002999999441"));
		// 销货方名称
		e.setXHFMC(of.createElectroniceInfoXHFMC("测试"));
		// 销货方地址
		e.setXHFDZ(of.createElectroniceInfoXHFDZ("广州市越秀区水荫路119号“星光映景”25楼（01至10室）"));
		// 销货方电话
		e.setXHFDH(of.createElectroniceInfoXHFDH("020-83208136"));
		// 销货方银行账号
		e.setXHFYHZH(of.createElectroniceInfoXHFYHZH("中国工商银行广州银山支行  3602051719200476881"));
		// 购货方名称
		e.setGHFMC(of.createElectroniceInfoGHFMC("测试企业"));
		
		// 开票员
		e.setKPR(of.createElectroniceInfoKPR("开票员"));
		// 开票类型
		e.setKPLX(of.createElectroniceInfoKPLX("1"));
		// 操作代码
		e.setCZDM(of.createElectroniceInfoCZDM("10"));
		// 价税合计金额
		e.setKPHJJE(1d);
		// 备注
		e.setBZ(of.createElectroniceInfoBZ("广州市越秀区水荫路119号“星光映景”25楼（01至10室）广州市越秀区水荫路119号“星光映景”25楼（01至10室）广州市越秀区水荫路119号“星光映景”25楼（01至10室）广州市越秀区水荫路119号“星光映景”25楼（01至10室）广州市越秀区水荫路119号“星光映景”25楼（01至10室）广州市越秀区水荫路119号“星光映景”25楼（01至10室）"));
		//        e.setYFPDM(of.createElectroniceInfoYFPDM("044001600111"));
		//        e.setYFPHM(of.createElectroniceInfoYFPHM("21847663"));
		ArrayOfElectroniceDetail list = new ArrayOfElectroniceDetail();
		/******** 发票明细类 ********/
		ArrayList<ElectroniceDetail> ds = (ArrayList<ElectroniceDetail>) list
				.getElectroniceDetail();
		ElectroniceDetail detail = new ElectroniceDetail();
		// 项目名称
		detail.setXMMC(of.createElectroniceDetailXMMC("商品"));
		// 项目单位
		detail.setDW(of.createElectroniceDetailDW("个"));
		// 规格型号
		detail.setGGXH(of.createElectroniceDetailGGXH("xxl"));
		// 项目编码
		detail.setXMBM(of.createElectroniceDetailXMBM("123321"));
		// 项目数量
		detail.setXMSL(1d);
		// 项目单价
		detail.setXMDJ(1.00/1.00);
		// 含税标志
		detail.setHSBZ(of.createElectroniceDetailHSBZ("1"));
		// 项目金额
		detail.setXMJE(1.00);
		// 税率
		detail.setSL(of.createElectroniceDetailSL("0.17"));
		// 项目税额
		detail.setSE(0.15);
		ds.add(detail);
		
		e.setDetails(of.createElectroniceInfoDetails(list));
		ReturnElectronice result = wb.getIEliWebServiceHttpPort().sendToInvEli(e);
		
		System.out.println("obj:" + convertToXml(e, "utf-8"));
		System.out.println("测试请求开票(异步)返回信息：" + result.getReturnMsg().getValue());
		
	}
	
	/**
	 * 测试查询订单状态
	 */
	public static void testQueryEliData() {
		IEliWebService wb = new IEliWebService();
		// 对象工厂
		ObjectFactory of = new ObjectFactory();
		/******** 发票总体类 ********/
		ElectroniceInfo e = new ElectroniceInfo();
		// 查询条件的流水号,订单号,开票方纳税人识别号,
		e.setFPQQLSH(of.createElectroniceInfoFPQQLSH("211831258"));
		e.setDDH(of.createElectroniceInfoDDH("34238"));
		e.setKPNSRSBH(of.createElectroniceInfoKPNSRSBH("440002999999441"));
		e.setXHFNSRSBH(of.createElectroniceInfoXHFNSRSBH("440002999999441"));
		
		ReturnElectronice result = wb.getIEliWebServiceHttpPort().queryEliData(
				e);
		System.out.println("测试查询订单状态返回信息：" + result.getReturnMsg().getValue()
				+ ";发票代码：" + result.getFPDM().getValue() + ";发票号码："
				+ result.getFPHM().getValue()+";检验码："+result.getJYM().getValue());
		
		if (result.getPDFURL() != null) {
			System.out
					.println("取出电子发票PDF地址：http://61.144.36.122:5000/IS/pd?id="
							+ result.getPDFURL().getValue());
			try {
				
				URL url = new URL("http://192.168.10.43:8080/IS/pd?id="
						+ result.getPDFURL().getValue());
				// 返回一个 URLConnection 对象，它表示到 URL 所引用的远程对象的连接。
				URLConnection uc = url.openConnection();
				
				// 打开的连接读取的输入流。
				InputStream is = uc.getInputStream();
				
				byte[] b = new byte[1024];
				int len = 0;
				ByteArrayOutputStream bos = new ByteArrayOutputStream();
				while ((len = is.read(b)) != -1) {
					bos.write(b, 0, len);
				}
				bos.close();
				
				byte[] getData = bos.toByteArray();
				
				// 文件保存位置
				File saveDir = new File("D:\\电子发票");
				if (!saveDir.exists()) {
					saveDir.mkdir();
				}
				File file = new File(saveDir + File.separator
						+ result.getPDFURL().getValue());
				FileOutputStream fos = new FileOutputStream(file);
				fos.write(getData);
				if (fos != null) {
					fos.close();
				}
				
				fos.flush();
				is.close();
			} catch (MalformedURLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			} catch (IOException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			
		}
	}
	
	
	/**
	 * 测试发送订单开票(同步)
	 */
	public static void testInvEli() {
		IEliWebService wb = new IEliWebService();
		// 对象工厂
		ObjectFactory of = new ObjectFactory();
		/******** 发票总体类 ********/
		ElectroniceInfo e = new ElectroniceInfo();
		e.setUserName(of.createElectroniceInfoUserName("14410101"));
		e.setPassWord(of
				.createElectroniceInfoPassWord("JWVFkqs7IP+++xBb1I9a/qr6/L/qYxyw=="));
		// 发票请求唯一流水号
		e.setFPQQLSH(of.createElectroniceInfoFPQQLSH("43535153611253"));
		// 业务订单号
		e.setDDH(of.createElectroniceInfoDDH("900122"));
		// 开票方纳税人识别号
		e.setKPNSRSBH(of.createElectroniceInfoKPNSRSBH("440002999999441"));
		// 开票方名称
		e.setKPNSRMC(of.createElectroniceInfoKPNSRMC("即开即传企业"));
		// 代开标志
		e.setDKBZ(of.createElectroniceInfoDKBZ("1"));
		// 销货方识别号
		e.setXHFNSRSBH(of.createElectroniceInfoXHFNSRSBH("9144060073757157XN"));
		// 销货方名称
		e.setXHFMC(of.createElectroniceInfoXHFMC("即开即传企业"));
		// 销货方地址
		e.setXHFDZ(of.createElectroniceInfoXHFDZ("测试销方地址"));
		// 销货方电话
		e.setXHFDH(of.createElectroniceInfoXHFDH("02083191299"));
		// 销货方银行账号
		e.setXHFYHZH(of.createElectroniceInfoXHFYHZH("测试销方银行21321321321"));
		// 购货方名称
		e.setGHFMC(of.createElectroniceInfoGHFMC("测试购方企业"));
		
		// 开票员
		e.setKPR(of.createElectroniceInfoKPR("开票员"));
		// 开票类型
		e.setKPLX(of.createElectroniceInfoKPLX("2"));
		// 操作代码
		e.setCZDM(of.createElectroniceInfoCZDM("10"));
		e.setYFPDM(of.createElectroniceInfoYFPDM("044001507111"));
		e.setYFPHM(of.createElectroniceInfoYFPHM("14514790"));
		// 价税合计金额
		e.setKPHJJE(100d);
		// 备注
		e.setBZ(of.createElectroniceInfoBZ("备注"));
		
		ArrayOfElectroniceDetail list = new ArrayOfElectroniceDetail();
		/******** 发票明细类 ********/
		ArrayList<ElectroniceDetail> ds = (ArrayList<ElectroniceDetail>) list
				.getElectroniceDetail();
		ElectroniceDetail detail = new ElectroniceDetail();
		// 项目名称
		detail.setXMMC(of.createElectroniceDetailXMMC("测试商品"));
		// 项目单位
		detail.setDW(of.createElectroniceDetailDW("个"));
		// 规格型号
		detail.setGGXH(of.createElectroniceDetailGGXH("xxl"));
		// 项目编码
		detail.setXMBM(of.createElectroniceDetailXMBM("123321"));
		// 项目数量
		detail.setXMSL(-10.00);
		// 项目单价
		detail.setXMDJ(10.00);
		// 含税标志
		detail.setHSBZ(of.createElectroniceDetailHSBZ("1"));
		// 项目金额
		detail.setXMJE(-100.00);
		// 税率
		detail.setSL(of.createElectroniceDetailSL("0.17"));
		// 项目税额
		detail.setSE(-14.53);
		ds.add(detail);
		
		e.setDetails(of.createElectroniceInfoDetails(list));
		ReturnElectronice result = wb.getIEliWebServiceHttpPort().invEli(
				e);
		System.out
				.println("测试请求开票(同步)返回信息：" + result.getReturnMsg().getValue());
		
		System.out.println("obj:" + convertToXml(e, "utf-8"));
	}
	public static void testQueryEliStock(){
		IEliWebService wb = new IEliWebService();
		// 对象工厂
		ObjectFactory of = new ObjectFactory();
		ElectroniceStock es=wb.getIEliWebServiceHttpPort().queryEliStock("440002999999441");
		System.out.println("返回请求库存信息：剩余份数："+es.getSYFPFS().getValue()+"返回信息："+es.getReturnMsg().getValue());
	}
	
	
	/**
	 * JavaBean转换成xml
	 * @param obj
	 * @param encoding
	 * @return
	 */
	public static String convertToXml(Object obj, String encoding) {
		String result = null;
		try {
			JAXBContext context = JAXBContext.newInstance(obj.getClass());
			Marshaller marshaller = context.createMarshaller();
			marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
			marshaller.setProperty(Marshaller.JAXB_ENCODING, encoding);
			
			StringWriter writer = new StringWriter();
			marshaller.marshal(obj, writer);
			result = writer.toString();
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return result;
	}
}
