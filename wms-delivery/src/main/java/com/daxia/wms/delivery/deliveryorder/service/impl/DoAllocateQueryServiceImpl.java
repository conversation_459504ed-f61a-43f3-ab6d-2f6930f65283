package com.daxia.wms.delivery.deliveryorder.service.impl;

import com.daxia.wms.delivery.deliveryorder.dao.DoAllocateDetailDAO;
import com.daxia.wms.delivery.deliveryorder.dao.DoAllocateHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.dao.DoHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateQueryService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Name("doAllocateQueryService")
@lombok.extern.slf4j.Slf4j
public class DoAllocateQueryServiceImpl implements DoAllocateQueryService {
    
    @In
    DoAllocateDetailDAO doAllocateDetailDAO;
    
    @Override
    public Map<Long, Long> getDoLeafSkuId(List<Long> doIds) {
        return doAllocateDetailDAO.getDoLeafSkuId(doIds);
    }
}