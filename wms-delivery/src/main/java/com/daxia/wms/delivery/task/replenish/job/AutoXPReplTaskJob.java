package com.daxia.wms.delivery.task.replenish.job;


import com.daxia.framework.common.util.*;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.Priority;
import com.daxia.wms.delivery.task.replenish.service.ReplHeaderService;
import com.daxia.wms.delivery.task.replenish.service.ReplenishTaskGenerateContext;
import com.daxia.wms.delivery.task.replenish.service.impl.ReplHeaderServiceImpl;
import com.daxia.wms.delivery.task.replenish.service.impl.ReplenishTaskGenerateContextImpl;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.master.service.impl.WarehouseServiceImpl;
import com.daxia.wms.util.AlarmUtil;
import org.jboss.seam.Component;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.log.Log;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Name("autoXPReplTaskJob")
@AutoCreate
@lombok.extern.slf4j.Slf4j
public class AutoXPReplTaskJob {
    private static volatile boolean running = false;

    private static Set<String> ignoreExceptions = new HashSet<String>();

    static {
        ignoreExceptions.add("error.delivery.noLocNeedFreeTimeRepl");
        ignoreExceptions.add("error.stock.noInitReplTaskExist");
    }



    public void run() {
        if (running) {
            log.debug("autoXPReplTaskJob Run Already. ");

            return;
        }

        running();
        log.debug("Begin Run autoXPReplTaskJob, Please Waiting ............... ");

        List<Warehouse> whList = loadRunningWh();
        if (whList != null) {
            for (Warehouse wh : whList) {
                try {
                    ParamUtil.setCurrentWarehouseId(wh.getId());
                    ReplenishTaskGenerateContext replenishTaskGenerateContext = ((ReplenishTaskGenerateContext) Component.getInstance(ReplenishTaskGenerateContextImpl.class));
                    ReplHeaderService replHeaderService = ((ReplHeaderService) Component.getInstance(ReplHeaderServiceImpl.class));
    
                    replenishTaskGenerateContext.createXPReplenishTask(null, null, null, Boolean.TRUE);
                    replHeaderService.addReplTask(Constants.ReplType.XP.getValue(), null, Boolean.TRUE);
                } catch (Exception e) {
                    //ignoreExceptions类型的BusinessException不做处理
                    if (e instanceof BusinessException && ignoreExceptions.contains(e.getMessage())) {
                        log.warn("Auto Publish Repl Task Faild:" + e.getMessage());
                    } else {
                        log.error("Auto Publish Repl Task Error:", e);
                        ByteArrayOutputStream bs = new ByteArrayOutputStream();
                        PrintStream ps = new PrintStream(bs);
                        try {
                            e.printStackTrace(ps);
        
                            String content = "自动补货异常：" + bs.toString();
                            AlarmUtil.sendEmail("自动补货失败", content);
                        } catch (Exception e1) {
                            log.error("Send mail error: ", e1);
                        } finally {
                            ps.close();
                        }
                    }
                }
            }
        }

        unRunning();
    }

    //加载所有运行状态的仓库信息，并catch异常
    private List<Warehouse> loadRunningWh() {
        try {
            WarehouseService warehouseService = ((WarehouseService) Component.getInstance(WarehouseServiceImpl.class));
            return warehouseService.getRunningWh();
        } catch (Exception e) {
            log.error("Component Instance Error:", e);
        }
        return null;
    }

    private static void running() {
        running = true;
    }

    private static void unRunning() {
        running = false;
    }
}