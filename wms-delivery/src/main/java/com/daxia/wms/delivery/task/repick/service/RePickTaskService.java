package com.daxia.wms.delivery.task.repick.service;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.Constants.TaskStatus;
import com.daxia.wms.Constants.TaskType;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.stock.stock.dto.DamageInfoDTO;
import com.daxia.wms.stock.task.entity.TrsTask;
import com.daxia.wms.stock.task.filter.TrsTaskFilter;

import java.util.List;

/**
 * 返拣任务接口
 */
public interface RePickTaskService {
	
	/**
     * 查询返拣任务页面
     * @param trsTaskFilter
     * @param startIndex
     * @param pageSize
     * @return
     */
    public DataPage<TrsTask> queryRePickTrs(TrsTaskFilter trsTaskFilter, int startIndex, int pageSize);
    
    /**
     * 执行返拣
     * @param trsTask
     * @param taskFromPage
     * @param reversePickHeaderId
     * @param updateBy
     * @return
     * @throws DeliveryException
     */
    public Long executeRePick(TrsTask trsTask, TrsTask taskFromPage, DamageInfoDTO damageInfoDTO,
                              Long reversePickHeaderId, String updateBy) throws DeliveryException;
    
    /**
     * 根据给定的任务idList选择任务列表
     * @param ids
     * @param taskType
     * @return
     */
    public List<TrsTask> getTasksByIds(List<Long> ids, String taskType);

    /**
     * 根据filter查询返拣任务
     * @param filter
     */
    public List<TrsTask> queryRePickTrs(TrsTaskFilter filter);
    
    List<TrsTask> findRePickTasks(TaskStatus released, TaskType rk, String containerNo);
    
    /**
     * 获取返拣任务，按照上架库位顺序排序
     */
    public List<TrsTask> findRePickTasks(Long skuId, TaskStatus status, TaskType taskType);
    
    /**
     * 获取指定状态的返拣任务的skuId     
     * @param status
     * @param taskType
     * @return 获取指定状态的返拣任务的skuId
     */
    public List<Long> findRePickTaskSkus(TaskStatus status, TaskType taskType);
    
    /**
     * 获取指定状态的返拣任务
     * @param status
     * @param taskType
     * @return 获取指定状态的返拣任务
     */
    public List<TrsTask> findRePickTasks(TaskStatus status, TaskType taskType);
    
    /**
     * 通过docId得到返拣任务
     * @param docId
     * @param taskType
     * @return
     */
    public List<TrsTask> findRepickTaskByDocId(Long docId, TaskType taskType);
    
    /**
     * do是否存在未完成的反拣任务
     * @param doHeaderId
     * @return
     */
    public boolean isDoHasNotFinishedRepickTask(Long doHeaderId);

    void batchExecuteRePick(List<Long> reverseHeaderIds, String locCode);
}
