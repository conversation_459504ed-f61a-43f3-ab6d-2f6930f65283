package com.daxia.wms.delivery.pick.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.framework.common.util.NumberUtil;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.entity.PackageInfoDetail;
import com.daxia.wms.master.entity.PackageInfoHeader;
import com.daxia.wms.master.entity.Sku;
import com.daxia.wms.stock.stock.entity.StockBatchAtt;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * 拣货任务实体
 */
@Entity
@Table(name = "tsk_pick")
@Where(clause = "IS_DELETED = 0")
@SQLDelete(sql = "update tsk_pick set is_deleted = 1 where id = ? and version = ?")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@lombok.extern.slf4j.Slf4j
public class PickTask extends WhBaseEntity {

    private static final long serialVersionUID = 1768226576744488653L;

    private Long id;

    /**
     * 产品ID
     */   
    private Long skuId;

    /**
     * 批次ID
     */
    private Long lotId;

    /**
     * 库位ID -- 拣货库位
     */
    private Long locId;

    /**
     * 托盘号
     */
    private String lpnNo;

    /**
     * 拣货到库位
     */
    private Long toLocId;

    /**
     * 拣货到LPN
     */
    private String toLpnNo;

    private Long waveHeaderId;

    private Long pktHeaderId;

    /**
     * DO单头信息ID
     */
    private Long doHeaderId;

    /**
     * DO单明细ID
     */
    private Long doDetailId;

    private String status;

    private BigDecimal qty;

    private BigDecimal qtyUnit;

    /**
     * 拣货数量
     */
    private BigDecimal pickedQty;

    private BigDecimal qtyPickedUnit;

    /**
     * 分拣数量
     */
    private BigDecimal qtySorted;

    private BigDecimal qtySortedUnit;

    /**
     * 发货数量
     */
    private BigDecimal qtyShipped;

    /**
     * 包装ID
     */
    private Long packId;

    /**
     * PACK_DETAIL_ID
     */
    private Long packDetailId;

    private String notes;

    /**
     * 拣货人
     */
    private String pickWho;

    /**
     * 拣货时间
     */
    private Date pickTime;

    /**
     * 分拣人
     */
    private String sortWho;

    /**
     * 分拣时间
     */
    private Date sortTime;

    /**
     * 发货人
     */
    private String shipWho;

    /**
     * 发货时间
     */
    private Date shipTime;

    /**
     * 拣货情况：0 ：正常 1：缺货 2：破损
     */
    private String stockStatus;

    private Long fmStockId;

    private Long toStockId;

    private Long priority;

    private Long allocatingId;

    private Location location;

    private Sku sku;

    private DeliveryOrderHeader doHeader;

    private DeliveryOrderDetail doDetail;

    private WaveHeader wave;

    private StockBatchAtt stockBatchAtt;
    
    private PickHeader pickHeader;
    
    private PackageInfoHeader packageInfoHeader;
    
    private PackageInfoDetail packageInfoDetail;
    
    private Integer isDeleted;
    /**
     * 拣货任务关联的容器号
     */
    private String containerNo;
    /**
     * 该任务是否是 开启拣货路径优化后确认正常拣货的
     */
    private Integer optFlag;

    private BigDecimal cartonQty;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)  
    @Column(name = "ID")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "WAVE_H_ID")
    public Long getWaveHeaderId() {
        return waveHeaderId;
    }

    public void setWaveHeaderId(Long waveHeaderId) {
        this.waveHeaderId = waveHeaderId;
    }

    @Column(name = "STATUS")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "NOTES")
    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    @Column(name = "QTY")
    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    @Column(name = "qty_unit")
    public BigDecimal getQtyUnit() {
        return qtyUnit;
    }

    public void setQtyUnit(BigDecimal qtyUnit) {
        this.qtyUnit = qtyUnit;
    }

    @Column(name = "QTY_PICKED")
    public BigDecimal getPickedQty() {
        return pickedQty;
    }

    public void setPickedQty(BigDecimal pickedQty) {
        this.pickedQty = pickedQty;
    }

    @Column(name = "qty_Picked_Unit")
    public BigDecimal getQtyPickedUnit() {
        return qtyPickedUnit;
    }

    public void setQtyPickedUnit(BigDecimal qtyPickedUnit) {
        this.qtyPickedUnit = qtyPickedUnit;
    }

    @Column(name = "QTY_SORTED")
    public BigDecimal getQtySorted() {
        return qtySorted;
    }

    public void setQtySorted(BigDecimal qtySorted) {
        this.qtySorted = qtySorted;
    }

    @Column(name = "qty_sorted_unit")
    public BigDecimal getQtySortedUnit() {
        return qtySortedUnit;
    }

    public void setQtySortedUnit(BigDecimal qtySortedUnit) {
        this.qtySortedUnit = qtySortedUnit;
    }

    @Column(name = "QTY_SHIPPED")
    public BigDecimal getQtyShipped() {
        return qtyShipped;
    }

    public void setQtyShipped(BigDecimal qtyShipped) {
        this.qtyShipped = qtyShipped;
    }

    @Column(name = "PICK_WHO")
    public String getPickWho() {
        return pickWho;
    }

    public void setPickWho(String pickWho) {
        this.pickWho = pickWho;
    }

    @Column(name = "PICK_TIME")
    public Date getPickTime() {
        return pickTime;
    }

    public void setPickTime(Date pickTime) {
        this.pickTime = pickTime;
    }

    @Column(name = "SORT_WHO")
    public String getSortWho() {
        return sortWho;
    }

    public void setSortWho(String sortWho) {
        this.sortWho = sortWho;
    }

    @Column(name = "SORT_TIME")
    public Date getSortTime() {
        return sortTime;
    }

    public void setSortTime(Date sortTime) {
        this.sortTime = sortTime;
    }

    @Column(name = "SHIP_WHO")
    public String getShipWho() {
        return shipWho;
    }

    public void setShipWho(String shipWho) {
        this.shipWho = shipWho;
    }

    @Column(name = "SHIP_TIME")
    public Date getShipTime() {
        return shipTime;
    }

    public void setShipTime(Date shipTime) {
        this.shipTime = shipTime;
    }

    @Column(name = "STOCK_STATUS")
    public String getStockStatus() {
        return stockStatus;
    }

    public void setStockStatus(String stockStatus) {
        this.stockStatus = stockStatus;
    }

    @Column(name = "SKU_ID")
    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    @Column(name = "LOT_ID")
    public Long getLotId() {
        return lotId;
    }

    public void setLotId(Long lotId) {
        this.lotId = lotId;
    }

    @Column(name = "FM_LOC_ID")
    public Long getLocId() {
        return locId;
    }

    public void setLocId(Long locId) {
        this.locId = locId;
    }

    @Column(name = "LPN_NO")
    public String getLpnNo() {
        return lpnNo;
    }

    public void setLpnNo(String lpnNo) {
        this.lpnNo = lpnNo;
    }

    @Column(name = "TO_LOC_ID")
    public Long getToLocId() {
        return toLocId;
    }

    public void setToLocId(Long toLocId) {
        this.toLocId = toLocId;
    }

    @Column(name = "TO_LPN_NO")
    public String getToLpnNo() {
        return toLpnNo;
    }

    public void setToLpnNo(String toLpnNo) {
        this.toLpnNo = toLpnNo;
    }

    @Column(name = "PKT_H_ID")
    public Long getPktHeaderId() {
        return pktHeaderId;
    }

    public void setPktHeaderId(Long pktHeaderId) {
        this.pktHeaderId = pktHeaderId;
    }

    @Column(name = "DOC_ID")
    public Long getDoHeaderId() {
        return doHeaderId;
    }

    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }

    @Column(name = "DOC_LINE_ID")
    public Long getDoDetailId() {
        return doDetailId;
    }

    public void setDoDetailId(Long doDetailId) {
        this.doDetailId = doDetailId;
    }

    @Column(name = "PACK_ID")
    public Long getPackId() {
        return packId;
    }

    public void setPackId(Long packId) {
        this.packId = packId;
    }

    @Column(name = "PACK_DETAIL_ID")
    public Long getPackDetailId() {
        return packDetailId;
    }

    public void setPackDetailId(Long packDetailId) {
        this.packDetailId = packDetailId;
    }

    @Column(name = "FM_STOCK_ID")
    public Long getFmStockId() {
        return fmStockId;
    }

    public void setFmStockId(Long fmStockId) {
        this.fmStockId = fmStockId;
    }

    @Column(name = "TO_STOCK_ID")
    public Long getToStockId() {
        return toStockId;
    }

    public void setToStockId(Long toStockId) {
        this.toStockId = toStockId;
    }

    @Column(name = "PRIORITY")
    public Long getPriority() {
        return priority;
    }

    public void setPriority(Long priority) {
        this.priority = priority;
    }
    
    @Column(name = "IS_DELETED")
    public Integer getIsDeleted() {
		return isDeleted;
	}
    
    public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}

    @ManyToOne(targetEntity = Location.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "FM_LOC_ID", insertable = false, updatable = false)
    public Location getLocation() {
        return location;
    }

    public void setLocation(Location location) {
        this.location = location;
    }

    @ManyToOne(targetEntity = Sku.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "SKU_ID", insertable = false, updatable = false)
    public Sku getSku() {
        return sku;
    }

    public void setSku(Sku sku) {
        this.sku = sku;
    }

    @ManyToOne(targetEntity = DeliveryOrderHeader.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "DOC_ID", insertable = false, updatable = false)
    public DeliveryOrderHeader getDoHeader() {
        return doHeader;
    }

    public void setDoHeader(DeliveryOrderHeader doHeader) {
        this.doHeader = doHeader;
    }

    @ManyToOne(targetEntity = DeliveryOrderDetail.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "DOC_LINE_ID", insertable = false, updatable = false)
    public DeliveryOrderDetail getDoDetail() {
        return doDetail;
    }

    public void setDoDetail(DeliveryOrderDetail doDetail) {
        this.doDetail = doDetail;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "WAVE_H_ID", insertable = false, updatable = false)
    public WaveHeader getWave() {
        return wave;
    }

    public void setWave(WaveHeader wave) {
        this.wave = wave;
    }
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PKT_H_ID", insertable = false, updatable = false)
    public PickHeader getPickHeader() {
		return pickHeader;
	}
    
    public void setPickHeader(PickHeader pickHeader) {
		this.pickHeader = pickHeader;
	}

    @ManyToOne(targetEntity = StockBatchAtt.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "LOT_ID", insertable = false, updatable = false)
    public StockBatchAtt getStockBatchAtt() {
        return stockBatchAtt;
    }

    public void setStockBatchAtt(StockBatchAtt stockBatchAtt) {
        this.stockBatchAtt = stockBatchAtt;
    }

    @Column(name = "STK_ALLOCATING_ID")
    public Long getAllocatingId() {
        return allocatingId;
    }

    public void setAllocatingId(Long allocatingId) {
        this.allocatingId = allocatingId;
    }
    
    
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = " PACK_ID",insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public PackageInfoHeader getPackageInfoHeader() {
        return packageInfoHeader;
    }

    public void setPackageInfoHeader(PackageInfoHeader packageInfoHeader) {
        this.packageInfoHeader = packageInfoHeader;
    }
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PACK_DETAIL_ID",insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public PackageInfoDetail getPackageInfoDetail() {
        return packageInfoDetail;
    }
    public void setPackageInfoDetail(PackageInfoDetail packageInfoDetail) {
        this.packageInfoDetail = packageInfoDetail;
    }
    
    @Column(name = "CONTAINER_NO")
    public String getContainerNo() {
        return containerNo;
    }
    
    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }
    
    @Transient
    public Integer getOptFlag() {
		return optFlag;
	}

	public void setOptFlag(Integer optFlag) {
		this.optFlag = optFlag;
	}

    @Transient
    public BigDecimal getCartonQty() {
         return NumberUtil.bigDecimalRemoveZero(pickedQty.divide(new BigDecimal(sku.getUdf4()),2, RoundingMode.HALF_UP));
    }
}