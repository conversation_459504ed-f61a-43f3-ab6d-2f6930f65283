package com.daxia.wms.delivery.pick.filter;

import java.util.List;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

/**
 * 拣货任务查询filter
 */
@lombok.extern.slf4j.Slf4j
public class PickTaskFilter extends WhBaseQueryFilter {

    private static final long serialVersionUID = 2704170050460539136L;

    private Long doHeaderId;

    private Long doDetailId;

    private Long pickHeaderId;

    private Long skuId;

    private Long locId;

    private String status;

    private List<Long> doDetailIds;
    
    private List<Long> doHeaderIds;
    
    private List<String> stockStatuses;
    
    private List<String> statusList;

    private String containerNo;

    private String partitionCode;
    
    private Long waveHeaderId;

    private String doNo;

    private String productCode;

	@Operation(fieldName = "o.skuId", operationType = OperationType.EQUAL)
    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    @Operation(fieldName = "o.status", operationType = OperationType.EQUAL)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Operation(fieldName = "o.doHeaderId", operationType = OperationType.EQUAL)
    public Long getDoHeaderId() {
        return doHeaderId;
    }

    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }

    @Operation(fieldName = "o.doDetailId", operationType = OperationType.EQUAL)
    public Long getDoDetailId() {
        return doDetailId;
    }

    public void setDoDetailId(Long doDetailId) {
        this.doDetailId = doDetailId;
    }

    @Operation(fieldName = "o.pktHeaderId", operationType = OperationType.EQUAL)
    public Long getPickHeaderId() {
        return pickHeaderId;
    }

    public void setPickHeaderId(Long pickHeaderId) {
        this.pickHeaderId = pickHeaderId;
    }

    @Operation(fieldName = "o.locId", operationType = OperationType.EQUAL)
    public Long getLocId() {
        return locId;
    }

    public void setLocId(Long locId) {
        this.locId = locId;
    }

    @Operation(fieldName = "o.doDetailId", operationType = OperationType.IN)
    public List<Long> getDoDetailIds() {
        return doDetailIds;
    }

    public void setDoDetailIds(List<Long> doDetailIds) {
        this.doDetailIds = doDetailIds;
    }

    @Operation(fieldName = "o.doHeaderId", operationType = OperationType.IN)
    public List<Long> getDoHeaderIds() {
        return doHeaderIds;
    }

    public void setDoHeaderIds(List<Long> doHeaderIds) {
        this.doHeaderIds = doHeaderIds;
    }

    @Operation(fieldName = "o.stockStatus", operationType = OperationType.IN)
    public List<String> getStockStatuses() {
        return stockStatuses;
    }

    public void setStockStatuses(List<String> stockStatuses) {
        this.stockStatuses = stockStatuses;
    }

    @Operation(fieldName = "o.status", operationType = OperationType.IN)
    public List<String> getStatusList() {
        return statusList;
    }
    
    public void setStatusList(List<String> statusList) {
        this.statusList = statusList;
    }

    @Operation(fieldName = "o.containerNo", operationType = OperationType.EQUAL)
    public String getContainerNo() {
        return containerNo;
    }
    
    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }

    @Operation(fieldName = "o.location.partition.partitionCode", operationType = OperationType.EQUAL)
    public String getPartitionCode() {
		return partitionCode;
	}

	public void setPartitionCode(String partitionCode) {
		this.partitionCode = partitionCode;
	}

	@Operation(fieldName = "o.waveHeaderId", operationType = OperationType.EQUAL)
	public Long getWaveHeaderId() {
		return waveHeaderId;
	}

	public void setWaveHeaderId(Long waveHeaderId) {
		this.waveHeaderId = waveHeaderId;
	}
    @Operation(fieldName = "o.doHeader.doNo", operationType = OperationType.EQUAL)
    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }
    @Operation(fieldName = "o.sku.productCode", operationType = OperationType.EQUAL)
    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }
}