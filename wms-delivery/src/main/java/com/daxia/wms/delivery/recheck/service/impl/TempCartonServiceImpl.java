package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.cfg.BeforeSyncOrderCfg;
import com.daxia.framework.common.util.*;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.Keys;
import com.daxia.wms.OrderLogConstants;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.OrderLogService;
import com.daxia.wms.delivery.load.service.LoadService;
import com.daxia.wms.delivery.recheck.dao.TempCartonDAO;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.entity.TempCarton;
import com.daxia.wms.delivery.recheck.service.TempCartonService;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 装箱单业务
 */
@Name("com.daxia.wms.delivery.tempCartonService")
@lombok.extern.slf4j.Slf4j
public class TempCartonServiceImpl implements TempCartonService {

    @In
    TempCartonDAO tempCartonDAO;

    @In
    private LoadService loadService;


    @In
    DeliveryOrderService deliveryOrderService;

    @In
    OrderLogService orderLogService;

    @In(create = true)
    private CartonNoGenerateDispatcher cartonNoGenerateDispatcher;

    @Override
    public TempCarton getByDoId(Long doHeaderId) {
        return tempCartonDAO.getByDoId(doHeaderId);
    }

    @Override
    public List<TempCarton> findByDoIdList(List<Long> doIdList) {
        return tempCartonDAO.findByDoIdList(doIdList);
    }

    @Override
    @Transactional
    public void saveOrUpdate(TempCarton tempCarton) {
        tempCartonDAO.removeByDoId(tempCarton.getDoHeaderId(), tempCarton.getCarrierId());
        tempCartonDAO.saveOrUpdate(tempCarton);
    }

    @Override
    @Transactional
    public TempCarton generateTempCarton(Long doId) {
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(doId);
        if (doHeader.getNeedCancel()) {
            //如果订单已取消，不下单
            return null;
        }
        ParamUtil.setCurrentWarehouseId(doHeader.getWarehouseId());
        TempCarton tempCarton = tempCartonDAO.getByDoId(doHeader.getId());
        if (tempCarton == null) {
            tempCarton = new TempCarton();
            tempCarton.setDoHeaderId(doHeader.getId());
            tempCarton.setCarrierId(doHeader.getCarrierId());
            tempCarton.setWarehouseId(doHeader.getWarehouseId());
            tempCarton.setCreatedAt(DateUtil.getNowTime());
        }
        //清空错误数据
        tempCarton.setErrorMsg(null);
        if (tempCarton.getCallCount() == null) {
            tempCarton.setCallCount(1);
        } else {
            tempCarton.setCallCount(tempCarton.getCallCount()+1);
        }

        if (StringUtils.isEmpty(tempCarton.getCartonNo())) {
            CartonHeader cartonHeader = new CartonHeader();
            try {
                doHeader.setWholesaleWaybillFlag(YesNo.YES.getValue());
                cartonNoGenerateDispatcher.genNewCarton(doHeader, cartonHeader,false);
                tempCarton.setCartonNo(cartonHeader.getCartonNo());
                tempCarton.setWayBill(cartonHeader.getWayBill());
                tempCarton.setTrackingNo(cartonHeader.getTrackingNo());
                tempCarton.setPrintData(cartonHeader.getPrintData());
                tempCarton.setSuccessFlag(YesNo.YES.getValue());
                tempCarton.setIsNeedSync(YesNo.YES.getValue());
                //记录预下单日志
                orderLogService.saveLog(doHeader,
                        OrderLogConstants.OrderLogType.WAYBILL_GET.getValue(),
                        ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_WAYBILL_GET,null,hideWaybillInfo(tempCarton.getWayBill())));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                String errorMsg = e.getMessage();
                if (e instanceof BusinessException) {
                    BusinessException be = (BusinessException)e;
                    if (be.getParams() != null) {
                        for  (Object param : be.getParams()){
                            errorMsg += param.toString();
                        }
                    }
                }

                tempCarton.setSuccessFlag(YesNo.NO.getValue());
                if (StringUtil.isNotEmpty(errorMsg) && errorMsg.length() > 1000) {
                    errorMsg = errorMsg.substring(0,1000);
                }
                tempCarton.setErrorMsg(errorMsg);
                return tempCarton;
            }
        }

        if (needSyncToPlatform(tempCarton,doHeader)){
            try {
                loadService.sync(tempCarton.getWayBill(),doHeader);
                tempCarton.setSuccessFlag(YesNo.YES.getValue());
                tempCarton.setIsNeedSync(YesNo.NO.getValue());
            } catch (Exception e) {
                tempCarton.setSuccessFlag(YesNo.NO.getValue());
                tempCarton.setIsNeedSync(YesNo.YES.getValue());
                String errorMsg = e.getMessage();
                if (StringUtil.isNotEmpty(errorMsg) && errorMsg.length() > 1000) {
                    errorMsg = errorMsg.substring(0,1000);
                }
                tempCarton.setErrorMsg(errorMsg);
            }
        } else {
            tempCarton.setSuccessFlag(YesNo.YES.getValue());
            tempCarton.setIsNeedSync(YesNo.NO.getValue());
        }

        return tempCarton;
    }

    /**
     * 预下单日志，隐藏运单号
     * @param waybill
     * @return
     */
    private String hideWaybillInfo(String waybill) {
        if (StringUtil.isEmpty(waybill) || waybill.length() < 4) {
            return waybill;
        }
        StringBuffer stringBuffer = new StringBuffer(waybill.substring(0,2));
        stringBuffer.append("****")
                .append(waybill.substring(waybill.length()-2));
        return stringBuffer.toString();
    }

    /**
     * 是否需要同步到平台
     * @param tempCarton
     * @param doHeader
     * @return
     */
    private boolean needSyncToPlatform(TempCarton tempCarton, DeliveryOrderHeader doHeader) {

        if (StringUtils.isEmpty(tempCarton.getWayBill()) ||
                !YesNo.YES.getValue().equals(tempCarton.getIsNeedSync()) ||
                !doHeader.getDoType().equals(Constants.DoType.SELL.getValue())){
                //只对销售订单进行回平台操作
            return false;
        }
        if(YesNo.YES.getValue().equals(doHeader.getIsTempCarton())){
            return true;
        }
        //团购订单
        if (Config.isFmJson(Keys.Delivery.before_sync_order_cfg, Config.ConfigLevel.WAREHOUSE, BeforeSyncOrderCfg.groupIsBefore) &&
                doHeader.getDoWaveEx() != null && Constants.AutoWaveType.BATCH_GROUP.getValue().equals(doHeader.getDoWaveEx().getAutoWaveType())) {
            return true;
        }
        //非团购订单，按店铺配置是否需要回单
        String shopIds = Config.getFmJson(Keys.Delivery.before_sync_order_cfg, Config.ConfigLevel.WAREHOUSE, BeforeSyncOrderCfg.beforeSyncShopIds);
        if (StringUtil.isNotEmpty(shopIds) && doHeader.getShopId() != null && Arrays.asList(shopIds.split(",")).contains(doHeader.getShopId().toString())) {
            return true;
        }
        if ("ALL".equals(shopIds)){
            return true;
        }
        return false;
    }

    @Override
    public TempCarton get(Long id) {
        return tempCartonDAO.get(id);
    }

    @Override
    public boolean existEmptyCartonNo(Long waveId) {
        return tempCartonDAO.existEmptyCartonNo(waveId);
    }

    @Override
    @Transactional
    public void removeByDoId(Long doId) {
        tempCartonDAO.removeByDoId(doId);
    }

    @Override
    @Transactional
    public void removeByDoId(Long doId, Long carrierId) {
        tempCartonDAO.removeByDoId(doId,carrierId);
    }

    @Override
    @Transactional
    public void removeByWaveId(Long waveId) {
        tempCartonDAO.removeByWaveId(waveId);
    }

    @Override
    public List<Long> findCartonIdsForPrint(Long doHeaderId) {
        return tempCartonDAO.findCartonIdsForPrint(doHeaderId);
    }

    @Override
    @Transactional
    public void updatePrintFlagByDoIdList(List<Long> doIdList) {
        tempCartonDAO.updatePrintFlagByDoIdList(doIdList);
    }

    @Override
    public boolean existRepeatCartonNo(Long waveId) {
        return tempCartonDAO.existRepeatCartonNo(waveId);
    }

    @Override
    public TempCarton getByCartonNo(String cartonNo) {
        return tempCartonDAO.getByCartonNo(cartonNo);
    }

    @Override
    @Transactional
    public void savePrintLog(List<Long> doIdList,String updateBy) {
        tempCartonDAO.savePrintLog(doIdList,updateBy);
    }

    @Override
    public boolean isExists(Long doId) {
        return tempCartonDAO.isExists("doHeaderId",doId,null);
    }

    @Override
    @Transactional
    public void refreshFailOrder() {
        tempCartonDAO.refreshFailOrder();
    }
}