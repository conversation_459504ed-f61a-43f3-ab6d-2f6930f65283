package com.daxia.wms.delivery.task.replenish.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

@lombok.extern.slf4j.Slf4j
public class ReplMoveTaskFilter extends WhBaseQueryFilter {

	private static final long serialVersionUID = 6825658944942630914L;
	
	private Long replTaskId;
	
	@Operation(fieldName = "o.docLineId", operationType = OperationType.EQUAL)
	public Long getReplTaskId() {
		return replTaskId;
	}
	
	public void setReplTaskId(Long replTaskId) {
		this.replTaskId = replTaskId;
	}
}
