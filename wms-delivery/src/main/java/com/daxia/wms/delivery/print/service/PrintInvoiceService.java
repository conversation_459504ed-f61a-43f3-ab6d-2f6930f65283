package com.daxia.wms.delivery.print.service;

import java.util.List;

import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.master.entity.Printer;
import com.daxia.wms.print.PrintConstants.PrintInvoicePos;
import com.daxia.wms.print.dto.PrintData;

public interface PrintInvoiceService {

    /**
     * 指定打印机绑定发票
     * @param printerCode
     * @param invoiceCode
     * @param firstInvoiceNo
     * @param lastInvoiceNo
     */
    public void bindNewInvoice(String printerCode, String invoiceCode, String firstInvoiceNo, String lastInvoiceNo);
    
    /**
     * 校验打印机
     * @param printerCode
     * @return 指定的打印机
     */    
    public Printer validatePrinter(String printerCode);

    /**
     * 更新当前发票号码
     * @param printerCode
     * @param curInvoiceNo
     */
    public void updateCurInvoiceNo(String printerCode, String curInvoiceNo) ;
    
    /**
     * 打印发票（波次号回车打印发票调用方法）
     * @param waveNo
     * @param printerCode
     * @param isDirectly 是否直接打印
     * @return
     */
    public PrintData printInvoice(String waveNo, String printerCode, Boolean isDirectly);
    
    /**
     * 实际打印方法
     * @param idList
     * @param printerCode 打印机编号
     * @return
     */
    public List<String> print(List<Long> idList, String printerCode, String waveNo, PrintInvoicePos pos);
    
    /**
     * 设置发票的打印状态
     * @param ids
     */
    public void setInvoicePrinted(List<Long> ids);
    
    /**
     * 当前发票号码增加指定数值（超过上限则置为-1）
     * @param printer
     * @param addNum
     */
    public void increaseCurrentInvoiceNo(Printer printer,  long addNum) ;
    
    /**
     * 获取发票打印版本
     * @return
     */
    public Integer getPrintInvoiceVersion();
    
    /**
     * 判断订单是否还需要打印发票
     * @param doHeader
     * @return
     */
    public Boolean isDoNeedPrintInvoice(DeliveryOrderHeader doHeader);
}
