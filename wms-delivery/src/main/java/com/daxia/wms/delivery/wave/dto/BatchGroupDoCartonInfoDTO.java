/**
 * 
 */
package com.daxia.wms.delivery.wave.dto;

/**
 * 团购波次对应的发货单和箱信息dto
 */
@lombok.extern.slf4j.Slf4j
public class  BatchGroupDoCartonInfoDTO {

	private Long cartonId;

	private Long doId;
	
	private String cartonNo;
	
	private String doNo;
	
	/**
	 * 箱对应箱标签是否打印
	 */
	private Integer isPrinted;
	
	/**
	 * do对应冻结释放状态
	 */
	private String doReleaseStatus;
	
	private String doStatus;
	
	private String planShipTime;
	
	/**
	 * do对应总箱数
	 */
	private Long totalCartons;
	
	/**
	 * do对应所有已打印箱数
	 */
	private Long totalPrintedCartons;
	
	/**
	 * do对应所有箱标签的打印状态
	 */
	private Integer cartonPrintStatus;
	
	/**
	 * do对应配送商
	 */
	private String doCarrierName;

	public Long getCartonId() {
		return cartonId;
	}

	public void setCartonId(Long cartonId) {
		this.cartonId = cartonId;
	}

	public Long getDoId() {
		return doId;
	}

	public void setDoId(Long doId) {
		this.doId = doId;
	}

	public String getCartonNo() {
		return cartonNo;
	}

	public void setCartonNo(String cartonNo) {
		this.cartonNo = cartonNo;
	}

	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}

	public Integer getIsPrinted() {
		return isPrinted;
	}

	public void setIsPrinted(Integer isPrinted) {
		this.isPrinted = isPrinted;
	}

	public String getDoReleaseStatus() {
		return doReleaseStatus;
	}

	public void setDoReleaseStatus(String doReleaseStatus) {
		this.doReleaseStatus = doReleaseStatus;
	}

	public String getDoStatus() {
		return doStatus;
	}

	public void setDoStatus(String doStatus) {
		this.doStatus = doStatus;
	}

	public String getPlanShipTime() {
		return planShipTime;
	}

	public void setPlanShipTime(String planShipTime) {
		this.planShipTime = planShipTime;
	}

	public Long getTotalCartons() {
		return totalCartons;
	}

	public void setTotalCartons(Long totalCartons) {
		this.totalCartons = totalCartons;
	}

	public Integer getCartonPrintStatus() {
		return cartonPrintStatus;
	}

	public void setCartonPrintStatus(Integer cartonPrintStatus) {
		this.cartonPrintStatus = cartonPrintStatus;
	}

	public String getDoCarrierName() {
		return doCarrierName;
	}

	public void setDoCarrierName(String doCarrierName) {
		this.doCarrierName = doCarrierName;
	}

	public Long getTotalPrintedCartons() {
		return totalPrintedCartons;
	}

	public void setTotalPrintedCartons(Long totalPrintedCartons) {
		this.totalPrintedCartons = totalPrintedCartons;
	}

}
