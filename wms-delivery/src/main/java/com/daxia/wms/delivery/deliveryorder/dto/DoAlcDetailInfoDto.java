
package com.daxia.wms.delivery.deliveryorder.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 发货单头DTO
 */
@lombok.extern.slf4j.Slf4j
public class DoAlcDetailInfoDto implements Serializable {

	private static final long serialVersionUID = 8316182654323875023L;

	private Long id;
	/** DO单号 */
	private String doNo;
	/**
	 * 发货单状态
	 */
	private String status;
	/**
	 * 冻结状态 HOLD：冻结 RELEASE：释放
	 */
	private String releaseStatus;
	/**
	 * 订货数量 EXPECTED_QTY_EACH
	 */
	private BigDecimal expectedQty;
	/** 发货数量 */
	private BigDecimal alcQty;
	/** DO创建时间 */
	private Date doCreateTime;

	private String productCode;

	private String productCname;
	private String lotatt05;

	private String lotatt01;

	private String lotatt02;

	private String lotatt09;

	private String specification;

	private String locCode;

	public DoAlcDetailInfoDto() {
	}

	public DoAlcDetailInfoDto(Long id, String doNo, String status, String releaseStatus, BigDecimal expectedQty,
							  BigDecimal alcQty, Date doCreateTime, String productCode, String productCname, String lotatt05,
							  String lotatt01, String lotatt02, String lotatt09,String specification,String locCode) {
		this.id = id;
		this.doNo = doNo;
		this.status = status;
		this.releaseStatus = releaseStatus;
		this.expectedQty = expectedQty;
		this.alcQty = alcQty;
		this.doCreateTime = doCreateTime;
		this.productCode = productCode;
		this.productCname = productCname;
		this.lotatt05 = lotatt05;
		this.lotatt01 = lotatt01;
		this.lotatt02 = lotatt02;
		this.lotatt09 = lotatt09;
		this.specification =  specification;
		this.locCode = locCode;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getReleaseStatus() {
		return releaseStatus;
	}

	public void setReleaseStatus(String releaseStatus) {
		this.releaseStatus = releaseStatus;
	}

	public BigDecimal getExpectedQty() {
		return expectedQty;
	}

	public void setExpectedQty(BigDecimal expectedQty) {
		this.expectedQty = expectedQty;
	}

	public BigDecimal getAlcQty() {
		return alcQty;
	}

	public void setAlcQty(BigDecimal alcQty) {
		this.alcQty = alcQty;
	}

	public Date getDoCreateTime() {
		return doCreateTime;
	}

	public void setDoCreateTime(Date doCreateTime) {
		this.doCreateTime = doCreateTime;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getProductCname() {
		return productCname;
	}

	public void setProductCname(String productCname) {
		this.productCname = productCname;
	}

	public String getLotatt05() {
		return lotatt05;
	}

	public void setLotatt05(String lotatt05) {
		this.lotatt05 = lotatt05;
	}

	public String getLotatt01() {
		return lotatt01;
	}

	public void setLotatt01(String lotatt01) {
		this.lotatt01 = lotatt01;
	}

	public String getLotatt02() {
		return lotatt02;
	}

	public void setLotatt02(String lotatt02) {
		this.lotatt02 = lotatt02;
	}

	public String getLotatt09() {
		return lotatt09;
	}

	public void setLotatt09(String lotatt09) {
		this.lotatt09 = lotatt09;
	}

	public String getSpecification() {
		return specification;
	}

	public void setSpecification(String specification) {
		this.specification = specification;
	}

	public String getLocCode() {
		return locCode;
	}

	public void setLocCode(String locCode) {
		this.locCode = locCode;
	}
}
