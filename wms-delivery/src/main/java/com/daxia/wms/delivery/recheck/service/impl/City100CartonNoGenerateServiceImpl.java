package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.wms.delivery.util.DoUtil;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.util.BusinessException;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants.SequenceName;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.dao.CartonHeaderDAO;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;
import com.daxia.wms.waybill.city100.dto.WayBillInfo;
import com.daxia.wms.waybill.city100.service.City100WaybillService;

/**
 * Created by szy on 2016/8/9.
 */
@Name("city100CartonNoGenerateService")
@lombok.extern.slf4j.Slf4j
public class City100CartonNoGenerateServiceImpl implements CartonNoGenerateService {

    @In
    private SequenceGeneratorService sequenceGeneratorService;

    @In(create = true)
    private City100WaybillService city100WaybillService;

    @In
    private CartonHeaderDAO cartonHeaderDAO;

    @Override
    public void generatorCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        String orderNo = doHeader.getDoNo();
        if (StringUtil.isEmpty(orderNo)){
            throw new DeliveryException(DeliveryException.DO_NO_IS_NULL);
        }

        String cartonNo = null;
        if (SystemConfig.configIsOpen("delivery.carton.genNoFmDb", ParamUtil.getCurrentWarehouseId())) {
            cartonNo = orderNo + cartonHeaderDAO.getCartonSeqByDo(doHeader.getId());
        } else {
            cartonNo = orderNo + sequenceGeneratorService.generateSequenceNo(SequenceName.CARTONNO.getValue(), ParamUtil.getCurrentWarehouseId());
        }
        cartonHeader.setCartonNo(cartonNo);
        cartonHeader.setWayBill(cartonNo);

        WayBillInfo wayBillInfo = new WayBillInfo();
        wayBillInfo.setCartonId(cartonHeader.getId());
        wayBillInfo.setCartonNo(cartonHeader.getCartonNo());
        wayBillInfo.setReceivable(doHeader.getReceivable());
        wayBillInfo.setTelephone(DoUtil.decryptPhone(doHeader.getTelephone()));
        wayBillInfo.setMobile(DoUtil.decryptPhone(doHeader.getMobile()));
        wayBillInfo.setAddress(doHeader.getAddress());
        wayBillInfo.setConsigneeName(doHeader.getConsigneeName());
        wayBillInfo.setCarrierId(doHeader.getCarrierId());
        wayBillInfo.setWarehouseId(doHeader.getWarehouseId());

        try {
            city100WaybillService.sendWayBill(wayBillInfo);
        } catch (BusinessException e) {
            throw new DeliveryException("发送运单号至城市100出错!",e);
        }

    }
}
