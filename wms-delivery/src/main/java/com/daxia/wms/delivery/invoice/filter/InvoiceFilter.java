package com.daxia.wms.delivery.invoice.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;

import java.util.Date;
import java.util.List;

/**
 * 发票信息查询过滤器
 */
@lombok.extern.slf4j.Slf4j
public class InvoiceFilter extends WhBaseQueryFilter {

    private static final long serialVersionUID = -4939343310051529481L;
    private String waveNo;
    private String doNo;
    private String soNo;
    private Date createTimeFrom;
    private Date createTimeTo;
    private String sortGridNo;
    private String invoiceCode;      // 发票代码
    private Integer invoiceNoFrom;   // 起始发票号码
    private Integer invoiceNoTo;     // 截止发票号码
    private String invoiceNoStatus;  // 发票号状态
    private String invoiceBookStatus;// 发票薄状态
    private Integer sortBy;
    private Integer invoicePrintFlag;

    private String invoiceType;
    //根据这个invoiceId取对应订单下所有的invoiceHeader;
    private Long refInvoiceId;

    private List<Long> doIds;
    private List<Long> ids;

    private InvoiceHeader.InvoiceStatus invoiceStatus;

    /**
     * 是否只查询包含搬仓sku的订单
     */
    private Boolean transSkuFlag;

    private Integer autoWaveType;

    private Boolean singlePromote;

    private Integer autoWaveTypeNotEqual;

    private Integer isWave;


    public Integer getSortBy() {
        return sortBy;
    }

    @Operation(fieldName = "sortBy", operationType = OperationType.EQUAL)
    public void setSortBy(Integer sortBy) {
        this.sortBy = sortBy;
    }

    @Operation(fieldName = "deliveryOrderHeader.waveHeader.waveNo", operationType = OperationType.EQUAL)
    public String getWaveNo() {
        return waveNo;
    }

    @Operation(fieldName = "o.deliveryOrderHeader.sortGridNo", operationType = OperationType.EQUAL)
    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    @Operation(fieldName = "deliveryOrderHeader.doNo", operationType = OperationType.EQUAL)
    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    @Operation(fieldName = "soCode", operationType = OperationType.EQUAL)
    public String getSoNo() {
        return soNo;
    }

    public void setSoNo(String soNo) {
        this.soNo = soNo;
    }

    @Operation(fieldName = "createdAt", operationType = OperationType.NOT_LESS_THAN)
    public Date getCreateTimeFrom() {
        return createTimeFrom;
    }

    public void setCreateTimeFrom(Date createTimeFrom) {
        this.createTimeFrom = createTimeFrom;
    }

    @Operation(fieldName = "createdAt", operationType = OperationType.NOT_GREAT_THAN)
    public Date getCreateTimeTo() {
        return createTimeTo;
    }

    public void setCreateTimeTo(Date createTimeTo) {
        this.createTimeTo = createTimeTo;
    }

    public void setInvoicePrintFlag(Integer invoicePrintFlag) {
        this.invoicePrintFlag = invoicePrintFlag;
    }

    @Operation(fieldName = "invoicePrintFlag", operationType = OperationType.EQUAL)
    public Integer getInvoicePrintFlag() {
        return invoicePrintFlag;
    }

    @Operation(fieldName = "invoiceCode", operationType = OperationType.EQUAL)
    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    @Operation(fieldName = "invoiceNoFrom", operationType = OperationType.EQUAL)
    public Integer getInvoiceNoFrom() {
        return invoiceNoFrom;
    }

    public void setInvoiceNoFrom(Integer invoiceNoFrom) {
        this.invoiceNoFrom = invoiceNoFrom;
    }

    @Operation(fieldName = "invoiceNoTo", operationType = OperationType.EQUAL)
    public Integer getInvoiceNoTo() {
        return invoiceNoTo;
    }

    public void setInvoiceNoTo(Integer invoiceNoTo) {
        this.invoiceNoTo = invoiceNoTo;
    }

    @Operation(fieldName = "status", operationType = OperationType.EQUAL)
    public String getInvoiceNoStatus() {
        return invoiceNoStatus;
    }

    public void setInvoiceNoStatus(String invoiceNoStatus) {
        this.invoiceNoStatus = invoiceNoStatus;
    }

    @Operation(fieldName = "status", operationType = OperationType.EQUAL)
    public String getInvoiceBookStatus() {
        return invoiceBookStatus;
    }

    public void setInvoiceBookStatus(String invoiceBookStatus) {
        this.invoiceBookStatus = invoiceBookStatus;
    }

    @Operation(fieldName = "doHeaderId", operationType = OperationType.IN)
    public List<Long> getDoIds() {
        return doIds;
    }

    public void setDoIds(List<Long> doIds) {
        this.doIds = doIds;
    }

    @Operation(clause = " o.doHeaderId in (SELECT doHeaderId from InvoiceHeader i where i.id = ?) ", operationType = OperationType.CLAUSE)
    public Long getRefInvoiceId() {
        return refInvoiceId;
    }

    public void setRefInvoiceId(Long refInvoiceId) {
        this.refInvoiceId = refInvoiceId;
    }

    @Operation(fieldName = "id", operationType = OperationType.IN)
    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    @Operation(operationType = OperationType.IGNORE)
    public Boolean getTransSkuFlag() {
        return transSkuFlag;
    }

    public void setTransSkuFlag(Boolean transSkuFlag) {
        this.transSkuFlag = transSkuFlag;
    }

    public InvoiceHeader.InvoiceStatus getInvoiceStatus() {
        return invoiceStatus;
    }

    public void setInvoiceStatus(InvoiceHeader.InvoiceStatus invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    @Operation(fieldName = "o.deliveryOrderHeader.waveFlag", operationType = OperationType.EQUAL)
    public Integer getIsWave() {
        return isWave;
    }

    public void setIsWave(Integer isWave) {
        this.isWave = isWave;
    }
}