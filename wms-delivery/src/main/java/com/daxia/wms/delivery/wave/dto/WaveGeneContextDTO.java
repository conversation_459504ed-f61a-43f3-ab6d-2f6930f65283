package com.daxia.wms.delivery.wave.dto;

import com.daxia.wms.Constants.AutoWaveType;

import java.io.Serializable;
import java.util.List;

/**
 * 生成波次基础数据DTO，包括波次规则，波次下最大发货单数，发货单Id List
 */
@lombok.extern.slf4j.Slf4j
public class WaveGeneContextDTO implements Serializable{

	private static final long serialVersionUID = -4836713643037694771L;

	private List<Long> doIdList;//发货单Id List
	
	private Long maxDOQty;     //波次下最大发货单数
	
	private Integer wavePriority;     //波次优先级
	
	private Boolean isRecommend;
	
	private Boolean isSemiAuto = false; 
	
	//自动波次类型
	private AutoWaveType autoWaveType = AutoWaveType.NORMAL;
	
	private Long ruleDetailId;
	
	private Long criteriaDetailId;

	/**
	 * 指定货品等级
	 */
	private Boolean pointGrade=Boolean.FALSE;
	public Boolean getPointGrade() {
		return pointGrade;
	}

	public void setPointGrade(Boolean pointGrade) {
		this.pointGrade = pointGrade;
	}

	public List<Long> getDoIdList() {
		return doIdList;
	}
	
	public void setDoIdList(List<Long> doIdList) {
		this.doIdList = doIdList;
	}
	
	public Long getMaxDOQty() {
		return maxDOQty;
	}
	
	public void setMaxDOQty(Long maxDOQty) {
		this.maxDOQty = maxDOQty;
	}

    public Integer getWavePriority() {
        return wavePriority;
    }
    
    public void setWavePriority(Integer wavePriority) {
        this.wavePriority = wavePriority;
    }

    public Boolean getIsRecommend() {
        return isRecommend;
    }

    public void setIsRecommend(Boolean isRecommend) {
        this.isRecommend = isRecommend;
    }

    /**
     * @return the isSemiAuto
     */
    public Boolean getIsSemiAuto() {
        return isSemiAuto;
    }

    /**
     * @param isSemiAuto the isSemiAuto to set
     */
    public void setIsSemiAuto(Boolean isSemiAuto) {
        this.isSemiAuto = isSemiAuto;
    }

    public AutoWaveType getAutoWaveType() {
        return autoWaveType;
    }

    public void setAutoWaveType(AutoWaveType autoWaveType) {
        this.autoWaveType = autoWaveType;
    }

	public Long getRuleDetailId() {
		return ruleDetailId;
	}

	public void setRuleDetailId(Long ruleDetailId) {
		this.ruleDetailId = ruleDetailId;
	}

	public Long getCriteriaDetailId() {
		return criteriaDetailId;
	}

	public void setCriteriaDetailId(Long criteriaDetailId) {
		this.criteriaDetailId = criteriaDetailId;
	}
}
