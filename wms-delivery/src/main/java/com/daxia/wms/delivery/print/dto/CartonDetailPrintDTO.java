package com.daxia.wms.delivery.print.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 箱标签明细打印数据dto
 */
@lombok.extern.slf4j.Slf4j
public class CartonDetailPrintDTO implements Serializable {

    private static final long serialVersionUID = -5891607901052129521L;

    private String productCode;
    
    private String ean13;

    private String productName;

    private BigDecimal number;
    
    public String getEan13() {
        return ean13;
    }
    
    public void setEan13(String ean13) {
        this.ean13 = ean13;
    }
    
    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getNumber() {
        return number;
    }

    public void setNumber(BigDecimal number) {
        this.number = number;
    }
}