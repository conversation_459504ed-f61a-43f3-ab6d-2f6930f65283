package com.daxia.wms.delivery.task.repick.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;

/**
 * 返拣单头信息实体
 */
@Entity
@Table(name ="doc_rp_header")
@Where(clause = "IS_DELETED = 0 ")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@SQLDelete(sql = "update doc_rp_header set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class ReversePickHeader extends WhBaseEntity {

	private static final long serialVersionUID = 5866729753503036061L;
	/**
	 * 主键
	 */
	private Long id;
	/**
	 * 状态 （00：初始化；40：已发布；99：完成；90：取消）
	 */
	private String status;
	/**
	 * 返拣单号
	 */
	private String rpNo;
	/**
	 * 发货单号
	 */
	private String doNo;
	/**
	 * 返拣原因代码
	 */
	private String reasonCode;
	/**
	 * 整/散
	 */
	private Integer pktType;

	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)  
	@Column(name="ID")
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	@Column(name ="STATUS")
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	@Column(name="RP_NO")
	public String getRpNo() {
		return rpNo;
	}
	public void setRpNo(String rpNo) {
		this.rpNo = rpNo;
	}

    @Column(name="DO_NO")
    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    @Column(name="REASON_CODE")
    public String getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    @Column(name="PKT_TYPE")
    public Integer getPktType() {
        return pktType;
    }

    public void setPktType(Integer pktType) {
        this.pktType = pktType;
    }
}