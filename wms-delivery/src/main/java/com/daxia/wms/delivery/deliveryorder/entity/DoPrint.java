package com.daxia.wms.delivery.deliveryorder.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;

/**
 * 发货单头扩展实体
 */
@Entity
@Table(name = "doc_do_print")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_do_print set is_deleted = 1 where id = ? and version = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class DoPrint extends WhBaseEntity {

	private static final long serialVersionUID = -8493475807139511070L;

	private Long id;
	
	/**
	 * 发货单头ID
	 */
	private Long doHeaderId;

	/**
	 * 类型
	 */
	private String type;

	/**
	 * 内容
	 */
	private String content;

	@Id
    @Column(name = "ID")
	@GeneratedValue(strategy = GenerationType.AUTO)  
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "DO_HEADER_ID")
	public Long getDoHeaderId() {
		return doHeaderId;
	}

	public void setDoHeaderId(Long doHeaderId) {
		this.doHeaderId = doHeaderId;
	}

	@Column(name = "TYPE")
	public String getType()
	{
		return type;
	}

	public void setType(String type)
	{
		this.type = type;
	}

	@Column(name = "CONTENT")
	public String getContent()
	{
		return content;
	}

	public void setContent(String content)
	{
		this.content = content;
	}
}