package com.daxia.wms.delivery.invoice.dto;

import java.math.BigDecimal;

@lombok.extern.slf4j.Slf4j
public class PrintInvoiceDetailDTO {
    //商品名
    private String skuDescr;

    //数量
    private BigDecimal qty;

    // 单价
    private BigDecimal price;

    //总金额
    private BigDecimal amount;

    //单位
    private String uom;

    //规格
    private String specification;

    //产地

    private String manufacturer;

    public String getSkuDescr() {
        return skuDescr;
    }

    public void setSkuDescr(String skuDescr) {
        this.skuDescr = skuDescr;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }
}
