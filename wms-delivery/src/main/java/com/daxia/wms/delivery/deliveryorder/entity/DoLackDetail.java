package com.daxia.wms.delivery.deliveryorder.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.entity.Sku;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "do_lack_detail")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update do_lack_detail set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class DoLackDetail extends WhBaseEntity {
	private static final long serialVersionUID = 4334481426691035691L;

	private Long id;
	
	private Long doHeaderId;
	
	private Long doDetailId;
	
	private Long skuId;
	
	private Long locId;
	
	private String locCodes;
	
	private Long lotId;
	
	private String lpnNo;
	
	private BigDecimal qty;
	
	private String holdReason;
	
	private String notes;
	
	private Integer isDeleted;
	
    private DeliveryOrderHeader doHeader;
    
    private Sku sku;
    
    private Location location;

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	
	@Column(name = "DO_HEADER_ID")
	public Long getDoHeaderId() {
		return doHeaderId;
	}

	public void setDoHeaderId(Long doHeaderId) {
		this.doHeaderId = doHeaderId;
	}

	@Column(name = "DO_DETAIL_ID")
	public Long getDoDetailId() {
		return doDetailId;
	}

	public void setDoDetailId(Long doDetailId) {
		this.doDetailId = doDetailId;
	}

	@Column(name = "SKU_ID")
	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	@Column(name = "LOC_ID")
	public Long getLocId() {
		return locId;
	}

	public void setLocId(Long locId) {
		this.locId = locId;
	}
	
	@Column(name = "LOC_CODES")
    public String getLocCodes() {
        return locCodes;
    }
    
    public void setLocCodes(String locCodes) {
        this.locCodes = locCodes;
    }

    @Column(name = "LOT_ID")
	public Long getLotId() {
		return lotId;
	}

	public void setLotId(Long lotId) {
		this.lotId = lotId;
	}

	@Column(name = "LPN_NO")
	public String getLpnNo() {
		return lpnNo;
	}

	public void setLpnNo(String lpnNo) {
		this.lpnNo = lpnNo;
	}

	@Column(name = "QTY")
	public BigDecimal getQty() {
		return qty;
	}

	public void setQty(BigDecimal qty) {
		this.qty = qty;
	}

	@Column(name = "HOLD_REASON")
	public String getHoldReason() {
		return holdReason;
	}

	public void setHoldReason(String holdReason) {
		this.holdReason = holdReason;
	}

	@Column(name = "NOTES")
	public String getNotes() {
		return notes;
	}

	public void setNotes(String notes) {
		this.notes = notes;
	}

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DO_HEADER_ID", insertable=false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public DeliveryOrderHeader getDoHeader() {
        return doHeader;
    }

    public void setDoHeader(DeliveryOrderHeader doHeader) {
        this.doHeader = doHeader;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SKU_ID", insertable=false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public Sku getSku() {
        return sku;
    }

    public void setSku(Sku sku) {
        this.sku = sku;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "LOC_ID",insertable=false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public Location getLocation() {
        return location;
    }

    public void setLocation(Location location) {
        this.location = location;
    }

    @Column(name = "IS_DELETED")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}