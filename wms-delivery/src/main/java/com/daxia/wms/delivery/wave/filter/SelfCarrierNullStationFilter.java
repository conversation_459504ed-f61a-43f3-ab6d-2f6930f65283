package com.daxia.wms.delivery.wave.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;

import java.util.Date;
import java.util.List;

/**
 * 自配送无配送站点订单看板filter
 */
@lombok.extern.slf4j.Slf4j
public class SelfCarrierNullStationFilter extends WhBaseQueryFilter {

    /**
     * 
     */
    private static final long serialVersionUID = -436483851764619290L;
    /**
     * 发货单号
     */

    private String doNo;
    /**
      *发货单创建时间
     */
    public Date doCreateTimeFrom;
    public Date doCreateTimeTo;
    
    private String notNullStation;
    
    /**
     * 发货单类型 DO、调拨、RTV
     */
    private String doType;
    /**
     * 配送商
     */
    private Long carrierId;
    /**
     * 限时达类型  0 - 普通  1 - 半日达   2 - 一日三送
     */
    private Integer halfDayDeliveryFlag;
   
    /**
     * 冻结状态 0：冻结 1：释放
     */
    private String releaseStatus;
    /**
     * do状态
     */
    private String status;
    /**
     * 是否已跑波次
     */
    private Integer waveFlag;
    
    
    /**
     * 配送站点
     */
    private Long stationId;

	//发票需要打印，且发票状态没有绑定
	private List<InvoiceHeader.InvoiceStatus> statusList;
    
    @Operation(clause = "(o.stationId is null and o.carrier.type = 1)", operationType = OperationType.CLAUSE)
    public String getNotNullStation() {
		return notNullStation;
	}
	public void setNotNullStation(String notNullStation) {
		this.notNullStation = notNullStation;
	}
	
	public Long getStationId() {
		return stationId;
	}
	public void setStationId(Long stationId) {
		this.stationId = stationId;
	}
	
	public String getDoNo() {
		return doNo;
	}
	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}
	
    @Operation(fieldName = "o.doCreateTime", operationType = OperationType.NOT_LESS_THAN  , dataType = "datetime")
	public Date getDoCreateTimeFrom() {
		return doCreateTimeFrom;
	}
	public void setDoCreateTimeFrom(Date doCreateTimeFrom) {
		this.doCreateTimeFrom = doCreateTimeFrom;
	}
	
	@Operation(fieldName = "o.doCreateTime", operationType = OperationType.NOT_GREAT_THAN  , dataType = "datetime")
	public Date getDoCreateTimeTo() {
		return doCreateTimeTo;
	}
	public void setDoCreateTimeTo(Date doCreateTimeTo) {
		this.doCreateTimeTo = doCreateTimeTo;
	}
	public String getDoType() {
		return doType;
	}
	public void setDoType(String doType) {
		this.doType = doType;
	}
	public Long getCarrierId() {
		return carrierId;
	}
	public void setCarrierId(Long carrierId) {
		this.carrierId = carrierId;
	}
	@Operation(fieldName = "o.isHalfDayDelivery" , operationType = OperationType.EQUAL)
	public Integer getHalfDayDeliveryFlag() {
		return halfDayDeliveryFlag;
	}
	public void setHalfDayDeliveryFlag(Integer halfDayDeliveryFlag) {
		this.halfDayDeliveryFlag = halfDayDeliveryFlag;
	}

	public String getReleaseStatus() {
		return releaseStatus;
	}
	public void setReleaseStatus(String releaseStatus) {
		this.releaseStatus = releaseStatus;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public Integer getWaveFlag() {
		return waveFlag;
	}
	public void setWaveFlag(Integer waveFlag) {
		this.waveFlag = waveFlag;
	}

	@Operation(fieldName = "o.invoiceStatus", operationType = OperationType.IN )
	public List<InvoiceHeader.InvoiceStatus> getStatusList() {
		return statusList;
	}

	public void setStatusList(List<InvoiceHeader.InvoiceStatus> statusList) {
		this.statusList = statusList;
	}
}