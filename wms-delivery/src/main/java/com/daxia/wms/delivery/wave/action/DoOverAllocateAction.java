package com.daxia.wms.delivery.wave.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateHeader;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateService;
import com.daxia.wms.delivery.wave.dto.OverAllocateDto;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.tuple.Pair;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.math.BigDecimal;
import java.util.List;

@Name("com.daxia.wms.delivery.doOverAllocateAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class DoOverAllocateAction extends PagedListBean<OverAllocateDto> {
    
    private List<OverAllocateDto> dtoList = Lists.newArrayList();
    
    private Long stkId;
    
    private BigDecimal overQty;
    
    @In
    DoAllocateService doAllocateService;
    
    @Override
    public void query() {
        dtoList = doAllocateService.queryOverAllocate();
    }
    
    public void cancelAllocate() {
        if (overQty == null && overQty.compareTo(BigDecimal.ZERO) <= 0) {
            this.sayMessage(MESSAGE_FAILED);
        }
        
        List<Pair<Long, BigDecimal>> qtyPair = doAllocateService.getStkAllocateQty(stkId);
        
        List<Long> ids = Lists.newArrayList();
        for (Pair<Long, BigDecimal> p : qtyPair) {
            overQty = overQty.subtract(p.getValue());
            ids.add(p.getKey());
            if (overQty.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
        }
        
        List<DoAllocateHeader> headers = doAllocateService.getHeaderByIds(ids);
        for (DoAllocateHeader header : headers) {
            doAllocateService.cancelAssign(header, true, false);
        }
        
        this.query();
        
        this.sayMessage(MESSAGE_SUCCESS);
    }
    
    public List<OverAllocateDto> getDtoList() {
        return dtoList;
    }
    
    public void setDtoList(List<OverAllocateDto> dtoList) {
        this.dtoList = dtoList;
    }
    
    public Long getStkId() {
        return stkId;
    }
    
    public void setStkId(Long stkId) {
        this.stkId = stkId;
    }
    
    public BigDecimal getOverQty() {
        return overQty;
    }
    
    public void setOverQty(BigDecimal overQty) {
        this.overQty = overQty;
    }
}
