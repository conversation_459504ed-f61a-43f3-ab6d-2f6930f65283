package com.daxia.wms.delivery.task.replenish.action;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.task.replenish.entity.ReplMoveTask;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishTask;
import com.daxia.wms.delivery.task.replenish.filter.ReplMoveTaskFilter;
import com.daxia.wms.delivery.task.replenish.service.ReplMoveTaskService;
import com.daxia.wms.delivery.task.replenish.service.ReplTaskService;

/**
 * 补货移库任务管理
 *
 */
@Name("com.daxia.wms.delivery.replMoveTaskAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ReplMoveTaskAction extends PagedListBean<ReplMoveTask> {

	private static final long serialVersionUID = 1829527166955372941L;
    
    private Long taskId;
    
    private ReplenishTask replenishTask;

    @In
    private ReplMoveTaskService replMoveTaskService;
    
    @In
    private ReplTaskService replTaskService;
    
    private boolean init = false;
    
    public ReplMoveTaskAction() {
        super();
    }
    
    public void init () {
    	if (!init) {
    		init = true;
    		query();
    	}
    }
    
    /**
     * 查询补货任务
     */
    @Override
    public void query() {
    	if (replenishTask == null) {
    		replenishTask = replTaskService.getReplenishTaskById(taskId);
    	}
    	ReplMoveTaskFilter replMoveTaskFilter = new ReplMoveTaskFilter();
    	replMoveTaskFilter.setReplTaskId(taskId);
    	DataPage<ReplMoveTask> dataPage = replMoveTaskService.query(replMoveTaskFilter, getStartIndex(), 
    			getPageSize());
        this.populateValues(dataPage);
    }
    
    public void cancelTask() {
    	replTaskService.cancelReplMoveTask(taskId);
    }
    
    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }
    
    public ReplenishTask getReplenishTask() {
		return replenishTask;
	}
    
    public void setReplenishTask(ReplenishTask replenishTask) {
		this.replenishTask = replenishTask;
	}
}