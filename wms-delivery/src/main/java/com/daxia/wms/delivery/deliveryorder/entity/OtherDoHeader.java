package com.daxia.wms.delivery.deliveryorder.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import org.hibernate.annotations.*;
import org.hibernate.annotations.CascadeType;
import org.hibernate.annotations.OrderBy;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "doc_other_do_header")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = "IS_DELETED = 0 ")
@SQLDelete(sql = "UPDATE doc_other_do_header SET IS_DELETED = 1 WHERE ID = ? AND VERSION = ?")
@lombok.extern.slf4j.Slf4j
public class OtherDoHeader extends WhBaseEntity {

    private static final long serialVersionUID = -5761539807613027331L;
    /**
     * 主键
     */
    private Long id;

    /**
     * DO编号
     */
    private String doNo;
    /**
     * DO类型
     */
    private String doType;
    /**
     * DO状态
     */
    private String doStatus;
    /**
     * 发货时间
     */
    private Date shipTime;
    /**
     * 参考编号1
     */
    private String refNo1;
    /**
     * 参考编号2
     */
    private String refNo2;

    /**
     * 应发数
     */
    private BigDecimal exptQty;
    /**
     * 实发数
     */
    private BigDecimal shipQty;
    /**
     * 备注
     */
    private String notes;

    /**
     * 订单关闭时间(审核)
     */
    private Date closeTime;

    /**
     * 发货人
     */
    private String shipBy;

    /**
     * 审核人
     */
    private String verifyBy;

    private List<OtherDoDetail> otherDoDetails;

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "autoIdGenerator")
    @GenericGenerator(name = "autoIdGenerator", strategy = "com.daxia.framework.common.dao.AutoIdentityGenerator")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    @Column(name = "DO_NO")
    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    @Column(name = "DO_TYPE")
    public String getDoType() {
        return doType;
    }

    public void setDoType(String doType) {
        this.doType = doType;
    }

    @Column(name = "DO_STATUS")
    public String getDoStatus() {
        return doStatus;
    }

    public void setDoStatus(String doStatus) {
        this.doStatus = doStatus;
    }

    @Column(name = "SHIP_TIME")
    public Date getShipTime() {
        return shipTime;
    }

    public void setShipTime(Date shipTime) {
        this.shipTime = shipTime;
    }

    @Column(name = "REF_NO1")
    public String getRefNo1() {
        return refNo1;
    }

    public void setRefNo1(String refNo1) {
        this.refNo1 = refNo1;
    }

    @Column(name = "REF_NO2")
    public String getRefNo2() {
        return refNo2;
    }

    public void setRefNo2(String refNo2) {
        this.refNo2 = refNo2;
    }

    @Column(name = "EXPT_QTY")
    public BigDecimal getExptQty() {
        return exptQty;
    }

    public void setExptQty(BigDecimal exptQty) {
        this.exptQty = exptQty;
    }

    @Column(name = "SHIP_QTY")
    public BigDecimal getShipQty() {
        return shipQty;
    }

    public void setShipQty(BigDecimal shipQty) {
        this.shipQty = shipQty;
    }

    @Column(name = "NOTES")
    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }


    @OneToMany(fetch = FetchType.LAZY, mappedBy = "otherDoHeader")
    @Where(clause = " IS_DELETED = 0 ")
    @Cascade(value = {CascadeType.SAVE_UPDATE})
    @OrderBy(clause = " ID DESC ")
    public List<OtherDoDetail> getOtherDoDetails() {
        return otherDoDetails;
    }

    public void setOtherDoDetails(List<OtherDoDetail> otherDoDetails) {
        this.otherDoDetails = otherDoDetails;
    }

    @Column(name = "CLOSE_TIME")
    public Date getCloseTime() {
        return closeTime;
    }

    public void setCloseTime(Date closeTime) {
        this.closeTime = closeTime;
    }

    @Column(name = "SHIP_BY")
    public String getShipBy() {
        return shipBy;
    }

    public void setShipBy(String shipBy) {
        this.shipBy = shipBy;
    }

    @Column(name = "VERIFY_BY")
    public String getVerifyBy() {
        return verifyBy;
    }

    public void setVerifyBy(String verifyBy) {
        this.verifyBy = verifyBy;
    }
}
