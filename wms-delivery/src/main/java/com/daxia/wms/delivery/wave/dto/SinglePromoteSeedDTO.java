package com.daxia.wms.delivery.wave.dto;

import java.util.Date;

import com.google.common.base.Objects;

@lombok.extern.slf4j.Slf4j
public class SinglePromoteSeedDTO {

    private Long stationId;

    private Long carrierId;

    private Long pointSkuRuleId;

    private Boolean invoiceFlag;
    
    private Date planShipTime;
    
    //一个波次需要包含的最多Units数量
    private Long waveUnitQty;

    public Long getPointSkuRuleId() {
        return pointSkuRuleId;
    }

    public void setPointSkuRuleId(Long pointSkuRuleId) {
        this.pointSkuRuleId = pointSkuRuleId;
    }

    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public Long getStationId() {
        return stationId;
    }

    public void setStationId(Long stationId) {
        this.stationId = stationId;
    }

    public Boolean getInvoiceFlag() {
        return invoiceFlag;
    }

    public void setInvoiceFlag(Boolean invoiceFlag) {
        this.invoiceFlag = invoiceFlag;
    }
    
    @Override
    public String toString() {
        return Objects.toStringHelper(this).omitNullValues().add("stationId", stationId)
                .add("carrierId", carrierId).add("pointSkuRuleId", pointSkuRuleId)
                .add("invoiceFlag", invoiceFlag).toString();
    }

    public Date getPlanShipTime() {
        return planShipTime;
    }

    public void setPlanShipTime(Date planShipTime) {
        this.planShipTime = planShipTime;
    }

    public Long getWaveUnitQty() {
        return waveUnitQty;
    }

    public void setWaveUnitQty(Long waveUnitQty) {
        this.waveUnitQty = waveUnitQty;
    }

}