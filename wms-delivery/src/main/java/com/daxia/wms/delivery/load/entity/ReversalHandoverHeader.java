package com.daxia.wms.delivery.load.entity;


import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToMany;
import javax.persistence.Table;

import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;

/**
 * 逆向交接单
 */
@Entity
@Table(name = "doc_reversal_header")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = "IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_reversal_header set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class ReversalHandoverHeader extends WhBaseEntity {
	
	private static final long serialVersionUID = 5246518269003749908L;
	
    /**
     * 主键
     */
    private Long id;
    
    /**
     * 逆向交接单单号
     */
    private String reversalNo;
    
    /**
     * 逆向交接单关联分录号
     */
    private List<ReversalHandoverDetail> reversalHandoverDetails;
    
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.AUTO)  
    public Long getId() {
        return id;
    }

    /**
     * Setter method for property <tt>id</tt>.
     * 
     * @param id value to be assigned to property id
     */
    public void setId(Long id) {
        this.id = id;
    }
    
    @Column(name = "RESVERSAL_NO")
	public String getReversalNo() {
		return reversalNo;
	}

	public void setReversalNo(String reversalNo) {
		this.reversalNo = reversalNo;
	}

    @ManyToMany(fetch = FetchType.LAZY, mappedBy = "reversalHandoverHeader")
    @Where(clause = " IS_DELETED = 0 ")
	public List<ReversalHandoverDetail> getReversalHandoverDetails() {
		return reversalHandoverDetails;
	}

	public void setReversalHandoverDetails(
			List<ReversalHandoverDetail> reversalHandoverDetails) {
		this.reversalHandoverDetails = reversalHandoverDetails;
	}
}