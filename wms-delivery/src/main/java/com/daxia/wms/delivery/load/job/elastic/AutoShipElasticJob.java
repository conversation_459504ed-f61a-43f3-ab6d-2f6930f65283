package com.daxia.wms.delivery.load.job.elastic;

import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.load.entity.ReShipDo;
import com.daxia.wms.delivery.load.service.AutoDeliverService;
import com.daxia.wms.delivery.load.service.LoadService;
import com.daxia.wms.delivery.load.service.impl.AutoDeliverServiceImpl;
import com.daxia.wms.delivery.load.service.impl.LoadServiceImpl;
import com.daxia.wms.exp.sys.base.util.ExpSrvUtil;
import com.daxia.wms.master.job.WarehouseSimpleJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.log.Log;

import java.util.List;

/**
 * 自动发货的定时任务：<br>
 * 定时对 分拣环自动交接失败（异步自动发货失败）的DO重新发货
 * 支持自动交接类型的和流水交接类型的do。
 */
@Name("autoShipElasticJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class AutoShipElasticJob extends WarehouseSimpleJob {

    /**
     * 筛选满足条件的DO进行自动分配
     */
    @Override
    protected void doRun() throws InterruptedException {
        List<ReShipDo> shipQueueList = this.findShipList(ParamUtil.getCurrentWarehouseId());
        if (CollectionUtils.isEmpty(shipQueueList)) {
            return;
        }
        AutoDeliverService autoDeliverService = ((AutoDeliverService) Component.getInstance(AutoDeliverServiceImpl.class));
        LoadService loadService = ((LoadService) Component.getInstance(LoadServiceImpl.class));
        for (ReShipDo toShip : shipQueueList) {
            // 设置仓库id
            loadService.evictReShipDo(toShip);
            boolean success = Boolean.TRUE;
            try {
                success = autoDeliverService.reShip(toShip);
            } catch (Exception e) {
                success = Boolean.FALSE;
                log.error("error run autoShip:" + toShip.getId(), e);
            } finally {
                // 重新发货失败次数达到上限，邮件预警
                try {
                    if (success) {
                        loadService.removeReShipByDoId(toShip
                                .getDocId());
                    } else {
                        toShip.setNextInvokeTime(DateUtil.dateAdd("mi", DateUtil.getNowTime(), ExpSrvUtil.getReinvokeInterval(toShip.getCount().intValue(), ParamUtil.getCurrentWarehouseId())));
                        loadService.addShipCount(toShip);
                    }
                } catch (Exception e) {
                    log.error(
                            "error run autoShip update:"
                                    + toShip.getId(), e);
                }
            }
        }
    }

    private List<ReShipDo> findShipList(Long warehouseId) {
        Integer failExceed = Integer.valueOf(SystemConfig.getConfigValue(
                "mail.when.fail.exceed", null));
        Integer batchNumber = Integer.parseInt(SystemConfig.getConfigValue(
                "batch.delivery.count", null));
        LoadService loadService = ((LoadService) Component.getInstance(LoadServiceImpl.class));
        List<ReShipDo> shipQueue = loadService.findToReShipDo(
                failExceed, batchNumber, warehouseId);
        return shipQueue;
    }

}
