package com.daxia.wms.delivery.print.dto;

import java.math.BigDecimal;

import com.daxia.wms.print.dto.PrintReportDto;

/**
 *
 * 越库发货箱标签打印数据DTO
 */
@lombok.extern.slf4j.Slf4j
public class CrossDockCartonPrintDTO extends PrintReportDto {
	private static final long serialVersionUID = 1L;

	/**
	 * 调拨出库ID
	 */
	private Long cdHeaderId;
	
	/**
	 * 调拨出库单号
	 */
	private String cdHeaderNo;
	
	/**
	 *  LPN编号
	 */
	private String lpnNo;

	 /**
	  * 调拨指令单号
	  */
	private String refNo1;
	
	/**
     * 目的仓库ID
     */
	private Long toWhId;

	 /**
     * 调入仓库：目的仓库
     */
	private String toWarehouse;
	
	/**
	 * 是否贵重物品(1:贵重物品；0：非贵重物品)
	 */
	private Integer valuableFlag;
	
	/**
	 * 箱号
	 */
	private String cartonNo;
	
	/**
	 *拆箱多个箱号
	 */
	private String cartonNos;
	
	/**
     * sku总数
     */
    private BigDecimal skuQty;

    /**
     * units总数
     */
    private BigDecimal unitsQty;
    
    /**
     * lpn总数
     */
    private BigDecimal lpnQty;
    
    /**
     * 多个LPN是否有相同的指令单号
     */
    private Integer isSingleRefNo1;

	public String getCdHeaderNo() {
		return cdHeaderNo;
	}

	public void setCdHeaderNo(String cdHeaderNo) {
		this.cdHeaderNo = cdHeaderNo;
	}

	public String getLpnNo() {
		return lpnNo;
	}

	public void setLpnNo(String lpnNo) {
		this.lpnNo = lpnNo;
	}

	public String getRefNo1() {
		return refNo1;
	}

	public void setRefNo1(String refNo1) {
		this.refNo1 = refNo1;
	}

	public String getToWarehouse() {
		return toWarehouse;
	}

	public void setToWarehouse(String toWarehouse) {
		this.toWarehouse = toWarehouse;
	}

	public Integer getValuableFlag() {
		return valuableFlag;
	}

	public void setValuableFlag(Integer valuableFlag) {
		this.valuableFlag = valuableFlag;
	}

	public String getCartonNo() {
		return cartonNo;
	}

	public void setCartonNo(String cartonNo) {
		this.cartonNo = cartonNo;
	}

	public BigDecimal getSkuQty() {
		return skuQty;
	}

	public void setSkuQty(BigDecimal skuQty) {
		this.skuQty = skuQty;
	}

	public BigDecimal getUnitsQty() {
		return unitsQty;
	}

	public void setUnitsQty(BigDecimal unitsQty) {
		this.unitsQty = unitsQty;
	}

	public BigDecimal getLpnQty() {
		return lpnQty;
	}

	public void setLpnQty(BigDecimal lpnQty) {
		this.lpnQty = lpnQty;
	}

	public String getCartonNos() {
		return cartonNos;
	}

	public void setCartonNos(String cartonNos) {
		this.cartonNos = cartonNos;
	}

	public Long getCdHeaderId() {
		return cdHeaderId;
	}

	public void setCdHeaderId(Long cdHeaderId) {
		this.cdHeaderId = cdHeaderId;
	}

	public Long getToWhId() {
		return toWhId;
	}

	public void setToWhId(Long toWhId) {
		this.toWhId = toWhId;
	}

	public Integer getIsSingleRefNo1() {
		return isSingleRefNo1;
	}

	public void setIsSingleRefNo1(Integer isSingleRefNo1) {
		this.isSingleRefNo1 = isSingleRefNo1;
	}
}
