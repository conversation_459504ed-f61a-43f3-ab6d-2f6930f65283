package com.daxia.wms.delivery.recheck.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.Constants.WaybillType;
import com.daxia.wms.Constants.YesNo;

/**
 * 箱信息取消日志
 */
@Entity
@Table(name = "doc_carton_cancel_log")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@lombok.extern.slf4j.Slf4j
public class CartonCancelLog extends WhBaseEntity {

    private static final long serialVersionUID = -911858142758677658L;

    private Long id;

    // 箱号
    private String cartonNo;

    // 电子面单号，运单号
    private String waybill;

    private WaybillType waybillType;

    private Long doHeadaerId;

    
    private Date cancelTime;

    private Long carrierId;

    private YesNo isVerify = YesNo.NO;

    private YesNo cancelSuccess = YesNo.YES;

    @Column(name = "cancel_success")
    @Enumerated(EnumType.STRING)
    public YesNo getCancelSuccess() {
        return cancelSuccess;
    }

    public void setCancelSuccess(YesNo cancelSuccess) {
        this.cancelSuccess = cancelSuccess;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "carton_no")
    public String getCartonNo() {
        return cartonNo;
    }

    public void setCartonNo(String cartonNo) {
        this.cartonNo = cartonNo;
    }

    @Column(name = "waybill")
    public String getWaybill() {
        return waybill;
    }

    public void setWaybill(String waybill) {
        this.waybill = waybill;
    }

    @Column(name = "waybill_type")
    public WaybillType getWaybillType() {
        return waybillType;
    }

    public void setWaybillType(WaybillType waybillType) {
        this.waybillType = waybillType;
    }

    @Column(name = "do_headaer_id")
    public Long getDoHeadaerId() {
        return doHeadaerId;
    }

    public void setDoHeadaerId(Long doHeadaerId) {
        this.doHeadaerId = doHeadaerId;
    }

    @Column(name = "cancel_time")
    public Date getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(Date cancelTime) {
        this.cancelTime = cancelTime;
    }

    @Column(name = "is_verify")
    @Enumerated(EnumType.STRING)
    public YesNo getIsVerify() {
        return isVerify;
    }

    public void setIsVerify(YesNo isVerify) {
        this.isVerify = isVerify;
    }

    @Column(name = "carrier_id")
    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }
}