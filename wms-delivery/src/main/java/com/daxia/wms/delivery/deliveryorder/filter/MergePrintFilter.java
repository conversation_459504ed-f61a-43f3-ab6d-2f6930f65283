package com.daxia.wms.delivery.deliveryorder.filter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@lombok.extern.slf4j.Slf4j
public class MergePrintFilter implements Serializable {

    private static final long serialVersionUID = -8161171226949691369L;

    private String doNo;

    private String consigneeName;

    private Integer isPrinted;

    private List<Long> doIdList;

    private Long carrierId;

    private String groupNo;

    public Date doCreateTimeFm;

    public Date doCreateTimeTo;

    public Long businessCustomerId;

    public String statusFm;
    public String statusTo;


    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public Integer getIsPrinted() {
        return isPrinted;
    }

    public void setIsPrinted(Integer isPrinted) {
        this.isPrinted = isPrinted;
    }

    public List<Long> getDoIdList() {
        return doIdList;
    }

    public void setDoIdList(List<Long> doIdList) {
        this.doIdList = doIdList;
    }

    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public Date getDoCreateTimeFm() {
        return doCreateTimeFm;
    }

    public void setDoCreateTimeFm(Date doCreateTimeFm) {
        this.doCreateTimeFm = doCreateTimeFm;
    }

    public Date getDoCreateTimeTo() {
        return doCreateTimeTo;
    }

    public void setDoCreateTimeTo(Date doCreateTimeTo) {
        this.doCreateTimeTo = doCreateTimeTo;
    }

    public Long getBusinessCustomerId() {
        return businessCustomerId;
    }

    public void setBusinessCustomerId(Long businessCustomerId) {
        this.businessCustomerId = businessCustomerId;
    }

    public String getStatusFm() {
        return statusFm;
    }

    public void setStatusFm(String statusFm) {
        this.statusFm = statusFm;
    }

    public String getStatusTo() {
        return statusTo;
    }

    public void setStatusTo(String statusTo) {
        this.statusTo = statusTo;
    }
}
