package com.daxia.wms.delivery.load.action;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.daxia.dubhe.api.qimen.wms.request.entity.DeliveryOrder;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.filter.DoHeaderFilter;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.load.dto.StockCrossDockDTO;
import com.daxia.wms.delivery.load.service.CrossDockDetailService;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.security.Restrict;
import org.json.JSONArray;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.DownloadUtil;
import com.daxia.wms.delivery.load.entity.TOCrossDockHeader;
import com.daxia.wms.delivery.load.filter.CrossDockHeaderFilter;
import com.daxia.wms.delivery.load.service.TOCrossDockService;
import com.daxia.wms.delivery.print.service.PrintService;

/**
 * 功能说明：CROSS-DOCK发货，crossDockHeaderList.xhtml、crossDockInfo.xhtml页面使用
*/
@SuppressWarnings("serial")
@Name("com.daxia.wms.delivery.crossDockDeliverAction")
@Restrict("#{identity.hasPermission('delivery.crossdock')}")
@Scope(ScopeType.PAGE) 
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class CrossDockDeliverAction extends PagedListBean<DoHeaderDto> {

    @In
    private DeliveryOrderService deliveryOrderService;
    @In
    private TOCrossDockService toCrossDockService;
    @In
    private CrossDockDetailService crossDockDetailService;

    @In
    private PrintService printService;
    // 越库发货明细集合
    private List<StockCrossDockDTO> stockCrossDockDTOs;


    private DoHeaderFilter doHeaderFilter;
    private DeliveryOrderHeader crossDockHeader;
    private Long crossDockHeaderId;
    private String printData = "[]";
    private boolean initialized = false;
    
    public CrossDockDeliverAction() {
        super();
        doHeaderFilter = new DoHeaderFilter();
    }

    /**
     * 分页查询的基础方法
     * 查询crossDockHeader
     */
    @Override
    public void query() {
        this.buildOrderFilterMap(doHeaderFilter);
        doHeaderFilter.getOrderByMap().put("id","desc");
        doHeaderFilter.setNeedCrossStock(Constants.YesNo.YES.getValue());
        DataPage<DoHeaderDto> dataPage = deliveryOrderService.query(doHeaderFilter, getStartIndex(), getPageSize());
//        DataPage<DeliveryOrderHeader> dataPage = toCrossDockService.findCrossDockHeaders(crossDockHeaderFilter,
//            getStartIndex(), getPageSize());
        this.populateValues(dataPage);
    }

	public void view() {
		if (!initialized) {// 确保初始化动作只执行一次
			this.crossDockHeader = this.deliveryOrderService.getDoHeaderById(this.crossDockHeaderId);
		}
	}
    
    /**
     * 得到需要打印的单据id
     */
    private List<Long> getPrintCDocIds() {
        List<Long> ids = new ArrayList<Long>();

        if (this.crossDockHeaderId == null) {
            for (Object id : getSelectedRowList()) {
                ids.add((Long) id);
            }
        } else {
            ids.add(this.crossDockHeaderId);
        }
        return ids;
    }

    public void doOnekeyShip(){
        //验证 1.doc_crdoc_header 状态,2.库存状态,3.数量匹配。
        stockCrossDockDTOs = crossDockDetailService.checkBeforeCrossdockDelivery(crossDockHeaderId);
        crossDockDetailService.delivery(crossDockHeaderId,stockCrossDockDTOs,false);
        query();
    }
	
	/**
	 * 打印
	 */
	public void print() {
        this.printData = "[]";
        
        List<Long> ids = getPrintCDocIds();

        CrossDockHeaderFilter crossDockHeaderFilter = new CrossDockHeaderFilter();
        crossDockHeaderFilter.setIds(ids);
        List<TOCrossDockHeader> toCrossDockHeaders = toCrossDockService.query(crossDockHeaderFilter);
        List<String> pages = printService.getCrossDocDoReport(toCrossDockHeaders);

        this.printData = new JSONArray(pages).toString();
    }
	
	/**
	 * 导出
	 */
	public void export() throws IOException {
        this.printData = "[]";
        
        List<Long> ids = this.getPrintCDocIds();

        CrossDockHeaderFilter crossDockHeaderFilter = new CrossDockHeaderFilter();
        crossDockHeaderFilter.setIds(ids);
        List<TOCrossDockHeader> toCrossDockHeaders = toCrossDockService.query(crossDockHeaderFilter);
        byte[] exportData = printService.exportCrossDocDoReport(toCrossDockHeaders);

        DownloadUtil.writeToResponse(exportData, DownloadUtil.PDF,
            "cdock_" + DateUtil.dateToString(new Date(), "yyMMddHHmmss") + ".pdf");
    }

    /**
     * 
     *显示CROSS_DOCK的明细信息
     *
     */
    public void showCroDockInfo() {
        this.crossDockHeader = deliveryOrderService.getDoHeaderById(this.crossDockHeaderId);
    }


    /**
     * @return the crossDockHeader
     */
    public DeliveryOrderHeader getCrossDockHeader() {
        return crossDockHeader;
    }

    /**
     * @param crossDockHeader
     *            the crossDockHeader to set
     */
    public void setCrossDockHeader(DeliveryOrderHeader crossDockHeader) {
        this.crossDockHeader = crossDockHeader;
    }

    public Long getCrossDockHeaderId() {
        return crossDockHeaderId;
    }

    public void setCrossDockHeaderId(Long crossDockHeaderId) {
        this.crossDockHeaderId = crossDockHeaderId;
    }

	public String getPrintData() {
		return printData;
	}

	public void setPrintData(String printData) {
		this.printData = printData;
	}

    public List<StockCrossDockDTO> getStockCrossDockDTOs() {
        return stockCrossDockDTOs;
    }

    public void setStockCrossDockDTOs(List<StockCrossDockDTO> stockCrossDockDTOs) {
        this.stockCrossDockDTOs = stockCrossDockDTOs;
    }

    public DoHeaderFilter getDoHeaderFilter() {
        return doHeaderFilter;
    }

    public void setDoHeaderFilter(DoHeaderFilter doHeaderFilter) {
        this.doHeaderFilter = doHeaderFilter;
    }
}
