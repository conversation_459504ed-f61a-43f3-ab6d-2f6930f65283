package com.daxia.wms.delivery.deliveryorder.dto;

import java.io.Serializable;
import java.util.Date;

@lombok.extern.slf4j.Slf4j
public class LackDoDto implements Serializable {

    private static final long serialVersionUID = -1938554313776436613L;

    private String doNo;

    private String sortBinNo;

    private String lackLocCode;

    private String status;

    private Integer lackStatus;

    private Date holdTime;

    private Date doFinishTime;

    private String holdCode;

    private String exceptionStatus;

    private Boolean needCancel;

    public LackDoDto(String doNo, String sortBinNo, String lackLocCode, String status, Integer lackStatus,
            Date holdTime, Date doFinishTime, String holdCode, String exceptionStatus, Boolean needCancel) {
        this.doNo = doNo;
        this.sortBinNo = sortBinNo;
        this.lackLocCode = lackLocCode;
        this.status = status;
        this.lackStatus = lackStatus;
        this.holdTime = holdTime;
        this.doFinishTime = doFinishTime;
        this.holdCode = holdCode;
        this.exceptionStatus = exceptionStatus;
        this.needCancel = needCancel;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getSortBinNo() {
        return sortBinNo;
    }

    public void setSortBinNo(String sortBinNo) {
        this.sortBinNo = sortBinNo;
    }

    public String getLackLocCode() {
        return lackLocCode;
    }

    public void setLackLocCode(String lackLocCode) {
        this.lackLocCode = lackLocCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getLackStatus() {
        return lackStatus;
    }

    public void setLackStatus(Integer lackStatus) {
        this.lackStatus = lackStatus;
    }

    public Date getHoldTime() {
        return holdTime;
    }

    public void setHoldTime(Date holdTime) {
        this.holdTime = holdTime;
    }

    public Date getDoFinishTime() {
        return doFinishTime;
    }

    public void setDoFinishTime(Date doFinishTime) {
        this.doFinishTime = doFinishTime;
    }

    public String getHoldCode() {
        return holdCode;
    }

    public void setHoldCode(String holdCode) {
        this.holdCode = holdCode;
    }

    public String getExceptionStatus() {
        return exceptionStatus;
    }

    public void setExceptionStatus(String exceptionStatus) {
        this.exceptionStatus = exceptionStatus;
    }

    public Boolean getNeedCancel() {
        return needCancel;
    }

    public void setNeedCancel(Boolean needCancel) {
        this.needCancel = needCancel;
    }

}
