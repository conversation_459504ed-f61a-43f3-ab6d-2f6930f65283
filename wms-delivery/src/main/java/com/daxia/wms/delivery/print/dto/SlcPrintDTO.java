package com.daxia.wms.delivery.print.dto;

import java.util.Date;
import java.util.List;

import com.daxia.wms.master.entity.City;
import com.daxia.wms.master.entity.County;
import com.daxia.wms.master.entity.Province;

@lombok.extern.slf4j.Slf4j
public class SlcPrintDTO {
    
    /**
     * 发货单号
     */
    private String doNo;
    
    /**
     * 参考编号1(SO单号/调拨指令号)
     */
    private String refNo1;
    
    /**
     * 收货方
     */
    private String consigneeName;

    /**
     * 收货地址
     */
    private String address;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 手机
     */
    private String mobile;
    
    /**
     * 制单人
     */
    private String createdBy;
    
    private Province provinceInfo;

    private City cityInfo;

    private County countyInfo;
    
    /**
     * 期望到货时间1
     */
    private Date expectedArriveTime1;
    /**
     * 是否第一次购买
     */
    private String userDeffine3;
    /**
     * 波次滑道号
     */
    private String waveNo;
    /**
     * 
     */
    private Integer tranType;
    /**
     * 
     */
    private String notes;
    
    /**
     * 发货仓库
     */
    private String warehouseName;
    
    /**
     * slc出库单明细
     */
    private List<SlcTaskDTO> slcTaskDTO;
    
    
    public List<SlcTaskDTO> getSlcTaskDTO() {
        return slcTaskDTO;
    }

    
    public void setSlcTaskDTO(List<SlcTaskDTO> slcTaskDTO) {
        this.slcTaskDTO = slcTaskDTO;
    }

    public String getDoNo() {
        return doNo;
    }
    
    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }
    
    public String getRefNo1() {
        return refNo1;
    }
    
    public void setRefNo1(String refNo1) {
        this.refNo1 = refNo1;
    }
    
    public String getConsigneeName() {
        return consigneeName;
    }
    
    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public String getTelephone() {
        return telephone;
    }
    
    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }
    
    public String getMobile() {
        return mobile;
    }
    
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    
    public Province getProvinceInfo() {
        return provinceInfo;
    }
    
    public void setProvinceInfo(Province provinceInfo) {
        this.provinceInfo = provinceInfo;
    }
    
    public City getCityInfo() {
        return cityInfo;
    }
    
    public void setCityInfo(City cityInfo) {
        this.cityInfo = cityInfo;
    }
    
    public County getCountyInfo() {
        return countyInfo;
    }
    
    public void setCountyInfo(County countyInfo) {
        this.countyInfo = countyInfo;
    }
    
    public Date getExpectedArriveTime1() {
        return expectedArriveTime1;
    }
    
    public void setExpectedArriveTime1(Date expectedArriveTime1) {
        this.expectedArriveTime1 = expectedArriveTime1;
    }
    
    public String getUserDeffine3() {
        return userDeffine3;
    }
    
    public void setUserDeffine3(String userDeffine3) {
        this.userDeffine3 = userDeffine3;
    }
    
    public String getWaveNo() {
        return waveNo;
    }
    
    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }
    
    public Integer getTranType() {
        return tranType;
    }
    
    public void setTranType(Integer tranType) {
        this.tranType = tranType;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }


    
    public String getWarehouseName() {
        return warehouseName;
    }


    
    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }
    
}
