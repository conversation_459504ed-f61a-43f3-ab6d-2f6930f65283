package com.daxia.wms.delivery.load.dao;

import java.util.ArrayList;
import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.stock.stock.entity.StockCrossDock;

/**
 * 越库发货Dao
 */
@Name("com.daxia.wms.delivery.crossDockDeliverDetailDao")
@lombok.extern.slf4j.Slf4j
public class CrossDockDeliverDetailDao extends HibernateBaseDAO<StockCrossDock, Long> {

	private static final long serialVersionUID = 2587119157929474252L;

	private static final String SHIPPED = Constants.StockCDStatus.SHIPPED.getValue();

	private static final String INITIAL = Constants.StockCDStatus.INITIAL.getValue();

	private static final String PACKING = Constants.StockCDStatus.PACKING.getValue();
	
	private static final String ALL_TO_LOCAL = Constants.StockCDStatus.ALL_TO_LOCAL.getValue();

	/**
	 * 根据LPN号获取越库发货单单头
	 * 
	 * @param lpnNo
	 *            LPN号
	 * @param isShipped
	 *            是否已经发货
	 * @return 越库发货单单头
	 */
	public Object findToCDHeaderByLpnNo(String lpnNo, boolean isShipped) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select o.id, 											")
				.append("       o.ref_no2, 									")
				.append("       o.do_no, 									")
				.append("       o.status, 									")
				.append("       o.ref_no1, 									")
				.append("       o.edi_2, 									")
				.append("		  mw.warehouse_name 						")
				.append("from   doc_crdock_header o, 						")
				.append("       doc_crdock_detail t, 						")
				.append("       stk_crossdock e, 							")
				.append("       md_warehouse mw 							")
				.append("where  e.doc_id = o.asn_header_id 					")
				.append("       and o.id = t.do_header_id 					")
				.append("       and t.sku_id = e.sku_id 					")
				.append("       and e.to_wh_id = o.edi_2 					")
				.append("       and mw.id = e.to_wh_id 						")
				.append("       and e.lpn_no = :lpnNo 						")
				.append("       and o.warehouse_id = :warehouseId           ")
				.append("       and t.warehouse_id = :warehouseId           ")
				.append("       and e.warehouse_id = :warehouseId           ");
		if (isShipped) {
			sql.append("       and e.status = :shipped 					    ");
		} else {
			sql.append("       and (e.status = :initial or e.status= :packing)");
		}
		Query query = createSQLQuery(sql.toString());
		query.setString("lpnNo", lpnNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId()); 
		queryAddStatus(query, isShipped);
		query.setMaxResults(1);
		return query.list();
	}

	/**
	 * 通过LpnNo获取发货需要扫描的LPN号`
	 * 
	 * @param lpnNo
	 *            lpn编号
	 * @param isShipped
	 *            是否已经发货
	 * @return 需要扫描的LPN号
	 */
	@SuppressWarnings("unchecked")
	public List<String> getNeedScannedLpnByLpnNo(String lpnNo, boolean isShipped) {
		StringBuffer sql = new StringBuffer();
		sql.append("    select distinct sc2.lpn_no											")
				.append("      from stk_crossdock sc1						   				")
				.append("     inner join stk_crossdock sc2									")
				.append("       on sc1.doc_id = sc2.doc_id and sc1.to_wh_id = sc2.to_wh_id 	")
		.append("     where sc1.lpn_no = :lpnNo										")
		        .append("       and sc1.warehouse_id = :warehouseId           				")
		        .append("       and sc2.warehouse_id = :warehouseId           				");
		if (isShipped) {
			sql.append("       and sc1.status = :shipped and sc2.status = :shipped			");
		} else {
			sql.append("       and (sc1.status = :initial or sc1.status = :packing)			");
			sql.append("       and (sc2.status = :initial or sc2.status = :packing)			");
		}
		sql.append("     order by sc2.lpn_no desc											");
		Query query = createSQLQuery(sql.toString());
		query.setParameter("lpnNo", lpnNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId()); 
		queryAddStatus(query, isShipped);
		return query.list();
	}

	/**
	 * 通过越库发货单单头获取需要扫描的LPN号
	 * 
	 * @param cdHeaderId
	 *            越库发货单单头
	 * @param isShipped
	 *            是否已经发货
	 * @return 需要扫描的LPN号
	 */
	@SuppressWarnings("unchecked")
	public List<String> getNeedScannedLpnByCDHeaderId(Long cdHeaderId,Integer crossStockType,
			boolean isShipped) {
		StringBuffer sql = new StringBuffer();
		sql.append("select distinct sc.lpn_no												")
			.append("       from stk_crossdock sc											")
			.append("      inner join doc_do_header dch									")
			.append("         on dch.source_asn_id = sc.doc_id  ");
		if(Constants.CrossDockType.WAREHOUSE.getValue().equals(crossStockType)){
			sql.append(" and sc.to_wh_id = dch.edi_2			");
		}else if(Constants.CrossDockType.CUSTOMER.getValue().equals(crossStockType)){
			sql.append(" and sc.to_customer_id = dch.business_customer_id			");
		}
			sql.append(" where dch.id = :cdHeaderId                                         	")
	        .append("        and sc.warehouse_id  = :warehouseId           					")
	        .append("        and dch.warehouse_id = :warehouseId          			 		");
		if (isShipped) {
			sql.append("       and sc.status = :shipped 					 				");
		} else {
			sql.append("       and (sc.status = :initial or sc.status = :packing)			");
		}
		sql.append("     order by sc.lpn_no desc											");
		Query query = createSQLQuery(sql.toString());
		query.setLong("cdHeaderId", cdHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId()); 
		queryAddStatus(query, isShipped);
		return query.list();
	}

	/**
	 * 通过LPN号获取已稍描的LPN对应的 stocCrossDockList
	 * @param lpnNo
	 *            lpn编号
	 * @param isShipped
	 *            是否已经发货
	 * @return 返回LPN对应的有效收货待转运库存
	 */
	@SuppressWarnings("unchecked")
	public List<StockCrossDock> getStockCrossDockByLpnNo(String lpnNo,
			boolean isShipped) {
		StringBuilder sql = new StringBuilder();
		sql.append("from stockcrossdock o where o.lpnNo = :lpnNo 							")
			.append("       and o.warehouseId = :warehouseId           						");
		if (isShipped) {
			sql.append(" and o.status = :shipped 											");
		} else {
			sql.append(" and (o.status = :INITIAL or o.status = :PACKING	)					");
		}
		Query query = createQuery(sql.toString());
		query.setString("lpnNo", lpnNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId()); 
		queryAddStatus(query, isShipped);
		return query.list();
	}

	/**
	 * 通过LPN号获得发货明细
	 * 
	 * @param isShipped
	 *            是否已经发货
	 * @return needScannedLpns 及尚需发货LPN
	 */
	@SuppressWarnings("unchecked")
	public List<Object> getCrossDockDetailDTOByLPN(Long cdHeaderId, String lpnNo,
			boolean isShipped) {
		StringBuilder sql = new StringBuilder();
		sql.append("select sc.id, 															")
			.append("       sku.product_code, 												")
			.append("       sku.ean13, 														")
			.append("       sku.product_cname, 												");
		if (!isShipped) {
			sql.append("    sc.qty, 														");
		} else {
			sql.append("    tsl.fm_qty, 													");
		}
		sql.append("        mb.lotatt01, 													")
			.append("       mb.lotatt02, 													")
			.append("       mb.lotatt05, 													")
			.append("       dah.asn_ref_no1,												")
			.append("       mb.lot_no, 														")
			.append("       sc.lpn_no, 														")
			.append("       sku.sn_qty, 													")
			.append("       sku.product_type, 												")
			.append("       sku.id as skuid, 												")
			.append("       dcd.id as cddid, 												")
			.append("       loc.loc_type, 													")
			.append("       sc.lot_id, 														")
			.append("       sc.status, 														")
			.append("       sc.loc_id,  													")
			.append("       loc.lock_status  												")
			.append(" from  stk_crossdock sc 												")
			.append("       inner join md_sku sku 											")
			.append("          on sku.id = sc.sku_id 										")
			.append("       inner join doc_asn_header dah 									")
			.append("          on dah.id = sc.doc_id 										")
			.append("       inner join stock_batch_att mb 									")
			.append("          on mb.id = sc.lot_id 										")
			.append("       inner join doc_do_header dch 								")
			.append("          on dch.source_asn_id = sc.doc_id and (dch.edi_2 = sc.to_wh_id " +
					"or (dch.edi_2 is null and sc.to_customer_id = dch.business_customer_id)) ")
			.append("       inner join doc_do_detail dcd 								")
			.append("          on dcd.sku_id = sc.sku_id and dcd.do_header_id = dch.id 		")
			.append("        left join md_location loc 										")
			.append("          on loc.id = sc.loc_id 										");
		if (isShipped) {
			sql.append("     left join trs_ship_log tsl										");
			sql.append("       on dch.id = tsl.doc_id										");
			sql.append("      and dcd.id = tsl.doc_line_id									");
			sql.append("      and sc.lot_id = tsl.fm_lot_id									");
			sql.append("      and sc.lpn_no = tsl.fm_lpn_no									");
			sql.append("      and sc.loc_id = tsl.fm_loc_id									");
			sql.append("      and sc.sku_id = tsl.fm_sku_id									");
		}
		sql.append(" where sc.warehouse_id  = :warehouseId          		 				")
	        .append("         and dah.warehouse_id = :warehouseId          	 				")
	        .append("         and mb.warehouse_id  = :warehouseId           				")
	        .append("         and dch.warehouse_id = :warehouseId           				")
	        .append("         and dcd.warehouse_id = :warehouseId           				")
	        .append("         and loc.warehouse_id = :warehouseId           				");
		if (isShipped) {
			sql.append(" and sc.lpn_no = :lpnNo	and sc.status = :SHIPPED 				");
		} else {
			sql.append(" and sc.lpn_no = :lpnNo	 and (sc.status = :INITIAL or sc.status = :PACKING)");
		}
        if (cdHeaderId != null) {
            sql.append("and sc.doc_id = :docId");
        }
        Query query = createSQLQuery(sql.toString());
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        if (cdHeaderId != null) {
            query.setLong("docId", cdHeaderId);
        }
		if(isShipped){
			List<String> lpnList = new ArrayList<String>();
			for (String lpnNoStr : lpnNo.split(",")) {
				lpnList.add(lpnNoStr);
			}
			query.setString("SHIPPED", SHIPPED);
			query.setParameterList("lpnNo", lpnList);
		}else{
			query.setString("INITIAL", INITIAL);
			query.setString("PACKING", PACKING);
			query.setString("lpnNo", lpnNo);
		}
		return query.list();
	}

	/**
	 * 通过LPN号获得发货明细
	 **/
	@SuppressWarnings("unchecked")
	public List<Object> getCrossDockDetailDTOByCdHeader(Long cdHeaderId,Integer crossDockType) {
		StringBuilder sql = new StringBuilder();
		sql.append("select sc.id, 															")
				.append("       sku.product_code, 												")
				.append("       sku.ean13, 														")
				.append("       sku.product_cname, 												")
				.append("    sc.qty, 														")
				.append("        mb.lotatt01, 													")
				.append("       mb.lotatt02, 													")
				.append("       mb.lotatt05, 													")
				.append("       dah.asn_ref_no1,												")
				.append("       mb.lot_no, 														")
				.append("       sc.lpn_no, 														")
				.append("       sku.sn_qty, 													")
				.append("       sku.product_type, 												")
				.append("       sku.id as skuid, 												")
				.append("       dcd.id as cddid, 												")
				.append("       loc.loc_type, 													")
				.append("       sc.lot_id, 														")
				.append("       sc.status, 														")
				.append("       sc.loc_id,  													")
				.append("       loc.lock_status  												")
				.append(" from  stk_crossdock sc 												")
				.append("       inner join md_sku sku 											")
				.append("          on sku.id = sc.sku_id 										")
				.append("       inner join doc_asn_header dah 									")
				.append("          on dah.id = sc.doc_id 										")
				.append("       inner join stock_batch_att mb 									")
				.append("          on mb.id = sc.lot_id 										")
				.append("       inner join doc_do_header dch on dch.source_asn_id = sc.doc_id		");
				if(Constants.CrossDockType.WAREHOUSE.getValue().equals(crossDockType)){
					sql.append("           and dch.edi_2 = sc.to_wh_id ");
				}else if(Constants.CrossDockType.CUSTOMER.getValue().equals(crossDockType)){
					sql.append("           and dch.business_customer_id = sc.to_customer_id ");
				}

				sql.append("       inner join doc_do_detail dcd 								")
				.append("          on dcd.sku_id = sc.sku_id and dcd.do_header_id = dch.id 		")
				.append("        left join md_location loc 										")
				.append("          on loc.id = sc.loc_id 										")
				.append(" where sc.warehouse_id  = :warehouseId          		 				")
				.append("         and dah.warehouse_id = :warehouseId          	 				")
				.append("         and mb.warehouse_id  = :warehouseId           				")
				.append("         and dch.warehouse_id = :warehouseId           				")
				.append("         and dcd.warehouse_id = :warehouseId           				")
				.append("         and loc.warehouse_id = :warehouseId           				")
				.append(" and dch.id = :cdHeaderId and dch.NEED_CROSSSTOCK = 1	" +
						" ");
		Query query = createSQLQuery(sql.toString());
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
//		query.setString("INITIAL", INITIAL);
		query.setParameter("cdHeaderId", cdHeaderId);
		return query.list();
	}

	/**
	 * 添加越库发货单状态条件
	 * 
	 * @param query
	 *            越库单查询query
	 * @param isShipped
	 *            是否已经发货
	 */
	private void queryAddStatus(Query query, boolean isShipped) {
		if (isShipped) {
			query.setString("shipped", SHIPPED);
		} else {
			query.setString("initial", INITIAL);
			query.setString("packing", PACKING);
		}
	}
	
	/**
	 * 通过越库发货单单头获取转储的单子
	 * 
	 * @param cdHeaderId
	 *            越库发货单单头
	 * @return 需要扫描的LPN号
	 */
	@SuppressWarnings("unchecked")
	public List<String> getCDJumpCDHeaderId(Long cdHeaderId,Integer crossDockType) {
		StringBuffer sql = new StringBuffer();
		sql.append("select distinct sc.lpn_no												")
			.append("       from stk_crossdock sc											")
			.append("      inner join doc_do_header dch									")
			.append("         on dch.source_asn_id = sc.doc_id  ");
		if(Constants.CrossDockType.WAREHOUSE.getValue().equals(crossDockType)){
			sql.append("           and dch.edi_2 = sc.to_wh_id ");
		}else if(Constants.CrossDockType.CUSTOMER.getValue().equals(crossDockType)){
			sql.append("           and dch.business_customer_id = sc.to_customer_id ");
		}
		sql.append(" where dch.id = :cdHeaderId                                         	")
			.append("        and sc.warehouse_id  = :warehouseId           					")
			.append("        and dch.warehouse_id = :warehouseId          			 		")
			.append("        and sc.status = :status										")
			.append("     order by sc.lpn_no desc											");
		Query query = createSQLQuery(sql.toString());
		query.setLong("cdHeaderId", cdHeaderId);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId()); 
		query.setParameter("status", ALL_TO_LOCAL); 
		return query.list();
	}
}
