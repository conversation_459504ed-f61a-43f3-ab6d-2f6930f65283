package com.daxia.wms.delivery.task.replenish.entity;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.master.entity.Partition;

/**
 * 补货头信息
 */
@Entity
@Table(name = "doc_rpl_header")
@Where(clause = "IS_DELETED = 0")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@lombok.extern.slf4j.Slf4j
public class ReplenishHeader extends WhBaseEntity {
    private static final long serialVersionUID = 7880994350324178974L;
    private Long id;

    private String status;

    private String replNo;

    private Long partitionId;
    
    private Long srcPartitionId;

    private Long regionId;
    
    private String replType;

    private List<ReplenishTask> replTaskList;
    
    private Partition partition;
    
    private Partition srcPartition;
    
    private String doType;
    
    private Long skus;	//商品数量
    
    private Long units;	//UNITS数量
    
    private Integer isAuto;

    private Date earlistPlanShipTime; //最早预计出库时间
    
    private Integer priority;
    
    /**
     * 劳动力(指派人)
     */
    private Long operUserId;
    
    /**
     * 是否可索取（默认可以索取）
     */
    private Boolean isAvailable;
    
    /**
     * @return the id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)  
    @Column(name = "ID")
    public Long getId() {
        return id;
    }

    /**
     * @param id the id to set
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return the status
     */
    @Column(name = "STATUS")
    public String getStatus() {
        return status;
    }

    /**
     * @param status the status to set
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * @return the rplNo
     */
    @Column(name = "RPL_NO")
    public String getReplNo() {
        return replNo;
    }

    /**
     * @param rplNo the rplNo to set
     */
    public void setReplNo(String replNo) {
        this.replNo = replNo;
    }
    
	@OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "DOC_OPER_ID", insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 AND TASK_TYPE = 'RP'")
    public List<ReplenishTask> getReplTaskList() {
        return replTaskList;
    }

    public void setReplTaskList(List<ReplenishTask> replTaskList) {
        this.replTaskList = replTaskList;
    }

    @Column(name = "PARTITION_ID")
	public Long getPartitionId() {
		return partitionId;
	}

	public void setPartitionId(Long partitionId) {
		this.partitionId = partitionId;
	}
    
	@Column(name = "SRC_PARTITION_ID")
    public Long getSrcPartitionId() {
        return srcPartitionId;
    }
    
    public void setSrcPartitionId(Long srcPartitionId) {
        this.srcPartitionId = srcPartitionId;
    }

    @Column(name = "REGION_ID")
	public Long getRegionId() {
		return regionId;
	}

	public void setRegionId(Long regionId) {
		this.regionId = regionId;
	}

    @ManyToOne(targetEntity = Partition.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "PARTITION_ID", insertable = false, updatable = false)
	public Partition getPartition() {
		return partition;
	}

	public void setPartition(Partition partition) {
		this.partition = partition;
	}

    @ManyToOne(targetEntity = Partition.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "SRC_PARTITION_ID", insertable = false, updatable = false)
    public Partition getSrcPartition() {
        return srcPartition;
    }

    public void setSrcPartition(Partition srcPartition) {
        this.srcPartition = srcPartition;
    }

	@Column(name = "REPL_TYPE")
	public String getReplType() {
		return replType;
	}

	public void setReplType(String replType) {
		this.replType = replType;
	}

	@Column(name = "DO_TYPE")
    public String getDoType() {
        return doType;
    }
    
    public void setDoType(String doType) {
        this.doType = doType;
    }	
	
    @Column(name = "SKU_QTY")
	public Long getSkus() {
		return skus;
	}

	public void setSkus(Long skus) {
		this.skus = skus;
	}

	@Column(name = "UNITS_QTY")
	public Long getUnits() {
		return units;
	}

	public void setUnits(Long units) {
		this.units = units;
	}
	
	@Column(name = "E_PLAN_SHIP_TIME")
    public Date getEarlistPlanShipTime() {
        return earlistPlanShipTime;
    }
    
    public void setEarlistPlanShipTime(Date earlistPlanShipTime) {
        this.earlistPlanShipTime = earlistPlanShipTime;
    }

    @Column(name = "IS_AUTO")
    public Integer getIsAuto() {
        return isAuto;
    }

    public void setIsAuto(Integer isAuto) {
        this.isAuto = isAuto;
    }
    
    @Column(name = "PRIORITY")
    public Integer getPriority() {
		return priority;
	}
    
    public void setPriority(Integer priority) {
		this.priority = priority;
	}
    
    @Column(name = "OPER_USER_ID")
	public Long getOperUserId() {
		return operUserId;
	}

	public void setOperUserId(Long operUserId) {
		this.operUserId = operUserId;
	}
	
	@Column(name = "IS_AVAILABLE")
	public Boolean getIsAvailable() {
		return isAvailable;
	}

	public void setIsAvailable(Boolean isAvailable) {
		this.isAvailable = isAvailable;
	}
}
