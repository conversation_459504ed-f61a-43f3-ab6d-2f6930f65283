package com.daxia.wms.delivery.invoice.service;

import java.math.BigDecimal;
import java.util.List;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.invoice.entity.InvoiceDetail;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.filter.InvoiceFilter;

/**
 * 发票信息service
 */
public interface InvoiceService {
    /**
     * 发票模式常量
     */
    public static String INVOICE_LEVEL_1 = "1";
    public static String INVOICE_LEVEL_2 = "2";
    public static String INVOICE_LEVEL_3 = "3";
    public static String INVOICE_LEVEL_4 = "4";
    public static String INVOICE_LEVEL_5 = "5";

    /**
     * 查询指定分页的制造商信息
     * 
     * @param Invoice
     * @param startIndex
     * @param pageSize
     * @return
     */
    public DataPage<InvoiceHeader> findInvoiceByFilter(InvoiceFilter invoiceFilter, int startIndex, int pageSize);

    /**
     * 获取指定ID的Invoice
     * 
     * @param id
     * @return
     */
	public InvoiceHeader getInvoiceById(Long id);

    /**
     * 根据ID获取发票头list
     * 
     * @param ids
     * @return
     */
	public List<InvoiceHeader> getInvoiceList(List<Long> ids);

    /**
     * 是否所有开票信息都绑定了打印状态的发票号码
     * 
     * @param doNo
     * @return
     */
    public boolean isALLBindedWithPrintedInv(String doNo) ;

    /**
     * 求关联的已打印状态的发票号码
     * 
     * @param doNo
     * @return
     */
    public String getBindedPrintedInvNo(InvoiceHeader invoiceHeader);
	
    /**
     * 获取发票头
     * 
     * @param doNo
     *            订单号
     * @param billNo
     *            开票序号
     * @return
     */
	public InvoiceHeader getBill(String doNo, String billNo);

    /**
     * 判断发票起止号码是否存在、是否在同一发票薄内、发票段是否存在不是未打印状态的发票号码
     * 
     * @param invoiceNoFrom
     *            起始发票号码
     * @param invoiceNoTo
     *            截止发票号码
     * @param invoiceCode
     *            发票代码
     * @return
     */
    public boolean checkInvoiceInfo(String invoiceNoFrom, String invoiceNoTo, String invoiceCode);

    /**
     * 获取当前发票打印模式
     * 
     * @return
     */
    public String getInvoiceLevel();

    /**
     * 绑定并打印发票
     * 
     * @param ids
     * @param invoiceCode
     * @param invoiceNoFrom
     * @param invoiceNoTo
     * @return
     */
    public List<String> bindAndPrint(List<Long> ids, String invoiceCode, String invoiceNoFrom, String invoiceNoTo);

    /**
     * 根据ID获取按波次ID，分拣格号排序的开票信息
     */
    public List<InvoiceHeader> getInvoiceListForPrint(List<Long> ids);
    
    /**
     * 根据订单ID集合获取对应的开票信息ID集合
     */
    public List<Long> getInvoiceHIdsByDoIds(List<Long> doIds);
    
    /**
     * 获取波次还需要的发票（排除已取消的订单的发票）
     * @param waveNo
     * @return
     */
    public List<InvoiceHeader> getInvoiceList4PrintByWave(String waveNo);
    
    /**
     * 根据idList查询发票并按波次和分拣格排序,用以打印
     * @return
     */
    public List<InvoiceHeader> getInvoiceList4Print(List<Long> idList);

    public List<InvoiceHeader> getInvoiceListByDoIds(List<Long> ids);
    
    /**
     * 查询波次需要发票数
     * @param waveNo
     * @return
     */
    public BigDecimal queryWaveInvoiceCount(String waveNo);
    
    /**
     * 保存或者更新发票信息
     * @param invoiceHeader
     */
    public void saveOrUpdate(InvoiceHeader invoiceHeader);
    
    public void saveOrUpdate(InvoiceDetail invoiceDetail);
    
    public List<InvoiceDetail> findInvoiceDetailsByHeaderId(Long invoiceHId);
    
    
    public InvoiceHeader getByReqSequenceNo(String sequenceNo);

    /**
     * 江西航天信息开票
     * @param doId
     */
    void issueInvoice(Long doId);

    /**
     * 江西航天信息发票打印
     * @param doId
     */
    void printInvoice(Long doId);
}
