package com.daxia.wms.delivery.deliveryorder.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.entity.OtherDoHeader;
import com.daxia.wms.delivery.deliveryorder.filter.OtherDoFilter;
import com.daxia.wms.delivery.deliveryorder.service.OtherDoService;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.Synchronized;

@Name("com.daxia.wms.receive.otherDoAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@Synchronized(timeout = 30000)
@lombok.extern.slf4j.Slf4j
public class OtherDoAction extends PagedListBean<OtherDoHeader> {

    @In
    private OtherDoService otherDoService;

    private OtherDoFilter otherDoFilter;

    private OtherDoHeader otherDoHeader;

    @In
    private SequenceGeneratorService sequenceGeneratorService;

    public OtherDoAction() {
        super();
        this.otherDoFilter = new OtherDoFilter();
    }

    @Override
    public void query() {
        otherDoFilter.getOrderByMap().put("createdAt", "desc");
        this.buildOrderFilterMap(otherDoFilter);
        DataPage<OtherDoHeader> dataPage = otherDoService.queryDoHeaderPageInfo(otherDoFilter,
                getStartIndex(), getPageSize());
        this.populateValues(dataPage);
    }

    public void preAdd() {
        this.setOperation(OPERATION_ADD);
        this.otherDoHeader = new OtherDoHeader();
        String doNo = sequenceGeneratorService.generateSequenceNo(Constants.SequenceName.OTHER_DO_NO.getValue(), ParamUtil.getCurrentWarehouseId());
        otherDoHeader.setDoNo(doNo);
        otherDoHeader.setRefNo1(doNo);
    }


    public void addOrUpdate() {
        this.setOperationStatus(-1);
        if (OPERATION_ADD.equals(this.getOperation())) {
            otherDoService.saveOrUpdate(otherDoHeader);
            dataPage.getDataList().add(0, otherDoHeader);
        } else {
            otherDoService.saveOrUpdate(otherDoHeader);
            int rowNum = ListUtil.getPosFromListIdValue(dataPage.getDataList(), otherDoHeader.getId(), "id");
            dataPage.getDataList().remove(rowNum);
            dataPage.getDataList().add(rowNum, otherDoHeader);
        }
        query();
        this.setOperationStatus(1);
        this.sayMessage(MESSAGE_SUCCESS);
    }

    public OtherDoFilter getOtherDoFilter() {
        return otherDoFilter;
    }

    public void setOtherDoFilter(OtherDoFilter otherDoFilter) {
        this.otherDoFilter = otherDoFilter;
    }

    public OtherDoHeader getOtherDoHeader() {
        return otherDoHeader;
    }

    public void setOtherDoHeader(OtherDoHeader otherDoHeader) {
        this.otherDoHeader = otherDoHeader;
    }
}
