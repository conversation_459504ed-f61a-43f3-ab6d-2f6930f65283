package com.daxia.wms.delivery.recheck.service;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.OrderFrozenException;
import com.daxia.wms.delivery.OrderNotInReCheckStatusException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.dto.PackMaterialInfoDto;
import com.daxia.wms.delivery.recheck.dto.ReCheckCartonInfo;
import com.daxia.wms.delivery.recheck.dto.ReCheckRecord;
import com.daxia.wms.delivery.recheck.dto.RecheckDetailInfoDTO;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.filter.CartonHeaderFilter;
import com.daxia.wms.stock.stock.entity.StockSerial;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 核拣功能Service接口定义
 */
public interface ReCheckService {
    /**
     * 批量关务放行事件
     */
    String BATCH_CUSTOMS_GOODS_PASS_EVENT = "BATCH_CUSTOMS_GOODS_PASS_EVENT";
    /**
     * 关务放行事件
     */
    String CUSTOMS_GOODS_PASS_EVENT = "CUSTOMS_GOODS_PASS_EVENT";

    /**
     * 核拣装箱自动拆箱生成新的装箱头并绑定do
     * 
     * @param doHeaderId
     * @param qty
     * @param packMaterialNos
     *            包材字符串
     * @param needAutoDelivery
     * @param recheckBy
     */
    void departNewCartons(Long doHeaderId, Integer qty, String packMaterialNos, Boolean needAutoDelivery,
        String recheckBy);

    /**
     * 判断订单是否处于可以核拣的状态
     *
     * @param doHeader
     * @return
     */
    boolean isOrderInReCheckStatus(DeliveryOrderHeader doHeader, String recheckType, Integer pkType);

    List<ReCheckRecord> getReCheckRecord4Pallet(Long doId, String packageType);

    /**
     * 查询发货单的核拣情况
     *
     * @param doId
     *            发货单Id号
     * @return 发货单的已核拣和待核拣的信息
     */
    List<ReCheckRecord> getReCheckRecord(Long doId, String packageType);

    /**
     *
     * @param containerNo
     * @param doId
     * @return
     */
    List<ReCheckRecord> getReCheckRecordByContainer(String containerNo, Long doId);

    Boolean isFinishedPack(DeliveryOrderHeader doHeader, String packageType);

    /**
     * 查询发货单已装箱数量
     *
     * @param doId
     * @return
     */
    int countPackedNumber(Long doId);

    /**
     * 查询发货单已装箱箱号
     *
     * @param doId
     * @return
     */
    List<String> findCartonNoByDoId(Long doId, String packageType);

    /**
     * 完成一箱商品的核拣，对记录进行保存
     *
     * @param recheckCartonInfo
     *            完成核拣的一箱记录
     * @throws OrderFrozenException
     *             定单处于冻结状态时抛出该异常
     * @throws OrderNotInReCheckStatusException
     *             如果定不能核拣的状态，则抛出该异常
     */
    String saveOneCartonReCheckRecord(ReCheckCartonInfo recheckCartonInfo)
        throws OrderFrozenException, OrderNotInReCheckStatusException;

    /**
     * 根据装箱头过滤器查询
     *
     * @param cartonHeaderFilter
     *            装箱头过滤器
     * @return
     */
    List<ReCheckCartonInfo> selectCartonsByFilter(CartonHeaderFilter cartonHeaderFilter, boolean queryMode);
    /**
     * 根据装箱头过滤器查询
     *
     * @param cartonHeaderFilter
     *            装箱头过滤器
     * @return
     */
    List<CartonHeader> selectCartonHeaderByFilter(CartonHeaderFilter cartonHeaderFilter);

    /**
     * 根据箱号来进行查询
     *
     * @param cartonNo
     * @param queryHistory
     *            true：查询历史数据 false:查询正常数据
     * @return
     */
    ReCheckCartonInfo selectCarton(String cartonNo, Boolean queryHistory);

    /**
     * 定单是否处于冻结状态
     *
     * @param doHeader
     * @return
     */
    boolean isFrozen(DeliveryOrderHeader doHeader);

    /**
     * 根据序列号查询StockSerial
     *
     * @param serialNo
     * @return
     */
    StockSerial findStockSerial(String serialNo, Long merchantId);

    /**
     * 根据序列号查询StockSerial
     *
     * @param serialNo
     * @return
     */
    List<StockSerial> findStockSerial(List<String> serialNo, Long merchantId);

    /**
     * 检查发货单下是否存在指定的sku
     *
     * @param doId
     * @param skuId
     * @return 存在时返回true，不存在时返回false
     */
    boolean isDoContainsSku(Long doId, Long skuId);

    /**
     * 核拣缺货
     *
     * @param doNo
     * @return
     */
    String recheckLack(String doNo) throws DeliveryException;

    /**
     * do下的skuId及已经装箱的数量
     *
     * @param doId
     * @return do下的skuId及已经装箱的数量
     */
    Map<Long, BigDecimal> findPackedNumber(Long doId, String packageType);

    /**
     * 核拣破损按指定SKU
     *
     * @param doHeaderId
     * @param skuId
     * @return
     */
    boolean dmBySku(Long doHeaderId, Long skuId);

    /**
     * 查询波次中有指定商品的最早预计出库时间的发货单
     *
     * @param waveId
     * @param skuId
     * @param status
     * @return
     */
    DeliveryOrderHeader getEstDoInWaveBySku(Long waveId, Long skuId, String status);

    /**
     * 根据DO下商品总数核拣(不包含捆绑商品)
     *
     * @param doHeaderId
     * @param totalQty
     * @param recheckBy
     */
    void recheckBySkuCount(Long doHeaderId, BigDecimal totalQty, String materialNo, Boolean needAutoDelivery,
        String recheckBy);

    /**
     * 微便利核检装箱
     */
    void saveOneCarton4WBL(Long doId);

    /**
     * 更新DO的装箱头重量
     *
     * @param doId
     * @param newWeight
     */
    void updateDoPackWeight(Long doId, BigDecimal newWeight);

    /**
     * 获取包裹重量差异标准
     */
    BigDecimal getPackWeightGap();

    /**
     * 包材推荐
     *
     * @param volume
     * @return
     */
    Map<String, Integer> recommendMaterial(BigDecimal volume);

    /**
     * 验证包材
     *
     * @param packMaterialNo
     * @param needCount
     */
    List<String> checkPackMaterial(String packMaterialNo, int needCount);

    /**
     * 按DTO格式返回包材推荐信息
     *
     * @param packMaterialCoutMap
     * @return
     */
    PackMaterialInfoDto buildPackMaterialDto(Map<String, Integer> packMaterialCoutMap);

    /**
     * 验证波次下DO是否都核拣完成 非冻结订单，状态装箱完成；冻结订单，绑定过分拣筐，否则视为波次未完结
     *
     * @param waveNo
     * @return
     */
    boolean isWaveCartoned(String waveNo);

    /**
     * 查询波次下未核拣完成的DO数
     *
     * @param waveId
     * @return
     */
    BigDecimal queryNotCartonedDoInWave(Long waveId);

    /**
     * 根据订单查询箱及箱的SKU
     *
     * @param id
     *            订单ID
     * @return Map<cartongHeaderId,List<skuId>>
     */
    Map<Long, List<Long>> findCartonSkuMap(Long id);

    void updateCartonAutoDeliveryFlagByDoId(Long id);

    /**
     * 对核捡记录按照未核减数量降序排序
     */
    void sortRecordsByNotPackedQty(List<ReCheckRecord> records);

    /**
     * 京东箱号因为带有总箱数和序号，所以在装箱完成的时候需要重新获取运单号拼装箱号。
     *
     * @param doHeaderId
     */
    void resetJDCartonNo(Long doHeaderId);

    /**
     * 快速核检
     *
     * @param waveId
     * @param materials
     */
    void quickRecheckByWave(Long waveId, String materials);

    /**
     * 快速核检带内置耗材
     * @param waveId
     * @param materials
     * @param innerMaterials
     *
     */
    void quickRecheckByWaveWithInnerMaterials(Long waveId, String materials,String innerMaterials);

    List<Long> recheckByWave(Long id, String qty, boolean autoDeliveryMode, String recheckBy2);

    /**
     * 根据核减记录做缺发发货操作
     *
     * @param reCheckRecords
     * @return
     */
    void loadRecheckLack(List<ReCheckRecord> reCheckRecords, Integer pkType);

    DataPage<CartonHeader> selectCartonsInfoByFilter(CartonHeaderFilter cartonHeaderFilter, int startIndex,
        int pageSize);

    /**
     * 查询一条箱信息
     * @param cartonHeaderFilter
     * @return
     */
    CartonHeader selectOneCartonInfoByFilter(CartonHeaderFilter cartonHeaderFilter);

    List<RecheckDetailInfoDTO> selectCartonsDetailInfoByCartonIds(List<CartonHeader> CartonHeaderList);

    /**
     * 判断订单是否
     * 
     * @param doHeader
     * @return
     */
    Boolean isPacked(DeliveryOrderHeader doHeader, String packageType);

    String resetDoStatusForEmpty(String orderNo, String packageType, String recheckType);

    Map<String, String> getDetailNotes(Long doHeaderId);

    String createCrossCarton(String doNo, Map<Long, BigDecimal> detailMap, Integer isLackRecheck);

    void quickCtnRecheck(Long doId);

    Boolean resetCrossDoStatus(DeliveryOrderHeader doHeader, Integer isLackRecheck);

    /**
     * 货物放行业务处理
     *
     * @param cartonNo
     *            箱号
     */
    void doGoodsPass(String cartonNo);
    /**
     * 货物放行业务处理
     *
     * @param cartonId 箱id
     * @param doNo 订单号
     */
    void doGoodsPass(Long cartonId, String doNo);

    /**
     * 查询待货物放行的箱号
     * @param batchSize
     * @param warehouseId
     * @return 返回格式 [ [carton_no,do_no] ,... ]
     */
    List<Object[]> findNeedPassCartonNoList(Integer batchSize, Long warehouseId);

    /**
     * 货物放行业务处理
     * 
     * @param cartonNo
     *            箱号
     * @param warehouseId
     *            仓库id
     */
    void doGoodsPass(String cartonNo, Long warehouseId);

    /**
     * 按波次进行货物放行业务处理
     *
     * @param waveNo
     *            波次号 箱号
     * @param warehouseId
     *            仓库id
     */
    void doGoodsPassByWave(String waveNo, Long warehouseId);
}
