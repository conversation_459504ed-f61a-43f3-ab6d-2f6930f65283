package com.daxia.wms.delivery.deliveryorder.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.BatchSize;

import com.daxia.framework.common.entity.WhBaseEntity;

@Entity
@Table(name = "md_special_do_label")
@org.hibernate.annotations.Entity
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class SpecialDoLabel extends WhBaseEntity {

    private static final long serialVersionUID = 7526627853897805644L;

    private String labelCode;
    
    private String labelName;

    //是否可索取
    private Boolean requestEnabled;

    //断定mvel脚本
    private String judgeScript;

    //断定SQL

    //对应波次细分类型
    private Integer waveDetailType;
    
    private Boolean available;
    private Integer sortNumber;

    @Id
    @Column(name = "LABEL_CODE")
    public String getLabelCode() {
        return labelCode;
    }

    public void setLabelCode(String labelCode) {
        this.labelCode = labelCode;
    }

    @Column(name = "JUDGE_SCRIPT")
    public String getJudgeScript() {
        return judgeScript;
    }

    public void setJudgeScript(String judgeScript) {
        this.judgeScript = judgeScript;
    }

    @Column(name = "WAVE_DETAIL_TYPE")
    public Integer getWaveDetailType() {
        return waveDetailType;
    }

    public void setWaveDetailType(Integer waveDetailType) {
        this.waveDetailType = waveDetailType;
    }

    @Column(name = "REQUEST_ENABLED")
    public Boolean getRequestEnabled() {
        return requestEnabled;
    }

    public void setRequestEnabled(Boolean requestEnabled) {
        this.requestEnabled = requestEnabled;
    }

    @Column(name = "AVAILABLE")
    public Boolean getAvailable() {
        return available;
    }

    public void setAvailable(Boolean available) {
        this.available = available;
    }

    @Column(name = "LABEL_NAME")
    public String getLabelName() {
        return labelName;
    }

    public void setLabelName(String labelName) {
        this.labelName = labelName;
    }
    @Column(name = "SORT_NUMBER")
    public Integer getSortNumber() {
        return sortNumber;
    }
    public void setSortNumber(Integer sortNumber) {
        this.sortNumber = sortNumber;
    }
}