package com.daxia.wms.delivery.deliveryorder.service;

import java.util.List;
import java.util.Set;

import com.daxia.wms.delivery.deliveryorder.entity.DoLackHeader;

public interface DoLackHeaderService {
    
    /**
     * 根据发货单查询缺货头
     * @param doHeaderId
     * @return
     */
    public DoLackHeader getByDoId(Long doHeaderId);
    
    /**
     * 新增或更新找货头 库区编码唯一按分号分割
     * @param doHeaderId
     * @param lackPartitions
     */
    public void addOrUpdate(Long doHeaderId, String lackPartition);
    
    /**
     * 新增或更新找货头 库区编码唯一按分号分割
     * @param doHeaderId
     * @param lackPartitions
     */
    public void addOrUpdate(Long doHeaderId, Set<String> lackPartitions);
    
    /**
     * 清除发货单的缺货信息
     * @param doId
     */
    public void deleteLackInfoByDoId(Long doId);
    
    /**
     * 更新缺货头生成状态
     * @param doIds
     * @param stauts
     */
    public void updateCreateStatus(List<Long> doIds, String stauts);
    
    /**
     * 根据发货单更新其缺货头状态
     * @param doId
     * @param stauts
     */
    public void updateCreateStatusByDoId(Long doId, String stauts);
}
