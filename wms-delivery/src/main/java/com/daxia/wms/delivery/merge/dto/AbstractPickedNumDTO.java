package com.daxia.wms.delivery.merge.dto;

import java.math.BigDecimal;

@lombok.extern.slf4j.Slf4j
public class AbstractPickedNumDTO {

    // 未拣货数量（不包含缺货的）
    private BigDecimal unPickedNum;

    // 未拣货数量页面显示百分比
    private Integer unPickedPercent = 33;

    // 已拣货数量
    private BigDecimal pickedNum;

    // 已拣货数量页面显示百分比
    private Integer pickedPercent = 34;

    // 缺货未拣数量；
    private BigDecimal lackUnpickedNum;

    // 缺货未拣数量；页面显示百分比
    private Integer lackUnpickedPercent = 33;

    public BigDecimal getUnPickedNum() {
        return unPickedNum;
    }

    public void setUnPickedNum(BigDecimal unPickedNum) {
        this.unPickedNum = unPickedNum;
    }

    public Integer getUnPickedPercent() {
        return unPickedPercent;
    }

    public void setUnPickedPercent(Integer unPickedPercent) {
        this.unPickedPercent = unPickedPercent;
    }

    public BigDecimal getPickedNum() {
        return pickedNum;
    }

    public void setPickedNum(BigDecimal pickedNum) {
        this.pickedNum = pickedNum;
    }

    public Integer getPickedPercent() {
        return pickedPercent;
    }

    public void setPickedPercent(Integer pickedPercent) {
        this.pickedPercent = pickedPercent;
    }

    public BigDecimal getLackUnpickedNum() {
        return lackUnpickedNum;
    }

    public void setLackUnpickedNum(BigDecimal lackUnpickedNum) {
        this.lackUnpickedNum = lackUnpickedNum;
    }

    public Integer getLackUnpickedPercent() {
        return lackUnpickedPercent;
    }

    public void setLackUnpickedPercent(Integer lackUnpickedPercent) {
        this.lackUnpickedPercent = lackUnpickedPercent;
    }

}
