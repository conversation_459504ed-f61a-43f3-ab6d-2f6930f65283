package com.daxia.wms.delivery.container.dao;

import java.util.List;

import com.daxia.wms.Constants;
import com.daxia.wms.OrderLogConstants;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.container.entity.ContainerLog;


/**
 * 容器使用日志DAO
 */
@Name("com.daxia.wms.delivery.containerLogDAO")
@lombok.extern.slf4j.Slf4j
public class ContainerLogDAO extends HibernateBaseDAO<ContainerLog , Long> {

    private static final long serialVersionUID = 1L;
    
    /**
     * 查询指定波次下的容器日志
     * @param waveId
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<ContainerLog> queryLogsByWave(Long waveId) {
        String hql = "select o from ContainerLog o, WaveHeader wh where wh.id = :waveId and o.docNo = wh.waveNo "
                + " and o.warehouseId = :warehouseId and wh.warehouseId = :warehouseId "
                + " order by o.operationTime desc";
        Query query = this.createQuery(hql);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("waveId", waveId);
        return query.list();
    }
    
    /**
     * 获取最后一次绑定的单据号
     * @param containerNo
     * @param docType
     * @return
     */
    public String getLaskBindDocNo(String containerNo, String docType) {
        String hql = "select o.docNo from ContainerLog o "
                + " where o.containerNo = :containerNo and o.docType = :docType and o.operation = :operation and o.warehouseId = :warehouseId "
                + " order by o.createdAt desc";
        Query query = this.createQuery(hql).setParameter("containerNo", containerNo)
                .setParameter("docType", docType).setParameter("operation",Constants.ContainerOperationType.BIND.getValue())
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (String) query.uniqueResult();
    }
    
    /**
     * 按单据号查询容器日志
     * @param docNo
     * @param opType
     * @param docType
     * @return
     */
    @SuppressWarnings("unchecked")
	public List<ContainerLog> queryLogsByDocNo(String docNo, String opType, String docType) {
    	String hql = "from ContainerLog o where o.docNo = :docNo and o.operation = :operation "
    			+ "and o.docType = :docType and o.warehouseId = :warehouseId";
    	Query query = this.createQuery(hql).setParameter("docNo", docNo)
                .setParameter("operation", opType)
                .setParameter("docType", docType)
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return  query.list();
    }
}
