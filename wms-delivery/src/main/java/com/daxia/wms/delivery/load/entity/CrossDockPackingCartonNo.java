package com.daxia.wms.delivery.load.entity;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;

/**
 * 越库装箱箱号类
 */
@Entity
@Table(name = "doc_crdock_packing_cartonno")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@BatchSize(size = 20)
@Where(clause = "IS_DELETED = 0")
@lombok.extern.slf4j.Slf4j
public class CrossDockPackingCartonNo extends WhBaseEntity {

	private static final long serialVersionUID = 881172018920148329L;

	// 主键
	private Long id;
	
	// 越库装箱实体ID
	private Long cdPackingId;
	
	private CrossDockPacking crossDockPacking;

	// 箱号
	private String cartonNo;

	@Id
	@Column(name = "ID")
	@GeneratedValue(strategy = GenerationType.AUTO)  
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "CD_PACKING_ID")
	public Long getCdPackingId() {
		return cdPackingId;
	}

	public void setCdPackingId(Long cdPackingId) {
		this.cdPackingId = cdPackingId;
	}
	
	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CD_PACKING_ID", insertable = false, updatable = false)
	@Where(clause = " IS_DELETED = 0 ")
	public CrossDockPacking getCrossDockPacking() {
		return crossDockPacking;
	}

	public void setCrossDockPacking(CrossDockPacking crossDockPacking) {
		this.crossDockPacking = crossDockPacking;
	}

	@Column(name = "CARTON_NO")
	public String getCartonNo() {
		return cartonNo;
	}

	public void setCartonNo(String cartonNo) {
		this.cartonNo = cartonNo;
	}
}
