package com.daxia.wms.delivery.load.entity;

import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;

/**
 * 逾期退单管理
 */
@Entity
@Table(name = "doc_overdue_rtv")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = "IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_overdue_rtv set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class OverdueRtv extends WhBaseEntity {

	private static final long serialVersionUID = -1409733884436099830L;

	private Long id;

	// RTV单号
	private String rtvNo;

	// 来源单号
	private String sourceNo;

	// 商品编码
	private String productCode;

	// 商品条码
	private String productBarCode;

	// 商品名称
	private String prodctCName;

	// 一级分类
	private String categoryLevel1;

	// 二级分类
	private String categoryLevel2;

	// 是否坏品
	private String isDamage;

	// 应退数量
	private BigDecimal retQty;

	// 实退数量
	private BigDecimal actQty;

	// 供应商名称
	private String supplierName;

	// 出库日期
	private Timestamp deliveryTime;

	// 状态
	private String status;

	// 处理方式
	private String dealWay;

	// 备注
	private String notes;

	@Id
	@Column(name = "ID")
	@GeneratedValue(strategy = GenerationType.AUTO)  
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "RTV_NO")
	public String getRtvNo() {
		return rtvNo;
	}

	public void setRtvNo(String rtvNo) {
		this.rtvNo = rtvNo;
	}

	@Column(name = "SOURCE_NO")
	public String getSourceNo() {
		return sourceNo;
	}

	public void setSourceNo(String sourceNo) {
		this.sourceNo = sourceNo;
	}

	@Column(name = "PRODUCT_CODE")
	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	@Column(name = "PRODUCT_BARCODE")
	public String getProductBarCode() {
		return productBarCode;
	}

	public void setProductBarCode(String productBarCode) {
		this.productBarCode = productBarCode;
	}

	@Column(name = "PRODUCT_CNAME")
	public String getProdctCName() {
		return prodctCName;
	}

	public void setProdctCName(String prodctCName) {
		this.prodctCName = prodctCName;
	}

	@Column(name = "CATEGORY_LEVEL1")
	public String getCategoryLevel1() {
		return categoryLevel1;
	}

	public void setCategoryLevel1(String categoryLevel1) {
		this.categoryLevel1 = categoryLevel1;
	}

	@Column(name = "CATEGORY_LEVEL2")
	public String getCategoryLevel2() {
		return categoryLevel2;
	}

	public void setCategoryLevel2(String categoryLevel2) {
		this.categoryLevel2 = categoryLevel2;
	}

	@Column(name = "IS_DAMAGE")
	public String getIsDamage() {
		return isDamage;
	}

	public void setIsDamage(String isDamage) {
		this.isDamage = isDamage;
	}

	@Column(name = "RET_QTY")
	public BigDecimal getRetQty() {
		return retQty;
	}

	public void setRetQty(BigDecimal retQty) {
		this.retQty = retQty;
	}

	@Column(name = "ACT_QTY")
	public BigDecimal getActQty() {
		return actQty;
	}

	public void setActQty(BigDecimal actQty) {
		this.actQty = actQty;
	}

	@Column(name = "SUPPLIER_NAME")
	public String getSupplierName() {
		return supplierName;
	}

	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}

	@Column(name = "DELIVERY_TIME")
	public Timestamp getDeliveryTime() {
		return deliveryTime;
	}

	public void setDeliveryTime(Timestamp deliveryTime) {
		this.deliveryTime = deliveryTime;
	}

	@Column(name = "STATUS")
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "DEAL_WAY")
	public String getDealWay() {
		return dealWay;
	}

	public void setDealWay(String dealWay) {
		this.dealWay = dealWay;
	}

	@Column(name = "NOTES")
	public String getNotes() {
		return notes;
	}

	public void setNotes(String notes) {
		this.notes = notes;
	}
}
