package com.daxia.wms.delivery.load.dao;

import java.util.Date;
import java.util.List;

import com.daxia.wms.Constants;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.stock.stock.entity.TrsShipLog;

@Name("trsShipLogDAO")
@lombok.extern.slf4j.Slf4j
public class TrsShipLogDAO  extends HibernateBaseDAO<TrsShipLog, Long> {
	private static final long serialVersionUID = -1115643492909409784L;

	@SuppressWarnings("unchecked")
	public List<TrsShipLog> queryTrsShipLogByDoNo(String docNo) {
		String hql = "SELECT o FROM TrsShipLog o, Warehouse w WHERE o.docNo = :docNo AND o.warehouseId = w.id AND w.tenantId = :tenantId";
		Query query = createQuery(hql);
		query.setParameter("docNo", docNo);
		query.setParameter("tenantId", ParamUtil.getCurrentTenantId());
		return (List<TrsShipLog>) query.list();
	}

	public List<TrsShipLog> queryTrsShipLogHisByDoNo(String docNo) {
		String hql = "SELECT o.* FROM trs_ship_log_his o " +
				"where o.doc_no = :docNo AND o.warehouse_id = :warehouseId";
		SQLQuery query = this.createSQLQuery(hql);
		query.setParameter("docNo", docNo);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.addEntity("o", TrsShipLog.class);
		return (List<TrsShipLog>)query.list();
	}

	@SuppressWarnings("unchecked")
	public List<Object[]> queryTrsShipLogByCreateTime(Date createTimeFrom, Date createTimeTo) {
		StringBuilder hql = new StringBuilder("from TrsShipLog trs,DeliveryOrderDetail doDetail ")
			.append(" where trs.docLineId = doDetail.id and trs.createdAt >= :createTimeFrom and trs.createdAt <= :createTimeTo")
			.append(" and trs.warehouseId = :warehouseId")
			.append(" and doDetail.haveCfy = :haveCfy")
			.append(" and doDetail.cfyHandleFlag = :cfyHandleFlag");
		Query query = createQuery(hql.toString());
		query.setTimestamp("createTimeFrom", createTimeFrom);
		query.setTimestamp("createTimeTo", createTimeTo);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameter("haveCfy", Constants.YesNo.YES.getValue());
		query.setParameter("cfyHandleFlag", Constants.YesNo.NO.getValue());

		return (List<Object[]>)query.list();
	}
}
