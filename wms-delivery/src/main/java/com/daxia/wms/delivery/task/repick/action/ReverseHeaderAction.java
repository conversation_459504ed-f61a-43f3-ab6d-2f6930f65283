package com.daxia.wms.delivery.task.repick.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;

import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.task.repick.entity.ReversePickHeader;
import com.daxia.wms.delivery.task.repick.filter.ReversePickHeaderFilter;
import com.daxia.wms.delivery.task.repick.service.RePickTaskService;
import com.daxia.wms.delivery.task.repick.service.ReversePickHeaderService;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.service.LocationService;
import com.daxia.wms.stock.StockException;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.util.List;

/**
 * Description:返拣单头业务Action
 */
@Name("com.daxia.wms.delivery.reverseHeaderAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ReverseHeaderAction extends PagedListBean<ReversePickHeader> {

    private static final long serialVersionUID = 3011217429923470775L;

    @In
    private ReversePickHeaderService reversePickHeaderService;
    private ReversePickHeaderFilter reversePickHeaderFilter;
    private Long partitionId;//库区

    @In
    LocationService locationService;

    private String locCode;
    private Long locId;

    @In
    private RePickTaskService rePickTaskService;

    public ReverseHeaderAction() {
        super();
        this.reversePickHeaderFilter = new ReversePickHeaderFilter();
        reversePickHeaderFilter.setRpStatus(Constants.TaskStatus.RELEASED.getValue());
    }

    /**
     * 查询反拣单头信息分页信息
     */
    @Override
    public void query() {
    	this.reversePickHeaderFilter.getOrderByMap().put("createdAt","desc");
        this.buildOrderFilterMap(reversePickHeaderFilter);
        DataPage<ReversePickHeader> dataPage = this.reversePickHeaderService.query(
            reversePickHeaderFilter, this.getStartIndex(), this.getPageSize());
        this.populateValues(dataPage);
    }

    /**
    * 新增反拣单
    */
    @Loggable
    public void save() {
        this.reversePickHeaderService.saveReversePickHeader(this.partitionId);
        this.query();
        this.sayMessage(MESSAGE_SUCCESS);
    }

    public void oneKeyRePick() {
        List<Long> reverseHeaderIds = ListUtil.convert(this.getSelectedRowList(), new ListUtil.Convertor<Object, Long>() {
            @Override
            public Long convert(Object id) {
                return (Long) id;
            }
        });

        if (ListUtil.isNullOrEmpty(reverseHeaderIds)) {
            return;
        }
        rePickTaskService.batchExecuteRePick(reverseHeaderIds,locCode);
        clearSelectLocation();
        this.query();
        this.sayMessage(MESSAGE_SUCCESS);
    }

    public ReversePickHeaderFilter getReversePickHeaderFilter() {
        return reversePickHeaderFilter;
    }

    public void setReversePickHeaderFilter(ReversePickHeaderFilter reversePickHeaderFilter) {
        this.reversePickHeaderFilter = reversePickHeaderFilter;
    }

    public Long getPartitionId() {
        return partitionId;
    }

    public void setPartitionId(Long partitionId) {
        this.partitionId = partitionId;
    }

    /**
     * 设置库位输入框信息
     * @param locationId
     */
    public void receiveSelectLocation(Long locationId) {
        Location selLocation = locationService.get(locationId);
        if (selLocation == null || Constants.LocType.DM.getValue().equals(selLocation.getLocType())) {
            throw new StockException(StockException.CHECK_MOVE_TASK_LOCATION_IS_DAMAGE);
        }
        this.locCode = selLocation.getLocCode();
        this.locId = selLocation.getId();
    }

    /**
     * 清空库位输入框信息
     */
    public void clearSelectLocation() {
        this.locCode = null;
    }

    public String getLocCode() {
        return locCode;
    }

    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }
}
