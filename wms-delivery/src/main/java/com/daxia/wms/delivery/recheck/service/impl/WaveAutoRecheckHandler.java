package com.daxia.wms.delivery.recheck.service.impl;

import java.util.List;

import org.jboss.seam.annotations.In;

import com.daxia.framework.common.util.ListUtil;
import com.daxia.wms.delivery.recheck.service.ReCheckService;
import com.daxia.wms.delivery.wave.service.WaveAutoHandler;

@lombok.extern.slf4j.Slf4j
public class Wave<PERSON>utoRecheckHandler extends WaveAutoHandler {
	@In
	private ReCheckService reCheckService;
	
	@Override
	protected void handle(Long waveId) {
		List<Long> doIds = deliveryOrderService.qureyDoHeaderIdsByWaveId(waveId);
		if (ListUtil.isNotEmpty(doIds)) {
			for (Long doId : doIds) {
				reCheckService.saveOneCarton4WBL(doId);
			}
		}
	}

	@Override
	protected void setName() {
		this.name = "waveAutoRecheckHandler";
	}
}
