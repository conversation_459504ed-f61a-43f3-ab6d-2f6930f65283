package com.daxia.wms.delivery.print.service.carton;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.framework.system.util.MD5Util;
import com.daxia.wms.Constants.WaybillType;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.print.dto.BaseCartonPrintDTO;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.helper.PrintCartonHelper;
import com.daxia.wms.delivery.print.helper.SFPrintCartonHelper;
import com.daxia.wms.delivery.print.service.PrintWaybillService;
import com.daxia.wms.delivery.recheck.entity.CartonDetail;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.print.dto.PrintCfg;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.print.utils.PrintHelper;
import com.daxia.wms.waybill.util.XStreamHandler;
import com.daxia.wms.waybill.zjs.dto.ZjsB2CInfoDetail;
import com.daxia.wms.waybill.zjs.dto.ZjsB2CInfoRequest;
import com.daxia.wms.waybill.zjs.dto.ZjsB2CInfoResponse;
import com.daxia.wms.waybill.zjs.util.ZjsUtil;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.List;

@Name("printZJSWaybillService")
@lombok.extern.slf4j.Slf4j
public class PrintZJSWaybillServiceImpl extends AbstractPrintWaybillService implements PrintWaybillService {
    @In
    private WarehouseService warehouseService;
    @In
    WarehouseCarrierService warehouseCarrierService;
    @In
    CartonService cartonService;

    @Create
    public void init () {
        this.setWaybillType(WaybillType.SF);
    }

    @Override
    protected void buildDTO(List<PrintReportDto> printReportDtos, DeliveryOrderHeader doHeader, BaseCartonPrintDTO carton, int index, int count) {
        CartonPrintDTO cartonPrintDTO = new CartonPrintDTO();
        cartonPrintDTO.setIsPrinted(carton.getIsPrinted());
        cartonPrintDTO.setCartonId(carton.getId());
        cartonPrintDTO.setCartonNo(carton.getCartonNo());
        cartonPrintDTO.setDoNo(doHeader.getDoNo());
        cartonPrintDTO.setOutRefNo(doHeader.getRefNo1());
        cartonPrintDTO.setIsCOD(PrintCartonHelper.isCOD(doHeader));
        cartonPrintDTO.setSortGridNo(doHeader.getSortGridNo());
        cartonPrintDTO.setWaveNo(doHeader.getWaveHeader().getWaveNo());
        cartonPrintDTO.setOriginalSoCode(doHeader.getOriginalSoCode());
        // 运费到付
        BigDecimal skuUnit = cartonService.sumSkuUnit(carton.getId());
        cartonPrintDTO.setSkuUnitQty(skuUnit);

        // 设置收货人地址
        cartonPrintDTO.setClientProvinceAndCityAndCountyAddress(SFPrintCartonHelper.buildProvinceAndCityAndCountyAddress(doHeader,"  "));
        cartonPrintDTO.setClientAddress(StringUtil.notNullString(doHeader.getAddress()));
        cartonPrintDTO.setClientName(SFPrintCartonHelper.buildConsigneeName(doHeader));
        cartonPrintDTO.setClientPhone(PrintCartonHelper.buildTelOrMobile(doHeader));
        // 设置寄件人地址信息
        setSendAddressInfo(doHeader,cartonPrintDTO);
        // 运单号
        cartonPrintDTO.setWayBill(carton.getCartonNo());
        // 设置代收金额
        cartonPrintDTO.setServiceCodAmount(doHeader.getReceivable().toString());
        // 设置图片路径
        cartonPrintDTO.setBasePrintImgPath(PrintHelper.getBasePrintImgPath());
        // 设置配送商的信息
        WarehouseCarrier warehouseCarrier = warehouseCarrierService.getCarrierInfoByWarehouseIdAndCarrierId(ParamUtil.getCurrentWarehouseId(), doHeader.getCarrierId());
        getZjsPrintInfo(carton,doHeader,warehouseService.getLocalWarehouse(),cartonPrintDTO,warehouseCarrier);

        cartonPrintDTO.setExt2(warehouseCarrier.getExt2());
        cartonPrintDTO.setExt3(warehouseCarrier.getExt3());

        Integer orderNum = SystemConfig.getConfigValueInt("fee.receiverPay.unit.number", ParamUtil.getCurrentWarehouseId());
        if (orderNum != null && doHeader.getExpectedQty().intValue() < orderNum) {
            cartonPrintDTO.setPaymentMethod("运费到付");
        }

        //设置备注
        cartonPrintDTO.setRemark("");
        cartonPrintDTO.setSkuUnitQty(getUnitQtyInCarton(doHeader,carton.getCartonNo()));
        cartonPrintDTO.setNotes(doHeader.getNotes());
        setProductInfo(doHeader, cartonPrintDTO);
        printReportDtos.add(cartonPrintDTO);
    }

    @Override
    protected PrintData genPrintData(List<PrintReportDto> dtoList, DeliveryOrderHeader doHeader) {
        PrintData printData = new PrintData();
        printData.setDtoList(dtoList);
        printData.setPrintCfg(generateSFPrintCfg());
        return printData;
    }

    private PrintCfg generateSFPrintCfg() {
        PrintCfg config = new PrintCfg("wayBillZJS", "100", "180");
        this.setPrintCfg(config);
        return config;
    }

    private void getZjsPrintInfo(BaseCartonPrintDTO cartonHeader, DeliveryOrderHeader doHeader, Warehouse warehouse,
                              CartonPrintDTO cartonPrintDTO,WarehouseCarrier warehouseCarrier){
        ZjsB2CInfoRequest zjsB2CInfoRequest = new ZjsB2CInfoRequest();
        zjsB2CInfoRequest.setClientFlag(warehouseCarrier.getExt1());
        ZjsB2CInfoDetail zjsB2CInfoDetail = new ZjsB2CInfoDetail();
        zjsB2CInfoDetail.setOrderCode(cartonHeader.getCartonNo());
        zjsB2CInfoDetail.setrAddress(SFPrintCartonHelper.buildProvinceAndCityAndCountyAddress(doHeader,""));
        zjsB2CInfoDetail.setsAddress(warehouse.getProvince().getProvinceCname() + warehouse.getCity().getCityCname() + warehouse.getCounty().getCountyCname() + warehouse.getAddressName());
        zjsB2CInfoRequest.setEi(zjsB2CInfoDetail);

        String b2cXml = XStreamHandler.toXML(zjsB2CInfoRequest);
        String responseXml = null;
        try
        {
            responseXml = ZjsUtil.getB2CInfos(b2cXml, MD5Util.md5(b2cXml));
        } catch (Exception e) {
            return;
        }

        if (StringUtil.isBlank(responseXml)) {
            return;
        }
        ZjsB2CInfoResponse b2CInfoResponse = new ZjsB2CInfoResponse();

        try
        {
            XStreamHandler.fromXML(responseXml,b2CInfoResponse);
            if (b2CInfoResponse.getResult().isSuccess()) {
                cartonPrintDTO.setPostCode(b2CInfoResponse.getResult().getPostCode());
                cartonPrintDTO.setShipmentWay(b2CInfoResponse.getResult().getShipmentWay());
                cartonPrintDTO.setUnitCode(b2CInfoResponse.getResult().getUnitCode());
            }
        } catch (Exception e) {
            return;
        }
    }
    private void setProductInfo(DeliveryOrderHeader doHeader, CartonHeader carton, CartonPrintDTO cartonPrintDTO) {
        List<CartonDetail> detailList = carton.getCartonDetails();
        if (CollectionUtils.isNotEmpty(detailList)) {
            cartonPrintDTO.setProductInfo(detailList.get(0).getSku().getProductCname()+ " 数量: " +detailList.get(0).getPackedNumber());
            return;
        }
        cartonPrintDTO.setProductInfo(doHeader.getDoDetails().get(0).getSku().getProductCname()+ " 数量: " +doHeader.getDoDetails().get(0).getShippedQty());
    }
}
