package com.daxia.wms.delivery.load.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.log.StopWatch;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.*;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoNotice;
import com.daxia.wms.delivery.deliveryorder.filter.DoHeaderFilter;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.DoNoticeService;
import com.daxia.wms.delivery.load.dto.LoadScanDTO;
import com.daxia.wms.delivery.load.entity.LoadDetail;
import com.daxia.wms.delivery.load.entity.LoadHeader;
import com.daxia.wms.delivery.load.entity.LoadHeaderHis;
import com.daxia.wms.delivery.load.entity.ReShipDo;
import com.daxia.wms.delivery.load.filter.LoadFilter;
import com.daxia.wms.delivery.load.service.AutoDeliverService;
import com.daxia.wms.delivery.load.service.LoadService;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.master.entity.Carrier;
import com.daxia.wms.master.entity.Supplier;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.service.CarrierService;
import com.daxia.wms.master.service.SupplierService;
import com.daxia.wms.master.service.WarehouseService;
import org.apache.commons.lang3.tuple.Triple;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.security.Restrict;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * 交接业务Action
 */
@Name("com.daxia.wms.delivery.loadAction")
@Restrict("#{identity.hasPermission('delivery.entruckNew')}")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class LoadAction extends PagedListBean<LoadHeader>{
    private static final long serialVersionUID = 8293293596471100893L;
    @In
    private SequenceGeneratorService sequenceGeneratorService;
    @In
    private LoadService loadService;
    @In
    private CartonService cartonService;
    @In
    private CarrierService carrierService;
    @In
    private SupplierService supplierService;
    @In
    private DeliveryOrderService deliveryOrderService;
    @In
    private ExpFacadeService expFacadeService;
    @In
    private AutoDeliverService autoDeliverService;
    @In
    private WarehouseService warehouseService;
    @In
    private DoNoticeService doNoticeService;


    private String jsonString;
    private boolean handleFlag = false;//处理标识，用于成功与否的判断。
    private boolean initialized = false;
    private LoadFilter loadFilter;
    private LoadDetail loadDetail;
    private LoadHeader loadHeader;
    private List<CartonHeader> cartonHeaderList;
    private List<DeliveryOrderHeader> doHeaderList;
    private Long loadHeaderId;
    private String printData = "[]";
    private String printContent = "";
    private Supplier supplier;
    private String doNo;
    private String cartonNo;
    private String trackingNo = "";
    //操作结果类型1正常2注意3错误31冻结32取消4多箱41三送42半日达5成功
    private int infoStatus;
    //交接单类型：TT/RTV/DO
    private String loadType;

    //已装车包裹List
    private String loadedCartons;
    //未装车包裹List
    private String unloadCartons;
    //是否查询历史数据
    private Boolean queryHistory;
    //交接明细明细是否查询历史数据
    private Boolean queryDetailHistory;

    private DataPage<LoadHeaderHis> dataPageHis;

    private Long cartonId;

    private Integer currScanCount;

    /**
     * 强制发货标记
     */
    private Integer isForceDeliver;

    /**
     * 是否实时同步平台
     */
    private Integer isSyncPlatform;

    /**
     * 合单发货时用，一次扫描多箱
     */
    private String nextCartonNo;

    private Long warehouseId;
    private String warehouseName;
    //地址是否修改 yes or no
    private Integer isModAddress;
    //修改后地址
    private String addressUpdated;

    public LoadAction() {
        super();
        this.loadFilter = new LoadFilter();
        this.loadHeader = new LoadHeader();
        this.doHeaderFilter = new DoHeaderFilter();
        queryHistory = false;
        queryDetailHistory = false;
        setCurrScanCount(0);
    }

    private DoHeaderFilter doHeaderFilter;

    protected DataPage<DoHeaderDto> doDataPage = new DataPage();

    /**
     * 页面查询方法
     */
    @Override
    public void query() {
        loadFilter.getOrderByMap().put("createdAt", "desc");
        this.buildOrderFilterMap(loadFilter);
        if (Boolean.FALSE.equals(queryHistory)) {
            dataPageHis = new DataPage<LoadHeaderHis>();
            populateValuesHis(dataPageHis);
            dataPageHis.setTotalCount(0);
            DataPage<LoadHeader> dataPage = loadService.query(loadFilter,
                    getStartIndex(), getPageSize());
            this.populateValues(dataPage);
            this.queryDetailHistory = false;
        } else {
            dataPage = new DataPage<LoadHeader>();
            this.populateValues(dataPage);
            dataPage.setTotalCount(0);
            dataPageHis = loadService.queryHis(loadFilter, getStartIndex(),
                    getPageSize());
            populateValuesHis(dataPageHis);
            this.queryDetailHistory = true;
        }
    }

    public void queryDo() {
        if (loadHeaderId != null) {
            LoadHeader loadHeader = loadService.getLoadHeader(loadHeaderId);
            if (loadHeader != null) {
                doHeaderFilter.setDoType(convert2DoType(loadHeader.getLoadType()));
                if (LoadHeader.DivideType.CARRIER.getV().equals(loadHeader.getDivideType())) {
                    doHeaderFilter.setCarrierId(loadHeader.getCarrierId());
                }
                if (LoadHeader.DivideType.LINE.getV().equals(loadHeader.getDivideType())) {
                    doHeaderFilter.setLineId(loadHeader.getLineId());
                }
                if (DoType.RTV.getValue().equals(doHeaderFilter.getDoType())) {
                    doHeaderFilter.setSupplierId(loadHeader.getSupplierId());
                }
                if (DoType.ALLOT.getValue().equals(doHeaderFilter.getDoType())) {
                    doHeaderFilter.setTranInWhID(StringUtil.notNullString(loadHeader.getTranInWhID(), null));
                }
                doHeaderFilter.setLoadHeaderId(loadHeaderId);
                doHeaderFilter.setStatusFrom(Constants.DoStatus.ALL_CARTON.getValue());
                doHeaderFilter.setStatusTo(Constants.DoStatus.ALL_CARTON.getValue());
                doHeaderFilter.setNeedCancel(false);
                doHeaderFilter.setReleaseStatus(com.daxia.wms.exp.Constants.ReleaseStatus.RELEASE.getValue());
            }
        }
        DataPage<DoHeaderDto> dataPage = deliveryOrderService.query(doHeaderFilter, getStartIndex(), getPageSize());
//        for(DoHeaderDto dto : dataPage.getDataList()){
//            dto.setWayBill(cartonService.getWayBillByDoId(dto.getId()));
//        }
        this.doDataPage.setDataList(dataPage.getDataList());
        this.doDataPage.setPageCount(dataPage.getPageCount());
        this.doDataPage.setTotalCount(dataPage.getTotalCount());
        this.doDataPage.setSumInfo(dataPage.getSumInfo());
    }

    private String convert2DoType(String loadType) {
        if (Constants.LoadType.WL.getValue().equals(loadType)) {
            return Constants.DoType.SELL.getValue();
        }
        if (Constants.LoadType.TT.getValue().equals(loadType)) {
            return Constants.DoType.ALLOT.getValue();
        }
        if (Constants.LoadType.RTV.getValue().equals(loadType)) {
            return Constants.DoType.RTV.getValue();
        }
        if (Constants.LoadType.WHOLESALE.getValue().equals(loadType)) {
            return Constants.DoType.WHOLESALE.getValue();
        }
        return null;
    }
    /**
     * 设置查询结果
     *
     * @param dataPage
     */
    private void populateValuesHis(DataPage<LoadHeaderHis> dataPage) {
        dataPage.setDataList(dataPage.getDataList());
        dataPage.setPageCount(dataPage.getPageCount());
        dataPage.setTotalCount(dataPage.getTotalCount());
        dataPage.setSumInfo(dataPage.getSumInfo());
    }

    /**
     *
     * <pre>
     * Description:新增或者修改交接单预处理
     * </pre>
     *
     * @throws Exception 序列号生成错误则抛出异常
     */
    public void editLoad() {
        //确保页面只加载一次
        if (!this.initialized) {
            if (this.loadDetail == null) {
                this.loadDetail = new LoadDetail();
            }
            if (this.loadHeaderId == null) {//新增
                this.loadHeader = new LoadHeader();
                String loadNum = this.sequenceGeneratorService.generateSequenceNo(SequenceName.LOADNO.getValue(), ParamUtil.getCurrentWarehouseId());
                this.loadHeader.setLoadNo(loadNum);
                this.loadHeader.setStatus(Constants.LoadStatus.INITIAL.getValue());
                this.loadHeader.setDocQty(Long.valueOf(0));
                this.loadHeader.setTotalCartonQty(Long.valueOf(0));
                this.loadHeader.setCartonQty(Long.valueOf(0));
                this.loadHeader.setTotalGrossWeight(BigDecimal.ZERO);
                this.loadHeader.setTotalNetWeight(BigDecimal.ZERO);
                this.loadHeader.setTotalVolume(BigDecimal.ZERO);
                this.loadHeader.setIsAuto(LoadMode.MANUAL.getValue());
                this.loadHeader.setLoadType(loadType);
                this.supplier = new Supplier();
            } else {//修改交接单
                this.loadHeader = loadService.findLoadDetailsInAcar(this.loadHeaderId, this.queryHistory);
                Collections.sort(this.loadHeader.getLoadDetails(), new Comparator<LoadDetail>() {
                    @Override
                    public int compare(LoadDetail o1, LoadDetail o2) {
                        return -(o1.getUpdatedAt().compareTo(o2.getUpdatedAt()));
                    }
                });
                Supplier supplier = supplierService.getSupplier(loadHeader.getSupplierId());
                this.supplier =  supplier == null ?  new Supplier() : supplier;
                if(loadHeader.getTranInWhID() !=null){
                    Warehouse warehouse = warehouseService.getWarehouse(loadHeader.getTranInWhID());
                    if(warehouse != null){
                        warehouseId = warehouse.getId();
                        warehouseName = warehouse.getWarehouseName();
                    }
                }
            }
            this.initialized = true;
        }
    }

    public void warehouseLoading() {
        //确保页面只加载一次
        if (!this.initialized) {
            if (this.loadDetail == null) {
                this.loadDetail = new LoadDetail();
            }
            if (this.loadHeaderId == null) {//新增
                this.loadHeader = new LoadHeader();
                String loadNum = this.sequenceGeneratorService.generateSequenceNo(SequenceName.LOADNO.getValue(), ParamUtil.getCurrentWarehouseId());
                this.loadHeader.setLoadNo(loadNum);
                this.loadHeader.setStatus(Constants.LoadStatus.INITIAL.getValue());
                this.loadHeader.setDocQty(Long.valueOf(0));
                this.loadHeader.setTotalCartonQty(Long.valueOf(0));
                this.loadHeader.setCartonQty(Long.valueOf(0));
                this.loadHeader.setTotalGrossWeight(BigDecimal.ZERO);
                this.loadHeader.setTotalNetWeight(BigDecimal.ZERO);
                this.loadHeader.setTotalVolume(BigDecimal.ZERO);
                this.loadHeader.setIsAuto(LoadMode.MANUAL.getValue());
                this.loadHeader.setLoadType(loadType);
                this.supplier = new Supplier();
            } else {//修改交接单
                this.loadHeader = loadService.findLoadDetailsInAcar(this.loadHeaderId, this.queryHistory);
            }
            if (Config.get(Keys.Delivery.sync_do_node, Config.ConfigLevel.WAREHOUSE, Constants.SyncDoNode.NONE.getValue()).compareTo(SyncDoNode.LOAD.getValue()) <= 0) {
                this.isSyncPlatform = YesNo.YES.getValue();
            } else {
                this.isSyncPlatform = YesNo.NO.getValue();
            }
            this.initialized = true;
        }
    }

    public void warehouseLoadingDetail(){

    }

    /**
     * <pre>
     * Description:新增交接单
     * </pre>
     *
     */
    public void add() {
        this.loadHeader = this.loadService.saveOrUpdate(this.loadHeader);
        this.loadHeaderId = this.loadHeader.getId();
        this.loadHeader.setCarrier(carrierService.getCarrier(this.loadHeader.getCarrierId()));

        this.loadDetail = new LoadDetail();
        this.sayMessage(MESSAGE_SUCCESS);
    }

    /**
     * 取消交接单
     * @throws Exception
     */
    public void cancel() {
        this.loadService.remove(this.loadHeader);
        this.loadHeader = new LoadHeader();
        this.sayMessage(MESSAGE_SUCCESS);
    }

    /**
     * 接收页面传供应商ID
     *
     * @param supplierId
     */
    public void receiveSelectedSupplier(Long supplierId) {
        loadHeader.setSupplierId(supplierId);
        this.supplier = supplierService.getSupplier(supplierId);
        if (supplier == null) {
            supplier = new Supplier();
        }
    }

    /**
     * 清除页面选择供应商ID
     */
    public void clearSelectSupplier() {
        loadHeader.setSupplierId(null);
        this.supplier = null;
    }

    /**
     * 修改交接单类型时，变量初始化
     */
    public void changeLoadStatus(){
        this.loadHeader.setSupplierId(null);
        this.supplier = new Supplier();
        this.loadHeader.setCarrierId(null);
        this.loadHeader.setTranInWhID(null);
    }

    /**
     * 关闭交接单（先装车锁定，然后再发货）
     */
    public void closeLoadOrder(){
        loadService.closeLoadOrder(loadHeader);
        this.sayMessage(MESSAGE_SUCCESS);
    }

    /**
     *
     * <pre>
     * 加载打印模板的数据
     * </pre>
     *
     */
    public void print() {
        this.printData = "[]";

        List<String> pages = loadService.print(this.loadHeader.getId());
        this.printData = new JSONArray(pages).toString();
    }

    /**
     * 打印预验收单
     */
    public void printLoadDo(){
        this.printContent = "";
        this.printContent = loadService.printLoadDo(this.loadHeader.getId());
    }

    /**
     * 导出
     * @throws IOException
     */
    @Deprecated
    public void export() throws IOException {
        this.printData = "[]";

        byte[] pdfData = loadService.exportPDF(this.loadHeader.getId());
        DownloadUtil.writeToResponse(pdfData, DownloadUtil.PDF,
                "load_" + DateUtil.dateToString(new Date(), "yyMMddHHmmss") + ".pdf");
    }

    /**
     * 按DO交接
     */
    public void loadCartonByDo(){
        this.handleFlag = false;
        this.infoStatus = 3;
        validateBeforeDOLoad();
        List<List<Object>> result = loadService.loadByCarton(cartonHeaderList, loadHeader,doHeaderList,isForceDeliver);
        handleAfterLoaded(result, loadHeader);
        loadHeader = loadService.getLoadHeader(loadHeader.getId());
        this.handleFlag = true;
    }

    //判断地址是否修改
    public void isModAddressByDoNo() {
        validateBeforeDOLoad();
        DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(doNo);
        List<DoNotice> doNotices = doNoticeService.getNoticesById(doHeader.getId(),doHeader.getWarehouseId());
        for (DoNotice doNotice : doNotices) {
            if (StringUtil.isNotEmpty(doNotice.getNoticeNode()) && DoNotice.LOAD.equals(doNotice.getNoticeNode())) {
                this.isModAddress = YesNo.YES.getValue();
                String address = doHeader.getProvinceName()+doHeader.getCityName()+doHeader.getCountyName()+doHeader.getAddress();
                this.addressUpdated = address.replaceAll("null","");
            }
        }
    }

    /**
     * 按箱交接
     */
    public void loadByCartonBF(){
        this.handleFlag = false;
        this.infoStatus = 3;
        this.doNo = "";
        StopWatch sumLoadStopWatch = new StopWatch("箱：'"+cartonNo+"'交接发货总时间");
        StopWatch loadValidateStopWatch = new StopWatch("箱：'"+cartonNo+"'交接前验证");

        //交接前验证
        Triple<LoadHeader, List<DeliveryOrderHeader>, List<CartonHeader>> triple = validateBeforeCartonLoad();
        this.loadHeader = triple.getLeft();
        this.doHeaderList = triple.getMiddle();
        this.cartonHeaderList = triple.getRight();
        log.debug(loadValidateStopWatch.stop());
        StopWatch loadStopWatch = new StopWatch("箱：'"+cartonNo+"'交接");

        //交接
        List<List<Object>> result = loadService.loadByCarton(cartonHeaderList, loadHeader, doHeaderList, isForceDeliver);
        log.debug(loadStopWatch.stop());
        StopWatch deliveryStopWatch = new StopWatch("箱：'"+cartonNo+"'交接发货");

        //交接后发货
        handleAfterLoaded(result, loadHeader);

        loadHeader = loadService.getLoadHeader(loadHeader.getId());
        log.debug(deliveryStopWatch.stop());
        this.handleFlag = true;
        log.debug(sumLoadStopWatch.stop());
    }


    /**
     * 装车扫描
     */
    public void loadingScan(){
        this.handleFlag = false;
        this.infoStatus = 3;
        StopWatch sumLoadStopWatch = new StopWatch("箱：'"+cartonNo+"'交接发货总时间");
        StopWatch loadValidateStopWatch = new StopWatch("箱：'"+cartonNo+"'交接前验证");
        //交接前验证
        Triple<LoadHeader, List<DeliveryOrderHeader>, List<CartonHeader>> triple = validateBeforeCartonLoad();
        this.loadHeader = triple.getLeft();
        this.doHeaderList = triple.getMiddle();
        this.cartonHeaderList = triple.getRight();

        log.debug(loadValidateStopWatch.stop());
        StopWatch loadStopWatch = new StopWatch("箱：'"+cartonNo+"'交接");
        //交接
        List<List<Object>> result = loadService.loadByCarton(cartonHeaderList, loadHeader, doHeaderList, isForceDeliver);
        isForceDeliver = YesNo.NO.getValue();
        log.debug(loadStopWatch.stop());
        StopWatch deliveryStopWatch = new StopWatch("箱：'"+cartonNo+"'交接发货");

        //交接后发货
        handleAfterLoaded(result,loadHeader);

        loadHeader = loadService.getLoadHeader(loadHeader.getId());
        if (1 == cartonHeaderList.size()) {
            //判断是否有合单发货
            nextCartonNo = loadService.getNextCartonNo4CombineShip(cartonHeaderList.get(0));
        }

        log.debug(deliveryStopWatch.stop());
        this.handleFlag = true;
        this.currScanCount++;
        log.debug(sumLoadStopWatch.stop());
    }

    //判断地址是否修改
    public void isModAddress() {
        Triple<LoadHeader, List<DeliveryOrderHeader>, List<CartonHeader>> triple = validateBeforeCartonLoad();
        this.doHeaderList = triple.getMiddle();
//        CartonHeader cartonHeader = cartonService.getCartonByNo(cartonNo);
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(doHeaderList.get(0).getId());
        List<DoNotice> doNotices = doNoticeService.getNoticesById(doHeader.getId(),doHeader.getWarehouseId());
        for (DoNotice doNotice : doNotices) {
            if (StringUtil.isNotEmpty(doNotice.getNoticeNode()) && DoNotice.LOAD.equals(doNotice.getNoticeNode())) {
                this.isModAddress = YesNo.YES.getValue();
                String address = doHeader.getProvinceName()+doHeader.getCityName()+doHeader.getCountyName()+doHeader.getAddress();
                this.addressUpdated = address.replaceAll("null","");
            }
        }
    }

    private Triple<LoadHeader, List<DeliveryOrderHeader>, List<CartonHeader>> validateBeforeCartonLoad() {
        Triple<LoadHeader, List<DeliveryOrderHeader>, List<CartonHeader>> triple = null;
        try {
            triple = loadService.validateBeforeCartonLoad(loadHeader.getId(), cartonNo);
        } catch (DeliveryException e) {
            if (DeliveryException.CARRIER_NO_IS_LOADED.equals(e.getMessage()) || DeliveryException.CARTON_STATUS_ERR_FOR_LOAD.equals(e.getMessage())) {
                this.infoStatus = 33;
            }
            if (DeliveryException.DO_ALREADY_FROZEN.equals(e.getMessage())) {
                this.infoStatus = 31;
            }
            if (DeliveryException.DO_NEED_CANCEL.equals(e.getMessage())) {
                this.infoStatus = 32;
            }
            throw e;
        }
        return triple;
    }

    private void handleAfterLoaded(List<List<Object>> result, LoadHeader loadHeader) {
        loadService.handleAfterLoaded(result,loadHeader);

        for (List<Object> objects : result) {
            List<LoadScanDTO> loadScanDTOs = (List<LoadScanDTO>) objects.get(0);
            CartonHeader cartonHeader = (CartonHeader)objects.get(3);

            DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(cartonHeader.getDoHeaderId());

            this.jsonString = toJSONString(loadScanDTOs);

            if (cartonService.isMultiCartonOfADo(doHeader.getId())) {
                infoStatus = 4; // 多箱
            } else {
                infoStatus = 5;
            }
            if(DeliveryLimitType.HALFDAY.getValue().equals(doHeader.getIsHalfDayDelivery())){
                infoStatus = 42;
            } else if(DeliveryLimitType.THREETIMES.getValue().equals(doHeader.getIsHalfDayDelivery())){
                infoStatus = 41;
            }

            this.handleFlag = true;
        }
    }

    /**
     * 仓库出库-发运确认
     */
    public void shipConfirm(){
        handleFlag = false;
        this.loadHeader = loadService.shipConfirm(this.getLoadHeader().getId());
        handleFlag = true;
    }

    /**
     * 按do交接前的校验。
     */
    private void validateBeforeDOLoad() {
        loadHeader = loadService.getLoadHeader(loadHeader.getId());
        if(loadHeader == null ||
                !(CompareUtil.compare(Constants.LoadStatus.INITIAL.getValue(),loadHeader.getStatus())
                        || CompareUtil.compare(Constants.LoadStatus.LOADING.getValue(),loadHeader.getStatus()))){
            throw new DeliveryException(DeliveryException.LOAD_STATUS_CANNOT_LOAD1);
        }
        DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(doNo);
        if(doHeader == null){
            throw new  DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        doHeaderList = new ArrayList<DeliveryOrderHeader>();
        doHeaderList.add(doHeader);
        // 根据订单取得一个待交接的箱子
        CartonHeader cartonHeader = loadService.findANotLoadedCartonHeaderByDoId(doHeader.getId());
        if (cartonHeader == null) {
            throw new DeliveryException(DeliveryException.NO_CARTON_TO_LOAD_IN_THE_DO);
        }
        cartonNo = cartonHeader.getCartonNo();
        cartonHeaderList = new ArrayList<CartonHeader>();
        cartonHeaderList.add(cartonHeader);
        validateCartonAndDo4Load();
    }

    private void validateCartonAndDo4Load() {
        //对于按照波次来交接,拿一个波次号出来验证
        CartonHeader modelCartonHeader = cartonHeaderList.get(0);
        // 判断该箱号是否已存在于交接单明细中
        if (this.loadService.isCartonLoaded(modelCartonHeader.getId())) {
            infoStatus = 33;
            throw new DeliveryException(DeliveryException.CARRIER_NO_IS_LOADED, modelCartonHeader.getCartonNo());
        }
        // 检查do与交接单类型是否匹配
        loadService.checkDoType(loadHeader, modelCartonHeader.getDoHeader(), modelCartonHeader.getCartonNo());

        for (CartonHeader carton : cartonHeaderList) {
            if (!carton.getStatus().equals(CartonStatus.PACK_OVER.getValue())) {
                infoStatus = 33;
                throw new DeliveryException(DeliveryException.CARTON_STATUS_ERR_FOR_LOAD, carton.getCartonNo());
            }
            check3PLWeightAndBind(carton.getDoHeader(), carton);
        }
        for (DeliveryOrderHeader header : doHeaderList) {
            if (header == null) {
                throw new DeliveryException(DeliveryException.NO_DO_NO_MATCH_CARRIER_NO, cartonNo);
            } else if (ReleaseStatus.HOLD.getValue().equals(header.getReleaseStatus())) {
                infoStatus = 31;
                throw new DeliveryException(DeliveryException.DO_ALREADY_FROZEN);
            } else if (header.getNeedCancel()) {
                infoStatus = 32;
                throw new DeliveryException(DeliveryException.DO_NEED_CANCEL);
            }
            if (StringUtil.isNotIn(header.getStatus(), Constants.DoStatus.ALL_CARTON.getValue(), Constants.DoStatus.PART_LOAD.getValue())) {
                throw new DeliveryException(DeliveryException.DO_STATUS_CANNOT_LOAD1, header.getDoNo());
            }
        }
    }

    /**
     * 按箱流水交接第一步，校验
     */
    public void flowLoadByCartonBF(){
        StopWatch sumFlowLoadStopWatch = new StopWatch("箱：'"+cartonNo+"'流水交接总时间");
        this.handleFlag = false;
        this.infoStatus = 3;
        CartonHeader cartonHeader = cartonService.getCartonByNo(cartonNo);
        if (cartonHeader == null) {
            throw new DeliveryException(DeliveryException.CARTON_NO_EXIST, cartonNo);
        }
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(cartonHeader
                .getDoHeaderId());
        if (doHeader == null) {
            throw new DeliveryException(
                    DeliveryException.NO_DO_NO_MATCH_CARRIER_NO, cartonNo);
        } else if (ReleaseStatus.HOLD.getValue().equals(
                doHeader.getReleaseStatus())) {
            infoStatus = 31;
            throw new DeliveryException(DeliveryException.DO_ALREADY_FROZEN);
        } else if (doHeader.getNeedCancel()) {
            infoStatus = 32;
            throw new DeliveryException(DeliveryException.DO_NEED_CANCEL);
        } else if (!cartonHeader.getStatus()
                .equals(CartonStatus.PACK_OVER.getValue())) {
            infoStatus = 33; // 重复
            throw new DeliveryException(
                    DeliveryException.CARTON_STATUS_ERR_FOR_LOAD, cartonNo);
        } else if (!(Constants.DoType.SELL.getValue().equals(doHeader
                .getDoType()))) {
            infoStatus = 3;
            throw new DeliveryException(
                    DeliveryException.DO_TYPE_DONOT_MATCH_LOAD_TYPE, cartonNo);
        } else {
            this.doNo = doHeader.getDoNo();
        }

        check3PLWeightAndBind(doHeader,cartonHeader);
        flowLoadByCartonAF();
        log.debug(sumFlowLoadStopWatch.stop());
    }

    private void check3PLWeightAndBind(DeliveryOrderHeader doHeader,CartonHeader cartonHeader) {
        if(!(DoType.SELL.getValue().equals(doHeader
                .getDoType()))){
            return;
        }
        Carrier carrier = carrierService.getCarrier(doHeader.getCarrierId());
        // 若配送商不需要称重和打印面单，则无需后面的校验
        if (!YesNo.YES.getValue().equals(carrier.getIsNeedWeightAndPrint())) {
            return;
        }
        // 判断是否已经绑定面单。
        if (StringUtil.isEmpty(cartonHeader.getWayBill())) {
            throw new DeliveryException(DeliveryException.CARTON_NOT_BIND,
                    cartonNo);
        }

        if (SystemConfig.configIsOpen("delivery.load.need.weight",cartonHeader.getWarehouseId()) &&
                YesNo.NO.getValue().intValue() == cartonHeader.getWeightFlag())
        {
            throw new DeliveryException(DeliveryException.CARTON_NOT_WEIGHTED,
                    cartonNo);
        }
    }

    /**
     * 流水按箱交接第二步。
     */
    @Loggable
    public void flowLoadByCartonAF() {
        this.infoStatus = 3;
        this.handleFlag = false;
        StopWatch flowLoadStopWatch = new StopWatch("箱：'"+cartonNo+"'流水交接发货");
        autoDeliverService.autoLoadAndDeliver(cartonNo,LoadMode.FLOW.getValue(), DateUtil.getNowTime());
        CartonHeader cartonHeader = cartonService.getCartonByNo(cartonNo);
        if (cartonHeader == null) {
            throw new DeliveryException(DeliveryException.CARTON_NO_EXIST, cartonNo);
        }
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(cartonHeader
                .getDoHeaderId());
        //获取未交接包裹箱号
        List<String> cartonList =cartonService.getCartonNoByDoId(doHeader.getId(), false);
        if(ListUtil.isNullOrEmpty(cartonList)){
            unloadCartons = "";
        } else {
            unloadCartons = StringUtil.substring(cartonList.toString(), 1, cartonList.toString().length()-1);
        }

        //获取已交接包裹箱号
        cartonList = cartonService.getCartonNoByDoId(doHeader.getId(), true);
        if(ListUtil.isNullOrEmpty(cartonList)){
            loadedCartons = "";
        } else {
            loadedCartons = StringUtil.substring(cartonList.toString(), 1, cartonList.toString().length()-1);
        }

        if (cartonService.isMultiCartonOfADo(doHeader.getId())) {
            infoStatus = 4; // 多箱
        } else {
            infoStatus = 5;
        }
        if(DeliveryLimitType.HALFDAY.getValue().equals(doHeader.getIsHalfDayDelivery())){
            infoStatus = 42;
        } else if(DeliveryLimitType.THREETIMES.getValue().equals(doHeader.getIsHalfDayDelivery())){
            infoStatus = 41;
        }
        this.handleFlag = true;
    }

    /**
     * 手动把需要发货的do发出去。
     */
    public void specialDeliver(){
        Integer batchNumber = Integer.parseInt(SystemConfig.getConfigValue("batch.delivery.count", null));
        Integer failExceed = Integer.valueOf(SystemConfig.getConfigValueInt("mail.when.fail.exceed", null));

        List<ReShipDo> shipQueue = loadService.findToReShipDo(failExceed, batchNumber, ParamUtil.getCurrentWarehouseId());
        if (!ListUtil.isNullOrEmpty(shipQueue)) {
            for (ReShipDo toShip : shipQueue) {
                loadService.evictReShipDo(toShip);
                boolean success=Boolean.TRUE;
                try {
                    //自动交接和流水交接，人工交接的订单可以发货。
                    if(LoadMode.AUTO.getValue().equals(toShip.getIsAuto())
                            || LoadMode.FLOW.getValue().equals(toShip.getIsAuto())
                            || LoadMode.MANUAL.getValue().equals(toShip.getIsAuto())){
                        success=autoDeliverService.reShip(toShip);
                    }else{
                        return;
                    }
                } catch (Exception e) {
                    success=Boolean.FALSE;
                    log.error("error run autoShip:"+toShip.getId(), e);
                } finally{
                    // 重新发货失败次数达到上限，邮件预警
                    try{
                        if(success){
                            loadService.removeReShipByDoId(toShip.getDocId());
                        }else{
                            loadService.addShipCount(toShip);
                            try{
                                if (Long.valueOf(SystemConfig.getConfigValue("mail.when.fail.exceed", null)).equals(toShip.getCount()+1)) {
                                    if(YesNo.YES.getValue().equals(toShip.getIsAuto())){
                                        // 重新发货失败次数达到上限，邮件预警
                                        loadService.sendNoticeMail(toShip.getId().toString(), DeliveryException.RESHIPDO_FAIL_EXCEED, null);
                                    }else{
                                        loadService.sendNoticeMail(toShip.getId().toString(), DeliveryException.RESHIPLOAD_STATUS_ERROR, null);
                                    }
                                }
                            }catch (Exception e) {
                                log.error("error run autoShip send mail:"+toShip.getId(), e);
                            }
                        }
                    }catch (Exception e) {
                        log.error("error run autoShip update:"+toShip.getId(), e);
                    }
                }
            }
        }
    }


    /**
     * List转为JSON
     * @param loadScanDTOs
     * @return
     */
    private String toJSONString(List<LoadScanDTO> loadScanDTOs) {
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        try {
            for (LoadScanDTO loadScanDTO : loadScanDTOs) {
                jsonArray.put(loadScanDTO.toJSONObject());
            }
            jsonObject.put("detail", jsonArray);
        } catch (Exception e) {}
        return jsonObject.toString();
    }
    public void receiveWarehouse(Long warehouseId) {
        if (warehouseId != null) {
            Warehouse wh = warehouseService.getWarehouse(warehouseId);
            loadHeader.setTranInWhID(warehouseId);
            warehouseName = wh.getWarehouseName();
        }
    }

    public void clearWarehouse() {
        loadHeader.setTranInWhID(null);
        this.warehouseId = null;
        this.warehouseName = null;
    }

    /**
     * 导出Excel
     * @throws IOException
     */
    public void exportExcel() throws IOException{
        log.debug("Enter...");
        byte[] excel = loadService.exportExcel(loadHeader.getId());
        DownloadUtil.writeToResponse(excel, DownloadUtil.OTHER, "发货统计.xls");
    }

    /**
     * 导出Excel
     *
     * @throws IOException
     */
    public void exportLoadDetailExcel() throws IOException {
        loadService.exportLoadDetailExcel(loadHeader.getId());
        this.sayMessage(MESSAGE_SUCCESS);
    }
    public void reFresh() {
        this.loadHeader = loadService.getLoadHeader(this.getLoadHeader().getId());
    }

    public void reFreshMore() {
        this.loadHeader = loadService.findLoadDetailsInAcar(this.loadHeaderId, this.queryHistory);
        Collections.sort(this.loadHeader.getLoadDetails(), new Comparator<LoadDetail>() {
            @Override
            public int compare(LoadDetail o1, LoadDetail o2) {
                return -(o1.getUpdatedAt().compareTo(o2.getUpdatedAt()));
            }
        });
        Supplier supplier = supplierService.getSupplier(loadHeader.getSupplierId());
        this.supplier =  supplier == null ?  new Supplier() : supplier;
        if(loadHeader.getTranInWhID() !=null){
            Warehouse warehouse = warehouseService.getWarehouse(loadHeader.getTranInWhID());
            if(warehouse != null){
                warehouseId = warehouse.getId();
                warehouseName = warehouse.getWarehouseName();
            }
        }
    }

    /**
     * 车牌号
     */
    private String vechileNo;
    /**
     * 设置车牌号
     */
    public void batchSetVechileNo(){
        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }
        log.debug("设置车牌号 ids:{},车牌号:{}",ids,vechileNo);
        loadService.batchSetVechileNo(ids,vechileNo);
        buttonQuery();
    }
    public void prepareSetVechileNo(){
        log.debug("当前选中ids：{}",getSelectedRowList());
    }

    public String getVechileNo() {
        return vechileNo;
    }

    public void setVechileNo(String vechileNo) {
        this.vechileNo = vechileNo;
    }

    /**
     * Getter method for property <tt>loadHeader</tt>.
     *
     * @return property value of loadHeader
     */
    public LoadHeader getLoadHeader() {
        return loadHeader;
    }

    /**
     * Setter method for property <tt>loadHeader</tt>.
     *
     * @param loadHeader value to be assigned to property loadHeader
     */
    public void setLoadHeader(LoadHeader loadHeader) {
        this.loadHeader = loadHeader;
    }

    public void setLoadFilter(LoadFilter loadFilter) {
        this.loadFilter = loadFilter;
    }

    public LoadFilter getLoadFilter() {
        return loadFilter;
    }

    public void setLoadHeaderId(Long loadHeaderId) {
        this.loadHeaderId = loadHeaderId;
    }

    public Long getLoadHeaderId() {
        return loadHeaderId;
    }

    public void setLoadDetail(LoadDetail loadDetail) {
        this.loadDetail = loadDetail;
    }

    public LoadDetail getLoadDetail() {
        return loadDetail;
    }

    /**
     * Setter method for property <tt>printData</tt>.
     *
     * @param printData value to be assigned to property printData
     */
    public void setPrintData(String printData) {
        this.printData = printData;
    }

    public String getPrintContent() {
        return printContent;
    }

    public void setPrintContent(String printContent) {
        this.printContent = printContent;
    }

    /**
     * Getter method for property <tt>printData</tt>.
     *
     * @return property value of printData
     */
    public String getPrintData() {
        return printData;
    }
    public Supplier getSupplier() {
        return supplier;
    }

    public void setSupplier(Supplier supplier) {
        this.supplier = supplier;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public int getInfoStatus() {
        return infoStatus;
    }

    public void setInfoStatus(int infoStatus) {
        this.infoStatus = infoStatus;
    }

    public String getTrackingNo() {
        return trackingNo;
    }

    public void setTrackingNo(String trackingNo) {
        this.trackingNo = trackingNo;
    }

    public String getJsonString() {
        return jsonString;
    }

    public void setJsonString(String jsonString) {
        this.jsonString = jsonString;
    }

    public boolean isHandleFlag() {
        return handleFlag;
    }

    public void setHandleFlag(boolean handleFlag) {
        this.handleFlag = handleFlag;
    }

    public String getCartonNo() {
        return cartonNo;
    }

    public void setCartonNo(String cartonNo) {
        this.cartonNo = cartonNo;
    }

    public String getLoadType() {
        return loadType;
    }

    public void setLoadType(String loadType) {
        this.loadType = loadType;
    }

    public String getLoadedCartons() {
        return loadedCartons;
    }

    public void setLoadedCartons(String loadedCartons) {
        this.loadedCartons = loadedCartons;
    }

    public String getUnloadCartons() {
        return unloadCartons;
    }

    public void setUnloadCartons(String unloadCartons) {
        this.unloadCartons = unloadCartons;
    }


    public Boolean getQueryHistory() {
        return queryHistory;
    }

    public void setQueryHistory(Boolean queryHistory) {
        this.queryHistory = queryHistory;
    }

    public Boolean getQueryDetailHistory() {
        return queryDetailHistory;
    }

    public void setQueryDetailHistory(Boolean queryDetailHistory) {
        this.queryDetailHistory = queryDetailHistory;
    }

    public DataPage<LoadHeaderHis> getDataPageHis() {
        return dataPageHis;
    }

    public void setDataPageHis(DataPage<LoadHeaderHis> dataPageHis) {
        this.dataPageHis = dataPageHis;
    }

    public Long getCartonId() {
        return cartonId;
    }

    public void setCartonId(Long cartonId) {
        this.cartonId = cartonId;
    }

    public Integer getCurrScanCount() {
        return currScanCount;
    }

    public void setCurrScanCount(Integer currScanCount) {
        this.currScanCount = currScanCount;
    }

    public Integer getIsForceDeliver() {
        return isForceDeliver;
    }

    public void setIsForceDeliver(Integer isForceDeliver) {
        this.isForceDeliver = isForceDeliver;
    }

    public Integer getIsSyncPlatform() {
        return isSyncPlatform;
    }

    public void setIsSyncPlatform(Integer isSyncPlatform) {
        this.isSyncPlatform = isSyncPlatform;
    }

    public String getNextCartonNo()
    {
        return nextCartonNo;
    }

    public void setNextCartonNo(String nextCartonNo)
    {
        this.nextCartonNo = nextCartonNo;
    }

    public List<CartonHeader> getCartonHeaderList() {
        return cartonHeaderList;
    }

    public void setCartonHeaderList(List<CartonHeader> cartonHeaderList) {
        this.cartonHeaderList = cartonHeaderList;
    }

    public List<DeliveryOrderHeader> getDoHeaderList() {
        return doHeaderList;
    }

    public void setDoHeaderList(List<DeliveryOrderHeader> doHeaderList) {
        this.doHeaderList = doHeaderList;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Integer getIsModAddress() {
        return isModAddress;
    }

    public void setIsModAddress(Integer isModAddress) {
        this.isModAddress = isModAddress;
    }

    public String getAddressUpdated() {
        return addressUpdated;
    }

    public void setAddressUpdated(String addressUpdated) {
        this.addressUpdated = addressUpdated;
    }

    public DoHeaderFilter getDoHeaderFilter() {
        return doHeaderFilter;
    }

    public void setDoHeaderFilter(DoHeaderFilter doHeaderFilter) {
        this.doHeaderFilter = doHeaderFilter;
    }

    public DataPage<DoHeaderDto> getDoDataPage() {
        return doDataPage;
    }

    public void setDoDataPage(DataPage<DoHeaderDto> doDataPage) {
        this.doDataPage = doDataPage;
    }
}