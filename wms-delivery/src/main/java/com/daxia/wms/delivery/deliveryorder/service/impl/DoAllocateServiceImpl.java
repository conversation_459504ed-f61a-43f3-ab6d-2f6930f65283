package com.daxia.wms.delivery.deliveryorder.service.impl;


import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.service.Dictionary;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.constants.Constants.YesNo;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.*;
import com.daxia.wms.Keys;
import com.daxia.wms.OrderLogConstants;
import com.daxia.wms.delivery.AllocateException;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.constant.DeliveryConstant;
import com.daxia.wms.delivery.deliveryorder.dao.*;
import com.daxia.wms.delivery.deliveryorder.dto.DoAlcDetailInfoDto;
import com.daxia.wms.delivery.deliveryorder.dto.DoAllocateHeaderDto;
import com.daxia.wms.delivery.deliveryorder.dto.ManualAllocDTO;
import com.daxia.wms.delivery.deliveryorder.entity.*;
import com.daxia.wms.delivery.deliveryorder.enums.CheckFlagEnum;
import com.daxia.wms.delivery.deliveryorder.filter.DoAlcHeaderFilter;
import com.daxia.wms.delivery.deliveryorder.filter.DoDetailFilter;
import com.daxia.wms.delivery.deliveryorder.service.*;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;
import com.daxia.wms.delivery.invoice.service.CancelInvoiceService;
import com.daxia.wms.delivery.invoice.service.ElectronicInvoiceService;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.pick.filter.PickTaskFilter;
import com.daxia.wms.delivery.pick.service.PickTaskService;
import com.daxia.wms.delivery.recheck.entity.TempCarton;
import com.daxia.wms.delivery.recheck.service.TempCartonService;
import com.daxia.wms.delivery.wave.dto.AutoWaveDTO;
import com.daxia.wms.delivery.wave.dto.OverAllocateDto;
import com.daxia.wms.exp.dto.DoExpDto;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.master.dto.SkuDTO;
import com.daxia.wms.master.entity.Carrier;
import com.daxia.wms.master.entity.PackageInfoDetail;
import com.daxia.wms.master.helper.SysConfigHelper;
import com.daxia.wms.master.rule.filter.BigWaveFilter;
import com.daxia.wms.master.service.PackageInfoDetailService;
import com.daxia.wms.master.service.RegionService;
import com.daxia.wms.master.service.SkuCache;
import com.daxia.wms.stock.stock.dao.StockBatchAttDAO;
import com.daxia.wms.stock.stock.dto.OrderStockDTO;
import com.daxia.wms.stock.stock.entity.StockBatchAtt;
import com.daxia.wms.stock.stock.entity.StockBatchLocLpn;
import com.daxia.wms.stock.stock.service.IOperator;
import com.daxia.wms.stock.stock.service.StockQueryService;
import com.daxia.wms.stock.stock.service.StockService;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.jboss.seam.core.Events;
import org.jboss.seam.security.Identity;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

@Name("com.daxia.wms.delivery.doAllocateService")
@lombok.extern.slf4j.Slf4j
public class DoAllocateServiceImpl implements DoAllocateService {
    @In
    private DoAllocateHeaderDAO doAllocateHeaderDAO;
    @In
    private DoHeaderDAO doHeaderDAO;
    @In
    private DoAllocateDetailDAO doAllocateDetailDAO;
    @In
    private DoDetailDAO doDetailDAO;
    @In
    private DoLackDetailDAO doLackDetailDAO;
    @In
    private PickTaskService pickTaskService;
    @In
    private StockService stockService;
    @In
    private StockQueryService stockQueryService;
    @In
    private IDoAllocate doAllocate;
    @In
    private DeliveryOrderService deliveryOrderService;
    @In
    private Identity identity;
    @In("lackOperator")
    private IOperator lackOperator;
    @In
	SkuCache skuCache;
    @In("allocateOperator")
    private IOperator allocateOperator;

    @In
    private DoLackHeaderService doLackHeaderService;
    @In
    private DoWaveExService doWaveExService;
    @In
    private OrderLogService orderLogService;
    @In
    private ExpFacadeService expFacadeService;
	@In
	IDoAllocate wholesaleAllocate;
	@In
	private PackageInfoDetailService packageInfoDetailService;
	@In
	private DoExceptionLogService doExceptionLogService;
	@In
	private CancelInvoiceService cancelInvoiceService;
	@In
	private ElectronicInvoiceService electronicInvoiceService;
	@In
	private StockBatchAttDAO stockBatchAttDAO;
	@In
	private RegionService regionService;
	@In
	private TempCartonService tempCartonService;
	public final static Set<String> PRIVATE_CHANEL_CODE_SET =new HashSet<String>(){
		{
			add("VTN");
			add("ABM");

		}
	};
    @Override
    public DataPage<DoAllocateHeaderDto> query(DoAlcHeaderFilter filter, int startIndex, int pageSize) {
		filter.setNeedCrossStock(Constants.YesNo.NO.getValue());
        return doAllocateHeaderDAO.findDoHeaderPageInfo(filter, startIndex, pageSize);
    }

    @Override
    public DoAllocateHeader getHeader(Long allocateHeaderId) {
        return doAllocateHeaderDAO.get(allocateHeaderId);
    }

	@Transactional
	@Override
	public void cleanExpireLimit(Long allocateHeaderId){
		DoAllocateHeader doAllocateHeader =getHeader( allocateHeaderId);
		doAllocateHeader.getDoAllocateDetails().forEach(x->{x.setMinExp(null); x.setMaxExp(null);});
		doAllocateHeader.getDeliveryOrderHeader().getDoDetails().forEach(x->{ x.setMinExp(null); x.setMaxExp(null);});
	}
	@Transactional
	@Override
	public void cleanExpireLimit(Long allocateHeaderId,List<Long> allocateDetailIds){
		DoAllocateHeader doAllocateHeader =getHeader( allocateHeaderId);
		doAllocateHeader.getDoAllocateDetails().stream()
				.filter(x->allocateDetailIds.contains(x.getId()))
				.forEach(x->{x.setMinExp(null); x.setMaxExp(null);});
		doAllocateHeader.getDeliveryOrderHeader().getDoDetails().stream()
				.filter(x->allocateDetailIds.contains(x.getId())).
				forEach(x->{ x.setMinExp(null); x.setMaxExp(null);});
	}

    /**
     * 手动分配
     */
    @Override
    @Loggable
    @Transactional
    public void manualAlloc(Long doAllocateDetailId, List<ManualAllocDTO> dtoList, Boolean isForce,Integer isException) {
        log.debug("Manual Alloc:[id: {}]", doAllocateDetailId);
        
    	boolean changeFlag = false;
    	DoAllocateDetail doAllocateDetail = doAllocateDetailDAO.get(doAllocateDetailId);
    	if (null == doAllocateDetail) {
    		throw new DeliveryException(DeliveryException.ERROR_ALLOC_DOALCDETAIL_NOT_EXIST);
    	}

		DoAllocateHeader doAllocateHeader = doAllocateDetail.getDoAllocateHeader();

    	//DO取消拦截
    	if(deliveryOrderService.doCancelIntercept(doAllocateHeader.getId())){
			return;
		}

        validateManualAssign(doAllocateDetail, doAllocateHeader,isException);
        
        //该订单已存在的拣货任务
        List<PickTask> pktTaskList = pickTaskService.getPickTasks(doAllocateDetail.getDoHeaderId(), doAllocateDetail.getId());

		//该doDetail的现分配总数
		BigDecimal allocateQtyCountPcs = doAllocateDetail.getAllocatedQtyPcs();
		BigDecimal allocateQtyCountUnit = doAllocateDetail.getAllocatedQtyUnit();

		ImmutablePair<PackageInfoDetail, PackageInfoDetail> pcsAndUnitPackages = packageInfoDetailService.findPcsAndUnitPackages(doAllocateDetail.getSkuId());

        for (ManualAllocDTO tempDto : dtoList) {
            BigDecimal assignQtyUnit = tempDto.getAllocingQty();//本次分配数
            BigDecimal allocatedQtyUnit = tempDto.getAllocatedQtyUnit();//已分配数(页面带过来的不准确,用于判断记录是否在本次操作中修改)
            //分配数不能小于0
			if (null == assignQtyUnit || assignQtyUnit.compareTo(BigDecimal.ZERO) < 0) {
				throw new DeliveryException(DeliveryException.DO_ASSIGN_NUM_CANNOT_BE_NEGATIVE);
			}
			Integer dotFlag = doAllocateDetail.getSku().getDotFlag();

			BigDecimal[] decimals = assignQtyUnit.divideAndRemainder(BigDecimal.ONE);
			if (!Constants.YesNo.YES.getValue().equals(dotFlag)) {
				if (decimals[1].compareTo(BigDecimal.ZERO) > 0) {
					throw new DeliveryException(DeliveryException.DOT_FLAG_ERROR, "商品:" + tempDto.getProductCode());
				}
			}

            //原分配数和现分配数无更改则跳过此明细
            if (assignQtyUnit.compareTo(allocatedQtyUnit) != 0) {
				BigDecimal changeQtyUnit;
            	changeFlag = true;
            	StockBatchLocLpn stockBatchLocLpn = stockQueryService.findStockBatchLocLpnById(tempDto.getStockId());

				Long packageId = Long.valueOf(stockBatchLocLpn.getStockBatchAtt().getLotatt07());
				int packageType = packageId.equals(pcsAndUnitPackages.getLeft().getId()) ? 0 : (packageId.equals(pcsAndUnitPackages.getRight().getId()) ? 1 : -1);// 0: PCS, 1: Unit
				if (packageType == -1) {
					throw new DeliveryException(DeliveryException.PACKAGE_TYPE_ERROR);
				}

				PickTask pickTask = this.getPktTaksByStock(pktTaskList, tempDto.getPicTaskId());
				//如果分配数为0则取消分配，如果不存在拣货任务则失败(可能被并发操作修改)
            	if (assignQtyUnit.compareTo(BigDecimal.ZERO) == 0) {
            		 if (null == pickTask) {
            			 throw new DeliveryException(DeliveryException.ERROR_ALLOC_PICTASK_NOT_EXIST);
            		 }
            		 //取消分配
					changeManualAllocStock(doAllocateDetail, stockBatchLocLpn, pickTask, BigDecimal.ZERO);
					pickTaskService.removeTask(pickTask);
					changeQtyUnit = pickTask.getQtyUnit().negate();
            	} else {
            		if (null == pickTask) {
            			//新增分配
            			Map<String, Long> stockidMap = changeManualAllocStock(doAllocateDetail, stockBatchLocLpn, null, assignQtyUnit);

						//创建拣货任务
						pickTaskService.createPickTask(doAllocateDetail, stockBatchLocLpn, assignQtyUnit.multiply(stockBatchLocLpn.getPackQty()), assignQtyUnit, Long.valueOf(stockBatchLocLpn.getStockBatchAtt().getLotatt07()),
								stockidMap.get(StockType.STOCK_ALLOC.getValue()), stockidMap.get(StockType.STOCK_ALLOCING.getValue()));

						changeQtyUnit = assignQtyUnit;
            		} else {
						//修改分配
						if (pickTask.getQtyUnit().compareTo(allocatedQtyUnit) != 0) {
							throw new DeliveryException(DeliveryException.ERROR_ALLOC_DATA_CHANGED);
						}
						changeManualAllocStock(doAllocateDetail, stockBatchLocLpn, pickTask, assignQtyUnit);
						changeQtyUnit = assignQtyUnit.subtract(pickTask.getQtyUnit());

						pickTask.setQty(assignQtyUnit.multiply(stockBatchLocLpn.getPackQty()));
						pickTask.setQtyUnit(assignQtyUnit);
						pickTaskService.updatePickTask(pickTask);
            		}
            	}
				if (packageType == 0) {
					allocateQtyCountPcs = allocateQtyCountPcs.add(changeQtyUnit);
				} else if (packageType == 1) {
					allocateQtyCountUnit = allocateQtyCountUnit.add(changeQtyUnit);
				}
            } else {
            	continue;//跳过没有修改的记录
            }
        }
        //有更改分配数量才执行更新
        if (changeFlag) {
        	BigDecimal allocatedQty = allocateQtyCountPcs.add(allocateQtyCountUnit.multiply(pcsAndUnitPackages.getRight().getQty()));
        	
			orderLogService.saveLog(doAllocateHeader.getDeliveryOrderHeader(), OrderLogConstants.OrderLogType.ALLOCATE_MANUAL.getValue(), ResourceUtils.getDispalyString(OrderLogConstants
					.ORDER_OPERATE_LOG_ALLOCATE_MANUAL, null, doAllocateDetail.getSku().getProductCode(), allocatedQty));

			//判断分配数是否大于期望分配数
			if (allocateQtyCountPcs.doubleValue() < 0 || allocateQtyCountUnit.doubleValue() < 0 || doAllocateDetail.getExpectedQtyPcs().compareTo(allocateQtyCountPcs) < 0 || doAllocateDetail.getExpectedQtyUnit().compareTo(allocateQtyCountUnit) < 0) {
				throw new DeliveryException(DeliveryException.DO_ASSIGN_NUM_ERROR);
			}

			doAllocateDetail.setAllocatedQty(allocatedQty);
			doAllocateDetail.setAllocatedQtyPcs(allocateQtyCountPcs);
			doAllocateDetail.setAllocatedQtyUnit(allocateQtyCountUnit);

			if (doAllocateDetail.getExpectedQty().compareTo(doAllocateDetail.getAllocatedQty()) < 0) {
				throw new DeliveryException(DeliveryException.DO_ASSIGN_NUM_ERROR);
			}

			if (doAllocateDetail.getAllocatedQty().compareTo(BigDecimal.ZERO) == 0) {
				doAllocateDetail.setLineStatus(DoStatus.INITIAL.getValue());
			} else if (doAllocateDetail.getExpectedQty().compareTo(doAllocateDetail.getAllocatedQty()) == 0) {
				doAllocateDetail.setLineStatus(DoStatus.ALLALLOCATED.getValue());
			} else {
				doAllocateDetail.setLineStatus(DoStatus.PARTALLOCATED.getValue());
			}
	        this.doAllocateDetailDAO.saveOrUpdate(doAllocateDetail);
	        doAllocateHeader.setAllocTime(DateUtil.getNowTime());
	        this.modifyDoAllocateHeader(doAllocateHeader);
	        
	        this.doAllocateDetailDAO.getSession().flush();
	        this.doAllocateDetailDAO.getSession().clear();

			if (doAllocateDetail.getDoAllocateHeader().getLotStrategy().equals(Constants.LotStrategy.ONE_BATCH_NO.getValue())) {
				List<String> allocatedLotNo = this.pickTaskService.findLotNoByDoDetail(doAllocateDetail.getId());
				if (allocatedLotNo.size() > 1 && !isForce) {
					throw new DeliveryException(DeliveryException.ALLOCATE_MULIT_LOT_NO);
				}
			}
	        //同步状态时间数量到主表
	        manualAllocSynAlc2DocDo(doAllocateHeader.getId());
        }
    }
    
    /**
	 * 验证doAllocateDetail是否能手动分配
	 * @param doAllocateDetail
	 * @param doAllocateHeader
	 */
	private void validateManualAssign(DoAllocateDetail doAllocateDetail, DoAllocateHeader doAllocateHeader,Integer isException) {
		//组合产品的父产品不能分配
		if (doAllocateDetail.getIsDoLeaf().intValue() == 0) {
			throw new DeliveryException(DeliveryException.DO_ASSIGN_ERROR_ISPARENT);
		}

		//doDetail的状态判断,只有初始化、部分分配、分配完成状态的才能分配
		String lineStatus = doAllocateDetail.getLineStatus();
		if (StringUtil.isNotIn(lineStatus, DoStatus.INITIAL.getValue(), DoStatus.PARTALLOCATED.getValue())) {
			throw new DeliveryException(DeliveryException.DODETAIL_STATUS_WRONG);
		}
		
		//只有释放状态的时候才能进行人工分配；
		ReleaseStatus releaseStatus = ReleaseStatus.fromString(doAllocateHeader.getReleaseStatus());
		if (ReleaseStatus.RELEASE != releaseStatus && !YesNo.YES.getValue().equals(isException)) {
			throw new DeliveryException(DeliveryException.DO_CANNOT_ASSIGN);
		}
		
		//只有RTV、调拨单、RMA才能进行人工分配
		DoType doType = DoType.fromString(doAllocateHeader.getDoType());
		if (!ImmutableSet.of(DoType.RTV, DoType.ALLOT, DoType.SELL, DoType.WHOLESALE,DoType.MPS_OUT).contains(doType)) {
			throw new DeliveryException(DeliveryException.DO_CANNOT_ASSIGN);
		}
	}
	
	 /**
     * 根据库存信息在拣货任务list中查找和其匹配的拣货任务，如果存在则返回并从list中删除
     * @return
     */
	 private PickTask getPktTaksByStock(List<PickTask> taskList, Long picTaskId) {
		 for (PickTask pt : taskList) {
			 if (CompareUtil.compare(pt.getId(), picTaskId)) {
				 return pt;
			 }
		 }
		
		 return null;
	 }
	
	/**
	 * 手动分配库存变更
	 * @param pickTask
	 * @param allocateQtyUnit
	 * @return
	 */
	private Map<String, Long> changeManualAllocStock(DoAllocateDetail doAllocateDetail, StockBatchLocLpn stockBatchLocLpn, PickTask pickTask, BigDecimal allocateQtyUnit) {
		if (null == stockBatchLocLpn) {
			throw new DeliveryException("库存记录不存在");
		}
		
		log.debug("Manual Alloc Stock change:[id: {}, stockId: #1, qty: #2]", doAllocateDetail.getId(), stockBatchLocLpn.getId(), allocateQtyUnit);
		
		BigDecimal changeQtyUnit = allocateQtyUnit;
		if (null != pickTask) {
			changeQtyUnit = allocateQtyUnit.subtract((pickTask.getQtyUnit() == null ? BigDecimal.ZERO : pickTask.getQtyUnit()));
		}
		BigDecimal canUseQtyUnit = stockBatchLocLpn.getCanUseQtyUnit();
		if (canUseQtyUnit.compareTo(changeQtyUnit) < 0) {
			throw new DeliveryException(DeliveryException.STOCK_QTY_LESS_THAN_CHANGE, canUseQtyUnit, changeQtyUnit);
		}
		
		//变更库存
		OrderStockDTO dto = new OrderStockDTO();
		dto.setStockLpnId(stockBatchLocLpn.getId());
		dto.setSkuId(stockBatchLocLpn.getSkuId());
		dto.setFmLocId(stockBatchLocLpn.getLocId());
		dto.setLotId(stockBatchLocLpn.getLotId());
		dto.setLpnNo(stockBatchLocLpn.getLpnNo());
		Map<String, Long> stockidMap = new HashMap<String, Long>();
		if (allocateQtyUnit.compareTo(BigDecimal.ZERO) == 0) { //页面输入0时，取消分配
			dto.setFmStockId(pickTask.getFmStockId());
			dto.setPlanQty(pickTask.getQty());
			dto.setPlanQtyUnit(pickTask.getQtyUnit());
			dto.setAllocatingId(pickTask.getAllocatingId());
			allocateOperator.setStockDto(dto);
			stockService.undo(allocateOperator);
		} else {
			if (null != pickTask) {//修改
				dto.setPlanQty(allocateQtyUnit.multiply(stockBatchLocLpn.getPackQty()).subtract(pickTask.getQty()).negate());
				dto.setPlanQtyUnit(allocateQtyUnit.subtract(pickTask.getQtyUnit()).negate());
				dto.setFmStockId(pickTask.getFmStockId());
				dto.setAllocatingId(pickTask.getAllocatingId());
				
			} else {//新增
				dto.setDocId(doAllocateDetail.getDoHeaderId());
				dto.setDocLineId(doAllocateDetail.getId());
				dto.setPlanQty(allocateQtyUnit.multiply(stockBatchLocLpn.getPackQty()).negate());
				dto.setPlanQtyUnit(allocateQtyUnit.negate());
			}
			allocateOperator.setStockDto(dto);
		    stockidMap = stockService.operateStock(allocateOperator);
		}
		return stockidMap;
	}
	
	/**
	 * 手动分配更新分配头信息
	 * @param doAllocateHeader
	 * @throws DeliveryException
	 */
	private void modifyDoAllocateHeader(DoAllocateHeader doAllocateHeader) throws DeliveryException{
		List<DoAllocateDetail> deliveryOrderDetails = doAllocateHeader.getDoAllocateDetails();
		int initCount = 0;
		int allAllocatedCount = 0;
		int parentCount = 0;
		for (DoAllocateDetail doDetail : deliveryOrderDetails) {
			if (doDetail.getIsDoLeaf().intValue() == 0) {
				parentCount++;
				continue;
			}
			String lineStatus = doDetail.getLineStatus();
			if (lineStatus.equals(Constants.DoStatus.INITIAL.getValue())) {
				initCount++;
			} else if (lineStatus.equals(Constants.DoStatus.ALLALLOCATED.getValue())) {
				allAllocatedCount++;
			}
		}
		
		int doDetailSize = deliveryOrderDetails.size() - parentCount;
		if (allAllocatedCount == doDetailSize ) {
			doAllocateHeader.setStatus(Constants.DoStatus.ALLALLOCATED.getValue());
			//doAllocateHeader.setAisles(doAllocateHeaderDAO.getAislesById(doAllocateHeader.getId()));
		} else if (initCount == doDetailSize) {
			doAllocateHeader.setStatus(Constants.DoStatus.INITIAL.getValue());
		} else {
			doAllocateHeader.setStatus(Constants.DoStatus.PARTALLOCATED.getValue());
		}
		additionalOpr2Alloc(doAllocateHeader);//分配同时做的一些额外的操作
		this.doAllocateHeaderDAO.update(doAllocateHeader);
	}
	 
	/**
	 * 分配审核
	 */
	@Override
    @Transactional
    public void checkAlloc(Long allocateHeaderId) {
        DoAllocateHeader doAllocateHeader = this.doAllocateHeaderDAO.get(allocateHeaderId);
        if (doAllocateHeader == null) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        //类型判断
        String doType = doAllocateHeader.getDoType();
		boolean isRightType = doType.equals(Constants.DoType.ALLOT.getValue()) || doType.equals(Constants.DoType.RTV.getValue()) || doType.equals(DoType.WHOLESALE.getValue());
		if (!isRightType) {
			throw new DeliveryException(DeliveryException.CHECK_ASSIGN_TYPE_WORNG);
		}
        //状态判断
        String status = doAllocateHeader.getStatus();
        if (!status.equals(Constants.DoStatus.PARTALLOCATED.getValue())) {
            throw new DeliveryException(DeliveryException.CHECK_ASSIGN_STATUS_WRONG);
        }
        //组合产品子产品未分配完成，分配审核不允许通过 活动
	    Long childCounts = this.doAllocateDetailDAO.findMixChildCountsByStatus(DoStatus.ALLALLOCATED.getValue() , allocateHeaderId);
	    if(childCounts.longValue() > 0 ){
	    	throw new DeliveryException(DeliveryException.CHECKASSIGN_FAILED_WRONGSTATUS);
	    }
	    //检查是否允许缺发
		if (YesNo.NO.getValue().equals(doAllocateHeader.getDeliveryOrderHeader().getLackShipFlag())) {
	    	//不允许缺发
			throw new DeliveryException(DeliveryException.DO_CAN_NOT_LACK_SHIP);
		}

		doAllocateHeader.setAllocTime(DateUtil.getNowTime());
	    doAllocateHeader.setStatus(Constants.DoStatus.ALLALLOCATED.getValue());
        doAllocateHeaderDAO.update(doAllocateHeader);
        //发货单分配审核时需要同时修改明细状态为分配完成 
        doAllocateDetailDAO.updateDoDetailStatusByDoId(doAllocateHeader.getId(), doAllocateHeader.getStatus());
        
        this.doAllocateDetailDAO.getSession().flush();
        this.doAllocateDetailDAO.getSession().clear();
        
        this.manualAllocSynAlc2DocDo(doAllocateHeader.getId());

        //记录日志
        orderLogService.saveLog(doAllocateHeader.getDeliveryOrderHeader(),
				OrderLogConstants.OrderLogType.ALLOCATE_CHECK.getValue(),
				ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_ALLOCATE_CHECK));
    }
	
	/**
	 * 根据DeliveryOrderHeader创建订单分配头
	 */
    @Override
    @Transactional
    public DoAllocateHeader createDoAllocateHeader(DeliveryOrderHeader doHeader){
		String user = !StringUtil.isEmpty(identity.getCredentials().getUsername()) ? identity.getCredentials().getUsername() : "SYSTEM";
		DoAllocateHeader doAllocateHeader = new DoAllocateHeader();
    	doAllocateHeader.setAddress(doHeader.getAddress());
    	doAllocateHeader.setAmountPayable(doHeader.getAmountPayable());
    	doAllocateHeader.setCarrierId(doHeader.getCarrierId());
    	doAllocateHeader.setCity(doHeader.getCity());
    	doAllocateHeader.setConsigneeName(doHeader.getConsigneeName());
    	doAllocateHeader.setContact(doHeader.getContact());
    	doAllocateHeader.setCountry(doHeader.getCountry());
    	doAllocateHeader.setCounty(doHeader.getCounty());
    	doAllocateHeader.setCreatedAt(doHeader.getCreatedAt());
    	doAllocateHeader.setCreatedBy(user);
		doAllocateHeader.setIsDirect(doHeader.getIsDirect());
    	doAllocateHeader.setDoCreateTime(doHeader.getDoCreateTime());
    	doAllocateHeader.setDoNo(doHeader.getDoNo());
    	doAllocateHeader.setDoType(doHeader.getDoType());
    	doAllocateHeader.setEdi1(doHeader.getEdi1());
    	doAllocateHeader.setEdi2(doHeader.getEdi2());
    	doAllocateHeader.setEmail(doHeader.getEmail());
    	doAllocateHeader.setExchangeFlag(doHeader.getExchangeFlag());
    	doAllocateHeader.setExpectedArriveTime1(doHeader.getExpectedArriveTime1());
    	doAllocateHeader.setExpectedArriveTime2(doHeader.getExpectedArriveTime2());
    	doAllocateHeader.setExpectedQty(doHeader.getExpectedQty());
    	doAllocateHeader.setExpectedReceiveTime(doHeader.getExpectedReceiveTime());
    	doAllocateHeader.setFlowFlag(doHeader.getFlowFlag());
    	doAllocateHeader.setGrossWt(doHeader.getGrossWt());
    	doAllocateHeader.setId(doHeader.getId());
    	doAllocateHeader.setInvoiceFlag(doHeader.getInvoiceFlag());
    	doAllocateHeader.setInvoiceQty(doHeader.getInvoiceQty());
    	doAllocateHeader.setIsHalfDayDelivery(doHeader.getIsHalfDayDelivery());
    	doAllocateHeader.setLastDcName(doHeader.getLastDcName());
    	doAllocateHeader.setMobile(doHeader.getMobile());
    	doAllocateHeader.setNeedCancel(doHeader.getNeedCancel());
    	doAllocateHeader.setNotes(doHeader.getNotes());
    	doAllocateHeader.setOrderAmount(doHeader.getOrderAmount());
		doAllocateHeader.setOrderSubType(doHeader.getOrderSubType());
    	doAllocateHeader.setOrderDeliveryFee(doHeader.getOrderDeliveryFee());
    	doAllocateHeader.setOrderFrostRebate(doHeader.getOrderFrostRebate());
    	doAllocateHeader.setOrigId(doHeader.getOrigId());
    	doAllocateHeader.setPaymentMethodName(doHeader.getPaymentMethodName());
    	doAllocateHeader.setPaymentType(doHeader.getPaymentType());
    	doAllocateHeader.setPostCode(doHeader.getPostCode());
    	doAllocateHeader.setProductAmount(doHeader.getProductAmount());
    	doAllocateHeader.setProvince(doHeader.getProvince());
		doAllocateHeader.setProvinceName(doHeader.getProvinceName());
		doAllocateHeader.setCityName(doHeader.getCityName());
		doAllocateHeader.setCountyName(doHeader.getCountyName());
    	doAllocateHeader.setReceivable(doHeader.getReceivable());
    	doAllocateHeader.setRefNo1(doHeader.getRefNo1());
    	doAllocateHeader.setRefNo2(doHeader.getRefNo2());
		doAllocateHeader.setReleaseStatus(doHeader.getReleaseStatus() == null ? "RL" : doHeader.getReleaseStatus());
		doAllocateHeader.setReplStatus(doHeader.getReplStatus());
    	doAllocateHeader.setSortGridNo(doHeader.getSortGridNo());
    	doAllocateHeader.setSpecFlag(doHeader.getSpecFlag());
    	doAllocateHeader.setStationId(doHeader.getStationId());
    	doAllocateHeader.setStatus(doHeader.getStatus());
    	doAllocateHeader.setSupplierId(doHeader.getSupplierId());
    	doAllocateHeader.setTelephone(doHeader.getTelephone());
    	doAllocateHeader.setTotalAllocatEa(doHeader.getTotalAllocatEa());
    	doAllocateHeader.setTotalGrossWt(doHeader.getTotalGrossWt());
    	doAllocateHeader.setUpdatedAt(DateUtil.getNowTime());
    	doAllocateHeader.setUpdatedBy(user);
    	doAllocateHeader.setUserDeffine1(doHeader.getUserDeffine1());
    	doAllocateHeader.setUserDeffine3(doHeader.getUserDeffine3());
    	doAllocateHeader.setUserDeffine4(doHeader.getUserDeffine4());
    	doAllocateHeader.setUserDeffine5(doHeader.getUserDeffine5());
    	doAllocateHeader.setUserDeffine6(doHeader.getUserDeffine6());
    	doAllocateHeader.setVolume(doHeader.getVolume());
    	doAllocateHeader.setDisplayPrice(doHeader.getDisplayPrice());
    	doAllocateHeader.setTranType(doHeader.getTranType());
    	doAllocateHeader.setIsValuable(doHeader.getIsValuable());
    	doAllocateHeader.setIsFresh(doHeader.getIsFresh());
    	doAllocateHeader.setPlanShipTime(doHeader.getPlanShipTime());
    	doAllocateHeader.setWarehouseId(doHeader.getWarehouseId());
    	doAllocateHeader.setInWine(doHeader.getInWine());
    	doAllocateHeader.setAisles(doHeader.getAisles());
    	doAllocateHeader.setCheckFlag(doHeader.getCheckFlag());
    	doAllocateHeader.setIsGroup(doHeader.getIsGroup());
    	doAllocateHeader.setIsAutoWave(doHeader.getIsAutoWave());
    	doAllocateHeader.setHaveCfy(doHeader.getHaveCfy());
		doAllocateHeader.setShopId(doHeader.getShopId());
		doAllocateHeader.setVolumeType(doHeader.getVolumeType());
		doAllocateHeader.setOriginalSoCode(doHeader.getOriginalSoCode());
		doAllocateHeader.setBusinessCustomerId(doHeader.getBusinessCustomerId());
		doAllocateHeader.setSellerRemark(doHeader.getSellerRemark());
		doAllocateHeader.setBuyerRemark(doHeader.getBuyerRemark());
		doAllocateHeader.setNoStockFlag(doHeader.getNoStockFlg() == null ? Constants.YesNo.NO.getValue() : doHeader.getNoStockFlg());
		doAllocateHeader.setPayTime(doHeader.getPayTime());
		doAllocateHeader.setMerchantId(doHeader.getMerchantId());
		doAllocateHeader.setCycleClass(doHeader.getCycleClass());
		doAllocateHeader.setSimilaritySign(doHeader.getSimilaritySign());
		doAllocateHeader.setChannelCode(doHeader.getChannelCode());
		doAllocateHeader.setTransportWendy(doHeader.getTransportWendy());
		Set<Long> skuIds = Sets.newHashSet();
		for (DeliveryOrderDetail deliveryOrderDetail : doHeader.getDoDetails()) {
			if (Constants.YesNo.YES.getValue().equals(deliveryOrderDetail.getIsDoLeaf())) {
				skuIds.add(deliveryOrderDetail.getSkuId());
			}
		}
		doAllocateHeader.setSkuQty(skuIds.size());
		
		doAllocateHeaderDAO.save(doAllocateHeader);
		doAllocateHeaderDAO.getSession().flush();
		doAllocateHeaderDAO.getSession().clear();
    	return doAllocateHeader;
    }
    
    /**
     * 根据DeliveryOrderDetail列表创建订单分配明细列表
     */
    @Override
    @Transactional
    public List<DoAllocateDetail> createDoAllocateDetail(List<DeliveryOrderDetail> doDetails){
    	String user = !StringUtil.isEmpty(identity.getCredentials().getUsername())?
    			identity.getCredentials().getUsername() : "SYSTEM";
    	List<DoAllocateDetail> doAllocateDetailList = new ArrayList<DoAllocateDetail>();
		Map<Long, ImmutablePair<BigDecimal, BigDecimal>> qtyMap = null;

		for(DeliveryOrderDetail doDetail : doDetails) {
    		DoAllocateDetail doAllocateDetail = new DoAllocateDetail();
    		doAllocateDetail.setAllocatedQty(doDetail.getAllocatedQty());
			if (doDetail.getLineStatus().compareTo(DoStatus.PARTALLOCATED.getValue()) >= 0) {
				if (qtyMap == null) {
					qtyMap = pickTaskService.countTask(doDetail.getDoHeader().getId());
				}
				ImmutablePair<BigDecimal, BigDecimal> qtyPair = qtyMap.get(doDetail.getId());
				BigDecimal qtyPcs = BigDecimal.ZERO;
				BigDecimal qtyUnit = BigDecimal.ZERO;
				if (qtyPair != null) {
					qtyPcs = qtyPair.getLeft() == null ? BigDecimal.ZERO : qtyPair.getLeft();
					qtyUnit = qtyPair.getRight() == null ? BigDecimal.ZERO : qtyPair.getRight();
				}
				doAllocateDetail.setAllocatedQtyPcs(qtyPcs);
				doAllocateDetail.setAllocatedQtyUnit(qtyUnit);
			}
			doAllocateDetail.setAllocationRule(doDetail.getAllocationRule());
    		doAllocateDetail.setCreatedAt(DateUtil.getNowTime());
    		doAllocateDetail.setCreatedBy(user);
    		doAllocateDetail.setDoHeaderId(doDetail.getDoHeader().getId());
			if (Constants.YesNo.YES.getValue().equals(doDetail.getIsDoLeaf()) || doDetail.getIsDoLeaf() == null) {
				PackageInfoDetail packageInfoDetail = packageInfoDetailService.findMainPackage(doDetail.getSkuId());
				// 电商订单或者期望数量存在小数，只分配散件；
				if (packageInfoDetail == null || packageInfoDetail.getQty().compareTo(BigDecimal.ONE) <= 0 || new BigDecimal(doDetail.getExpectedQty().intValue()).compareTo(doDetail.getExpectedQty()) != 0 || doDetail.getDoHeader
						().getDoType().equals(DoType.SELL.getValue())) {
					doAllocateDetail.setExpectedQtyPcs(doDetail.getExpectedQty());
					doAllocateDetail.setExpectedQtyUnit(BigDecimal.ZERO);
				} else {
					doAllocateDetail.setExpectedQtyPcs(doDetail.getExpectedQty().remainder(packageInfoDetail.getQty()).setScale(0, BigDecimal.ROUND_HALF_UP));
					doAllocateDetail.setExpectedQtyUnit(doDetail.getExpectedQty().divide(packageInfoDetail.getQty(), 0, BigDecimal.ROUND_DOWN));
				}
			} else {
				doAllocateDetail.setExpectedQtyPcs(doDetail.getExpectedQty());
				doAllocateDetail.setExpectedQtyUnit(BigDecimal.ZERO);
			}
    		doAllocateDetail.setExpectedQty(doDetail.getExpectedQty());
    		doAllocateDetail.setGrossweight(doDetail.getGrossweight());
    		doAllocateDetail.setId(doDetail.getId());
    		doAllocateDetail.setIsDamaged(doDetail.getIsDamaged());
    		doAllocateDetail.setIsDoLeaf(doDetail.getIsDoLeaf());
    		doAllocateDetail.setIsPromote(doDetail.getIsPromote());
    		doAllocateDetail.setIsValueables(doDetail.getIsValueables());
    		doAllocateDetail.setLineNo(doDetail.getLineNo());
    		doAllocateDetail.setLineStatus(doDetail.getLineStatus());
            doAllocateDetail.setLotNo(doDetail.getLotNo());
    		doAllocateDetail.setLotatt01(doDetail.getLotatt01());
    		doAllocateDetail.setLotatt02(doDetail.getLotatt02());
    		doAllocateDetail.setLotatt03(doDetail.getLotatt03());
    		doAllocateDetail.setLotatt04(doDetail.getLotatt04());
    		doAllocateDetail.setLotatt05(doDetail.getLotatt05());
    		doAllocateDetail.setLotatt06(doDetail.getLotatt06());
    		doAllocateDetail.setLotatt07(doDetail.getLotatt07());
    		doAllocateDetail.setLotatt08(doDetail.getLotatt08());
    		doAllocateDetail.setLotatt09(doDetail.getLotatt09());
    		doAllocateDetail.setLotatt10(doDetail.getLotatt10());
    		doAllocateDetail.setLotatt11(doDetail.getLotatt11());
    		doAllocateDetail.setLotatt12(doDetail.getLotatt12());
            doAllocateDetail.setLotatt13(doDetail.getLotatt13());
            doAllocateDetail.setLotatt14(doDetail.getLotatt14());
            doAllocateDetail.setLotatt15(doDetail.getLotatt15());
            doAllocateDetail.setLotatt16(doDetail.getLotatt16());
            doAllocateDetail.setLotatt17(doDetail.getLotatt17());
    		doAllocateDetail.setNeedReplQty(doDetail.getNeedReplQty());
    		doAllocateDetail.setNetweight(doDetail.getNetweight());
    		doAllocateDetail.setNotes(doDetail.getNotes());
    		doAllocateDetail.setOrigDetailId(doDetail.getOrigDetailId());
    		doAllocateDetail.setOrigHeaderId(doDetail.getOrigHeaderId());
    		doAllocateDetail.setPackageId(doDetail.getPackageId());
    		doAllocateDetail.setPackDetailId(doDetail.getPackDetailId());
    		doAllocateDetail.setParentId(doDetail.getParentId());
    		doAllocateDetail.setPickzone(doDetail.getPickzone());
    		doAllocateDetail.setPrice(doDetail.getPrice());
    		doAllocateDetail.setSkuId(doDetail.getSkuId());
    		doAllocateDetail.setTargetWarehouseId(doDetail.getTargetWarehouseId());
    		doAllocateDetail.setUom(doDetail.getUom());
    		doAllocateDetail.setUpdatedAt(DateUtil.getNowTime());
    		doAllocateDetail.setUpdatedBy(user);
    		doAllocateDetail.setUserdefine1(doDetail.getUserdefine1());
    		doAllocateDetail.setUserdefine2(doDetail.getUserdefine2());
    		doAllocateDetail.setUserdefine3(doDetail.getUserdefine3());
    		doAllocateDetail.setUserdefine4(doDetail.getUserdefine4());
    		doAllocateDetail.setVolume(doDetail.getVolume());
    		doAllocateDetail.setWarehouseId(doDetail.getWarehouseId());
    		doAllocateDetail.setWineFlag(doDetail.getWineFlag());
    		doAllocateDetail.setLeastAvailableDay(doDetail.getLeastAvailableDay());
    		// 设置销售等级 货品等级
			Integer goodsGrade = doDetail.getGoodsGrade();
			doAllocateDetail.setGoodsGrade(goodsGrade);
			doAllocateDetail.setSalesGrade(doDetail.getSalesGrade());
			// 效期管控品 设置时间段 货品等级
			doAllocateDetail.setMinExp(doDetail.getMinExp());
			doAllocateDetail.setMaxExp(doDetail.getMaxExp());

    		doAllocateDetailDAO.save(doAllocateDetail);
    		doAllocateDetailList.add(doAllocateDetail);
			doAllocateDetailDAO.getSession().flush();
    	}
    	return doAllocateDetailList;
    }
    
    @Override
    public Integer getFailCheckConfig(){
        Integer failCheck = SystemConfig.getConfigValueInt("Delivery.AutoAllocateJob.FailCheck", null);
        return failCheck;
    }
	
	@Override
    @Transactional
	public Map<String, List<String>> autoAllocate(Long alcId) throws Exception {
		return autoAllocate(alcId, Lists.<Long>newArrayList());
	}
	
	@Override
	@Transactional
	public List<Long> findIdForWave(BigWaveFilter bigWaveFilter) {
		return this.doAllocateHeaderDAO.findIdForWave(bigWaveFilter);
	}
	
	//是否成功分配：只有订单存在，且分配成功才返回true
	@Override
    @Transactional
	public Map<String, List<String>> autoAllocate(Long alcId, List<Long> ids) throws Exception {
		if (alcId == null) {
			return Maps.newHashMap();
		}
		DoAllocateHeader alcHeader = doAllocateHeaderDAO.get(alcId);
		if (alcHeader == null){
			return Maps.newHashMap();
		}
		//DO取消拦截
		if (deliveryOrderService.doCancelIntercept(alcHeader.getId())){
			Map<String, List<String>> map =  Maps.newHashMap();
			map.put(IDoAllocate.DO_CANCEL,Lists.<String>newArrayList());
			return map;
		}

		assignAutoCheck(alcHeader);
		
		alcHeader.setReplStatus(Constants.DoReplStatus.NONE.getValue());
		
		List<DoAllocateDetail> needFrozenAlcDetails = new ArrayList<DoAllocateDetail>();
		Map<String, List<String>> allocateResult = Maps.newHashMap();
		// 分配忽略区域, 默认全区域可分配
		List<DoAllocateDetail> alcDetails = alcHeader.getDoAllocateDetails();
		allocateDetail(ids, alcHeader, alcDetails, needFrozenAlcDetails, allocateResult, null);
		checkDoStatusAfterAllocate(alcHeader);
		
		if (!ListUtil.isNullOrEmpty(needFrozenAlcDetails)) {
			autoFrozenOnAllocate(alcHeader.getId(), needFrozenAlcDetails, Constants.Reason.ALLOC_LACK.getValue(), ResourceUtils.getDispalyString("frozen.holdWho.default"));
		}
		
		return allocateResult;
	}
	
	private void allocateDetail(List<Long> ids, DoAllocateHeader alcHeader, List<DoAllocateDetail> alcDetails, List<DoAllocateDetail> needFrozenAlcDetails, Map<String, List<String>> allocateResult, String region) throws Exception {

		String result=null;
		// 2C订单 预包分配
		if (Constants.DoType.SELL.getValue().equals(alcHeader.getDoType())) {
			result = doAllocate.skuCombiStockAllocate(alcHeader, allocateResult);
			if (IDoAllocate.ALC_RESULT_SUCCESS.equals(result)) {
				// 生成零时记录
					TempCarton tc = tempCartonService.generateTempCarton(alcHeader.getDeliveryOrderHeader().getId());
						tempCartonService.saveOrUpdate(tc);
				alcHeader.setAllocTime(DateUtil.getNowTime());
				return;
			}
		}
		// 普通分配
		for (DoAllocateDetail alcDetail : alcDetails) {
			if (ListUtil.isNotEmpty(ids) && !ids.contains(alcDetail.getId())) {
				continue;
			}
			SkuDTO skuDTO = skuCache.getSku(alcDetail.getSkuId());
			String productCode = skuDTO.getProductCode();
			try {
				//开始分配前清空待补货数据
				alcDetail.setNeedReplQty(BigDecimal.ZERO);
				alcDetail.setNoStockFlag(Constants.YesNo.NO.getValue());
				
				result = executeAllocate(alcDetail, region);
				if (!IDoAllocate.ALC_RESULT_SUCCESS.equals(result)) {
					if (allocateResult.get(result) == null) {
						allocateResult.put(result, Lists.<String>newArrayList(productCode));
					} else {
						allocateResult.get(result).add(productCode);
					}
					log.info("allocate result: " + result);
				}

				
				if (IDoAllocate.NEED_REPL.equals(result)) {
					alcHeader.setReplStatus(DoReplStatus.WAIT.getValue());
					alcHeader.setReplStartTime(DateUtil.getNowTime());
					if (StringUtil.isNotEmpty(region)) {
						alcHeader.setReplRegionId(Long.valueOf(region));
					}
				}
				
				doDetailDAO.update(alcDetail);
				doDetailDAO.getSession().flush();
			} catch (AllocateException ex) {
				String exMsg = IDoAllocate.NO_ENOUGH_STOCK_QTY;
				if (allocateResult.get(exMsg) == null) {
					allocateResult.put(exMsg, Lists.<String>newArrayList(productCode));
				} else {
					allocateResult.get(exMsg).add(productCode);
				}
				
				//log.error("allocateError", ex);
				needFrozenAlcDetails.add(alcDetail);
				alcHeader.setNoStockFlag(YesNo.YES.getValue());
			}
		}
		// 优贝仓 && 私域渠道生成零时箱信息
		if(Config.isDefaultFalse(Keys.Delivery.gene_temp_carton, Config.ConfigLevel.WAREHOUSE)&&PRIVATE_CHANEL_CODE_SET.contains(alcHeader.getChannelCode()) ){
			// 生成零时记录
			TempCarton tc = tempCartonService.generateTempCarton(alcHeader.getDeliveryOrderHeader().getId());
			tempCartonService.saveOrUpdate(tc);
		}
	}




	@Override
    public List<OverAllocateDto> queryMoreAllocate() {
        return doAllocateHeaderDAO.queryMoreAllocate();
    }

    private String executeAllocate(DoAllocateDetail alcDetail, String region) throws Exception {
		DoAllocateHeader alcHeader = alcDetail.getDoAllocateHeader();
		if (StringUtil.isIn(alcHeader.getDoType(), Constants.DoType.WHOLESALE.getValue(), Constants.DoType.ALLOT.getValue(), Constants.DoType.RTV.getValue(), DoType.MPS_OUT.getValue())) {
			return wholesaleAllocate.executeAllocate(alcDetail, region);
		} else if (Constants.DoType.SELL.getValue().equals(alcHeader.getDoType())) {
			return doAllocate.executeAllocate(alcDetail, region);
		}
		return null;
	}
	
	/**
     * 根据分配返回值确定返回消息提示常量
     * @param returnValue 分配结果返回值
     * @return 消息提示常量
     */
    private String findMessageByReturnValue(Boolean isDOType, String returnValue) {
		if (IDoAllocate.NO_ENOUGH_STOCK_QTY.equals(returnValue) && (!isDOType)) {
			return DeliveryConstant.STOCK_NOT_ENOUGH_FOR_AALOT;
		}
        
        String constantMes = "";
        if (StringUtil.isEmpty(returnValue)) {
            return constantMes;
        }
        constantMes = DeliveryConstant.getAllocatMessage(returnValue);
        return constantMes;
    }
	
	private void assignAutoCheck(DoAllocateHeader alcHeader) {
		if (alcHeader == null) {
			throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
		}
		
		//默认配置商不分配
		if (DoType.SELL.getValue().equals(alcHeader.getDoType()) && Carrier.DEFAULT_ID.equals(alcHeader.getCarrierId())) {
			throw new DeliveryException(DeliveryException.CAN_NOT_SUPPORT_CARRIER);
		}
		
		//只有初始化或者部分非配状态且释放状态的时候才能进行自动分配；
		if (!(StringUtil.isIn(alcHeader.getStatus(), DoStatus.INITIAL.getValue(), DoStatus.PARTALLOCATED.getValue()) && ReleaseStatus.RELEASE.getValue().equals(alcHeader.getReleaseStatus()))) {
			throw new DeliveryException(DeliveryException.DO_CANNOT_ASSIGN);
		}
	}
	
	/**
     * 分配完成后检查订单状态是否需要进行更新
     * @param alcHeader
     */
    @Override
    public void checkDoStatusAfterAllocate(DoAllocateHeader alcHeader) {
		log.debug("Check Do Status:[id: {}, status: #1, replStatus: #2, allocTime: #3]", alcHeader.getId(), alcHeader.getStatus(), alcHeader.getReplStatus(), alcHeader.getAllocTime());
	
		int finishedCount = 0; //分配完成的明细数量
		int needAllocateCount = 0; //需要分配的明细数量
		boolean isPartAllocated = false;
		for (DoAllocateDetail doDetail : alcHeader.getDoAllocateDetails()) {
			if (DoStatus.PARTALLOCATED.getValue().equals(doDetail.getLineStatus())) {
				isPartAllocated = true;
				break;
			} else if (Integer.valueOf(1).equals(doDetail.getIsDoLeaf())) {
				needAllocateCount++;
				if (DoStatus.ALLALLOCATED.getValue().equals(doDetail.getLineStatus())) {
					finishedCount++;
				}
			}
		}
		String doStatus;
        if (isPartAllocated) {
            doStatus = Constants.DoStatus.PARTALLOCATED.getValue();
        } else if (finishedCount == 0) {
            doStatus = Constants.DoStatus.INITIAL.getValue();
        } else if (finishedCount < needAllocateCount) {
            doStatus = Constants.DoStatus.PARTALLOCATED.getValue();
        } else {
            doStatus = Constants.DoStatus.ALLALLOCATED.getValue();

			alcHeader.setAisles(doAllocateHeaderDAO.getAislesById(alcHeader.getId()));
        }
        alcHeader.setStatus(doStatus);
        alcHeader.setAllocTime(DateUtil.getNowTime());

        doAllocateHeaderDAO.update(alcHeader);
        additionalOpr2Alloc(alcHeader);//分配的一些额外操作
        //自动分配，分配中只同步头状态
        if (Constants.DoStatus.ALLALLOCATED.getValue().equals(alcHeader.getStatus())) {
            synAlc2DocDo(alcHeader.getId());
        } else {
            DeliveryOrderHeader doHeader = this.doHeaderDAO.get(alcHeader.getId());

            if (doHeader == null) {
                throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
            }
            
            if(doHeader.getNeedCancel()){
                throw new DeliveryException(DeliveryException.DO_IS_CANCELED);
            }

            doHeader.setStatus(alcHeader.getStatus());
            doHeader.setAllocTime(alcHeader.getAllocTime());
            doHeaderDAO.update(doHeader);
        }
    }
    
    /**
     * 手动分配同步ALC分配表和DOC表状态
     * @param doAllocateHeaderId
     */
    private void manualAllocSynAlc2DocDo(Long doAllocateHeaderId) {
        DoAllocateHeader doAllocateHeader = this.doAllocateHeaderDAO.get(doAllocateHeaderId);

        DeliveryOrderHeader doHeader = this.doHeaderDAO.get(doAllocateHeaderId);
        if (doHeader == null) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        doHeader.setStatus(doAllocateHeader.getStatus());
        doHeader.setAllocTime(doAllocateHeader.getAllocTime());
        doHeaderDAO.update(doHeader);

        //如果分配完成，同步状态
        if (Constants.DoStatus.ALLALLOCATED.getValue().equals(doAllocateHeader.getStatus())) {
            List<DoAllocateDetail> doAllocateDetailList = doAllocateHeader.getDoAllocateDetails();
            for (DoAllocateDetail doAllocateDetail : doAllocateDetailList) {
                doDetailDAO.updateManualAllocInfo(doAllocateDetail);
            }
		} else if (StringUtil.isIn(doAllocateHeader.getStatus(), Constants.DoStatus.PARTALLOCATED.getValue(),
            Constants.DoStatus.INITIAL.getValue())) {
            //如果是部分分配或初始化则更新原表为初始化
            if (!Constants.DoStatus.INITIAL.getValue().equals(doHeader.getStatus())) {
                this.doDetailDAO.updateDoDetail2Initial(doHeader.getId());
            }
		}
    }

	private void cancelAllocDo(DeliveryOrderHeader doHeader, DoExceptionLog doExceptionLog, DoAllocateHeader doAlcHeader, Integer openDelPickcfg) {


	}


	private void cancleInvoice(DeliveryOrderHeader doHeader) {
		if(!Long.valueOf(YesNo.YES.getValue()).equals(doHeader.getInvoiceFlag())){
			return;
		}
		List<InvoiceNo> invoiceNoList = cancelInvoiceService.getInvoiceNoByDoNo(doHeader.getDoNo());
		if(ListUtil.isNullOrEmpty(invoiceNoList)){
			return;
		}

		for (InvoiceNo invoiceNo : invoiceNoList) {
			cancelInvoiceService.cancelSingleInvoice(invoiceNo.getId());
		}
		//红冲电子发票
		electronicInvoiceService.writeBackInvoice(doHeader);
	}


    /**
    * 同步ALC分配表和DOC表状态
    * @param doAllocateHeaderId
    */
    private void synAlc2DocDo(Long doAllocateHeaderId) {
        DoAllocateHeader doAllocateHeader = this.doAllocateHeaderDAO.get(doAllocateHeaderId);
        DeliveryOrderHeader doHeader = this.doHeaderDAO.get(doAllocateHeaderId);

        if (doHeader == null) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }

        doHeader.setStatus(doAllocateHeader.getStatus());
        doHeader.setAllocTime(doAllocateHeader.getAllocTime());
        doHeader.setAisles(doAllocateHeader.getAisles());
        doHeader.setReplStartTime(doAllocateHeader.getReplStartTime());
        doHeader.setReplEndTime(doAllocateHeader.getReplEndTime());
        doHeaderDAO.update(doHeader);

		//记录日志
		orderLogService.saveLog(doHeader, OrderLogConstants.OrderLogType.ALLOCATE_COMPLETE.getValue(), ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_ALLOCATE_COMPLETE));

        List<DoAllocateDetail> doAllocateDetailList = doAllocateHeader.getDoAllocateDetails();

        for (DoAllocateDetail doAllocateDetail : doAllocateDetailList) {
            doDetailDAO.updateManualAllocInfo(doAllocateDetail);
        }
    }
    
    /**
     * 同步订单信息到订单分配头
     */
    @Override
    @Transactional
    public void sycDo2Alc(Long id) {
        DoAllocateHeader doAllocateHeader = this.doAllocateHeaderDAO.get(id);
        Integer noStockFlg = YesNo.NO.getValue();
        if (doAllocateHeader != null) {
			noStockFlg = doAllocateHeader.getNoStockFlag();
            this.removeAllocate(id);
            this.doAllocateHeaderDAO.getSession().flush();
            this.doAllocateHeaderDAO.getSession().clear();
        }

        DeliveryOrderHeader doHeader = this.doHeaderDAO.get(id);
		doHeader.setNoStockFlg(noStockFlg);
        if (doHeader == null) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        
        this.createDoAllocateHeader(doHeader);
        List<DeliveryOrderDetail> doDetails = deliveryOrderService.getDoDetailByDoHeaderId(id);
        this.createDoAllocateDetail(doDetails);
    }

    /**
     * 订单分配自动冻结
     */
    @Override
    @Transactional
	public void autoFrozenOnAllocate(Long doHeaderId, List<DoAllocateDetail> needFrozenDoDetails, String reasonCode,
                                      String holdWho) {
        log.debug("Frozen Do:[id: {}, reasonCode: #1]", doHeaderId, reasonCode);
        
        DeliveryOrderHeader doHeader = deliveryOrderService.validate2Frozen(doHeaderId, reasonCode, false);
        if (null == doHeader) {
            return;
        }
        //删除异常已经完成的订单的缺货明细。
        if (DoExpStatus.COMPLETE.getValue().equals(doHeader.getExceptionStatus())) {
            doLackHeaderService.deleteLackInfoByDoId(doHeader.getId());
        }
        
        String expStatus = "";
        //如果配置自动通知客服，则缺货自动通知客服。
        Boolean lackAutoAnounceCs = SysConfigHelper.getSwitchDefalutOpen("alloc.lack.autoAnounceCs");
		if (lackAutoAnounceCs) {
			DoExpDto dto = new DoExpDto();
            dto.setId(doHeaderId);
            dto.setHoldCode(Constants.Reason.ALLOC_LACK.getValue());
            dto.setNotifyType(NotifyCSType.AUTO.getValue());
			expFacadeService.callCSCreateDatas(dto);
			
			Boolean isNoNeedCsFeedBack = SystemConfig.configIsClosed("notify.cs.notNeed.feedback", ParamUtil.getCurrentWarehouseId());
			if (isNoNeedCsFeedBack) {
				if (DoStatus.INITIAL.getValue().equals(doHeader.getStatus())) {
					expStatus = DoExpStatus.TO_BE_REPL.getValue();
				} else {
					expStatus = DoExpStatus.TO_BE_ROLLBACK.getValue();
				}
			} else {
				expStatus = DoExpStatus.TO_BE_FEEDBACK.getValue();
			}
		} else {
			expStatus = DoExpStatus.TO_BE_ANNOUNCE.getValue();
		}
        
		deliveryOrderService.frozen(doHeader, reasonCode, null, holdWho, expStatus, ExOpType.ALLOC.getValue());

		Map<String, String> reasonMap = Dictionary.getDictionary("REASON_HDD");
		String holdReason = reasonMap.get(Reason.ALLOC_LACK.getValue());
        //写缺货明细
		StringBuffer lackSkuCode = new StringBuffer();
        for (DoAllocateDetail doDetail : needFrozenDoDetails) {
            //插入新的缺货明细记录。
            DoLackDetail lackDetail = new DoLackDetail();
            lackDetail.setDoHeaderId(doDetail.getDoHeaderId());
            lackDetail.setDoDetailId(doDetail.getId());
            lackDetail.setSkuId(doDetail.getSkuId());
            lackDetail.setHoldReason(holdReason);
            lackDetail.setCreatedBy(holdWho);
            lackDetail.setUpdatedBy(holdWho);
            lackDetail.setQty(doDetail.getExpectedQty().subtract(doDetail.getAllocatedQty()));
            doLackDetailDAO.saveOrUpdate(lackDetail);
			
			lackSkuCode.append(skuCache.getSku(doDetail.getSkuId()).getProductCode()).append("，");
        }

		//分配缺货日志
		orderLogService.saveLog(doHeader,
				OrderLogConstants.OrderLogType.ALLOCATE_LACK.getValue(),
				ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_ALLOCATE_LACK,null,lackSkuCode.toString()));
    }

    /**
     * 批量移出订单分配信息
     */
    @Override
    @Loggable
    @Transactional
    public void removeAllocate(List<Long> ids) {
        if (ListUtil.isNullOrEmpty(ids)) {
        	return;
        }
        doAllocateHeaderDAO.remove(ids);

        doAllocateDetailDAO.removeByHeaderId(ids);
    }
    
    /**
     * 移出订单分配信息
     */
    @Override
    @Transactional
    public void removeAllocate(Long id) {
        if (id == null) {
        	return;
        }

        List<Long> headerIdList = new ArrayList<Long>();
        headerIdList.add(id);

        this.removeAllocate(headerIdList);
    }

    /**
     * 获取订单分配明细
     */
	@Override
	public DoAllocateDetail getDoAllocateDetailById(Long doAllocateDetailId) {
		return doAllocateDetailDAO.get(doAllocateDetailId);
	}
	
	/**
	 * 取消分配
	 */
    @Override
    @Transactional
    public void cancelAssign(Long doHeaderId, List<Long> doDetailIds, boolean isAuto, boolean checkReleaseStatus) throws DeliveryException {
        if (doHeaderId == null) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        if (ListUtil.isNullOrEmpty(doDetailIds)) {
            throw new DeliveryException(DeliveryException.NO_RECORD_SELECTED);
        }
        DeliveryOrderHeader doHeader = doHeaderDAO.get(doHeaderId);

        //只有部分分配或者分配完成状态的订单才能取消分配
        boolean doStatusFlag = doHeader.getStatus().equals(Constants.DoStatus.PARTALLOCATED.getValue())
                               || doHeader.getStatus().equals(Constants.DoStatus.ALLALLOCATED.getValue());
        if (!doStatusFlag) {
            throw new DeliveryException(DeliveryException.CANCEL_ASSIGN_STATUS_WRONG);
        }
        //已跑波次的订单不能取消分配!
        boolean isInWave = doHeader.getWaveFlag().intValue() == Constants.YesNo.YES.getValue().intValue();
        if (isInWave) {
            throw new DeliveryException(DeliveryException.CANCEL_ASSIGN_FAILED_INWAVE);
        }
        if (checkReleaseStatus) {
            //订单如果被冻结则不能取消分配
            boolean isFrozen = doHeader.getReleaseStatus().equals(Constants.ReleaseStatus.HOLD.getValue());
            if (isFrozen) {
                throw new DeliveryException(DeliveryException.DO_ALREADY_FROZEN);
            }
        }
        releaseAssignedStock(doHeaderId, doDetailIds);
        //修改doAllocateHeader和doHeader状态
        DoAllocateHeader doAllocateHeader = doAllocateHeaderDAO.get(doHeaderId);
        doAllocateHeader.setReplStatus(Constants.DoReplStatus.NONE.getValue());
        // 释放订单的自动波次失败次数，及订单需要取消分配标识
        doAllocateHeader.setWaveFailNum(Integer.valueOf(0));
        doAllocateHeader.setNeedCancelAlloc(YesNo.NO.getValue());
        // 自动取消分配的，需要清空分配时间，以便能够自动再次分配
    	if (isAuto) {
    		doAllocateHeader.setAllocTime(null);
    	}
		BigDecimal expectedQty = doAllocateHeader.getExpectedQty();
		// 获取sku种类数量
		long skuIds = doAllocateDetailDAO.getByKeys(doDetailIds)
				.stream().filter(detail -> YesNo.YES.getValue().equals(detail.getIsDoLeaf())).count();
		// 设置订单标记
		CheckFlagEnum checkFlagEnum = calculateCheckFlag(expectedQty, skuIds);
		doHeader.setCheckFlag(checkFlagEnum.getCode());
		doAllocateHeader.setCheckFlag(checkFlagEnum.getCode());
		doAllocateHeader.setUserDeffine3(null);
		doHeader.setUserDeffine3(null);
		doHeader.setEmergencyFlag(YesNo.NO.getValue());

		modifyDoAllocateHeaderStatus(doAllocateHeader);
        doHeader.setStatus(doAllocateHeader.getStatus());
        this.doHeaderDAO.update(doHeader);
		//订单状态回退至初始化，记录取消日志
		if(doAllocateHeader.getStatus().equals(DoStatus.INITIAL.getValue())) {
			orderLogService.saveLog(doHeader, OrderLogConstants.OrderLogType.ALLOCATE_CANCEL.getValue(), ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_ALLOCATE_CANCEL));
		}
        
        this.doHeaderDAO.getSession().flush();
        this.doHeaderDAO.getSession().clear();
    }
	private CheckFlagEnum calculateCheckFlag(BigDecimal expectedQty, long skuQty) {
		if (BigDecimal.ONE.compareTo(expectedQty) == 0) {
			return CheckFlagEnum.SINGLE_PRODUCT_SINGLE_PIECE;
		}

		if (skuQty == 1L) {
			return CheckFlagEnum.SINGLE_PRODUCT_MULTIPLE_PIECE;
		}

		return CheckFlagEnum.MULTIPLE_PRODUCT_MULTIPLE_PIECE;

	}
    /**
     * 取消分配
     */
    @Override
    @Transactional
    public void cancelAssign(DoAllocateHeader header, boolean isAuto, boolean checkReleaseStatus) {
        header = this.getHeader(header.getId());
        
        Long headerId = header.getId();
        
        List<Long> detailIds = new ArrayList<Long>();
        List<DoAllocateDetail> details = header.getDoAllocateDetails();
        for (DoAllocateDetail detail : details) {
            detailIds.add(detail.getId());
        }
        
        this.cancelAssign(headerId, detailIds, isAuto,true);
    }
    
    /**
     * 取消分配——供定时器调用
     */
    @Override
    @Transactional
    public void autoCancelAssign(Long alcHeaderid) {
    	DoAllocateHeader header = this.getHeader(alcHeaderid);
    	ParamUtil.setCurrentWarehouseId(header.getWarehouseId());
    	
        List<Long> detailIds = new ArrayList<Long>();
        List<DoAllocateDetail> details = header.getDoAllocateDetails();
        for (DoAllocateDetail detail : details) {
            detailIds.add(detail.getId());
        }

        this.cancelAssign(alcHeaderid, detailIds, true, true);
    }
    
    /**
     * 根据订单明细的数量，修改订单分配头的状态（初始化、分配完成、部分分配）
     * @param doAllocateHeader
     * @throws DeliveryException
     */
    public void modifyDoAllocateHeaderStatus(DoAllocateHeader doAllocateHeader) throws DeliveryException {
        //更改doHeader的状态
        Long doHeaderId = doAllocateHeader.getId();
        Long initCount = doAllocateDetailDAO.findDoAllocateDetailCountByStatus(
            Constants.DoStatus.INITIAL.getValue(), doHeaderId);
        Long partCount = doAllocateDetailDAO.findDoAllocateDetailCountByStatus(
            Constants.DoStatus.PARTALLOCATED.getValue(), doHeaderId);
        Long compCount = doAllocateDetailDAO.findDoAllocateDetailCountByStatus(
            Constants.DoStatus.ALLALLOCATED.getValue(), doHeaderId);
        if (partCount.longValue() > 0) {
        	doAllocateHeader.setStatus(Constants.DoStatus.PARTALLOCATED.getValue());
        } else {
            if (compCount.longValue() == 0) {
            	doAllocateHeader.setStatus(Constants.DoStatus.INITIAL.getValue());
            } else if (initCount == 0) {
            	doAllocateHeader.setStatus(Constants.DoStatus.ALLALLOCATED.getValue());
            } else {
            	doAllocateHeader.setStatus(Constants.DoStatus.PARTALLOCATED.getValue());
            }
        }
        additionalOpr2Alloc(doAllocateHeader);//添加一些额外操作
        doAllocateHeaderDAO.update(doAllocateHeader);
    }
    
    /**
     * 释放分配库存
     */
    @Override
    @Transactional
	public void releaseAssignedStock(Long doHeaderId, List<Long> doDetailIds) {
		//根据选中的doDetail查询出所有的pickTask
        PickTaskFilter filter = new PickTaskFilter();
        filter.setDoDetailIds(doDetailIds);
		List<PickTask> pickTasks = pickTaskService.query(filter);
		
        /**
         * 下面这段代码对应如下场景： 如果用户选择的明细中有分配数量为0且状态为分配完成的明细，则该明细是做过分配审核的，
         * 既使pickTasks为空，也同样可以取消分配，不提示错误。反正则提示错误。
         */
        if (pickTasks.isEmpty()) {
            Boolean isEmpty = Boolean.TRUE;
            for (Long doDetailId : doDetailIds) {
                DoAllocateDetail doDetail = this.getDoAllocateDetailById(doDetailId);
                // 分配数量为0且状态为分配完成的订单明细视为审核后的明细，可以取消分配
				if (BigDecimal.ZERO.compareTo(doDetail.getAllocatedQty())==0 && DoStatus.ALLALLOCATED.getValue().equals
						(doDetail.getLineStatus())) {
					isEmpty = false;
					break;
				}
            }
            if (isEmpty) {
                throw new DeliveryException(DeliveryException.CANCEL_ASSIGN_FAILED_PKTISNULL);
            }
        }
		for (PickTask pickTask : pickTasks) {
			//更改需要取消的pktDetai所占据的分配库存
			OrderStockDTO stockDto = new OrderStockDTO();
			stockDto.setPlanQty(pickTask.getQty());
			stockDto.setPlanQtyUnit(pickTask.getQtyUnit());
			stockDto.setFmStockId(pickTask.getFmStockId());
			stockDto.setAllocatingId(pickTask.getAllocatingId());
			allocateOperator.setStockDto(stockDto);
			stockService.undo(allocateOperator);
            if (Config.isDefaultFalse(Keys.Delivery.cancel_allocate_release_do, Config.ConfigLevel.WAREHOUSE)) {
                StockBatchAtt att = stockBatchAttDAO.get(pickTask.getLotId());
				if (att != null) {
					Events.instance().raiseEvent("PICK_LOC_STOCK_ADD_EVENT", att.getSkuId(), att.getLotatt04(), att.getLotatt06(), att.getLotatt08());
				}
            }
            //修改任务状态为取消并逻辑删除拣货任务
            pickTaskService.cancelAndRemovePktTask(pickTask);
		}
		//取消Dodetail分配,将分配数归零,拣货数归零,状态设定为取消状态
		this.doDetailDAO.updateDoDetail2Initial(doHeaderId);
	
		this.doAllocateDetailDAO.cancelDodetailAssignByIds(Constants.DoStatus.INITIAL.getValue(), doDetailIds);
	}
    
    /**
     * 部分释放分配库存
     */
    @Override
    @Transactional
	public void releaseAssignedStock(Long doHeaderId, Long doDetailId, BigDecimal diffQty) {
		List<PickTask> pickTasks = pickTaskService.getPickTasksIOrderByBatch(doHeaderId, doDetailId);
		
        for (PickTask pickTask : pickTasks) {
        	if (pickTask.getQty().compareTo(diffQty) > 0) { // 更新分配数
        		pickTask.setQty(pickTask.getQty().subtract(diffQty));
//        		pickTask.setQtyEach(pickTask.getQty().subtract(diffQty));
        		pickTaskService.updatePickTask(pickTask);

    		    OrderStockDTO stockDto = new OrderStockDTO();
    		    stockDto.setPlanQty(pickTask.getQty());
                stockDto.setFmStockId(pickTask.getFmStockId());
                stockDto.setAllocatingId(pickTask.getAllocatingId());
    		    lackOperator.setStockDto(stockDto);
    		    stockService.operateStock(lackOperator);
    		    diffQty = BigDecimal.ZERO;
        	} else { // 取消分配数
    		    OrderStockDTO stockDto = new OrderStockDTO();
    		    stockDto.setPlanQty(pickTask.getQty());
				stockDto.setPlanQtyUnit(pickTask.getQtyUnit());
                stockDto.setFmStockId(pickTask.getFmStockId());
                stockDto.setAllocatingId(pickTask.getAllocatingId());
    		    allocateOperator.setStockDto(stockDto);
    		    stockService.undo(allocateOperator);

    		    //修改任务状态为取消并逻辑删除拣货任务
    		    pickTaskService.cancelAndRemovePktTask(pickTask);
    		    diffQty = diffQty.subtract(pickTask.getQty());
        	}
        	if (BigDecimal.ZERO.compareTo(diffQty)==0) {
        		break;
        	}
		}
	}

    /**
     * 根据ID列表获取订单分配明细头
     */
    @Override
    public List<DoAllocateHeader> getHeaderByIds(List<Long> ids) {
        return this.doAllocateHeaderDAO.getByKeys(ids);
    }

    /**
     * 分页查询ManualAllocDTO
     */
	@Override
	public DataPage<ManualAllocDTO> queryManualAllocInfo(DoAllocateHeader doAllocateHeader, DoAllocateDetail doAllocateDetail, int startRow, int pageSize, String locType, String locCode) {
		Long packUnitId  = packageInfoDetailService.findMainPackage(doAllocateDetail.getSkuId()).getId();
		Long packPcsId = packageInfoDetailService.findEaDetailBySkuId(doAllocateDetail.getSkuId()).getId();
		
		List<String> packIds = Lists.newArrayList();
		if (DoType.SELL.getValue().equals(doAllocateHeader.getDoType())) {
			packIds.add(packPcsId.toString());
		} else {
			packIds.add(packPcsId.toString());
			packIds.add(packUnitId.toString());
		}

		return doAllocateDetailDAO.queryManualAllocInfo(doAllocateHeader, doAllocateDetail, packIds, startRow, pageSize,locType,locCode);
	}

    /**
     * 保存分配订单头信息
     */
    @Override
    @Transactional
    public void updateDoAllocateHeader(DoAllocateHeader header) {
        doAllocateHeaderDAO.update(header);
    }
    
    /**
     * 保存分配明细信息
     */
    @Transactional
    public void updateDoAllocateDetail(DoAllocateDetail detail) {
        doAllocateDetailDAO.update(detail);
    }
    
    /**
     * 获取订单自动分配失败上限值
     * @return
     */
    @Override
    public Integer getAllocateFailNum(){
        return SystemConfig.getConfigValueInt("Delivery.AutoAllocateJob.AllocateFailNum", null);
    }

    @Override
	public List<Long> queryNeedAllocateDoHeaderIds(int batchAllocateNum, Long warehouseId) {
		//按照订单的导入时间进行排序（先进先分配）
        Integer failMaxNum = this.getAllocateFailNum();
        Integer failCheck = this.getFailCheckConfig();
		List<Long> alcHeaderIds = this.doAllocateHeaderDAO.queryNeedAllocateDoHeaderIds(batchAllocateNum, failMaxNum, failCheck, warehouseId);
		return alcHeaderIds;
    }
    
    @Override
    public List<Long> queryNeedAllocateRtvIds(int batchAllocateNum) {
        Integer failMaxNum = this.getAllocateFailNum();
        Integer failCheck = this.getFailCheckConfig();
        List<Long> alcHeaderIds = this.doAllocateHeaderDAO.queryNeedAllocateRtvIds(batchAllocateNum, failMaxNum,
                failCheck);
        return alcHeaderIds;
    }
    
    @Override
    public List<DoAllocateHeader> findByIds(List<Long> alcheaderIds) {
        return this.doAllocateHeaderDAO.queryByIds(alcheaderIds);
    }

    @Override
    @Transactional
    public Long addFailNum(Long id) {
        this.doAllocateHeaderDAO.getSession().clear();

        DoAllocateHeader newHeader = this.getHeader(id);
        Long failNum = newHeader.getAllocateFailNum() + 1;
        newHeader.setAllocateFailNum(failNum);
        
        this.updateDoAllocateHeader(newHeader);
        
        this.doAllocateHeaderDAO.getSession().flush();
        return failNum;
    }

    @Override
    @Transactional
    public void updateDoNotAutoWave(List<Long> ids, List<String> nos) {
        doAllocateHeaderDAO.updateDoNotAutoWave(ids, nos);
    }
	
	/**
	 * 设置订单的自动波次类型
	 * @param alcHeader
	 * @param doWaveEx
	 */
	private void buildAutoWaveType2DoWaveEx(DoAllocateHeader alcHeader, DoWaveEx doWaveEx) {
		if (null != doWaveEx) {
			doWaveEx.setAutoWaveType(generateDoAutoWaveType(alcHeader, doWaveEx));
		}
	}
	
	/**
	 * 生成订单的自动波次类型
	 * @param alcHeader
	 * @param doWaveEx
	 * @return
	 */
	private Integer generateDoAutoWaveType(DoAllocateHeader alcHeader,
			DoWaveEx doWaveEx) {
		
		if (DoType.SELL.getValue().equals(alcHeader.getDoType()) && doWaveEx.getAutoWaveType() != null) {
			if (AutoWaveType.BATCH_GROUP.getValue().equals(doWaveEx.getAutoWaveType())) {
				return AutoWaveType.BATCH_GROUP.getValue();
			}
		}

		// 前面都不匹配，则置为NULL（可能RMA也被置为-1了）
		return AutoWaveType.NORMAL.getValue();
	}
	
	/**
	 * 分配时的一些附加操作（涉及：分配，及对应取消分配、状态回退）
	 * 包括：
	 * 	1、设置订单的指定库区规则
	 *  2、设置订单自动波次类型
	 * @param alcHeader
	 */
	private void additionalOpr2Alloc(DoAllocateHeader alcHeader) {
		DoWaveEx doWaveEx = doWaveExService.findByDoHeaderId(alcHeader.getId());//添加指定库区规则id
		buildAutoWaveType2DoWaveEx(alcHeader, doWaveEx);//设置订单自动波次规则类型
		if (null != doWaveEx) {
			doWaveExService.update(doWaveEx);
		}
	}
	
	@Override
	@Transactional
	public void setAlcNeedCancelFlag(List<Long> ids) {
		doAllocateHeaderDAO.setAlcNeedCancelFlag(ids);
	}
	
	/**
	 * 查询可自动取消分配订单（不区分仓库）
	 */
	@Override
	public List<Long> queryNeedCancelAllocIds(int size) {
		// 此处，和自动分配用了同一个最大失败次数
		Integer failMaxNum = this.getAllocateFailNum();
		return doAllocateHeaderDAO.queryNeedCancelAllocIds(size, failMaxNum);
	}
	
	@Override
    @Transactional
    public Long addCancelFailNum(Long id) {
        this.doAllocateHeaderDAO.getSession().clear();

        DoAllocateHeader newHeader = this.getHeader(id);
        Long failNum = newHeader.getCancelAllocFailNum() + 1;
        newHeader.setCancelAllocFailNum(failNum);
        
        this.updateDoAllocateHeader(newHeader);
        
        this.doAllocateHeaderDAO.getSession().flush();
        return failNum;
    }
	
	/**
     * 完全释放分配库存
     */
    @Override
    @Transactional
	public void releaseAssignedStock(List<Long> doDetailIds) {
		//根据选中的doDetail查询出所有的pickTask
        PickTaskFilter filter = new PickTaskFilter();
        filter.setDoDetailIds(doDetailIds);
		List<PickTask> pickTasks = pickTaskService.query(filter);
		
        for (PickTask pickTask : pickTasks) {
		    //更改需要取消的pktDetai所占据的分配库存
		    OrderStockDTO stockDto = new OrderStockDTO();
		    stockDto.setPlanQty(pickTask.getQty());
			stockDto.setPlanQtyUnit(pickTask.getQtyUnit());
            stockDto.setFmStockId(pickTask.getFmStockId());
            stockDto.setAllocatingId(pickTask.getAllocatingId());
		    allocateOperator.setStockDto(stockDto);
		    stockService.undo(allocateOperator);

		    //修改任务状态为取消并逻辑删除拣货任务
		    pickTaskService.cancelAndRemovePktTask(pickTask);
		}
	}
    
    @Override
    public List<Long> findNotLacks() {
    	return doAllocateHeaderDAO.findNotLacks();
	}
	@Override
    @Transactional
	public void  saveOrUpdate(DoAllocateDetail detail){
		doAllocateDetailDAO.saveOrUpdate(detail);
	}
	@Override
	public List<DoAlcDetailInfoDto> getAlcDetailInfosByDo(Long doId){
		return doAllocateDetailDAO.getAlcDetailInfosByDo(doId);
	}
	
	@Override
	public List<OverAllocateDto> queryOverAllocate() {
		return doAllocateHeaderDAO.queryOverAllocate();
	}
	
	@Override
	public List<Pair<Long, BigDecimal>> getStkAllocateQty(Long stkId) {
		return doAllocateHeaderDAO.getStkAllocateQty(stkId);
	}

    @Override
    public List<Long> findReAllocateId() {
        return doAllocateHeaderDAO.findReAllocateId();
    }

    @Override
    @Transactional
    public void removeTempAlloc(List<Long> ids) {
        doAllocateHeaderDAO.getSession().flush();
        doAllocateHeaderDAO.removeTempAlloc(ids);
    }



	@Override
	public List<AutoWaveDTO> getAutoWaveInfo(Integer lowerLimit) {
		return doAllocateHeaderDAO.getAutoWaveInfo(lowerLimit);
	}

	@Override
	public List<AutoWaveDTO> getAutoWaveInfoByFilter(BigWaveFilter filter ,Integer lowerLimit){
		return doAllocateHeaderDAO.getAutoWaveInfoByFilter(filter,lowerLimit);
	}

	@Override
	public List<Long> getAutoWaveDoIds(AutoWaveDTO dto) {
		return doAllocateHeaderDAO.getAutoWaveDoIds(dto);
	}
	@Override
	public List<Long> getAutoWaveDoIdsByFilter(BigWaveFilter bigWaveFilter, AutoWaveDTO autoWaveDTO){
		return doAllocateHeaderDAO.getAutoWaveDoIdsByFilter(bigWaveFilter,autoWaveDTO);
	}

	/**
	 * @param bigWaveFilter
	 * @return
	 */
	@Override
	public List<Long> getAutoWaveDoIdsByFilter(BigWaveFilter bigWaveFilter) {
		return doAllocateHeaderDAO.getAutoWaveDoIdsByFilter(bigWaveFilter);
	}

	@Override
	@Transactional
	public void clearAlcDate(List<String> goodsCodes,Long warehouseId) {

		if(CollectionUtils.isEmpty(goodsCodes)){
			return;
		}
		List<Long> skuIds=new ArrayList<>();
		for (int i = 0; i < goodsCodes.size(); i++) {
			SkuDTO sku = skuCache.getSkuByGoodsCode(goodsCodes.get(i));
			if(Objects.isNull(sku)){
				continue;
			}
			skuIds.add(sku.getId());

		}
		DoDetailFilter filter = new DoDetailFilter();
		filter.setSkuIds(skuIds);
		filter.setWarehouseId(warehouseId);
		filter.setStatusTo(DoStatus.ALLALLOCATED.getValue());
		List list = doAllocateDetailDAO.findByFilter(filter);
		for (int i=0;i<list.size();i++) {
			DoAllocateDetail detail = (DoAllocateDetail) list.get(i);
			detail.setMinExp(null);
			detail.setMaxExp(null);
			doAllocateDetailDAO.update(detail);
		}
		list = doDetailDAO.findByFilter(filter);
		for (int i=0;i<list.size();i++) {
			DeliveryOrderDetail detail = (DeliveryOrderDetail) list.get(i);
			detail.setMinExp(null);
			detail.setMaxExp(null);
			doDetailDAO.update(detail);
		}
	}
}