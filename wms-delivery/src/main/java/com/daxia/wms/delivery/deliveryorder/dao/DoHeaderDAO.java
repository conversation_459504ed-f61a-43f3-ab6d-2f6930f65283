package com.daxia.wms.delivery.deliveryorder.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.entity.CfgConfiguration;
import com.daxia.framework.common.util.*;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.*;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.deliveryorder.dto.DoDetailDto;
import com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.filter.DoHeaderFilter;
import com.daxia.wms.delivery.recheck.entity.TempCarton;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.print.dto.PrintDoDTO;
import com.daxia.wms.delivery.print.dto.PrintDoDetailDTO;
import com.daxia.wms.delivery.store.dto.StoreInTaskDTO;
import com.daxia.wms.delivery.wave.dto.SelfCarrierNullStationDTO;
import com.daxia.wms.delivery.wave.dto.WaveDetailDTO;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.filter.SelfCarrierNullStationFilter;
import com.daxia.wms.master.dto.AutoCompleteDTO;
import com.daxia.wms.master.dto.MergeBoardDTO;
import com.daxia.wms.master.entity.MergeLoc;
import com.daxia.wms.master.filter.MergeBoardFilter;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Criteria;
import org.hibernate.Hibernate;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.annotations.Fetch;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.hibernate.transform.Transformers;
import org.jboss.seam.annotations.Name;

import java.io.Serializable;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.*;

/**
 * Description:发运订单头信息业务DAO
 */
@Name("com.daxia.wms.delivery.doHeaderDAO")
@lombok.extern.slf4j.Slf4j
public class DoHeaderDAO extends HibernateBaseDAO<DeliveryOrderHeader, Long> {

    private static final long serialVersionUID = 7411075726855990225L;

    /**
     * 发货单界面查询
     */
    @SuppressWarnings("unchecked")
    public DataPage<DoHeaderDto> findDoHeaderPageInfo(DoHeaderFilter doHeaderFilter, int startIndex, int pageSize) {
        Long whId = ParamUtil.getCurrentWarehouseId();

        // ********************【HQL 最终修正版】********************
        StringBuilder hql = new StringBuilder("select new com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto(");
        hql.append(" o.id, o.doNo, o.status, o.releaseStatus, o.expectedQty, o.shipQty, o.doCreateTime, o.shipTime, o.doType,")
                .append(" o.provinceName, o.cityName, o.countyName, wh.waveNo, o.sortGridNo, o.consigneeName, o.address, o.postCode, o.telephone, o.mobile, c.distSuppCompName,")
                .append(" o.invoiceQty, o.userDeffine1, o.holdCode, o.holdReason, o.holdWho, o.holdTime, o.productAmount, o.orderDeliveryFee, o.exchangeFlag, o.grossWt, o.receivable, o.notes, o.createdAt,")
                // 【关键修正】: 将 o.doFinishTime 改回 o.planShipTime。实体中没有doFinishTime字段，但DTO构造函数对应位置需要一个Date类型的值。
                .append(" o.refNo1, o.planShipTime, o.buyerRemark, o.sellerRemark, o.payTime, o.expectedArriveTime1, o.expectedArriveTime2, o.orderSubType, o.needCrossStock, o.sourceAsnId, o.refNo2,");

        if (DoType.WHOLESALE.getValue().equals(doHeaderFilter.getDoType()) || doHeaderFilter.getDoType() == null) {
            hql.append(" bc.customerName,");
        } else {
            hql.append(" '',");
        }
        // 【注意】实体中的 o.planShipTime 字段被用于填充DTO中的两个不同字段（doFinishTime 和 planShipTime），这是完全正确的。
        hql.append(" o.emergencyFlag, o.planShipTime, o.originalSoCode, o.packedQty,")
                .append(" o.userDeffine1, o.userDeffine2, o.userDeffine3, o.userDeffine4, o.userDeffine5, o.userDeffine6,")
                .append(" o.printFlag, o.packEndTime, o.createdAt, we.specialLabelCode, o.transportWendy, o.trackingNo, o.carrierId, o.channelCode, o.storeCode, o.cycleClass,");

        hql.append(" (select count(distinct dd.skuId) from DeliveryOrderDetail dd where dd.doHeaderId = o.id and dd.warehouseId = o.warehouseId),")
                .append(" (select max(tc.isPrinted) from TempCarton tc where tc.doHeaderId = o.id and tc.warehouseId = o.warehouseId and tc.successFlag = 1)");

        hql.append(")");

        // ********************【HQL 修正结束】********************

        // 是否从历史数据表查询
        if (Boolean.TRUE.equals(doHeaderFilter.getQueryHis())) {
            hql.append(" from DeliveryOrderHeaderHis o ");
        } else {
            hql.append(" from DeliveryOrderHeader o ");
        }

        hql.append(" left join o.waveHeader wh");
        hql.append(" left join o.doWaveEx we");
        hql.append(" left join o.carrier c");
        if (DoType.WHOLESALE.getValue().equals(doHeaderFilter.getDoType()) || doHeaderFilter.getDoType() == null) {
            hql.append(" left join o.businessCustomer bc");
        }
        hql.append(" where o.warehouseId = ").append(whId);

        StringBuilder countHql = new StringBuilder("select count(o.id) ");
        // 是否从历史数据表查询
        if (Boolean.TRUE.equals(doHeaderFilter.getQueryHis())) {
            countHql.append(" from DeliveryOrderHeaderHis o ");
        } else {
            countHql.append(" from DeliveryOrderHeader o ");
        }

        countHql.append(" where o.warehouseId = ").append(whId).append(" ");

        return (DataPage<DoHeaderDto>) this.executeQueryByFilter(hql.toString(), countHql.toString(), startIndex,
                pageSize, doHeaderFilter);
    }
    /**
     * 根据id集合查询发运单集合
     *
     * @param ids id集合
     * @return 发运单集合
     */
    @SuppressWarnings("unchecked")
    public List<DeliveryOrderHeader> findDoHeadersByIds(List<Long> ids, boolean isTempAlloc) {
        String hql = "select distinct do_id from temp_alloc ";
        SQLQuery query = this.createSQLQuery(hql);
        query.addScalar("do_id", Hibernate.LONG);
        List<Long> doIds = query.list();
        if (!isTempAlloc) {
            doIds = ListUtils.EMPTY_LIST;
        }

        hql = " from DeliveryOrderHeader o where o.id in (:ids) and o.warehouseId = :warehouseId  ";
        if (CollectionUtils.isNotEmpty(doIds)) {
            hql += " and o.id not in (:doIds) ";
        }
        Query query2 = this.createQuery(hql);
        query2.setParameterList("ids", ids);
        if (CollectionUtils.isNotEmpty(doIds)) {
            query2.setParameterList("doIds", doIds);
        }
        query2.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query2.list();
    }

    /**
     * 根据DO_NO查询订单DeliveryOrderHeader
     *
     * @param orderCode 订单的doNo
     * @return DeliveryOrderHeader
     */
    public DeliveryOrderHeader findByOrderCode(String orderCode) {
        Criteria cri = this.getSession().createCriteria(DeliveryOrderHeader.class);
        cri.add(Restrictions.eq("doNo", orderCode));
        cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
        cri.setMaxResults(1);
        return (DeliveryOrderHeader) cri.uniqueResult();
    }

    /**
     * 根据DO_NO\来源单号查询RTV订单DeliveryOrderHeader
     *
     * @param orderNo 订单的doNo
     * @return DeliveryOrderHeader
     */
    public DeliveryOrderHeader findRtvByOrderCodeOrRefNo(String orderNo, Long warehouseId) {
        String hql = "select o from  DeliveryOrderHeader o where (o.doNo =:orderNo or o.refNo1 = :orderNo) and "
                + " o.warehouseId =:warehouseId and o.doType = :doType ";
        Query query = this.createQuery(hql);
        query.setParameter("orderNo", orderNo);
        query.setLong("warehouseId", warehouseId);
        query.setParameter("doType", DoType.RTV.getValue());
        return (DeliveryOrderHeader) query.uniqueResult();
    }

    public void removeById(Long doId) {
        String sql = "delete from  doc_do_header where id = :id";
        Query query = createSQLQuery(sql);
        query.setLong("id", doId);
        query.executeUpdate();
    }

    /**
     * 更新订单的状态
     *
     * @param id     订单ID
     * @param status 订单状态
     */
    public void updateStatus(Long id, String status) {
        String updateQuery = "update DeliveryOrderHeader o set o.status = :s where o.id = :id and o.warehouseId = :warehouseId";
        Query query = createUpdateQuery(updateQuery);
        query.setLong("id", id);
        query.setString("s", status);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 批量更新定单释放状态，释放定单或锁定定单时可以调用该方法
     *
     * @param releaseStauts
     * @param ids
     */
    public void updateReleaseStatus(String releaseStauts, List<Long> ids) {
        String hql = "update DeliveryOrderHeader o set o.releaseStatus = :status, o.exceptionStatus = :exceptionStatus "
                +
                "where o.id in (:ids) and o.warehouseId = :warehouseId";
        Query query = createUpdateQuery(hql);
        query.setString("status", releaseStauts);
        query.setString("exceptionStatus", DoExpStatus.COMPLETE.getValue());
        query.setParameterList("ids", ids);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 统计波次中订单的数量
     *
     * @param waveId 波次ID
     * @return 订单的数量
     */
    public int countSortingNumberByWaveId(Long waveId) {
        String hql = "select count(o) from DeliveryOrderHeader o where o.waveId = :waveId and o.warehouseId = :warehouseId";
        return ((Long) (createQuery(hql).setLong("waveId", waveId)
                .setLong("warehouseId", ParamUtil.getCurrentWarehouseId()).uniqueResult())).intValue();
    }

    /**
     * 波次中处于某几种状态的释放的订单的数量；
     *
     * @param waveId      波次ID
     * @param orderStatus 状态范围
     * @return
     */
    public int countUnSortingDoHeader(Long waveId, String[] orderStatus) {
        Criteria cri = getSession().createCriteria(DeliveryOrderHeader.class);
        cri.add(Restrictions.eq("waveHeader.id", waveId));
        cri.add(Restrictions.in("status", orderStatus));
        cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
        cri.setProjection(Projections.rowCount());
        return (Integer) cri.uniqueResult();
    }

    @SuppressWarnings("unchecked")
    /**
     * 根据波次ID查询该波次下所有的DO
     * 
     * @param waveId 波次主键
     */
    public List<DeliveryOrderHeader> findDoHeaderByWaveId(Long waveId) {
        String hql = "select o from  DeliveryOrderHeader o  where o.waveId =:waveId and o.warehouseId =:warehouseId";
        Query query = this.createQuery(hql);
        query.setLong("waveId", waveId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    @SuppressWarnings("unchecked")
    /**
     * 根据波次ID查询该波次下所有的DOId
     * 
     * @param waveId 波次主键
     */
    public List<Long> findDoHeaderIdsByWaveId(Long waveId) {
        String hql = "select o.id from  DeliveryOrderHeader o  where o.waveId =:waveId and o.warehouseId =:warehouseId";
        Query query = this.createQuery(hql);
        query.setLong("waveId", waveId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 更新波次下订单的状态及分拣时间
     *
     * @param waveId   波次ID
     * @param status   状态
     * @param sortTime 分拣时间
     */
    public void updateDoHeaderByWaveId(Long waveId, String status, Timestamp sortTime, String filterStatus) {
        filterStatus = StringUtil.isNotBlank(filterStatus) ? filterStatus : DoStatus.UNCHECKED.getValue();
        StringBuilder sql = new StringBuilder();
        sql.append(
                "update doc_do_header o set o.status = if(o.status < :filterStatus,:status,o.status), o.sort_Time =:sortTime, ");
        sql.append(
                "o.sorted_by =:sortedBy, o.sort_Start_Time = (Case When o.Status = '60' Then Sysdate() Else  o.sort_Start_Time end) ");
        sql.append("where o.wave_Id =:waveId ");
        sql.append("and o.warehouse_Id =:warehouseId ");
        Query query = this.createSQLQuery(sql.toString());
        query.setString("status", status);
        query.setTimestamp("sortTime", sortTime);
        query.setLong("waveId", waveId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setString("sortedBy", getOperateUser());
        query.setString("filterStatus", filterStatus);
        query.executeUpdate();
    }

    /**
     * 修改订单的状态为补货完成; 订单满足一下条件：
     * <ul>
     * <li>补货状态为等待补货</li>
     * <li>补货的明细补货完毕则修改补货状态为补货完成</li>
     * <li>订单包含某范围的商品</li>
     * </ul>
     *
     * @param skuIds sku_ID的范围
     */
    public void checkReplStatus(List<Long> skuIds) {
        Boolean repl_allocateNow = Config.isDefaultFalse(Keys.Delivery.repl_allocateNow, Config.ConfigLevel.WAREHOUSE);
        Long whId = ParamUtil.getCurrentWarehouseId();
        String sql = "UPDATE doc_alc_header dh INNER JOIN doc_alc_detail ad ON dh.id = ad.do_header_id " +
                "SET dh.repl_end_time = now(), dh.repl_status = :complete " +
                "WHERE dh.repl_status=:wait AND ad.sku_id in (:skuIds) " +
                (repl_allocateNow ? ""
                        : "AND not exists (SELECT 1 FROM doc_alc_detail dd WHERE dd.need_repl_qty > 0 AND dd.do_header_id = dh.id AND dd.warehouse_id = dh.warehouse_id) ")
                +
                "AND dh.warehouse_id = :dhWhId AND ad.warehouse_id = :dhWhId AND ad.is_deleted = 0";
        Query query = createUpdateSqlQuery(sql, "dh")
                .setInteger("wait", Constants.DoReplStatus.WAIT.getValue())
                .setLong("dhWhId", whId)
                .setInteger("complete", Constants.DoReplStatus.COMPLETE.getValue())
                .setParameterList("skuIds", skuIds);
        query.executeUpdate();
    }

    public void checkNoStockFlag(List<Long> skuIds) {
        Long whId = ParamUtil.getCurrentWarehouseId();

        String sql = "update doc_alc_header dh inner join doc_alc_detail dd on dh.id = dd.do_header_id  set dh.no_Stock_Flag = :notLack where dh.warehouse_id = :dhWhId and dh.no_Stock_Flag = 1 "
                +
                "and dd.sku_id in (:skuIds)";
        Query query = createUpdateSqlQuery(sql, "dh")
                .setLong("dhWhId", whId)
                .setInteger("notLack", YesNo.NO.getValue())
                .setParameterList("skuIds", skuIds);
        query.executeUpdate();
    }

    /**
     * 更新订单总重量（totalGrossWt）
     *
     * @param doId do_no
     */
    public void upddateDoWeight(Long doId) {
        String sql = "update doc_do_header o  set o.update_by = :updateBy , o.last_weigh_time = :weighTime, o.TOTAL_GROSS_WT = (select  sum(ch.ACTUAL_GROSSWEIGHT) from doc_carton_header ch "
                + " where ch.DO_HEADER_ID = :doId and ch.is_deleted = :isDeleted and ch.warehouse_id = :warehouseId) "
                + "where o.id = :doId and o.warehouse_Id = :warehouseId and o.is_deleted = :isDeleted";
        Query query = createSQLQuery(sql);
        query.setParameter("isDeleted", Constants.YesNo.NO.getValue());
        query.setParameter("doId", doId);
        query.setParameter("weighTime", DateUtil.getNowTime());
        query.setParameter("updateBy", ParamUtil.getCurrentLoginName());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 更新称重标识
     *
     * @param doIds id集合
     */
    public void updateDoWeightFlag(List<Long> doIds) {
        String sql = "update doc_do_header o  set weight_flag = 1 "
                + " where o.id in( :doIds ) and o.warehouse_Id = :warehouseId and o.is_deleted = :isDeleted";
        Query query = createSQLQuery(sql);
        query.setParameter("isDeleted", Constants.YesNo.NO.getValue());
        query.setParameterList("doIds", doIds);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 根据查询条件分页查找记录
     * 
     * @param filter
     * @param startIndex
     * @param pageSize
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<DeliveryOrderHeader> findByFilter(Serializable filter, int startIndex, int pageSize) {
        StringBuilder hql = new StringBuilder();
        hql.append(buildSelectHql(filter));
        hql.append(" where 1=1 ");
        List<Object> paramList = new ArrayList<Object>();
        String condition = buildConditionHql(filter, paramList);
        String orderHql = buildOrderCondition(filter, false);
        hql.append(condition).append(orderHql);
        log.debug("hql:[{}] ", hql);
        Query query = createQuery(hql.toString());
        if (pageSize > 0) {
            query.setFirstResult(startIndex);
            query.setMaxResults(pageSize);
        }
        log.debug("condition:[{}] ", condition);
        this.prepareParameter(paramList, query);
        return query.list();
    }

    /**
     * 查询波次waveId的发货单
     *
     * @param waveId
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<DeliveryOrderHeader> queryDoHeadersInWave(Long waveId) {
        String hql = " from DeliveryOrderHeader o where o.waveId = ? and o.warehouseId = ? ";
        Query query = this.createQuery(hql);
        query.setParameter(0, waveId);
        query.setParameter(1, ParamUtil.getCurrentWarehouseId());
        return (List<DeliveryOrderHeader>) query.list();
    }

    @SuppressWarnings("unchecked")
    public List<DeliveryOrderHeader> queryDoHeadersInWaves(List<Long> waveIds) {
        String hql = " from DeliveryOrderHeader o where o.waveId in (:waveIds) and o.warehouseId = :whId ";
        Query query = this.createQuery(hql);
        query.setParameterList("waveIds", waveIds);
        query.setLong("whId", ParamUtil.getCurrentWarehouseId());
        return (List<DeliveryOrderHeader>) query.list();
    }

    /**
     * 原始单据ID和DO_TYPE获取订单
     *
     * @param oirgId
     * @param doType
     * @return
     */
    public DeliveryOrderHeader getByOrigId(String oirgId, DoType doType) {
        if (StringUtil.isEmpty(oirgId)) {
            return null;
        }
        String hql = "from DeliveryOrderHeader o where o.origId = ? and o.doType = ? and o.warehouseId = ?  ";
        Query query = createQuery(hql);
        query.setString(0, oirgId);
        query.setString(1, doType.getValue());
        query.setLong(2, ParamUtil.getCurrentWarehouseId());
        return (DeliveryOrderHeader) query.uniqueResult();
    }

    /**
     * 原始单据ID获取订单
     *
     * @param oirgId
     * @return
     */
    public DeliveryOrderHeader getByOrigId(String oirgId) {
        if (StringUtil.isEmpty(oirgId)) {
            return null;
        }
        String hql = "from DeliveryOrderHeader o where o.origId = ? and o.warehouseId = ?  ";
        Query query = createQuery(hql);
        query.setString(0, oirgId);
        query.setLong(1, ParamUtil.getCurrentWarehouseId());
        return (DeliveryOrderHeader) query.uniqueResult();
    }

    public DeliveryOrderHeader getByOrigId(String oirgId, List<String> doTypes) {
        if (StringUtil.isEmpty(oirgId)) {
            return null;
        }
        String hql = "from DeliveryOrderHeader o where o.origId = :origId and o.doType in( :doTypes) and o.warehouseId = :warehouseId ";
        Query query = createQuery(hql);
        query.setString("origId", oirgId);
        query.setParameterList("doTypes", doTypes);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (DeliveryOrderHeader) query.uniqueResult();
    }

    public DeliveryOrderHeader getByDoNo(String doNo, List<String> doTypes) {
        if (StringUtil.isEmpty(doNo)) {
            return null;
        }
        String hql = "from DeliveryOrderHeader o where o.doNo = :doNo and o.doType in( :doTypes) and o.warehouseId = :warehouseId ";
        Query query = createQuery(hql);
        query.setString("doNo", doNo);
        query.setParameterList("doTypes", doTypes);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (DeliveryOrderHeader) query.uniqueResult();
    }

    public DeliveryOrderHeader getByOriginalSoCode(String originalSoCode, List<String> doTypes) {
        if (StringUtil.isEmpty(originalSoCode)) {
            return null;
        }
        String hql = "from DeliveryOrderHeader o where o.originalSoCode = :originalSoCode and o.doType in( :doTypes) and o.warehouseId = :warehouseId ";
        Query query = createQuery(hql);
        query.setString("originalSoCode", originalSoCode);
        query.setParameterList("doTypes", doTypes);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (DeliveryOrderHeader) query.uniqueResult();
    }

    /**
     * 根据trackingNo查询订单DeliveryOrderHeader
     *
     * @param trackingNo 物流单号
     * @return DeliveryOrderHeader
     */
    public DeliveryOrderHeader findByTrackingNo(String trackingNo) {
        if (StringUtil.isEmpty(trackingNo)) {
            return null;
        }
        Criteria cri = this.getSession().createCriteria(DeliveryOrderHeader.class);
        cri.add(Restrictions.eq("trackingNo", trackingNo));
        cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
        cri.setMaxResults(1);
        return (DeliveryOrderHeader) cri.uniqueResult();
    }

    /**
     * DO_ID获取订单的异常状态、释放冻结状态、订单号、订单类型，
     *
     * @param doId
     * @return
     */
    public Object getDoHeaderExceptionStatus(Long doId) {
        String hql = "select o.exceptionStatus, o.releaseStatus, o.doNo, o.doType from DeliveryOrderHeader o " +
                "where o.id = :doId and o.warehouseId = :warehouseId";
        Query query = createQuery(hql).setLong("doId", doId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.uniqueResult();
    }

    /**
     * 获取订单所属波次的创建时间
     *
     * @param doHeaderId
     * @return
     */
    public Date getWaveTime(Long doHeaderId) {
        String hql = "select w.createdAt from WaveHeader w,DeliveryOrderHeader dh " +
                "where w.id = dh.waveId and w.warehouseId = dh.warehouseId and w.warehouseId =:warehouseId and dh.id = :doHeaderId";
        Query query = createQuery(hql);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setLong("doHeaderId", doHeaderId);
        return (Date) query.uniqueResult();
    }

    /**
     * 获取波次下的所有订单ID
     *
     * @param ids
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Long> findDoIdByWaveId(List<Long> ids) {
        String hql = "select dh.id from DeliveryOrderHeader dh where dh.waveId in (:waveIds) and dh.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setParameterList("waveIds", ids);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (List<Long>) query.list();
    }

    /**
     * 获取波次下的任意一个do
     *
     * @param waveId
     * @return
     */
    public DeliveryOrderHeader getDoHeaderByWaveId(Long waveId) {
        String hql = "from DeliveryOrderHeader dh where dh.waveId = :waveId and dh.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setLong("waveId", waveId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (DeliveryOrderHeader) query.uniqueResult();
    }

    /**
     * 查询系统当中没有绑定发票的订单信息
     *
     * @param selCarrierFilter 查询条件过滤器
     * @param startIndex       查询起始位置
     * @param pageSize         分页单位
     * @return 发货单分页信息
     */
    @SuppressWarnings("unchecked")
    public DataPage<SelfCarrierNullStationDTO> findSelInvoiceErrorDoList(SelfCarrierNullStationFilter selCarrierFilter,
            int startIndex, int pageSize) {
        String whId = ParamUtil.getCurrentWarehouseId().toString();
        StringBuilder hql = new StringBuilder("select new com.daxia.wms.delivery.wave.dto.SelfCarrierNullStationDTO(");
        hql.append(
                " c.doNo,c.status,c.expectedQty,c.doCreateTime,c.doType,c.isHalfDayDelivery,c.invoiceQty,c.receivable, o.sortBy,o.invoiceStatus,o.id,o.reqErrorCode,o.reqSequenceNo");
        hql.append(" )");
        hql.append(" from InvoiceHeader  o ");
        hql.append(" inner join o.deliveryOrderHeader c ");
        hql.append(" where c.invoiceFlag =1 and c.warehouseId = " + whId + "  and o.invoiceType = '"
                + InvoiceHeader.InvoiceType.ELECTRONIC.getValue() + "' ");

        String countHql = "select count(o.id) from InvoiceHeader o inner join o.deliveryOrderHeader c " +
                "where c.invoiceFlag =1 and c.warehouseId =  " + whId + " and o.invoiceType = '"
                + InvoiceHeader.InvoiceType.ELECTRONIC.getValue() + "'  ";

        return (DataPage<SelfCarrierNullStationDTO>) this.executeQueryByFilter(hql.toString(), countHql, startIndex,
                pageSize, selCarrierFilter);
    }

    /**
     * 将波次waveHeaderId下的DO的分拣柜更新为指定分拣柜sortingBinId
     *
     * @param waveHeaderId
     * @param sortingBinId
     */
    public void updateDoSortingBinByWave(Long waveHeaderId, Long sortingBinId) {
        String hql = "update DeliveryOrderHeader o set o.sortingBinId =:sortingBinId " +
                "where o.waveId =:waveHeaderId and o.warehouseId = :warehouseId ";
        Query query = this.createQuery(hql);
        query.setLong("sortingBinId", sortingBinId);
        query.setLong("waveHeaderId", waveHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    public void updateDoSortingBinByIds(List<Long> ids, Long sortingBinId) {
        if (ListUtil.isNullOrEmpty(ids)) {
            return;
        }
        String hql = "update DeliveryOrderHeader o set o.sortingBinId =:sortingBinId " +
                "where o.id in (:ids) ";
        Query query = this.createQuery(hql);
        query.setLong("sortingBinId", sortingBinId);
        query.setParameterList("ids", ids);
        // query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 统计预计出库时间内的订单数
     *
     * @param cutOffTime
     * @param warehouseId
     * @return
     */
    public Long countByCutOffTime(Date cutOffTime, Long warehouseId) {
        String hql = "select count(o.id) from DeliveryOrderHeader o where o.doFinishTime = :doFinishTime " +
                "and o.needCancel = :needCancel and o.doCreateTime >= :doCreateTime and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql).setParameter("doFinishTime", cutOffTime)
                .setParameter("needCancel", Boolean.FALSE)
                .setParameter("doCreateTime", DateUtil.dateAdd("dd", DateUtil.getNowTime(), -10))
                .setParameter("warehouseId", warehouseId);
        return (Long) query.uniqueResult();
    }

    /**
     * 查询可释放DO
     *
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Long> queryAllCanReleaseDos() {
        StringBuffer hqlSb = new StringBuffer();
        hqlSb.append("select o.id from DeliveryOrderHeader o ")
                .append("where o.needCancel <> :needCancel ")
                .append("and o.releaseStatus = :releaseStatus ")
                .append("and o.doType = :doType and o.exceptionStatus = :exceptionStatus ")
                .append("and o.warehouseId = :warehouseId ")
                .append("order by o.id asc ");

        Query query = this.createQuery(hqlSb.toString());
        query.setParameter("needCancel", true);
        query.setString("releaseStatus", Constants.ReleaseStatus.HOLD.getValue());
        query.setParameter("doType", Constants.DoType.SELL.getValue()); // 注意这里是String 枚举是 int
        query.setParameter("exceptionStatus", Constants.DoExpStatus.TO_BE_REPL.getValue());

        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 根据id查询未打波次订单的订单号
     *
     * @param ids
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<String> getNotWavedDoNosByIds(List<Long> ids) {
        String hql = "select o.doNo from DeliveryOrderHeader o where o.id in (:ids) and o.waveId is null and o.waveFlag = :waveFlag and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setParameterList("ids", ids);
        query.setLong("waveFlag", Constants.YesNo.NO.getValue());
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());

        return query.list();
    }

    /**
     * 根据dto保存SLC订单
     */
    public Long saveDoHeader(DoHeaderDto doHeaderDto) {
        String sql = "insert into doc_do_header(do_no,  status,  do_type, ref_no1, consignee_name, post_code, address,  telephone, mobile,  orig_id, do_create_time, invoice_flag, create_by, update_by, warehouse_id) "
                + "select :doNo, :status, :doType, :poNum,  :receiver,      :post,     :address, :tel,      :mobile, :origId,  now(),        :invoiceFlag, :createBy, :createBy, :whId from dual "
                + "where not exists (select 1 from doc_do_header where ref_no1 = :poNum and is_deleted = 0 and warehouse_id = :whId)";
        Query query = this.createSQLQuery(sql);
        query.setParameter("doNo", doHeaderDto.getDoNo());
        query.setParameter("status", DoStatus.INITIAL.getValue());
        query.setParameter("doType", DoType.SELL.getValue());
        query.setParameter("poNum", doHeaderDto.getRefNo1());
        query.setParameter("receiver", doHeaderDto.getConsigneeName());
        query.setParameter("post", doHeaderDto.getPostCode());
        query.setParameter("address", doHeaderDto.getAddress());
        query.setParameter("tel", doHeaderDto.getTelephone());
        query.setParameter("mobile", doHeaderDto.getMobile());
        query.setParameter("origId", -1L);
        query.setParameter("invoiceFlag", YesNo.NO.getValue());
        query.setParameter("createBy", getOperateUser());
        query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();

        String hql = "select d.id from DeliveryOrderHeader d where  d.doNo = :doNo";
        query = this.createQuery(hql);
        query.setParameter("doNo", doHeaderDto.getDoNo());
        Object obj = query.uniqueResult();
        return (Long) obj;
    }

    /**
     * 删除SLC订单
     */
    public void deleteDoHeader(Long doHeaderId) {
        String sql = "delete from doc_do_header where id = :doHeaderId and warehouse_id = :whId";
        Query query = createSQLQuery(sql);
        query.setParameter("doHeaderId", doHeaderId);
        query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 更新订单订货数量及是否贵重品
     */
    public void updateHeaderTotalQtyAndValuable(Long doId, BigDecimal totalQty, boolean isValuableFlag) {
        String sql = "update doc_do_header set expected_qty = :totalQty, is_valuable = :valuable where id = :doId and warehouse_id = :whId";
        Query query = createSQLQuery(sql);
        query.setParameter("totalQty", totalQty);
        query.setParameter("valuable", isValuableFlag ? YesNo.YES.getValue() : YesNo.NO.getValue());
        query.setParameter("doId", doId);
        query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 查询波次中有指定商品的最早预计出库时间的发货单
     *
     * @param waveId
     * @param skuId
     * @return
     */
    public DeliveryOrderHeader getEstDoInWaveBySku(Long waveId, Long skuId, String status) {
        String hql = " from DeliveryOrderHeader o where o.waveId = :waveId and o.status in (:statuses) and o.warehouseId = :warehouseId and o.releaseStatus = :releaseStatus "
                + " and (o.packedBy = :currentUser or o.packedBy is null) "
                + " and exists( select 1 from o.doDetails d where d.skuId = :skuId and d.lineStatus in (:statuses) and d.warehouseId = :warehouseId) ";
        if (Constants.ReleaseStatus.HOLD.getValue().equals(status)) {
            hql += " and not exists(select 1 from Container c where c.containerType = :containerType and c.docNo = o.doNo and c.warehouseId = :warehouseId ) ";
        }
        hql += " order by o.planShipTime asc ";

        Query query = this.createQuery(hql)
                .setParameter("waveId", waveId)
                .setParameterList("statuses",
                        new String[] { Constants.DoStatus.ALLSORTED.getValue(),
                                Constants.DoStatus.PART_CARTON.getValue() })
                .setParameter("currentUser", ParamUtil.getCurrentLoginName())
                .setParameter("skuId", skuId)
                .setParameter("releaseStatus", status)
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        if (Constants.ReleaseStatus.HOLD.getValue().equals(status)) {
            query.setParameter("containerType", Constants.ContainerType.SORT_CONTAINER.getValue());
            query.setParameter("isHdLabelPrinted", YesNo.YES.getValue());
        }
        query.setMaxResults(1);
        return (DeliveryOrderHeader) query.uniqueResult();
    }

    /**
     * 获取订单所属波次的波次号
     *
     * @param doId
     * @return
     */
    public String getWaveNoByDoId(Long doId) {
        String hql = "select w.waveNo from WaveHeader w,DeliveryOrderHeader dh "
                + "where w.id = dh.waveId and w.warehouseId = dh.warehouseId and w.warehouseId =:warehouseId and dh.id = :doHeaderId";
        Query query = createQuery(hql);
        query.setLong("doHeaderId", doId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (String) query.uniqueResult();
    }

    /**
     * 根据箱id获取do头信息
     *
     * @param cartonIds
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<DeliveryOrderHeader> finDoByCartonIds(List<Long> cartonIds) {
        String hql = "select distinct ch.doHeader from CartonHeader ch where ch.id in (:cartonIds) and ch.warehouseId = :whId order by ch.doHeader.sortGridNo asc";
        Query query = createQuery(hql);
        query.setParameterList("cartonIds", cartonIds);
        query.setLong("whId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 获得未拣货完成 未分拣完成 未装箱完成的订单数量
     *
     * @param whId
     * @param moniterTime
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Object[]> findDoNumsByEndOrderTime(Long whId, Integer moniterTime) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        // 查询开始
        StringBuffer sql = new StringBuffer();
        sql.append("select Count(Case When ddh.Status < 60 Then ddh.Status End) as status_1,");
        sql.append("Count(Case When ddh.status >= 60 and ddh.status < 65 Then ddh.Status End) As Status_2,");
        sql.append("Count(Case When ddh.status >= 65 and ddh.status < 67 Then ddh.Status End) As Status_3,");
        sql.append(" date_format(ddh.plan_ship_time,'%Y-%m-%d %H:%i:%S') from doc_do_header ddh");
        sql.append(" where ddh.is_deleted = 0 and ddh.do_type = 1 and ddh.warehouse_id = :warehouseId");
        if (moniterTime == null) {
            sql.append(
                    " and ddh.plan_ship_time >= sysdate() and ddh.plan_ship_time < date_add(sysdate(), interval 1 day) ");
        } else {
            // 监控 的时间从当天8点开始
            String date = DateUtil.dateToString(new Date());
            String startMonitorDate = date + " 08:00:00";
            sql.append(
                    " and ddh.plan_ship_time >= str_To_Date(:startMonitorDate, '%Y-%m-%d %H:%i:%S')  and ddh.plan_ship_time < sysdate() + :moniterTime/60/24");
            paramMap.put("moniterTime", moniterTime);// sql里转换为天
            paramMap.put("startMonitorDate", startMonitorDate);
        }
        sql.append(" group by ddh.plan_ship_time order by ddh.plan_ship_time");
        paramMap.put("warehouseId", whId);

        Query query = getSession().createSQLQuery(sql.toString());
        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            query.setParameter(entry.getKey(), entry.getValue());
        }
        return query.list();
    }

    /**
     * 根据波次id和分拣筐编号查找分拣格号
     *
     * @param waveId
     * @param containerNo
     * @param containerType
     * @return
     */
    public Object[] findByWaveIdAndConNo(Long waveId,
            String containerNo, String containerType) {
        StringBuilder hql = new StringBuilder();
        hql.append(
                "select do.status,do.sortGridNo,do.doNo,do.doType,do.id from Container o,DeliveryOrderHeader do,WaveHeader wh where o.docNo = do.doNo");
        hql.append(" and do.waveId = wh.id and o.containerNo = :containerNo");
        hql.append(" and do.waveId = :waveId");
        hql.append(" and o.containerType = :containerType");
        hql.append(" and wh.warehouseId = :warehouseId ");
        hql.append(" and o.warehouseId = :warehouseId and do.warehouseId = :warehouseId");
        Query query = this.createQuery(hql.toString());
        query.setString("containerNo", containerNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setString("containerType", containerType);
        query.setLong("waveId", waveId);
        query.setMaxResults(1);
        return (Object[]) query.uniqueResult();
    }

    public String findFrozenOrdersByLoadHeaderId(Long loadHeaderId) {
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT GROUP_CONCAT(d.do_no) FROM doc_do_header d WHERE d.id in (")
                .append("SELECT ld.do_header_id FROM  doc_load_detail ld WHERE ld.load_header_id = :loadHeaderId")
                .append(") AND d.release_status = 'HD'");
        Query query = getSession().createSQLQuery(sql.toString());
        query.setParameter("loadHeaderId", loadHeaderId);
        return (String) query.uniqueResult();
    }

    // public void updateStateByLoadHeaderId(Long id, String value) {
    // String updateQuery = "update DeliveryOrderHeader o set o.status = :status
    // where o.id in "
    // + "(select doHeaderId from LoadDetail l where l.loadHeaderId =
    // :loadHeaderId)";
    // Query query = createUpdateQuery(updateQuery);
    // query.setLong("loadHeaderId", id);
    // query.setString("status", value);
    // query.executeUpdate();
    // }

    public Integer updateStateByLoadHeaderId(Long id, String value) {
        StringBuilder sql = new StringBuilder(
                "update doc_do_header ddh left join   doc_load_detail dld on dld.DO_HEADER_ID =  ddh.id ");
        sql.append(
                " set ddh.status = :status, ddh.version = ddh.version+1,  ddh.update_time = :nowTime, ddh.update_by = :updateBy ");
        sql.append(" where  dld.load_header_id = :loadHeaderId and ddh.is_deleted = 0 ");
        if (Constants.DoStatus.PART_DELIVER.getValue().equals(value)) {
            sql.append("and ddh.status <> '80' ");
        }
        Query query = this.createSQLQuery(sql.toString());
        query.setLong("loadHeaderId", id);
        query.setString("status", value);
        query.setParameter("updateBy", getOperateUser());
        query.setParameter("nowTime", DateUtil.getNowTime());
        return query.executeUpdate();
    }

    public boolean isMixType(List<Long> doIds) {
        String hql = "SELECT distinct o.doType FROM DeliveryOrderHeader o WHERE o.id IN (:doIds) AND warehouseId = :warehouseId";
        List<String> list = this.createQuery(hql).setParameterList("doIds", doIds)
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).list();
        if (CollectionUtils.isEmpty(list) || list.size() <= 1) {
            return false;
        }
        return true;
    }

    // 按波次、分拣格排序，优先打印波次创建靠后的。这样同时打印的时候，波次创建时间考前的单据就会在波次创建时间靠后的上面；
    public List<PrintDoDTO> getPrintDoDTO(List<Long> doIds) {
        String sql = "SELECT dh.channel_code channelCode, dh.store_code storeCode ,dh.id AS doId,wh.do_count " +
                "doCount, dh.do_no AS doNo, wh.wave_no AS waveNo, dh" +
                ".sort_grid_no AS sortGridNo," +
                " dh.do_create_time AS doCreateTime, dh.consignee_name AS consigneeName, dh.original_so_code AS originalSoCode, carrier.short_name AS carrierShortName, "
                +
                "warehouse.nick_name AS whNickName, shop.nick_name AS shopNickName, dh.invoice_qty AS invoiceQty, dh.order_amount AS orderAmount, dh.product_amount AS productAmount, dh.account_payable AS amountPayable, dh.discount_amount AS disCountAmount, dh.order_delivery_fee AS orderDeliveryFee, "
                +
                "dh.receivable AS receivable,dh.notes as notes ,dh.expected_qty AS expectedQty, dh.buyer_remark AS buyerRemark, dh.seller_remark AS sellerRemark, dh.have_cfy AS isCfy, dh.delivery_service_fee AS deliveryServiceFee,bc.customer_name AS customerName, dh.address AS address, "
                +
                "dh.province_name as province,dh.city_name as city,dh.county_name as county,tc.way_bill as waybill,dh.userdefine1 as udf1,dh.mobile as mobile,dh.telephone as telephone,"
                +
                "if(dh.ship_time < date'1970-01-01' or dh.ship_time is null, now(),dh.ship_time ) shipTime,dp.content udfPrint,dh.userdefine6 as isPickUp,dh.order_sub_type as orderSubType FROM "
                +
                "doc_do_header dh LEFT JOIN doc_wave_header wh ON dh.wave_id = wh.id LEFT JOIN md_carrier carrier ON dh.carrier_id = carrier.id AND carrier.is_deleted = 0 LEFT JOIN md_warehouse warehouse ON dh.warehouse_id = warehouse.id LEFT JOIN md_shop shop ON dh.shop_id = shop.id "
                +
                " left join md_business_customer bc on dh.business_customer_id = bc.id " +
                " left join doc_do_print dp on dh.id = dp.do_header_id and dp.type = 0 " +
                " left join doc_temp_carton tc on dh.id = tc.do_header_id and tc.success_flag=1 and tc.is_deleted = 0 "
                +
                " WHERE dh.id in (:doIds) AND dh.warehouse_id = :warehouseId AND dh.is_deleted = 0 ORDER BY dh" +
                ".wave_id desc, dh.sort_grid_no";

        SQLQuery query = this.createSQLQuery(sql).addScalar("channelCode", Hibernate.STRING)
                .addScalar("storeCode", Hibernate.STRING)
                .addScalar("doId", Hibernate.LONG).addScalar("doCount", Hibernate.LONG).addScalar(
                        "doNo",
                        Hibernate.STRING)
                .addScalar("waveNo", Hibernate.STRING).addScalar("sortGridNo", Hibernate.STRING)
                .addScalar("doCreateTime", Hibernate.DATE);
        query.addScalar("consigneeName", Hibernate.STRING).addScalar("originalSoCode", Hibernate.STRING)
                .addScalar("carrierShortName", Hibernate.STRING).addScalar("whNickName", Hibernate.STRING)
                .addScalar("invoiceQty", Hibernate.INTEGER).addScalar("deliveryServiceFee", Hibernate.BIG_DECIMAL);
        query.addScalar("orderAmount", Hibernate.BIG_DECIMAL).addScalar("productAmount", Hibernate.BIG_DECIMAL)
                .addScalar("amountPayable", Hibernate.BIG_DECIMAL).addScalar("disCountAmount", Hibernate.BIG_DECIMAL)
                .addScalar("orderDeliveryFee", Hibernate.BIG_DECIMAL).addScalar("address", Hibernate.STRING)
                .addScalar("province", Hibernate.STRING).addScalar("city", Hibernate.STRING)
                .addScalar("shipTime", Hibernate.DATE).addScalar("udfPrint", Hibernate.STRING)
                .addScalar("isPickUp", Hibernate.STRING).addScalar("orderSubType", Hibernate.STRING)
                .addScalar("county", Hibernate.STRING).addScalar("waybill", Hibernate.STRING)
                .addScalar("udf1", Hibernate.STRING).addScalar("mobile", Hibernate.STRING)
                .addScalar("telephone", Hibernate.STRING);
        query.addScalar("receivable", Hibernate.BIG_DECIMAL).addScalar("notes", Hibernate.STRING)
                .addScalar("expectedQty", Hibernate.BIG_DECIMAL).addScalar("buyerRemark", Hibernate.STRING)
                .addScalar("sellerRemark", Hibernate.STRING).addScalar("isCfy", Hibernate.BOOLEAN)
                .addScalar("shopNickName", Hibernate.STRING).addScalar("customerName", Hibernate.STRING);
        return query.setParameterList("doIds", doIds).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId())
                .setResultTransformer(Transformers.aliasToBean(PrintDoDTO.class)).list();
    }

    public List<PrintDoDetailDTO> getPrintDoDetailDTO(Long doId) {
        String sql = "";
        DeliveryOrderHeader header = get(doId);
        SQLQuery query = null;
        if ((header.getDoType().equals(DoType.SELL.getValue())
                || header.getStatus().compareTo(DoStatus.ALLPICKED.getValue()) < 0
                || header.getStatus().equals(DoStatus.CANCELED.getValue()))
                && !Config.isDefaultFalse(Keys.Print.do_printByBatch, Config.ConfigLevel.WAREHOUSE)) {
            sql = "SELECT sku.id AS skuId, sku.product_code AS productCode, sku.product_cname AS productName, sku.product_ename AS productEname, sku.specification AS specification, sku.udf6 AS uom, dd.price AS price, dd.expected_qty AS qty, dd"
                    +
                    ".expected_qty AS expectedQty, dd.price * dd.expected_qty AS amountPrice, dd.parent_id AS parentId, dd.orig_detail_id AS origDetailId, dd.is_do_leaf AS isDoLeaf "
                    +
                    ",merchant.descr_c as merchantName,sku.ean13 as barcode,dd.notes notes,dd.allocated_qty allocatedQty "
                    +
                    "FROM doc_do_detail dd LEFT JOIN md_sku sku ON dd.sku_id = sku.id " +
                    "LEFT JOIN md_merchant merchant on merchant.id = dd.lotatt06 " +
                    "WHERE dd.do_header_id = :doId AND dd.warehouse_id = :warehouseId AND dd.is_deleted = 0 ";
            query = this.createSQLQuery(sql).addScalar("barcode", Hibernate.STRING)
                    .addScalar("productName", Hibernate.STRING).addScalar("productEname", Hibernate.STRING)
                    .addScalar("specification", Hibernate.STRING).addScalar("price", Hibernate.BIG_DECIMAL);
            query.addScalar("qty", Hibernate.BIG_DECIMAL).addScalar("amountPrice", Hibernate.BIG_DECIMAL)
                    .addScalar("parentId", Hibernate.STRING).addScalar("origDetailId", Hibernate.STRING)
                    .addScalar("isDoLeaf", Hibernate.BOOLEAN).addScalar("skuId", Hibernate.LONG);
            query.addScalar("expectedQty", Hibernate.BIG_DECIMAL).addScalar("productCode", Hibernate.STRING)
                    .addScalar("merchantName", Hibernate.STRING).addScalar("uom", Hibernate.STRING)
                    .addScalar("notes", Hibernate.STRING).addScalar("allocatedQty", Hibernate.BIG_DECIMAL);
        } else {
            sql = "SELECT sku.id AS skuId, sku.product_code AS productCode, sku.product_cname AS productName, sku.product_ename AS productEname, sku.specification AS specification, sku.udf6 AS uom, sba.lotatt05 AS lotNo, sba.LOTATT02 AS expiredTime, dd.price AS price, ";
            if (header.getDoType().equals(DoType.SELL.getValue())) {
                sql += "if(dd.is_do_leaf = 1, tp.qty, dd.expected_qty) AS qty, ";
            } else {
                sql += "if(dd.is_do_leaf = 1, tp.qty_picked, dd.expected_qty) AS qty, ";
            }
            sql += "if(dd.is_do_leaf = 1, ifnull(tp.qty_picked ,dd.expected_qty), dd.expected_qty) AS expectedQty, dd.price * tp.qty_picked AS amountPrice, dd"
                    +
                    ".parent_id AS parentId, dd.orig_detail_id AS origDetailId, dd.is_do_leaf AS isDoLeaf, merchant.descr_c as merchantName,mf.descr_c manufacturer,sku.register_no registerNo,"
                    +
                    "sku.ean13 as barcode,dd.notes notes,dd.allocated_qty allocatedQty,ml.loc_code locCode " +
                    "FROM doc_do_detail dd LEFT JOIN md_sku sku ON dd.sku_id = sku.id " +
                    "LEFT JOIN tsk_pick tp ON tp.doc_line_id = dd.id AND tp.is_deleted = 0 AND tp.warehouse_id = :warehouseId "
                    +
                    "LEFT JOIN stock_batch_att sba ON tp.lot_id = sba.id AND sba.warehouse_id = :warehouseId AND sba.is_deleted = 0 "
                    +
                    "LEFT JOIN md_merchant merchant on merchant.id = dd.lotatt06 " +
                    "LEFT JOIN md_manufacturer mf on mf.id = sba.lotatt08 " +
                    "LEFT JOIN md_location ml on ml.id = tp.to_loc_id " +
                    "WHERE dd.do_header_id = :doId AND dd.warehouse_id = :warehouseId AND dd.is_deleted = 0 ";
            query = this.createSQLQuery(sql).addScalar("barcode", Hibernate.STRING)
                    .addScalar("productName", Hibernate.STRING).addScalar("productEname", Hibernate.STRING)
                    .addScalar("specification", Hibernate.STRING).addScalar("lotNo", Hibernate.STRING)
                    .addScalar("expiredTime", Hibernate.STRING).addScalar("price", Hibernate.BIG_DECIMAL);
            query.addScalar("qty", Hibernate.BIG_DECIMAL).addScalar("amountPrice", Hibernate.BIG_DECIMAL)
                    .addScalar("parentId", Hibernate.STRING).addScalar("origDetailId", Hibernate.STRING)
                    .addScalar("isDoLeaf", Hibernate.BOOLEAN).addScalar("skuId", Hibernate.LONG);
            query.addScalar("expectedQty", Hibernate.BIG_DECIMAL).addScalar("productCode", Hibernate.STRING)
                    .addScalar("merchantName", Hibernate.STRING).addScalar("manufacturer", Hibernate.STRING)
                    .addScalar("registerNo", Hibernate.STRING)
                    .addScalar("uom", Hibernate.STRING).addScalar("notes", Hibernate.STRING)
                    .addScalar("allocatedQty", Hibernate.BIG_DECIMAL).addScalar("locCode", Hibernate.STRING);
        }
        return query.setParameter("doId", doId).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId())
                .setResultTransformer(Transformers.aliasToBean(PrintDoDetailDTO.class)).list();
    }

    public Map<ReleaseStatus, Integer> countByReleaseStatus(Long waveId) {
        String hql = "SELECT COUNT(dh.id), dh.releaseStatus FROM DeliveryOrderHeader dh WHERE dh.waveId = :waveId AND dh.warehouseId = :warehouseId GROUP BY dh.releaseStatus";
        List<Object[]> results = this.createQuery(hql).setParameter("waveId", waveId)
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).list();

        Map<ReleaseStatus, Integer> rMap = Maps.newHashMap();
        if (ListUtil.isNotEmpty(results)) {
            for (Object[] os : results) {
                String releaseStatus = (String) os[1];
                Integer qty = ((Long) os[0]).intValue();
                if (ReleaseStatus.RELEASE.getValue().equals(releaseStatus)) {
                    rMap.put(ReleaseStatus.RELEASE, qty);
                } else if (ReleaseStatus.HOLD.getValue().equals(releaseStatus)) {
                    rMap.put(ReleaseStatus.HOLD, qty);
                }
            }
        }
        return rMap;
    }

    public String getMaxStatus(Long waveId) {
        String hql = "SELECT MAX(dh.status) FROM DeliveryOrderHeader dh WHERE dh.waveId = :waveId AND dh.warehouseId = :warehouseId";
        return (String) this.createQuery(hql).setParameter("waveId", waveId)
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).setMaxResults(1).uniqueResult();
    }

    public BigDecimal getAllocateQty(Long doHeaderId) {
        String hql = "SELECT SUM(dd.allocatedQty) FROM DeliveryOrderDetail dd WHERE dd.doHeaderId = :doHeaderId AND dd.warehouseId = :warehouseId";

        return (BigDecimal) this.createQuery(hql).setParameter("doHeaderId", doHeaderId)
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).setMaxResults(1).uniqueResult();
    }

    public void updateWeightByWaveId(Long waveId, BigDecimal weight) {
        this.createUpdateQuery(
                "UPDATE DeliveryOrderHeader o SET o.totalGrossWt = :weight, o.lastWeighTime = :weighTime " +
                        " WHERE o.waveId = :waveId")
                .setParameter("weight", weight).setParameter("weighTime", DateUtil.getNowTime())
                .setParameter("waveId", waveId).executeUpdate();
    }

    public List<Long> findList4TempCarton(Long warehouseId, Integer rows, boolean localWaybillFlag,
            String jobParameter) {
        List<String> localWaybillCarrierList = null;
        CfgConfiguration cfg = Config.getConfig(ConfigKeys.LOCAL_WAYBILL_CARRIER_LIST, Config.ConfigLevel.GLOBAL);
        if (cfg != null && StringUtil.isNotEmpty(cfg.getValueString())) {
            localWaybillCarrierList = Arrays.asList(cfg.getValueString().split(","));
        }
        if (localWaybillFlag && CollectionUtils.isEmpty(localWaybillCarrierList)) {
            return null;
        }
        List<String> skuCodes = Config.getByDelimit(Keys.Delivery.auto_temp_carton_skuCodes,
                Config.ConfigLevel.WAREHOUSE);
        String sql = "SELECT dh.id FROM doc_do_header dh LEFT JOIN doc_temp_carton tc ON " +
                "tc.do_header_id = dh.id AND tc.is_deleted = 0 AND tc.carrier_id = dh.carrier_id AND tc.warehouse_id = :warehouseId ";
        if ("error".equals(jobParameter)) {
            sql += " AND tc.success_flag = 0 AND tc.call_count < 3  ";
        } else if ("normal".equals(jobParameter)) {

        } else {
            sql += " AND (tc.success_flag = 1 OR (tc.success_flag = 0 AND tc.call_count >= 3)) ";
        }
        sql += " INNER JOIN md_carrier carrier on carrier.id = dh.carrier_id WHERE ";
        if ("error".equals(jobParameter)) {
            sql += " tc.id is not null ";
        } else if ("normal".equals(jobParameter)) {
            sql += " tc.id is null ";
        } else {
            sql += " tc.id is null ";
        }
        sql += "  AND ( (dh.status >= :ALLALLOCATED AND dh.status <= :ALLSORTED) ";
        if (CollectionUtils.isNotEmpty(skuCodes)) {
            sql += " or ( dh.is_temp_carton=1 and dh.status >= '00' AND dh.status <= :ALLSORTED) ";
        }
        sql += " ) AND dh.do_type in (:doTypes) AND dh.carrier_id IS NOT NULL AND dh.carrier_id != :defaultCarrierId " +
                "AND dh.is_deleted = 0 AND dh.need_cancel = 0 AND dh.warehouse_id = :warehouseId ";
        if (CollectionUtils.isNotEmpty(localWaybillCarrierList)) {
            sql = sql + " AND carrier.waybill_type " + (localWaybillFlag ? "" : "not") + " in (:carrierList) ";
        }
        if ("error".equals(jobParameter)) {
            sql += "ORDER BY tc.call_count ASC,dh.id ASC";
        } else if ("normal".equals(jobParameter)) {
            sql += "ORDER BY dh.id ASC ";
        } else {
            sql += "ORDER BY dh.id ASC";
        }
        SQLQuery query = createSQLQuery(sql);
        Integer defaultCarrierId = SystemConfig.getConfigValueInt("dubhe.default.carrier.id", warehouseId);
        if (defaultCarrierId == null) {
            defaultCarrierId = -9999;
        }
        if (CollectionUtils.isNotEmpty(localWaybillCarrierList)) {
            query.setParameterList("carrierList", localWaybillCarrierList);
        }

        List<String> doTypes = Config.getByDelimit(Keys.Delivery.auto_temp_carton_doTypes,
                Config.ConfigLevel.WAREHOUSE);
        if (ListUtil.isNullOrEmpty(doTypes)) {
            doTypes = Lists.newArrayList(DoType.SELL.getValue());
        }

        query.addScalar("id", Hibernate.LONG).setParameter("warehouseId", warehouseId)
                .setParameter("ALLALLOCATED", DoStatus.ALLALLOCATED.getValue())
                .setParameter("ALLSORTED", DoStatus.ALLSORTED.getValue()).setParameterList("doTypes", doTypes)
                .setParameter("defaultCarrierId", defaultCarrierId.longValue()).setMaxResults(rows);
        return query.list();
    }

    public Long countAllResults(DoHeaderFilter doHeaderFilter) {
        DataPage<DoHeaderDto> dtos = findDoHeaderPageInfo(doHeaderFilter, 0, 1);
        return dtos.getTotalCount();
    }

    public List<AutoCompleteDTO> findSkuHelperData(Long doId) {
        String hql = "SELECT DISTINCT new com.daxia.wms.master.dto.AutoCompleteDTO(sku.productCname, sku.helpCode, sku.productCode) FROM DeliveryOrderHeader dh LEFT JOIN dh.doDetails dd "
                +
                " INNER JOIN dd.sku sku WHERE dh.id = :doId AND sku.helpCode IS NOT NULL";
        return this.createQuery(hql, ImmutableMap.<String, Object>of("doId", doId)).list();
    }

    public void updateCarrier(List<Long> idList, Long carrierId) {
        String hql = "update DeliveryOrderHeader o set o.carrierId = :carrierId " +
                "where o.id in (:ids) and o.warehouseId = :warehouseId";
        Query query = createUpdateQuery(hql);
        query.setLong("carrierId", carrierId);
        query.setParameterList("ids", idList);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    public void batchUpdateDoHeader4Pick(List<Long> batchIdList, String updateBy, Long waveId) {
        String sql = "update doc_do_header dd " +
                " inner join tsk_pick p on p.doc_id = dd.id and dd.warehouse_id = p.warehouse_id and p.is_deleted =0 " +
                " left join ( SELECT  count(*) as total, tp.doc_id,tp.warehouse_id " +
                " from tsk_pick tp where  tp.is_deleted =0 and tp.status not in (:pickTaskList) and tp.wave_h_id =:waveId "
                +
                " group by tp.doc_id,tp.warehouse_id) t" +
                " on t.doc_id = dd.id and  t.warehouse_id = dd.warehouse_id" +

                " left join ( SELECT  count(*) as total, tp.doc_id,tp.warehouse_id " +
                " from tsk_pick tp where  tp.is_deleted =0 and tp.stock_status != :stockStatus and tp.wave_h_id =:waveId "
                +
                " group by tp.doc_id,tp.warehouse_id) lockTask" +
                " on lockTask.doc_id = dd.id and  lockTask.warehouse_id = dd.warehouse_id" +

                " set dd.status = (case ifnull(t.total,0) when 0 then '60' else '50' end)," +
                " dd.update_by=:updateBy,dd.update_time=now(), " +
                " dd.lack_status = (case ifnull(t.total,0) when 0 then (case ifnull(lockTask.total,0) when 0 then dd.lack_status else 1 end) else dd.lack_status end)"
                +
                " where dd.warehouse_id =:warehouseId and p.id in (:batchIdList) and dd.is_deleted =0";

        SQLQuery sqlQuery = this.createSQLQuery(sql);
        sqlQuery.setParameter("updateBy", updateBy);
        sqlQuery.setParameterList("batchIdList", batchIdList);
        sqlQuery.setParameter("waveId", waveId);
        sqlQuery.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        sqlQuery.setParameter("stockStatus", StockStatus.NORMAL.getValue());
        List<String> pickTaskList = Lists.newArrayList();
        pickTaskList.add(DoStatus.ALLPICKED.getValue());
        pickTaskList.add(DoStatus.CANCELED.getValue());
        sqlQuery.setParameterList("pickTaskList", pickTaskList);
        sqlQuery.executeUpdate();
    }

    public List<Long> getDoListByPickTaskAndStatus(List<Long> batchIdList, String status) {
        String sql = "select d.id from doc_do_header d " +
                " inner join tsk_pick p on p.doc_id = d.id and d.warehouse_id = p.warehouse_id and p.is_deleted =0 " +
                " where d.warehouse_id =:warehouseId and p.id in (:batchIdList) and d.is_deleted =0 and d.status =:status ";
        SQLQuery sqlQuery = createSQLQuery(sql);
        sqlQuery.setParameter("status", status);
        sqlQuery.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        sqlQuery.setParameterList("batchIdList", batchIdList);
        sqlQuery.addScalar("id", Hibernate.LONG);
        return sqlQuery.list();
    }

    public List<Long> getHoldDoListByPickTask(List<Long> batchIdList) {
        String sql = "select d.id from doc_do_header d " +
                " inner join tsk_pick p on p.doc_id = d.id and d.warehouse_id = p.warehouse_id and p.is_deleted =0 " +
                " where d.warehouse_id =:warehouseId and p.id in (:batchIdList) and d.is_deleted =0 and d.release_status =:releaseStatus ";
        SQLQuery sqlQuery = createSQLQuery(sql);
        sqlQuery.setParameter("releaseStatus", Constants.ReleaseStatus.HOLD.getValue());
        sqlQuery.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        sqlQuery.setParameterList("batchIdList", batchIdList);
        sqlQuery.addScalar("id", Hibernate.LONG);
        return sqlQuery.list();
    }

    public List<WaveDetailDTO> qureyWaveDetail(Long waveId) {
        String hql = "SELECT new com.daxia.wms.delivery.wave.dto.WaveDetailDTO(id, doNo, sortGridNo, doType, status, doCreateTime, notes, releaseStatus, channelCode) FROM DeliveryOrderHeader dh WHERE dh.waveId = :waveId "
                +
                "AND dh.warehouseId = :warehouseId";
        return this.createQuery(hql).setParameter("waveId", waveId)
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).list();
    }

    public void updateForPack(Long waveId, String operator) {
        String hql = "UPDATE doc_do_header dh " +
                "SET dh.pack_time_start = now(), dh.pack_time_end = now(), dh.recheck_type = :recheckType, dh.status = :status, dh.update_time =  now(), dh.update_by = :operator, dh.packed_by = :operator, "
                +
                "dh.version = dh.version + 1 WHERE dh.wave_id = :waveId AND dh.warehouse_id = :warehouseId AND dh.release_status = :releaseStatus AND dh.is_deleted = 0";
        Query query = createSQLQuery(hql);
        query.setParameter("operator", operator);
        query.setParameter("waveId", waveId);
        query.setParameter("recheckType", Constants.YesNo.YES.getValue());
        query.setParameter("status", Constants.DoStatus.ALL_CARTON.getValue());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("releaseStatus", ReleaseStatus.RELEASE.getValue());
        query.executeUpdate();
    }

    public List<Long> getIdListByWaveIds(List<Long> ids) {
        String sql = "select d.id from doc_do_header d " +
                " where d.warehouse_id =:warehouseId and d.wave_id in (:ids) and d.is_deleted =0 and d.release_status =:releaseStatus ";
        SQLQuery sqlQuery = createSQLQuery(sql);
        sqlQuery.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        sqlQuery.setParameterList("ids", ids);
        sqlQuery.setParameter("releaseStatus", ReleaseStatus.RELEASE.getValue());
        sqlQuery.addScalar("id", Hibernate.LONG);
        return sqlQuery.list();
    }

    public List<Long> getIdListByWaveIdAndSrotGrid(Long waveId, String sortGridFm, String sortGridTo) {
        String sql = "select d.id from doc_do_header d " +
                " where d.warehouse_id =:warehouseId and d.wave_id = :waveId " +
                " and d.is_deleted =0 and d.release_status =:releaseStatus ";
        if (StringUtils.isNotEmpty(sortGridFm)) {
            sql += " and d.sort_grid_no >= :sortGridFm ";
        }
        if (StringUtils.isNotEmpty(sortGridTo)) {
            sql += " and d.sort_grid_no <= :sortGridTo ";
        }
        SQLQuery sqlQuery = createSQLQuery(sql);
        sqlQuery.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        sqlQuery.setParameter("waveId", waveId);
        sqlQuery.setParameter("releaseStatus", ReleaseStatus.RELEASE.getValue());
        if (StringUtils.isNotEmpty(sortGridFm)) {
            sqlQuery.setParameter("sortGridFm", sortGridFm);
        }
        if (StringUtils.isNotEmpty(sortGridTo)) {
            sqlQuery.setParameter("sortGridTo", sortGridTo);
        }
        sqlQuery.addScalar("id", Hibernate.LONG);
        return sqlQuery.list();
    }

    public DataPage<DoDetailDto> findDoDetailPageInfo(DoHeaderFilter doHeaderFilter, int startIndex, Integer pageSize) {
        Long whId = ParamUtil.getCurrentWarehouseId();
        StringBuilder hql = new StringBuilder("select new com.daxia.wms.delivery.deliveryorder.dto.DoDetailDto(");
        hql.append(" o.id,o.doNo,o.status,o.releaseStatus,o.expectedQty,o.shipQty,o.doCreateTime,");
        hql.append(" o.shipTime,o.doType, ");
        hql.append(" (case when o.provinceName is null then pi.provinceCname else o.provinceName end), ");
        hql.append(" (case when o.cityName is null then cii.cityCname else o.cityName end), ");
        hql.append(" (case when o.countyName is null then cti.countyCname else o.countyName end), ");
        hql.append(" wh.waveNo,o.sortGridNo,");
        hql.append(" o.consigneeName,o.address,o.postCode,o.telephone,o.mobile,c.distSuppCompName,");
        hql.append(" o.invoiceQty,o.userDeffine1,o.holdCode,o.holdReason,o.holdWho,o.holdTime,");
        hql.append(
                " o.productAmount,o.orderDeliveryFee,o.exchangeFlag, o.grossWt,o.receivable,o.notes,o.createdAt, o.refNo1, o.planShipTime,o.buyerRemark,o.sellerRemark,o.payTime, ");
        hql.append(
                " o.expectedArriveTime1,o.expectedArriveTime2,o.orderSubType,s.productCode,s.productCname,s.specification,dd.expectedQty,o.originalSoCode,o.trackingNo,o.carrierId,s.productEname)");
        // 是否从历史数据表查询
        if (Boolean.TRUE.equals(doHeaderFilter.getQueryHis())) {
            hql.append(" from DeliveryOrderHeaderHis o ");
        } else {
            hql.append(" from DeliveryOrderHeader o ");
        }

        hql.append(" left join o.doDetails dd ");
        hql.append(" left join dd.sku s");
        hql.append(" left join o.provinceInfo pi");
        hql.append(" left join o.cityInfo cii");
        hql.append(" left join o.countyInfo cti");
        hql.append(" left join o.waveHeader wh");
        hql.append(" left join o.carrier c");

        if (StringUtil.isNotEmpty(doHeaderFilter.getBusinessCustomerName())) {
            hql.append(" left join o.businessCustomer bc");
        }

        hql.append(" where o.warehouseId = " + whId.toString());

        StringBuilder countHql = new StringBuilder("select count(o.id) ");
        // 是否从历史数据表查询
        if (Boolean.TRUE.equals(doHeaderFilter.getQueryHis())) {
            // countHql.append(" from DeliveryOrderHeaderHis o left join o.doDetails dd ");
            countHql.append(" from DeliveryOrderHeaderHis o ");
        } else {
            // countHql.append(" from DeliveryOrderHeader o left join o.doDetails dd ");
            countHql.append(" from DeliveryOrderHeader o ");
        }
        countHql.append(" where o.warehouseId = " + whId.toString() + " ");
        return (DataPage<DoDetailDto>) this.executeQueryByFilter(hql.toString(), countHql.toString(), startIndex,
                pageSize, doHeaderFilter);
    }

    public long countAllDetailResults(DoHeaderFilter doHeaderFilter) {
        DataPage<DoDetailDto> dtos = findDoDetailPageInfo(doHeaderFilter, 0, 1);
        return dtos.getTotalCount();
    }

    public List<StoreInTaskDTO> findStoreInTask(String boxNo) {
        String sql = " SELECT mc.grid_no gridNo, GROUP_CONCAT(mc.container_no) turnoverBoxNos,mc.doc_no docNo,mc.doc_type docType, ph.pkt_type pktType, ml.MERGE_CODE recommendStoreLoc "
                +
                " FROM md_container mc INNER JOIN doc_do_header dh ON mc.doc_no = dh.do_no and dh.warehouse_id = :warehouseId "
                +
                " INNER JOIN doc_pkt_header ph ON ph.pkt_no = mc.REF_NO_1 and ph.warehouse_id = :warehouseId " +
                " LEFT JOIN md_merge_loc ml ON ml.DOC_NO = mc.doc_no and ml.pkt_type = ph.pkt_type and ml.doc_type = mc.doc_type and ml.WAREHOUSE_ID = :warehouseId and ml.IS_DELETED= 0 "
                +
                " WHERE ph.status = :pickHeaderStatus AND mc.doc_type = :docType AND mc.business_status in (:businessStatusList) "
                +
                " AND EXISTS ( SELECT 1 FROM md_container mc1 WHERE mc1.container_no = :containerNo AND mc1.REF_NO_1 = mc.REF_NO_1 AND mc1.doc_type = :docType) "
                +
                " and mc.is_deleted = 0 and mc.warehouse_id = :warehouseId" +
                " GROUP BY mc.doc_no,mc.doc_type,ph.pkt_type ORDER BY mc.grid_no ";
        SQLQuery sqlQuery = createSQLQuery(sql);
        sqlQuery.setParameter("docType", Constants.BindDocType.DELIVERYORDER.getValue());
        sqlQuery.setParameterList("businessStatusList", Arrays.asList(
                Constants.ContainerBusinessStatus.PICKING.getValue(), ContainerBusinessStatus.OUT_MERGE.getValue()));
        sqlQuery.setParameter("pickHeaderStatus", PktStatus.COMPLETED.getValue());
        sqlQuery.setParameter("containerNo", boxNo);
        sqlQuery.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        sqlQuery.addScalar("gridNo", Hibernate.STRING).addScalar("turnoverBoxNos", Hibernate.STRING)
                .addScalar("docType", Hibernate.STRING)
                .addScalar("docNo", Hibernate.STRING).addScalar("pktType", Hibernate.STRING)
                .addScalar("recommendStoreLoc", Hibernate.STRING)
                .setResultTransformer(Transformers.aliasToBean(StoreInTaskDTO.class));
        return sqlQuery.list();
    }

    public Long getLineId(Long id) {
        String sql = "SELECT lbc.line_id AS id FROM doc_do_header dh, md_line_business_customer lbc WHERE dh.business_customer_id = lbc.business_customer_id AND dh.id = :doId";
        List<Long> lineIds = this.createSQLQuery(sql, ImmutableMap.<String, Object>of("doId", id))
                .addScalar("id", Hibernate.LONG).setMaxResults(1).list();
        if (ListUtil.isNotEmpty(lineIds)) {
            return lineIds.get(0);
        } else {
            return null;
        }
    }

    public DataPage<MergeBoardDTO> queryBoard(String sql, String countSql, Map<String, Object> paramMap, int startIndex,
            int pageSize) {
        SQLQuery query = createSQLQuery(sql);
        SQLQuery countQuery = createSQLQuery(countSql);

        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            if (entry.getValue() instanceof List) {
                query.setParameterList(entry.getKey(), (Collection) entry.getValue());
                countQuery.setParameterList(entry.getKey(), (Collection) entry.getValue());
            } else {
                query.setParameter(entry.getKey(), entry.getValue());
                countQuery.setParameter(entry.getKey(), entry.getValue());
            }
        }
        if (pageSize > 0) {
            query.setFirstResult(startIndex);
            query.setMaxResults(pageSize);
        }
        List list = query.list();
        List<MergeBoardDTO> mergeBoardDTOList = new ArrayList<MergeBoardDTO>();
        setMergeBoardDtos(list, mergeBoardDTOList);
        Long cnt = ((BigInteger) countQuery.uniqueResult()).longValue();
        return new DataPage<MergeBoardDTO>(cnt.intValue(), startIndex, pageSize, mergeBoardDTOList);
    }

    public String buildSelectNosHql(Serializable filter) {
        StringBuilder hql = new StringBuilder();
        hql.append("select o.doNo from ").append(getEntityClass().getName()).append(" o ");
        Method[] methods = getEntityClass().getMethods();
        for (Method method : methods) {
            Fetch annotation = method.getAnnotation(Fetch.class);
            if (annotation != null) {
                hql.append(" left join fetch o.").append(method.getName().substring(3, 4).toLowerCase())
                        .append(method.getName().substring(4)).append(" ");
            }
        }
        return hql.toString();
    }

    public List<String> findNosByFilter(DoHeaderFilter filter) {
        StringBuilder hql = new StringBuilder();
        hql.append(buildSelectNosHql(filter));
        hql.append(" where 1=1 ");
        List<Object> paramList = new ArrayList<Object>();
        String condition = buildConditionHql(filter, paramList);
        String orderHql = buildOrderCondition(filter, false);
        hql.append(condition).append(orderHql);
        Query query = createQuery(hql.toString());
        this.prepareParameter(paramList, query);
        // query.setMaxResults(3000);
        Iterator<String> it = query.iterate();
        List<String> list = new ArrayList<String>();
        while (it.hasNext()) {
            list.add(it.next());
        }
        return list;
    }

    private void setMergeBoardDtos(List objs, List<MergeBoardDTO> mergeBoardDTOList) {
        for (Object obj : objs) {
            Object[] o = (Object[]) obj;
            MergeBoardDTO stockQueryDTO = new MergeBoardDTO();
            stockQueryDTO.setDocNo((String) o[0]);
            stockQueryDTO.setPktType(o[1] == null ? null : ((Byte) o[1]).intValue());
            stockQueryDTO.setPickTotal(o[2] == null ? null : ((BigInteger) o[2]).intValue());
            stockQueryDTO.setFinishedPiskNos((String) o[3]);
            stockQueryDTO.setNotFinishedPiskNos((String) o[4]);
            stockQueryDTO.setContainerNo1((String) o[5]);
            stockQueryDTO.setContainerNo2((String) o[6]);
            stockQueryDTO.setContainerNo3((String) o[7]);
            stockQueryDTO.setMergeCode((String) o[8]);
            stockQueryDTO.setMergeStatus(o[9] == null ? null : ((Integer) o[9]));
            stockQueryDTO.setWaveNo((String) o[10]);
            mergeBoardDTOList.add(stockQueryDTO);
        }
    }

    public String builderHqlByAll(MergeBoardFilter mergeBoardFilter, Map<String, Object> paramMap) {
        String sql = "select tt.* from ( SELECT dh.do_no docNo,ph.pkt_type pktType,count(DISTINCT ph.pkt_no) pickTotal, "; // 总拣货单数
        sql += " GROUP_CONCAT(distinct if(ph.status = '90' or ph.status = '99',ph.pkt_no,null) ) finishedPiskNos, "; // 已拣完拣货单
        sql += " GROUP_CONCAT(distinct if(ph.status != '90' and ph.status != '99',ph.pkt_no,null) ) notFinishedPiskNos, "; // 未拣完拣货单
        sql += " GROUP_CONCAT(DISTINCT mc1.container_no) containerNo1, "; // 已绑未入容器号
        sql += " GROUP_CONCAT(DISTINCT mc2.container_no) containerNo2, "; // 已入未出容器号
        sql += " GROUP_CONCAT(DISTINCT mc3.container_no) containerNo3,  "; // 已出容器号
        sql += " ml.MERGE_CODE mergeCode,ml.status mergeStatus,wh.wave_no waveNo FROM doc_do_header dh"; // 集货位,集货状态
        sql += " INNER JOIN doc_pkt_header ph ON ph.wave_header_id = dh.wave_id ";
        sql += " INNER JOIN doc_wave_header wh ON wh.id = dh.wave_id ";
        sql += " LEFT JOIN md_container mc1 ON mc1.doc_no = dh.do_no AND mc1.REF_NO_1 = ph.pkt_no AND mc1.is_deleted = 0 and mc1.doc_type = :docType and mc1.business_status = :businessStatus1 and mc1.warehouse_id = :warehouseId ";
        sql += " LEFT JOIN md_container mc2 ON mc2.doc_no = dh.do_no AND mc2.REF_NO_1 = ph.pkt_no AND mc2.is_deleted = 0 and mc2.doc_type = :docType and mc2.business_status = :businessStatus2 and mc2.warehouse_id = :warehouseId ";
        sql += " LEFT JOIN md_container mc3 ON mc3.doc_no = dh.do_no AND mc3.REF_NO_1 = ph.pkt_no AND mc3.is_deleted = 0 and mc3.doc_type = :docType and mc3.business_status = :businessStatus3 and mc3.warehouse_id = :warehouseId ";
        sql += " LEFT JOIN md_merge_loc ml ON ml.DOC_NO = dh.do_no and ml.DOC_TYPE = :docType and ml.pkt_type = ph.pkt_type and ml.warehouse_id = :warehouseId ";
        sql += " LEFT JOIN md_merge_partition mp ON mp.id = ml.merge_partition_id and mp.warehouse_id = :warehouseId ";
        sql += " WHERE dh.warehouse_id = :warehouseId and dh.status >= '40' and dh.wave_flag = 1 ";
        if (StringUtil.isNotBlank(mergeBoardFilter.getDocNo())) {
            sql += " and dh.do_no = :docNo";
            paramMap.put("docNo", mergeBoardFilter.getDocNo());
        }
        if (StringUtil.isNotBlank(mergeBoardFilter.getWaveNo())) {
            sql += " and wh.wave_no = :waveNo";
            paramMap.put("waveNo", mergeBoardFilter.getWaveNo());
        }
        if (StringUtil.isNotBlank(mergeBoardFilter.getMergeCode())) {
            sql += " and ml.merge_code = :mergeCode";
            paramMap.put("mergeCode", mergeBoardFilter.getMergeCode());
        }
        if (mergeBoardFilter.getPktType() != null) {
            sql += " and ph.pkt_type = :pktType";
            paramMap.put("pktType", mergeBoardFilter.getPktType());
        }
        if (mergeBoardFilter.getMergePartitionId() != null) {
            sql += " and mp.id = :mergePartitionId";
            paramMap.put("mergePartitionId", mergeBoardFilter.getMergePartitionId());
        }
        if (mergeBoardFilter.getDoCreateTimeFrom() != null) {
            sql += " and dh.do_create_time >= :doCreateTimeFrom";
            paramMap.put("doCreateTimeFrom", mergeBoardFilter.getDoCreateTimeFrom());
        }
        if (mergeBoardFilter.getDoCreateTimeTo() != null) {
            sql += " and dh.do_create_time <= :doCreateTimeTo";
            paramMap.put("doCreateTimeTo", mergeBoardFilter.getDoCreateTimeTo());
        }
        sql += " GROUP BY wh.id,dh.id,ph.pkt_type ) tt  ";
        sql += " where ( tt.containerNo1 is not null or tt.containerNo2 is not null or tt.containerNo3 is not null or tt.mergeCode is not null or tt.finishedPiskNos is null )";

        if (MergeLoc.MergeStatus.INIT.getValue().equals(mergeBoardFilter.getMergeStatus())) {
            sql += " and tt.mergeCode is null and  tt.containerNo2 is null and  tt.containerNo3 is null ";
        }
        if (MergeLoc.MergeStatus.IN.getValue().equals(mergeBoardFilter.getMergeStatus())
                || MergeLoc.MergeStatus.DONE.getValue().equals(mergeBoardFilter.getMergeStatus())) {
            sql += " and tt.mergeStatus = :mergeStatus";
            paramMap.put("mergeStatus", mergeBoardFilter.getMergeStatus());
        }
        if (MergeLoc.MergeStatus.OUT.getValue().equals(mergeBoardFilter.getMergeStatus())) {
            sql += " and tt.containerNo3 is not null ";
        }
        if (mergeBoardFilter.getOnlyMerge()) {
            sql += " and tt.mergeStatus is not null ";
        }
        if (StringUtil.isNotBlank(mergeBoardFilter.getContainerNo())) {
            sql += " and ( FIND_IN_SET(:containerNo,tt.containerNo1)>0 or FIND_IN_SET(:containerNo,tt.containerNo2)>0 or FIND_IN_SET(:containerNo,tt.containerNo3)>0 )";
        }
        sql += " order by tt.mergeCode desc ";
        paramMap.put("docType", Constants.BindDocType.DELIVERYORDER.getValue());
        paramMap.put("businessStatus1", Constants.ContainerBusinessStatus.PICKING.getValue());
        paramMap.put("businessStatus2", Constants.ContainerBusinessStatus.MERGED.getValue());
        paramMap.put("businessStatus3", Constants.ContainerBusinessStatus.OUT_MERGE.getValue());
        if (StringUtil.isNotBlank(mergeBoardFilter.getContainerNo())) {
            paramMap.put("containerNo", mergeBoardFilter.getContainerNo());
        }
        paramMap.put("warehouseId", ParamUtil.getCurrentWarehouseId());
        return sql;
    }

    public Long getNeedSyncSerialNoCount(Long doId) {
        String sql = "SELECT " +
                " SUM(dd.expected_qty) " +
                "FROM " +
                " doc_do_detail dd " +
                "INNER JOIN md_sku sku ON sku.id = dd.sku_id " +
                "WHERE dd.do_header_id = :doId AND dd.is_do_leaf = 1 AND sku.sn_qty > 0";
        Query query = this.createSQLQuery(sql).setParameter("doId", doId);
        BigDecimal count = (BigDecimal) query.uniqueResult();
        return count == null ? null : count.longValue();
    }

    public List<String> findDoNoList4WavePickByPartDoNo(String doNo) {
        String sql = "SELECT do_no FROM doc_do_header dh " +
                "INNER JOIN doc_wave_header wh ON wh.id = dh.wave_id " +
                "WHERE dh.warehouse_id = :warehouseId AND wh.status < :allPicked " +
                "AND dh.release_status = :releaseStatus AND dh.do_no LIKE '%" + doNo + "'";
        Query query = this.createSQLQuery(sql);
        query.setParameter("allPicked", WaveStatus.ALLPICKED.getValue());
        query.setParameter("releaseStatus", ReleaseStatus.RELEASE.getValue());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    public List<String> findDoNos4RecheckByPartDoNo(String doNo) {
        String sql = "SELECT do_no FROM doc_do_header dh " +
                "WHERE dh.warehouse_id = :warehouseId AND dh.status in (:doStatusList) " +
                "AND dh.release_status = :releaseStatus AND dh.do_no LIKE '%" + doNo + "'";
        Query query = this.createSQLQuery(sql);
        query.setParameterList("doStatusList",
                Arrays.asList(DoStatus.ALLSORTED.getValue(), DoStatus.PART_CARTON.getValue()));
        query.setParameter("releaseStatus", ReleaseStatus.RELEASE.getValue());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    public void updatePrintFlag(List<Long> doIds) {
        String hql = "UPDATE doc_do_header dh " +
                "SET dh.print_flag  = :printFlag where dh.id in (:doIds) and dh.warehouse_id = :warehouseId";
        Query query = createSQLQuery(hql);
        query.setParameter("printFlag", Constants.YesNo.YES.getValue());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameterList("doIds", doIds);
        query.executeUpdate();
    }

    public void updatecCrossSeedAllocate(Long crossSeedHeaderId, Long asnId) {
        String hql = " UPDATE doc_do_header dh left join doc_alc_header ah on ah.id = dh.id" +
                " SET dh.status = :toStatus,ah.status = :toStatus ";
        hql += " WHERE dh.source_asn_id =:asnId AND dh.status= :fmStatus ";
        Query query = this.createSQLQuery(hql);
        query.setParameter("asnId", asnId);
        query.setParameter("fmStatus", Constants.DoStatus.INITIAL.getValue());
        query.setParameter("toStatus", Constants.DoStatus.ALLALLOCATED.getValue());
        query.executeUpdate();

        hql = " UPDATE doc_do_detail dd INNER JOIN doc_do_header dh ON dd.do_header_id = dh.id ";
        hql += " INNER JOIN ( ";
        hql += " SELECT SUM(cd.allocated_qty) allocatedQty,cd.do_detail_id ";
        hql += " FROM doc_cross_seed_detail cd ";
        hql += " WHERE cd.header_id = :crossSeedHeaderId ";
        hql += " GROUP BY cd.do_detail_id ) tt on tt.do_detail_id = dd.id ";
        hql += " LEFT JOIN doc_alc_detail ad on ad.id = dd.id ";
        hql += " SET dd.allocated_qty = tt.allocatedQty,dd.linestatus = :toStatus,";
        hql += " ad.allocated_qty = tt.allocatedQty,ad.linestatus = :toStatus ";
        hql += " WHERE dh.source_asn_id = :asnId and dh.warehouse_id = :warehouseId and dd.linestatus=:fmStatus";
        query = this.createSQLQuery(hql);
        query.setParameter("crossSeedHeaderId", crossSeedHeaderId);
        query.setParameter("asnId", asnId);
        query.setParameter("toStatus", Constants.DoStatus.ALLALLOCATED.getValue());
        query.setParameter("fmStatus", Constants.DoStatus.INITIAL.getValue());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    public void updateStatus(List<Long> doIds, String updateBy, String status) {
        String hql = "update DeliveryOrderHeader o set o.status = :status,o.updatedBy = :updatedBy " +
                "where o.id in (:ids) and o.warehouseId = :warehouseId";
        Query query = createUpdateQuery(hql);
        query.setParameterList("ids", doIds);
        query.setString("updatedBy", updateBy);
        query.setString("status", status);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    public List<DeliveryOrderHeader> getBySourceAsnIds(List<Long> asnHeaderIds) {
        String hql = "from DeliveryOrderHeader do where do.sourceAsnId in (:asnHeaderIds) and do.warehouseId = :whId";
        Query query = createQuery(hql);
        query.setParameterList("asnHeaderIds", asnHeaderIds);
        query.setLong("whId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    public List<Long> findCanShipDoIdList() {
        int times = Config.getInt(Keys.Delivery.cross_ship_delay_time, Config.ConfigLevel.WAREHOUSE, 60);
        String shipStatus = Config.get(Keys.Delivery.cross_ship_do_status, Config.ConfigLevel.WAREHOUSE,
                DoStatus.ALL_CARTON.getValue());
        String sql = "select d.id from doc_do_header d " +
                " where d.warehouse_id =:warehouseId and d.need_crossstock = :needCrossstock " +
                " and d.is_deleted =0 and d.release_status = :releaseStatus and d.need_cancel = 0 and d.status = :status "
                +
                " and d.pack_time_end < DATE_SUB(CURRENT_TIMESTAMP(),INTERVAL " + times + " MINUTE) ";
        if (!DoStatus.ALL_CARTON.getValue().equals(shipStatus)) {
            sql += " and exists ( select 1 from doc_load_header lh,doc_load_detail ld where ld.load_header_id = lh.id "
                    +
                    " and ld.do_header_id = d.id and ld.is_deleted = 0 and lh.is_deleted = 0 and lh.status = '80' )";
        }
        SQLQuery sqlQuery = createSQLQuery(sql);
        sqlQuery.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        sqlQuery.setParameter("needCrossstock", YesNo.YES.getValue());
        sqlQuery.setParameter("releaseStatus", ReleaseStatus.RELEASE.getValue());
        sqlQuery.setParameter("status", shipStatus);
        sqlQuery.addScalar("id", Hibernate.LONG);
        return sqlQuery.list();
    }

    public DeliveryOrderHeader getDoHeaderByContainerNo(String containerNo) {
        String hql = "from DeliveryOrderHeader dh where dh.userDeffine6 = :containerNo and dh.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setParameter("containerNo", containerNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (DeliveryOrderHeader) query.uniqueResult();
    }

    public List<Long> findCtnReCheckDoIds() {
        String sql = "select dh.id from doc_do_header dh " +
                " where dh.warehouse_id =:warehouseId and dh.is_deleted =0 and dh.order_sub_type = :orderSubType " +
                " and dh.status = '65' and dh.need_cancel = 0 and dh.release_status = :releaseStatus AND (dh.auto_flag & :autoFlag) != 0 ";
        SQLQuery sqlQuery = createSQLQuery(sql);
        sqlQuery.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        sqlQuery.setParameter("orderSubType", DoSubType.CTN.getValue());
        sqlQuery.setParameter("releaseStatus", ReleaseStatus.RELEASE.getValue());
        sqlQuery.setParameter("autoFlag", WaveHeader.FLAG_AUTO_RECHECK);
        sqlQuery.addScalar("id", Hibernate.LONG);
        return sqlQuery.list();
    }

    public void modifyCarrier(List<Long> doIdList, Long carrierId) {
        String hql = "update DeliveryOrderHeader o set o.carrierId = :carrierId " +
                "where o.id in (:ids) and o.warehouseId = :warehouseId";
        Query query = createUpdateQuery(hql);
        query.setParameterList("ids", doIdList);
        query.setLong("carrierId", carrierId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    public boolean checkDoStatus(List<Long> doIdList, String fmStatus, String toStatus) {
        String sql = "SELECT o.id FROM doc_do_header o WHERE o.id IN (:doIds) AND warehouse_id = :warehouseId and o.status >= :fmStatus and o.status <= :toStatus ";
        SQLQuery sqlQuery = this.createSQLQuery(sql);
        sqlQuery.setParameterList("doIds", doIdList).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId())
                .setParameter("fmStatus", fmStatus).setParameter("toStatus", toStatus);
        sqlQuery.addScalar("id", Hibernate.LONG);
        List<Long> checkList = sqlQuery.list();
        return checkList.size() == doIdList.size();
    }

    public List<DeliveryOrderHeader> queryByDoNos(List<String> doNos, Long warehouseId) {
        Criteria cri = this.getSession().createCriteria(DeliveryOrderHeader.class);
        cri.add(Restrictions.in("doNo", doNos));
        cri.add(Restrictions.eq("warehouseId", warehouseId));
        return cri.list();
    }

}