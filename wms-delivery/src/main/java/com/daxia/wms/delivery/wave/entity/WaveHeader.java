package com.daxia.wms.delivery.wave.entity;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.xml.bind.annotation.XmlRootElement;

import com.daxia.framework.common.util.FlagUtil;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.master.entity.SortingBin;

/**
 * 波次头信息实体
 */
@Entity
@Table(name = "doc_wave_header")
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = " update doc_wave_header set is_deleted = 1 where id = ? and version = ?")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@BatchSize(size = 20)
@XmlRootElement
@lombok.extern.slf4j.Slf4j
public class WaveHeader extends WhBaseEntity {

    private static final long serialVersionUID = 1384967082963505035L;
    
    public static final int FLAG_AUTO_PICK = FlagUtil.FLAG_ONE;
    public static final int FLAG_AUTO_SORT = FlagUtil.FLAG_TWO;
    public static final int FLAG_AUTO_RECHECK = FlagUtil.FLAG_THREE;
    public static final int FLAG_AUTO_SHIP = FlagUtil.FLAG_FOUR;
    
    public static final int FLAG_AUTO_ALL = FlagUtil.add(FLAG_AUTO_SHIP, FlagUtil.add(FLAG_AUTO_RECHECK, FlagUtil.add(FLAG_AUTO_SORT, FlagUtil.add(FLAG_AUTO_PICK, 0))));
    
    //失败类型
    public static final int FAILED_TYPE_PICK = 1;
    public static final int FAILED_TYPE_SORT = 2;
    public static final int FAILED_TYPE_RECHECK = 3;
    
    public static final int FAILED_NUM_MAX = 5;
    
    public static final int FLAG_PRINT_DO = FlagUtil.FLAG_ONE;
    public static final int FLAG_PRINT_PICK = FlagUtil.FLAG_TWO;
    public static final int FLAG_PRINT_PKT_LABEL = FlagUtil.FLAG_THREE;
    public static final int FLAG_PRINT_CARTON = FlagUtil.FLAG_FOUR;
    public static final int FLAG_PRINT_INSPECTION_REPORT = FlagUtil.FLAG_FIVE;
    public static final int FLAG_PRINT_YAO_TO = FlagUtil.FLAG_SIX;
    public static final int FLAG_PRINT_PICK_WAYBILL = FLAG_PRINT_PICK+FLAG_PRINT_CARTON;
    public static final int FLAG_PRINT_PICK_DO_WAYBILL = FLAG_PRINT_PICK+FLAG_PRINT_CARTON+FLAG_PRINT_DO;

    /**
     *  主键
     */
    private Long id;

    /**
     * 波次编号
     */
    private String waveNo;
    /**
     * 波次状态
     */
    private String waveStatus;
    /**
     * 波次类型
     */
    private String waveType;
    /**
     * 拣货单数量
     */
    private Long pktCount;
    /**
     * 订单数量
     */
    private Integer doCount;

    /**
     * 分拣柜号
     */
    private String sortCabintNo;
    /**
     * 备注
     */
    private String notes;
    /**
     * 打印标记（0：未打印；1：已打印）
     */
   private Long printFlag;
    
    /**
     * 促销单品
     * 0未拣货，1已拣货
     */
    private Integer promoteSkuPickFlag;

    /**
     * 拣货单
     */
    private List<PickHeader> pktHeaders;
    
    /**
     * 分拣柜Id
     */
    private Long sortGridId;
    
    /**
     * 集货位Id
     */
    private Long mergeLocId;
    
    /**
     * 集货状态（0：未集货，1：集货中, 2已集货，3：释放集货位）
     */
    private Integer mergeStatus;
    
    /**
     * 分拣柜
     */
    private SortingBin sortingBin;

    /**
     * 发货单集合
     */
    private List<DeliveryOrderHeader> doHeaders;
    
    /**
     * 拣货明细
     */
    private List<PickTask> pickTasks;
    
    /**
     * 波次优先级
     */
    private Integer priority;
    
    /**
     * 集货开始时间
     */
    private Timestamp mergeTime;
    
    /**
     * 集货完成时间（集货出区完成）
     */
    private Timestamp mergeFinishTime;
    
    /**
     * 波次最早预计出库时间
     */
    private Date estDoFinishTime;
    
    /**
     * 波次是否分配过分拣柜 0（默认）  1 拣货时分配过  2 RF分配分拣柜页面分配过 
     */
    private Integer allocateSortingBin;
    
    private Integer isRecommend;
    
    private Integer isAuto;
    
    private Integer invoiceFlag;
    
    /* 发票打印状态
     * 0 - 未打印 / 1 - 已打印 / 2 - 部分打印 */
    // 德开：0：初始化；1：已发送；2：已绑定
    private Integer invoicePrintFlag;
    
    //AutoWaveType：{0：团购；1：普通；2：准时达；3：特殊；4：RTV；5：调拨}
    private Integer autoType;
    
    private Long ruleDetailId;
    
    private Long criteriaDetailId;
    
    private Integer volumeType;

    /**
     * 打印人
     */
    private String printBy;
    
    /**
     * 自动处理失败次数
     */
    private Integer autoFailTime;

    //自动处理标记
    private Integer autoFlag;
    
    //失败类型
    private Integer failedType;
    
    private Integer failedNumber;

    /**
     * 紧急标记
     */
    private Integer emergencyFlag;
    /**
     * 打印购物清单标记
     */
    private Integer  doNeedPrint;
    @Column(name = "do_need_print")
    public Integer getDoNeedPrint() {
        return doNeedPrint;
    }

    public void setDoNeedPrint(Integer doNeedPrint) {
        this.doNeedPrint = doNeedPrint;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)  
    @Column(name = "ID")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    
    @Column(name = "auto_flag")
    public Integer getAutoFlag() {
        return autoFlag;
    }
    
    public void setAutoFlag(Integer autoFlag) {
        this.autoFlag = autoFlag;
    }
    
    @Column(name = "failed_type")
    public Integer getFailedType() {
        return failedType;
    }
    
    public void setFailedType(Integer failedType) {
        this.failedType = failedType;
    }
    
    @Column(name = "failed_number")
    public Integer getFailedNumber() {
        return failedNumber;
    }
    
    public void setFailedNumber(Integer failedNumber) {
        this.failedNumber = failedNumber;
    }
    
    @Column(name = "WAVE_NO")
    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    @Column(name = "STATUS")
    public String getWaveStatus() {
        return waveStatus;
    }

    public void setWaveStatus(String waveStatus) {
        this.waveStatus = waveStatus;
    }

    @Column(name = "WAVE_TYPE")
    public String getWaveType() {
        return waveType;
    }

    public void setWaveType(String waveType) {
        this.waveType = waveType;
    }

    @Column(name = "PKT_COUNT")
    public Long getPktCount() {
        return pktCount;
    }

    public void setPktCount(Long pktCount) {
        this.pktCount = pktCount;
    }
    
    @Column(name = "DO_COUNT")
    public Integer getDoCount() {
        return doCount;
    }

    
    public void setDoCount(Integer doCount) {
        this.doCount = doCount;
    }

    @Column(name = "SORT_CABINET_NO")
    public String getSortCabintNo() {
        return sortCabintNo;
    }

    public void setSortCabintNo(String sortCabintNo) {
        this.sortCabintNo = sortCabintNo;
    }

    @Column(name = "NOTES")
    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public void setDoHeaders(List<DeliveryOrderHeader> doHeaders) {
        this.doHeaders = doHeaders;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "waveHeader")
    @Where(clause = " IS_DELETED = 0 ")
    public List<DeliveryOrderHeader> getDoHeaders() {
        return doHeaders;
    }

    public void setPktHeaders(List<PickHeader> pktHeaders) {
        this.pktHeaders = pktHeaders;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "waveHeader")
    @Where(clause = " IS_DELETED = 0 ")
    public List<PickHeader> getPktHeaders() {
        return pktHeaders;
    }

    @Column(name = "PRINT_FLAG")
	public Long getPrintFlag() {
		return printFlag;
	}

	public void setPrintFlag(Long printFlag) {
		this.printFlag = printFlag;
	}

	@Column(name = "PROMOTESKU_PICK_FLAG")
	public Integer getPromoteSkuPickFlag() {
		return promoteSkuPickFlag;
	}

	public void setPromoteSkuPickFlag(Integer promoteSkuPickFlag) {
		this.promoteSkuPickFlag = promoteSkuPickFlag;
	}
	
	@Column(name = "SORT_GRID_ID")
	public Long getSortGridId() {
		return sortGridId;
	}

	public void setSortGridId(Long sortGridId) {
		this.sortGridId = sortGridId;
	}
	
    @ManyToOne(targetEntity = SortingBin.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "SORT_GRID_ID", insertable = false, updatable = false)
	public SortingBin getSortingBin() {
		return sortingBin;
	}

	public void setSortingBin(SortingBin sortingBin) {
		this.sortingBin = sortingBin;
	}

	@Column(name = "MERGE_LOC_ID")
	public Long getMergeLocId() {
		return mergeLocId;
	}

	public void setMergeLocId(Long mergeLocId) {
		this.mergeLocId = mergeLocId;
	}

	@Column(name = "MERGE_STATUS")
	public Integer getMergeStatus() {
		return mergeStatus;
	}

	public void setMergeStatus(Integer mergeStatus) {
		this.mergeStatus = mergeStatus;
	}

    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "WAVE_H_ID", insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public List<PickTask> getPickTasks() {
        return pickTasks;
    }

    public void setPickTasks(List<PickTask> pickTasks) {
        this.pickTasks = pickTasks;
    }

    @Column(name = "PRIORITY")
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    @Column(name = "MERGE_TIME")
    public Timestamp getMergeTime() {
        return mergeTime;
    }

    public void setMergeTime(Timestamp mergeTime) {
        this.mergeTime = mergeTime;
    }

    @Column(name = "MERGE_FINISH_TIME")
    public Timestamp getMergeFinishTime() {
        return mergeFinishTime;
    }
    
    public void setMergeFinishTime(Timestamp mergeFinishTime) {
        this.mergeFinishTime = mergeFinishTime;
    }

    @Column(name = "E_DO_FINISH_TIME")
    public Date getEstDoFinishTime() {
        return estDoFinishTime;
    }
    
    public void setEstDoFinishTime(Date estDoFinishTime) {
        this.estDoFinishTime = estDoFinishTime;
    }
    
    @Column(name = "ALLOCATE_SORTING_BIN")
    public Integer getAllocateSortingBin() {
		return allocateSortingBin;
	}
    
    public void setAllocateSortingBin(Integer allocateSortingBin) {
		this.allocateSortingBin = allocateSortingBin;
	}

    @Column(name = "IS_RECOMMEND")
    public Integer getIsRecommend() {
        return isRecommend;
    }

    public void setIsRecommend(Integer isRecommend) {
        this.isRecommend = isRecommend;
    }

    @Column(name = "IS_ATUO")
    public Integer getIsAuto() {
        return isAuto;
    }

    public void setIsAuto(Integer isAuto) {
        this.isAuto = isAuto;
    }

    @Column(name = "INVOICE_FLAG")
    public Integer getInvoiceFlag() {
        return invoiceFlag;
    }

    public void setInvoiceFlag(Integer invoiceFlag) {
        this.invoiceFlag = invoiceFlag;
    }

    @Column(name = "INVOICE_PRINT_FLAG")
    public Integer getInvoicePrintFlag() {
        return invoicePrintFlag;
    }

    public void setInvoicePrintFlag(Integer invoicePrintFlag) {
        this.invoicePrintFlag = invoicePrintFlag;
    }

    @Column(name = "AUTO_TYPE")
    public Integer getAutoType() {
        return autoType;
    }

    public void setAutoType(Integer autoType) {
        this.autoType = autoType;
    }

    @Column(name = "RULE_DETAIL_ID")
	public Long getRuleDetailId() {
		return ruleDetailId;
	}

	public void setRuleDetailId(Long ruleDetailId) {
		this.ruleDetailId = ruleDetailId;
	}

	@Column(name = "CRITERIA_DETAIL_ID")
	public Long getCriteriaDetailId() {
		return criteriaDetailId;
	}

	public void setCriteriaDetailId(Long criteriaDetailId) {
		this.criteriaDetailId = criteriaDetailId;
	}
	
	@Column(name = "AUTO_FAIL_TIME")
	public Integer getAutoFailTime() {
		return autoFailTime;
	}
	
	public void setAutoFailTime(Integer autoFailTime) {
		this.autoFailTime = autoFailTime;
	}

	@Column(name = "volume_type")
	public Integer getVolumeType() {
		return volumeType;
	}

	public void setVolumeType(Integer volumeType) {
		this.volumeType = volumeType;
	}

    @Column(name = "print_by")
    public String getPrintBy() {
        return printBy;
    }

    public void setPrintBy(String printBy) {
        this.printBy = printBy;
    }

    @Column(name = "EMERGENCY_FLAG")
    public Integer getEmergencyFlag() {
        return emergencyFlag;
    }

    public void setEmergencyFlag(Integer emergencyFlag) {
        this.emergencyFlag = emergencyFlag;
    }
}