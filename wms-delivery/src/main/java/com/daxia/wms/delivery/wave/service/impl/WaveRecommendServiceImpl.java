package com.daxia.wms.delivery.wave.service.impl;

import java.util.Collections;
import java.util.List;

import com.daxia.wms.delivery.wave.service.WaveRecommend;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoAllocateHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto;
import com.daxia.wms.master.rule.filter.BigWaveFilter;
import com.daxia.wms.delivery.wave.service.WaveRecommendService;
import com.daxia.wms.wave.vo.DoRecommendDTO;
import com.daxia.wms.wave.vo.DoRecommendLimit;
import com.daxia.wms.wave.vo.DoRecommendResult;

@Name("waveRecommendService")
@lombok.extern.slf4j.Slf4j
public class WaveRecommendServiceImpl implements WaveRecommendService {
    
    @In
    WaveRecommend waveRecommend;
    
    @In
    private DoAllocateHeaderDAO doAllocateHeaderDAO;
    
    @Override
    public DoRecommendResult recommend(List<DoRecommendDTO> recomendDTOs, Integer waveDocQty) {
        if (waveRecommend == null) {
            throw new DeliveryException(DeliveryException.WAVE_RECOMMENT_NOT_CONNECT);
        }
        
        DoRecommendLimit limit = new DoRecommendLimit();
        limit.setMaxUnitNum(Double.valueOf(SystemConfig.getConfigValue("delivery.wave.maxUnitNum", ParamUtil.getCurrentWarehouseId())));
        limit.setMaxVolume(Double.valueOf(SystemConfig.getConfigValue("delivery.wave.maxVolume", ParamUtil.getCurrentWarehouseId())));
        limit.setMaxWeight(Double.valueOf(SystemConfig.getConfigValue("delivery.wave.maxWeight", ParamUtil.getCurrentWarehouseId())));
        limit.setRecommendNum(waveDocQty);
        limit.setAdditionNum(SystemConfig.getConfigValueInt("delivery.wave.additionNum", ParamUtil.getCurrentWarehouseId()));
        
        return waveRecommend.recommend(recomendDTOs, limit);
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public List<DoHeaderDto> findBigWaveDoList(BigWaveFilter bigWaveFilter, Integer maxDOQty) {
        List<DoRecommendDTO> dtos = doAllocateHeaderDAO.findDoRecommends(bigWaveFilter, SystemConfig.getConfigValueInt("delivery.wave.recommendPoolSize", ParamUtil.getCurrentWarehouseId()));
        
        if (ListUtil.isNullOrEmpty(dtos)) {
            return Collections.EMPTY_LIST;
        }
        
        List<Long> doIds = recommend(dtos, maxDOQty).getDoIds();
        
        return doAllocateHeaderDAO.findBigWaveDoList(doIds);
    }
}