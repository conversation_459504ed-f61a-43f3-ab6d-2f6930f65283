package com.daxia.wms.delivery.invoice.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.*;
import com.daxia.wms.Constants;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.service.impl.ElectronicInvoiceContext;
import com.daxia.wms.delivery.invoice.entity.InvoiceDetail;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.filter.InvoiceFilter;
import com.daxia.wms.delivery.invoice.service.ElectronicInvoiceService;
import com.daxia.wms.delivery.invoice.service.InvoiceService;
import com.daxia.wms.delivery.invoice.service.PrintInvoiceService;
import com.daxia.wms.delivery.util.ExportDeliveryOrderUtil;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.security.Restrict;
import org.jboss.seam.annotations.web.RequestParameter;

import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 发票Action
 */
@Name("com.daxia.wms.delivery.invoiceAction")
@Restrict("#{identity.hasPermission('delivery.invoice')}")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class InvoiceAction extends PagedListBean<InvoiceHeader> {

    private static final long serialVersionUID = 8303705957722790990L;

    private static Set<String> bindErrorSet;
    private InvoiceFilter invoiceFilter;
    private Boolean selectAll = Boolean.FALSE;
    private Boolean lockFlag = false;
    private String invoiceNoFrom;
    private String invoiceNoTo;
    private String invoiceCode;
    private Long invoiceId;
    private List<InvoiceDetail> invoiceDetails;
    //下一次打印开始的发票号码
    private String invoiceNoNext = "";

    private String printContent = "";


    @In
    private ElectronicInvoiceContext electronicInvoiceContext;

    @In
    private InvoiceService invoiceService;

    @In(value = "com.daxia.wms.delivery.invoice.printInvoiceService")
    private PrintInvoiceService printInvoiceService;

    @RequestParameter
    private Long id;

    //发票查询默认pageSize
    private static final int DEFAULT_PAGESIZE_FOR_INVOICE = 50;


    /**
     * 存取发票异常信息
     */
    static {
        bindErrorSet = new HashSet<String>();
        bindErrorSet.add(DeliveryException.ERROR_INVOICENO_NULL);
        bindErrorSet.add(DeliveryException.ERROR_INVOICECODE_NULL);
        bindErrorSet.add(DeliveryException.ERROR_INVOICE_NOFM_NOTEXSIT);
        bindErrorSet.add(DeliveryException.ERROR_INVOICE_NOTO_NOTEXSIT);
        bindErrorSet.add(DeliveryException.ERROR_INVOICE_NOFM_BIG_NOTO);
        bindErrorSet.add(DeliveryException.ERROR_INVOICE_NO_NOT_SAMEBOOK);
        bindErrorSet.add(DeliveryException.ERROR_INVOICE_BOOK_PRINTED);
    }

    @Create
    public void init() {
        invoiceFilter = new InvoiceFilter();
        if (Config.isDefaultFalse(Keys.Delivery.invoice_hangXinSwitch, Config.ConfigLevel.TENANT)) {//是否启用航天信息电子发票
            invoiceFilter.setIsWave(Constants.YesNo.YES.getValue());
        }
        this.getDataPage().setPageSize(DEFAULT_PAGESIZE_FOR_INVOICE);
    }

    /**
     * 查询发票信息
     */
    @Override
    public void query() {
        this.buildOrderFilterMap(invoiceFilter);
        DataPage<InvoiceHeader> dp = invoiceService.findInvoiceByFilter(invoiceFilter, getStartIndex(), getPageSize());
        populateValues(dp);
    }

    public void printRollInovice() {
        List<Long> ids = getSelectId();
        if (ListUtil.isNullOrEmpty(ids)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        printContent = "";
        if (Config.isDefaultFalse(Keys.Delivery.invoice_hangXinSwitch, Config.ConfigLevel.TENANT)) {//是否启用航天信息电子发票
            for (Long aLong : ids) {
                invoiceService.issueInvoice(aLong);
                invoiceService.printInvoice(aLong);
            }
            query();
            sayMessage(MESSAGE_SUCCESS);
        } else {
            printContent = printInvoiceService.printByInvoice(ids);
        }
    }

    private List<Long> getSelectId() {
        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }
        return ids;
    }

    /**
     * 发票模式是否是模式3\4\5
     */
    public boolean isLevel3Or4() {
        return InvoiceService.INVOICE_LEVEL_3.equals(invoiceService.getInvoiceLevel())
                || InvoiceService.INVOICE_LEVEL_4.equals(invoiceService.getInvoiceLevel())
                || InvoiceService.INVOICE_LEVEL_5.equals(invoiceService.getInvoiceLevel());
    }

    /**
     * 锁定发票
     */
    public void lockInvoice() {
        this.setLockFlag(false);
        this.setLockFlag(invoiceService.checkInvoiceInfo(this.getInvoiceNoFrom(), this.getInvoiceNoTo(),
                this.getInvoiceCode()));
    }

    public void detail() {
        InvoiceHeader invoiceHeader = invoiceService.getInvoiceById(invoiceId);
        this.invoiceDetails = invoiceService.findInvoiceDetailsByHeaderId(invoiceHeader.getId());
    }

    public void batchBind() {
        List<Long> ids = getSelectId();
        if (ListUtil.isNullOrEmpty(ids)) {
            return;
        }
        for (Long id : ids) {
            try {
                InvoiceHeader invoiceHeader = invoiceService.getInvoiceById(id);
                if (invoiceHeader != null && InvoiceHeader.InvoiceType.ELECTRONIC.getValue().equals(invoiceHeader.getInvoiceType()) && InvoiceHeader.InvoiceStatus.UPLOAD.equals(invoiceHeader.getInvoiceStatus())) {
                    electronicInvoiceContext.bind(invoiceHeader);
                }
            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }
        }
    }

    public void bind() {
        InvoiceHeader invoiceHeader = invoiceService.getInvoiceById(invoiceId);
        if (invoiceHeader != null && InvoiceHeader.InvoiceType.ELECTRONIC.getValue().equals(invoiceHeader.getInvoiceType()) && InvoiceHeader.InvoiceStatus.UPLOAD.equals(invoiceHeader.getInvoiceStatus())) {
            electronicInvoiceContext.bind(invoiceHeader);
        }
    }

    public void writeBack() {
        InvoiceHeader invoiceHeader = invoiceService.getInvoiceById(invoiceId);
        if (invoiceHeader != null && InvoiceHeader.InvoiceType.ELECTRONIC.getValue().equals(invoiceHeader.getInvoiceType())) {
            electronicInvoiceContext.writeBack(invoiceHeader);
        }
    }

    public void batchBilling() {
        List<Long> ids = getSelectId();
        if (ListUtil.isNullOrEmpty(ids)) {
            return;
        }
        for (Long id : ids) {
            try {
                InvoiceHeader invoiceHeader = invoiceService.getInvoiceById(id);
                if (invoiceHeader != null && InvoiceHeader.InvoiceType.ELECTRONIC.getValue().equals(invoiceHeader.getInvoiceType()) && InvoiceHeader.InvoiceStatus.INIT.equals(invoiceHeader.getInvoiceStatus())) {
                    electronicInvoiceContext.billing(invoiceHeader);
                }
            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }
        }
    }

    public void billing() {
        InvoiceHeader invoiceHeader = invoiceService.getInvoiceById(invoiceId);
        if (invoiceHeader != null && InvoiceHeader.InvoiceType.ELECTRONIC.getValue().equals(invoiceHeader.getInvoiceType()) && InvoiceHeader.InvoiceStatus.INIT.equals(invoiceHeader.getInvoiceStatus())) {
            electronicInvoiceContext.billing(invoiceHeader);
        }
    }

    /**
     * 导出
     *
     * @throws IOException
     */
    public void export() throws IOException {
        this.buildOrderFilterMap(invoiceFilter);
        DataPage<InvoiceHeader> dataPage = invoiceService.findInvoiceByFilter(invoiceFilter, getStartIndex(), getPageSize());

        byte[] bytes;
        List<InvoiceHeader> headerList = dataPage.getDataList();
        try {
            bytes = ExportDeliveryOrderUtil.generateForInvoiceDetail(headerList);
        } catch (IOException e) {
            throw new DeliveryException("导出失败");
        }
        DownloadUtil.writeToResponse(bytes, DownloadUtil.EXCEL, "发票明细.xls");
    }

    /**
     * 解锁发票
     */
    public void unlockInvoice() {
        this.setLockFlag(false);
    }

    public InvoiceFilter getInvoiceFilter() {
        return invoiceFilter;
    }

    public void setInvoiceFilter(InvoiceFilter invoiceFilter) {
        this.invoiceFilter = invoiceFilter;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public InvoiceService getInvoiceService() {
        return invoiceService;
    }

    public void setInvoiceService(InvoiceService invoiceService) {
        this.invoiceService = invoiceService;
    }

    public Boolean getSelectAll() {
        return selectAll;
    }

    public void setSelectAll(Boolean selectAll) {
        this.selectAll = selectAll;
    }

    public String getInvoiceNoFrom() {
        return invoiceNoFrom;
    }

    public void setInvoiceNoFrom(String invoiceNoFrom) {
        this.invoiceNoFrom = invoiceNoFrom;
    }

    public String getInvoiceNoTo() {
        return invoiceNoTo;
    }

    public void setInvoiceNoTo(String invoiceNoTo) {
        this.invoiceNoTo = invoiceNoTo;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public Boolean getLockFlag() {
        return lockFlag;
    }

    public void setLockFlag(Boolean lockFlag) {
        this.lockFlag = lockFlag;
    }

    public String getInvoiceNoNext() {
        return invoiceNoNext;
    }

    public void setInvoiceNoNext(String invoiceNoNext) {
        this.invoiceNoNext = invoiceNoNext;
    }

    public Long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public List<InvoiceDetail> getInvoiceDetails() {
        return invoiceDetails;
    }

    public void setInvoiceDetails(List<InvoiceDetail> invoiceDetails) {
        this.invoiceDetails = invoiceDetails;
    }

    public String getPrintContent() {
        return printContent;
    }

    public void setPrintContent(String printContent) {
        this.printContent = printContent;
    }
}