package com.daxia.wms.delivery.pick.filter;

import java.util.Date;
import java.util.List;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * pkt过滤器:
 */
@lombok.extern.slf4j.Slf4j
public class PickHeaderFilter extends WhBaseQueryFilter {

    private static final long serialVersionUID = 5140085214077864256L;

    private List<Long> waveIds;
    private Long waveId;
    
    private Integer mergeStatus;
    
    private Integer mergeStatusNotEqual;

    private List<Long> regionIdsNotIn;
    
    private String waveNo;
    
    private String doNo;
    
    private String pktNo;
    
    private String containerNo;

    private String regionNo;
    
    @Operation(clause = "exists (select 1 from Container c WHERE c.refNo1 = o.pktNo AND c.docType = '3' AND c.containerNo = ? )", operationType = OperationType.CLAUSE)
    public String getContainerNo() {
        return containerNo;
    }
    
    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }
    
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeFm;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeTo;
    
    @Operation(fieldName = "o.createdAt", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")
    public Date getCreateTimeFm() {
        return createTimeFm;
    }
    
    public void setCreateTimeFm(Date createTimeFm) {
        this.createTimeFm = createTimeFm;
    }
    
    @Operation(fieldName = "o.createdAt", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
    public Date getCreateTimeTo() {
        return createTimeTo;
    }
    
    public void setCreateTimeTo(Date createTimeTo) {
        this.createTimeTo = createTimeTo;
    }
    
    @Operation(fieldName = "o.pktNo", operationType = OperationType.EQUAL)
    public String getPktNo() {
        return pktNo;
    }
    
    public void setPktNo(String pktNo) {
        this.pktNo = pktNo;
    }
    
    @Operation(fieldName = "o.waveHeader.waveNo", operationType = OperationType.EQUAL)
    public String getWaveNo() {
        return waveNo;
    }
    
    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    @Operation(fieldName = "o.region.regionCode", operationType = OperationType.EQUAL)
    public String getRegionNo() {
        return regionNo;
    }

    public void setRegionNo(String regionNo) {
        this.regionNo = regionNo;
    }

    @Operation(clause = "exists (select 1 from PickTask tp, DeliveryOrderHeader doHeader WHERE tp.pktHeaderId = o.id and tp.doHeaderId = doHeader.id and doHeader.doNo = ? )", operationType = OperationType.CLAUSE)
    public String getDoNo() {
        return doNo;
    }
    
    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }
    
    public void setWaveIds(List<Long> waveIds) {
        this.waveIds = waveIds;
    }

    @Operation(fieldName = "o.waveHeadId", operationType = OperationType.IN)
    public List<Long> getWaveIds() {
        return waveIds;
    }

    @Operation(fieldName = "o.waveHeadId" , operationType = OperationType.EQUAL)
	public Long getWaveId() {
		return waveId;
	}

	public void setWaveId(Long waveId) {
		this.waveId = waveId;
	}

    @Operation(fieldName = "o.mergeStatus" , operationType = OperationType.EQUAL)
    public Integer getMergeStatus() {
        return mergeStatus;
    }

    public void setMergeStatus(Integer mergeStatus) {
        this.mergeStatus = mergeStatus;
    }

    @Operation(fieldName = "o.mergeStatus", operationType = OperationType.NOT_EQUAL)
    public Integer getMergeStatusNotEqual() {
        return mergeStatusNotEqual;
    }

    public void setMergeStatusNotEqual(Integer mergeStatusNotEqual) {
        this.mergeStatusNotEqual = mergeStatusNotEqual;
    }

    @Operation(fieldName = "o.regionId", operationType = OperationType.NOTIN)
    public List<Long> getRegionIdsNotIn() {
        return regionIdsNotIn;
    }

    public void setRegionIdsNotIn(List<Long> regionIdsNotIn) {
        this.regionIdsNotIn = regionIdsNotIn;
    }
}
