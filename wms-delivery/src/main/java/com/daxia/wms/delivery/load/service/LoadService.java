package com.daxia.wms.delivery.load.service;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.load.entity.LoadDetail;
import com.daxia.wms.delivery.load.entity.LoadHeader;
import com.daxia.wms.delivery.load.entity.LoadHeaderHis;
import com.daxia.wms.delivery.load.entity.ReShipDo;
import com.daxia.wms.delivery.load.filter.LoadDetailFilter;
import com.daxia.wms.delivery.load.filter.LoadFilter;
import com.daxia.wms.delivery.print.dto.LoadPrint;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import org.apache.commons.lang3.tuple.Triple;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * Description:交接业务Service接口
 * </pre>
 *
 */
public interface LoadService {
    
    /**
     * 是否自动发货
     */
    public static final String IS_AUTO = "isAuto";
    /**
     * 自动发货时间
     */
    public static final String SHIP_TIME = "shipTime";
    
    /**
     * 自动发货时间
     */
    public static final String OPERATOR = "operator";
    
    /**
     * 
     * <pre>
     * Description:新增交接单
     * </pre>
     *
     * @param loadHeader 需要保存的交接单对象
     * @return 返回被保存的交接单对象
     * @throws DeliveryException 保存出错时抛出的异常
     */
    public LoadHeader saveOrUpdate(LoadHeader loadHeader) throws DeliveryException;

    /**
     * 
     * <pre>
     * Description:取消交接单
     * </pre>
     *
     * @param loadHeader 需要被取消的交接单
     * @throws DeliveryException 取消交接单出错时抛出的异常
     */
    public void remove(LoadHeader loadHeader) throws DeliveryException;

    /**
     * 
     * <pre>
     * 分页查询送货单信息。
     * </pre>
     * 
     * @param filter
     *            查询条件过滤器
     * @param startIndex
     *            初始页
     * @param pageSize
     *            显示数据条数
     * @return
     */
    public DataPage<LoadHeader> query(LoadFilter loadFilter, int startIndex, int pageSize);
    
    /**
     * 
     * <pre>
     * 分页查询送货单历史信息。
     * </pre>
     * 
     * @param filter
     *            查询条件过滤器
     * @param startIndex
     *            初始页
     * @param pageSize
     *            显示数据条数
     * @return
     */
    public DataPage<LoadHeaderHis> queryHis(LoadFilter loadFilter, int startIndex, int pageSize);

    /**
     * 
     * <pre>
     * 根据loadHeaderId获取loadHeader
     * </pre>
     *
     * @param loadHeaderId
     * @return
     */
    public LoadHeader getLoadHeader(Long loadHeaderId);
    
    
    LoadHeader getLoadHeader(String loadNo);

    /**
     * 
     * <pre>
     * 交接锁定
     * </pre>
     *
     * @param loadHeader
     */
    public LoadHeader lockLoadOrder(LoadHeader loadHeader);
    
    /**
     * 
     * <pre>
     * 交接单打印
     * </pre>
     *
     * @param id
     * @return
     */
    public List<String> print(Long id);

    /**
     * 
     * <pre>
     * 交接单导出
     * </pre>
     *
     * @param id
     * @return
     */
    public byte[] exportPDF(Long id);
    
    /**
     * 查找doId对应的没有交接的交接单
     * @param doId
     * @return
     */
    public CartonHeader findANotLoadedCartonHeaderByDoId(Long doId);
    
    /**
     * 
     * <pre>
     * 导出excel
     * </pre>
     *
     * @param id
     * @return
     */
    public byte[] exportExcel(Long id);

    public void exportLoadDetailExcel(Long id);
    
    /**
     * 
     * <pre>根据交接单一次性获得所有交接信息</pre>
     *
     * @param loadHeaderId
     * @return
     */
    public LoadHeader findLoadDetailsInAcar(Long loadHeaderId, Boolean queryHistory);
    
	/**
	 * do发货；同时支持自动发货
	 * 
	 * @param doHeader
	 * @param autoParam
	 *            配置 是否自动发货 及 发货时间。<br/>
	 *            配置自动发货：<br/>
	 *            autoParam.put(LoadService.IS_AUTO, YesNo.YES.getValue());<br/>
	 *            autoParam.put(LoadService.SHIP_TIME,
	 *            WCS调用DTS执行自动发货的时间-Date类型);
	 */
	public void deliver(DeliveryOrderHeader doHeader,
			Map<String, Object> autoParam);

    /**
     * 按箱交接
     * @param cartonHeader
     * @param loadHeader
     * @param doHeader
     * @param isForceDeliver 是否强制发货
     * @return 装箱结果list中第一条是交接的箱明细，第二条是“是否需要同步包裹到tms”标志
     */
    public List<List<Object>> loadByCarton(List<CartonHeader> cartonHeaderList, LoadHeader loadHeader,List<DeliveryOrderHeader> doHeaderList,Integer isForceDeliver);
    
    /**
     * 接口调用:
     * do状态,箱信息,实体卡,do发送到tms,发票回写
     * @param doHeader
     */
    public void callInterfaceWhenDeliver(DeliveryOrderHeader doHeader);

    /**
     * 获取导出数据
     * @param dataList
     * @return
     */
    public List<LoadPrint> getReportData(List<LoadHeader> dataList);
    
    /**
     * 自动交接前包裹和订单状态的校验。
     * @param cartonNo
     * @return
     * @throws DeliveryException
     */
    public CartonHeader validateBeforeAutoDelivery(String cartonNo) throws DeliveryException ;
    
    /**
     * 如果交接单中包裹数达到最大限制则完成该交接单。
     * 
     * @param lh
     * @throws DeliveryException
     */
    public void completeLoadHeader(LoadHeader lh) throws DeliveryException;

    /**
     * 根据DoId查找ReShipDo
     * 
     * @param doId
     * @param loadMode
     * @return
     */
    public ReShipDo findReShipDoByDoId(Long doId, Integer loadMode);
    
    /**
     * do发货；自动发货
     * @param doHeader
     * @param autoParam 配置 是否自动发货 及 发货时间。<br/>配置自动发货：<br/>
     *    autoParam.put(LoadService.IS_AUTO, YesNo.YES.getValue());<br/>
     *    autoParam.put(LoadService.SHIP_TIME, WCS调用DTS执行自动发货的时间-Date类型);
     */
    public void invokeAutoDeliver(DeliveryOrderHeader doHeader, Map<String, Object> autoParam);
    
    /**
     * 增加失败执行次数
     * @param reShipDo
     */
    public  void addShipCount(ReShipDo reShipDo);
    
    /**
     * 发送邮件。
     * @param docNo
     * @param msgKey
     * @param ex
     */
    public  void sendNoticeMail(String docNo, String msgKey, Exception ex) ;
    
    /**
     * 查找需要发货的ReShipDo记录
     * @param failCeiling
     * @param maxCount
     * @param warehouseId 为null时表示全仓
     * @return
     */
    public List<ReShipDo> findToReShipDo(Integer failCeiling, Integer maxCount, Long warehouseId);

    void evictReShipDo(ReShipDo reShipDo);

    public LoadHeader autoLoad(CartonHeader ch, Integer loadMode, ReShipDo reShipDo, Date newOpTime)
            throws DeliveryException;
    
    /**
     * 人工交接的发货流程。
     * @param cartonHeader
     * @param loadType
     */
    public void manualDelivery(CartonHeader cartonHeader, String loadType);
    
    /**
     * 给TMS回写Do和箱信息。
     * @param
     * @param
     */
    public void sendCartonAndDOTogether2TMS(Long doId, String doNo, String currentCartonNo,boolean isFirstCarton);
    
    /**
     * 关闭交接单
     * @param loadHeader
     * @return
     */
    public LoadHeader completeLoad(LoadHeader loadHeader);
    
    public LoadHeader getLoadHeaderHis(Long loadHeaderId);
    
    /**
     * 发货时删除临时表中do下的所有记录
     * @param doId
     */
    public void removeReShipByDoId(Long doId);
    
    /**
     * 查询包裹是否已经交接
     * @param cartonHeaderId
     * @return
     */
    public Boolean isCartonLoaded(Long cartonHeaderId);
    
    /**
     * do单的类型必须与交接单的类型匹配
     * @param loadHeader
     * @param doHeader
     * @param cartonNo
     */
    public void checkDoType(LoadHeader loadHeader,DeliveryOrderHeader doHeader,String cartonNo);
    
    /**
     * 锁定装车单并发货
     * @param loadHeader
     */
    public void closeLoadOrder(LoadHeader loadHeader);
	
	/**
	 * 根据箱ID和装车单ID，删除装车记录
	 * @param cartonId
	 * @param loadHeaderId
	 */
	public void removeCartonFromLoadHeader(Long cartonId, Long loadHeaderId);

	/**
	 * 明细分页查询
	 * @param loadDetailFilter
	 * @param startIndex
	 * @param pageSize
	 * @return
	 */

	public DataPage<LoadDetail> queryDetail(LoadDetailFilter loadDetailFilter, int startIndex, int pageSize);

	/**
	 * 仓库发货-发运确认
	 * @param id
	 */
	public LoadHeader shipConfirm(Long loadHeaderId);

    /**
     * 合单发货，获取下个待扫描箱号
     * @param cartonHeader
     * @return
     */
    String getNextCartonNo4CombineShip(CartonHeader cartonHeader);

    void syncToPlatform(CartonHeader cartonHeader, DeliveryOrderHeader doHeader,
                               String loadType, Integer isForceDeliver);

    /**
     * 同步到平台
     * @param wayBill
     * @param doHeader
     * @throws Exception
     */
    void sync(String wayBill, DeliveryOrderHeader doHeader) throws Exception;
    
    List<LoadHeader> findUnFinished(int size);

    /**
     * 校验并查询装箱所需参数
     * @param loadId
     * @param cartonNo 箱号/发运单号/运单号/板号
     * @return
     */
    Triple<LoadHeader, List<DeliveryOrderHeader>, List<CartonHeader>> validateBeforeCartonLoad(Long loadId, String cartonNo);
    
    List<LoadDetail> getLastDetails(Long loadId, int size);
    
    void handleAfterLoaded(List<List<Object>> resultDto, LoadHeader loadHeader);

    List<String> findCanLoadDo(Long aLong, String doNo);

    String printLoadDo(Long id);

    /**
     * 批量设置交接单车牌号
     * @param ids 主键集合
     * @param vechileNo 车牌号
     */
    void batchSetVechileNo(List<Long> ids ,String vechileNo);
}
