package com.daxia.wms.delivery.task.replenish.dto;

import com.daxia.wms.delivery.task.replenish.dto.ReplenishSkuDTO;
import com.daxia.wms.master.dto.SkuDTO;
import com.daxia.wms.stock.stock.dto.Stock2AllocateDTO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@lombok.extern.slf4j.Slf4j
public class ReplGenterateDTO {
    private final List<Stock2AllocateDTO> stockList;
    private BigDecimal supplyMost;
    private final SkuDTO sku;
    private final Long defaultLocId;
    private final String replType;
    private final String doType;
    private final Boolean isAuto;
    private final int taskMadeCount;
    private final Boolean isPackage;
    private final Date earliestPlanShipTime;
    
    /**
     * @param stockList
     * @param supplyMost      补货数量，可能是包装数量，也可能是散件数量
     * @param sku
     * @param defaultLocId
     * @param replType
     */
    public ReplGenterateDTO(List<Stock2AllocateDTO> stockList, BigDecimal supplyMost, SkuDTO sku, Long defaultLocId, String replType, String doType, Boolean isAuto, int taskMadeCount, Boolean isPackage, Date earliestPlanShipTime) {
        this.stockList = stockList;
        this.supplyMost = supplyMost;
        this.sku = sku;
        this.defaultLocId = defaultLocId;
        this.replType = replType;
        this.doType = doType;
        this.isAuto = isAuto;
        this.taskMadeCount = taskMadeCount;
        this.isPackage = isPackage;
        this.earliestPlanShipTime = earliestPlanShipTime;
    }
    
    public Date getEarliestPlanShipTime() {
        return earliestPlanShipTime;
    }
    
    public Boolean getPackage() {
        return isPackage;
    }
    
    public List<Stock2AllocateDTO> getStockList() {
        return stockList;
    }
    
    public BigDecimal getSupplyMost() {
        return supplyMost;
    }
    
    public SkuDTO getSku() {
        return sku;
    }
    
    public Long getDefaultLocId() {
        return defaultLocId;
    }
    
    public String getReplType() {
        return replType;
    }
    
    public String getDoType() {
        return doType;
    }
    
    public Boolean getAuto() {
        return isAuto;
    }
    
    public int getTaskMadeCount() {
        return taskMadeCount;
    }
    
    public void setSupplyMost(BigDecimal supplyMost) {
        this.supplyMost = supplyMost;
    }
}
