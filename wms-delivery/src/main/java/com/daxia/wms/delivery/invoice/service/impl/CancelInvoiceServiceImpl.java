package com.daxia.wms.delivery.invoice.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.daxia.framework.common.util.ListUtil;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.jboss.seam.security.Identity;

import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.invoice.dao.InvoiceBookDAO;
import com.daxia.wms.delivery.invoice.dao.InvoiceDao;
import com.daxia.wms.delivery.invoice.dao.InvoiceNoDAO;
import com.daxia.wms.delivery.invoice.entity.InvoiceBook;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;
import com.daxia.wms.delivery.invoice.filter.InvoiceNoFilter;
import com.daxia.wms.delivery.invoice.service.CancelInvoiceService;
import com.daxia.wms.delivery.invoice.service.InvoiceBookService;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.InvoiceBookStatus;
import com.daxia.wms.Constants.InvoiceNoStatus;
import com.daxia.wms.exp.service.ExpFacadeService;

/**
 * 发票作废Service
 */
@Name("com.daxia.wms.delivery.cancelInvoiceService")
@lombok.extern.slf4j.Slf4j
public class CancelInvoiceServiceImpl implements CancelInvoiceService {
	@In
	private InvoiceNoDAO invoiceNoDAO;
	@In
	private InvoiceBookDAO invoiceBookDAO;
	@In
	private InvoiceDao invoiceDao;
	@In
	private DoHeaderDAO doHeaderDAO;
	@In
	private Identity identity;
	@In
	private ExpFacadeService expFacadeService;
	@In
	private InvoiceBookService invoiceBookService;

	/**
	 * 查询指定分页的制造商信息
	 * 
	 * @param invoiceNoFilter
	 * @param startIndex
	 * @param pageSize
	 * @return
	 */
	@Override
    public DataPage<InvoiceNo> findInvoiceNoByFilter(InvoiceNoFilter invoiceNoFilter, int startIndex, int pageSize) {
		return invoiceNoDAO.findRangeByFilter(invoiceNoFilter, startIndex, pageSize);
	}

	@Override
	@Transactional
	public void cancelSingleInvoice(Long invoiceNoId) {
		InvoiceNo no = invoiceNoDAO.get(invoiceNoId);
		if (no == null) {
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_CANCEL_INVOICEID_NOEXISTS, invoiceNoId); // 发票号ID{0}不存在
																													// error.invoice.cancel.invoiceIdNoExists
		}

		if (InvoiceNoStatus.INITIAL.getValue().equals(no.getStatus())
				|| InvoiceNoStatus.PRINT.getValue().equals(no.getStatus())) {// 0
																				// 未打印
																				// 1已打印
			this.commonCancelInvoice(no);
		} else {
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_CANCEL_MUST_BE_PRINT_OR_NOPRINT,
					no.getInvoiceNo()); // 发票号{0}必须是【未打印】或者【已打印】状态,请重新查询页面进行确认！
		}
	}

	@Override
	@Transactional
	public void cancelBatchInvoice(Long invoiceNoId) {
		InvoiceNo no = invoiceNoDAO.get(invoiceNoId);
		if (no == null) {
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_CANCEL_INVOICEID_NOEXISTS, invoiceNoId); // 发票号ID{0}不存在
																													// error.invoice.cancel.invoiceIdNoExists
		}

		if (InvoiceNoStatus.INITIAL.getValue().equals(no.getStatus())) {// 0 未打印
			this.commonCancelInvoice(no);
		} else {
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_CANCEL_MUST_BE_NOPRINT, no.getInvoiceNo()); // 发票号{0}必须是【未打印】状态,请重新查询页面进行确认！
		}
	}

	@Override
    public void cancelSingleInvoiceForExp(Long invoiceNoId) {
		InvoiceNo no = invoiceNoDAO.get(invoiceNoId);
		if (no == null) {
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_CANCEL_INVOICEID_NOEXISTS, invoiceNoId); // 发票号ID{0}不存在
																													// error.invoice.cancel.invoiceIdNoExists
		}

		if (InvoiceNoStatus.INITIAL.getValue().equals(no.getStatus())
				|| InvoiceNoStatus.PRINT.getValue().equals(no.getStatus())) {// 0
																				// 未打印
																				// 1已打印
			no.setInvoiceHeaderId(null);
			no.setInvoiceHeader(null);
			this.commonCancelInvoice(no);
		} else {
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_CANCEL_MUST_BE_PRINT_OR_NOPRINT,
					no.getInvoiceNo()); // 发票号{0}必须是【未打印】或者【已打印】状态,请重新查询页面进行确认！
		}
	}

	/**
	 * 单个和批量作废的公用代码
	 * 
	 * @param no
	 */
	private void commonCancelInvoice(InvoiceNo no) {
		DeliveryOrderHeader doheader = doHeaderDAO.findByOrderCode(no.getDoNo());
		if (doheader != null) {
			if (DoStatus.ALL_DELIVER.getValue().equals(doheader.getStatus())) {
				throw new DeliveryException(DeliveryException.ERROR_INVOICE_CANCEL_DOSTATUT_IS_DEPLOY,
						no.getInvoiceNo()); // 发票号{0}绑定的do单已出库
											// error.invoice.cancel.doStatutIsDeploy
			}
		}

		// 修改发票号状态为作废
		no.setStatus(InvoiceNoStatus.DES.getValue());
		no.setInvalidBy(identity.getCredentials().getUsername());
		no.setInvalidDate(new Date());
		invoiceNoDAO.update(no);
		// 修改发票薄状态为使用中
		InvoiceBook book = invoiceBookDAO.get(no.getInvoiceBookId());
		if (book == null) {
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_CANCEL_NO_BOOKINFORINVOICENO,
					no.getInvoiceNo()); // 发票号{0}没有对应的发票薄信息
										// error.invoice.cancel.noBookInForInvoiceNo
		}

		if (InvoiceBookStatus.INITIAL.getValue().equals(book.getStatus())) {
			// 发票薄状态：未使用

			// 改成使用中
			book.setStatus(InvoiceBookStatus.USING.getValue());
			invoiceBookDAO.update(book);
		}
		// 清空开票信息绑定的发票号码
		if (no.getInvoiceHeaderId() != null) {
			InvoiceHeader header = invoiceDao.get(no.getInvoiceHeaderId());
			if (header != null) {
				header.setInvoiceNumber(null);
				invoiceDao.update(header);
			}
		}
	}

	/**
	 * 根据doNo查询发票实体信息
	 * @param doNo
	 */
	@Override
	public List<InvoiceNo> getInvoiceNoByDoNo(String doNo) {
		return invoiceNoDAO.getInvoiceNoByDoNo(doNo);
	}

	@Override
	@Transactional
	public void save(InvoiceBook invoiceBook) {
//		Map<String, Object> parameters = new HashMap<String, Object>(2);
//		parameters.put("invoiceCode", invoiceBook.getInvoiceCode());
//		parameters.put("warehouseId", ParamUtil.getCurrentWarehouseId());
//		boolean isExist = invoiceNoDAO.isExists(parameters, null);
//		if (isExist) {
//			throw new DeliveryException(DeliveryException.INVOICECODE_NO_DUPLICATED);
//		}

		List<InvoiceNo> invoiceNoList = invoiceNoDAO.getInvoiceNoList(invoiceBook.getInvoiceCode(), invoiceBook.getInvoiceNoFrom(), invoiceBook.getInvoiceNoTo());
		if (ListUtil.isNotEmpty(invoiceNoList)) {
			throw new DeliveryException(DeliveryException.INVOICECODE_NO_DUPLICATED);
		}

		invoiceBook.setStatus(InvoiceBookStatus.INITIAL.getValue());
		invoiceBookService.save(invoiceBook);
		long from = Long.parseLong(invoiceBook.getInvoiceNoFrom());
		long to = Long.parseLong(invoiceBook.getInvoiceNoTo());

		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < invoiceBook.getInvoiceNoTo().length(); i++) {
			char str = invoiceBook.getInvoiceNoTo().charAt(i);
			if (str == '0') {
				sb.append("0");
			}else{
                break;
            }
		}
		for (; from <= to; from++) {
			InvoiceNo invoiceNo = new InvoiceNo();
			invoiceNo.setInvoiceBookId(invoiceBook.getId());
			invoiceNo.setInvoiceCode(invoiceBook.getInvoiceCode());
			invoiceNo.setStatus(InvoiceNoStatus.INITIAL.getValue());
			invoiceNo.setInvoiceNo(sb.toString() + from);
			invoiceNoDAO.save(invoiceNo);
		}
	}

	@Override
	@Transactional
	public void remove(Long invoiceNoId) {
		InvoiceNo no = invoiceNoDAO.get(invoiceNoId);
		if (null == no) {
			return;
		}
		List<InvoiceNo> invoiceList = new ArrayList<InvoiceNo>();
		invoiceList.add(no);
		InvoiceBook invoiceBook = invoiceDao.getInvoiceBookById(no.getInvoiceBookId());
		if (null != invoiceBook) {
			invoiceList.addAll(invoiceBook.getInvoiceNos());
			invoiceBookDAO.remove(invoiceBook);
		}
		for (InvoiceNo invoiceNo : invoiceList) {
			invoiceNoDAO.remove(invoiceNo);
		}
	}
	
	
}
