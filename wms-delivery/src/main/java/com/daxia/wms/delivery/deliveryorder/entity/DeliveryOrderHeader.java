package com.daxia.wms.delivery.deliveryorder.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.recheck.entity.CartonDetail;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.entity.TempCarton;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.master.entity.*;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.annotations.CascadeType;
import org.hibernate.annotations.OrderBy;
import org.hibernate.annotations.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * DO单头
 */
@Entity
@Table(name = "doc_do_header")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@BatchSize(size = 20)
@SQLDelete(sql = "update doc_do_header set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class DeliveryOrderHeader extends WhBaseEntity {

    private static final long serialVersionUID = 5652899835930211814L;

    // 主键
    private Long id;
    // 商品等级 DO明细ABC分类标识 Do明细全A类时,1 Do明细全B类时,2 Do明细全C类时,3 Do明细全D类时,4 Do明细以上混合时,5
    /**
     * 订单货品等级标识 A1 10, A2 11, A3 12, A1+A2 15,A1+A3 16, A2+A3 17, A1+A2+A3 18, B1 20, B1 21, C 22,
     * B1+B2 25, B1+C 26,B2+C 27, B1+B2+C 28  A+B/A+C  36
     */
    private Long cycleClass;
    // 发货单号
    private String doNo;

    // 发货单状态
    private String status;
    // 零散部分状态
    private String pcsStatus;
    // 整件部分状态
    private String unitStatus;

    // 发运时间
    private Date shipTime;

    // 预计出库时间
    private Date planShipTime;

    private Date planShipTimeEnd;

    // 发货单类型：DO、调拨、RTV
    private String doType;

    // 冻结状态 HOLD：冻结 RELEASE：释放
    private String releaseStatus;

    // 参考编号1(SO单号/调拨指令号)
    private String refNo1;

    // 参考编号2
    private String refNo2;

    // 邮编
    private String postCode;

    // 配送说明 半日达/一日三送/团购
    // 半日达普通/半日达大件/普通/普通大件/团购/一日三送/自提（约定数值分别是：1/2/3/4/5/6/7）
    private Long specFlag;

    // 有无发票
    private Long invoiceFlag;

    // 订货数量 EXPECTED_QTY_EACH
    private BigDecimal expectedQty;

    // 发货数量
    private BigDecimal shipQty;

    private BigDecimal packedQty;

    // 国家
    private String country;

    // 省份
    private Long province;

    // 城市
    private Long city;

    // 区
    private Long county;

    private String provinceName;

    private String cityName;

    private String countyName;

    // 收货方
    private String consigneeName;

    // 收货地址
    private String address;

    // 电话
    private String telephone;

    // 手机
    private String mobile;

    // 配送公司Id
    private Long carrierId;

    // 发票数量
    private Long invoiceQty;

    // 用户自定义属性1
    private String userDeffine1;

    // 用户自定义属性2
    private String userDeffine2;

    // 是否已跑波次
    private Integer waveFlag;
    // 是否称重
    private Integer weightFlag;

    // 用户自定义属性3
    private String userDeffine3;

    // 用户自定义属性3
    private String userDeffine4;

    // 期望到货时间1
    private Date expectedArriveTime1;

    // 期望到货时间2
    private Date expectedArriveTime2;

    // 备注
    private String notes;

    // 波次号
    private Long waveId;

    // 分拣格号
    private String sortGridNo;

    //分拣柜
    private Long sortingBinId;

    // 冻结人
    private String holdWho;

    // 冻结时间
    private Date holdTime;

    // 冻结原因代码
    private String holdCode;

    // 冻结原因
    private String holdReason;

    // 订单总额
    private BigDecimal orderAmount;

    // 货品总额
    private BigDecimal productAmount;

    // 已收款
    private BigDecimal amountPayable;

    // 总返利金额
    private BigDecimal orderFrostRebate;

    // 运费
    private BigDecimal orderDeliveryFee;

    // 应收款
    private BigDecimal receivable;

    // 毛重
    private BigDecimal grossWt;

    // 称重毛重
    private BigDecimal totalGrossWt;

    // 支付类型
    private Integer paymentType;

    // DO分配总量(所有明细分配数量之和 仅作显示用 不映射数据库字段)
    private BigDecimal totalAllocatEa;

    // DO创建时间
    private Date doCreateTime;

    private Integer recheckType;

    // DO单明细
    private List<DeliveryOrderDetail> doDetails;

    private List<CartonDetail> cartonDetails;

    private List<CartonHeader> cartonHeaders;


    private TempCarton tempCarton;

    private List<InvoiceHeader> invoiceHeaders;

    private WaveHeader waveHeader;

    // 配送商
    private Carrier carrier;

    private Province provinceInfo;

    private City cityInfo;

    private County countyInfo;

    // 配送站点
    private Station station;

    //是否需要取消，只有需要取消的订单才能取消
    private Boolean needCancel;

    // 普通/半日达/一日三送/准时送/指定日期（0/1/2/3/4）
    private Integer isHalfDayDelivery;

    // 原始单据ID
    private String origId;

    private String contact;

    // 付款方式
    private String paymentMethodName;

    // 是否忽略效期
    public Integer ignoreExpiryDate;

    private String expectedReceiveTime;

    private String edi1;

    //调拨目标仓库
    private String edi2;

    // 退换货标识
    private Integer exchangeFlag;

    // 拣货开始时间
    private Date pickStartTime;

    // 拣货结束时间
    private Date pickEndTime;

    // 装箱开始时间
    private Date packStartTime;

    // 装箱结束时间
    private Date packEndTime;

    // 分配时间
    private Date allocTime;

    // 分拣时间
    private Date sortTime;

    // 分拣结束时间
    private Date sortStartTime;

    private String sortedBy;

    private String packedBy;

    private Long stationId;

    //供应商
    private Long supplierId;

    // 补货状态
    private Integer replStatus;

    // 体积
    private BigDecimal volume;

    // 用户自定义字段5
    private String userDeffine5;

    // 父DO包含子DO数量
    private String userDeffine6;

    private String email;

    private String exceptionStatus;

    private List<DoLackDetail> doLackDetails;

    // 打印时是否显示金额，0：不显示，1：显示
    private Integer displayPrice;

    private List<PickTask> pickTasks;

    // 流程标记
    private Integer flowFlag;

    // 服务类型 0:普通，1：SBY,2：LBY
    private Integer serviceType;

    //销售商家的名字
    private String lastDcName;

    //调拨单类型
    private Integer tranType;

    //贵重品标记  null|0: 非贵重   1: 贵重
    private Integer isValuable;

    /**
     * 初始冻结原因代码
     */
    private String firstHoldCode;

    /**
     * 订单缺货状态：0 不缺货 1 缺货
     */
    private Integer lackStatus;

    //生鲜标记  
    private Integer isFresh;

    private SortingBin sortingBin;

    private Integer inWine; //包含酒类随附单
    /**
     * 酒类随附单号
     */
    private String wineAttachedNo;

    private String aisles;
    /**
     * 登记标识，1：合约机订单，2:实名制网卡订单 ，3：选号入网订单
     */
    private Integer checkFlag;

    /**
     * 组合单标记
     */
    private Integer emergencyFlag;

    private String sourceSystem;//来源系统

    private String departureTime;//预计发车时间

    /**
     * 团购订单标识(0：否；1：是)
     */
    private Integer isGroup;
    /**
     * 是否自动波次标识(0：否；1：是)
     */
    private Integer isAutoWave;

    /**
     * 配送属性（可以组合多个属性，用","分割），液体，易漏等
     */
    private String deliveryFeature;
    /**
     * 优惠金额
     **/
    private BigDecimal disCountAmount;
    //快递服务费用
    private BigDecimal deliveryServiceFee;

    //补货开始时间
    private Timestamp replStartTime;

    //补货结束时间
    private Timestamp replEndTime;
    /**
     * 是否有处方药(1-有,0-没有)
     **/
    private Integer haveCfy;
    /**
     * 站点名称
     */
    private String stationName;
    /*
     * 订单来源
     */
    private Integer orderSource;
    //店铺ID
    private Long shopId;
    //店铺
    private ShopInfo shopInfo;

    //订单原始编码（比如天猫平台的订单编号）
    private String originalSoCode;

    private String buyerRemark;//买家备注
    private String sellerRemark;//卖家备注
    private String platformRemark;//平台备注

    //0：小件 ，1 中件  2 大件 
    private Integer volumeType;

    //商户Id，toB业务
    private Long businessCustomerId;

    // 支付时间
    private Date payTime;

    // 称重时间
    private Date lastWeighTime;

    private BusinessCustomer businessCustomer;

    private Integer noStockFlg;

    /**
     * 订单审核状态，默认为1
     */
    private Integer orderCheckState;

    //订单子类型: 0-普通订单 1-O2O订单
    private String orderSubType;

    private DoWaveEx doWaveEx;

    private Integer isPicked;
    //是否直通出库（走数据）
    private Integer isDirect;

    private Integer autoFlag;

    /**
     * 商家ID
     */
    private Long merchantId;

    private Merchant merchant;

    /**
     * 是否越库:0 否，1 是
     */
    private Integer needCrossStock;

    private Long sourceAsnId;

    private Integer crossStockType;

    private Warehouse targetWarehouse;

    /**
     * 整件箱数
     */
    private Long cartonCountC;
    /**
     * 散件箱数
     */
    private Long cartonCountB;

    /**
     * 是否打印
     */
    private Integer printFlag;

    /**
     * 是否自动下单回单
     */
    private Integer isTempCarton;

    private Supplier supplier;

    //失败类型
    private Integer failedType;

    private Integer failedNumber;

    private Integer wholesaleWaybillFlag;
    private String parcelQuantity;

    /**
     * 缺发标记
     */
    private Integer lackShipFlag;
    /**
     * 运单号
     */
    private String trackingNo;
    /**
     * 运输温度
     */
    private Integer transportWendy;
    /**
     * 相似度明文
     */
    private String similarity;
    /**
     * 相似度密文
     */
    private String similaritySign;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 店铺编码
     */
    private String storeCode;
    @OneToOne(mappedBy = "doHeader",fetch = FetchType.LAZY)
    @Where(clause = " IS_DELETED = 0 ")
    public TempCarton getTempCarton() {
        return tempCarton;
    }

    public void setTempCarton(TempCarton tempCarton) {
        this.tempCarton = tempCarton;
    }

    @Column(name = "channel_code")
    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    @Column(name = "store_code")
    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    @Column(name = "similarity")
    public String getSimilarity() {
        return similarity;
    }

    public void setSimilarity(String similarity) {
        this.similarity = similarity;
    }
    @Column(name = "similarity_sign")
    public String getSimilaritySign() {
        return similaritySign;
    }

    public void setSimilaritySign(String similaritySign) {
        this.similaritySign = similaritySign;
    }

    @Column(name = "transport_wendy")
    public Integer getTransportWendy() {
        return transportWendy;
    }

    public void setTransportWendy(Integer transportWendy) {
        this.transportWendy = transportWendy;
    }
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "autoIdGenerator")
    @GenericGenerator(name = "autoIdGenerator", strategy = "com.daxia.framework.common.dao.AutoIdentityGenerator")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "DO_NO")
    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    @Column(name = "STATUS")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "SHIP_TIME")
    public Date getShipTime() {
        return shipTime;
    }

    public void setShipTime(Date shipTime) {
        this.shipTime = shipTime;
    }

    @Column(name = "PLAN_SHIP_TIME")
    public Date getPlanShipTime() {
        return planShipTime;
    }

    public void setPlanShipTime(Date planShipTime) {
        this.planShipTime = planShipTime;
    }

    @Column(name = "PLAN_SHIP_TIME_END")
    public Date getPlanShipTimeEnd() {
        return planShipTimeEnd;
    }

    public void setPlanShipTimeEnd(Date planShipTimeEnd) {
        this.planShipTimeEnd = planShipTimeEnd;
    }

    @Column(name = "DO_TYPE")
    public String getDoType() {
        return doType;
    }

    public void setDoType(String doType) {
        this.doType = doType;
    }

    @Column(name = "RELEASE_STATUS")
    public String getReleaseStatus() {
        return releaseStatus;
    }

    public void setReleaseStatus(String releaseStatus) {
        this.releaseStatus = releaseStatus;
    }

    @Column(name = "REF_NO1")
    public String getRefNo1() {
        return refNo1;
    }

    public void setRefNo1(String refNo1) {
        this.refNo1 = refNo1;
    }

    @Column(name = "REF_NO2")
    public String getRefNo2() {
        return refNo2;
    }

    public void setRefNo2(String refNo2) {
        this.refNo2 = refNo2;
    }

    @Column(name = "POST_CODE")
    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    @Column(name = "SPEC_FLAG")
    public Long getSpecFlag() {
        return specFlag;
    }

    public void setSpecFlag(Long specFlag) {
        this.specFlag = specFlag;
    }

    @Column(name = "INVOICE_FLAG")
    public Long getInvoiceFlag() {
        return invoiceFlag;
    }

    public void setInvoiceFlag(Long invoiceFlag) {
        this.invoiceFlag = invoiceFlag;
    }

    @Column(name = "EXPECTED_QTY")
    public BigDecimal getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(BigDecimal expectedQty) {
        this.expectedQty = expectedQty;
    }

    @Column(name = "SHIP_QTY")
    public BigDecimal getShipQty() {
        return shipQty;
    }

    public void setShipQty(BigDecimal shipQty) {
        this.shipQty = shipQty;
    }

    @Column(name = "PACKED_QTY")
    public BigDecimal getPackedQty() {
        return packedQty;
    }

    public void setPackedQty(BigDecimal packedQty) {
        this.packedQty = packedQty;
    }

    @Column(name = "COUNTRY")
    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    @Column(name = "PROVINCE")
    public Long getProvince() {
        return province;
    }

    public void setProvince(Long province) {
        this.province = province;
    }

    @Column(name = "CITY")
    public Long getCity() {
        return city;
    }

    public void setCity(Long city) {
        this.city = city;
    }

    @Column(name = "COUNTY")
    public Long getCounty() {
        return county;
    }

    public void setCounty(Long county) {
        this.county = county;
    }

    @Column(name = "province_name")
    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    @Column(name = "city_name")
    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    @Column(name = "county_name")
    public String getCountyName() {
        return countyName;
    }

    public void setCountyName(String countyName) {
        this.countyName = countyName;
    }

    @Column(name = "CONSIGNEE_NAME")
    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    @Column(name = "ADDRESS")
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Column(name = "TELEPHONE")
    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    @Column(name = "MOBILE")
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    @Column(name = "CARRIER_ID")
    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    @Column(name = "INVOICE_QTY")
    public Long getInvoiceQty() {
        return invoiceQty;
    }

    public void setInvoiceQty(Long invoiceQty) {
        this.invoiceQty = invoiceQty;
    }

    @Column(name = "USERDEFINE1")
    public String getUserDeffine1() {
        return userDeffine1;
    }

    public void setUserDeffine1(String userDeffine1) {
        this.userDeffine1 = userDeffine1;
    }

    @Column(name = "USERDEFINE2")
    public String getUserDeffine2() {
        return userDeffine2;
    }

    public void setUserDeffine2(String userDeffine2) {
        this.userDeffine2 = userDeffine2;
    }

    @Column(name = "USERDEFINE3")
    public String getUserDeffine3() {
        return userDeffine3;
    }

    public void setUserDeffine3(String userDeffine3) {
        this.userDeffine3 = userDeffine3;
    }

    @Column(name = "USERDEFINE4")
    public String getUserDeffine4() {
        return userDeffine4;
    }

    public void setUserDeffine4(String userDeffine4) {
        this.userDeffine4 = userDeffine4;
    }


    @OneToMany(fetch = FetchType.LAZY, mappedBy = "doHeader")
    @Where(clause = " IS_DELETED = 0 ")
    @OrderBy(clause = "id")
    @Cascade(value = {CascadeType.SAVE_UPDATE})
    public List<DeliveryOrderDetail> getDoDetails() {
        return doDetails;
    }

    public void setDoDetails(List<DeliveryOrderDetail> doDetails) {
        this.doDetails = doDetails;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "deliveryOrderHeader")
    @Where(clause = " IS_DELETED = 0 ")
    @Cascade(value = {CascadeType.SAVE_UPDATE})
    public List<InvoiceHeader> getInvoiceHeaders() {
        return invoiceHeaders;
    }

    public void setInvoiceHeaders(List<InvoiceHeader> invoiceHeaders) {
        this.invoiceHeaders = invoiceHeaders;
    }

    @Column(name = "EXPECTED_ARRIVE_TIME1")
    public Date getExpectedArriveTime1() {
        return expectedArriveTime1;
    }

    public void setExpectedArriveTime1(Date expectedArriveTime1) {
        this.expectedArriveTime1 = expectedArriveTime1;
    }

    @Column(name = "EXPECTED_ARRIVE_TIME2")
    public Date getExpectedArriveTime2() {
        return expectedArriveTime2;
    }

    public void setExpectedArriveTime2(Date expectedArriveTime2) {
        this.expectedArriveTime2 = expectedArriveTime2;
    }

    @Column(name = "WAVE_FLAG")
    public Integer getWaveFlag() {
        return waveFlag;
    }

    public void setWaveFlag(Integer waveFlag) {
        this.waveFlag = waveFlag;
    }
    @Column(name = "weight_flag")
    public Integer getWeightFlag() {
        return weightFlag;
    }

    public void setWeightFlag(Integer weightFlag) {
        this.weightFlag = weightFlag;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "doHeader")
    @Where(clause = " IS_DELETED = 0 ")
    public List<CartonDetail> getCartonDetails() {
        return cartonDetails;
    }

    public void setCartonDetails(List<CartonDetail> cartonDetails) {
        this.cartonDetails = cartonDetails;
    }

    public void setCarrier(Carrier carrier) {
        this.carrier = carrier;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "WAVE_ID", insertable = false, updatable = false)
    public WaveHeader getWaveHeader() {
        return waveHeader;
    }

    public void setWaveHeader(WaveHeader waveHeader) {
        this.waveHeader = waveHeader;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CARRIER_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    public Carrier getCarrier() {
        return carrier;
    }

    @Column(name = "NOTES")
    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    @Column(name = "WAVE_ID")
    public Long getWaveId() {
        return waveId;
    }

    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PROVINCE", insertable = false, updatable = false)
    public Province getProvinceInfo() {
        return provinceInfo;
    }

    public void setProvinceInfo(Province provinceInfo) {
        this.provinceInfo = provinceInfo;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CITY", insertable = false, updatable = false)
    public City getCityInfo() {
        return cityInfo;
    }

    public void setCityInfo(City cityInfo) {
        this.cityInfo = cityInfo;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "COUNTY", insertable = false, updatable = false)
    public County getCountyInfo() {
        return countyInfo;
    }

    public void setCountyInfo(County countyInfo) {
        this.countyInfo = countyInfo;
    }

    @Column(name = "SORT_GRID_NO")
    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    @Column(name = "SORTING_BIN_ID")
    public Long getSortingBinId() {
        return sortingBinId;
    }

    public void setSortingBinId(Long sortingBinId) {
        this.sortingBinId = sortingBinId;
    }

    @Column(name = "HOLD_WHO")
    public String getHoldWho() {
        return holdWho;
    }

    public void setHoldWho(String holdWho) {
        this.holdWho = holdWho;
    }

    @Column(name = "HOLD_TIME")
    public Date getHoldTime() {
        return holdTime;
    }

    public void setHoldTime(Date holdTime) {
        this.holdTime = holdTime;
    }

    @Column(name = "HOLD_CODE")
    public String getHoldCode() {
        return holdCode;
    }

    public void setHoldCode(String holdCode) {
        this.holdCode = holdCode;
    }

    @Column(name = "HOLD_REASON")
    public String getHoldReason() {
        return holdReason;
    }

    public void setHoldReason(String holdReason) {
        this.holdReason = holdReason;
    }

    @Column(name = "ORDER_AMOUNT")
    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    @Column(name = "PRODUCT_AMOUNT")
    public BigDecimal getProductAmount() {
        return productAmount;
    }

    public void setProductAmount(BigDecimal productAmount) {
        this.productAmount = productAmount;
    }

    @Column(name = "ACCOUNT_PAYABLE")
    public BigDecimal getAmountPayable() {
        return amountPayable;
    }

    public void setAmountPayable(BigDecimal amountPayable) {
        this.amountPayable = amountPayable;
    }

    @Column(name = "ORDER_FROST_REBATE")
    public BigDecimal getOrderFrostRebate() {
        return orderFrostRebate;
    }

    public void setOrderFrostRebate(BigDecimal orderFrostRebate) {
        this.orderFrostRebate = orderFrostRebate;
    }

    @Column(name = "ORDER_DELIVERY_FEE")
    public BigDecimal getOrderDeliveryFee() {
        return orderDeliveryFee;
    }

    public void setOrderDeliveryFee(BigDecimal orderDeliveryFee) {
        this.orderDeliveryFee = orderDeliveryFee;
    }

    @Column(name = "GROSS_WT")
    public BigDecimal getGrossWt() {
        return grossWt;
    }

    public void setGrossWt(BigDecimal grossWt) {
        this.grossWt = grossWt;
    }

    public void setCartonHeaders(List<CartonHeader> cartonHeaders) {
        this.cartonHeaders = cartonHeaders;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "doHeader")
    @Where(clause = " IS_DELETED = 0 ")
    public List<CartonHeader> getCartonHeaders() {
        return cartonHeaders;
    }

    @Transient
    public BigDecimal getTotalAllocatEa() {
        return totalAllocatEa;
    }

    public void setTotalAllocatEa(BigDecimal totalAllocatEa) {
        this.totalAllocatEa = totalAllocatEa;
    }

    @Column(name = "NEED_CANCEL")
    public Boolean getNeedCancel() {
        return needCancel;
    }

    public void setNeedCancel(Boolean needCancel) {
        this.needCancel = needCancel;
    }

    @Transient
    public boolean isCod() {
        return this.receivable != null && this.receivable.compareTo(BigDecimal.ZERO) > 0;
    }

    @Column(name = "RECEIVABLE")
    public BigDecimal getReceivable() {
        return receivable;
    }

    public void setReceivable(BigDecimal receivable) {
        this.receivable = receivable;
    }

    public void setIsHalfDayDelivery(Integer isHalfDayDelivery) {
        this.isHalfDayDelivery = isHalfDayDelivery;
    }

    @Column(name = "IS_HALF_DAY_DELIVERY")
    public Integer getIsHalfDayDelivery() {
        return isHalfDayDelivery;
    }

    /**
     * @return the origId
     */
    @Column(name = "ORIG_ID")
    public String getOrigId() {
        return origId;
    }

    /**
     * @param origId the origId to set
     */
    public void setOrigId(String origId) {
        this.origId = origId;
    }

    @Column(name = "CONTACT")
    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    @Column(name = "ignore_expiry_date")
    public Integer getIgnoreExpiryDate() {
        return ignoreExpiryDate;
    }

    public void setIgnoreExpiryDate(Integer ignoreExpiryDate) {
        this.ignoreExpiryDate = ignoreExpiryDate;
    }

    @Column(name = "EXPECTED_RECEIVE_TIME")
    public String getExpectedReceiveTime() {
        return expectedReceiveTime;
    }

    public void setExpectedReceiveTime(String expectedReceiveTime) {
        this.expectedReceiveTime = expectedReceiveTime;
    }

    @Column(name = "EDI_1")
    public String getEdi1() {
        return edi1;
    }

    public void setEdi1(String edi1) {
        this.edi1 = edi1;
    }

    @Column(name = "EDI_2")
    public String getEdi2() {
        return edi2;
    }

    public void setEdi2(String edi2) {
        this.edi2 = edi2;
    }

    @Column(name = "PAYMENT_TYPE")
    public Integer getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    @Column(name = "PAYMENT_METHOD_NAME")
    public String getPaymentMethodName() {
        return paymentMethodName;
    }

    public void setPaymentMethodName(String paymentMethodName) {
        this.paymentMethodName = paymentMethodName;
    }

    @Column(name = "DO_CREATE_TIME ")
    public Date getDoCreateTime() {
        return doCreateTime;
    }

    public void setDoCreateTime(Date doCreateTime) {
        this.doCreateTime = doCreateTime;
    }

    @Column(name = "RECHECK_TYPE")
    public Integer getRecheckType() {
        return recheckType;
    }


    public void setRecheckType(Integer recheckType) {
        this.recheckType = recheckType;
    }

    @Column(name = "EXCHANGE_FLAG")
    public Integer getExchangeFlag() {
        return exchangeFlag;
    }

    public void setExchangeFlag(Integer exchangeFlag) {
        this.exchangeFlag = exchangeFlag;
    }

    @Column(name = "PK_TIME_START")
    public Date getPickStartTime() {
        return pickStartTime;
    }

    public void setPickStartTime(Date pickStartTime) {
        this.pickStartTime = pickStartTime;
    }

    @Column(name = "PK_TIME_END")
    public Date getPickEndTime() {
        return pickEndTime;
    }

    public void setPickEndTime(Date pickEndTime) {
        this.pickEndTime = pickEndTime;
    }

    @Column(name = "pack_time_start")
    public Date getPackStartTime() {
        return packStartTime;
    }

    public void setPackStartTime(Date packStartTime) {
        this.packStartTime = packStartTime;
    }

    @Column(name = "pack_time_end")
    public Date getPackEndTime() {
        return packEndTime;
    }

    public void setPackEndTime(Date packEndTime) {
        this.packEndTime = packEndTime;
    }

    @Column(name = "ALLOC_TIME")
    public Date getAllocTime() {
        return allocTime;
    }

    public void setAllocTime(Date allocTime) {
        this.allocTime = allocTime;
    }

    @Column(name = "SORT_TIME")
    public Date getSortTime() {
        return sortTime;
    }

    public void setSortTime(Date sortTime) {
        this.sortTime = sortTime;
    }

    @Column(name = "STATION_ID")
    public Long getStationId() {
        return stationId;
    }

    public void setStationId(Long stationId) {
        this.stationId = stationId;
    }

    @Column(name = "SUPPLIER_ID")
    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public void setStation(Station station) {
        this.station = station;
    }

    @Column(name = "LACK_STATUS")
    public Integer getLackStatus() {
        return lackStatus;
    }

    public void setLackStatus(Integer lackStatus) {
        this.lackStatus = lackStatus;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "STATION_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    public Station getStation() {
        return station;
    }

    /**
     * @return 补货状态
     * @see com.daxia.wms.Constants.DoReplStatus
     */
    @Column(name = "REPL_STATUS")
    public Integer getReplStatus() {
        return replStatus;
    }

    public void setReplStatus(Integer replStatus) {
        this.replStatus = replStatus;
    }

    @Column(name = "TOTAL_GROSS_WT")
    public BigDecimal getTotalGrossWt() {
        return totalGrossWt;
    }

    public void setTotalGrossWt(BigDecimal totalGrossWt) {
        this.totalGrossWt = totalGrossWt;
    }

    @Column(name = "VOLUME")
    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    @Column(name = "USERDEFINE5")
    public String getUserDeffine5() {
        return userDeffine5;
    }

    public void setUserDeffine5(String userDeffine5) {
        this.userDeffine5 = userDeffine5;
    }

    @Column(name = "USERDEFINE6")
    public String getUserDeffine6() {
        return userDeffine6;
    }

    public void setUserDeffine6(String userDeffine6) {
        this.userDeffine6 = userDeffine6;
    }

    @Column(name = "EMAIL")
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Column(name = "EXCEPTION_STATUS")
    public String getExceptionStatus() {
        return exceptionStatus;
    }

    public void setExceptionStatus(String exceptionStatus) {
        this.exceptionStatus = exceptionStatus;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "doHeader")
    @Where(clause = " IS_DELETED = 0 ")
    public List<DoLackDetail> getDoLackDetails() {
        return doLackDetails;
    }

    public void setDoLackDetails(List<DoLackDetail> doLackDetails) {
        this.doLackDetails = doLackDetails;
    }

    @Column(name = "DISPLAY_PRICE")
    public Integer getDisplayPrice() {
        return displayPrice;
    }

    public void setDisplayPrice(Integer displayPrice) {
        this.displayPrice = displayPrice;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "doHeader")
    @Where(clause = " IS_DELETED = 0 ")
    public List<PickTask> getPickTasks() {
        return pickTasks;
    }

    public void setPickTasks(List<PickTask> pickTasks) {
        this.pickTasks = pickTasks;
    }

    @Column(name = "FLOW_FLAG")
    public Integer getFlowFlag() {
        return flowFlag;
    }

    public void setFlowFlag(Integer flowFlag) {
        this.flowFlag = flowFlag;
    }

    @Column(name = "SERVICE_TYPE")
    public Integer getServiceType() {
        return serviceType;
    }

    public void setServiceType(Integer serviceType) {
        this.serviceType = serviceType;
    }

    @Column(name = "LAST_DC_NAME")
    public String getLastDcName() {
        return lastDcName;
    }

    public void setLastDcName(String lastDcName) {
        this.lastDcName = lastDcName;
    }

    @Column(name = "TRAN_TYPE")
    public Integer getTranType() {
        return tranType;
    }

    public void setTranType(Integer tranType) {
        this.tranType = tranType;
    }

    @Column(name = "IS_VALUABLE")
    public Integer getIsValuable() {
        return isValuable;
    }

    public void setIsValuable(Integer isValuable) {
        this.isValuable = isValuable;
    }

    @Column(name = "FIRST_HOLD_CODE")
    public String getFirstHoldCode() {
        return firstHoldCode;
    }

    public void setFirstHoldCode(String firstHoldCode) {
        this.firstHoldCode = firstHoldCode;
    }

    @Column(name = "IS_FRESH")
    public Integer getIsFresh() {
        return isFresh;
    }

    public void setIsFresh(Integer isFresh) {
        this.isFresh = isFresh;
    }

    public void setSortingBin(SortingBin sortingBin) {
        this.sortingBin = sortingBin;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SORTING_BIN_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    public SortingBin getSortingBin() {
        return sortingBin;
    }

    @Column(name = "IN_WINE")
    public Integer getInWine() {
        return inWine;
    }

    public void setInWine(Integer inWine) {
        this.inWine = inWine;
    }

    @Column(name = "WINE_NO")
    public String getWineAttachedNo() {
        return wineAttachedNo;
    }

    public void setWineAttachedNo(String wineAttachedNo) {
        this.wineAttachedNo = wineAttachedNo;
    }

    @Column(name = "AISLES")
    public String getAisles() {
        return aisles;
    }

    public void setAisles(String aisles) {
        this.aisles = aisles;
    }

    @Column(name = "CHECK_FLAG")
    public Integer getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(Integer checkFlag) {
        this.checkFlag = checkFlag;
    }

    @Column(name = "SOURCE_SYSTEM")
    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    @Column(name = "SORT_START_TIME")
    public Date getSortStartTime() {
        return sortStartTime;
    }

    public void setSortStartTime(Date sortStartTime) {
        this.sortStartTime = sortStartTime;
    }

    @Column(name = "SORTED_BY")
    public String getSortedBy() {
        return sortedBy;
    }

    public void setSortedBy(String sortedBy) {
        this.sortedBy = sortedBy;
    }

    @Column(name = "PACKED_BY")
    public String getPackedBy() {
        return packedBy;
    }

    public void setPackedBy(String packedBy) {
        this.packedBy = packedBy;
    }

    /**
     * @return the departureTime
     */
    @Column(name = "DEPARTURE_TIME")
    public String getDepartureTime() {
        return departureTime;
    }

    /**
     * @param departureTime the departureTime to set
     */
    public void setDepartureTime(String departureTime) {
        this.departureTime = departureTime;
    }

    @Column(name = "IS_GROUP")
    public Integer getIsGroup() {
        return isGroup;
    }

    public void setIsGroup(Integer isGroup) {
        this.isGroup = isGroup;
    }

    @Column(name = "IS_AUTO_WAVE")
    public Integer getIsAutoWave() {
        return isAutoWave;
    }

    public void setIsAutoWave(Integer isAutoWave) {
        this.isAutoWave = isAutoWave;
    }

    @Column(name = "DELEVERY_FEATURE")
    public String getDeliveryFeature() {
        return deliveryFeature;
    }

    public void setDeliveryFeature(String deliveryFeature) {
        this.deliveryFeature = deliveryFeature;
    }

    /**
     * @return the disCountAmount
     */
    @Column(name = "DISCOUNT_AMOUNT")
    public BigDecimal getDisCountAmount() {
        return disCountAmount;
    }

    /**
     * @param disCountAmount the disCountAmount to set
     */
    public void setDisCountAmount(BigDecimal disCountAmount) {
        this.disCountAmount = disCountAmount;
    }

    @Column(name = "REPL_START_TIME")
    public Timestamp getReplStartTime() {
        return replStartTime;
    }

    public void setReplStartTime(Timestamp replStartTime) {
        this.replStartTime = replStartTime;
    }

    @Column(name = "REPL_END_TIME")
    public Timestamp getReplEndTime() {
        return replEndTime;
    }

    public void setReplEndTime(Timestamp replEndTime) {
        this.replEndTime = replEndTime;
    }

    @Column(name = "HAVE_CFY")
    public Integer getHaveCfy() {
        return haveCfy;
    }

    public void setHaveCfy(Integer haveCfy) {
        this.haveCfy = haveCfy;
    }

    @Column(name = "STATION_NAME")
    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    @Column(name = "order_source")
    public Integer getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(Integer orderSource) {
        this.orderSource = orderSource;
    }

    @Column(name = "shop_id")
    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    @Column(name = "original_so_code")
    public String getOriginalSoCode() {
        return originalSoCode;
    }

    public void setOriginalSoCode(String originalSoCode) {
        this.originalSoCode = originalSoCode;
    }

    @Column(name = "buyer_remark")
    public String getBuyerRemark() {
        return buyerRemark;
    }

    public void setBuyerRemark(String buyerRemark) {
        this.buyerRemark = buyerRemark;
    }

    @Column(name = "seller_remark")
    public String getSellerRemark() {
        return sellerRemark;
    }

    public void setSellerRemark(String sellerRemark) {
        this.sellerRemark = sellerRemark;
    }

    @Column(name = "platform_remark")
    public String getPlatformRemark() {
        return platformRemark;
    }

    public void setPlatformRemark(String platformRemark) {
        this.platformRemark = platformRemark;
    }

    @Column(name = "volume_type")
    public Integer getVolumeType() {
        return volumeType;
    }

    public void setVolumeType(Integer volumeType) {
        this.volumeType = volumeType;
    }

    @Column(name = "business_customer_id")
    public Long getBusinessCustomerId() {
        return businessCustomerId;
    }

    public void setBusinessCustomerId(Long businessCustomerId) {
        this.businessCustomerId = businessCustomerId;
    }

    @Column(name = "pay_time")
    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "business_customer_id", referencedColumnName = "id", insertable = false, updatable = false)
    public BusinessCustomer getBusinessCustomer() {
        return businessCustomer;
    }

    public void setBusinessCustomer(BusinessCustomer businessCustomer) {
        this.businessCustomer = businessCustomer;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "shop_id", referencedColumnName = "id", insertable = false, updatable = false)
    public ShopInfo getShopInfo() {
        return shopInfo;
    }

    public void setShopInfo(ShopInfo shopInfo) {
        this.shopInfo = shopInfo;
    }

    @Column(name = "last_weigh_time")
    public Date getLastWeighTime() {
        return lastWeighTime;
    }

    public void setLastWeighTime(Date lastWeighTime) {
        this.lastWeighTime = lastWeighTime;
    }

    @Column(name = "order_check_state")
    public Integer getOrderCheckState() {
        return orderCheckState;
    }

    public void setOrderCheckState(Integer orderCheckState) {
        this.orderCheckState = orderCheckState;
    }

    @Transient
    public Integer getNoStockFlg() {
        return noStockFlg;
    }

    public void setNoStockFlg(Integer noStockFlg) {
        this.noStockFlg = noStockFlg;
    }

    @Column(name = "order_sub_type")
    public String getOrderSubType() {
        return orderSubType;
    }

    public void setOrderSubType(String orderSubType) {
        this.orderSubType = orderSubType;
    }

    @OneToOne(mappedBy = "deliveryOrderHeader",fetch = FetchType.LAZY)
    @Where(clause = " IS_DELETED = 0 ")
    public DoWaveEx getDoWaveEx() {
        return doWaveEx;
    }

    public void setDoWaveEx(DoWaveEx doWaveEx) {
        this.doWaveEx = doWaveEx;
    }

    @Transient
    public Integer getIsPicked() {
        return isPicked;
    }

    public void setIsPicked(Integer isPicked) {
        this.isPicked = isPicked;
    }

    @Column(name = "delivery_service_fee")
    public BigDecimal getDeliveryServiceFee() {
        return deliveryServiceFee;
    }

    public void setDeliveryServiceFee(BigDecimal deliveryServiceFee) {
        this.deliveryServiceFee = deliveryServiceFee;
    }


    @Column(name = "is_direct")
    public Integer getIsDirect() {
        return isDirect;
    }

    public void setIsDirect(Integer isDirect) {
        this.isDirect = isDirect;
    }

    @Column(name = "MERCHANT_ID")
    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "MERCHANT_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    public Merchant getMerchant() {
        return merchant;
    }

    public void setMerchant(Merchant merchant) {
        this.merchant = merchant;
    }

    @Column(name = "pcs_status")
    public String getPcsStatus() {
        return pcsStatus;
    }

    public void setPcsStatus(String pcsStatus) {
        this.pcsStatus = pcsStatus;
    }

    @Column(name = "unit_status")
    public String getUnitStatus() {
        return unitStatus;
    }

    public void setUnitStatus(String unitStatus) {
        this.unitStatus = unitStatus;
    }

    @Column(name = "auto_flag")
    public Integer getAutoFlag() {
        return autoFlag;
    }

    public void setAutoFlag(Integer autoFlag) {
        this.autoFlag = autoFlag;
    }
    @Column(name = "tracking_no")
    public String getTrackingNo() {
        return trackingNo;
    }

    public void setTrackingNo(String trackingNo) {
        this.trackingNo = trackingNo;
    }

    @Column(name = "NEED_CROSSSTOCK")
    public Integer getNeedCrossStock() {
        return needCrossStock;
    }

    public void setNeedCrossStock(Integer needCrossStock) {
        this.needCrossStock = needCrossStock;
    }

    @Column(name = "source_asn_id")
    public Long getSourceAsnId() {
        return sourceAsnId;
    }

    public void setSourceAsnId(Long sourceAsnId) {
        this.sourceAsnId = sourceAsnId;
    }

    @Column(name = "EMERGENCY_FLAG")
    public Integer getEmergencyFlag() {
        return emergencyFlag;
    }

    public void setEmergencyFlag(Integer emergencyFlag) {
        this.emergencyFlag = emergencyFlag;
    }

    @Column(name = "PRINT_FLAG")
    public Integer getPrintFlag() {
        return printFlag;
    }

    public void setPrintFlag(Integer printFlag) {
        this.printFlag = printFlag;
    }

    @Column(name = "is_temp_carton")
    public Integer getIsTempCarton() {
        return isTempCarton;
    }

    public void setIsTempCarton(Integer isTempCarton) {
        this.isTempCarton = isTempCarton;
    }


    @Column(name = "failed_type")
    public Integer getFailedType() {
        return failedType;
    }

    public void setFailedType(Integer failedType) {
        this.failedType = failedType;
    }

    @Column(name = "failed_number")
    public Integer getFailedNumber() {
        return failedNumber;
    }

    public void setFailedNumber(Integer failedNumber) {
        this.failedNumber = failedNumber;
    }

    @Column(name = "LACK_SHIP_FLAG")
    public Integer getLackShipFlag() {
        return lackShipFlag;
    }

    public void setLackShipFlag(Integer lackShipFlag) {
        this.lackShipFlag = lackShipFlag;
    }

    @Transient
    public Integer getCrossStockType() {
        if (Constants.DoType.ALLOT.getValue().equals(doType)) {
            return Constants.CrossDockType.WAREHOUSE.getValue();
        } else {
            return Constants.CrossDockType.CUSTOMER.getValue();
        }
    }


    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "EDI_2", referencedColumnName = "ID", insertable = false, updatable = false)
    public Warehouse getTargetWarehouse() {
        return targetWarehouse;
    }

    public void setTargetWarehouse(Warehouse targetWarehouse) {
        this.targetWarehouse = targetWarehouse;
    }

    @Transient
    public Long getCartonCountC() {
        Long count = 0L;
        if (CollectionUtils.isEmpty(this.getCartonHeaders())) {
            return count;
        }
        for (CartonHeader c : this.getCartonHeaders()) {
            if (StringUtil.equals(c.getExt1(), Constants.PackageType.C.getValue())) {
                count++;
            }
        }
        return count;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "supplier_id", referencedColumnName = "id", insertable = false, updatable = false)
    public Supplier getSupplier() {
        return supplier;
    }

    public void setSupplier(Supplier supplier) {
        this.supplier = supplier;
    }

    @Transient
    public Long getCartonCountB() {
        Long count = 0L;
        if (CollectionUtils.isEmpty(this.getCartonHeaders())) {
            return count;
        }
        for (CartonHeader c : this.getCartonHeaders()) {
            if (StringUtil.equals(c.getExt1(), Constants.PackageType.B.getValue())) {
                count++;
            }
        }
        return count;
    }

    @Transient
    public Integer getWholesaleWaybillFlag() {
        return wholesaleWaybillFlag;
    }

    public void setWholesaleWaybillFlag(Integer wholesaleWaybillFlag) {
        this.wholesaleWaybillFlag = wholesaleWaybillFlag;
    }

    @Transient
    public String getParcelQuantity() {
        return parcelQuantity;
    }

    public void setParcelQuantity(String parcelQuantity) {
        this.parcelQuantity = parcelQuantity;
    }
    public void setCycleClass(Long cycleClass) {
        this.cycleClass = cycleClass;
    }

    @Column(name="CYCLE_CLASS")
    public Long getCycleClass() {
        return cycleClass;
    }

    @Transient
    public String getUserDeffine5Formatted() {
        if (userDeffine5 != null) {
            return userDeffine5.replaceFirst("^0+(?!$)", "");
        }
        return "";
    }
}