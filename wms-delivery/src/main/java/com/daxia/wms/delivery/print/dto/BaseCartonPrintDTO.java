package com.daxia.wms.delivery.print.dto;

import com.daxia.wms.delivery.recheck.entity.CartonDetail;
import com.daxia.wms.print.dto.PrintReportDto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 箱打印基础DTO，兼容carton和tempCarton
 */
@lombok.extern.slf4j.Slf4j
public class BaseCartonPrintDTO{

    private Long id;

    private String cartonNo;

    private String wayBill;

    private String printData;

    private Integer isPrinted;

    /**
     * 实测重量
     */
    private BigDecimal actualGrossWeight;

    private String packageType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCartonNo() {
        return cartonNo;
    }

    public void setCartonNo(String cartonNo) {
        this.cartonNo = cartonNo;
    }

    public String getPrintData() {
        return printData;
    }

    public void setPrintData(String printData) {
        this.printData = printData;
    }

    public BigDecimal getActualGrossWeight() {
        return actualGrossWeight;
    }

    public void setActualGrossWeight(BigDecimal actualGrossWeight) {
        this.actualGrossWeight = actualGrossWeight;
    }

    public String getWayBill() {
        return wayBill;
    }

    public void setWayBill(String wayBill) {
        this.wayBill = wayBill;
    }

    public Integer getIsPrinted() {
        return isPrinted;
    }

    public void setIsPrinted(Integer isPrinted) {
        this.isPrinted = isPrinted;
    }

    public String getPackageType() {
        return packageType;
    }

    public void setPackageType(String packageType) {
        this.packageType = packageType;
    }
}