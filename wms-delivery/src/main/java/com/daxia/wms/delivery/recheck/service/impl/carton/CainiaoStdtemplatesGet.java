package com.daxia.wms.delivery.recheck.service.impl.carton;

import com.taobao.api.ApiException;
import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.request.CainiaoCloudprintStdtemplatesGetRequest;
import com.taobao.api.response.CainiaoCloudprintStdtemplatesGetResponse;

@lombok.extern.slf4j.Slf4j
public class CainiaoStdtemplatesGet {
    
    public static void main(String[] args){
        TaobaoClient client = new DefaultTaobaoClient("http://gw.api.tbsandbox.com/router/rest", "1023389720", "********************************");
        CainiaoCloudprintStdtemplatesGetRequest req = new CainiaoCloudprintStdtemplatesGetRequest();
        CainiaoCloudprintStdtemplatesGetResponse rsp = null;
        try {
            rsp = client.execute(req, "6201e308d576aa53f37be4d069bfe46cb29bcfhjdd1418f2054718218");
            System.out.println(rsp.getBody());
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }
}
