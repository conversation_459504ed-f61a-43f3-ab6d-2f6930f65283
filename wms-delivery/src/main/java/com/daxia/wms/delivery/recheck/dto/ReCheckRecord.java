package com.daxia.wms.delivery.recheck.dto;

import com.daxia.framework.common.util.StringUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;

/**
 * 对应一条核拣记录，该记录与定单中的商品是一对一的关系
 */
@lombok.extern.slf4j.Slf4j
public class ReCheckRecord {
	private Long doId;
	private Long productId;
	
	/**
	 * 商品是否需要进行序列号扫描
	 */
	private boolean scanSerial;
	
	/**
	 * 商品是否需要扫描 药监码, 0（不用扫码）、1（可选）、2（必扫）
	 */
	private int spvsnCodeFlag;
	
	/**
	 * 商品编号
	 */
	private String productCode;
	/**
	 * 商品条码
	 */
	private String productBarCode;
	private String productName;
	private String productEName;
	/**
	 * 托盘号
	 */
	private String pallet;
	/**
	 * 商品描述
	 */
	private String productDetail;
	/**
	 * 商品计数单位
	 */
	private String unit;

	/**
	 * 应核拣数
	 */
	private BigDecimal needCheckNumber;
	/**
	 * 已核拣数
	 */
	private BigDecimal finishedCheckNumber;
	
	private Double grossWight;//商品毛重
	
	private BigDecimal volume;//商品体积

    /**
     * 商品助记码
     */
    private String helpCode;
    private String specification;
	/**
	 * 主包装描述
	 */
	private String uomDescr;

	private int packQty;

    private String lotatt05;
	/**
	 * 运输温度
	 */
	private String transportWendy;
	/**
	 * 组套商品条码
	 */
	private String groupBarCode;
	/**
	 * 组套商品数量
	 */
	private Integer groupQty;

	/**
	 * 是否预包 0-否 1-是
	 */
	private boolean prePack;
	@Getter
	@Setter
	/**
	 * 订单平台SO单号
	 */
	private String saleNo;

	public String getTransportWendy() {
		return transportWendy;
	}

	public void setTransportWendy(String transportWendy) {
		this.transportWendy = transportWendy;
	}

	public Long getDoId() {
		return doId;
	}

	public void setDoId(Long doId) {
		this.doId = doId;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getProductBarCode() {
		return productBarCode;
	}

	public void setProductBarCode(String productBarCode) {
		this.productBarCode = productBarCode;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public BigDecimal getNeedCheckNumber() {
		return needCheckNumber;
	}

	public void setNeedCheckNumber(BigDecimal needCheckNumber) {
		this.needCheckNumber = needCheckNumber;
	}

	public BigDecimal getFinishedCheckNumber() {
		return finishedCheckNumber;
	}

	public void setFinishedCheckNumber(BigDecimal finishedCheckNumber) {
		this.finishedCheckNumber = finishedCheckNumber;
	}

	/**
	 * @return 待核拣商品数量
	 */
	public BigDecimal getPendingCheckNumber() {
		return this.needCheckNumber.subtract(this.finishedCheckNumber);
	}

	/**
	 * 
	 * @return 商品的描述信息
	 */
	public String getProductDetail() {
		return this.productDetail;
	}

	public void setProductDetail(String productDetail) {
		this.productDetail = productDetail;
	}

	/**
	 * 增加该记录对应商品的已核拣数量
	 *
	 * @param number
	 */
	public void addCheckNumber(BigDecimal number) {
		this.finishedCheckNumber = this.finishedCheckNumber.add(number);
	}

	/**
	 * 减少该记录对应商品的已核拣数量
	 *
	 * @param number
	 */
	public void decreaseCheckNumber(BigDecimal number) {
		this.finishedCheckNumber = this.finishedCheckNumber.subtract(number);
	}
	
	public boolean isScanSerial(){
		return scanSerial;
	}

	public void setScanSerial(boolean scanSerial) {
		this.scanSerial = scanSerial;
	}

	/**
	 * 获取 商品是否需要扫描 药监码, 0（不用扫码）、1（可选）、2（必扫）
	 * @return
	 */
	public int getSpvsnCodeFlag() {
		return spvsnCodeFlag;
	}

	public void setSpvsnCodeFlag(int spvsnCodeFlag) {
		this.spvsnCodeFlag = spvsnCodeFlag;
	}

	public Double getGrossWight() {
		return grossWight;
	}

	public void setGrossWight(Double grossWight) {
		this.grossWight = grossWight;
	}

	public BigDecimal getVolume() {
		return volume;
	}

	public void setVolume(BigDecimal volume) {
		this.volume = volume;
	}

    public String getHelpCode() {
        return helpCode;
    }

    public void setHelpCode(String helpCode) {
        this.helpCode = helpCode;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

	public String getUomDescr() {
		return uomDescr;
	}

	public void setUomDescr(String uomDescr) {
		this.uomDescr = uomDescr;
	}

    public int getPackQty() {
        return packQty;
    }

    public void setPackQty(int packQty) {
        this.packQty = packQty;
    }

    public String getLotatt05() {
        return lotatt05;
    }

    public void setLotatt05(String lotatt05) {
        this.lotatt05 = lotatt05;
    }

	public String getGroupBarCode() {
		return groupBarCode;
	}

	public void setGroupBarCode(String groupBarCode) {
		this.groupBarCode = groupBarCode;
	}

	public Integer getGroupQty() {
		return groupQty;
	}

	public void setGroupQty(Integer groupQty) {
		this.groupQty = groupQty;
	}

	public String getUid() {
		if (StringUtils.isNotBlank(lotatt05)) {
			return productId + "_" + lotatt05.hashCode();
		}
        return productId + "_" + StringUtil.notNullString(lotatt05);
    }

	public String getPallet() {
		return pallet;
	}

	public void setPallet(String pallet) {
		this.pallet = pallet;
	}

	public String getProductEName() {
		return productEName;
	}

	public void setProductEName(String productEName) {
		this.productEName = productEName;
	}

	public boolean isPrePack() {
		return prePack;
	}

	public void setPrePack(boolean prePack) {
		this.prePack = prePack;
	}
}
