package com.daxia.wms.delivery.print.service.carton;

import com.daxia.dubhe.api.wms.dto.LogisticsDTO;
import com.daxia.dubhe.api.wms.dto.LogisticsExtendInfo;
import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.WaybillType;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoPrintDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoPrint;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.delivery.deliveryorder.service.DoWaveExService;
import com.daxia.wms.delivery.print.dto.BaseCartonPrintDTO;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.helper.PrintCartonHelper;
import com.daxia.wms.delivery.print.helper.SFPrintCartonHelper;
import com.daxia.wms.delivery.print.service.PrintWaybillService;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.print.PrintConstants;
import com.daxia.wms.print.dto.PrintCfg;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.print.utils.PrintHelper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Name("printSFWaybillService")
@lombok.extern.slf4j.Slf4j
public class PrintSFWaybillServiceImpl extends AbstractPrintWaybillService implements PrintWaybillService {
    @In
    private DoWaveExService doWaveExService;
    @In
    CartonService cartonService;
    @In
    WarehouseCarrierService warehouseCarrierService;
    @In
    private DoPrintDAO doPrintDAO;
    private ObjectMapper objectMapper = new ObjectMapper();

    @Create
    public void init () {
        this.setWaybillType(WaybillType.SF);
    }

    @Override
    protected void buildDTO(List<PrintReportDto> printReportDtos, DeliveryOrderHeader doHeader,
                            BaseCartonPrintDTO carton, int index, int count) {
        CartonPrintDTO cartonPrintDTO = new CartonPrintDTO();
        cartonPrintDTO.setIsPrinted(carton.getIsPrinted());
        cartonPrintDTO.setCartonId(carton.getId());
        cartonPrintDTO.setCartonNo(carton.getCartonNo());
        cartonPrintDTO.setDoNo(doHeader.getDoNo());
        cartonPrintDTO.setOutRefNo(doHeader.getRefNo1());
        cartonPrintDTO.setIsCOD(PrintCartonHelper.isCOD(doHeader));
        cartonPrintDTO.setSortGridNo(doHeader.getSortGridNo());
        cartonPrintDTO.setSortGridCount(doHeader.getWaveHeader().getDoHeaders().size());
        cartonPrintDTO.setWaveNo(doHeader.getWaveHeader().getWaveNo());

        // 设置篮号（波次顺序）
        cartonPrintDTO.setBasketNo(String.valueOf(index));
        cartonPrintDTO.setOriginalSoCode(doHeader.getOriginalSoCode());
        // 设置箱子里面商品个数
        BigDecimal skuUnit = cartonService.sumSkuUnit(carton.getId());
        cartonPrintDTO.setSkuUnitQty(skuUnit);
        // 设置收货人地址
        cartonPrintDTO.setClientProvinceAndCityAndCountyAddress(
            SFPrintCartonHelper.buildProvinceAndCityAndCountyAddress(doHeader, ","));
        cartonPrintDTO.setClientAddress(StringUtil.notNullString(doHeader.getAddress()));
        cartonPrintDTO.setClientName(SFPrintCartonHelper.buildConsigneeName(doHeader));
        cartonPrintDTO.setClientPhone(PrintCartonHelper.buildTelOrMobile(doHeader));
        // 设置寄件人地址信息
        this.setSendAddressInfo(doHeader, cartonPrintDTO);
        // 设置始发地代码编号和目的地代码编号
        DoWaveEx doWaveEx = doWaveExService.findByDoHeaderId(doHeader.getId());
        if (null != doWaveEx) {
            cartonPrintDTO.setStartAddressCode(doWaveEx.getOriginCode());
            cartonPrintDTO.setDestAddressCode(doWaveEx.getDestinationCode());
            cartonPrintDTO.setMainNo(doWaveEx.getTrackingNo());
        }
        // 运单号
        cartonPrintDTO.setWayBill(carton.getCartonNo());
        cartonPrintDTO.setMainWayBill(doHeader.getTrackingNo());
        // 是否需要待收货款
        boolean receivable =
            doHeader.getReceivable() != null && doHeader.getReceivable().compareTo(BigDecimal.ZERO) > 0;
        cartonPrintDTO.setNeedReceivable(receivable);
        if (receivable) {
            // 设置代收金额
            cartonPrintDTO.setServiceCodAmount(doHeader.getReceivable().toString());
            cartonPrintDTO.setProductSalesType("COD");
        }
        // 设置图片路径
        cartonPrintDTO.setBasePrintImgPath(PrintHelper.getBasePrintImgPath());
        // 设置配送商的信息
        // WarehouseCarrier warehouseCarrier = warehouseCarrierService
        // .getCarrierInfoByWarehouseIdAndCarrierId(ParamUtil.getCurrentWarehouseId(), doHeader.getCarrierId());
        // cartonPrintDTO.setMonthlyAccount(warehouseCarrier.getExt1());
        cartonPrintDTO.setPaymentMethod(PrintConstants.SFPaymentMethod.JIFU.getValue());
        // 设置是否第三方网点支付
        // if (StringUtils.isNotBlank(warehouseCarrier.getExt2())) {
        // cartonPrintDTO.setPaymentMethod(PrintConstants.SFPaymentMethod.DISANFANG.getValue());
        // cartonPrintDTO.setCpCode(warehouseCarrier.getExt2());
        // }
        // 付款方式为到付
        Integer orderNum =
            SystemConfig.getConfigValueInt("fee.receiverPay.unit.number", ParamUtil.getCurrentWarehouseId());
        if (orderNum != null && doHeader.getExpectedQty().intValue() < orderNum) {
            cartonPrintDTO.setMonthlyAccount("");
            cartonPrintDTO.setPaymentMethod(PrintConstants.SFPaymentMethod.RECEIVE.getValue());
        }
        // 设置顺丰产品类型
        cartonPrintDTO.setSuggestCode("");
        // cartonPrintDTO.setProductType(warehouseCarrier.getExt3());
        // if (PrintConstants.SFProductType.E.getName().equals(warehouseCarrier.getExt3())) {
        // cartonPrintDTO.setSuggestCode("E");
        // }

        // 设置备注
        cartonPrintDTO.setRemark("");
        String remark = SystemConfig.getConfigValue("sf.default.remark", ParamUtil.getCurrentWarehouseId());
        if (StringUtils.isNotBlank(remark)) {
            cartonPrintDTO.setRemark(remark);
        }
        cartonPrintDTO.setSkuUnitQty(this.getUnitQtyInCarton(doHeader, carton.getCartonNo()));
        cartonPrintDTO.setNotes(doHeader.getNotes());
        // cartonPrintDTO.setCodAccount(StringUtil.isNotEmpty(warehouseCarrier.getExt6())?warehouseCarrier.getExt6():warehouseCarrier.getExt1());
        this.setProductInfo(doHeader, cartonPrintDTO);
        printReportDtos.add(cartonPrintDTO);

        // 额外运输信息
        cartonPrintDTO.setWeight(doHeader.getGrossWt().toString());
        // 设置集货标识
        String jibao=Config.get(Keys.Print.sf_jibao_products, Config.ConfigLevel.WAREHOUSE,"");
        Set<String> doProducts = doHeader.getDoDetails().stream().map(x -> x.getSku().getEan13()).collect(Collectors.toSet());
        HashSet<String> set = new HashSet<>(Arrays.asList(jibao.split(",")));
        set.retainAll(doProducts);
        if(!set.isEmpty()){
            cartonPrintDTO.setSpecialFlags("集");
        }

        this.processWayBillInfo(doHeader.getId(), cartonPrintDTO);
    }

    @Override
    protected PrintData genPrintData(List<PrintReportDto> dtoList, DeliveryOrderHeader doHeader) {
        PrintData printData = new PrintData();
        printData.setDtoList(dtoList);
        printData.setPrintCfg(this.generateSFPrintCfg());
        return printData;
    }

    private PrintCfg generateSFPrintCfg() {
        PrintCfg config = new PrintCfg("wayBillSF", "100", "150");
        this.setPrintCfg(config);
        return config;
    }

    @SneakyThrows
    private void processWayBillInfo(Long doId, CartonPrintDTO cartonPrintDTO) {
        DoPrint doPrint = doPrintDAO.findByDoHeaderId(doId, Constants.DoPrintInfoType.WAYBILL_JSON.getValue());
        if (null == doPrint) {
            throw new DeliveryException(DeliveryException.WAYBILL_IMAGE_NOT_EXIST);
        }
        LogisticsDTO logisticsDTO = objectMapper.readValue(doPrint.getContent(), LogisticsDTO.class);

        cartonPrintDTO.setDestRouteLabel(logisticsDTO.getDestRouteLabel());
        cartonPrintDTO.setDestTeamCode(logisticsDTO.getDestTeamCode());
        cartonPrintDTO.setCodingMapping(logisticsDTO.getCodingMapping());
        cartonPrintDTO.setTwoDimensionCode(logisticsDTO.getTwoDimensionCode());
        cartonPrintDTO.setAgingName(logisticsDTO.getAgingName());
        cartonPrintDTO.setPrintIcon(logisticsDTO.getPrintIcon());
        if(StringUtils.isNotBlank(logisticsDTO.getExtendInfo())) {
            cartonPrintDTO.setExtendInfo(objectMapper.readValue(logisticsDTO.getExtendInfo(), LogisticsExtendInfo.class));
        }

    }
}
