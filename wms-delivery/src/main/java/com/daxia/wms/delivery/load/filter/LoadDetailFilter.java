package com.daxia.wms.delivery.load.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

/**
 * 交接单明细查询filter
 */
@lombok.extern.slf4j.Slf4j
public class LoadDetailFilter extends WhBaseQueryFilter {

    private static final long serialVersionUID = 3280896717494333588L;

    private String carrierNo;
    
    private String doNo;
    
    private Long loadHeaderId;
    
    private Long doHeaderId;
    
    private Long notLoadHeaderId;

    public void setCarrierNo(String carrierNo) {
        this.carrierNo = carrierNo;
    }

    @Operation(fieldName = "o.carrierNo", operationType = OperationType.EQUAL)
    public String getCarrierNo() {
        return carrierNo;
    }

    public void setLoadHeaderId(Long loadHeaderId) {
        this.loadHeaderId = loadHeaderId;
    }
    
    @Operation(fieldName = "o.loadHeaderId", operationType = OperationType.EQUAL)
    public Long getLoadHeaderId() {
        return loadHeaderId;
    }

    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }

    @Operation(fieldName = "o.doHeaderId", operationType = OperationType.EQUAL)
    public Long getDoHeaderId() {
        return doHeaderId;
    }

    public void setNotLoadHeaderId(Long notLoadHeaderId) {
        this.notLoadHeaderId = notLoadHeaderId;
    }

    @Operation(fieldName = "o.loadHeaderId", operationType = OperationType.NOT_EQUAL)
    public Long getNotLoadHeaderId() {
        return notLoadHeaderId;
    }

    @Operation(fieldName = "o.doNo", operationType = OperationType.EQUAL)
	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}
}
