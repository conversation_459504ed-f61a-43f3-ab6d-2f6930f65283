package com.daxia.wms.delivery.container.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.wms.delivery.container.entity.ContainerLog;
import com.daxia.wms.delivery.container.filter.ContainerLogFilter;
import com.daxia.wms.delivery.container.service.ContainerLogService;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.sql.Timestamp;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

@Name("com.daxia.wms.delivery.containerLogAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ContainerLogAction extends PagedListBean<ContainerLog> {
    
    ContainerLogFilter containerLogFilter;
    
    @In
    ContainerLogService containerLogService;
    
    
    public ContainerLogAction() {
        super();
        containerLogFilter = new ContainerLogFilter();
        containerLogFilter.setOperationTimeFm(DateUtil.dateAdd("dd", new Timestamp(System.currentTimeMillis()), -10));
    
        Map<String, String> orderByMap = new LinkedHashMap<String, String>();
        orderByMap.put("operationTime", "desc");
        containerLogFilter.setOrderByMap(orderByMap);
    }
    
    @Override
    public void query() {
        this.buildOrderFilterMap(containerLogFilter);
        DataPage<ContainerLog> dataPage = containerLogService.query(containerLogFilter, getStartIndex(), getPageSize());
        populateValues(dataPage);
    }
    
    public ContainerLogFilter getContainerLogFilter() {
        return containerLogFilter;
    }
    
    public void setContainerLogFilter(ContainerLogFilter containerLogFilter) {
        this.containerLogFilter = containerLogFilter;
    }
}
