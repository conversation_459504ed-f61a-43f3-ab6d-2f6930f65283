package com.daxia.wms.delivery.print.dto;


/**
 *  打印参数设置
 */
@lombok.extern.slf4j.Slf4j
public class PrintCfg {
    
    /**
     * 打印纸张类型（如：A4、A5等）
     */
    private String pageType;
    
    /**
     * 打印方向
     */
    private String orient;
    
    /**
     * 报表名
     */
    private String reportName;
    
    /**
     * 宽度
     */
    private String pageWidth;
    
    /**
     * 高度
     */
    private String pageHeight;
    
    public String getPageType() {
        return pageType;
    }
    
    public void setPageType(String pageType) {
        this.pageType = pageType;
    }
    
    public String getOrient() {
        return orient;
    }
    
    public void setOrient(String orient) {
        this.orient = orient;
    }
    
    public String getReportName() {
        return reportName;
    }
    
    public void setReportName(String reportName) {
        this.reportName = reportName;
    }
    
    public String getPageWidth() {
        return pageWidth;
    }
    
    public void setPageWidth(String pageWidth) {
        this.pageWidth = pageWidth;
    }
    
    public String getPageHeight() {
        return pageHeight;
    }
    
    public void setPageHeight(String pageHeight) {
        this.pageHeight = pageHeight;
    }    
}
