package com.daxia.wms.delivery.print.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.daxia.dubhe.api.internal.util.NumberUtils;
import com.daxia.framework.common.util.*;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoType;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.context.GroupPrintDoContext;
import com.daxia.wms.delivery.deliveryorder.dao.DoHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.invoice.service.InvoiceService;
import com.daxia.wms.delivery.print.dto.PrintCfg;
import com.daxia.wms.delivery.print.dto.PrintDoDTO;
import com.daxia.wms.delivery.print.dto.PrintDoDetailDTO;
import com.daxia.wms.delivery.print.service.PrintDoService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.master.component.BusinessCenterComponent;
import com.daxia.wms.print.PrintConstants;
import com.daxia.wms.print.service.PrintLogService;
import com.daxia.wms.print.utils.PrintHelper;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * 发货单打印业务实现类
 */
@Name("com.daxia.wms.delivery.printDoService")
@lombok.extern.slf4j.Slf4j
public class PrintDoServiceImpl implements PrintDoService {
    @In
    private DeliveryOrderService deliveryOrderService;

    @In
    private DoHeaderDAO doHeaderDAO;

    @In
    private InvoiceService invoiceService;

    @In
    private PrintLogService printLogService;

    @In
    private WaveService waveService;
    @In
    private BusinessCenterComponent businessCenterComponent;

    /**
     * 设置发货单打印参数
     *
     * @param docId         单据id
     * @param printEntrance 打印入口
     * @return
     */
    @Override
    public PrintCfg setDoPrintCfg(Long docId, String printEntrance) {
        return new PrintCfg();
    }

    @Override
    public String genDataByWave(List<Long> waveIds) {

        List<WaveHeader> waveList = waveService.getWaveList(waveIds);
        if (CollectionUtils.isEmpty(waveList)) {
            return null;
        }

        List<String> list = Lists.newArrayList();
        Map<String, PrintDoDTO> map = new HashMap<>(waveIds.size());
        for (WaveHeader waveHeader : waveList) {
            if (Constants.AutoWaveType.BATCH_GROUP.getValue().equals(waveHeader.getAutoType())) {
                list.add(waveHeader.getWaveNo());
                map.put(waveHeader.getWaveNo(), null);
            }
        }
        GroupPrintDoContext.setWaveNoList(list);
        GroupPrintDoContext.setGroupPrintCache(map);


        List<Long> doIds = deliveryOrderService.findDoIdByWaveId(waveIds);
        for (Long waveId : waveIds) {
            printLogService.savePrintLog(waveId, waveService.getWave(waveId).getWaveNo(), PrintConstants.PrintType.DO, DoType.SELL.getValue(), 0L, null, null);
        }
        return genData(doIds);
    }

    @Override
    public String genData(List<Long> doIds) {
        if (ListUtil.isNullOrEmpty(doIds)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }

        int doCount = Integer.parseInt(AppConfig.getProperty("print.wave.do.count", "500"));
        if (doIds.size() > doCount) {
            throw new DeliveryException(DeliveryException.PRINT_WAVE_DO_COUNT_MAX, doCount);
        }

        if (doHeaderDAO.isMixType(doIds)) {
            throw new DeliveryException(DeliveryException.DO_PRINT_TYPE_ERROR);
        }

        // 数据组装-数据初始化
        List<PrintDoDTO> dtos = doHeaderDAO.getPrintDoDTO(doIds);
        Map<String, PrintDoDTO> groupPrintCache = GroupPrintDoContext.getGroupPrintCache();
        List<String> waveNoGroupList = GroupPrintDoContext.getWaveNoList();

        // 商品明细行拆分-数据初始化
        List<PrintDoDTO> printDoDTOList = new ArrayList<>();
        int count = Integer.parseInt(AppConfig.getProperty("print.do.page.count", "16"));
        dtos.stream().forEach(dto -> {
            String waveNo = dto.getWaveNo();
            PrintDoDTO printDoDTO =
                    Optional.ofNullable(groupPrintCache).map(cache -> cache.get(waveNo)).orElse(null);

            // 是团购波次且缓存数据有值  根据缓存数据进行组装
            if (CollectionUtils.isNotEmpty(waveNoGroupList) && waveNoGroupList.contains(waveNo) && printDoDTO != null) {
                copyPrintDoDTO(printDoDTO, dto);
            } else {
                buildPrintDoDTO(dto);
                // 是团购波次 缓存数据
                if (CollectionUtils.isNotEmpty(waveNoGroupList) && waveNoGroupList.contains(waveNo) && printDoDTO != null) {
                    groupPrintCache.put(waveNo, dto);
                }
            }

            // 拆分商品明细行
            List<PrintDoDetailDTO> printDoDetails = dto.getDetailDtos();
            AtomicInteger indexPage = new AtomicInteger(1);
            if (printDoDetails.size() < count) {
                dto.setIndexPage(indexPage.get());
                dto.setTotalPage(indexPage.get());
                printDoDTOList.add(dto);
                return;
            }
            List<List<PrintDoDetailDTO>> lists = Lists.partition(printDoDetails, count);
            List<PrintDoDTO> printDoDTOS = lists.stream().map(detailDTOS -> {
                PrintDoDTO printDTO = new PrintDoDTO();
                BeanUtils.copyProperties(dto, printDTO);
                printDTO.setDetailDtos(detailDTOS);
                printDTO.setIndexPage(indexPage.getAndIncrement());
                printDTO.setTotalPage(lists.size());
                return printDTO;
            }).sorted((a,b)->Integer.valueOf(b.getSortGridNo())-Integer.valueOf(a.getSortGridNo()))
                    .collect(Collectors.toList());

            printDoDTOList.addAll(printDoDTOS);
        });

        Map<String, Object> originalUnitProps = new HashMap<>();
        originalUnitProps.put("dtos", printDoDTOList);

        GroupPrintDoContext.removeGroupPrintCache();
        GroupPrintDoContext.removeWaveNoList();

        // 更新打印标记
        deliveryOrderService.updatePrintFlag(doIds);

        return PrintTemplateUtil.process(PrintConstants.PrintTemplate.DO.name(), originalUnitProps);
    }

    private void copyPrintDoDTO(PrintDoDTO source, PrintDoDTO target) {
        target.setDoNoCodeType(PrintHelper.decide128CodeType(target.getDoNo()));
        target.setBasePrintPath(PrintHelper.getBasePath(false));
        target.setQty(source.getQty());
        target.setTotalAmount(source.getTotalAmount());
        target.setObject(source.getObject());
        target.setMerchantName(source.getDetailDtos().get(0).getMerchantName());
        target.setDetailDtos(source.getDetailDtos());
        target.setChannelCode(source.getChannelCode());
        target.setShopNickName(businessCenterComponent.getStoreMap().get(source.getStoreCode()));
    }

    private void buildPrintDoDTO(PrintDoDTO printDoDTO) {
        printDoDTO.setShopNickName(businessCenterComponent.getStoreMap().get(printDoDTO.getStoreCode()));
        //断定Code类型
        printDoDTO.setDoNoCodeType(PrintHelper.decide128CodeType(printDoDTO.getDoNo()));
        printDoDTO.setBasePrintPath(PrintHelper.getBasePath(false));
//        List<InvoiceHeader> invoiceHeaders = invoiceService.getInvoiceListByDoIds(Lists.newArrayList(printDoDTO.getDoId()));
//        if (invoiceHeaders.size() > 0) {
//            for (InvoiceHeader invoiceHeader : invoiceHeaders) {
//                if (invoiceHeader.getInvoiceStatus() == InvoiceHeader.InvoiceStatus.BILLED || invoiceHeader.getInvoiceStatus() == InvoiceHeader.InvoiceStatus.PRINT) {
//                    printDoDTO.setInvoiceUrl(invoiceHeader.getInvoiceUrl());
//                    printDoDTO.setInvoiceType(invoiceHeader.getInvoiceType());
//
//                    break;
//                }
//            }
//        }
        //代购订单
        if ("20".equals(printDoDTO.getOrderSubType())) {
            printDoDTO.setIsPurchasingAgent(Constants.YesNo.YES.getValue().toString());
        } else {
            printDoDTO.setIsPurchasingAgent(Constants.YesNo.NO.getValue().toString());
        }

            List<PrintDoDetailDTO> detailDtos = sortDetail(doHeaderDAO.getPrintDoDetailDTO(printDoDTO.getDoId()), printDoDTO);
            BigDecimal qty = BigDecimal.ZERO;
            BigDecimal totalAmount = BigDecimal.ZERO;
            for (PrintDoDetailDTO dto : detailDtos) {
                qty = qty.add(dto.getQty());
                totalAmount = totalAmount.add(NumberUtils.object2BigDecimal(dto.getAmountPrice(), BigDecimal.ZERO));
            }
            printDoDTO.setQty(qty);
            printDoDTO.setTotalAmount(totalAmount);
            JSONObject jasonObject = new JSONObject();
            printDoDTO.setObject(jasonObject);
            printDoDTO.getObject().put("printTime", DateUtil.dateToString(DateUtil.getNowTime(), DateUtil.DATETIME_PATTERN));
            DeliveryOrderHeader deliveryOrderHeader = doHeaderDAO.get(printDoDTO.getDoId());
            printDoDTO.getObject().put("emergencyFlag", deliveryOrderHeader.getEmergencyFlag() != null && deliveryOrderHeader.getEmergencyFlag() == 1 ? "是" : "否");
            printDoDTO.getObject().put("paymentMethod", deliveryOrderHeader.getPaymentMethodName());
            printDoDTO.getObject().put("shipTime", DateUtil.dateToString(deliveryOrderHeader.getPlanShipTimeEnd()));
            printDoDTO.getObject().put("printFlag", deliveryOrderHeader.getPrintFlag());
            printDoDTO.setMerchantName(detailDtos.get(0).getMerchantName());
            ListUtil.megareList(detailDtos, new ListUtil.ListMegareOpr<PrintDoDetailDTO>() {
                @Override
                public boolean isNeedMegare(PrintDoDetailDTO t1, PrintDoDetailDTO t2) {
                    Boolean isMegareBySku = SystemConfig.configIsOpen("print.do.megare.by.sku", ParamUtil.getCurrentWarehouseId());
                    if (isMegareBySku) {
                        return CompareUtil.compare(t1.getSkuId(), t2.getSkuId())
                                && CompareUtil.compare(t1.getOrigDetailId(), t2.getOrigDetailId());
                    }
                    return CompareUtil.compare(t1.getLotNo(), t2.getLotNo()) && CompareUtil.compare(t1.getExpiredTime(), t2.getExpiredTime()) && CompareUtil.compare(t1.getSkuId(), t2.getSkuId())
                            && CompareUtil.compare(t1.getOrigDetailId(), t2.getOrigDetailId());
                }

                @Override
                public void megareOpr(PrintDoDetailDTO t1, PrintDoDetailDTO t2) {
                    t1.setQty(t1.getQty().add(t2.getQty()));
                    t1.setAllocatedQty(t1.getAllocatedQty().add(t2.getAllocatedQty()));
                }
            });
            printDoDTO.setDetailDtos(detailDtos);
        }

    private List<PrintDoDetailDTO> sortDetail(List<PrintDoDetailDTO> detailDtos, PrintDoDTO printDoDTO) {
        List<PrintDoDetailDTO> resultSubList = new ArrayList<PrintDoDetailDTO>();
        // 处理组合商品，使组合商品的父商品和字商品排在一起
        Map<String, List<PrintDoDetailDTO>> childrenMap = new HashMap<String, List<PrintDoDetailDTO>>();
        Map<String, PrintDoDetailDTO> parentMap = new HashMap<String, PrintDoDetailDTO>();
        BigDecimal totalQty = BigDecimal.ZERO;
        for (PrintDoDetailDTO sub : detailDtos) {
            String origDetailId = sub.getOrigDetailId();
            String parentId = sub.getParentId();

            if (sub.getDoLeaf()) {
                // qty = null,表示该记录尚无对应拣货任务，此时数量从do明细上取
                if (null == sub.getQty()) {
                    sub.setQty(BigDecimal.ZERO);
                }
                totalQty = totalQty.add(sub.getQty());

                // 处理组合商品逻辑
                List<PrintDoDetailDTO> children = childrenMap.get(parentId);
                if (children == null) {
                    children = new ArrayList<PrintDoDetailDTO>();
                    childrenMap.put(parentId, children);
                }
                // 是组合商品，且是子商品
                if (parentId != null) {
                    children.add(sub);
                } else {
                    // 不是组合商品
                    resultSubList.add(sub);
                }
            } else {
                // 组合商品的父商品(父商品的数量从doDetail上取)
                sub.setQty(sub.getExpectedQty());
                parentMap.put(origDetailId, sub);
            }
        }
        printDoDTO.setExpectedQty(totalQty);
        Set<Map.Entry<String, PrintDoDetailDTO>> parentEntrySet = parentMap.entrySet();
        for (Map.Entry<String, PrintDoDetailDTO> entry : parentEntrySet) {
            resultSubList.add(entry.getValue());

            List<PrintDoDetailDTO> children = childrenMap.get(entry.getKey());
            if (children != null) {
                resultSubList.addAll(children);
            }
        }
        return resultSubList;
    }
}
