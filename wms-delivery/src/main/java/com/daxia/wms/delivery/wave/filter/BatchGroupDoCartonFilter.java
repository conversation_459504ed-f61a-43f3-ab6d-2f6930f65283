/**
 * 
 */
package com.daxia.wms.delivery.wave.filter;

import com.daxia.framework.common.filter.WhBaseQueryFilter;

/**
 * 
 * 团购波次do信息查询filter
 *
 */
@lombok.extern.slf4j.Slf4j
public class BatchGroupDoCartonFilter extends WhBaseQueryFilter {

	private static final long serialVersionUID = -125534445887341636L;
	
	/**
	 * 波次箱标签打印状态
	 */
	private Integer catornPrintStatus;
	
	private String doNo;
	
	private String cartonNo;
	
	private String releaseStatus;
	
	private String doStatusFrom;
	
	private String doStatusTo;

	public Integer getCatornPrintStatus() {
		return catornPrintStatus;
	}

	public void setCatornPrintStatus(Integer catornPrintStatus) {
		this.catornPrintStatus = catornPrintStatus;
	}

	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}

	public String getReleaseStatus() {
		return releaseStatus;
	}

	public void setReleaseStatus(String releaseStatus) {
		this.releaseStatus = releaseStatus;
	}

	public String getDoStatusFrom() {
		return doStatusFrom;
	}

	public void setDoStatusFrom(String doStatusFrom) {
		this.doStatusFrom = doStatusFrom;
	}

	public String getDoStatusTo() {
		return doStatusTo;
	}

	public void setDoStatusTo(String doStatusTo) {
		this.doStatusTo = doStatusTo;
	}

	public String getCartonNo() {
		return cartonNo;
	}

	public void setCartonNo(String cartonNo) {
		this.cartonNo = cartonNo;
	}
}
