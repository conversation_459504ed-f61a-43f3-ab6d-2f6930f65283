package com.daxia.wms.delivery.deliveryorder.dao;

import java.util.List;

import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.deliveryorder.entity.DoExceptionLog;

@Name("com.daxia.wms.delivery.doExceptionLogDAO")
@lombok.extern.slf4j.Slf4j
public class DoExceptionLogDAO extends HibernateBaseDAO<DoExceptionLog, Long> {

    private static final long serialVersionUID = -125405442368058573L;

   /**
    * 根据订单ID获取该订单的异常日志（创建时间排序）
    * @param doHeaderId
    * @return
    */
    @SuppressWarnings("unchecked")
    public List<DoExceptionLog> findExceptionLog(Long doHeaderId) {
        return this.createQuery("FROM DoExceptionLog del where del.doHeaderId = :doHeaderId" +
        		" and del.warehouseId = :warehouseId ORDER BY del.createdAt")
            .setParameter("doHeaderId", doHeaderId).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).list();
    }

}
