package com.daxia.wms.delivery.deliveryorder.service.impl;

import java.util.List;
import java.util.Set;
import java.util.TreeSet;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import com.daxia.framework.common.util.ListUtil;
import com.daxia.wms.delivery.deliveryorder.dao.DoLackDetailDAO;
import com.daxia.wms.delivery.deliveryorder.dao.DoLackHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DoLackHeader;
import com.daxia.wms.delivery.deliveryorder.service.DoLackHeaderService;
import com.daxia.wms.Constants;

@Name("com.daxia.wms.delivery.doLackHeaderService")
@lombok.extern.slf4j.Slf4j
public class DoLackHeaderServiceImpl implements DoLackHeaderService {
    
    @In
    private DoLackHeaderDAO doLackHeaderDAO;
    @In
    private DoLackDetailDAO doLackDetailDAO;
    
    @Override
    public DoLackHeader getByDoId(Long doHeaderId) {
        return doLackHeaderDAO.getByDoId(doHeaderId);
    }

    @Override
    @Transactional
    public void addOrUpdate(Long doHeaderId, String lackPartition) {
        Set<String> lackPartitions = new TreeSet<String>();
        lackPartitions.add(lackPartition);
        addOrUpdate(doHeaderId, lackPartitions);
    }
    
    @Override
    @Transactional
    public void addOrUpdate(Long doHeaderId, Set<String> lackPartitions) {
        if (null == lackPartitions || lackPartitions.size() < 1) {
            return;
        }
        DoLackHeader doLackHeader = getByDoId(doHeaderId);
        if (null != doLackHeader) {
            //如果是已完成则改为未生成(有漏洞暂这么处理)
            if (Constants.FindLackCreateStatus.COMPLETED.getValue().equals(doLackHeader.getCreateStatus())) {
                doLackHeader.setCreateStatus(Constants.FindLackCreateStatus.NOT_CREATED.getValue());
            }
            String [] existPartitions = doLackHeader.getLackPartitions().split(";");
            for (String p : existPartitions) {
                lackPartitions.add(p);
            }
        } else {
            doLackHeader = new DoLackHeader();
            doLackHeader.setCreateStatus(Constants.FindLackCreateStatus.NOT_CREATED.getValue());
            doLackHeader.setDoHeaderId(doHeaderId);
        }
        String lackPartitionStr = ListUtil.collection2String(lackPartitions, ";");
        doLackHeader.setLackPartitions(lackPartitionStr);
        doLackHeaderDAO.saveOrUpdate(doLackHeader);
    }

    @Override
    @Transactional
    public void deleteLackInfoByDoId(Long doId) {
        doLackDetailDAO.removeLackDoDetail(doId);
    }

    @Override
    @Transactional
    public void updateCreateStatus(List<Long> doIds, String stauts) {
        doLackHeaderDAO.updateCreateStatus(doIds, stauts);
    }

    @Override
    @Transactional
    public void updateCreateStatusByDoId(Long doId, String stauts) {
        doLackHeaderDAO.updateCreateStatusByDoId(doId, stauts);
    }
}
