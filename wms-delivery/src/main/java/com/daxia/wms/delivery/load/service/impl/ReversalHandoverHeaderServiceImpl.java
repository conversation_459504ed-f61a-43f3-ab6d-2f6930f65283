package com.daxia.wms.delivery.load.service.impl;

import java.io.Serializable;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;


import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.load.dao.ReversalHandoverDetailDAO;
import com.daxia.wms.delivery.load.dao.ReversalHandoverHeaderDAO;
import com.daxia.wms.delivery.load.entity.ReversalHandoverDetail;
import com.daxia.wms.delivery.load.entity.ReversalHandoverHeader;
import com.daxia.wms.delivery.load.service.ReversalHandoverHeaderService;
import com.daxia.wms.Constants.ReversalStatus;
import com.daxia.wms.Constants.SequenceName;

/**
 * 逆向交接单接口实现类
 */
@Name("com.daxia.wms.delivery.reversalHandoverHeaderService")
@lombok.extern.slf4j.Slf4j
public class ReversalHandoverHeaderServiceImpl implements ReversalHandoverHeaderService, Serializable {

	private static final long serialVersionUID = 73805189912603333L;

    @In
    private SequenceGeneratorService sequenceGeneratorService;
    
    @In
    private ReversalHandoverHeaderDAO reversalHandoverHeaderDAO;
    
    @In
    private ReversalHandoverDetailDAO reversalHandoverDetailDAO;

	@Override
	public String generatorReversalHandoverNumber() {
		return sequenceGeneratorService.generateSequenceNo(SequenceName.REVERSALHANDOVERNO.getValue(), ParamUtil.getCurrentWarehouseId());
	}

	@Override
	public ReversalHandoverHeader get(Long id) {
		return reversalHandoverHeaderDAO.get(id);
	}
	
	@Override
    @Transactional
	public void cartonHandover(ReversalHandoverHeader reversalHandoverHeader,String cartonNo) {
		ReversalHandoverDetail reversalHandoverDetail = this.reversalHandoverDetailDAO.findReversalHandoverDetailByCartonNo(cartonNo);
		if(reversalHandoverDetail == null){
			throw new DeliveryException(DeliveryException.REVERSAL_CARTON_NOT_EXIST);
		}
		if(reversalHandoverDetail.getReversalHandoverId() != null){
			throw new DeliveryException(DeliveryException.REVERSAL_CARTON_HAD_HANDOVER);
		}
		if(reversalHandoverHeader.getId() == null){
			reversalHandoverHeaderDAO.save(reversalHandoverHeader);
		}
		reversalHandoverDetail.setReversalHandoverId(reversalHandoverHeader.getId());
		reversalHandoverDetail.setReceiveTime(DateUtil.getNowTime());
		reversalHandoverDetail.setReversalStatus(ReversalStatus.RECEIVED.getValue());
		reversalHandoverDetail.setReceiveBy(ParamUtil.getCurrentLoginName());
		reversalHandoverDetailDAO.save(reversalHandoverDetail);
		reversalHandoverHeader.getReversalHandoverDetails().add(reversalHandoverDetail);
	}
}
