package com.daxia.wms.delivery.task.replenish.filter;

import java.util.Date;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

/**
 * 补货单头查询过滤器
 */
@lombok.extern.slf4j.Slf4j
public class ReplHeaderFilter extends WhBaseQueryFilter {

    private static final long serialVersionUID = -918605295585591361L;

    private String replStatus;
    private String replNo;
	private String productBarCode;
	private String productCode;
    private Date fmCreateTime;
    private Date toCreateTime;
    private String replType;
    private String doType;
    private Integer isAutoPublish;
    private Date planShipTimeStdFm;		// 预计出库时间Fm
    private Date planShipTimeStdTo;		// 预计出库时间To
    
    /**
     * 是否只查询包含搬仓sku的订单
     */
    private Boolean transSkuFlag;
    
    /**
     * 源库区
     */
    private String fmPartitionCode;
    
    /**
     * 目标库区
     */
    private String toPartitionCode;
    
    @Operation(fieldName = "status", operationType = OperationType.EQUAL)
    public String getReplStatus() {
        return replStatus;
    }
    
    public void setReplStatus(String replStatus) {
        this.replStatus = replStatus;
    }

    @Operation(fieldName = "replNo", operationType = OperationType.EQUAL)
    public String getReplNo() {
        return replNo;
    }

    public void setReplNo(String replNo) {
        this.replNo = replNo.trim();
    }
    
    @Operation(operationType = OperationType.CLAUSE ,clause = " o.id in ( select task.docOperId from ReplenishTask task, Sku sku where task.skuId = sku.id and sku.productCode = ? )")
	public String getProductCode() {
		return productCode;
	}
	
	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}
	
    @Operation(operationType = OperationType.CLAUSE ,clause = " o.id in ( select task.docOperId from ReplenishTask task,Sku sku,ProductBarcode barCode  where  barCode.skuId = sku.id and task.skuId = sku.id and barCode.barcodeLevel1 = ?  )")
	public String getProductBarCode() {
        return productBarCode ;
    }

    public void setProductBarCode(String productBarCode) {
        this.productBarCode = productBarCode;
    }

    @Operation(fieldName = "createdAt", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")
    public Date getFmCreateTime() {
        return fmCreateTime;
    }

    public void setFmCreateTime(Date fmCreateTime) {
        this.fmCreateTime = fmCreateTime;
    }

    @Operation(fieldName = "createdAt", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
    public Date getToCreateTime() {
        return toCreateTime;
    }

    public void setToCreateTime(Date toCreateTime) {
        this.toCreateTime = toCreateTime;
    }

    @Operation(fieldName = "replType", operationType = OperationType.EQUAL)
	public String getReplType() {
		return replType;
	}

	public void setReplType(String replType) {
		this.replType = replType;
	}

	@Operation(fieldName = "doType", operationType = OperationType.EQUAL)
    public String getDoType() {
        return doType;
    }

    public void setDoType(String doType) {
        this.doType = doType;
    }  
    
    @Operation( operationType = OperationType.IGNORE)
    public Boolean getTransSkuFlag() {
        return transSkuFlag;
    }
    
    public void setTransSkuFlag(Boolean transSkuFlag) {
        this.transSkuFlag = transSkuFlag;
    }

    @Operation(fieldName = "isAuto", operationType = OperationType.EQUAL)
    public Integer getIsAutoPublish() {
        return isAutoPublish;
    }

    public void setIsAutoPublish(Integer isAutoPublish) {
        this.isAutoPublish = isAutoPublish;
    }

    @Operation(operationType = OperationType.CLAUSE ,clause = " o.srcPartitionId in ( select p.id from Partition p where p.warehouseId = :warehouseId and p.partitionCode = ?)")
    public String getFmPartitionCode() {
        return fmPartitionCode;
    }

    public void setFmPartitionCode(String fmPartitionCode) {
        this.fmPartitionCode = fmPartitionCode;
    }
    
    @Operation(operationType = OperationType.CLAUSE ,clause = " o.partitionId in ( select p.id from Partition p where p.warehouseId = :warehouseId and p.partitionCode = ?)")
    public String getToPartitionCode() {
        return toPartitionCode;
    }

    public void setToPartitionCode(String toPartitionCode) {
        this.toPartitionCode = toPartitionCode;
    }
    
    @Operation(fieldName = "earlistPlanShipTime", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")
	public Date getPlanShipTimeStdFm() {
		return planShipTimeStdFm;
    }
    
	public void setPlanShipTimeStdFm(Date planShipTimeStdFm) {
		this.planShipTimeStdFm = planShipTimeStdFm;
    }
    
    @Operation(fieldName = "earlistPlanShipTime", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
	public Date getPlanShipTimeStdTo() {
		return planShipTimeStdTo;
    }
    
	public void setPlanShipTimeStdTo(Date planShipTimeStdTo) {
		this.planShipTimeStdTo = planShipTimeStdTo;
    }
    
}
