package com.daxia.wms.delivery.deliveryorder.service;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.deliveryorder.entity.OtherDoDetail;
import com.daxia.wms.delivery.deliveryorder.entity.OtherDoHeader;
import com.daxia.wms.delivery.deliveryorder.filter.OtherDoFilter;

import java.math.BigDecimal;

public interface OtherDoService {

    DataPage<OtherDoHeader> queryDoHeaderPageInfo(OtherDoFilter otherDoFilter, int startIndex, int pageSize);

    void saveOrUpdate(OtherDoHeader otherDoHeader);

    OtherDoHeader getHeaderById(Long doHeaderId);

    OtherDoDetail getDetailById(Long doDetailId);


    void deletedDetail(Long doHeaderId, Long doDetailId);

    void createDetail(OtherDoDetail otherDoDetail, Long stockId, BigDecimal planQty);

    void confirmShip(Long doHeaderId);

    void verify(Long doHeaderId);

    void cancel(Long doHeaderId);

    String print(Long doHeaderId);
}
