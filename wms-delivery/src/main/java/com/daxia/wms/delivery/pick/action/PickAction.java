package com.daxia.wms.delivery.pick.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.Config;
import com.daxia.wms.Constants;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.service.PickService;
import com.daxia.wms.delivery.sort.service.SortingService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.security.Restrict;

/**
 * Description:拣货Action
 */
@Name("com.daxia.wms.delivery.pickAction")
@Restrict("#{identity.hasPermission('delivery.picking')}")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class PickAction extends PagedListBean<PickHeader> {

    private static final long serialVersionUID = -8884192102510433388L;

    private String pktNo;         //拣货单号
    private String pickerNo;      //拣货人工号
    /**
     * 是否二次分拣
     */
    private Boolean pickSort = false;

    private String printData = "[]";
    
    
    @In
    private PickService pickService;
    @In
    private SortingService sortingService;
    @In
    private WaveService waveService;
    
    @Override
    public void query() {

    }

    /**
     * Description:拣货
     */
    public void pick() throws Exception {
        pickService.pick(pktNo, pickerNo,!pickSort);

        //边拣边分，且波次拣货完成，波次直接分拣
        if (!pickSort) {
            WaveHeader waveHeader = waveService.getWaveHeaderByPktNo(pktNo);
            if (waveHeader == null) {//兼容波次号操作
                waveHeader = waveService.getWaveHeaderByWaveNum(pktNo);
            }
            if (waveHeader != null) {
                if (Constants.WaveStatus.ALLPICKED.getValue().equals(waveHeader.getWaveStatus())) {
                    sortingService.compelSorting(waveHeader.getWaveNo(), pickerNo);
                }
            }
        }

        this.sayMessage(MESSAGE_SUCCESS);
    }

    public String getPktNo() {
        return pktNo;
    }

    public void setPktNo(String pktNo) {
        this.pktNo = pktNo;
    }

    public String getPickerNo() {
        return pickerNo;
    }

    public void setPickerNo(String pickerNo) {
        this.pickerNo = pickerNo;
    }

    public String getPrintData() {
        return printData;
    }

    public void setPrintData(String printData) {
        this.printData = printData;
    }

    public Boolean getPickSort() {
        return pickSort;
    }

    public void setPickSort(Boolean pickSort) {
        this.pickSort = pickSort;
    }
}
