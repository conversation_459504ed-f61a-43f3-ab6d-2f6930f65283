package com.daxia.wms.delivery.store.dto;

import java.io.Serializable;

@lombok.extern.slf4j.Slf4j
public class StoreOutTaskDTO implements Serializable {

    /**
     * 集货位
     */
    private String storeLoc;

    /**
     * 容器号，有可能多个，逗号分隔
     */
    private String turnoverBoxNos;

    /**
     * 实际集货位
     */
    private String scanStoreLoc;

    /**
     * 是否可出区标记
     */
    private String finishedFlag;

    public String getStoreLoc() {
        return storeLoc;
    }

    public void setStoreLoc(String storeLoc) {
        this.storeLoc = storeLoc;
    }

    public String getTurnoverBoxNos() {
        return turnoverBoxNos;
    }

    public void setTurnoverBoxNos(String turnoverBoxNos) {
        this.turnoverBoxNos = turnoverBoxNos;
    }

    public String getScanStoreLoc() {
        return scanStoreLoc;
    }

    public void setScanStoreLoc(String scanStoreLoc) {
        this.scanStoreLoc = scanStoreLoc;
    }

    public String getFinishedFlag() {
        return finishedFlag;
    }

    public void setFinishedFlag(String finishedFlag) {
        this.finishedFlag = finishedFlag;
    }
}
