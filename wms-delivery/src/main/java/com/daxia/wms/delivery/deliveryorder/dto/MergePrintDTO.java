package com.daxia.wms.delivery.deliveryorder.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 合单打印实体
 */
@lombok.extern.slf4j.Slf4j
public class MergePrintDTO implements Serializable {

    private Long id;

    private String doNo;

    private String consigneeName;

    private Date doCreateTime;

    private String groupNo;

    /**
     * 配送商
     */
    private Long carrierId;
    /**
     * 配送商
     */
    private String carrierName;

    /**
     * 订单数
     */
    private Integer doNum;
    /**
     * 箱数
     */
    private Integer cartonNum;

    /**
     * 运单号
     */
    private String wayBill;
    /**
     * 下单内容
     */
    private String printData;

    private String refNo;
    private String status;
    private String customerName;

    private Integer isPrinted;

    private Integer wayBillNum;

    private List<Long> doIdList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public Date getDoCreateTime() {
        return doCreateTime;
    }

    public void setDoCreateTime(Date doCreateTime) {
        this.doCreateTime = doCreateTime;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public String getCarrierName() {
        return carrierName;
    }

    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName;
    }

    public Integer getDoNum() {
        return doNum;
    }

    public void setDoNum(Integer doNum) {
        this.doNum = doNum;
    }

    public Integer getCartonNum() {
        return cartonNum;
    }

    public void setCartonNum(Integer cartonNum) {
        this.cartonNum = cartonNum;
    }

    public String getWayBill() {
        return wayBill;
    }

    public void setWayBill(String wayBill) {
        this.wayBill = wayBill;
    }

    public String getPrintData() {
        return printData;
    }

    public void setPrintData(String printData) {
        this.printData = printData;
    }

    public String getRefNo() {
        return refNo;
    }

    public void setRefNo(String refNo) {
        this.refNo = refNo;
    }

    public Integer getIsPrinted() {
        return isPrinted;
    }

    public void setIsPrinted(Integer isPrinted) {
        this.isPrinted = isPrinted;
    }

    public Integer getWayBillNum() {
        return wayBillNum;
    }

    public void setWayBillNum(Integer wayBillNum) {
        this.wayBillNum = wayBillNum;
    }

    public List<Long> getDoIdList() {
        return doIdList;
    }

    public void setDoIdList(List<Long> doIdList) {
        this.doIdList = doIdList;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
}