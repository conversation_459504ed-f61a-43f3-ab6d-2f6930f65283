package com.daxia.wms.delivery.deliveryorder.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.master.entity.*;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.CascadeType;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "doc_alc_header")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class DoAllocateHeader extends WhBaseEntity {

    private static final long serialVersionUID = -340143555022285660L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 订单货品等级标识 A1 10, A2 11, A3 12, A1+A2 15,A1+A3 16, A2+A3 17, A1+A2+A3 18, B1 20, B1 21, C 22,
     * B1+B2 25, B1+C 26,B2+C 27, B1+B2+C 28  A+B/A+C  36
     */
    private Long cycleClass;
    /**
     * 发货单号
     */
    private String doNo;

    /**
     * 发货单状态
     */
    private String status;

    /**
     * 发运时间
     */
    private Date shipTime;

    /**
     * 发货单类型 DO、调拨、RTV
     */
    private String doType;

    /**
     * 冻结状态 HOLD：冻结 RELEASE：释放
     */
    private String releaseStatus;

    /**
     * 参考编号1(SO单号/调拨指令号)
     */
    private String refNo1;

    /**
     * 参考编号2
     */
    private String refNo2;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 配送说明 半日达/一日三送/团购
     */
    private Long specFlag;

    /**
     * 有无发票
     */
    private Long invoiceFlag;

    /**
     * 订货数量  EXPECTED_QTY
     */
    private BigDecimal expectedQty;

    /**
     * 发货数量
     */
    private BigDecimal shipQty;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private Long province;

    /**
     * 城市
     */
    private Long city;

    /**
     * 区
     */
    private Long county;
    /**
     * 省中文
     */
    private String provinceName;
    /**
     * 市中文
     */
    private String cityName;
    /**
     * 区中文
     */
    private String countyName;

    /**
     * 收货方
     */
    private String consigneeName;

    /**
     * 收货地址
     */
    private String address;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 配送公司Id
     */
    private Long carrierId;

    /**
     * 发票数量
     */
    private Long invoiceQty;

    /**
     * 是否第一次购买
     */
    private String userDeffine1;

    /**
     * 是否已跑波次
     */
    private Long waveFlag;

    /**
     * 用户自定义3
     * 调拨出库时：调入仓库联系人
     */
    private String userDeffine3;

    /**
     * 用户自定义4
     */
    private String userDeffine4;

    /**
     * 期望到货时间1
     */
    private Date expectedArriveTime1;

    /**
     * 期望到货时间2
     */
    private Date expectedArriveTime2;
    /**
     * 备注
     */
    private String notes;
    /**
     * 波次号
     */
    private Long waveId;

    /**
     * 分拣格号
     */
    private String sortGridNo;
    /**
     * 冻结人
     */
    private String holdWho;
    /**
     * 冻结时间
     */
    private Date holdTime;
    /**
     * 冻结原因代码
     */
    private String holdCode;
    /**
     * 冻结原因
     */
    private String holdReason;
    //是否直通出库（走数据）
    private Integer isDirect;
    //批次策略
    private Integer lotStrategy;
    //订单总额
    private BigDecimal orderAmount;
    //货品总额
    private BigDecimal productAmount;
    //已收款
    private BigDecimal amountPayable;
    //总返利金额
    private BigDecimal orderFrostRebate;
    //运费
    private BigDecimal orderDeliveryFee;
    //毛重
    private BigDecimal grossWt;
    //称重毛重
    private BigDecimal totalGrossWt;
    /**
     * 配送方式
     */
    private Integer paymentType;

    /**
     * DO分配总量(所有明细分配数量之和 仅作显示用 不映射数据库字段)
     */
    private BigDecimal totalAllocatEa;
    private Date doCreateTime; //DO创建时间

    /**
     * DO单明细
     */
    private List<DoAllocateDetail> doAllocateDetails;

    private List<InvoiceHeader> invoiceHeaders;

    private WaveHeader waveHeader;
    /**
     * 配送商
     */
    private Carrier carrier;

    private Province provinceInfo;

    private City cityInfo;

    private County countyInfo;
    
    private DoWaveEx doWaveEx;

    //配送站点
    private Station station;

    private Boolean needCancel;

    //应收款
    private BigDecimal receivable;

    // 普通/半日达/一日三送/准时送/指定日期（0/1/2/3/4）
    private Integer isHalfDayDelivery;
    private String origId;//原始单据ID
    private String contact;
    //付款方式
    private String paymentMethodName;
    private String expectedReceiveTime;
    private String edi1;
    private String edi2;
    //退换货标识
    private Integer exchangeFlag;

    //拣货开始时间
    private Date pickStartTime;
    //拣货结束时间
    private Date pickEndTime;
    /**
     * 装箱开始时间
     */
    private Date packStartTime;
    /**
     * 装箱结束时间
     */
    private Date packEndTime;
    //分配时间
    private Date allocTime;
    //分拣时间
    private Date sortTime;

    private Long stationId;

    private Long supplierId;
    /**
     * 补货状态
     */
    private Integer replStatus;
    
    /**
     * 待补货区域Id
     */
    private Long replRegionId;

    /**
     * 体积
     */
    private BigDecimal volume;

    /**
     * 父SO Code
     */
    private String userDeffine5;
    /**
     * 父DO包含子DO数量
     */
    private String userDeffine6;
    
    private Integer skuQty;

    private String email;

    private String exceptionStatus;
    /**
     * 打印时是否显示金额，0：不显示，1：显示
     */
    private Integer displayPrice;
    
    //调拨单类型
    private Integer tranType;
    
    //贵重品标记  null|0: 非贵重   1: 贵重
    private Integer isValuable; 
    
    //生鲜标记  
    private Integer isFresh; 
    
    //订单自动分配失败次数
    private Long allocateFailNum;
    
    //自动波次失败次数
    private Integer waveFailNum;
    
    //订单自动取消分配失败次数
    private Long cancelAllocFailNum;
    
    // 首次预计出库时间
    private Date doFinishTime;
    // 预计出库时间
    private Date planShipTime;

    private Date planShipTimeEnd;
    // 标准预计出库时间
    private Date planShipTimeStd;
    
    // 流程引擎 （超小体积do标识 ）
    private Integer flowFlag;
    
    //销售商家的名字
    private String lastDcName;
    private Integer inWine; //包含酒类随附单
    
    private String aisles;
    /**登记标识，1：合约机订单，2:实名制网卡订单 ，3：选号入网订单*/
    private Integer checkFlag;
    /**团购订单标识(0：否；1：是)*/
    private Integer isGroup; 
    /**是否自动波次标识(0：否；1：是)*/
    private Integer isAutoWave;

    /**
     * 分配任务库区集合 
     */
    private String partitionCodes;

    /**
     * 分配任务库区分组
     */
    private String partitionCategory;
    
    //补货开始时间
    private Timestamp replStartTime;
    
    //补货结束时间
    private Timestamp replEndTime;

    /**是否有处方药(1-有,0-没有)**/
    private Integer haveCfy;
    
    /**
     * 是否需要取消分配
     */
    private Integer needCancelAlloc;

    private Long shopId;
    
    //0：小件 ，1 中件  2 大件 
    private Integer volumeType;
    
    //订单原始编码（比如天猫平台的订单编号）
    private String originalSoCode;
    
    private Integer noStockFlag; //缺货标记
    
    private DeliveryOrderHeader deliveryOrderHeader;

    //商户Id，toB业务
    private Long businessCustomerId;

    private BusinessCustomer businessCustomer;

    private String sellerRemark;

    private String buyerRemark;

    private String orderSubType;

    // 支付时间
    private Date payTime;

    /**
     * 商家ID
     */
    private Long merchantId;

    private Merchant merchant;


    /**
     * 运输温度
     */
    private Integer transportWendy;


    /**
     * 相似度密文
     */
    private String similaritySign;

    /**
     * 渠道编码
     */
    private String channelCode;

    @Column(name = "MERCHANT_ID")
    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "MERCHANT_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    public Merchant getMerchant() {
        return merchant;
    }

    public void setMerchant(Merchant merchant) {
        this.merchant = merchant;
    }
    
    @Column(name = "sku_qty")
    public Integer getSkuQty() {
        return skuQty;
    }
    
    public void setSkuQty(Integer skuQty) {
        this.skuQty = skuQty;
    }
    
    @Column(name = "seller_remark")
    public String getSellerRemark() {
        return sellerRemark;
    }

    public void setSellerRemark(String sellerRemark) {
        this.sellerRemark = sellerRemark;
    }

    @Column(name = "buyer_remark")
    public String getBuyerRemark() {
        return buyerRemark;
    }

    public void setBuyerRemark(String buyerRemark) {
        this.buyerRemark = buyerRemark;
    }

    @Column(name = "SHOP_ID")
    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    @Id
    @Column(name = "ID")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "DO_NO")
    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    @Column(name = "STATUS")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "SHIP_TIME")
    public Date getShipTime() {
        return shipTime;
    }

    public void setShipTime(Date shipTime) {
        this.shipTime = shipTime;
    }

    @Column(name = "DO_TYPE")
    public String getDoType() {
        return doType;
    }

    public void setDoType(String doType) {
        this.doType = doType;
    }
    
    @Column(name = "is_direct")
    public Integer getIsDirect() {
        return isDirect;
    }

    public void setIsDirect(Integer isDirect) {
        this.isDirect = isDirect;
    }
    
    @Column(name = "lot_strategy")
    public Integer getLotStrategy() {
        return lotStrategy;
    }
    
    public void setLotStrategy(Integer lotStrategy) {
        this.lotStrategy = lotStrategy;
    }
    
    @Column(name = "RELEASE_STATUS")
    public String getReleaseStatus() {
        return releaseStatus;
    }

    public void setReleaseStatus(String releaseStatus) {
        this.releaseStatus = releaseStatus;
    }

    @Column(name = "REF_NO1")
    public String getRefNo1() {
        return refNo1;
    }

    public void setRefNo1(String refNo1) {
        this.refNo1 = refNo1;
    }

    @Column(name = "REF_NO2")
    public String getRefNo2() {
        return refNo2;
    }

    public void setRefNo2(String refNo2) {
        this.refNo2 = refNo2;
    }

    @Column(name = "POST_CODE")
    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    @Column(name = "SPEC_FLAG")
    public Long getSpecFlag() {
        return specFlag;
    }

    public void setSpecFlag(Long specFlag) {
        this.specFlag = specFlag;
    }

    @Column(name = "INVOICE_FLAG")
    public Long getInvoiceFlag() {
        return invoiceFlag;
    }

    @Column(name = "original_so_code")
    public void setInvoiceFlag(Long invoiceFlag) {
        this.invoiceFlag = invoiceFlag;
    }

    @Column(name = "EXPECTED_QTY")
    public BigDecimal getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(BigDecimal expectedQty) {
        this.expectedQty = expectedQty;
    }

    @Column(name = "SHIP_QTY")
    public BigDecimal getShipQty() {
        return shipQty;
    }

    public void setShipQty(BigDecimal shipQty) {
        this.shipQty = shipQty;
    }

    @Column(name = "COUNTRY")
    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    @Column(name = "PROVINCE")
    public Long getProvince() {
        return province;
    }

    public void setProvince(Long province) {
        this.province = province;
    }

    @Column(name = "CITY")
    public Long getCity() {
        return city;
    }

    public void setCity(Long city) {
        this.city = city;
    }

    @Column(name = "COUNTY")
    public Long getCounty() {
        return county;
    }

    public void setCounty(Long county) {
        this.county = county;
    }

    @Column(name = "CONSIGNEE_NAME")
    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    @Column(name = "ADDRESS")
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Column(name = "TELEPHONE")
    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    @Column(name = "MOBILE")
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    @Column(name = "CARRIER_ID")
    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    @Column(name = "INVOICE_QTY")
    public Long getInvoiceQty() {
        return invoiceQty;
    }

    public void setInvoiceQty(Long invoiceQty) {
        this.invoiceQty = invoiceQty;
    }

    @Column(name = "USERDEFINE1")
    public String getUserDeffine1() {
        return userDeffine1;
    }

    public void setUserDeffine1(String userDeffine1) {
        this.userDeffine1 = userDeffine1;
    }

    @Column(name = "USERDEFINE3")
    public String getUserDeffine3() {
        return userDeffine3;
    }

    public void setUserDeffine3(String userDeffine3) {
        this.userDeffine3 = userDeffine3;
    }

    @Column(name = "USERDEFINE4")
    public String getUserDeffine4() {
        return userDeffine4;
    }

    public void setUserDeffine4(String userDeffine4) {
        this.userDeffine4 = userDeffine4;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "deliveryOrderHeader")
    @Where(clause = " IS_DELETED = 0 ")
    @Cascade(value = { CascadeType.SAVE_UPDATE })
    public List<InvoiceHeader> getInvoiceHeaders() {
        return invoiceHeaders;
    }

    public void setInvoiceHeaders(List<InvoiceHeader> invoiceHeaders) {
        this.invoiceHeaders = invoiceHeaders;
    }

    @Column(name = "EXPECTED_ARRIVE_TIME1")
    public Date getExpectedArriveTime1() {
        return expectedArriveTime1;
    }

    public void setExpectedArriveTime1(Date expectedArriveTime1) {
        this.expectedArriveTime1 = expectedArriveTime1;
    }

    @Column(name = "EXPECTED_ARRIVE_TIME2")
    public Date getExpectedArriveTime2() {
        return expectedArriveTime2;
    }

    public void setExpectedArriveTime2(Date expectedArriveTime2) {
        this.expectedArriveTime2 = expectedArriveTime2;
    }

    @Column(name = "WAVE_FLAG")
    public Long getWaveFlag() {
        return waveFlag;
    }

    public void setWaveFlag(Long waveFlag) {
        this.waveFlag = waveFlag;
    }

    public void setCarrier(Carrier carrier) {
        this.carrier = carrier;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "WAVE_ID", insertable = false, updatable = false)
    public WaveHeader getWaveHeader() {
        return waveHeader;
    }

    public void setWaveHeader(WaveHeader waveHeader) {
        this.waveHeader = waveHeader;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CARRIER_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    public Carrier getCarrier() {
        return carrier;
    }

    @Column(name = "NOTES")
    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    @Column(name = "WAVE_ID")
    public Long getWaveId() {
        return waveId;
    }

    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PROVINCE", insertable = false, updatable = false)
    public Province getProvinceInfo() {
        return provinceInfo;
    }

    public void setProvinceInfo(Province provinceInfo) {
        this.provinceInfo = provinceInfo;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CITY", insertable = false, updatable = false)
    public City getCityInfo() {
        return cityInfo;
    }

    public void setCityInfo(City cityInfo) {
        this.cityInfo = cityInfo;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "COUNTY", insertable = false, updatable = false)
    public County getCountyInfo() {
        return countyInfo;
    }

    public void setCountyInfo(County countyInfo) {
        this.countyInfo = countyInfo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    @Column(name = "SORT_GRID_NO")
    public String getSortGridNo() {
        return sortGridNo;
    }

    @Column(name = "HOLD_WHO")
    public String getHoldWho() {
        return holdWho;
    }

    public void setHoldWho(String holdWho) {
        this.holdWho = holdWho;
    }

    @Column(name = "HOLD_TIME")
    public Date getHoldTime() {
        return holdTime;
    }

    public void setHoldTime(Date holdTime) {
        this.holdTime = holdTime;
    }

    @Column(name = "HOLD_CODE")
    public String getHoldCode() {
        return holdCode;
    }

    public void setHoldCode(String holdCode) {
        this.holdCode = holdCode;
    }

    @Column(name = "HOLD_REASON")
    public String getHoldReason() {
        return holdReason;
    }

    public void setHoldReason(String holdReason) {
        this.holdReason = holdReason;
    }

    @Column(name = "ORDER_AMOUNT")
    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    @Column(name = "PRODUCT_AMOUNT")
    public BigDecimal getProductAmount() {
        return productAmount;
    }

    public void setProductAmount(BigDecimal productAmount) {
        this.productAmount = productAmount;
    }

    @Column(name = "ACCOUNT_PAYABLE")
    public BigDecimal getAmountPayable() {
        return amountPayable;
    }

    public void setAmountPayable(BigDecimal amountPayable) {
        this.amountPayable = amountPayable;
    }

    @Column(name = "ORDER_FROST_REBATE")
    public BigDecimal getOrderFrostRebate() {
        return orderFrostRebate;
    }

    public void setOrderFrostRebate(BigDecimal orderFrostRebate) {
        this.orderFrostRebate = orderFrostRebate;
    }

    @Column(name = "ORDER_DELIVERY_FEE")
    public BigDecimal getOrderDeliveryFee() {
        return orderDeliveryFee;
    }

    public void setOrderDeliveryFee(BigDecimal orderDeliveryFee) {
        this.orderDeliveryFee = orderDeliveryFee;
    }

    @Column(name = "GROSS_WT")
    public BigDecimal getGrossWt() {
        return grossWt;
    }

    public void setGrossWt(BigDecimal grossWt) {
        this.grossWt = grossWt;
    }

    @Transient
    public BigDecimal getTotalAllocatEa() {
        return totalAllocatEa;
    }

    public void setTotalAllocatEa(BigDecimal totalAllocatEa) {
        this.totalAllocatEa = totalAllocatEa;
    }

    @Column(name = "NEED_CANCEL")
    public Boolean getNeedCancel() {
        return needCancel;
    }

    public void setNeedCancel(Boolean needCancel) {
        this.needCancel = needCancel;
    }

    @Column(name = "RECEIVABLE")
    public BigDecimal getReceivable() {
        return receivable;
    }

    public void setReceivable(BigDecimal receivable) {
        this.receivable = receivable;
    }

    public void setIsHalfDayDelivery(Integer isHalfDayDelivery) {
        this.isHalfDayDelivery = isHalfDayDelivery;
    }

    @Column(name = "IS_HALF_DAY_DELIVERY")
    public Integer getIsHalfDayDelivery() {
        return isHalfDayDelivery;
    }

    /**
     * @return the origId
     */
    @Column(name = "ORIG_ID")
    public String getOrigId() {
        return origId;
    }

    /**
     * @param origId the origId to set
     */
    public void setOrigId(String origId) {
        this.origId = origId;
    }

    @Column(name = "CONTACT")
    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    @Column(name = "EXPECTED_RECEIVE_TIME")
    public String getExpectedReceiveTime() {
        return expectedReceiveTime;
    }

    public void setExpectedReceiveTime(String expectedReceiveTime) {
        this.expectedReceiveTime = expectedReceiveTime;
    }

    @Column(name = "EDI_1")
    public String getEdi1() {
        return edi1;
    }

    public void setEdi1(String edi1) {
        this.edi1 = edi1;
    }

    @Column(name = "EDI_2")
    public String getEdi2() {
        return edi2;
    }

    public void setEdi2(String edi2) {
        this.edi2 = edi2;
    }

    @Column(name = "PAYMENT_TYPE")
    public Integer getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    @Column(name = "PAYMENT_METHOD_NAME")
    public String getPaymentMethodName() {
        return paymentMethodName;
    }

    public void setPaymentMethodName(String paymentMethodName) {
        this.paymentMethodName = paymentMethodName;
    }

    @Column(name = "DO_CREATE_TIME ")
    public Date getDoCreateTime() {
        return doCreateTime;
    }

    public void setDoCreateTime(Date doCreateTime) {
        this.doCreateTime = doCreateTime;
    }

    @Column(name = "EXCHANGE_FLAG")
    public Integer getExchangeFlag() {
        return exchangeFlag;
    }

    public void setExchangeFlag(Integer exchangeFlag) {
        this.exchangeFlag = exchangeFlag;
    }

    @Column(name = "PK_TIME_START")
    public Date getPickStartTime() {
        return pickStartTime;
    }

    public void setPickStartTime(Date pickStartTime) {
        this.pickStartTime = pickStartTime;
    }

    @Column(name = "PK_TIME_END")
    public Date getPickEndTime() {
        return pickEndTime;
    }

    public void setPickEndTime(Date pickEndTime) {
        this.pickEndTime = pickEndTime;
    }

    @Column(name = "pack_time_start")
    public Date getPackStartTime() {
        return packStartTime;
    }

    public void setPackStartTime(Date packStartTime) {
        this.packStartTime = packStartTime;
    }

    @Column(name = "pack_time_end")
    public Date getPackEndTime() {
        return packEndTime;
    }

    public void setPackEndTime(Date packEndTime) {
        this.packEndTime = packEndTime;
    }

    @Column(name = "ALLOC_TIME")
    public Date getAllocTime() {
        return allocTime;
    }

    public void setAllocTime(Date allocTime) {
        this.allocTime = allocTime;
    }

    @Column(name = "SORT_TIME")
    public Date getSortTime() {
        return sortTime;
    }

    public void setSortTime(Date sortTime) {
        this.sortTime = sortTime;
    }

    @Column(name = "STATION_ID")
    public Long getStationId() {
        return stationId;
    }

    public void setStationId(Long stationId) {
        this.stationId = stationId;
    }

    @Column(name = "SUPPLIER_ID")
    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public void setStation(Station station) {
        this.station = station;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "STATION_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    public Station getStation() {
        return station;
    }
    
    @Column(name = "repl_region_id")
    public Long getReplRegionId() {
        return replRegionId;
    }
    
    public void setReplRegionId(Long replRegionId) {
        this.replRegionId = replRegionId;
    }
    
    /**
     * @return 补货状态
     * @see com.daxia.wms.Constants.DoReplStatus
     */
    @Column(name = "REPL_STATUS")
    public Integer getReplStatus() {
        return replStatus;
    }

    public void setReplStatus(Integer replStatus) {
        this.replStatus = replStatus;
    }

    @Column(name = "TOTAL_GROSS_WT")
    public BigDecimal getTotalGrossWt() {
        return totalGrossWt;
    }

    public void setTotalGrossWt(BigDecimal totalGrossWt) {
        this.totalGrossWt = totalGrossWt;
    }

    @Column(name = "VOLUME")
    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    @Column(name = "USERDEFINE5")
    public String getUserDeffine5() {
        return userDeffine5;
    }

    public void setUserDeffine5(String userDeffine5) {
        this.userDeffine5 = userDeffine5;
    }

    @Column(name = "USERDEFINE6")
    public String getUserDeffine6() {
        return userDeffine6;
    }

    public void setUserDeffine6(String userDeffine6) {
        this.userDeffine6 = userDeffine6;
    }

    @Column(name = "EMAIL")
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Column(name = "EXCEPTION_STATUS")
    public String getExceptionStatus() {
        return exceptionStatus;
    }

    public void setExceptionStatus(String exceptionStatus) {
        this.exceptionStatus = exceptionStatus;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "doAllocateHeader")
    @Where(clause = " IS_DELETED = 0 ")
    public List<DoAllocateDetail> getDoAllocateDetails() {
        return doAllocateDetails;
    }

    public void setDoAllocateDetails(List<DoAllocateDetail> doAllocateDetails) {
        this.doAllocateDetails = doAllocateDetails;
    }
    
    @Column(name = "DISPLAY_PRICE")
    public Integer getDisplayPrice() {
		return displayPrice;
	}
	public void setDisplayPrice(Integer displayPrice) {
		this.displayPrice = displayPrice;
	}
	
    @Column(name = "TRAN_TYPE")
    public Integer getTranType() {
        return tranType;
    }
    public void setTranType(Integer tranType) {
        this.tranType = tranType;
    }

    @Column(name = "IS_VALUABLE")
	public Integer getIsValuable() {
		return isValuable;
	}

	public void setIsValuable(Integer isValuable) {
		this.isValuable = isValuable;
	}

	@Column(name = "IS_FRESH")
	public Integer getIsFresh() {
		return isFresh;
	}

	public void setIsFresh(Integer isFresh) {
		this.isFresh = isFresh;
	}

	@Column(name = "ALLOCATE_FAIL_NUM")
    public Long getAllocateFailNum() {
        return allocateFailNum;
    }
    
    public void setAllocateFailNum(Long allocateFailNum) {
        this.allocateFailNum = allocateFailNum;
    }    
    
    @Column(name = "WAVE_FAIL_NUM")
    public Integer getWaveFailNum() {
        return waveFailNum;
    }

    public void setWaveFailNum(Integer waveFailNum) {
        this.waveFailNum = waveFailNum;
    }

    @Column(name = "DO_FINISH_TIME")
    public Date getDoFinishTime() {
        return doFinishTime;
    }
    
    public void setDoFinishTime(Date doFinishTime) {
        this.doFinishTime = doFinishTime;
    }

    @Column(name = "PLAN_SHIP_TIME")
    public Date getPlanShipTime() {
        return planShipTime;
    }

    public void setPlanShipTime(Date planShipTime) {
        this.planShipTime = planShipTime;
    }

    @Column(name = "PLAN_SHIP_TIME_END")
    public Date getPlanShipTimeEnd() {
        return planShipTimeEnd;
    }

    public void setPlanShipTimeEnd(Date planShipTimeEnd) {
        this.planShipTimeEnd = planShipTimeEnd;
    }

    @Column(name = "PLAN_SHIP_TIME_STD")
    public Date getPlanShipTimeStd() {
        return planShipTimeStd;
    }

    public void setPlanShipTimeStd(Date planShipTimeStd) {
        this.planShipTimeStd = planShipTimeStd;
    }  
    
    @Override
    public String toString() {
        return new ToStringBuilder(this).append("doNo", this.getDoNo()).toString();
    }
    
    @Column(name = "FLOW_FLAG")
    public Integer getFlowFlag() {
        return flowFlag;
    }
    
    public void setFlowFlag(Integer flowFlag) {
        this.flowFlag = flowFlag;
    }
    
    @Column(name = "LAST_DC_NAME")
    public String getLastDcName() {
        return lastDcName;
    }

    public void setLastDcName(String lastDcName) {
        this.lastDcName = lastDcName;
    }
    
    @Column(name = "IN_WINE")
	public Integer getInWine() {
		return inWine;
	}

	public void setInWine(Integer inWine) {
		this.inWine = inWine;
	}

	@Column(name = "AISLES")
    public String getAisles() {
        return aisles;
    }

    public void setAisles(String aisles) {
        this.aisles = aisles;
    }  
    
    @Column(name = "CHECK_FLAG")
	public Integer getCheckFlag() {
		return checkFlag;
	}

	public void setCheckFlag(Integer checkFlag) {
		this.checkFlag = checkFlag;
	}
	
	@Column(name = "IS_GROUP")
	public Integer getIsGroup() {
		return isGroup;
	}

	public void setIsGroup(Integer isGroup) {
		this.isGroup = isGroup;
	}

	@Column(name = "IS_AUTO_WAVE")
	public Integer getIsAutoWave() {
		return isAutoWave;
	}

	public void setIsAutoWave(Integer isAutoWave) {
		this.isAutoWave = isAutoWave;
	}
	
	@Column(name = "PARTITION_CODES")
    public String getPartitionCodes() {
		return partitionCodes;
	}

	public void setPartitionCodes(String partitionCodes) {
		this.partitionCodes = partitionCodes;
	}
	
	@Column(name = "PARTITION_CATEGORY")
	public String getPartitionCategory() {
		return partitionCategory;
	}

	public void setPartitionCategory(String partitionCategory) {
		this.partitionCategory = partitionCategory;
	}

	@Column(name = "REPL_START_TIME")
    public Timestamp getReplStartTime() {
        return replStartTime;
    }
    
    public void setReplStartTime(Timestamp replStartTime) {
        this.replStartTime = replStartTime;
    }
    
    @Column(name = "REPL_END_TIME")
    public Timestamp getReplEndTime() {
        return replEndTime;
    }
    
    public void setReplEndTime(Timestamp replEndTime) {
        this.replEndTime = replEndTime;
    }
    
    @Column(name = "HAVE_CFY")
    public Integer getHaveCfy() {
        return haveCfy;
    }

    public void setHaveCfy(Integer haveCfy) {
        this.haveCfy = haveCfy;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID", referencedColumnName = "DO_H_ID", insertable = false, updatable = false)
    public DoWaveEx getDoWaveEx() {
        return doWaveEx;
    }

    public void setDoWaveEx(DoWaveEx doWaveEx) {
        this.doWaveEx = doWaveEx;
    }

    @Column(name = "NEED_CANCEL_ALLOC")
	public Integer getNeedCancelAlloc() {
		return needCancelAlloc;
	}

	public void setNeedCancelAlloc(Integer needCancelAlloc) {
		this.needCancelAlloc = needCancelAlloc;
	}

	@Column(name = "CANCEL_ALLOC_FAIL_NUM")
	public Long getCancelAllocFailNum() {
		return cancelAllocFailNum;
	}

	public void setCancelAllocFailNum(Long cancelAllocFailNum) {
		this.cancelAllocFailNum = cancelAllocFailNum;
	}

	@Column(name = "VOLUME_TYPE")
	public Integer getVolumeType() {
		return volumeType;
	}

	public void setVolumeType(Integer volumeType) {
		this.volumeType = volumeType;
	}

	@Column(name="NO_STOCK_FLAG")
	public Integer getNoStockFlag() {
		return noStockFlag;
	}

	public void setNoStockFlag(Integer noStockFlag) {
		this.noStockFlag = noStockFlag;
	}

	@OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID", referencedColumnName = "ID", insertable = false, updatable = false)
	public DeliveryOrderHeader getDeliveryOrderHeader() {
		return deliveryOrderHeader;
	}

	public void setDeliveryOrderHeader(DeliveryOrderHeader deliveryOrderHeader) {
		this.deliveryOrderHeader = deliveryOrderHeader;
	}

    @Column(name = "business_customer_id")
    public Long getBusinessCustomerId() {
        return businessCustomerId;
    }

    public void setBusinessCustomerId(Long businessCustomerId) {
        this.businessCustomerId = businessCustomerId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "business_customer_id", referencedColumnName = "id", insertable = false, updatable = false)
    public BusinessCustomer getBusinessCustomer() {
        return businessCustomer;
    }

    public void setBusinessCustomer(BusinessCustomer businessCustomer) {
        this.businessCustomer = businessCustomer;
    }

    @Column(name = "original_so_code")
	public String getOriginalSoCode() {
		return originalSoCode;
	}

	public void setOriginalSoCode(String originalSoCode) {
		this.originalSoCode = originalSoCode;
	}
    @Column(name = "province_name")
    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    @Column(name = "city_name")
    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    @Column(name = "county_name")
    public String getCountyName() {
        return countyName;
    }

    public void setCountyName(String countyName) {
        this.countyName = countyName;
    }

    @Column(name = "order_sub_type")
    public String getOrderSubType() {
        return orderSubType;
    }

    public void setOrderSubType(String orderSubType) {
        this.orderSubType = orderSubType;
    }

    @Column(name = "pay_time")
    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public void setCycleClass(Long cycleClass) {
        this.cycleClass = cycleClass;
    }

    @Column(name="CYCLE_CLASS")
    public Long getCycleClass() {
        return cycleClass;
    }

    @Column(name="transport_wendy")
    public Integer getTransportWendy() {
        return transportWendy;
    }

    public void setTransportWendy(Integer transportWendy) {
        this.transportWendy = transportWendy;
    }

    @Column(name="similarity_sign")
    public String getSimilaritySign() {
        return similaritySign;
    }

    public void setSimilaritySign(String similaritySign) {
        this.similaritySign = similaritySign;
    }

    @Column(name="channel_code")
    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }
}