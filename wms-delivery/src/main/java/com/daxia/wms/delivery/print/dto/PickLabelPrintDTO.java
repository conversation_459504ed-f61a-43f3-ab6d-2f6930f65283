package com.daxia.wms.delivery.print.dto;

import java.math.BigDecimal;
import java.util.Date;


/**
 *  拣货标签打印dto
 */
@lombok.extern.slf4j.Slf4j
public class PickLabelPrintDTO {
    /**
     * 拣货单号
     */
    private String pktNo;
    
    /**
     * 拣货单下拣货任务对应的所有库区code
     */
    private String pktPartitionCodes;
    
    /**
     * 波次号
     */
    private String waveNo;
    
    /**
     * 分拣柜号
     */
    private String sortingZoneNo;
    
    /**
     * 拣货区域code
     */
    private String regionCode;

    /**
     * 波次优先级
     */
    private Integer wavePriority;
    
    /**
     * units总数
     */
    private BigDecimal unitsQty;
    
    /**
     * 总重量（毛重）
     */
    private BigDecimal totalGtWeghit;
    
    /**
     * 总体积
     */
    private BigDecimal totalVolume;
    
    /**
     * 波次最早预计出库时间
     */
    private Date planShipTime;
    
    private String carrierName;
    
    /**
     * 零散捡货数量
     */
    private BigDecimal totalQty;
    /**
     * 整箱捡货数量
     */
    private BigDecimal totalQtyUnit;


    private String customerName;
    public String getCarrierName() {
        return carrierName;
    }

    private Integer orderCount;
    private Integer skuTypeCount;
    private Integer skuCount;

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public Integer getSkuTypeCount() {
        return skuTypeCount;
    }

    public void setSkuTypeCount(Integer skuTypeCount) {
        this.skuTypeCount = skuTypeCount;
    }

    public Integer getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName;
    }
    
    public String getPktNo() {
        return pktNo;
    }
    
    public void setPktNo(String pktNo) {
        this.pktNo = pktNo;
    }
    
    public String getPktPartitionCodes() {
        return pktPartitionCodes;
    }
    
    public void setPktPartitionCodes(String pktPartitionCodes) {
        this.pktPartitionCodes = pktPartitionCodes;
    }
    
    public String getWaveNo() {
        return waveNo;
    }
    
    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }
    
    public String getSortingZoneNo() {
        return sortingZoneNo;
    }
    
    public void setSortingZoneNo(String sortingZoneNo) {
        this.sortingZoneNo = sortingZoneNo;
    }
    
    public String getRegionCode() {
        return regionCode;
    }
    
    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }
    
    public Integer getWavePriority() {
        return wavePriority;
    }
    
    public void setWavePriority(Integer wavePriority) {
        this.wavePriority = wavePriority;
    }
    
    public BigDecimal getUnitsQty() {
        return unitsQty;
    }
    
    public void setUnitsQty(BigDecimal unitsQty) {
        this.unitsQty = unitsQty;
    }
    
    public BigDecimal getTotalGtWeghit() {
        return totalGtWeghit;
    }
    
    public void setTotalGtWeghit(BigDecimal totalGtWeghit) {
        this.totalGtWeghit = totalGtWeghit;
    }
    
    public BigDecimal getTotalVolume() {
        return totalVolume;
    }
    
    public void setTotalVolume(BigDecimal totalVolume) {
        this.totalVolume = totalVolume;
    }
    
    public Date getPlanShipTime() {
        return planShipTime;
    }
    
    public void setPlanShipTime(Date planShipTime) {
        this.planShipTime = planShipTime;
    }

    public BigDecimal getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(BigDecimal totalQty) {
        this.totalQty = totalQty;
    }

    public BigDecimal getTotalQtyUnit() {
        return totalQtyUnit;
    }

    public void setTotalQtyUnit(BigDecimal totalQtyUnit) {
        this.totalQtyUnit = totalQtyUnit;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
}
