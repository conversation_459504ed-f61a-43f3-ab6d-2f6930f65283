package com.daxia.wms.delivery.deliveryorder.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import com.daxia.framework.common.util.*;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Keys;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.log.Log;

import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DoCutOffService;
import com.daxia.wms.Constants.DeliveryLimitType;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.master.entity.CutOffTimeDetail;
import com.daxia.wms.master.entity.CutOffTimeHeader;
import com.daxia.wms.master.entity.Station;
import com.daxia.wms.master.service.CutOffTimeService;

@Name("com.daxia.wms.delivery.doCutOffService")
@lombok.extern.slf4j.Slf4j
public class DoCutOffServiceImpl implements DoCutOffService {

    // 异常的时间
    public static final String EXCEPTION_TIME = "00:01";

    // 异常时间天数偏差
    public static final int EXCEPTION_OFFSET = 1;

    public static enum ResultType {
        RIGHT, // 匹配成功
        DETAIL_IS_NOT_MATCH, // 明细不匹配，可以继续匹配该HEADER下其他明细
        HEADER_IS_NOT_MATCH;// 头不匹配
    }

    private static class MatchResult {

        private ResultType resultType;

        private Date date;

        public ResultType getResultType() {
            return resultType;
        }

        public Date getDate() {
            return date;
        }

        public MatchResult(ResultType resultType, Date date) {
            super();
            this.resultType = resultType;
            this.date = date;
        }

        public MatchResult(ResultType resultType) {
            super();
            this.resultType = resultType;
        }
    }

    @In
    CutOffTimeService cutOffTimeService;

    @In
    DoHeaderDAO doHeaderDAO;

    @Logger
    Log logger;

    @Override
    public Date easyCalculate(DeliveryOrderHeader doHeader) {
        Date doCreateTime = NullUtil.notNull(doHeader.getPayTime(), doHeader.getDoCreateTime(), DateUtil.getNowTime());

        Integer defaultTime = SystemConfig.getConfigValueInt(ConfigKeys.IMP_DO_DEFAULT_EXECUTE_TIME, ParamUtil.getCurrentWarehouseId());
        if (defaultTime == null) {
            defaultTime = 3;//默认处理时间为3小时；
        }

        // 订单期望出库时间
        Calendar expectedTime = Calendar.getInstance();
        expectedTime.setTime(doCreateTime);

        //取处理时间后的整点时间，比如：创建时间为：13:05，那么期望时间为：17:00
        if (expectedTime.get(Calendar.MINUTE) > 0 || expectedTime.get(Calendar.SECOND) > 0 || expectedTime.get(Calendar.MILLISECOND) > 0) {
            expectedTime.add(Calendar.HOUR, defaultTime + 1);
            expectedTime.set(Calendar.MINUTE, 0);
            expectedTime.set(Calendar.SECOND, 0);
            expectedTime.set(Calendar.MILLISECOND, 0);
        } else {
            expectedTime.add(Calendar.HOUR, defaultTime);
        }
        return expectedTime.getTime();
    }

    @Override
    public Date calculateCutOffTime(DeliveryOrderHeader doHeader) {
        Date doCreateTime = getImportTime(doHeader);
    
//        "import org.joda.time.DateTime;" +
//                "DateTime dateTime = new DateTime(doHeader.getDoCreateTime());" +
//                "int hour = dateTime.hourOfDay().get();" +
//                "hour = (((Integer)hour/4) + 1) * 4;" +
//                "dateTime = dateTime.withHourOfDay(hour).withSecondOfMinute(0).withMinuteOfHour(0);" + //每4个小时一个波次
//                "return dateTime.toDate();";
        String script = Config.get(Keys.Interface.do_finishTime, Config.ConfigLevel.WAREHOUSE);
        if (StringUtil.isNotEmpty(script)) {
            Map maps = Maps.newHashMap();
            maps.put("doHeader", doHeader);
            return (Date) MvelUtil.eval(script, maps);
        } else {
            return switchTime(EXCEPTION_TIME, doCreateTime, EXCEPTION_OFFSET);
        }
    }

    /**
     * 获取订单导入时间，
     * 
     * @param doHeader
     * @return
     */
    private Date getImportTime(DeliveryOrderHeader doHeader) {
        // 始终订单创建时间
        return doHeader.getDoCreateTime();
    }

    /**
     * 获取匹配的预计出库配置明细，没有匹配的返回null
     * 
     * @param doHeader
     * @param cutOffTimeHeader
     * @param matchHeaders
     * @return
     */
    private Date getMatchDate(DeliveryOrderHeader doHeader, CutOffTimeHeader cutOffTimeHeader,
            Set<CutOffTimeDetail> matchHeaders) {
        List<CutOffTimeDetail> cutOffTimeDetails = cutOffTimeHeader.getDetails();

        // 没有激活
        if (YesNo.NO.getValue().equals(cutOffTimeHeader.getActiveFlag())) {
            return null;
        }

        // 没有明细直接跳过
        if (ListUtil.isNullOrEmpty(cutOffTimeDetails)) {
            return null;
        }

        // 遍历配置明细
        for (CutOffTimeDetail cutOffTimeDetail : cutOffTimeDetails) {
            if (isMatch(doHeader, cutOffTimeDetail)) {
                matchHeaders.add(cutOffTimeDetail);

                MatchResult result = dynamicMatch(doHeader, cutOffTimeDetail, 0);
                if (result.getResultType() == ResultType.RIGHT) {
                    return result.getDate();
                }
                else if (result.getResultType() == ResultType.DETAIL_IS_NOT_MATCH) {
                    // 该明细规则不匹配，继续下条明细
                    continue;
                }
                else if (result.getResultType() == ResultType.HEADER_IS_NOT_MATCH) {
                    // 头规则不匹配，直接掉过循环
                    return null;
                }
            }
        }

        return null;
    }

    /**
     * 规则的动态匹配部分
     * 
     * @param doHeader
     * @param cutOffTimeDetail
     * @param num
     *            第几次匹配该规则，根据num偏移天数，从0开始
     * @return
     */
    private MatchResult dynamicMatch(DeliveryOrderHeader doHeader, CutOffTimeDetail cutOffTimeDetail, int num) {
        Date doCreateTime = getImportTime(doHeader);

        // 订单最短处理截至时间
        Calendar doDealCalendar = Calendar.getInstance();
        doDealCalendar.setTime(doCreateTime);
        doDealCalendar.add(Calendar.MINUTE, Integer.valueOf(cutOffTimeDetail.getDealTime()).intValue());

        Date cutOffTime = null;
        String cutffTimeStr = cutOffTimeDetail.getCutOffTimeHeader().getCutOffTime();
        // 预计出库时间小于订单导入时间，则设置预计出库时间为下一天的同一时间
        if (compareTime(cutffTimeStr, doCreateTime) < 0) {
            cutOffTime = switchTime(cutffTimeStr, doCreateTime, num + 1);
        } else {
            cutOffTime = switchTime(cutffTimeStr, doCreateTime, num);
        }

        MatchResult result = null;

        // 订单导入时间 + 该规则明细处理时间 小于等于 预计出库时间
        if (doDealCalendar.getTime().compareTo(cutOffTime) <= 0) {

            // 预计出库时间运力是否超载
            if (dealIsOverflow(cutOffTimeDetail.getCutOffTimeHeader(), cutOffTime)) {
                result = new MatchResult(ResultType.HEADER_IS_NOT_MATCH);
            } else {
                result = new MatchResult(ResultType.RIGHT, cutOffTime);
            }
        } else {
            result = new MatchResult(ResultType.DETAIL_IS_NOT_MATCH);
        }
        return result;
    }

    /**
     * 运力是否足够
     * 
     * @param cutOffTimeHeader
     * @param cutOffTime
     * @return
     */
    private boolean dealIsOverflow(CutOffTimeHeader cutOffTimeHeader, Date cutOffTime) {
        Long count = doHeaderDAO.countByCutOffTime(cutOffTime, cutOffTimeHeader.getWarehouseId());
        return cutOffTimeHeader.getDealCapacity().longValue() <= count.longValue();
    }

    private boolean isMatch(DeliveryOrderHeader doHeader, CutOffTimeDetail cutOffTimeDetail) {
        // 没有激活，跳过该明细
        if (YesNo.NO.getValue().equals(cutOffTimeDetail.getActiveFlag())) {
            return false;
        }

        // 订单类型是否匹配
        if (StringUtil.isNotEmpty(cutOffTimeDetail.getDoType())
                && !cutOffTimeDetail.getDoType().equals(doHeader.getDoType())) {
            return false;
        }

        // 配送商是否匹配
        if (cutOffTimeDetail.getCarrierId() != null && !cutOffTimeDetail.getCarrierId().equals(doHeader.getCarrierId())) {
            return false;
        }

        // 配送站点是否匹配
        if (cutOffTimeDetail.getStationId() != null && !cutOffTimeDetail.getStationId().equals(doHeader.getStationId())) {
            return false;
        }

        // 限时达类型
        if (cutOffTimeDetail.getOrderLimitType() != null) {
            // 普通订单
            if (DeliveryLimitType.NORMAL.getValue().equals(cutOffTimeDetail.getOrderLimitType())
                    && (doHeader.getIsHalfDayDelivery() == 1 || doHeader.getIsHalfDayDelivery() == 2 || doHeader
                            .getIsHalfDayDelivery() == 3)) {
                return false;
            }
            // 半日达
            if (DeliveryLimitType.HALFDAY.getValue().equals(cutOffTimeDetail.getOrderLimitType())
                    && doHeader.getIsHalfDayDelivery() != 1) {
                return false;
            }
            // 一日三送
            if (DeliveryLimitType.THREETIMES.getValue().equals(cutOffTimeDetail.getOrderLimitType())
                    && doHeader.getIsHalfDayDelivery() != 2) {
                return false;
            }
            // 准时达
            if (DeliveryLimitType.ONTIME.getValue().equals(cutOffTimeDetail.getOrderLimitType())
                    && doHeader.getIsHalfDayDelivery() != 3) {
                return false;
            }
        }

        // 贵重
        if (cutOffTimeDetail.getIsValuable() != null
                && !cutOffTimeDetail.getIsValuable().equals(doHeader.getIsValuable())) {
            return false;
        }

        // 站点支持中午发货
        if (cutOffTimeDetail.getStationLimitType() != null) {
            Station s = doHeader.getStation();
            if (YesNo.YES.getValue().equals(cutOffTimeDetail.getStationLimitType())) {
                // 支持
                if (s == null || s.getDeliveryLimitType() == null || 1 != s.getDeliveryLimitType().intValue()) {
                    return false;
                }
            } else if (YesNo.NO.getValue().equals(cutOffTimeDetail.getStationLimitType())) {
                // 不支持
                if (s != null && s.getDeliveryLimitType() != null && 1 == s.getDeliveryLimitType().intValue()) {
                    return false;
                }
            }
        }

        // 国家
        if (cutOffTimeDetail.getCountry() != null && !cutOffTimeDetail.getCountry().equals(doHeader.getCountry())) {
            return false;
        }

        // 省
        if (cutOffTimeDetail.getProvince() != null && !cutOffTimeDetail.getProvince().equals(doHeader.getProvince())) {
            return false;
        }

        // 市
        if (cutOffTimeDetail.getCity() != null && !cutOffTimeDetail.getCity().equals(doHeader.getCity())) {
            return false;
        }

        // 区
        if (cutOffTimeDetail.getCounty() != null && !cutOffTimeDetail.getCounty().equals(doHeader.getCounty())) {
            return false;
        }

        return true;
    }

    /**
     * 对预计出库时间进行排序，小于导入时间的排列在大于导入时间的后面，例如：{doCreateTime：12:00;cutOffTimes:[8:00,
     * 9: 00,13:00,14:00]} 经过排序如下：cutOffTimes:[13:00,14:00,8:00,9:00]
     * 
     * @param cutOffTimeHeaders
     * @param doCreateTime
     */
    private void sortCutOffTime(List<CutOffTimeHeader> cutOffTimeHeaders, final Date doCreateTime) {
        Collections.sort(cutOffTimeHeaders, new Comparator<CutOffTimeHeader>() {

            @Override
            public int compare(CutOffTimeHeader o1, CutOffTimeHeader o2) {
                String cutOffTime1 = o1.getCutOffTime();
                String cutOffTime2 = o2.getCutOffTime();

                int compareTime1 = compareTime(cutOffTime1, doCreateTime);
                int compareTime2 = compareTime(cutOffTime2, doCreateTime);

                if (compareTime1 > 0 && compareTime2 < 0) {
                    // compareTime1大于导入时间，compareTime2小于导入时间
                    return -1;
                } else if (compareTime1 < 0 && compareTime2 > 0) {
                    return 1;
                } else {
                    return cutOffTime1.compareTo(cutOffTime2);
                }
            }
        });
    }

    /**
     * 对比预计出库时间配置项与订单导入时间
     * 
     * @param cutOffTime
     * @param doCreateTime
     * @return >0:预计出库时间大于订单导入时间；
     */
    private int compareTime(String cutOffTime, Date doCreateTime) {
        String doTimeStr = new SimpleDateFormat("HH:mm").format(doCreateTime);
        return cutOffTime.compareTo(doTimeStr);
    }

    /**
     * 转换预计出库时间为具体的DATE类型
     * 
     * @param cutOffTimeStr
     *            预计出库时间
     * @param doCreateTime
     *            基准时间
     * @param addDate
     *            相对基准时间偏移的天数
     * @return
     */
    private Date switchTime(String cutOffTimeStr, Date doCreateTime, int addDate) {
        // 预计出库时间
        Calendar cutOffCalendar = Calendar.getInstance();
        cutOffCalendar.setTime(doCreateTime);

        if (addDate != 0) {
            cutOffCalendar.add(Calendar.DATE, addDate);
        }
        // 设置预计出库时间的小时分钟
        cutOffCalendar.setTime(DateUtil.valueOf(new SimpleDateFormat("yyyy-MM-dd ").format(cutOffCalendar.getTime()) + cutOffTimeStr, "yyyy-MM-dd HH:mm"));
        return cutOffCalendar.getTime();
    }
}