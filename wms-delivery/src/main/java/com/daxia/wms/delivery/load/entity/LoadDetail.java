package com.daxia.wms.delivery.load.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;

/**
 * 交接单明细实体
 */
@Entity
@Table(name = "doc_load_detail")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = "IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_load_detail set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class LoadDetail extends WhBaseEntity {

    private static final long serialVersionUID = 2923852229065686468L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 交接单头信息主键
     */
    private Long loadHeaderId;
    /**
     * 装箱单头信息主键
     */
    private Long cartonHeaderId;
    /**
    * 箱号
    */
    private String carrierNo;
    /**
    * 发货单头信息主键
    */
    private Long doHeaderId;
    /**
    * 发货单单号
    */
    private String doNo;
    /**
     * 运单号
     */
    private String trackingNo;
    /**
    * 备注
    */
    private String notes;
    
    private String address;//送货地址
    
    private String consigneeName;//送货人
    
    private String provinceCname;
    
    private String cityCname;//送货地址
    
    private String countyCname;//送货人
    private String lpnNo;
     
    private LoadHeader loadHeader;

    private CartonHeader cartonHeader;

    private DeliveryOrderHeader deliveryOrderHeader;

    /**
     * Getter method for property <tt>id</tt>.
     * 
     * @return property value of id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)  
    public Long getId() {
        return id;
    }

    /**
     * Setter method for property <tt>id</tt>.
     * 
     * @param id value to be assigned to property id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter method for property <tt>loadHeaderId</tt>.
     * 
     * @return property value of loadHeaderId
     */
    @Column(name = "LOAD_HEADER_ID")
    public Long getLoadHeaderId() {
        return loadHeaderId;
    }

    /**
     * Setter method for property <tt>loadHeaderId</tt>.
     * 
     * @param loadHeaderId value to be assigned to property loadHeaderId
     */
    public void setLoadHeaderId(Long loadHeaderId) {
        this.loadHeaderId = loadHeaderId;
    }

    /**
     * Getter method for property <tt>cartonHeaderId</tt>.
     * 
     * @return property value of cartonHeaderId
     */
    @Column(name = "CARTON_HEADER_ID")
    public Long getCartonHeaderId() {
        return cartonHeaderId;
    }

    /**
     * Setter method for property <tt>cartonHeaderId</tt>.
     * 
     * @param cartonHeaderId value to be assigned to property cartonHeaderId
     */
    public void setCartonHeaderId(Long cartonHeaderId) {
        this.cartonHeaderId = cartonHeaderId;
    }

    /**
     * Getter method for property <tt>carrierNo</tt>.
     * 
     * @return property value of carrierNo
     */
    @Column(name = "CARRIER_NO")
    public String getCarrierNo() {
        return carrierNo;
    }

    /**
     * Setter method for property <tt>carrierNo</tt>.
     * 
     * @param carrierNo value to be assigned to property carrierNo
     */
    public void setCarrierNo(String carrierNo) {
        this.carrierNo = carrierNo;
    }

    /**
     * Getter method for property <tt>doHeaderId</tt>.
     * 
     * @return property value of doHeaderId
     */
    @Column(name = "DO_HEADER_ID")
    public Long getDoHeaderId() {
        return doHeaderId;
    }

    /**
     * Setter method for property <tt>doHeaderId</tt>.
     * 
     * @param doHeaderId value to be assigned to property doHeaderId
     */
    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }

    /**
     * Getter method for property <tt>doNo</tt>.
     * 
     * @return property value of doNo
     */
    @Column(name = "DO_NO")
    public String getDoNo() {
        return doNo;
    }

    /**
     * Setter method for property <tt>doNo</tt>.
     * 
     * @param doNo value to be assigned to property doNo
     */
    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    /**
     * Getter method for property <tt>notes</tt>.
     * 
     * @return property value of notes
     */
    @Column(name = "NOTES")
    public String getNotes() {
        return notes;
    }

    /**
     * Setter method for property <tt>notes</tt>.
     * 
     * @param notes value to be assigned to property notes
     */
    public void setNotes(String notes) {
        this.notes = notes;
    }

    public void setLoadHeader(LoadHeader loadHeader) {
        this.loadHeader = loadHeader;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "LOAD_HEADER_ID", insertable = false, updatable = false)
    public LoadHeader getLoadHeader() {
        return loadHeader;
    }

    public void setTrackingNo(String trackingNo) {
        this.trackingNo = trackingNo;
    }

    @Column(name = "TRACKING_NO")
    public String getTrackingNo() {
        return trackingNo;
    }

    /**
     * Setter method for property <tt>cartonHeader</tt>.
     * 
     * @param cartonHeader value to be assigned to property cartonHeader
     */
    public void setCartonHeader(CartonHeader cartonHeader) {
        this.cartonHeader = cartonHeader;
    }

    /**
     * Getter method for property <tt>cartonHeader</tt>.
     * 
     * @return property value of cartonHeader
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CARTON_HEADER_ID", insertable = false, updatable = false)
    public CartonHeader getCartonHeader() {
        return cartonHeader;
    }

    /**
     * Setter method for property <tt>deliveryOrderHeader</tt>.
     * 
     * @param deliveryOrderHeader value to be assigned to property deliveryOrderHeader
     */
    public void setDeliveryOrderHeader(DeliveryOrderHeader deliveryOrderHeader) {
        this.deliveryOrderHeader = deliveryOrderHeader;
    }

    /**
     * Getter method for property <tt>deliveryOrderHeader</tt>.
     * 
     * @return property value of deliveryOrderHeader
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DO_HEADER_ID", insertable = false, updatable = false)
    public DeliveryOrderHeader getDeliveryOrderHeader() {
        return deliveryOrderHeader;
    }
    @Transient
    public String getAddress() {
    return address;
    }
    public void setAddress(String address) {
    this.address = address;
    }
    @Transient
    public String getConsigneeName() {
    return consigneeName;
    }
    public void setConsigneeName(String consigneeName) {
    this.consigneeName = consigneeName;
    }
    @Transient
    public String getProvinceCname() {
        return provinceCname;
    }

    public void setProvinceCname(String provinceCname) {
        this.provinceCname = provinceCname;
    }
    @Transient
    public String getCityCname() {
        return cityCname;
    }

    public void setCityCname(String cityCname) {
        this.cityCname = cityCname;
    }
    @Transient
    public String getCountyCname() {
        return countyCname;
    }

    public void setCountyCname(String countyCname) {
        this.countyCname = countyCname;
    }
    @Transient
	public String getLpnNo() {
		return lpnNo;
	}

	public void setLpnNo(String lpnNo) {
		this.lpnNo = lpnNo;
	}
    
}
