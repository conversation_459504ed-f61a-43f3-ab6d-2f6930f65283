package com.daxia.wms.delivery.recheck.service.impl.carton;

import com.daxia.wms.Constants.OrderSource;
import com.daxia.wms.waybill.cainiao.CainiaoConstants.OrderChannelsType;

@lombok.extern.slf4j.Slf4j
public class CainiaoHelper {


    public static String converOrderChannelsType(Integer orderSource) {

        OrderChannelsType orderChannelsType = null;

        if (OrderSource.TMALL.getValue().equals(orderSource)) {
            orderChannelsType = OrderChannelsType.TM;
        } else if (OrderSource.JD.getValue().equals(orderSource)) {
            orderChannelsType = OrderChannelsType.JD;
        } else if (OrderSource.YHD.getValue().equals(orderSource)) {
            orderChannelsType = OrderChannelsType.YHD;
        } else if (OrderSource.GUOMEI.getValue().equals(orderSource)) {
            orderChannelsType = OrderChannelsType.GM;
        } else {
            orderChannelsType = OrderChannelsType.OTHERS;
        }

        return orderChannelsType.name();
    }
}
