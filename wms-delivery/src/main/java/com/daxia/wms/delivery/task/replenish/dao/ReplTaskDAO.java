package com.daxia.wms.delivery.task.replenish.dao;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

import com.daxia.wms.stock.task.entity.TrsTask;
import com.daxia.wms.stock.task.filter.TrsTaskFilter;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishTask;
import com.daxia.wms.Constants.ReplType;
import com.daxia.wms.Constants.TaskStatus;

/**
 * 补货任务持久层
 */
@Name("replTaskDAO")
@lombok.extern.slf4j.Slf4j
public class ReplTaskDAO extends HibernateBaseDAO<ReplenishTask, Long> {
	private static final long serialVersionUID = 4354554905476477552L;

    /**
     * 根据补货任务状态获取补货任务中的商品id的集合
     * @param status
     * @return
     */
	@SuppressWarnings("unchecked")
	public List<Long> findReplTaskSkus(String status) {
        String hql = "select o.skuId from ReplenishTask o where o.taskStatus = :taskStatus and o.warehouseId = :warehouseId ";
        Query query = createQuery(hql);
        query.setString("taskStatus", status);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

	/**
	 * 根据补货任务状态获取补货任务，并按上架顺序升序排序
	 * @param status
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<ReplenishTask> findReplTasks(List<String> status) {
    	String hql = "from ReplenishTask o where o.taskStatus in (:taskStatus) and o.warehouseId = :warehouseId " +
    			     "order by o.planLocation.putawaySeq";
    	Query query = createQuery(hql);
        query.setParameterList("taskStatus", status);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }
	
	/**
	 * 根据商品id及补货任务状态查找补货任务，并按上架顺序升序排列
	 * @param skuId
	 * @param status
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<ReplenishTask> findReplTasks(Long skuId, List<String> status) {
    	String hql = "from ReplenishTask o where o.taskStatus in (:taskStatus) and o.skuId = :skuId and o.warehouseId = :warehouseId " +
    			     "order by o.planLocation.putawaySeq";
    	Query query = createQuery(hql);
    	query.setParameterList("taskStatus", status);
        query.setLong("skuId", skuId);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }
    
    /**
     * 根据托盘号、任务状态、商品id查询补货任务，并按上架顺序升序排序
     * @param lpnNum
     * @param taskStatus
     * @param skuId
     * @return
     */
    @SuppressWarnings("unchecked")
	public List<ReplenishTask> findReplTasks(String lpnNum, List<String> status, Long skuId){
    	String hql = "from ReplenishTask o where o.toLpnNo = :lpnNum and o.skuId = :skuId and o.warehouseId = :warehouseId " +
    			     "and o.taskStatus in (:taskStatus) order by o.planLocation.putawaySeq";
		Query query = createQuery(hql);
		query.setString("lpnNum", lpnNum);
		query.setParameterList("taskStatus", status);
		query.setLong("skuId", skuId);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		return query.list();
    }
    
    /**
     * 修改指定id的拣货任务的目标托盘号
     * @param taskId
     * @param lpnNum
     */
    public void updateReplTaskLpn(Long taskId, String lpnNum){
    	String sql = "update ReplenishTask o set o.toLpnNo = :lpnNum where o.id = :taskId and o.warehouseId = :warehouseId ";
		Query query = createQuery(sql);
		query.setString("lpnNum", lpnNum);
		query.setLong("taskId", taskId);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();
    }
    
    /**
     * 根据补货单号，任务状态查询补货任务
     * @param replNo
     * @param taskStatus
     * @return
     */
    @SuppressWarnings("unchecked")
	public List<ReplenishTask> findReplTasksByReplNoStatus(String replNo, List<String> taskStatus) {
    	Query query = null;
    	StringBuilder sbHql = new StringBuilder("from ReplenishTask o where o.docOperNo = :docOperNo and o.warehouseId = :warehouseId ");
		if (taskStatus != null) {
			if (taskStatus.size() > 1) {
				sbHql.append("and o.taskStatus in (:taskStatus)  order by o.fmLocId ");
				query = this.createQuery(sbHql.toString());
				query.setParameterList("taskStatus", taskStatus);
			} else {
				sbHql.append("and o.taskStatus = :taskStatus   order by o.fmLocId ");
				query = this.createQuery(sbHql.toString());
				query.setParameter("taskStatus", taskStatus.get(0));
			}
		} else {
			sbHql.append("  order by o.fmLocId ");
			query = this.createQuery(sbHql.toString());
		}

		query.setParameter("docOperNo", replNo);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
    	return query.list();
    }
    
    /**
     * 根据中转容器，任务状态获取补货任务
     * @param lpnNo
     * @return
     */
    @SuppressWarnings("unchecked")
	public List<ReplenishTask> findReplTasksByLpnNo(String lpnNo, List<String> taskStatus) {
    	String hql = "from ReplenishTask o where o.toLpnNo = :lpnNo and o.taskStatus in (:taskStatus) and o.warehouseId = :warehouseId ";
    	Query query = this.createQuery(hql);
    	query.setParameter("lpnNo", lpnNo);
    	query.setParameterList("taskStatus", taskStatus);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
    	return query.list();
    }
    
	/**
	 * 根据sku查询所有初始化状态的闲时补货任务总qty
	 */
	public BigDecimal findFreeReplQtyBySku(Long skuId) {
		String sql = "select ifnull(sum(qty), 0) from trs_replenish_task where sku_id = :skuId and task_status = :status "
				+ " and repl_type = :prefix and warehouse_id = :whId ";
		Query query = createSQLQuery(sql);
		query.setParameter("skuId", skuId);
		query.setParameter("status", TaskStatus.INITIALIZED.getValue());
		query.setParameter("prefix", ReplType.XP.getValue());
		query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
		query.setMaxResults(1);
		return (BigDecimal)query.uniqueResult();
	}
	
	public List<BigDecimal> getReleasedJPTask(Long skuId) {
		String sql = "select id from trs_replenish_task where sku_id = :skuId and task_status = :status and is_deleted = 0 and "
				+ "repl_type = :prefix and warehouse_id = :whId";
		Query query = createSQLQuery(sql);
		query.setParameter("skuId", skuId);
		query.setParameter("status", TaskStatus.RELEASED.getValue());
		query.setParameter("prefix", ReplType.JP.getValue());
		query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
		return (List<BigDecimal>)query.list();
	}

	@Override
	public List<ReplenishTask> findByFilter(Serializable filter) {
		//TODO,由于父类没有提供很好的扩展点，此方法无法基于父类方法实现，造成多一条COUNT SQL.
		if (filter instanceof TrsTaskFilter) {
			TrsTaskFilter trsTaskFilter = (TrsTaskFilter) filter;
			String hql = "from ReplenishTask o ";
			String countHql = "select count(o.id) from ReplenishTask o ";
			return (List<ReplenishTask>)this.executeQueryByFilter(hql, countHql, 0, -1, trsTaskFilter).getDataList();
		} else {
			return Collections.EMPTY_LIST;
		}
	}
}