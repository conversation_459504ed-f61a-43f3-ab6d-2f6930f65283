package com.daxia.wms.delivery.load.action;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.load.entity.ReversalHandoverDetail;
import com.daxia.wms.delivery.load.entity.ReversalHandoverHeader;
import com.daxia.wms.delivery.load.filter.ReversalHandoverDetailFilter;
import com.daxia.wms.delivery.load.service.ReversalHandoverDetailService;

/**
 * 逆向交接单Action
 */
@SuppressWarnings("serial")
@Name("com.daxia.wms.delivery.reversalHandoverHeaderAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ReversalHandoverHeaderAction extends PagedListBean<ReversalHandoverDetail> {

	@In
	private ReversalHandoverDetailService reversalHandoverDetailService;

	private ReversalHandoverDetailFilter reversalHandoverDetailFilter;

	private ReversalHandoverHeader reversalHandoverHeader;

	public ReversalHandoverHeaderAction() {
		super();
		reversalHandoverDetailFilter = new ReversalHandoverDetailFilter();
	}

	/**
	 * 分页查询的基础方法 查询ReversalHandoverDetail
	 */
	@Override
    public void query() {
		this.buildOrderFilterMap(reversalHandoverDetailFilter);
		reversalHandoverDetailFilter.getOrderByMap().put("createdAt", "desc");
		DataPage<ReversalHandoverDetail> dataPage = reversalHandoverDetailService.
			findReversalHandoverDetails(reversalHandoverDetailFilter,getStartIndex(), getPageSize());
		this.populateValues(dataPage);
	}

	public ReversalHandoverDetailFilter getReversalHandoverDetailFilter() {
		return reversalHandoverDetailFilter;
	}

	public void setReversalHandoverDetailFilter(ReversalHandoverDetailFilter reversalHandoverDetailFilter) {
		this.reversalHandoverDetailFilter = reversalHandoverDetailFilter;
	}

	public ReversalHandoverHeader getReversalHandoverHeader() {
		return reversalHandoverHeader;
	}

	public void setReversalHandoverHeader(ReversalHandoverHeader reversalHandoverHeader) {
		this.reversalHandoverHeader = reversalHandoverHeader;
	}

}