package com.daxia.wms.delivery.pick.action;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.pick.dto.PathDto;
import com.daxia.wms.delivery.pick.service.PickTaskService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;

@Name("pickPathAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class PickPathAction extends PagedListBean<PathDto> {

	private static final long serialVersionUID = -273969745591193189L;

	private String waveNo;

	@In
	private PickTaskService pickTaskService;
	@In
	private WaveService waveService;

	@Override
	public void query() {
		WaveHeader wave = waveService.getWaveHeaderByWaveNum(waveNo);
		if(null == wave){
			throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
		}
		Map<String, String> pathMap = pickTaskService.getPickPathOfWave(wave.getId());
		Map<String, String> optPathMap = pickTaskService.getOptPickPathOfWave(wave.getId());
		List<PathDto> pathList = new ArrayList<PathDto>();
		for (Entry<String, String> entry : pathMap.entrySet()) {
			PathDto dto = new PathDto();
			dto.setPartition(entry.getKey()+"库区，优化前：");
			dto.setPath(entry.getValue());
			pathList.add(dto);
		}
		List<PathDto> optPathList = new ArrayList<PathDto>();
		for (Entry<String, String> entry : optPathMap.entrySet()) {
			PathDto dto = new PathDto();
			dto.setPartition(entry.getKey()+"库区，优化后：");
			dto.setPath(entry.getValue());
			optPathList.add(dto);
		}
		getDataPage().getDataList().clear();
		getDataPage().getDataList().addAll(pathList);
		getDataPage().getDataList().addAll(optPathList);
	}

	public String getWaveNo() {
		return waveNo;
	}

	public void setWaveNo(String waveNo) {
		this.waveNo = waveNo;
	}

}
