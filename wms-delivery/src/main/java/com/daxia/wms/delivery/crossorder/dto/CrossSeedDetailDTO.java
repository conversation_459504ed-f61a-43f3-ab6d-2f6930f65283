package com.daxia.wms.delivery.crossorder.dto;

import java.math.BigDecimal;

@lombok.extern.slf4j.Slf4j
public class CrossSeedDetailDTO{

    // 主键
    private Long id;

    /**
     * 越库分播单头id
     */
    private Long headerId;

    /**
     * 订单号
     */
    private String doNo;

    /**
     * 分播区格号
     */
    private String sortGridNo;

    /**
     * 分播区每个DO的容器号
     */
    private String containerNo;

    /**
     * 客户名
     */
    private String consigneeName;

    /**
     * 批次号
     */
    private String lotNo;

    /**
     * 批号
     */
    private String lotatt05;

    /**
     * 生产日期
     */
    private String lotatt01;

    /**
     * 有效期至
     */
    private String lotatt02;

    /**
     * 期望数
     */
    private BigDecimal expectedQty;

    /**
     * 预分播数量
     */
    private BigDecimal allocatedQty;

    /**
     * 实际分播数量
     */
    private BigDecimal sortedQty;

    /**
     * 实际复核数
     */
    private BigDecimal packedQty;

    private String productCode;

    private String ean13;

    private String productCname;

    private String udf4;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getHeaderId() {
        return headerId;
    }

    public void setHeaderId(Long headerId) {
        this.headerId = headerId;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    public String getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public String getLotNo() {
        return lotNo;
    }

    public void setLotNo(String lotNo) {
        this.lotNo = lotNo;
    }

    public String getLotatt05() {
        return lotatt05;
    }

    public void setLotatt05(String lotatt05) {
        this.lotatt05 = lotatt05;
    }

    public String getLotatt01() {
        return lotatt01;
    }

    public void setLotatt01(String lotatt01) {
        this.lotatt01 = lotatt01;
    }

    public String getLotatt02() {
        return lotatt02;
    }

    public void setLotatt02(String lotatt02) {
        this.lotatt02 = lotatt02;
    }

    public BigDecimal getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(BigDecimal expectedQty) {
        this.expectedQty = expectedQty;
    }

    public BigDecimal getAllocatedQty() {
        return allocatedQty;
    }

    public void setAllocatedQty(BigDecimal allocatedQty) {
        this.allocatedQty = allocatedQty;
    }

    public BigDecimal getSortedQty() {
        return sortedQty;
    }

    public void setSortedQty(BigDecimal sortedQty) {
        this.sortedQty = sortedQty;
    }

    public BigDecimal getPackedQty() {
        return packedQty;
    }

    public void setPackedQty(BigDecimal packedQty) {
        this.packedQty = packedQty;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getEan13() {
        return ean13;
    }

    public void setEan13(String ean13) {
        this.ean13 = ean13;
    }

    public String getProductCname() {
        return productCname;
    }

    public void setProductCname(String productCname) {
        this.productCname = productCname;
    }

    public String getUdf4() {
        return udf4;
    }

    public void setUdf4(String udf4) {
        this.udf4 = udf4;
    }

    public CrossSeedDetailDTO(Long id,String doNo, String sortGridNo, String containerNo, String consigneeName, String lotNo, String lotatt05, String lotatt01, String lotatt02, BigDecimal expectedQty, BigDecimal allocatedQty, BigDecimal sortedQty, BigDecimal packedQty, String productCode, String ean13, String productCname, String udf4) {
        this.id = id;
        this.doNo = doNo;
        this.sortGridNo = sortGridNo;
        this.containerNo = containerNo;
        this.consigneeName = consigneeName;
        this.lotNo = lotNo;
        this.lotatt05 = lotatt05;
        this.lotatt01 = lotatt01;
        this.lotatt02 = lotatt02;
        this.expectedQty = expectedQty;
        this.allocatedQty = allocatedQty;
        this.sortedQty = sortedQty;
        this.packedQty = packedQty;
        this.productCode = productCode;
        this.ean13 = ean13;
        this.productCname = productCname;
        this.udf4 = udf4;
    }
}