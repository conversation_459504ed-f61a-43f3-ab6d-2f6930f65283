package com.daxia.wms.delivery.load.action;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.security.Restrict;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.load.entity.LoadDetail;
import com.daxia.wms.delivery.load.entity.LoadHeader;
import com.daxia.wms.delivery.load.filter.LoadDetailFilter;
import com.daxia.wms.delivery.load.service.LoadService;

@Name("com.daxia.wms.delivery.loadDetailAction")
@Restrict("#{identity.hasPermission('delivery.entruckNew')}")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class LoadDetailAction extends PagedListBean<LoadDetail>{
	private static final long serialVersionUID = 5589916774874216214L;

	@In
	private LoadService loadService;
    
	private LoadDetailFilter loadDetailFilter;
	
	private Long cartonId;
	
	private Long loadHeaderId;
	
	private LoadHeader loadHeader;
	
    public LoadDetailAction() {
        super();
        this.loadDetailFilter = new LoadDetailFilter();
    }
    
    public void warehouseLoadingDetail() {
    	this.loadHeader = loadService.getLoadHeader(loadDetailFilter.getLoadHeaderId());
    }
    
    /**
     * 页面查询方法
     */
    @Override
	public void query() {
    	loadDetailFilter.getOrderByMap().put("createdAt", "desc");
		this.buildOrderFilterMap(loadDetailFilter);
		DataPage<LoadDetail> dataPage = loadService.queryDetail(loadDetailFilter,
				getStartIndex(), getPageSize());
		this.populateValues(dataPage);
	}
    
    /**
     * 取消装车
     */
    public void removeCarton() {
    	if (cartonId == null || loadHeaderId == null) {
    		return;
    	}
    	loadService.removeCartonFromLoadHeader(cartonId, loadHeaderId);
    }

	public LoadDetailFilter getLoadDetailFilter() {
		return loadDetailFilter;
	}

	public void setLoadDetailFilter(LoadDetailFilter loadDetailFilter) {
		this.loadDetailFilter = loadDetailFilter;
	}

	public Long getCartonId() {
		return cartonId;
	}

	public void setCartonId(Long cartonId) {
		this.cartonId = cartonId;
	}

	public Long getLoadHeaderId() {
		return loadHeaderId;
	}

	public void setLoadHeaderId(Long loadHeaderId) {
		this.loadHeaderId = loadHeaderId;
	}

	public LoadHeader getLoadHeader() {
		return loadHeader;
	}

	public void setLoadHeader(LoadHeader loadHeader) {
		this.loadHeader = loadHeader;
	}
}