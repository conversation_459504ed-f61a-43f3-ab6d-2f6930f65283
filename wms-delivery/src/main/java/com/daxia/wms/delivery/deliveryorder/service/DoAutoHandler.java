package com.daxia.wms.delivery.deliveryorder.service;

import org.hibernate.Session;
import org.jboss.seam.Component;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.log.Log;

import com.daxia.wms.delivery.DeliveryException;

public abstract class DoAutoHandler {
	protected DoAutoHandler nextHandler;
	protected String name;
	@Logger
    protected Log log;

	public String getName() {
		return name;
	}
	
	public void handleDo(Long doId) {
		setName();
		log.debug(getName(), doId);
		
		try {
			handle(doId);
		} catch (DeliveryException e) {
			log.error(getName() + doId, e);
		}
		
		if (nextHandler != null) {
			Session session = (Session) Component.getInstance("hibernateSession");
			session.flush();
			session.clear();
			nextHandler.handleDo(doId);
		}
	}

	// 设置下一个处理请求的人
	public void setNextHandler(DoAutoHandler nextHandler) {
		this.nextHandler = nextHandler;
	}
	
	// 处理请求，由子类完成
	protected abstract void handle(Long doId);
	
	// 设置节点名称
	protected abstract void setName();
}
