package com.daxia.wms.delivery.deliveryorder.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;

/**
 * 发货单头扩展实体
 */
@Entity
@Table(name = "doc_do_reservation_ex")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_do_reservation_ex set is_deleted = 1 where id = ? and version = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class DoReservationEx extends WhBaseEntity {
	
	private static final long serialVersionUID = -8894722359616209501L;

	private Long id;
	
	/**
	 * 发货单头ID
	 */
	private Long doHeaderId;

	/**
	 * 免租日期
	 */
	private Date freeRentDate;	
	/**
	 * 过期日期
	 */
	private Date expirationDate;

	@Id
    @Column(name = "ID")
	@GeneratedValue(strategy = GenerationType.AUTO)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "DO_H_ID")
	public Long getDoHeaderId() {
		return doHeaderId;
	}

	public void setDoHeaderId(Long doHeaderId) {
		this.doHeaderId = doHeaderId;
	}

	@Column(name = "FREE_RENT_DATE")
	public Date getFreeRentDate() {
		return freeRentDate;
	}

	public void setFreeRentDate(Date freeRentDate) {
		this.freeRentDate = freeRentDate;
	}

	@Column(name = "EXPIRATION_DATE")
	public Date getExpirationDate() {
		return expirationDate;
	}

	public void setExpirationDate(Date expirationDate) {
		this.expirationDate = expirationDate;
	}
}