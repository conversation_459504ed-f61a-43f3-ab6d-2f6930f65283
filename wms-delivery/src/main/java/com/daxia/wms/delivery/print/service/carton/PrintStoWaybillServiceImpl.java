package com.daxia.wms.delivery.print.service.carton;

import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.delivery.deliveryorder.service.DoWaveExService;
import com.daxia.wms.delivery.print.dto.BaseCartonPrintDTO;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.helper.CurrencyConverter;
import com.daxia.wms.delivery.print.helper.PrintCartonHelper;
import com.daxia.wms.delivery.print.helper.SFPrintCartonHelper;
import com.daxia.wms.delivery.print.helper.WaybillPrintHelper;
import com.daxia.wms.delivery.print.service.PrintWaybillService;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.print.dto.PrintCfg;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.print.utils.PrintHelper;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.List;

@Name("printStoWaybillService")
@lombok.extern.slf4j.Slf4j
public class PrintStoWaybillServiceImpl extends AbstractPrintWaybillService implements PrintWaybillService {
    @In
    DoWaveExService doWaveExService;
    @In
    WarehouseService warehouseService;
    @In
    WarehouseCarrierService warehouseCarrierService;

    @Create
    public void init () {
        this.setWaybillType(Constants.WaybillType.STO);
    }


    @Override
    protected PrintData genPrintData(List<PrintReportDto> dtoList, DeliveryOrderHeader doHeader) {
        PrintData printData = new PrintData();
        printData.setDtoList(dtoList);
        printData.setPrintCfg(new PrintCfg("wayBillSto", "100", "150"));
        return printData;
    }

    @Override
    protected void buildDTO(List<PrintReportDto> printReportDtos, DeliveryOrderHeader doHeader, BaseCartonPrintDTO carton, int index, int count) {
        CartonPrintDTO cartonPrintDTO = new CartonPrintDTO();
        cartonPrintDTO.setIsPrinted(carton.getIsPrinted());
        cartonPrintDTO.setCartonId(carton.getId());
        cartonPrintDTO.setCartonNo(carton.getCartonNo());
        cartonPrintDTO.setDoNo(doHeader.getDoNo());
        cartonPrintDTO.setOutRefNo(doHeader.getRefNo1());
        cartonPrintDTO.setIsCOD(PrintCartonHelper.isCOD(doHeader));
        cartonPrintDTO.setSortGridNo(doHeader.getSortGridNo());
        cartonPrintDTO.setWaveNo(doHeader.getWaveHeader().getWaveNo());
        cartonPrintDTO.setOriginalSoCode(doHeader.getOriginalSoCode());
        // 设置收货人地址
        cartonPrintDTO.setClientProvinceAndCityAndCountyAddress(
                SFPrintCartonHelper.buildProvinceAndCityAndCountyAddress(doHeader, ""));
        cartonPrintDTO.setClientAddress(StringUtil.notNullString(doHeader.getAddress()));
        cartonPrintDTO.setClientName(doHeader.getConsigneeName());
        cartonPrintDTO.setClientPhone(PrintCartonHelper.buildTelOrMobile(doHeader));
        // 设置寄件人地址信息
        setSendAddressInfo(doHeader,cartonPrintDTO);
        //SFPrintCartonHelper.getCityCname(doHeader) + " " +
        cartonPrintDTO.setShortAddress(SFPrintCartonHelper
                .getCountyCname(doHeader));
        // 运单号
        cartonPrintDTO.setWayBill(carton.getCartonNo());
        cartonPrintDTO.setCarrier128Code("128B");
        // 是否需要待收货款
        boolean receivable = doHeader.getReceivable() != null && doHeader.getReceivable().compareTo(BigDecimal.ZERO) > 0;
        cartonPrintDTO.setNeedReceivable(receivable);
        if (receivable) {
            // 设置代收金额
            cartonPrintDTO.setServiceCodAmount(doHeader.getReceivable().toString());
            cartonPrintDTO.setCodCurrency(CurrencyConverter.convert(doHeader.getReceivable().toString()));
            cartonPrintDTO.setProductSalesType("COD");
        }
        DoWaveEx doWaveEx = doWaveExService.findByDoHeaderId(doHeader.getId());
        if (null != doWaveEx) {
            cartonPrintDTO.setStartAddressCode(doWaveEx.getOriginCode());
            cartonPrintDTO.setDestAddressCode(doWaveEx.getDestinationCode());
            cartonPrintDTO.setMainNo(doWaveEx.getTrackingNo());
        }
        // 设置图片路径
        cartonPrintDTO.setBasePrintImgPath(PrintHelper.getBasePrintImgPath());

        cartonPrintDTO.setRefNo1(StringUtil.notNullString(doHeader.getOriginalSoCode(), ""));

        String weight = "";
        if (carton.getActualGrossWeight() != null && carton.getActualGrossWeight().compareTo(BigDecimal.ZERO) > 0) {
            weight = carton.getActualGrossWeight().setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        }
        cartonPrintDTO.setWeight(weight);
        if (doHeader.getExpectedQty() != null) {
            cartonPrintDTO.setNumber(doHeader.getExpectedQty().toString());
        }
        setProductInfo(doHeader,cartonPrintDTO);
        printReportDtos.add(cartonPrintDTO);
    }
}
