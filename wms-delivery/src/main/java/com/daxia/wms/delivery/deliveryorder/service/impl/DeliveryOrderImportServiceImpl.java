package com.daxia.wms.delivery.deliveryorder.service.impl;

import com.daxia.framework.common.util.BusinessException;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.ResourceUtils;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.framework.system.util.DataList;
import com.daxia.framework.system.util.ExcelUtil;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.DoType;
import com.daxia.wms.Constants.SequenceName;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderImportService;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.master.entity.AllocationRuleHeader;
import com.daxia.wms.master.entity.Sku;
import com.daxia.wms.master.service.AllocationRuleHeaderService;
import com.daxia.wms.master.service.ProductBarcodeService;
import com.daxia.wms.master.service.SkuService;
import com.daxia.wms.stock.stock.service.StockBatchAttService;
import jxl.Cell;
import jxl.Sheet;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.log.Log;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
@Name("deliveryOrderImportService")
@lombok.extern.slf4j.Slf4j
public class DeliveryOrderImportServiceImpl implements DeliveryOrderImportService {

	/**
	 *  明细上面有8行
	 */
	private final int HEADER_LINE_NUM = 8;
	/**
	 *  PO号
	 */
	private final int PO_LINE = 0;
	/**
	 *  收货人
	 */
	private final int RECEIVER_LINE = 1;
	/**
	 *  地址
	 */
	private final int ADDRESS_LINE = 2;
	/**
	 *  邮编
	 */
	private final int POST_NUM_LINE = 3;
	/**
	 *  电话
	 */
	private final int PHONE_LINE = 4;
	/**
	 *  手机
	 */
	private final int TEL_LINE = 5;
	/**
	 *  商品编码
	 */
	private final int PRODUCT_CELL = 0;
	/**
	 *  商品条码
	 */
	private final int BARCODE_CELL = 1;
	/**
	 *  贵重品
	 */
	private final int VALUABLE_CELL = 4;
	/**
	 *  数量
	 */
	private final int QTY_CELL = 5;
	
	private final int HEADER_CONTENT_INDEX = 1;
	
	private final Integer maxRowSize = 500;
	
	@In
	private SkuService skuService;
	
	@In
	private DeliveryOrderService deliveryOrderService;
	
	@In
	private StockBatchAttService stockBatchAttService;
	
	@In
	private ProductBarcodeService productBarcodeService;
	
	@In
	private AllocationRuleHeaderService allocationRuleHeaderService;
	
    @In 
    private SequenceGeneratorService sequenceGeneratorService;
    
    @Logger
    Log logger;
	
	@Override
	public Object importDo(List<String> fileList) {
        for (String file : fileList) {
            Sheet[] sheets = ExcelUtil.getExcelSheetByName(file);
            return importFromSheet(sheets[0]);
        }
        return null;
	}
	
	private Set<String> importFromSheet(Sheet sheet) {
		Set<String> errorSet = new HashSet<String>();
		// 验证整个excel
		validate(sheet);
		
		// 导入doHeader
		Long doId = importHeader(sheet);
		
		// 导入detail
		importDetail(sheet, doId, errorSet);
		
		// 如果有异常，则删除本do相关信息
		if (!errorSet.isEmpty()) {
			deliveryOrderService.deleteDo(doId);
		}
        return errorSet;
    }
	
	
	/**
	 * 验证整个sheet
	 */
	private void validate(Sheet sheet) {
        int rowSize = sheet.getRows();

        if (rowSize > (maxRowSize + 8)) {
        	throw new DeliveryException(DeliveryException.UPLOAD_EXL_ROW_IS_GREATER, maxRowSize);
        }
	}
	
	/**
	 * 导入do头
	 */
	private Long importHeader(Sheet sheet) {
		validateHeader(sheet);
		
		return saveHeader(sheet);
	}
	
	/**
	 *  验证excel中订单头的输入信息
	 */
	private void validateHeader(Sheet sheet) {
		Cell[] poCells = sheet.getRow(PO_LINE);
		String poNum = getCellContent(poCells, HEADER_CONTENT_INDEX);
		if (StringUtil.isEmpty(poNum)) {
			throw new DeliveryException(DeliveryException.IMPORT_NO_PO);
		}
		
		Cell[] reveiverCells = sheet.getRow(RECEIVER_LINE);
		String reveiver = getCellContent(reveiverCells, HEADER_CONTENT_INDEX);
		if (StringUtil.isEmpty(reveiver)) {
			throw new DeliveryException(DeliveryException.IMPORT_NO_RECEIVER);
		}
		
		Cell[] addressCells = sheet.getRow(ADDRESS_LINE);
		String address = getCellContent(addressCells, HEADER_CONTENT_INDEX);
		if (StringUtil.isEmpty(address)) {
			throw new DeliveryException(DeliveryException.IMPORT_NO_ADDRESS);
		}
		
		Cell[] phoneCells = sheet.getRow(PHONE_LINE);
		Cell[] telCells =   sheet.getRow(TEL_LINE);
		String phone = getCellContent(phoneCells, HEADER_CONTENT_INDEX);
		String tel = getCellContent(telCells, HEADER_CONTENT_INDEX);
		if (StringUtil.isEmpty(phone) && StringUtil.isEmpty(tel)) {
			throw new DeliveryException(DeliveryException.IMPORT_NO_TEL_PHO);
		}
		
//		// 校验该PO是否已存在
//		if (deliveryOrderService.isExistsDoWithPoNum(poNum)) {
//			throw new DeliveryException(DeliveryException.IMPORT_PO_EXISTS);
//		}
	}
	
	/**
	 * 保存订单头和分配头
	 */
	private Long saveHeader(Sheet sheet) {
		Cell[] poCells =       sheet.getRow(PO_LINE);
		Cell[] reveiverCells = sheet.getRow(RECEIVER_LINE);
		Cell[] addressCells =  sheet.getRow(ADDRESS_LINE);
		Cell[] postCells =     sheet.getRow(POST_NUM_LINE);
		Cell[] phoneCells =    sheet.getRow(PHONE_LINE);
		Cell[] telCells =      sheet.getRow(TEL_LINE);
		
		String poNum =    getCellContent(poCells, HEADER_CONTENT_INDEX);
		String reveiver = getCellContent(reveiverCells, HEADER_CONTENT_INDEX);
		String address =  getCellContent(addressCells, HEADER_CONTENT_INDEX);
		String postNum =  getCellContent(postCells, HEADER_CONTENT_INDEX);
		String phone =    getCellContent(phoneCells, HEADER_CONTENT_INDEX);
		String tel =      getCellContent(telCells, HEADER_CONTENT_INDEX);
		String doNum =    sequenceGeneratorService.generateSequenceNo(
				SequenceName.DO_NO.getValue(), ParamUtil.getCurrentWarehouseId());
		
		DoHeaderDto doHeaderDto = new DoHeaderDto();
		doHeaderDto.setRefNo1(poNum);
		doHeaderDto.setConsigneeName(reveiver);
		doHeaderDto.setAddress(address);
		doHeaderDto.setPostCode(postNum);
		doHeaderDto.setTelephone(DoUtil.encryptPhone(phone));
		doHeaderDto.setMobile(DoUtil.encryptPhone(tel));
		doHeaderDto.setDoNo(doNum);
		
		Long headerId =  deliveryOrderService.saveDoHeader(doHeaderDto);
		
//		// 插入完成后，验证是否并发都插入成功了。如果并发都成功了则删除本DO
//		if (deliveryOrderService.isExistsMultiDoWithPoNum(poNum)) {
//			deliveryOrderService.deleteDo(headerId);
//			throw new DeliveryException(DeliveryException.IMPORT_NO_RESULT);
//		}
		
		return headerId;
	}
	
	/**
	 * 验证excel中订单明细的输入，并返回条码对应的sku id
	 */
	private Long validateDetail(Cell[] cells) {
		String productcode = getCellContent(cells, PRODUCT_CELL);
		String barcode = getCellContent(cells, BARCODE_CELL);
		if (StringUtil.isEmpty(productcode) || 
				StringUtil.isEmpty(barcode)) {
			throw new DeliveryException(DeliveryException.IMPORT_NO_BARCODE);
		}
		Sku sku = skuService.getSkuByCodeAndBarcode(productcode.trim(), barcode.trim());
		if (sku == null) {
			throw new DeliveryException(DeliveryException.IMPORT_BARCODE_NOT_EXISTS);
		}

		String qty = getCellContent(cells, QTY_CELL);
		if (StringUtil.isEmpty(qty)
				|| Long.valueOf(qty.trim()).compareTo(0L) <= 0) {
			throw new DeliveryException(DeliveryException.IMPORT_QTY_WRONG);
		}
		
		return sku.getId();
	}
	
	/**
	 *  导入do明细和分配明细
	 */
	private void importDetail(Sheet sheet, Long doId, Set<String> errorSet) {
        int rowSize = sheet.getRows();
        DataList dataList = new DataList(1000, maxRowSize, 3);
        String whId = ParamUtil.getCurrentWarehouseId().toString();
        String merchantId = stockBatchAttService.getDefaultMerchantId();
        BigDecimal totalQty = BigDecimal.ZERO;
        boolean isValuableFlag = false;
        for (int i = HEADER_LINE_NUM; i < rowSize; i++) {
        	try {
                Cell[] cells = sheet.getRow(i);
                Long skuId = validateDetail(cells);
                Sku sku = skuService.getSkuById(skuId);
                AllocationRuleHeader allocationRuleHeader = allocationRuleHeaderService.getMatchAllocationRule
                		(sku.getAllocationRuleId(), DoType.SELL.getValue(), null);
                if (allocationRuleHeader == null) {
                	throw new DeliveryException(DeliveryException.ALLOCATE_NO_RULE_ERROR);
                }
                
        		String isValuable = getCellContent(cells, VALUABLE_CELL);
        		String qty        = getCellContent(cells, QTY_CELL).trim();
        		String valuable   = "是".equals(isValuable.trim())? YesNo.YES.getValue().toString() : YesNo.NO.getValue().toString();
        		if (YesNo.YES.getValue().toString().equals(valuable)) {
        			isValuableFlag = true;
        		}
                List<String> insertList = new ArrayList<String>();
                insertList.add(doId.toString());
                insertList.add(DoStatus.INITIAL.getValue());
                insertList.add(skuId.toString());
                insertList.add(qty);
//                insertList.add(qty);
                insertList.add(merchantId);
                insertList.add(valuable);
                insertList.add(allocationRuleHeader.getId().toString());
                insertList.add(ParamUtil.getCurrentLoginName());
                insertList.add(ParamUtil.getCurrentLoginName());
                insertList.add(whId);
                insertList.add(doId.toString());
                insertList.add(whId);
                dataList.addInsertData(insertList);
                totalQty = totalQty.add(new BigDecimal(qty));
                if (errorSet.isEmpty() && (dataList.isBatchEnough() || i == rowSize - 1)) {
                	deliveryOrderService.saveDoDetail(doId, dataList, rowSize - 8);
                }
			} catch (Exception e) {
				if (e instanceof BusinessException) {
					errorSet.add("excel 第" + (i + 1) + "行  " + 
							ResourceUtils.getDispalyString(e.getMessage(), e.getClass().getName(),
					                ((BusinessException) e).getParams()));
				} else {
					errorSet.add("excel 第" + (i + 1) + "行  " + e.toString());
					log.error("line " + (i + 1), e);
				}
			}
        	if (errorSet.isEmpty()) {
        		deliveryOrderService.updateHeaderTotalQtyAndValuable(doId, totalQty, isValuableFlag);
        	}
        }
	}
	
	private String getCellContent(Cell[] cells, int index) {
		return cells.length > index? cells[index].getContents() : "";
	}
}
