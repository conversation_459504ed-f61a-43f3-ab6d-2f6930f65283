package com.daxia.wms.delivery.invoice.service.impl;

import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.InvoiceNoStatus;
import com.daxia.wms.Constants.InvoicePrintFlg;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.delivery.deliveryorder.dao.PrintInvoiceDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.invoice.dao.InvoiceNoDAO;
import com.daxia.wms.delivery.invoice.dto.Invoice4JinrenDTO;
import com.daxia.wms.delivery.invoice.entity.InvoiceDetail;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;
import com.daxia.wms.delivery.invoice.job.JinrenDBOperate;
import com.daxia.wms.delivery.invoice.service.InvoiceJinrenService;
import com.daxia.wms.delivery.invoice.service.InvoiceNoService;
import com.daxia.wms.delivery.invoice.service.InvoiceService;
import com.daxia.wms.delivery.print.dto.DoInvoicePrintDTO;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Name("com.daxia.wms.delivery.invoiceJinrenService")
@lombok.extern.slf4j.Slf4j
public class InvoiceJinrenServiceImpl implements InvoiceJinrenService {
    @In
    private WaveService waveService ;
    
    @In
    private InvoiceNoService invoiceNoService;
    
    @In
    private InvoiceService invoiceService;
    
    @In
    private JinrenDBOperate jinrenDBOperate;
    @In
    private InvoiceNoDAO invoiceNoDAO;
    @In
    private PrintInvoiceDAO printInvoiceDAO;
    
	@Override
	public void writeJinrenInvoice(WaveHeader initialWaveHeader) {
		if(YesNo.NO.getValue().equals(initialWaveHeader.getInvoiceFlag())){
			return;
		}
		List<Invoice4JinrenDTO> jinrenDTOs = new ArrayList<Invoice4JinrenDTO>();
		//插入前删除发票头下的发票。
		List<String> invoiceHeaderIds = new ArrayList<String>();
		// 查询do
		List<DeliveryOrderHeader> doHeaders = initialWaveHeader.getDoHeaders();
		String strShopIds = SystemConfig.getConfigValue("invoiceJinren.shopIds", initialWaveHeader.getWarehouseId());
		for (DeliveryOrderHeader doHeader : doHeaders) {
			if (YesNo.YES.getValue().equals(doHeader.getInvoiceFlag().intValue())) {
				List<String> shopIds = StringUtil.splitToList(strShopIds,",");
				if(doHeader.getShopId() == null || ListUtil.isNullOrEmpty(shopIds) || (!shopIds.contains(doHeader.getShopId().toString()))){
					continue;
				}
				List<InvoiceHeader> invoiceHeaders = doHeader
						.getInvoiceHeaders();
				for (InvoiceHeader invoiceHeader : invoiceHeaders) {
					if (StringUtil.isEmpty(invoiceHeader.getInvoiceNumber())) {
						invoiceHeaderIds.add(invoiceHeader.getId().toString());
						List<InvoiceDetail> invoiceDetails = invoiceService
								.findInvoiceDetailsByHeaderId(invoiceHeader.getId());
						for (InvoiceDetail invoiceDetail : invoiceDetails) {
							Invoice4JinrenDTO jinrenDTO = new Invoice4JinrenDTO();
							jinrenDTO.setDocNo(invoiceHeader.getId() == null ? null
											: invoiceHeader.getId().toString());
							jinrenDTO.setTitle(invoiceHeader.getInvoiceTitle());
							jinrenDTO.setProductName(invoiceDetail
									.getSkuDescr());
							jinrenDTO.setSkuType(invoiceDetail.getSkuType());
							jinrenDTO.setUnit(invoiceDetail.getUomDescr());
							jinrenDTO.setQty(invoiceDetail.getQty() == null ? null
											: invoiceDetail.getQty().toString());
							jinrenDTO.setTaxRate(invoiceDetail.getTaxRate() == null ? null
											: invoiceDetail.getTaxRate().divide(BigDecimal.valueOf(100)).toString());
							jinrenDTO
									.setPrice(invoiceDetail.getPrice() == null ? null
											: invoiceDetail.getPrice()
													.toString());
							jinrenDTO
									.setAmount(invoiceDetail.getAmount() == null ? null
											: invoiceDetail.getAmount()
													.toString());
							jinrenDTO.setRemarks(invoiceHeader.getNotes());
							jinrenDTO.setDiy1(doHeader.getSortGridNo());
							jinrenDTO.setDiy2(doHeader.getDoNo());
							jinrenDTO.setWaveNo(initialWaveHeader.getWaveNo());
							jinrenDTO.setTaxCategoryCode(invoiceDetail.getTaxCategoryCode());
							jinrenDTO.setBuyerTaxNo(invoiceHeader.getBuyerTaxNo());
							jinrenDTOs.add(jinrenDTO);
						}
					}
				}
			}
		}

		if (ListUtil.isNotEmpty(jinrenDTOs)) {
			boolean result = jinrenDBOperate.writeInvoiceInfo(jinrenDTOs, invoiceHeaderIds);
			if(!result){
				return;
			}
		}
		initialWaveHeader.setInvoicePrintFlag(InvoicePrintFlg.SENDED
				.getValue());
		waveService.updateWaveHeader(initialWaveHeader);
	}
	
	@Override
    @Transactional
	public void readBindJinrenInvoice(WaveHeader initialWaveHeader) {
			List<Invoice4JinrenDTO> jinrenDTOs = new ArrayList<Invoice4JinrenDTO>();
			// 查询do
			List<DeliveryOrderHeader> doHeaders = initialWaveHeader.getDoHeaders();
			List<String> invoiceIds = new ArrayList<String>();
		String strShopIds = SystemConfig.getConfigValue(
				"invoiceJinren.shopIds", initialWaveHeader.getWarehouseId());
			for(DeliveryOrderHeader doHeader : doHeaders){
				if(YesNo.YES.getValue().equals(doHeader.getInvoiceFlag().intValue())){
					List<String> shopIds = StringUtil.splitToList(strShopIds,",");
					if(doHeader.getShopId() == null || ListUtil.isNullOrEmpty(shopIds) || (!shopIds.contains(doHeader.getShopId().toString()))){
						continue;
					}
					List<InvoiceHeader> invoiceHeaders = doHeader.getInvoiceHeaders();
					for (InvoiceHeader invoiceHeader : invoiceHeaders) {
						// 发票号为空
						if (StringUtil
								.isEmpty(invoiceHeader.getInvoiceNumber())) {
							invoiceIds.add(invoiceHeader.getId().toString());
						}
					}
				}
			}
			//根据doIds 批量查出发票信息   select doid,
			jinrenDTOs = jinrenDBOperate.readInvoiceInfo(invoiceIds);
			if(ListUtil.isNullOrEmpty(jinrenDTOs)){
				return;
			}
		for (Invoice4JinrenDTO jinrenDTO : jinrenDTOs) {
			InvoiceHeader invoiceHeader = invoiceService.getInvoiceById(Long.valueOf(jinrenDTO.getDocNo()));
			if (!StringUtil.isEmpty(invoiceHeader.getInvoiceNumber())) {
				continue;
			}
			// 检查do是否可以进行发票号操作
			DoInvoicePrintDTO dto = printInvoiceDAO.getInvoicePrintInfoOfDo(invoiceHeader.getDoHeaderId());
			if (DoStatus.CANCELED.getValue().equals(dto.getStatus()) || DoStatus.ALL_DELIVER.getValue().equals(dto.getStatus())) {
				continue;
			}
			InvoiceNo invoiceNo = invoiceNoService.getInvoiceNoObjByInvNo(jinrenDTO.getInvoiceNo());
			if (null == invoiceNo) {
				Long warehouseId = ParamUtil.getCurrentWarehouseId();
				String defaultInvoiceCode = SystemConfig.getConfigValue("default.invoice.code", warehouseId);
				invoiceNo = new InvoiceNo();
				invoiceNo.setInvoiceBookId(Long.valueOf(SystemConfig.getConfigValueInt("delivery.invoice.bookId", warehouseId)));
				invoiceNo.setInvoiceCode(defaultInvoiceCode);
				invoiceNo.setStatus(InvoiceNoStatus.INITIAL.getValue());
				invoiceNo.setInvoiceNo(jinrenDTO.getInvoiceNo());
				invoiceNo.setWarehouseId(warehouseId);
				invoiceNoDAO.save(invoiceNo);
			}
			
			//发票状态为初始化的才绑定。
			if(!InvoiceNoStatus.INITIAL.getValue().equals(invoiceNo.getStatus())){
				continue;
			}
			
			invoiceNoService.bind(invoiceHeader, invoiceNo);
		}
			
			//将所有都已经绑定的 波次打印状态改为已绑定。
			Boolean isAllBinded = waveService.isWaveAllBindedInvoice(initialWaveHeader.getId());
			 if(isAllBinded){
				 initialWaveHeader.setPrintFlag(Long.valueOf(InvoicePrintFlg.BINDED.getValue()));
					waveService.updateWaveHeader(initialWaveHeader);
			 }
		}
}