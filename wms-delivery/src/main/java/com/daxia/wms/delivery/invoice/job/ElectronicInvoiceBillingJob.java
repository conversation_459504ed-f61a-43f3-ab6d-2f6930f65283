package com.daxia.wms.delivery.invoice.job;

import com.daxia.wms.delivery.deliveryorder.service.impl.ElectronicInvoiceServiceImpl;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.service.ElectronicInvoiceService;
import com.daxia.wms.delivery.invoice.service.InvoiceService;
import com.daxia.wms.delivery.invoice.service.impl.InvoiceServiceImpl;
import com.daxia.wms.exp.sys.entity.SExpSrvLog;
import com.daxia.wms.exp.sys.srv.ExpFacadeSrvBase;
import com.daxia.wms.exp.sys.srv.SExpSrvLogService;
import com.daxia.wms.master.job.AbstractJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.Session;
import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.util.ArrayList;
import java.util.List;

@Name("electronicInvoiceBillingJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class ElectronicInvoiceBillingJob extends AbstractJob {
    private int DEFAULT_BATCH_NUM = 20;
    private int DEFAULT_FAILED_NUM = 5;

    @Override
    protected void doRun() throws Exception {
        ElectronicInvoiceService electronicInvoiceService = ((ElectronicInvoiceService) Component.getInstance(ElectronicInvoiceServiceImpl.class));

        // 记录失败异常订单集合
        List<Long> failedList = new ArrayList<Long>();

        List<InvoiceHeader> invoiceHeaderList = electronicInvoiceService.loadInvoiceToBilling(DEFAULT_BATCH_NUM, DEFAULT_FAILED_NUM);
        for (InvoiceHeader invoiceHeader : invoiceHeaderList) {

        	try{
        		electronicInvoiceService.billing(invoiceHeader);
        	} catch (Exception e ){
                processException(e);
                failedList.add(invoiceHeader.getId());
        	}
        }

        if (CollectionUtils.isNotEmpty(failedList)) {
            Session session = (Session) Component.getInstance("hibernateSession");
            session.clear();

            InvoiceService invoiceService = ((InvoiceService) Component.getInstance(InvoiceServiceImpl.class));
            List<InvoiceHeader> invoiceHeaders = invoiceService.getInvoiceList(failedList);

            for (InvoiceHeader invoiceHeader : invoiceHeaders) {
                try {
                    invoiceHeader.setFailedNum(invoiceHeader.getFailedNum() + 1);

                    invoiceService.saveOrUpdate(invoiceHeader);
                } catch (Exception e) {
                    log.error("failCheck  failed", e);

                    processException(e);
                }
            }

            failedList.clear();
        }
    }

    @In
    private SExpSrvLogService sExpSrvLogService;

    @In
    private ExpFacadeSrvBase expFacadeService;


    public void reinvokeService(){

        SExpSrvLog serviceLog = sExpSrvLogService.get(63244L);

        for (int i = 0; i < 100; i++) {

            try {
                expFacadeService.reinvoke(serviceLog);

            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    }





}