package com.daxia.wms.delivery.print.service.carton;

import com.daxia.dubhe.api.wms.dto.LogisticsDTO;
import com.daxia.framework.common.util.MvelUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.WaybillType;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoPrintDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoPrint;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.delivery.deliveryorder.service.DoWaveExService;
import com.daxia.wms.delivery.print.dto.BaseCartonPrintDTO;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.helper.PrintCartonHelper;
import com.daxia.wms.delivery.print.service.PrintWaybillService;
import com.daxia.wms.master.entity.City;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.print.dto.PrintCfg;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.print.utils.PrintHelper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import lombok.SneakyThrows;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.util.List;

@Name("printJDWaybillService")
@lombok.extern.slf4j.Slf4j
public class PrintJDWaybillServiceImpl extends AbstractPrintWaybillService implements PrintWaybillService {

    @In
    private DoPrintDAO doPrintDAO;
    private ObjectMapper objectMapper = new ObjectMapper();
    @In
    private WarehouseService warehouseService;

    @In
    private DoWaveExService doWaveExService;

    @In
    private WarehouseCarrierService warehouseCarrierService;

    @Create
    public void init () {
        this.setWaybillType(WaybillType.JD);
    }

    @Override
    protected void buildDTO(List<PrintReportDto> printReportDtos, DeliveryOrderHeader doHeader, BaseCartonPrintDTO carton,
                            int index, int count) {
        CartonPrintDTO cartonPrintDTO = new CartonPrintDTO();
        cartonPrintDTO.setCartonId(carton.getId());
        cartonPrintDTO.setDocId(doHeader.getId());
        cartonPrintDTO.setDoNo(doHeader.getDoNo());
        cartonPrintDTO.setDocNo(doHeader.getDoNo());
        cartonPrintDTO.setSortGridNo(doHeader.getSortGridNo());
        cartonPrintDTO.setWaveNo(doHeader.getWaveHeader().getWaveNo());
        cartonPrintDTO.setOriginalSoCode(doHeader.getOriginalSoCode());
        // 箱号
        cartonPrintDTO.setCartonNo(carton.getCartonNo());
        // 商家订单号
        cartonPrintDTO.setRefNo1(doHeader.getOriginalSoCode());
        // 运单号
        cartonPrintDTO.setWayBill(carton.getWayBill());
        // 寄件人地址
        Warehouse warehouse = warehouseService.getLocalWarehouse();
        City city = warehouse.getCity();
        if (city != null) {
            cartonPrintDTO.setSendCity(city.getCityCname());
        }
        // 寄件人信息
        // 设置寄件人地址信息
        setSendAddressInfo(doHeader, cartonPrintDTO);
        // 设置始发地和目的地信息
        DoWaveEx doWaveEx = doWaveExService.findByDoHeaderId(doHeader.getId());
        if (null != doWaveEx) {
            //站点&路区
            cartonPrintDTO.setShortAddress(doWaveEx.getShortAddress());
            cartonPrintDTO.setPackageCenterName(doWaveEx.getPackageCenterName());
            cartonPrintDTO.setStartAddressName(doWaveEx.getOriginName());
            cartonPrintDTO.setStartAddressCode(doWaveEx.getOriginCode());
            cartonPrintDTO.setDestAddressName(doWaveEx.getDestinationName());
            cartonPrintDTO.setDestAddressCode(doWaveEx.getDestinationCode());
        }

        cartonPrintDTO.setCartonIndex(index);
//        if (Constants.DoStatus.ALL_CARTON.getValue().equals(doHeader.getStatus()) || Constants.DoStatus.ALL_CARTON.getValue().equals(doHeader.getPcsStatus())) {
            cartonPrintDTO.setCartonCount(count);
//        }
        // 客户电话
        cartonPrintDTO.setClientPhone(PrintCartonHelper.buildTelOrMobile(doHeader));

        // 设置图片路径
        cartonPrintDTO.setBasePrintImgPath(PrintHelper.getBasePrintImgPath());
        // 收货人
        cartonPrintDTO.setClientName(PrintCartonHelper.buildConsigneeName(doHeader));
        // 设置客户栏联系方式
        cartonPrintDTO.setTelOrMobile(PrintCartonHelper.buildTelOrMobile(doHeader));
        // 设置地址信息
        cartonPrintDTO.setClientAddress(PrintCartonHelper.buildAddress(doHeader));
        // 应收金额
        cartonPrintDTO.setReceivable(doHeader.getReceivable());
        //备注
        cartonPrintDTO.setRemark("");
        //商家编码
        WarehouseCarrier warehouseCarrier = warehouseCarrierService.findJDWarehouseCarrier(doHeader.getShopId());
        if (warehouseCarrier != null) {
            cartonPrintDTO.setCardNo(warehouseCarrier.getExt1());
        }
        //保价逻辑
        String jdGuaranteeScript = SystemConfig.getConfigValue("delivery.waybill.jdGuaranteeScript", ParamUtil
                .getCurrentWarehouseId());
        try {
            if (StringUtil.isNotEmpty(jdGuaranteeScript) && Boolean.TRUE.equals(MvelUtil.eval(jdGuaranteeScript,
                    ImmutableMap.of("doHeader", (Object) doHeader)))) {
                cartonPrintDTO.setRemark("【保价" + doHeader.getOrderAmount() + "元】");
            }
        } catch (Exception e) {

        }

        cartonPrintDTO.setSkuUnitQty(getUnitQtyInCarton(doHeader,carton.getCartonNo()));
        cartonPrintDTO.setNotes(doHeader.getNotes());
        setProductInfo(doHeader, cartonPrintDTO);
        cartonPrintDTO.setIsPrinted(carton.getIsPrinted());
        cartonPrintDTO.setSortGridCount(doHeader.getWaveHeader().getDoHeaders().size());
        printReportDtos.add(cartonPrintDTO);
        this.processWayBillInfo(doHeader.getId(), cartonPrintDTO);
    }

    @Override
    protected PrintData genPrintData(List<PrintReportDto> dtoList, DeliveryOrderHeader doHeader) {
        PrintData printData = new PrintData();
        printData.setDtoList(dtoList);
        printData.setPrintCfg(generateSFPrintCfg());
        return printData;
    }

    private PrintCfg generateSFPrintCfg() {
        PrintCfg config = new PrintCfg("wayBillJD", "100", "180");
        this.setPrintCfg(config);
        return config;
    }
    @SneakyThrows
    private void processWayBillInfo(Long doId, CartonPrintDTO cartonPrintDTO) {
        DoPrint doPrint = doPrintDAO.findByDoHeaderId(doId, Constants.DoPrintInfoType.WAYBILL_JSON.getValue());
        if (null == doPrint) {
            throw new DeliveryException(DeliveryException.WAYBILL_IMAGE_NOT_EXIST);
        }
        LogisticsDTO logisticsDTO = objectMapper.readValue(doPrint.getContent(), LogisticsDTO.class);

        cartonPrintDTO.setStartAddressName(logisticsDTO.getSourceCityCode());
        cartonPrintDTO.setStartAddressCode(logisticsDTO.getSourceRouteLabel());
        cartonPrintDTO.setDestAddressName(logisticsDTO.getDestCityCode());
        cartonPrintDTO.setDestAddressCode(logisticsDTO.getDestRouteLabel());
        cartonPrintDTO.setDestDeptCode(logisticsDTO.getDestDeptCode());
        cartonPrintDTO.setDestTeamCode(logisticsDTO.getDestTeamCode());
        cartonPrintDTO.setProductType(logisticsDTO.getPromiseTimeType());

    }
}
