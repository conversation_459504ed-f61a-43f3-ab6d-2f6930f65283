package com.daxia.wms.delivery.recheck.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.deliveryorder.filter.BackExceptionFilter;
import com.daxia.wms.delivery.recheck.dto.TempCartonExceptionDTO;
import com.daxia.wms.delivery.recheck.entity.TempCarton;
import com.daxia.wms.print.PrintConstants;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.jboss.seam.annotations.Name;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * 包材推荐日志DAO
 */
@Name("com.daxia.wms.delivery.tempCartonDAO")
@lombok.extern.slf4j.Slf4j
public class TempCartonDAO extends HibernateBaseDAO<TempCarton, Long> {

	private static final long serialVersionUID = 2071835084740771635L;
	/**
	 * 根据DOId查询
	 * @param doId
	 * @return
	 */
	public TempCarton getByDoId(Long doId) {
		String hql = "from TempCarton o where o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId " +
				"and exists(select 1 from DeliveryOrderHeader d where d.id = o.doHeaderId and d.carrierId = o.carrierId) " +
				"and not exists(select 1 from CartonHeader ch where ch.doHeader.id = o.doHeaderId and o.cartonNo = ch.cartonNo )";
		return (TempCarton) createQuery(hql).setParameter("doHeaderId", doId)
				.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId())
				.uniqueResult();
	}

	public boolean existEmptyCartonNo(Long waveId) {
		String sql = "SELECT count(dh.id) FROM doc_do_header dh LEFT JOIN doc_temp_carton tc ON tc.do_header_id = dh.id  " +
				" AND tc.carrier_id = dh.carrier_id AND tc.warehouse_id = :warehouseId AND tc.is_deleted = 0 AND tc.success_flag = 1 " +
				"WHERE tc.id IS NULL AND dh.wave_id = :waveId AND dh.warehouse_id = :warehouseId AND dh.is_deleted = 0 and dh.release_status = :releaseStatus";
		return ((Number) this.createSQLQuery(sql).setParameter("waveId", waveId).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).setParameter("releaseStatus", Constants.ReleaseStatus.RELEASE.getValue()).setMaxResults(1).uniqueResult()).intValue() > 0;
	}

	public List<TempCarton> findByDoIdList(List<Long> doIdList) {
		String hql = "select new com.daxia.wms.delivery.recheck.entity.TempCarton(o.id,o.cartonNo,o.trackingNo,o.wayBill,o.printData,o.doHeaderId,o.carrierId,o.successFlag," +
				"o.errorMsg,o.callCount,o.isPrinted " +
				") from TempCarton o inner join o.doHeader d where o.doHeaderId in (:doIdList) and o.warehouseId = :warehouseId and o.successFlag = 1 " +
				"and d.id = o.doHeaderId and d.carrierId = o.carrierId order by d.waveId,d.sortGridNo asc";
		return  createQuery(hql).setParameterList("doIdList", doIdList)
				.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId())
				.list();
	}

	public void removeByDoId(Long doId) {
		String sql = "insert into doc_temp_carton_his select * from doc_temp_carton t where t.do_header_id =:id ";
		Query query = this.createSQLQuery(sql);
		query.setLong("id", doId).executeUpdate();

		sql = "delete o from doc_temp_carton o where o.do_header_id=:id";
		query = this.createSQLQuery(sql);
		query.setLong("id", doId);
		query.executeUpdate();
	}

    public void removeByDoId(Long doId, Long carrierId) {
        String sql = "insert into doc_temp_carton_his select * from doc_temp_carton t where t.do_header_id =:id and t.carrier_id !=:carrierId ";
        Query query = this.createSQLQuery(sql);
        query.setLong("id", doId).setLong("carrierId", carrierId).executeUpdate();

        sql = "delete o from doc_temp_carton o where o.do_header_id=:id and o.carrier_id !=:carrierId";
        query = this.createSQLQuery(sql);
        query.setLong("id", doId);
        query.setLong("carrierId", carrierId);
        query.executeUpdate();
    }

	public void removeByWaveId(Long waveId) {
		String sql = "insert into doc_temp_carton_his select * from doc_temp_carton t where t.do_header_id  in (select id from doc_do_header where wave_id = :waveId) ";
		Query query = this.createSQLQuery(sql);
		query.setLong("waveId", waveId).executeUpdate();

		sql = "delete o from doc_temp_carton o where o.do_header_id in (select id from doc_do_header where wave_id = :waveId) ";
		query = this.createSQLQuery(sql);
		query.setLong("waveId", waveId);
		query.executeUpdate();
	}

	public List<Long> findCartonIdsForPrint(Long doHeaderId) {
		String sql ="SELECT ch.id FROM doc_carton_header ch " +
				"INNER JOIN doc_do_header dh " +
				"WHERE ch.do_header_id = dh.id " +
				"AND dh.id = :doHeaderId " +
				"AND dh.warehouse_id = :whId " +
				"AND NOT EXISTS ( " +
				"  SELECT " +
				"    1 " +
				"  FROM " +
				"    doc_temp_carton tc " +
				"  WHERE " +
				"    tc.do_header_id = dh.id AND tc.is_printed = 1 " +
				"  AND tc.carton_no = ch.carton_no " +
				")";
		SQLQuery query = this.createSQLQuery(sql);
		query.setParameter("doHeaderId",doHeaderId)
			.setParameter("whId",ParamUtil.getCurrentWarehouseId());
		query.addScalar("id", Hibernate.LONG);
		return query.list();
	}

	public void updatePrintFlagByDoIdList(List<Long> doIdList) {
		String sql = "update doc_temp_carton o set o.is_printed = 1 where o.do_header_id " +
				" in (:doIdList)";
		Query query = this.createSQLQuery(sql);
		query.setParameterList("doIdList", doIdList);
		query.executeUpdate();
	}

	public boolean existRepeatCartonNo(Long waveId) {
		String sql = "select count(1) from (SELECT 1 FROM" +
				" doc_do_header dh, " +
				" doc_temp_carton tc " +
				"WHERE " +
				"dh.WAVE_ID = :waveId AND tc.DO_HEADER_ID = dh.id " +
				"AND tc.CARRIER_ID = dh.carrier_id AND tc.warehouse_id = :whId " +
				"AND tc.IS_DELETED = 0 and tc.SUCCESS_FLAG = 1 " +
				"GROUP BY dh.id HAVING COUNT(1) > 1) temp";
		return ((Number) this.createSQLQuery(sql).setParameter("whId",ParamUtil.getCurrentWarehouseId())
				.setParameter("waveId", waveId).setMaxResults(1).uniqueResult()).intValue() > 0;
	}

	public TempCarton getByCartonNo(String cartonNo) {
		String hql = "from TempCarton o where o.cartonNo = :cartonNo and o.successFlag = :successFlag and o.warehouseId = :warehouseId " +
				"and exists(select 1 from DeliveryOrderHeader d where d.id = o.doHeaderId and d.carrierId = o.carrierId)";
		return (TempCarton) createQuery(hql).setParameter("cartonNo", cartonNo)
				.setParameter("successFlag", Constants.YesNo.YES.getValue())
				.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId())
				.uniqueResult();
	}

	public void savePrintLog(List<Long> doIdList,String updateBy) {
		String sql = "INSERT INTO log_print (`DOC_ID`,`DOC_NO`,`PRINT_TYPE`,`PRINT_TYPE_EX`, " +
				" `COSTS`,`CREATE_BY`,`CREATE_TIME`,`UPDATE_BY`,`UPDATE_TIME`,`WAREHOUSE_ID`, " +
				" `IS_DELETED`,`VERSION`,`DEVICE_NAME`,`REF_NO`,`OUT_REF_NO`) " +
				"select -1,dtc.CARTON_NO,:printType ,carrier.waybill_type ,NULL,:updateBy, " +
				"  now(),:updateBy,now(),dh.warehouse_id,'0','0','WMS',dh.do_no,dh.ref_no1 " +
				"from doc_temp_carton dtc,doc_do_header dh,md_carrier carrier where  " +
				"dtc.DO_HEADER_ID = dh.ID and dtc.IS_DELETED = 0 AND dtc.CARRIER_ID = dh.carrier_id " +
				"AND dh.carrier_id = carrier.id " +
				"AND dtc.SUCCESS_FLAG = 1 AND dh.Id in (:doIdList)";
		Query query = this.createSQLQuery(sql);
			query.setParameter("printType", PrintConstants.PrintType.CARTON_LABEL.name())
			.setParameter("updateBy",updateBy).setParameterList("doIdList",doIdList)
			.executeUpdate();
	}


	public List<TempCartonExceptionDTO> findTempCartonExceptionList(BackExceptionFilter backExceptionFilter, int startIndex, int pageSize) {

		SQLQuery query = this.createSQLQuery(createTempCartonExceptionSql(backExceptionFilter));

		handleTempCartonExceptionCondition(query,backExceptionFilter);

		query.setFirstResult(startIndex).setMaxResults(pageSize);
		// dh.id,dh.do_no,dh.`status`,wave.wave_no,carrier.dist_supp_comp_name,tc.ERROR_MSG
		query.addScalar("id", Hibernate.LONG);
		query.addScalar("do_no", Hibernate.STRING);
		query.addScalar("status", Hibernate.STRING);
		query.addScalar("wave_no", Hibernate.STRING);
		query.addScalar("dist_supp_comp_name", Hibernate.STRING);
		query.addScalar("ERROR_MSG", Hibernate.STRING);
		List list = query.list();

		List<TempCartonExceptionDTO> dtoList = new ArrayList<TempCartonExceptionDTO>();
		TempCartonExceptionDTO dto;
		Object[] result;
		for (int i = 0; i < list.size(); i++) {
			result = (Object[])list.get(i);
			dto = new TempCartonExceptionDTO();
			dto.setDoId((Long)result[0]);
			dto.setDoNo((String)result[1]);
			dto.setStatus((String)result[2]);
			dto.setWaveNo((String)result[3]);
			dto.setCarrierName((String)result[4]);
			dto.setErrorMsg((String)result[5]);
			dtoList.add(dto);
		}
		return dtoList;
	}

	public Long countTempCartonException(BackExceptionFilter backExceptionFilter) {
		SQLQuery query = this.createSQLQuery("select count(1) from ("+createTempCartonExceptionSql(backExceptionFilter)+") temp");
		handleTempCartonExceptionCondition(query,backExceptionFilter);
		return ((BigInteger)query.uniqueResult()).longValue();
	}

	private String createTempCartonExceptionSql(BackExceptionFilter backExceptionFilter){
		List<String> doTypes = Config.getByDelimit(Keys.Delivery.auto_temp_carton_doTypes, Config.ConfigLevel.WAREHOUSE);
		if(ListUtil.isNullOrEmpty(doTypes)){
			doTypes = Lists.newArrayList(Constants.DoType.SELL.getValue());
		}
		
		String doTypeStr = ListUtil.collection2String(doTypes, ",");
		
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT dh.id,dh.do_no,dh.`status`,wave.wave_no,carrier.dist_supp_comp_name,tc.ERROR_MSG FROM doc_do_header dh ")
				.append("LEFT JOIN doc_wave_header wave on wave.id = dh.wave_id ")
				.append("LEFT JOIN doc_temp_carton tc on tc.DO_HEADER_ID = dh.id AND tc.IS_DELETED = 0 ")
				.append("LEFT JOIN md_carrier carrier on carrier.id = dh.carrier_id ")
				.append("WHERE ( (dh.`status` >= 40 AND dh.`status` <= 65 ) or dh.is_temp_carton = 1 ) AND dh.do_type in ("+ doTypeStr + ") AND dh.need_cancel = 0 ")
				.append("AND dh.warehouse_id = :warehouseId ");
		if (backExceptionFilter.getCreatedFrom() != null) {
			sql.append("AND dh.create_time >= :createTimeFm ");
		}
		if (backExceptionFilter.getCreatedTo() != null) {
			sql.append("AND dh.create_time <= :createTimeTo ");
		}
		if (StringUtils.isNotEmpty(backExceptionFilter.getDoNo())) {
			sql.append("AND dh.do_no = :doNo ");
		}
		if (StringUtils.isNotEmpty(backExceptionFilter.getWaveNo())) {
			sql.append("AND wave.wave_no = :waveNo ");
		}
		if (backExceptionFilter.getNotOrderFlag() != null) {
			sql.append("AND tc.id is null ");
		}
		if (backExceptionFilter.getOrderFailFlag() != null) {
			sql.append("AND tc.success_flag = 0 ");
		}
		if (backExceptionFilter.getDefaultCarrierId() != null) {
			sql.append("AND dh.carrier_id != :defaultCarrierId ");
		}
		if (backExceptionFilter.getCarrierId() != null) {
			sql.append("AND dh.carrier_id = :carrierId ");
		}
		sql.append("order by dh.id asc ");
		return sql.toString();
	}

	private void handleTempCartonExceptionCondition(SQLQuery query,BackExceptionFilter backExceptionFilter){
		query.setParameter("warehouseId",ParamUtil.getCurrentWarehouseId());
		if (backExceptionFilter.getCreatedFrom() != null) {
			query.setParameter("createTimeFm",backExceptionFilter.getCreatedFrom());
		}
		if (backExceptionFilter.getCreatedTo() != null) {
			query.setParameter("createTimeTo",backExceptionFilter.getCreatedTo());
		}
		if (StringUtils.isNotEmpty(backExceptionFilter.getDoNo())) {
			query.setParameter("doNo",backExceptionFilter.getDoNo());
		}
		if (StringUtils.isNotEmpty(backExceptionFilter.getWaveNo())) {
			query.setParameter("waveNo",backExceptionFilter.getWaveNo());
		}
		if (backExceptionFilter.getDefaultCarrierId() != null) {
			query.setParameter("defaultCarrierId",backExceptionFilter.getDefaultCarrierId());
		}
		if (backExceptionFilter.getCarrierId() != null) {
			query.setParameter("carrierId", backExceptionFilter.getCarrierId());
		}
	}

	public void refreshFailOrder() {
		String sql = "update doc_temp_carton SET CALL_COUNT = 2 where IS_DELETED = 0 AND SUCCESS_FLAG = 0 AND WAREHOUSE_ID = :warehouseId ";
		SQLQuery query = this.createSQLQuery(sql);
		query.setParameter("warehouseId",ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();
	}
}
