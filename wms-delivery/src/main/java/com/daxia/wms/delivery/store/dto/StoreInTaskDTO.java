package com.daxia.wms.delivery.store.dto;

import java.io.Serializable;

@lombok.extern.slf4j.Slf4j
public class StoreInTaskDTO implements Serializable {

    /**
     * 格号，边拣边分模式下容器绑定的格号
     */
    private String gridNo;

    /**
     * 容器号，有可能多个，逗号分隔
     */
    private String turnoverBoxNos;

    /**
     * 整散标记 1散 2整
     */
    private String pktType;

    /**
     * 单据类型：3订单 0波次
     */
    private String docType;
    /**
     * 订单号/波次号
     */
    private String docNo;

    /**
     * 推荐集货位
     */
    private String recommendStoreLoc;

    /**
     * 实际集货位
     */
    private String actualStoreLoc;

    public String getGridNo() {
        return gridNo;
    }

    public void setGridNo(String gridNo) {
        this.gridNo = gridNo;
    }

    public String getTurnoverBoxNos() {
        return turnoverBoxNos;
    }

    public void setTurnoverBoxNos(String turnoverBoxNos) {
        this.turnoverBoxNos = turnoverBoxNos;
    }

    public String getPktType() {
        return pktType;
    }

    public void setPktType(String pktType) {
        this.pktType = pktType;
    }

    public String getDocType() {
        return docType;
    }

    public void setDocType(String docType) {
        this.docType = docType;
    }

    public String getDocNo() {
        return docNo;
    }

    public void setDocNo(String docNo) {
        this.docNo = docNo;
    }

    public String getRecommendStoreLoc() {
        return recommendStoreLoc;
    }

    public void setRecommendStoreLoc(String recommendStoreLoc) {
        this.recommendStoreLoc = recommendStoreLoc;
    }

    public String getActualStoreLoc() {
        return actualStoreLoc;
    }

    public void setActualStoreLoc(String actualStoreLoc) {
        this.actualStoreLoc = actualStoreLoc;
    }
}
