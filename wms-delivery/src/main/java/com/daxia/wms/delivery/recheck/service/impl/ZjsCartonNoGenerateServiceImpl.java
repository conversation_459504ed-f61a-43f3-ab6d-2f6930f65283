package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.SubDocNoService;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.waybill.zjs.dto.ZjsReceiver;
import com.daxia.wms.waybill.zjs.dto.ZjsSender;
import com.daxia.wms.waybill.zjs.dto.ZjsWaybillRequest;
import com.daxia.wms.waybill.zjs.service.ZjsWaybillService;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;

@Name("zjsCartonNoGenerateService")
@lombok.extern.slf4j.Slf4j
public class ZjsCartonNoGenerateServiceImpl implements CartonNoGenerateService {

    @In
    private SequenceGeneratorService sequenceGeneratorService;
    @In(create = true)
    private ZjsWaybillService zjsWaybillService;
    @In
    private WarehouseCarrierService warehouseCarrierService;
    @In
    private WarehouseService warehouseService;
    @In
    private SubDocNoService subDocNoService;

    @Override
    public void generatorCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {

        WarehouseCarrier warehouseCarrier = warehouseCarrierService.getCarrierInfoByWarehouseIdAndCarrierId(doHeader
                .getWarehouseId(),doHeader.getCarrierId());
        Warehouse warehouse = warehouseService.getLocalWarehouse();
        String orderNo = doHeader.getDoNo();
        if (StringUtil.isEmpty(orderNo)) {
            throw new DeliveryException(DeliveryException.DO_NO_IS_NULL);
        }


        String cartonNo = subDocNoService.useOneByCarrierCorpId(Constants.CarrierCorp.ZJS.getValue(),null,null);
        if (StringUtil.isEmpty(cartonNo)) {
            throw new DeliveryException("宅急送单号不足，请联系网点！");
        }
        cartonHeader.setCartonNo(cartonNo);
        cartonHeader.setWayBill(cartonNo);


        boolean isPrintOnly = StringUtils.equals(warehouseCarrier.getExt4(),"1");
        if (isPrintOnly) {
            return;
        }

        ZjsWaybillRequest request = new ZjsWaybillRequest();

        request.setLogisticProviderID(warehouseCarrier.getExt1());
        request.setOrderNo(cartonNo);
        request.setTradeNo(cartonNo);
        request.setMailNo(cartonNo);
        if (doHeader.getReceivable().compareTo(BigDecimal.ZERO) > 0) {
            request.setType(0);
            request.setCodAmount(doHeader.getReceivable().toString());
        } else {
            request.setType(1);
        }
        request.setItemsName("货品");
        request.setItemsNumber(doHeader.getExpectedQty().toString());

        ZjsSender sender = new ZjsSender();
        sender.setName(warehouse.getContactor());
        sender.setPhone(StringUtil.isEmpty(warehouse.getMobile()) ? warehouse.getPhone() : warehouse.getMobile());
        sender.setProv(warehouse.getProvince().getProvinceCname());
        sender.setCity(warehouse.getCity().getCityCname());
        sender.setDistrict(warehouse.getCounty().getCountyCname());
        sender.setAddress(warehouse.getAddressName());
        request.setSender(sender);


        ZjsReceiver receiver = new ZjsReceiver();
        receiver.setName(doHeader.getConsigneeName());
        receiver.setPhone(DoUtil.decryptPhone(StringUtil.isEmpty(doHeader.getMobile())? doHeader.getTelephone():doHeader.getMobile()));
        receiver.setProv(doHeader.getProvince() == null ? doHeader.getProvinceName() : doHeader.getProvinceInfo().getProvinceCname());
        receiver.setCity(doHeader.getCity() == null ? doHeader.getCityName() : doHeader.getCityInfo().getCityCname());
        receiver.setDistrict(doHeader.getCounty() == null ? doHeader.getCountyName() : doHeader.getCountyInfo().getCountyCname());
        receiver.setAddress(doHeader.getAddress());
        request.setReceiver(receiver);

        //运费到付备注
        Integer orderNum = SystemConfig.getConfigValueInt("fee.receiverPay.unit.number", ParamUtil.getCurrentWarehouseId());
        if (orderNum != null && doHeader.getExpectedQty().intValue() < orderNum) {
            request.setRemark("运费到付");
        }

        //oppo特殊逻辑
        if(ParamUtil.getCurrentTenantCode().equals("oppo")) {
            //oppo订单重量，unit * 0.5
            request.setItemsWeight(
                    String.valueOf((doHeader.getExpectedQty().doubleValue() > 10 ? 10 : doHeader.getExpectedQty().doubleValue())*0.5)
            );
        }
        zjsWaybillService.waybillOrder(request,warehouseCarrier.getAppSecret(),warehouseCarrier.getAppToken());
    }
}
