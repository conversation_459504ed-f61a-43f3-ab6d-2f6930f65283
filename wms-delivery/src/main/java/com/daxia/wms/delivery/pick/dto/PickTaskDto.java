package com.daxia.wms.delivery.pick.dto;

import com.google.common.collect.Sets;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

/**
 * 拣货任务dto
 */
@lombok.extern.slf4j.Slf4j
public class PickTaskDto implements Serializable {
    
    private static final long serialVersionUID = -4446652871405504204L;
    /**
     * 主键
     */
    private Long id;
    /**
     * pkt单头信息ID
     */
    private Long pktHeaderId;
    /**
     * 波次头ID
     */
    private Long waveHeaderId;
    /**
     * DO单头信息ID
     */
    private Long doHeaderId;
    /**
     * DO单明细ID
     */
    private Long doDetailId;
    /**
     * 分拣明细状态
     */
    private String status;
    /**
     * 货主ID
     */
    private Long cargoOwnerId;
    /**
     * 产品ID
     */
    private Long skuId;
    /**
     * 批次ID
     */
    private Long lotId;
    /**
     * 批次等级
     */
    private String lotatt14;
    /**
     * 库位ID -- 拣货库位
     */
    private Long locId;
    /**
     * 托盘号
     */
    private String lpnNo;
    /**
     * 分配数量
     */
    private BigDecimal qty;
    
    /**
     * 分配数量
     */
    private BigDecimal qtyUnit;
    //	/**
    //	 * 分配数量EACH
    //	 */
    //	private BigDecimal qtyEach;
    
    private BigDecimal qtyPickedUnit;
    /**
     * 拣货数量
     */
    private BigDecimal pickedQty;
    /**
     * 分拣数量
     */
    private BigDecimal qtySorted;
    /**
     * 待分拣数量
     */
    //private BigDecimal qtyUnsortedEach;
    /**
     * 发货数量
     */
    private BigDecimal qtyShipped;
    /**
     * 单位
     */
    private String uom;
    /**
     * 包装ID
     */
    private Long packId;
    /**
     * PACK_DETAIL_ID
     */
    private Long packDetailId;
    /**
     * 毛重
     */
    private BigDecimal grossWeight;
    /**
     * 净重
     */
    private BigDecimal netWeight;
    /**
     * 体积
     */
    private BigDecimal volume;
    /**
     * 分拣格ID
     */
    private Long sortGridNo;
    /**
     * 拣货到库位
     */
    private Long toLocId;
    /**
     * 拣货到LPN
     */
    private String toLpnNo;
    /**
     * 拣货人
     */
    private String pickWho;
    /**
     * 拣货时间
     */
    private Date pickTime;
    /**
     * 分拣人
     */
    private String sortWho;
    /**
     * 分拣时间
     */
    private Date sortTime;
    /**
     * 发货人
     */
    private String shipWho;
    /**
     * 发货时间
     */
    private Date shipTime;
    /**
     * 备注
     */
    private String notes;
    
    private String productCode;
    
    private String ean13;
    
    private String productName;
    
    /**
     * 是否合并标识,默认没有合并
     */
    private Boolean isGrouped = Boolean.FALSE;
    
    //标记拣货任务包含涉及到的订单需求数量是否一致，用于打印拣货单
    private Boolean doNeedQtyIsEquale;
    //拣货任务涉及到的订单数，用于打印拣货单
    private Integer doNum;
    //分拣信息
    private String sortInfo;

    private Long regionId;
    
    /**
     * 批号
     */
    private String lotNo;
    
    private String locCode;
    
    private String containerNo;
    
    public PickTaskDto(String productCode, String productName, String locCode, BigDecimal qty, BigDecimal pickedQty, String containerNo, Date pickTime, String pickWho) {
        this.productCode = productCode;
        this.productName = productName;
        this.locCode = locCode;
        this.qty = qty;
        this.pickedQty = pickedQty;
        this.containerNo = containerNo;
        this.pickTime = pickTime;
        this.pickWho = pickWho;
    }
    
    public PickTaskDto() {
    
    }
    
    public String getLocCode() {
        return locCode;
    }
    
    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }
    
    private Set<Long> doIdSet = Sets.newHashSet();
    
    public Set<Long> getDoIdSet() {
        return doIdSet;
    }
    
    public void setDoIdSet(Set<Long> doIdSet) {
        this.doIdSet = doIdSet;
    }
    
    public Boolean getDoNeedQtyIsEquale() {
        return doNeedQtyIsEquale;
    }
    
    public void setDoNeedQtyIsEquale(Boolean pickQtyIsEquale) {
        this.doNeedQtyIsEquale = pickQtyIsEquale;
    }
    
    public BigDecimal getQty() {
        return qty;
    }
    
    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }
    
    public Long getLocId() {
        return locId;
    }
    
    public void setLocId(Long locId) {
        this.locId = locId;
    }
    
    public Long getSkuId() {
        return skuId;
    }
    
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getPktHeaderId() {
        return pktHeaderId;
    }
    
    public void setPktHeaderId(Long pktHeaderId) {
        this.pktHeaderId = pktHeaderId;
    }
    
    public Long getWaveHeaderId() {
        return waveHeaderId;
    }
    
    public void setWaveHeaderId(Long waveHeaderId) {
        this.waveHeaderId = waveHeaderId;
    }
    
    public Long getDoHeaderId() {
        return doHeaderId;
    }
    
    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }
    
    public Long getDoDetailId() {
        return doDetailId;
    }
    
    public void setDoDetailId(Long doDetailId) {
        this.doDetailId = doDetailId;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Long getCargoOwnerId() {
        return cargoOwnerId;
    }
    
    public void setCargoOwnerId(Long cargoOwnerId) {
        this.cargoOwnerId = cargoOwnerId;
    }
    
    public Long getLotId() {
        return lotId;
    }
    
    public void setLotId(Long lotId) {
        this.lotId = lotId;
    }
    
    public String getLpnNo() {
        return lpnNo;
    }
    
    public void setLpnNo(String lpnNo) {
        this.lpnNo = lpnNo;
    }
    
    //	public BigDecimal getQtyEach() {
    //		return qtyEach;
    //	}
    //
    //	public void setQtyEach(BigDecimal qtyEach) {
    //		this.qtyEach = qtyEach;
    //	}
    
    public BigDecimal getPickedQty() {
        return pickedQty;
    }
    
    public void setPickedQty(BigDecimal pickedQty) {
        this.pickedQty = pickedQty;
    }
    
    public BigDecimal getQtySorted() {
        return qtySorted;
    }
    
    public void setQtySorted(BigDecimal qtySorted) {
        this.qtySorted = qtySorted;
    }
    
    public BigDecimal getQtyShipped() {
        return qtyShipped;
    }
    
    public void setQtyShipped(BigDecimal qtyShipped) {
        this.qtyShipped = qtyShipped;
    }
    
    public String getUom() {
        return uom;
    }
    
    public void setUom(String uom) {
        this.uom = uom;
    }
    
    public Long getPackId() {
        return packId;
    }
    
    public void setPackId(Long packId) {
        this.packId = packId;
    }
    
    public Long getPackDetailId() {
        return packDetailId;
    }
    
    public void setPackDetailId(Long packDetailId) {
        this.packDetailId = packDetailId;
    }
    
    public BigDecimal getGrossWeight() {
        return grossWeight;
    }
    
    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }
    
    public BigDecimal getNetWeight() {
        return netWeight;
    }
    
    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }
    
    public BigDecimal getVolume() {
        return volume;
    }
    
    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }
    
    public Long getSortGridNo() {
        return sortGridNo;
    }
    
    public void setSortGridNo(Long sortGridNo) {
        this.sortGridNo = sortGridNo;
    }
    
    public Long getToLocId() {
        return toLocId;
    }
    
    public void setToLocId(Long toLocId) {
        this.toLocId = toLocId;
    }
    
    public String getToLpnNo() {
        return toLpnNo;
    }
    
    public void setToLpnNo(String toLpnNo) {
        this.toLpnNo = toLpnNo;
    }
    
    public String getPickWho() {
        return pickWho;
    }
    
    public void setPickWho(String pickWho) {
        this.pickWho = pickWho;
    }
    
    public Date getPickTime() {
        return pickTime;
    }
    
    public void setPickTime(Date pickTime) {
        this.pickTime = pickTime;
    }
    
    public String getSortWho() {
        return sortWho;
    }
    
    public void setSortWho(String sortWho) {
        this.sortWho = sortWho;
    }
    
    public Date getSortTime() {
        return sortTime;
    }
    
    public void setSortTime(Date sortTime) {
        this.sortTime = sortTime;
    }
    
    public String getShipWho() {
        return shipWho;
    }
    
    public void setShipWho(String shipWho) {
        this.shipWho = shipWho;
    }
    
    public Date getShipTime() {
        return shipTime;
    }
    
    public void setShipTime(Date shipTime) {
        this.shipTime = shipTime;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    
    public String getProductCode() {
        return productCode;
    }
    
    
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }
    
    
    public String getEan13() {
        return ean13;
    }
    
    
    public void setEan13(String ean13) {
        this.ean13 = ean13;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }
    
    public Boolean getIsGrouped() {
        return isGrouped;
    }
    
    public void setIsGrouped(Boolean isGrouped) {
        this.isGrouped = isGrouped;
    }
    
    public Integer getDoNum() {
        return doNum;
    }
    
    public void setDoNum(Integer doNum) {
        this.doNum = doNum;
    }
    
    public String getLotNo() {
        return lotNo;
    }
    
    public void setLotNo(String lotNo) {
        this.lotNo = lotNo;
    }
    
    public BigDecimal getQtyUnit() {
        return qtyUnit;
    }
    
    public void setQtyUnit(BigDecimal qtyUnit) {
        this.qtyUnit = qtyUnit;
    }
    
    public BigDecimal getQtyPickedUnit() {
        return qtyPickedUnit;
    }
    
    public void setQtyPickedUnit(BigDecimal qtyPickedUnit) {
        this.qtyPickedUnit = qtyPickedUnit;
    }
    
    public String getSortInfo() {
        return sortInfo;
    }
    
    public void setSortInfo(String sortInfo) {
        this.sortInfo = sortInfo;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public String getLotatt14() {
        return lotatt14;
    }

    public void setLotatt14(String lotatt14) {
        this.lotatt14 = lotatt14;
    }
}
