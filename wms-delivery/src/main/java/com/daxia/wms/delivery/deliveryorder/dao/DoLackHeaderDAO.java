package com.daxia.wms.delivery.deliveryorder.dao;

import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.deliveryorder.entity.DoLackHeader;

@Name("com.daxia.wms.delivery.doLackHeaderDAO")
@lombok.extern.slf4j.Slf4j
public class DoLackHeaderDAO extends HibernateBaseDAO<DoLackHeader, Long>{
    private static final long serialVersionUID = 1699758221284288573L;
    
    /**
     * 根据发货单头查询缺货头
     * @param doHeaderId
     * @return
     */
    public DoLackHeader getByDoId(Long doHeaderId) {
        String hql = "from DoLackHeader o where o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql)
                .setLong("doHeaderId", doHeaderId)
                .setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (DoLackHeader) query.uniqueResult();
    }
    
    /**
     * 根据DO删除缺货头
     * @param doId
     */
    public void deleteByDoHeaderId(Long doId) {
        String hql = "update DoLackHeader o set o.isDeleted = 1 where o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql)
                .setLong("doHeaderId", doId)
                .setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }
    
    /**
     * 更新缺货头找货任务生成状态
     * @param doIds
     * @param stauts
     */
    public void updateCreateStatus(List<Long> doIds, String stauts) {
        String hql = "update DoLackHeader o  set o.createStatus = :createStatus where o.doHeaderId in (:doIds) and o.warehouseId = :warehouseId ";
        Query query = this.createQuery(hql)
                .setString("createStatus", stauts)
                .setParameterList("doIds", doIds)
                .setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }
    
    /**
     * 根据发货单更新其缺货头状态
     * @param doId
     * @param stauts
     */
    public void updateCreateStatusByDoId(Long doId, String stauts) {
        String hql = "update DoLackHeader o  set o.createStatus = :createStatus where o.doHeaderId = :doId and o.warehouseId = :warehouseId ";
        Query query = this.createQuery(hql)
                .setString("createStatus", stauts)
                .setLong("doId", doId)
                .setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }
}
