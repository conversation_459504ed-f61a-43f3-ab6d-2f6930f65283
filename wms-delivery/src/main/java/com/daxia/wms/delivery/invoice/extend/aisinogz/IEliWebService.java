package com.daxia.wms.delivery.invoice.extend.aisinogz;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * This class was generated by the JAX-WS RI. JAX-WS RI 2.1.3-hudson-390-
 * Generated source version: 2.0
 * <p>
 * An example of how this class may be used:
 * 
 * <pre>
 * IEliWebService service = new IEliWebService();
 * IEliWebServicePortType portType = service.getIEliWebServiceHttpPort();
 * portType.queryEliData(...);
 * </pre>
 * 
 * </p>
 * 
 */
@WebServiceClient(name = "IEliWebService", targetNamespace = "http://webservice.companyInterface.dzfp.fp.aisinogd.com", wsdlLocation = "http://www.aisinogz.com:19876/AisinoFp-test/eliWebService.ws?wsdl")
@lombok.extern.slf4j.Slf4j
public class IEliWebService extends Service {

	private final static URL IELIWEBSERVICE_WSDL_LOCATION;

	static {
		URL url = null;
		try {
			URL baseUrl;
			baseUrl = IEliWebService.class.getResource(".");
			url = new URL(baseUrl, "http://www.aisinogz.com:19876/AisinoFp-test/eliWebService.ws?wsdl");
		} catch (MalformedURLException e) {
			log.warn("Failed to create URL for the wsdl Location: 'http://www.aisinogz" +
					".com:19876/AisinoFp-test/eliWebService.ws?wsdl', retrying as a local file");
			log.warn(e.getMessage());
		}
		IELIWEBSERVICE_WSDL_LOCATION = url;
	}

	public IEliWebService(URL wsdlLocation, QName serviceName) {
		super(wsdlLocation, serviceName);
	}

	public IEliWebService() {
		super(IELIWEBSERVICE_WSDL_LOCATION, new QName("http://webservice.companyInterface.dzfp.fp.aisinogd.com", "IEliWebService"));
	}

	/**
	 * 
	 * @return returns IEliWebServicePortType
	 */
	@WebEndpoint(name = "IEliWebServiceHttpPort")
	public IEliWebServicePortType getIEliWebServiceHttpPort() {
		return super.getPort(new QName("http://webservice.companyInterface.dzfp.fp.aisinogd.com", "IEliWebServiceHttpPort"), IEliWebServicePortType.class);
	}
}
