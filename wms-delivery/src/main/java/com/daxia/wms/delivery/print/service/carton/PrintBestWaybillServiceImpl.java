package com.daxia.wms.delivery.print.service.carton;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.delivery.deliveryorder.service.DoWaveExService;
import com.daxia.wms.delivery.print.dto.BaseCartonPrintDTO;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.helper.CurrencyConverter;
import com.daxia.wms.delivery.print.helper.PrintCartonHelper;
import com.daxia.wms.delivery.print.helper.WaybillPrintHelper;
import com.daxia.wms.delivery.print.service.PrintWaybillService;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.print.dto.PrintCfg;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.print.utils.PrintHelper;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.List;


@Name("printBestWaybillService")
@lombok.extern.slf4j.Slf4j
public class PrintBestWaybillServiceImpl extends AbstractPrintWaybillService implements PrintWaybillService {

    @In
    private DoWaveExService doWaveExService;
    @In
    private WarehouseService warehouseService;
    @In
    WarehouseCarrierService warehouseCarrierService;

    @Create
    public void init (){
        this.setWaybillType(Constants.WaybillType.HTKY);
    }
    @Override
    protected void buildDTO(List<PrintReportDto> printReportDtos, DeliveryOrderHeader doHeader, BaseCartonPrintDTO carton,
                            int index, int count) {
        CartonPrintDTO cartonPrintDTO = new CartonPrintDTO();
        cartonPrintDTO.setIsPrinted(carton.getIsPrinted());
        cartonPrintDTO.setCartonId(carton.getId());
        cartonPrintDTO.setCartonNo(carton.getCartonNo());
        cartonPrintDTO.setDoNo(doHeader.getDoNo());
        cartonPrintDTO.setOutRefNo(doHeader.getRefNo1());
        cartonPrintDTO.setIsCOD(PrintCartonHelper.isCOD(doHeader));
        cartonPrintDTO.setSortGridNo(doHeader.getSortGridNo());
        cartonPrintDTO.setWaveNo(doHeader.getWaveHeader().getWaveNo());
        cartonPrintDTO.setOriginalSoCode(doHeader.getOriginalSoCode());
        // 设置收货人地址
        cartonPrintDTO.setClientAddress(PrintCartonHelper.buildAddress(doHeader));
        cartonPrintDTO.setClientName(doHeader.getConsigneeName());
        cartonPrintDTO.setClientPhone(PrintCartonHelper.buildTelOrMobile(doHeader));
        // 设置寄件人地址信息
        setSendAddressInfo(doHeader,cartonPrintDTO);
        // 设置始发地代码编号和目的地代码编号
        DoWaveEx doWaveEx = doWaveExService.findByDoHeaderId(doHeader.getId());
        if (null != doWaveEx) {
            //大头笔&集包地
            cartonPrintDTO.setShortAddress(doWaveEx.getShortAddress());
            cartonPrintDTO.setPackageCenterName(doWaveEx.getPackageCenterName());

            cartonPrintDTO.setStartAddressName(doWaveEx.getOriginName());
            cartonPrintDTO.setStartAddressCode(doWaveEx.getOriginCode());
            cartonPrintDTO.setDestAddressName(doWaveEx.getDestinationName());
            cartonPrintDTO.setDestAddressCode(doWaveEx.getDestinationCode());
        }
        // 运单号
        cartonPrintDTO.setWayBill(carton.getWayBill());
        // 设置图片路径
        cartonPrintDTO.setBasePrintImgPath(PrintHelper.getBasePrintImgPath());

        cartonPrintDTO.setRefNo1(doHeader.getOriginalSoCode());

        setProductInfo(doHeader, cartonPrintDTO);

        printReportDtos.add(cartonPrintDTO);
    }

    @Override
    protected PrintData genPrintData(List<PrintReportDto> dtoList, DeliveryOrderHeader doHeader) {
        PrintData printData = new PrintData();
        printData.setDtoList(dtoList);
        printData.setPrintCfg(new PrintCfg("wayBillHTKY", "100", "180"));
        return printData;
    }
}
