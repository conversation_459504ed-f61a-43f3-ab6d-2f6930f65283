package com.daxia.wms.delivery.recheck.action;

import java.io.Serializable;
import java.util.List;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.ActionBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.wms.delivery.recheck.entity.CartonDetail;
import com.daxia.wms.delivery.recheck.entity.CartonDetailHis;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeaderHis;
import com.daxia.wms.delivery.recheck.service.CartonService;

/**
 * 装箱详细信息Action
 */
@Name("com.daxia.wms.delivery.cartonDetailInfoAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class CartonDetailInfoAction extends ActionBean implements Serializable{

    /**
     * 
     */
    private static final long serialVersionUID = -4425414276148208215L;
    
    private long cartonHeaderId;
    private List<CartonDetail> cartonDetailList;
    private List<CartonDetailHis> cartonDetailHisList;
    private boolean queryMode;
    @In
    private CartonService cartonService;

    /**
     *  初始化，供页面跳转调用
     */
    public void initPage() {
        if (queryMode) {
            CartonHeaderHis cartonHeaderHis = cartonService.queryCartonHeaderHisById(cartonHeaderId);
            if (null != cartonHeaderHis) {
                cartonDetailHisList = cartonHeaderHis.getCartonDetails();
            }
        } else {
            CartonHeader cartonHeader = cartonService.getCartonHeader(cartonHeaderId);
            if (cartonHeader != null) {
                this.cartonDetailList = cartonHeader.getCartonDetails();
            }
        }
    }
    
    public long getCartonHeaderId() {
        return cartonHeaderId;
    }

    public void setCartonHeaderId(long cartonHeaderId) {
        this.cartonHeaderId = cartonHeaderId;
    }

    public List<CartonDetailHis> getCartonDetailHisList() {
        return cartonDetailHisList;
    }

    public void setCartonDetailHisList(List<CartonDetailHis> cartonDetailHisList) {
        this.cartonDetailHisList = cartonDetailHisList;
    }

    public List<CartonDetail> getCartonDetailList() {
        return cartonDetailList;
    }
    
    public void setCartonDetailList(List<CartonDetail> cartonDetailList) {
        this.cartonDetailList = cartonDetailList;
    }
    
    public boolean isQueryMode() {
        return queryMode;
    }

    public void setQueryMode(boolean queryMode) {
        this.queryMode = queryMode;
    }
    
}
