package com.daxia.wms.delivery.print.helper;

import org.apache.commons.lang.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 金额转换器      数字转换成大写
 */
@lombok.extern.slf4j.Slf4j
public class CurrencyConverter
{

	private static final double MAXIMUM_NUMBER = 9999999999999.99d;

	private static final String CN_ZERO = "零";
	private static final String CN_DOLLAR = "圆";
	private static final String CN_INTEGER = "整";

	private static final String[] digits = new String[] { "零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖" };
	private static final String[] radices = new String[] { "", "拾", "佰", "仟" };
	private static final String[] bigRadices = new String[] { "", "万", "亿", "万" };
	private static final String[] decimals = new String[] { "角", "分" };

	private static final Pattern CHAR_PATTERN = Pattern.compile("/[^,.\\d]/", Pattern.CASE_INSENSITIVE);

	private static final Pattern NUM_PATTERN = Pattern.compile("/^((\\d{1,3}(,\\d{3})*(.((\\d{3},)*\\d{1,3}))?)|(\\d+(.\\d+)?))$/", Pattern.CASE_INSENSITIVE);


	/**
	 * 转换大写
	 * 
	 * @param currencyDigits
	 * @return
	 */
	public static String convert(String currencyDigits) {

		//如果金额格式有错，返回null
		if (!check(currencyDigits)){
			return null;
		}
		String integral = null; // 整数部分
		String decimal = null; // 小数部分
		StringBuilder outputCharacters = new StringBuilder(); // 最终转换输出结果

		String d = null;
		int zeroCount = 0, p = 0, quotient = 0, modulus = 0;

		// 删除数字中的逗号,
		currencyDigits = currencyDigits.replace("/,/g", "");
		// 删除数字左边的0
		currencyDigits = currencyDigits.replace("/^0+/", "");

		// 拆分数字中的整数与小数部分
		String[] parts = currencyDigits.split("\\.");
		if (parts.length > 1) {
			integral = parts[0];
			decimal = parts[1];
			//处理小数点后数字
			if (decimal.length() > 2) {
				//当大于0.994元时，处理进位到元的问题 
				if(Long.valueOf(decimal).compareTo(994L) > 0){
					integral = Long.toString(Long.valueOf(integral) + 1);
					decimal = "0";
				}else{
					Boolean flag = false;
					if(decimal.substring(0,1).equals("0")){
						flag = true;
					}
					long dd = Math.round(Double.parseDouble("0."+decimal) * 100) ;
					decimal = Long.toString(dd);
					if (decimal.length() < 2 && flag) {
						decimal = "0"+decimal;
					}
				}
			}
		} else {
			integral = parts[0];
			decimal = "0";
		}

		// Start processing:
		// Process integral part if it is larger than 0:
		if (Double.parseDouble(integral) > 0) {

			zeroCount = 0;

			for (int i = 0; i < integral.length(); i++) {

				p = integral.length() - i - 1;
				d = integral.substring(i, i + 1);

				quotient = p / 4;
				modulus = p % 4;
				if (d.equals("0")) {
					zeroCount++;
				} else {
					if (zeroCount > 0) {
						outputCharacters.append(digits[0]);
					}
					zeroCount = 0;
					outputCharacters.append(digits[Integer.parseInt(d)] + radices[modulus]);
				}
				if (modulus == 0 && zeroCount < 4) {
					outputCharacters.append(bigRadices[quotient]);
				}
			}
			outputCharacters.append(CN_DOLLAR);
		}

		// Process decimal part if it is larger than 0:
		if (Double.parseDouble(decimal) > 0) {
			for (int i = 0; i < decimal.length(); i++) {
				d = decimal.substring(i, i + 1);
				if (!d.equals("0")) {
					outputCharacters.append(digits[Integer.parseInt(d)] + decimals[i]);
				} else {
					if (i == 0) {
						outputCharacters.append(CN_ZERO);
					}
				}
			}
		}
		
		// Confirm and return the final output string:
		if (outputCharacters.toString().equals("")) {
			outputCharacters = new StringBuilder();
			outputCharacters.append(CN_ZERO + CN_DOLLAR);
		}
		if (decimal == null || decimal.equals("0")) {
			outputCharacters.append(CN_INTEGER);
		}

		return outputCharacters.toString();
	}
	
	/**
	 * 检查输入数字的合法性
	 * 
	 * @param currencyDigits
	 * @return
	 */
	private static boolean check(final String currencyDigits) {
		boolean b = true;
		//传入空
		if (currencyDigits == null || currencyDigits.trim().equals("")) {
			b = false;
		}
		
		//含有非法字符
		Matcher matcher = CHAR_PATTERN.matcher(currencyDigits);

		if (matcher.find()) {
			b = false;
		}

		//数字格式错误
		matcher = NUM_PATTERN.matcher(currencyDigits);
		if (matcher.find()) {
			b = false;
		}

		//超出最大范围
		if (StringUtils.isNotEmpty(currencyDigits) && Double.parseDouble(currencyDigits) > MAXIMUM_NUMBER) {
			b = false;
		}

		return b;
	}
}
