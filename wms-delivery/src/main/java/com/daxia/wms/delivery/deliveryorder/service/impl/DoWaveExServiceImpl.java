package com.daxia.wms.delivery.deliveryorder.service.impl;

import com.daxia.framework.common.util.ListUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoWaveExDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.delivery.deliveryorder.service.DoWaveExService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Observer;
import org.jboss.seam.annotations.Transactional;

import java.util.List;

@Name("com.daxia.wms.delivery.doWaveExService")
@lombok.extern.slf4j.Slf4j
public class DoWaveExServiceImpl implements DoWaveExService {

	@In
	private DoWaveExDAO doWaveExDAO;
	
	@Override
	@Transactional
	@Observer("DEL_WAVE_CRITERIA_EX_RULE_EVENT")
	public void updateDoWaveEx2Normal(Long ruleId) {
		doWaveExDAO.updateDoExWhenDelRule(ruleId);
	}

	@Override
	@Transactional
	public void save(DoWaveEx doWaveEx) {
		boolean isExist = doWaveExDAO.isExists("doHeaderId", doWaveEx.getDoHeaderId(), null);
		if (isExist) {
			throw new DeliveryException(DeliveryException.DO_WAVE_EX_IS_ALREADY_EXIST);
		}
		doWaveExDAO.save(doWaveEx);
	}
	
	@Override
	@Transactional
	public void update(DoWaveEx doWaveEx) {
		doWaveExDAO.update(doWaveEx);
	}
	
	@Override
	public DoWaveEx findByDoHeaderId(Long doHeaderId) {
		return doWaveExDAO.findByDoHeaderId(doHeaderId);
	}
	
	@Override
	public Constants.AutoWaveType findAutoWaveType(List<Long> doIds) {
		if (ListUtil.isNullOrEmpty(doIds)) {
			return null;
		}
		
		List<Integer> autoWaveTypes = doWaveExDAO.findAutoWaveType(doIds);
		if (ListUtil.isNotEmpty(autoWaveTypes)) {
			Integer autoWaveType = autoWaveTypes.get(0);
			if (autoWaveType != null) {
				return Constants.AutoWaveType.of(autoWaveType);
			}
		}
		return null;
	}
}
