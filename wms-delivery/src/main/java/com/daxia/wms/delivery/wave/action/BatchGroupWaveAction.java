package com.daxia.wms.delivery.wave.action;

import com.alibaba.fastjson.JSON;
import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Constants.PrintStatus;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.filter.PickHeaderFilter;
import com.daxia.wms.delivery.pick.service.PickHeaderService;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.dto.PrintCfg;
import com.daxia.wms.delivery.print.dto.SingleWaveLabelPrintDTO;
import com.daxia.wms.delivery.print.service.PrintDoService;
import com.daxia.wms.delivery.print.service.PrintGroupWaveLabelService;
import com.daxia.wms.delivery.print.service.PrintService;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.wave.dto.BatchGroupDoCartonInfoDTO;
import com.daxia.wms.delivery.wave.dto.BatchGroupWaveDTO;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.filter.BatchGroupDoCartonFilter;
import com.daxia.wms.delivery.wave.filter.BatchGroupWaveFilter;
import com.daxia.wms.delivery.wave.service.BatchGroupWaveService;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.print.dto.PrintData;
import com.google.gson.Gson;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.security.Restrict;
import org.json.JSONArray;

import java.util.*;

/**
 * 促销单品波次及箱信息
 */
@Name("batchGroupWaveAction")
@Restrict("#{identity.hasPermission('delivery.singleWaveCarton')}")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class BatchGroupWaveAction extends PagedListBean<BatchGroupWaveDTO> {

    private static final long serialVersionUID = -3674135709740412368L;

    @In
    private BatchGroupWaveService batchGroupWaveService;

    @In
    private WaveService waveService;

    @In
    private CartonService cartonService;

    @In
    private PrintGroupWaveLabelService printGroupWaveLabelService;

    @In
    private PickHeaderService pickHeaderService;
    @In
    private PrintService printService;
    @In(create = true)
    private PrintDoService printDoService;

    private BatchGroupWaveFilter filter;

    private BatchGroupDoCartonFilter doCartonFilter;

    private String waveNo;

    private String waveStatus;

    private String cartonNo;

    private WaveHeader wave;

    private List<BatchGroupDoCartonInfoDTO> dtoList;

    private List<SingleWaveLabelPrintDTO> printWaveLabelDtos;

    private boolean initialized = false;

    private String printDatas = "[]";
    private String printContent = "";
    private Boolean isAllCartonNotPrinted = Boolean.FALSE;
    /**
     * 打印参数
     */
    private PrintCfg printCfg;

    private List<CartonPrintDTO> printCartonDTOs;

    private Integer maxCartonNum4Print;

    /**
     * 是否使用A5纸打印拣货单
     */
    private Integer isPrintPickTaskByA5;

    public BatchGroupWaveAction() {
        super();
        filter = new BatchGroupWaveFilter();
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        filter.setToDate(calendar.getTime());
        calendar.add(Calendar.DATE, -6);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        filter.setFromDate(calendar.getTime());
        filter.setCatornPrintStatus(PrintStatus.NO.getValue());

        printCfg = new PrintCfg();
        printCfg.setPageType("A5");
        printCfg.setOrient("2");
        printCfg.setReportName("DO单");

        maxCartonNum4Print = SystemConfig.getConfigValueInt("print.carton.maxNum4Print", ParamUtil.getCurrentWarehouseId());
    }

    @Create
    @Loggable
    public void init(){
        this.isPrintPickTaskByA5 = printService.isPrintPickTaskByA5();
    }

    @Override
    public void query() {
        selectedMap.clear();
        DataPage<BatchGroupWaveDTO> dataPage = batchGroupWaveService.query(filter, getStartIndex(), getPageSize());
        this.populateValues(dataPage);
    }

    public void openSingleWaveCartonPage() {
        if (!initialized) {
            wave = waveService.getWaveHeaderByWaveNum(waveNo);
            queryWaveCartons();
            initialized = true;
        }
    }

    /**
     * 查看促销单品波次箱信息
     */
    public void queryWaveCartons() {
        dtoList = cartonService.findCartonAndDoInfoByWave(waveNo, null);
    }

    public void openSingleWaveDoPage() {
        if (!initialized) {
            doCartonFilter = new BatchGroupDoCartonFilter();
            wave = waveService.getWaveHeaderByWaveNum(waveNo);
            showDoInfoInWave();
            initialized = true;
        }
    }

    /**
     * 查看波次订单信息及箱统计信息
     */
    public void showDoInfoInWave() {
        dtoList = cartonService.findDoAndCartonInfoByWave(waveNo, doCartonFilter);
    }

    public void openSingleWaveCartonInfoPage() {
        if (!initialized) {
            doCartonFilter = new BatchGroupDoCartonFilter();
            wave = waveService.getWaveHeaderByWaveNum(waveNo);
            showCartonInfoInWave();
            initialized = true;
        }
    }

    /**
     * 查看促销单品箱信息
     */
    public void showCartonInfoInWave() {
        dtoList = cartonService.findCartonAndDoInfoByWave(waveNo, doCartonFilter);
    }

    public void printPick() {
        printContent = "";
        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }

        PickHeaderFilter pktFilter = new PickHeaderFilter();
        pktFilter.setWaveIds(ids);
        List<PickHeader> pkts = pickHeaderService.query(pktFilter);

        printContent = printService.printPickByPkt(pkts, true);
    }

    public void printDo(){
        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }

        List<String> pages = printService.getDoReportByWaveIds(ids);

        this.printDatas = new JSONArray(pages).toString();
    }

    /**
     * 打印波次信息标签及箱标签
     */
    public void printCartonLabel() {
        printDatas = "[]";
        printContent = "";
        List<Long> ids = getNotEmptySelectedIds();
        PrintData printData = printGroupWaveLabelService.printWaveCartonLabels(ids);
        // 设置提示信息
        if (printData.getUserDefine1() != null) {
            StringBuilder tipMsg = new StringBuilder();
            tipMsg.append("应打").append(printData.getUserDefine1())
                    .append("个，实打").append(printData.getUserDefine2()).append("个.");
            if (null != printData.getUserDefine3()) {
                tipMsg.append("其中:").append(printData.getUserDefine3())
                        .append("未打印");
            }
            this.sayMessage(tipMsg.toString());
        }
        printDatas = printData.getData();
        printContent = printData.getTemplateJs();
        System.out.println(printDatas);
    }

    /**
     * 按箱打印箱标签
     */
    @SuppressWarnings("unchecked")
    public void printCartonByCartons() {
        printDatas = "[]";
        printContent = "";
        List<Long> ids = getNotEmptySelectedIds();
        List<PrintData> dataList = printGroupWaveLabelService.printCartons(ids);
        printDatas = new Gson().toJson(dataList);
        printContent = getPrintJs(dataList);
    }

    private String getPrintJs(List<PrintData> dataList) {
        Map<String, String> templateMap = new HashMap<String, String>();
        String templateJs = "";
        for (PrintData printData : dataList) {
            if (!templateMap.containsKey(printData.getPrintCfg().getLodopTemplate())) {
                templateJs = templateJs + printData.getTemplateJs();
                templateMap.put(printData.getPrintCfg().getLodopTemplate(), templateJs + printData.getTemplateJs());
            }
        }
        return templateJs;
    }
    /**
     * 按DO打印箱标签
     */
    @SuppressWarnings("unchecked")
    public void printCartonByDos() {
        printDatas = "[]";
        printContent = "";
        List<Long> ids = getNotEmptySelectedIds();
        List<PrintData> dataList = printGroupWaveLabelService.printCartonsByDos(ids);
        printDatas = new Gson().toJson(dataList);
        printContent = getPrintJs(dataList);
    }

    /**
     * 校验选中箱子是否有已经打印过
     */
    public void vIsCartonPrinted() {
        List<Long> ids = getNotEmptySelectedIds();
        isAllCartonNotPrinted = cartonService.isAllCartonNotPrinted(ids, "CARTON");
    }

    /**
     * 校验选中do是否有已经打印过箱标签
     */
    public void vIsDoCartonPrinted() {
        List<Long> ids = getNotEmptySelectedIds();
        isAllCartonNotPrinted = cartonService.isAllCartonNotPrinted(ids, "DO");
    }

    /**
     * 获取选中行非空ids
     *
     * @return
     */
    private List<Long> getNotEmptySelectedIds() {
        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }
        selectedMap.clear();
        if (ListUtil.isNullOrEmpty(ids)) {
            this.sayMessage(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        return ids;
    }

    public BatchGroupWaveFilter getFilter() {
        return filter;
    }

    public void setFilter(BatchGroupWaveFilter filter) {
        this.filter = filter;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getWaveStatus() {
        return waveStatus;
    }

    public void setWaveStatus(String waveStatus) {
        this.waveStatus = waveStatus;
    }

    public WaveHeader getWave() {
        return wave;
    }

    public void setWave(WaveHeader wave) {
        this.wave = wave;
    }

    public List<BatchGroupDoCartonInfoDTO> getDtoList() {
        return dtoList;
    }

    public void setDtoList(List<BatchGroupDoCartonInfoDTO> dtoList) {
        this.dtoList = dtoList;
    }

    public String getCartonNo() {
        return cartonNo;
    }

    public void setCartonNo(String cartonNo) {
        this.cartonNo = cartonNo;
    }

    public BatchGroupDoCartonFilter getDoCartonFilter() {
        return doCartonFilter;
    }

    public void setDoCartonFilter(BatchGroupDoCartonFilter doCartonFilter) {
        this.doCartonFilter = doCartonFilter;
    }

    public List<SingleWaveLabelPrintDTO> getPrintWaveLabelDtos() {
        return printWaveLabelDtos;
    }

    public void setPrintWaveLabelDtos(List<SingleWaveLabelPrintDTO> printWaveLabelDtos) {
        this.printWaveLabelDtos = printWaveLabelDtos;
    }

    public PrintCfg getPrintCfg() {
        return printCfg;
    }

    public void setPrintCfg(PrintCfg printCfg) {
        this.printCfg = printCfg;
    }

    public List<CartonPrintDTO> getPrintCartonDTOs() {
        return printCartonDTOs;
    }

    public void setPrintCartonDTOs(List<CartonPrintDTO> printCartonDTOs) {
        this.printCartonDTOs = printCartonDTOs;
    }

    public Boolean getIsAllCartonNotPrinted() {
        return isAllCartonNotPrinted;
    }

    public void setIsAllCartonNotPrinted(Boolean isAllCartonNotPrinted) {
        this.isAllCartonNotPrinted = isAllCartonNotPrinted;
    }

    public Integer getMaxCartonNum4Print() {
        return maxCartonNum4Print;
    }

    public void setMaxCartonNum4Print(Integer maxCartonNum4Print) {
        this.maxCartonNum4Print = maxCartonNum4Print;
    }

    public String getPrintDatas() {
        return printDatas;
    }

    public void setPrintDatas(String printDatas) {
        this.printDatas = printDatas;
    }

    public Integer getIsPrintPickTaskByA5()
    {
        return isPrintPickTaskByA5;
    }

    public void setIsPrintPickTaskByA5(Integer isPrintPickTaskByA5)
    {
        this.isPrintPickTaskByA5 = isPrintPickTaskByA5;
    }

    public String getPrintContent() {
        return printContent;
    }

    public void setPrintContent(String printContent) {
        this.printContent = printContent;
    }
}
