package com.daxia.wms.delivery.transport.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.delivery.transport.entity.DoTransportReport;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

@Name("doTransportReportDAO")
@lombok.extern.slf4j.Slf4j
public class DoTransportReportDAO extends HibernateBaseDAO<DoTransportReport, Long> {

    private static final long serialVersionUID = -4294716361174654685L;

    public DoTransportReport getDoTransportReportByLoadId(Long loadId) {
        String hql = " from DoTransportReport o where o.loadId = :loadId";
        Query query = this.createQuery(hql);
        query.setParameter("loadId", loadId);
        query.setMaxResults(1);
        return (DoTransportReport) query.uniqueResult();
    }

}
