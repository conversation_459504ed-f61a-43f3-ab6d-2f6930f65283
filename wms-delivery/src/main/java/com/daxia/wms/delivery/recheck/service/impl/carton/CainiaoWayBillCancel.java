package com.daxia.wms.delivery.recheck.service.impl.carton;

import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.master.entity.CarrierCainiaoEx;
import com.daxia.wms.master.service.CarrierCainiaoExService;
import com.daxia.wms.master.service.ShopInfoService;
import com.daxia.wms.master.service.SkuCache;
import com.taobao.api.request.CainiaoWaybillIiCancelRequest;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.log.Log;

@Name("cainiaoWayBillCancel")
@lombok.extern.slf4j.Slf4j
public class CainiaoWayBillCancel extends CainiaoWayBillBase {

    @In
    CarrierCainiaoExService carrierCainiaoExService;

    @In
    SkuCache skuCache;

    @In
    ShopInfoService shopInfoService;



    public void reqeust(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        /*
        Carrier carrier = doHeader.getCarrier();
        CarrierCainiaoEx carrierCainiaoEx = carrierCainiaoExService.getByCarrier(carrier.getId());

        WarehouseCarrier warehouseCarrier = loadWarehouseCarrier(carrier.getId());
        CainiaoWaybillIiCancelRequest req = genWaybillApplyCancelRequest(doHeader, cartonHeader, carrierCainiaoEx);

        TaobaoClient client = new DefaultTaobaoClient(CainiaoConstants.getCainiaoConfig().getServerUrl(), warehouseCarrier.getAppKey(), warehouseCarrier.getAppSecret());
        try {
            CainiaoWaybillIiCancelResponse rsp = client.execute(req, warehouseCarrier.getAppToken());
            if (!rsp.isSuccess()) {
                log.error("Cainiao cancel error, request:" + req + ", response: " + rsp.getBody());
            }
        } catch (ApiException e) {
            log.error("Cainiao cancel error! ", e);
            //throw new DeliveryException(DeliveryException.WAYBILL_CAINIO_GET_ERROR, e.getMessage());
        }
        */
    }

    private CainiaoWaybillIiCancelRequest genWaybillApplyCancelRequest(DeliveryOrderHeader doHeader, CartonHeader cartonHeader, CarrierCainiaoEx carrierCainiaoEx) {
        CainiaoWaybillIiCancelRequest cancelRequest = new CainiaoWaybillIiCancelRequest();
        cancelRequest.setCpCode(carrierCainiaoEx.getCpCode());
        cancelRequest.setWaybillCode(cartonHeader.getWayBill());
        return cancelRequest;
    }
}