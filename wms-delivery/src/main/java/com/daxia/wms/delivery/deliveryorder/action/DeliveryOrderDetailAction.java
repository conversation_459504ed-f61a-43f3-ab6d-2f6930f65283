package com.daxia.wms.delivery.deliveryorder.action;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;

/**
 * Description:DO明细信息业务Action
 */
@Name("com.daxia.wms.delivery.deliveryOrderDetailAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class DeliveryOrderDetailAction extends PagedListBean<DeliveryOrderDetail> {

    private static final long serialVersionUID = -5239750150922682368L;
    
    public DeliveryOrderDetailAction() {
		super();
		// TODO Auto-generated constructor stub
	}



	@Override
	public void query() {
		// TODO Auto-generated method stub
		
	}
    
  
}
