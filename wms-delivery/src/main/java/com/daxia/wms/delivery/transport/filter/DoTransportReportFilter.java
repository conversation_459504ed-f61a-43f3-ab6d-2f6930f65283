/**
 *
 */
package com.daxia.wms.delivery.transport.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

@lombok.extern.slf4j.Slf4j
public class DoTransportReportFilter extends WhBaseQueryFilter {

    private static final long serialVersionUID = -3573678886508446586L;
    private String transportNo;

    private String doNo;

    private String loadNo;

    @Operation(fieldName = "o.transportNo", operationType = OperationType.EQUAL)
    public String getTransportNo() {
        return transportNo;
    }

    public void setTransportNo(String transportNo) {
        this.transportNo = transportNo;
    }

    @Operation(clause = " o.loadId in (SELECT loadHeaderId from LoadDetail i where i.deliveryOrderHeader.doNo =? ) ", operationType = OperationType.CLAUSE)
    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    @Operation(fieldName = "o.loadNo", operationType = OperationType.EQUAL)
    public String getLoadNo() {
        return loadNo;
    }

    public void setLoadNo(String loadNo) {
        this.loadNo = loadNo;
    }
}
