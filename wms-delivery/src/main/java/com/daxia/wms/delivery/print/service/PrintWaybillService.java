package com.daxia.wms.delivery.print.service;

import java.util.List;

import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.entity.TempCarton;
import com.daxia.wms.print.dto.PrintData;

public interface PrintWaybillService {

    public void doRepare(DeliveryOrderHeader doHeader, List<Long> cartonIds);

    public PrintData genPrintData(DeliveryOrderHeader doHeader, List<Long> cartonIds);
    
    PrintData genTempCartonPrintData(DeliveryOrderHeader doHeader, TempCarton tempCarton);
}
