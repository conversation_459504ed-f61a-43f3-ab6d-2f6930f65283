package com.daxia.wms.delivery.wave.service.impl;

import com.daxia.framework.common.util.*;
import com.daxia.framework.redis.RedisCacheProvider;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.BindDocType;
import com.daxia.wms.Constants.WaveAllocateSortingBin;
import com.daxia.wms.Constants.WaveStatus;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoAllocateHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.dao.DoHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.service.PickHeaderService;
import com.daxia.wms.delivery.pick.service.PickTaskService;
import com.daxia.wms.delivery.wave.dao.WaveDAO;
import com.daxia.wms.delivery.wave.dto.SelfCarrierNullStationDTO;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.filter.SelfCarrierNullStationFilter;
import com.daxia.wms.delivery.wave.filter.WaveHeaderFilter;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.master.component.BusinessCenterComponent;
import com.daxia.wms.master.dto.AutoCompleteDTO;
import com.daxia.wms.master.entity.Container;
import com.daxia.wms.master.rule.filter.BigWaveFilter;
import com.daxia.wms.master.service.ContainerService;
import com.daxia.wms.master.service.SortingBinService;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.daxia.wms.delivery.DeliveryException.WAVE_LOCK_EXIST_ERROR;

/**
 * 波次业务逻辑service实现类
 */
@Name("com.daxia.wms.delivery.waveService")
@lombok.extern.slf4j.Slf4j
public class WaveServiceImpl implements WaveService, Serializable {
    @In
    private WaveDAO waveDAO;

    @In
    private DoHeaderDAO doHeaderDAO;

    @In
    private DoAllocateHeaderDAO doAllocateHeaderDAO;

    @In
    private DeliveryOrderService deliveryOrderService;

    @In
    private SortingBinService sortingBinService;

    @In
    private PickTaskService pickTaskService;

    @In
    private ContainerService containerService;

    @In
    private PickHeaderService pickHeaderService;

    @In
    private RedisCacheProvider redisCacheProvider;

    @In
    private BusinessCenterComponent businessCenterComponent;

    /**
     * 根据波次Id获取波次头
     *
     * @param waveId 波次Id
     * @return 波次头
     */
    @Override
    public WaveHeader getWave(Long waveId) {
        return waveDAO.get(waveId);
    }

    /**
     * 根据WaveHeaderFilter分页查询波次信息
     *
     * @param filter     波次头filter
     * @param startIndex 起始行
     * @param pageSize   每页行数
     * @return 波次头分页信息DataPage<WaveHeader>
     */
    @Override
    public DataPage<WaveHeader> query(WaveHeaderFilter filter, int startIndex, int pageSize) {
        return waveDAO.findRangeByFilter(filter, startIndex, pageSize);
    }

    /**
     * 根据波次号获取波次头
     *
     * @param waveNo 波次号
     * @return 波次头实体
     */
    @Override
    public WaveHeader queryWaveByNo(String waveNo) {
        List<WaveHeader> waves = waveDAO.findWaveByNo(waveNo);
        if (waves == null || waves.size() == 0 || waves.size() > 1) {
            throw new DeliveryException(DeliveryException.WAVE_NO_ERRER);
        }
        return waves.get(0);
    }

    @Override
    public WaveHeader queryWaveByContainerNo(String containerNo) {
        Container container = containerService.getContainerByNo(containerNo);
        if (container == null) {
            return null;
        }
        if (StringUtil.isEmpty(container.getDocType())) {
            return null;
        }
        if (BindDocType.WAVE.getValue().equals(container.getDocType())) {
            return waveDAO.getWaveHeaderByWaveNum(container.getDocNo());
        } else if (BindDocType.PICK.getValue().equals(container.getDocType())) {
            PickHeader pickHeader = pickHeaderService.getPktHeaderByPktNo(container.getDocNo());
            if (pickHeader == null) {
                return null;
            } else {
                return pickHeader.getWaveHeader();
            }
        }
        return null;
    }

    /**
     * 更新波头次状态
     *
     * @param wave   波次头
     * @param status 波次状态
     */
    @Override
    @Transactional
    public void updateWaveStatus(WaveHeader wave, String status) {
        wave.setWaveStatus(status);
        waveDAO.saveOrUpdate(wave);
    }

    /**
     * 根据发货单Id获取其波次
     *
     * @param doId 发货单Id
     * @return 波次头
     */
    @Override
    public WaveHeader getWaveHeaderByDoId(Long doId) {

        return waveDAO.getWaveHeaderByDoId(doId);
    }

    /**
     * 更新波次头
     *
     * @param waveHeader 波次头
     */
    @Override
    @Transactional
    public void updateWaveHeader(WaveHeader waveHeader) {
        waveDAO.update(waveHeader);
    }

    /**
     * 根据波次头Id List集合查询波次头
     *
     * @param ids 根据波次头Id List
     * @return 波次头 List
     */
    @Override
    public List<WaveHeader> getWaveList(List<Long> ids) {
        return waveDAO.getByKeys(ids);
    }

    /**
     * 批量更新波次打印标识
     *
     * @param ids  波次头id List
     * @param type 波次打印标识
     */
    @Override
    @Transactional
    public void updateWavePrintFlag(List<Long> ids, Integer type) {
        waveDAO.updateWavePrintFlag(ids, type);
    }

    /**
     * 批量更新波次打印购物清单标记
     *
     * @param ids   波次头id List
     * @param value 打印购物清单标记值（0：已消费，1：需要打印）
     */
    @Override
    @Transactional
    public void updateDoNeedPrintFlag(List<Long> ids, Integer value) {
        waveDAO.updateDoNeedPrintFlag(ids, value);
    }

    /**
     * 根据波次号查询波次
     *
     * @param waveNum 波次号
     * @return 波次头
     */
    @Override
    public WaveHeader getWaveHeaderByWaveNum(String waveNum) {
        return waveDAO.getWaveHeaderByWaveNum(waveNum);
    }

    /**
     * 删除波次,同时判断分拣柜和集货位的排队数是否需要减1
     *
     * @param waveHeader
     */
    @Override
    @Transactional
    public void removeWaveHeader(WaveHeader waveHeader) {
        // 分拣柜的排队数减1
        if (WaveStatus.PARTSORTED.getValue().compareTo(waveHeader.getWaveStatus()) > 0) {
            // 分拣开始，分拣柜排队数量减少1
            if (waveHeader.getSortGridId() != null) {
                sortingBinService.subWaveQty(waveHeader.getSortGridId());
            }
        }
        // 删除波次
        waveDAO.remove(waveHeader.getId());
    }

    /**
     * 根据查询条件查询大波次业务DO分页信息
     *
     * @param bigWaveFilter 查询过滤器
     * @param startIndex    查询起始记录下标
     * @param pageSize      分页单位
     * @return DO分页信息
     * @throws DeliveryException 查询失败抛出的异常
     */
    @Override
    public DataPage<DoHeaderDto> findBigWaveDoList(BigWaveFilter bigWaveFilter, int startIndex, int pageSize)
            throws DeliveryException {
        DataPage<DoHeaderDto> page = this.doAllocateHeaderDAO.findBigWaveDoList(bigWaveFilter, startIndex,
                pageSize);

        List<DoHeaderDto> dataList = page.getDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            return page;
        }

        Map<String, String> channelMap = businessCenterComponent.getChannelMap();
        Map<String, String> storeMap = businessCenterComponent.getStoreMap();

        List<DoHeaderDto> collect = dataList.stream()
                .map(doHeaderDTO -> {
                    String channelName = doHeaderDTO.getChannelCode();
                    String storeName = doHeaderDTO.getStoreCode();
                    if (Objects.nonNull(doHeaderDTO.getChannelCode()) && Objects.nonNull(doHeaderDTO.getStoreCode())) {

                        channelName = channelMap.getOrDefault(doHeaderDTO.getChannelCode(),
                                doHeaderDTO.getChannelCode());
                        storeName = storeMap.getOrDefault(doHeaderDTO.getStoreCode(),
                                doHeaderDTO.getStoreCode());
                    }

                    doHeaderDTO.setChannelName(channelName);
                    doHeaderDTO.setStoreName(storeName);
                    return doHeaderDTO;
                })
                .collect(Collectors.toList());

        page.setDataList(collect);

        return page;
    }

    /**
     * 通过波次的do单状态判断波次是否处于分拣完成状态
     *
     * @param waveId 波次Id
     * @return 波次下do全部分拣完成返回true, 否则返回false
     */
    @Override
    public boolean isWaveSorted(Long waveId) {
        List<DeliveryOrderHeader> doHeaders = doHeaderDAO.queryDoHeadersInWave(waveId);
        for (DeliveryOrderHeader doHeader : doHeaders) {
            BigDecimal doStatus = new BigDecimal(doHeader.getStatus());
            if (doStatus.compareTo(new BigDecimal(Constants.DoStatus.ALLSORTED.getValue())) == -1) {
                return false;
            }
        }
        return true;
    }

    /**
     * 根据拣货单号查询其波次
     *
     * @param pktNo 拣货单号
     * @return 波次实体
     */
    @Override
    public WaveHeader getWaveHeaderByPktNo(String pktNo) {
        return waveDAO.getWaveHeaderByPktNo(pktNo);
    }

    /**
     * 根据波次Id查询波次
     *
     * @param waveId 波次号
     * @return 波次实体
     */
    @Override
    public WaveHeader findWaveById(Long waveId) {
        return waveDAO.findWaveById(waveId);
    }

    /**
     * TODO 可删除
     *
     * @param filter
     * @return
     */
    @Deprecated
    @Override
    public List<WaveHeader> findByFilter(WaveHeaderFilter filter) {
        return waveDAO.findByFilter(filter);
    }

    @Override
    public DataPage<SelfCarrierNullStationDTO> findSelInvoiceErrorDoList(SelfCarrierNullStationFilter selCarrierFilter,
            int startIndex, int pageSize) throws DeliveryException {
        return doHeaderDAO.findSelInvoiceErrorDoList(selCarrierFilter, startIndex, pageSize);
    }

    @Override
    public WaveHeader queryByDoNo(String doNo) {
        return waveDAO.queryByDoNo(doNo);
    }

    /**
     * 分页查询波次单
     *
     * @param waveHeaderFilter
     * @param startIndex
     * @param pageSize
     * @return
     */
    @Override
    public DataPage<WaveHeader> findWaveHeaderByFilter(WaveHeaderFilter waveHeaderFilter, int startIndex,
            int pageSize) {
        return waveDAO.findWaveHeaderByFilter(waveHeaderFilter, startIndex, pageSize);
    }

    @Override
    @Transactional
    public void releaseSortingBin(WaveHeader wave, DeliveryOrderHeader doHeader) {
        if (null == doHeader) {
            // 随机取出波次下某订单
            doHeader = waveDAO.findOneDoInWave(wave.getId());
        }
        // 波次下订单已绑定分拣柜ID，说明波次曾经分拣过，当时已经释放过分拣柜了，此时不用再次释放
        if (null != doHeader && null != doHeader.getSortingBinId()) {
            return;
        }
        if (WaveStatus.ALLPICKED.getValue().equals(wave.getWaveStatus())) {
            // 分拣开始，分拣柜排队数量减少1
            if (wave.getSortGridId() != null) {
                sortingBinService.subWaveQty(wave.getSortGridId());
            }
        }
    }

    /**
     * RF扫描容器或者拣货标签后，或者拣货绑定容器、拆箱、换箱时，是否需要重新分配分拣柜
     *
     * @param waveId
     * @param isPick 是否是拣货
     */
    public boolean needReAllocateSortingBin(Long waveId, boolean isPick) {
        Integer reAllocatedSortingBinFlag = SystemConfig.getConfigValueInt("need_reAllocated_SortingBin",
                ParamUtil.getCurrentWarehouseId());
        if (!YesNo.YES.getValue().equals(reAllocatedSortingBinFlag)) {
            return false;
        }
        return waveDAO.needReAllocateSortinhBin(waveId, isPick);
    }

    @Override
    @Transactional
    public void reAllocateSoringBin(WaveHeader waveHeader, boolean isPick) {
        if (needReAllocateSortingBin(waveHeader.getId(), isPick)) {
            String waveStatus = waveHeader.getWaveStatus();
            // 如果波次分拣完成（一个波次一个DO），则不需要对分拣柜排队数增减
            if (!WaveStatus.ALLSORTED.getValue().equals(waveStatus)) {
                // 先减原分拣柜的排队数
                Long oldSortingBin = waveHeader.getSortGridId();
                sortingBinService.subWaveQty(oldSortingBin);
                waveDAO.getSession().flush();
            }

            // 重新分配分拣柜
            List<DeliveryOrderHeader> doHeaders = waveHeader.getDoHeaders();
            String doType = doHeaders.get(0).getDoType();
            Long newSortingBin = sortingBinService.assignSortBin(doType, false, waveHeader.getAutoType());
            if (newSortingBin != null) {
                waveHeader.setSortGridId(newSortingBin);
                if (isPick) {
                    waveHeader.setAllocateSortingBin(WaveAllocateSortingBin.PICK.getValue());
                } else {
                    waveHeader.setAllocateSortingBin(WaveAllocateSortingBin.REALLOCATE.getValue());
                }
                waveDAO.update(waveHeader);
                if (!WaveStatus.ALLSORTED.getValue().equals(waveStatus)) {
                    // 新分拣柜的排队数+1
                    sortingBinService.addWaveQty(newSortingBin);
                }
            }
        }
    }

    @Override
    public List<Object> getWaveMergeInfo(Long partitionId, Timestamp createTimeFrom,
            Timestamp createTimeTo, String mergeStatusFrom, String mergeStatusTo, int startIndex, int pageSize) {
        return waveDAO.getWaveMergeInfo(partitionId, createTimeFrom, createTimeTo, mergeStatusFrom,
                mergeStatusTo, startIndex, pageSize);
    }

    @Override
    public Long getWaveMergeInfoCount(Long partitionId, Timestamp createTimeFrom,
            Timestamp createTimeTo, String mergeStatusFrom, String mergeStatusTo) {
        return waveDAO.getWaveMergeInfoCount(partitionId, createTimeFrom, createTimeTo, mergeStatusFrom,
                mergeStatusTo);
    }

    @Override
    public List<Object> getFreeSortBins() {
        return waveDAO.getFreeSortBins();
    }

    @Override
    @Transactional
    public void updateWaveSortingStatus(Long waveHeaderId) {
        WaveHeader wave = this.getWave(waveHeaderId);
        if (this.isWaveSorted(wave.getId())) {
            wave.setWaveStatus(Constants.WaveStatus.ALLSORTED.getValue());
            this.updateWaveHeader(wave);
        } else {
            if (!Constants.WaveStatus.PARTSORTED.getValue().equals(wave.getWaveStatus())) {
                wave.setWaveStatus(Constants.WaveStatus.PARTSORTED.getValue());
                this.updateWaveHeader(wave);
            }
        }
    }

    /**
     * 波次是否包含酒类随附单
     */
    @Override
    public boolean isWineWave(String waveNo) {
        return waveDAO.isWineWave(waveNo);
    }

    @Override
    @Transactional
    public void updatePromotedWaveAndPickTask(WaveHeader waveHeader, String updateBy) {
        waveHeader.setPromoteSkuPickFlag(Constants.YesNo.YES.getValue());
        updateWaveHeader(waveHeader);
        pickTaskService.updatePickTaskInfo(waveHeader.getId(), updateBy);
    }

    @Override
    @Transactional
    public Integer addFailNum(Long id) {
        waveDAO.getSession().clear();

        WaveHeader waveHeader = this.getWave(id);
        Integer failNum = waveHeader.getAutoFailTime() == null ? 0 : waveHeader.getAutoFailTime();
        failNum = failNum + 1;
        waveHeader.setAutoFailTime(failNum);

        this.updateWaveHeader(waveHeader);

        this.doAllocateHeaderDAO.getSession().flush();
        return failNum;
    }

    @Override
    public List<WaveHeader> findWaveByPrintFlg(Integer printFlg) {
        return waveDAO.findWaveByPrintFlg(printFlg);
    }

    @Override
    public Boolean isWaveAllBindedInvoice(Long waveId) {
        return waveDAO.isWaveAllBindedInvoice(waveId);
    }

    @Override
    public List<AutoCompleteDTO> findSkuHelperData(Long waveId) {
        return waveDAO.findSkuHelperData(waveId);
    }

    @Override
    public long getWaveDocQty(Integer volumeType, Integer bigWaveFlg) {
        if (YesNo.YES.getValue().equals(bigWaveFlg)) {
            return Long.valueOf(NullUtil.notNull(
                    SystemConfig.getConfigValueInt("BIG_WAVE_DOC_QTY", ParamUtil.getCurrentWarehouseId()), 100));
        } else {
            String strMaxQty = NullUtil.notEmpty(
                    SystemConfig.getConfigValue("VOLUME_WAVE_DOC_QTY", ParamUtil.getCurrentWarehouseId()), "30,30,30");
            return Long.valueOf(strMaxQty.split(",")[volumeType]);
        }
    }

    @Override
    public List<AutoCompleteDTO> loadSkuByWaveId(Long waveId) {
        return waveDAO.loadSkuByWaveId(waveId);
    }

    @Override
    public void checkVersion(Long waveId, Integer version) {
        if (!waveDAO.checkVersion(waveId, version)) {
            throw new DeliveryException(DeliveryException.CLIENT_PRINT_WAVE_VERSION_ERROR);
        }
    }

    @Override
    @Transactional
    public void updatePrinting(Long waveId, String printBy) {
        WaveHeader waveHeader = this.getWave(waveId);
        waveHeader.setUpdatedAt(DateUtil.getNowTime());
        waveHeader.setPrintBy(printBy);
        this.updateWaveHeader(waveHeader);
    }

    /**
     * map: [{warehouseId: 1, id: 1}]
     * 
     * @param autoFlag
     * @return
     */
    @Override
    public List<Map<String, Object>> getAutoWave(Integer autoFlag) {
        return waveDAO.getAutoWave(autoFlag);
    }

    @Override
    @Transactional
    public void updateFailedNumber(Long waveId, Integer failedType) {
        WaveHeader waveHeader = this.getWave(waveId);
        if (waveHeader != null) {
            if (waveHeader.getFailedType().equals(failedType)) {
                waveHeader.setFailedNumber(waveHeader.getFailedNumber() + 1);
            } else {
                waveHeader.setFailedNumber(1);
                waveHeader.setFailedType(failedType);
            }

            waveDAO.update(waveHeader);
        }
    }

    @Override
    public String getDistinctCarrierName(Long waveId) {
        return waveDAO.getDistinctCarrierName(waveId);
    }

    @Override
    public Long getWaveMerchantId(Long waveId) {
        List<Long> merchentIds = waveDAO.findDoMerchentIds(waveId);
        if (CollectionUtils.isNotEmpty(merchentIds) &&
                merchentIds.size() == 1) {
            return merchentIds.get(0);
        }
        return null;
    }

    @Override
    public long getUnpickDocNum(String unpickedRegionCodes) {
        return waveDAO.getUnpickDocNum(unpickedRegionCodes);
    }

    @Override
    public void tryRequiredLock() {
        boolean locked = redisCacheProvider.putNx("wave_lock", ParamUtil.getCurrentWarehouseId() + "",
                System.currentTimeMillis(), 180);
        if (!locked) {
            throw new DeliveryException(WAVE_LOCK_EXIST_ERROR);
        }
    }

    @Override
    public void releaseLock() {
        redisCacheProvider.remove("wave_lock", ParamUtil.getCurrentWarehouseId() + "");
    }
}