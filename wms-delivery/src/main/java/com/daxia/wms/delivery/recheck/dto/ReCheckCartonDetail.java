package com.daxia.wms.delivery.recheck.dto;

import java.math.BigDecimal;

/**
 * 一个箱子里面某一个商品的详细装箱记录
 */
@lombok.extern.slf4j.Slf4j
public class ReCheckCartonDetail {
	private ReCheckRecord record;
	private ReCheckCartonInfo cartonInfo;
	private BigDecimal numberInCarton;
	private int packQty;

	
	public ReCheckCartonDetail(ReCheckCartonInfo cartonInfo, ReCheckRecord record, BigDecimal initNumberInCarton){
		this.record = record;
		this.numberInCarton = initNumberInCarton;
		this.cartonInfo = cartonInfo;
	}
	
	public Long getProductId() {
		return record.getProductId();
	}
	
	public void setRecord(ReCheckRecord record){
		this.record = record;
	}
	
	public ReCheckRecord getRecord() {
		return record;
	}

	public BigDecimal getNumberInCarton() {
		return numberInCarton;
	}
	public void setNumberInCarton(BigDecimal numberInCarton) {
		this.numberInCarton = numberInCarton;
	}
	
	public ReCheckCartonInfo getCartonInfo() {
		return cartonInfo;
	}

	public void increaseNumberInCarton(BigDecimal number) {
		this.numberInCarton = this.numberInCarton.add(number);
	}

    public int getPackQty() {
        return packQty;
    }

    public void setPackQty(int packQty) {
        this.packQty = packQty;
    }
}
