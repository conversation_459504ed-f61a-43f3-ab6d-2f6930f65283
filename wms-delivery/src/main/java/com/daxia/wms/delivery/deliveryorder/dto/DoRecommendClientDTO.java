package com.daxia.wms.delivery.deliveryorder.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import org.springframework.beans.BeanUtils;

import com.daxia.wms.wave.vo.DoRecommendDTO;

@lombok.extern.slf4j.Slf4j
public class DoRecommendClientDTO implements Serializable {

    private static final long serialVersionUID = -871453260441873569L;

    private Set<String> aisles = new HashSet<String>();
    private Long doHeaderId;
    private BigDecimal volume;
    private BigDecimal weight;
    private BigDecimal expectedQty;
    private BigDecimal coselectionRate;
    private Date planShipTime;

    private Boolean isSplited;

    public Boolean getIsSplited() {
        return isSplited;
    }

    public void setIsSplited(Boolean isSplited) {
        this.isSplited = isSplited;
    }

    public Set<String> getAisles() {
        return aisles;
    }

    public void setAisles(Set<String> aisles) {
        this.aisles = aisles;
    }

    public Long getDoHeaderId() {
        return doHeaderId;
    }

    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(BigDecimal expectedQty) {
        this.expectedQty = expectedQty;
    }

    public BigDecimal getCoselectionRate() {
        return coselectionRate;
    }

    public void setCoselectionRate(BigDecimal coselectionRate) {
        this.coselectionRate = coselectionRate;
    }
    
    public Date getPlanShipTime() {
        return planShipTime;
    }
    
    public void setPlanShipTime(Date planShipTime) {
        this.planShipTime = planShipTime;
    }
    
    public int getAisleSize() {
        return aisles.size();
    }
    
    public DoRecommendDTO converToDoRecommendDTO() {
        DoRecommendDTO doRecommendDTO = new DoRecommendDTO();
        BeanUtils.copyProperties(this, doRecommendDTO);
        return doRecommendDTO;
    }
}