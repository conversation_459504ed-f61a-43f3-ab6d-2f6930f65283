package com.daxia.wms.delivery.print.service.carton;

import com.daxia.framework.common.log.StopWatch;
import com.daxia.framework.common.util.*;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.WaybillType;
import com.daxia.wms.Keys;
import com.daxia.wms.OrderLogConstants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.OrderLogService;
import com.daxia.wms.delivery.print.dto.BaseCartonPrintDTO;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.helper.PrintCartonHelper;
import com.daxia.wms.delivery.recheck.entity.CartonDetail;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.entity.TempCarton;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.master.component.BusinessCenterComponent;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.service.ShopInfoService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.print.PrintConstants.PrintType;
import com.daxia.wms.print.dto.PrintCfg;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.print.service.PrintLogService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public abstract class AbstractPrintWaybillService {
    protected ObjectMapper objectMapper = new ObjectMapper();
    @In
    private WarehouseService warehouseService;

    @In
    private PrintLogService printLogService;
    @In
    private OrderLogService orderLogService;
    @In
    protected CartonService cartonService;

    @In
    private DeliveryOrderService deliveryOrderService;

    @In
    private ShopInfoService shopInfoService;
    @In
    private BusinessCenterComponent businessCenterComponent;

    private WaybillType waybillType;

    private PrintCfg printCfg;

    public AbstractPrintWaybillService(WaybillType waybillType, PrintCfg printCfg) {
        this.setWaybillType(waybillType);
        this.printCfg = printCfg;
    }

    public AbstractPrintWaybillService() {
        super();
    }

    public void doRepare(DeliveryOrderHeader doHeader, List<Long> cartonIds) {
    }

    @Transactional
    public PrintData genPrintData(DeliveryOrderHeader doHeader, List<Long> cartonIds) {
        StopWatch stopWatch = new StopWatch("PrintCartonLabel");

        if (ListUtil.isNullOrEmpty(cartonIds)) {
            cartonIds = cartonService.findCartonIdByDoId(ImmutableList.of(doHeader.getId()), Constants.PackageType.B.getValue());
        }

        if (ListUtil.isNullOrEmpty(cartonIds)) {
            cartonIds = cartonService.findCartonIdByDoId(ImmutableList.of(doHeader.getId()));
        }

        List<PrintReportDto> dtoList = buildDTOByCartons(cartonIds);
        stopWatch.stop();

        // write print log
        List<Long> printedIdList = new ArrayList<Long>();
        for (PrintReportDto dto : dtoList) {
            CartonPrintDTO cartonPrintDto = (CartonPrintDTO) dto;
            printedIdList.add(cartonPrintDto.getCartonId());
            printLogService.savePrintLog(cartonPrintDto.getCartonId(), cartonPrintDto.getCartonNo(), PrintType.CARTON_LABEL, this.getWaybillType().name(),
                    stopWatch.getElapsedTime() / dtoList.size(), cartonPrintDto.getDoNo(), cartonPrintDto.getOutRefNo());

            String logMessage = ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_PRINT_WAYBILL,
                    null, cartonPrintDto.getCartonNo());
            orderLogService.asyncSaveLog(deliveryOrderService.findDoByCartonIds(Lists.newArrayList(cartonPrintDto.getCartonId())).get(0)
                , OrderLogConstants.OrderLogType.PRINT_WAYBILL.getValue(),logMessage,ParamUtil.getCurrentLoginName());
        }
        // set carton is printed

        cartonService.setCartonPrinted(printedIdList);

        PrintData printData = genPrintData(dtoList, doHeader);

        return doSomethingCommon(printData, doHeader);
    }

    private PrintData doSomethingCommon(PrintData printData, DeliveryOrderHeader doHeader) {
        printData.setCarrierId(doHeader.getCarrierId());
        return printData;
    }

    @Transactional
    public PrintData genTempCartonPrintData(DeliveryOrderHeader doHeader, TempCarton tempCarton) {
        StopWatch stopWatch = new StopWatch("PrintCartonLabel");
        List<PrintReportDto> dtoList = buildDTOByTempCarton(doHeader, tempCarton);
        stopWatch.stop();
        PrintData printData = genPrintData(dtoList, doHeader);
        return doSomethingCommon(printData, doHeader);
    }

    //默认使用LODOP打印箱标签（电子面单）
    protected abstract PrintData genPrintData(List<PrintReportDto> dtoList, DeliveryOrderHeader doHeader);

    /**
     * 根据cartonId构造箱标签打印DTO
     *
     * @param
     * @return
     */
    private List<PrintReportDto> buildDTOByCartons(List<Long> cartonIds) {
        List<PrintReportDto> printReportDtos = new ArrayList<PrintReportDto>();

        List<DeliveryOrderHeader> doHeaders = deliveryOrderService.findDoByCartonIds(cartonIds);

        for (DeliveryOrderHeader doHeader : doHeaders) {
            // 将箱标签按箱号升序排列
            List<CartonHeader> oldCartons = doHeader.getCartonHeaders();
            if (ListUtil.isNullOrEmpty(oldCartons)) {
                throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
            }
            // 重新new一个list，防止级联更新doheader
            List<CartonHeader> cartons = new ArrayList<CartonHeader>(oldCartons);
            if (Config.isDefaultTrue(Keys.Print.carton_split_by_package, Config.ConfigLevel.WAREHOUSE)) {
                //整
                List<CartonHeader> cCartons = new ArrayList<CartonHeader>();
                int cIdx = 0;
                //散
                List<CartonHeader> bCartons = new ArrayList<CartonHeader>();
                int bIdx = 0;

                PrintCartonHelper.sortCartonsByIdx(cCartons);
                PrintCartonHelper.sortCartonsByIdx(bCartons);
                for (CartonHeader c : cartons) {
                    if (StringUtils.equals(c.getExt1(), Constants.PackageType.C.getValue())) {
                        cIdx++;
                        c.setCartonIndex(cIdx);
                        cCartons.add(c);
                    } else {
                        bIdx++;
                        c.setCartonIndex(bIdx);
                        bCartons.add(c);
                    }
                }

                int cCount = cCartons.size();
                int bCount = bCartons.size();
                for (CartonHeader carton : cCartons) {
                    if (cartonIds.contains(carton.getId())) {
                        buildDTO(printReportDtos, doHeader, convertToBaseDto(carton), carton.getCartonIndex(), cCount);
                    }
                }

                for (CartonHeader carton : bCartons) {
                    if (cartonIds.contains(carton.getId())) {
                        buildDTO(printReportDtos, doHeader, convertToBaseDto(carton), carton.getCartonIndex(), bCount);
                    }
                }
            } else {
                PrintCartonHelper.sortCartonsByIdx(cartons);

                int idx = 0;
                for (CartonHeader c : cartons) {
                    c.setCartonIndex(++idx);

                    if (cartonIds.contains(c.getId())) {
                        buildDTO(printReportDtos, doHeader, convertToBaseDto(c), c.getCartonIndex(), cartons.size());
                    }
                }
            }
        }
        if (ListUtil.isNullOrEmpty(printReportDtos)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        return printReportDtos;
    }

    private List<PrintReportDto> buildDTOByTempCarton(DeliveryOrderHeader doHeader, TempCarton tempCarton) {
        List<PrintReportDto> printReportDtos = new ArrayList<PrintReportDto>();
        buildDTO(printReportDtos, doHeader, convertToBaseDto(tempCarton), 1, 1);
        if (ListUtil.isNullOrEmpty(printReportDtos)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        return printReportDtos;
    }

    private BaseCartonPrintDTO convertToBaseDto(TempCarton tempCarton) {
        BaseCartonPrintDTO dto = new BaseCartonPrintDTO();
        //tempCarton打印无箱ID，固定-1
        dto.setId(-1L);
        dto.setCartonNo(tempCarton.getCartonNo());
        dto.setPrintData(tempCarton.getPrintData());
        dto.setWayBill(tempCarton.getWayBill());
        dto.setIsPrinted(tempCarton.getIsPrinted());
        dto.setPackageType("B");
        return dto;
    }

    private BaseCartonPrintDTO convertToBaseDto(CartonHeader carton) {
        BaseCartonPrintDTO dto = new BaseCartonPrintDTO();
        dto.setId(carton.getId());
        dto.setCartonNo(carton.getCartonNo());
        dto.setPrintData(carton.getPrintData());
        dto.setWayBill(carton.getWayBill());
        dto.setIsPrinted(carton.getIsPrinted());
        dto.setPackageType(carton.getExt1());
        return dto;
    }

    protected abstract void buildDTO(List<PrintReportDto> printReportDtos, DeliveryOrderHeader doHeader, BaseCartonPrintDTO carton, int index, int count);

    /**
     * 处理仓库code，超过3位的截取前3位
     *
     * @return
     */
    public String buildWarehouseCode() {
        Warehouse warehouse = warehouseService.getLocalWarehouse();
        String warehouseCode = warehouse == null ? "" : warehouse.getWarehouseCode();
        warehouseCode = StringUtil.trim(warehouseCode);
        if (3 < StringUtil.length(warehouseCode)) {
            warehouseCode = StringUtil.substring(warehouseCode, 0, 3);
        }
        return warehouseCode;
    }

    /**
     * 设置寄件人信息
     */
    public void setSendAddressInfo(DeliveryOrderHeader doHeader, CartonPrintDTO cartonPrintDTO) {
        // ShopInfo shopInfo = null;
        // if (doHeader.getShopId() != null) {
        // shopInfo = shopInfoService.get(doHeader.getShopId());
        // }
        // if (Constants.YesNo.YES.getValue().equals(doHeader.getHaveCfy())) {
        // cartonPrintDTO.setSendName(StringUtil.isBlank(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_NAME_CFY,
        // ParamUtil.getCurrentWarehouseId())) ? shopInfo.getNickName() :
        // SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_NAME_CFY, ParamUtil.getCurrentWarehouseId()));
        // cartonPrintDTO.setSendAddress(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_ADDRESS_CFY,
        // ParamUtil.getCurrentWarehouseId()));
        // cartonPrintDTO.setSendPhone(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_PHONE_CFY,
        // ParamUtil.getCurrentWarehouseId()));
        // } else {
        // cartonPrintDTO.setSendName(shopInfo != null ? shopInfo.getNickName() :
        // Objects.equals(doHeader.getChannelCode(),"VTN")?
        // SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_NAME, ParamUtil.getCurrentWarehouseId()):"");
        // String sendName = SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_NAME_COVER,
        // ParamUtil.getCurrentWarehouseId());
        // if (StringUtil.isNotBlank(sendName)) {
        // cartonPrintDTO.setSendName(sendName);
        // }
        cartonPrintDTO.setSendAddress(StringUtil.notNullString(
                SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_ADDRESS, ParamUtil.getCurrentWarehouseId()),
                warehouseService.getLocalWarehouse().getAddressName()));

        // String sendPhone = NullUtil.notEmpty(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_PHONE + "_" +
        // doHeader.getShopId(), ParamUtil.getCurrentWarehouseId()),
        // SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_PHONE, ParamUtil.getCurrentWarehouseId()));
        // cartonPrintDTO.setSendPhone(sendPhone);
        // }
        // List<Long> shopList = getNotDisplayShopIds();
        // //特定店铺名称不显示
        // if (CollectionUtils.isNotEmpty(shopList) && shopList.contains(doHeader.getShopId())) {
        // cartonPrintDTO.setSendName("");
        // }
        String vbody = AppConfig.getProperty("distribution.channelcode.shapefree", "VBODY");
        String sendName = "";
        if( Objects.equals(doHeader.getChannelCode(), "VTN")){
            sendName = SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_NAME, ParamUtil.getCurrentWarehouseId());
        } else if (Objects.equals(doHeader.getChannelCode(), vbody)){
            sendName =  SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_NAME_SF, ParamUtil.getCurrentWarehouseId());
        } else {
            sendName = businessCenterComponent.getStoreMap().get(doHeader.getStoreCode());
        }
        cartonPrintDTO.setSendName(sendName);
        cartonPrintDTO.setCarrierId(doHeader.getCarrierId());
    }

    /**
     * 箱明细数量汇总
     *
     * @return
     */
    public BigDecimal getUnitQtyInCarton(DeliveryOrderHeader doHeader, String cartonNo) {
        BigDecimal result = BigDecimal.ZERO;
        if (StringUtil.isEmpty(cartonNo)) {
            return result;
        }

        for (CartonHeader c : doHeader.getCartonHeaders()) {
            if (!StringUtils.equals(cartonNo, c.getCartonNo())) {
                continue;
            }
            for (CartonDetail cd : c.getCartonDetails()) {
                result = result.add(cd.getPackedNumber());
            }
            break;
        }

        return result.compareTo(BigDecimal.ZERO) == 0 ? doHeader.getExpectedQty() : result;
    }

    private List<Long> getNotDisplayShopIds() {
        String shopListStr = SystemConfig.getConfigValue(ConfigKeys.PRINT_NODISPLAYNAMESHOPIDS, ParamUtil.getCurrentWarehouseId());
        if (StringUtils.isBlank(shopListStr)) {
            return null;
        }
        String[] shopArray = shopListStr.split(",");
        List<Long> shopList = new ArrayList<Long>();
        for (String str : shopArray) {
            shopList.add(Long.valueOf(str));
        }
        return shopList;
    }

    protected void setProductInfo(DeliveryOrderHeader doHeader, CartonPrintDTO cartonPrintDTO) {
        List<CartonPrintDTO.ProductInfo> listPair = deliveryOrderService.getGiftProductInfo(doHeader);
        //判断是否是非保赠品订单
        cartonPrintDTO.setIsGift(Boolean.FALSE);
        if (Constants.SpecialLabelCode.GIFT.getCode().equals(doHeader.getDoWaveEx().getSpecialLabelCode())) {
            //非保赠品订单
            cartonPrintDTO.setIsGift(Boolean.TRUE);
            CartonPrintDTO.ProductInfo productInfo = listPair.get(0);
            BigDecimal totalQty = listPair.stream().map(CartonPrintDTO.ProductInfo::getQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            cartonPrintDTO.setProducts(listPair);
            cartonPrintDTO.setTotalGiftQty(totalQty.intValue());
            cartonPrintDTO.setProductInfo("含" + productInfo.getName() + "等, " + totalQty.intValue() + "件");
        }

        // 设置购物清单信息
        setShoppingList(doHeader, cartonPrintDTO);
    }

    /**
     * 设置购物清单信息
     */
    protected void setShoppingList(DeliveryOrderHeader doHeader, CartonPrintDTO cartonPrintDTO) {
        List<DeliveryOrderDetail> details = deliveryOrderService.getDoDetailByDoHeaderId(doHeader.getId());
        if (details == null || details.isEmpty()) {
            return;
        }

        // 合并相同SKU的数量
        Map<Long, DeliveryOrderDetail> skuMap = new HashMap<>();
        for (DeliveryOrderDetail detail : details) {
            Long skuId = detail.getSkuId();
            if (skuMap.containsKey(skuId)) {
                DeliveryOrderDetail existing = skuMap.get(skuId);
                existing.setExpectedQty(existing.getExpectedQty().add(detail.getExpectedQty()));
            } else {
                DeliveryOrderDetail copy = new DeliveryOrderDetail();
                copy.setSkuId(detail.getSkuId());
                copy.setSku(detail.getSku());
                copy.setExpectedQty(detail.getExpectedQty());
                skuMap.put(skuId, copy);
            }
        }

        List<DeliveryOrderDetail> mergedDetails = new ArrayList<>(skuMap.values());

        // 计算总品数和总件数
        int totalSkuCount = mergedDetails.size();
        BigDecimal totalQty = mergedDetails.stream()
                .map(DeliveryOrderDetail::getExpectedQty)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 构建购物清单字符串
        StringBuilder shoppingList = new StringBuilder();
        shoppingList.append("购物清单：总品数：").append(doHeader.getUserDeffine5Formatted())
                   .append("，总件数：").append(doHeader.getExpectedQty()).append("。条码如下：\n");

        // 添加商品条码信息，最多显示8种
        int maxItems = Math.min(8, mergedDetails.size());
        for (int i = 0; i < maxItems; i++) {
            DeliveryOrderDetail detail = mergedDetails.get(i);
            if (detail.getSku() != null && detail.getSku().getEan13() != null) {
                if (i > 0) {
                    shoppingList.append("\n");
                }

                // 使用序号①②③等
                String serialNumber = getSerialNumber(i + 1);
                shoppingList.append(serialNumber).append(detail.getSku().getEan13())
                           .append(" * ").append(detail.getExpectedQty().intValue());
            }
        }

        // 如果超过8种商品，添加提示文案
        if (mergedDetails.size() > 8) {
            shoppingList.append("等");
        }

        // 设置到打印DTO中
        cartonPrintDTO.setShoppingList(shoppingList.toString());
        cartonPrintDTO.setTotalSkuCount(totalSkuCount);
        cartonPrintDTO.setTotalQtyCount(totalQty.intValue());
    }

    /**
     * 获取序号字符（①②③...）
     */
    private String getSerialNumber(int number) {
        if (number <= 0 || number > 8) {
            return String.valueOf(number);
        }
        // Unicode编码：①是\u2460，②是\u2461，以此类推
        return String.valueOf((char) (0x2460 + number - 1));
    }

    protected int getCartonIndex(DeliveryOrderHeader doHeader, String cartonNo) {
        int index = 1;
        for (CartonHeader c : doHeader.getCartonHeaders()) {
            if (c.getCartonNo().equals(cartonNo)) {
                return index;
            }
            index++;
        }
        return index;
    }

    protected void buildExtAttr(PrintData printData, DeliveryOrderHeader doHeader, CartonPrintDTO cartonPrintDTO) {
        String script = Config.get(Keys.Print.carton_diy_script, Config.ConfigLevel.WAREHOUSE);
        if (StringUtil.isNotEmpty(script)) {
            MvelUtil.eval(script, ImmutableMap.of("doHeader", (Object) doHeader, "dto", cartonPrintDTO, "printCfg", printData.getPrintCfg()));
        }
    }

    public WaybillType getWaybillType() {
        return waybillType;
    }

    public void setWaybillType(WaybillType waybillType) {
        this.waybillType = waybillType;
    }

    public PrintCfg getPrintCfg() {
        return printCfg;
    }


    public void setPrintCfg(PrintCfg printCfg) {
        this.printCfg = printCfg;
    }
}
