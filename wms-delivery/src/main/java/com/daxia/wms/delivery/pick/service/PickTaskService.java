package com.daxia.wms.delivery.pick.service;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.Constants.PktStatus;
import com.daxia.wms.Constants.TaskStatus;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateDetail;
import com.daxia.wms.delivery.pick.dto.PickTaskDto;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.pick.filter.PickTaskFilter;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.master.entity.Partition;
import com.daxia.wms.stock.stock.dto.Stock2AllocateDTO;
import com.daxia.wms.stock.stock.dto.Stock4CombiDoAllocateDTO;
import com.daxia.wms.stock.stock.entity.StockBatchLocLpn;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface PickTaskService {
	/**
	 * 
	 * <pre>
	 * Description:更新拣货任务
	 * </pre>
	 *
	 * @param PickTask
	 */
	public void updatePickTask(PickTask PickTask);

    /**
     * <pre>
     * 查询拣货任务
     * </pre>
     *
     * @param pktTaskFilter
     * @return
     */
    public List<PickTask> query(PickTaskFilter pktTaskFilter);

    /**
     * <pre>
     * 删除拣货任务
     * </pre>
     *
     * @param pickTask
     */
    public void removeTask(PickTask pickTask);
    
    /**
     * 
     * <pre>
     * Description:根据DoHeaderId删除拣货任务,并修改任务状态为取消
     * </pre>
     *
     * @param doHeaderId
     */
    public void cancelPickTaskByDoHeaderId(Long doHeaderId);
    
    /**
     * 取得拣货任务
     * @param taskId
     * @return
     */
    public PickTask getTaskById(Long taskId);
    
    /**
     * 创建拣货任务
     * @param task
     * @param qty
     * @param newFrmStockId
     * @param updateBy
     */
	public PickTask createPickTask(PickTask task, BigDecimal qtyUnit, BigDecimal qty, Long newFrmStockId, String updateBy);
	    /**
     * 根据任务号和状态获取拣货任务
     * @param taskNum
     * @param taskStatus
     * @return
     */
	public PickTask getPickTask(String taskNum, TaskStatus taskStatus);
	
	/**
	 * 根据do单头和明细获取拣货任务
	 * @param doId
	 * @param doDetailId
	 * @return
	 */
	public List<PickTask> getPickTasks(Long doId, Long doDetailId);
	
    /**
     * 根据波次ID返回拣货明细，包括sku id，loc id， 需要拣货的数量（不group）
     * @param waveId
     * @param status
     * @return
     */
    public List<PickTaskDto> getPickTasksByWaveId(Long waveId, String status);
    
    /**
     * 根据波次ID返回拣货明细，包括sku id，loc id， 需要拣货的数量（不区分批次，group by sku和库位）
     * @param waveId
     * @param status
     * @return
     */
    public List<PickTaskDto> getPickTasksByWaveIdGrouped(Long waveId, String status);
    
    /**
     * 根据波次ID返回拣货明细，包括sku id，loc id， 需要拣货的数量（不区分批次，group by sku和库位）
     * @param waveId
     * @param status
     * @return
     */
    public List<PickTaskDto> getPickTasksByWaveIdGroupedOpt(WaveHeader wave, PktStatus status);
    
    /**
     * 根据拣货头ID返回拣货明细，包括sku id，loc id， 需要拣货的数量（不区分批次，group by sku和库位）
     * @param pktId
     * @param status
     * @return
     */
    public List<PickTaskDto> getPickTasksByPktIdGrouped(Long pktId, PktStatus status);

    /**
     * 按照商品库位和状态找到相应的拣货明细
     * @param skuId
     * @param locId
     * @param waveId
     * @param status
     * @return
     */
	public List<PickTask> findByWaveId(Long skuId, Long locId, Long waveId, TaskStatus status,Long packageDetailId);
	
	/**
	 * 按照商品库位和状态找到相应的拣货明细
	 * @param skuId
	 * @param locId
	 * @param pktId
	 * @param status
	 * @return
	 */
	public List<PickTask> findByPktId(Long skuId, Long locId, Long pktId, TaskStatus status,Long packageDetailId);

	/**
	 * 删除并修改状态为取消；
	 * @param pickTask
	 */
    public void cancelAndRemovePktTask(PickTask pickTask);

    /**
     * 将do相关的拣货任务从发布状态变为初始化状态，并解除与wave/pktHeader的关联
     * @param doHeaderId
     */
    public void unrelate(Long doHeaderId);

    /**
     * 创建拣货任务(人工分配)
     * @param alcDetail
     * @param stockBatchLocLpn
     * @param allocateQty
     * @param fmStockId
     * @param allocatingId
     */
    public void createPickTask(DoAllocateDetail alcDetail, StockBatchLocLpn stockBatchLocLpn, BigDecimal allocateQty, BigDecimal allocateQtyUnit, Long packDetailId, Long fmStockId, Long allocatingId);

    /**
     * 创建拣货任务(分配)
     * @param alcDetail
     * @param allocateStock
     * @param allocateQty
     * @param fmStockId
     * @param allocatingId
     */
    public void createPickTask(DoAllocateDetail alcDetail, Stock2AllocateDTO allocateStock, BigDecimal allocateQty, BigDecimal allocateQtyUnit, Long packDetailId, Long fmStockId, Long allocatingId);

    void createPickTask(DoAllocateDetail alcDetail, Stock4CombiDoAllocateDTO allocateStock, BigDecimal allocateQty, BigDecimal allocateQty1, Long id, Long aLong, Long aLong1);

    /**
     * 将拣货任务修改为发运状态；
     * @param doHeader
     * @param updateUser
     */
    public void deliveryPktTaskByDoHeader(Long doHeader, String updateUser);

    /**
     * 根据状态和单号获取拣货任务的sku
     * @param status
     * @param docNo
     * @return
     */
    public List<Long> getPktTaskSkus(String status, String docNo);

    /**
     * 拣货任务查询
     * @param pickTaskFilter
     * @param startIndex
     * @param pageSize
     * @return
     */
    public DataPage<PickTask> query(PickTaskFilter pickTaskFilter, int startIndex, int pageSize);

    /**
     * 关联拣货任务和波次
     * @param waveHeaderId
     * @param pktHeaderId
     * @param doIdList
     * @param regionId
     * @param isEaTask
     */
	public void relate(Long waveHeaderId, Long pktHeaderId, List<Long> doIdList, Long regionId, boolean isEaTask);
	
	/**
	 * 查询指定波次下已发布的拣货任务，用以绑定波次分页
	 * @param waveHeaderId
	 * @param status
	 * @param startIndex
	 * @param pageSize
	 * @return
	 */
	 public DataPage<PickTask> findPickTask4BindList(Long waveHeaderId, PktStatus status, int startIndex, int pageSize);   
	
    /**
     * 根据波次头id，获取已绑定容器的相关拣货任务
     */
    public List<PickTask> findBindPickTasksByWave(Long waveHeaderId);

    /**
     * 根据容器号，获取已绑定该容器的相关拣货任务
     */
    public List<PickTask> findBindPickTasksByContainerNo(String containerNo,Long waveId);

    /**
     * 统计波次下拣货任务的SKU的数量，按波次ID分组
     * @param waveIds 波次ID范围
     * @param statusList 任务状态 
     * @param stockStatus 缺货状态
     * @return
     */
    public Map<Long, BigDecimal> getPickTaskSum(List<Long> waveIds, List<String> unPickedStats,
            List<String> normalStockStatus);

    /**
     * 统计波次下，拣货单的拣货任务的SKU数量，按拣货单分组
     * @param waveId 波次ID
     * @param statusList  任务状态
     * @param stockStatus 任务缺货状态
     * @return
     */
    public Map<Long, BigDecimal> getPktPickTaskSum(Long waveId, List<String> unPickedStats,
            List<String> normalStockStatus);

    /**
     * 波次下，拣货单的拣货任务的SKU总数量，按拣货单分组
     * @param waveId 波次ID
     * @return
     */
    public Map<Long, BigDecimal> getPktPickTaskNumMap(Long waveId);
    
    /**
     * 根据do头id获取区域列表
     */
    List<Long> findRegionId(List<Long> doIdList);

    public List<Object> getPktPickTaskSumByPktHId(List<Long> pktHeaderIds);

    /**
     * 根据波次和容器查询拣货任务明细（合并同一商品）
     * @param containerNo
     * @param waveId
     * @return
     */
    public List<PickTaskDto> findPickTasksByWaveContainer(String containerNo, Long waveId);
    
    /**
     * 需要拣货的数量
     */
    public BigDecimal getTotalNeedPickQty(List<PickTask> pickTasks);
    
    /**
     * 合并且排序拣货任务，并进行"拣货路径优化"(wms打印、客户端打印，rf拣货有用到)
     * @param pickTasks 优化之前的拣货任务
     * @return 优化后的拣货任务dto
     */
    public List<PickTaskDto> setPickTaskDtosGrouped(List<PickTask> pickTasks);
    
    public Map<String, String> getPickPathOfWave(Long waveId);
    
    public Map<String, String> getOptPickPathOfWave(Long waveId);
    
    /**
     * 查询拣货单下的库区
     * @param pktHeaderId
     * @return
     */
    public List<Partition> queryPartitionByPktHId(Long pktHeaderId);

	/**
	 * 查询波茨下的库区
	 * @param waveHeaderId
	 * @param containerNo
	 * @return
	 */
	public List<Partition> queryPartitionByWaveIdConNo(Long waveHeaderId,String containerNo);

	/**
	 * 显示捡货单号
	 * @param waveHeaderId
	 * @param containerNo
	 * @return
	 */
	public String queryPktHNosByWaveIdConNo(Long waveHeaderId,String containerNo);
	
    /**
     * 更新拣货任务的拣货人信息
     * @param waveHeaderId
     * @param updateBy
     */
    public void updatePickTaskInfo(Long waveHeaderId, String updateBy);

	List<PickTaskDto> getTspTasksByPktIdGrouped(PickHeader pickHeader, PktStatus status);
	
	public List<PickTask> getPickTasksIOrderByBatch(Long doId, Long doDetailId);

    //是否存在EA类型的拣货任务
    boolean existUnitPickTask(Long regionId, List<Long> doIdList, boolean isEaTask);

    //是否存在EA类型的拣货任务
    boolean existUnitPickTask(List<Long> regionIdList, List<Long> doIdList, boolean isEaTask);

    //查找拣货任务
    List<PickTask> findByPktId(Long pktHeaderId);
    
    List<String> findLotNoByDoDetail(Long id);
    
    /**
     * doDetailId：[qtyPcs, qtyUnit]
     * @param doHeaderId
     * @return
     */
    Map<Long,ImmutablePair<BigDecimal,BigDecimal>> countTask(Long doHeaderId);
    
    List<Object[]> findCompletedTaskByWaveNo(String docNum);
    
    List<Object[]> findCompletedTaskByPktNo(String docNum);

    List<Integer> countQtyByDoc4Group(Long id, Long locId, Long skuId);

    List<PickTask> findPickTaskByDoIdAndSkuId(Long doId, Long skuId, String lotatt05, Integer pkType);

    List<PickTask> findPickTaskByDoId(Long doId);

    Map<String,Long> findStatusCountByWave(Long waveId);
    
    boolean existTaskByDo(List<Long> genDoIds, Long regionId, boolean isEa);
    
    List<PickTaskDto> getTasksPreContainer(Long id);

    List<PickTask> findByContainer(Long skuId, Long locId, String containerNo, String taskStatus, String docNo);
    
    ////不支持整箱的算容器；
    Long splitPickTask(Long taskId, BigDecimal newQty);

    BigDecimal findPickTaskByDoIdAndSkuIdForQty(Long doId, Long skuId, String lotatt05);

}
