package com.daxia.wms.delivery.deliveryorder.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;

/**
 * 合单打印实体
 */
@Entity
@Table(name = "doc_merge_print_header")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_merge_print_header set is_deleted = 1 where id = ? and version = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class MergePrintHeader extends WhBaseEntity {

	private static final long serialVersionUID = -8493475807139511070L;

	private Long id;

	private String groupNo;


	/**
	 * 配送商
	 */
	private Long carrierId;

	/**
	 * 订单数
	 */
	private Integer doNum;
	/**
	 * 箱数
	 */
	private Integer cartonNum;

	/**
	 * 运单号
	 */
	private String wayBill;
	/**
	 * 下单内容
	 */
	private String printData;

	@Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "autoIdGenerator")
    @GenericGenerator(name = "autoIdGenerator", strategy = "com.daxia.framework.common.dao.AutoIdentityGenerator")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

    @Column(name = "GROUP_NO")
    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    @Column(name = "CARRIER_ID")
    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    @Column(name = "DO_NUM")
    public Integer getDoNum() {
        return doNum;
    }

    public void setDoNum(Integer doNum) {
        this.doNum = doNum;
    }

    @Column(name = "CARTON_NUM")
    public Integer getCartonNum() {
        return cartonNum;
    }

    public void setCartonNum(Integer cartonNum) {
        this.cartonNum = cartonNum;
    }

    @Column(name = "WAY_BILL")
    public String getWayBill() {
        return wayBill;
    }

    public void setWayBill(String wayBill) {
        this.wayBill = wayBill;
    }

    @Column(name = "PRINT_DATA")
    public String getPrintData() {
        return printData;
    }

    public void setPrintData(String printData) {
        this.printData = printData;
    }
}