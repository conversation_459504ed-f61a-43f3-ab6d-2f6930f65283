package com.daxia.wms.delivery.deliveryorder.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;

/**
 * 合单打印实体
 */
@Entity
@Table(name = "doc_merge_print_detail")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_merge_print_detail set is_deleted = 1 where id = ? and version = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class MergePrintDetail extends WhBaseEntity {

	private static final long serialVersionUID = -8493475807139511070L;

	private Long id;

	private Long headerId;

	private Long doHeaderId;

	private Integer cartonNum;


	@Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "autoIdGenerator")
    @GenericGenerator(name = "autoIdGenerator", strategy = "com.daxia.framework.common.dao.AutoIdentityGenerator")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

    @Column(name = "DO_HEADER_ID")
    public Long getDoHeaderId() {
        return doHeaderId;
    }

    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }

    @Column(name = "HEADER_ID")
    public Long getHeaderId() {
        return headerId;
    }

    public void setHeaderId(Long headerId) {
        this.headerId = headerId;
    }

    @Column(name = "CARTON_NUM")
    public Integer getCartonNum() {
        return cartonNum;
    }

    public void setCartonNum(Integer cartonNum) {
        this.cartonNum = cartonNum;
    }
}