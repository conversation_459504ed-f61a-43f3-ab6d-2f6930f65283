package com.daxia.wms.delivery.crossorder.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

import java.util.Date;

@lombok.extern.slf4j.Slf4j
public class CrossSeedFilter extends WhBaseQueryFilter {

    private static final long serialVersionUID = 7424540574507778411L;
    /** 分播单号 */
    private String seedNo;
    /** ASN单号 */
    private String asnNo;

    private String status;

    private String ean13;

    /** 打印标记：0否 1是 */
    private Integer isPrinted;

    /** 分播开始时间 */
    public Date seedFromTime;
    /** 分播完成时间 */
    public Date seedToTime;

    private String doNo;

    private String containerNo;

    @Operation(fieldName = "o.seedNo", operationType = OperationType.EQUAL)
    public String getSeedNo() {
        return seedNo;
    }

    public void setSeedNo(String seedNo) {
        this.seedNo = seedNo;
    }

    @Operation(fieldName = "o.asnNo", operationType = OperationType.EQUAL)
    public String getAsnNo() {
        return asnNo;
    }

    public void setAsnNo(String asnNo) {
        this.asnNo = asnNo;
    }

    @Operation(fieldName = "o.status", operationType = OperationType.EQUAL)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Operation(fieldName = "s.ean13", operationType = OperationType.EQUAL)
    public String getEan13() {
        return ean13;
    }

    public void setEan13(String ean13) {
        this.ean13 = ean13;
    }

    @Operation(fieldName = "o.seedFromTime", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")
    public Date getSeedFromTime() {
        return seedFromTime;
    }

    public void setSeedFromTime(Date seedFromTime) {
        this.seedFromTime = seedFromTime;
    }

    @Operation(fieldName = "o.seedToTime", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
    public Date getSeedToTime() {
        return seedToTime;
    }

    public void setSeedToTime(Date seedToTime) {
        this.seedToTime = seedToTime;
    }

    @Operation(fieldName = "o.isPrinted", operationType = OperationType.EQUAL)
    public Integer getIsPrinted() {
        return isPrinted;
    }

    public void setIsPrinted(Integer isPrinted) {
        this.isPrinted = isPrinted;
    }

    @Operation(clause = " exists (select 1 from CrossSeedDetail d where d.headerId = o.id and d.doNo = ? )", operationType = OperationType.CLAUSE)
    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    @Operation(clause = " exists (select 1 from CrossSeedDetail d where d.headerId = o.id and d.containerNo = ? )", operationType = OperationType.CLAUSE)
    public String getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }
}