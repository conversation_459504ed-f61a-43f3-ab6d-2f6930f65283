package com.daxia.wms.delivery.load.service.impl;

import java.util.List;

import org.jboss.seam.annotations.In;

import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.delivery.deliveryorder.service.DoAutoHandler;
import com.daxia.wms.delivery.load.service.AutoDeliverService;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.Constants.LoadMode;
import com.daxia.wms.Constants.YesNo;

@lombok.extern.slf4j.Slf4j
public class AutoLoadHandler extends DoAutoHandler {
    @In
    private CartonService cartonService;
    
    @In
    private AutoDeliverService autoDeliverService;

	@Override
	public void handle(Long doId) {
		Integer convenienceMode = SystemConfig.getConfigValueInt("delivery.mode.convenience.tms", ParamUtil.getCurrentWarehouseId());
		if (YesNo.YES.getValue().equals(convenienceMode)) {
			List<CartonHeader> cartonHeaders = cartonService.findByDoId(doId);
			for (CartonHeader cartonHeader : cartonHeaders) {
				autoDeliverService.autoLoadAndDeliver(cartonHeader.getCartonNo(),
						LoadMode.AUTO.getValue(), DateUtil.getNowTime());
			}
		}
	}

	@Override
	public void setName() {
		this.name = "autoLoadHandler";
	}
}
