package com.daxia.wms.delivery.container.dao;

import com.daxia.dubhe.api.wms.request.PickContainerRequest;
import com.daxia.dubhe.api.wms.response.PickContainerResponse;
import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.container.entity.PktContainerHeader;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

@Name("com.daxia.wms.delivery.pktContainerHeaderDAO")
@lombok.extern.slf4j.Slf4j
public class PktContainerHeaderDAO extends HibernateBaseDAO<PktContainerHeader, Long> {
    public List<PickContainerResponse.Container> findList4Wcs(PickContainerRequest request) {
        StringBuffer sql = new StringBuffer("SELECT ph.id,ph.container_no,ph.doc_no,ph.doc_type FROM doc_pkt_container_header ph ");
        sql.append(" INNER JOIN doc_pkt_container_detail pd on pd.header_id = ph.id and pd.is_deleted = 0 ")
                .append(" INNER JOIN tsk_pick pickTask on pickTask.id = pd.task_id ")
                .append(" INNER JOIN md_region region on region.id = ph.region_id ")
                .append(" WHERE ph.is_deleted = 0 and ph.warehouse_id = :warehouseId and region.warehouse_id = :warehouseId ")
                .append(" AND pickTask.status = :status AND pickTask.is_deleted = 0 AND ph.register_flag = :registerFlag ");
        if (request.getStartTime() != null) {
            sql.append(" AND ph.CREATE_TIME >= :startTime ");
        }
        if (request.getEndTime() != null) {
            sql.append(" AND ph.CREATE_TIME <= :endTime ");
        }
//        if (StringUtil.isNotEmpty(request.getRegionCode())) {
//            sql.append(" AND region.region_code = :regionCode ");
//        }
        if (StringUtil.isNotEmpty(request.getContainerNo())) {
            sql.append(" AND ph.container_no = :containerNo ");
        }
        sql.append(" GROUP BY ph.id ORDER BY ph.id ");

        Query query = createSQLQuery(sql.toString());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("status", Constants.TaskStatus.RELEASED.getValue());
        query.setParameter("registerFlag", Constants.YesNo.YES.getValue());
        if (request.getStartTime() != null) {
            query.setParameter("startTime", request.getStartTime());
        }
        if (request.getEndTime() != null) {
            query.setParameter("endTime", request.getEndTime());
        }
//        if (StringUtil.isNotEmpty(request.getRegionCode())) {
//            query.setParameter("regionCode", request.getRegionCode());
//        }
        if (StringUtil.isNotEmpty(request.getContainerNo())) {
            query.setParameter("containerNo", request.getContainerNo());
        }
        query.setFirstResult((request.getPageIndex()-1)*request.getPageSize());
        query.setMaxResults(request.getPageSize());

        List<PickContainerResponse.Container> resultList = new ArrayList<PickContainerResponse.Container>();
        List list = query.list();
        PickContainerResponse.Container c;
        Object[] result;
        for (int i = 0; i < list.size(); i++) {
            result = (Object[])list.get(i);
            c = new PickContainerResponse.Container();
            c.setId(((BigInteger)(result[0])).longValue());
            c.setContainerNo((String)result[1]);
            c.setDocNo((String)result[2]);
            c.setDocType((String)result[3]);
            resultList.add(c);
        }
        return resultList;
    }

    public List<PickContainerResponse.PickTask> findPickTaskList4Wcs(List<Long> idList, Boolean ignoreBatchFlag) {
        StringBuffer sql = new StringBuffer("SELECT loc.loc_code,sum(pickTask.qty),s.product_code,s.product_cname,pd.header_id ");
                if (!ignoreBatchFlag) {
                    sql.append(" ,att.lotatt05,att.lotatt03 ");
                }
                sql.append(" FROM doc_pkt_container_detail pd ")
                .append(" INNER JOIN tsk_pick pickTask ON pickTask.id = pd.task_id ")
                .append(" INNER JOIN md_location loc ON loc.id = pickTask.fm_loc_id ")
                .append(" INNER JOIN stock_batch_att att ON att.id = pickTask.lot_id ")
                .append(" INNER JOIN md_sku s ON s.id = pickTask.sku_id ")
                .append(" where pd.header_id in (:idList) and pickTask.warehouse_id = :warehouseId ")
                .append(" AND pickTask.status = :pickStatus and pickTask.is_deleted = 0 ")
                .append(" group by loc.loc_code,s.product_code,pd.header_id");
                if (!ignoreBatchFlag) {
                    sql.append(" ,att.lotatt05,att.lotatt03");
                }

        Query query = createSQLQuery(sql.toString());
        query.setParameterList("idList", idList);
        query.setParameter("pickStatus", Constants.TaskStatus.RELEASED.getValue());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());

        List<PickContainerResponse.PickTask> resultList = new ArrayList<PickContainerResponse.PickTask>();
        List list = query.list();
        PickContainerResponse.PickTask pt;
        Object[] result;
        for (int i = 0; i < list.size(); i++) {
            result = (Object[])list.get(i);
            pt = new PickContainerResponse.PickTask();
            pt.setLocCode((String)result[0]);
            pt.setToPickQty((BigDecimal)result[1]);
            pt.setProductCode((String)result[2]);
            pt.setProductName((String)result[3]);
            pt.setHeaderId(((BigInteger)(result[4])).longValue());
            if (!ignoreBatchFlag) {
                pt.setLotNo((String)result[5]);
                pt.setExpiryDate((String)result[6]);
            }
            resultList.add(pt);
        }
        return resultList;
    }

    public PktContainerHeader getPktContainerHeader4Bind(List<Long> regionIds) {
        String sql = "SELECT {pch.*} FROM doc_pkt_container_header pch WHERE pch.region_id in (:regionIds) AND register_flag = :registerFlag AND warehouse_id = :warehouseId " +
                "AND NOT EXISTS (" +
                "SELECT 1 FROM tsk_pick tp, doc_pkt_container_detail pcd WHERE " +
                "pcd.task_id = tp.id AND pcd.header_id = pch.id AND (tp.is_deleted = 1 OR tp.status != :releaseStatus OR tp.wave_h_id is null) AND tp.warehouse_id = :warehouseId " +
                "AND pcd.warehouse_id = :warehouseId AND pcd.is_deleted = 0 " +
                ") AND pch.is_deleted = 0 ORDER BY est_do_finish_time ASC, priority ASC";
    
        SQLQuery sqlQuery = this.createSQLQuery(sql);
        sqlQuery.addEntity("pch", PktContainerHeader.class);
        sqlQuery.setParameterList("regionIds",regionIds);
        sqlQuery.setParameter("registerFlag", Constants.YesNo.NO.getValue());
        sqlQuery.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        sqlQuery.setParameter("releaseStatus", Constants.TaskStatus.RELEASED.getValue());
        return (PktContainerHeader) (sqlQuery.setMaxResults(1).uniqueResult());
    }

    public Integer countPickTaskSize(String regionCode) {
        String sql = " SELECT count(1) FROM doc_pkt_container_header ph " +
                " INNER JOIN md_region region ON region.id = ph.region_id " +
                " WHERE region_code in (:regionCode) AND ph.register_flag = :registerFlag AND " +
                " ph.warehouse_id = :warehouseId AND ph.is_deleted = 0 " +
                " AND NOT EXISTS (SELECT 1 FROM tsk_pick tp, doc_pkt_container_detail pcd WHERE " +
                "   pcd.task_id = tp.id AND pcd.header_id = ph.id AND (tp.is_deleted = 1 OR tp.status != :releaseStatus OR tp.wave_h_id is null) " +
                "   AND tp.warehouse_id = :warehouseId AND pcd .warehouse_id = :warehouseId AND pcd.is_deleted = 0)";
        Query query = createSQLQuery(sql);
        query.setParameterList("regionCode",StringUtil.splitToList(regionCode,","));
        query.setParameter("registerFlag", Constants.YesNo.NO.getValue());
        query.setParameter("warehouseId",ParamUtil.getCurrentWarehouseId());
        query.setParameter("releaseStatus", Constants.TaskStatus.RELEASED.getValue());
        return ((BigInteger) query.uniqueResult()).intValue();
    }

    public Integer countPickTask4ChuteCheck(String containerNo, String chute) {
        String sql = "SELECT count(1) FROM doc_pkt_container_header ph " +
                " INNER JOIN doc_pkt_container_detail pd ON pd.header_id = ph.id " +
                " INNER JOIN tsk_pick pickTask ON pickTask.id = pd.task_id " +
                " INNER JOIN md_location loc on loc.id = pickTask.fm_loc_id " +
                " WHERE ph.is_deleted = 0 " +
                " AND pd.is_deleted = 0 " +
                " AND pickTask.is_deleted = 0 " +
                " AND pickTask.status = :pickTaskStatus " +
                " AND ph.container_no = :containerNo " +
                " AND loc.chute = :chute AND ph.warehouse_id = :warehouseId ";
        Query query = createSQLQuery(sql);
        query.setParameter("pickTaskStatus", Constants.TaskStatus.RELEASED.getValue());
        query.setParameter("containerNo",containerNo);
        query.setParameter("chute",chute);
        query.setParameter("warehouseId",ParamUtil.getCurrentWarehouseId());
        return ((BigInteger) query.uniqueResult()).intValue();
    }
}
