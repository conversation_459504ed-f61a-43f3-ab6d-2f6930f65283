package com.daxia.wms.delivery.sort.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.sort.dto.SortedDoDetailDto;
import com.daxia.wms.delivery.sort.dto.SortingDoDetailDTO;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.master.entity.Container;
import com.daxia.wms.master.entity.SortingBin;

/**
 * 分拣业务逻辑
 */
public interface SortingService {
    
    public static final String NEED_VALID_MERGE = "needValidateMerge";
    
	//一码多品标志
	final Long MULTI_PROD=0L;
	//扫描的条码没有找到商品
	final Long NO_BARCODE=-1L;
    
    /**
     * 根据波次号和商品条码(编码)判断该商品是否属于该波次
     * @param waveId 波次号
     * @param barcode 商品条码(编码)
     * @throws DeliveryException 商品条码(编码)对应的商品如果不在波次中则抛出异常
     */
    public void checkSkuInWave(Long waveId,String barcode) throws DeliveryException;
	
    /**
     * 按波次强制分拣
     * @param waveNo 要分拣的波次
     * @param staffNo 分拣人
     * @throws DeliveryException
     */
    public void compelSorting(String waveNo,String staffNo) throws DeliveryException;

    /**
	 * 强制分拣
	 * @param doDetailIds 要强制分拣的doDetail ID集合
	 * @param doHeader 要强制分拣的发货单
	 * @throws DeliveryException
	 */
	public void forceSorting(List<Long> doDetailIds, String doNo,String staffNo, String sotringBinNo) throws DeliveryException;
	 
	/**
	 * 逐件分拣
	 * @param doDetail
	 * @param sortingBinId 分拣柜Id
	 * @param staffNo
	 * @param wave
	 * @param skuId
	 * @throws DeliveryException
	 */
	public void oneByOneSorting(DeliveryOrderDetail doDetail, Long sortingBinId, String staffNo, WaveHeader wave, Long skuId) throws DeliveryException;
	
	/**
	 * 查找wave
	 * @param waveNo
	 * @return
	 * @throws DeliveryException
	 */
	public WaveHeader queryWave(String waveNo) throws DeliveryException;
	
	/**
	 * 根据商品条码查询商品Id
	 * @param barcode
	 * @return
	 */
	public List<Long> querySkuByBarcodeInWave(Long waveId,String barcode);
	
	/**
	 * 根据波次号和商品编码查询该商品的主键
	 * @param waveId 波次号
	 * @param barcode 商品编码
	 * @return 商品的主键
	 */
	public Long findSkyByCodeAndWaveId (Long waveId, String  barcode); 

	/**
	 * 查询波次号对应的分拣do明细
	 * @param waveNo
	 * @param isSorted
	 * @return
	 */
	public List<DeliveryOrderDetail> querySortingDoDetailOfWave(String waveNo , boolean isSorted);
	
	/**
	 * 对特定的定单下的某一个商品进行批量分拣
	 * @param sortingBinId 分拣柜Id
	 * @param doId 定单号
	 * @param wave 波次
	 * @param prodId 分拣的商品
	 * @param number 批量分拣数量
	 */
	public void batchSorting(Long sortingBinId, String staffNo,Long doId, WaveHeader wave, Long prodId, BigDecimal number);
	
	/**
	 * 根据波次Id和skuId查询对应需要分拣的定单明细
	 * @param waveId
	 * @param skuId
	 * @return
	 */
	public DeliveryOrderDetail queryDoDetail(Long waveId, Long skuId);
	
	/**
	 * 计算定单下指定商品未完成分拣的数量
	 * @param doHeader
	 * @return
	 */
	public BigDecimal countUnSortingNumber(DeliveryOrderHeader doHeader, Long skuId);
	
	/**
	 * 分拣缺货处理
	 * @param waveNo 波次号
	 * @param holdWho 操作人
	 * @return List<String> 已做缺货处理的订单号
	 */
	public List<String> lack(String waveNo, String holdWho);
	
	/**
     * 根据发货单号查询需要强制分拣的SortingDoDetailDTO
     * @param doNo
     * @return
     */
    public List<SortingDoDetailDTO> getDetails4ForceSorting(String doNo) throws DeliveryException;
    
    /**
     * 
     * @param waveId
     * @param skuId
     * @return
     */
    public Long dmBySku(Long waveId, Long skuId);
    
    /**
     * 波次关联DO需要绑定容器
     * @param waveId
     * @return
     */
    public List<String> doNeedBingContatiner(Long waveId);
    
    /**
     * 波次绑定分拣箱
     * @param container
     * @param waveNo
     * @return
     */
    public String bindSortContainer(Container container,String waveNo,List<String> doIds);
    
    public List<BigDecimal> getSortNoListByWaveId(Long waveId);
    
    /**
     * 执行换分拣筐
     * @param doHeader
     * @param container
     */
    public void executeChangeSortContainer(DoHeaderDto doHeader,Container container);

	/**
	 * 索取波次
	 * @param sortingBin
	 * @return
	 */
	public void bindWave(SortingBin sortingBin ,WaveHeader waveHeader);

	/**
     * 如果破损是最后1个unit 需要释放分拣柜和波次关系
     * @return
     */
	public void dealWaveLastUnit(Long waveId);
	
	public String batchSorting(Long doId, List<SortedDoDetailDto> dots,
			Long sortingBinId, WaveHeader waveHeader) ;
	public List<String> batchSortingForMultiDo(Map<Long, List<SortedDoDetailDto>> groupedByHeaderIdDetails,
			WaveHeader waveHeader, Long sortingBinId);
}
