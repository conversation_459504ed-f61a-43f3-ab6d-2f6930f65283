//package com.daxia.wms.delivery.recheck.service.impl;
//
//import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
//import com.daxia.wms.delivery.recheck.entity.CartonHeader;
//import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;
//import com.daxia.wms.delivery.recheck.service.impl.carton.PddWayBillGet;
//import com.pdd.pop.sdk.http.api.response.PddWaybillGetResponse;
//import org.jboss.seam.annotations.In;
//import org.jboss.seam.annotations.Name;
//
//@Name("pddCartonNoGenerateService")
//@lombok.extern.slf4j.Slf4j
// public class PddCartonNoGenerateServiceImpl implements CartonNoGenerateService {
//
//    @In
//    PddWayBillGet pddWayBillGet;
//
//    @Override
//    public void generatorCarton(DeliveryOrder<PERSON>ead<PERSON> doHeader, CartonHeader cartonHeader) {
//        PddWaybillGetResponse.InnerPddWaybillGetResponseModulesItem waybillApplyNewInfo = pddWayBillGet.reqeust(doHeader, cartonHeader);
//
//        cartonHeader.setCartonNo(waybillApplyNewInfo.getWaybillCode());
//        cartonHeader.setWayBill(waybillApplyNewInfo.getWaybillCode());
//        cartonHeader.setPrintData(waybillApplyNewInfo.getPrintData());
//    }
//}
