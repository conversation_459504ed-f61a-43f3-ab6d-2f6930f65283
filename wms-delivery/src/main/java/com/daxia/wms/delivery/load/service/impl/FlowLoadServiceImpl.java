package com.daxia.wms.delivery.load.service.impl;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.jboss.seam.log.Log;


import com.daxia.framework.common.annotation.Loggable;
import com.daxia.wms.delivery.load.entity.ReShipDo;
import com.daxia.wms.delivery.load.service.FlowLoadService;
import com.daxia.wms.delivery.load.service.LoadService;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.stock.stock.service.TransactionService;

@Name("com.daxia.wms.delivery.flowLoadService")
@lombok.extern.slf4j.Slf4j
public class FlowLoadServiceImpl implements FlowLoadService {

    @In
    private LoadService loadService;
    @In
    private TransactionService transactionService;


    
    @Override
    @Loggable
    @Transactional
    public void autoDeliver(CartonHeader ch, Integer loadMode, ReShipDo reShipDo, String userName) {
//        log.debug("autoDeliver: cartonHeaderId = " + ch.getId() + ",loadMode = " + loadMode + "WhId =" + ch.getWarehouseId());
//        ParamUtil.setCurrentWarehouseId(ch.getWarehouseId());
//        DeliveryOrderHeader doHeader = ch.getDoHeader();
//        try {
//            Map<String, Object> autoParam = new HashMap<String, Object>();
//            autoParam.put(LoadService.IS_AUTO, loadMode);
//            autoParam.put(LoadService.SHIP_TIME, reShipDo.getOpTime());
//            autoParam.put(LoadService.OPERATOR, userName);
//            log.debug("DELIVER invokeAutoDeliver START DO {0}", doHeader.getId());
//            loadService.invokeAutoDeliver(doHeader, autoParam);
//            log.debug("DELIVER invokeAutoDeliver END DO {0}", doHeader.getId());
//            if (log.isDebugEnabled()) {
//                BigDecimal count = transactionService.getShipTransactionCount(doHeader.getId());
//                log.debug("DELIVER invokeAutoDeliver transaction count {0}", count.toString());
//            }
//            loadService.removeReShipByDoId(reShipDo.getDocId());
//            // 调用接口
//            loadService.callInterfaceWhenDeliver(doHeader);
//        } catch (Exception e) {
//            if (e instanceof HibernateException) {
//                try {
//                    log.debug("rollback begin");
//                    Session session = (Session) Component.getInstance("hibernateSession");
//                    session.getTransaction().rollback();
//                    log.debug("rollback end");
//                } catch (Exception e2) {
//                    log.debug("rollback error", e2);
//                }
//            }
//            log.error("订单号：" + doHeader.getDoNo() + "发货出现异常; 异常信息：", e);
            loadService.addShipCount(reShipDo);
//        }
    }
    
}