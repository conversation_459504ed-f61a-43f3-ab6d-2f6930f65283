package com.daxia.wms.delivery.load.filter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.jboss.seam.annotations.intercept.BypassInterceptors;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

/**
 * <p>CrdockHeader查询条件值对像</p>
 */
@SuppressWarnings("serial")
@BypassInterceptors
@lombok.extern.slf4j.Slf4j
public class CrossDockHeaderFilter extends WhBaseQueryFilter implements Serializable {

    private String asnNo;  			//ASN单号 
    private String doNo;   		  	//调拨发货单号

    private String refNo1;          //调拨指令单号
    private String status;          //状态
    
    private String edi2;			//目标仓库
    
    private Date receiveTimeFrom;   //收货时间-创建时间
    private Date receiveTimeTo;

    private Date deliveryTimeFrom; //发货时间-交接时间
    private Date deliveryTimeTo;

    private List<Long> ids;
    
    @Operation(fieldName = "refNo2", operationType = OperationType.EQUAL)
    public String getAsnNo() {
        return asnNo;
    }
    public void setAsnNo(String asnNo) {
        this.asnNo = asnNo;
    }

    @Operation(fieldName = "o.doNo", operationType = OperationType.EQUAL)
    public String getDoNo() {
        return doNo;
    }
    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    @Operation(fieldName = "o.refNo1", operationType = OperationType.EQUAL)
    public String getRefNo1() {
        return refNo1;
    }
    public void setRefNo1(String refNo1) {
        this.refNo1 = refNo1;
    }

    @Operation(fieldName = "status", operationType = OperationType.EQUAL)
    public String getStatus() {
        return status;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    @Operation(fieldName = "createdAt", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")
    public Date getReceiveTimeFrom() {
        return receiveTimeFrom;
    }

    public void setReceiveTimeFrom(Date receiveTimeFrom) {
        this.receiveTimeFrom = receiveTimeFrom;
    }

    @Operation(fieldName = "createdAt", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
    public Date getReceiveTimeTo() {
        return receiveTimeTo;
    }

    public void setReceiveTimeTo(Date receiveTimeTo) {
        this.receiveTimeTo = receiveTimeTo;
    }

    @Operation(fieldName = "shipTime", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")
    public Date getDeliveryTimeFrom() {
        return deliveryTimeFrom;
    }
    public void setDeliveryTimeFrom(Date deliveryTimeFrom) {
        this.deliveryTimeFrom = deliveryTimeFrom;
    }

    @Operation(fieldName = "shipTime", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
    public Date getDeliveryTimeTo() {
        return deliveryTimeTo;
    }
    public void setDeliveryTimeTo(Date deliveryTimeTo) {
        this.deliveryTimeTo = deliveryTimeTo;
    }

    public String getEdi2() {
        return edi2;
    }
    public void setEdi2(String edi2) {
        this.edi2 = edi2;
    }
    
    @Operation(fieldName = "id", operationType = OperationType.IN)
    public List<Long> getIds() {
        return ids;
    }
    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

}
