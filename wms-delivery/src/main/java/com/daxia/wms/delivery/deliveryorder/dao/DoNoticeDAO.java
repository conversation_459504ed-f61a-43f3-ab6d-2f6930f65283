package com.daxia.wms.delivery.deliveryorder.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DoNotice;
import org.jboss.seam.annotations.Name;

import java.util.List;

@Name("com.daxia.wms.delivery.doNoticeDAO")
@lombok.extern.slf4j.Slf4j
public class DoNoticeDAO extends HibernateBaseDAO<DoNotice, Long> {
    public DoNotice getLast(Long doId) {
        return (DoNotice) this.createQuery("FROM DoNotice d WHERE d.doId = :doId ORDER BY d.createdAt DESC").setParameter("doId", doId).setMaxResults(1).uniqueResult();
    }

    public List<DoNotice> getNotices(Long doHeaderId) {
        return (List<DoNotice>) this.createQuery("FROM DoNotice d WHERE d.doId = :doId ORDER BY d.createdAt DESC").setParameter("doId", doHeaderId).list();
    }

    public void cancelNotice(Long doId, String holdBy) {
        this.createUpdateQuery("UPDATE DoNotice o SET o.isDeleted = 1, o.updatedBy = :updatedBy WHERE o.doId = :doId").setParameter("updatedBy", holdBy).setParameter("doId", doId).executeUpdate();
    }

    public List<DoNotice> getNoticesById(Long doHeaderId, Long warehouseId) {
        return (List<DoNotice>) this.createQuery("FROM DoNotice d WHERE d.doId = :doId and d.warehouseId = :warehouseId ORDER BY d.createdAt DESC").setParameter("doId", doHeaderId).setParameter("warehouseId", warehouseId).list();
    }
}
