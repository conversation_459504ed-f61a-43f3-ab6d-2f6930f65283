package com.daxia.wms.delivery.print.service.impl;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.jboss.seam.log.Log;

import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.daxia.framework.common.service.ReportGenerator;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.system.constants.Constants.YesNo;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.PrintInvoiceDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.invoice.dao.InvoiceDao;
import com.daxia.wms.delivery.invoice.entity.InvoiceDetail;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;
import com.daxia.wms.delivery.invoice.service.InvoiceNoService;
import com.daxia.wms.delivery.invoice.service.InvoiceService;
import com.daxia.wms.delivery.print.dto.DoInvoicePrintDTO;
import com.daxia.wms.delivery.print.dto.InvoiceHeaderDTO;
import com.daxia.wms.delivery.print.dto.RowDTO;
import com.daxia.wms.delivery.print.service.PrintDoInvoiceService;
import com.daxia.wms.delivery.print.service.PrintInvoiceService;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.ReleaseStatus;
import com.daxia.wms.master.entity.Printer;
import com.daxia.wms.master.service.PrinterService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.print.PrintConstants.PrintInvoicePos;
import com.daxia.wms.print.PrintConstants.PrintType;
import com.daxia.wms.print.dto.PrintCfg;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.print.service.impl.AbstractLodopPrint;
import com.daxia.wms.print.utils.PrintHelper;

@Name("printDoInvoiceService")
@lombok.extern.slf4j.Slf4j
public class PrintDoInvoiceServieImpl extends
		AbstractLodopPrint<InvoiceHeader> implements PrintDoInvoiceService {

	private static final String BINDED_INVOICE_USED_UP = "-1";// 绑定发票已用完

	@In
	private PrintInvoiceService printInvoiceService;

	@In
	private PrinterService printerService;

	@In
	private InvoiceService invoiceService;

	@In
	private InvoiceNoService invoiceNoService;

	@In
	private WaveService waveService;

	@In
	private InvoiceDao invoiceDao;

	@In
	private ReportGenerator reportGenerator;

	@In
	private DeliveryOrderService deliveryOrderService;

	@In
	private WarehouseService warehouseService;

	@In
	private PrintInvoiceDAO printInvoiceDAO;



	@Transactional
	@Override
	public PrintData printInvoice(DeliveryOrderHeader doHeader,
			String printerCode, PrintInvoicePos pos) {
		// 校验打印机
		Printer printer = printInvoiceService.validatePrinter(printerCode);
		// 打印机没有绑定发票
		if (StringUtil.isEmpty(printer.getInvoiceNoCurrent())) {
			throw new DeliveryException(DeliveryException.PRINTER_IS_NOT_BIND_INVOICE);
		}
		// 绑定发票已用完
		if (BINDED_INVOICE_USED_UP.equals(printer.getInvoiceNoCurrent())) {
			throw new DeliveryException(DeliveryException.BINDED_INVOICE_USED_UP_PLEANSE_CHANGE);
		}
		// 订单不需要发票
		if (doHeader.getInvoiceFlag() != 1) {
    		throw new DeliveryException(DeliveryException.ERROR_DO_HAS_NO_INVOICE);
    	}
		// 订单发票全部已经打印
		List<Long> doIds = Lists.newArrayList();
		doIds.add(doHeader.getId());
		List<Long> notPrintedIds = invoiceService.getInvoiceHIdsByDoIds(doIds);
		if (ListUtil.isNullOrEmpty(notPrintedIds)) {
    		throw new DeliveryException(DeliveryException.DO_INVOICE_ALL_PRINTED);
    	}
		
		// 订单已冻结
		if (!ReleaseStatus.RELEASE.getValue().equals(doHeader.getReleaseStatus())) {
			throw new DeliveryException(DeliveryException.DO_ALREADY_FROZEN);
		}
		// 订单状态
		if (StringUtil.isNotIn(doHeader.getStatus(), DoStatus.ALLSORTED.getValue(), 
				DoStatus.PART_CARTON.getValue(), DoStatus.ALL_CARTON.getValue(), 
				DoStatus.PART_LOAD.getValue(), DoStatus.ALL_LOAD.getValue())) {
			throw new DeliveryException(DeliveryException.DO_STATUS_ERROR_CAN_NOT_OPERATE);
		}
		
		PrintData pd = bindAndPrint(doHeader, printer, pos);
		PrintCfg printCfg = new PrintCfg();
		printCfg.setPrinter(printer);
		pd.setPrintCfg(printCfg);
		return pd;
	}

	private PrintData bindAndPrint(DeliveryOrderHeader doHeader, Printer printer, PrintInvoicePos pos) {
		long start = System.currentTimeMillis();
		List<Long> bindedInvoiceIdList = new ArrayList<Long>();
		List<InvoiceNo> invoiceNos = invoiceNoService.getInvoiceNoList(
				printer.getInvoiceCode(), printer.getInvoiceNoCurrent(),
				printer.getInvoiceNoTo());
		// 获取订单未打印的发票
		List<InvoiceHeader> invoiceList = invoiceDao.getNotPrintedInvoiceInDo(doHeader.getId());

		int i = 0;
		for (InvoiceHeader invoiceHeader : invoiceList) {
			if (i >= invoiceNos.size()) {
				break;
			}
			// 绑定
			invoiceNoService.bind(invoiceHeader, invoiceNos.get(i++));
			bindedInvoiceIdList.add(invoiceHeader.getId());
		}
		invoiceDao.getSession().flush();
		invoiceDao.getSession().clear();

		if (log.isDebugEnabled()) {
			log.debug("Print Invoice: {}", new Gson().toJson(bindedInvoiceIdList));
		}

		if (ListUtil.isNullOrEmpty(bindedInvoiceIdList)) {
			throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
		}
		this.paramMap.put("printerCode", printer.getCode());
		this.paramMap.put(this.PRINT_NOTES, pos.getValue() + "_lodop");
		this.paramMap.put(this.PRINT_TYPE, PrintType.INVOICE);
		// 获取打印数据
		PrintData printData = this.buildPrintDtos(bindedInvoiceIdList);
		// 修改发票打印状态
		printInvoiceService.setInvoicePrinted(bindedInvoiceIdList);
		// 如果发票不够，打完了要提示换发票
		if (invoiceNos.size() < invoiceList.size()) {
			// 设置异常提示信息
			printData.setUserDefine1(DeliveryException.INVOICE_NO_NOT_ENOUGH_PLEANSE_CHANGE);
			// 设置实际打印张数
			printData.setUserDefine2(String.valueOf(bindedInvoiceIdList.size()));
			// 设置未打印张数
			printData.setUserDefine3(String.valueOf(invoiceList.size() - invoiceNos.size()));
		}

		// 更新当前发票号码
		increaseCurrentInvoiceNo(printer, bindedInvoiceIdList.size());		
		long end = System.currentTimeMillis();
		if (log.isDebugEnabled()) {
			log.debug("{} Print Roll Invoice Costs: #1 ms",	"New", (end - start));
		}
		return printData;
	}

	/**
	 *
	 * @param detail
	 * @return
	 */
	private RowDTO buildInvoiceDetailDataInNewWay(InvoiceDetail detail,
			int index) {
		RowDTO rowDTO = new RowDTO();
		rowDTO.setRow1(detail.getSkuDescr().length() > 26 ? detail
				.getSkuDescr().substring(0, 26) : detail.getSkuDescr());
		rowDTO.setTopOffset((49.5 + index * 2 * 3.5) + "mm");
		if (detail.getSkuDescr().length() >= 26) {
			rowDTO.setRow2(detail.getSkuDescr().substring(26));
		} else {
			rowDTO.setRow2("");
		}

		if (rowDTO.getRow2().length() > 8) {
			rowDTO.setRow2(rowDTO.getRow2().substring(0, 8));
		} else if (rowDTO.getRow2().length() < 8) {
			rowDTO.setRow2(rowDTO.getRow2());
		}
		DecimalFormat df = new DecimalFormat("###0.00");
		rowDTO.setUnitPrice(df.format(detail.getPrice() == null ? BigDecimal.ZERO
				: detail.getPrice()));
		rowDTO.setQty(String.valueOf(detail.getQty() == null ? 0 : detail
				.getQty()));
		rowDTO.setTotalPrice(df.format(detail.getAmount() == null ? BigDecimal.ZERO
				: detail.getAmount()));
		rowDTO.setTopOffset((52.5 + index * 2 * 3.5) + "mm");
		return rowDTO;
	}
	
	/**
     * 当前发票号码增加指定数值（超过上限则置为-1）
     * @param printer
     * @param addNum
     */
    @Transactional
    public void increaseCurrentInvoiceNo(Printer printer,  long addNum) {
        String curStr = printer.getInvoiceNoCurrent();
        String toStr = printer.getInvoiceNoTo();
        long max = Long.parseLong(toStr);
        long cur = Long.parseLong(curStr) + addNum;
        if (cur > max) {
            cur = -1L;
            printer.setInvoiceNoCurrent(String.valueOf(cur));
        } else {
            printer.setInvoiceNoCurrent(StringUtil.convertToString(cur, toStr.length(), "0"));
        } 
        printerService.saveOrUpdate(printer);
    }
    
    
    public Printer validatePrinter(String printerCode) {
        //校验打印机编号是否为空
        if (StringUtil.isEmpty(printerCode)) {
            throw new DeliveryException(DeliveryException.PRINTER_CODE_IS_NULL);
        }
        //校验打印机是否存在
        Printer printer =printerService.findPrinterByCode(printerCode);
        if (null == printer) {
            throw new DeliveryException(DeliveryException.PRINTER_IS_NOT_EXIST);
        }
        //校验打印机系统状态是否可用
        if (YesNo.NO.getValue().equals(printer.getStatus())) {
            throw new DeliveryException(DeliveryException.PRINTER_IS_NOT_AVALIABLE);
        }
        return printer;
    }

	@Override
	protected List<InvoiceHeader> getSourceList(List<Long> ids) {
		List<InvoiceHeader> invoiceList = invoiceService
				.getInvoiceList4Print(ids);
		if (invoiceList.isEmpty()) {
			throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
		}
		return invoiceList;
	}

	@Override
	protected List<PrintReportDto> buildPrintDataDto(InvoiceHeader invoice) {
		String waveNo = "";
		List<PrintReportDto> invoiceHeaderDTOList = new ArrayList<PrintReportDto>();
		DoInvoicePrintDTO invoicePrintInfo = printInvoiceDAO
				.getInvoicePrintInfoOfDo(invoice.getDoHeaderId());
		if (StringUtil.isEmpty(waveNo)) {
			waveNo = printInvoiceDAO.getWaveNoByDoId(invoice
					.getDoHeaderId());
		}
		List<RowDTO> rows = new ArrayList<RowDTO>();
		int index = 0;
		List<InvoiceDetail> details = invoiceService.findInvoiceDetailsByHeaderId(invoice.getId());
		for (InvoiceDetail detail : details) {
			if (StringUtil.isEmpty(detail.getSkuDescr())) {// 当发票明细的商品名称描述为空时，放弃该条明细
				continue;
			}
			rows.add(buildInvoiceDetailDataInNewWay(detail, index));
			index = index + 1;
		}

		// 发票号码
		InvoiceHeaderDTO invoiceHeaderDTO = new InvoiceHeaderDTO();
		invoiceHeaderDTO.setInvoiceNo(invoice.getInvoiceNumber());
		invoiceHeaderDTO.setSoCode(invoice.getSoCode());
		invoiceHeaderDTO.setDoNo(invoicePrintInfo.getDoNo());
		invoiceHeaderDTO.setTotal(new DecimalFormat("###0.00")
				.format(invoice.getInvoiceAmount()));
		invoiceHeaderDTO.setTotalRmb(StringUtil.numToRMBStr(invoice
				.getInvoiceAmount()));
		invoiceHeaderDTO.setReceiver(PrintHelper.getNotNullOperateUser());
		invoiceHeaderDTO.setMachineNo((String)this.paramMap.get("printerCode"));
		invoiceHeaderDTO.setReceiveCom(invoice.getBranchName());
		invoiceHeaderDTO.setRegisterNum(invoice.getTaxNo());
		invoiceHeaderDTO.setDate(DateUtil.dateToString(
				DateUtil.getTodayDate(), "yyyy-MM-dd"));
		invoiceHeaderDTO.setPayer(invoice.getInvoiceTitle());
		invoiceHeaderDTO.setSortGridNo(invoicePrintInfo.getSortGridNo());
		invoiceHeaderDTO.setWaveNo(waveNo);
		invoiceHeaderDTO.setRowDtoList(rows);
		invoiceHeaderDTO.setDocId(invoice.getId());
		invoiceHeaderDTO.setDocNo(invoice.getInvoiceNumber());
		invoiceHeaderDTO.setRefNo(invoiceHeaderDTO.getDoNo());
		invoiceHeaderDTO.setOutRefNo(invoice.getSoCode());
		invoiceHeaderDTOList.add(invoiceHeaderDTO);
		return invoiceHeaderDTOList;
	}
}
