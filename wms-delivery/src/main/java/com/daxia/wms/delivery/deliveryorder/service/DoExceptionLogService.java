package com.daxia.wms.delivery.deliveryorder.service;

import java.util.List;

import com.daxia.wms.delivery.deliveryorder.dto.DoExStatusOpDto;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoExceptionLog;

public interface DoExceptionLogService {
	/**
	 * 添加do异常日志
	 * @param doExceptionLog
	 */
	public void saveDoExceptionLog(DoExceptionLog doExceptionLog);

	/**
	 * 获取订单异常日志
	 */
    public List<DoExceptionLog> findExceptionLog(Long doHeaderId);

    /**
     * 格式化日志信息
     */
    public List<String> formatExceptionLogs(List<DoExceptionLog> exceptionLogs);
    
    /**
     * 保存异常日志
     * @param doLogDto 保存doHeader原状态和操作人的dto
     * @param doHeader
     */
    public void saveDoExceptionLog(DoExStatusOpDto doLogDto, DeliveryOrderHeader doHeader);
    
    /**
     * 根据订单状态异常日志
     * @param doId
     * @param createBy
     */
    public void saveReleaseExpLog(Long doId, String createBy);
}
