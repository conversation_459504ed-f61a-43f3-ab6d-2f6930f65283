package com.daxia.wms.delivery.deliveryorder.job;

import com.daxia.dubhe.api.internal.util.NumberUtils;

import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.master.job.AbstractJob;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.*;
import org.jboss.seam.log.Log;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 第三方物流对账报表自动生成excel
 */
@Name("tplShippingInfoReportJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class TplShippingInfoReportJob extends AbstractJob {

    @In
    private CartonService cartonService;



    @Override
    protected void doRun() throws InterruptedException {
        List<String> whIds = Config.getByDelimit(Keys.Delivery.create_tpl_whIds, Config.ConfigLevel.GLOBAL);
        if (CollectionUtils.isEmpty(whIds)) {
            return;
        }
        for (String whId : whIds) {
            ParamUtil.setCurrentWarehouseId(NumberUtils.object2Long(whId));
            try {
                cartonService.createTplShippingInfoExcel();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

}