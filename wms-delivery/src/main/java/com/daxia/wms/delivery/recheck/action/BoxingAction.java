package com.daxia.wms.delivery.recheck.action;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

import com.daxia.framework.common.cfg.AutoLoadAndDeliverCfg;
import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Keys;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.security.Restrict;

import com.daxia.framework.common.action.ActionBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.DoWaveExService;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.master.entity.Carrier;

/**
 * 拆箱拼箱Action
 */
@Name("com.daxia.wms.delivery.boxingAction")
@Restrict("#{identity.hasPermission('delivery.boxing')}")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class BoxingAction extends ActionBean {

	private static final long serialVersionUID = 1743787137091084731L;

	/**
	 * 根据do号是否查询到箱号，用于判断页面上结果的显示
	 */
	private boolean hasResult;
	
	/**
	 * 输入的do号
	 */
	private String doNum;
	
	/**
	 * 如果为true，为拼箱；否则为拆箱
	 */
	private boolean boxing;
	
	/**
	 * from箱号的下拉列表
	 */
	private List<SelectItem> fromContainerNumlList;
	
	/**
	 * to箱号的下拉列表
	 */
	private List<SelectItem> toContainerNumlList; 
	
	
	/**
	 * 选择的from箱ID
	 */
	private Long fromContainerId;
	
	/**
	 * 选择的to箱ID
	 */
	private Long toContainerId;
	
	private Integer cartonCount;
	
	@In
	private CartonService cartonService;
	
	@In
	private DeliveryOrderService deliveryOrderService;
	
	@In
	private DoWaveExService doWaveExService;
	
	private Boolean isCarrierDoNo = false;
	
	private String carrierNo;
	
    /**
     * 查询装箱列表
     */
	public void doSearch(){
		DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(doNum);
		if(doHeader == null){
			throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
		}
		//订单只有在部分装箱和装箱完成状态才可以拆拼箱。
        if(StringUtil.isNotIn(doHeader.getStatus(), DoStatus.PART_CARTON.getValue(), DoStatus.ALL_CARTON.getValue())){
            throw new DeliveryException(DeliveryException.DO_STATUS_CANNOT_BOXING, doNum);
        }
        
        if (Constants.DoType.SELL.getValue().equals(doHeader.getDoType())) {
        	  if (null == doHeader.getCarrierId()){
              	throw new DeliveryException(DeliveryException.DO_HAS_NO_CARRIER);
              }
        	  Carrier carrier = doHeader.getCarrier();
              if(null != carrier.getCarrierCorp()){
              	isCarrierDoNo = true;
      			DoWaveEx doWaveEx = doWaveExService.findByDoHeaderId(doHeader.getId());
      			carrierNo = doWaveEx.getTrackingNo();
              }else{
          		isCarrierDoNo = false;
          		carrierNo = null;
              }
        } else {
        	isCarrierDoNo = false;
      		carrierNo = null;
        }
        
		List<CartonHeader> cartonhList = cartonService.findByDoNo(doNum);
		if(ListUtil.isNullOrEmpty(cartonhList)){
			hasResult = false;
			throw new DeliveryException(DeliveryException.DO_HAVE_NO_CARTON, doNum);
		} else {
			hasResult = true;
			setContainerNums(cartonhList);
		}
	}
	
	/**
	 * 加入进下拉列表
	 * @param cartonhList
	 */
	private void setContainerNums(List<CartonHeader> cartonhList){
		fromContainerNumlList = new ArrayList<SelectItem>();
		toContainerNumlList = new ArrayList<SelectItem>();
		for (CartonHeader cartonHeader : cartonhList) {
			SelectItem from = new SelectItem();
			SelectItem to = new SelectItem();
			to.setLabel(cartonHeader.getCartonNo());
			to.setValue(cartonHeader.getId());
			toContainerNumlList.add(to);
			//如果为拼箱且3PL配送，则从箱号不包含主单号 
			if (isCarrierDoNo && to.getLabel().equals(carrierNo) && boxing) {
				continue;
			}
			from.setLabel(cartonHeader.getCartonNo());
			from.setValue(cartonHeader.getId());
			fromContainerNumlList.add(from);
		}
	}
	
	/**
	 * 拼箱
	 */
	public void doBoxing(){
		validate();
		cartonService.boxing(fromContainerId, toContainerId);
		doSearch();
		sayMessage(MESSAGE_SUCCESS);
	}
	
	/**
	 * 拆箱
	 */
	public void doUnBoxing(){
		validate();
		cartonService.unBoxing(fromContainerId, cartonCount);
		doSearch();
		sayMessage(MESSAGE_SUCCESS);
	}
	
	/**
	 * 验证业务逻辑
	 * @return
	 */
	private boolean validate(){
		DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(doNum);
		if(doHeader == null){
			throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
		}
		//订单只有在部分装箱和装箱完成状态才可以拆拼箱。
		if(StringUtil.isNotIn(doHeader.getStatus(), DoStatus.PART_CARTON.getValue(), DoStatus.ALL_CARTON.getValue())
				&& !isDoCanBoxing(doHeader)){
			throw new DeliveryException(DeliveryException.DO_STATUS_CANNOT_BOXING, doNum);
		}
		if(boxing){
			if(fromContainerId == null || toContainerId == null) {
				throw new DeliveryException(DeliveryException.CARTON_NUM_CAN_NOT_EMPTY);
			}
			if(fromContainerId.equals(toContainerId)){
				throw new DeliveryException(DeliveryException.CARTON_NUM_CAN_NOT_SAME);
			}
		}
		if(!boxing){
			if(fromContainerId == null){
				throw new DeliveryException(DeliveryException.CARTON_NUM_CAN_NOT_EMPTY);
			}
			if(cartonCount == null || cartonCount <= 0){
				throw new DeliveryException(DeliveryException.CARTON_COUNT_ERROR);
			}
		}
		//COD非京东配送商订单不能装多箱
		if (!boxing && doHeader.getReceivable().compareTo(BigDecimal.ZERO) > 0 && !Constants.WaybillType.JD.equals(doHeader.getCarrier().getWaybillType())){
			if (CollectionUtils.isNotEmpty(cartonService.findByDoId(doHeader.getId()))) {
				throw new DeliveryException(DeliveryException.COD_MULTI_CARTON_NOT_ALLOW);
			}
		}
		return true;
	}

	/**
	 * 验证订单是否可以拆箱拼箱
	 * @param doHeader
	 * @return
     */
	private boolean isDoCanBoxing(DeliveryOrderHeader doHeader)
	{
		//不是交接完成状态，不能拆箱拼箱
		if (!doHeader.getStatus().equals(DoStatus.ALL_LOAD.getValue())) {
			return false;
		}

		String autoNode = Config.getFmJson(Keys.Delivery.auto_load_and_deliver_cfg, Config.ConfigLevel.WAREHOUSE, AutoLoadAndDeliverCfg.type);
		if (!StringUtil.isInArrays(autoNode, Constants.AutoLoadAndDeliverNode.PACK.getValue(), Constants.AutoLoadAndDeliverNode.WEIGHT.getValue())) {//是否装箱和称重自动交接
			return true;
		}
		return false;
	}

	public String getDoNum() {
		return doNum;
	}
	
	public void setDoNum(String doNum) {
		this.doNum = doNum;
	}
	
	public boolean getHasResult(){
		return hasResult;
	}
	
	public void setHasResult(boolean hasResult){
		this.hasResult = hasResult;
	}
	
	public boolean getBoxing(){
		return boxing;
	}
	
	public void setBoxing(boolean boxing){
		this.boxing = boxing;
	}
	
	public Long getFromContainerId() {
		return fromContainerId;
	}
	
	public void setFromContainerId(Long fromContainerId) {
		this.fromContainerId = fromContainerId;
	}
	
	public Long getToContainerId() {
		return toContainerId;
	}
	
	public void setToContainerId(Long toContainerId) {
		this.toContainerId = toContainerId;
	}
	
	public List<SelectItem> getFromContainerNumlList() {
		return fromContainerNumlList;
	}
	
	public void setFromContainerNumlList(List<SelectItem> fromContainerNumlList) {
		this.fromContainerNumlList = fromContainerNumlList;
	}
	
	public List<SelectItem> getToContainerNumlList() {
		return toContainerNumlList;
	}
	
	public void setToContainerNumlList(List<SelectItem> toContainerNumlList) {
		this.toContainerNumlList = toContainerNumlList;
	}
	
	public Integer getCartonCount() {
		return cartonCount;
	}
	
	public void setCartonCount(Integer cartonCount) {
		this.cartonCount = cartonCount;
	}
}
