package com.daxia.wms.delivery.deliveryorder.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;

import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.DoType;
import com.daxia.wms.Constants.NotifyCSType;
import com.daxia.wms.Constants.ReleaseStatus;
import com.daxia.wms.delivery.constant.DeliveryConstant;
import com.daxia.wms.delivery.deliveryorder.dto.DoAllocateHeaderDto;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateHeader;
import com.daxia.wms.delivery.deliveryorder.filter.DoAlcHeaderFilter;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateService;
import com.daxia.wms.delivery.deliveryorder.service.IDoAllocate;
import com.daxia.wms.exp.dto.DoExpDto;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.master.entity.*;
import com.daxia.wms.master.helper.SysConfigHelper;
import com.daxia.wms.master.service.AreaService;
import com.daxia.wms.master.service.BusinessCustomerService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.security.Restrict;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

@Name("com.daxia.wms.delivery.doAllocateAction")
@Restrict("#{identity.hasPermission('delivery.allocate')}")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class DoAllocateAction extends PagedListBean<DoAllocateHeaderDto> {

    private static final long serialVersionUID = 3194246682236658191L;

    private DoAlcHeaderFilter doAlcHeaderFilter;

    @In
    private DoAllocateService doAllocateService;
    @In
    private AreaService areaService;//查询区域用service
    @In
    private ExpFacadeService expFacadeService;

    @In
    private BusinessCustomerService businessCustomerService;


    private boolean needInit = true;
    
    //地区查询条件省，市，国家下拉数据
    private List<SelectItem> provinces = new ArrayList<SelectItem>();
    private List<SelectItem> cites = new ArrayList<SelectItem>();
    private List<SelectItem> counties = new ArrayList<SelectItem>();
    //地区变动是所选国家，省，市数据
    private Long countryId;
    private Long provinceId;
    private Long cityId;

    private String customerName;

    @Create
    public void initialize() {
        if (needInit) {
            doAlcHeaderFilter = new DoAlcHeaderFilter();
            doAlcHeaderFilter.setReleaseStatus(ReleaseStatus.RELEASE.getValue());
            doAlcHeaderFilter.setReplStatusArray(new Integer[]{});
            doAlcHeaderFilter.getOrderByMap().put("doCreateTime", "desc");
            doAlcHeaderFilter.getOrderByMap().put("planShipTime", "asc");
            needInit = false;
        }
    }

    public void receiveSelectBusinessCustomer(Long businessCustomerId) {
        BusinessCustomer businessCustomer = null;
        if (businessCustomerId == null) {
            businessCustomer = new BusinessCustomer();
        } else {
            businessCustomer = businessCustomerService.getBusinessCustomer(businessCustomerId);
        }
        this.doAlcHeaderFilter.setBusinessCustomerId(businessCustomerId);
        this.setCustomerName(businessCustomer.getCustomerName());

    }

    public void clearBusinessCustomer() {
        this.doAlcHeaderFilter.setBusinessCustomerId(null);
        this.setCustomerName(null);
    }

    @Override
    public void query() {
    	
    	//增加获取页面表格指定字段的排序规则
    	this.buildOrderFilterMap( doAlcHeaderFilter );
    
        DataPage<DoAllocateHeaderDto> dataPage = doAllocateService.query(doAlcHeaderFilter, getStartIndex(), getPageSize());
        this.populateValues(dataPage);
        
        this.selectedMap.clear();
    }

    /**
     * 批量分配
     * @throws Exception
     */
    @Loggable
    public void batchAllocate() throws Exception {
        List<DoAllocateHeader> headers = this.getSeletedAlcHeaders();
    
        Map<String, List<String>> resultMap = Maps.newHashMap();
        List<String> faildHeaderList = new ArrayList<String>();
        for (DoAllocateHeader header : headers) {
            if (DoType.RTV.getValue().equals(header.getDoType())) {
                faildHeaderList.add(header.getDoNo());
                continue;
            }
        
            try {
                resultMap = doAllocateService.autoAllocate(header.getId());
                if (resultMap.get(IDoAllocate.NO_ENOUGH_STOCK_QTY) != null) {
                    // 调用接口通知客服；
                    DoExpDto dto = new DoExpDto();
                    dto.setId(header.getId());
                    dto.setHoldCode(Constants.Reason.ALLOC_LACK.getValue());
                    dto.setNotifyType(NotifyCSType.AUTO.getValue());
                    // 如果配置自动通知客服，则缺货自动通知客服。
                    Boolean lackAutoAnounceCs = SysConfigHelper.getSwitchDefalutOpen("alloc.lack.autoAnounceCs");
                    if (lackAutoAnounceCs) {
                        expFacadeService.callCS(dto);
                    }
                    // doExpService.notifyExpcetionDoOms(dto);
                    /**冻结do时调scs重新计算预计出库时间接口*/
                    expFacadeService.sendDoReleaseOrHold2Scs(header.getId());
                }
            
                if (StringUtil.isIn(header.getStatus(), DoStatus.INITIAL.getValue(), DoStatus.PARTALLOCATED.getValue())) {
                    faildHeaderList.add(header.getDoNo());
                }
            } catch (Exception e) {
                if (resultMap.get(e.getMessage()) == null) {
                    resultMap.put(e.getMessage(), Lists.<String>newArrayList(header.getDoNo()));
                } else {
                    resultMap.get(e.getMessage()).add(header.getDoNo());
                }
                faildHeaderList.add(header.getDoNo());
            }
        }
        if (resultMap.isEmpty()) {
            this.sayMessage(MESSAGE_SUCCESS);
        } else {
            for (Entry<String, List<String>> entry : resultMap.entrySet()) {
                String msg = DeliveryConstant.getAllocatMessage(entry.getKey());
                if (StringUtil.isBlank(msg)) {
                    msg = entry.getKey();
                }
                this.sayMessage(msg, ListUtil.collection2String(entry.getValue(), ","));
            }
        }
        this.query();
    }

    /**
     * 批量取消分配
     */
    @Loggable
    public void batchAlcCancel() {
        List<DoAllocateHeader> headers = this.getSeletedAlcHeaders();

        List<String> faildHeaderList = new ArrayList<String>();
        for (DoAllocateHeader header : headers) {
            try {
                doAllocateService.cancelAssign(header, false,true);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                faildHeaderList.add(header.getDoNo());
            }
        }

        if (faildHeaderList.isEmpty()) {
            this.sayMessage(MESSAGE_SUCCESS);
        } else {
            this.sayMessage("delivery.operator.error", faildHeaderList.toString());
        }
        
        this.query();
    }

    /**
     * 获取选中的AlcHeader
     * @return
     */
    private List<DoAllocateHeader> getSeletedAlcHeaders() {
        List<Object> selectDetailIds = this.getSelectedRowList();
        List<Long> ids = new ArrayList<Long>(selectDetailIds.size());
        for (Object obj : selectDetailIds) {
            ids.add((Long) obj);
        }

        return doAllocateService.getHeaderByIds(ids);
    }

    public DoAlcHeaderFilter getDoAlcHeaderFilter() {
        return doAlcHeaderFilter;
    }

    public void setDoAlcHeaderFilter(DoAlcHeaderFilter doAlcHeaderFilter) {
        this.doAlcHeaderFilter = doAlcHeaderFilter;
    }
    
    /**
     * 
     * <pre>
     * Description:得到所有的国家信息
     * </pre>
     *
     * @return 国家信息
     */
    public List<SelectItem> findCountryList() {
        List<Country> counties = this.areaService.finAllCountry();
        List<SelectItem> list = new ArrayList<SelectItem>();
        for (Country country : counties) {
            SelectItem item = new SelectItem();
            item.setValue(country.getId());
            item.setLabel(country.getCountryCname());
            list.add(item);
        }
        return list;
    }
    
    /**
     * 
     * <pre>
     * Description: 国家下拉列表发生变动事件
     * </pre>
     *
     */
    public void changeListeForProvince() {
        provinces.clear();
        this.doAlcHeaderFilter.setProvince(null);
        this.doAlcHeaderFilter.setCity(null);
        this.doAlcHeaderFilter.setCounty(null);
        if (this.countryId != null && this.countryId.longValue() != -1) {
            List<Province> list = this.areaService.findProvinceByCountry(this.countryId);
            for (Province province : list) {
                SelectItem item = new SelectItem();
                item.setValue(province.getId());
                item.setLabel(province.getProvinceCname());
                provinces.add(item);
            }
        }
        this.countryId = null;
        cites.clear();
        counties.clear();
    }

    /**
     * 
     * <pre>
     * Description: 省份下拉列表发生变动事件
     * </pre>
     *
     */
    public void changeListeForCity() {
        cites.clear();
        this.doAlcHeaderFilter.setCity(null);
        this.doAlcHeaderFilter.setCounty(null);
        if (this.provinceId != null && this.provinceId.longValue() != -1) {
            List<City> list = this.areaService.findCityByProvince(this.provinceId);
            for (City city : list) {
                SelectItem item = new SelectItem();
                item.setValue(city.getId());
                item.setLabel(city.getCityCname());
                cites.add(item);
            }
        }
        this.provinceId = null;
        counties.clear();
    }

    /**
     * 
     * <pre>
     * Description:城市下拉列表发生变动事件
     * </pre>
     */
    public void changeListeForCounty() {
        counties.clear();
        this.doAlcHeaderFilter.setCounty(null);
        if (this.cityId != null && this.cityId.longValue() != -1) {
            List<County> list = this.areaService.findCountyByCity(this.cityId);
            for (County county : list) {
                SelectItem item = new SelectItem();
                item.setValue(county.getId());
                item.setLabel(county.getCountyCname());
                counties.add(item);
            }
        }
        this.cityId = null;
    }

	public List<SelectItem> getProvinces() {
		return provinces;
	}

	public void setProvinces(List<SelectItem> provinces) {
		this.provinces = provinces;
	}

	public List<SelectItem> getCites() {
		return cites;
	}

	public void setCites(List<SelectItem> cites) {
		this.cites = cites;
	}

	public List<SelectItem> getCounties() {
		return counties;
	}

	public void setCounties(List<SelectItem> counties) {
		this.counties = counties;
	}

	public Long getCountryId() {
		return countryId;
	}

	public void setCountryId(Long countryId) {
		this.countryId = countryId;
	}

	public Long getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}

	public Long getCityId() {
		return cityId;
	}

	public void setCityId(Long cityId) {
		this.cityId = cityId;
	}

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
}