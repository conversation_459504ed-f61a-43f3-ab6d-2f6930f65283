package com.daxia.wms.delivery.recheck.service.impl.carton;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.system.SequenceKeys;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.master.entity.Carrier;
import com.daxia.wms.master.entity.CarrierCainiaoEx;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.CarrierCainiaoExService;
import com.daxia.wms.master.service.ShopInfoService;
import com.daxia.wms.master.service.SkuCache;
import com.daxia.wms.waybill.cainiao.CainiaoConstants;
import com.daxia.wms.waybill.cainiao.CainiaoConstants.ProductType;
import com.google.common.collect.Lists;
import com.taobao.api.ApiException;
import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.request.CainiaoWaybillIiGetRequest;
import com.taobao.api.response.CainiaoWaybillIiGetResponse;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Name("cainiaoWayBillGet")
@lombok.extern.slf4j.Slf4j
public class CainiaoWayBillGet extends CainiaoWayBillBase {

    @In
    CarrierCainiaoExService carrierCainiaoExService;

    @In
    SkuCache skuCache;

    @In
    ShopInfoService shopInfoService;

    @In
    SequenceGeneratorService sequenceGeneratorService;



    public CainiaoWaybillIiGetResponse.WaybillCloudPrintResponse reqeust(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        //设置LPN为WMS箱号
        cartonHeader.setRefNo(sequenceGeneratorService.generateSequenceNo(SequenceKeys.CARTON_NO, ParamUtil.getCurrentWarehouseId()));

        Carrier carrier = doHeader.getCarrier();
        CarrierCainiaoEx carrierCainiaoEx = carrierCainiaoExService.getByCarrier(carrier.getId());
        if (carrierCainiaoEx == null) {
            throw new DeliveryException(DeliveryException.WAYBILL_CAINIO_GET_ERROR, "配送商菜鸟配置信息缺失，请联系管理员");
        }
        WarehouseCarrier warehouseCarrier = loadWarehouseCarrier(carrier.getId());

        CainiaoWaybillIiGetRequest req = new CainiaoWaybillIiGetRequest();
        req.setParamWaybillCloudPrintApplyNewRequest(genWaybillApplyNewRequest(doHeader, cartonHeader, carrierCainiaoEx));

        TaobaoClient client = new DefaultTaobaoClient(CainiaoConstants.getCainiaoConfig().getServerUrl(), warehouseCarrier.getAppKey(), warehouseCarrier.getAppSecret());
        try {
            CainiaoWaybillIiGetResponse rsp = client.execute(req, warehouseCarrier.getAppToken());

            if (!rsp.isSuccess()) {
                log.error("Cainiao get error, request:" + req.getParamWaybillCloudPrintApplyNewRequest() + ", response: " + rsp.getBody());
                throw new DeliveryException(DeliveryException.WAYBILL_CAINIO_GET_ERROR, rsp.getMsg() + ", " + rsp.getSubMsg());
            }

            return rsp.getModules().get(0);
        } catch (ApiException e) {
            log.error("Cainiao get error! ", e);
            throw new DeliveryException(DeliveryException.WAYBILL_CAINIO_GET_ERROR);
        }
    }

    private CainiaoWaybillIiGetRequest.WaybillCloudPrintApplyNewRequest genWaybillApplyNewRequest(DeliveryOrderHeader doHeader, CartonHeader cartonHeader, CarrierCainiaoEx carrierCainiaoEx) {
        CainiaoWaybillIiGetRequest.WaybillCloudPrintApplyNewRequest applyNewRequest = new CainiaoWaybillIiGetRequest.WaybillCloudPrintApplyNewRequest();
        // 物流服务商编码
        applyNewRequest.setCpCode(carrierCainiaoEx.getCpCode());
        // 快递服务产品类型编码
        applyNewRequest.setProductCode(ProductType.STANDARD_EXPRESS.name());
        if (doHeader.getReceivable() != null && doHeader.getReceivable().compareTo(BigDecimal.ZERO) > 0) {
            applyNewRequest.setProductCode(ProductType.COD.name());
        }
        applyNewRequest.setSender(genShippingAddress4Get(carrierCainiaoEx, doHeader));
        applyNewRequest.setTradeOrderInfoDtos(Lists.newArrayList(genTradeOrderInfo(doHeader, cartonHeader, carrierCainiaoEx)));
//        applyNewRequest.setNeedEncrypt(Config.isDefaultFalse(Keys.Delivery.cainiao_way_bill_need_encrypt, Config.ConfigLevel.WAREHOUSE));
        return applyNewRequest;
    }

    // 面单详细信息
    private CainiaoWaybillIiGetRequest.TradeOrderInfoDto genTradeOrderInfo(DeliveryOrderHeader doHeader, CartonHeader cartonHeader, CarrierCainiaoEx carrierCainiaoEx) {
        CainiaoWaybillIiGetRequest.TradeOrderInfoDto tradeOrderInfo = new CainiaoWaybillIiGetRequest.TradeOrderInfoDto();
        //收货人信息
        tradeOrderInfo.setRecipient(genConsigneeInfo4Get(doHeader));

        CainiaoWaybillIiGetRequest.OrderInfoDto orderInfoDto = new CainiaoWaybillIiGetRequest.OrderInfoDto();
        // 订单渠道
        orderInfoDto.setOrderChannelsType(CainiaoHelper.converOrderChannelsType(doHeader.getOrderSource()));
        orderInfoDto.setTradeOrderList(Lists.newArrayList(doHeader.getDoNo()));
        tradeOrderInfo.setOrderInfo(orderInfoDto);
        // 使用者ID(发货时商家 ID一致)
        // 淘系订单【使用者 id】可以使用查询卖家用户信息接口taobao.user.seller.get获取
        // 非淘系订单【使用者id】直接使用【申请者id】， 风控只针对淘系订单
        tradeOrderInfo.setObjectId(doHeader.getDoNo() + cartonHeader.getRefNo());
        tradeOrderInfo.setUserId(genRealUserId(doHeader, carrierCainiaoEx));
        tradeOrderInfo.setPackageInfo(genPackageIntem(doHeader, cartonHeader));
        // 交易订单列表
//        tradeOrderInfo.setPackageId(cartonHeader.getRefNo());
        //设置打印模版
        if (carrierCainiaoEx.getTemplateURL() == null) {
            throw new DeliveryException(DeliveryException.WAYBILL_CAINIO_GET_ERROR, "配送商打印模版配置信息缺失，请联系管理员");
        }
        tradeOrderInfo.setTemplateUrl(carrierCainiaoEx.getTemplateURL());
        tradeOrderInfo.setLogisticsServices(genLogisticsServiceList4Get(doHeader));

        return tradeOrderInfo;
    }

    // 包裹中的商品类型
    private CainiaoWaybillIiGetRequest.PackageInfoDto genPackageIntem(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        List<DeliveryOrderDetail> details = doHeader.getDoDetails();
        CainiaoWaybillIiGetRequest.PackageInfoDto packageItem = new CainiaoWaybillIiGetRequest.PackageInfoDto();
        List<CainiaoWaybillIiGetRequest.Item> itemList = new ArrayList<CainiaoWaybillIiGetRequest.Item>();
        for (DeliveryOrderDetail detail : details) {
            String skuName = skuCache.getSku(detail.getSkuId()).getProductCname();
            if (StringUtils.isNotEmpty(skuName) && itemList.size() < 100) {
                CainiaoWaybillIiGetRequest.Item item = new CainiaoWaybillIiGetRequest.Item();
                item.setName(skuName);
                item.setCount(detail.getExpectedQty().longValue());
                itemList.add(item);
            }
        }
        if (cartonHeader != null) {
            packageItem.setId(cartonHeader.getRefNo());
        }
        packageItem.setItems(itemList);

        return packageItem;
    }
}