//package com.daxia.wms.delivery.load.job;
//
//import com.daxia.framework.common.annotation.Loggable;
//import com.daxia.framework.common.cfg.AutoLoadAndDeliverCfg;
//import com.daxia.framework.common.util.*;
//import com.daxia.wms.Constants;
//import com.daxia.wms.Keys;
//import com.daxia.wms.delivery.recheck.entity.CartonHeader;
//import com.daxia.wms.delivery.recheck.service.CartonService;
//import com.daxia.wms.master.entity.Warehouse;
//import com.daxia.wms.master.job.AbstractJob;
//import com.daxia.wms.master.service.WarehouseService;
//import com.google.common.collect.Lists;
//import org.jboss.seam.Component;
//import org.jboss.seam.annotations.AutoCreate;
//import org.jboss.seam.annotations.In;
//import org.jboss.seam.annotations.Logger;
//import org.jboss.seam.annotations.Name;
//import org.jboss.seam.log.Log;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.concurrent.CountDownLatch;
//
///**
// * 自动交接的定时任务：定时对 装箱完成的DO类型订单自动交接发货。
// */
//@Name("com.daxia.wms.delivery.autoLoadAndDeliverJob")
//@AutoCreate
//@lombok.extern.slf4j.Slf4j
//public class AutoLoadAndDeliverJob extends AbstractJob {
//
//    private static volatile boolean running = false;
//    @In
//    private CartonService cartonService;
//
//    @In
//    private WarehouseService warehouseService;
//
//    // 一次交接订单的数量
//    private static final int DEFAULT_BATCH_LOAD_NUM = 500;
//
//    public boolean isWaiting() {
//        return !running;
//    }
//
//    /**
//     * 筛选满足条件的箱进行交接发货
//     */
//    @Loggable
//    public void doRun() {
//        if (this.isWaiting()) {
//            log.info("Begin Run autoLoadAndDeliverJob, Please Waiting ............... ");
//            running();
//            try {
//                //取得DB下的所有仓库List
//                List<Warehouse> warehouseList = warehouseService.findAll();
//                List<Long> whIdList = new ArrayList<Long>();
//                for (Warehouse warehouse : warehouseList) {
//                    ParamUtil.setCurrentWarehouseId(warehouse.getId());
//                    String autoNode = Config.getFmJson(Keys.Delivery.auto_load_and_deliver_cfg, Config.ConfigLevel.WAREHOUSE, AutoLoadAndDeliverCfg.type);
//                    if (StringUtil.isInArrays(autoNode, Constants.AutoLoadAndDeliverNode.PACK.getValue(), Constants.AutoLoadAndDeliverNode.WEIGHT.getValue())) {//是否装箱和称重自动交接
//                        whIdList.add(warehouse.getId());
//                    }
//                }
//                List<CartonHeader> cartonList = cartonService.findCartons4AutoLoad(whIdList);
//                Integer multiThreadNum = getMultiThreadNum();
//                if (ListUtil.isNotEmpty(cartonList)) {
//                    if (cartonList.size() > multiThreadNum * DEFAULT_BATCH_LOAD_NUM) {
//                        cartonList = cartonList.subList(0, multiThreadNum * DEFAULT_BATCH_LOAD_NUM - 1);
//                    }
//
//                    // 实际线程数
//                    int threadNum = (cartonList.size() + DEFAULT_BATCH_LOAD_NUM - 1) / DEFAULT_BATCH_LOAD_NUM;
//
//                    if (threadNum == 1) {
//                        // 单线程交接
//                        log.debug("Single thread load And Deliver");
//
//                        AutoLoadAndDeliverExecutor executor = ((AutoLoadAndDeliverExecutor) Component.getInstance(AutoLoadAndDeliverExecutor.class));
//                        executor.doLoadAndDeliver(cartonList);
//                    } else {
//                        // 多线程交接
//                        log.debug("Multiple threads load And Deliver with threadNum: " + threadNum);
//                        CountDownLatch threadSignal = new CountDownLatch(threadNum);
//
//                        List<List<CartonHeader>> partitions = Lists.partition(cartonList, DEFAULT_BATCH_LOAD_NUM);
//                        for (int i = 0; i < partitions.size(); i++) {
//                            new AutoLoadAndDeliverTask(i, partitions.get(i), threadSignal).start();
//                        }
//
//                        threadSignal.await();
//                    }
//                }
//            } catch (Exception e) {
//                log.error("error run autoLoadAndDeliverJob", e);
//            } finally {
//                unRunning();
//                log.info("End autoLoadAndDeliverJob. ");
//            }
//        } else {
//            log.info("autoLoadAndDeliverJob Run Already. ");
//        }
//    }
//
//    private static void running() {
//        running = true;
//    }
//
//    private static void unRunning() {
//        running = false;
//    }
//
//    /**
//     * 分配线程数
//     */
//    private int getMultiThreadNum() {
//        Integer nThreads = SystemConfig.getConfigValueInt("delivery.loadAndDeliverJob.multiThreadNum", null);
//        if (nThreads == null || nThreads < 1 || nThreads > 10) {
//            nThreads = 1;
//        }
//        return nThreads;
//    }
//}

