package com.daxia.wms.delivery.invoice.extend.aisinogz.dto;


import com.daxia.wms.invoice.entity.ReturnElectronice;

import javax.xml.bind.annotation.*;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="out" type="{http://pojo.hessian.companyInterface.dzfp.fp.aisinogd.com}ReturnElectronice"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "out" })
@XmlRootElement(name = "invEliResponse")
@lombok.extern.slf4j.Slf4j
public class InvEliResponse {

	@XmlElement(required = true, nillable = true)
	protected ReturnElectronice out;

	/**
	 * Gets the value of the out property.
	 * 
	 * @return possible object is {@link ReturnElectronice }
	 * 
	 */
	public ReturnElectronice getOut() {
		return out;
	}

	/**
	 * Sets the value of the out property.
	 * 
	 * @param value
	 *            allowed object is {@link ReturnElectronice }
	 * 
	 */
	public void setOut(ReturnElectronice value) {
		this.out = value;
	}

}
