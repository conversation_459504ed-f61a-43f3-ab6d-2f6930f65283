package com.daxia.wms.delivery.print.dto;

import java.math.BigDecimal;
import java.util.List;

@lombok.extern.slf4j.Slf4j
public class NopaperListDTO {
	//do单号 
	public String doNo;
	//do条码
	public String barCodeUrl;
	//so条码
	public String soCode;
	//货物总数量
	public BigDecimal sumqty;
	
	private Boolean invoiceFlag = Boolean.FALSE;
	//送货明细和延保产品清单
	public List<NopaperPrintDTO> nopaperList;

	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}

	public BigDecimal getSumqty() {
		return sumqty;
	}

	public void setSumqty(BigDecimal sumqty) {
		this.sumqty = sumqty;
	}

	public String getBarCodeUrl() {
		return barCodeUrl;
	}

	public void setBarCodeUrl(String barCodeUrl) {
		this.barCodeUrl = barCodeUrl;
	}

	public String getSoCode() {
		return soCode;
	}

	public void setSoCode(String soCode) {
		this.soCode = soCode;
	}

	public List<NopaperPrintDTO> getNopaperList() {
		return nopaperList;
	}

	public void setNopaperList(List<NopaperPrintDTO> nopaperList) {
		this.nopaperList = nopaperList;
	}

    public Boolean getInvoiceFlag() {
        return invoiceFlag;
    }

    public void setInvoiceFlag(Boolean invoiceFlag) {
        this.invoiceFlag = invoiceFlag;
    }
}
