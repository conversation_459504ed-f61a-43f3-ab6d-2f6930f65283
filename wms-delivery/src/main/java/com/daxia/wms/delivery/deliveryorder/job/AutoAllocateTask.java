package com.daxia.wms.delivery.deliveryorder.job;

import com.daxia.framework.common.util.ParamUtil;
import org.jboss.seam.Component;
import org.jboss.seam.contexts.Lifecycle;

import java.util.List;
import java.util.concurrent.CountDownLatch;

@lombok.extern.slf4j.Slf4j
public class AutoAllocateTask extends Thread {

    private List<Long> alcheaderIds; // 待分配订单

    private CountDownLatch threadSignal;

    private Long warehouseId;

    public AutoAllocateTask(int i, List<Long> alcheaderIds, CountDownLatch threadSignal,Long warehouseId) {
        super("AutoAllocateTask_" + i);
        this.alcheaderIds = alcheaderIds;
        this.threadSignal = threadSignal;
        this.warehouseId = warehouseId;
    }

    private void setup() {
        Lifecycle.beginCall();
        ParamUtil.setCurrentWarehouseId(warehouseId);
    }

    @Override
    public void run() {
        try {
            setup();
            AutoAllocateExecutor executor = ((AutoAllocateExecutor) Component.getInstance(AutoAllocateExecutor.class));
            executor.doAllocate(alcheaderIds,warehouseId);
        } finally {
            threadSignal.countDown();
            cleanup();
        }
    }

    private void cleanup() {
        Lifecycle.endCall();
    }
}