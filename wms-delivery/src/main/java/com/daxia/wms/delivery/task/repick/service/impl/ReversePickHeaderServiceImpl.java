package com.daxia.wms.delivery.task.repick.service.impl;

import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.task.repick.dao.ReversePickHeaderDAO;
import com.daxia.wms.delivery.task.repick.entity.ReversePickHeader;
import com.daxia.wms.delivery.task.repick.filter.ReversePickHeaderFilter;
import com.daxia.wms.delivery.task.repick.service.RePickContainerService;
import com.daxia.wms.delivery.task.repick.service.ReversePickHeaderService;
import com.daxia.wms.stock.task.dao.TrsTaskDAO;
import com.daxia.wms.stock.task.entity.TrsTask;
import com.daxia.wms.stock.task.filter.TrsTaskFilter;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 反拣单头信息Service实现类
 */
@Name("com.daxia.wms.delivery.reversePickHeaderService")
@lombok.extern.slf4j.Slf4j
public class ReversePickHeaderServiceImpl implements ReversePickHeaderService {
	
	@In
	private ReversePickHeaderDAO reversePickHeaderDAO;
	@In
	private SequenceGeneratorService sequenceGeneratorService;
	@In
    private TrsTaskDAO trsTaskDAO;
	@In
    RePickContainerService rePickContainerService;
	
	/**
     * 查询反拣单头信息分页信息
     */
    @Override
    public DataPage<ReversePickHeader> query( ReversePickHeaderFilter reversePickHeaderFilter, int startIndex,int pageSize) {
        return this.reversePickHeaderDAO.findRangeByFilter(reversePickHeaderFilter, startIndex, pageSize);
    }

    /**
     * 生成返捡单业务: 
     *    1、根据页面所选库区查出所有初始化状态的返捡任务，如果没有反拣任务则抛出异常
     *    2、生成返拣单头信息
     *    3、将返拣任务与返拣头信息进行关联，并且将返拣任务的状态改为发布状态
     */
    @Override
    @Transactional
    public void saveReversePickHeader(Long partitionId) throws DeliveryException {
        try {
            //1、根据页面所选库区查出所有初始化状态的返捡任务，如果没有反拣任务则抛出异常
            TrsTaskFilter trsTaskFilter = new TrsTaskFilter();
            trsTaskFilter.setTaskStatus(Constants.TaskStatus.INITIALIZED.getValue());
            trsTaskFilter.setTaskType(Constants.TaskType.RK.getValue());
            List<TrsTask> tasks = this.trsTaskDAO.findByFilter(trsTaskFilter);
            if (tasks == null || tasks.isEmpty()) {
                throw new DeliveryException(DeliveryException.REVERSEPICK_ADD_FAILED_NOTASK);
            }

            Map<Long, List<Long>> doMap1 = new HashMap<Long, List<Long>>();
            Map<Long, List<Long>> doMap2 = new HashMap<Long, List<Long>>();

            for (TrsTask trsTask : tasks) {
                Long doId = trsTask.getDocId();
                if (trsTask.getQty().compareTo(trsTask.getQtyUnit()) == 0) {
                    List<Long> temLong = doMap1.get(doId);
                    if (temLong == null) {
                        temLong = new ArrayList<Long>();
                        doMap1.put(doId, temLong);
                    }
                    temLong.add(trsTask.getId());
                } else {
                    List<Long> temLong = doMap2.get(doId);
                    if (temLong == null) {
                        temLong = new ArrayList<Long>();
                        doMap2.put(doId, temLong);
                    }
                    temLong.add(trsTask.getId());
                }
            }
    
            createReversePickTask(doMap1, PickHeader.PKT_TYPE_PCS);
            createReversePickTask(doMap2, PickHeader.PKT_TYPE_UNIT);
            trsTaskDAO.getSession().flush();
        } catch (DeliveryException de) {
            throw de;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new DeliveryException(DeliveryException.SYSTEM_ERROR);
        }
    }

    private void createReversePickTask(Map<Long, List<Long>> doMap, Integer pktType) {
        for (List<Long> taskIdList : doMap.values()) {
            //2、生成返拣单头信息
            TrsTask task = this.trsTaskDAO.findTask(taskIdList.get(0), Constants.TaskType.RK.getValue());
            ReversePickHeader reversePickHeader = new ReversePickHeader();
            String rkNo = this.sequenceGeneratorService.generateSequenceNo(Constants.SequenceName.REVERSEPICKNO.getValue(), ParamUtil.getCurrentWarehouseId());
            reversePickHeader.setStatus(Constants.TaskStatus.RELEASED.getValue());
            reversePickHeader.setRpNo(rkNo);
            reversePickHeader.setDoNo(task.getDocNo());
            reversePickHeader.setReasonCode(task.getUserdefine02());
            reversePickHeader.setPktType(pktType);
            this.reversePickHeaderDAO.save(reversePickHeader);
            //3、将返拣任务与返拣头信息进行关联，并且将返拣任务的状态改为发布状态
            this.trsTaskDAO.updateReversePickTask(reversePickHeader.getId(), Constants.TaskStatus.RELEASED.getValue(), reversePickHeader.getRpNo(), taskIdList);
    
            rePickContainerService.bindRepickHeader(pktType.equals(PickHeader.PKT_TYPE_PCS) ? "B" : "C", task.getDocId(), reversePickHeader.getId());
        }
    }

    /**
     * 根据ID获取反拣单头
     */
    @Override
    public ReversePickHeader get(Long reversePickHeaderId) {
        return reversePickHeaderDAO.get(reversePickHeaderId);
    }

    /**
     * 未完待续的反拣任务(INITIALIZED,RELEASED,SUSPENDED)的个数
     */
    @Override
    public Long getNeedDoingTaskCount(Long docId) {
        return this.reversePickHeaderDAO.getNeedDoingTaskCount(docId);
    }
}
