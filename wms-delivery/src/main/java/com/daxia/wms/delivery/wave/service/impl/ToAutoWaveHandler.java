package com.daxia.wms.delivery.wave.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.jboss.seam.annotations.In;

import com.daxia.wms.delivery.deliveryorder.service.ToAutoHandler;
import com.daxia.wms.delivery.wave.dto.WaveGeneContextDTO;
import com.daxia.wms.Constants;

@lombok.extern.slf4j.Slf4j
public class ToAutoWaveHandler extends ToAutoHandler {

	@In("autoWaveService")
    private WaveStrategy waveStrategy;
	
	@Override
	protected void handle(Long doId) {
        if (null == doId) {
            return;
        }
        List<Long> doIdList = new ArrayList<Long>();
        doIdList.add(doId);

        WaveGeneContextDTO context = new WaveGeneContextDTO();
        context.setDoIdList(doIdList);
        context.setMaxDOQty(Long.valueOf(1));
        context.setWavePriority(Constants.WavePriority.THIRD.getValue());
        context.setIsRecommend(Boolean.FALSE);
        try {
            waveStrategy.generateWave(context);
        } catch (Exception e) {
            log.error("ToAutoWaveHandler handle do {" + doId + "} failed", e);
        }
	}

	@Override
	protected void setName() {
		this.name = "toAutoWaveHandler";		
	}

}
