package com.daxia.wms.delivery.deliveryorder.service;

import java.util.List;

import com.daxia.dubhe.api.wms.request.DoRequest;
import com.daxia.tianshu.api.request.DeliveryOrderRequest;
import com.daxia.wms.Constants.AutoWaveType;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.delivery.deliveryorder.entity.SpecialDoLabel;

public interface SpecialDoLabelService {

    List<SpecialDoLabel> findAvailable();

    AutoWaveType findWaveDetailType(List<Long> selectedDOIdList);

    //如果波次细分类型对应的特殊标记中的记录是可获取或者为空，那么可获取
    boolean isRequestAvailable(Integer waveDetailType);
}