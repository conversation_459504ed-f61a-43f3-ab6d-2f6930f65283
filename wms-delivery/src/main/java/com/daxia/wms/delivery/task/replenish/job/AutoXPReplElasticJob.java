package com.daxia.wms.delivery.task.replenish.job;

import com.daxia.framework.common.util.BusinessException;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.task.replenish.service.ReplHeaderService;
import com.daxia.wms.delivery.task.replenish.service.ReplenishTaskGenerateContext;
import com.daxia.wms.delivery.task.replenish.service.impl.ReplHeaderServiceImpl;
import com.daxia.wms.delivery.task.replenish.service.impl.ReplenishTaskGenerateContextImpl;
import com.daxia.wms.master.job.WarehouseSimpleJob;
import com.daxia.wms.util.AlarmUtil;
import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.log.Log;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.util.HashSet;
import java.util.Set;

@Name("autoXPReplElasticJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class AutoXPReplElasticJob extends WarehouseSimpleJob {



    private static Set<String> ignoreExceptions = new HashSet<String>();

    static {
        ignoreExceptions.add("error.delivery.noLocNeedFreeTimeRepl");
        ignoreExceptions.add("error.stock.noInitReplTaskExist");
    }

    @Override
    protected void doRun() throws Exception {
        try {
            ReplenishTaskGenerateContext replenishTaskGenerateContext = ((ReplenishTaskGenerateContext) Component.getInstance(ReplenishTaskGenerateContextImpl.class));
            ReplHeaderService replHeaderService = ((ReplHeaderService) Component.getInstance(ReplHeaderServiceImpl.class));

            replenishTaskGenerateContext.createXPReplenishTask(null, null, null, Boolean.TRUE);
            replHeaderService.addReplTask(Constants.ReplType.XP.getValue(), null, Boolean.TRUE);
        } catch (Exception e) {
            //ignoreExceptions类型的BusinessException不做处理
            if (e instanceof BusinessException && ignoreExceptions.contains(e.getMessage())) {
                log.warn("Auto Publish Repl Task Faild:" + e.getMessage());
            } else {
                log.error("Auto Publish Repl Task Error:", e);
                ByteArrayOutputStream bs = new ByteArrayOutputStream();
                PrintStream ps = new PrintStream(bs);
                try {
                    e.printStackTrace(ps);
                    String content = "自动补货异常：" + bs.toString();
                    AlarmUtil.sendEmail("自动补货失败", content);
                } catch (Exception e1) {
                    log.error("Send mail error: ", e1);
                } finally {
                    ps.close();
                }
            }

        }
    }
}