package com.daxia.wms.delivery.recheck.action;

import com.daxia.dubhe.api.internal.util.NumberUtils;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;

import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.cfg.AutoLoadAndDeliverCfg;
import com.daxia.framework.common.log.LogUtil;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.service.impl.CacheService;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.OrderFrozenException;
import com.daxia.wms.delivery.OrderNotInReCheckStatusException;
import com.daxia.wms.delivery.crossorder.entity.CrossSeedDetail;
import com.daxia.wms.delivery.crossorder.service.CrossSeedService;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.print.service.carton.PrintCartonDispatcher;
import com.daxia.wms.delivery.recheck.dao.CartonDetailDAO;
import com.daxia.wms.delivery.recheck.dto.CrossReCheckCartonInfo;
import com.daxia.wms.delivery.recheck.dto.CrossReCheckRecord;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.filter.CartonHeaderFilter;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.recheck.service.ReCheckService;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.master.dto.AutoCompleteDTO;
import com.daxia.wms.master.entity.Carrier;
import com.daxia.wms.master.entity.ShopInfo;
import com.daxia.wms.master.entity.Sku;
import com.daxia.wms.master.service.SkuService;
import com.daxia.wms.print.dto.PrintData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.*;
import org.jboss.seam.annotations.security.Restrict;
import org.jboss.seam.log.Log;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * 对核拣装箱进行操作
 */
@Name("com.daxia.wms.delivery.crossReCheckAction")
@Restrict("#{identity.hasPermission('delivery.cross.recheck')}")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class CrossReCheckAction implements Serializable {

    @In
    CrossSeedService crossSeedService;
    @In
    DeliveryOrderService deliveryOrderService;
    @In
    CartonDetailDAO cartonDetailDAO;
    @In
    ReCheckService reCheckService;
    @In
    CartonService cartonService;
    @In
    ExpFacadeService expFacadeService;
    @In
    PrintCartonDispatcher printCartonDispatcher;

    private PrintData wayBillPrintData = new PrintData();
    private String printContent = "";

    private Long orderId;
    /**
     * 当前操作的发货单号
     */
    private String doNo = "";


    private String orderStatus;

    private String sortGridNo;

    /**
     * 确认装箱操作码
     */
    private String packConfirmCode;

    /**
     * 已完成核拣的箱数
     */
    private int finishedNo;
    /**
     * 已完成核减的箱号
     */
    private List<String> finishedCartonNoList;

    /**
     * 容器列表
     */
    private List<String> containerList;

    /**
     * 当前订单所有的商品的核拣记录，包括已核拣了多少，未核拣多少，及对应的商品信息
     */
    private List<CrossReCheckRecord> reCheckRecords;

    /**
     * 操作失败时，与do有关的错误信息
     */
    private String doErrorMessage;

    /**
     * 核拣某一商品失败时，与该商品有关的错误信息
     */
    private String reCheckErrorMessage;

    /**
     * 在核拣过程中，用户删除的已完成装箱的Id号，Id号为-1时表示为还没有存储的装箱数据
     */
    private Long clearedCartonId;

    /**
     * 要核拣过程中，用户删除的某一个商品的Id
     */
    private Long clearedProductId;

    private CrossReCheckCartonInfo currentReCheckCartonInfo;

    /**
     * 正在操作（增加或删除）的核拣记录，格式为： 1含序列号 ：skuId1/number1/12,232;skuId2/number2/12,212
     * 2不含序列号：skuId1/number1;skuId2/number2
     */
    private String operateCartonInfo;

    private boolean contentShow = false;

    private boolean dataError = false;

    private String containerNo;

    private Integer isLackRecheck = YesNo.NO.getValue();

    private BigDecimal totalSkuQty;// 按商品总数核拣 商品总数

    @Logger
    protected Log log;

    private CartonHeaderFilter cartonHeaderFilter;

    /**
     * 按商品核拣一码多品选择
     */
    private List<Sku> skuList = new ArrayList<Sku>();
    private String carrierName;

    private String shopName;

    private String recheckBy;

    private String noticeContent;

    private String productBarCode;

    private String skuHelperData;

    private String currentCartonNo;

    private String notesJson;

    private String checkByBtn = "1";

    private String consigneeName;

    Map<String, String> noteMap = new HashMap<String, String>();

    @In
    CacheService cacheService;
    @In
    SkuService skuService;

    public CrossReCheckAction() {
        cartonHeaderFilter = new CartonHeaderFilter();
    }

    /**
     * 初始化参数，获取系统当前发票的模式
     */
    @Create
    public void init() {
        String autoNode = Config.getFmJson(Keys.Delivery.auto_load_and_deliver_cfg, Config.ConfigLevel.WAREHOUSE, AutoLoadAndDeliverCfg.type);
        packConfirmCode = SystemConfig.getConfigValue("recheck.pack.confirm.code", ParamUtil.getCurrentWarehouseId());
    }


    /**
     * 获取当前核拣产品的错误信息
     *
     * @return 当前核拣产品的错误信息
     */
    public String getReCheckErrorMessage() {
        return reCheckErrorMessage;
    }

    /**
     * 重置参数
     */
    private void reset() {
        reCheckErrorMessage = "";
        doErrorMessage = "";
        sortGridNo = "";
        orderStatus = "";
        currentReCheckCartonInfo = null;
        reCheckRecords = null;
        operateCartonInfo = "";
        finishedNo = 0;
        finishedCartonNoList = null;
        containerList = null;
        contentShow = false;
        dataError = false;
        skuList.clear();
        carrierName = "";
        shopName = "";
        this.noticeContent = "";
        currentCartonNo = null;
        recheckBy = "";
    }

    /**
     * 根据Do号查询发货单核拣
     *
     * @throws Exception
     */
    public void startCheckByContainerNo() throws Exception {
        reset();
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderByContainerNo(containerNo);
        if(null ==doHeader){
            doErrorMessage = "cross.recheck.containerNo.notExist";
            return;
        }
        if(!Constants.DoStatus.ALLSORTED.getValue().equals(doHeader.getStatus()) &&
            !Constants.DoStatus.PART_CARTON.getValue().equals(doHeader.getStatus())){
            doErrorMessage = "recheck.invalid.do_status";
            return;
        }
        List<CrossReCheckRecord> reCheckRecords = new ArrayList<CrossReCheckRecord>();
        List<CrossSeedDetail> crossDetails = crossSeedService.getCrossDetailsBycontainerNo(containerNo);
        for (CrossSeedDetail crossDetail : crossDetails) {
            CrossReCheckRecord crossReCheckRecord = new CrossReCheckRecord();
            crossReCheckRecord.setDoNo(crossDetail.getDoNo());//订单号
            crossReCheckRecord.setContainerNo(crossDetail.getContainerNo());//容器号
            crossReCheckRecord.setConsigneeName(crossDetail.getConsigneeName());//客户名
            crossReCheckRecord.setProductCode(crossDetail.getSku().getProductCode());//商品编码
            crossReCheckRecord.setProductBarCode(crossDetail.getSku().getEan13());//商品条码
            crossReCheckRecord.setProductName(crossDetail.getSku().getProductCname());//商品名称
            crossReCheckRecord.setUdf4(crossDetail.getSku().getUdf4());//箱规
            crossReCheckRecord.setCrossDetailId(crossDetail.getId());//分播单明细id
            crossReCheckRecord.setBatchNo(crossDetail.getLotatt05());//批号
            crossReCheckRecord.setEffectiveDate(crossDetail.getLotatt01());//生效日期
            crossReCheckRecord.setExpirationDate(crossDetail.getLotatt02());//失效日期
            crossReCheckRecord.setNeedCheckNumber(crossDetail.getSortedQty());
            crossReCheckRecord.setFinishedCheckNumber(crossDetail.getPackedQty() == null ? BigDecimal.ZERO : crossDetail.getPackedQty());
            reCheckRecords.add(crossReCheckRecord);
        }
        this.reCheckRecords = reCheckRecords;
        setNomalInfo(doHeader);

    }


    private void setNomalInfo(DeliveryOrderHeader doHeader) {
        this.sortGridNo = doHeader.getSortGridNo();
        Carrier carrier = doHeader.getCarrier();
        ShopInfo shopInfo = doHeader.getShopInfo();

        carrierName = carrier == null ? "" : carrier.getDistSuppCompName();
        shopName = shopInfo == null ? "" : StringUtil.notNullString(shopInfo.getNickName(), shopInfo.getName());
        consigneeName = doHeader.getConsigneeName();
        doNo = doHeader.getDoNo();
        orderId = doHeader.getId();
        this.orderStatus = doHeader.getStatus();
        // 获取已核拣箱数
        finishedCartonNoList = reCheckService.findCartonNoByDoId(doHeader.getId(), Constants.PackageType.B.getValue());
        finishedNo = finishedCartonNoList.size();

        remarkPackBy(doHeader);
    }

    /**
     * 记录发货单的核拣人
     *
     * @param doHeader
     */
    private void remarkPackBy(DeliveryOrderHeader doHeader) {
        if (StringUtil.isEmpty(doHeader.getPackedBy())) {
            doHeader.setPackedBy(ParamUtil.getCurrentLoginName());
            deliveryOrderService.updateDoHeader(doHeader);
        }
    }


    /**
     * 客户端调用此方法创建新的箱号
     */
    public void createCartonNumber() {
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(orderId);
        if (doHeader == null) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        currentReCheckCartonInfo = createCarton();
    }

    /**
     * 创新装箱，并将其进行缓存
     *
     * @return
     */
    private CrossReCheckCartonInfo createCarton() {
        DeliveryOrderHeader doHeader = new DeliveryOrderHeader();
        doHeader.setId(orderId);
        CrossReCheckCartonInfo currentReCheckCartonInfo = new CrossReCheckCartonInfo(doHeader);
        currentReCheckCartonInfo.setDoNo(doNo);
        return currentReCheckCartonInfo;
    }

    /**
     * 完成当前箱的包装，需要将当前箱的数据进行存储，这里还需要检查订单是否在操作期间被冻结
     */
    @Loggable
    public void completeCurrentPack() {
        doErrorMessage = "";
        if (currentReCheckCartonInfo == null || StringUtil.isBlank(operateCartonInfo)) {
            //空箱时的缺发发货
            if (isLackRecheck.equals(YesNo.YES.getValue())) {
                List<CartonHeader> cartonHeaders = cartonService.findByDoNo(doNo);
                if (CollectionUtils.isEmpty(cartonHeaders)) {
                    throw new DeliveryException(DeliveryException.DO_HAVE_NO_CARTON, doNo);
                }
                DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(doNo);
                reCheckService.resetCrossDoStatus(doHeader,isLackRecheck);

                DeliveryOrderHeader doHeaderNew = deliveryOrderService.findDoHeaderByDoNo(doNo);
                orderStatus = doHeaderNew.getStatus();

                // 核检装箱完成
                if (orderStatus.equals(Constants.DoStatus.ALL_CARTON.getValue())) {
                    // 调用接口回写数据
                    expFacadeService.sendDo2Oms(orderId, orderStatus, 1, null, doHeaderNew.getDoType());
                }
                finishedCartonNoList = reCheckService.findCartonNoByDoId(doHeaderNew.getId(), Constants.PackageType.B.getValue());
                finishedNo = finishedCartonNoList.size();//已完成核拣的箱数
                return;
            }
            return;
            //越库复核不允许空箱发货
//            doErrorMessage = "cross.recheck.empty.cartonfail";
//            return;
        }

        Map<Long, BigDecimal> detailMap = new HashMap<Long, BigDecimal>();
        String[] infos = operateCartonInfo.split(";");
        for (String info : infos) {
            String pair[] = info.split("/");
            detailMap.put(Long.valueOf(pair[0]),NumberUtils.object2BigDecimal(pair[1]));
        }
        // 对核减记录按未核减数降序排序
//        reCheckService.sortRecordsByNotPackedQty(reCheckRecords);

        String cartonNo = null;
        try {
            cartonNo = reCheckService.createCrossCarton(doNo,detailMap,isLackRecheck);
        } catch (OrderFrozenException e) {
            doErrorMessage = "recheck.do.forzen.cartonfail";
            return;
        } catch (OrderNotInReCheckStatusException e) {
            orderStatus = e.getStatus();
            doErrorMessage = "recheck.invalid.do_status";
            return;
        } catch (BusinessException e) {
            if (DeliveryException.RECHECK_LOGIN_INFO_EXCEPTION.equals(e.getMessage())) {
                throw new DeliveryException(DeliveryException.RECHECK_LOGIN_INFO_EXCEPTION, e.getParams());
            }
            log.error(e.getMessage(), e);

            doErrorMessage = ResourceUtils.getDispalyString(e.getMessage(), e.getClass().getName(), e.getParams());
            if (StringUtil.isBlank(doErrorMessage)) {
                doErrorMessage = e.getMessage();
            }
            return;
        } catch (RuntimeException e) {
            log.error(e.getMessage(), e);
            throw e;
        }
        DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(doNo);
        orderStatus = doHeader.getStatus();
        log.debug("completeCurrentPack: doHeaderId = " + doHeader.getId());

        // 核检装箱完成
        if (orderStatus.equals(Constants.DoStatus.ALL_CARTON.getValue())) {
            // 调用接口回写数据
            expFacadeService.sendDo2Oms(orderId, orderStatus, 1, null, doHeader.getDoType());
        }
        finishedCartonNoList = reCheckService.findCartonNoByDoId(doHeader.getId(), Constants.PackageType.B.getValue());
        finishedNo = finishedCartonNoList.size();//已完成核拣的箱数
//        this.currentCartonNo = currentReCheckCartonInfo.getCartonNo();
    }

    public void printCarton1By1() {
        clearPrintData();
        List<Long> cartonIds = new ArrayList<Long>();
        for (String cartonNo : finishedCartonNoList) {
            cartonIds.add(cartonService.getCartonByNo(cartonNo).getId());
        }

        this.wayBillPrintData = printCartonDispatcher.print(orderId,cartonIds);
        if (wayBillPrintData != null) {
            printContent = wayBillPrintData.getTemplateJs();
        }
    }

    /**
     * 清空箱标签打印数据
     */
    public void cleareCartonPrintData() {
        printContent = "";
        if (wayBillPrintData != null) {
            wayBillPrintData.clear();
        }
    }

    /**
     * 清空打印数据
     */
    public void clearPrintData() {
        printContent = "";
        if (wayBillPrintData != null) {
            wayBillPrintData.clear();
        }
    }

    /**
     * 给字符串加双引号
     *
     * @param s
     * @return
     */
    public String jsonString(String s) {
        if (s == null || s.length() == 0) {
            return "\"\"";
        }
        s = StringEscapeUtils.escapeJavaScript(s);
        return "\"" + s + "\"";
    }

    /**
     * 获取装箱信息
     *
     * @return
     */
    private Map<Long, ClientCartonRecord> getNewCartonInfo() {
        if (operateCartonInfo == null || operateCartonInfo.length() == 0) {
            return Collections.emptyMap();
        }
        Map<Long, ClientCartonRecord> result = new HashMap<Long, ClientCartonRecord>();
        String[] skuInfos = operateCartonInfo.split(";");
        for (String skuInfo : skuInfos) {
            ClientCartonRecord c = ClientCartonRecord.parse(skuInfo);
            result.put(c.getId(), c);
        }
        return result;
    }

    public String getOperateCartonInfo() {
        return operateCartonInfo;
    }

    /**
     * 设置当前正在操作的装箱信息。格式为：skuId/number;skuId/number。多个sku间用";"分隔，
     * 每个sku的装箱信息由sku的id和其在该箱中的数量两部分组成
     *
     * @param operateCartonInfo
     */
    public void setOperateCartonInfo(String operateCartonInfo) {
        this.operateCartonInfo = operateCartonInfo;
    }





    /**
     * 封装客户端发送来的核拣记录
     */
    private static class ClientCartonRecord {

        private Long id;

        private BigDecimal number;

        static ClientCartonRecord parse(String info) {
            ClientCartonRecord instance = new ClientCartonRecord();
            String pair[] = info.split("/");
            instance.id = Long.valueOf(pair[0]);
            instance.number = NumberUtils.object2BigDecimal(pair[1]);
            return instance;
        }

        public Long getId() {
            return id;
        }

        public BigDecimal getNumber() {
            return number;
        }
    }

    private void loadSkuHelperData(Long doId) {
        List<AutoCompleteDTO> autoCompleteDTOS = deliveryOrderService.findSkuHelperData(doId);
        this.skuHelperData = JsonUtil.objToString(autoCompleteDTOS);
    }

    public String getSkuHelperData() {
        return skuHelperData;
    }

    public void setSkuHelperData(String skuHelperData) {
        this.skuHelperData = skuHelperData;
    }


    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    public void setContentShow(boolean contentShow) {
        this.contentShow = contentShow;
    }

    public boolean getContentShow() {
        return this.contentShow;
    }

    public void setDataError(boolean dataError) {
        this.dataError = dataError;
    }

    public boolean getDataError() {
        return this.dataError;
    }

    public String getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }

    public BigDecimal getTotalSkuQty() {
        return totalSkuQty;
    }

    public void setTotalSkuQty(BigDecimal totalSkuQty) {
        this.totalSkuQty = totalSkuQty;
    }

    public List<Sku> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<Sku> skuList) {
        this.skuList = skuList;
    }

    public String getCarrierName() {
        return carrierName;
    }

    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public int getFinishedNo() {
        return finishedNo;
    }

    public void setFinishedNo(int finishedNo) {
        this.finishedNo = finishedNo;
    }

    public Long getClearedCartonId() {
        return clearedCartonId;
    }

    public void setClearedCartonId(Long clearedCartonId) {
        this.clearedCartonId = clearedCartonId;
    }

    public Long getClearedProductId() {
        return clearedProductId;
    }

    public void setClearedProductId(Long clearedProductId) {
        this.clearedProductId = clearedProductId;
    }

    public List<CrossReCheckRecord> getReCheckRecords() {
        return reCheckRecords;
    }

    public void setReCheckRecords(List<CrossReCheckRecord> reCheckRecords) {
        this.reCheckRecords = reCheckRecords;
    }

    public String getDoErrorMessage() {
        return doErrorMessage;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getRecheckBy() {
        return recheckBy;
    }

    public void setRecheckBy(String recheckBy) {
        this.recheckBy = recheckBy;
    }

    public String getNoticeContent() {
        return noticeContent;
    }

    public void setNoticeContent(String noticeContent) {
        this.noticeContent = noticeContent;
    }

    public String getProductBarCode() {
        return productBarCode;
    }

    public void setProductBarCode(String productBarCode) {
        this.productBarCode = productBarCode;
    }

    public String getPackConfirmCode() {
        return packConfirmCode;
    }

    public void setPackConfirmCode(String packConfirmCode) {
        this.packConfirmCode = packConfirmCode;
    }

    public String getCurrentCartonNo() {
        return currentCartonNo;
    }

    public void setCurrentCartonNo(String currentCartonNo) {
        this.currentCartonNo = currentCartonNo;
    }

    public List<String> getFinishedCartonNoList() {
        return finishedCartonNoList;
    }

    public void setFinishedCartonNoList(List<String> finishedCartonNoList) {
        this.finishedCartonNoList = finishedCartonNoList;
    }

    public List<String> getContainerList() {
        return containerList;
    }

    public void setContainerList(List<String> containerList) {
        this.containerList = containerList;
    }

    public Map<String, String> getNoteMap() {
        return noteMap;
    }

    public void setNoteMap(Map<String, String> noteMap) {
        this.noteMap = noteMap;
    }

    public String getNotesJson() {
        return notesJson;
    }

    public void setNotesJson(String notesJson) {
        this.notesJson = notesJson;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public String getCheckByBtn() {
        return checkByBtn;
    }

    public void setCheckByBtn(String checkByBtn) {
        this.checkByBtn = checkByBtn;
    }

    public Integer getIsLackRecheck() {
        return isLackRecheck;
    }

    public void setIsLackRecheck(Integer isLackRecheck) {
        this.isLackRecheck = isLackRecheck;
    }

    public PrintData getWayBillPrintData() {
        return wayBillPrintData;
    }

    public void setWayBillPrintData(PrintData wayBillPrintData) {
        this.wayBillPrintData = wayBillPrintData;
    }

    public String getPrintContent() {
        return printContent;
    }

    public void setPrintContent(String printContent) {
        this.printContent = printContent;
    }
}