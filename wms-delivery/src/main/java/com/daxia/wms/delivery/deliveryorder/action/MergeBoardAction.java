package com.daxia.wms.delivery.deliveryorder.action;


import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.store.service.GoodsStoreService;
import com.daxia.wms.master.dto.MergeBoardDTO;
import com.daxia.wms.master.entity.MergePartition;
import com.daxia.wms.master.filter.MergeBoardFilter;
import com.daxia.wms.master.service.MergeLocService;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;


/**
 * 集货看板
 */
@Name("com.daxia.wms.delivery.mergeBoardAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class MergeBoardAction extends PagedListBean<MergeBoardDTO> {

    private static final long serialVersionUID = -3634807178853785342L;

    private MergeBoardFilter mergeBoardFilter;

    @In
    private GoodsStoreService goodsStoreService;

    private List<SelectItem> selectItemList;

    @In
    private MergeLocService mergeLocService;

    public MergeBoardAction() {
        super();
        this.mergeBoardFilter = new MergeBoardFilter();
    }

    @Create
    public void initialize() {
        selectItemList = new ArrayList<SelectItem>();
        List<MergePartition> mergePartitions = mergeLocService.findMergePartitions();
        for (MergePartition mergeLoc : mergePartitions) {
            SelectItem item = new SelectItem();
            item.setValue(mergeLoc.getId());
            item.setLabel(mergeLoc.getPartitionCode());
            selectItemList.add(item);
        }
    }

    @Override
    public void query() {
        this.buildOrderFilterMap(mergeBoardFilter);
        mergeBoardFilter.setDocType(Integer.valueOf(3));
        DataPage<MergeBoardDTO> dataPage = goodsStoreService.queryBoard(mergeBoardFilter, getStartIndex(), getPageSize());
        populateValues(dataPage);
    }


    /**
     * 点击查询按钮
     */
    @Override
    public void buttonQuery() {
        this.dataPage.setPageIndex(-1);
        this.query();
    }

    public MergeBoardFilter getMergeBoardFilter() {
        return mergeBoardFilter;
    }

    public void setMergeBoardFilter(MergeBoardFilter orderLogFilter) {
        this.mergeBoardFilter = orderLogFilter;
    }


    public List<SelectItem> getSelectItemList() {
        return selectItemList;
    }

    public void setSelectItemList(List<SelectItem> selectItemList) {
        this.selectItemList = selectItemList;
    }
}
