package com.daxia.wms.delivery.invoice.dto;

import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "invoice")
@lombok.extern.slf4j.Slf4j
public class Invoice4JinrenDTO {

    // 单据编号（订单号）
    private String docNo;

    // 发票编号
    private String invoiceNo;

    // 抬头
    private String title;

    // 商品名称
    private String productName;

    // 规格
    private String skuType;

    // 单位
    private String unit;

    private String qty;

    // 税率
    private String taxRate;

    // 创建时间
    private String createTime;

    // 含税单价
    private String price;

    // 含税金额
    private String amount;

    // 备注
    private String remarks;

    // 备用字段1
    private String diy1;

    // 备用字段2
    private String diy2;

    // 备用字段3
    private String diy3;
    
    private String waveNo;

	private String taxCategoryCode;

	private String buyerTaxNo;

	public String getDocNo() {
		return docNo;
	}

	public void setDocNo(String docNo) {
		this.docNo = docNo;
	}

	public String getInvoiceNo() {
		return invoiceNo;
	}

	public void setInvoiceNo(String invoiceNo) {
		this.invoiceNo = invoiceNo;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getSkuType() {
		return skuType;
	}

	public void setSkuType(String skuType) {
		this.skuType = skuType;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public String getQty() {
		return qty;
	}

	public void setQty(String qty) {
		this.qty = qty;
	}

	public String getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(String taxRate) {
		this.taxRate = taxRate;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getDiy1() {
		return diy1;
	}

	public void setDiy1(String diy1) {
		this.diy1 = diy1;
	}

	public String getDiy2() {
		return diy2;
	}

	public void setDiy2(String diy2) {
		this.diy2 = diy2;
	}

	public String getDiy3() {
		return diy3;
	}

	public void setDiy3(String diy3) {
		this.diy3 = diy3;
	}

	public String getWaveNo() {
		return waveNo;
	}

	public void setWaveNo(String waveNo) {
		this.waveNo = waveNo;
	}

	public String getTaxCategoryCode() {
		return taxCategoryCode;
	}

	public void setTaxCategoryCode(String taxCategoryCode) {
		this.taxCategoryCode = taxCategoryCode;
	}

	public String getBuyerTaxNo() {
		return buyerTaxNo;
	}

	public void setBuyerTaxNo(String buyerTaxNo) {
		this.buyerTaxNo = buyerTaxNo;
	}
}