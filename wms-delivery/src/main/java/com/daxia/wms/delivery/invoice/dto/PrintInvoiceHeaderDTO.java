package com.daxia.wms.delivery.invoice.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@lombok.extern.slf4j.Slf4j
public class PrintInvoiceHeaderDTO {
    //收款单位
    private String payee;

    //抬头，付款单位
    private String title;

    private Date printDate;

    private BigDecimal amount;

    private String amountRMB;

    private String doNo;

    private String sortGridNo;

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    private List<PrintInvoiceDetailDTO> detailList;

    public String getPayee() {
        return payee;
    }

    public void setPayee(String payee) {
        this.payee = payee;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Date getPrintDate() {
        return printDate;
    }

    public void setPrintDate(Date printDate) {
        this.printDate = printDate;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getAmountRMB() {
        return amountRMB;
    }

    public void setAmountRMB(String amountRMB) {
        this.amountRMB = amountRMB;
    }

    public List<PrintInvoiceDetailDTO> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<PrintInvoiceDetailDTO> detailList) {
        this.detailList = detailList;
    }
}
