package com.daxia.wms.delivery.print.dto;

import com.daxia.framework.common.util.AppConfig;
import com.daxia.wms.delivery.util.I18nUtil;

import java.sql.Timestamp;
import java.util.List;

/**
 * 打印拣货单
 */
@lombok.extern.slf4j.Slf4j
public class PickPrint {
    //波次号
    private String waveNo;

    //拣货单号
    private String pktNo;

    //库区
    private String partitionCode;

    //拣货单当前份数
    private Integer pktIndex;

    //波次拣货单份数
    private Integer pktCount;

    //创建时间
    private Timestamp createTime;
    
    /**
     * 区域code
     */
    private String regionCode;

    private String customerName;

    private String remark;

    private String doNo;

    private String address;

    private String phone;

    private String buyerRemark;
    /**
     * 配送商
     */
    private String carrierShortName;
    
    public String getWaveType() {
        return waveType;
    }

    private String waveType;
    /**
     * 渠道名称
     */
    private String channelName;

    private List<PickPrintSub> pktPrintSubs;

    private String groupRuleDesc;

    private Integer docCount;

    private String notes;
    /**
     * 每单商品数量
     */
    private Integer perDocSkuCount;

    /**
     * 订单类型
     */
    private String docType;
    /**
     * 体积类型
     */
    private String volumeType;
    /**
     * 打印购物清单标记
     */
    private Integer  doNeedPrint;
    public Integer getDoNeedPrint() {
        return doNeedPrint;
    }

    public void setDoNeedPrint(Integer doNeedPrint) {
        this.doNeedPrint = doNeedPrint;
    }
    public String getDocType() {
        return docType;
    }

    public void setDocType(String docType) {
        if (!AppConfig.useEnglish()){
            this.docType = docType;
        } else {
            this.docType = I18nUtil.getValue(docType);
        }
    }

    public Integer getPerDocSkuCount() {
        return perDocSkuCount;
    }

    public void setPerDocSkuCount(Integer perDocSkuCount) {
        this.perDocSkuCount = perDocSkuCount;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public String getPktNo() {
        return pktNo;
    }

    public void setPktNo(String pktNo) {
        this.pktNo = pktNo;
    }

    public String getPartitionCode() {
        return partitionCode;
    }

    public void setPartitionCode(String partitionCode) {
        this.partitionCode = partitionCode;
    }

    public Integer getPktIndex() {
        return pktIndex;
    }

    public void setPktIndex(Integer pktIndex) {
        this.pktIndex = pktIndex;
    }

    public Integer getPktCount() {
        return pktCount;
    }

    public void setPktCount(Integer pktCount) {
        this.pktCount = pktCount;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public void setPktPrintSubs(List<PickPrintSub> pktPrintSubs) {
        this.pktPrintSubs = pktPrintSubs;
    }

    public List<PickPrintSub> getPktPrintSubs() {
        return pktPrintSubs;
    }

    public void setWaveType(String waveType) {
        if (!AppConfig.useEnglish()){
            this.waveType = waveType;
        } else {
            this.waveType = I18nUtil.getValue(waveType);
        }
    }
    
    public String getRegionCode() {
        return regionCode;
    }
    
    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getBuyerRemark() {
        return buyerRemark;
    }

    public void setBuyerRemark(String buyerRemark) {
        this.buyerRemark = buyerRemark;
    }

    public String getGroupRuleDesc() {
        return groupRuleDesc;
    }

    public void setGroupRuleDesc(String groupRuleDesc) {
        this.groupRuleDesc = groupRuleDesc;
    }

    public Integer getDocCount() {
        return docCount;
    }

    public void setDocCount(Integer docCount) {
        this.docCount = docCount;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getCarrierShortName() {
        return carrierShortName;
    }

    public void setCarrierShortName(String carrierShortName) {
        this.carrierShortName = carrierShortName;
    }

    public String getVolumeType() {
        return volumeType;
    }

    public void setVolumeType(String volumeType) {
        if (!AppConfig.useEnglish()){
            this.volumeType = volumeType;
        } else {
            this.volumeType = I18nUtil.getValue(volumeType);
        }

    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }
}
