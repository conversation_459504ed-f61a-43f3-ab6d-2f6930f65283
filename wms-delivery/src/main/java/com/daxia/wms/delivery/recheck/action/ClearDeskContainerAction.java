package com.daxia.wms.delivery.recheck.action;

import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.wms.master.entity.PackingDeskSortLog;
import com.daxia.wms.master.service.PackingDeskSortLogService;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.io.Serializable;
import java.util.List;

@Name("com.daxia.wms.delivery.clearDeskContainerAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ClearDeskContainerAction implements Serializable {
    private static final long serialVersionUID = 1L;

    private String packingDeskNo;

    private Long deskLogId;

    /**
     * 包装台对应的已分配容器明细
     */
    private List<PackingDeskSortLog> resultList;

    @In
    private PackingDeskSortLogService packingDeskSortLogService;

    /**
     * 操作过程中的错误信息
     */
    private String errorMessage;


    /**
     * 调用此方法查询Carton
     */
    public void selectContainerInfo() {
        resultList = null;
        if (packingDeskNo == null) {
            return;
        }
        packingDeskNo = packingDeskNo.trim();
        resultList = packingDeskSortLogService.getByDeskNo(packingDeskNo);
        if (CollectionUtils.isEmpty(resultList)) {
            errorMessage = "recheck.carton.notexist";
            return;
        }
        errorMessage = "";
    }

    public void removeContainer() {
        if (deskLogId == null) {
            return;
        }
        packingDeskSortLogService.removeLog(deskLogId);
        selectContainerInfo();
    }

    public String getPackingDeskNo() {
        return packingDeskNo;
    }

    public void setPackingDeskNo(String packingDeskNo) {
        this.packingDeskNo = packingDeskNo;
    }

    public Long getDeskLogId() {
        return deskLogId;
    }

    public void setDeskLogId(Long deskLogId) {
        this.deskLogId = deskLogId;
    }

    public List<PackingDeskSortLog> getResultList() {
        return resultList;
    }

    public void setResultList(List<PackingDeskSortLog> resultList) {
        this.resultList = resultList;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
