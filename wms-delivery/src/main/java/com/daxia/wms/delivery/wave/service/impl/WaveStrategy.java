package com.daxia.wms.delivery.wave.service.impl;


import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.log.LogUtil;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.security.ExtendedIdentity;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants.*;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateQueryService;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateService;
import com.daxia.wms.delivery.deliveryorder.service.DoWaveExService;
import com.daxia.wms.delivery.pick.service.PickTaskService;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.delivery.wave.dto.WaveGeneContextDTO;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveGenerater;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.master.service.SortingBinService;
import com.google.common.collect.Maps;
import org.jboss.seam.annotations.In;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 波次生成策略抽象类
 */
@lombok.extern.slf4j.Slf4j
public abstract class WaveStrategy {
	
	@In
	protected DoHeaderDAO doHeaderDAO;
	@In
	protected DeliveryOrderService deliveryOrderService;
    @In
    protected WaveGenerater waveGenerater;
    @In
    protected SortingBinService sortingBinService;

    @In
    private ExtendedIdentity identity;
    @In
    protected PickTaskService pickTaskService;
    @In
    private DoWaveExService doWaveExService;
    @In
    private ExpFacadeService expFacadeService;
    @In
    protected DoAllocateService doAllocateService;
    @In
    private DoAllocateQueryService doAllocateQueryService;
    
    @SuppressWarnings("unchecked")
    protected WaveHeader generate(List<DeliveryOrderHeader> doList, List<Long> doIdList, List<Long> regionIds,
                          Long sortingBinId, String waveType, Integer wavePriority, Boolean isRecommend, AutoWaveType autoWaveType, Boolean
            isSemiAuto, Long ruleDetailId, Long criteriaDetailId) throws Exception {
        return waveGenerater.generateWave(doList, doIdList, regionIds, sortingBinId, waveType, wavePriority, isRecommend, autoWaveType, isSemiAuto, ruleDetailId, criteriaDetailId);
    }
    
    /**
     * 先验证是否能生成波次，再按照订单体积从大到小排序，再生成波次
     * @param doIdList 发货单Id List
     * @param maxDOQty 波次下最大发货单数
     * @param wavePriority 波次优先级别
     * @param isRecommend 
     * @param isSemiAuto 
     * @param isSemiAuto 是否自動波次
     * @return 生成波次的波次号
     * @throws Exception
     */
    @Loggable
    protected String generateWave(List<Long> doIdList, Long maxDOQty, Integer wavePriority, Boolean isRecommend, AutoWaveType autoWaveType, Boolean isSemiAuto, Long ruleDetailId, Long criteriaDetailId) throws Exception {
        List<DeliveryOrderHeader> doList = doHeaderDAO.findDoHeadersByIds(doIdList,true);
    	WaveHeader waveHeader = null;
    	
    	if(validationGenWave(doList, maxDOQty)){
        	//订单排序，用来依次分配分拣格号
    	    sortDoHeader(doList);
        	
    		List<Long> regionIds = pickTaskService.findRegionId(doIdList);

    		if(CollectionUtils.isEmpty(regionIds)){
    		    throw new DeliveryException(DeliveryException.PICK_PARTITION_REGION_ERROR);
            }
    		autoWaveType = getAutoWaveType(autoWaveType, doList);
    		
            // 分配分拣柜
            Long sortingBinId = sortingBinService.assignSortBin(doList.get(0).getDoType(), false, autoWaveType.getValue());
			
    		//如果没取到任何分拣柜, 则抛出异常, 提示没有分拣柜
    		if (sortingBinId == null) {
    			throw new DeliveryException(DeliveryException.NO_SORT_BIN);
    		}
    		
    		String waveType = getWaveTypeByDoType(doList.get(0));
        
            String wavePriorityScrit = Config.get(Keys.Delivery.wave_priorityScrit, Config.ConfigLevel.WAREHOUSE);
            if(StringUtil.isNotEmpty(wavePriorityScrit)){
                wavePriority = genWavePriority(doList, wavePriorityScrit);
            }
    		
    		// 生成波次
            waveHeader = generate(doList, doIdList, regionIds, sortingBinId, waveType, wavePriority, isRecommend, autoWaveType, isSemiAuto, ruleDetailId, criteriaDetailId);
    		
    		return waveHeader.getWaveNo();
    	}
        return null;    
    }
    
    protected Integer genWavePriority(List<DeliveryOrderHeader> doList, String wavePriorityScrit) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("doList", doList);
        return (Integer) MvelUtil.eval(wavePriorityScrit, params);
    }
    
    // 按是否有发票（有发票在前），体积大小（体积大在前）排序（分配分拣格用）
    protected void sortDoHeader(List<DeliveryOrderHeader> doList) {
        Collections.sort(doList, new Comparator<DeliveryOrderHeader>() {

            @Override
            public int compare(DeliveryOrderHeader o1, DeliveryOrderHeader o2) {

                int invoiceCompreResult = CompareUtil.compare(o2.getInvoiceFlag(), o1.getInvoiceFlag(), false);

                if (invoiceCompreResult == 0) {
                    return CompareUtil.compare(o2.getVolume(), o1.getVolume(), false);
                }

                return invoiceCompreResult;
            }
        });
    }
    
    /**
     * 调用回写OST、OMS接口
     * @param doHeader 发货单
     */
    protected void callInterfaceAfterGenerateWave(DeliveryOrderHeader doHeader) {
        log.debug("CallInterfaceAfterGenerateWave: [doHeaderId: {}]", doHeader.getId());
        
//        expFacadeService.sendDo2Oms(doHeader.getId(),doHeader.getStatus(),doHeader.getWaveFlag(),null,doHeader.getDoType());
//        expFacadeService.send2Ost(doHeader.getId(), OstOrderStatus.OST_DO_PRINTED, identity.getCredentials().getUsername());
    }

    /**
     * 验证是否可以生成波次
     * @param doList 发货单 List
     * @param maxDOQty 波次下最大发货单数
     * @return 是否可用生成波次
     */
    protected boolean validationGenWave(List<DeliveryOrderHeader> doList, Long maxDOQty){
		if (doList.size() > maxDOQty) {
			throw new DeliveryException(DeliveryException.DO_COUNTS_OVERLOAD);
		}
		// 校验选中的发货单记录的状态
		validateDoHeaderListStatus(doList);
		
    	return true;
    }
    
    /**
     * 校验选中的发运单记录的状态,已跑波次、非分配完成状态或者为冻结状态的发运单不能生成波次
     * @param dataList 选中的发运单记录集合
     * @throws DeliveryException 有冻结状态或者非分配完成状态的记录时抛出的异常
     */
    protected void validateDoHeaderListStatus(List<DeliveryOrderHeader> dataList) throws DeliveryException {
        //String preCycleNo = null;
//        int aloneDoCount = 0;//计数器：记录必须单独波次订单数量（合约机订单、实名制网卡订单、选号入网订单）

        if (ListUtil.isNullOrEmpty(dataList) || null == dataList.get(0)) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }

        // 校验是否允许生成波次
        checkMixConfig(dataList);

        for (DeliveryOrderHeader doHeader : dataList) {
            //如果定单已跑波次,则抛出异常
            if (doHeader.getWaveFlag().intValue() == 1 || doHeader.getWaveId() != null) {
                log.error(ResourceUtils.getDispalyString(DeliveryException.DO_ALREADY_IN_WAVE, DeliveryException.class.getSimpleName(), doHeader.getDoNo()));
                throw new DeliveryException(DeliveryException.DO_ALREADY_IN_WAVE, doHeader.getDoNo());
            }

            //如果订单已冻结或者没有分配完成, 则抛出异常
            boolean isRelease = ReleaseStatus.RELEASE.getValue().equals(doHeader.getReleaseStatus());
            boolean isCompleteAssign = DoStatus.ALLALLOCATED.getValue().equals(doHeader.getStatus());
            if (!(isRelease && isCompleteAssign)) {
                throw new DeliveryException(DeliveryException.DO_STATUS_NOTMATCH);
            }

            //如果订单的配送商属于自配送类型, 而且配送站点为空, 则抛出异常
            if (DoType.SELL.getValue().equals(doHeader.getDoType()) && (CarrierType.SELF.getValue().equals(doHeader.getCarrier().getType())) && doHeader.getStationId() == null) {
                throw new DeliveryException(DeliveryException.DO_STATION_NULL);
            }
//
//            // 校验合约机订单、实名制网卡订单、选号入网订单必须单独生成波次
//            boolean isOrderNeedAloneBigWave = DoUtil.isDoNeedRegister(doHeader);
//            // 提前判断，避免后续无意义操作
//            if (aloneDoCount > 0 && !isOrderNeedAloneBigWave) {
//                //如果选中订单中含有这3种订单，且又有这3种以外的订单，则直接抛异常
//                throw new DeliveryException(DeliveryException.DO_MUST_ALONE_BIG_WAVE);
//            }
//            if (isOrderNeedAloneBigWave) {
//                aloneDoCount++;
//            }
        }
//
//        //（防止遗漏）如果选中订单中含有这3种订单，且又有这3种以外的订单，则直接抛异常
//        if (aloneDoCount > 0 && aloneDoCount < dataList.size()) {
//            throw new DeliveryException(DeliveryException.DO_MUST_ALONE_BIG_WAVE);
//        }
    }

    /**
     * 校验是否允许混供应商 渠道生成波次
     * @param dataList
     */
    protected void checkMixConfig(List<DeliveryOrderHeader> dataList) {
        if (DoType.SELL.getValue().equals(dataList.get(0).getDoType())) {
            // 是否允许混配送商
            boolean mixCarrier = Config.convert2Boolean(Config.getConfig(ConfigKeys.DELIVERY_WAVE_MIXCARRIER,
                    Config.ConfigLevel.WAREHOUSE), Boolean.TRUE);

            // 是否允许混渠道
            boolean mixChannel = Config.convert2Boolean(Config.getConfig(ConfigKeys.DELIVERY_WAVE_MIXCHANNEL,
                    Config.ConfigLevel.WAREHOUSE), Boolean.TRUE);

            if (!mixChannel) {
                boolean result = dataList.stream()
                        .map(DeliveryOrderHeader::getChannelCode)
                        .distinct()
                        .count() > 1L;
                if (result) {
                    throw new DeliveryException(DeliveryException.WAVE_CANNOT_MIX_CHANNEL);
                }
            }

            if (!mixCarrier) {
                boolean result = dataList.stream()
                        .map(DeliveryOrderHeader::getCarrierId)
                        .distinct()
                        .count() > 1L;
                if (result) {
                    throw new DeliveryException(DeliveryException.WAVE_CANNOT_MIX_CARRIER);
                }
            }
        }
    }

    /**
     * 根据DO类型返回波次类型,如果为正常do，按照do特殊类型决定波次类型
     * @param doHeader 发货单
     * @return 波次类型
     */
    @SuppressWarnings("deprecation")
    protected String getWaveTypeByDoType(DeliveryOrderHeader doHeader){
        String waveType = "";
        String doType = doHeader.getDoType();
        if (StringUtil.isEmpty(doType)) {
            return waveType;
        }
        if (DoType.SELL.getValue().equals(doType)) {
            waveType = WaveType.WAVE_NORMAL.getValue();
        } else if (DoType.RTV.getValue().equals(doType)||DoType.MPS_OUT.getValue().equals(doType)) {
            waveType = WaveType.WAVE_RTV.getValue();
        } else if (DoType.ALLOT.getValue().equals(doType)|| DoType.MPS_OUT.getValue().equals(doHeader.getDoType())) {
            waveType = WaveType.WAVE_TT.getValue();
        }else if (DoType.WHOLESALE.getValue().equals(doType)) {
            waveType = WaveType.WAVE_2B.getValue();
        }
        return waveType;
    }
    
    /**
     * 获取细分类型
     */
    protected AutoWaveType getAutoWaveType(AutoWaveType autoWaveType, List<DeliveryOrderHeader> doList) {
        // RTV/调拨/RMA的细分类型为空
        if (StringUtil.isIn(doList.get(0).getDoType(), DoType.RTV.getValue(), DoType.ALLOT.getValue())) {
            autoWaveType = AutoWaveType.NORMAL;
        } else {
            if (autoWaveType == null) {// 判断细分类型逻辑
                autoWaveType = AutoWaveType.NORMAL;
            }
        }
        return autoWaveType;
    }

    /**
     * 根据波次基础数据DTO生成波次
     * @param context 波次基础数据DTO
     * @return 波次号
     * @throws Exception
     */
    public abstract String generateWave(WaveGeneContextDTO context) throws Exception;
}
