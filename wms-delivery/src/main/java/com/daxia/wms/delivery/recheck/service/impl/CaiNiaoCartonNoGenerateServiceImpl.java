package com.daxia.wms.delivery.recheck.service.impl;

import com.taobao.api.response.CainiaoWaybillIiGetResponse;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;
import com.daxia.wms.delivery.recheck.service.impl.carton.CainiaoWayBillGet;
import com.taobao.api.domain.WaybillApplyNewInfo;

@Name("caiNiaoCartonNoGenerateService")
@lombok.extern.slf4j.Slf4j
public class CaiNiaoCartonNoGenerateServiceImpl implements CartonNoGenerateService {
    
    @In
    CainiaoWayBillGet cainiaoWayBillGet;

    @Override
    public void generatorCarton(DeliveryOrder<PERSON>ead<PERSON> doHeader, CartonHeader cartonHeader) {
        CainiaoWaybillIiGetResponse.WaybillCloudPrintResponse waybillApplyNewInfo = cainiaoWayBillGet.reqeust(doHeader, cartonHeader);
    
        cartonHeader.setCartonNo(waybillApplyNewInfo.getWaybillCode());
        cartonHeader.setWayBill(waybillApplyNewInfo.getWaybillCode());
        cartonHeader.setPrintData(waybillApplyNewInfo.getPrintData());
    }
}
