package com.daxia.wms.delivery.task.replenish.dto;

import com.daxia.wms.delivery.task.replenish.entity.ReplMoveTask;
import com.daxia.wms.stock.stock.dto.Stock2AllocateDTO;

@lombok.extern.slf4j.Slf4j
public class ReplMoveTaskStock2AllocateDTO extends Stock2AllocateDTO {
	private Long fmStockId;
	private Long toStockId;
	private ReplMoveTask replMoveTask;
	
	public Long getFmStockId() {
		return fmStockId;
	}
	
	public void setFmStockId(Long fmStockId) {
		this.fmStockId = fmStockId;
	}
	
	public Long getToStockId() {
		return toStockId;
	}
	
	public void setToStockId(Long toStockId) {
		this.toStockId = toStockId;
	}
	
	public ReplMoveTask getReplMoveTask() {
		return replMoveTask;
	}
	
	public void setReplMoveTask(ReplMoveTask replMoveTask) {
		this.replMoveTask = replMoveTask;
	}
}
