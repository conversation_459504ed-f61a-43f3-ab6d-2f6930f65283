package com.daxia.wms.delivery.container.service.impl;

import java.util.List;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.container.filter.ContainerLogFilter;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.container.dao.ContainerLogDAO;
import com.daxia.wms.delivery.container.entity.ContainerLog;
import com.daxia.wms.delivery.container.service.ContainerLogService;
import com.daxia.wms.master.entity.Container;

/**
 *  容器日志业务实现类
 */
@Name("com.daxia.wms.delivery.containerLogService")
@lombok.extern.slf4j.Slf4j
public class ContainerLogServiceImpl implements ContainerLogService {
    @In
    private ContainerLogDAO containerLogDAO;
    
    /**
     * 新增保存容器使用日志
     * 
     * @param containerLog
     */
    @Override
    public void save(ContainerLog containerLog) {
        containerLogDAO.save(containerLog);
    }

    /**
     * 新增一条容器操作日志
     * @param container
     * @param mergeLocCode 集货位号，集货入出区需要该参数
     * @param operType 操作类型
     * @param operator 操作人
     */
    @Override
    @Transactional
    public void addContainerLog(Container container, String mergeLocCode, String operType, String operator) {
        ContainerLog containerLog = new ContainerLog();
        containerLog.setContainerNo(container.getContainerNo());
        containerLog.setDocType(container.getDocType());
        containerLog.setDocNo(container.getDocNo());
        containerLog.setMergeCode(mergeLocCode);
        containerLog.setOperation(operType);
        containerLog.setOperationTime(DateUtil.getNowTime());
        containerLog.setOperSource(ParamUtil.getCurrentDevice());        // 设置操作来源
        if (StringUtil.isEmpty(operator)) {
            containerLog.setOperator(containerLogDAO.getOperateUser());
        } else {
            containerLog.setOperator(operator);
        }
        save(containerLog);
    }
    
    /**
     * 查询指定波次下的容器日志
     * @param waveId
     * @return
     */
    @Override
    public List<ContainerLog> queryLogsByWave(Long waveId) {
        return containerLogDAO.queryLogsByWave(waveId);
    }

    @Override
    public String getLaskBindDocNo(String containerNo, String docType) {
        return containerLogDAO.getLaskBindDocNo(containerNo, docType);
    }

	@Override
	public List<ContainerLog> queryLogsByDocNo(String docNo, String opType, String docType) {
		return containerLogDAO.queryLogsByDocNo(docNo, opType, docType);
	}
    
    @Override
    public DataPage<ContainerLog> query(ContainerLogFilter containerLogFilter, int startIndex, int pageSize) {
        return containerLogDAO.findRangeByFilter(containerLogFilter, startIndex, pageSize);
    }
}
