package com.daxia.wms.delivery.deliveryorder.job;

import com.daxia.framework.common.util.CollectionUtil;
import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.crossorder.entity.CrossSeedHeader;
import com.daxia.wms.delivery.crossorder.service.CrossSeedService;
import com.daxia.wms.delivery.crossorder.service.impl.CrossSeedServiceImpl;
import com.daxia.wms.exp.entity.ExpAsn;
import com.daxia.wms.exp.service.AsnExpService;
import com.daxia.wms.exp.service.impl.AsnExpServiceImpl;
import com.daxia.wms.master.job.AbstractJob;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.master.service.impl.WarehouseServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.util.List;

/**
 * 越库分播单生成的定时任务
 */
@Name("crossSeedCreateJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class CrossSeedCreateJob extends AbstractJob {
    @Override
    protected void doRun() throws InterruptedException {
        WarehouseService warehouseService = (WarehouseService) Component.getInstance(WarehouseServiceImpl.class);
        AsnExpService asnExpService = (AsnExpService) Component.getInstance(AsnExpServiceImpl.class);
        String whIds = Config.get(Keys.Task.cross_seed_create_whIds, Config.ConfigLevel.GLOBAL);
        List<Long> whIdList = CollectionUtil.splitToListOfLong(whIds);

        CrossSeedService crossSeedService = (CrossSeedService) Component.getInstance(CrossSeedServiceImpl.class);
        for (Long whId : whIdList) {
            ParamUtil.setCurrentWarehouseId(whId);
            List<String> asnNoList = crossSeedService.findAsnForCreateAndAllocate();
            if (CollectionUtils.isNotEmpty(asnNoList)) {
                for (String asnNo : asnNoList) {
                    CrossSeedHeader crossSeedHeader = crossSeedService.getCrossSeedByAsnNo(asnNo);
                    ExpAsn asn = asnExpService.findByAsnNo(asnNo, whId);
                    if(StringUtil.isBlank(asn.getUserDefine04())){
                        continue;
                    }
                    List<String> doNoList = CollectionUtil.splitToList(asn.getUserDefine04());
                    if (crossSeedHeader != null) {
                        if (Constants.AsnStatus.ORDER_CLOSED.getValue().equals(asn.getAsnStatus())
                                && Constants.CrossSeedStatus.INITIAL.getValue().equals(crossSeedHeader.getStatus())) {
                            //入库单已经审核，分播单还是初始化，则进行预分播
                            crossSeedService.doAllocate(crossSeedHeader.getId(),asn.getId());
                        }
                    } else {
                        //不存在分播单，则创建
                        crossSeedService.createCrossSeedByDoList(asn.getAsnNo(), doNoList);
                    }
                }
            }
        }
    }
}