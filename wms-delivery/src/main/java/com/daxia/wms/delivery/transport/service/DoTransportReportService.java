package com.daxia.wms.delivery.transport.service;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.transport.entity.DoTransportReport;
import com.daxia.wms.delivery.transport.filter.DoTransportReportFilter;

public interface DoTransportReportService {

    public DataPage<DoTransportReport> queryTransportRecord(DoTransportReportFilter doTransportReportFilter, int startIndex,
                                                            int pageSize);

    public DoTransportReport getById(Long transportRecordId);

    public DoTransportReport getDoTransportReportByLoadId(Long loadId);

    public void saveDoTransportReport(DoTransportReport doTransportReport);

    public void deleteById(Long id);
}
