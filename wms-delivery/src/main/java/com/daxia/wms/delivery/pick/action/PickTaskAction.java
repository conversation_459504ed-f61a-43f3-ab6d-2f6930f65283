package com.daxia.wms.delivery.pick.action;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

import com.daxia.wms.delivery.pick.entity.PickHeader;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.pick.filter.PickTaskFilter;
import com.daxia.wms.delivery.pick.service.PickHeaderService;
import com.daxia.wms.delivery.pick.service.PickTaskService;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.StockStatus;
import com.daxia.wms.master.entity.Partition;

/**
 * 拣货任务Action
 */
@Name("com.daxia.wms.delivery.pickTaskAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class PickTaskAction extends PagedListBean<PickTask> {

	private static final long serialVersionUID = -9146326679613769827L;
	
	@In
	private PickTaskService pickTaskService;
    @In
    private PickHeaderService pickHeaderService;
    @In
    private WaveService waveService;
    
	private Long pktHeaderId;
	private PickTaskFilter pickTaskFilter = new PickTaskFilter();
	private boolean initialized = false;
	private Long queryType;
	private String containerNo;
	private String pktNo;
	private List<SelectItem> partitionSelectList = new ArrayList<SelectItem>();
	
	/**
	 * 总拣货数量
	 */
	private BigDecimal totalPickedNum;
	
	/**
	 * 显示捡货单号
	 */
	private String pktHeaderNos;

    public void initPickTaskPage(){
		if (!initialized) {
			this.query();
			this.initialized = true;
		}
    }

	/**
	 * 分页查询拣货任务
	 */
	@Override
	public void query() {
		if(this.pickTaskFilter == null){
			this.pickTaskFilter = new PickTaskFilter();
		}
        if (null != this.pktHeaderId) {
            pickTaskFilter.setPickHeaderId(this.pktHeaderId);
            initPartitionSelectList(pktHeaderId);
		} else if (StringUtil.isNotEmpty(containerNo) && StringUtil.isNotEmpty(pktNo)) {
			String waveNo = pktNo;
			// 容器日志跳转
			pickTaskFilter.setWaveHeaderId(waveService.getWaveHeaderByWaveNum(waveNo).getId());
			pickTaskFilter.setContainerNo(containerNo);
			initPartitionSelectList(pickTaskFilter.getWaveHeaderId(), containerNo);
			// 显示拣货单号
			showPktHeaderNo(pickTaskFilter.getWaveHeaderId(), containerNo);
		} else if (StringUtil.isNotEmpty(pktNo)) {
			PickHeader pickHeader = pickHeaderService.getPktHeaderByPktNo(pktNo);
			if (pickHeader != null) {
				pickTaskFilter.setPickHeaderId(pickHeader.getId());
				initPartitionSelectList(pickHeader.getId());
			}
		}
		if (null != queryType) {
         // 缺货的库存状态
            List<String> lackStockStatus = new ArrayList<String>(2);
            lackStockStatus.add(StockStatus.BROKEN.getValue());
            lackStockStatus.add(StockStatus.LACK.getValue());

            // 没有缺货的库存状态
            List<String> normalStockStatus = new ArrayList<String>(1);
            normalStockStatus.add(StockStatus.NORMAL.getValue());

            //拣货完成的拣货任务状态
            List<String> pickedStats = new ArrayList<String>(2);
            pickedStats.add(DoStatus.ALLPICKED.getValue());
            pickedStats.add(DoStatus.ALL_DELIVER.getValue());

            //没有拣货完成的拣货任务状态
            List<String> unPickedStats = new ArrayList<String>(2);
            unPickedStats.add(DoStatus.INITIAL.getValue());
            unPickedStats.add(DoStatus.ALLALLOCATED.getValue());
			if (1 == queryType) {
				pickTaskFilter.setStatusList(pickedStats);
			} else if (2 == queryType) {
				pickTaskFilter.setStatusList(unPickedStats);
				pickTaskFilter.setStockStatuses(normalStockStatus);
			} else {
				pickTaskFilter.setStatusList(unPickedStats);
				pickTaskFilter.setStockStatuses(lackStockStatus);
			}
		}
       
		DataPage<PickTask> dataPage = this.pickTaskService.query(this.pickTaskFilter, this.getStartIndex(), this.getPageSize());
		
		computePickedNum(pickTaskService.query(pickTaskFilter));
		this.populateValues(dataPage);
	}
	
	/**
	 * 显示捡货单号
	 * @param waveHeaderId
	 * @param containerNo
	 */
	private void showPktHeaderNo(Long waveHeaderId, String containerNo) {
		pktHeaderNos = pickTaskService.queryPktHNosByWaveIdConNo(waveHeaderId,containerNo);
	}

	/**
	 * 容器页面
	 * @param waveHeaderId
	 * @param containerNo
	 */
	private void initPartitionSelectList(Long waveHeaderId, String containerNo) {

	    if (ListUtil.isNullOrEmpty(partitionSelectList)) {
	        List<Partition> partitions = pickTaskService.queryPartitionByWaveIdConNo(waveHeaderId,containerNo);
	        for (Partition p : partitions) {
	                SelectItem item = new SelectItem();
	                item.setLabel(p.getPartitionCode());
	                item.setValue(p.getPartitionCode());
	                partitionSelectList.add(item);
	        }
	    }
	
	}

	/**
	 * 计算总拣货数量
	 * @param dataList
	 */
	private void computePickedNum(List<PickTask> dataList) {
	    BigDecimal totalPickNum = BigDecimal.ZERO;
	    for(PickTask pt : dataList){
	        totalPickNum = totalPickNum.add(pt.getPickedQty());
	    }
	    setTotalPickedNum(totalPickNum);
	}

    private void initPartitionSelectList(Long pktHeaderId) {
	    if (ListUtil.isNullOrEmpty(partitionSelectList)) {
	        List<Partition> partitions = pickTaskService.queryPartitionByPktHId(pktHeaderId);
	        for (Partition p : partitions) {
	                SelectItem item = new SelectItem();
	                item.setLabel(p.getPartitionCode());
	                item.setValue(p.getPartitionCode());
	                partitionSelectList.add(item);
	        }
	    }
	}

	public Long getPktHeaderId() {
		return pktHeaderId;
	}

	public void setPktHeaderId(Long pktHeaderId) {
		this.pktHeaderId = pktHeaderId;
	}
    
    public PickTaskFilter getPickTaskFilter() {
        return pickTaskFilter;
    }
    
    public void setPickTaskFilter(PickTaskFilter pickTaskFilter) {
        this.pickTaskFilter = pickTaskFilter;
    }
    
    public Long getQueryType() {
        return queryType;
    }
    
    public void setQueryType(Long queryType) {
        this.queryType = queryType;
    }
    
    public String getContainerNo() {
        return containerNo;
    }
    
    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }
    
    public String getPktNo() {
        return pktNo;
    }
    
    public void setPktNo(String pktNo) {
        this.pktNo = pktNo;
    }

    
    public List<SelectItem> getPartitionSelectList() {
        return partitionSelectList;
    }

    
    public void setPartitionSelectList(List<SelectItem> partitionSelectList) {
        this.partitionSelectList = partitionSelectList;
    }

    
    public BigDecimal getTotalPickedNum() {
        return totalPickedNum;
    }

    
    public void setTotalPickedNum(BigDecimal totalPickedNum) {
        this.totalPickedNum = totalPickedNum;
    }

	public String getPktHeaderNos() {
		return pktHeaderNos;
	}

	public void setPktHeaderNos(String pktHeaderNos) {
		this.pktHeaderNos = pktHeaderNos;
	}
}
