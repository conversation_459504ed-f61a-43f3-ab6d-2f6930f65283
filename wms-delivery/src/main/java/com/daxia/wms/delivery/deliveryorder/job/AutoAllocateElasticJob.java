package com.daxia.wms.delivery.deliveryorder.job;

import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateService;
import com.daxia.wms.delivery.deliveryorder.service.impl.DoAllocateServiceImpl;
import com.daxia.wms.master.job.WarehouseSimpleJob;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * 自动分配Do的定时任务,废弃多线程跑一个仓库的数据,避免超分
 */
@Name("autoAllocateElasticJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class AutoAllocateElasticJob extends WarehouseSimpleJob {

    private List<Long> getAlcHeaderIds(int batchAllocateNum, Long warehouseId) {
        DoAllocateService service = ((DoAllocateService) Component.getInstance(DoAllocateServiceImpl.class));
        return service.queryNeedAllocateDoHeaderIds(batchAllocateNum, warehouseId);
    }

    /**
     * 筛选满足条件的DO进行自动分配
     */
    @Override
    protected void doRun() throws InterruptedException {
        int batchSize = Config.getInt(Keys.Delivery.allocate_batch_num, Config.ConfigLevel.GLOBAL, 20);
        // 需要分配的alcHeaderId
        List<Long> alcHeaderIds = getAlcHeaderIds(batchSize, ParamUtil.getCurrentWarehouseId());
        if (CollectionUtils.isEmpty(alcHeaderIds)) {
            return;
        }
        int threadNum = getMultiThreadNum();
        log.info("alc header id size:" + alcHeaderIds.size());

        if (threadNum == 1) {
            // 单线程运行
            AutoAllocateExecutor executor = ((AutoAllocateExecutor) Component.getInstance(AutoAllocateExecutor.class));
            executor.doAllocate(alcHeaderIds, ParamUtil.getCurrentWarehouseId());
        } else {
            // 多线程分配
            log.info("Multiple threads allocation with threadNum: " + threadNum);
            CountDownLatch threadSignal = new CountDownLatch(threadNum);

            List<List<Long>> partitions = Lists.partition(alcHeaderIds, threadNum);
            for (int i = 0; i < partitions.size(); i++) {
                new AutoAllocateTask(i, partitions.get(i), threadSignal, ParamUtil.getCurrentWarehouseId()).start();
            }
            threadSignal.await();
        }


    }

    /**
     * 多线程分配线程数
     */
    private int getMultiThreadNum() {
        Integer nThreads = SystemConfig.getConfigValueInt("delivery.allocateJob.multiThreadNum", null);
        if (nThreads == null || nThreads < 1 || nThreads > 10) {
            nThreads = 1;
        }

        return nThreads;
    }
}