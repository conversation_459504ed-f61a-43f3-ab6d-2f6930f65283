package com.daxia.wms.delivery.print.service;

import java.util.List;

import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.print.dto.PrintCfg;
import com.daxia.wms.delivery.print.dto.PrintDoDTO;


/**
 *  发货单打印接口
 */
public interface PrintDoService {
    /**
     * 设置发货单打印参数
     * @param docId 单据id
     * @param printEntrance 打印入口
     * @return
     */
    public PrintCfg setDoPrintCfg(Long docId, String printEntrance);

    public String genDataByWave(List<Long> waveIds);

    public String genData(List<Long> doIds);
}
