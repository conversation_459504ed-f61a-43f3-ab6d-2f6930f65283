package com.daxia.wms.delivery.deliveryorder.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.daxia.dubhe.api.internal.util.NumberUtils;

import com.daxia.framework.common.util.*;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.dao.MergePrintDetailDAO;
import com.daxia.wms.delivery.deliveryorder.dao.MergePrintHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.dto.MergePrintDTO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.MergePrintDetail;
import com.daxia.wms.delivery.deliveryorder.entity.MergePrintHeader;
import com.daxia.wms.delivery.deliveryorder.filter.MergePrintFilter;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.MergePrintService;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.print.helper.WaybillPrintHelper;
import com.daxia.wms.delivery.print.service.PrintWaybillService;
import com.daxia.wms.delivery.print.service.carton.PrintCartonDispatcher;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.entity.TempCarton;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.recheck.service.impl.CartonNoGenerateDispatcher;
import com.daxia.wms.exp.entity.ExpInvoiceHeader;
import com.daxia.wms.exp.receive.srv.Invoice2ErpExpSrv;
import com.daxia.wms.print.dto.PrintData;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.util.*;

@Name("com.daxia.wms.delivery.mergePrintService")
@lombok.extern.slf4j.Slf4j
public class MergePrintImpl implements MergePrintService {

    @In
    private MergePrintHeaderDAO mergePrintHeaderDAO;

    @In
    private MergePrintDetailDAO mergePrintDetailDAO;

    @In
    private DeliveryOrderService deliveryOrderService;

    @In
    private CartonService cartonService;

    @In
    private CartonNoGenerateDispatcher cartonNoGenerateDispatcher;

    @In
    private SequenceGeneratorService sequenceGeneratorService;

    @In
    private PrintCartonDispatcher printCartonDispatcher;
    @In
    private Invoice2ErpExpSrv invoice2ErpExpSrv;


    @Override
    public DataPage<MergePrintDTO> queryPageInfo(MergePrintFilter mergePrintFilter, int startIndex, int pageSize) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        String sql = mergePrintHeaderDAO.builderHqlByAll(mergePrintFilter, paramMap);
        StringBuilder countSql = new StringBuilder("select count(*) from (");
        countSql.append(sql).append(") a");
        DataPage<MergePrintDTO> dtoDataPage = mergePrintHeaderDAO.queryData(sql, countSql.toString(), paramMap,
                startIndex, pageSize);
        List<MergePrintDTO> stls = dtoDataPage.getDataList();
        int totalcount = NumberUtils.object2Integer(dtoDataPage.getTotalCount());
        return new DataPage<MergePrintDTO>(totalcount, dtoDataPage.getStartIndex(), dtoDataPage.getPageSize(), stls);
    }

    @Override
    @Transactional
    public List<PrintData> execute(MergePrintDTO mergePrintDTO) {
        List<PrintData> printDataList = new ArrayList<PrintData>();
        String printData = null;
        if (Constants.YesNo.YES.getValue().equals(mergePrintDTO.getIsPrinted())) {
            printData = mergePrintDTO.getPrintData();
        } else {
            printData = generateWayBill(mergePrintDTO);
        }
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(mergePrintDTO.getDoIdList().get(0));
        doHeader.setWholesaleWaybillFlag(Constants.YesNo.YES.getValue());
        PrintWaybillService printWaybillService = printCartonDispatcher.getPrintService(doHeader);
        JSONArray objects = JSONArray.parseArray(printData);
        for (int i = 0; i < objects.size(); i++) {
            JSONObject obj = objects.getJSONObject(i);
            TempCarton dto = new TempCarton();
            dto.setId(-1L);
            dto.setCartonNo(obj.getString("cartonNo_x"));
            dto.setPrintData(obj.toJSONString());
            dto.setWayBill(obj.getString("wayBill_x"));
            dto.setIsPrinted(mergePrintDTO.getIsPrinted());
            PrintData data = printWaybillService.genTempCartonPrintData(doHeader, dto);
            Map<String, Object> originalUnitProps = new HashMap<String, Object>();
            originalUnitProps.put("data", data.getData());
            data.setTemplateJs(PrintTemplateUtil.process(data.getPrintCfg().getLodopTemplate(), originalUnitProps));
            printDataList.add(data);
        }
        //发票信息回写ERP
        List<DeliveryOrderHeader> doHeaderList = deliveryOrderService.findDoByIds(mergePrintDTO.getDoIdList());
        for (DeliveryOrderHeader header : doHeaderList) {
            if(ListUtil.isNotEmpty(header.getInvoiceHeaders())){
                for (InvoiceHeader invoiceHeader : header.getInvoiceHeaders()) {
                    invoice2ErpExpSrv.createMsg(invoiceHeader.getId(),Constants.InvoiceWriteBackType.MERGE.getValue());
                }
            }
        }
        return WaybillPrintHelper.sortPrintDataByPrintTemplate(printDataList);
    }

    private String generateWayBill(MergePrintDTO mergePrintDTO) {
        deliveryOrderService.modifyCarrier(mergePrintDTO.getDoIdList(), mergePrintDTO.getCarrierId());
        boolean checkFlag = deliveryOrderService.checkDoStatus(mergePrintDTO.getDoIdList(), Constants.DoStatus.ALL_CARTON.getValue(),Constants.DoStatus.ALL_DELIVER.getValue());
        if (!checkFlag) {
            throw new BusinessException("只有装箱完成以后的订单才能下单");
        }
        MergePrintHeader header = new MergePrintHeader();
        String groupNo = sequenceGeneratorService.generateSequenceNo(Constants.SequenceName.MC.getValue(), ParamUtil.getCurrentWarehouseId());
        header.setGroupNo(groupNo);
        header.setCarrierId(mergePrintDTO.getCarrierId());
        header.setDoNum(mergePrintDTO.getDoNum());
        header.setCartonNum(mergePrintDTO.getCartonNum());
        mergePrintHeaderDAO.save(header);
        if (Constants.WaybillType.SF.equals(deliveryOrderService.getDoHeaderById(mergePrintDTO.getDoIdList().get(0)).getCarrier().getWaybillType())) {
            generateSF(mergePrintDTO, header);
        } else {
            generateNormal(mergePrintDTO, header);
        }
        return header.getPrintData();
    }

    private void generateSF(MergePrintDTO mergePrintDTO, MergePrintHeader header) {
        DeliveryOrderHeader deliveryOrderHeader = deliveryOrderService.getDoHeaderById(mergePrintDTO.getDoIdList().get(0));
        CartonHeader cartonHeader = deliveryOrderHeader.getCartonHeaders().get(0);
        deliveryOrderHeader.setWholesaleWaybillFlag(Constants.YesNo.YES.getValue());
        deliveryOrderHeader.setParcelQuantity(StringUtil.toString(mergePrintDTO.getWayBillNum()));
        cartonHeader.setWholesaleWaybillFlag(Constants.YesNo.YES.getValue());
        cartonNoGenerateDispatcher.genNewCarton(deliveryOrderHeader, cartonHeader, false);
        if (StringUtil.isBlank(cartonHeader.getWayBill())) {
            throw new BusinessException("电子面单下单异常！");
        }
        int size = 1;
        String wayBill = cartonHeader.getWayBill();
        JSONArray objects = new JSONArray();
        String[] wayBillList = wayBill.split(",");
        for (Long doId : mergePrintDTO.getDoIdList()) {
            MergePrintDetail mergePrintDetail = new MergePrintDetail();
            DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(doId);
            List<CartonHeader> cartonHeaderList = doHeader.getCartonHeaders();
            for (CartonHeader ch : cartonHeaderList) {
                if (size > mergePrintDTO.getWayBillNum()) {
                    break;
                }
                JSONObject obj = obj = new JSONObject();
                obj.put("wayBill_x", wayBillList[size - 1]);
                obj.put("cartonNo_x", wayBillList[size - 1]);
                objects.add(obj);
                size++;
            }
            mergePrintDetail.setHeaderId(header.getId());
            mergePrintDetail.setDoHeaderId(doId);
            mergePrintDetail.setCartonNum(cartonHeaderList.size());
            mergePrintDetailDAO.save(mergePrintDetail);
        }
        header.setWayBill(wayBill);
        header.setPrintData(objects.toJSONString());
        mergePrintHeaderDAO.update(header);
    }

    private void generateNormal(MergePrintDTO mergePrintDTO, MergePrintHeader header) {
        int size = 1;
        String wayBill = "";
        JSONArray objects = new JSONArray();
        for (Long doId : mergePrintDTO.getDoIdList()) {
            MergePrintDetail mergePrintDetail = new MergePrintDetail();
            DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(doId);
            doHeader.setWholesaleWaybillFlag(Constants.YesNo.YES.getValue());
            List<CartonHeader> cartonHeaderList = doHeader.getCartonHeaders();
            for (CartonHeader ch : cartonHeaderList) {
                if (size > mergePrintDTO.getWayBillNum()) {
                    break;
                }
                ch.setWholesaleWaybillFlag(Constants.YesNo.YES.getValue());
                cartonNoGenerateDispatcher.genNewCarton(doHeader, ch, false);
                if (StringUtil.isBlank(ch.getWayBill())) {
                    throw new BusinessException("电子面单下单异常！");
                }
                if (size < mergePrintDTO.getWayBillNum()) {
                    wayBill += ch.getWayBill() + ",";
                } else {
                    wayBill += ch.getWayBill();
                }
                JSONObject obj = null;
                if (StringUtil.isNotBlank(ch.getPrintData())) {
                    obj = JSONObject.parseObject(ch.getPrintData());
                } else {
                    obj = new JSONObject();
                }
                obj.put("wayBill_x", ch.getWayBill());
                obj.put("cartonNo_x", ch.getCartonNo());
                objects.add(obj);
                size++;
            }
            mergePrintDetail.setHeaderId(header.getId());
            mergePrintDetail.setDoHeaderId(doId);
            mergePrintDetail.setCartonNum(cartonHeaderList.size());
            mergePrintDetailDAO.save(mergePrintDetail);
        }
        header.setWayBill(wayBill);
        header.setPrintData(objects.toJSONString());
        mergePrintHeaderDAO.update(header);
    }
}
