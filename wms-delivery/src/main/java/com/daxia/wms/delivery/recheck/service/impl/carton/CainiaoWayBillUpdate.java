package com.daxia.wms.delivery.recheck.service.impl.carton;

import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.master.entity.Carrier;
import com.daxia.wms.master.entity.CarrierCainiaoEx;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.CarrierCainiaoExService;
import com.daxia.wms.master.service.ShopInfoService;
import com.daxia.wms.master.service.SkuCache;
import com.daxia.wms.waybill.cainiao.CainiaoConstants;
import com.taobao.api.ApiException;
import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.request.CainiaoWaybillIiUpdateRequest;
import com.taobao.api.response.CainiaoWaybillIiUpdateResponse;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.util.ArrayList;
import java.util.List;

@Name("cainiaoWayBillUpdate")
@lombok.extern.slf4j.Slf4j
public class CainiaoWayBillUpdate extends CainiaoWayBillBase {

    @In
    CarrierCainiaoExService carrierCainiaoExService;

    @In
    SkuCache skuCache;

    @In
    ShopInfoService shopInfoService;

    @In
    SequenceGeneratorService sequenceGeneratorService;



    public void reqeust(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        Carrier carrier = doHeader.getCarrier();
        CarrierCainiaoEx carrierCainiaoEx = carrierCainiaoExService.getByCarrier(carrier.getId());
        if (carrierCainiaoEx == null) {
            throw new DeliveryException(DeliveryException.WAYBILL_CAINIO_GET_ERROR, "配送商菜鸟配置信息缺失，请联系管理员");
        }
        WarehouseCarrier warehouseCarrier = loadWarehouseCarrier(carrier.getId());

        CainiaoWaybillIiUpdateRequest req = new CainiaoWaybillIiUpdateRequest();
        req.setParamWaybillCloudPrintUpdateRequest(genWaybillApplyNewRequest(doHeader, cartonHeader, carrierCainiaoEx));

        TaobaoClient client = new DefaultTaobaoClient(CainiaoConstants.getCainiaoConfig().getServerUrl(), warehouseCarrier.getAppKey(), warehouseCarrier.getAppSecret());
        try {
            CainiaoWaybillIiUpdateResponse rsp = client.execute(req, warehouseCarrier.getAppToken());

            if (!rsp.isSuccess()) {
                log.error("Cainiao get error, request:" + req.getParamWaybillCloudPrintUpdateRequest() + ", response: " + rsp.getBody());
                throw new DeliveryException(DeliveryException.WAYBILL_CAINIO_GET_ERROR, rsp.getSubMsg() + ", " + rsp.getSubCode());
            }
            // return rsp.getWaybillApplyNewCols().get(0);
        } catch (ApiException e) {
            log.error("Cainiao get error! ", e);
            throw new DeliveryException(DeliveryException.WAYBILL_CAINIO_GET_ERROR);
        }
    }

    private CainiaoWaybillIiUpdateRequest.WaybillCloudPrintUpdateRequest genWaybillApplyNewRequest(DeliveryOrderHeader doHeader, CartonHeader cartonHeader, CarrierCainiaoEx carrierCainiaoEx) {
        CainiaoWaybillIiUpdateRequest.WaybillCloudPrintUpdateRequest applyNewRequest = new CainiaoWaybillIiUpdateRequest.WaybillCloudPrintUpdateRequest();
        applyNewRequest.setCpCode(carrierCainiaoEx.getCpCode());
        applyNewRequest.setLogisticsServices(genLogisticsServiceList4Get(doHeader));
        applyNewRequest.setPackageInfo(genPackageIntem(doHeader, cartonHeader));
        applyNewRequest.setWaybillCode(cartonHeader.getCartonNo());
        applyNewRequest.setTemplateUrl(carrierCainiaoEx.getTemplateURL());
        //收货人信息
        applyNewRequest.setRecipient(genConsigneeInfo4Update(doHeader));
        return applyNewRequest;
    }

    // 包裹中的商品类型
    private CainiaoWaybillIiUpdateRequest.PackageInfoDto genPackageIntem(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        List<DeliveryOrderDetail> details = doHeader.getDoDetails();
        CainiaoWaybillIiUpdateRequest.PackageInfoDto packageItem = new CainiaoWaybillIiUpdateRequest.PackageInfoDto();
        List<CainiaoWaybillIiUpdateRequest.Item> itemList = new ArrayList<CainiaoWaybillIiUpdateRequest.Item>();
        for (DeliveryOrderDetail detail : details) {
            String skuName = skuCache.getSku(detail.getSkuId()).getProductCname();
            if (StringUtils.isNotEmpty(skuName)) {
                CainiaoWaybillIiUpdateRequest.Item item = new CainiaoWaybillIiUpdateRequest.Item();
                item.setName(skuName);
                item.setCount(detail.getExpectedQty().longValue());
                itemList.add(item);
            }
        }
        packageItem.setItems(itemList);
        packageItem.setWeight(getWeight(cartonHeader));
        return packageItem;
    }
}