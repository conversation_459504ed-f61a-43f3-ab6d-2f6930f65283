package com.daxia.wms.delivery.task.replenish.dao;

import java.math.BigDecimal;
import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.task.replenish.entity.ReplMoveTask;
import com.daxia.wms.Constants.TaskStatus;

@Name("replMoveTaskDAO")
@lombok.extern.slf4j.Slf4j
public class ReplMoveTaskDAO extends HibernateBaseDAO<ReplMoveTask, Long> {
	private static final long serialVersionUID = -6413336408959512129L;

	@SuppressWarnings("unchecked")
	public List<ReplMoveTask> getReplMoveTasksByReplTask(Long replTaskId, TaskStatus taskStatus) {
		String hql = "from ReplMoveTask o where o.docLineId = :replTaskId and o.status = :status "
				+ " and o.warehouseId = :warehouseId ";
		Query query = createQuery(hql);
		query.setParameter("replTaskId", replTaskId);
		query.setParameter("status", taskStatus.getValue());
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		return query.list();
	}
	
	public BigDecimal getReplMoveTaskQtyUnitByReplTask(Long replTaskId, TaskStatus taskStatus) {
		String hql = "select ifnull(sum(o.qty_unit), 0) from trs_repl_move_task o where o.doc_line_id = :replTaskId and o.status = :status and is_deleted = 0"
				+ " and o.warehouse_id = :warehouseId ";
		Query query = createSQLQuery(hql);
		query.setParameter("replTaskId", replTaskId);
		query.setParameter("status", taskStatus.getValue());
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		BigDecimal qty = (BigDecimal)query.uniqueResult();
		return qty == null? BigDecimal.ZERO : qty;
	}
	
	public void updateReplMoveTaskStatusByReplTask(Long replTaskId, TaskStatus fmStatus, TaskStatus toStatus) {
		String hql = "update ReplMoveTask o set o.status = :toStatus where o.docLineId = :replTaskId and o.status = :fmStatus "
				+ " and o.warehouseId = :warehouseId ";
		Query query = createUpdateQuery(hql);
		query.setParameter("replTaskId", replTaskId);
		query.setParameter("toStatus", toStatus.getValue());
		query.setParameter("fmStatus", fmStatus.getValue());
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();
	}
}