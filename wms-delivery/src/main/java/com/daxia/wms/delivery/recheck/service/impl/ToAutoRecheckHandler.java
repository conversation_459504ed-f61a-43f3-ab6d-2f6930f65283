package com.daxia.wms.delivery.recheck.service.impl;

import org.jboss.seam.annotations.In;

import com.daxia.wms.delivery.deliveryorder.service.ToAutoHandler;
import com.daxia.wms.delivery.recheck.service.ReCheckService;

@lombok.extern.slf4j.Slf4j
public class ToAutoRecheckHandler extends ToAutoHandler {
	@In
    private ReCheckService reCheckService;
	
	@Override
	protected void handle(Long doId) {
		reCheckService.saveOneCarton4WBL(doId);
	}

	@Override
	protected void setName() {
		this.name = "toAutoRecheckHandler";		
	}

}
