package com.daxia.wms.delivery.deliveryorder.dao;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.print.dto.DoInvoicePrintDTO;

@Name("com.daxia.wms.delivery.printInvoiceDAO")
@lombok.extern.slf4j.Slf4j
public class PrintInvoiceDAO extends
		HibernateBaseDAO<DeliveryOrderHeader, Long> {

	private static final long serialVersionUID = -4750124386346913515L;

	/**
	 * 根据订单Id查找 分拣时 打印波次发票 所需信息
	 * 
	 * @param doId
	 * @return
	 */
	public DoInvoicePrintDTO getInvoicePrintInfoOfDo(Long doId) {
		String sql = "Select t.Do_No,t.Status,t.Sort_Grid_No, t.ref_no1 From doc_do_header t Where t.Id = :doId And t.Warehouse_Id = :whId";
		Query query = this.createSQLQuery(sql).setParameter("doId", doId)
				.setParameter("whId", ParamUtil.getCurrentWarehouseId());
		Object obj = query.uniqueResult();
		Object[] properties = (Object[]) obj;
		DoInvoicePrintDTO dto = new DoInvoicePrintDTO();
		dto.setDoNo((String) properties[0]);
		dto.setStatus((String) properties[1]);
		dto.setSortGridNo(String.valueOf(properties[2]));
		dto.setRefNo1((String) properties[3]);
		return dto;
	}

	/**
	 * 获取订单所属波次的波次号
	 * 
	 * @param doId
	 * @return
	 */
	public String getWaveNoByDoId(Long doId) {
		String hql = "select w.waveNo from WaveHeader w,DeliveryOrderHeader dh "
				+ "where w.id = dh.waveId and w.warehouseId = dh.warehouseId and w.warehouseId =:warehouseId and dh.id = :doHeaderId";
		Query query = createQuery(hql);
		query.setLong("doHeaderId", doId);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		return (String) query.uniqueResult();
	}
}