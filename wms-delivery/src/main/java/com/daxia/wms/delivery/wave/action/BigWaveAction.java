package com.daxia.wms.delivery.wave.action;

import com.danchuang.global.scm.address.client.dto.BaseAddressDTO;
import com.daxia.common.QueueHolder;
import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.service.CfgConfigurationService;
import com.daxia.framework.system.service.impl.CacheService;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoType;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.constant.DeliveryConstant;
import com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.SpecialDoLabel;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateService;
import com.daxia.wms.delivery.deliveryorder.service.SpecialDoLabelService;
import com.daxia.wms.delivery.wave.dto.WaveGeneContextDTO;
import com.daxia.wms.delivery.wave.service.AutoWaveGenService;
import com.daxia.wms.delivery.wave.service.WaveRecommendService;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.delivery.wave.service.impl.WaveStrategy;
import com.daxia.wms.master.MasterException;
import com.daxia.wms.master.component.BusinessCenterComponent;
import com.daxia.wms.master.entity.*;
import com.daxia.wms.master.rule.entity.WaveCriterial;
import com.daxia.wms.master.rule.entity.WaveRuleDetail;
import com.daxia.wms.master.rule.filter.BigWaveFilter;
import com.daxia.wms.master.rule.service.WaveCriterialService;
import com.daxia.wms.master.service.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.*;
import org.jboss.seam.annotations.security.Restrict;

import javax.faces.model.SelectItem;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生成波次业务相关的Action
 */
@Name("com.daxia.wms.delivery.bigWaveAction")
@Restrict("#{identity.hasPermission('delivery.wave')}")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class BigWaveAction extends PagedListBean<DoHeaderDto> {

    private static final long serialVersionUID = -6004098950408595704L;
    /**
     * 异步生成波次事件名称
     */
    public static final String ASYNC_GENE_WAVE_EVENT="ASYNC_GENE_WAVE_EVENT";
    @In
    WaveService waveService;
    @In
    WaveRecommendService waveRecommendService;
    @In
    AreaService areaService;
    @In("waveMaunalGenerateService")
    WaveStrategy waveStrategy;
    @In
    PartitionService partitionService;
    @In
    CfgConfigurationService cfgConfigurationService;
    @In
    SpecialDoLabelService specialDoLabelService;
    @In
    ShopInfoService shopInfoService;
    @In
    DeliveryOrderService deliveryOrderService;
    @In
    DoAllocateService doAllocateService;
    @In
    MerchantService merchantService;
    @In
    WaveCriterialService waveCriterialService;

    @In
    AutoWaveGenService autoWaveGenService;

    @In
    private LineInfoService lineInfoService;

    @In
    private BusinessCustomerService businessCustomerService;

    @In
    private AddressService addressService;

    @In
    private BusinessCenterComponent businessCenterComponent;

    private static final String REGION = CacheService.SYS_CONFIG;

    private static final ObjectMapper MAPPER=new ObjectMapper()
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    private WaveRuleDetail waveRuleDetail = new WaveRuleDetail();
    private BigWaveFilter bigWaveFilter;
    private List<SelectItem> provinces = new ArrayList<SelectItem>();
    private List<SelectItem> cites = new ArrayList<SelectItem>();
    private List<SelectItem> counties = new ArrayList<SelectItem>();
    private Long countryId;
    private Long provinceId;
    private Long cityId;
    private List<SelectItem> notProvinces = new ArrayList<SelectItem>();
    private List<SelectItem> notCites = new ArrayList<SelectItem>();
    private List<SelectItem> notCounties = new ArrayList<SelectItem>();
    private Long notCountryId;
    private Long notProvinceId;
    private Long notCityId;
    private String generatedWaveNum;
    private Long maxDOQty;
    private Integer volumeDoQty;
    private List<SelectItem> partitionList;
    private List<SelectItem> filterList;
    private List<SelectItem> lineList;

    private Integer wavePriority;//波次优先级（默认为3）
    private List<SelectItem> specialLabelList;
    private List<SelectItem> shopInfos;
    private Boolean recommend = false;
    private Integer bigWaveFlg = Integer.valueOf(0);
    private Boolean sellerRemarkFrontFlag = false;
    private String merchantName;
    private String criterialNo;
    private String criterialNotes;
    private String customerName;
    private String checkPartitionRegion;

    private String countryCode;
    private String provinceCode;
    private String cityCode;
    private String countyCode;

    private String countryName;
    private String provinceName;
    private String cityName;
    private String countyName;
    private List<SelectItem> storeList;

    /**
     * 波次生成的最大订单数量
     */
    private Integer waveOrderCount;
    @Create
    @Loggable
    public void init() {
        this.bigWaveFilter = waveCriterialService.initFilter(true);

        recommend = isDoRecommendOpen();
        sellerRemarkFrontFlag = isSellerRemarkFront();

        // 波次优先级初始化为3
        this.setWavePriority(Constants.WavePriority.THIRD.getValue());

        storeList = new ArrayList<>();
        partitionList = new ArrayList<SelectItem>();
        List<Partition> partitions = partitionService.getAll();
        Collections.sort(partitions, new Comparator<Partition>() {
            @Override
            public int compare(Partition o1, Partition o2) {
                return o1.getPartitionCode().compareTo(o2.getPartitionCode());
            }
        });
        for (Partition partition : partitions) {
            SelectItem selectItem = new SelectItem();
            selectItem.setValue(partition.getId());
            selectItem.setLabel(partition.getPartitionCode());
            selectItem.setDescription(partition.getDescription());
            partitionList.add(selectItem);
            if (partition.getRegion() == null || !ParamUtil.getCurrentWarehouseId().equals(partition.getRegion().getWarehouseId())) {
                checkPartitionRegion = partition.getPartitionCode();
            }
        }

        refreshFilter();

        specialLabelList = Lists.newArrayList();
        List<SpecialDoLabel> specialDoLabels = specialDoLabelService.findAvailable();
        SelectItem selectItem1 = new SelectItem();
        selectItem1.setValue("-1");
        selectItem1.setLabel("无标记订单");
        specialLabelList.add(selectItem1);
        for (SpecialDoLabel specialDoLabel : specialDoLabels) {
            SelectItem selectItem = new SelectItem();
            selectItem.setValue(specialDoLabel.getLabelCode());
            selectItem.setLabel(specialDoLabel.getLabelName());
            specialLabelList.add(selectItem);
        }

        shopInfos = Lists.newArrayList();
        List<ShopInfo> shopInfoList = shopInfoService.getAll();
        for (ShopInfo shopInfo : shopInfoList) {
            SelectItem selectItem = new SelectItem();
            selectItem.setValue(shopInfo.getId());
            selectItem.setLabel(shopInfo.getName());
            shopInfos.add(selectItem);
        }

        lineList = Lists.newArrayList();
        List<LineInfo> lineInfoLists = lineInfoService.getAll();
        for (LineInfo lineInfo : lineInfoLists) {
            SelectItem selectItem = new SelectItem();
            selectItem.setValue(lineInfo.getId());
            selectItem.setLabel(lineInfo.getLineName());
            lineList.add(selectItem);
        }


        this.getDataPage().setPageSize(DeliveryConstant.DEFAULT_PAGESIZE_FOR_BIGWAVE);

        this.bigWaveFilter.getOrderByMap().put("createdAt", "asc");
        this.bigWaveFilter.getOrderByMap().put("planShipTime", "asc");
    }

    public void receiveSelectBusinessCustomer(Long businessCustomerId) {
        BusinessCustomer businessCustomer = null;
        if (businessCustomerId == null) {
            businessCustomer = new BusinessCustomer();
        } else {
            businessCustomer = businessCustomerService.getBusinessCustomer(businessCustomerId);
        }
        this.bigWaveFilter.setBusinessCustomerId(businessCustomerId);
        this.setCustomerName(businessCustomer.getCustomerName());

    }

    public void clearBusinessCustomer() {
        this.bigWaveFilter.setBusinessCustomerId(null);
        this.setCustomerName(null);
    }

    private void refreshFilter() {
        filterList = Lists.newArrayList();
        List<WaveCriterial> waveCriterials = waveCriterialService.findLocal();
        for (WaveCriterial waveCriterial : waveCriterials) {
            SelectItem selectItem = new SelectItem();
            selectItem.setValue(waveCriterial.getId());
            selectItem.setLabel(waveCriterial.getCriterialNo());
            selectItem.setDescription(waveCriterial.getNotes());
            filterList.add(selectItem);
        }
    }

    public void receiveSelectMerchant(Long merchantId) {
        Merchant merchant = null;
        if (merchantId == null) {
            merchant = new Merchant();
        } else {
            merchant = merchantService.getMerchant(merchantId);
        }
        this.bigWaveFilter.setMerchantId(merchantId);
        this.setMerchantName(merchant.getDescrC());

    }

    public void clearSelectMerchant() {
        this.bigWaveFilter.setMerchantId(null);
        this.setMerchantName(null);
    }

    @Override
    @Loggable
    public void query() {

        // 转换地址名称
        convert2AddressName(bigWaveFilter, countryCode, provinceCode, cityCode, countyCode);

        bigWaveFilter = waveCriterialService.buildFilter(bigWaveFilter);
        // 无订单标记 调整为null
        if(Objects.equals(bigWaveFilter.getSpecialLabel(),"-1")){
            bigWaveFilter.setSpecialLabel(null);
        }
        if(bigWaveFilter.getEmptyGrade()){
            bigWaveFilter.setGoodsGrades(null);
        }
        maxDOQty = getMaxDoQty();

        this.buildOrderFilterMap(bigWaveFilter);
        bigWaveFilter.getOrderByMap().put("o.deliveryOrderHeader.channelCode","desc");
        bigWaveFilter.getOrderByMap().put("LENGTH(o.aisles) - LENGTH(REPLACE(o.aisles, ',', ''))","ASC");

        DataPage<DoHeaderDto> dataPage;
        if (recommend) {
            if (!bigWaveFilter.getDoType().equals(DoType.SELL.getValue().toString())) {
                throw new DeliveryException(DeliveryException.WAVE_RECOMMENT_NOT_SUPPORT);
            }

            Long time = System.currentTimeMillis();
            List<DoHeaderDto> dtoList = waveRecommendService.findBigWaveDoList(bigWaveFilter, maxDOQty.intValue());
            log.debug("Wave Recommend ,times: {}ms", System.currentTimeMillis() - time);

            dataPage = new DataPage<DoHeaderDto>(dtoList.size(), 1, dtoList.size(), dtoList);
        } else {
            Long time = System.currentTimeMillis();
            dataPage = waveService.findBigWaveDoList(bigWaveFilter, this.getStartIndex(), this.getPageSize());
            log.debug("Wave Query ,times: {}ms", System.currentTimeMillis() - time);
        }
        this.populateValues(dataPage);
        this.populateSelectedMap(dataPage);
    }

    private void convert2AddressName(BigWaveFilter bigWaveFilter, String countryCode, String provinceCode, String cityCode,
                                     String countyCode) {

        if (StringUtils.isNotBlank(countryCode)) {
            BaseAddressDTO addressDTO = addressService.findByAddressCode(countryCode);
            bigWaveFilter.setCountryName(addressDTO == null ? null : addressDTO.getName());
        } else {
            bigWaveFilter.setCountryName(null);
        }

        if (StringUtils.isNotBlank(provinceCode)) {
            BaseAddressDTO addressDTO = addressService.findByAddressCode(provinceCode);
            bigWaveFilter.setProvinceName(addressDTO == null ? null : addressDTO.getName());
        } else {
            bigWaveFilter.setProvinceName(null);
        }

        if (StringUtils.isNotBlank(cityCode)) {
            BaseAddressDTO addressDTO = addressService.findByAddressCode(cityCode);
            bigWaveFilter.setCityName(addressDTO == null ? null : addressDTO.getName());
        } else {
            bigWaveFilter.setCityName(null);
        }

        if (StringUtils.isNotBlank(countyCode)) {
            BaseAddressDTO addressDTO = addressService.findByAddressCode(countyCode);
            bigWaveFilter.setCountyName(addressDTO == null ? null : addressDTO.getName());
        } else {
            bigWaveFilter.setCountyName(null);
        }

    }

    //波次订单数
    private Long getMaxDoQty() {
        String[] maxQtys = NullUtil.notEmpty(SystemConfig.getConfigValue("VOLUME_WAVE_DOC_QTY", ParamUtil.getCurrentWarehouseId()), "30,30,30").split(",");
        return Long.valueOf(bigWaveFilter.getVolumeType() == null ? SystemConfig.getConfigValue("WAVE_DOC_QTY", ParamUtil.getCurrentWarehouseId()) : maxQtys[bigWaveFilter.getVolumeType()]);
    }

    private void populateSelectedMap(DataPage<DoHeaderDto> dataPage) {
        //在每次查询后清空已经选中的map，避免之前选中的记录还在map中，影响本次设置默认选中订单
        this.selectedMap.clear();
        List<DoHeaderDto> dataList = dataPage.getDataList();
        int dataListSize = dataList.size();
        int defaultDoQty = Objects.nonNull(waveOrderCount) ? waveOrderCount : this.maxDOQty.intValue();
        int size = dataListSize < defaultDoQty ? dataListSize : defaultDoQty;

        if (!ListUtil.isNullOrEmpty(dataList)) {
            for (int i = 0; i < size; i++) {
                this.selectedMap.put(dataList.get(i).getId(), Boolean.TRUE);
            }
        }
    }

    public boolean isDoRecommendOpen() {
        return SystemConfig.configIsOpen("delivery.wave.doRecommend", ParamUtil.getCurrentWarehouseId());
    }

    private boolean isSellerRemarkFront() {
        return SystemConfig.configIsOpen("delivery.wave.sellerRemarkFront", ParamUtil.getCurrentWarehouseId());
    }

    /**
     * 得到所有的国家信息
     *
     * @return 国家信息
     */
    public List<SelectItem> findCountryList() {
        List<BaseAddressDTO> baseAddressList = addressService.findAllCountryByAddressLibrary(Constants.ZERO);
        return baseAddressList.stream().map(baseAddress -> {
            SelectItem selectItem = new SelectItem();
            selectItem.setLabel(baseAddress.getName());
            selectItem.setValue(baseAddress.getAddressCode());
            return selectItem;
        }).collect(Collectors.toList());
    }

    /**
     * 国家下拉列表发生变动事件  省份联动变更
     */
    public void changeListForProvince() {
        provinces.clear();
        this.bigWaveFilter.setProvinceName(null);
        this.bigWaveFilter.setCityName(null);
        this.bigWaveFilter.setCountyName(null);
        if (StringUtils.isNotBlank(countryCode)) {
            List<BaseAddressDTO> list = addressService.findAllCountryByAddressLibrary(countryCode);
            for (BaseAddressDTO province : list) {
                SelectItem item = new SelectItem();
                item.setLabel(province.getName());
                item.setValue(province.getAddressCode());
                provinces.add(item);
            }
        }
        this.countryCode = null;
        cites.clear();
        counties.clear();
    }

    /**
     * 省份下拉列表发生变动事件
     */
    public void changeListForCity() {
        cites.clear();
        this.bigWaveFilter.setCityName(null);
        this.bigWaveFilter.setCountyName(null);
        if (StringUtils.isNotBlank(provinceCode)) {
            List<BaseAddressDTO> list = addressService.findAllCountryByAddressLibrary(provinceCode);
            for (BaseAddressDTO city : list) {
                SelectItem item = new SelectItem();
                item.setLabel(city.getName());
                item.setValue(city.getAddressCode());
                cites.add(item);
            }
        }
        this.provinceCode = null;
        counties.clear();
    }

    /**
     * 城市下拉列表发生变动事件
     */
    public void changeListForCounty() {
        counties.clear();
        this.bigWaveFilter.setCountyName(null);
        if (StringUtils.isNotBlank(cityCode)) {
            List<BaseAddressDTO> list = addressService.findAllCountryByAddressLibrary(cityCode);
            for (BaseAddressDTO county : list) {
                SelectItem item = new SelectItem();
                item.setValue(county.getAddressCode());
                item.setLabel(county.getName());
                counties.add(item);
            }
        }
        this.cityCode = null;
    }

    public void changeStoreListByChannelCode() {
        storeList.clear();
        List<SelectItem> selectItems = businessCenterComponent.changeStoreListByChannelCode(bigWaveFilter.getChannelCode());
        storeList.addAll(selectItems);
    }

    /**
     * 国家下拉列表发生变动事件
     */
    public void changeListeForNotProvince() {
        notProvinces.clear();
        this.bigWaveFilter.setNotCountry(null);
        this.bigWaveFilter.setNotProvince(null);
        this.bigWaveFilter.setNotCity(null);
        this.bigWaveFilter.setNotCounty(null);
        if (this.notCountryId != null && this.notCountryId.longValue() != -1) {
            List<Province> list = this.areaService.findProvinceByCountry(this.notCountryId);
            for (Province province : list) {
                SelectItem item = new SelectItem();
                item.setValue(province.getId());
                item.setLabel(province.getProvinceCname());
                notProvinces.add(item);
            }
        }
        this.notCountryId = null;
        notCites.clear();
        notCounties.clear();
    }

    /**
     * 省份下拉列表发生变动事件
     */
    public void changeListeForNotCity() {
        notCites.clear();
        this.bigWaveFilter.setNotCountry(null);
        this.bigWaveFilter.setNotProvince(null);
        this.bigWaveFilter.setNotCity(null);
        this.bigWaveFilter.setNotCounty(null);
        if (this.notProvinceId != null && this.notProvinceId.longValue() != -1) {
            List<City> list = this.areaService.findCityByProvince(this.notProvinceId);
            for (City city : list) {
                SelectItem item = new SelectItem();
                item.setValue(city.getId());
                item.setLabel(city.getCityCname());
                notCites.add(item);
            }
        }
        this.notProvinceId = null;
        notCounties.clear();
    }

    /**
     * 城市下拉列表发生变动事件
     */
    public void changeListeForNotCounty() {
        notCounties.clear();
        this.bigWaveFilter.setNotCountry(null);
        this.bigWaveFilter.setNotProvince(null);
        this.bigWaveFilter.setNotCity(null);
        this.bigWaveFilter.setNotCounty(null);
        if (this.notCityId != null && this.notCityId.longValue() != -1) {
            List<County> list = this.areaService.findCountyByCity(this.notCityId);
            for (County county : list) {
                SelectItem item = new SelectItem();
                item.setValue(county.getId());
                item.setLabel(county.getCountyCname());
                notCounties.add(item);
            }
        }
        this.notCityId = null;
    }


    public void changeWhenNotCounty() {
        this.bigWaveFilter.setNotCountry(null);
        this.bigWaveFilter.setNotProvince(null);
        this.bigWaveFilter.setNotCity(null);
    }

    public void saveCriterial() {
        WaveCriterial waveCriterial = new WaveCriterial();
        waveCriterial.setNotes(this.getCriterialNotes());
        waveCriterial.setCriterialNo(this.criterialNo);
        Boolean mixCarrier = Config.convert2Boolean(Config.getConfig(ConfigKeys.DELIVERY_WAVE_MIXCARRIER,
                Config.ConfigLevel.WAREHOUSE), Boolean.TRUE);
        if (!mixCarrier) {
            if (StringUtil.isNotEmpty(bigWaveFilter.getDoType())
                    && DoType.SELL.getValue().equals(bigWaveFilter.getDoType())
                    && bigWaveFilter.getCarrierId() == null) {
                throw new DeliveryException(DeliveryException.WAVE_CANNOT_MIX_CARRIER);
            }
        }
        waveCriterial.setContent(JsonUtil.objToString(this.bigWaveFilter));

        waveCriterialService.create(waveCriterial);

        refreshFilter();
        sayMessage(MESSAGE_SUCCESS);
    }


    /**
     * 团购生成波次逻辑, 根据订单一致性,满足订单一致性订单数>指定订单数量时候,生成波次
     */
    public void autoGenWave() throws Exception {
        // 转换地址名称
        convert2AddressName(bigWaveFilter, countryCode, provinceCode, cityCode, countyCode);
        // 无订单标记 调整为null
        if (Objects.equals(bigWaveFilter.getSpecialLabel(), "-1")) {
            bigWaveFilter.setSpecialLabel(null);
        }
        if(bigWaveFilter.getEmptyGrade()){
            bigWaveFilter.setGoodsGrades(null);
        }
        // 获取生成波次锁
        waveService.tryRequiredLock();
        try {
            List<String> waveNos = autoWaveGenService.genWave(bigWaveFilter, waveRuleDetail);
            if (waveNos.isEmpty()) {
                sayMessage("没有满足条件的订单!");
            } else {
                sayMessage(DeliveryConstant.GENERATE_SUCCESS, ListUtil.collection2String(waveNos, ","));
            }
        } finally {
            // 释放生成波次锁
            waveService.releaseLock();
        }
    }

    /**
     * 异步执行
     * 团购生成波次逻辑, 根据订单一致性,满足订单一致性订单数>指定订单数量时候,生成波次
     */
    public void asyncAutoGenWave() throws Exception {
        waveService.tryRequiredLock();

        if(bigWaveFilter.getEmptyGrade()){
            bigWaveFilter.setGoodsGrades(null);
        }
        // 无订单标记 调整为null
        if (Objects.equals(bigWaveFilter.getSpecialLabel(), "-1")) {
            bigWaveFilter.setSpecialLabel(null);
        }
        bigWaveFilter.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        BigWaveFilter filter=MAPPER.readValue(MAPPER.writeValueAsString(bigWaveFilter),BigWaveFilter.class);
        WaveRuleDetail ruleDetail=MAPPER.readValue(MAPPER.writeValueAsString(waveRuleDetail),WaveRuleDetail.class);

        autoWaveGenService.asyncGenWave(filter, ruleDetail,ParamUtil.getCurrentLoginName());
        QueueHolder.pushMsg("bigWave:" + bigWaveFilter.getWarehouseId(), "波次生成中... ");

    }


    public BigWaveFilter getBigWaveFilter() {
        return bigWaveFilter;
    }

    public void setBigWaveFilter(BigWaveFilter bigWaveFilter) {
        this.bigWaveFilter = bigWaveFilter;
    }

    public List<SelectItem> getProvinces() {
        return provinces;
    }

    public void setProvinces(List<SelectItem> provinces) {
        this.provinces = provinces;
    }

    public List<SelectItem> getCites() {
        return cites;
    }

    public void setCites(List<SelectItem> cites) {
        this.cites = cites;
    }

    public List<SelectItem> getCounties() {
        return counties;
    }

    public void setCounties(List<SelectItem> counties) {
        this.counties = counties;
    }

    public Long getCountryId() {
        return countryId;
    }

    public void setCountryId(Long countryId) {
        this.countryId = countryId;
    }

    public Long getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Long provinceId) {
        this.provinceId = provinceId;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public String getGeneratedWaveNum() {
        return generatedWaveNum;
    }

    public void setGeneratedWaveNum(String generatedWaveNum) {
        this.generatedWaveNum = generatedWaveNum;
    }

    public List<SelectItem> getPartitionList() {
        return partitionList;
    }

    public void setPartitionList(List<SelectItem> partitionList) {
        this.partitionList = partitionList;
    }

    @Loggable
    @Transactional
    public void generateWave() throws Exception {
        waveService.tryRequiredLock();
        try {
            if (StringUtil.isNotBlank(checkPartitionRegion)) {
                throw new MasterException(MasterException.PARTITION_REGION_ERROR, checkPartitionRegion);
            }
            List<Long> selectedDOIdList = ListUtil.convert(this.getSelectedRowList(), id -> (Long) id);

            if (ListUtil.isNullOrEmpty(selectedDOIdList)) {
                return;
            }


            deliveryOrderService.checkEmergencyOrder4GenerateWave(selectedDOIdList);

            DeliveryOrderHeader firstDo = deliveryOrderService.getDoHeaderById(selectedDOIdList.get(0));
            if (Config.isDefaultFalse(Keys.Delivery.generate_wave_by_one_do, Config.ConfigLevel.WAREHOUSE)
                    && DoType.WHOLESALE.getValue().equals(firstDo.getDoType()) && selectedDOIdList.size() > 1) {
                //批发订单一单一波次
                List<String> successDo = new ArrayList<String>();
                List<String> failDo = new ArrayList<String>();
                for (Long doId : selectedDOIdList) {
                    WaveGeneContextDTO context = new WaveGeneContextDTO();
                    context.setDoIdList(Arrays.asList(doId));
                    generateWave(context, firstDo.getVolumeType());
                    if (StringUtil.isNotBlank(generatedWaveNum)) {
                        successDo.add(deliveryOrderService.getDoHeaderById(doId).getDoNo());
                    } else {
                        failDo.add(deliveryOrderService.getDoHeaderById(doId).getDoNo());
                    }
                }
                if (CollectionUtils.isEmpty(successDo)) {
                    sayMessage(DeliveryConstant.GENERATE_FAILED);
                } else {
                    this.selectedMap.clear();
                    this.query();//生成波次成功以后，重新查询订单
                    sayMessage(DeliveryConstant.GENERATE_SUCCESS_ONE, successDo, failDo);
                }
            } else {
                WaveGeneContextDTO context = new WaveGeneContextDTO();
                context.setDoIdList(selectedDOIdList);
                context.setPointGrade(bigWaveFilter.getPointGrade());
                generateWave(context, firstDo.getVolumeType());
                if (!StringUtil.isEmpty(generatedWaveNum)) {
                    this.selectedMap.clear();
                    this.query();//生成波次成功以后，重新查询订单
                    sayMessage(DeliveryConstant.GENERATE_SUCCESS, generatedWaveNum);
                } else {
                    sayMessage(DeliveryConstant.GENERATE_FAILED);
                }
            }
        } finally {
            waveService.releaseLock();
        }
        query();
    }

    private void generateWave(WaveGeneContextDTO context, Integer volumeType) throws Exception {
        context.setMaxDOQty(Long.valueOf(waveService.getWaveDocQty(volumeType, bigWaveFlg)));
        context.setWavePriority(wavePriority);
        context.setIsRecommend(recommend);
        context.setIsSemiAuto(Boolean.FALSE);
        context.setAutoWaveType(specialDoLabelService.findWaveDetailType
                (context.getDoIdList()));
        generatedWaveNum = waveStrategy.generateWave(context);
    }

    @Loggable
    public void cleanGroup() throws Exception {
        List<Long> selectedDOIdList = ListUtil.convert(this.getSelectedRowList(), id -> (Long) id);
        if (ListUtil.isNullOrEmpty(selectedDOIdList)) {
            return;
        }
        if (selectedDOIdList.size() > 24) {
            throw new BusinessException("所选订单数过大");
        }
        for (Long doId : selectedDOIdList) {
            try {
                deliveryOrderService.cleanGroup(doId);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        this.selectedMap.clear();
        this.query();//生成波次成功以后，重新查询订单
        sayMessage(MESSAGE_SUCCESS);
    }

    public String getCriterialNo() {
        return criterialNo;
    }

    public void setCriterialNo(String criterialNo) {
        this.criterialNo = criterialNo;
    }

    public String getCriterialNotes() {
        return criterialNotes;
    }

    public void setCriterialNotes(String criterialNotes) {
        this.criterialNotes = criterialNotes;
    }

    public List<SelectItem> getShopInfos() {
        return shopInfos;
    }

    public void setShopInfos(List<SelectItem> shopInfos) {
        this.shopInfos = shopInfos;
    }

    public Integer getWavePriority() {
        return wavePriority;
    }

    public void setWavePriority(Integer wavePriority) {
        this.wavePriority = wavePriority;
    }

    public Boolean getRecommend() {
        return recommend;
    }

    public void setRecommend(Boolean recommend) {
        this.recommend = recommend;
    }

    public List<SelectItem> getSpecialLabelList() {
        return specialLabelList;
    }

    public void setSpecialLabelList(List<SelectItem> specialLabelList) {
        this.specialLabelList = specialLabelList;
    }

    public Integer getBigWaveFlg() {
        return bigWaveFlg;
    }

    public void setBigWaveFlg(Integer bigWaveFlg) {
        this.bigWaveFlg = bigWaveFlg;
    }

    public Integer getVolumeDoQty() {
        return volumeDoQty;
    }

    public void setVolumeDoQty(Integer volumeDoQty) {
        this.volumeDoQty = volumeDoQty;
    }

    public List<SelectItem> getNotProvinces() {
        return notProvinces;
    }

    public void setNotProvinces(List<SelectItem> notProvinces) {
        this.notProvinces = notProvinces;
    }

    public List<SelectItem> getNotCites() {
        return notCites;
    }

    public void setNotCites(List<SelectItem> notCites) {
        this.notCites = notCites;
    }

    public List<SelectItem> getNotCounties() {
        return notCounties;
    }

    public void setNotCounties(List<SelectItem> notCounties) {
        this.notCounties = notCounties;
    }

    public Long getNotCountryId() {
        return notCountryId;
    }

    public void setNotCountryId(Long notCountryId) {
        this.notCountryId = notCountryId;
    }

    public Long getNotProvinceId() {
        return notProvinceId;
    }

    public void setNotProvinceId(Long notProvinceId) {
        this.notProvinceId = notProvinceId;
    }

    public Long getNotCityId() {
        return notCityId;
    }

    public void setNotCityId(Long notCityId) {
        this.notCityId = notCityId;
    }

    public Boolean getSellerRemarkFrontFlag() {
        return sellerRemarkFrontFlag;
    }

    public void setSellerRemarkFrontFlag(Boolean sellerRemarkFrontFlag) {
        this.sellerRemarkFrontFlag = sellerRemarkFrontFlag;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public List<SelectItem> getFilterList() {
        return filterList;
    }

    public void setFilterList(List<SelectItem> filterList) {
        this.filterList = filterList;
    }

    public WaveRuleDetail getWaveRuleDetail() {
        return waveRuleDetail;
    }

    public void setWaveRuleDetail(WaveRuleDetail waveRuleDetail) {
        this.waveRuleDetail = waveRuleDetail;
    }

    public List<SelectItem> getLineList() {
        return lineList;
    }

    public void setLineList(List<SelectItem> lineList) {
        this.lineList = lineList;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Integer getWaveOrderCount() {
        return waveOrderCount;
    }

    public void setWaveOrderCount(Integer waveOrderCount) {
        this.waveOrderCount = waveOrderCount;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCountyCode() {
        return countyCode;
    }

    public void setCountyCode(String countyCode) {
        this.countyCode = countyCode;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCountyName() {
        return countyName;
    }

    public void setCountyName(String countyName) {
        this.countyName = countyName;
    }

    public List<SelectItem> getStoreList() {
        return storeList;
    }

    public void setStoreList(List<SelectItem> storeList) {
        this.storeList = storeList;
    }
}
