package com.daxia.wms.delivery.invoice.action;

import java.util.ArrayList;
import java.util.List;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.json.JSONArray;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.filter.InvoiceFilter;
import com.daxia.wms.delivery.invoice.service.InvoiceService;
import com.daxia.wms.delivery.print.service.PrintInvoiceNewService;

@Name("com.daxia.wms.delivery.doInvoiceListAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class DoInvoiceListAction extends PagedListBean<InvoiceHeader> {
    
    private static final long serialVersionUID = 5817939903123108863L;

    private Long invoiceHeaderId;
    
    private String printData = "[]";
    
    private DeliveryOrderHeader deliveryOrderHeader;

    @In
    InvoiceService invoiceService;
    
    @In(create = true)
    private PrintInvoiceNewService printInvoiceNewService;

    public void initPage() {
        this.query();
    }

    @Override
    public void query() {
        InvoiceFilter invoiceFilter = new InvoiceFilter();
        invoiceFilter.setRefInvoiceId(invoiceHeaderId);
        
        DataPage<InvoiceHeader> dataPage = invoiceService.findInvoiceByFilter(invoiceFilter, getStartIndex(), getPageSize());
        this.populateValues(dataPage);
        
        List<InvoiceHeader> invoiceList = dataPage.getDataList();
        if(!ListUtil.isNullOrEmpty(invoiceList)){
            setDeliveryOrderHeader(invoiceList.get(0).getDeliveryOrderHeader());
        }
    }
    
    /**
     * 发票打印
     */
    public void print(){
        this.printData = "[]";

        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }

        List<String> dataList = printInvoiceNewService.print(ids, null);

        this.printData = new JSONArray(dataList).toString();
    }

    public Long getInvoiceHeaderId() {
        return invoiceHeaderId;
    }

    public void setInvoiceHeaderId(Long invoiceHeaderId) {
        this.invoiceHeaderId = invoiceHeaderId;
    }

    public String getPrintData() {
        return printData;
    }

    public void setPrintData(String printData) {
        this.printData = printData;
    }

    public DeliveryOrderHeader getDeliveryOrderHeader() {
        return deliveryOrderHeader;
    }

    public void setDeliveryOrderHeader(DeliveryOrderHeader deliveryOrderHeader) {
        this.deliveryOrderHeader = deliveryOrderHeader;
    }
}