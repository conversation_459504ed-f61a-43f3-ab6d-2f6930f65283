package com.daxia.wms.delivery.recheck.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.constants.Constants.YesNo;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.delivery.deliveryorder.service.DoWaveExService;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.master.entity.ShopInfo;
import com.daxia.wms.master.service.ShopInfoService;
import com.daxia.wms.waybill.yunda.dto.*;
import com.daxia.wms.waybill.yunda.service.YundaWaybillService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by szy on 2016/8/18.
 */
@Name("yundaCartonNoGenerateService")
@lombok.extern.slf4j.Slf4j
public class YundaCartonNoGenerateServiceImpl implements CartonNoGenerateService {

    @In(create = true)
    private YundaWaybillService yundaWaybillService;

    @In
    private ShopInfoService shopInfoService;

    @In
    private SequenceGeneratorService sequenceGeneratorService;

    @In
    private DoWaveExService doWaveExService;


    @Override
    public void generatorCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {

        DoWaveEx doWaveEx = doWaveExService.findByDoHeaderId(doHeader.getId());

        CreateOrderRequest request = new CreateOrderRequest();
        OrderInfo info = new OrderInfo();
        request.setOrder(info);


        //生成一个箱号作为业务单号
        String txLogisticId = doHeader.getDoNo();
        cartonHeader.setTrackingNo(txLogisticId);

        info.setOrderSerialNo(doHeader.getDoNo());
        info.setKhddh(txLogisticId);
        BigDecimal receivable = doHeader.getReceivable();
        if (receivable.compareTo(BigDecimal.ZERO) > 0) {
            info.setOrderType("cod");
        } else {
            info.setOrderType("common");
        }

        SenderInfo senderInfo = new SenderInfo();
        ShopInfo shopInfo = null;
        if (doHeader.getShopId() != null) {
            shopInfo = shopInfoService.get(doHeader.getShopId());
        }
        if (Constants.YesNo.YES.getValue().equals(doHeader.getHaveCfy())) {
            senderInfo.setName(shopInfo != null ? shopInfo.getNickName() : SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_NAME_CFY, ParamUtil.getCurrentWarehouseId()));
            senderInfo.setAddress(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_ADDRESS_CFY, ParamUtil.getCurrentWarehouseId()));
            senderInfo.setPhone(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_PHONE_CFY, ParamUtil.getCurrentWarehouseId()));
            senderInfo.setCity(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_CITY_CFY, ParamUtil.getCurrentWarehouseId()));
        } else {
            senderInfo.setName(shopInfo != null ? shopInfo.getNickName() : SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_NAME, ParamUtil.getCurrentWarehouseId()));
            senderInfo.setAddress(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_ADDRESS, ParamUtil.getCurrentWarehouseId()));
            String sendPhone = NullUtil.notEmpty(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_PHONE + "_" + shopInfo.getId(), ParamUtil.getCurrentWarehouseId()),
                    SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_PHONE, ParamUtil.getCurrentWarehouseId()));
            senderInfo.setPhone(sendPhone);
            senderInfo.setCity(SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_CITY, ParamUtil.getCurrentWarehouseId()));
        }
        String sendName = SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_SEND_NAME_COVER, ParamUtil.getCurrentWarehouseId());
        if (StringUtil.isNotBlank(sendName)) {
            senderInfo.setName(sendName);
        }
        info.setSender(senderInfo);

        ReceiverInfo receiverInfo = new ReceiverInfo();
        receiverInfo.setName(doHeader.getConsigneeName());
        String cityInfo = StringUtil.notNullString(doHeader.getProvinceName()) + StringUtil.notNullString(doHeader.getCityName()) + StringUtil.notNullString(doHeader.getCountyName());
        receiverInfo.setAddress(cityInfo+doHeader.getAddress());
        receiverInfo.setCity(String.valueOf(doHeader.getProvinceName()) + "," + doHeader.getCityName() + "," + doHeader.getCountyName());
        receiverInfo.setPostcode(doHeader.getPostCode());
        receiverInfo.setPhone(DoUtil.decryptPhone(doHeader.getTelephone()));
        receiverInfo.setMobile(DoUtil.decryptPhone(doHeader.getMobile()));
        info.setReceiver(receiverInfo);
        info.setCollectionValue(receivable);

        List<ProductInfo> items = new ArrayList<ProductInfo>();
        ProductInfo productInfo = new ProductInfo();
        productInfo.setName("默认商品");
        productInfo.setNumber(1);
        items.add(productInfo);
        info.setItems(items);

        try {
            CreateOrderResponse response = yundaWaybillService.fetchWayBill(request, doHeader.getWarehouseId(), doHeader.getCarrierId());
            if(!YesNo.YES.getValue().equals(response.getResponse().getStatus())){
                throw new BusinessException(response.getResponse().getMsg());
            }
            String wayBillNo = response.getResponse().getMailNo();
            cartonHeader.setWayBill(wayBillNo);
            cartonHeader.setCartonNo(wayBillNo);

            String pdfInfo = response.getResponse().getPdfInfo();
            JSONArray pdfInfoObject = JSON.parseArray(pdfInfo);
            JSONObject obj =  pdfInfoObject.getJSONArray(0).getJSONObject(0);

            if(obj.containsKey("package_wd")){
                doWaveEx.setTrackingNo(StringUtil.unicode2string(StringUtil.notNullString(obj.get("package_wd"))));
            }

            if(obj.containsKey("package_wdjc")){
                doWaveEx.setDestinationCode(StringUtil.unicode2string(StringUtil.notNullString(obj.get("package_wdjc"))));
            }

            if(obj.containsKey("position")){
                doWaveEx.setOriginCode(StringUtil.unicode2string(StringUtil.notNullString(obj.get("position"))));
            }
            doWaveExService.update(doWaveEx);

        } catch (BusinessException e) {
            throw new DeliveryException("从韵达获取运单号出错!"+e.getMessage());
        } catch (Exception e) {
            throw new DeliveryException("从韵达获取运单号出错!");
        }
    }

    public static void main(String[] args) {
        /**String s = "\\u96c6\\u5305\\u5730\\uff1a\\u4e0a\\u6d77zz";
        String s1 = "\\u6caa\\u9752\\u6d66";
        String s2 = "2000";
        System.out.println(StringUtil.unicode2string(s));
        System.out.println(StringUtil.unicode2string(s1));
        System.out.println(StringUtil.unicode2string(s2)); **/
    }

}
