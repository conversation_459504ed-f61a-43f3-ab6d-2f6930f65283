
package com.daxia.wms.delivery.deliveryorder.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * 发货日志信息DTO
 *
 */
@lombok.extern.slf4j.Slf4j
public class DoTimeInfoDto implements Serializable{

	private static final long serialVersionUID = 8316182654323875023L;

	private Long id;
	/** 
	 * DO单号 
	 */
	private String doNo;

	/** 
	 * DO创建时间 
	 */
	private Date doCreateTime; 
	/** 
	 * DO导入时间 
	 */
	private Date createTime; 
	
	/**
	 * 分配时间
	 */
    private Date allocTime;
    
	/**
	 *  进入波次时间
	 */
	private Date waveTime; 
	
	/**
	 * 拣货开始时间
	 */
    private Date pickStartTime;
    /**
     * 拣货结束时间
     */
    private Date pickEndTime;
    
    /**
     * 分拣时间
     */
    private Date sortTime;
    
    /**
     * 装箱开始时间
     */
    private Date packStartTime;
    
    /**
     * 装箱结束时间
     */
    private Date packEndTime;
    
    /** 
     * 出库交接时间 
     */
	private Date handoverTime;
    
	/** 
	 * 出库时间 
	 */
	private Date shipTime;

	/**
	 * 称重时间
	 */
	private Date weighTime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}

	public Date getDoCreateTime() {
		return doCreateTime;
	}

	public void setDoCreateTime(Date doCreateTime) {
		this.doCreateTime = doCreateTime;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getAllocTime() {
		return allocTime;
	}

	public void setAllocTime(Date allocTime) {
		this.allocTime = allocTime;
	}

	public Date getWaveTime() {
		return waveTime;
	}

	public void setWaveTime(Date waveTime) {
		this.waveTime = waveTime;
	}

	public Date getPickStartTime() {
		return pickStartTime;
	}

	public void setPickStartTime(Date pickStartTime) {
		this.pickStartTime = pickStartTime;
	}

	public Date getPickEndTime() {
		return pickEndTime;
	}

	public void setPickEndTime(Date pickEndTime) {
		this.pickEndTime = pickEndTime;
	}

	public Date getSortTime() {
		return sortTime;
	}

	public void setSortTime(Date sortTime) {
		this.sortTime = sortTime;
	}

	public Date getPackStartTime() {
		return packStartTime;
	}

	public void setPackStartTime(Date packStartTime) {
		this.packStartTime = packStartTime;
	}

	public Date getPackEndTime() {
		return packEndTime;
	}

	public void setPackEndTime(Date packEndTime) {
		this.packEndTime = packEndTime;
	}

	public Date getHandoverTime() {
		return handoverTime;
	}

	public void setHandoverTime(Date handoverTime) {
		this.handoverTime = handoverTime;
	}

	public Date getShipTime() {
		return shipTime;
	}

	public void setShipTime(Date shipTime) {
		this.shipTime = shipTime;
	}

	public Date getWeighTime() {
		return weighTime;
	}

	public void setWeighTime(Date weighTime) {
		this.weighTime = weighTime;
	}
}
