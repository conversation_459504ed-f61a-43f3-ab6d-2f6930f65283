package com.daxia.wms.delivery.invoice.action;

import java.io.Serializable;
import java.util.List;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.ActionBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceDetail;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.service.InvoiceService;

/**
 * 发票开票明细页面action
 */
@Name("com.daxia.wms.delivery.doInvoiceDetailAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class DoInvoiceDetailAction extends ActionBean implements Serializable{

    /**
     * 
     */
    private static final long serialVersionUID = 1L;

    @In
    private InvoiceService invoiceService;

    private Long invoiceHeaderId;
    private DeliveryOrderHeader doHeader;
    private List<InvoiceDetail> invoiceDetailList;
    
    /**
     * 初始化，页面弹出时调用
     */
    public void initPage() {
        InvoiceHeader invoiceHeader = invoiceService.getInvoiceById(invoiceHeaderId);
        this.doHeader = invoiceHeader.getDeliveryOrderHeader();
        this.setInvoiceDetailList(invoiceService
				.findInvoiceDetailsByHeaderId(invoiceHeader.getId()));
    }
    
    public Long getInvoiceHeaderId() {
        return invoiceHeaderId;
    }
    
    public void setInvoiceHeaderId(Long invoiceHeaderId) {
        this.invoiceHeaderId = invoiceHeaderId;
    }

    public List<InvoiceDetail> getInvoiceDetailList() {
        return invoiceDetailList;
    }

    public void setInvoiceDetailList(List<InvoiceDetail> invoiceDetailList) {
        this.invoiceDetailList = invoiceDetailList;
    }

    public DeliveryOrderHeader getDoHeader() {
        return doHeader;
    }
    
    public void setDoHeader(DeliveryOrderHeader doHeader) {
        this.doHeader = doHeader;
    }
}
