package com.daxia.wms.delivery.deliveryorder.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.daxia.framework.common.entity.WhBaseEntity;
import org.hibernate.annotations.BatchSize;

/**
 * DO打印扩展表，DO导入时同时写入
 */
@Entity
@Table(name = "doc_do_header_print_ex")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class DoHeaderPrintEx extends WhBaseEntity {

	//DO 头id
	private Long doHeaderId;
	//是否打印发货清单
	private Integer isPrintOrderList;
	//冻结订单打印标识
	private Integer isHoldLabelPrinted;
	
	@Id
	@Column(name = "DO_HEADER_ID")
	public Long getDoHeaderId() {
		return doHeaderId;
	}
	public void setDoHeaderId(Long doHeaderId) {
		this.doHeaderId = doHeaderId;
	}
	@Column(name = "IS_PRINT_ORDERLIST")
	public Integer getIsPrintOrderList() {
		return isPrintOrderList;
	}
	public void setIsPrintOrderList(Integer isPrintOrderList) {
		this.isPrintOrderList = isPrintOrderList;
	}
	@Column(name = "IS_HOLD_LABEL_PRINTED")
	public Integer getIsHoldLabelPrinted() {
		return isHoldLabelPrinted;
	}
	public void setIsHoldLabelPrinted(Integer isHoldLabelPrinted) {
		this.isHoldLabelPrinted = isHoldLabelPrinted;
	}
}