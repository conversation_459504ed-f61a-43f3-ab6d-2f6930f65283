package com.daxia.wms.delivery.container.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.container.entity.PktContainerDetail;
import org.apache.commons.beanutils.DynaBean;
import org.apache.commons.beanutils.LazyDynaBean;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 容器使用日志DAO
 */
@Name("com.daxia.wms.delivery.pktContainerDetailDAO")
@lombok.extern.slf4j.Slf4j
public class PktContainerDetailDAO extends HibernateBaseDAO<PktContainerDetail, Long> {

    /**
     * 按容器头id清除明细
     * @param headerId
     */
    public void delByHeaderId(Long headerId) {
        String sql = " header_id = :headerId and WAREHOUSE_ID = :warehouseId ";
        Query query = this.createDeleteSqlQuery(sql);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("headerId", headerId);
        query.executeUpdate();
    }

    public Map<String, DynaBean> findPackInforByContainer(String containerNo, String doNo) {
        String sql = "select cd.sku_id, sum(cd.qty_unit),sum(cd.qty),att.lotatt05 FROM doc_pkt_container_detail cd, doc_pkt_container_header ch,stock_batch_att att WHERE " +
                "cd.header_id = ch.id AND ch.container_no = :containerNo AND ch.doc_no =:doNo AND " +
                "ch.warehouse_id =:warehouseId AND ch.is_deleted = 0 AND cd.warehouse_id =:warehouseId AND cd.is_deleted = 0 " +
                " and att.id = cd.lot_id " +
                "GROUP BY sku_id,att.lotatt05";
        Query query = createSQLQuery(sql);
        query.setParameter("containerNo", containerNo);
        query.setParameter("doNo", doNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        List<Object[]> result = (List<Object[]>) query.list();
        if (ListUtil.isNullOrEmpty(result)) {
            return null;
        }
        Map<String, DynaBean> map = new HashMap<String, DynaBean>();
        for (Object[] p : result) {
            DynaBean bean = new LazyDynaBean();
            bean.set("qtyUnit", (BigDecimal) p[1]);
            bean.set("packQty", ((BigDecimal) p[2]).divide((BigDecimal) p[1]));
            map.put(p[0] + "_" + StringUtil.notNullString(p[3]), bean);
        }
        return map;
    }

    public List<String> findChuteListByHeaderId(Long id) {
        String sql = "SELECT DISTINCT(loc.chute) " +
                " FROM doc_pkt_container_detail pd " +
                " INNER JOIN md_location loc ON loc.id = pd.loc_id " +
                " INNER JOIN tsk_pick pt on pt.id = pd.task_id " +
                " WHERE pd.header_id = :headerId and pd.warehouse_id = :warehouseId " +
                " AND pt.status = :release ";
        SQLQuery query = createSQLQuery(sql);
        query.setParameter("headerId", id);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("release", Constants.TaskStatus.RELEASED.getValue());
        return query.list();
    }
}
