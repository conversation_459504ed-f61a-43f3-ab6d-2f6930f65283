package com.daxia.wms.delivery.sort.action;

import java.util.ArrayList;
import java.util.List;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.ActionBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;

import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.sort.service.SortingService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.Constants;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.master.entity.Sku;
import com.daxia.wms.master.service.SkuService;

@Name("com.daxia.wms.delivery.dmBySkuAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class DmBySkuAction extends ActionBean {
    private static final long serialVersionUID = 3192852995960844920L;
    
    private Long waveId;
    private String waveNo;
    private String skuCode;
    private Long skuId;
    private WaveHeader waveHeader;
    private List<Sku> skuList = new ArrayList<Sku>();
    private boolean initPageFlag;
    private boolean multiSkuFlag;
    private String showFlag;
    
    
    @In
    private SkuService skuService;
    @In
    private WaveService waveService;
    @In
    private SortingService sortingService;
    @In
    private ExpFacadeService expFacadeService;
    
    public void initialize() {
        if (!initPageFlag) {
            getAndCheckWave();
            initPageFlag = true;
            showFlag = "1";
        }
    }
    
    public void queryOrDoDm() throws Exception {
        multiSkuFlag = false;
        skuId = null;
        getAndCheckWave();
        sortingService.checkSkuInWave(waveId, skuCode);
        List<Long> skuIds = sortingService.querySkuByBarcodeInWave(waveId, skuCode);
        if (skuIds.isEmpty()) {
            skuId = this.sortingService.findSkyByCodeAndWaveId(waveId, skuCode);
        } else if (skuIds.size()== 1) {
            skuId = skuIds.get(0);
        } else {
            // 一码多品
            multiSkuFlag = true;
            if (ListUtil.isNotEmpty(skuIds)) {
                this.skuList = skuService.querySkuList(skuIds);
            }
            return;
        }
        if (skuId == null) {
            throw new DeliveryException(DeliveryException.PRODUCT_ALREADY_SORTING);
        }
        dmBySku();
    }
    
    private void getAndCheckWave() {
        if (StringUtil.isEmpty(waveNo)) {
            throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
        }
        this.waveHeader = waveService.queryWaveByNo(waveNo);
        if (this.waveHeader == null) {
            throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
        }
        if (StringUtil.isNotIn(waveHeader.getWaveStatus(), Constants.WaveStatus.ALLPICKED.getValue(), 
                Constants.WaveStatus.PARTSORTED.getValue())) {
            throw new DeliveryException(DeliveryException.DO_IS_NOT_PICKED_TO_SORTING);
        }
        this.waveId = waveHeader.getId();
    }
    
    public void dmBySku() {
        getAndCheckWave();
        Long doId = sortingService.dmBySku(waveHeader.getId(), skuId);
        
        //最后如果破损是最后1个unit 需要释放分拣柜和波次关系
	    sortingService.dealWaveLastUnit(waveHeader.getId());
	    
        if (doId != null) {
            expFacadeService.sendDoReleaseOrHold2Scs(doId);
        }
        this.sayMessage(MESSAGE_SUCCESS);
    }

    
    public Long getWaveId() {
        return waveId;
    }

    
    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }

    
    public String getWaveNo() {
        return waveNo;
    }

    
    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    
    public String getSkuCode() {
        return skuCode;
    }

    
    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    
    public Long getSkuId() {
        return skuId;
    }

    
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    
    public WaveHeader getWaveHeader() {
        return waveHeader;
    }

    
    public void setWaveHeader(WaveHeader waveHeader) {
        this.waveHeader = waveHeader;
    }

    
    public List<Sku> getSkuList() {
        return skuList;
    }

    
    public void setSkuList(List<Sku> skuList) {
        this.skuList = skuList;
    }

    
    
    public boolean isInitPageFlag() {
        return initPageFlag;
    }

    
    public void setInitPageFlag(boolean initPageFlag) {
        this.initPageFlag = initPageFlag;
    }

    public boolean isMultiSkuFlag() {
        return multiSkuFlag;
    }

    
    public void setMultiSkuFlag(boolean multiSkuFlag) {
        this.multiSkuFlag = multiSkuFlag;
    }

    
    public String getShowFlag() {
        return showFlag;
    }

    
    public void setShowFlag(String showFlag) {
        this.showFlag = showFlag;
    }
}
