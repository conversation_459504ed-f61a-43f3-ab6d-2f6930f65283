package com.daxia.wms.delivery.container.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "doc_pkt_container_header")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " is_deleted = 0 ")
@SQLDelete(sql = "update doc_pkt_container_header set is_deleted = 1 where id = ? and version = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class PktContainerHeader extends WhBaseEntity {
    
    private Long id;
    
    /**
     * 容器号
     */
    private String containerNo;
    
    /**
     * 与容器关联单据号
     */
    private String docNo;
    
    /**
     * 与容器关联单据类型
     */
    private String docType;

    /**
     * 注册标识，默认0，容器注册后为1
     */
    private Integer registerFlag;

    /**
     * 区域ID
     */
    private Long regionId;
    
    private Integer priority;
    
    private Date estDoFinishTime;
    
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID")
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    @Column(name = "priority")
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    @Column(name = "est_do_finish_time")
    public Date getEstDoFinishTime() {
        return estDoFinishTime;
    }
    
    public void setEstDoFinishTime(Date estDoFinishTime) {
        this.estDoFinishTime = estDoFinishTime;
    }
    
    @Column(name = "container_no")
    public String getContainerNo() {
        return containerNo;
    }
    
    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }
    
    @Column(name = "doc_no")
    public String getDocNo() {
        return docNo;
    }
    
    public void setDocNo(String docNo) {
        this.docNo = docNo;
    }
    
    @Column(name = "doc_type")
    public String getDocType() {
        return docType;
    }
    
    public void setDocType(String docType) {
        this.docType = docType;
    }

    @Column(name = "register_flag")
    public Integer getRegisterFlag() {
        return registerFlag;
    }

    public void setRegisterFlag(Integer registerFlag) {
        this.registerFlag = registerFlag;
    }

    @Column(name = "region_id")
    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }
}
