package com.daxia.wms.delivery.load.service.impl;

import java.util.List;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.load.dao.TOCrossDockDetailDAO;
import com.daxia.wms.delivery.load.dao.TOCrossDockHeaderDAO;
import com.daxia.wms.delivery.load.entity.TOCrossDockDetail;
import com.daxia.wms.delivery.load.entity.TOCrossDockHeader;
import com.daxia.wms.delivery.load.filter.CrossDockHeaderFilter;
import com.daxia.wms.delivery.load.service.TOCrossDockService;

/**
 * crdockHeader 的业务操作接口实现类
 */
@Name("com.daxia.wms.delivery.toCrossDockService")
@lombok.extern.slf4j.Slf4j
public class TOCrossDockServiceImpl implements TOCrossDockService {

    @In
    private TOCrossDockHeaderDAO toCrossDockHeaderDAO;

    @In
    private TOCrossDockDetailDAO toCrossDockDetailDAO;
    

    /**
     * 以分页面方式查询符合条件的CrdockHeader
     * @param filter 查询条件过滤器
     * @param startIndex 当前页
     * @param pageSize 每页显示条数
     * return 符合条件的CrdockHeader, 返回值不为空
     */
    @Override
    public DataPage<TOCrossDockHeader> findCrossDockHeaders(CrossDockHeaderFilter filter, int startIndex, int pageSize) {
        return toCrossDockHeaderDAO.findRangeByFilter(filter, startIndex, pageSize);
    }

    /**
     * 根据ID查询CrossDockHeader
     * @param id The id of CrossDockHeader
     * return ID对应的CrossDockHeader
     */
    @Override
    public TOCrossDockHeader get(Long id) {
        return toCrossDockHeaderDAO.get(id);
    }
    /**
     * 根据ID查询CrossDockDetail
     * @param id The id of CrossDockHeader
     * return ID对应的CrossDockHeader
     */
    @Override
    public TOCrossDockDetail getCrossDockDetail(Long id) {
        return toCrossDockDetailDAO.get(id);
    }
    /**
     * 根据ID序列查询所有CrossDockHeader
     * @param ids The id collection of CrossDockHeader
     * return 序列ID所对应的所有CrossDockHeader
     */
    public List<TOCrossDockHeader> get(List<Long> ids) {
        return toCrossDockHeaderDAO.getByKeys(ids);
    }

    /**
     * 通过filter查询
     * @param crossDockHeaderFilter 查询filter
     * @return 页面显示datalist
     */
    @Override
    public List<TOCrossDockHeader> query(CrossDockHeaderFilter crossDockHeaderFilter) {
        return toCrossDockHeaderDAO.findByFilter(crossDockHeaderFilter);
    }
}
