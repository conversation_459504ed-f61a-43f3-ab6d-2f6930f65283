package com.daxia.wms.delivery.deliveryorder.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.master.entity.*;
import com.daxia.wms.stock.stock.entity.StockBatchAtt;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "doc_other_do_detail")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = "IS_DELETED = 0 ")
@lombok.extern.slf4j.Slf4j
public class OtherDoDetail extends WhBaseEntity {
    private static final long serialVersionUID = 1998028554970952175L;
    /**
     * 主键
     */
    private Long id;
    /**
     * ASN头信息Id
     */
    private Long doHeaderId;
    /**
     * 行状态
     */
    private String lineStatus;
    /**
     * Sku ID
     */
    private Long skuId;
    /**
     * Lot ID
     */
    private Long stkLpnId;
    /**
     * Lot ID
     */
    private Long lotId;
    /**
     * Loc ID
     */
    private Long locId;
    /**
     * Loc ID
     */
    private String lpnNo;

    /**
     * 库存ID
     */
    private Long stkAllocatingId;
    /**
     * 库存ID
     */
    private Long stkAllocatedId;
    /**
     * 库存ID
     */
    private Long stkPickedId;

    /**
     * 预期数量
     */
    private BigDecimal exptQty;

    /**
     * 收货数量
     */
    private BigDecimal shipQty;

    /**
     * 备注
     */
    private String notes;
    /**
     * doHeader实体
     */
    private OtherDoHeader otherDoHeader;

    private Sku sku;

    private Location location;

    private StockBatchAtt stockBatchAtt;

    private Integer isDeleted;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "autoIdGenerator")
    @GenericGenerator(name = "autoIdGenerator", strategy = "com.daxia.framework.common.dao.AutoIdentityGenerator")
    @Column(name = "ID")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "DO_HEADER_ID", insertable = false, updatable = false)
    public Long getDoHeaderId() {
        return doHeaderId;
    }

    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }

    @Column(name = "LINE_STATUS")
    public String getLineStatus() {
        return lineStatus;
    }

    public void setLineStatus(String lineStatus) {
        this.lineStatus = lineStatus;
    }

    @Column(name = "SKU_ID")
    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    @Column(name = "STK_LPN_ID")
    public Long getStkLpnId() {
        return stkLpnId;
    }

    public void setStkLpnId(Long stkLpnId) {
        this.stkLpnId = stkLpnId;
    }

    @Column(name = "LOT_ID")
    public Long getLotId() {
        return lotId;
    }

    public void setLotId(Long lotId) {
        this.lotId = lotId;
    }

    @Column(name = "LOC_ID")
    public Long getLocId() {
        return locId;
    }

    public void setLocId(Long locId) {
        this.locId = locId;
    }

    @Column(name = "LPN_NO")
    public String getLpnNo() {
        return lpnNo;
    }

    public void setLpnNo(String lpnNo) {
        this.lpnNo = lpnNo;
    }

    @Column(name = "STK_ALLOCATING_ID")
    public Long getStkAllocatingId() {
        return stkAllocatingId;
    }

    public void setStkAllocatingId(Long stkAllocatingId) {
        this.stkAllocatingId = stkAllocatingId;
    }

    @Column(name = "STK_ALLOCATED_ID")
    public Long getStkAllocatedId() {
        return stkAllocatedId;
    }

    public void setStkAllocatedId(Long stkAllocatedId) {
        this.stkAllocatedId = stkAllocatedId;
    }

    @Column(name = "STK_PICKED_ID")
    public Long getStkPickedId() {
        return stkPickedId;
    }

    public void setStkPickedId(Long stkPickedId) {
        this.stkPickedId = stkPickedId;
    }

    @Column(name = "EXPT_QTY")
    public BigDecimal getExptQty() {
        return exptQty;
    }

    public void setExptQty(BigDecimal exptQty) {
        this.exptQty = exptQty;
    }

    @Column(name = "SHIP_QTY")
    public BigDecimal getShipQty() {
        return shipQty;
    }

    public void setShipQty(BigDecimal shipQty) {
        this.shipQty = shipQty;
    }

    @Column(name = "NOTES")
    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DO_HEADER_ID")
    @Where(clause = " IS_DELETED = 0 ")
    public OtherDoHeader getOtherDoHeader() {
        return otherDoHeader;
    }

    public void setOtherDoHeader(OtherDoHeader otherDoHeader) {
        this.otherDoHeader = otherDoHeader;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SKU_ID", insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public Sku getSku() {
        return sku;
    }

    public void setSku(Sku sku) {
        this.sku = sku;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "LOC_ID", insertable = false, updatable = false)
    public Location getLocation() {
        return location;
    }

    public void setLocation(Location location) {
        this.location = location;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "LOT_ID", insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public StockBatchAtt getStockBatchAtt() {
        return stockBatchAtt;
    }

    public void setStockBatchAtt(StockBatchAtt stockBatchAtt) {
        this.stockBatchAtt = stockBatchAtt;
    }

    @Column(name = "IS_DELETED")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}
