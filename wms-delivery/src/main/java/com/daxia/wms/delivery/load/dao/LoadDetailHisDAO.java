package com.daxia.wms.delivery.load.dao;

import java.util.Date;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.load.entity.LoadDetailHis;

@Name("com.daxia.wms.delivery.loadDetailHisDAO")
@lombok.extern.slf4j.Slf4j
public class LoadDetailHisDAO extends HibernateBaseDAO<LoadDetailHis, Long> {

    private static final long serialVersionUID = -8261094672922534017L;

    public Date getHandoverTime(Long id) {
        String hql = "select ld.createdAt from LoadDetailHis ld where ld.doHeaderId = :doHeaderId " +
        		" and ld.warehouseId = :warehouseId order by ld.createdAt desc ";
        Query query = createQuery(hql);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setLong("doHeaderId", id);
        query.setMaxResults(1);
        return (Date) query.uniqueResult();
    }
}
