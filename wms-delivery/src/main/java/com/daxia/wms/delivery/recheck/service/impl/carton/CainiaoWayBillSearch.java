package com.daxia.wms.delivery.recheck.service.impl.carton;

import com.daxia.framework.common.cfg.CainiaoWaybillCfg;
import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.recheck.dto.CainiaoWaybillDTO;
import com.daxia.wms.master.entity.CarrierCainiaoEx;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.CarrierCainiaoExService;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.google.common.collect.Lists;
import com.taobao.api.ApiException;
import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.request.CainiaoWaybillIiSearchRequest;
import com.taobao.api.response.CainiaoWaybillIiSearchResponse;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.log.Log;

import java.util.List;

@Name("cainiaoWayBillSearch")
@lombok.extern.slf4j.Slf4j
public class CainiaoWayBillSearch extends CainiaoWayBillBase {
    @In
    WarehouseCarrierService warehouseCarrierService;
    @In
    CarrierCainiaoExService carrierCainiaoExService;


    public List<CainiaoWaybillDTO> reqeust(Long carrierId) {
        CainiaoWaybillIiSearchRequest cainiaoWaybillIiSearchRequest = new CainiaoWaybillIiSearchRequest();
    
        WarehouseCarrier warehouseCarrier = loadWarehouseCarrier(carrierId);
    
        CarrierCainiaoEx carrierCainiaoEx = carrierCainiaoExService.getByCarrier(carrierId);
        if (carrierCainiaoEx != null) {
            cainiaoWaybillIiSearchRequest.setCpCode(carrierCainiaoEx.getCpCode());
        }
    
        TaobaoClient client = new DefaultTaobaoClient(Config.getFmJson(Keys.Master.waybill_cainiao_cfg, Config.ConfigLevel.WAREHOUSE, CainiaoWaybillCfg.serverUrl), warehouseCarrier.getAppKey(), warehouseCarrier.getAppSecret());
        try {
            CainiaoWaybillIiSearchResponse rsp = client.execute(cainiaoWaybillIiSearchRequest, warehouseCarrier.getAppToken());

            if (!rsp.isSuccess()) {
                log.error("Cainiao search error, request:" + cainiaoWaybillIiSearchRequest + ", response: " + rsp.getBody());
                throw new DeliveryException(DeliveryException.WAYBILL_CAINIO_GET_ERROR, rsp.getMsg() + ", " + rsp.getSubCode());
            }

            return genCainiaoWaybillDTO(rsp.getWaybillApplySubscriptionCols());
        } catch (ApiException e) {
            log.error("Cainiao search error! ", e);
            throw new DeliveryException(DeliveryException.WAYBILL_CAINIO_GET_ERROR);
        }
    }

    private List<CainiaoWaybillDTO> genCainiaoWaybillDTO(List<CainiaoWaybillIiSearchResponse.WaybillApplySubscriptionInfo> subscribtions) {
        List<CainiaoWaybillDTO> cainiaoWaybillDTOs = Lists.newArrayList();
        if (ListUtil.isNotEmpty(subscribtions)) {
            for (CainiaoWaybillIiSearchResponse.WaybillApplySubscriptionInfo subscribtion : subscribtions) {
                if (subscribtion != null) {
                    List<CainiaoWaybillIiSearchResponse.WaybillBranchAccount> branchAccountCols = subscribtion.getBranchAccountCols();
                    if (ListUtil.isNotEmpty(branchAccountCols)) {
                        for (CainiaoWaybillIiSearchResponse.WaybillBranchAccount branchAccountCol : branchAccountCols) {
                            List<CainiaoWaybillIiSearchResponse.AddressDto> shippAddressCols = branchAccountCol.getShippAddressCols();
                            for (CainiaoWaybillIiSearchResponse.AddressDto waybillAddress : shippAddressCols) {
                                CainiaoWaybillDTO cainiaoWaybillDTO = new CainiaoWaybillDTO();
                                cainiaoWaybillDTO.setCpCode(subscribtion.getCpCode());
                                cainiaoWaybillDTO.setTown(waybillAddress.getTown());
                                cainiaoWaybillDTO.setAddressDetail(waybillAddress.getDetail());
                                cainiaoWaybillDTO.setAllocatedQuantity(branchAccountCol.getAllocatedQuantity().toString());
                                cainiaoWaybillDTO.setArea(waybillAddress.getDistrict());
                                cainiaoWaybillDTO.setBranchName(branchAccountCol.getBranchName());
                                cainiaoWaybillDTO.setCancelQuantity(branchAccountCol.getCancelQuantity().toString());
                                cainiaoWaybillDTO.setCity(waybillAddress.getCity());
                                cainiaoWaybillDTO.setProvince(waybillAddress.getProvince());
                                cainiaoWaybillDTO.setPrintQuantity(branchAccountCol.getPrintQuantity().toString());
                                cainiaoWaybillDTO.setQuantity(branchAccountCol.getQuantity().toString());
                            
                                cainiaoWaybillDTOs.add(cainiaoWaybillDTO);
                            }
                        }
                    }
                }
            }
        }
        return cainiaoWaybillDTOs;
    }
}
