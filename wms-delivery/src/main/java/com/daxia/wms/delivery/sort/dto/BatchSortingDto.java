package com.daxia.wms.delivery.sort.dto;

import java.util.List;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;

import com.daxia.wms.delivery.sort.dto.SortedDoDetailDto;


/**
 * <pre>
 * 分拣集中提交Dto
 * </pre>
 */
@XmlRootElement(name="batchSortingDto")
@lombok.extern.slf4j.Slf4j
public class BatchSortingDto {
    /**
     * 波次号
     */
    private String waveNo;
    /**
     * 仓库ID
     */
    private String warehouseId;
    /**
     * 分拣柜
     */
    private String sortingBin;
    
    private List<SortedDoDetailDto> sortedDoDetailList;
    
	public String getWaveNo() {
		return waveNo;
	}
	public void setWaveNo(String waveNo) {
		this.waveNo = waveNo;
	}
	public String getWarehouseId() {
		return warehouseId;
	}
	public void setWarehouseId(String warehouseId) {
		this.warehouseId = warehouseId;
	}
	public String getSortingBin() {
		return sortingBin;
	}
	public void setSortingBin(String sortingBin) {
		this.sortingBin = sortingBin;
	}
    @XmlElement(name = "sortedDoDetail")
    @XmlElementWrapper
	public List<SortedDoDetailDto> getSortedDoDetailList() {
		return sortedDoDetailList;
	}
	public void setSortedDoDetailList(List<SortedDoDetailDto> sortedDoDetailList) {
		this.sortedDoDetailList = sortedDoDetailList;
	}
}
