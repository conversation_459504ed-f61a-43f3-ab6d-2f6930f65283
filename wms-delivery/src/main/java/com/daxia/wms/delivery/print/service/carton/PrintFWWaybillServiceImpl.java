package com.daxia.wms.delivery.print.service.carton;

import com.daxia.dubhe.api.wms.dto.LogisticsDTO;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.WaybillType;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoPrintDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoPrint;
import com.daxia.wms.delivery.deliveryorder.service.DoWaveExService;
import com.daxia.wms.delivery.print.dto.BaseCartonPrintDTO;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.helper.PrintCartonHelper;
import com.daxia.wms.delivery.print.helper.SFPrintCartonHelper;
import com.daxia.wms.delivery.print.service.PrintWaybillService;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.print.dto.PrintCfg;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.List;

@Name("printFWWaybillService")
@lombok.extern.slf4j.Slf4j
public class PrintFWWaybillServiceImpl extends AbstractPrintWaybillService implements PrintWaybillService {
    @In
    private DoWaveExService doWaveExService;
    @In
    CartonService cartonService;
    @In
    WarehouseCarrierService warehouseCarrierService;
    @In
    private DoPrintDAO doPrintDAO;
    private ObjectMapper objectMapper = new ObjectMapper();

    @Create
    public void init () {
        this.setWaybillType(WaybillType.FWSY);
    }

    @Override
    protected void buildDTO(List<PrintReportDto> printReportDtos, DeliveryOrderHeader doHeader,
                            BaseCartonPrintDTO carton, int index, int count) {
        CartonPrintDTO cartonPrintDTO = new CartonPrintDTO();
        cartonPrintDTO.setIsPrinted(carton.getIsPrinted());
        cartonPrintDTO.setCartonId(carton.getId());
        cartonPrintDTO.setCartonNo(carton.getCartonNo());
        cartonPrintDTO.setDoNo(doHeader.getDoNo());
        cartonPrintDTO.setOutRefNo(doHeader.getRefNo1());
        cartonPrintDTO.setSortGridNo(doHeader.getSortGridNo());
        cartonPrintDTO.setSortGridCount(doHeader.getWaveHeader().getDoHeaders().size());
        cartonPrintDTO.setWaveNo(doHeader.getWaveHeader().getWaveNo());
        cartonPrintDTO.setOriginalSoCode(doHeader.getOriginalSoCode());
        // 设置箱子里面商品个数
        BigDecimal skuUnit = cartonService.sumSkuUnit(carton.getId());
        cartonPrintDTO.setSkuUnitQty(skuUnit);
        // 设置收货人地址
        cartonPrintDTO.setClientProvinceAndCityAndCountyAddress(
                SFPrintCartonHelper.buildProvinceAndCityAndCountyAddress(doHeader, ","));
        cartonPrintDTO.setClientAddress(StringUtil.notNullString(doHeader.getAddress()));
        cartonPrintDTO.setClientName(SFPrintCartonHelper.buildConsigneeName(doHeader));
        cartonPrintDTO.setClientPhone(PrintCartonHelper.buildTelOrMobile(doHeader));
        // 设置寄件人地址信息
        this.setSendAddressInfo(doHeader, cartonPrintDTO);
        // 运单号
        cartonPrintDTO.setWayBill(carton.getCartonNo());
        cartonPrintDTO.setMainWayBill(doHeader.getTrackingNo());
        printReportDtos.add(cartonPrintDTO);
        this.processWayBillInfo(doHeader.getId(), cartonPrintDTO);
    }

    @Override
    protected PrintData genPrintData(List<PrintReportDto> dtoList, DeliveryOrderHeader doHeader) {
        PrintData printData = new PrintData();
        printData.setDtoList(dtoList);
        printData.setPrintCfg(this.generateSFPrintCfg());
        return printData;
    }

    private PrintCfg generateSFPrintCfg() {
        PrintCfg config = new PrintCfg("wayBillFW", "100", "150");
        this.setPrintCfg(config);
        return config;
    }

    @SneakyThrows
    private void processWayBillInfo(Long doId, CartonPrintDTO cartonPrintDTO) {
        DoPrint doPrint = doPrintDAO.findByDoHeaderId(doId, Constants.DoPrintInfoType.WAYBILL_JSON.getValue());
        if (null == doPrint) {
            throw new DeliveryException(DeliveryException.WAYBILL_IMAGE_NOT_EXIST);
        }
        LogisticsDTO logisticsDTO = objectMapper.readValue(doPrint.getContent(), LogisticsDTO.class);

        cartonPrintDTO.setDestRouteLabel(logisticsDTO.getDestRouteLabel());
        cartonPrintDTO.setDestTeamCode(logisticsDTO.getDestRouteLabel2());
        cartonPrintDTO.setCodingMapping(logisticsDTO.getCodingMapping());
        cartonPrintDTO.setTwoDimensionCode(logisticsDTO.getTwoDimensionCode());
        cartonPrintDTO.setAgingName(logisticsDTO.getAgingName());
        cartonPrintDTO.setPrintIcon(logisticsDTO.getPrintIcon());
        cartonPrintDTO.setBagAddr(logisticsDTO.getBagAddr());
        //集包类型
        cartonPrintDTO.setExt1(logisticsDTO.getBoxOrStationType());
        //驿站内筒
        cartonPrintDTO.setExt2(logisticsDTO.getBoxOrStationCode());
    }
}
