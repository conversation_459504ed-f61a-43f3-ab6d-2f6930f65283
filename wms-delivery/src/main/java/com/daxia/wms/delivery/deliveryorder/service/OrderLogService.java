package com.daxia.wms.delivery.deliveryorder.service;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.OrderLog;
import com.daxia.wms.master.dto.OrderLogDTO;
import com.daxia.wms.master.filter.LpnFilter;
import com.daxia.wms.master.filter.OrderLogFilter;

import java.util.List;

public interface OrderLogService {
	
	/**
	 * 分页查询
	 * @param filter
	 * @param startIndex
	 * @param pageSize
	 * @return
	 */
	public DataPage<OrderLog> queryByFilter(LpnFilter filter, int startIndex,
                                            int pageSize);

	/**
	 * 保存
	 * @param orderLog
	 */
	public OrderLog saveLog(DeliveryOrderHeader doHeader, String operateType, String orderLog, String operator);

	/**
	 * 保存
	 * @param orderLog
	 */
	public void saveLog(DeliveryOrderHeader doHeader, String operateType,String orderLog);
	public void asyncSaveLog(DeliveryOrderHeader doHeader, String operateType,String orderLog,String operator);

	List<OrderLog> findLogByOrderId(Long id);

    boolean existsLog(Long id, String operateType);

    public void saveLog(Long waveId, String value, String dispalyString);

	void saveLog4PickList(List<Long>pickTaskIdList,String operateType,String orderLog,String operateBy);

	public void batchSaveLog(List<Long> doList, String operateType, String orderLog);

    DataPage<OrderLogDTO> query(OrderLogFilter orderLogFilter, int startIndex, int pageSize);

    void saveLog(DeliveryOrderHeader doHeader, String operateType, String orderLog, String updateBy, OrderLogDTO orderLogDTO);
}
