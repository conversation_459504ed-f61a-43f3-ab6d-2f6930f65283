package com.daxia.wms.delivery.recheck.dao;

import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.recheck.entity.PackMaterialLog;

/**
 * 包材推荐日志DAO
 */
@Name("com.daxia.wms.delivery.packMaterialLogDAO")
@lombok.extern.slf4j.Slf4j
public class PackMaterialLogDAO extends HibernateBaseDAO<PackMaterialLog, Long> {
	private static final long serialVersionUID = -7498311294781384229L;
	
	/**
	 * 根据DOId查询
	 * @param doId
	 * @return
	 */
	public PackMaterialLog getByDoId(Long doId) {
		String hql = "from PackMaterialLog o where o.doId = :doId and o.warehouseId = :warehouseId";
		return (PackMaterialLog) createQuery(hql).setParameter("doId", doId)
				.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId())
				.uniqueResult();
	}
}
