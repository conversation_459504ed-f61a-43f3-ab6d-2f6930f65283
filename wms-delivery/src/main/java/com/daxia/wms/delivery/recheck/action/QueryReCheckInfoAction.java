package com.daxia.wms.delivery.recheck.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.framework.system.constants.Constants.YesNo;
import com.daxia.framework.system.entity.UserAccount;
import com.daxia.framework.system.security.SystemSecurityException;
import com.daxia.framework.system.service.UserAccountService;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.print.dto.NopaperListDTO;
import com.daxia.wms.delivery.print.helper.WaybillPrintHelper;
import com.daxia.wms.delivery.print.service.carton.PrintCartonDispatcher;
import com.daxia.wms.delivery.recheck.dto.ReCheckCartonInfo;
import com.daxia.wms.delivery.recheck.dto.RecheckDetailInfoDTO;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.filter.CartonHeaderFilter;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.recheck.service.ReCheckService;
import com.daxia.wms.delivery.wave.service.BatchGroupWaveService;
import com.daxia.wms.master.entity.BusinessCustomer;
import com.daxia.wms.master.service.BusinessCustomerService;
import com.daxia.wms.master.service.SkuScanService;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.util.ExecutorUtil;
import com.google.gson.Gson;
import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.contexts.Lifecycle;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 查询核拣记录
 */
@Name("com.daxia.wms.delivery.queryReCheckInfoAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class QueryReCheckInfoAction extends PagedListBean<CartonHeader> {

    private static final long serialVersionUID = -8277272791377861759L;

    private String waybill;

    private CartonHeaderFilter cartonHeaderFilter;

    private List<ReCheckCartonInfo> reCheckCartonList;

    private List<RecheckDetailInfoDTO> reCheckCartonDetailList;

    private String printDatas = "[]";

    private String productCode;

    /**
     * 电子面单的打印数据
     */
    private String printContent = "";
    private DeliveryOrderHeader doHeader;

    private boolean find = true;

    private boolean queryMode = false;

    private List<NopaperListDTO> nopaperList;

    private Integer isprint = 0;//无纸化需求---从接口那边获取是否需要自动打印

    private String isAtuoPrintnoPaperDetailCfg;

    /**
     * 使用3pl对应的箱标签
     */
    private boolean is3PLCartonLabel = false;

    @In
    DeliveryOrderService deliveryOrderService;
    @In
    private ReCheckService reCheckService;
    @In
    private UserAccountService userAccountService;
    @In
    private PrintCartonDispatcher printCartonDispatcher;

    @In
    private SkuScanService skuScanService;

    private boolean isAutoDelivery;

    private boolean updateDeliveryFlag;

    private String customerName;

    @In
    private BatchGroupWaveService batchGroupWaveService;
    @In
    private BusinessCustomerService businessCustomerService;
    @In
    private CartonService cartonService;

    private Long doId;
    private Long hrefDoId;

    private boolean initialized = false;

    public QueryReCheckInfoAction() {
        cartonHeaderFilter = new CartonHeaderFilter();
    }

    public void initializePage() {
        if (!initialized) {
            String orderNo = null;
            if (hrefDoId != null) {
                DeliveryOrderHeader header = deliveryOrderService.getDoHeaderById(hrefDoId);
                orderNo = header == null ? null : header.getDoNo();
            }
            cartonHeaderFilter.setDoNoList(orderNo);
            this.dataPage.setPageSize(50);
            initialized = true;
        }
    }

    @Override
    public void query() {
        selectedMap.clear();
        doId = null;
        find = true;
        cartonHeaderFilter.getOrderByMap().put("id", "desc");
        cartonHeaderFilter.setWayBill(waybill);
        if (StringUtil.isNotBlank(cartonHeaderFilter.getLpnNo())) {
            cartonHeaderFilter.setIsPrinted(YesNo.NO.getValue());
        } else if (StringUtil.isNotEmpty(cartonHeaderFilter.getCartonNo()) ||
                StringUtil.isNotEmpty(cartonHeaderFilter.getDoNo()) ||
                StringUtil.isNotEmpty(cartonHeaderFilter.getWaveNo()) ||
                StringUtil.isNotEmpty(cartonHeaderFilter.getWayBill())) {
            cartonHeaderFilter.setIsPrinted(null);
        }
        this.buildOrderFilterMap(cartonHeaderFilter);
        DataPage<CartonHeader> dataPag = reCheckService.selectCartonsInfoByFilter(cartonHeaderFilter, getStartIndex(), getPageSize());
        reCheckCartonDetailList = reCheckService.selectCartonsDetailInfoByCartonIds(dataPag.getDataList());
        this.populateValues(dataPag);
    }

    /**
     * 打印箱标签
     */
    public void print() throws ExecutionException, InterruptedException {
        printContent = "";
        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }

        ids.stream()
                .forEach(id -> {
                    DeliveryOrderHeader doHeader = cartonService.getDoHeaderById(id);

                    if (doHeader == null) {
                        throw new DeliveryException(DeliveryException.CARTON_NOEXIST);
                    }

                    if (Constants.DoStatus.ALL_CARTON.getValue().compareTo(doHeader.getStatus()) > 0) {
                        throw new DeliveryException(DeliveryException.COMBINE_ERROR_DO_NOT_PACK_OVER, doHeader.getDoNo());
                    }
                });

        if (updateDeliveryFlag && doId != null) {
            reCheckService.updateCartonAutoDeliveryFlagByDoId(doId);
            isAutoDelivery = false;
        }

        selectedMap.clear();
        Long warehouseId = ParamUtil.getCurrentWarehouseId();
        // 将所有future收集到一个列表中
        List<CompletableFuture<PrintData>> futures=new ArrayList<>();
        for (Long id : ids) {
            futures.add(
                    CompletableFuture.supplyAsync(()->{
                        try {
                            // 设置上下文
                            Lifecycle.beginCall();
                            ParamUtil.setCurrentWarehouseId(warehouseId);
                            return ((PrintCartonDispatcher)Component.getInstance(PrintCartonDispatcher.class)).print(null, Arrays.asList(id));
                        }finally {
                            Lifecycle.endCall();
                        }
                    }, ExecutorUtil.ioExecutor)
            );
        }

        List<PrintData> resultList = WaybillPrintHelper.sortPrintDataByPrintTemplate(futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
        printDatas = new Gson().toJson(resultList);
        printContent = WaybillPrintHelper.getPrintJs(resultList);
    }


    /**
     * 返回操作人，页面选择操作用户组件调用
     *
     * @param userId
     */
    public void receiveSelectUser(Long userId) {
        if (userId != null) {
            UserAccount userAccount = userAccountService.getUserAccount(userId);
            if (userAccount == null) {
                throw new SystemSecurityException(SystemSecurityException.USER_NOT_EXISTS);
            }
            this.cartonHeaderFilter.setCreatedBy(userAccount.getLoginName());
        }
    }


    public void receiveSelectBusinessCustomer(Long businessCustomerId) {
        BusinessCustomer businessCustomer = null;
        if (businessCustomerId == null) {
            businessCustomer = new BusinessCustomer();
        } else {
            businessCustomer = businessCustomerService.getBusinessCustomer(businessCustomerId);
        }
        this.cartonHeaderFilter.setBusinessCustomerId(businessCustomerId);
        this.setCustomerName(businessCustomer.getCustomerName());

    }

    public void clearBusinessCustomer() {
        this.cartonHeaderFilter.setBusinessCustomerId(null);
        this.setCustomerName(null);
    }

    public void transformProductCode(){
        if (StringUtil.isEmpty(this.productCode)) {
            return;
        }
        productCode = skuScanService.transformScanCode(productCode);
    }

    public CartonHeaderFilter getCartonHeaderFilter() {
        return cartonHeaderFilter;
    }

    public void setCartonHeaderFilter(CartonHeaderFilter cartonHeaderFilter) {
        this.cartonHeaderFilter = cartonHeaderFilter;
    }

    public void clearSelectUser() {
        this.cartonHeaderFilter.setCreatedBy(null);
    }

    public List<ReCheckCartonInfo> getViewList() {
        return reCheckCartonList;
    }

    public void setViewList(List<ReCheckCartonInfo> reCheckCartonList) {
        this.reCheckCartonList = reCheckCartonList;
    }

    public boolean isFind() {
        return find;
    }

    public void setFind(boolean find) {
        this.find = find;
    }


    public DeliveryOrderHeader getDoHeader() {
        return doHeader;
    }

    public void setDoHeader(DeliveryOrderHeader doHeader) {
        this.doHeader = doHeader;
    }

    public boolean isQueryMode() {
        return queryMode;
    }

    public void setQueryMode(boolean queryMode) {
        this.queryMode = queryMode;
    }

    public List<NopaperListDTO> getNopaperList() {
        return nopaperList;
    }

    public void setNopaperList(List<NopaperListDTO> nopaperList) {
        this.nopaperList = nopaperList;
    }

    public Integer getIsprint() {
        return isprint;
    }

    public void setIsprint(Integer isprint) {
        this.isprint = isprint;
    }


    public String getIsAtuoPrintnoPaperDetailCfg() {
        return isAtuoPrintnoPaperDetailCfg;
    }


    public void setIsAtuoPrintnoPaperDetailCfg(String isAtuoPrintnoPaperDetailCfg) {
        this.isAtuoPrintnoPaperDetailCfg = isAtuoPrintnoPaperDetailCfg;
    }

    public boolean getIs3PLCartonLabel() {
        return is3PLCartonLabel;
    }

    public void setIs3PLCartonLabel(boolean is3plCartonLabel) {
        is3PLCartonLabel = is3plCartonLabel;
    }

    public boolean getIsAutoDelivery() {
        return isAutoDelivery;
    }

    public void setIsAutoDelivery(boolean isAutoDelivery) {
        this.isAutoDelivery = isAutoDelivery;
    }

    public boolean getUpdateDeliveryFlag() {
        return updateDeliveryFlag;
    }

    public void setUpdateDeliveryFlag(boolean updateDeliveryFlag) {
        this.updateDeliveryFlag = updateDeliveryFlag;
    }

    public String getPrintContent() {
        return printContent;
    }

    public void setPrintContent(String printContent) {
        this.printContent = printContent;
    }

    public String getPrintDatas() {
        return printDatas;
    }

    public void setPrintDatas(String printDatas) {
        this.printDatas = printDatas;
    }

    public String getWaybill() {
        return waybill;
    }

    public void setWaybill(String waybill) {
        this.waybill = waybill;
    }

    public List<RecheckDetailInfoDTO> getReCheckCartonDetailList() {
        return reCheckCartonDetailList;
    }

    public void setReCheckCartonDetailList(List<RecheckDetailInfoDTO> reCheckCartonDetailList) {
        this.reCheckCartonDetailList = reCheckCartonDetailList;
    }

    public Long getHrefDoId() {
        return hrefDoId;
    }

    public void setHrefDoId(Long hrefDoId) {
        this.hrefDoId = hrefDoId;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    /**
     * 获取视频前缀
     */
    public String getVideoBaseUrl(){
        return SystemConfig.getConfigValue("recheck.video.base.url", ParamUtil.getCurrentWarehouseId());
    }
}