package com.daxia.wms.delivery.deliveryorder.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.DoType;
import com.daxia.wms.Constants.NotifyCSType;
import com.daxia.wms.Constants.ReleaseStatus;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.constant.DeliveryConstant;
import com.daxia.wms.delivery.deliveryorder.dto.DoTimeInfoDto;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateHeader;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateService;
import com.daxia.wms.delivery.deliveryorder.service.IDoAllocate;
import com.daxia.wms.exp.dto.DoExpDto;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.master.helper.SysConfigHelper;
import com.google.common.collect.ImmutableSet;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

@Name("com.daxia.wms.delivery.doAllocateDetailAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class DoAllocateDetailAction extends PagedListBean<DoAllocateDetail> {

    private static final long serialVersionUID = 3194246682236658191L;

    private Long allocateHeaderId;
    private Integer isException = 0 ;
    private DoAllocateHeader doAllocateHeader;
    private DoTimeInfoDto doTimeInfoDto;
    @In
    private DoAllocateService doAllocateService;
    @In
    private ExpFacadeService expFacadeService;

    public void initialize() {
        this.doAllocateHeader = doAllocateService.getHeader(this.allocateHeaderId);
        if (this.doAllocateHeader == null) {
            log.warn("主键查询订单分配头doAllocateHeader为空,allocateHeaderId:{}", this.allocateHeaderId);
            throw new DeliveryException(DeliveryException.ERROR_ALLOC_HEAD_NOT_EXIST);
        }
        sortDetails(doAllocateHeader);
    }

    /*
     * 将明细排序 组合产品的父产品与其下子产品放一起显示
     */
    private void sortDetails(DoAllocateHeader doAllocateHeader) {
        BigDecimal totalAllocatEa = BigDecimal.ZERO;
        List<DoAllocateDetail> details = doAllocateHeader.getDoAllocateDetails();

        if (!ListUtil.isNullOrEmpty(details)) {
            //用来放组合产品父产品
            List<DoAllocateDetail> parentList = new ArrayList<DoAllocateDetail>();
            //用来放组合产品子产品
            List<DoAllocateDetail> childList = new ArrayList<DoAllocateDetail>();
            //用来放非组合产品
            List<DoAllocateDetail> oterList = new ArrayList<DoAllocateDetail>();
            if (details == null || details.isEmpty()) {
                doAllocateHeader.setTotalAllocatEa(totalAllocatEa);
            } else {
                for (DoAllocateDetail detail : details) {
                    if (detail.getIsDoLeaf().intValue() == 0) {
                        parentList.add(detail);
                    } else {
                        totalAllocatEa = totalAllocatEa.add(detail.getAllocatedQty());
                        if (detail.getParentId() == null) {
                            oterList.add(detail);
                        } else {
                            childList.add(detail);
                        }
                    }
                }
                doAllocateHeader.setTotalAllocatEa(totalAllocatEa);

                //排序发货单明细
                details.clear();
                int lineNo = 1;
                for (DoAllocateDetail parent : parentList) {
                    parent.setLineNo(Integer.valueOf(lineNo));
                    details.add(parent);
                    for (DoAllocateDetail child : childList) {
                        if (parent.getOrigDetailId().equals(child.getParentId())) {
                            details.add(child);
                        }
                    }
                    lineNo++;
                }
                for (DoAllocateDetail other : oterList) {
                    other.setLineNo(lineNo);
                    details.add(other);
                    lineNo++;
                }
                doAllocateHeader.setDoAllocateDetails(details);
            }
        }
    }
    
    /**
     * 自动分配
     * @throws Exception
     */
    @Loggable
    public void assignAuto() throws Exception {
        List<Object> selectDetailIds = this.getSelectedRowList();
        List<Long> ids = new ArrayList<Long>(selectDetailIds.size());
        for (Object obj : selectDetailIds) {
            ids.add((Long) obj);
        }
    
        Map<String, List<String>> resultMap = doAllocateService.autoAllocate(doAllocateHeader.getId(), ids);
    
        if (resultMap.get(IDoAllocate.NO_ENOUGH_STOCK_QTY) != null) {
            // 如果全仓缺货需要调用接口通知客服。
        
            //调用接口通知客服；
            DoExpDto dto = new DoExpDto();
            dto.setId(doAllocateHeader.getId());
            dto.setHoldCode(Constants.Reason.ALLOC_LACK.getValue());
            dto.setNotifyType(NotifyCSType.AUTO.getValue());
            // 如果配置自动通知客服，则缺货自动通知客服。
            Boolean lackAutoAnounceCs = SysConfigHelper.getSwitchDefalutOpen("alloc.lack.autoAnounceCs");
            if (lackAutoAnounceCs) {
                expFacadeService.callCS(dto);
            }
            /**冻结do时调scs重新计算预计出库时间接口*/
            expFacadeService.sendDoReleaseOrHold2Scs(doAllocateHeader.getId());
        }
    
        if (resultMap.isEmpty()) {
            this.sayMessage(MESSAGE_SUCCESS);
        } else {
            for (Entry<String, List<String>> entry : resultMap.entrySet()) {
                this.sayMessage(DeliveryConstant.getAllocatMessage(entry.getKey()), ListUtil.collection2String(entry.getValue(), ","));
            }
        }
        
        this.initialize();
        
        this.selectedMap.clear();
    }

    @Override
    public void query() {
    }
    
    /**
     * 分配审核
     */
    public void checkAlloc() {
    	doAllocateService.checkAlloc(this.doAllocateHeader.getId());
    	this.initialize();
    	this.sayMessage(MESSAGE_SUCCESS);
    }
    
    /**
     * 取消分配
     */
    @Loggable
    public void cancelAssign() {
        //获得页面选取的发货单明细Ids
        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }
        boolean checkReleaseStatus = true;
        if(Constants.YesNo.YES.getValue().equals(isException)){
            checkReleaseStatus = false;
        }
        doAllocateService.cancelAssign(doAllocateHeader.getId(), ids, false,checkReleaseStatus);
        this.selectedMap.clear();
        this.initialize();
        this.sayMessage(MESSAGE_SUCCESS);
    }
    
	public boolean isManualAllocationAllowed() {
        return doAllocateHeader.getWaveFlag() == 0
                && EnumSet.range(DoStatus.INITIAL, DoStatus.ALLALLOCATED)
                .contains(DoStatus.fromString(doAllocateHeader.getStatus()))
                && (ReleaseStatus.RELEASE == ReleaseStatus.fromString(doAllocateHeader.getReleaseStatus()) || Constants.YesNo.YES.getValue().equals(isException))
                && ImmutableSet.of(DoType.ALLOT, DoType.WHOLESALE, DoType.RTV, DoType.SELL,DoType.MPS_OUT)
                .contains(DoType.fromString(doAllocateHeader.getDoType()));
    }

    public boolean isForcedAllocationAllowed() {
        Boolean rtvCanLackDelivery = SystemConfig.configIsOpen("delivery.rtvCanLackDelivery", ParamUtil.getCurrentWarehouseId());
        if (rtvCanLackDelivery) {
            return ImmutableSet.of(DoType.ALLOT, DoType.RTV, DoType.WHOLESALE).contains(DoType.fromString(doAllocateHeader.getDoType())) && DoStatus.PARTALLOCATED == DoStatus.fromString(doAllocateHeader.getStatus());
        } else {
            return ImmutableSet.of(DoType.ALLOT, DoType.WHOLESALE).contains(DoType.fromString(doAllocateHeader.getDoType())) && DoStatus.PARTALLOCATED == DoStatus.fromString(doAllocateHeader.getStatus());
        }
    }

    /**
     * 清空订单效期范围限制
     */
    public void cleanExpireLimit(){
        List<Object> selectDetailIds = this.getSelectedRowList();
        List<Long> ids = new ArrayList<Long>(selectDetailIds.size());
        for (Object obj : selectDetailIds) {
            ids.add((Long) obj);
        }
        doAllocateService.cleanExpireLimit(doAllocateHeader.getId(),ids);
        initialize();
        this.sayMessage(MESSAGE_SUCCESS);
    }

    public Long getAllocateHeaderId() {
        return allocateHeaderId;
    }

    public void setAllocateHeaderId(Long allocateHeaderId) {
        this.allocateHeaderId = allocateHeaderId;
    }

    public Integer getIsException() {
        return isException;
    }

    public void setIsException(Integer isException) {
        this.isException = isException;
    }

    public DoAllocateHeader getDoAllocateHeader() {
        return doAllocateHeader;
    }

    public void setDoAllocateHeader(DoAllocateHeader doAllocateHeader) {
        this.doAllocateHeader = doAllocateHeader;
    }

    public DoTimeInfoDto getDoTimeInfoDto() {
        return doTimeInfoDto;
    }

    public void setDoTimeInfoDto(DoTimeInfoDto doTimeInfoDto) {
        this.doTimeInfoDto = doTimeInfoDto;
    }
}