package com.daxia.wms.delivery.task.repick.action;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.json.JSONArray;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;

import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.entity.CfgCodeDetail;
import com.daxia.framework.common.service.Dictionary;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.DownloadUtil;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.print.service.PrintService;
import com.daxia.wms.delivery.task.repick.entity.ReversePickHeader;
import com.daxia.wms.delivery.task.repick.service.RePickTaskService;
import com.daxia.wms.delivery.task.repick.service.ReversePickHeaderService;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.TaskStatus;
import com.daxia.wms.Constants.TaskType;
import com.daxia.wms.master.MasterException;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.service.LocationService;
import com.daxia.wms.stock.StockException;
import com.daxia.wms.stock.stock.dto.DamageInfoDTO;
import com.daxia.wms.stock.stock.service.StockBatchAttExtService;
import com.daxia.wms.stock.task.entity.TrsTask;
import com.daxia.wms.stock.task.filter.TrsTaskFilter;
import com.daxia.wms.stock.util.BatchPropertyUtil;

/**
 * 反拣任务管理
 */
@Name("com.daxia.wms.delivery.rePickTaskAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class RePickTaskAction extends PagedListBean<TrsTask> {

    private static final long serialVersionUID = -6529313027336728713L;

    @In
    private PrintService printService;
    @In
    private RePickTaskService rePickTaskService;
    @In
    private ReversePickHeaderService reversePickHeaderService;
    @In
    private LocationService locationService;
    @In
    StockBatchAttExtService stockBatchAttExtService;
    
    private String printData = "[]";
    private boolean initialized = false;
    private TrsTaskFilter trsTaskFilter;
    private Long reversePickHeaderId;
    private List<TrsTask> rePickTaskList;
    /**
     * 返拣单头信息
     */
    private ReversePickHeader reversePickHeader;
    
    /**
     * 弹出框标识
     */
    private boolean popFlag = false;
    
    private Long taskId;
    

	public RePickTaskAction() {
        super();
        trsTaskFilter = new TrsTaskFilter();
    }

	/**
	 * 查询返拣任务
	 */
    @Override
    public void query() {
        this.buildOrderFilterMap(trsTaskFilter);
        DataPage<TrsTask> dataPage = rePickTaskService.queryRePickTrs(trsTaskFilter,
            this.getStartIndex(), this.getPageSize());
        this.populateValues(dataPage);
    }

    /**
     * 初始化返拣任务明细页面
     */
    public void initRePickListPage() {
        if (!this.initialized) {
        	this.setReversePickHeader(this.reversePickHeaderService.get(reversePickHeaderId));
            this.trsTaskFilter.setDocOperId(this.reversePickHeaderId);
            this.trsTaskFilter.setTaskType(Constants.TaskType.RK.getValue());
            this.rePickTaskList = this.rePickTaskService.queryRePickTrs(trsTaskFilter);
            if (!ListUtil.isNullOrEmpty(rePickTaskList)) {
                for (TrsTask trsTask : rePickTaskList) {
                	if(trsTask.getToLocId() == null && trsTask.getPlanLocId() != null){
                		trsTask.setRealLoc(trsTask.getPlanLocation().getLocCode());
                	}else if(trsTask.getToLocId() != null){
                		trsTask.setRealLoc(trsTask.getLocation().getLocCode());
                	}
                	// 如果是从好品反拣到坏品库位，原因代码自动选择成“坏品”不允许用户更改
                	if(Constants.TaskStatus.RELEASED.getValue().equals(trsTask.getTaskStatus()) && isNomalToDM(trsTask,Boolean.FALSE)){
                	    trsTask.setReasonCode(Constants.ReasonCode.DAMAGE.getValue());
                	    trsTask.setIsNomalToDM(Boolean.TRUE);
                	}
                }
            }
            this.initialized = true;
        }
    }
    
    public Boolean isNomalToDM(TrsTask trsTask,Boolean isNotInit){
        Location planLocation = trsTask.getPlanLocation();
        Location toLocation = locationService.queryLocationByLocCode(trsTask.getRealLoc());
        if (isNotInit && null == toLocation) {
            throw new MasterException(MasterException.LOCATION_NOT_EXIST);
        }
        if (null != planLocation && !Constants.LocType.DM.getValue().equals(planLocation.getLocType()) &&
                null != toLocation && Constants.LocType.DM.getValue().equals(toLocation.getLocType())){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
    /**
     * 确认返拣
     */
    @Loggable
    public void confirmRevesePick() {
        List<TrsTask> tempTaskList = new ArrayList<TrsTask>();
        List<Object> taskList = getSelectedRowList();
        for (Object task : taskList) {
            TrsTask t = (TrsTask) task;
            tempTaskList.add(t);
        }
		try {
			if (reversePickHeaderId == null) {
				//返拣单头不存在
				throw new DeliveryException(DeliveryException.REVERSEPICK_HEADER_NOTEXIST);
			}
			if (tempTaskList == null || tempTaskList.isEmpty()) {
				//返拣单中没有返拣任务
				throw new DeliveryException(DeliveryException.REVERSEPICK_HEADER_NOTASK);
			}

			Map<Long, TrsTask> taskMap = new HashMap<Long, TrsTask>();
			List<Long> ids = new ArrayList<Long>();
			for (TrsTask trsTask : tempTaskList) {
				Long taskId = trsTask.getId();
				taskMap.put(taskId, trsTask);
				ids.add(taskId);
			}

			List<TrsTask> realTasksByIds = rePickTaskService.getTasksByIds(ids, Constants.TrsType.RK.getValue());
			if (realTasksByIds == null || realTasksByIds.isEmpty()) {
				throw new DeliveryException(DeliveryException.REVERSEPICK_HEADER_NOTASK);
			}
			for (TrsTask trsTask : realTasksByIds) {
				// 页面保存数据的task对象
				TrsTask taskFromPage = taskMap.get(trsTask.getId());
				if (StringUtil.isNotEmpty(taskFromPage.getReasonMasterCode())) {
                    trsTask.setReasonDescr(StringUtil.isEmpty(taskFromPage.getReasonDescr()) ? "" : Dictionary.getDictionary(taskFromPage.getReasonMasterCode()).get(taskFromPage.getReasonDescr()));
		        }else{
		            trsTask.setReasonDescr(taskFromPage.getReasonDescr());
		        }
				
				taskFromPage = this.makeReasonDescr(taskFromPage);
                rePickTaskService.executeRePick(trsTask, taskFromPage, genDamageInfoDTO(taskFromPage), reversePickHeaderId, null);
				trsTask.setIsNomalToDM(Boolean.FALSE);
			}
		} catch (DeliveryException de) {
			throw de;
		} catch (MasterException me) {
			throw me;
		} catch (StockException e) {
			throw e;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new DeliveryException(DeliveryException.SYSTEM_ERROR);
		}
        this.initialized = false;
        this.initRePickListPage();
        this.selectedMap.clear();
        this.sayMessage(MESSAGE_SUCCESS);

    }
    
    private DamageInfoDTO genDamageInfoDTO(TrsTask trsTask) {
        DamageInfoDTO dto = new DamageInfoDTO();

        dto.setDeptClass4(trsTask.getDeptClass4());
        dto.setDmDescr(trsTask.getNotes());
        dto.setDmReason(trsTask.getReasonDescr());
        dto.setHasEntity(trsTask.getHasEntity());
        dto.setDutyDept(trsTask.getResDepart());

        //如果4级部门不可能用的时候，清空4级部门信息；
        if (stockBatchAttExtService.disableDeptClass4(dto)) {
            dto.setDeptClass4(null);
        }

        BatchPropertyUtil.genLot12(dto);
        return dto;
    }
    
    /**
     * 设置实际上架库位，以及判断是否将原因代码设为坏品
     */
    public void checkRealLocation() {
        for(TrsTask trsTask : rePickTaskList){
            if(trsTask.getId().equals(taskId)) {
                trsTask.setReasonCode(null);
                // 如果是从好品反拣到坏品库位，原因代码自动选择成"坏品"不允许用户更改
                if(Constants.TaskStatus.RELEASED.getValue().equals(trsTask.getTaskStatus()) && isNomalToDM(trsTask,Boolean.TRUE)){
                    trsTask.setReasonCode(Constants.ReasonCode.DAMAGE.getValue());
                    trsTask.setIsNomalToDM(Boolean.TRUE);
                }else{
                    trsTask.setIsNomalToDM(Boolean.FALSE);
                }
                trsTask.setResDepart(null);
                trsTask.setReasonMasterCode(null);
                trsTask.setReasonDescr(null);
                break;
            }
        }
    }
    
    /**
     * 拼好原因描述
     */
    public TrsTask makeReasonDescr(TrsTask trsTask) {
        // 如果选坏品，责任部门和原因描述不能为空 责任部门-原因-来源单号(0)-备注
        if (Constants.ReasonCode.DAMAGE.getValue().equals(trsTask.getReasonCode())
                && (StringUtil.isEmpty(trsTask.getResDepart()) || StringUtil.isEmpty(trsTask.getReasonDescr()))) {
            throw new DeliveryException(DeliveryException.REVERSEPICK_TASK_REASON_IS_NULL);
        }

        return trsTask;
    }
    
    /**
     * 确认返拣前验证是否要弹出提示框(如果目标库位是锁定则提示)
     */
    public void confirm4PopWarnWindow() {
    	 popFlag = false;
         List<Object> taskList = getSelectedRowList();
         for (Object task : taskList) {
             TrsTask t = (TrsTask) task;
             //SQL从数据库中验证库位释放锁定状态
             if (locationService.isLocked(t.getRealLoc())) {
            	 popFlag = true;
            	 break;
             }
         }
    }
    
    /**
     * 打印返拣单
     */
    public void print() {
        this.printData = "[]";

        List<TrsTask> rePickTasks = getData2Print();
        ReversePickHeader rePickHeader = reversePickHeaderService.get(this.reversePickHeaderId);
        this.setPrintData(new JSONArray(printService.getRepickReport(rePickHeader,rePickTasks)).toString());
    }

    /**
     * 获取返拣单的打印数据
     * @return
     */
    private List<TrsTask> getData2Print() {
        TrsTaskFilter filter = new TrsTaskFilter();
        filter.setDocOperId(this.reversePickHeaderId);
        filter.setTaskType(TaskType.RK.getValue());
        filter.setTaskStatus(TaskStatus.RELEASED.getValue());
        return rePickTaskService.queryRePickTrs(filter);
    }

    
    /**
     * 导出返拣单
     * @throws IOException
     */
    public void export() throws IOException {
        this.printData = "[]";

        List<TrsTask> rePickTasks = getData2Print();
        ReversePickHeader rePickHeader = reversePickHeaderService.get(this.reversePickHeaderId);
        byte[] pdfData = printService.exportRepickReport(rePickHeader,rePickTasks);
        DownloadUtil.writeToResponse(pdfData, DownloadUtil.PDF,
            "repickTask_" + DateUtil.dateToString(new Date(), "yyMMddHHmmss") + ".pdf");
    }

    /**
     * 设置原因描述
     */
    public void editReasonDesc() {
        for(TrsTask trsTask : rePickTaskList){
            if(trsTask.getId().equals(taskId)) {
                trsTask.setResDepart(null);
                trsTask.setReasonMasterCode(null);
                trsTask.setReasonDescr(Dictionary.getDictionary("REASON_PA").get(trsTask.getReasonCode()));
                break;
            }
        }
    }
    
    /**
     * 根据责任部门获取相应masterCode
     */
    public void madeMasterCode(){
        //原因描述级联操作 
        for(TrsTask trsTask : rePickTaskList){
            if(trsTask.getId().equals(taskId)) {
                trsTask.setReasonDescr(null);
                trsTask.setReasonMasterCode(null);
                
                updateDmInfo(trsTask);
                
                List<CfgCodeDetail> codeList = Dictionary.findByCategory("REASON_MOVE_3");
                for (CfgCodeDetail cd : codeList) {
                    if (cd.getCodeValue().equals(trsTask.getResDepart())) {
                        trsTask.setReasonMasterCode(cd.getUuid());
                        return;
                    }
                }
            }
        }
    }
    
    /**
     * 页面上原因描述选择框出现变更
     */
    public void handelByReasonDescr() {
        for (TrsTask task : rePickTaskList) {
            if (taskId.equals(task.getId())) {
                
                updateDmInfo(task);
            }
        }
    }  
    
    /**
     * 更新4级部门、是否实物信息
     * @param dto
     */
    private void updateDmInfo(TrsTask task) {
        if (StringUtil.isNotEmpty(task.getResDepart()) && StringUtil.isNotEmpty(task.getReasonDescr())) {
            DamageInfoDTO dto = new DamageInfoDTO();
            dto.setDutyDept(task.getResDepart());
            dto.setDmReason(task.getReasonDescr());
            
            task.setHasEntity(stockBatchAttExtService.getDefaultHasEntity(dto));
            task.setDeptClass4Disable(stockBatchAttExtService.disableDeptClass4(dto));
        } else {
            task.setHasEntity(null);
            task.setDeptClass4Disable(true);
        }
        if (task.getDeptClass4Disable()) {
            task.setDeptClass4("");
        }
    }
    
    public String getPrintData() {
        return printData;
    }

    public void setPrintData(String printData) {
        this.printData = printData;
    }

    public TrsTaskFilter getTrsTaskFilter() {
        return trsTaskFilter;
    }

    public void setTrsTaskFilter(TrsTaskFilter trsTaskFilter) {
        this.trsTaskFilter = trsTaskFilter;
    }

    public Long getReversePickHeaderId() {
        return reversePickHeaderId;
    }

    public void setReversePickHeaderId(Long reversePickHeaderId) {
        this.reversePickHeaderId = reversePickHeaderId;
    }

	public List<TrsTask> getRePickTaskList() {
		return rePickTaskList;
	}

	public void setRePickTaskList(List<TrsTask> rePickTaskList) {
		this.rePickTaskList = rePickTaskList;
	}
	
    public ReversePickHeader getReversePickHeader() {
		return reversePickHeader;
	}

	public void setReversePickHeader(ReversePickHeader reversePickHeader) {
		this.reversePickHeader = reversePickHeader;
	}

	
	public boolean isPopFlag() {
		return popFlag;
	}

	
	public void setPopFlag(boolean popFlag) {
		this.popFlag = popFlag;
	}
    
    public Long getTaskId() {
        return taskId;
    }

    
    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }
}
