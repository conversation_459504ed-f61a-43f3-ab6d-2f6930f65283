package com.daxia.wms.delivery.pick.job;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.pick.service.PickService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.master.job.AbstractJob;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.util.List;
import java.util.Map;

@Name("autoPickJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class AutoPickJob extends AbstractJob {
    
    @In
    WaveService waveService;
    @In
    PickService pickService;

    
    @Override
    protected void doRun() throws Exception {
        List<Map<String, Object>> waveIdList = waveService.getAutoWave(WaveHeader.FLAG_AUTO_PICK);
        for (Map<String, Object> waveIdMap : waveIdList) {
            Long warehouseId = (Long) waveIdMap.get("warehouseId");
            Long waveId = (Long) waveIdMap.get("id");
            try {
                ParamUtil.setCurrentWarehouseId(warehouseId);
                WaveHeader waveHeader = waveService.getWave(waveId);
                if (waveHeader != null) {
                    pickService.pickByWaveId(waveHeader.getId());
                }
            } catch (Exception e) {
                log.error("Auto pick error！", e);
        
                super.processException(e);
        
                doInFailed(waveId);
            }
        }
    }
    
    private void doInFailed(Long waveId) {
        waveService.updateFailedNumber(waveId, WaveHeader.FAILED_TYPE_PICK);
    }
}