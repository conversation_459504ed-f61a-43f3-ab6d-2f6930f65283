package com.daxia.wms.delivery.print.dto;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlRootElement;

/**
 * 促销单品波次信息标签打印数据DTO
 */
@XmlRootElement
@lombok.extern.slf4j.Slf4j
public class SingleWaveLabelPrintDTO implements Serializable {

	private static final long serialVersionUID = -8157666125533061246L;

	private String waveNo;
	
	private BigDecimal totalCartons;
	
	private String productCode;
	
	private String pickLocCode;
	
	private String productBarcode;
	
	private String productName;
	
	private BigDecimal countStations;
	
	private String stationCode;
	
	private BigDecimal countCarriers;
	
	private String carrierName;
	
	private BigDecimal totalUnits;
	
	private BigDecimal salesQty;
	
	private String planShipTime;

	public String getWaveNo() {
		return waveNo;
	}

	public void setWaveNo(String waveNo) {
		this.waveNo = waveNo;
	}

	public BigDecimal getTotalCartons() {
		return totalCartons;
	}

	public void setTotalCartons(BigDecimal totalCartons) {
		this.totalCartons = totalCartons;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getPickLocCode() {
		return pickLocCode;
	}

	public void setPickLocCode(String pickLocCode) {
		this.pickLocCode = pickLocCode;
	}

	public String getProductBarcode() {
		return productBarcode;
	}

	public void setProductBarcode(String productBarcode) {
		this.productBarcode = productBarcode;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public BigDecimal getCountStations() {
		return countStations;
	}

	public void setCountStations(BigDecimal countStations) {
		this.countStations = countStations;
	}

	public String getStationCode() {
		return stationCode;
	}

	public void setStationCode(String stationCode) {
		this.stationCode = stationCode;
	}

	public BigDecimal getCountCarriers() {
		return countCarriers;
	}

	public void setCountCarriers(BigDecimal countCarriers) {
		this.countCarriers = countCarriers;
	}

	public String getCarrierName() {
		return carrierName;
	}

	public void setCarrierName(String carrierName) {
		this.carrierName = carrierName;
	}

	public BigDecimal getTotalUnits() {
		return totalUnits;
	}

	public void setTotalUnits(BigDecimal totalUnits) {
		this.totalUnits = totalUnits;
	}

	public BigDecimal getSalesQty() {
		return salesQty;
	}

	public void setSalesQty(BigDecimal salesQty) {
		this.salesQty = salesQty;
	}

	public String getPlanShipTime() {
		return planShipTime;
	}

	public void setPlanShipTime(String planShipTime) {
		this.planShipTime = planShipTime;
	}
}
