package com.daxia.wms.delivery.load.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.recheck.entity.CartonDetail;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.master.entity.*;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.CascadeType;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "doc_crdock_header")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@BatchSize(size = 20)
@Where(clause = "IS_DELETED = 0")
@lombok.extern.slf4j.Slf4j
public class TOCrossDockHeader extends WhBaseEntity {
    
    private static final long serialVersionUID = -3874600952890600738L;
    
    // 主键
    private Long id;
    
    // 发货单号
    private String doNo;
    
    // 发货单状态
    private String status;
    
    // 发运时间
    private Date shipTime;
    
    // 发货单类型：DO、调拨、RTV
    private String doType;
    
    // 冻结状态 HOLD：冻结 RELEASE：释放
    private String releaseStatus;
    
    // 拨指令号
    private String refNo1;
    
    // 关联的ASN单号
    private String refNo2;
    
    private String targetWarehouseId;
    
    // 订货数量 EXPECTED_QTY_EACH
    private BigDecimal expectedQtyEa;
    
    // 发货数量
    private BigDecimal shipQtyEa;
    
//    private Date expectedDate;
    
    private Boolean needCancel;
    
    // 收货方
    private String consigneeName;
    
    // 收货地址
    private String address;
    
    // 是否已跑波次
    private Long waveFlag;
    
    // 备注
    private String notes;
    
    // 波次号
    private Long waveId;
    
    // DO创建时间
    private Date doCreateTime;
    
    private String departureTime;
    
    private Date plantShipTime;
    
    // DO单明细
    private List<TOCrossDockDetail> toDetails;
    
    private List<CartonDetail> cartonDetails;
    
    private List<CartonHeader> cartonHeaders;
    
    private WaveHeader waveHeader;
    
    // 原始单据ID
    private String origId;
    
    //调拨单类型
    private Integer tranType;
    
    private Warehouse wareHouse;
    
    private Long sourceAsnId;

    private String holdWho;

    private String holdCode;

    private String holdReason;

    private Date holdTime;

    private Long carrierId;

    private Carrier carrier;

    private String moble;

    private String sortGridNo;

    private String postCode;

    private Date expectedArriveTime1;

    private Date  expectedReceiveTime;

    private Country countryInfo;

    private Province provinceInfo;

    private City cityInfo;

    private County countyInfo;

    // 省份
    private Long province;

    // 城市
    private Long city;

    // 区
    private Long county;

    // 国家
    private Long country;

    private Integer crossStockType;

    private BusinessCustomer businessCustomer;

    private Long targetCustomerId;


    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.AUTO)
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    @Column(name = "EDI_2")
    public String getTargetWarehouseId() {
        return targetWarehouseId;
    }

    public void setTargetWarehouseId(String targetWarehouseId) {
        this.targetWarehouseId = targetWarehouseId;
    }

    @Column(name = "NEED_CANCEL")
    public Boolean getNeedCancel() {
        return needCancel;
    }
    
    public void setNeedCancel(Boolean needCancel) {
        this.needCancel = needCancel;
    }
    
//    public Date getExpectedDate() {
//        return expectedDate;
//    }
//
//    public void setExpectedDate(Date expectedDate) {
//        this.expectedDate = expectedDate;
//    }
    
    @Column(name = "DO_NO")
    public String getDoNo() {
        return doNo;
    }
    
    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }
    
    @Column(name = "STATUS")
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    @Column(name = "SHIP_TIME")
    public Date getShipTime() {
        return shipTime;
    }
    
    public void setShipTime(Date shipTime) {
        this.shipTime = shipTime;
    }
    
    @Column(name = "DO_TYPE")
    public String getDoType() {
        return doType;
    }
    
    public void setDoType(String doType) {
        this.doType = doType;
    }
    
    @Column(name = "RELEASE_STATUS")
    public String getReleaseStatus() {
        return releaseStatus;
    }
    
    public void setReleaseStatus(String releaseStatus) {
        this.releaseStatus = releaseStatus;
    }
    
    @Column(name = "REF_NO1")
    public String getRefNo1() {
        return refNo1;
    }
    
    public void setRefNo1(String refNo1) {
        this.refNo1 = refNo1;
    }
    
    @Column(name = "REF_NO2")
    public String getRefNo2() {
        return refNo2;
    }
    
    public void setRefNo2(String refNo2) {
        this.refNo2 = refNo2;
    }
    
    @Column(name = "EXPECTED_QTY_EACH")
    public BigDecimal getExpectedQtyEa() {
        return expectedQtyEa;
    }
    
    public void setExpectedQtyEa(BigDecimal expectedQtyEa) {
        this.expectedQtyEa = expectedQtyEa;
    }
    
    @Column(name = "SHIP_QTY_EACH")
    public BigDecimal getShipQtyEa() {
        return shipQtyEa;
    }
    
    public void setShipQtyEa(BigDecimal shipQtyEa) {
        this.shipQtyEa = shipQtyEa;
    }
    
    @Column(name = "CONSIGNEE_NAME")
    public String getConsigneeName() {
        return consigneeName;
    }
    
    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }
    
    @Column(name = "ADDRESS")
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "toHeader")
    @Where(clause = " IS_DELETED = 0 ")
    @Cascade(value = {CascadeType.SAVE_UPDATE})
    public List<TOCrossDockDetail> getToDetails() {
        return toDetails;
    }
    
    public void setToDetails(List<TOCrossDockDetail> toDetails) {
        this.toDetails = toDetails;
    }
    
    @Column(name = "WAVE_FLAG")
    public Long getWaveFlag() {
        return waveFlag;
    }
    
    public void setWaveFlag(Long waveFlag) {
        this.waveFlag = waveFlag;
    }
    
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "doHeader")
    @Where(clause = " IS_DELETED = 0 ")
    public List<CartonDetail> getCartonDetails() {
        return cartonDetails;
    }
    
    public void setCartonDetails(List<CartonDetail> cartonDetails) {
        this.cartonDetails = cartonDetails;
    }
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "WAVE_ID", insertable = false, updatable = false)
    public WaveHeader getWaveHeader() {
        return waveHeader;
    }
    
    public void setWaveHeader(WaveHeader waveHeader) {
        this.waveHeader = waveHeader;
    }
    
    @Column(name = "NOTES")
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    @Column(name = "WAVE_ID")
    public Long getWaveId() {
        return waveId;
    }
    
    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }
    
    
    public void setCartonHeaders(List<CartonHeader> cartonHeaders) {
        this.cartonHeaders = cartonHeaders;
    }
    
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "doHeader")
    @Where(clause = " IS_DELETED = 0 ")
    public List<CartonHeader> getCartonHeaders() {
        return cartonHeaders;
    }
    
    /**
     * @return the origId
     */
    @Column(name = "ORIG_ID")
    public String getOrigId() {
        return origId;
    }
    
    /**
     * @param origId the origId to set
     */
    public void setOrigId(String origId) {
        this.origId = origId;
    }
    
    
    @Column(name = "DO_CREATE_TIME ")
    public Date getDoCreateTime() {
        return doCreateTime;
    }
    
    public void setDoCreateTime(Date doCreateTime) {
        this.doCreateTime = doCreateTime;
    }
    
    @Column(name = "TRAN_TYPE")
    public Integer getTranType() {
        return tranType;
    }
    
    public void setTranType(Integer tranType) {
        this.tranType = tranType;
    }
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "EDI_2", referencedColumnName = "ID", insertable = false, updatable = false)
    public Warehouse getWareHouse() {
        return wareHouse;
    }
    
    public void setWareHouse(Warehouse wareHouse) {
        this.wareHouse = wareHouse;
    }
    
    @Column(name = "ASN_HEADER_ID")
    public Long getSourceAsnId() {
        return sourceAsnId;
    }
    
    public void setSourceAsnId(Long sourceAsnId) {
        this.sourceAsnId = sourceAsnId;
    }

    @Column(name = "HOLD_WHO")
    public String getHoldWho() {
        return holdWho;
    }

    public void setHoldWho(String holdWho) {
        this.holdWho = holdWho;
    }
    @Column(name = "HOLD_CODE")
    public String getHoldCode() {
        return holdCode;
    }

    public void setHoldCode(String holdCode) {
        this.holdCode = holdCode;
    }
    @Column(name = "HOLD_REASON")
    public String getHoldReason() {
        return holdReason;
    }

    public void setHoldReason(String holdReason) {
        this.holdReason = holdReason;
    }
    @Column(name = "HOLD_TIME")
    public Date getHoldTime() {
        return holdTime;
    }

    public void setHoldTime(Date holdTime) {
        this.holdTime = holdTime;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CARRIER_ID", insertable = false, updatable = false)
    public Carrier getCarrier() {
        return carrier;
    }

    public void setCarrier(Carrier carrier) {
        this.carrier = carrier;
    }

    @Column(name = "CARRIER_ID")
    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    @Column(name = "MOBILE")
    public String getMoble() {
        return moble;
    }

    public void setMoble(String moble) {
        this.moble = moble;
    }

    @Column(name = "SORT_GRID_NO")
    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    @Column(name = "COUNTRY")
    public Long getCountry() {
        return country;
    }

    public void setCountry(Long country) {
        this.country = country;
    }

    @Column(name = "PROVINCE")
    public Long getProvince() {
        return province;
    }

    public void setProvince(Long province) {
        this.province = province;
    }

    @Column(name = "CITY")
    public Long getCity() {
        return city;
    }

    public void setCity(Long city) {
        this.city = city;
    }

    @Column(name = "COUNTY")
    public Long getCounty() {
        return county;
    }

    public void setCounty(Long county) {
        this.county = county;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "COUNTRY",insertable = false, updatable = false)
    public Country getCountryInfo() {
        return countryInfo;
    }

    public void setCountryInfo(Country countryInfo) {
        this.countryInfo = countryInfo;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PROVINCE",insertable = false, updatable = false)
    public Province getProvinceInfo() {
        return provinceInfo;
    }

    public void setProvinceInfo(Province provinceInfo) {
        this.provinceInfo = provinceInfo;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CITY",insertable = false, updatable = false)
    public City getCityInfo() {
        return cityInfo;
    }

    public void setCityInfo(City cityInfo) {
        this.cityInfo = cityInfo;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "COUNTY",insertable = false, updatable = false)
    public County getCountyInfo() {
        return countyInfo;
    }

    public void setCountyInfo(County countyInfo) {
        this.countyInfo = countyInfo;
    }

    @Column(name = "crossStock_type")
    public Integer getCrossStockType() {
        return crossStockType;
    }

    public void setCrossStockType(Integer crossStockType) {
        this.crossStockType = crossStockType;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "target_customer_id", referencedColumnName = "ID", insertable = false, updatable = false)
    public BusinessCustomer getBusinessCustomer() {
        return businessCustomer;
    }

    public void setBusinessCustomer(BusinessCustomer businessCustomer) {
        this.businessCustomer = businessCustomer;
    }
    @Column(name = "target_customer_id")
    public Long getTargetCustomerId() {
        return targetCustomerId;
    }

    public void setTargetCustomerId(Long targetCustomerId) {
        this.targetCustomerId = targetCustomerId;
    }
}