package com.daxia.wms.delivery.deliveryorder.service;

import java.util.Date;

import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;

public interface DoCutOffService {

    /**
     * 计算订单预计出库时间
     * 方法会访问doHeader.getStation();
     * @param doHeader
     * @return
     */
    Date calculateCutOffTime(DeliveryOrderHeader doHeader);

    /**
     * 简单计算订单预计出库时间，取处理时间后的整点时间，比如：创建时间为：13:05，那么期望时间为：17:00
     * @param doHeader
     * @return
     */
    public Date easyCalculate(DeliveryOrderHeader doHeader);
}
