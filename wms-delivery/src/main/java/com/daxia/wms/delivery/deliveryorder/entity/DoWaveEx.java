package com.daxia.wms.delivery.deliveryorder.entity;

import javax.persistence.*;

import com.daxia.wms.master.entity.GroupRule;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;

import java.util.Set;

/**
 * 发货单头扩展实体
 */
@Entity
@Table(name = "doc_do_wave_ex")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_do_wave_ex set is_deleted = 1 where id = ? and version = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class DoWaveEx extends WhBaseEntity {
    
    private static final long serialVersionUID = -8894722359616209501L;
    
    private Long id;
    
    /**
     * 发货单头ID
     */
    private Long doHeaderId;
    
    /**
     * 波次规则指定商品、库区ID
     */
    private Long waveCriteriaExId;
    
    /**
     * 自动波次类型
     */
    private Integer autoWaveType;
    
    /**
     * 顺丰主单号
     */
    private String trackingNo;
    /**
     * 始发地代码
     */
    private String originCode;
    /**
     * 始发地名称
     */
    private String originName;
    /**
     * 目的地代码
     */
    private String destinationCode;
    /**
     * 目的地名称
     */
    private String destinationName;

    // 大头笔
    private String shortAddress;

    // 集包地名
    private String packageCenterName;

    private String specialLabelCode;
    
    /**
     * 团购倍数；
     */
    private Integer groupTimes;
    
    private GroupRule groupRule;
    
    private DeliveryOrderHeader deliveryOrderHeader;
    
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.AUTO)
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "WAVE_CRITERIA_EX_ID", insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public GroupRule getGroupRule() {
        return groupRule;
    }
    
    public void setGroupRule(GroupRule groupRule) {
        this.groupRule = groupRule;
    }
    
    @Column(name = "DO_H_ID", insertable = false, updatable = false)
    public Long getDoHeaderId() {
        return doHeaderId;
    }
    
    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }
    
    @Column(name = "WAVE_CRITERIA_EX_ID")
    public Long getWaveCriteriaExId() {
        return waveCriteriaExId;
    }
    
    public void setWaveCriteriaExId(Long waveCriteriaExId) {
        this.waveCriteriaExId = waveCriteriaExId;
    }
    
    @Column(name = "AUTO_WAVE_TYPE")
    public Integer getAutoWaveType() {
        return autoWaveType;
    }
    
    public void setAutoWaveType(Integer autoWaveType) {
        this.autoWaveType = autoWaveType;
    }
    
    @Column(name = "TRACKING_NO")
    public String getTrackingNo() {
        return trackingNo;
    }
    
    public void setTrackingNo(String trackingNo) {
        this.trackingNo = trackingNo;
    }
    
    @Column(name = "ORIGIN_CODE")
    public String getOriginCode() {
        return originCode;
    }
    
    public void setOriginCode(String originCode) {
        this.originCode = originCode;
    }
    
    @Column(name = "DESTINATION_CODE")
    public String getDestinationCode() {
        return destinationCode;
    }
    
    public void setDestinationCode(String destinationCode) {
        this.destinationCode = destinationCode;
    }
    
    @Column(name = "SPECIAL_LABEL_CODE")
    public String getSpecialLabelCode() {
        return specialLabelCode;
    }
    
    public void setSpecialLabelCode(String specialLabelCode) {
        this.specialLabelCode = specialLabelCode;
    }
    
    @Column(name = "group_times")
    public Integer getGroupTimes() {
        return groupTimes;
    }
    
    public void setGroupTimes(Integer groupTimes) {
        this.groupTimes = groupTimes;
    }
    
    @OneToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "do_h_id")
    public DeliveryOrderHeader getDeliveryOrderHeader() {
        return deliveryOrderHeader;
    }
    
    public void setDeliveryOrderHeader(DeliveryOrderHeader deliveryOrderHeader) {
        this.deliveryOrderHeader = deliveryOrderHeader;
    }

    @Column(name = "origin_name")
    public String getOriginName() {
        return originName;
    }

    public void setOriginName(String originName) {
        this.originName = originName;
    }
    @Column(name = "destination_name")
    public String getDestinationName() {
        return destinationName;
    }

    public void setDestinationName(String destinationName) {
        this.destinationName = destinationName;
    }
    @Column(name = "short_address")
    public String getShortAddress() {
        return shortAddress;
    }

    public void setShortAddress(String shortAddress) {
        this.shortAddress = shortAddress;
    }
    @Column(name = "package_center_name")
    public String getPackageCenterName() {
        return packageCenterName;
    }

    public void setPackageCenterName(String packageCenterName) {
        this.packageCenterName = packageCenterName;
    }
}