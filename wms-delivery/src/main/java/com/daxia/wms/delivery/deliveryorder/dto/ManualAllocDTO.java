package com.daxia.wms.delivery.deliveryorder.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 人工分配DTO
 */
@lombok.extern.slf4j.Slf4j
public class ManualAllocDTO implements Serializable {

	private static final long serialVersionUID = -2805508132908756277L;
	private Long stockId;
	//分配数量
	private BigDecimal allocingQty;
	//原分配数量
	private BigDecimal allocatedQty;
	private BigDecimal allocatedQtyPcs;
	private BigDecimal allocatedQtyUnit;
	private String lotNo;
	private String locCode;
	private String locType;
	private String locPackageType;
	private String lpnNo;
	private String productCode;
	private String ean13;
	private String productName;
	private String partion;
	private BigDecimal stockQty;
	private BigDecimal stockQtyUnit;
	private String uom;
	private String lotatt01;
	private String lotatt02;
	private String lotatt03;
	private String lotatt05;
	private String lotatt07;
	private String lotatt08;
	private String supplier;
	private String merchant;
	private Long picTaskId;
	private String lotatt10;
	private String lotatt16;
	private String lotatt14;

	public String getLotatt16() {
		return lotatt16;
	}

	public void setLotatt16(String lotatt16) {
		this.lotatt16 = lotatt16;
	}

	public BigDecimal getAllocatedQtyPcs() {
		return allocatedQtyPcs;
	}

	public void setAllocatedQtyPcs(BigDecimal allocatedQtyPcs) {
		this.allocatedQtyPcs = allocatedQtyPcs;
	}

	public Long getStockId() {
		return stockId;
	}
	
	public void setStockId(Long stockId) {
		this.stockId = stockId;
	}
	
	public BigDecimal getAllocingQty() {
		return allocingQty;
	}
	
	public void setAllocingQty(BigDecimal allocingQty) {
		this.allocingQty = allocingQty;
	}
	
	public BigDecimal getAllocatedQty() {
		return allocatedQty;
	}
	
	public void setAllocatedQty(BigDecimal allocatedQty) {
		this.allocatedQty = allocatedQty;
	}
	
	public String getLotNo() {
		return lotNo;
	}
	
	public void setLotNo(String lotNo) {
		this.lotNo = lotNo;
	}
	
	public String getLocCode() {
		return locCode;
	}
	
	public void setLocCode(String locCode) {
		this.locCode = locCode;
	}
	
	public String getLpnNo() {
		return lpnNo;
	}
	
	public void setLpnNo(String lpnNo) {
		this.lpnNo = lpnNo;
	}
	
	public String getProductCode() {
		return productCode;
	}
	
	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}
	
	public String getEan13() {
		return ean13;
	}
	
	public void setEan13(String ean13) {
		this.ean13 = ean13;
	}
	
	public String getProductName() {
		return productName;
	}
	
	public void setProductName(String productName) {
		this.productName = productName;
	}
	
	public String getPartion() {
		return partion;
	}
	
	public void setPartion(String partion) {
		this.partion = partion;
	}
	
	public BigDecimal getStockQty() {
		return stockQty;
	}
	
	public void setStockQty(BigDecimal stockQty) {
		this.stockQty = stockQty;
	}

	public BigDecimal getStockQtyUnit() {
		return stockQtyUnit;
	}

	public void setStockQtyUnit(BigDecimal stockQtyUnit) {
		this.stockQtyUnit = stockQtyUnit;
	}

	public String getUom() {
		return uom;
	}
	
	public void setUom(String uom) {
		this.uom = uom;
	}
	
	public String getLotatt01() {
		return lotatt01;
	}
	
	public void setLotatt01(String lotatt01) {
		this.lotatt01 = lotatt01;
	}
	
	public String getLotatt02() {
		return lotatt02;
	}
	
	public void setLotatt02(String lotatt02) {
		this.lotatt02 = lotatt02;
	}
	
	public String getLotatt03() {
		return lotatt03;
	}
	
	public void setLotatt03(String lotatt03) {
		this.lotatt03 = lotatt03;
	}
	
	public String getLotatt05() {
		return lotatt05;
	}
	
	public void setLotatt05(String lotatt05) {
		this.lotatt05 = lotatt05;
	}
	
	public String getLotatt07() {
		return lotatt07;
	}
	
	public void setLotatt07(String lotatt07) {
		this.lotatt07 = lotatt07;
	}
	
	public String getLotatt08() {
		return lotatt08;
	}
	
	public void setLotatt08(String lotatt08) {
		this.lotatt08 = lotatt08;
	}
	
	public String getSupplier() {
		return supplier;
	}
	
	public void setSupplier(String supplier) {
		this.supplier = supplier;
	}
	
	public String getMerchant() {
		return merchant;
	}
	
	public void setMerchant(String merchant) {
		this.merchant = merchant;
	}

	
	public Long getPicTaskId() {
		return picTaskId;
	}

	
	public void setPicTaskId(Long picTaskId) {
		this.picTaskId = picTaskId;
    }

    public String getLotatt10() {
        return lotatt10;
    }

    public void setLotatt10(String lotatt10) {
        this.lotatt10 = lotatt10;
    }

	public BigDecimal getAllocatedQtyUnit() {
		return allocatedQtyUnit;
	}

	public void setAllocatedQtyUnit(BigDecimal allocatedQtyUnit) {
		this.allocatedQtyUnit = allocatedQtyUnit;
	}

	public String getLocPackageType() {
		return locPackageType;
	}

	public void setLocPackageType(String locPackageType) {
		this.locPackageType = locPackageType;
	}

	public String getLocType() {
		return locType;
	}

	public void setLocType(String locType) {
		this.locType = locType;
	}

	public String getLotatt14() {
		return lotatt14;
	}

	public void setLotatt14(String lotatt14) {
		this.lotatt14 = lotatt14;
	}
}
