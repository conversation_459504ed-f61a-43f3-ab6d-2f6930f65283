package com.daxia.wms.delivery.wave.action;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.ActionBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.wms.delivery.pick.dto.PickInfoDTO;
import com.daxia.wms.delivery.util.TimeShowUtil;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;

@Name("com.daxia.wms.delivery.wavePickInfoAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class WavePickInfoAction extends ActionBean {

    private static final long serialVersionUID = -4362106518202046071L;

    private Long waveId;
    
    private  WaveHeader waveHeader;
    
    //波次拣货总数量
    private BigDecimal pktQtySum = BigDecimal.ZERO;
    
    //拣货时间From
    private Date pickTimeFrom;
    
    //拣货时间To
    private Date pickTimeTo;
    
    //拣货已用时间  
    private String pickTimeUsed;
    
    private List<PickInfoDTO> pickInfoLIst;

    @In
    WaveService waveService;
    
    public WavePickInfoAction() {
        super();
    }

    /**
     * 重置所有变量的值
     */
    private void reset() {
        waveHeader = null;
        pickInfoLIst = null;
        pktQtySum = BigDecimal.ZERO;
        pickTimeFrom = null;
        pickTimeTo = null;
        pickTimeUsed = null;
    }
    
    public void initialize() {
        reset();
        
        waveHeader = waveService.getWave(waveId);

        for (PickInfoDTO dto : pickInfoLIst) {
            pktQtySum = pktQtySum.add(dto.getPickNum());

            Date dtoPickTimeF = dto.getPickTimeFrom();
            if (dtoPickTimeF != null) {
                if (pickTimeFrom == null || (pickTimeFrom.compareTo(dtoPickTimeF) > 0)) {
                    pickTimeFrom = dtoPickTimeF;
                }
            }

            Date dtoPickTimeT = dto.getPickTimeTo();
            dtoPickTimeT = null == dtoPickTimeT ? DateUtil.getNowTime() : dtoPickTimeT;
            if (null == pickTimeTo || (pickTimeTo.compareTo(dtoPickTimeT) < 0)) {
                pickTimeTo = dtoPickTimeT;
            }
            
        }

        if (pickTimeFrom != null) {
            Integer[] times = TimeShowUtil.calculateTime(pickTimeFrom, pickTimeTo);
            pickTimeUsed = TimeShowUtil.getShowTime(times);
        }
    }

    public Long getWaveId() {
        return waveId;
    }

    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }

    public List<PickInfoDTO> getPickInfoLIst() {
        return pickInfoLIst;
    }

    public void setPickInfoLIst(List<PickInfoDTO> pickInfoLIst) {
        this.pickInfoLIst = pickInfoLIst;
    }

    public WaveHeader getWaveHeader() {
        return waveHeader;
    }

    public void setWaveHeader(WaveHeader waveHeader) {
        this.waveHeader = waveHeader;
    }

    public BigDecimal getPktQtySum() {
        return pktQtySum;
    }

    public void setPktQtySum(BigDecimal pktQtySum) {
        this.pktQtySum = pktQtySum;
    }

    public Date getPickTimeFrom() {
        return pickTimeFrom;
    }

    public void setPickTimeFrom(Date pickTimeFrom) {
        this.pickTimeFrom = pickTimeFrom;
    }

    public String getPickTimeUsed() {
        return pickTimeUsed;
    }

    public void setPickTimeUsed(String pickTimeUsed) {
        this.pickTimeUsed = pickTimeUsed;
    }
}
