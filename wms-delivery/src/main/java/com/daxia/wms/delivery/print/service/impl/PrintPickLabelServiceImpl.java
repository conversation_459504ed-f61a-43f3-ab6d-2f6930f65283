package com.daxia.wms.delivery.print.service.impl;

import com.daxia.framework.common.service.ReportGenerator;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.PrintTemplateUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.service.PickHeaderService;
import com.daxia.wms.delivery.print.dto.PickLabelPrintDTO;
import com.daxia.wms.delivery.print.service.PrintPickLabelService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.master.entity.BusinessCustomer;
import com.daxia.wms.master.entity.SortingBin;
import com.daxia.wms.master.service.SortingBinService;
import com.daxia.wms.master.service.impl.AbstractPrintService;
import com.daxia.wms.print.PrintConstants;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 *  打印拣货标签业务实现类
 */
@Name("com.daxia.wms.delivery.printPickLabelService")
@lombok.extern.slf4j.Slf4j
public class PrintPickLabelServiceImpl extends AbstractPrintService<PickLabelPrintDTO> implements PrintPickLabelService {
    @In
    private PickHeaderService pickHeaderService;
    @In
    private ReportGenerator reportGenerator;
    @In
    private WaveService waveService;
    @In
    private SortingBinService sortingBinService;
    @Override
    public String printPickLabel(List<Long> waveIds) {
    
        List<PickLabelPrintDTO> pktLabelPrintDTOs = Lists.newArrayList();
        for (Long waveId : waveIds) {
            pktLabelPrintDTOs.addAll(buildPickLabelPrintDTO(waveId));
        }
    
        return PrintTemplateUtil.process(PrintConstants.PrintTemplate.PKTLABEL.name(), ImmutableMap.of("dtos", pktLabelPrintDTOs));
    }
    
    /**
     * 构造波次下所有拣货标签打印数据dto
     * @param waveId
     * @return
     */
    @Override
    public List<PickLabelPrintDTO> buildPickLabelPrintDTO(Long waveId) {
        List<PickHeader> pktHeaders = pickHeaderService.getPktHeadersByWaveId(waveId);
        WaveHeader wave = waveService.getWave(waveId);
        if (null == wave || pktHeaders.isEmpty()) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }

        String customerName = null;
        if(wave.getDoHeaders().size() ==  1){
            BusinessCustomer customer = wave.getDoHeaders().get(0).getBusinessCustomer();
            if(null != customer ){
                customerName = customer.getCustomerName();
            }
        }

        List<PickLabelPrintDTO> dtoList = new LinkedList<PickLabelPrintDTO>();
        //拣货单按区域排序
        Collections.sort(pktHeaders, new Comparator<PickHeader>() {
            @Override
            public int compare(PickHeader o1, PickHeader o2) {
                return o1.getRegion().getRegionCode()
                        .compareTo(o2.getRegion().getRegionCode());
            }
        });
        //一次性取得波次下的各拣货单对应的所有库区code信息
        Map<Long, List<String>> map = pickHeaderService.findPktPartitionCodesInWave(waveId);
        //设置分拣柜号
        SortingBin sortingBin = wave.getSortingBin();
        String sortingZoneNo = sortingBin == null ? null : sortingBin.getSortingZoneNbr();
        //获取波次最早预计出库时间
        Date ePlanShipTime = wave.getEstDoFinishTime();
        int skuTypeCount =
            wave.getDoHeaders().stream().flatMap(doh -> doh.getDoDetails().stream().map(DeliveryOrderDetail::getSkuId))
                .collect(Collectors.toSet()).size();
        int skuCount=
                wave.getDoHeaders().stream().flatMap(doh -> doh.getDoDetails().stream().map(DeliveryOrderDetail::getAllocatedQty))
                        .reduce(BigDecimal.ZERO,BigDecimal::add).intValue();
        int i = 0;
        for (PickHeader pktHeader : pktHeaders) {
            PickLabelPrintDTO pktLabelPrintDTO = new PickLabelPrintDTO();
            pktLabelPrintDTO.setCustomerName(customerName);
            pktLabelPrintDTO.setPktNo(pktHeader.getPktNo());
            pktLabelPrintDTO.setWaveNo(wave.getWaveNo());
            pktLabelPrintDTO.setSortingZoneNo(sortingZoneNo);
            pktLabelPrintDTO.setWavePriority(wave.getPriority());
            pktLabelPrintDTO.setCarrierName(waveService.getDistinctCarrierName(wave.getId()));
            //设置拣货区域code
            String regionCode = pktHeader.getRegion() == null ? null : pktHeader.getRegion().getRegionCode();
            pktLabelPrintDTO.setRegionCode(regionCode);
            // 设置拣货库区
            pktLabelPrintDTO.setPktPartitionCodes(buildPktPartitionCodes(pktHeader.getId(), map));
            // 设置最早预计出库时间
            pktLabelPrintDTO.setPlanShipTime(ePlanShipTime);
            //根据配置项来决定拣货标签是否显示units总数，总重量，总体积信息
            boolean isShowDetails = Constants.YesNo.YES.getValue().equals(SystemConfig.getConfigValueInt("print.pktLabel.isShowDetails", ParamUtil.getCurrentWarehouseId()));
            if (isShowDetails) {
                //设置拣货单的汇总信息，包括units总数，总重量，总体积
                Map<String, BigDecimal> totalMap = pickHeaderService.calcTotalInfo(pktHeader.getId());
                if (!totalMap.isEmpty()) {
                    pktLabelPrintDTO.setUnitsQty(totalMap.get("totalUnits"));
                    BigDecimal totalGtWeight = totalMap.get("totalGtWeight");
                    BigDecimal totalVolume = totalMap.get("totalVolume");
                    if (totalGtWeight != null) {
                        pktLabelPrintDTO.setTotalGtWeghit(totalGtWeight.setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                    if (totalVolume != null) {
                        pktLabelPrintDTO.setTotalVolume(totalVolume.setScale(0, BigDecimal.ROUND_HALF_UP));
                    }
                }
            }
            pktLabelPrintDTO.setOrderCount(wave.getDoCount());
            pktLabelPrintDTO.setSkuCount(skuCount);
            pktLabelPrintDTO.setSkuTypeCount(skuTypeCount);
            dtoList.add(pktLabelPrintDTO);
            i++;
        }
        return dtoList;
    }
    
    /**
     * 获取拣货单下拣货任务的所有库区的code，去重复，并拼接成字符串
     * @param pktHeaderId 拣货单头Id
     * @param map 拣货单及及库区codes
     * @return 拼接后的库区编码
     */
    private String buildPktPartitionCodes(Long pktHeaderId, Map<Long, List<String>> map) {
        List<String> codeList = map.get(pktHeaderId);
        return ListUtil.collection2String(codeList, ", ");
    }
}
