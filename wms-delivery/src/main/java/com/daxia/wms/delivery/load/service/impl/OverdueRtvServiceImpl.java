package com.daxia.wms.delivery.load.service.impl;

import com.daxia.framework.common.service.Dictionary;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.wms.Constants.OverdueRtvStatus;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.load.dao.OverdueRtvDAO;
import com.daxia.wms.delivery.load.entity.OverdueRtv;
import com.daxia.wms.delivery.load.filter.OverdueRtvFilter;
import com.daxia.wms.delivery.load.service.OverdueRtvService;
import com.daxia.wms.master.entity.Supplier;
import com.daxia.wms.master.service.CategoryService;
import com.daxia.wms.master.service.SupplierService;
import jodd.util.StringUtil;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Description:逾期退单管理Service实现类
 */
@Name("com.daxia.wms.delivery.overdueRtvService")
@lombok.extern.slf4j.Slf4j
public class OverdueRtvServiceImpl implements OverdueRtvService {

	@In
	OverdueRtvDAO overdueRtvDAO;

	@In
	DeliveryOrderService deliveryOrderService;

	@In
	SupplierService supplierService;

	@In
	CategoryService categoryService;

	@Override
	public DataPage<OverdueRtv> findOverdueRtvs(OverdueRtvFilter filter,
			int startIndex, int pageSize) {
		return overdueRtvDAO.findRangeByFilter(filter, startIndex, pageSize);
	}

	@Override
	@Transactional
	public void saveOverdueRtv(OverdueRtv overdueRtv) {
		overdueRtvDAO.update(overdueRtv);
	}

	@Override
	@Transactional
	public void createOverdueRtvs(Long doHeaderId) {
		DeliveryOrderHeader deliveryOrderHeader = deliveryOrderService.getDoHeaderById(doHeaderId);
		Supplier supplier = supplierService.getSupplier(deliveryOrderHeader.getSupplierId());
		for (DeliveryOrderDetail deliveryOrderDetail : deliveryOrderHeader.getDoDetails()) {
			OverdueRtv overdueRtv = new OverdueRtv();
			overdueRtv.setRtvNo(deliveryOrderHeader.getDoNo());
			overdueRtv.setSourceNo(deliveryOrderHeader.getRefNo1());
			overdueRtv.setProductCode(deliveryOrderDetail.getSku().getProductCode());
			overdueRtv.setProductBarCode(deliveryOrderDetail.getSku().getEan13());
			overdueRtv.setProdctCName(deliveryOrderDetail.getSku().getProductCname());
			overdueRtv.setCategoryLevel1(categoryService.getTopCategory1Name(
					deliveryOrderDetail.getSku().getCategoryId()));
			overdueRtv.setCategoryLevel2(categoryService.getTopCategory2Name(
					deliveryOrderDetail.getSku().getCategoryId()));
			overdueRtv.setIsDamage(deliveryOrderDetail.getIsDamaged().toString());
			overdueRtv.setRetQty(deliveryOrderDetail.getExpectedQty());
			overdueRtv.setActQty(deliveryOrderDetail.getShippedQty());
			overdueRtv.setSupplierName(supplier.getSupplierCompanyName());
			overdueRtv.setDeliveryTime(DateUtil.getNowTime());
			overdueRtv.setStatus(OverdueRtvStatus.INITIAL.getValue());
			overdueRtvDAO.save(overdueRtv);
		}
	}

	@Override
	@Transactional
	public void batchAuditOverdueRtvs(List<Long> overdueRtvIds, String notes) {
		List<OverdueRtv> overdueRtvs = new ArrayList<OverdueRtv>();
		for (Long id : overdueRtvIds) {
			OverdueRtv overdueRtv = overdueRtvDAO.get(id);
			if (!OverdueRtvStatus.HANDLING.getValue().equals(overdueRtv.getStatus())) {
				throw new DeliveryException(DeliveryException.ERROR_OVERDUERTV_ONLYAUDITHANDLINGBILL);
			}
			if (StringUtil.isEmpty(overdueRtv.getDealWay())) {
				throw new DeliveryException(DeliveryException.ERROR_OVERDUERTV_MUSTCHOSEDEALWAYBEFOREAUDIT);
			}
			if (StringUtil.isNotEmpty(notes)) {
				overdueRtv.setNotes(notes);
			}
			overdueRtv.setStatus(OverdueRtvStatus.DONE.getValue());
			overdueRtvs.add(overdueRtv);
		}
		for (OverdueRtv overdueRtv : overdueRtvs) {
			overdueRtvDAO.update(overdueRtv);
		}
	}

	@Override
	public byte[] exportExcel(List<Long> ids) throws IOException {
		List<OverdueRtv> overdueRtvList = this.getOverdueRtv(ids);
		return this.generateOverdueRtv(overdueRtvList);
	}

	private byte[] generateOverdueRtv(List<OverdueRtv> overdueRtvList) throws IOException {
		HSSFWorkbook workbook = new HSSFWorkbook();
		HSSFSheet sheet = workbook.createSheet("逾期退单管理");
		HSSFCellStyle style = setBorder(workbook);
		HSSFRow row = sheet.createRow(0);
		createCell(row, (short) 0, new HSSFRichTextString("RTV单号"), style);
		createCell(row, (short) 1, new HSSFRichTextString("来源单号"), style);
		createCell(row, (short) 2, new HSSFRichTextString("商品编码"), style);
		createCell(row, (short) 3, new HSSFRichTextString("商品条码"), style);
		createCell(row, (short) 4, new HSSFRichTextString("商品名称"), style);
		createCell(row, (short) 5, new HSSFRichTextString("一级分类"), style);
		createCell(row, (short) 6, new HSSFRichTextString("二级分类"), style);
		createCell(row, (short) 7, new HSSFRichTextString("是否不合格品"), style);
		createCell(row, (short) 8, new HSSFRichTextString("应退数量"), style);
		createCell(row, (short) 9, new HSSFRichTextString("实退数量"), style);
		createCell(row, (short) 10, new HSSFRichTextString("供应商名称"), style);
		createCell(row, (short) 11, new HSSFRichTextString("创建日期"), style);
		createCell(row, (short) 12, new HSSFRichTextString("出库日期"), style);
		createCell(row, (short) 13, new HSSFRichTextString("状态"), style);
		createCell(row, (short) 14, new HSSFRichTextString("处理方式"), style);
		createCell(row, (short) 15, new HSSFRichTextString("操作员"), style);
		createCell(row, (short) 16, new HSSFRichTextString("操作时间"), style);
		createCell(row, (short) 17, new HSSFRichTextString("备注"), style);

		Map<String, String> status = Dictionary.getDictionary("OVERDUE_RTV_STATUS");
		Map<String, String> dealWay = Dictionary.getDictionary("OVERDUE_RTV_DEALWAY");
		// 创建数据
		for (int i = 0; i < overdueRtvList.size(); i++) {
			OverdueRtv dto = overdueRtvList.get(i);
			row = sheet.createRow(i + 1);
			createCell(row, (short) 0, new HSSFRichTextString(dto.getRtvNo()),style);
			createCell(row, (short) 1, new HSSFRichTextString(dto.getSourceNo()), style);
			createCell(row, (short) 2, new HSSFRichTextString(dto.getProductCode()), style);
			createCell(row, (short) 3, new HSSFRichTextString(dto.getProductBarCode()), style);
			createCell(row, (short) 4, new HSSFRichTextString(dto.getProdctCName()), style);
			createCell(row, (short) 5, new HSSFRichTextString(dto.getProdctCName()), style);
			createCell(row, (short) 5, new HSSFRichTextString(dto.getCategoryLevel1()), style);
			createCell(row, (short) 6, new HSSFRichTextString(dto.getCategoryLevel2()), style);
			createCell(row, (short) 7, new HSSFRichTextString(dto.getIsDamage().equals("1") ? "是" : "否"), style);
			createCell(row, (short) 8, new HSSFRichTextString(dto.getRetQty().toString()), style);
			createCell(row, (short) 9, new HSSFRichTextString(dto.getActQty().toString()), style);
			createCell(row, (short) 10, new HSSFRichTextString(dto.getSupplierName()), style);
			createCell(row, (short) 11, new HSSFRichTextString(DateUtil.dateToString(
							dto.getCreatedAt(), DateUtil.DATETIME_PATTERN)),style);
			createCell(row, (short) 12, new HSSFRichTextString(DateUtil.dateToString(
							dto.getDeliveryTime(), DateUtil.DATETIME_PATTERN)),style);
			createCell(row, (short) 13, new HSSFRichTextString(status.get(dto.getStatus())), style);
			createCell(row, (short) 14, new HSSFRichTextString(dealWay.get(dto.getDealWay())), style);
			createCell(row, (short) 15, new HSSFRichTextString(dto.getUpdatedBy()), style);
			createCell(row, (short) 16, new HSSFRichTextString(DateUtil.dateToString(
							dto.getUpdatedAt(), DateUtil.DATETIME_PATTERN)), style);
			createCell(row, (short) 17, new HSSFRichTextString(dto.getNotes()), style);
		}
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		workbook.write(baos);
		return baos.toByteArray();

	}

	/**
	 * 设置边框
	 * 
	 * @param workbook
	 * @return
	 */
	private static HSSFCellStyle setBorder(HSSFWorkbook workbook) {
		HSSFCellStyle cellStyle = workbook.createCellStyle();
		cellStyle.setBorderBottom(BorderStyle.THIN);
		short index = HSSFColor.HSSFColorPredefined.BLACK.getIndex();
		cellStyle.setBottomBorderColor(index);
		cellStyle.setBorderLeft(BorderStyle.THIN);
		cellStyle.setLeftBorderColor(index);
		cellStyle.setBorderRight(BorderStyle.THIN);
		cellStyle.setRightBorderColor(index);
		cellStyle.setBorderTop(BorderStyle.THIN);
		cellStyle.setTopBorderColor(index);
		cellStyle.setWrapText(true);// 自动换行

		return cellStyle;
	}

	/**
	 * 创建单元格
	 * 
	 * @param row
	 * @param index
	 * @param content
	 * @param style
	 */
	private static void createCell(HSSFRow row, short index,
			HSSFRichTextString content, HSSFCellStyle style) {
		HSSFCell cell = row.createCell(index);
		cell.setCellValue(content);
		cell.setCellStyle(style);
	}

	/**
	 * 根据ids获取overdueRtvList
	 */
	private List<OverdueRtv> getOverdueRtv(List<Long> ids) {
		return overdueRtvDAO.getByKeys(ids);
	}
}
