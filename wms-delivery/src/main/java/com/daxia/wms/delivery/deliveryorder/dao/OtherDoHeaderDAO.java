package com.daxia.wms.delivery.deliveryorder.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.delivery.deliveryorder.entity.OtherDoHeader;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

@Name("com.daxia.wms.delivery.otherDoHeaderDAO")
@lombok.extern.slf4j.Slf4j
public class OtherDoHeaderDAO extends HibernateBaseDAO<OtherDoHeader, Long> {

    private static final long serialVersionUID = 7411075726855990225L;

    public Boolean existsByDoCode(String doNo, Long warehouseId) {
        String hql = "select COUNT(id) FROM OtherDoHeader doHeader WHERE doHeader.doNo = :doNo and doHeader.warehouseId = :whId";
        Query query = this.createQuery(hql);
        query.setString("doNo", doNo);
        query.setLong("whId", warehouseId);
        return Integer.valueOf(query.uniqueResult().toString()) > 0 ? true : false;
    }

}