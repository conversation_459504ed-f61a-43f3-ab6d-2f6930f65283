package com.daxia.wms.delivery.print.action;

import com.daxia.framework.common.action.ActionBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.PrintTemplateUtil;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.print.dto.PrintDoDTO;
import com.daxia.wms.delivery.print.service.PrintDoService;
import com.daxia.wms.print.PrintConstants;
import com.google.common.collect.Lists;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Name("com.daxia.wms.delivery.printDoAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class PrintDoAction extends ActionBean implements Serializable {
    private String printData;

    private List<Long> doIds = Lists.newArrayList(6370L, 6369L);

    @In
    PrintDoService printDoService;

    public void genData() {
        printData = "";

        printData = printDoService.genData(doIds);
    }

    public String getPrintData() {
        return printData;
    }

    public void setPrintData(String printData) {
        this.printData = printData;
    }
}
