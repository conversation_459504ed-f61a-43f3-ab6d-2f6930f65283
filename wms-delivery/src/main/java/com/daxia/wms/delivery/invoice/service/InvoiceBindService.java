package com.daxia.wms.delivery.invoice.service;

import java.util.List;

import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;
import com.daxia.wms.delivery.invoice.filter.InvoiceNoFilter;

/**
 * 发票绑定service
 */
public interface InvoiceBindService {
	/**
	 * 查询发票号码信息
	 * 
	 * @param filter
	 * @return
	 */
	public List<InvoiceNo> findInvoiceNoByFilter( InvoiceNoFilter filter );

    /**
     * 修改发票号绑定
     * 注意：该方法会调用接口的sendVoidedInvoice2FinanceCreateDatas创建接口数据，调用前请确认是否需要
     * @param invoiceHeaderId
     *            发票号ID
     * @param newInvoiceNo
     *            新发票号码
     */
    public void modifyBindInvoiceNo(Long invoiceHeaderId, String newInvoiceNo, String oldInvoiceNo);

    /**
     * 检查do状态是否可以进行发票号操作
     * 
     * @param doHeader
     */
    public void checkDoForInvoiceOperate(DeliveryOrderHeader doHeader);

    /**
     * 检查do状态、原发票号码状态是否可以进行发票号操作
     * 
     * @param doHeader
     */
    public InvoiceNo checkDoAndNo(DeliveryOrderHeader doHeader, String oldInvoiceNo);
}
