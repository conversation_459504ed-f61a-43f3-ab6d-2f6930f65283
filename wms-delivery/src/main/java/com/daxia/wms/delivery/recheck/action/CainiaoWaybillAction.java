package com.daxia.wms.delivery.recheck.action;

import com.daxia.framework.common.action.ActionBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.recheck.dto.CainiaoWaybillDTO;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.recheck.service.impl.carton.CainiaoWayBillCancel;
import com.daxia.wms.delivery.recheck.service.impl.carton.CainiaoWayBillSearch;
import com.daxia.wms.master.entity.Carrier;
import com.daxia.wms.master.entity.ShopInfo;
import com.daxia.wms.master.service.CarrierService;
import com.daxia.wms.master.service.ShopInfoService;
import com.daxia.wms.waybill.cainiao.CainiaoConfig;
import com.daxia.wms.waybill.cainiao.CainiaoConstants;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

@Name("com.daxia.wms.delivery.cainiaoWaybillAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class CainiaoWaybillAction extends ActionBean {

    @In
    CainiaoWayBillSearch cainiaoWayBillSearch;
    @In
    CainiaoWayBillCancel cainiaoWayBillCancel;
    @In
    CartonService cartonService;
    @In
    CarrierService carrierService;
    @In
    ShopInfoService shopInfoService;

    private List<CainiaoWaybillDTO> cainiaoWaybillDTOs;

    private Long carrierId;

    private String cartonNo;

    private List<SelectItem> carrierSelector = new ArrayList<SelectItem>();
    
    @Create
    public void init() {
        List<Carrier> carriers = carrierService.getByWaybillType(Constants.WaybillType.CAINIAO);
        
        carrierSelector.clear();
        for (Carrier carrier : carriers) {
            SelectItem item = new SelectItem();
            item.setValue(carrier.getId());
            item.setLabel(carrier.getDistSuppCompName());
            carrierSelector.add(item);
        }
    }
    
    public void query() {
        cainiaoWaybillDTOs = cainiaoWayBillSearch.reqeust(carrierId);
    }

    public String getCartonNo() {
        return cartonNo;
    }

    public void setCartonNo(String cartonNo) {
        this.cartonNo = cartonNo;
    }
    
    public Long getCarrierId() {
        return carrierId;
    }
    
    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }
    
    public List<CainiaoWaybillDTO> getCainiaoWaybillDTOs() {
        return cainiaoWaybillDTOs;
    }

    public void setCainiaoWaybillDTOs(List<CainiaoWaybillDTO> cainiaoWaybillDTOs) {
        this.cainiaoWaybillDTOs = cainiaoWaybillDTOs;
    }
    
    public List<SelectItem> getCarrierSelector() {
        return carrierSelector;
    }
    
    public void setCarrierSelector(List<SelectItem> carrierSelector) {
        this.carrierSelector = carrierSelector;
    }
}
