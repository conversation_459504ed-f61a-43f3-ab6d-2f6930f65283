package com.daxia.wms.delivery.task.replenish.job;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.task.replenish.service.ReplHeaderService;
import com.daxia.wms.delivery.task.replenish.service.ReplenishTaskGenerateContext;
import com.daxia.wms.delivery.task.replenish.service.impl.ReplHeaderServiceImpl;
import com.daxia.wms.delivery.task.replenish.service.impl.ReplenishTaskGenerateContextImpl;
import com.daxia.wms.master.job.WarehouseSimpleJob;
import com.daxia.wms.stock.StockException;
import lombok.extern.slf4j.Slf4j;
import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

@Name("autoJPReplTaskElasticJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class AutoJPReplTaskElasticJob extends WarehouseSimpleJob {

    @Override
    protected void doRun() throws Exception {
        ReplenishTaskGenerateContext replenishTaskGenerateContext = (ReplenishTaskGenerateContext) Component.getInstance(ReplenishTaskGenerateContextImpl.class);
        log.debug("Auto Publish JPRepl, warehouse Id is:" + ParamUtil.getCurrentWarehouseId());
        try{
            replenishTaskGenerateContext.createJPReplenishTask(null,null,Boolean.TRUE);
        } catch (StockException se) {
        //失败消息不为空且异常不为{没有初始化状态的补货任务}，就不需要抛出异常
        if (!StockException.NO_INIT_REPL_TASK_EXIST.equals(se.getMessage()) ) {
            throw se;
        }
    }
    }

}