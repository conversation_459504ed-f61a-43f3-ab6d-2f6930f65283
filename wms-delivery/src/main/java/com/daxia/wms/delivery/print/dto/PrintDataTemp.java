package com.daxia.wms.delivery.print.dto;

import java.util.List;

import com.daxia.wms.master.entity.Printer;

@lombok.extern.slf4j.Slf4j
public class PrintDataTemp {

	private List<InvoiceHeaderDTO> invoiceHeaderDTOList;
    /**
     * 打印数据list
     */
    private List<String> data;
    
    private Printer printer;

    /**
     * 用户自定义1
     */
    private String userDefine1;

    /**
     * 用户自定义2
     */
    private String userDefine2;

    /**
     * 用户自定义3
     */
    private String userDefine3;

    public List<String> getData() {
        return data;
    }

    public void setData(List<String> data) {
        this.data = data;
    }

    public String getUserDefine1() {
        return userDefine1;
    }

    public void setUserDefine1(String userDefine1) {
        this.userDefine1 = userDefine1;
    }

    public String getUserDefine2() {
        return userDefine2;
    }

    public void setUserDefine2(String userDefine2) {
        this.userDefine2 = userDefine2;
    }

    public String getUserDefine3() {
        return userDefine3;
    }

    public void setUserDefine3(String userDefine3) {
        this.userDefine3 = userDefine3;
    }

	public Printer getPrinter() {
		return printer;
	}

	public void setPrinter(Printer printer) {
		this.printer = printer;
	}

	public List<InvoiceHeaderDTO> getInvoiceHeaderDTOList() {
		return invoiceHeaderDTOList;
	}

	public void setInvoiceHeaderDTOList(List<InvoiceHeaderDTO> invoiceHeaderDTOList) {
		this.invoiceHeaderDTOList = invoiceHeaderDTOList;
	}

}
