package com.daxia.wms.delivery.recheck.action;

import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.cfg.AutoLoadAndDeliverCfg;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.util.WmsUtil;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants;
import com.daxia.wms.Keys;
import com.daxia.wms.PageConfig;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.print.service.PrintGroupWaveLabelService;
import com.daxia.wms.delivery.recheck.dao.CartonHeaderDAO;
import com.daxia.wms.delivery.recheck.dto.ReCheckRecord;
import com.daxia.wms.delivery.recheck.service.ReCheckService;
import com.daxia.wms.delivery.recheck.service.WaveRandomCheckService;
import com.daxia.wms.delivery.recheck.service.impl.WaveRandomCheckServiceImpl;
import com.daxia.wms.delivery.sort.service.SortingService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.print.dto.PrintData;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.classic.Session;
import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.*;
import org.jboss.seam.annotations.security.Restrict;
import org.jboss.seam.core.Events;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.daxia.wms.Constants.TransportWendyEnum.NORMAL;
import static com.daxia.wms.Keys.Delivery.random_check_switch;
import static com.daxia.wms.delivery.recheck.service.WaveRandomCheckService.CREATE_EVENT_KEY;

/**
 * 对核拣装箱进行操作
 */
@Name("com.daxia.wms.delivery.quickRecheckAction")
@Restrict("#{identity.hasPermission('delivery.quickRecheck')}")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class QuickRecheckAction implements Serializable {

    private static final long serialVersionUID = -5692990914975736264L;
    private String waveNo;

    private String doNo;

    private String materials;

    private BigDecimal skuQty;

    private String printDatas = "[]";
    private String printContent = "";
    private Integer doQty;
    /**
     * 组合条码
     */
    @Setter
    @Getter
    private String combiBarcode;
    /**
     * 防冻防高温波次标记
     */
    @Setter
    @Getter
    private boolean fdfgwWv=false;
    /**
     * 恒温耗材扫描信息
     */
    @Setter
    @Getter
    private String innerMaterial;

    private Integer doReleaseQty;

    private Integer doHoldQty;

    private boolean needMaterial;

    private String isAutoPrintAfterPack = "1";

    private String recheckType;

    private static final String RECHECK_BY_WAVE = "1";

    private static final String RECHECK_BY_DO = "2";
    /**
     * 波次单订单中所有的商品的核拣记录，包括已核拣了多少，未核拣多少，及对应的商品信息
     */
    private List<ReCheckRecord> reCheckRecords;

    @In
    WaveService waveService;
    @In
    DeliveryOrderService deliveryOrderService;
    @In
    ReCheckService reCheckService;
    @In
    PrintGroupWaveLabelService printGroupWaveLabelService;
    @In
    ContainerMgntService containerMgntService;
    @In
    SortingService sortingService;
    @In
    CartonHeaderDAO cartonHeaderDAO;

    @Create
    public void init() {
        needMaterial = Constants.YesNo.YES.getValue().toString().equals(SystemConfig.getConfigValue("recheck.recommondMaterial", ParamUtil.getCurrentWarehouseId()));
        recheckType = RECHECK_BY_WAVE;
    }
    public void pack() {
        SessionFactory factory = (SessionFactory) Component.getInstance("hibernateSessionFactory");
        Session sess=factory.openSession();
        Transaction htx = sess.beginTransaction();
        printDatas = "[]";
        printContent = "";
        isAutoPrintAfterPack = "1";
        String doNotPrintScript = SystemConfig.getConfigValue("delivery.doNotPrintAfterPack.script", ParamUtil.getCurrentWarehouseId());

        List<Long> doHeaderIds = null;
        WaveHeader waveHeader = null;
        DeliveryOrderHeader deliveryOrderHeader = null;
        if (recheckType.equals(RECHECK_BY_WAVE)) {
            //先通过波次号找波次,然后再通过订单去查找
            waveHeader = getWaveHeaderByNo(waveNo);
            if (waveHeader == null) {
                throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
            }
            if (!Constants.AutoWaveType.BATCH_GROUP.getValue().equals(waveHeader.getAutoType())) {
                throw new DeliveryException("非团购波次无法快速核拣!");
            }
            // 配置过的productCode不需要溯源
            String closeScanSerial = SystemConfig.getConfigValue("close.recheck.scanSerial.list",
                    ParamUtil.getCurrentWarehouseId());
            if(waveHeader.getDoHeaders().get(0).getDoDetails().stream().anyMatch(x->x.getSku().getSnQty()>0
                    && !closeScanSerial.contains(x.getSku().getOriginalId()))){
                throw new DeliveryException("溯源商品订单请使用普通复核流程!");

            }
            doHeaderIds = deliveryOrderService.qureyDoHeaderIdsByWaveId(waveHeader.getId());
            //校验是否有异步取消
            for (Long doId : doHeaderIds) {
                if (deliveryOrderService.doCancelIntercept(doId)) {
                    deliveryOrderHeader = deliveryOrderService.getDoHeaderById(doId);
                    throw new DeliveryException(DeliveryException.RECHECK_DO_IS_FROZEN_DONO, deliveryOrderHeader.getDoNo());
                }
            }


            deliveryOrderHeader = deliveryOrderService.getDoHeaderById(doHeaderIds.get(0));

            if (deliveryOrderHeader.getStatus().equals(Constants.DoStatus.ALLPICKED.getValue()) && waveHeader.getAutoType().equals(Constants.AutoWaveType.BATCH_GROUP.getValue())) {
                if (SystemConfig.configIsOpen("delivery.recheck.batchAutoSort", ParamUtil.getCurrentWarehouseId())) {
                    sortingService.compelSorting(waveHeader.getWaveNo(), WmsUtil.getOperateBy());
                }
            }

            reCheckService.quickRecheckByWaveWithInnerMaterials(waveHeader.getId(), materials,innerMaterial);

            if (StringUtil.isNotEmpty(doNotPrintScript) && Boolean.TRUE.equals(MvelUtil.eval(doNotPrintScript, ImmutableMap.of("doHeader", (Object) deliveryOrderHeader)))) {
                isAutoPrintAfterPack = "0";
            }
            // 当开启抽检开关时 创建抽检任务
            if(Config.isDefaultFalse(random_check_switch, Config.ConfigLevel.WAREHOUSE)) {
                WaveRandomCheckService waveRandomCheckService= (WaveRandomCheckService)Component.getInstance(WaveRandomCheckServiceImpl.class);
                waveRandomCheckService.createWaveRandomCheck( waveHeader.getWaveNo(),
                        ParamUtil.getCurrentWarehouseId(), ParamUtil.getCurrentLoginName());
            }
        } else if (recheckType.equals(RECHECK_BY_DO)) {
            if (StringUtil.isEmpty(doNo)) {
                throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
            }
            deliveryOrderHeader = deliveryOrderService.findDoHeaderByDoNoWithoutException(doNo);
            if (deliveryOrderHeader == null) {
                throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
            }
            doNo = deliveryOrderHeader.getDoNo();
            if (deliveryOrderService.doCancelIntercept(deliveryOrderHeader.getId())) {
                throw new DeliveryException(DeliveryException.RECHECK_DO_IS_FROZEN);
            }
            waveHeader = deliveryOrderHeader.getWaveHeader();
            if (waveHeader == null) {
                throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
            }

            if (StringUtil.isNotEmpty(doNotPrintScript) && Boolean.TRUE.equals(MvelUtil.eval(doNotPrintScript, ImmutableMap.of("doHeader", (Object) deliveryOrderHeader)))) {
                isAutoPrintAfterPack = "0";
            }

            if (skuQty == null || BigDecimal.ZERO.compareTo(skuQty) == 0) {
                throw new DeliveryException(DeliveryException.RECHECK_SKU_COUNT_LH_ZERO);
            }
            String autoNode = Config.getFmJson(Keys.Delivery.auto_load_and_deliver_cfg, Config.ConfigLevel.WAREHOUSE, AutoLoadAndDeliverCfg.type);
            Boolean needAutoDelivery = StringUtil.equals(autoNode, Constants.AutoLoadAndDeliverNode.PACK.getValue());
            reCheckService.recheckBySkuCount(deliveryOrderHeader.getId(), skuQty, null, needAutoDelivery, WmsUtil.getOperateBy());

            doHeaderIds = Lists.newArrayList(deliveryOrderHeader.getId());
        }
        cartonHeaderDAO.getSession().flush();
        cartonHeaderDAO.getSession().clear();


        Integer printPoint = PageConfig.getInt(ConfigKeys.CARTON_PRINT_POINT, Config.ConfigLevel.WAREHOUSE.getValue());
        if (printPoint != null && !Constants.CartonPrintPoint.RECHECK.getValue().equals(printPoint)
                && Constants.DoType.SELL.getValue().equals(deliveryOrderHeader.getDoType())) {
            isAutoPrintAfterPack = "0";
        }
        sess.flush();
        htx.commit();

        if (isAutoPrintAfterPack == "1") {
            List<PrintData> resultList = printGroupWaveLabelService.printCartonsByDosForWave(doHeaderIds);
            printDatas = new Gson().toJson(resultList);
            printContent = getPrintJs(resultList);
            if (waveHeader != null) {
                //更新打印标识
                waveService.updateWavePrintFlag(Arrays.asList(waveHeader.getId()), WaveHeader.FLAG_PRINT_CARTON);
            }
        }
        sess=factory.openSession();
        htx = sess.beginTransaction();
        clearData();
        //释放容器
        releaseContainer(waveHeader);

        //异步处理 团购波次&& 需要关务放行  关务货物放行业务
//        if (Constants.AutoWaveType.BATCH_GROUP.getValue().equals(waveHeader.getAutoType()) && Objects.equals(
//            SystemConfig.getConfigValueInt("customs.goods.pass.enable", ParamUtil.getCurrentWarehouseId()),
//            Constants.YesNo.YES.getValue())) {
//            Events.instance().raiseTransactionSuccessEvent(ReCheckService.BATCH_CUSTOMS_GOODS_PASS_EVENT, waveNo,
//                ParamUtil.getCurrentWarehouseId());
//        }

        sess.flush();
        htx.commit();
    }

    /**
     * 释放容器
     *
     * @param waveHeader
     */
    public void releaseContainer(WaveHeader waveHeader) {
        containerMgntService.releaseContainerByWave(waveHeader, Constants.ContainerType.WAVE_CONTAINER.getValue(), Constants.BindDocType.WAVE.getValue());
    }

    private String getPrintJs(List<PrintData> dataList) {
        Map<String, String> templateMap = new HashMap<String, String>();
        String templateJs = "";
        for (PrintData printData : dataList) {
            if (!templateMap.containsKey(printData.getPrintCfg().getLodopTemplate())) {
                templateJs = templateJs + printData.getTemplateJs();
                templateMap.put(printData.getPrintCfg().getLodopTemplate(), templateJs + printData.getTemplateJs());
            }
        }
        return templateJs;
    }

    private void clearData() {
        doQty = null;
        doReleaseQty = null;
        doHoldQty = null;
        materials = "";
        reCheckRecords = null;
        fdfgwWv=false;
        innerMaterial=null;
    }

    public void loadWaveInfo() {
        clearData();

        if (StringUtil.isEmpty(waveNo)) {
            throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
        }
        WaveHeader waveHeader = getWaveHeaderByNo(waveNo);
        if (waveHeader == null) {
            throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
        }

        if (!Constants.AutoWaveType.BATCH_GROUP.getValue().equals(waveHeader.getAutoType())) {
            throw new DeliveryException("非团购波次无法快速核拣!");
        }
        // 配置过的productCode不需要溯源
        String closeScanSerial = SystemConfig.getConfigValue("close.recheck.scanSerial.list",
                ParamUtil.getCurrentWarehouseId());
        if(waveHeader.getDoHeaders().get(0).getDoDetails().stream().anyMatch(x->x.getSku().getSnQty()>0
                && !closeScanSerial.contains(x.getSku().getOriginalId()))){
            throw new DeliveryException("溯源商品订单请使用普通复核流程!");
        }

        String status = deliveryOrderService.getMaxStatus(waveHeader.getId());
        if (!Constants.DoStatus.ALLSORTED.getValue().equals(status) && !waveHeader.getAutoType().equals(Constants.AutoWaveType.BATCH_GROUP.getValue())) {
            throw new DeliveryException(DeliveryException.DO_IS_NOT_ALLSORTED);
        } else if (waveHeader.getAutoType().equals(Constants.AutoWaveType.BATCH_GROUP.getValue())) {
            if (StringUtil.isNotIn(status, Constants.DoStatus.ALLPICKED.getValue(), Constants.DoStatus.ALLSORTED.getValue())) {
                throw new DeliveryException(DeliveryException.RECHECK_DO_STATUS_ERROR,waveNo);
            }
        }

        Map<Constants.ReleaseStatus, Integer> qtyMap = deliveryOrderService.countByReleaseStatus(waveHeader.getId());
        if (qtyMap != null) {
            Integer qty = qtyMap.get(Constants.ReleaseStatus.HOLD);
            doHoldQty = qty == null ? 0 : qty;

            qty = qtyMap.get(Constants.ReleaseStatus.RELEASE);
            doReleaseQty = qty == null ? 0 : qty;

            doQty = doHoldQty + doReleaseQty;
        }
        // 校验波次中单订单商品数据
        List<Long> doHeaderIds = deliveryOrderService.qureyDoHeaderIdsByWaveId(waveHeader.getId());
        reCheckRecords = reCheckService.getReCheckRecord(doHeaderIds.get(0), Constants.PackageType.B.getValue());
        combiBarcode=deliveryOrderService.getDoHeaderById(doHeaderIds.get(0)).getUserDeffine3();
        for (ReCheckRecord reCheckRecord : reCheckRecords) {
            if(!NORMAL.getDesc().equals(reCheckRecord.getTransportWendy())){
                fdfgwWv=true;
            }
        }
    }

    /**
     * 通过扫描的条码查找波次信息, 支持按照订单号转换
     *
     * @param waveNo 扫描的条码信息
     * @return 波次信息
     */
    private WaveHeader getWaveHeaderByNo(String waveNo) {
        if (StringUtil.isEmpty(waveNo)) {
            throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
        }
        WaveHeader waveHeader = waveService.getWaveHeaderByWaveNum(waveNo);
        if (null == waveHeader) {
            DeliveryOrderHeader deliveryOrderHeader = deliveryOrderService.findDoHeaderByDoNoWithoutException(waveNo);
            if (deliveryOrderHeader != null) {
                waveHeader = deliveryOrderHeader.getWaveHeader();
            }
        }
        return waveHeader;
    }

    public boolean isNeedMaterial() {
        return needMaterial;
    }

    public void setNeedMaterial(boolean needMaterial) {
        this.needMaterial = needMaterial;
    }

    public String getMaterials() {
        return materials;
    }

    public void setMaterials(String materials) {
        this.materials = materials;
    }

    public Integer getDoQty() {
        return doQty;
    }

    public void setDoQty(Integer doQty) {
        this.doQty = doQty;
    }

    public Integer getDoReleaseQty() {
        return doReleaseQty;
    }

    public void setDoReleaseQty(Integer doReleaseQty) {
        this.doReleaseQty = doReleaseQty;
    }

    public Integer getDoHoldQty() {
        return doHoldQty;
    }

    public void setDoHoldQty(Integer doHoldQty) {
        this.doHoldQty = doHoldQty;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public BigDecimal getSkuQty() {
        return skuQty;
    }

    public void setSkuQty(BigDecimal skuQty) {
        this.skuQty = skuQty;
    }

    public String getPrintDatas() {
        return printDatas;
    }

    public void setPrintDatas(String printDatas) {
        this.printDatas = printDatas;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getPrintContent() {
        return printContent;
    }

    public void setPrintContent(String printContent) {
        this.printContent = printContent;
    }

    public String getIsAutoPrintAfterPack() {
        return isAutoPrintAfterPack;
    }

    public void setIsAutoPrintAfterPack(String isAutoPrintAfterPack) {
        this.isAutoPrintAfterPack = isAutoPrintAfterPack;
    }

    public String getRecheckType() {
        return recheckType;
    }

    public void setRecheckType(String recheckType) {
        this.recheckType = recheckType;
    }

    public List<ReCheckRecord> getReCheckRecords() {
        return reCheckRecords;
    }

    public void setReCheckRecords(List<ReCheckRecord> reCheckRecords) {
        this.reCheckRecords = reCheckRecords;
    }
}
