package com.daxia.wms.delivery.invoice.service.impl;

import com.daxia.framework.common.util.*;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.invoice.dto.PrintInvoiceDetailDTO;
import com.daxia.wms.delivery.invoice.dto.PrintInvoiceHeaderDTO;
import com.daxia.wms.delivery.invoice.entity.InvoiceDetail;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.service.InvoiceService;
import com.daxia.wms.delivery.invoice.service.PrintInvoiceService;
import com.daxia.wms.print.PrintConstants;
import com.google.common.collect.Lists;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Name("com.daxia.wms.delivery.invoice.printInvoiceService")
@lombok.extern.slf4j.Slf4j
public class PrintInvoiceServiceImpl implements PrintInvoiceService {
    @In
    InvoiceService invoiceService;
    @In
    DeliveryOrderService deliveryOrderService;

    @Override
    public String printByDo(Long orderId) {
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(orderId);
        List<InvoiceHeader> invoiceHeaderList = invoiceService.getInvoiceListByDoIds(Lists.newArrayList(orderId));
        if (ListUtil.isNotEmpty(invoiceHeaderList)) {

            List<PrintInvoiceHeaderDTO> dtos = genDTO(invoiceHeaderList, doHeader);
            if (ListUtil.isNotEmpty(dtos)) {
                Map<String, Object> originalUnitProps = new HashMap<String, Object>();
                originalUnitProps.put("dtos", dtos);

                return PrintTemplateUtil.process(PrintConstants.PrintTemplate.INVOICE_ROLL.name(), originalUnitProps);
            }
        }
        return "";
    }

    @Override
    public String printByInvoice(List<Long> ids) {
        List<InvoiceHeader> invoiceHeaderList = invoiceService.getInvoiceList(ids);
        if (ListUtil.isNotEmpty(invoiceHeaderList)) {

            List<PrintInvoiceHeaderDTO> dtos = genDTO(invoiceHeaderList, null);
            if (ListUtil.isNotEmpty(dtos)) {
                Map<String, Object> originalUnitProps = new HashMap<String, Object>();
                originalUnitProps.put("dtos", dtos);

                return PrintTemplateUtil.process(PrintConstants.PrintTemplate.INVOICE_ROLL.name(), originalUnitProps);
            }
        }
        return "";
    }

    //混订单的发票打印的时候，传DoHeader为空，需要根据invoiceHeader获取Doheader
    private List<PrintInvoiceHeaderDTO> genDTO(List<InvoiceHeader> invoiceHeaderList, DeliveryOrderHeader doHeader) {
        List<PrintInvoiceHeaderDTO> dtos = Lists.newArrayList();
        for (InvoiceHeader invoiceHeader : invoiceHeaderList) {
            DeliveryOrderHeader tempDoHeader = NullUtil.notNull(doHeader, invoiceHeader.getDeliveryOrderHeader());

            PrintInvoiceHeaderDTO dto = new PrintInvoiceHeaderDTO();
            dto.setPrintDate(new Date());
            dto.setDoNo(tempDoHeader.getDoNo());
            dto.setSortGridNo(tempDoHeader.getSortGridNo());
            dto.setTitle(invoiceHeader.getInvoiceTitle());
            dto.setAmount(BigDecimal.valueOf(invoiceHeader.getInvoiceAmount()));
            dto.setAmountRMB(invoiceHeader.getInvoiceAmountRbm());
            String issuingName = SystemConfig.getConfigValue("invoice.issuing.name", ParamUtil.getCurrentWarehouseId());
            dto.setPayee(issuingName);
            dto.setDetailList(genDetail(invoiceHeader.getInvoiceDetails()));

            dtos.add(dto);
        }
        return dtos;
    }

    private List<PrintInvoiceDetailDTO> genDetail(List<InvoiceDetail> invoiceDetails) {
        List<PrintInvoiceDetailDTO> deailDTOs = Lists.newArrayList();
        for (InvoiceDetail detail : invoiceDetails) {
            PrintInvoiceDetailDTO detailDTO = new PrintInvoiceDetailDTO();
            detailDTO.setSkuDescr(detail.getSkuDescr());
            detailDTO.setPrice(detail.getPrice());
            detailDTO.setQty(detail.getQty());
            detailDTO.setAmount(detail.getAmount());
            detailDTO.setUom(detail.getUomDescr());

            deailDTOs.add(detailDTO);
        }
        return deailDTOs;
    }
}
