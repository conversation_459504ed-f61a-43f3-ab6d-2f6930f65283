package com.daxia.wms.delivery.sort.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 强制分拣时doDetail的Dto
 */
@lombok.extern.slf4j.Slf4j
public class SortingDoDetailDTO implements Serializable {
	private static final long serialVersionUID = -1257526307654971136L;
	private Long id;
	/**
	 * 订单头ID
	 */
	private Long doHeaderId;
	private boolean selected;
	/**
	 * 产品代码
	 */
	private String productCode;
	/**
	 * 产品条码
	 */
	private String ean13;
	/**
	 * 分拣总数量
	 */
	private BigDecimal totalSortQty;
	/**
	 * 已分拣数量
	 */
	private BigDecimal sortedQty;
	/**
	 * 需要分拣数量
	 */
	private BigDecimal needSortQty;
	
	public SortingDoDetailDTO(Long id, Long doHeaderId, String productCode, 
			String ean13, BigDecimal totalSortQty, BigDecimal sortedQty) {
		this.id = id;
		this.doHeaderId = doHeaderId;
		this.selected = false;
		this.productCode = productCode;
		this.ean13 = ean13;
		this.totalSortQty = totalSortQty;
		this.sortedQty = sortedQty;
		this.needSortQty = totalSortQty.subtract(sortedQty);
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getDoHeaderId() {
		return doHeaderId;
	}
	public void setDoHeaderId(Long doHeaderId) {
		this.doHeaderId = doHeaderId;
	}
	public boolean isSelected() {
		return selected;
	}
	public void setSelected(boolean selected) {
		this.selected = selected;
	}
	public String getProductCode() {
		return productCode;
	}
	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}
	public String getEan13() {
		return ean13;
	}
	public void setEan13(String ean13) {
		this.ean13 = ean13;
	}
	public BigDecimal getTotalSortQty() {
		return totalSortQty;
	}
	public void setTotalSortQty(BigDecimal totalSortQty) {
		this.totalSortQty = totalSortQty;
	}
	public BigDecimal getSortedQty() {
		return sortedQty;
	}
	public void setSortedQty(BigDecimal sortedQty) {
		this.sortedQty = sortedQty;
	}
	public BigDecimal getNeedSortQty() {
		return needSortQty;
	}
	public void setNeedSortQty(BigDecimal needSortQty) {
		this.needSortQty = needSortQty;
	}
	@Override
	public boolean equals(Object obj) {
		if((obj instanceof SortingDoDetailDTO))
		{
			SortingDoDetailDTO temp = (SortingDoDetailDTO) obj;
			if(this.getId().compareTo(temp.getId()) == 0)
			{
				return true;
			}
		}
		return false;
	}
	@Override
	public int hashCode() {
		return this.getId().intValue();
	}
}
