package com.daxia.wms.delivery.print.service;

import java.util.ArrayList;
import java.util.List;

import com.daxia.wms.delivery.crossorder.entity.CrossSeedHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.load.entity.TOCrossDockHeader;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.print.dto.DoPrint;
import com.daxia.wms.delivery.print.dto.PickPrint;
import com.daxia.wms.delivery.print.service.impl.PrintServiceImpl.ReportName;
import com.daxia.wms.delivery.task.repick.entity.ReversePickHeader;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.master.entity.Carrier;
import com.daxia.wms.stock.task.entity.TrsTask;

public interface PrintService {
    /**
     * 
     * <pre>
     * 分拣单
     * </pre>
     *
     * @param waves
     * @return
     */
    public List<String> getSttReport(List<WaveHeader> waves);

    /**
     * 
     * <pre>
     * 反拣单
     * </pre>
     * @param rePickHeader 
     *
     * @param rePickTasks
     * @return
     */
    public List<String> getRepickReport(ReversePickHeader rePickHeader, List<TrsTask> rePickTasks);

    /**
     * 
     * <pre>
     * 导出分拣单
     * </pre>
     *
     * @param waves
     * @return 
     */
    public byte[] exportSttReport(List<WaveHeader> waves);

    /**
     * 
     * <pre>
     * Description：CrossDoc发货单
     * </pre>
     *
     * @param doHeaderList
     * @return
     */
    public List<String> getCrossDocDoReport(List<TOCrossDockHeader> toCrossDockHeaders);
    
    /**
     * 
     * <pre>
     * Description:导出CrossDoc发货单
     * </pre>
     *
     * @param doHeaderList
     * @return
     */
    public byte[] exportCrossDocDoReport(List<TOCrossDockHeader> toCrossDockHeaders);
    
    /**
     * Description:导出发货单
     * @param doHeaderList
     * @return
     */
    public byte[] exportDoReportByHeaderList(List<DeliveryOrderHeader> doHeaderList);
    
    /**
     * Description:导出反拣报表
     * @param rePickHeader
     * @param rePickTasks
     * @return
     */
    public byte[] exportRepickReport(ReversePickHeader rePickHeader,List<TrsTask> rePickTasks);

    /**
     * Description:获取do单的电话或手机号码
     * @param doHeader
     * @return
     */
    public String getPhone(DeliveryOrderHeader doHeader);


    /**
     * Description:reportName.getName()==reportName.name ? 1 : 2
     * @param reportName
     * @return
     */
    public int getPickOrient(ReportName reportName);

    /**
     * Description:根据波次获取发货单
     * @param ids
     * @return
     */
    public List<String> getDoReportByWaveIds(List<Long> ids);
    
    /**
     * Description:根据波次获取TRV报表
     * @param ids
     * @return
     */
    public List<String> getRtvReportByWaveIds(List<Long> ids);

    /**
     * Description:根据发货单头获取发货单报表
     * @param ids
     * @return
     */
    public List<String> getDoReportByDoHeaderIds(List<Long> ids);

	/**
	 * Description:预览do明细
	 * @param doHeaderList
	 */
	void preDetailDo(List<DeliveryOrderHeader> doHeaderList);
	/**
     * Description:根据发货单头获取RTV报表
     * @param ids
     * @return
     */
    public List<String> getRTVReportByDoHeaderIds(List<Long> ids);

    public List<String> getYaoRtvReportByDoHeaderIds(List<Long> ids);

    public List<String> getYaoToReportByDoHeaderIds(List<Long> ids);

    /***
     * 是否使用A5纸打印拣货单
     * @return
     */
    Integer isPrintPickTaskByA5();

    String printPickByPkt(List<PickHeader> pkts, boolean equals);

    String genYaoToData(List<Long> printDoIds);

    String printCrossByHeader(CrossSeedHeader crossSeedHeader);

    String printCrossByDoList(List<DeliveryOrderHeader> doList);

    String genPrintInspectionByWave(List<Long> ids);

    String genPrintYaoToByWave(List<Long> ids);
}
