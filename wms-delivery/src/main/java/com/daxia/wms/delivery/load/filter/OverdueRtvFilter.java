package com.daxia.wms.delivery.load.filter;

import java.util.Date;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

/**
 * 逾期退单管理
 */
@lombok.extern.slf4j.Slf4j
public class OverdueRtvFilter extends WhBaseQueryFilter {

	private static final long serialVersionUID = 5384364840254226428L;

	// RTV单号
	private String rtvNo;

	// 来源单号
	private String sourceNo;

	// 商品编码
	private String productCode;

	// 商品条码
	private String productBarCode;

	// 状态
	private String status;

	// 处理方式
	private String dealWay;

	// 操作时间
	private Date fmOperateTime;
	
	// 操作时间
	private Date toOperateTime;

	// 出库时间
	private Date fmDeliveryTime;
	
	// 出库时间
	private Date toDeliveryTime;

	@Operation(fieldName = "o.rtvNo", operationType = OperationType.EQUAL)
	public String getRtvNo() {
		return rtvNo;
	}

	public void setRtvNo(String rtvNo) {
		this.rtvNo = rtvNo;
	}

	@Operation(fieldName = "o.sourceNo", operationType = OperationType.EQUAL)
	public String getSourceNo() {
		return sourceNo;
	}

	public void setSourceNo(String sourceNo) {
		this.sourceNo = sourceNo;
	}

	@Operation(fieldName = "o.productCode", operationType = OperationType.EQUAL)
	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	@Operation(fieldName = "o.productBarCode", operationType = OperationType.EQUAL)
	public String getProductBarCode() {
		return productBarCode;
	}

	public void setProductBarCode(String productBarCode) {
		this.productBarCode = productBarCode;
	}

	@Operation(fieldName = "o.status", operationType = OperationType.EQUAL)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Operation(fieldName = "o.dealWay", operationType = OperationType.EQUAL)
	public String getDealWay() {
		return dealWay;
	}

	public void setDealWay(String dealWay) {
		this.dealWay = dealWay;
	}
	
	@Operation(fieldName = "o.updatedAt", operationType = OperationType.NOT_LESS_THAN)
	public Date getFmOperateTime() {
		return fmOperateTime;
	}

	public void setFmOperateTime(Date fmOperateTime) {
		this.fmOperateTime = fmOperateTime;
	}

	@Operation(fieldName = "o.updatedAt", operationType = OperationType.NOT_GREAT_THAN)
	public Date getToOperateTime() {
		return toOperateTime;
	}

	public void setToOperateTime(Date toOperateTime) {
		this.toOperateTime = toOperateTime;
	}

	@Operation(fieldName = "o.deliveryTime", operationType = OperationType.NOT_LESS_THAN)
	public Date getFmDeliveryTime() {
		return fmDeliveryTime;
	}

	public void setFmDeliveryTime(Date fmDeliveryTime) {
		this.fmDeliveryTime = fmDeliveryTime;
	}

	@Operation(fieldName = "o.deliveryTime", operationType = OperationType.NOT_GREAT_THAN)
	public Date getToDeliveryTime() {
		return toDeliveryTime;
	}

	public void setToDeliveryTime(Date toDeliveryTime) {
		this.toDeliveryTime = toDeliveryTime;
	}
}
