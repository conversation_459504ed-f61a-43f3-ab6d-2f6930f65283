package com.daxia.wms.delivery.load.job;

import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.load.entity.ReShipDo;
import com.daxia.wms.delivery.load.service.AutoDeliverService;
import com.daxia.wms.delivery.load.service.LoadService;
import com.daxia.wms.exp.sys.base.util.ExpSrvUtil;
import com.daxia.wms.master.job.AbstractJob;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.*;
import org.jboss.seam.log.Log;

import java.util.List;

/**
 * 自动发货的定时任务：<br>
 * 定时对 分拣环自动交接失败（异步自动发货失败）的DO重新发货
 * 支持自动交接类型的和流水交接类型的do。
 */
@Name("com.daxia.wms.delivery.autoShipJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class AutoShipJob extends AbstractJob {

	private static volatile boolean running = false;
	
	@In
	private AutoDeliverService autoDeliverService;
	@In
    private LoadService loadService;
	@In
	private DeliveryOrderService deliveryOrderService;

	public boolean isWaiting() {
		return !running;
	}

    /**
     * 筛选满足条件的DO进行发货
     */ 
	@Override
    @Loggable
    public void doRun() {
		if (this.isWaiting()) {
			log.info("Begin Run StockExpJob, Please Waiting ............... ");
			running();
			try {
				Integer batchNumber = Integer.parseInt(SystemConfig.getConfigValue(
						"batch.delivery.count", null));
				Integer failExceed = Integer.valueOf(SystemConfig.getConfigValue(
						"mail.when.fail.exceed", null));

				List<ReShipDo> shipQueue = loadService.findToReShipDo(
						failExceed, batchNumber, null);
				if (!ListUtil.isNullOrEmpty(shipQueue)) {
					for (ReShipDo toShip : shipQueue) {
						// 设置仓库id
						ParamUtil.setCurrentWarehouseId(toShip.getWarehouseId());
						loadService.evictReShipDo(toShip);
						boolean success = Boolean.TRUE;
						try {
								success = autoDeliverService.reShip(toShip);
						} catch (Exception e) {
							success = Boolean.FALSE;
							log.error("error run autoShip:" + toShip.getId(), e);
						} finally {
							// 重新发货失败次数达到上限，邮件预警
							try {
								if (success) {
									loadService.removeReShipByDoId(toShip
											.getDocId());
								} else {
									toShip.setNextInvokeTime(DateUtil.dateAdd("mi", DateUtil.getNowTime(), ExpSrvUtil.getReinvokeInterval(toShip.getCount().intValue(), ParamUtil.getCurrentWarehouseId())));
									loadService.addShipCount(toShip);
									try {
										if (Long.valueOf(
												SystemConfig
														.getConfigValue(
																"mail.when.fail.exceed",
																null)).equals(
												toShip.getCount() + 1)) {
											if (YesNo.YES.getValue().equals(
													toShip.getIsAuto())) {
												// 重新发货失败次数达到上限，邮件预警
												loadService
														.sendNoticeMail(
																toShip.getId()
																		.toString(),
																DeliveryException.RESHIPDO_FAIL_EXCEED,
																null);
											} else {
												loadService
														.sendNoticeMail(
																toShip.getId()
																		.toString(),
																DeliveryException.RESHIPLOAD_STATUS_ERROR,
																null);
											}
										}
									} catch (Exception e) {
										log.error(
												"error run autoShip send mail:"
														+ toShip.getId(), e);
									}
								}
							} catch (Exception e) {
								log.error(
										"error run autoShip update:"
												+ toShip.getId(), e);
							}
						}
					}
				}
			}catch (Exception e) {
				log.error("AutoShipJob encounter exception: ", e);
			} finally {
				unRunning();
			}
	        
			log.info("End TmsExpJob. ");
		} else {
			log.info("TmsExpJob Run Already. ");
		}
	}

    private static void running(){
    	running = true;
    }
    
    private static void unRunning(){
    	running = false;
    }
}
