package com.daxia.wms.delivery.wave.service.impl.manual;

import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import org.jboss.seam.annotations.Name;

import com.daxia.wms.delivery.wave.dto.WaveGeneContextDTO;
import com.daxia.wms.delivery.wave.service.impl.WaveStrategy;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 手动生成波次策略实现
 */
@Name("waveMaunalGenerateService")
@lombok.extern.slf4j.Slf4j
public class WaveMaunalGenerateServiceImpl extends WaveStrategy {
	
	@Override
    public String generateWave(WaveGeneContextDTO context) throws Exception {
		try {
			return generateWave(context.getDoIdList(), context.getMaxDOQty(), context.getWavePriority(), context.getIsRecommend(), context.getAutoWaveType(), context.getIsSemiAuto(), context.getRuleDetailId(), context
					.getCriteriaDetailId(),context.getPointGrade());
		} catch (Exception e) {
			log.error("WaveGenerateServiceImpl", e);
			throw e;
		}
	}

	public String generateWave(List<Long> doIdList, Long maxDOQty, Integer wavePriority, Boolean isRecommend,
						   Constants.AutoWaveType autoWaveType, Boolean isSemiAuto, Long ruleDetailId,
							   Long criteriaDetailId, Boolean pointGrade) throws Exception {
			if(!pointGrade){
				return generateWaveByGoodsGrades(doIdList,  maxDOQty,  wavePriority,  isRecommend, autoWaveType,  isSemiAuto,  ruleDetailId,  criteriaDetailId);
			}else {
				return generateWave(doIdList,  maxDOQty,  wavePriority,  isRecommend, autoWaveType,  isSemiAuto,  ruleDetailId,  criteriaDetailId);
			}

	}

	public String generateWaveByGoodsGrades(List<Long> doIdList, Long maxDOQty, Integer wavePriority,
										   Boolean isRecommend,
						  Constants.AutoWaveType autoWaveType, Boolean isSemiAuto, Long ruleDetailId, Long criteriaDetailId) throws Exception {
		List<DeliveryOrderHeader> doList = doHeaderDAO.findDoHeadersByIds(doIdList,true);
		WaveHeader waveHeader = null;
		StringBuilder result=new StringBuilder();
		if(validationGenWave(doList, maxDOQty)){
			//订单排序，用来依次分配分拣格号
			sortDoHeader(doList);

			List<Long> regionIds = pickTaskService.findRegionId(doIdList);

			if(CollectionUtils.isEmpty(regionIds)){
				throw new DeliveryException(DeliveryException.PICK_PARTITION_REGION_ERROR);
			}
			autoWaveType = getAutoWaveType(autoWaveType, doList);

			// 分配分拣柜
			Long sortingBinId = sortingBinService.assignSortBin(doList.get(0).getDoType(), false, autoWaveType.getValue());

			//如果没取到任何分拣柜, 则抛出异常, 提示没有分拣柜
			if (sortingBinId == null) {
				throw new DeliveryException(DeliveryException.NO_SORT_BIN);
			}

			String waveType = getWaveTypeByDoType(doList.get(0));

			String wavePriorityScrit = Config.get(Keys.Delivery.wave_priorityScrit, Config.ConfigLevel.WAREHOUSE);
			if(StringUtil.isNotEmpty(wavePriorityScrit)){
				wavePriority = genWavePriority(doList, wavePriorityScrit);
			}
			Map<Long, List<DeliveryOrderHeader>> listMap = doList.stream().collect(Collectors.groupingBy(x-> Objects.isNull(x.getCycleClass())?Long.MAX_VALUE:
					x.getCycleClass()));
			// 生成波次
			for (List<DeliveryOrderHeader> item:listMap.values()) {
				result.append(generate(item, item.stream().map(DeliveryOrderHeader::getId).collect(Collectors.toList()),
						regionIds, sortingBinId,
						waveType,
						wavePriority, isRecommend,
						autoWaveType,
						isSemiAuto, ruleDetailId, criteriaDetailId).getWaveNo()).append(",");
			}

		}
		return result.deleteCharAt(result.length()-1).toString();
	}
}
