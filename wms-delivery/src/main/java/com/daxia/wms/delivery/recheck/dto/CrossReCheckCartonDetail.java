package com.daxia.wms.delivery.recheck.dto;

import java.math.BigDecimal;

/**
 * 一个箱子里面某一个商品的详细装箱记录
 */
@lombok.extern.slf4j.Slf4j
public class CrossReCheckCartonDetail {
	private CrossReCheckRecord record;
	private CrossReCheckCartonInfo cartonInfo;
	private BigDecimal numberInCarton;
	private int packQty;

	
	public CrossReCheckCartonDetail(CrossReCheckCartonInfo cartonInfo, CrossReCheckRecord record, BigDecimal initNumberInCarton){
		this.record = record;
		this.numberInCarton = initNumberInCarton;
		this.cartonInfo = cartonInfo;
	}
	
	public Long getProductId() {
		return record.getCrossDetailId();
	}
	
	public void setRecord(CrossReCheckRecord record){
		this.record = record;
	}
	
	public CrossReCheckRecord getRecord() {
		return record;
	}

	public BigDecimal getNumberInCarton() {
		return numberInCarton;
	}
	public void setNumberInCarton(BigDecimal numberInCarton) {
		this.numberInCarton = numberInCarton;
	}
	
	public CrossReCheckCartonInfo getCartonInfo() {
		return cartonInfo;
	}

	public void increaseNumberInCarton(BigDecimal number) {
		this.numberInCarton = this.numberInCarton.add(number);
	}

    public int getPackQty() {
        return packQty;
    }

    public void setPackQty(int packQty) {
        this.packQty = packQty;
    }
}
