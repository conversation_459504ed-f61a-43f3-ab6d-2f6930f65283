package com.daxia.wms.delivery.invoice.dto;

import com.thoughtworks.xstream.annotations.XStreamAlias;

@XStreamAlias("service")
@lombok.extern.slf4j.Slf4j
public class InvoiceReq {
    private String sid;
    private String ip;
    private String port;

    private InvoiceData data;

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public InvoiceData getData() {
        return data;
    }

    public void setData(InvoiceData data) {
        this.data = data;
    }
}
