package com.daxia.wms.delivery.deliveryorder.service;

import com.daxia.wms.delivery.AllocateException;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateHeader;

import java.util.List;
import java.util.Map;

/**
 * 执行分配操作接口
 */
public interface IDoAllocate {
    /**
     * doDetail不存在
     */
    public static final String ALC_RESULT_NODO = "1";

    /**
     * 没有库存
     */
    public static final String ALC_RESULT_NOSTOCK = "2";

    /**
     * 分配成功
     */
    public static final String ALC_RESULT_SUCCESS = "3";

    /**
     * doDetail中产品没有默认拣货位
     */
    public static final String ALC_RESULT_NORULE = "4";

    /**
     * 补货任务已存在
     */
    public static final String REPL_TASK_IS_EXIST = "10";

    /**
     * 该商品没有默认拣货位
     */
    public static final String SKU_NO_DEFAULT_LOC = "21";

    /**
     * 库存已补货足够
     */
    public static final String STOCK_REPL_QTY_ENOUGH = "22";

    /**
     * 库存数量不足
     */
    public static final String NO_ENOUGH_STOCK_QTY = "23";

    /**
     * 补货成功
     */
    public static final String REP_SUCCESS = "24";

    /**
     * 没有补货规则
     */
    public static final String NO_REPL_RULE = "25";

    /**
     * 需要补货
     */
    public static final String NEED_REPL = "26";

    /**
     * 订单已经取消
     */
    public static final String DO_CANCEL = "27";

    /**
     * 执行分配
     *
     * @param alcDetail
     * @param region
     * @return
     * @throws AllocateException
     * @throws Exception
     */
    public String executeAllocate(DoAllocateDetail alcDetail, String region) throws AllocateException, Exception;

    /**
     * 组合品订单分配库存
     * @param alcHeader
     * @param allocateResult
     * @return
     */
    String skuCombiStockAllocate(DoAllocateHeader alcHeader, Map<String, List<String>> allocateResult);
}