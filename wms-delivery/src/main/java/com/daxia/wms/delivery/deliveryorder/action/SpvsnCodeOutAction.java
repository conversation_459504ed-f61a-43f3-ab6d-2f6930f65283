package com.daxia.wms.delivery.deliveryorder.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.master.entity.Supplier;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.service.SkuService;
import com.daxia.wms.master.service.SupplierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.serial.SerialException;
import com.daxia.wms.serial.entity.TrsSpvsnCodeLog;
import com.daxia.wms.serial.service.SpvsnCodeService;
import com.daxia.wms.serial.service.TrsService;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

@Name("spvsnCodeOutAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class SpvsnCodeOutAction extends PagedListBean<TrsSpvsnCodeLog> {

	private static final long serialVersionUID = 2305475058160842249L;

	private List<TrsSpvsnCodeLog> spvsnCodeList;

	private Long docId;
	 
	private Long docLineId;
    private Long skuId;
	
	private String docNo;
	
	private DeliveryOrderHeader deliveryOrderHeader;
	
    private DeliveryOrderDetail doDetail;

	@In
	private WarehouseService warehouseService;

	// 录入序列号是否正确的标识
	private String successFlag;

	private String spvsnCode;
	
	private BigDecimal stillNeedLeastCount;
	
	private List<String> spvsnPrefixs = new  ArrayList<String>();
	
	private List<Long> packQtys = new  ArrayList<Long>();
	
	public static final String IN_OUT_TYPE = "SO";

	@In
	private SpvsnCodeService spvsnCodeService;
	@In
	private TrsService trsService;
	
	@In
	private DeliveryOrderService deliveryOrderService;

	@In
	private SkuService skuService;
	
	@In
	private SupplierService supplierService;

	public SpvsnCodeOutAction() {
		super();
		this.spvsnCodeList = new ArrayList<TrsSpvsnCodeLog>();
	}

	@Override
	public void query() {
		// null
		// 根据单据查询入库类型的监管码交易

	}

	public void findSpvsnCodeListInDoc() {
		if(StringUtil.isNotEmpty(docNo) ){
			deliveryOrderHeader = deliveryOrderService.findDoHeaderByDoNo(docNo);
			
			spvsnCodeList = spvsnCodeService.findByDocNo(docNo, IN_OUT_TYPE);
		}
		
		Collections.sort(spvsnCodeList, new Comparator<TrsSpvsnCodeLog>() {
			@Override
			public int compare(TrsSpvsnCodeLog o1, TrsSpvsnCodeLog o2) {
				if (o1.getCreatedAt() == null) {
					return 1;
				} else if (o2.getCreatedAt() == null) {
					return -1;
				} else {
					return -(o1.getCreatedAt().compareTo(o2.getCreatedAt()));
				}
			}
		});
		clearInputText();
	}
	
	public void findSpvsnCodeList() {
		deliveryOrderHeader = deliveryOrderService.getDoHeaderById(docId);
		docNo = deliveryOrderHeader.getDoNo();
		doDetail = deliveryOrderService.getDoDetail(docLineId);
		skuId = doDetail.getSkuId();
		spvsnCodeList = spvsnCodeService.findByDocIdAndType(docId, IN_OUT_TYPE,skuId);
		
		Collections.sort(spvsnCodeList, new Comparator<TrsSpvsnCodeLog>() {
			@Override
			public int compare(TrsSpvsnCodeLog o1, TrsSpvsnCodeLog o2) {
				if (o1.getCreatedAt() == null) {
					return 1;
				} else if (o2.getCreatedAt() == null) {
					return -1;
				} else {
					return -(o1.getCreatedAt().compareTo(o2.getCreatedAt()));
				}
			}
		});
		clearInputText();
	}
	
	public void addSpvsnCode() throws Exception {
		successFlag = "0";
		if(BigDecimal.valueOf(spvsnCodeList.size() +1).compareTo(doDetail.getExpectedQty()) > 0){
			throw new SerialException(SerialException.ERROR_SPVSN_CODE_CANNOT_OVER_SCAN);
		}
		if(spvsnPrefixs.size() == 3){
			int i = 0;
			for(String spvsnPrefix : spvsnPrefixs){
				if(spvsnCode.startsWith(spvsnPrefix)){
					break;
				}
				i++;
				if(i==3){
					throw new SerialException(SerialException.SPVSN_CODE_NOT_MATCH_SKU); 
				}
			}
		}

		// 将监管码交易和库存增加。
		//检验药监码对应的商品是否存在于入库单中
		TrsSpvsnCodeLog trsSpvsnCodeLog = new TrsSpvsnCodeLog();
		trsSpvsnCodeLog.setDocId(deliveryOrderHeader.getId());
		trsSpvsnCodeLog.setDocNo(deliveryOrderHeader.getDoNo());
		trsSpvsnCodeLog.setDocType(IN_OUT_TYPE);
		trsSpvsnCodeLog.setSkuId(skuId);
		trsSpvsnCodeLog.setSpvsnDocType(deliveryOrderHeader.getDoType());
		if (deliveryOrderHeader.getSupplierId() != null) {
			Supplier supplier = supplierService.getSupplier(deliveryOrderHeader.getSupplierId());
			if(supplier != null){
				trsSpvsnCodeLog.setFromCorpID(supplier.getCorpId());
			}
		} else if (Constants.DoType.ALLOT.getValue().equals(deliveryOrderHeader.getDoType())
				&& StringUtil.isNotEmpty(deliveryOrderHeader.getEdi2())){
			Warehouse warehouse = warehouseService.getWarehouse(Long.valueOf(deliveryOrderHeader.getEdi2()));
			if(warehouse != null){
				trsSpvsnCodeLog.setToCorpID(warehouse.getCorpId());
			}
		}
		trsSpvsnCodeLog.setSpvsnCode(spvsnCode);
		spvsnCodeService.saveSpvsnCode(trsSpvsnCodeLog);
		
//		spvsnCodeService.saveSpvsnCode(asnHeader.getId(),asnHeader.getAsnNo(),IN_OUT_TYPE,docType, spvsnCode);
		clearInputText();
		findSpvsnCodeList();
//		resetLeastCount();
		// 序列号入库业务逻辑执行完成，将标志符置为“1”
		successFlag = "1";
		this.sayMessage(MESSAGE_SUCCESS);
	}
	
	public void addSpvsnCodeByDocId() throws Exception {
		successFlag = "0";
		if(spvsnPrefixs.size() == 3){
			int i = 0;
			for(String spvsnPrefix : spvsnPrefixs){
				if(spvsnCode.startsWith(spvsnPrefix)){
					break;
				}
				i++;
				if(i==3){
					throw new SerialException(SerialException.SPVSN_CODE_NOT_MATCH_SKU); 
				}
			}
		}

		// 将监管码交易和库存增加。
		//检验药监码对应的商品是否存在于入库单中
		TrsSpvsnCodeLog trsSpvsnCodeLog = new TrsSpvsnCodeLog();
		trsSpvsnCodeLog.setDocId(deliveryOrderHeader.getId());
		trsSpvsnCodeLog.setDocNo(deliveryOrderHeader.getDoNo());
		trsSpvsnCodeLog.setDocType(IN_OUT_TYPE);
		trsSpvsnCodeLog.setSpvsnDocType(deliveryOrderHeader.getDoType());
		if (deliveryOrderHeader.getSupplierId() != null) {
			Supplier supplier = supplierService.getSupplier(deliveryOrderHeader.getSupplierId());
			if(supplier != null){
				trsSpvsnCodeLog.setFromCorpID(supplier.getCorpId());
			}
		}else if (Constants.DoType.ALLOT.getValue().equals(deliveryOrderHeader.getDoType())
				&& StringUtil.isNotEmpty(deliveryOrderHeader.getEdi2())){
			Warehouse warehouse = warehouseService.getWarehouse(Long.valueOf(deliveryOrderHeader.getEdi2()));
			if(warehouse != null){
				trsSpvsnCodeLog.setToCorpID(warehouse.getCorpId());
			}
		}
		trsSpvsnCodeLog.setSpvsnCode(spvsnCode);
		spvsnCodeService.saveSpvsnCode(trsSpvsnCodeLog);
		
//		spvsnCodeService.saveSpvsnCode(asnHeader.getId(),asnHeader.getAsnNo(),IN_OUT_TYPE,docType, spvsnCode);
		clearInputText();
		findSpvsnCodeListInDoc();
//		resetLeastCount();
		// 序列号入库业务逻辑执行完成，将标志符置为“1”
		successFlag = "1";
		this.sayMessage(MESSAGE_SUCCESS);
	}

//	private void resetLeastCount() {
//		BigDecimal stillQty = asnDetail.getReceivedQty().subtract(new BigDecimal(spvsnCodeList.size()));
//		
//		stillNeedLeastCount = stillQty;
//		if(packQtys.size() >0){
//			int i = 0;
//			stillNeedLeastCount = BigDecimal.ZERO;
//			while (stillQty.compareTo(BigDecimal.ZERO) > 0 && i<packQtys.size()){
//				if(packQtys.get(i) > 0l){
//					BigDecimal[] kk = stillQty.divideAndRemainder(new BigDecimal(packQtys.get(i)));
//					stillNeedLeastCount =stillNeedLeastCount.add(kk[0]);
//					stillQty = kk[1];
//				}
//				i++;
//			}
//		}
//	}

	

	private void clearInputText() {
		this.spvsnCode = null;
	}

	public void delSpvsnCode() {
		successFlag = "0";
		// 操作前校验
		spvsnCodeService.removeSpvsnCode(spvsnCode,IN_OUT_TYPE, spvsnCodeList.get(0).getDocId());
		clearInputText();
		findSpvsnCodeListInDoc();
//		resetLeastCount();
		successFlag = "1";
		this.sayMessage(MESSAGE_SUCCESS);
	}

	public void delAllSpvsnCodeInDoc() {
		spvsnCodeService.removeSpvsnCode(null, IN_OUT_TYPE,spvsnCodeList.get(0).getDocId());
		clearInputText();
//		resetLeastCount();
		this.spvsnCodeList = new ArrayList<TrsSpvsnCodeLog>();
	}

	public String getSpvsnCode() {
		return spvsnCode;
	}

	public void setSpvsnCode(String spvsnCode) {
		this.spvsnCode = spvsnCode;
	}

	public Long getDocId() {
		return docId;
	}

	public void setDocId(Long docId) {
		this.docId = docId;
	}

	public List<TrsSpvsnCodeLog> getSpvsnCodeList() {
		return spvsnCodeList;
	}

	public void setSpvsnCodeList(List<TrsSpvsnCodeLog> spvsnCodeList) {
		this.spvsnCodeList = spvsnCodeList;
	}

	public String getSuccessFlag() {
		return successFlag;
	}

	public void setSuccessFlag(String successFlag) {
		this.successFlag = successFlag;
	}

	public String getDocNo() {
		return docNo;
	}

	public void setDocNo(String docNo) {
		this.docNo = docNo;
	}

	public Long getDocLineId() {
		return docLineId;
	}

	public void setDocLineId(Long docLineId) {
		this.docLineId = docLineId;
	}

	public List<String> getSpvsnPrefixs() {
		return spvsnPrefixs;
	}

	public void setSpvsnPrefixs(List<String> spvsnPrefixs) {
		this.spvsnPrefixs = spvsnPrefixs;
	}

	public List<Long> getPackQtys() {
		return packQtys;
	}

	public void setPackQtys(List<Long> packQtys) {
		this.packQtys = packQtys;
	}

	public BigDecimal getStillNeedLeastCount() {
		return stillNeedLeastCount;
	}

	public void setStillNeedLeastCount(BigDecimal stillNeedLeastCount) {
		this.stillNeedLeastCount = stillNeedLeastCount;
	}

	public DeliveryOrderHeader getDeliveryOrderHeader() {
		return deliveryOrderHeader;
	}

	public void setDeliveryOrderHeader(DeliveryOrderHeader deliveryOrderHeader) {
		this.deliveryOrderHeader = deliveryOrderHeader;
	}

	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	public DeliveryOrderDetail getDoDetail() {
		return doDetail;
	}

	public void setDoDetail(DeliveryOrderDetail doDetail) {
		this.doDetail = doDetail;
	}
}