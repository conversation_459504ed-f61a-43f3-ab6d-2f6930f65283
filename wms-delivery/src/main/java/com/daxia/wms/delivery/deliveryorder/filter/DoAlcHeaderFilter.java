package com.daxia.wms.delivery.deliveryorder.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.DoBaseQueryFilter;
import com.daxia.wms.Constants;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@lombok.extern.slf4j.Slf4j
public class DoAlcHeaderFilter extends DoBaseQueryFilter {

    private static final long serialVersionUID = 775094009325456870L;

    private List<Long> ids;

    private List<Long> waveIds;

    /**
     * 发货单号
     */
    private String doNoFrom;
    private String doNoTo;
    private String doNo;

    /**
     * 发货单状态
     */
    private String statusFrom;
    private String statusTo;
    private String status;
    private String statusNotEqual;

    /**
     * 发货单创建时间
     */
    public Date doCreateTimeFrom;
    public Date doCreateTimeTo;

    /**
     * 发运时间
     */
    private Date shipTimeFrom;
    private Date shipTimeTo;

    /**
     * 发货单类型 DO、调拨、RTV
     */
    private String doType;

    /**
     * 冻结状态 0：冻结 1：释放
     */
    private String releaseStatus;
    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 参考编号1
     */
    private String refNo1;

    /**
     * 参考编号2
     */
    private String refNo2;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 配送说明 半日达/一日三送/团购
     */
    private Long specFlag;

    /**
     * 有无发票
     */
    private Long invoiceFlag;

    /**
     * 订货数量  EXPECTED_QTY_EACH
     */
    private BigDecimal expectedQty;

    /**
     * 发货数量
     */
    private BigDecimal shipQty;

    /**
     * 国家
     */
    private Long country;

    /**
     * 省份
     */
    private Long province;

    /**
     * 城市
     */
    private Long city;

    /**
     * 县
     */
    private Long county;


    /**
     * 收货方
     */
    private String consigneeName;

    /**
     * 收货地址
     */
    private String address;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 配送公司Id
     */
    private Long carrierId;

    /**
     * 运单号
     */
    private String trackingNo;

    /**
     * 发票数量
     */
    private Long invoiceQty;

    /**
     * 是否第一次购买
     */
    private String userDeffine1;

    /**
     * 是否分期付款
     */
    private String userDeffine2;

    /**
     * 波次编号
     */
    private Long waveId;
    /**
     * 波次号
     */
    private String waveNo;
    /**
     * 是否已跑波次
     */
    private Long waveFlag;

    private Integer isHalfDayDelivery;

    private String productCode;

    private Integer ediSendFlag3;

    private String sortGridNo;

    /**
     * 调拨单类型
     *
     * @return
     */
    private Integer tranType;

    /**
     * 是否只查询包含搬仓sku的订单
     */
    private Boolean transSkuFlag;

    /**
     * 预计出库时间
     */
    private Date doFinishTimeFrom;
    private Date doFinishTimeTo;

    //补货状态范围
    private Integer[] replStatusArray;

    /**
     * 需要取消分配
     */
    private Integer needCancelAlloc;


    private Integer businessCustomerType;

    private Long businessCustomerId;

    private Integer needCrossStock;

    private Boolean batchGroup;

    private Integer batchGroupType;

    private Integer autoWaveType;

    private Integer autoWaveTypeNotEqual;

    public Boolean getBatchGroup() {
        return batchGroup;
    }

    public void setBatchGroup(Boolean batchGroup) {
        if (null != batchGroup) {
            if (batchGroup) {
                this.setAutoWaveType(com.daxia.wms.Constants.AutoWaveType.BATCH_GROUP.getValue());
                this.setAutoWaveTypeNotEqual(null);
            } else {
                this.setAutoWaveType(null);
                this.setAutoWaveTypeNotEqual(com.daxia.wms.Constants.AutoWaveType.BATCH_GROUP.getValue());
            }
        } else {
            this.setAutoWaveType(null);
            this.setAutoWaveTypeNotEqual(null);
        }
    }

    public Integer getBatchGroupType() {
        return batchGroupType;
    }

    public void setBatchGroupType(Integer batchGroupType) {
        if (null != batchGroupType) {
            if (Constants.YesNo.YES.getValue().equals(batchGroupType)) {
                setBatchGroup(Boolean.TRUE);
            } else {
                setBatchGroup(Boolean.FALSE);
            }
        } else {
            setBatchGroup(null);
        }
    }

    @Operation(clause = " (o.doWaveEx.autoWaveType <> ? or o.doWaveEx.autoWaveType is null) ", operationType = OperationType.CLAUSE)
    public Integer getAutoWaveTypeNotEqual() {
        return autoWaveTypeNotEqual;
    }

    private void setAutoWaveTypeNotEqual(Integer autoWaveTypeNotEqual) {
        this.autoWaveTypeNotEqual = autoWaveTypeNotEqual;
    }


    @Operation(fieldName = " o.doWaveEx.autoWaveType", operationType = OperationType.EQUAL)
    public Integer getAutoWaveType() {
        return autoWaveType;
    }

    public void setAutoWaveType(Integer autoWaveType) {
        this.autoWaveType = autoWaveType;
    }


    @Operation(fieldName = "o.doNo", operationType = OperationType.NOT_LESS_THAN)
    public String getDoNoFrom() {
        return doNoFrom;
    }

    public void setDoNoFrom(String doNoFrom) {
        this.doNoFrom = doNoFrom;
    }

    @Operation(fieldName = "o.doNo", operationType = OperationType.NOT_GREAT_THAN)
    public String getDoNoTo() {
        return doNoTo;
    }

    public void setDoNoTo(String doNoTo) {
        this.doNoTo = doNoTo;
    }

    @Operation(fieldName = "o.doNo", operationType = OperationType.EQUAL)
    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    @Operation(fieldName = "o.status", operationType = OperationType.NOT_LESS_THAN)
    public String getStatusFrom() {
        return statusFrom;
    }

    public void setStatusFrom(String statusFrom) {
        this.statusFrom = statusFrom;
    }

    @Operation(fieldName = "o.status", operationType = OperationType.NOT_GREAT_THAN)
    public String getStatusTo() {
        return statusTo;
    }

    public void setStatusTo(String statusTo) {
        this.statusTo = statusTo;
    }

    @Operation(fieldName = "o.doCreateTime", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")
    public Date getDoCreateTimeFrom() {
        return doCreateTimeFrom;
    }

    public void setDoCreateTimeFrom(Date doCreateTimeFrom) {
        this.doCreateTimeFrom = doCreateTimeFrom;
    }

    @Operation(fieldName = "o.doCreateTime", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
    public Date getDoCreateTimeTo() {
        return doCreateTimeTo;
    }

    public void setDoCreateTimeTo(Date doCreateTimeTo) {
        this.doCreateTimeTo = doCreateTimeTo;
    }

    @Operation(fieldName = "o.shipTime", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")
    public Date getShipTimeFrom() {
        return shipTimeFrom;
    }

    public void setShipTimeFrom(Date shipTimeFrom) {
        this.shipTimeFrom = shipTimeFrom;
    }

    @Operation(fieldName = "o.shipTime", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
    public Date getShipTimeTo() {
        return shipTimeTo;
    }

    public void setShipTimeTo(Date shipTimeTo) {
        this.shipTimeTo = shipTimeTo;
    }

    @Operation(fieldName = "o.doType", operationType = OperationType.EQUAL)
    public String getDoType() {
        return doType;
    }

    public void setDoType(String doType) {
        this.doType = doType;
    }

    @Operation(fieldName = "releaseStatus", operationType = OperationType.EQUAL)
    public String getReleaseStatus() {
        return releaseStatus;
    }

    public void setReleaseStatus(String releaseStatus) {
        this.releaseStatus = releaseStatus;
    }

    @Operation(fieldName = "refNo1", operationType = OperationType.EQUAL)
    public String getRefNo1() {
        return refNo1;
    }

    public void setRefNo1(String refNo1) {
        this.refNo1 = refNo1;
    }

    @Operation(fieldName = "refNo2", operationType = OperationType.EQUAL)
    public String getRefNo2() {
        return refNo2;
    }

    public void setRefNo2(String refNo2) {
        this.refNo2 = refNo2;
    }

    @Operation(fieldName = "postCode", operationType = OperationType.EQUAL)
    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    @Operation(fieldName = "specFlag", operationType = OperationType.EQUAL)
    public Long getSpecFlag() {
        return specFlag;
    }

    public void setSpecFlag(Long specFlag) {
        this.specFlag = specFlag;
    }

    @Operation(fieldName = "invoiceFlag", operationType = OperationType.EQUAL)
    public Long getInvoiceFlag() {
        return invoiceFlag;
    }

    public void setInvoiceFlag(Long invoiceFlag) {
        this.invoiceFlag = invoiceFlag;
    }

    @Operation(fieldName = "userDeffine1", operationType = OperationType.EQUAL)
    public String getUserDeffine1() {
        return userDeffine1;
    }

    public void setUserDeffine1(String userDeffine1) {
        this.userDeffine1 = userDeffine1;
    }

    @Operation(fieldName = "userDeffine2", operationType = OperationType.EQUAL)
    public String getUserDeffine2() {
        return userDeffine2;
    }

    public void setUserDeffine2(String userDeffine2) {
        this.userDeffine2 = userDeffine2;
    }

    @Operation(fieldName = "expectedQty", operationType = OperationType.EQUAL)
    public BigDecimal getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(BigDecimal expectedQty) {
        this.expectedQty = expectedQty;
    }

    @Operation(fieldName = "shipQty", operationType = OperationType.EQUAL)
    public BigDecimal getShipQty() {
        return shipQty;
    }

    public void setShip(BigDecimal shipQty) {
        this.shipQty = shipQty;
    }

    @Operation(fieldName = "country", operationType = OperationType.EQUAL)
    public Long getCountry() {
        return country;
    }

    public void setCountry(Long country) {
        this.country = country;
    }

    @Operation(fieldName = "province", operationType = OperationType.EQUAL)
    public Long getProvince() {
        return province;
    }

    public void setProvince(Long province) {
        this.province = province;
    }

    @Operation(fieldName = "city", operationType = OperationType.EQUAL)
    public Long getCity() {
        return city;
    }

    public void setCity(Long city) {
        this.city = city;
    }

    @Operation(fieldName = "county", operationType = OperationType.EQUAL)
    public Long getCounty() {
        return county;
    }

    public void setCounty(Long county) {
        this.county = county;
    }

    @Operation(fieldName = "consigneeName", operationType = OperationType.LIKE)
    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    @Operation(fieldName = "address", operationType = OperationType.LIKE)
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Operation(fieldName = "telephone", operationType = OperationType.EQUAL)
    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    @Operation(fieldName = "mobile", operationType = OperationType.LEFT_LIKE)
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    @Operation(fieldName = "carrierId", operationType = OperationType.EQUAL)
    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    @Operation(fieldName = "invoiceQty", operationType = OperationType.EQUAL)
    public Long getInvoiceQty() {
        return invoiceQty;
    }

    public void setInvoiceQty(Long invoiceQty) {
        this.invoiceQty = invoiceQty;
    }

    public void setTrackingNo(String trackingNo) {
        this.trackingNo = trackingNo;
    }

    public String getTrackingNo() {
        return trackingNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    @Operation(fieldName = "o.waveHeader.waveNo", operationType = OperationType.EQUAL)
    public String getWaveNo() {
        return waveNo;
    }

    @Operation(fieldName = "waveFlag", operationType = OperationType.EQUAL)
    public Long getWaveFlag() {
        return waveFlag;
    }

    public void setWaveFlag(Long waveFlag) {
        this.waveFlag = waveFlag;
    }

    @Operation(clause = " o.id in (select detail.doHeaderId from DoAllocateDetail detail,Sku sku where detail.skuId = sku.id and sku.productCode = ? )", operationType = OperationType.CLAUSE)
    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    @Operation(clause = " o.id not in (select detail.doHeaderId from DoAllocateDetail detail,Sku sku where detail.skuId = sku.id and sku.productCode = ? )", operationType = OperationType.CLAUSE)
    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    /**
     * Setter method for property <tt>status</tt>.
     *
     * @param status value to be assigned to property status
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * Getter method for property <tt>status</tt>.
     *
     * @return property value of status
     */
    @Operation(fieldName = "status", operationType = OperationType.NOT_LESS_THAN)
    public String getStatus() {
        return status;
    }

    /**
     * Setter method for property <tt>ids</tt>.
     *
     * @param ids value to be assigned to property ids
     */
    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    /**
     * Getter method for property <tt>ids</tt>.
     *
     * @return property value of ids
     */
    @Operation(fieldName = "id", operationType = OperationType.IN)
    public List<Long> getIds() {
        return ids;
    }

    public Long getWaveId() {
        return waveId;
    }

    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }

    public void setWaveIds(List<Long> waveIds) {
        this.waveIds = waveIds;
    }

    @Operation(fieldName = "waveId", operationType = OperationType.IN)
    public List<Long> getWaveIds() {
        return waveIds;
    }

    @Operation(fieldName = "o.isHalfDayDelivery", operationType = OperationType.EQUAL)
    public Integer getIsHalfDayDelivery() {
        return isHalfDayDelivery;
    }

    public void setIsHalfDayDelivery(Integer isHalfDayDelivery) {
        this.isHalfDayDelivery = isHalfDayDelivery;
    }

    public Integer getEdiSendFlag3() {
        return ediSendFlag3;
    }

    public void setEdiSendFlag3(Integer ediSendFlag3) {
        this.ediSendFlag3 = ediSendFlag3;
    }

    @Operation(fieldName = "sortGridNo", operationType = OperationType.EQUAL)
    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    @Operation(fieldName = "o.tranType", operationType = OperationType.EQUAL)
    public Integer getTranType() {
        return tranType;
    }

    public void setTranType(Integer tranType) {
        this.tranType = tranType;
    }

    @Operation(operationType = OperationType.IGNORE)
    public Boolean getTransSkuFlag() {
        return transSkuFlag;
    }

    public void setTransSkuFlag(Boolean transSkuFlag) {
        this.transSkuFlag = transSkuFlag;
    }

    @Operation(fieldName = "o.planShipTime", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")
    public Date getDoFinishTimeFrom() {
        return doFinishTimeFrom;
    }

    public void setDoFinishTimeFrom(Date doFinishTimeFrom) {
        this.doFinishTimeFrom = doFinishTimeFrom;
    }

    @Operation(fieldName = "o.planShipTime", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
    public Date getDoFinishTimeTo() {
        return doFinishTimeTo;
    }

    public void setDoFinishTimeTo(Date doFinishTimeTo) {
        this.doFinishTimeTo = doFinishTimeTo;
    }

    @Operation(fieldName = "o.needCancelAlloc", operationType = OperationType.EQUAL)
    public Integer getNeedCancelAlloc() {
        return needCancelAlloc;
    }

    public void setNeedCancelAlloc(Integer needCancelAlloc) {
        this.needCancelAlloc = needCancelAlloc;
    }

    @Operation(fieldName = "status", operationType = OperationType.NOT_EQUAL)
    public String getStatusNotEqual() {
        return statusNotEqual;
    }

    public void setStatusNotEqual(String statusNotEqual) {
        this.statusNotEqual = statusNotEqual;
    }

    @Operation(fieldName = "o.replStatus", operationType = OperationType.IN)
    public Integer[] getReplStatusArray() {
        if (replStatusArray.length == 0) {
            return null;
        }
        return replStatusArray;
    }

    public void setReplStatusArray(Integer[] replStatusArray) {
        this.replStatusArray = replStatusArray;
    }


    @Operation(clause = " exists (select 1 from BusinessCustomer bc where bc.id = o.businessCustomerId and bc.customerType = ? ) ", operationType = OperationType.CLAUSE)
    public Integer getBusinessCustomerType() {
        return businessCustomerType;
    }

    public void setBusinessCustomerType(Integer businessCustomerType) {
        this.businessCustomerType = businessCustomerType;
    }

    @Operation(fieldName = "o.businessCustomerId", operationType = OperationType.EQUAL)
    public Long getBusinessCustomerId() {
        return businessCustomerId;
    }

    public void setBusinessCustomerId(Long businessCustomerId) {
        this.businessCustomerId = businessCustomerId;
    }

    @Operation(fieldName = "o.deliveryOrderHeader.needCrossStock", operationType = OperationType.EQUAL)
    public Integer getNeedCrossStock() {
        return needCrossStock;
    }

    public void setNeedCrossStock(Integer needCrossStock) {
        this.needCrossStock = needCrossStock;
    }
}