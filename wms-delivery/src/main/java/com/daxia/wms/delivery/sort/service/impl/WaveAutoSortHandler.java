package com.daxia.wms.delivery.sort.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.jboss.seam.annotations.In;

import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.sort.service.SortingService;
import com.daxia.wms.delivery.wave.dao.WaveDAO;
import com.daxia.wms.delivery.wave.service.WaveAutoHandler;
import com.daxia.wms.Constants;

@lombok.extern.slf4j.Slf4j
public class WaveAutoSortHandler extends WaveAutoHandler {
    @In
    private SortingService sortingService;

    @In
    private WaveDAO waveDAO;
    
	@Override
	protected void handle(Long waveId) {
		List<Long> doIds = deliveryOrderService.qureyDoHeaderIdsByWaveId(waveId);
		if (ListUtil.isNotEmpty(doIds)) {
			for (Long doId : doIds) {
		        DeliveryOrderHeader dh = deliveryOrderService.getDoHeaderById(doId);
		        if (StringUtil.isNotIn(dh.getStatus(), Constants.DoStatus.ALLPICKED.getValue(),
		                Constants.DoStatus.PARTSORTED.getValue())) {
		            return;
		        }
		        List<Long> doDetailIds = new ArrayList<Long>();
		        for (DeliveryOrderDetail detail : dh.getDoDetails()) {
		            if (StringUtil.isIn(detail.getLineStatus(), Constants.DoStatus.ALLPICKED.getValue(),
		                    Constants.DoStatus.PARTSORTED.getValue())) {
		                doDetailIds.add(detail.getId());
		            }
		        }
		        sortingService.forceSorting(doDetailIds, dh.getDoNo(), waveDAO.getOperateUser(), null);
			}
		}
	}

	@Override
	protected void setName() {
		this.name = "waveAutoSortHandler";
	}
}
