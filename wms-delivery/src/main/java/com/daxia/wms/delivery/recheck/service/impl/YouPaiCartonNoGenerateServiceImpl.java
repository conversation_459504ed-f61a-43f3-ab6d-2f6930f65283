package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.BusinessException;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.SequenceName;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.dao.CartonHeaderDAO;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.master.service.SubDocNoService;
import com.daxia.wms.waybill.city100.dto.WayBillInfo;
import com.daxia.wms.waybill.city100.service.City100WaybillService;
import com.daxia.wms.waybill.youpai.service.YouPaiWaybillService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

/**
 * Created by szy on 2016/8/9.
 */
@Name("youPaiCartonNoGenerateService")
@lombok.extern.slf4j.Slf4j
public class YouPaiCartonNoGenerateServiceImpl implements CartonNoGenerateService {

    @In
    private SubDocNoService subDocNoService;

    @In(create = true)
    private YouPaiWaybillService youPaiWaybillService;

    @Override
    public void generatorCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        String orderNo = doHeader.getDoNo();
        if (StringUtil.isEmpty(orderNo)){
            throw new DeliveryException(DeliveryException.DO_NO_IS_NULL);
        }

        String cartonNo = subDocNoService.useOneByCarrierCorpId(Constants.CarrierCorp.YOUPAI.getValue(),null,null);

        if (StringUtil.isEmpty(cartonNo)) {
            throw new DeliveryException("优配单号不足，请联系网点！");
        }

        cartonHeader.setCartonNo(cartonNo);
        cartonHeader.setWayBill(cartonNo);

        WayBillInfo wayBillInfo = new WayBillInfo();
        wayBillInfo.setCartonId(cartonHeader.getId());
        wayBillInfo.setCartonNo(cartonHeader.getCartonNo());
        wayBillInfo.setReceivable(doHeader.getReceivable());
        wayBillInfo.setTelephone(DoUtil.decryptPhone(doHeader.getTelephone()));
        wayBillInfo.setMobile(DoUtil.decryptPhone(doHeader.getMobile()));
        wayBillInfo.setAddress(doHeader.getAddress());
        wayBillInfo.setConsigneeName(doHeader.getConsigneeName());
        wayBillInfo.setCarrierId(doHeader.getCarrierId());
        wayBillInfo.setWarehouseId(doHeader.getWarehouseId());

        try {
            youPaiWaybillService.sendWayBill(wayBillInfo);
        } catch (BusinessException e) {
            throw new DeliveryException("发送运单号至优派出错!",e);
        }

    }
}
