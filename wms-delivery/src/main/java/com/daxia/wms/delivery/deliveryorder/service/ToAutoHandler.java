package com.daxia.wms.delivery.deliveryorder.service;

import org.hibernate.Session;
import org.jboss.seam.Component;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.log.Log;

public abstract class ToAutoHandler {
	protected ToAutoHandler nextHandler;
	protected String name;
	@Logger
    protected Log log;

	public String getName() {
		return name;
	}
	
	public void handleTo(Long doId) throws Exception {
		setName();
		log.debug(getName(), doId);
		
		try {
			handle(doId);
		} catch (Exception e) {
			log.error(getName() + doId, e);
			throw e;
		}
		
		if (nextHandler != null) {
			Session session = (Session) Component.getInstance("hibernateSession");
			session.flush();
			session.clear();
			nextHandler.handleTo(doId);
		}
	}

	// 设置下一个处理请求的人
	public void setNextHandler(ToAutoHandler nextHandler) {
		this.nextHandler = nextHandler;
	}
	
	// 处理请求，由子类完成
	protected abstract void handle(Long doId);
	
	// 设置节点名称
	protected abstract void setName();
}
