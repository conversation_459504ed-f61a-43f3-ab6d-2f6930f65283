package com.daxia.wms.delivery.print.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.service.ReportGenerator;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.load.entity.ReversalHandoverHeader;
import com.daxia.wms.delivery.print.service.PrintReversalHandoverService;

/**
 * 逆向交接单打印业务实现类
 */
@Name("printReversalHandoverService")
@lombok.extern.slf4j.Slf4j
public class PrintReversalHandoverServiceImpl implements PrintReversalHandoverService {

    public static final String REPORT_NAME = "reversalHandover";
    
    @In
    private ReportGenerator reportGenerator;
    
    @Override
    public List<String> printReversalHandover(ReversalHandoverHeader reversalHandoverHeader) {
        if (ListUtil.isNullOrEmpty(reversalHandoverHeader.getReversalHandoverDetails())) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        Map<String, Object> params = this.getParams(reversalHandoverHeader);
        List<String> printData = new ArrayList<String>();
        printData.addAll(reportGenerator.builtPrintDataNoSub(REPORT_NAME, params,
                reversalHandoverHeader.getReversalHandoverDetails()));
        return printData;
    }

    /**
     * 打印参数
     * @param doType
     * @return
     */
    private Map<String, Object> getParams(ReversalHandoverHeader reversalHandoverHeader) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("reversalNo", reversalHandoverHeader.getReversalNo());
        params.put("count", reversalHandoverHeader.getReversalHandoverDetails().size());
        return params;
    }
}
