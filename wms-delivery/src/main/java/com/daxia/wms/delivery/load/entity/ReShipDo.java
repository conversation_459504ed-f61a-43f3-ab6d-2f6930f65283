package com.daxia.wms.delivery.load.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.Date;

/**
 * 临时表：记录分拣环自动交接失败（异步自动发货失败）的DO 供 定时任务重新发货
 */
@Entity
@Table(name = "exp_ship_queue")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update exp_ship_queue set is_deleted = 1 where id = ? and version = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class ReShipDo extends WhBaseEntity{

    private static final long serialVersionUID = 8619494139736773917L;
    
    private Long id;
    
    /**
     * 单据id，自动发货的数据，单据ID是订单ID，手动发货的数据，单据ID是交接单ID
     */
    private Long docId;

    /**
     * 是否是自动发货的数据 0:手动 1：自动
     */
    private Integer isAuto;

    /**
     * 发货失败次数
     */
    private Long count;
    
    /**
     * 操作时间
     */
    private Date opTime;

    /**
     * 下次调用时间
     */
    private Date nextInvokeTime;

    private DeliveryOrderHeader deliveryOrderHeader;
    
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)  
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "DOC_ID")
    public Long getDocId() {
        return docId;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }

    public void setIsAuto(Integer isAuto) {
        this.isAuto = isAuto;
    }

    @Column(name = "IS_AUTO")
    public Integer getIsAuto() {
        return isAuto;
    }

    @Column(name = "COUNT" )
    public Long getCount() {
        return count;
    }
    
    public void setCount(Long count) {
        this.count = count;
    }
    
    @Column(name = "OP_TIME" )
	public Date getOpTime() {
		return opTime;
	}

	public void setOpTime(Date opTime) {
		this.opTime = opTime;
	}

	@Column(name = "next_invoke_time")
    public Date getNextInvokeTime() {
        return nextInvokeTime;
    }

    public void setNextInvokeTime(Date nextInvokeTime) {
        this.nextInvokeTime = nextInvokeTime;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DOC_ID", insertable = false, updatable = false)
    public DeliveryOrderHeader getDeliveryOrderHeader() {
        return deliveryOrderHeader;
    }

    public void setDeliveryOrderHeader(DeliveryOrderHeader deliveryOrderHeader) {
        this.deliveryOrderHeader = deliveryOrderHeader;
    }
}