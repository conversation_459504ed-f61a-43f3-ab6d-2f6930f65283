/**
 * 
 */
package com.daxia.wms.delivery.wave.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * 促销单品波次及箱信息DTO
 */
@lombok.extern.slf4j.Slf4j
public class BatchGroupWaveDTO implements Serializable {

	private static final long serialVersionUID = -4615777310247190366L;

	private Long waveId;
	
	/**
	 * 波次号
	 */
	private String waveNo;
	
	/**
	 * 总箱数
	 */
	private Long totalCartons;
	
	/**
	 * 已打印箱标签的箱数
	 */
	private Long totalPrintedCarton;
	
	/**
	 * 该波次箱标签打印状态
	 */
	private Integer cartonPrintStatus;
	
	/**
	 * 波次状态
	 */
	private String waveStatus;
	
	/**
     * 波次最早预计出库时间
     */
    private Date estDoFinishTime;
    
    /**
     * 创建时间
     */
    private Date createtime;
    
    /**
     * 创建人
     */
    private String createBy;
    
	public Long getWaveId() {
		return waveId;
	}

	public void setWaveId(Long waveId) {
		this.waveId = waveId;
	}

	public String getWaveNo() {
		return waveNo;
	}

	public void setWaveNo(String waveNo) {
		this.waveNo = waveNo;
	}

	public Long getTotalCartons() {
		return totalCartons;
	}

	public void setTotalCartons(Long totalCartons) {
		this.totalCartons = totalCartons;
	}

	public Integer getCartonPrintStatus() {
		return cartonPrintStatus;
	}

	public void setCartonPrintStatus(Integer cartonPrintStatus) {
		this.cartonPrintStatus = cartonPrintStatus;
	}

	public String getWaveStatus() {
		return waveStatus;
	}

	public void setWaveStatus(String waveStatus) {
		this.waveStatus = waveStatus;
	}

	public Date getEstDoFinishTime() {
		return estDoFinishTime;
	}

	public void setEstDoFinishTime(Date estDoFinishTime) {
		this.estDoFinishTime = estDoFinishTime;
	}

	public Date getCreatetime() {
		return createtime;
	}

	public void setCreatetime(Date createtime) {
		this.createtime = createtime;
	}

	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}

	public Long getTotalPrintedCarton() {
		return totalPrintedCarton;
	}

	public void setTotalPrintedCarton(Long totalPrintedCarton) {
		this.totalPrintedCarton = totalPrintedCarton;
	}
}
