package com.daxia.wms.delivery.recheck.action;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.ActionBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.print.service.PrintDoInvoiceService;
import com.daxia.wms.delivery.print.service.PrintInvoiceService;
import com.daxia.wms.master.entity.Printer;
import com.daxia.wms.print.PrintConstants.PrintInvoicePos;
import com.daxia.wms.print.dto.PrintData;

/**
 * 绑定发票处理action
 */
@Name("bindRollInvoiceAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class BindRollInvoiceAction extends ActionBean {

	private static final long serialVersionUID = 4669516073346334087L;

	/**
	 * 打印机编号
	 */
	private String printerCode;
	
	/**
	 * 发票代码
	 */
	private String invoiceCode;
	
	/**
	 * 发票开始号码
	 */
	private String firstInvoiceNo;
	
	/**
	 * 发票结束号码
	 */	
	private String lastInvoiceNo;
	
	/**
	 * 当前发票号码
	 */
	private String curInvoiceNo;
	
	/**
	 * 原始打印机信息
	 */
	private Printer originalPrinter = new Printer();
	
	/**
	 * 发票代码长度
	 */
	private Integer invoiceCodeLength;
	
	/**
	 * 发票号长度
	 */
	private Integer invoiceNoLength;
	
	/**
	 * 每卷发票的张数
	 */
	private Integer countPerRoll;
	
	/**
	 * 打印的单据号
	 */
	private String docNo;
	
	private PrintData invoiceData; // 发票打印数据
	
	@In
	private PrintInvoiceService printInvoiceService;
	
	@In
    private PrintDoInvoiceService printDoInvoiceService;
	
	@In
	private DeliveryOrderService deliveryOrderService;
    /**
	 * 锁定打印机
	 */
	public void validatePrinter(){
	    //在打开绑定发票页面的时候校验先打印机信息
		originalPrinter = printInvoiceService.validatePrinter(this.getPrinterCode());
	}
	

	public void bindNewInvoice() {
	    printInvoiceService.bindNewInvoice(printerCode, invoiceCode, firstInvoiceNo, lastInvoiceNo);
	    this.invoiceCode = "";
	    this.firstInvoiceNo = "";
	    this.lastInvoiceNo = "";
	    this.curInvoiceNo = "";
        this.sayMessage(MESSAGE_SUCCESS);
	}
	
	/**
	 * 打开绑定发票页面
	 */
	public void openBindNewInvoice() {
		try {
	      //在打开绑定发票页面的时候校验先打印机信息，并设置原始的打印机信息
	        originalPrinter = printInvoiceService.validatePrinter(this.getPrinterCode());
	        if (null != originalPrinter) {
	            this.setInvoiceCode(originalPrinter.getInvoiceCode());
	            this.setFirstInvoiceNo(originalPrinter.getInvoiceNoFrom());
	            this.setLastInvoiceNo(originalPrinter.getInvoiceNoTo());
	        }
	        // 设置卷票的基本配置信息
	        Integer iCodeLength = SystemConfig.getConfigValueInt("print.invoice.invoiceCodeLength", ParamUtil.getCurrentWarehouseId());
			Integer iNoLength = SystemConfig.getConfigValueInt("print.invoice.invoiceNoLength", ParamUtil.getCurrentWarehouseId());
			this.invoiceCodeLength = (iCodeLength == null ? Integer.valueOf(12) : iCodeLength);
			this.invoiceNoLength = (iNoLength == null ? Integer.valueOf(8) : iNoLength);
			this.countPerRoll = (countPerRoll == null ? Integer.valueOf(100) : countPerRoll);
	    } catch (DeliveryException e) {
	        // 当校验不通过时，清空参数，避免页面缓存
	        clearInvoiceInfoInput();
            throw e;
	    }
	}
	

	/**
     * 打开更新当前发票号码界面
     */
    public void openUpdateCurInvoiceNo() {
        try {
          //在打开绑定发票页面的时候校验先打印机信息，并设置原始的打印机信息
            originalPrinter = printInvoiceService.validatePrinter(this.getPrinterCode());
            if (null != originalPrinter && StringUtil.isEmpty(originalPrinter.getInvoiceNoCurrent())) {
                throw new DeliveryException(DeliveryException.PRINTER_IS_NOT_BIND_INVOICE);
            }
            if (null != originalPrinter) {
                this.setCurInvoiceNo(originalPrinter.getInvoiceNoCurrent());
                this.setFirstInvoiceNo(originalPrinter.getInvoiceNoFrom());
                this.setLastInvoiceNo(originalPrinter.getInvoiceNoTo());
            }
            
          } catch (DeliveryException e) {
              // 当校验不通过时，清空参数，避免页面缓存
              clearInvoiceInfoInput();
              throw e;
          }
    }
    
    /**
     * 打开打印发票界面
     */
    public void openInvoicePrint() {
        
    }
    
    /**
     * 更新当前发票号码
     */
    public void updateCurInvoiceNo() {
        printInvoiceService.updateCurInvoiceNo(printerCode, curInvoiceNo);
        this.sayMessage(MESSAGE_SUCCESS);
    }
    
    /**
     * 清空几个发票细腻系输入框
     */
    public void clearInvoiceInfoInput() {
        this.invoiceCode = "";
        this.firstInvoiceNo = "";
        this.lastInvoiceNo = "";
        this.curInvoiceNo = "";
    }
    
    /**
     * 核捡装箱，根据DO自动打印发票
     */
    public void printInvoiceRepairByDo() {
    	if (null != invoiceData) {
    		invoiceData.clear();
    	}
    	
    	DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(docNo);
        invoiceData = printDoInvoiceService.printInvoice(doHeader, printerCode, PrintInvoicePos.RECHECK_N_RE);
        originalPrinter = invoiceData.getPrintCfg().getPrinter();
        //提示发票不够
        if (StringUtil.isNotEmpty(invoiceData.getUserDefine1())) {
            throw new DeliveryException(invoiceData.getUserDefine1(), invoiceData.getUserDefine2(), invoiceData.getUserDefine3());
        }
    }


	public String getPrinterCode() {
		return printerCode;
	}


	public void setPrinterCode(String printerCode) {
		this.printerCode = printerCode;
	}


	public String getInvoiceCode() {
		return invoiceCode;
	}


	public void setInvoiceCode(String invoiceCode) {
		this.invoiceCode = invoiceCode;
	}


	public String getFirstInvoiceNo() {
		return firstInvoiceNo;
	}


	public void setFirstInvoiceNo(String firstInvoiceNo) {
		this.firstInvoiceNo = firstInvoiceNo;
	}


	public String getLastInvoiceNo() {
		return lastInvoiceNo;
	}


	public void setLastInvoiceNo(String lastInvoiceNo) {
		this.lastInvoiceNo = lastInvoiceNo;
	}


	public String getCurInvoiceNo() {
		return curInvoiceNo;
	}


	public void setCurInvoiceNo(String curInvoiceNo) {
		this.curInvoiceNo = curInvoiceNo;
	}


	public Printer getOriginalPrinter() {
		return originalPrinter;
	}


	public void setOriginalPrinter(Printer originalPrinter) {
		this.originalPrinter = originalPrinter;
	}


	public Integer getInvoiceCodeLength() {
		return invoiceCodeLength;
	}


	public void setInvoiceCodeLength(Integer invoiceCodeLength) {
		this.invoiceCodeLength = invoiceCodeLength;
	}


	public Integer getInvoiceNoLength() {
		return invoiceNoLength;
	}


	public void setInvoiceNoLength(Integer invoiceNoLength) {
		this.invoiceNoLength = invoiceNoLength;
	}


	public Integer getCountPerRoll() {
		return countPerRoll;
	}


	public void setCountPerRoll(Integer countPerRoll) {
		this.countPerRoll = countPerRoll;
	}


	public PrintData getInvoiceData() {
		return invoiceData;
	}


	public void setInvoiceData(PrintData invoiceData) {
		this.invoiceData = invoiceData;
	}


	public String getDocNo() {
		return docNo;
	}


	public void setDocNo(String docNo) {
		this.docNo = docNo;
	}
	
}
