package com.daxia.wms.delivery.pick.service;

import java.math.BigDecimal;
import java.util.List;

import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.pick.dto.BatchPickDTO;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.master.entity.Container;

public interface PickService {

	/**
	 * 拣货业务(根据拣货单拣货)
	 * @param pktNo 拣货单号
	 * @param pickerNo 拣货人编号
	 * @throws DeliveryException 拣货失败抛出的异常
	 */
	public void pick(String pktNo, String pickerNo,Boolean autoSort)throws DeliveryException;
	
	/**
	 * (RF拣货)正常拣货
	 */
	public void pickByTask(List<PickTask> pickTasks, Container container,BigDecimal pickedQtyUnit,  BigDecimal pickedQty,
			WaveHeader waveHeader, String reason, String updateBy);

	/**
	 * (RF拣货)正常拣货
	 */
	public void pickByTask(List<PickTask> pickTasks, Container container,BigDecimal pickedQtyUnit,  BigDecimal pickedQty,
						   WaveHeader waveHeader, String reason, String updateBy,String containerNo);
	
	/**
	 * (RF拣货)非正常拣货，点击缺货，不操作库存
	 */
	public void pickByTask(List<PickTask> pickTasks, String reason, String updateBy);
	
	
	/**
	 * 强制拣货
	 */
	public void force2pick(Long doId);
	
	/**
	 * 修改波次信息
	 * @param waveHeader
	 * @param updateBy
	 * @param allowDelete
	 * @param autoSort 自动分拣
     * @throws DeliveryException
	 */
	public void modifyWave(WaveHeader waveHeader, String updateBy, boolean allowDelete,Boolean autoSort) throws DeliveryException;
	
	/**
	 * 修改do单头和波次
	 * @param doHeader
	 */
	public void modifyDoHeaderAndWaveHeader(DeliveryOrderHeader doHeader, boolean removeDo);
	
	/**
	 * 微便利按DO拣货
	 * @param doId
	 */
	public void doHandlePick(Long doId);
	
	/**
	 * 按照DO强制拣其有缺货标记的拣货任务，更新其拣货单
	 * @param doHeader
	 * @param updateBy
	 */
	public void forcePickTsk(DeliveryOrderHeader doHeader, String updateBy);
	
	/**
	 * 查询发货明细的拣货任务数最大的拣货任务
	 * @param doDetailId
	 * @return
	 */
	public PickTask queryMaxCountByDetail(Long doDetailId);
	
	/**
	 *  客服取消拣货中的DO
	 * @param doHeader
	 * @return
	 */
	public int doRollBackPickTask(DeliveryOrderHeader doHeader);
	
	public boolean isAllLackDo(DeliveryOrderHeader doHeader);
	
	public boolean rollBackLackDoPickTask(DeliveryOrderHeader doHeader);

    /**
     * dh.sortGridNo, dh.doNo, sum(p.qtyPickedEach)
     * @param pickTaskList
     * @return
     */
	List<Object[]> getPickSortDetails(List<Long> pickTaskList);

	void completLackPick(Long doId);

	/**
	 * 修改以拣货数量
     * @param pickTask
     * @param recheckQty
     * @param value
     */
	void updatePickQty(PickTask pickTask, BigDecimal recheckQty, String reversePickReason);

	Long isStockIsLock(Long id);
    
    List<Object[]> getPickSortDetails(String docNum, Integer docType, Long skuId);

	/**
	 * 批量更新拣货记录
	 * @param batchIdList
	 * @param container
	 * @param pickerNo
	 * @param reason
	 */
	void batchUpdate4Pick(List<BatchPickDTO> batchIdList, Container container, String pickerNo, String reason);
 
	//同时存在整、散件的拣货任务；
    boolean existCBType(Long doId);

	void flushSession();

    void pickByWaveId(Long waveId);
}
