package com.daxia.wms.delivery.recheck.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeaderHis;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 装箱头历史表
 */
@Entity
@Table(name = "doc_carton_header_his")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_carton_header_his set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class CartonHeaderHis extends WhBaseEntity {
    private static final long serialVersionUID = -2523549403562097535L;

    private Long id;

    /**
     * 箱号
     */
    private String cartonNo;

    /**
     * 毛重
     */
    private BigDecimal grossWeight;

    /**
     * 实测重量
     */
    private BigDecimal actualGrossWeight;

    /**
     * 体积
     */
    private BigDecimal volume;

    /**
     * 实测体积
     */
    private BigDecimal actualVolume;

    /**
     * 是否已经称重，0：未称重；1：已经称重
     */
    private int weightFlag;
    
    /**
     * 净重
     */
    private BigDecimal netWeight;

    /**
     * 运单号
     */
    private String trackingNo;
    
    /**
     * 装箱单状态  40 ：装箱完成   90 ： 取消
     */
    private String status;
    
    private String lpnNo;

    /**
     * 发货单历史
     */
    private DeliveryOrderHeaderHis doHeader;
    
    /**
     * 装箱明细历史
     */
    private List<CartonDetailHis> cartonDetails;
    /**
     * 货物放行 0未放行 1 已放行'
     */
    private Integer  goodPass;
    @Column(name = "good_pass")
    public Integer getGoodPass() {
        return goodPass;
    }

    public void setGoodPass(Integer goodPass) {
        this.goodPass = goodPass;
    }
    @Id
    @Column(name = "ID")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "CARTON_NO")
    public String getCartonNo() {
        return cartonNo;
    }

    public void setCartonNo(String cartonNo) {
        this.cartonNo = cartonNo;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DO_HEADER_ID")
    @Where(clause = " IS_DELETED = 0 ")
    public DeliveryOrderHeaderHis getDoHeader() {
        return doHeader;
    }

    public void setDoHeader(DeliveryOrderHeaderHis doHeader) {
        this.doHeader = doHeader;
    }

    @Transient
    public Long getDoHeaderId() {
        return doHeader != null ? doHeader.getId() : null;
    }

    public void setDoHeaderId(Long doHeaderId) {
        this.doHeader = new DeliveryOrderHeaderHis();
        doHeader.setId(doHeaderId);
    }

    @Column(name = "GROSSWEIGHT")
    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }

    @Column(name = "ACTUAL_GROSSWEIGHT")
    public BigDecimal getActualGrossWeight() {
        return actualGrossWeight;
    }

    public void setActualGrossWeight(BigDecimal actualGrossWeight) {
        this.actualGrossWeight = actualGrossWeight;
    }

    @Column(name = "VOLUME")
    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    @Column(name = "ACTUAL_VOLUME")
    public BigDecimal getActualVolume() {
        return actualVolume;
    }

    public void setActualVolume(BigDecimal actualVolume) {
        this.actualVolume = actualVolume;
    }

    @Column(name = "IS_WEIGHT")
    public int getWeightFlag() {
        return weightFlag;
    }

    public void setWeightFlag(int weightFlag) {
        this.weightFlag = weightFlag;
    }

    @Transient
    public boolean isWeight() {
        return this.weightFlag == 1;
    }

    public void setWeight(boolean weight) {
        this.weightFlag = weight ? 1 : 0;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "cartonHeader")
    @Where(clause = " IS_DELETED = 0 ")
    public List<CartonDetailHis> getCartonDetails() {
        return cartonDetails;
    }

    public void setCartonDetails(List<CartonDetailHis> cartonDetails) {
        this.cartonDetails = cartonDetails;
    }

    @Column(name = "STATUS")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setTrackingNo(String trackingNo) {
        this.trackingNo = trackingNo;
    }

    @Column(name = "TRACKING_NO")
    public String getTrackingNo() {
        return trackingNo;
    }

    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }
    
    @Column(name = "NETWEIGHT")
    public BigDecimal getNetWeight() {
        return netWeight;
    }

    @Column(name = "LPN_NO")
    public String getLpnNo() {
        return lpnNo;
    }

    public void setLpnNo(String lpnNo) {
        this.lpnNo = lpnNo;
    }
}
