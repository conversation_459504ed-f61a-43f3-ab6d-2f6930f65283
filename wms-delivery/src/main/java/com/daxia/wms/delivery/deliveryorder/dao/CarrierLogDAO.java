package com.daxia.wms.delivery.deliveryorder.dao;

import com.daxia.framework.common.util.ParamUtil;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.delivery.deliveryorder.entity.CarrierLog;

import java.util.List;

@Name("com.daxia.wms.delivery.carrierLogDAO")
@lombok.extern.slf4j.Slf4j
public class CarrierLogDAO extends HibernateBaseDAO<CarrierLog, Long> {

	private static final long serialVersionUID = -5683873841049361032L;
	public void insertCarrierLog(List<Long> idList, Long toCarrierId) {
		String hql = " insert into  doc_carrier_log (DO_HEADER_ID, "+
				" CARRIER_ID , "+
				" TO_CARRIER_ID , "+
				" CREATE_TIME , "+
				" CREATE_BY, "+
				" UPDATE_TIME, "+
				" UPDATE_BY , "+
				" WAREHOUSE_ID) "+
				" select ddh.id,ddh.carrier_id,:toCarrierId,sysdate(),:operator,sysdate(),:operator,:warehouseId from doc_do_header ddh where ddh.id in (:ids) ";
		Query query = createSQLQuery(hql);
		query.setLong("toCarrierId", toCarrierId);
		query.setParameterList("ids", idList);
		query.setParameter("operator", ParamUtil.getCurrentLoginName());
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.executeUpdate();
	}
}
