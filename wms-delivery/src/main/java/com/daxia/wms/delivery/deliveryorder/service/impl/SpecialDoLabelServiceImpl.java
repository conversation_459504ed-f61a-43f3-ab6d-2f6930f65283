package com.daxia.wms.delivery.deliveryorder.service.impl;

import java.util.List;
import java.util.Map;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.log.Log;

import com.daxia.dubhe.api.wms.request.DoRequest;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.MvelUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.tianshu.api.request.DeliveryOrderRequest;
import com.daxia.wms.Constants.AutoWaveType;
import com.daxia.wms.delivery.deliveryorder.dao.SpecialDoLabelDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.delivery.deliveryorder.entity.SpecialDoLabel;
import com.daxia.wms.delivery.deliveryorder.service.SpecialDoLabelService;
import com.google.common.collect.Maps;

@Name("com.daxia.wms.master.specialDoLabelService")
@lombok.extern.slf4j.Slf4j
public class SpecialDoLabelServiceImpl implements SpecialDoLabelService {



	@In
	SpecialDoLabelDAO specialDoLabelDAO;

	@Override
	public List<SpecialDoLabel> findAvailable() {
		return specialDoLabelDAO.findAvailable(ParamUtil.getCurrentWarehouseId());
	}

	/**
	 * 根据ID查找订单波次扩展表中的特殊标记对应的波次细分类型，如果所有订单的波次细分类型一致，那么返回该类型，否则返回为NULL
	 */
	@Override
	public AutoWaveType findWaveDetailType(List<Long> selectedDOIdList) {
		List<Integer> waveDetailTypes = specialDoLabelDAO.findWaveDetailType(selectedDOIdList);
		if (ListUtil.isNotEmpty(waveDetailTypes) && waveDetailTypes.size() == 1) {
			Integer waveDetailType = waveDetailTypes.get(0);
			if (waveDetailType != null) {
				return AutoWaveType.of(waveDetailType);
			}
		}
		return AutoWaveType.NORMAL;
	}

	@Override
	public boolean isRequestAvailable(Integer waveDetailType) {
		List<SpecialDoLabel> specialDoLabels = specialDoLabelDAO.findByWaveDetailType(waveDetailType);
		if (ListUtil.isNullOrEmpty(specialDoLabels)) {
			return true;
		} else {
			SpecialDoLabel first = specialDoLabels.get(0);
			return first == null || first.getRequestEnabled();
		}
	}
}