package com.daxia.wms.delivery.deliveryorder.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.master.entity.Carrier;
import com.daxia.wms.master.entity.Station;

/**
 * 配送商修改日志
 *
 */
@Entity
@Table(name = "doc_carrier_log")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " is_deleted = 0 ")
@SQLDelete(sql = "update doc_carrier_log set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class CarrierLog extends WhBaseEntity {
	
	private static final long serialVersionUID = 5890089735295974021L;
	
	private Long id;
	private Long doHeaderId;//发货单ID
	private Long carrierId;//原配送商
	private Long toCarrierId;//目标配送商
	private Long stationId;//原配送站点
	private Long toStationId;//目标配送站点
	
	private String udf1;
	private String udf2;
	private Integer isDeleted;
	
	//do单头信息
	private DeliveryOrderHeader doHeaderInfo;
	
	//原配送商
	private Carrier carrierInfo;
	
	//修改后配送商
	private Carrier toCarrierInfo;
	
	//原配送站点
	private Station stationInfo;
	
	//修改后配送站点
	private Station toStationInfo;
	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)  
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	@Column(name="DO_HEADER_ID")
	public Long getDoHeaderId() {
		return doHeaderId;
	}
	public void setDoHeaderId(Long doHeaderId) {
		this.doHeaderId = doHeaderId;
	}
	
	@Column(name="CARRIER_ID")
	public Long getCarrierId() {
		return carrierId;
	}
	public void setCarrierId(Long carrierId) {
		this.carrierId = carrierId;
	}
	
	@Column(name="TO_CARRIER_ID")
	public Long getToCarrierId() {
		return toCarrierId;
	}
	public void setToCarrierId(Long toCarrierId) {
		this.toCarrierId = toCarrierId;
	}
	
	@Column(name="UDF1")
	public String getUdf1() {
		return udf1;
	}
	public void setUdf1(String udf1) {
		this.udf1 = udf1;
	}
	
	@Column(name="UDF2")
	public String getUdf2() {
		return udf2;
	}
	public void setUdf2(String udf2) {
		this.udf2 = udf2;
	}
	
	@Column(name="IS_DELETED")
	public Integer getIsDeleted() {
		return isDeleted;
	}
	public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}
	
	@ManyToOne(fetch = FetchType.LAZY )
    @JoinColumn(name = "DO_HEADER_ID",insertable = false, updatable = false)
	public DeliveryOrderHeader getDoHeaderInfo() {
		return doHeaderInfo;
	}
	public void setDoHeaderInfo(DeliveryOrderHeader doHeaderInfo) {
		this.doHeaderInfo = doHeaderInfo;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CARRIER_ID",insertable = false, updatable = false)
	public Carrier getCarrierInfo() {
		return carrierInfo;
	}
	public void setCarrierInfo(Carrier carrierInfo) {
		this.carrierInfo = carrierInfo;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TO_CARRIER_ID",insertable = false, updatable = false)
	public Carrier getToCarrierInfo() {
		return toCarrierInfo;
	}
	public void setToCarrierInfo(Carrier toCarrierInfo) {
		this.toCarrierInfo = toCarrierInfo;
	}
	
	@Column(name="STATION_ID")
	public Long getStationId() {
		return stationId;
	}
	
	public void setStationId(Long stationId) {
		this.stationId = stationId;
	}
	
	@Column(name="TO_STATION_ID")
	public Long getToStationId() {
		return toStationId;
	}
	
	public void setToStationId(Long toStationId) {
		this.toStationId = toStationId;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "STATION_ID",insertable = false, updatable = false)
	public Station getStationInfo() {
		return stationInfo;
	}
	
	public void setStationInfo(Station stationInfo) {
		this.stationInfo = stationInfo;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TO_STATION_ID",insertable = false, updatable = false)
	public Station getToStationInfo() {
		return toStationInfo;
	}
	
	public void setToStationInfo(Station toStationInfo) {
		this.toStationInfo = toStationInfo;
	}
}