package com.daxia.wms.delivery.deliveryorder.service.impl;

import com.daxia.framework.common.util.*;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.AisinogzInvoiceCallService;
import com.daxia.wms.delivery.invoice.dao.InvoiceDao;
import com.daxia.wms.delivery.invoice.entity.InvoiceDetail;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.extend.aisinogz.IEliWebService;
import com.daxia.wms.delivery.invoice.service.InvoiceService;
import com.daxia.wms.invoice.entity.*;
import com.daxia.wms.master.entity.Merchant;
import com.daxia.wms.master.entity.MerchantInvoiceEx;
import com.daxia.wms.master.service.MerchantInvoiceExService;
import com.daxia.wms.master.service.MerchantService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.springframework.beans.BeanUtils;

import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.List;

@Name("aisinogzInvoiceCallService")
@lombok.extern.slf4j.Slf4j
public class AisinogzInvoiceCallServiceImpl implements AisinogzInvoiceCallService {

    @In
    private MerchantService merchantService;

    @In
    private MerchantInvoiceExService merchantInvoiceExService;

    @In
    private InvoiceDao invoiceDao;
    @In(create = true)
    private InvoiceService invoiceService;


    /**
     * 测试发送订单开票
     */
    @Override
    @Transactional
    public void sendInvEli(InvoiceHeader invoiceHeader, boolean isSync) {
        if (!invoiceHeader.getInvoiceType().equals(InvoiceHeader.InvoiceType.ELECTRONIC.getValue())) {
            return;
        }
        if (InvoiceHeader.InvoiceStatus.INIT == invoiceHeader.getInvoiceStatus()) {
            if (invoiceHeader == null) {
                throw new BusinessException("没有找到对应的发票信息");
            }
            List<InvoiceDetail> details = invoiceHeader.getInvoiceDetails();
            if (ListUtil.isNullOrEmpty(details)) {
                throw new BusinessException("发票对应的开票明细信息不存在");
            }
            Merchant mc = merchantService.getMerchant(invoiceHeader.getMerchantId());
            if (mc == null) {
                throw new BusinessException("没有找到发票对应的商家信息");
            }
            MerchantInvoiceEx merchantInvoiceEx = merchantInvoiceExService.findByMerchantId(mc.getId());
            if (merchantInvoiceEx == null) {
                throw new BusinessException("商家对应的发票配置信息不存在");
            }

            String orgSequenceNo = invoiceHeader.getReqSequenceNo();
            if (StringUtil.isEmpty(orgSequenceNo)) {
                String invoiceIdStr = invoiceHeader.getId().toString();
                int length = invoiceIdStr.length();
                for (int i = 0; i < 15 - length; i++) {
                    invoiceIdStr = "0" + invoiceIdStr;
                }
                invoiceIdStr = "U" + invoiceIdStr + "0001"; // 普通上传
                orgSequenceNo = invoiceIdStr;
            } else {
                String strIndex = orgSequenceNo.substring(orgSequenceNo.length() - 4, orgSequenceNo.length());
                int curIndex = Integer.valueOf(strIndex) + 1;
                String invoiceIdStr = String.valueOf(curIndex);
                int length = String.valueOf(curIndex).length();
                for (int j = 0; j < 4 - length; j++) {
                    invoiceIdStr = "0" + invoiceIdStr;
                }
                orgSequenceNo = orgSequenceNo.substring(0, orgSequenceNo.length() - 4) + invoiceIdStr;
            }
            IEliWebService wb = new IEliWebService();
            // 对象工厂
            ObjectFactory of = new ObjectFactory();
            /******** 发票总体类 ********/
            ElectroniceInfo e = new ElectroniceInfo();

            generateCommonInfo(invoiceHeader, mc, merchantInvoiceEx, orgSequenceNo, of, e);
            // 购货方名称(发票抬头)
            e.setGHFMC(of.createElectroniceInfoGHFMC(invoiceHeader.getInvoiceTitle()));
            // 开票员
            e.setKPR(of.createElectroniceInfoKPR(merchantInvoiceEx.getDrawer()));
            // 开票类型
            e.setKPLX(of.createElectroniceInfoKPLX(InvoiceHeader.KpType.NORMAL.getValue()));
            // 操作代码
            e.setCZDM(of.createElectroniceInfoCZDM(InvoiceHeader.OperateType.NORMAL.getValue()));
            // 价税合计金额
            e.setKPHJJE(invoiceHeader.getInvoiceAmount());
            // 备注
            e.setBZ(of.createElectroniceInfoBZ(""));//TODO 备注逻辑按要求修改。
            e.setBMBBBH(of.createElectroniceInfoBMBBBH("12.0"));
            ArrayOfElectroniceDetail list = new ArrayOfElectroniceDetail();
            /******** 发票明细类 ********/
            ArrayList<ElectroniceDetail> ds = (ArrayList<ElectroniceDetail>) list
                    .getElectroniceDetail();
            for (InvoiceDetail invoiceDetail : invoiceHeader.getInvoiceDetails()) {
                ElectroniceDetail detail = new ElectroniceDetail();
                // 项目名称
                detail.setXMMC(of.createElectroniceDetailXMMC(invoiceDetail.getSkuDescr()));
                // 项目单位
                detail.setDW(of.createElectroniceDetailDW(invoiceDetail.getUomDescr()));
                // 规格型号
                detail.setGGXH(of.createElectroniceDetailGGXH(invoiceDetail.getSkuDescr()));
                // 项目编码
//				detail.setXMBM(of.createElectroniceDetailXMBM(invoiceDetail.ge)); TODO 项目编码
                // 项目数量
                detail.setXMSL(new Double(invoiceDetail.getQty().toString()));
                // 项目单价
                detail.setXMDJ(new Double(invoiceDetail.getPrice().toString()));
                // 含税标志
                detail.setHSBZ(of.createElectroniceDetailHSBZ(Constants.YesNo.YES.getValue().toString()));
                // 项目金额
                detail.setXMJE(new Double(invoiceDetail.getAmount().toString()));
                // 税率
                detail.setSL(of.createElectroniceDetailSL(invoiceDetail.getTaxRate().toString()));
                // 项目税额
//				detail.setSE(0.15);  	TODO 不传应该没有问题。
                ds.add(detail);
            }

            e.setDetails(of.createElectroniceInfoDetails(list));
            ReturnElectronice result;
            if (isSync) {
                result = wb.getIEliWebServiceHttpPort().invEli(e);
            } else {
                result = wb.getIEliWebServiceHttpPort().sendToInvEli(e);
            }

            if (result == null) {
                Integer failedNum = invoiceHeader.getFailedNum() == null ? Integer.valueOf(0) : invoiceHeader.getFailedNum();
                invoiceHeader.setReqErrorCode("请求接口异常，请联系管理员！");
                invoiceHeader.setFailedNum(failedNum + 1);
            } else {
                if ("0".equals(result.getReturnCode())) {
                    if (isSync) {
                        //同步调用后直接把结果返回更新
                        updateInvoiceByResult(invoiceHeader, result);
                    } else {
                        invoiceHeader.setInvoiceStatus(InvoiceHeader.InvoiceStatus.UPLOAD);
                        invoiceHeader.setReqErrorCode("");
                        invoiceHeader.setFailedNum(0);
                        System.out
                                .println("测试请求开票(异步)返回信息：" + result.getReturnMsg().getValue());
                    }

                } else {
                    Integer failedNum = invoiceHeader.getFailedNum() == null ? Integer.valueOf(0) : invoiceHeader.getFailedNum();
                    invoiceHeader.setReqErrorCode(result.getReturnMsg().getValue());
                    invoiceHeader.setFailedNum(failedNum + 1);
                }
                invoiceHeader.setReqSequenceNo(orgSequenceNo);
            }
            invoiceDao.saveOrUpdate(invoiceHeader);
        }
    }

    /**
     * 测试查询订单状态
     */
    @Override
    public void bind(InvoiceHeader invoiceHeader) {
        if (!invoiceHeader.getInvoiceType().equals(
                InvoiceHeader.InvoiceType.ELECTRONIC.getValue())) {
            return;
        }
        if (InvoiceHeader.InvoiceStatus.UPLOAD == invoiceHeader.getInvoiceStatus()) {
            MerchantInvoiceEx merchantInvoiceEx = merchantInvoiceExService.findByMerchantId(invoiceHeader.getMerchantId());
            if (merchantInvoiceEx == null) {
                throw new BusinessException("商家对应的发票配置信息不存在");
            }
            IEliWebService wb = new IEliWebService();
            // 对象工厂
            ObjectFactory of = new ObjectFactory();
            /******** 发票总体类 ********/
            ElectroniceInfo e = new ElectroniceInfo();
            // 查询条件的流水号,订单号,开票方纳税人识别号,
            e.setFPQQLSH(of.createElectroniceInfoFPQQLSH(invoiceHeader.getReqSequenceNo()));
            e.setDDH(of.createElectroniceInfoDDH(invoiceHeader.getSoCode()));
            e.setKPNSRSBH(of.createElectroniceInfoKPNSRSBH(merchantInvoiceEx.getTaxPayerCode()));
            e.setXHFNSRSBH(of.createElectroniceInfoXHFNSRSBH(merchantInvoiceEx.getTaxPayerCode()));

            ReturnElectronice result = wb.getIEliWebServiceHttpPort().queryEliData(
                    e);
            if (result == null) {
                Integer failedNum = invoiceHeader.getFailedNum() == null ? Integer
                        .valueOf(0) : invoiceHeader.getFailedNum();
                invoiceHeader.setReqErrorCode("请求接口异常，请联系管理员！");
                invoiceHeader.setFailedNum(failedNum + 1);
            } else {
                String code = result.getReturnCode().toString();
                // 成功修改发票状态，回写sequenceno。
                if ("0".equals(code)) {
                    updateInvoiceByResult(invoiceHeader, result);
                } else {
                    invoiceHeader.setReqErrorCode(result.getReturnCode().toString());
                    invoiceHeader
                            .setFailedNum(invoiceHeader.getFailedNum() + 1);
                }
            }
            invoiceDao.saveOrUpdate(invoiceHeader);
        }
    }

    private void updateInvoiceByResult(InvoiceHeader invoiceHeader, ReturnElectronice result) {
        invoiceHeader.setInvoiceStatus(InvoiceHeader.InvoiceStatus.BILLED);
        invoiceHeader.setInvoiceCode(result.getFPDM().toString());
        invoiceHeader.setInvoiceNumber(result.getFPHM().toString());
        invoiceHeader.setCheckCode(result.getJYM().toString());
        invoiceHeader.setInvoiceUrl(result.getPDFURL().toString());
        invoiceHeader.setReqErrorCode("");
        invoiceHeader.setFailedNum(0);
        System.out.println("测试查询订单状态返回信息：" + result.getReturnMsg().getValue() + ";发票代码：" + result.getFPDM().getValue() + ";发票号码：" + result.getFPHM().getValue() + ";检验码：" + result.getJYM().getValue());
        if (result.getPDFURL() != null) {
            try {
                URL url = new URL("http://192.168.10.43:8080/IS/pd?id=" + result.getPDFURL().getValue());
                // 返回一个 URLConnection 对象，它表示到 URL 所引用的远程对象的连接。
                URLConnection uc = url.openConnection();

                // 打开的连接读取的输入流。
                InputStream is = uc.getInputStream();

                byte[] b = new byte[1024];
                int len = 0;
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                while ((len = is.read(b)) != -1) {
                    bos.write(b, 0, len);
                }
                bos.close();

                byte[] getData = bos.toByteArray();

                // 文件保存位置
                File saveDir = new File("D:\\电子发票");
                if (!saveDir.exists()) {
                    saveDir.mkdir();
                }
                File file = new File(saveDir + File.separator + result.getPDFURL().getValue());
                FileOutputStream fos = new FileOutputStream(file);
                fos.write(getData);
                if (fos != null) {
                    fos.close();
                }

                fos.flush();
                is.close();
            } catch (MalformedURLException e1) {
                // TODO Auto-generated catch block
                e1.printStackTrace();
            } catch (IOException e1) {
                // TODO Auto-generated catch block
                e1.printStackTrace();
            }

        }
    }

    @Override
    public Long queryEliStock() {
        IEliWebService wb = new IEliWebService();
        // 对象工厂
        ObjectFactory of = new ObjectFactory();
        ElectroniceStock es = wb.getIEliWebServiceHttpPort().queryEliStock("440002999999441");
        System.out.println("返回请求库存信息：剩余份数：" + es.getSYFPFS().getValue()
                + "返回信息：" + es.getReturnMsg().getValue());
        return Long.valueOf(es.getSYFPFS().getValue());
    }

    @Transactional
    public void writeBackInvoice(DeliveryOrderHeader doHeader) {
        if (doHeader.getInvoiceFlag() == 1 && SystemConfig.configIsOpen(ConfigKeys.INVOICE_USE_ELECTRONIC_INVOICE, ParamUtil.getCurrentWarehouseId())) {
            List<InvoiceHeader> invoiceHeaders = doHeader.getInvoiceHeaders();
            for (InvoiceHeader invoiceHeader : invoiceHeaders) {
                if (invoiceHeader.getPositiveInvoiceId() == null && invoiceHeader.getInvoiceType().equals(InvoiceHeader.InvoiceType.ELECTRONIC.getValue())) {
                    this.chInvoice(invoiceHeader,true);
                }
            }
        }
    }
    /**
     * 测试发送订单开票
     */
    @Override
    @Transactional
    public void chInvoice(InvoiceHeader invoiceHeader, boolean isSync) {
        Merchant mc = merchantService.getMerchant(invoiceHeader.getMerchantId());
        if (mc == null) {
            throw new BusinessException("没有找到发票对应的商家信息");
        }
        MerchantInvoiceEx merchantInvoiceEx = merchantInvoiceExService.findByMerchantId(mc.getId());
        if (merchantInvoiceEx == null) {
            throw new BusinessException("商家对应的发票配置信息不存在");
        }

        String orgSequenceNo = invoiceHeader.getReqSequenceNo();
        if (StringUtil.isEmpty(orgSequenceNo)) {
            String invoiceIdStr = invoiceHeader.getId().toString();
            int length = invoiceIdStr.length();
            for (int i = 0; i < 15 - length; i++) {
                invoiceIdStr = "0" + invoiceIdStr;
            }
            invoiceIdStr = "C" + invoiceIdStr + "0001"; // 普通上传
            orgSequenceNo = invoiceIdStr;
        } else {
            String strIndex = orgSequenceNo.substring(orgSequenceNo.length() - 4, orgSequenceNo.length());
            int curIndex = Integer.valueOf(strIndex) + 1;
            String invoiceIdStr = String.valueOf(curIndex);
            int length = String.valueOf(curIndex).length();
            for (int j = 0; j < 4 - length; j++) {
                invoiceIdStr = "0" + invoiceIdStr;
            }
            orgSequenceNo = orgSequenceNo.substring(0, orgSequenceNo.length() - 4) + invoiceIdStr;
        }
        IEliWebService wb = new IEliWebService();
        // 对象工厂
        ObjectFactory of = new ObjectFactory();
        /******** 发票总体类 ********/
        ElectroniceInfo e = new ElectroniceInfo();
        generateCommonInfo(invoiceHeader, mc, merchantInvoiceEx, orgSequenceNo, of, e);
        // 购货方名称
        e.setGHFMC(of.createElectroniceInfoGHFMC(invoiceHeader.getReceiverName()));

        // 开票员
        e.setKPR(of.createElectroniceInfoKPR(merchantInvoiceEx.getDrawer()));
        // 开票类型
        e.setKPLX(of.createElectroniceInfoKPLX(InvoiceHeader.KpType.CH.getValue()));
        // 操作代码
        e.setCZDM(of.createElectroniceInfoCZDM(InvoiceHeader.OperateType.ALL_WRITE_BACK.getValue()));
        // 价税合计金额
        e.setKPHJJE(-invoiceHeader.getInvoiceAmount());
        // 备注
        e.setBZ(of.createElectroniceInfoBZ("对应正数发票代码:" + invoiceHeader.getInvoiceCode() +
                "号码" + invoiceHeader.getInvoiceNumber()));
        e.setYFPDM(of.createElectroniceInfoYFPDM(invoiceHeader.getInvoiceCode()));
        e.setYFPHM(of.createElectroniceInfoYFPHM(invoiceHeader.getInvoiceNumber()));
        ArrayOfElectroniceDetail list = new ArrayOfElectroniceDetail();
        /******** 发票明细类 ********/
        ArrayList<ElectroniceDetail> ds = (ArrayList<ElectroniceDetail>) list
                .getElectroniceDetail();
        for (InvoiceDetail invoiceDetail : invoiceHeader.getInvoiceDetails()) {
            ElectroniceDetail detail = new ElectroniceDetail();
            // 项目名称
            detail.setXMMC(of.createElectroniceDetailXMMC(invoiceDetail.getSkuDescr()));
            // 项目单位
            detail.setDW(of.createElectroniceDetailDW(invoiceDetail.getUomDescr()));
            // 规格型号
            detail.setGGXH(of.createElectroniceDetailGGXH(invoiceDetail.getSkuDescr()));
            // 项目编码
//				detail.setXMBM(of.createElectroniceDetailXMBM(invoiceDetail.ge)); TODO 项目编码
            // 项目数量
            detail.setXMSL(new Double(invoiceDetail.getQty().negate().toString()));
            // 项目单价
            detail.setXMDJ(new Double(invoiceDetail.getPrice().toString()));
            // 含税标志
            detail.setHSBZ(of.createElectroniceDetailHSBZ(Constants.YesNo.YES.getValue().toString()));
            // 项目金额
            detail.setXMJE(new Double(invoiceDetail.getAmount().negate().toString()));
            // 税率
            detail.setSL(of.createElectroniceDetailSL(invoiceDetail.getTaxRate().toString()));
            // 项目税额
//				detail.setSE(0.15);  	TODO 不传应该没有问题。
            ds.add(detail);
        }

        e.setDetails(of.createElectroniceInfoDetails(list));
        ReturnElectronice result;
        if (isSync) {
            result = wb.getIEliWebServiceHttpPort().invEli(e);
        } else {
            result = wb.getIEliWebServiceHttpPort().sendToInvEli(e);
        }

        if (result == null) {
            Integer failedNum = invoiceHeader.getFailedNum() == null ? Integer.valueOf(0) : invoiceHeader.getFailedNum();
            invoiceHeader.setReqErrorCode("请求接口异常，请联系管理员！");
            invoiceHeader.setInvoiceStatus(InvoiceHeader.InvoiceStatus.NEEDCANCEL);
            invoiceHeader.setFailedNum(failedNum + 1);
        } else {
            if ("0".equals(result.getReturnCode())) {
                // 生成新的负票信息
                InvoiceHeader newInvoiceHeader = new InvoiceHeader();
                BeanUtils.copyProperties(invoiceHeader, newInvoiceHeader);
                newInvoiceHeader.setInvoiceAmount(-newInvoiceHeader.getInvoiceAmount());
                newInvoiceHeader.setId(null);
                newInvoiceHeader.setInvoiceStatus(InvoiceHeader.InvoiceStatus.UPLOAD);
                newInvoiceHeader
                        .seteInvoiceIssueType(Constants.EInvoiceIssueType.WHOLE_CH
                                .getValue());
                newInvoiceHeader.setPositiveInvoiceId(invoiceHeader
                        .getId());
                newInvoiceHeader.setReqSequenceNo(orgSequenceNo);
                newInvoiceHeader.setInvoiceUrl(null);
                newInvoiceHeader.setInvoiceCode(null);
                newInvoiceHeader.setCheckCode(null);
                newInvoiceHeader.setInvoiceNumber(null);
                newInvoiceHeader.setInvoiceDetails(null);
                invoiceService.saveOrUpdate(newInvoiceHeader);
                List<InvoiceDetail> invoiceDetails = invoiceService
                        .findInvoiceDetailsByHeaderId(invoiceHeader.getId());
                for (InvoiceDetail invoiceDetail : invoiceDetails) {
                    InvoiceDetail chedInvoiceDetail = new InvoiceDetail();
                    BeanUtils.copyProperties(invoiceDetail,chedInvoiceDetail);
                    chedInvoiceDetail.setId(null);
                    chedInvoiceDetail.setInvoiceHeader(newInvoiceHeader);
                    chedInvoiceDetail.setUpdatedAt(null);
                    chedInvoiceDetail.setQty(chedInvoiceDetail.getQty().negate());
                    chedInvoiceDetail.setAmount(chedInvoiceDetail.getAmount().negate());
                    invoiceService.saveOrUpdate(chedInvoiceDetail);
                }
                invoiceHeader.setInvoiceStatus(InvoiceHeader.InvoiceStatus.WRITE_BACK);
                invoiceHeader.setReqErrorCode("");
                invoiceHeader.setFailedNum(0);

                System.out
                        .println("测试请求开票(异步)返回信息：" + result.getReturnMsg().getValue());
                if (isSync) {
                    //同步调用后直接把结果返回更新
                    updateInvoiceByResult(newInvoiceHeader, result);
                }
                invoiceService.saveOrUpdate(newInvoiceHeader);
            } else {
                Integer failedNum = invoiceHeader.getFailedNum() == null ? Integer.valueOf(0) : invoiceHeader.getFailedNum();
                invoiceHeader.setInvoiceStatus(InvoiceHeader.InvoiceStatus.NEEDCANCEL);
                invoiceHeader.setReqErrorCode(result.getReturnMsg().getValue());
                invoiceHeader.setFailedNum(failedNum + 1);
            }
            invoiceHeader.setChedSequenceNo(orgSequenceNo);
        }
        invoiceDao.saveOrUpdate(invoiceHeader);
    }

    private void generateCommonInfo(InvoiceHeader invoiceHeader, Merchant mc, MerchantInvoiceEx merchantInvoiceEx, String orgSequenceNo, ObjectFactory of, ElectroniceInfo e) {
        e.setUserName(of.createElectroniceInfoUserName(merchantInvoiceEx.getKeyStoreAbner()));
        e.setPassWord(of.createElectroniceInfoPassWord(merchantInvoiceEx.getKeyStorePassWord()));
        // 发票请求唯一流水号
        e.setFPQQLSH(of.createElectroniceInfoFPQQLSH(orgSequenceNo));
        // 业务订单号
        e.setDDH(of.createElectroniceInfoDDH(invoiceHeader.getSoCode()));
        // 开票方纳税人识别号
        e.setKPNSRSBH(of.createElectroniceInfoKPNSRSBH(merchantInvoiceEx.getTaxPayerCode()));
        // 开票方名称
        e.setKPNSRMC(of.createElectroniceInfoKPNSRMC(merchantInvoiceEx.getTaxpayerName()));
        // 代开标志
        e.setDKBZ(of.createElectroniceInfoDKBZ(Constants.YesNo.NO.getValue().toString()));
        // 销货方识别号
        e.setXHFNSRSBH(of.createElectroniceInfoXHFNSRSBH(merchantInvoiceEx.getTaxpayerName()));
        // 销货方名称
        e.setXHFMC(of.createElectroniceInfoXHFMC(merchantInvoiceEx.getTaxpayerName()));
        // 销货方地址
        e.setXHFDZ(of.createElectroniceInfoXHFDZ(mc.getAddress()));
        // 销货方电话
        e.setXHFDH(of.createElectroniceInfoXHFDH(mc.getContactTel()));
        // 销货方银行账号
        e.setXHFYHZH(of.createElectroniceInfoXHFYHZH("中国工商银行广州银山支行  3602051719200476881"));
    }


}
