package com.daxia.wms.delivery.task.replenish.service.impl;


import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.log.LogUtil;
import com.daxia.framework.common.service.Dictionary;
import com.daxia.framework.common.service.ReportGenerator;
import com.daxia.framework.common.util.*;
import com.daxia.framework.common.util.ListUtil.ListMegareOpr;
import com.daxia.framework.system.service.CfgWarehouseService;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.*;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoDetailDAO;
import com.daxia.wms.delivery.deliveryorder.dao.DoHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.task.replenish.dao.ReplTaskDAO;
import com.daxia.wms.delivery.task.replenish.dao.ReplenishHeaderDAO;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishHeader;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishTask;
import com.daxia.wms.delivery.task.replenish.service.ReplMoveTaskService;
import com.daxia.wms.delivery.task.replenish.service.ReplTaskService;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.entity.PackageInfoDetail;
import com.daxia.wms.master.service.LocationService;
import com.daxia.wms.master.service.PackageInfoDetailService;
import com.daxia.wms.master.service.SkuPickLocAssnService;
import com.daxia.wms.master.service.TempLaborTaskService;
import com.daxia.wms.stock.stock.dao.StockBatchAttDAO;
import com.daxia.wms.stock.stock.dao.StockQueryDao;
import com.daxia.wms.stock.stock.dto.StockDTO;
import com.daxia.wms.stock.stock.entity.StockBatchAtt;
import com.daxia.wms.stock.stock.entity.StockBatchLocLpn;
import com.daxia.wms.stock.stock.entity.StockPending;
import com.daxia.wms.stock.stock.entity.TrsTransactionLog;
import com.daxia.wms.stock.stock.service.IOperator;
import com.daxia.wms.stock.stock.service.StockQueryService;
import com.daxia.wms.stock.stock.service.StockService;
import com.daxia.wms.stock.stock.service.TransactionService;
import com.daxia.wms.stock.task.dao.TrsTaskDAO;
import com.daxia.wms.stock.task.entity.TrsTask;
import com.daxia.wms.stock.task.filter.TrsTaskFilter;
import com.daxia.wms.util.Switch;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.math.BigDecimal;
import java.util.*;

import static com.daxia.wms.Constants.LocType.EACB;
import static com.daxia.wms.delivery.DeliveryException.REPL_LOCATION_CANNOT_BE_EACB;

@Name("com.daxia.wms.delivery.replTaskService")
@lombok.extern.slf4j.Slf4j
public class ReplTaskServiceImpl implements ReplTaskService {
	private static final int SCALE = 5;
	
	@In
	private DoHeaderDAO doHeaderDAO;
	@In
	private DoDetailDAO doDetailDAO;
	@In
	private TrsTaskDAO trsTaskDAO;
	@In
	private ReplTaskDAO replTaskDAO;
	@In
	private SequenceGeneratorService sequenceGeneratorService;
	@In
	private StockService stockService;
	@In
	private TransactionService transactionService;
	@In
	private ReplenishHeaderDAO replenishHeaderDAO;
	@In
	private ReportGenerator reportGenerator;
	@In
	private LocationService locationService;
	@In
	private StockBatchAttDAO stockBatchAttDAO;
	@In
	private ReplMoveTaskService replMoveTaskService;
	@In
	private DeliveryOrderService deliveryOrderService;
    @In("replenishOperator")
    private IOperator replenishOperator;
    
    @In("offShelfOperator")
    private IOperator offShelfOperator;

    @In("replConfirmOperator")
    private IOperator replConfirmOperator;
    
    @In
    private CfgWarehouseService cfgWarehouseService;
    
    @In
    private SkuPickLocAssnService skuPickLocAssnService;
    
    @In
    private TempLaborTaskService tempLaborTaskService;

	@In
	private PackageInfoDetailService packageInfoDetailService;

	@In
	private StockQueryService stockQueryService;

	@In
	protected StockQueryDao stockQueryDao;
    

    
    /**
     * 打印文件名
     *
     */
	public enum ReportName {
		REP_TASK("repTask", null);

		private String name;
		private String subName;

		ReportName(String name, String subName) {
			this.name = name;
			this.subName = subName;
		}

		public String getName() {
			return name;
		}

		public String getSubName() {
			return subName;
		}
	}

	/**
	 *  查询补货任务
	 */
	@Override
    public DataPage<TrsTask> queryReplTrs(TrsTaskFilter trsTaskFilter,
                                          int startIndex, int pageSize) {
		trsTaskFilter.setTaskType(Constants.TaskType.RP.getValue());
		return trsTaskDAO
				.findRangeByFilter(trsTaskFilter, startIndex, pageSize);
	}

	/**
	 * 查询补货任务
	 */
	@Override
    public List<ReplenishTask> queryReplTrs(TrsTaskFilter trsTaskFilter) {
		trsTaskFilter.setTaskType(Constants.TaskType.RP.getValue());
		List<ReplenishTask> list = replTaskDAO.findByFilter(trsTaskFilter);
		for (ReplenishTask replenishTask : list) {
			replenishTask.setRealQty(replenishTask.getQtyUnit());
		}
		return list;
	}

	/**
	 * 根据id查询补货任务
	 */
	@Override
	public List<ReplenishTask> getTasksByIds(List<Long> ids) {
		return replTaskDAO.getByKeys(ids);
	}

	/**
	 *  执行补货任务
	 * @throws Exception
	 */
	@Override
    @Loggable
	@Transactional
	public Long executeRepl(Long id, BigDecimal realQtyUnit, String realLocStr, String reasonCode, String reasonDescr, boolean ignorePending) throws Exception {
		log.debug("Execute Repl :[realQty: {}, realQty: {}, reasonCode: {}, reasonDescr: {}, realLocStr: {}]", realQtyUnit, realQtyUnit, reasonCode, reasonDescr, realLocStr);

		Long nextId = null;
		Location realLoc = null;
		ReplenishTask replTask = replTaskDAO.get(id);
		if (replTask == null) {
			throw new DeliveryException(DeliveryException.REPL_TASK_NOT_EXIST);
		}

		if (ReplTaskStatus.CLOSED.getValue().equals(replTask.getTaskStatus()) || ReplTaskStatus.CANCELLED.getValue().equals(replTask.getTaskStatus())) {
			// 补货明细状态不正确，不允许保存。
			throw new DeliveryException(DeliveryException.PEP_TASK_STATUS_ILLEGAL);
		}
	    
		if (ReplTaskStatus.INPROGRESS.getValue().equals(replTask.getTaskStatus())) {
			// 验证库位
	        realLoc = checkLocation(realLocStr, replTask.getSkuId(), replTask.getLotId(), ignorePending);
			if(!realLoc.getPackageType().equals(replTask.getPlanLocation().getPackageType())){
				throw new DeliveryException(DeliveryException.LOCATION_PACKAGE_TYPE_ERROR);
			}
			boolean changeLoc = !replTask.getPlanLocId().equals(realLoc.getId());
			StockPending stockPending = stockQueryDao.getStockPendingById(replTask.getToStockId());
			replMoveTaskService.allocateReplMoveTask(replTask, realLoc.getId(), reasonCode, reasonDescr, changeLoc, realQtyUnit, ignorePending, stockPending.getLotId());
			BigDecimal replMoveTaskQtyUnit = replMoveTaskService.getReplMoveTaskQtyUnitByReplTask(replTask.getId(), TaskStatus.RELEASED);
			if (BigDecimal.ZERO.compareTo(replMoveTaskQtyUnit) < 0) {
				replTask.setTaskStatus(ReplTaskStatus.INPROGRESS.getValue().toString());
				nextId = replTask.getId();
			} else {
				replTask.setTaskStatus(ReplTaskStatus.CLOSED.getValue().toString());
			}
			replTaskDAO.save(replTask);
		} else {
			//补货校验并获取实际补货库位
	        realLoc = validateRepl(replTask, realQtyUnit, realLocStr, ignorePending);
	        
			Boolean hasOffShelfed = ReplTaskStatus.OFFSHELF.getValue().equals(replTask.getTaskStatus());
			
			BigDecimal qtyUnit = replTask.getQtyUnit();
			if (hasOffShelfed) {
				qtyUnit = replTask.getOffShelfQtyUnit();
			}
		    
			Long realLocId = realLoc.getId();
	        Long planLocId = replTask.getPlanLocId();
	        
			//库存变更
	        StockDTO stockDto = new StockDTO();
	        stockDto.setFmLocId(replTask.getFmLocId());
	        stockDto.setFmStockId(replTask.getFmStockId());
	        stockDto.setToStockId(replTask.getToStockId());
	        //改变了目标库存的位置
	        if (realLocId.compareTo(planLocId) != 0) {
	            stockDto.setToLocId(realLocId);
	        }
	        BigDecimal actualQtyUnit = realQtyUnit.compareTo(qtyUnit) > 0 ? qtyUnit : realQtyUnit;
			BigDecimal packingQty = replTask.getPackQty();
	        stockDto.setPlanQty(qtyUnit.multiply(packingQty));
			stockDto.setPlanQtyUnit(qtyUnit);
			stockDto.setActualQty(actualQtyUnit.multiply(packingQty));
			stockDto.setActualQtyUnit(actualQtyUnit);
	        stockDto.setSkuId(replTask.getSkuId());
	        stockDto.setLotId(replTask.getLotId());
	        stockDto.setLpnNo(replTask.getLpnNo());
	        stockDto.setGenerateFlag(Boolean.FALSE);
			stockDto.setPackQty(packingQty);
	        replenishOperator.setStockDto(stockDto);
	        Map<String, Long> map = stockService.operateStock(replenishOperator);

	        //任务变更
			ReplenishTask task = changeTask(realQtyUnit, qtyUnit, replTask, realLoc, hasOffShelfed, reasonCode, reasonDescr, map, ignorePending);
			if (!StringUtil.isEmpty(task.getUserdefine01())) {
	        	nextId = Long.valueOf(task.getUserdefine01());
	        }
			StockBatchLocLpn stockBatchLocLpn = stockQueryService.findStockBySkuAndLocAndLot(
					replTask.getSkuId(), replTask.getLotId(), replTask.getFmLocId(), replTask.getLpnNo());
			StockBatchLocLpn toStockBatchLocLpn = stockQueryService.findStockBatchLocLpnById(map.get(StockType.STOCK.getValue()));
			// 生成交易日志
			createTaskTranLog(task, realLoc, hasOffShelfed,stockBatchLocLpn.getStockBatchAtt(),toStockBatchLocLpn.getStockBatchAtt());
			StockBatchAtt attr = stockBatchAttDAO.get(replTask.getLotId());
			// 更新doHeader和doDetail
			updateDoDetailStatus(replTask.getSkuId(), replTask.getLotId());
			doDetailDAO.clearNoStockFlag(replTask.getSkuId(), attr.getLotatt04(), attr.getLotatt06(), attr.getLotatt08()); //更新分配明细缺货标记
			List<Long> skuIds = new ArrayList<Long>();
			skuIds.add(replTask.getSkuId());
			refreshDoHeaderReplStatus(skuIds);
			doHeaderDAO.checkNoStockFlag(skuIds);//更新分配头缺货标记
		}
		// 更新补货单头状态
		refreshReplHeaderStatus(replTask.getDocOperId());
		
		if (SystemConfig.configIsOpen("delivery.repl.updatePickLoc", ParamUtil.getCurrentWarehouseId())) {
			skuPickLocAssnService.updateSkuPickLocAssn(replTask.getSkuId(), realLoc.getId());
		}
		
		return nextId;
	}
	
	/**
	 * 补货任务变更
	 * @param realQtyUnit
	 * @param qtyUnit
	 * @param primaryTask
	 * @param realLoc
	 * @param hasOffShelfed
	 * @param reasonCode
	 * @param reasonDescr
	 * @param map
	 * @return
	 * @throws Exception
	 */
	@Transactional
    private ReplenishTask changeTask(BigDecimal realQtyUnit, BigDecimal qtyUnit, ReplenishTask primaryTask, Location realLoc, Boolean hasOffShelfed, String reasonCode, String reasonDescr, Map<String, Long> map, boolean
			ignorePending) throws Exception {
		log.debug("Change Repl Task:[realQty: {}, qty: #1, reasonCode: #2, reasonDescr: #3, realLocStr: #4, taskId: #5]", realQtyUnit, qtyUnit, reasonCode, reasonDescr, realLoc, primaryTask.getId());
		

		ReplenishTask newTask = null;
        //实际补货库位（即用户填写的库位）
        Long realLocId = realLoc.getId();

        if (realQtyUnit.compareTo(qtyUnit) == -1) {
        	//用户部分补货
            newTask = createNewReplTask(primaryTask);
            //修改原补货任务。
			primaryTask.setQtyUnit(qtyUnit.subtract(realQtyUnit));
			primaryTask.setQty(primaryTask.getQtyUnit().multiply(primaryTask.getPackQty()));

            //设置补货任务类型
            newTask.setReplType(primaryTask.getReplType());
            //设置预计出库时间
            newTask.setPlanShipTime(primaryTask.getPlanShipTime());
            newTask.setMerchantId(primaryTask.getMerchantId());
            newTask.setToLocId(realLocId);
            newTask.setTaskNo(sequenceGeneratorService.generateSequenceNo(Constants.SequenceName.RP_TASKNO.getValue(), ParamUtil.getCurrentWarehouseId()));
            newTask.setQty(realQtyUnit.multiply(primaryTask.getPackQty()));
			newTask.setQtyUnit(realQtyUnit);
            newTask.setUserdefine01(primaryTask.getId().toString());
            if (hasOffShelfed) {
            	newTask.setOffShelfQty(newTask.getQty());
				newTask.setOffShelfQtyUnit(newTask.getQtyUnit());
            	primaryTask.setOffShelfQty(primaryTask.getQty());
				primaryTask.setOffShelfQtyUnit(primaryTask.getQtyUnit());
            }
            newTask.setFmStockId(primaryTask.getFmStockId());
            newTask.setToStockId(primaryTask.getToStockId());
            
            newTask.setReasonCode(reasonCode);
            newTask.setReasonDescr(reasonDescr);
            
            primaryTask.setToStockId(map.get(StockType.STOCK_PENDING.getValue()));
            primaryTask.setFmStockId(map.get(StockType.STOCK_ALLOC.getValue()));
            primaryTask.setBeSplitted(YesNo.YES.getValue());
            replTaskDAO.save(newTask);
        } else {
        	//用户补货完成
        	primaryTask.setToLocId(realLocId);
            primaryTask.setReasonCode(reasonCode);
            primaryTask.setReasonDescr(reasonDescr);
        	newTask = primaryTask;
	
			BigDecimal overQtyUnit = realQtyUnit.subtract(qtyUnit);
			StockBatchLocLpn toStockBatchLocLpn = stockQueryService.findStockBatchLocLpnById(map.get(StockType.STOCK.getValue()));
			// 超补
			if (BigDecimal.ZERO.compareTo(overQtyUnit) < 0) {
				if (!hasOffShelfed) {
					replMoveTaskService.allocate4move(primaryTask, overQtyUnit, toStockBatchLocLpn.getLotId());
				}
				boolean changeLoc = !primaryTask.getPlanLocId().equals(realLocId);
				replMoveTaskService.allocateReplMoveTask(primaryTask, realLocId, reasonCode, reasonDescr, changeLoc, overQtyUnit, ignorePending, toStockBatchLocLpn.getLotId());
			}
	
			this.trsTaskDAO.getSession().flush();
	
			BigDecimal replMoveTaskQtyUnit = replMoveTaskService.getReplMoveTaskQtyUnitByReplTask(primaryTask.getId(), TaskStatus.RELEASED);
			if (BigDecimal.ZERO.compareTo(replMoveTaskQtyUnit) < 0) {
				primaryTask.setTaskStatus(ReplTaskStatus.INPROGRESS.getValue().toString());
				newTask.setUserdefine01(primaryTask.getId().toString());
			} else {
				primaryTask.setTaskStatus(ReplTaskStatus.CLOSED.getValue().toString());
			}
		}

        replTaskDAO.merge(primaryTask);
        replTaskDAO.getSession().flush();
        replTaskDAO.getSession().clear();
        return newTask;
    }
	 
	/**
	 * 生成新补货任务
	 * @param oldReplTask
	 * @return
	 */
	 private ReplenishTask createNewReplTask(ReplenishTask oldReplTask){
		ReplenishTask temp = new ReplenishTask();
		temp.setSkuId(oldReplTask.getSkuId());
		temp.setLotId(oldReplTask.getLotId());
		temp.setFmLocId(oldReplTask.getFmLocId());
		temp.setPlanLocId(oldReplTask.getPlanLocId());
		temp.setDocOperId(oldReplTask.getDocOperId());
		temp.setDocOperNo(oldReplTask.getDocOperNo());
		 temp.setIsUnit(oldReplTask.getIsUnit());
		 temp.setPackQty(oldReplTask.getPackQty());
		temp.setLpnNo(oldReplTask.getLpnNo());
		temp.setToLpnNo(oldReplTask.getToLpnNo());
		temp.setTaskType(Constants.TaskType.RP.getValue());
		temp.setTaskStatus(ReplTaskStatus.CLOSED.getValue().toString());
		temp.setBySo(oldReplTask.getBySo());
//		temp.setPackId(Long.valueOf(-1));
		temp.setPackageDetailId(oldReplTask.getPackageDetailId());
		return temp;
	 }

	 /**
	  * 验证补货任务
	  * @param trsTask
	  * @param realQtyUnit
	  * @param realLocStr
	  * @return
	  */
    private Location validateRepl(TrsTask trsTask, BigDecimal realQtyUnit, String realLocStr, boolean ignorePending) {
		BigDecimal qtyUnit = trsTask.getQtyUnit();

        if (realQtyUnit == null) {
        	throw new DeliveryException(DeliveryException.REPL_QTY_IS_NULL);
        }
        
        // 验证库位
        Location realLoc = checkLocation(realLocStr, trsTask.getSkuId(), trsTask.getLotId(), ignorePending);

		if(!realLoc.getPackageType().equals(trsTask.getPlanLocation().getPackageType())){
			throw new DeliveryException(DeliveryException.LOCATION_PACKAGE_TYPE_ERROR);
		}

        if (!(realLoc.getId().equals(trsTask.getPlanLocId())) || realQtyUnit.compareTo(qtyUnit) != 0) {
        	// 改变了目标库位或者计划补货数量，必须输入原因
            if ("".equals(trsTask.getReasonCode()) || "".equals(trsTask.getReasonDescr())) {
                throw new DeliveryException(DeliveryException.REASON_IS_NULL);
            }
        }
        return realLoc;
    }
    
	/**
	 * 商品进行补货后，不管补了多少，将等待该批次商品的doDetail的补货数量设置为0，并检查DoHeader的状态，
	 * 如果doHeader下的所有doDeteail都不需要再补货，则允许doHeader再次被自动分配。
	 * @param skuId
	 */
	private void updateDoDetailStatus(Long skuId, Long lotId){
		StockBatchAtt attr = stockBatchAttDAO.get(lotId);
		deliveryOrderService.clearDoDetailReplNum(skuId, attr.getLotatt04(), attr.getLotatt06(), attr.getLotatt08());
	}
	
	/**
	 * 检查doHeader下面所有的doDetail是否已补货完毕，如果补货完毕，则修改doHeader的状态，允许再次被自动分配
	 */
	private void refreshDoHeaderReplStatus(List<Long> skuIds) {
		deliveryOrderService.checkDoHeaderReplStatus(skuIds);
	}
	
	/**
	 * 根据补货单id更新补货单状态
	 * @param replHeaderId
	 * @return
	 */
	private void refreshReplHeaderStatus(Long replHeaderId) {
        log.debug("RefreshReplHeaderStatus, replHeaderId: {}", replHeaderId);
	    
		ReplenishHeader replHeader = this.replenishHeaderDAO.get(replHeaderId);

		TrsTaskFilter filter = new TrsTaskFilter();
		filter.setDocOperId(replHeaderId);
		filter.setTaskType(TaskType.RP.getValue());
		List<TrsTask> replTasks = this.trsTaskDAO.findByFilter(filter);

		int totalTasks = replTasks.size();
		if (totalTasks == 0) {
			return;  //直接跳出
		}
		int releaseCount = 0;
		int closedCount = 0;
		int cancelCount = 0;
		int inProgressCount = 0;

		for (TrsTask trsTask : replTasks) {
			if (ReplTaskStatus.RELEASED.getValue().equals(trsTask.getTaskStatus())) {
				releaseCount++;
			} else if (ReplTaskStatus.CLOSED.getValue().equals(trsTask.getTaskStatus())) {
				closedCount++;
			} else if (ReplTaskStatus.CANCELLED.getValue().equals(trsTask.getTaskStatus())) {
				cancelCount++;
			} else if (ReplTaskStatus.INPROGRESS.getValue().equals(trsTask.getTaskStatus())) {
				inProgressCount++;
			}
		}
		
		if (releaseCount == 0 && inProgressCount == 0) {
			delLbfTaskOfDoc(replHeader.getReplNo());
		}

		/**
		 * HEADER状态	能存在的DETAIL状态合集
			已发布		已发布
			补货中		已发布+已下架
						已发布+已下架+已取消
						已发布+已上架
						已发布+已取消
						已发布+已上架+已取消
						已下架
						已下架+已上架
						已下架+已取消
						已下架+已上架+已取消
			已完成		已上架+已取消
						已上架
			已取消		已取消
		 */
		if (totalTasks == releaseCount) {
			replHeader.setStatus(ReplStatus.RELEASED.getValue());
		} else if (totalTasks == cancelCount) {
			replHeader.setStatus(ReplStatus.CANCELLED.getValue());
		} else if (totalTasks == closedCount || totalTasks == (closedCount + cancelCount)) {
			replHeader.setStatus(ReplStatus.CLOSED.getValue());
		} else {
			replHeader.setStatus(ReplStatus.INPROGRESS.getValue());
		}

		this.replenishHeaderDAO.update(replHeader);
	}

	/**
	 * 记录补货交易记录
	 * @param task
	 * @param realLoc
	 * @param hasOffShelfed
	 */
	@Loggable
	public void createTaskTranLog(TrsTask task, Location realLoc,Boolean hasOffShelfed,StockBatchAtt fromStockBatchAtt,StockBatchAtt toStockBatchAtt) {
		TrsTransactionLog trsTraLog = new TrsTransactionLog();
		trsTraLog.setTaskId(task.getId());
		trsTraLog.setDocId(task.getDocOperId());
		trsTraLog.setDocNo(task.getDocOperNo());
		trsTraLog.setDocLineId(task.getDocLineId());
		trsTraLog.setDocType(Constants.DocType.MOVE.getValue().toString());
		trsTraLog.setTransactionType(task.getTaskType());
		trsTraLog.setPackId(task.getPackId());
		PackageInfoDetail fromDetail = packageInfoDetailService.get(Long.valueOf(fromStockBatchAtt.getLotatt07()));
		BigDecimal fromPackQty = fromDetail.getQty();
		PackageInfoDetail toDetail = packageInfoDetailService.get(Long.valueOf(toStockBatchAtt.getLotatt07()));
		BigDecimal toPackQty = toDetail.getQty();

		trsTraLog.setFmCargoOwnerId(task.getMerchantId());
		trsTraLog.setFmLocId(task.getFmLocId());
		trsTraLog.setFmLotId(fromStockBatchAtt.getId());
		trsTraLog.setFmQty(task.getQty());
		trsTraLog.setFmQtyUnit(task.getQtyUnit());
		trsTraLog.setFmUom(fromDetail.getUomDescr());
		trsTraLog.setFmGrossWeight(task.getGrossWeight());
		trsTraLog.setFmNetWeight(task.getNetWeight());
		trsTraLog.setFmVolume(task.getVolume());
		trsTraLog.setFmPackDetailId(fromDetail.getId());
		trsTraLog.setFmSkuId(task.getSkuId());
		trsTraLog.setFmUomQty(fromPackQty);


		trsTraLog.setToCargoOwnerId(task.getMerchantId());
		trsTraLog.setToLocId(realLoc.getId());
		trsTraLog.setToLotId(toStockBatchAtt.getId());
		trsTraLog.setToQty(task.getQty());
		trsTraLog.setToQtyUnit(task.getQty().divideAndRemainder(toPackQty)[0]);
		trsTraLog.setToUom(toDetail.getUomDescr());
		trsTraLog.setToGrossWeight(task.getGrossWeight());
		trsTraLog.setToNetWeight(task.getNetWeight());
		trsTraLog.setToVolume(task.getVolume());
		trsTraLog.setToPackDetailId(toDetail.getId());
		trsTraLog.setToUomQty(toDetail.getQty());
		trsTraLog.setToSkuId(task.getSkuId());

		trsTraLog.setIsDamage(Constants.YesNo.NO.getValue().longValue());
		trsTraLog.setReasonCode(task.getReasonCode());
		trsTraLog.setReasonDescr(task.getReasonDescr());
		trsTraLog.setCreatedBy(ParamUtil.getCurrentLoginName());
		trsTraLog.setUpdatedBy(ParamUtil.getCurrentLoginName());
		trsTraLog.setOperationId(ParamUtil.getCurrentLoginName());
		trsTraLog.setOperSource(ParamUtil.getCurrentDevice()); // 设置操作来源(WMS/RF)
		if (hasOffShelfed) {
			trsTraLog.setFmLpnNo(task.getToLpnNo());
			trsTraLog.setToLpnNo("*");
		} else {
			trsTraLog.setFmLpnNo(task.getLpnNo());
			trsTraLog.setToLpnNo(task.getToLpnNo());
		}
		//商家id库存回写用
		trsTraLog.setMerchantId(StringUtils.isEmpty(fromStockBatchAtt.getLotatt06())?null:Long.parseLong(fromStockBatchAtt.getLotatt06()));

		transactionService.saveTrsTransactionLog(trsTraLog);
	}

	/**
	 * 验证库位
	 */
	@Override
    public Location checkLocation(String realLoc, Long skuId, Long lotId, boolean ignorePending) {
		Location tempLoc = locationService.checkLocType(realLoc, this.locationService.getReplLocTypes());
		// 验证产品
//		if (tempLoc.getCanMixProduct().intValue() == 0) {
//            if (stockService.isMixSku(skuId, tempLoc.getId())) {
//            	throw new DeliveryException(DeliveryException.LOC_CAN_NOT_MIX_SKU);
//            }
//		}
		stockService.checkPutAwayMixSku(skuId, tempLoc, ignorePending);
		// 验证批次
        if (tempLoc.getCanMixBatch().intValue() == 0) {
            if (stockService.isMixLot(skuId, lotId, tempLoc.getId(), null)) {
                throw new DeliveryException(DeliveryException.LOC_CAN_NOT_MIX_BATCH);
            }
        }

        //不能补货到锁定库位
		//tempLoc从缓存中查得，SQL从数据库中判断库位是否锁定，
        if (locationService.isLocked(tempLoc.getId())) {
            throw new DeliveryException(DeliveryException.REPL_LOCATION_MUST_BE_UNLOCKED, tempLoc.getLocCode());
        }
		if(EACB.getValue().equals(tempLoc.getLocType())){
			throw new DeliveryException(REPL_LOCATION_CANNOT_BE_EACB,tempLoc.getLocCode());
		}
		return tempLoc;
	}

	/**
	 *  取消补货任务
	 */
	@Loggable
	@Override
	@Transactional
	public Date cancelRepl(Long id) {
	    log.debug("Cancel Repl :[id: {}]", id);
	    
		// 取得补货任务list
		ReplenishTask replTask = replTaskDAO.get(id);
		if (!TaskStatus.RELEASED.getValue().equals(replTask.getTaskStatus())) {
			throw new DeliveryException(DeliveryException.PEP_TASK_STATUS_ILLEGAL_FOR_CANCEL); //存在正在执行的任务或者已经完成补货的任务；不允许取消。
		}
		
		Date planShipTime = replTask.getPlanShipTime();
		// 修改任务状态为取消状态
		replTask.setTaskStatus(TaskStatus.CANCELED.getValue());

		replTaskDAO.update(replTask);

		//库存变更
        StockDTO stockDto = new StockDTO();
        stockDto.setFmLocId(replTask.getFmLocId());
        stockDto.setFmStockId(replTask.getFmStockId());
        stockDto.setToStockId(replTask.getToStockId());
        //更新库存
        stockDto.setPlanQty(replTask.getQty());
		stockDto.setPlanQtyUnit(replTask.getQtyUnit());
        stockDto.setSkuId(replTask.getSkuId());
        stockDto.setLotId(replTask.getLotId());
        stockDto.setLpnNo(replTask.getLpnNo());
        replenishOperator.setStockDto(stockDto);
        stockService.undo(replenishOperator);
        
        if (canOverReplenish()) {
        	replMoveTaskService.cancelReplMoveTask(replTask.getId(), false);
        }

		List<Long> skuIds = new ArrayList<Long>();
		skuIds.add(replTask.getSkuId());
        //更新订单明细
		updateDoDetailStatus(replTask.getSkuId(), replTask.getLotId());

		//更新相应订单
		refreshDoHeaderReplStatus(skuIds);

		//更新补货单头
		refreshReplHeaderStatus(replTask.getDocOperId());

		return planShipTime;
	}

	/**
	 * 导出补货任务的PDF数据
	 */
	@Override
	public byte[] exportPDF(Long replHeaderId) {
	    ReplenishHeader replHeader = replenishHeaderDAO.get(replHeaderId);
	    
		List<ReplenishTask> replTasks = getReportData(replHeaderId);
		if (replTasks.isEmpty()) {
			throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
		}

        return reportGenerator.generatePDF(ReportName.REP_TASK.getName(), null, getReportParam(replHeader), replTasks,
            false);
	}

	/**
	 * 打印补货任务的数据
	 */
	@Override
	public List<String> print(Long replHeaderId) {
	    ReplenishHeader replHeader = replenishHeaderDAO.get(replHeaderId);
	    
		List<ReplenishTask> replTasks = getReportData(replHeaderId);
		if (replTasks.isEmpty()) {
			throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
		}
			
		return reportGenerator.builtPrintDataNoSub(
				ReportName.REP_TASK.getName(), getReportParam(replHeader),
				replTasks);
	}

	/**
	 * 获取补货任务的数据
	 * @param replHeaderId
	 * @return
	 */
	private List<ReplenishTask> getReportData(Long replHeaderId) {
		TrsTaskFilter taskFilter = new TrsTaskFilter();
		taskFilter.setDocOperId(replHeaderId);
		taskFilter.setTaskStatus(TaskStatus.RELEASED.getValue());
		taskFilter.setTaskType(TaskType.RP.getValue());

		List<ReplenishTask> tasks = this.queryReplTrs(taskFilter);

		if (tasks == null || tasks.isEmpty()) {
			throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
		}

		ListUtil.megareList(tasks, new ListMegareOpr<ReplenishTask>() {
			@Override
			public boolean isNeedMegare(ReplenishTask t1, ReplenishTask t2) {
				if (t1.getDocOperId().compareTo(t2.getDocOperId()) != 0) {
					throw new DeliveryException(DeliveryException.REP_TASK_NO_NOT_EQUALS);
				}

				return CompareUtil.compare(t1.getSkuId(), t2.getSkuId())
						&& CompareUtil.compare(t1.getFmLocId(), t2.getFmLocId())
						&& CompareUtil.compare(t1.getToLocId(), t2.getToLocId());
			}

			@Override
			public void megareOpr(ReplenishTask t1, ReplenishTask t2) {
//				t1.setQtyEach(t1.getQtyEach().add(t2.getQtyEach()));
				t1.setQty(t1.getQty().add(t2.getQty()));
			}
		});
		return tasks;
	}

	/**
	 * 获取打印参数
	 * @param replHeader
	 * @return
	 */
	private Map<String, Object> getReportParam(ReplenishHeader replHeader) {
	    if (null == replHeader) {
	        throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
	    }
		Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("warehouseName", cfgWarehouseService.getCurrentWarehouseName());
		parameters.put("replHeaderNo", replHeader.getReplNo());
        parameters.put("createTime", replHeader.getCreatedAt());
        parameters.put("skuCounts", replHeader.getSkus());
        parameters.put("unitCounts", replHeader.getUnits());
        //设置即时补货业务类型信息
        if (ReplType.JP.getValue().equals(replHeader.getReplType())) {
            if (StringUtil.isNotEmpty(replHeader.getDoType())) {
                Map<String, String> jpTypes = Dictionary.getDictionary("REPL_DO_TYPE");
                parameters.put("jpType", jpTypes.get(replHeader.getDoType()));
            }
        }
        
		return parameters;
	}

	/**
     * @return 获取指定状态的补货任务的skuId
     */
	@Override
	public List<Long> findReplTaskSkus(TaskStatus status) {
		return replTaskDAO.findReplTaskSkus(status.getValue());
	}

	  /**
     * 获取补货任务，按照上架库位顺序排序
     */
	@Override
	public List<ReplenishTask> findReplTasks(Long skuId, List<String> status) {
		return replTaskDAO.findReplTasks(skuId, status);
	}

	 /**
     * @return 获取指定状态的补货任务
     */
	@Override
	public List<ReplenishTask> findReplTasks(List<String> status) {
		return replTaskDAO.findReplTasks(status);
	}

	/**
	 * 根据toLpnNo、任务状态skuId获取补货任务
	 * @param lpnNum
	 * @param skuId
	 * @return
	 */
	@Override
	public List<ReplenishTask> findReplTasks(String lpnNum, List<String> status, Long skuId) {
		return replTaskDAO.findReplTasks(lpnNum, status, skuId);
	}
	
	 /**
     * 根据taskId和toLpnNo更新补货任务
     * @param taskId
     * @return
     */
	@Override
    @Transactional
	public void updateReplTaskLpn(Long taskId, String lpnNum){
		replTaskDAO.updateReplTaskLpn(taskId, lpnNum);
	}

	/**
	 * 根据中转容器查询补货任务
	 * @param lpnNo
	 * @return
	 */
	@Override
	public List<ReplenishTask> findReplTasksByLpnNo(String lpnNo, List<String> status) {
		return replTaskDAO.findReplTasksByLpnNo(lpnNo, status);
	}

	/**
	 * 根据补货单号，任务状态获取补货任务
	 * @param replNo
	 * @param taskStatus
	 * @return
	 */
	@Override
	public List<ReplenishTask> findReplTasksByReplNoStatus(String replNo,
			List<String> taskStatus) {
		
		return replTaskDAO.findReplTasksByReplNoStatus(replNo, taskStatus);
	}

	/**
	 * 执行下架
	 * @param taskId
	 * @param offsHelfQtyUnit
	 * @param reasonCode
	 * @param reasonDescr
	 * @throws Exception
	 */
	@Loggable
	@Override
	@Transactional
	public Long executeOffShelfReplTask(Long taskId, BigDecimal offsHelfQtyUnit, String lpnNo,
			String reasonCode, String reasonDescr) throws Exception {
	    log.debug("Execute repl off:[taskId: {}, qty: #1, lpnNo: #2, reasonCode: #3, reasonDescr #4]", taskId, offsHelfQtyUnit,
                lpnNo, reasonCode, reasonDescr);
	    
		ReplenishTask replTask = replTaskDAO.get(taskId);
		if (null == replTask) {
			throw new DeliveryException(DeliveryException.REPL_TASK_NOT_EXIST);
		}
		
		if (!replTask.getTaskStatus().equals(Constants.ReplTaskStatus.RELEASED.getValue())) {
			//如果不是发布状态则抛异常
			throw new DeliveryException(DeliveryException.REPL_OFFSHELF_TASK_STATUS_ERROR);
		}
		
		if (StringUtil.isEmpty(lpnNo)) {
			throw new DeliveryException(DeliveryException.REPL_LPNNO_NULL);
		}
		
		if (offsHelfQtyUnit.compareTo(replTask.getQtyUnit()) == -1) {
			//如果下架数量小于计划数量
			BigDecimal subQtyUnit = replTask.getQtyUnit().subtract(offsHelfQtyUnit);
			
			//不相等要校验原因
			if (StringUtil.isEmpty(reasonCode) || StringUtil.isEmpty(reasonDescr)) {
				throw new DeliveryException(DeliveryException.REASON_IS_NULL);
			}
			Map<String,Long> map = changeOffShelfStock(replTask, offsHelfQtyUnit);
			ReplenishTask newTask = saveOffShelfReplTask(replTask, offsHelfQtyUnit, lpnNo, reasonCode, reasonDescr, map);
			BigDecimal packQty = replTask.getPackQty();
			replTask.setQty(subQtyUnit.multiply(packQty));//重新设置计划数量
			replTask.setQtyUnit(subQtyUnit);
			replTask.setBeSplitted(YesNo.YES.getValue());
			replTaskDAO.update(replTask);
			saveOffShelfTransactionLog(newTask);//记录日志
		} else if (offsHelfQtyUnit.compareTo(replTask.getQty()) >= 0) {
			//更新任务信息
			replTask.setToLpnNo(lpnNo);//保存中转容器号
			replTask.setTaskStatus(Constants.ReplTaskStatus.OFFSHELF.getValue());//下架完成
			replTask.setOffShelfTime(DateUtil.getNowTime());
			replTask.setOffShelfBy(ParamUtil.getCurrentLoginName());
			replTask.setReasonCode(reasonCode);
			replTask.setReasonDescr(reasonDescr);
			replTask.setOffShelfQty(replTask.getQty());//设置下架数量
			replTask.setOffShelfQtyUnit(replTask.getQtyUnit());//设置下架数量
			this.replTaskDAO.update(replTask);
			saveOffShelfTransactionLog(replTask);//记录日志
			StockPending stockPending = stockQueryDao.getStockPendingById(replTask.getToStockId());
			BigDecimal overQtyUnit = offsHelfQtyUnit.subtract(replTask.getQtyUnit());
			if (BigDecimal.ZERO.compareTo(overQtyUnit) < 0) {
				// 超补
				replMoveTaskService.allocate4move(replTask, overQtyUnit, stockPending.getLotId());
			}

		}
		//更新补货单头信息
		motifyReplenishHeader(replTask.getDocOperId(),
				Constants.ReplStatus.RELEASED.getValue(), Constants.ReplStatus.INPROGRESS.getValue());
		boolean isDocDone = isReplHeaderOffShelfed(replTask.getDocOperNo());
		if (isDocDone) {
			delLbfTaskOfDoc(replTask.getDocOperNo());
		}
		return replTask.getId();
	}
	
	private boolean isReplHeaderOffShelfed(String docNo) {
		List<ReplenishTask> tasks = replTaskDAO.findReplTasksByReplNoStatus(docNo, null);
		if (ListUtil.isNullOrEmpty(tasks)) {
			return true;
		}
		for (ReplenishTask task : tasks) {
			if (!StringUtil.isInArrays(task.getTaskStatus(), ReplTaskStatus.CANCELLED.getValue(), ReplTaskStatus.OFFSHELF.getValue(),
					ReplTaskStatus.CLOSED.getValue())) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 生成下架任务
	 * @param oldTask
	 * @param offShelfQtyUnit
	 * @param lpnNo
	 * @param resonCode
	 * @param reasonDescr
	 * @param resultMap
	 * @return
	 */
	private ReplenishTask saveOffShelfReplTask(ReplenishTask oldTask, BigDecimal offShelfQtyUnit, String lpnNo,
			String resonCode, String reasonDescr, Map<String, Long> resultMap) {
        log.debug("SaveOffShelfReplTask:[taskId: {}, qty: #1, lpnNo: #2, reasonCode: #3, reasonDescr #4]",
                oldTask.getId(), offShelfQtyUnit, lpnNo, resonCode, reasonDescr);
	    
		BigDecimal uomEach = offShelfQtyUnit.divide(oldTask.getQtyUnit(), SCALE, BigDecimal.ROUND_HALF_UP);
		
		ReplenishTask newTask = new ReplenishTask();
		newTask.setTaskNo(sequenceGeneratorService.generateSequenceNo(Constants.SequenceName.RP_TASKNO.getValue(), ParamUtil.getCurrentWarehouseId()));
		newTask.setTaskStatus(Constants.ReplTaskStatus.OFFSHELF.getValue());//拆分出来的任务为下架完成
		newTask.setTaskType(oldTask.getTaskType());
		//设置补货任务类型
		newTask.setReplType(oldTask.getReplType());
		//设置预计出库时间
		newTask.setPlanShipTime(oldTask.getPlanShipTime());
		newTask.setMerchantId(oldTask.getMerchantId());
		newTask.setSkuId(oldTask.getSkuId());
		newTask.setLotId(oldTask.getLotId());
		newTask.setLpnNo(oldTask.getLpnNo());
		newTask.setDocId(oldTask.getDocId());
		newTask.setDocType(oldTask.getDocType());
		newTask.setFmLocId(oldTask.getFmLocId());
		newTask.setToLocId(oldTask.getToLocId());
		newTask.setPlanLocId(oldTask.getPlanLocId());
		newTask.setToLpnNo(lpnNo);//记录中转容器
		newTask.setPackId(oldTask.getPackId());
		newTask.setUom(oldTask.getUom());
		newTask.setPackageDetailId(oldTask.getPackageDetailId());
		newTask.setPackQty(oldTask.getPackQty());
		newTask.setIsUnit(YesNo.YES.getValue());
		newTask.setQtyUnit(offShelfQtyUnit);//设置数量为下架数量
		newTask.setQty(offShelfQtyUnit.multiply(oldTask.getPackQty()));//设置数量为下架数量
//        newTask.setQtyEach(offShelfQtyUnit.multiply(oldTask.getPackQty()));//设置数量为下架数量
        newTask.setGrossWeight(oldTask.getGrossWeight().multiply(uomEach));
        newTask.setNetWeight(oldTask.getNetWeight().multiply(uomEach));
        newTask.setVolume(oldTask.getVolume().multiply(uomEach));
        newTask.setBySo(oldTask.getBySo());
		newTask.setDocOperId(oldTask.getDocOperId());
		newTask.setDocOperNo(oldTask.getDocOperNo());
        newTask.setToStockId(resultMap.get(StockType.STOCK_PENDING.getValue()));
        newTask.setFmStockId(resultMap.get(StockType.STOCK_ALLOC.getValue()));
        newTask.setCreatedBy(ParamUtil.getCurrentLoginName());//记录创建人
        newTask.setUpdatedBy(ParamUtil.getCurrentLoginName());//记录创建人
        newTask.setUserdefine01(oldTask.getId().toString());//记录原单号ID为拆分标记
		newTask.setOffShelfQty(newTask.getQtyUnit().multiply(oldTask.getPackQty()));
		newTask.setOffShelfQtyUnit(newTask.getQtyUnit());
        newTask.setOffShelfTime(DateUtil.getNowTime());//下架时间
        newTask.setOffShelfBy(ParamUtil.getCurrentLoginName());//下架人
        newTask.setReasonCode(resonCode);//记录原因
        newTask.setReasonDescr(reasonDescr);//记录原因代码
        replTaskDAO.save(newTask);
        return newTask;
	}
	
	/**
	 * 记录下架日志
	 * @param replTask
	 */
	private void saveOffShelfTransactionLog(ReplenishTask replTask) {
		TrsTransactionLog trsTraLog = new TrsTransactionLog();
		trsTraLog.setTaskId(replTask.getId());
		trsTraLog.setDocId(replTask.getDocOperId());
		trsTraLog.setDocNo(replTask.getDocOperNo());
		trsTraLog.setDocLineId(replTask.getDocLineId());
		trsTraLog.setDocType(Constants.DocType.MOVE.getValue());
		trsTraLog.setTransactionType(Constants.TrsType.RO.getValue());//交易类型
		trsTraLog.setPackId(replTask.getPackId());
		
		trsTraLog.setFmCargoOwnerId(replTask.getMerchantId());
		trsTraLog.setFmLocId(replTask.getFmLocId());
		trsTraLog.setFmLotId(replTask.getLotId());
		trsTraLog.setFmLpnNo(replTask.getLpnNo());
		trsTraLog.setFmQty(replTask.getQty());
//		trsTraLog.setFmQtyEach(replTask.getQty());
		trsTraLog.setFmUom(replTask.getUom());
		trsTraLog.setFmGrossWeight(replTask.getGrossWeight());
		trsTraLog.setFmNetWeight(replTask.getNetWeight());
		trsTraLog.setFmVolume(replTask.getVolume());
		trsTraLog.setFmPackDetailId(replTask.getPackageDetailId());
		trsTraLog.setFmSkuId(replTask.getSkuId());

		trsTraLog.setToCargoOwnerId(replTask.getMerchantId());
		trsTraLog.setToLocId(replTask.getFmLocId());//设置为from_loc_id
		trsTraLog.setToLotId(replTask.getLotId());
		trsTraLog.setToLpnNo(replTask.getToLpnNo());//toLpnNo
		trsTraLog.setToQty(replTask.getOffShelfQty());//下架数量
		trsTraLog.setToUom(replTask.getUom());
		trsTraLog.setToPackDetailId(replTask.getPackageDetailId());
		trsTraLog.setToSkuId(replTask.getSkuId());

		trsTraLog.setIsDamage(Constants.YesNo.NO.getValue().longValue());
		trsTraLog.setReasonCode(replTask.getReasonCode());
		trsTraLog.setReasonDescr(replTask.getReasonDescr());
		trsTraLog.setCreatedBy(replTask.getOffShelfBy());
		trsTraLog.setUpdatedBy(replTask.getOffShelfBy());
		trsTraLog.setOperationId(replTask.getOffShelfBy());
		trsTraLog.setOperSource(ParamUtil.getCurrentDevice());   // 设置操作来源 (WMS/RF)
		
		StockBatchAtt stockBatchAtt = stockService.queryStockBatchAttById(replTask.getLotId());
		//商家id库存回写用
		trsTraLog.setMerchantId(StringUtils.isEmpty(stockBatchAtt.getLotatt06())?null:Long.parseLong(stockBatchAtt.getLotatt06()));

		transactionService.saveTrsTransactionLog(trsTraLog);
	}
	
	/**
	 * 如果是fromStatus则更新为toStatus
	 * @param replHeaderId
	 * @param fromStatus
	 * @param toStatus
	 */
	private void motifyReplenishHeader(Long replHeaderId, String fromStatus, String toStatus) {
		ReplenishHeader replHeader = this.replenishHeaderDAO.get(replHeaderId);
		if (null == replHeader) {
			throw new DeliveryException(DeliveryException.REPL_HEADER_NOT_EXIST);
		}
		if (replHeader.getStatus().equals(fromStatus)) {
			replHeader.setStatus(toStatus);
			this.replenishHeaderDAO.update(replHeader);
		}
	}
	
	/**
	 * 操作下架库存
	 * @param replTask
	 * @param offsHelfQty
	 * @return
	 */
	private Map<String,Long> changeOffShelfStock(ReplenishTask replTask, BigDecimal offsHelfQtyUnit) {
		StockDTO stockDto = new StockDTO();
		stockDto.setFmStockId(replTask.getFmStockId());
		stockDto.setToStockId(replTask.getToStockId());
		stockDto.setPlanQty(replTask.getQty());
		stockDto.setPlanQtyUnit(replTask.getQtyUnit());
		stockDto.setActualQtyUnit(offsHelfQtyUnit);
		stockDto.setPackQty(replTask.getPackQty());
		stockDto.setActualQty(offsHelfQtyUnit.multiply(replTask.getPackQty()));
		offShelfOperator.setStockDto(stockDto);
	    return stockService.operateStock(offShelfOperator);
	}
	
	/**
	 * 根据补货单id完成补货
	 * @param replHeaderId
	 * @return
	 */
	@Loggable
    @Override
    @Transactional
    public void confirmReplHeader(Long replHeaderId) {
	    log.debug("Comfirm repl header:{replHeaderId: {}}", replHeaderId);
	    
        ReplenishHeader replenishHeader = replenishHeaderDAO.get(replHeaderId);

        if (StringUtil.isNotIn(replenishHeader.getStatus(), ReplStatus.RELEASED.getValue(),
            ReplStatus.INPROGRESS.getValue())) {
        	 //非已发布，补货中状态不能完成补货；
        	
            throw new DeliveryException(DeliveryException.COMFIRM_REPL_HEADER_STATUS_ERROR);
        }

        List<ReplenishTask> replenishTasks = getTaskInStatus(replHeaderId,ReplTaskStatus.OFFSHELF.getValue(),
                ReplTaskStatus.INPROGRESS.getValue());
        if (ListUtil.isNotEmpty(replenishTasks)) {
            throw new DeliveryException(DeliveryException.COMFIRM_REPL_HEADER_DETAIL_STATUS_ERROR);
        }

        //补货单状态更新为已完成
        replenishHeader.setStatus(ReplStatus.CLOSED.getValue());
        replenishHeaderDAO.update(replenishHeader);
        delLbfTaskOfDoc(replenishHeader.getReplNo());

        //初始化、发布状态的任务的库存需要还原到源库位
        List<ReplenishTask> replTaskList = getTaskInStatus(replHeaderId,ReplTaskStatus.RELEASED.getValue(),
            ReplTaskStatus.INITIAL.getValue());
        if (!ListUtil.isNullOrEmpty(replTaskList)) {
            for (ReplenishTask task : replTaskList) {
                StockDTO stockDto = new StockDTO();
				stockDto.setFmLocId(task.getFmLocId());
				stockDto.setFmStockId(task.getFmStockId());
				stockDto.setToStockId(task.getToStockId());
				//更新库存
				stockDto.setPlanQty(task.getQty());
				stockDto.setPlanQtyUnit(task.getQtyUnit());
				stockDto.setSkuId(task.getSkuId());
				stockDto.setLotId(task.getLotId());
				stockDto.setLpnNo(task.getLpnNo());
				replConfirmOperator.setStockDto(stockDto);
                stockService.operateStock(replConfirmOperator);

				//任务状态更新为已取消
				task.setTaskStatus(ReplTaskStatus.CANCELLED.getValue());
                replTaskDAO.update(task);
            }
        }
    }

    /**
	 * 根据补货单id取消补货单
	 * @param replHeaderId
	 * @return
	 */
	@Loggable
    @Override
    @Transactional
    public void cancelReplHeader(Long replHeaderId) {
	    log.debug("Cancel repl header:[replHeaderId: {}]", replHeaderId);
	    
        ReplenishHeader replenishHeader = replenishHeaderDAO.get(replHeaderId);

        if (!ReplStatus.RELEASED.getValue().equals(replenishHeader.getStatus())) {
        	//只有发布状态的补货单才能取消
        	
            throw new DeliveryException(DeliveryException.CANCEL_REPL_HEADER_STATUS_ERROR);
        }
        
        //存在已下架、已上架的任务不能取消
        List<ReplenishTask> replTaskList = getTaskInStatus(replHeaderId,ReplTaskStatus.OFFSHELF.getValue(),
        		ReplTaskStatus.INPROGRESS.getValue(), ReplTaskStatus.CLOSED.getValue());
        if (!ListUtil.isNullOrEmpty(replTaskList)) {
            throw new DeliveryException(DeliveryException.CANCEL_REPL_HEADER_DETAIL_STATUS_ERROR);
        }

        //补货单状态更新为已取消
        replenishHeader.setStatus(ReplStatus.CANCELLED.getValue());
        replenishHeaderDAO.update(replenishHeader);

        replTaskList = getTaskInStatus(replHeaderId,ReplTaskStatus.INITIAL.getValue(), ReplTaskStatus.RELEASED.getValue());
        if (!ListUtil.isNullOrEmpty(replTaskList)) {
            for (ReplenishTask task : replTaskList) {
                StockDTO stockDto = new StockDTO();
				stockDto.setFmLocId(task.getFmLocId());
				stockDto.setFmStockId(task.getFmStockId());
				stockDto.setToStockId(task.getToStockId());
				//更新库存
				stockDto.setPlanQty(task.getQty());
				stockDto.setPlanQtyUnit(task.getQtyUnit());
				stockDto.setSkuId(task.getSkuId());
				stockDto.setLotId(task.getLotId());
				stockDto.setLpnNo(task.getLpnNo());
                replConfirmOperator.setStockDto(stockDto);
                stockService.undo(replConfirmOperator);

                //任务状态更新为已取消
                task.setTaskStatus(ReplTaskStatus.CANCELLED.getValue());
                replTaskDAO.update(task);
            }
        }
        
        delLbfTaskOfDoc(replenishHeader.getReplNo());
    }
    
	/**
	 * 删除补货单相关的劳动力任务
	 * @param replNo
	 */
	private void delLbfTaskOfDoc(String replNo) {
		Integer isLbrFcOpen = SystemConfig.getConfigValueInt(Switch.LABOR_FORCE_SWITCH_REPL, ParamUtil.getCurrentWarehouseId());
		if (YesNo.YES.getValue().equals(isLbrFcOpen)) {
			tempLaborTaskService.deleteByTaskNo(replNo);
		}
	}
	
    /**
     * 根据补货单id和状态列表获取补货任务
     * @param replHeaderId
     * @param value
     * @param strs
     * @return
     */
	private List<ReplenishTask> getTaskInStatus(Long replHeaderId,String value, String... strs) {
        List<String> statusList = new ArrayList<String>();
        statusList.add(value);
        for (String str : strs) {
            statusList.add(str);
        }

        TrsTaskFilter trsTaskFilter = new TrsTaskFilter();
        trsTaskFilter.setStatusList(statusList);
        trsTaskFilter.setDocOperId(replHeaderId);
        return replTaskDAO.findByFilter(trsTaskFilter);
    }
	
	@Override
    public ReplenishTask getReplenishTaskById(Long taskId) {
		return replTaskDAO.get(taskId);
	}
	
	@Override
    public boolean canOverReplenish() {
		return YesNo.YES.getValue().equals(SystemConfig.getConfigValueInt("delivery.replenish.overReplenish",
				ParamUtil.getCurrentWarehouseId()));
	}

	@Override
	@Transactional
	public void cancelReplMoveTask(Long replTaskId) {
		if (!canOverReplenish()) {
			throw new DeliveryException(DeliveryException.PEP_TASK_STATUS_ILLEGAL);
		}
		ReplenishTask replenishTask = getReplenishTaskById(replTaskId);
		if (!StringUtil.isIn(replenishTask.getTaskStatus(),
				ReplTaskStatus.OFFSHELF.getValue(), ReplTaskStatus.INPROGRESS.getValue())) {
			throw new DeliveryException(DeliveryException.PEP_TASK_STATUS_ILLEGAL);
		}
		replMoveTaskService.cancelReplMoveTask(replTaskId, true);
		if (ReplTaskStatus.INPROGRESS.getValue().equals(replenishTask.getTaskStatus())) {
			replenishTask.setTaskStatus(ReplStatus.CLOSED.getValue());
			replTaskDAO.save(replenishTask);
		}
		refreshReplHeaderStatus(replenishTask.getDocOperId());
	}
	
	/**
	 * 根据sku查询所有初始化状态的闲时补货任务总qty
	 */
	@Override
    public BigDecimal findFreeReplQtyBySku(Long skuId) {
		return replTaskDAO.findFreeReplQtyBySku(skuId);
	}
	
	
	/**
	 * 根据商品查发布的即时补货任务
	 */
	@Override
    public List<BigDecimal> getReleasedJPTask(Long skuId) {
		return replTaskDAO.getReleasedJPTask(skuId);
	}
}