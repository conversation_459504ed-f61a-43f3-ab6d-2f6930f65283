package com.daxia.wms.delivery.print.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import com.daxia.framework.common.util.CompareUtil;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ListUtil.ListMegareOpr;
import com.daxia.framework.system.constants.Constants.YesNo;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.print.dto.PrintHoldDoLabelDTO;
import com.daxia.wms.delivery.print.dto.PrintHoldDoLabelDetailDTO;
import com.daxia.wms.delivery.print.service.PrintHoldDoLabelService;
import com.daxia.wms.master.entity.Sku;
import com.daxia.wms.print.PrintConstants.PrintType;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.print.service.impl.AbstractLodopPrint;

@Name("printHoldDoLabelService")
@lombok.extern.slf4j.Slf4j
public class PrintHoldDoLabelServiceImpl extends AbstractLodopPrint<DeliveryOrderHeader> implements PrintHoldDoLabelService {

	private static final int DETAILS_IN_PER_LABEL = 6;
	
	@In
	private DeliveryOrderService deliveryOrderService;
	
	@Override
	protected List<DeliveryOrderHeader> getSourceList(List<Long> ids) {
		return deliveryOrderService.findDoByIds(ids);
	}

	@Override
	protected List<PrintReportDto> buildPrintDataDto(DeliveryOrderHeader doHeader) {
		List<PrintReportDto> dtoList = new ArrayList<PrintReportDto>();
		List<PrintHoldDoLabelDetailDTO> allDetailDtos = new ArrayList<PrintHoldDoLabelDetailDTO>();
		for (DeliveryOrderDetail detail : doHeader.getDoDetails()) {
			Sku sku = detail.getSku();
			PrintHoldDoLabelDetailDTO detailDto = new PrintHoldDoLabelDetailDTO();
			detailDto.setSkuId(sku.getId());
			detailDto.setProductBarcode(sku.getEan13());
			detailDto.setProductName(sku.getProductCname());
			detailDto.setQty(detail.getPickedQty() == null ? BigDecimal.ZERO : detail.getPickedQty());
			allDetailDtos.add(detailDto);
		}
		
		// 合并同sku的记录
		ListUtil.megareList(allDetailDtos, new ListMegareOpr<PrintHoldDoLabelDetailDTO>(){
			@Override
            public boolean isNeedMegare(PrintHoldDoLabelDetailDTO dto1, PrintHoldDoLabelDetailDTO dto2){
                return CompareUtil.compare(dto1.getSkuId(), dto2.getSkuId());
            }
            @Override
            public void megareOpr(PrintHoldDoLabelDetailDTO dto1, PrintHoldDoLabelDetailDTO dto2){
            	dto1.setQty(dto1.getQty().add(dto2.getQty()));
            }
		});

		int size = allDetailDtos.size();
		int total = size % DETAILS_IN_PER_LABEL == 0 ? size
				/ DETAILS_IN_PER_LABEL : size / DETAILS_IN_PER_LABEL + 1;
		PrintHoldDoLabelDTO dto = null;
		for (int index = 0; index < total; index++) {
			dto = new PrintHoldDoLabelDTO();
			dto.setDoNo(doHeader.getDoNo());
			dto.setDocId(doHeader.getId());
			dto.setDocNo(doHeader.getDoNo());
			dto.setCurPage(Long.valueOf(index + 1));
			dto.setTotalPage(Long.valueOf(total));
			int endIndex = (index + 1) * DETAILS_IN_PER_LABEL > size ? size
					: (index + 1) * DETAILS_IN_PER_LABEL;
			dto.setDetailList(allDetailDtos.subList(index * DETAILS_IN_PER_LABEL, endIndex));
			dtoList.add(dto);
		}
		return dtoList;
	}

	@Override
	@Transactional
	public PrintData print(Long doHeaderId) {
		List<Long> idList = new ArrayList<Long>();
		idList.add(doHeaderId);
		this.paramMap.put(this.PRINT_NOTES, "hold_label");
		this.paramMap.put(this.PRINT_TYPE, PrintType.HD_DO_LABEL);
		PrintData printData = this.buildPrintDtos(idList);
		return printData;
	}
}
