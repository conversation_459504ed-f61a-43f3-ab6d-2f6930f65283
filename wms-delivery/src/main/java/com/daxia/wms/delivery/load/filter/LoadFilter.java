package com.daxia.wms.delivery.load.filter;

import java.util.Date;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

/**
 * 交接单头信息查询过滤器
 */
@lombok.extern.slf4j.Slf4j
public class LoadFilter extends WhBaseQueryFilter {

    private static final long serialVersionUID = -7698824619052084988L;
    
	/**
     * 交接单号
     */
    private String loadNo;
    
    /**
     * 交接类型
     */
    private String loadType;
    
    /**
     * 箱号
     */
    private String cartonNo;
    /**
     * 箱号
     */
    private String doNo;
    /**
     * 交接单状态
     */
    private String status;
    /**
     * 配送商
     */
    private Long carrierId;
    /**
	 * 创建时间From
	 */
	private Date createTimeFrom;
	/**
	 * 创建时间To
	 */
	private Date CreateTimeTo;

	private Date deliveryTimeFrom;
	private Date deliveryTimeTo;
	
    /**
	 * 交接方式  0： 人工交接 ，1：自动交接
	 */
	private Integer isAuto;
    
    @Operation(fieldName = "o.loadNo", operationType = OperationType.EQUAL)
    public String getLoadNo() {
		return loadNo;
	}
	public void setLoadNo(String loadNo) {
		this.loadNo = loadNo;
	}
	
	@Operation(fieldName = "o.status", operationType = OperationType.EQUAL)
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	@Operation(fieldName = "o.carrierId", operationType = OperationType.EQUAL)
	public Long getCarrierId() {
		return carrierId;
	}
	public void setCarrierId(Long carrierId) {
		this.carrierId = carrierId;
	}
	
	@Operation(fieldName = "o.createdAt", operationType = OperationType.NOT_LESS_THAN , dataType="datetime")
	public Date getCreateTimeFrom() {
		return createTimeFrom;
	}
	public void setCreateTimeFrom(Date createTimeFrom) {
		this.createTimeFrom = createTimeFrom;
	}
	
	@Operation(fieldName = "o.createdAt", operationType = OperationType.NOT_GREAT_THAN  , dataType="datetime")
	public Date getCreateTimeTo() {
		return CreateTimeTo;
	}
	public void setCreateTimeTo(Date createTimeTo) {
		CreateTimeTo = createTimeTo;
	}
	
	public void setCartonNo(String cartonNo) {
		this.cartonNo = cartonNo;
	}
	@Operation(fieldName = "o.loadType", operationType = OperationType.EQUAL)
	public String getLoadType() {
        return loadType;
    }
    public void setLoadType(String loadType) {
        this.loadType = loadType;
    }
    @Operation(
	        clause = " o.id in (select detail.loadHeaderId from LoadDetail detail where detail.carrierNo = ? )" 
	      , operationType = OperationType.CLAUSE
	    )
	public String getCartonNo() {
		return cartonNo;
	}
    @Operation(
        clause = " o.id in (select detail.loadHeaderId from LoadDetail detail where detail.deliveryOrderHeader.doNo = ? )" 
      , operationType = OperationType.CLAUSE
    )
    public String getDoNo() {
        return doNo;
    }
    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }
    
    @Operation(fieldName = "o.isAuto", operationType = OperationType.EQUAL)
    public Integer getIsAuto() {
        return isAuto;
    }
    public void setIsAuto(Integer isAuto) {
        this.isAuto = isAuto;
    }

	@Operation(fieldName = "o.deliveryTime", operationType = OperationType.NOT_LESS_THAN , dataType="datetime")
	public Date getDeliveryTimeFrom() {
		return deliveryTimeFrom;
	}

	public void setDeliveryTimeFrom(Date deliveryTimeFrom) {
		this.deliveryTimeFrom = deliveryTimeFrom;
	}

	@Operation(fieldName = "o.deliveryTime", operationType = OperationType.NOT_GREAT_THAN, dataType="datetime")
	public Date getDeliveryTimeTo() {
		return deliveryTimeTo;
	}

	public void setDeliveryTimeTo(Date deliveryTimeTo) {
		this.deliveryTimeTo = deliveryTimeTo;
	}
}
