package com.daxia.wms.delivery.print.helper;

import java.awt.Font;
import java.awt.FontMetrics;

import javax.swing.JComponent;
import javax.swing.JLabel;


/**
 *  发票打印帮助工具类
 */
@lombok.extern.slf4j.Slf4j
public class PrintInvoiceHelper {
    
  //用以计算字符的宽度
    private static JComponent t = new JLabel();
    //设定字体
    private static Font f = new Font("Microsoft YaHei", Font.PLAIN, 10);
    private static FontMetrics fm = t.getFontMetrics(f);
    
    public static void main(String[] args) {   
//    	System.out.println(fm.stringWidth("123456"));
//    	System.out.println(fm.stringWidth("度"));
//    	System.out.println(fm.stringWidth("1"));
//    	System.out.println(fm.stringWidth("."));
//    	System.out.println(fm.stringWidth(" "));
//    	System.out.println("123456");
//    	System.out.println("1        6");
////        System.out.println(formatStr("12345.12", "12345678", "1234567.12"));
//        System.out.println(formatStr("100.00", "78945", "1234567.45"));
//        System.out.println(formatStr("12345.12", "123", "1234567.10"));
//        System.out.println(formatStr("21.54", "12", "1234.12"));
//        System.out.println(formatStr("21.54", "12", "34.12"));
////        
//        System.out.println(fm.stringWidth(formatStr("12345.12", "12345678", "1234567.12")));
//        System.out.println(fm.stringWidth(formatStr("100.00", "78945", "12345678.45")));
//        System.out.println(fm.stringWidth(formatStr("12345678.12", "123", "123456789.1")));
//        System.out.println(fm.stringWidth(formatStr("21.00", "12", "1234.12")));


    }
    
    /**
     * 获取字符串所占宽度
     * @param str
     * @return
     */
    public static int stringWidth(String str) {
        if (null == str) {
            return 0;
        }
        return fm.stringWidth(str);
    }
    
    /**
     * 格式化发票明细的单价、数量、总价信息，使它们尽量对齐
     * @param unitPriceStr
     * @param qtyStr
     * @param totalPriceStr
     * @return
     */
    public static String formatStr(String unitPriceStr, String qtyStr, String totalPriceStr) {
    	int maxUnitW = fm.stringWidth("12345.78");
    	int maxQtyW = fm.stringWidth("12345678");
    	int maxTotalW = fm.stringWidth("1234567.89");
        int qty1 = calNeedBlankQty(unitPriceStr, maxUnitW);
        int qty2 = calNeedBlankQty(qtyStr, maxQtyW);
        int qty3 = calNeedBlankQty(totalPriceStr, maxTotalW);
        int totalLength = fm.stringWidth(unitPriceStr) + fm.stringWidth(qtyStr) + fm.stringWidth(totalPriceStr);        
        String perSpace = "  ";
        //如果总宽度直接超过指定宽度，直接返回，不做任何处理
        if (totalLength >= (maxUnitW + maxQtyW + maxTotalW)) {
            return perSpace + unitPriceStr + perSpace + qtyStr + perSpace + totalPriceStr;
        } 
        StringBuilder sb = new StringBuilder();
        //思想：前一个可以依次向后借空格，有多少借多少，没有就直接添加，尽可能多的对齐
        sb.append(perSpace);
        sb.append(formatWithBlank(unitPriceStr, qty1));//单价在最前面，直接添加
        sb.append(perSpace);
        if (qty1 > 0 ) {
            sb.append(formatWithBlank(qtyStr, qty2));//数量在中间，当单价没有向后借空格时，直接添加
            sb.append(perSpace);
            if(qty2 > 0) {
                sb.append(formatWithBlank(totalPriceStr, qty3));//总价在最后，当单价和数量都没有向后借空格时，直接添加
            } else {
                if (qty3 >0) {
                    sb.append(formatWithBlank(totalPriceStr,qty2+qty3));//数量向后借空格，总价的空格数减少
                } else {
                    sb.append(totalPriceStr);//总价也没有空格可借，则直接添加
                }
            }
        } else {
            if(qty2 > 0) {
                sb.append(formatWithBlank(qtyStr, qty1 + qty2));//单价向数量借空格
                sb.append(perSpace);
                if (qty3 >0) {
                    sb.append(formatWithBlank(totalPriceStr, ((qty1 + qty2) < 0 ? (qty1 + qty2) : 0) + qty3));//单价向数量借空格，可能依然不够，再向总价借
                } else {
                    sb.append(totalPriceStr);//总价没有空格可借，直接添加
                }
            } else {
                sb.append(qtyStr);//数量没有空格可借，直接添加
                sb.append(perSpace);
                if (qty3 >0) {
                    sb.append(formatWithBlank(totalPriceStr, qty1 + qty2+qty3));//单价和数量都向总价借空格
                } else {
                    sb.append(totalPriceStr);//都没有可借空格，直接添加
                }
            }
        }
        return sb.toString();
    }
    
    /**
     * 格式化发票明细的数量、总价信息，使它们尽量对齐
     * @param unitPriceStr
     * @param qtyStr
     * @param totalPriceStr
     * @return
     */
    public static String formatStr(String qtyStr, String totalPriceStr) {
    	int maxQtyW = fm.stringWidth("12345678");
    	int maxTotalW = fm.stringWidth("123456789.00");
        int qty1 = calNeedBlankQty(qtyStr, maxQtyW);
        int qty2 = calNeedBlankQty(totalPriceStr, maxTotalW);
        int totalLength = fm.stringWidth(qtyStr) + fm.stringWidth(totalPriceStr);        
        String perSpace = "  ";
        //如果总宽度直接超过指定宽度，直接返回，不做任何处理
        if (totalLength >= (maxQtyW + maxTotalW)) {
            return perSpace + qtyStr + perSpace + perSpace + totalPriceStr;
        } 
        StringBuilder sb = new StringBuilder();
        sb.append(perSpace);
        sb.append(formatWithBlank(qtyStr, qty1));
        sb.append(perSpace);
        sb.append(perSpace);
        if (qty1 > 0 ) {
            sb.append(formatWithBlank(totalPriceStr, qty2));
        } else {
            if(qty2 > 0) {
                sb.append(formatWithBlank(totalPriceStr, qty1 + qty2));
            } else {
                sb.append(totalPriceStr);
            }
        }
        return sb.toString();
    }
    
    private static String formatWithBlank(String text, int qty) {
        if(qty == 0) {
            return text;
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < qty; i++) {
            sb.append(" ");
        }
       
        if (null != text) {
            sb.append(text);
        }
        
        return sb.toString();
    }
        
    /**
     * 计算文本填充满指定宽度还需要的空格数
     * @param text
     * @param width
     * @return qty <0 ,表示需要借的空格数 ， qty>0表示需要添加的空格数 
     */
    public static int calNeedBlankQty(String text, int width) {
        int sum = fm.stringWidth(text);
        int tempWidth = fm.stringWidth(" ");
        int qty = 0;
        if (sum ==width) {
            qty = 0;
        } else if (sum > width) {
            while (width+ tempWidth <= sum) {
                width = width + tempWidth;
                qty--;
            }
        } else{
            while (sum+ tempWidth <= width) {
                sum = sum + tempWidth;
                qty++;
            }
        }
        return qty;
    }
    
    /**
     * 在指定宽度的区域中放置字符串
     * return 最多放置的字符数
     */
    public static int cal(String text, int width) {
        char[] ch = text.toCharArray();
        int sum = 0;
        for (int i = 0; i < ch.length; i++) {
            String temp = "" + ch[i];
            int tempWidth = fm.stringWidth(temp);
            if ((sum + tempWidth) > width) {
                return i;
            }
            sum = sum + tempWidth;
        }
        return text.length();
    }
    
    /**
     * 在指定宽度的区域中放置字符串，不够宽度时用空格填充
     * return 最终放置的字符串
     */
    public static String calAndFillWithBlank(String text, int width) {
    	return calAndFillWithBlank(text, width, false);
    }
    
    /**
     * 在指定宽度的区域中放置字符串，不够宽度时用空格填充.
     * (考虑框架中最终将文本字体缩小1个像素，这里填充时可以选择是否考虑这一因素)
     * @param text
     * @param width
     * @param isMinus1px 是否对单个文本所占宽度减去1
     * @return
     */
    public static String calAndFillWithBlank(String text, int width, boolean isMinus1px) {
        char[] ch = text.toCharArray();
        int sum = 0;
        int blankWidth = fm.stringWidth(" ");
        for (int i = 0; i < ch.length; i++) {
            String temp = "" + ch[i];
            int tempWidth = fm.stringWidth(temp) - (isMinus1px ? 1 : 0);
            if ((sum + tempWidth) > width) {
                StringBuilder sb = new StringBuilder(text.substring(0, i));
                while (sum + blankWidth <= width) {
                    sum = sum + blankWidth;
                    sb.append(" "); 
                }
                return sb.toString();
            }
            sum = sum + tempWidth;
        }
        
        StringBuilder sb = new StringBuilder(text);
        while (sum + blankWidth <= width) {
            sum = sum + blankWidth;
            sb.append(" ");
        }
        return sb.toString();
    }
}
