//package com.daxia.wms.delivery.recheck.service.impl.carton;
//
//import com.daxia.framework.common.util.Config;
//import com.daxia.framework.common.util.ParamUtil;
//import com.daxia.framework.system.SequenceKeys;
//import com.daxia.framework.system.service.SequenceGeneratorService;
//import com.daxia.wms.Keys;
//import com.daxia.wms.delivery.DeliveryException;
//import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
//import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
//import com.daxia.wms.delivery.recheck.entity.CartonHeader;
//import com.daxia.wms.master.entity.Carrier;
//import com.daxia.wms.master.entity.CarrierCainiaoEx;
//import com.daxia.wms.master.entity.WarehouseCarrier;
//import com.daxia.wms.master.service.CarrierCainiaoExService;
//import com.daxia.wms.master.service.ShopInfoService;
//import com.daxia.wms.master.service.SkuCache;
//import com.google.common.collect.Lists;
//import com.pdd.pop.sdk.http.PopClient;
//import com.pdd.pop.sdk.http.PopHttpClient;
//import com.pdd.pop.sdk.http.api.request.PddWaybillGetRequest;
//import com.pdd.pop.sdk.http.api.response.PddWaybillGetResponse;
//import org.apache.commons.lang.StringUtils;
//import org.jboss.seam.annotations.In;
//import org.jboss.seam.annotations.Logger;
//import org.jboss.seam.annotations.Name;
//import org.jboss.seam.log.Log;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Name("pddWayBillGet")
//@lombok.extern.slf4j.Slf4j
// public class PddWayBillGet extends PddWayBillBase {
//
//    @In
//    CarrierCainiaoExService carrierCainiaoExService;
//
//    @In
//    SkuCache skuCache;
//
//    @In
//    ShopInfoService shopInfoService;
//
//    @In
//    SequenceGeneratorService sequenceGeneratorService;
//
//    @Logger
//    Log log;
//
//    public PddWaybillGetResponse.InnerPddWaybillGetResponseModulesItem reqeust(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
//        //设置LPN为WMS箱号
//        cartonHeader.setRefNo(sequenceGeneratorService.generateSequenceNo(SequenceKeys.CARTON_NO, ParamUtil.getCurrentWarehouseId()));
//
//        Carrier carrier = doHeader.getCarrier();
//        CarrierCainiaoEx carrierCainiaoEx = carrierCainiaoExService.getByCarrier(carrier.getId());
//        if (carrierCainiaoEx == null) {
//            throw new DeliveryException(DeliveryException.WAYBILL_PDD_GET_ERROR, "配送商拼多多配置信息缺失，请联系管理员");
//        }
//        WarehouseCarrier warehouseCarrier = loadWarehouseCarrier(carrier.getId());
//
//        PddWaybillGetRequest req = new PddWaybillGetRequest();
//        req.setParamWaybillCloudPrintApplyNewRequest(genWaybillApplyNewRequest(doHeader, cartonHeader, carrierCainiaoEx));
//
//        PopClient client = new PopHttpClient(warehouseCarrier.getAppKey(), warehouseCarrier.getAppSecret());
//        try {
//            PddWaybillGetResponse rsp = client.syncInvoke(req, warehouseCarrier.getAppToken());
//            if (null != rsp.getErrorResponse()) {
//                log.error("pdd get error, response: " + rsp.getErrorResponse().getErrorMsg());
//                throw new DeliveryException(DeliveryException.WAYBILL_PDD_GET_ERROR, rsp.getErrorResponse().getErrorMsg());
//            }
//            return rsp.getPddWaybillGetResponse().getModules().get(0);
//        } catch (Exception e) {
//            log.error("pdd get error! ", e);
//            throw new DeliveryException(DeliveryException.WAYBILL_PDD_GET_ERROR, e);
//        }
//    }
//
//    private PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequest genWaybillApplyNewRequest(DeliveryOrderHeader doHeader, CartonHeader cartonHeader, CarrierCainiaoEx carrierCainiaoEx) {
//        PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequest applyNewRequest = new PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequest();
//        applyNewRequest.setWpCode(carrierCainiaoEx.getCpCode());//物流公司Code
//        applyNewRequest.setNeedEncrypt(Config.isDefaultFalse(Keys.Delivery.pdd_way_bill_need_encrypt, Config.ConfigLevel.WAREHOUSE));//设定取号返回的云打印报文是否加密  非必填
////        applyNewRequest.setNeedEncrypt(false);//设定取号返回的云打印报文是否加密  非必填
//        applyNewRequest.setSender(genShippingAddress4Get(carrierCainiaoEx, doHeader));//发货人信息
//        applyNewRequest.setTradeOrderInfoDtos(genTradeOrderInfo(doHeader, cartonHeader, carrierCainiaoEx));// 请求面单信息，数量限制为10
//        return applyNewRequest;
//    }
//
//    // 面单详细信息
//    private List<PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItem> genTradeOrderInfo(DeliveryOrderHeader doHeader, CartonHeader cartonHeader, CarrierCainiaoEx carrierCainiaoEx) {
//        List<PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItem> tradeOrderInfoDtos = new ArrayList<PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItem>();
//        PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItem item = new PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItem();
//
//        //收货人信息
//        item.setRecipient(genConsigneeInfo4Get(doHeader));
//
//        PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItemOrderInfo orderInfoDto = new PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItemOrderInfo();
//        // 订单渠道
//        orderInfoDto.setOrderChannelsType(CainiaoHelper.converOrderChannelsType(doHeader.getOrderSource()));
//        orderInfoDto.setTradeOrderList(Lists.newArrayList(doHeader.getDoNo()));
//        item.setOrderInfo(orderInfoDto);
//        // 使用者ID(发货时商家 ID一致)
//        // 淘系订单【使用者 id】可以使用查询卖家用户信息接口taobao.user.seller.get获取
//        // 非淘系订单【使用者id】直接使用【申请者id】， 风控只针对淘系订单
//        item.setObjectId(doHeader.getDoNo() + cartonHeader.getRefNo());
//        item.setUserId(genRealUserId(doHeader, carrierCainiaoEx));
//        item.setPackageInfo(genPackageIntem(doHeader, cartonHeader));
//        // 交易订单列表
////        tradeOrderInfo.setPackageId(cartonHeader.getRefNo());
//        //设置打印模版
//        if (carrierCainiaoEx.getTemplateURL() == null) {
//            throw new DeliveryException(DeliveryException.WAYBILL_PDD_GET_ERROR, "配送商打印模版配置信息缺失，请联系管理员");
//        }
//        item.setTemplateUrl(carrierCainiaoEx.getTemplateURL());
//        item.setLogisticsServices(genLogisticsServiceList4Get(doHeader));
//        tradeOrderInfoDtos.add(item);
//        return tradeOrderInfoDtos;
//    }
//
//    // 包裹中的商品类型
//    private PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItemPackageInfo genPackageIntem(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
//        List<DeliveryOrderDetail> details = doHeader.getDoDetails();
//        PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItemPackageInfo packageInfo = new PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItemPackageInfo();
//        List<PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItemPackageInfoItemsItem> itemList = new ArrayList<PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItemPackageInfoItemsItem>();
//        for (DeliveryOrderDetail detail : details) {
//            String skuName = skuCache.getSku(detail.getSkuId()).getProductCname();
//            if (StringUtils.isNotEmpty(skuName) && itemList.size() < 100) {
//                PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItemPackageInfoItemsItem item = new PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItemPackageInfoItemsItem();
//                item.setName(skuName);
//                item.setCount(detail.getExpectedQty().intValue());
//                itemList.add(item);
//            }
//        }
//        if (cartonHeader != null) {
//            packageInfo.setId(cartonHeader.getRefNo());
//        }
//        packageInfo.setItems(itemList);
//
//        return packageInfo;
//    }
//
//}