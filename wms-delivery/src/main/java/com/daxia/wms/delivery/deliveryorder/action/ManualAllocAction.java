package com.daxia.wms.delivery.deliveryorder.action;

import com.daxia.dubhe.api.internal.util.NumberUtils;
import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;

import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.dto.ManualAllocDTO;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateHeader;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateService;
import com.daxia.wms.master.entity.PackageInfoDetail;
import com.daxia.wms.master.service.PackageInfoDetailService;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

/**
 * 手动分配(分配调拨/RTV)action
 */
@Name("com.daxia.wms.delivery.manualAllocAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ManualAllocAction extends PagedListBean<ManualAllocDTO> {

	private static final long serialVersionUID = -4742102873686803160L;
	private Long doAllocateDetailId;
    private DoAllocateDetail doAllocateDetail;
    private boolean initialized = false;
    private Integer isException ;
	private PackageInfoDetail packagePcs;
	private PackageInfoDetail packageUnit;
	@Setter
	@Getter
	private String locType;
	@Setter
	@Getter
	private String locCode;
    @In
    private DoAllocateService doAllocateService;

	@In
	private PackageInfoDetailService packageInfoDetailService;
	
	public ManualAllocAction () {
	}
	
	/**
	 * 页面加载时的初始化方法
	 */
	public void initializePage() {
		if (!initialized) {
			this.query();
			this.initialized = true;
		}
	}

	public void changeAlcPcsAndUnit(){
		if (!Constants.DoStatus.INITIAL.getValue().equals(doAllocateDetail.getLineStatus())) {
			this.sayMessage(MESSAGE_FAILED);
			return;
		}
		doAllocateService.saveOrUpdate(doAllocateDetail);
		this.sayMessage(MESSAGE_SUCCESS);
	}

	/**
	 * 根据订单明细di，获取此条明细可分配的所有库存记录
	 */
	@Override
	public void query() {
		//获取本次操作的订单明细
	    this.doAllocateDetail = doAllocateService.getDoAllocateDetailById(this.doAllocateDetailId);

		ImmutablePair<PackageInfoDetail, PackageInfoDetail> pcsAndUnitPackages = packageInfoDetailService.findPcsAndUnitPackages(doAllocateDetail.getSkuId());
		packagePcs = pcsAndUnitPackages.getLeft();
		packageUnit = pcsAndUnitPackages.getRight();

		//获取订单头信息
	    DoAllocateHeader doAllocateHeader = doAllocateDetail.getDoAllocateHeader();
	    //分页获取订单明细可用库存信息
		DataPage<ManualAllocDTO> data = doAllocateService.queryManualAllocInfo(doAllocateHeader, doAllocateDetail, getStartIndex(), getPageSize(),locType,locCode);
		this.populateValues(data);
	}
	
	/**
     * 
     * 手动分配库存
     *
     * @throws Exception
     */
	@Loggable
	public void manualAlloc(boolean isForce) throws Exception {
		doAllocateService.manualAlloc(this.doAllocateDetail.getId(), this.dataPage.getDataList(), isForce, NumberUtils.object2Integer(isException,0));
		this.query();
		this.sayMessage(MESSAGE_SUCCESS);
	}

	public Long getDoAllocateDetailId() {
		return doAllocateDetailId;
	}

	public void setDoAllocateDetailId(Long doAllocateDetailId) {
		this.doAllocateDetailId = doAllocateDetailId;
	}

	public DoAllocateDetail getDoAllocateDetail() {
		return doAllocateDetail;
	}

	public void setDoAllocateDetail(DoAllocateDetail doAllocateDetail) {
		this.doAllocateDetail = doAllocateDetail;
	}

	public PackageInfoDetail getPackagePcs() {
		return packagePcs;
	}

	public void setPackagePcs(PackageInfoDetail packagePcs) {
		this.packagePcs = packagePcs;
	}

	public PackageInfoDetail getPackageUnit() {
		return packageUnit;
	}

	public void setPackageUnit(PackageInfoDetail packageUnit) {
		this.packageUnit = packageUnit;
	}

    public Integer getIsException() {
        return isException;
    }

    public void setIsException(Integer isException) {
        this.isException = isException;
    }
}
