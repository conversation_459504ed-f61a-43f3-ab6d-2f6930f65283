package com.daxia.wms.delivery.load.service.impl;


import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ResourceUtils;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.CrossDockStatus;
import com.daxia.wms.Constants.DocType;
import com.daxia.wms.Constants.TrsType;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.OrderLogConstants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.OrderLogService;
import com.daxia.wms.delivery.load.dao.CrossDockDeliverDetailDao;
import com.daxia.wms.delivery.load.dao.TOCrossDockDetailDAO;
import com.daxia.wms.delivery.load.dao.TOCrossDockHeaderDAO;
import com.daxia.wms.delivery.load.dao.TrsShipLogDAO;
import com.daxia.wms.delivery.load.dto.CrossDockDTO;
import com.daxia.wms.delivery.load.dto.StockCrossDockDTO;
import com.daxia.wms.delivery.load.entity.TOCrossDockDetail;
import com.daxia.wms.delivery.load.entity.TOCrossDockHeader;
import com.daxia.wms.delivery.load.service.CrossDockDetailService;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.exp.stock.srv.StockExpSrv;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.serial.dao.TrsSerialLogDAO;
import com.daxia.wms.serial.entity.TrsSerialLog;
import com.daxia.wms.serial.service.TrsService;
import com.daxia.wms.stock.stock.dao.StockCrossDockDAO;
import com.daxia.wms.stock.stock.dao.StockSerialDAO;
import com.daxia.wms.stock.stock.dto.StockDTO;
import com.daxia.wms.stock.stock.entity.StockCrossDock;
import com.daxia.wms.stock.stock.entity.StockSerial;
import com.daxia.wms.stock.stock.entity.TrsShipLog;
import com.daxia.wms.stock.stock.service.IOperator;
import org.apache.commons.lang3.tuple.Triple;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description:越库发货业务Service实现类
 */
@Name("com.daxia.wms.delivery.crossDockDetailService")
@lombok.extern.slf4j.Slf4j
public class CrossDockDetailServiceImpl implements CrossDockDetailService,Serializable {

	private static final long serialVersionUID = -1118976896962806098L;

	@In
	private CrossDockDeliverDetailDao crossDockDeliverDetailDao;

	@In
	private TOCrossDockHeaderDAO toCrossDockHeaderDAO;

	@In
	private StockCrossDockDAO stockCrossDockDAO;

	@In
	private DeliveryOrderService deliveryOrderService;

	@In
	private TOCrossDockDetailDAO toCrossDockDetailDAO;

	@In
	private StockSerialDAO stockSerialDAO;

	@In
	private ExpFacadeService expFacadeService;
	@In
	private OrderLogService orderLogService;

	@In
	private TrsService trsService;

	@In
	private TrsShipLogDAO trsShipLogDAO;
	@In
	private StockExpSrv stockExpSrv;
	
	@In("crossDockDeliveryOperator")
	private IOperator crossDockDeliveryOperator;
	
	/**
	 * 根据LPN号获取越库发货单单头
	 * 
	 * @param lpnNo
	 *            LPN号
	 * @param isShipped
	 *            是否发货
	 * @return 越库发货单单头
	 */
	@Override
	@SuppressWarnings("unchecked")
	public TOCrossDockHeader findToCDHeaderByLpnNo(String lpnNo,boolean isShipped) {
		Object object = crossDockDeliverDetailDao.findToCDHeaderByLpnNo(lpnNo,isShipped);
		TOCrossDockHeader toCrossDockHeader = null;
		if (object != null && ((List<Object>) object).size() > 0) {
			Object[] obj = (Object[]) ((List<Object>) object).get(0);
			toCrossDockHeader = new TOCrossDockHeader();
			toCrossDockHeader.setId(Long.valueOf(((Object[]) obj)[0].toString()));
			toCrossDockHeader.setRefNo2(StringUtil.notNullString(((Object[]) obj)[1]));
			toCrossDockHeader.setDoNo(StringUtil.notNullString(((Object[]) obj)[2]));
			toCrossDockHeader.setStatus(StringUtil.notNullString(((Object[]) obj)[3]));
			toCrossDockHeader.setRefNo1(StringUtil.notNullString(((Object[]) obj)[4]));
			toCrossDockHeader.setTargetWarehouseId(StringUtil.notNullString(((Object[]) obj)[5]));
			Warehouse warehouse = new Warehouse();
			warehouse.setWarehouseName(StringUtil.notNullString(((Object[]) obj)[6]));
			toCrossDockHeader.setWareHouse(warehouse);
		}
		return toCrossDockHeader;
	}

	/**
	 * 通过LpnNo获取发货需要扫描的LPN号`
	 * 
	 * @param lpnNo
	 *            lpn编号
	 * @param isShipped
	 *            是否发货
	 * @return 需要扫描的LPN号
	 */
	@Override
	public List<String> getNeedScannedLpnByLpnNo(String lpnNo, boolean isShipped) {
		return crossDockDeliverDetailDao.getNeedScannedLpnByLpnNo(lpnNo,isShipped);
	}

	/**
	 * 通过越库发货单单头获取需要扫描的LPN号
	 * 
	 * @param cdHeaderId
	 *            越库发货单单头
	 * @param isShipped
	 *            是否发货
	 * @return 需要扫描的LPN号
	 */
	@Override
	public List<String> getNeedScannedLpnByCDHeaderId(Long cdHeaderId, Integer crossStockType, boolean isShipped) {
		return crossDockDeliverDetailDao.getNeedScannedLpnByCDHeaderId(cdHeaderId,crossStockType, isShipped);
	}

	/**
	 * 通过LPN号获取已稍描的LPN对应的 stocCrossDockList
	 * 
	 * @param lpnNo
	 *            lpn编号
	 * @param isShipped
	 *            是否发货
	 * @return 返回LPN对应的有效收货待转运库存
	 */
	public List<StockCrossDock> getStockCrossDockByLpnNo(String lpnNo,boolean isShipped) {
		return crossDockDeliverDetailDao.getStockCrossDockByLpnNo(lpnNo,isShipped);
	}

	/**
	 * 通过lpnNo组装stockCrossDockDTO
	 * 
	 * @param lpnNo
	 *            l lpn编号
	 * @param isShipped
	 *            是否发货
	 * @return 组装后的stockCrossDockDTO
	 */
	@Override
	public CrossDockDTO getStockCrossDockDTOByLpnNo(String lpnNo, boolean isShipped) {
		TOCrossDockHeader toCrossDockHeader = this.findToCDHeaderByLpnNo(lpnNo,isShipped);
		if(toCrossDockHeader == null) {
            return null;
        }
		List<String> needScannedLpns = this.getNeedScannedLpnByLpnNo(lpnNo,isShipped);
		CrossDockDTO crossDockDTO = this.getStockCrossDockDTOByCDANDLPN(toCrossDockHeader,needScannedLpns,null);
		crossDockDTO.setCdDumpLpnNo(StringUtil.combine(crossDockDeliverDetailDao.getCDJumpCDHeaderId
				(toCrossDockHeader.getId(),toCrossDockHeader.getCrossStockType()), ","));
		return crossDockDTO;
	}

	/**
	 * 通过cdHeaderId组装stockCrossDockDTO
	 * 
	 * @param cdHeaderId
	 *            越库发货单单头ID
	 * @return 组装后的stockCrossDockDTO
	 */
	@Override
	public CrossDockDTO getStockCrossDockDTOByCDHeaderId(Long cdHeaderId) {
		DeliveryOrderHeader toCrossDockHeader = deliveryOrderService.getDoHeaderById(cdHeaderId);
		List<String> needScannedLpns = this.getNeedScannedLpnByCDHeaderId(cdHeaderId,toCrossDockHeader
				.getCrossStockType(), false);
		List<String> notNeedScannedLpns = this.getNeedScannedLpnByCDHeaderId(cdHeaderId,toCrossDockHeader
				.getCrossStockType(), true);
		CrossDockDTO crossDockDTO  = this.getStockCrossDockDTOByCDANDLPN(toCrossDockHeader,needScannedLpns,notNeedScannedLpns);
		crossDockDTO.setCdDumpLpnNo(StringUtil.combine(crossDockDeliverDetailDao.getCDJumpCDHeaderId(cdHeaderId,
				toCrossDockHeader.getCrossStockType()), ","));
		return crossDockDTO;
	}

	/**
	 * 通过cdHeaderId获取越库单据头
	 * 
	 * @param cdHeaderId
	 *            越库发货单单头ID
	 * @return 越库单据头
	 */
	@Override
	public TOCrossDockHeader getToCrossDockHeaderByCDHeaderId(Long cdHeaderId) {
		return toCrossDockHeaderDAO.get(Long.valueOf(cdHeaderId));
	}

	/**
	 * 通过越库单单头及尚需发货LPN构建StockCrossDockDTO
	 * 
	 * @param toCrossDockHeader
	 *            越库单单头
	 * @param needScannedLpns
	 *            及尚需发货LPN
	 * @return CrossDockDTO越库发货明细主DTO
	 */
	@Override
	public CrossDockDTO getStockCrossDockDTOByCDANDLPN(DeliveryOrderHeader toCrossDockHeader, List<String> needScannedLpns,
													   List<String> notNeedScannedLpns) {
		CrossDockDTO stockCrossDockDTO = new CrossDockDTO();
		stockCrossDockDTO.setToCrossDockId(toCrossDockHeader.getId());
		stockCrossDockDTO.setDoNo(toCrossDockHeader.getDoNo());
		stockCrossDockDTO.setStatus(toCrossDockHeader.getStatus());
		stockCrossDockDTO.setRefNo1(toCrossDockHeader.getRefNo1());
		stockCrossDockDTO.setAsnNo(toCrossDockHeader.getRefNo2());
		if(StringUtil.isNotEmpty(toCrossDockHeader.getEdi2())){
			stockCrossDockDTO.setToWhId(toCrossDockHeader.getEdi2());
			stockCrossDockDTO.setToWarehouseName(toCrossDockHeader.getTargetWarehouse().getWarehouseName());
		}
		if(toCrossDockHeader.getBusinessCustomerId() != null){
			stockCrossDockDTO.setToCustomerId(toCrossDockHeader.getBusinessCustomerId());
			stockCrossDockDTO.setToCustomerName(toCrossDockHeader.getBusinessCustomer().getCustomerName());
		}

		if (needScannedLpns != null && needScannedLpns.size() > 0) {
			stockCrossDockDTO.setNeedScannedLpnNo(StringUtil.combine(needScannedLpns, ","));
		}
		if (notNeedScannedLpns != null && notNeedScannedLpns.size() > 0) {
			stockCrossDockDTO.setScannedLpnNo(StringUtil.combine(notNeedScannedLpns, ","));
			if(StringUtil.isEmpty(stockCrossDockDTO.getNeedScannedLpnNo())){
				stockCrossDockDTO.setNeedScannedLpnNo(StringUtil.combine(notNeedScannedLpns, ","));
			}else{
				stockCrossDockDTO.setNeedScannedLpnNo(stockCrossDockDTO.getNeedScannedLpnNo() + 
					"," +StringUtil.combine(notNeedScannedLpns, ","));
			}
		}
		return stockCrossDockDTO;
	}

	/**
	 * 通过越库单单头及尚需发货LPN构建StockCrossDockDTO
	 *
	 * @param toCrossDockHeader
	 *            越库单单头
	 * @param needScannedLpns
	 *            及尚需发货LPN
	 * @return CrossDockDTO越库发货明细主DTO
	 */
	@Override
	public CrossDockDTO getStockCrossDockDTOByCDANDLPN(TOCrossDockHeader toCrossDockHeader, List<String>
			needScannedLpns, List<String> notNeedScannedLpns) {
		CrossDockDTO stockCrossDockDTO = new CrossDockDTO();
		stockCrossDockDTO.setToCrossDockId(toCrossDockHeader.getId());
		stockCrossDockDTO.setDoNo(toCrossDockHeader.getDoNo());
		stockCrossDockDTO.setStatus(toCrossDockHeader.getStatus());
		stockCrossDockDTO.setRefNo1(toCrossDockHeader.getRefNo1());
		stockCrossDockDTO.setAsnNo(toCrossDockHeader.getRefNo2());
			stockCrossDockDTO.setToWhId(toCrossDockHeader.getTargetWarehouseId());
			stockCrossDockDTO.setToWarehouseName(toCrossDockHeader.getWareHouse().getWarehouseName());

		if (needScannedLpns != null && needScannedLpns.size() > 0) {
			stockCrossDockDTO.setNeedScannedLpnNo(StringUtil.combine(needScannedLpns, ","));
		}
		if (notNeedScannedLpns != null && notNeedScannedLpns.size() > 0) {
			stockCrossDockDTO.setScannedLpnNo(StringUtil.combine(notNeedScannedLpns, ","));
			if(StringUtil.isEmpty(stockCrossDockDTO.getNeedScannedLpnNo())){
				stockCrossDockDTO.setNeedScannedLpnNo(StringUtil.combine(notNeedScannedLpns, ","));
			}else{
				stockCrossDockDTO.setNeedScannedLpnNo(stockCrossDockDTO.getNeedScannedLpnNo() +
						"," +StringUtil.combine(notNeedScannedLpns, ","));
			}
		}
		return stockCrossDockDTO;
	}

	/**
	 * 通过发货单ID及明细发货
	 * 
	 * @param toCrossDockId
	 *            越库单单头ID
	 * @param stockCrossDocks
	 *            发货单DTO集合
	 *            发货数量
	 * @return stockCrossDocks 越库发货明细
	 */
	@Override
	@Transactional
	@Loggable
	public void delivery(Long toCrossDockId,List<StockCrossDockDTO> stockCrossDocks, boolean isPacking) {
		// 获取最新越库单数据
		DeliveryOrderHeader toCrossDockHeader = deliveryOrderService.getDoHeaderById(toCrossDockId);
		// 获取最新越库库存数据 ,StockCrossDockDTO与StockCrossDock进行关联
		Map<Long, List<StockSerial>> stockSerialMap = new HashMap<Long, List<StockSerial>>();
		assemblyDeliveryData(stockCrossDocks);
		// 检查越库单状态
		checkCDStatus(toCrossDockHeader.getStatus());
		// 检查发货DTO明细数据
		checkCDDetailStatus(toCrossDockId, toCrossDockHeader.getCrossStockType(), stockCrossDocks, stockSerialMap,
				isPacking);
		// 完成发货
		completeDelivery(toCrossDockHeader, stockCrossDocks, stockSerialMap);
		BigDecimal deliveryAmount = BigDecimal.ZERO;
		for (int i = 0; i < stockCrossDocks.size(); i++) {
			StockCrossDockDTO stockCrossDockDTO = stockCrossDocks.get(i);
			deliveryAmount = deliveryAmount.add(stockCrossDockDTO.getQty());
		}
		// 更新发货单单头
		updateCrossDockHeader(toCrossDockHeader, deliveryAmount);
		//记录日志，已出库
		orderLogService.saveLog(toCrossDockHeader,
				OrderLogConstants.OrderLogType.SHIP_COMPLETE.getValue(),
				ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_MODIFY_SHIP_COMPLETE));
		// 事务内接口调用
		expInDeliveryTransactional(toCrossDockHeader.getId(),toCrossDockHeader.getDoNo(), toCrossDockHeader.getDoType());
	}

	/**
	 * 组装发货相关数据，即越库库存数据
	 * 
	 * @param stockCrossDocks
	 *            越库单状态
	 * 
	 */
	private void assemblyDeliveryData(List<StockCrossDockDTO> stockCrossDocks) {
		for (StockCrossDockDTO stockCrossDockDTO : stockCrossDocks) {
			stockCrossDockDTO.setStockCrossDock(stockCrossDockDAO.get(stockCrossDockDTO.getId()));
		}
	}
	
	/**
	 * 检查发货DTO明细数据，如果此时DTO关联sku为序列号产品，则获取序列号集合并进行校验
	 * 
	 * @param toCrossDockId
	 *            越库单单头ID
	 * @param stockCrossDocks
	 *            发货单DTO集合
	 * @param stockSerialMap
	 *            发货单序列号集合
	 * 
	 */
	private void checkCDDetailStatus(Long toCrossDockId,Integer crossStockType,  List<StockCrossDockDTO>
			stockCrossDocks,Map<Long,
			List<StockSerial>> stockSerialMap,
			boolean isPacking) {
		// 通过越库发货单获取需要扫描发货的LPN，以防在扫描发货中部分LPN状态发生改变
		List<String> needScannedLpns = this.getNeedScannedLpnByCDHeaderId(toCrossDockId,crossStockType, false);
		// 已校验的LPN，用于处理存在多条越库库存记录本次发货时关联同一LPN的问题
		Map<String, String> validateLpns = new HashMap<String, String>();
		for (StockCrossDockDTO stockCrossDockDTO : stockCrossDocks) {
            if (Constants.StockCDStatus.CANCELD.getValue().equals(stockCrossDockDTO.getStatus())) {
                continue;
            }
			StockCrossDock stockCrossDock = stockCrossDockDTO.getStockCrossDock();
			// 检验关联LPN是否正确，如果本次发货LPN数量大于实际应发货LPN数量，则报错
			if (!needScannedLpns.remove(stockCrossDock.getLpnNo()) && validateLpns.get(stockCrossDock.getLpnNo()) == null) {
				throw new DeliveryException(DeliveryException.RELATE_LPN_ERROR);
			} else {
				// 将已扫描LPN关联到校验LPN中
				validateLpns.put(stockCrossDock.getLpnNo(),stockCrossDock.getLpnNo());
			}
			if (stockCrossDockDTO.getQty().compareTo(stockCrossDock.getQty()) != 0) {
				throw new DeliveryException(DeliveryException.NOTALLOW_DELIVERY_BYCHANGEDSTOCKCD);
			}
			if (stockCrossDockDTO.getSn_qty() ==0 || stockCrossDockDTO.getQty().intValue() == 0) {
				continue;
			}
			List<StockSerial> stockSerialList = new ArrayList<StockSerial>();
			for (String serialNumber : stockCrossDockDTO.getSerialNos()) {
				StockSerial serial = stockSerialDAO.findBySerialNo(serialNumber);
				if (serial == null || "0".equals(serial.getQty().toString())) {
					// 关联序列号为number的序列号库存数据不存在或已被使用
					throw new DeliveryException(DeliveryException.RECHECK_SERIAL_ISALREADYUSED,serialNumber);
				} else {
					stockSerialList.add(serial);
				}
			}
			//将获取序列号库存数据组装，最后统一保存调用
			stockSerialMap.put(stockCrossDock.getId(), stockSerialList);
		}
		// 如果关联LPN错误则报错，如果本次发货LPN数量小于实际应发货LPN数量，则报错
		if (!isPacking && needScannedLpns.size() > 0) {
            throw new DeliveryException(DeliveryException.RELATE_LPN_ERROR);
        }
	}

	/**
	 * 检查越库发货单状态，如果不为初始化则不能发货
	 * 
	 * @param cdStatus
	 *            越库单状态
	 * 
	 */
	@Override
	public void checkCDStatus(String cdStatus) {
		if (CrossDockStatus.CANCELED.getValue().equals(cdStatus)) {
			throw new DeliveryException(DeliveryException.NOTALLOW_DELIVERY_BYSDCANCELDCD);
		}
		// 该单据已发货
		if (CrossDockStatus.SHIPED.getValue().equals(cdStatus)) {
			throw new DeliveryException(DeliveryException.NOTALLOW_DELIVERY_BYSHHIPEDCD);
		}
	}

	/**
	 * 检查越库发货单状态，如果不为初始化则不能发货
	 * 
	 * @param stockCDStatus
	 *            越库库存记录状态
	 */
	public void checkStockCDStatus(String stockCDStatus) {
		// 检查是否扫描发货
		if (Constants.StockCDStatus.SHIPPED.getValue().equals(stockCDStatus)) {
			throw new DeliveryException(DeliveryException.NOTALLOW_DELIVERY_BYSCANNEDLPN);
		}
		// 检查是否扫描取消
		if (Constants.StockCDStatus.CANCELD.getValue().equals(stockCDStatus)) {
			throw new DeliveryException(DeliveryException.NOTALLOW_DELIVERY_BYSCANCELDLPN);
		}
		// 检查是否全部转储
		if (Constants.StockCDStatus.ALL_TO_LOCAL.getValue().equals(stockCDStatus)) {
			throw new DeliveryException(DeliveryException.NOTALLOW_DELIVERY_BYSCANCELDLPN);
		}
	}

	/**
	 * 完成发货
	 * 
	 * @param toCrossDockHeader
	 *            越库订单头
	 * @param stockCrossDocks
	 *            越库分录明细
	 * @param stockSerialMap
	 *            越库发货关联越库库存序列号
	 * 
	 */
	@Override
	public void completeDelivery(DeliveryOrderHeader toCrossDockHeader, List<StockCrossDockDTO> stockCrossDocks,
								 Map<Long, List<StockSerial>> stockSerialMap) {
		for (StockCrossDockDTO stockCrossDockDTO : stockCrossDocks) {
            if (Constants.StockCDStatus.CANCELD.getValue().equals(stockCrossDockDTO.getStatus())) {
                continue;
            }
			StockCrossDock stockCrossDock = stockCrossDockDTO.getStockCrossDock();
			StockDTO stockDTO = new StockDTO();
	        stockDTO.setId(stockCrossDock.getId());
	        stockDTO.setActualQty(stockCrossDock.getQty());
			DeliveryOrderDetail toCrossDockDetail = deliveryOrderService.getDoDetail(stockCrossDockDTO.getCddid());
			toCrossDockDetail.setShippedQty(toCrossDockDetail.getShippedQty().add(stockCrossDock.getQty()));
			toCrossDockDetail.setLineStatus(CrossDockStatus.SHIPED.getValue());
			toCrossDockDetailDAO.save(toCrossDockDetail);
			//回写库存
			crossDockDeliveryOperator.setStockDto(stockDTO);
			crossDockDeliveryOperator.execute();
			// 写交易日志
			if(BigDecimal.ZERO.compareTo(stockCrossDockDTO.getQty()) != 0){
				Long transactionId = createTrsShipLogByCrossDock(toCrossDockHeader,toCrossDockDetail, stockCrossDockDTO);
//				if (stockSerialMap.get(stockCrossDock.getId()) == null) {
//					continue;
//				}
//				for (StockSerial stockSerial : stockSerialMap.get(stockCrossDock.getId())) {
//					stockSerial.setQty(0);
//					stockSerial.setIsDeleted(YesNo.YES.getValue());
//					stockSerialDAO.update(stockSerial);
//					createSerialLogByCrossDock(stockSerial, toCrossDockHeader,transactionId);
//				}
			}
		}
	}

	/**
	 * 更新越库单为发货状态
	 * 
	 * @param toCrossDockHeader
	 *            越库库存单据头
	 * @param deliveryAmount
	 *            越库发货数量
	 */
	@Override
	@Transactional
	public void updateCrossDockHeader(DeliveryOrderHeader toCrossDockHeader,BigDecimal deliveryAmount) {
		List<StockCrossDock> stockCrossDockList = stockCrossDockDAO.getNotShipQtyByCDHeaderId(toCrossDockHeader.getSourceAsnId(),
				StringUtil.isEmpty(toCrossDockHeader.getEdi2()) ? null : Long.valueOf(toCrossDockHeader.getEdi2()),
				toCrossDockHeader.getBusinessCustomerId());
		BigDecimal qty = BigDecimal.ZERO;
		for (StockCrossDock stockCrossDock : stockCrossDockList) {
			qty = qty.add(stockCrossDock.getQty());
		}
		if(qty != null && qty.compareTo(BigDecimal.ZERO) > 0){
			toCrossDockHeader.setStatus(CrossDockStatus.PACKING.getValue());
			for (StockCrossDock stockCrossDock : stockCrossDockList) {
				stockCrossDock.setStatus(Constants.StockCDStatus.PACKING.getValue());
				stockCrossDockDAO.save(stockCrossDock);
			}
		}else{
			toCrossDockHeader.setStatus(CrossDockStatus.SHIPED.getValue());
		}
		toCrossDockHeader.setShipTime(DateUtil.getNowTime());
		if(toCrossDockHeader.getShipQty() == null){
			toCrossDockHeader.setShipQty(BigDecimal.ZERO);
		}
		toCrossDockHeader.setShipQty(toCrossDockHeader.getShipQty().add(deliveryAmount));
		deliveryOrderService.saveOrUpdateDoHeader(toCrossDockHeader);
	}

	/**
	 * 越库发货事务内接口调用
	 * 
	 *            越库库存记录状态
	 */
	private void expInDeliveryTransactional(Long crossDockHeaderId,String doNo, String doType) {
		if(CrossDockStatus.SHIPED.getValue().equals(deliveryOrderService.getDoHeaderById(crossDockHeaderId).getStatus())){
            expFacadeService.sendDo2OmsCreateDatas(crossDockHeaderId, Constants.DoStatus.ALL_DELIVER.getValue(), 1, null, doType);
			expFacadeService.sendBatchQty2WmsCreateDatas(crossDockHeaderId, doType);
			expFacadeService.send2OmsCrossDockCreateDatas(crossDockHeaderId, doNo);
//			expFacadeService.send2TmsCrossDockCreateDatas(crossDockHeaderId, doNo);
			stockExpSrv.createCdShipOmsMsg(crossDockHeaderId);
		}
	}

	/**
	 * 通过越库发货单创建序列号交易日志
	 * 
	 * @param serial
	 *            序列号库存记录
	 * @param toCrossDockHeader
	 *            越库发货单单头
	 * @param transactionId
	 *            越库发货交易日志ID
	 */
	@Override
	public void createSerialLogByCrossDock(StockSerial serial, TOCrossDockHeader toCrossDockHeader, Long transactionId) {
		TrsSerialLog log = new TrsSerialLog();
		log.setSerialNo(serial.getSerialNo());
		log.setMerchantId(serial.getMerchantId());
		log.setSupplierId(serial.getSupplierId());
		log.setMerchantId(serial.getMerchantId());
		log.setLocId(serial.getLocId());
		log.setLpnNo(serial.getLpnNo());
		log.setLotId(serial.getLotId());
		log.setSkuId(serial.getSkuId());
		log.setDocNo(toCrossDockHeader.getDoNo());
		log.setDocType(DocType.SO.getValue());
		log.setDocId(toCrossDockHeader.getId());
		log.setTransactionId(transactionId);
		trsService.addSerial4CD(log);
	}

	/**
	 * 通过越库发货单创建发货交易日志
	 * 
	 * @param toCrossDockHeader
	 *            越库发货单单头
	 * @param toCrossDockDetail
	 *            越库发货单明细
	 * @param stockCrossDockDTO
	 *            越库发货明细，用于获取LPN等相关信息
	 *            实际发货数量
	 * 
	 */
	@Override
	public Long createTrsShipLogByCrossDock(DeliveryOrderHeader toCrossDockHeader, DeliveryOrderDetail toCrossDockDetail,
											StockCrossDockDTO stockCrossDockDTO) {
		TrsShipLog trsShipLog = new TrsShipLog();
		trsShipLog.setDocId(toCrossDockHeader.getId());
		trsShipLog.setDocNo(toCrossDockHeader.getDoNo());
		trsShipLog.setDocLineId(toCrossDockDetail.getId());
		trsShipLog.setDocType(DocType.SO.getValue());
		trsShipLog.setFmLotId(stockCrossDockDTO.getLotId());
		trsShipLog.setFmSkuId(toCrossDockDetail.getSku().getId());
		trsShipLog.setFmLockStatus(stockCrossDockDTO.getLocalStauts());
		trsShipLog.setFmLpnNo(stockCrossDockDTO.getLpnNo());
		trsShipLog.setFmLocType(stockCrossDockDTO.getLocType());
		trsShipLog.setFmLocId(stockCrossDockDTO.getLocId());
		trsShipLog.setFmSupplierId(toCrossDockDetail.getSupplier() == null ? 0L	: toCrossDockDetail.getSupplier().getId());
		trsShipLog.setFmPackDetailId(toCrossDockDetail.getPackDetailId());
		trsShipLog.setFmQty(stockCrossDockDTO.getQty());
//		trsShipLog.setFmQtyEach(stockCrossDockDTO.getQty());
		trsShipLog.setMerchantId(StringUtil.isEmpty(toCrossDockDetail.getLotatt06()) ? null : Long.valueOf(toCrossDockDetail.getLotatt06()));
		trsShipLog.setPackId(toCrossDockDetail.getPackageId());
		trsShipLog.setSupplier(toCrossDockDetail.getSupplier());
		trsShipLog.setSku(toCrossDockDetail.getSku());
		trsShipLog.setTransactionType(TrsType.CR_SO.getValue());
		trsShipLog.setToSupplierId(StringUtil.isEmpty(toCrossDockDetail.getLotatt04()) ? null : Long.valueOf(toCrossDockDetail.getLotatt04()));
		trsShipLog.setToSkuId(toCrossDockDetail.getSku().getId());
		trsShipLog.setToLocType(stockCrossDockDTO.getLocType());
		trsShipLog.setToLockStatus(stockCrossDockDTO.getLocalStauts());
		trsShipLog.setToLocId(stockCrossDockDTO.getLocId());
		trsShipLog.setToLotId(stockCrossDockDTO.getLotId());
		trsShipLog.setToLpnNo(stockCrossDockDTO.getLpnNo());
		trsShipLog.setToPackDetailId(toCrossDockDetail.getPackDetailId());
		trsShipLog.setToWhId(StringUtil.isEmpty(toCrossDockHeader.getEdi2()) ? null : Long.valueOf(toCrossDockHeader.getEdi2()));
		trsShipLog.setToQty(stockCrossDockDTO.getQty());
		trsShipLogDAO.save(trsShipLog);
		return trsShipLog.getId();
	}

	private List<StockCrossDockDTO> assemble(List<Object> objs){
		List<StockCrossDockDTO> stockCrossDockDetailDTOs = new ArrayList<StockCrossDockDTO>(
				0);
		// 增加查询关联序列号关系
		for (int i = 0; i < objs.size(); i++) {
			StockCrossDockDTO stockCrossDockDetailDTO = new StockCrossDockDTO();
			stockCrossDockDetailDTO.setId(Long.valueOf(((Object[]) objs.get(i))[0].toString()));
			stockCrossDockDetailDTO.setProductCode(StringUtil.notNullString(((Object[]) objs.get(i))[1]));
			stockCrossDockDetailDTO.setEan13(StringUtil.notNullString(((Object[]) objs.get(i))[2]));
			stockCrossDockDetailDTO.setProductName(StringUtil.notNullString(((Object[]) objs.get(i))[3]));
			stockCrossDockDetailDTO.setQty(((Object[]) objs.get(i))[4] == null ? BigDecimal.ZERO
					: BigDecimal.valueOf(Double.valueOf(((Object[]) objs.get(i))[4].toString())));
			stockCrossDockDetailDTO.setLotatt01(StringUtil.notNullString(((Object[]) objs.get(i))[5]));
			stockCrossDockDetailDTO.setLotatt02(StringUtil.notNullString(((Object[]) objs.get(i))[6]));
			stockCrossDockDetailDTO.setLotatt05(StringUtil.notNullString(((Object[]) objs.get(i))[7]));
			stockCrossDockDetailDTO.setAsnferNo1(StringUtil.notNullString(((Object[]) objs.get(i))[8]));
			stockCrossDockDetailDTO.setLotNo(StringUtil.notNullString(((Object[]) objs.get(i))[9]));
			stockCrossDockDetailDTO.setLpnNo(StringUtil.notNullString(((Object[]) objs.get(i))[10]));
			stockCrossDockDetailDTO.setSn_qty(Long.valueOf(((Object[]) objs.get(i))[11].toString()));
			stockCrossDockDetailDTO.setProdcutType(StringUtil.notNullString(((Object[]) objs.get(i))[12]));
			stockCrossDockDetailDTO.setSkuId(Long.valueOf(((Object[]) objs.get(i))[13].toString()));
			stockCrossDockDetailDTO.setCddid(Long.valueOf(((Object[]) objs.get(i))[14].toString()));
			stockCrossDockDetailDTO.setLocType(StringUtil.notNullString(((Object[]) objs.get(i))[15]));
			stockCrossDockDetailDTO.setLotId(Long.valueOf(((Object[]) objs.get(i))[16].toString()));
			stockCrossDockDetailDTO.setStatus(StringUtil.notNullString(((Object[]) objs.get(i))[17]));
			stockCrossDockDetailDTO.setLocId(Long.valueOf(((Object[]) objs.get(i))[18].toString()));
			if (((Object[]) objs.get(i))[19] != null&& !StringUtil.isEmpty(((Object[]) objs.get(i))[19].toString())) {
				stockCrossDockDetailDTO.setLocalStatus(Integer.valueOf(((Object[]) objs.get(i))[19].toString()));
			}
			stockCrossDockDetailDTOs.add(stockCrossDockDetailDTO);
		}
		return stockCrossDockDetailDTOs;
	}

	/**
	 * 通过LPN号获得发货明细
	 * 
	 *            越库单库存对象
	 * @param isShipped
	 *            是否发货
	 * @return StockCrossDockDTO LPN关联发货信息，如果有序列号，则一起予以显示
	 */
    @Override
	public List<StockCrossDockDTO> getCrossDockDetailDTOByLPN(Long cdHeaderId, String lpnNo,
															  boolean isShipped) {
        List<Object> objs = crossDockDeliverDetailDao.getCrossDockDetailDTOByLPN(cdHeaderId,lpnNo, isShipped);
        return assemble(objs);
    }


	@Override
	public List<StockCrossDockDTO> checkBeforeCrossdockDelivery(Long cdHeaderId) {
		DeliveryOrderHeader toCrossDockHeader = deliveryOrderService.getDoHeaderById(cdHeaderId);
		if (!CrossDockStatus.INITIAL.getValue().equals(toCrossDockHeader.getStatus())) {
			throw new DeliveryException(DeliveryException.CROSS_DOCK_STATUS_ERROR);
		}
		BigDecimal totalShipQty = BigDecimal.ZERO;

		List<StockCrossDockDTO> stockCrossDockDTOs = getCrossDockDetailDTOByCdHeader(cdHeaderId,toCrossDockHeader
				.getCrossStockType());
		for (StockCrossDockDTO stockCrossDockDTO : stockCrossDockDTOs) {
            if (Constants.StockCDStatus.CANCELD.getValue().equals(stockCrossDockDTO.getStatus())) {
                continue;
            }
			if (!Constants.StockCDStatus.INITIAL.getValue().equals(stockCrossDockDTO.getStatus())) {
				throw new DeliveryException(DeliveryException.CROSS_DOCK_STOCK_STATUS_ERROR);
			}
			totalShipQty = totalShipQty.add(stockCrossDockDTO.getQty());
		}
		if (toCrossDockHeader.getExpectedQty().compareTo(totalShipQty)!= 0) {
			throw new DeliveryException(DeliveryException.CROSS_DOCK_STOCK_NUMBER_ERROR);
		}
		return stockCrossDockDTOs;
	}
	@Override
	public List<StockCrossDockDTO> getCrossDockDetailDTOByCdHeader(Long cdHeaderId,Integer crossStockType) {
		List<Object> objs = crossDockDeliverDetailDao.getCrossDockDetailDTOByCdHeader(cdHeaderId,crossStockType);
		return assemble(objs);
	}
	/**
	 * 接口调用异步操作
	 * 
	 * @param toCrossDockId
	 *            越库发货单单头
	 */
	@Override
	@Loggable
	public void expDataToWMSANDOSM(Long toCrossDockId) {
		TOCrossDockHeader toCrossDockHeader = toCrossDockHeaderDAO.get(toCrossDockId);
		//expFacadeService.sendBatchQty2WMS(toCrossDockHeader.getId(), toCrossDockHeader.getDoType());
		expFacadeService.sendBatchQty2WmsCreateDatas(toCrossDockHeader.getId(), toCrossDockHeader.getDoType());
		expFacadeService.send2OmsCrossDock(toCrossDockHeader.getId());
		expFacadeService.send2TmsCrossDock(toCrossDockHeader.getId());
	}
}
