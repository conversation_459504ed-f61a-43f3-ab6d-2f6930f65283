package com.daxia.wms.delivery.invoice.dto;

import java.math.BigDecimal;

/**
 * 航天信息：开具发票返回消息
 * <refp>
 * <RETCODE></RETCODE>
 * <RETMSG></RETMSG>
 * <JE></JE>
 * <SE></SE>
 * <KPRQ></KPRQ>
 * <FPDM></FPDM>
 * <FPHM></FPHM>
 * <SYZFPDM></SYZFPDM>
 * <SYZFPHM></SYZFPHM>
 * <SYZFPZL></SYZFPZL>
 * </refp>
 */
@lombok.extern.slf4j.Slf4j
public class InvoiceRefp {

    public static enum RETCODE {
        KZTYC("-1"),//控制台异常信息
        LJYC("-2"),//连接控制台异常
        IPYC("-3"),//控制台ip或端口号为空
        CG("0");//操作成功

        RETCODE(String value) {
            this.value = value;
        }

        private final String value;

        public String getValue() {
            return value;
        }
    }


    public static enum INVOIC_RETCODE {

        CRSJBHF("4001"),//传入数据不合法
        KPQJSKZTC("4002"),//开票前金税卡状态错
        JSKKPCW("4003"),//金税卡开票调用错误
        KPHQJSKZTC("4004"),//开票后取金税卡状态错
        KPCG("4011"),//开票成功
        KPSB("4012"),//开票失败
        WZDFPQD("5001"),//未找到发票或清单
        DYCG("5011"),//打印成功
        WDY("5012"),//未打印
        DYSB("5013");//打印失败

        INVOIC_RETCODE(String value) {
            this.value = value;
        }

        private final String value;

        public String getValue() {
            return value;
        }
    }

    private String RETCODE;//错误代码 必填
    private String RETMSG;//错误信息,必填
    private BigDecimal JE;//金额
    private BigDecimal SE;//税额
    private String KPRQ;//开票日期
    private String FPZL;//发票种类
    private String FPDM;//发票代码
    private String FPHM;//发票号码
    private String SYZFPDM;//上一张发票代码
    private String SYZFPHM;//上一张发票号码
    private String SYZFPZL;//上一张发票种类

    public String getRETCODE() {
        return RETCODE;
    }

    public void setRETCODE(String RETCODE) {
        this.RETCODE = RETCODE;
    }

    public String getRETMSG() {
        return RETMSG;
    }

    public void setRETMSG(String RETMSG) {
        this.RETMSG = RETMSG;
    }

    public BigDecimal getJE() {
        return JE;
    }

    public void setJE(BigDecimal JE) {
        this.JE = JE;
    }

    public BigDecimal getSE() {
        return SE;
    }

    public void setSE(BigDecimal SE) {
        this.SE = SE;
    }

    public String getKPRQ() {
        return KPRQ;
    }

    public void setKPRQ(String KPRQ) {
        this.KPRQ = KPRQ;
    }

    public String getFPZL() {
        return FPZL;
    }

    public void setFPZL(String FPZL) {
        this.FPZL = FPZL;
    }

    public String getFPDM() {
        return FPDM;
    }

    public void setFPDM(String FPDM) {
        this.FPDM = FPDM;
    }

    public String getFPHM() {
        return FPHM;
    }

    public void setFPHM(String FPHM) {
        this.FPHM = FPHM;
    }

    public String getSYZFPDM() {
        return SYZFPDM;
    }

    public void setSYZFPDM(String SYZFPDM) {
        this.SYZFPDM = SYZFPDM;
    }

    public String getSYZFPHM() {
        return SYZFPHM;
    }

    public void setSYZFPHM(String SYZFPHM) {
        this.SYZFPHM = SYZFPHM;
    }

    public String getSYZFPZL() {
        return SYZFPZL;
    }

    public void setSYZFPZL(String SYZFPZL) {
        this.SYZFPZL = SYZFPZL;
    }
}
