package com.daxia.wms.delivery.recheck.dao;

import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.recheck.entity.CartonHeaderHis;


/**
 * 核拣装箱记录历史DAO
 */
@Name("com.daxia.wms.delivery.cartonHeaderHisDAO")
@lombok.extern.slf4j.Slf4j
public class CartonHeaderHisDAO extends HibernateBaseDAO<CartonHeaderHis, Long> {

    private static final long serialVersionUID = -2052911993234625648L;
    
    /**
     * 根据箱号来进行查询箱头
     * 
     * @param cartonNo
     * @return CartonHeaderHis
     */
    public CartonHeaderHis findByCartonNo(String cartonNo) {
        Criteria cri = getSession().createCriteria(CartonHeaderHis.class);
        cri.add(Restrictions.eq("cartonNo", cartonNo));
        cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
        return (CartonHeaderHis) (cri.setMaxResults(1).uniqueResult());
    }

    
}
