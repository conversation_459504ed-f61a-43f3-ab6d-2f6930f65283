package com.daxia.wms.delivery.container.service;

import com.daxia.dubhe.api.wms.request.PickContainerRequest;
import com.daxia.dubhe.api.wms.response.PickContainerResponse;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.container.entity.PktContainerDetail;
import com.daxia.wms.delivery.container.entity.PktContainerHeader;
import com.daxia.wms.delivery.container.filter.PktContainerDetailFilter;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.master.entity.Container;

import java.math.BigDecimal;
import java.util.List;

public interface PktContainerService {
    public void delByContainerNo(String containerNo);

    DataPage<PktContainerDetail> queryByFilter(PktContainerDetailFilter containerDetailFilter, int startIndex, int pageSize);
    
    void create(Container container, PickTask pickTask, BigDecimal pickedQty, BigDecimal pickedQtyUnit,Long regionId);
    
    void createTemp(WaveHeader waveHeader);

    List<PickContainerResponse.Container> findList4Wcs(PickContainerRequest request);

    /**
     *
     * @param idList
     * @param ignoreBatchFlag   是否忽略批次
     * @return
     */
    List<PickContainerResponse.PickTask> findPickTaskList4Wcs(List<Long> idList, Boolean ignoreBatchFlag);

    void createDetail(PktContainerDetail pktContainerDetail);

    List<PktContainerDetail> findListByFilter(PktContainerDetailFilter containerDetailFilter);

    void updateDetail(PktContainerDetail pktContainerDetail);

    void updateHeader(PktContainerHeader pktContainerHeader);

    PktContainerHeader getPktContainerHeader4Bind(List<Long> regionIds);

    List<String> findChuteListByHeaderId(Long id);

    Integer countPickTaskSize(String regionCode);

    Integer checkContainerChute(String containerNo, String chute);
    
    void delByWave(WaveHeader waveHeader);
}
