package com.daxia.wms.delivery.invoice.dto;

import com.thoughtworks.xstream.annotations.XStreamAlias;

import java.math.BigDecimal;

/**
 * 航天信息：开具发票明细
 * <fpmx>
 * <SPMC></SPMC>
 * <HSBZ></HSBZ>
 * <SLV></SLV>
 * <JE></JE>
 * <DJ></DJ>
 * <JLDW></JLDW>
 * <GGXH></GGXH>
 * <SE></SE>
 * <SL></SL>
 * <BMBBH></BMBBH>
 * <SSFLBM></SSFLBM>
 * <YHZC></YHZC>
 * <YHZCNR></YHZCNR>
 * <LSLBS></LSLBS>
 * <QYZBM></QYZBM>
 * <KCE></KCE>
 * </fpmx>
 */
@XStreamAlias("fpmx")
@lombok.extern.slf4j.Slf4j
public class InvoiceFpmx {
    private String SPMC = "";//商品名称,必填
    private String HSBZ = "";//含税标志,必填 固定值0：不含税 1：含税
    private BigDecimal SLV = BigDecimal.ZERO;//税率,必填 10,6
    private BigDecimal JE = BigDecimal.ZERO;//金额,必填 16,2
    private BigDecimal DJ = BigDecimal.ZERO;//单价,非必填
    private String JLDW = "";//计量单位,非必填
    private String GGXH = "";//规格型号,非必填

    private BigDecimal SE = BigDecimal.ZERO;//税额,必填
    private BigDecimal SL = BigDecimal.ZERO;//数量,非必填
    private String BMBBH = "";//编码版本号,必填 详见国税局网站发布的分类编码表格
    private String SSFLBM = "";//税收分类编码,必填 详见国税局网站发布的分类编码表格
    private String YHZC = "";//是否享受优惠政策,必填 固定值 0：不享受 1：享受
    private String YHZCNR = "";//享受优惠政策内容,非必填 如果不享受优惠政策，该字段为空；如果享受优惠政策，该字段不能为空，内容填写为对应的优惠政策
    private String LSLBS = "";//零税率标识,非必填 固定值 空：非零税率 0：出口退税 1：免税 2：不征收 3：普通零税率
    private String QYZBM = "";//企业自编码,非必填 企业内部自编的商品编码，可以为空
    private String KCE = "";//扣除额,非必填 差额税使用，非差额税可以为空

    public String getSPMC() {
        return SPMC;
    }

    public void setSPMC(String SPMC) {
        this.SPMC = SPMC;
    }

    public String getHSBZ() {
        return HSBZ;
    }

    public void setHSBZ(String HSBZ) {
        this.HSBZ = HSBZ;
    }

    public BigDecimal getSLV() {
        return SLV;
    }

    public void setSLV(BigDecimal SLV) {
        this.SLV = SLV;
    }

    public BigDecimal getJE() {
        return JE;
    }

    public void setJE(BigDecimal JE) {
        this.JE = JE;
    }

    public BigDecimal getDJ() {
        return DJ;
    }

    public void setDJ(BigDecimal DJ) {
        this.DJ = DJ;
    }

    public String getJLDW() {
        return JLDW;
    }

    public void setJLDW(String JLDW) {
        this.JLDW = JLDW;
    }

    public String getGGXH() {
        return GGXH;
    }

    public void setGGXH(String GGXH) {
        this.GGXH = GGXH;
    }

    public BigDecimal getSE() {
        return SE;
    }

    public void setSE(BigDecimal SE) {
        this.SE = SE;
    }

    public BigDecimal getSL() {
        return SL;
    }

    public void setSL(BigDecimal SL) {
        this.SL = SL;
    }

    public String getBMBBH() {
        return BMBBH;
    }

    public void setBMBBH(String BMBBH) {
        this.BMBBH = BMBBH;
    }

    public String getSSFLBM() {
        return SSFLBM;
    }

    public void setSSFLBM(String SSFLBM) {
        this.SSFLBM = SSFLBM;
    }

    public String getYHZC() {
        return YHZC;
    }

    public void setYHZC(String YHZC) {
        this.YHZC = YHZC;
    }

    public String getYHZCNR() {
        return YHZCNR;
    }

    public void setYHZCNR(String YHZCNR) {
        this.YHZCNR = YHZCNR;
    }

    public String getLSLBS() {
        return LSLBS;
    }

    public void setLSLBS(String LSLBS) {
        this.LSLBS = LSLBS;
    }

    public String getQYZBM() {
        return QYZBM;
    }

    public void setQYZBM(String QYZBM) {
        this.QYZBM = QYZBM;
    }

    public String getKCE() {
        return KCE;
    }

    public void setKCE(String KCE) {
        this.KCE = KCE;
    }
}
