package com.daxia.wms.delivery.pick.dto;

import com.daxia.wms.delivery.pick.entity.PickTask;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by mason on 2017/4/15.
 */
@lombok.extern.slf4j.Slf4j
public class BatchPickDTO implements Serializable {

    private PickTask pickTask;
    private BigDecimal pickedQty;
    private BigDecimal pickedQtyUnit;

    public PickTask getPickTask() {
        return pickTask;
    }

    public void setPickTask(PickTask pickTask) {
        this.pickTask = pickTask;
    }

    public BigDecimal getPickedQty() {
        return pickedQty;
    }

    public void setPickedQty(BigDecimal pickedQty) {
        this.pickedQty = pickedQty;
    }

    public BigDecimal getPickedQtyUnit() {
        return pickedQtyUnit;
    }

    public void setPickedQtyUnit(BigDecimal pickedQtyUnit) {
        this.pickedQtyUnit = pickedQtyUnit;
    }
}
