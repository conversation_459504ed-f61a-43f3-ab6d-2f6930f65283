package com.daxia.wms.delivery.recheck.dto;

import java.math.BigDecimal;

/**
 * 对应一条核拣记录，该记录与定单中的商品是一对一的关系
 */
@lombok.extern.slf4j.Slf4j
public class CrossReCheckRecord {
	private Long crossDetailId;
	/**
	 * 订单号
	 */
	private String doNo;

	private String containerNo;

	private String consigneeName;
	/**
	 * 商品编号
	 */
	private String productCode;
	/**
	 * 商品条码
	 */
	private String productBarCode;
	private String productName;
	private String udf4;

	/**
	 * 商品计数单位
	 */
	private String unit;

	/**
	 * 应核拣数
	 */
	private BigDecimal needCheckNumber;
	/**
	 * 已核拣数
	 */
	private BigDecimal finishedCheckNumber;

	private String effectiveDate;//生效日期

	private String expirationDate;//失效日期

	private String batchNo;//批号

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getProductBarCode() {
		return productBarCode;
	}

	public void setProductBarCode(String productBarCode) {
		this.productBarCode = productBarCode;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

    public String getUdf4() {
        return udf4;
    }

    public void setUdf4(String udf4) {
        this.udf4 = udf4;
    }

    public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public BigDecimal getNeedCheckNumber() {
		return needCheckNumber;
	}

	public void setNeedCheckNumber(BigDecimal needCheckNumber) {
		this.needCheckNumber = needCheckNumber;
	}

	public BigDecimal getFinishedCheckNumber() {
		return finishedCheckNumber;
	}

	public void setFinishedCheckNumber(BigDecimal finishedCheckNumber) {
		this.finishedCheckNumber = finishedCheckNumber;
	}

	/**
	 * @return 待核拣商品数量
	 */
	public BigDecimal getPendingCheckNumber() {
		return this.needCheckNumber.subtract(this.finishedCheckNumber);
	}

	/**
	 * 增加该记录对应商品的已核拣数量
	 *
	 * @param number
	 */
	public void addCheckNumber(BigDecimal number) {
		this.finishedCheckNumber = this.finishedCheckNumber.add(number);
	}

	/**
	 * 减少该记录对应商品的已核拣数量
	 *
	 * @param number
	 */
	public void decreaseCheckNumber(BigDecimal number) {
		this.finishedCheckNumber = this.finishedCheckNumber.subtract(number);
	}

	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}

	public String getContainerNo() {
		return containerNo;
	}

	public void setContainerNo(String containerNo) {
		this.containerNo = containerNo;
	}

	public String getConsigneeName() {
		return consigneeName;
	}

	public void setConsigneeName(String consigneeName) {
		this.consigneeName = consigneeName;
	}

	public String getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(String effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public String getExpirationDate() {
		return expirationDate;
	}

	public void setExpirationDate(String expirationDate) {
		this.expirationDate = expirationDate;
	}

	public String getBatchNo() {
		return batchNo;
	}

	public void setBatchNo(String batchNo) {
		this.batchNo = batchNo;
	}

	public Long getCrossDetailId() {
		return crossDetailId;
	}

	public void setCrossDetailId(Long crossDetailId) {
		this.crossDetailId = crossDetailId;
	}
}
