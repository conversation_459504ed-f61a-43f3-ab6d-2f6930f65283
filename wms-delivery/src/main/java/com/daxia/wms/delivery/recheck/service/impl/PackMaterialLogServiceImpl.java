package com.daxia.wms.delivery.recheck.service.impl;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;


import com.daxia.wms.delivery.recheck.dao.PackMaterialLogDAO;
import com.daxia.wms.delivery.recheck.entity.PackMaterialLog;
import com.daxia.wms.delivery.recheck.service.PackMaterialLogService;

/**
 * 包材推荐日志service
 */
@Name("com.daxia.wms.delivery.packMaterialLogService")
@lombok.extern.slf4j.Slf4j
public class PackMaterialLogServiceImpl implements PackMaterialLogService {
	
	@In
	private  PackMaterialLogDAO packMaterialLogDAO;
	
	@Override
	public PackMaterialLog getByDoId(Long doId) {
		return packMaterialLogDAO.getByDoId(doId);
	}

	@Override
	@Transactional
	public void savePackMaterialLog(PackMaterialLog log) {
		packMaterialLogDAO.save(log);
	}
	
	@Override
	@Transactional
	public void update(PackMaterialLog log) {
		packMaterialLogDAO.update(log);
	}
}
