package com.daxia.wms.delivery.crossorder.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.wms.delivery.crossorder.entity.CrossSeedDetail;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

@Name("com.daxia.wms.delivery.crossSeedDetailAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class CrossSeedDetailAction extends PagedListBean<CrossSeedDetail> {

    private static final long serialVersionUID = -8504837298538640360L;

    @Override
    public void query() {

    }
}
