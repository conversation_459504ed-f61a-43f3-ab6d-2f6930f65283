package com.daxia.wms.delivery.recheck.job;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.delivery.deliveryorder.job.TempCartonTask;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.impl.DeliveryOrderServiceImpl;
import com.daxia.wms.delivery.recheck.entity.TempCarton;
import com.daxia.wms.delivery.recheck.service.TempCartonService;
import com.daxia.wms.delivery.recheck.service.impl.TempCartonServiceImpl;
import com.daxia.wms.master.job.WarehouseSimpleJob;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * 电子面单预下单定时任务
 */
@Name("tempCartonGenerateElasticJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class TempCartonGenerateElasticJob extends WarehouseSimpleJob {

    private static final int DEFAULT_BATCH_NUM = 200;

    @Override
    protected void doRun() throws InterruptedException {
        TempCartonService tempCartonService = ((TempCartonService) Component.getInstance(TempCartonServiceImpl.class));
        DeliveryOrderService deliveryOrderService = ((DeliveryOrderService) Component.getInstance(DeliveryOrderServiceImpl.class));
        int batchNum = DEFAULT_BATCH_NUM;
        //查询需要预下单的订单
        //1.订单状态为 已审核 到 分拣完成
        //2.不存在已经下单成功的tempCarton，或失败次数小于3（重调）
        Long warehouseId = ParamUtil.getCurrentWarehouseId();
        List<Long> doIdList = deliveryOrderService.findList4TempCarton(batchNum * getMultiThreadNum(), warehouseId, false, jobParameter);
        if (CollectionUtils.isNotEmpty(doIdList)) {
            int threadNum = (doIdList.size() + batchNum - 1) / batchNum;
            if (threadNum <= 1) {
                TempCarton tc;
                for (Long doId : doIdList) {
                    tc = tempCartonService.generateTempCarton(doId);
                    if (tc != null) {
                        tempCartonService.saveOrUpdate(tc);
                    }
                }
            } else {
                // 多线程下
                log.debug("Multiple threads tempCartonGenerateJob with threadNum: " + threadNum);
                List<List<Long>> partitions = Lists.partition(doIdList, batchNum);
                CountDownLatch threadSignal = new CountDownLatch(threadNum);
                for (int i = 0; i < partitions.size(); i++) {
                    new TempCartonTask(i, partitions.get(i), threadSignal, warehouseId).start();
                }
                threadSignal.await();
            }
        }
        //本地单号池
        doIdList = deliveryOrderService.findList4TempCarton(batchNum, warehouseId, true, jobParameter);
        if (CollectionUtils.isNotEmpty(doIdList)) {
            TempCarton tc;
            for (Long doId : doIdList) {
                tc = tempCartonService.generateTempCarton(doId);
                if (tc != null) {
                    tempCartonService.saveOrUpdate(tc);
                }
            }
        }
    }

    private int getMultiThreadNum() {
        Integer nThreads = SystemConfig.getConfigValueInt("delivery.tempCartonGenerateJob.multiThreadNum", null);
        if (nThreads == null || nThreads < 1 || nThreads > 10) {
            nThreads = 1;
        }
        return nThreads;
    }

    public List<Long> findTempCartonWhIds() {
        String whIds = SystemConfig.getConfigValue("waybill.generateTempCarton.whIds");
        if (StringUtil.isEmpty(whIds)) {
            return null;
        }
        List<Long> whIdList = new ArrayList<Long>();
        for (String s : whIds.split(",")) {
            whIdList.add(Long.valueOf(s));
        }
        return whIdList;
    }


}
