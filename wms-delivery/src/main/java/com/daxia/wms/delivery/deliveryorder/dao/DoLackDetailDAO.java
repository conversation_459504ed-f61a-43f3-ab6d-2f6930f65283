package com.daxia.wms.delivery.deliveryorder.dao;

import java.math.BigDecimal;
import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.deliveryorder.entity.DoLackDetail;
import com.daxia.wms.Constants.ReleaseStatus;
import com.daxia.wms.Constants.YesNo;

@Name("com.daxia.wms.delivery.doLackDetailDAO")
@lombok.extern.slf4j.Slf4j
public class DoLackDetailDAO extends HibernateBaseDAO<DoLackDetail, Long> {

    private static final long serialVersionUID = -8638811105895866683L;

    /**
     * 获取某个商品的缺货总数量
     * 
     * @param skuId
     * @return
     */
    public BigDecimal getSkuLackQty(Long skuId) {
        return (BigDecimal) this
                .createQuery(
                        "SELECT SUM(dd.qty) FROM DoLackDetail dd WHERE dd.skuId = :skuId "
                                + " and dd.warehouseId = :warehouseId")
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).setParameter("skuId", skuId)
                .uniqueResult();
    }
    
    /**
     * 获取某个商品的缺货总数量
     * 
     * @param skuId
     * @return
     */
    public BigDecimal findTotalLack(Long skuId) {
        return (BigDecimal) this
                .createQuery(
                        "SELECT SUM(dd.qty) FROM DoLackDetail dd, DeliveryOrderHeader do "
                                + " WHERE dd.doHeaderId = do.id " + " and do.releaseStatus = :releaseStatus"
                                + " and dd.skuId = :skuId"+" and dd.warehouseId = :warehouseId and do.warehouseId = :warehouseId ")
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId())
                .setParameter("releaseStatus", ReleaseStatus.HOLD.getValue()).setParameter("skuId", skuId)
                .uniqueResult();
    }
    
    /**
     * 逻辑删除该订单下所有的缺货明细
     * @param doHeaderId
     * @return
     */
    public int removeLackDoDetail(Long doHeaderId){
		String hql="update DoLackDetail dd set dd.isDeleted = :deleted WHERE dd.doHeaderId = :doHeaderId " +
				" and dd.warehouseId = :warehouseId ";
		Query query = this.getSession().createQuery(hql);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setLong("doHeaderId", doHeaderId);
		query.setInteger("deleted", YesNo.YES.getValue());
		return query.executeUpdate();
	}
    
    /**
     * doHeaderId、doDetailId、skuId、locId4个条件联合查询缺货明细；
     * @param doHeaderId
     * @param doDetailId
     * @param skuId
     * @param locId
     * @return
     */
    public DoLackDetail findLackDoDetail(Long doHeaderId,Long doDetailId,Long skuId,Long locId){
		String hql="from DoLackDetail dd WHERE dd.doHeaderId = :doHeaderId and dd.doDetailId = :doDetailId and dd.skuId = :skuId and dd.locId = :locId " +
				" and dd.warehouseId = :warehouseId ";
		Query query = this.getSession().createQuery(hql);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setLong("doHeaderId", doHeaderId);
		query.setLong("doDetailId", doDetailId);
		query.setLong("locId", locId);
		query.setLong("skuId", skuId);
		return  (DoLackDetail)query.uniqueResult();
	}
    
    /**
     * 根据商品和发货明细查询总缺货数
     * @param doDetailId
     * @param skuId
     * @return
     */
    public BigDecimal findTotalByDoDetailSku(Long doDetailId, Long skuId) {
        String sql="select sum(ifnull(dd.qty,0)) from do_lack_detail dd WHERE  dd.do_Detail_Id = :doDetailId and dd.sku_Id = :skuId and dd.warehouse_Id = :warehouseId "
        		+ " AND dd.IS_DELETED = 0 ";
        Query query = this.createSQLQuery(sql);
        query.setLong("doDetailId", doDetailId);
        query.setLong("skuId", skuId);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        Object obj = query.uniqueResult();
        return obj == null ? BigDecimal.ZERO : ((BigDecimal)obj);
    }
    
	/**
	 * 计算DO下某sku的总缺货数
	 * 
	 * @param doDetailId
	 * @param skuId
	 * @return
	 */
	public BigDecimal countLackSkuInTotal(Long doId, Long skuId) {
		String sql = "select sum(ifnull(dd.qty,0)) from do_lack_detail dd WHERE  dd.DO_HEADER_ID = :doId and dd.SKU_ID = :skuId and dd.warehouse_Id = :warehouseId "
				+ " AND dd.IS_DELETED = 0 ";
		Query query = this.createSQLQuery(sql);
		query.setLong("doId", doId);
		query.setLong("skuId", skuId);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		Object obj = query.uniqueResult();
		return obj == null ? BigDecimal.ZERO : ((BigDecimal) obj);
	}
    
    /**
     * 查询发货单缺指定sku总数
     * @param doHeaderId
     * @param skuId
     * @return
     */
    public BigDecimal findTotalByDoSku(Long doHeaderId, Long skuId) {
        String hql="select sum(COALESCE(dd.qty,0)) from DoLackDetail dd WHERE  dd.doHeaderId = :doHeaderId and dd.skuId = :skuId and dd.warehouseId = :warehouseId ";
        Query query = this.createQuery(hql);
        query.setLong("doHeaderId", doHeaderId);
        query.setLong("skuId", skuId);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        Object obj = query.uniqueResult();
        return obj == null ? BigDecimal.ZERO : ((BigDecimal)obj);
    }
    
    /**
     * 根据发货单号查询缺货明细
     * @param doHeaderId
     * @return
     */
    @SuppressWarnings("unchecked")
	public List<DoLackDetail> findDoLackDetail(Long doHeaderId) {
    	String hql = "from DoLackDetail o where o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId";
    	Query query = this.createQuery(hql)
    			.setLong("doHeaderId", doHeaderId)
    			.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
    	return  query.list();
    }
    
    /**
     * 查询某个Do的缺货数量
     * @param doHeaderId
     * @return
     */
    public BigDecimal getDoLackQty(Long doHeaderId) {
        BigDecimal lackQty = (BigDecimal) this
                .createQuery(
                        "SELECT SUM(dd.qty) FROM DoLackDetail dd WHERE dd.doHeaderId = :doHeaderId "
                                + " and dd.warehouseId = :warehouseId")
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).setParameter("doHeaderId", doHeaderId)
                .uniqueResult();
        return lackQty == null ? BigDecimal.ZERO : lackQty;
    }
    
    /**
     * 查询订单的缺货 sku数 （去重）
     * @param doIds
     * @return
     */
    public BigDecimal findDisTSkuByDoNos(List<Long> doIds) {
        String hql = "select count(distinct o.skuId) from DoLackDetail o where o.doHeaderId in (:doIds) and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql)
                .setParameterList("doIds", doIds)
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return BigDecimal.valueOf(((Long) query.uniqueResult()).longValue());
    }

    /**
     * 查询订单的缺货总数
     * @param doIds
     * @return
     */
    public BigDecimal findTotalCountByDos(List<Long> doIds) {
        String hql = "select sum(o.qty) from DoLackDetail o where o.doHeaderId in (:doIds) and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql)
                .setParameterList("doIds", doIds)
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (BigDecimal) query.uniqueResult();
    }
    
    /**
     * 根据波次id查询波次下面所有破损的数量
     * @param waveId
     * @return
     */
    public BigDecimal getDoLackQtyByWaveId (Long waveId) {
		StringBuffer hql = new StringBuffer();
		hql.append("select sum(dl.qty) ")
		   .append(" from WaveHeader wh,DeliveryOrderDetail od,DeliveryOrderHeader oh,DoLackDetail dl,DoLackHeader dh") 
		   .append(" where wh.id = oh.waveId and oh.id = od.doHeaderId and oh.id = dl.doHeaderId")
		   .append(" and dh.doHeaderId = dl.doHeaderId")
		   .append(" and od.id = dl.doDetailId and wh.id = :waveId")
		   .append(" and wh.warehouseId = :warehouseId and od.warehouseId = :warehouseId")
		   .append(" and dl.warehouseId = :warehouseId and oh.warehouseId = :warehouseId");
		Query query = this.createQuery(hql.toString());
		query.setParameter("waveId", waveId);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		return (BigDecimal) query.uniqueResult();
    }
}
