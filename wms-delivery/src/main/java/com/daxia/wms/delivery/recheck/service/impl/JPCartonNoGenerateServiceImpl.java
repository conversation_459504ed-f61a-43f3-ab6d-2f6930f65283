package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.MvelUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.waybill.WaybillException;
import com.daxia.wms.waybill.jd.JdWaybillSendDto;
import com.google.common.collect.ImmutableMap;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.io.IOException;
import java.math.BigDecimal;

@Name("jPCartonNoGenerateService")
@lombok.extern.slf4j.Slf4j
public class JPCartonNoGenerateServiceImpl implements CartonNoGenerateService {

    @In
    private SequenceGeneratorService sequenceGeneratorService;
//    @In(create = true)
//    private JpWaybillService jpWaybillService;

    @In
    private WarehouseService warehouseService;

    @In
    private CartonService cartonService;

    @In
    private WarehouseCarrierService warehouseCarrierService;

    @Override
    public void generatorCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
//        //京东已经产生过箱子的，不再下单
//        if (CollectionUtils.isNotEmpty(cartonService.findByDoId(doHeader.getId()))) {
//            String orderNo = doHeader.getDoNo();
//            if (StringUtil.isEmpty(orderNo)) {
//                throw new DeliveryException(DeliveryException.DO_NO_IS_NULL);
//            }
//            String cartonNo = orderNo + sequenceGeneratorService.generateSequenceNo(SequenceName.CARTONNO.getValue(), ParamUtil.getCurrentWarehouseId());
//            cartonHeader.setCartonNo(cartonNo);
//            return;
//        }
        WarehouseCarrier warehouseCarrier = warehouseCarrierService.getCarrierInfoByWarehouseIdAndCarrierIdAndType(ParamUtil.getCurrentWarehouseId(), doHeader.getCarrierId(),Constants.WaybillType.JP.name());
        if (warehouseCarrier == null) {
            throw new WaybillException(WaybillException.WAREHOUSE_CARRIER_NOT_EXITS);
        }
        Warehouse warehouse = warehouseService.getWarehouse(doHeader.getWarehouseId());
        StringBuffer address = new StringBuffer();
        address.append(doHeader.getProvinceInfo() == null ? doHeader.getProvinceName() : doHeader.getProvinceInfo().getProvinceCname())
                .append(doHeader.getCityInfo() == null ? doHeader.getCityName() : doHeader.getCityInfo().getCityCname())
                .append(doHeader.getCountyInfo() == null ? doHeader.getCountyName() : doHeader.getCountyInfo().getCountyCname())
                .append(doHeader.getAddress());

        JdWaybillSendDto dto = new JdWaybillSendDto();
        //根据店铺判断京东快递平台
        String platScript = SystemConfig.getConfigValue("jdwaybill.plat.script", ParamUtil.getCurrentWarehouseId());
        if (StringUtil.isBlank(platScript)) {
            // 默认为京东平台
            dto.setSalePlat(JdWaybillSendDto.JD_PLAT);
            dto.setThrOrderId(doHeader.getOriginalSoCode());
        } else {
            String plat = (String) MvelUtil.eval(platScript, ImmutableMap.of("shopId", (Object) (doHeader.getShopId() == null ? -100L : doHeader.getShopId())));
            dto.setThrOrderId(doHeader.getOriginalSoCode());
            dto.setSalePlat(plat);
        }
//        String index = CollectionUtils.isEmpty(doHeader.getCartonHeaders()) ? "1" : String.valueOf(doHeader.getCartonHeaders().size() + 1);
//        dto.setOrderId(doHeader.getDoNo() + "_" + index);
        dto.setOrderId(sequenceGeneratorService.generateSequenceNo(Constants.SequenceName.CARTONNO.getValue(), ParamUtil.getCurrentWarehouseId()));
        dto.setSenderName(warehouse.getWarehouseName());
        dto.setSenderProvinceName(warehouse.getProvince().getProvinceCname());
        dto.setSenderCityName(warehouse.getCity().getCityCname());
        dto.setSenderCountyName(warehouse.getCounty().getCountyCname());
        dto.setSenderAddress(warehouse.getProvince().getProvinceCname()+warehouse.getCity().getCityCname()+
                warehouse.getCounty().getCountyCname()+warehouse.getAddressName());
        dto.setSenderTel(warehouse.getPhone());
        dto.setReceiveName(doHeader.getConsigneeName());
        dto.setReceiveAddress(address.toString());
        dto.setReceiveProvinceName(doHeader.getProvinceInfo() == null ? doHeader.getProvinceName() : doHeader.getProvinceInfo().getProvinceCname());
        dto.setReceiveCityName(doHeader.getCityInfo() == null ? doHeader.getCityName() : doHeader.getCityInfo().getCityCname());
        dto.setReceiveCountyName(doHeader.getCountyInfo() == null ? doHeader.getCountyName() : doHeader.getCountyInfo().getCountyCname());
        dto.setReceiveTel(DoUtil.decryptPhone(doHeader.getTelephone()));
        dto.setReceiveMobile(DoUtil.decryptPhone(doHeader.getMobile()));
        dto.setPackageCount(1);
        dto.setWeight(doHeader.getGrossWt().doubleValue());
        dto.setVloumn(0);
        dto.setOrderAmount(doHeader.getOrderAmount());
        dto.setCollectionMoney(doHeader.getReceivable().doubleValue());
        if (doHeader.getReceivable().compareTo(BigDecimal.ZERO) > 0) {
            dto.setCollectionValue(1);
        } else {
            dto.setCollectionValue(0);
        }

        //保价逻辑
        String jdGuaranteeScript = SystemConfig.getConfigValue("delivery.waybill.jdGuaranteeScript", ParamUtil
                .getCurrentWarehouseId());
        try {
            if (StringUtil.isNotEmpty(jdGuaranteeScript) && Boolean.TRUE.equals(MvelUtil.eval(jdGuaranteeScript,
                    ImmutableMap.of("doHeader", (Object) doHeader)))) {
                dto.setGuaranteeFlag(Constants.YesNo.YES.getValue());
                dto.setGuaranteeMoney(doHeader.getOrderAmount().doubleValue());
            }
        } catch (Exception e) {

        }

        String waybill = null;
//        try {
//            String [] result = jpWaybillService.getWaybill(dto, warehouseCarrier);
//            waybill = result[0];
//            if(StringUtil.isBlank(waybill)){
//                throw new WaybillException(result[1]);
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
        cartonHeader.setCartonNo(waybill);
        cartonHeader.setWayBill(waybill);
    }
}
