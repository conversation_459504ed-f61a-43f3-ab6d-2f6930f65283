package com.daxia.wms.delivery.task.repick.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.system.util.WmsUtil;
import com.daxia.wms.delivery.task.repick.entity.ReversePickContainer;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import java.util.List;

@Name("com.daxia.wms.delivery.reversePickContainerDAO")
@lombok.extern.slf4j.Slf4j
public class ReversePickContainerDAO extends HibernateBaseDAO<ReversePickContainer,Long> {
    public void createByCarton(Long doId) {
        String sql = "INSERT INTO md_container_reverse(do_header_id, container_no, type, warehouse_id, create_by, create_time, update_by, update_time) " +
                "SELECT DISTINCT o.do_header_id, o.carton_no, o.ext_1, warehouse_id, :creator, now(), :creator, now() FROM doc_carton_header o " +
                "WHERE o.do_header_id = :doId AND o.is_deleted = 0 AND o.warehouse_id = :warehouseId";
        Query sqlQuery = this.createSQLQuery(sql).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).setParameter("creator", WmsUtil.getOperateBy()).
                setParameter("doId", doId);
        sqlQuery.executeUpdate();
    }
    
    public void createByPick(List<Long> pickTaskId) {
        String sql = "INSERT INTO md_container_reverse(do_header_id, container_no, type, warehouse_id, create_by, create_time, update_by, update_time) " +
                "SELECT DISTINCT o.doc_id, o.container_no, IF(ph.pkt_type = 1, 'B', 'C'), o.warehouse_id, :creator, now(), :creator, now() FROM tsk_pick o " +
                "INNER JOIN doc_pkt_header ph on o.pkt_h_id = ph.id " +
                "LEFT JOIN md_container_reverse cr ON cr.do_header_id = o.doc_id AND cr.is_deleted = 0 AND cr.type = IF(ph.pkt_type = 1, 'B', 'C') " +
                "WHERE cr.id is null AND o.id in (:pickTaskId) AND o.is_deleted = 0 AND o.warehouse_id = :warehouseId AND o.container_no is not null";
        Query sqlQuery = this.createSQLQuery(sql).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).setParameter("creator", WmsUtil.getOperateBy()).
                setParameterList("pickTaskId", pickTaskId);
        sqlQuery.executeUpdate();
    }
    
    public void createByPick(Long doHeaderId) {
        String sql = "INSERT INTO md_container_reverse(do_header_id, container_no, type, warehouse_id, create_by, create_time, update_by, update_time) " +
                "SELECT DISTINCT o.doc_id, o.container_no, IF(ph.pkt_type = 1, 'B', 'C'), o.warehouse_id, :creator, now(), :creator, now() FROM tsk_pick o " +
                "INNER JOIN doc_pkt_header ph on o.pkt_h_id = ph.id " +
                "LEFT JOIN md_container_reverse cr ON cr.do_header_id = o.doc_id AND cr.is_deleted = 0 AND cr.type = IF(ph.pkt_type = 1, 'B', 'C') " +
                "WHERE cr.id is null AND o.doc_id = :doHeaderId AND o.is_deleted = 0 AND o.warehouse_id = :warehouseId AND o.container_no is not null";
        Query sqlQuery = this.createSQLQuery(sql).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).setParameter("creator", WmsUtil.getOperateBy()).
                setParameter("doHeaderId", doHeaderId);
        sqlQuery.executeUpdate();
    }
    
    public void bindRepickHeader(String rePickType, Long doId, Long rePickHeaderId) {
        String sql="UPDATE md_container_reverse SET repick_header_id = :rePickHeaderId WHERE " +
                "is_deleted = 0 AND warehouse_id = :warehouseId AND do_header_id = :doId AND type = :rePickType";
        this.createSQLQuery(sql).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId()).setParameter("rePickHeaderId", rePickHeaderId).setParameter("doId", doId)
                .setParameter("rePickType", rePickType).executeUpdate();
    }
}
