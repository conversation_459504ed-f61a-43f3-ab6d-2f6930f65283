package com.daxia.wms.delivery.pick.action;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.filter.PickHeaderFilter;
import com.daxia.wms.delivery.pick.service.PickHeaderService;
import com.daxia.wms.Constants.PktMergeStatus;

/**
 * 拣货单Action
 */
@Name("com.daxia.wms.delivery.pktAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class PktAction extends PagedListBean<PickHeader> {
	
	private static final long serialVersionUID = -3105842460469767185L;
	@In
	private PickHeaderService pickHeaderService;
	@In
    private DeliveryOrderService deliveryOrderService;
	private boolean initialized = false;
	private Long doDetailId;
    private Long doHeaderId;
    private Long waveId;
    private Boolean isMerged;
	private PickHeaderFilter pktFilter;
    private PickHeader pktHeader;
	private DeliveryOrderHeader doHeader;
	private DeliveryOrderDetail doDetail;
	
	public PktAction() {
		super();
		this.pktFilter = new PickHeaderFilter();
	}
	
	/**
	 * 查询
	 */
	@Override
	public void query() {
		
	}
	
	/**
     * <pre>
     * Description:初始化页面
     * </pre>
     *
     */
    public void initializePage(){
        if(!initialized){
        	this.doDetail = this.deliveryOrderService.getDoDetail(doDetailId);
        	this.doHeader = this.doDetail.getDoHeader();
        	this.initialized = true;
        }
    }
    
    /**
     * <pre>
     * Description:初始化拣货页面
     * </pre>
     *
     */
	public void initPktHeaderPage() {
		if (!initialized) {
			this.pktFilter.setWaveId(this.waveId);
			if(isMerged != null){
			    if(isMerged){
			        this.pktFilter.setMergeStatus(Integer.valueOf(PktMergeStatus.MERGED.getValue()));
			    }else{
	                this.pktFilter.setMergeStatus(Integer.valueOf(PktMergeStatus.NO_MERGE.getValue()));
			    }
			}
			DataPage<PickHeader> dataPage = pickHeaderService.findPktHeaders(pktFilter, this.getStartIndex(), this.getPageSize());
			this.populateValues(dataPage);
			this.initialized = true;
		}
	}

	public PickHeaderFilter getPktFilter() {
		return pktFilter;
	}

	public void setPktFilter(PickHeaderFilter pktFilter) {
		this.pktFilter = pktFilter;
	}

	public PickHeader getPktHeader() {
		return pktHeader;
	}

	public void setPktHeader(PickHeader pktHeader) {
		this.pktHeader = pktHeader;
	}

	public DeliveryOrderHeader getDoHeader() {
		return doHeader;
	}

	public void setDoHeader(DeliveryOrderHeader doHeader) {
		this.doHeader = doHeader;
	}

	public boolean isInitialized() {
		return initialized;
	}

	public void setInitialized(boolean initialized) {
		this.initialized = initialized;
	}

	public DeliveryOrderService getDeliveryOrderService() {
		return deliveryOrderService;
	}

	public void setDeliveryOrderService(DeliveryOrderService deliveryOrderService) {
		this.deliveryOrderService = deliveryOrderService;
	}

	public Long getDoDetailId() {
		return doDetailId;
	}

	public void setDoDetailId(Long doDetailId) {
		this.doDetailId = doDetailId;
	}

	public Long getDoHeaderId() {
		return doHeaderId;
	}

	public void setDoHeaderId(Long doHeaderId) {
		this.doHeaderId = doHeaderId;
	}

	public DeliveryOrderDetail getDoDetail() {
		return doDetail;
	}

	public void setDoDetail(DeliveryOrderDetail doDetail) {
		this.doDetail = doDetail;
	}

	public Long getWaveId() {
		return waveId;
	}

	public void setWaveId(Long waveId) {
		this.waveId = waveId;
	}

    public Boolean getIsMerged() {
        return isMerged;
    }

    public void setIsMerged(Boolean isMerged) {
        this.isMerged = isMerged;
    }
}
