package com.daxia.wms.delivery.deliveryorder.service;

import com.daxia.wms.delivery.deliveryorder.entity.DoHeaderPrintEx;

public interface DoHeaderPrintExService {

//	public void save(DoHeaderPrintEx doHeaderPrintEx);
//
//	public void update(DoHeaderPrintEx doHeaderPrintEx);
//
//	public DoHeaderPrintEx getById(Long doId);
//
//	/**
//	 * 冻结订单是否在核拣页面打印
//	 * @param doId
//	 * @return
//	 */
//	public boolean isHDDoPrint(Long doId);
}
