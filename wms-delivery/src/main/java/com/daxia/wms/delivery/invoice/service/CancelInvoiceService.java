package com.daxia.wms.delivery.invoice.service;

import java.util.List;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.invoice.entity.InvoiceBook;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;
import com.daxia.wms.delivery.invoice.filter.InvoiceNoFilter;

/**
 * 发票作废Service
 */
public interface CancelInvoiceService {
	/**
	 * 
	 * @param Invoice
	 * @param startIndex
	 * @param pageSize
	 * @return
	 */
	public DataPage<InvoiceNo> findInvoiceNoByFilter(
			InvoiceNoFilter invoiceFilter, int startIndex, int pageSize);
	
	/**
     * 单个作废发票
     * 注意：该方法会调用接口的sendVoidedInvoice2FinanceCreateDatas创建接口数据，调用前请确认是否需要
     * @param invoiceNoId 发票号ID
     */
    public void cancelSingleInvoice(Long invoiceNoId);
    /**
     * 批量作废发票
     * 注意：该方法会调用接口的sendVoidedInvoice2FinanceCreateDatas创建接口数据，调用前请确认是否需要
     * @param invoiceNoId 发票号ID
     */
    public void cancelBatchInvoice(Long invoiceNoId);
    
    /**
     * 根据doNo查询发票实体信息
     */
    public List<InvoiceNo> getInvoiceNoByDoNo(String doNo);
    
	public void save(InvoiceBook invoiceBook);

	public void remove(Long invoiceNoId);

	public void cancelSingleInvoiceForExp(Long invoiceNoId) ;
}
