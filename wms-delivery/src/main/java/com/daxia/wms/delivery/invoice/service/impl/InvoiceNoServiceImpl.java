package com.daxia.wms.delivery.invoice.service.impl;

import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.log.LogUtil;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.system.security.ExtendedIdentity;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.InvoiceBookStatus;
import com.daxia.wms.Constants.InvoiceNoStatus;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.PrintInvoiceDAO;
import com.daxia.wms.delivery.invoice.dao.InvoiceDao;
import com.daxia.wms.delivery.invoice.dao.InvoiceNoDAO;
import com.daxia.wms.delivery.invoice.entity.InvoiceBook;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;
import com.daxia.wms.delivery.invoice.filter.InvoiceFilter;
import com.daxia.wms.delivery.invoice.filter.InvoiceNoFilter;
import com.daxia.wms.delivery.invoice.service.InvoiceBookService;
import com.daxia.wms.delivery.invoice.service.InvoiceNoService;
import com.daxia.wms.delivery.print.dto.DoInvoicePrintDTO;
import com.daxia.wms.exp.service.ExpFacadeService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 发票号service实现类
 */
@Name("com.daxia.wms.delivery.invoiceNoService")
@lombok.extern.slf4j.Slf4j
public class InvoiceNoServiceImpl implements InvoiceNoService {

	@In
	private InvoiceNoDAO invoiceNoDAO;
	@In
	private InvoiceBookService invoiceBookService;
	@In
	private ExtendedIdentity identity;
	@In
	private InvoiceDao invoiceDao;
	@In
	private ExpFacadeService expFacadeService;
    @In
    private PrintInvoiceDAO printInvoiceDAO;
    
    /**
     * 根据发票簿Id分页查询发票号信息
     */
	@Override
	public DataPage<InvoiceNo> findInvoiceNoByInvoiceBookId(Long invoiceBookId, int startIndex, int pageSize) {
		return invoiceNoDAO.findInvoiceNoByInvoiceBookId(invoiceBookId, startIndex, pageSize);
	}

    /**
     * 发票号申请遗失
     */
	@Override
	@Transactional
	public InvoiceBook invoiceNoLost(Long invoiceBookId, String invoiceNoFm, String invoiceNoTo) {
	    //验证发票薄和发票薄中号码段中的发票号都是未打印
		InvoiceBook invoiceBook = validate2InvoiceLost(invoiceBookId, invoiceNoFm, invoiceNoTo);
		//更新状态为已遗失
		invoiceNoDAO.updateInvoiceBookInvoiceNos(invoiceBookId, invoiceNoFm, invoiceNoTo, 
				Constants.InvoiceNoStatus.LOST.getValue(), DateUtil.getNowTime(), identity.getCredentials().getUsername());
		//更新发票薄状态
		if (!Constants.InvoiceBookStatus.USING.getValue().equals(invoiceBook.getStatus())) {
			invoiceBook.setStatus(Constants.InvoiceBookStatus.USING.getValue());
		}
		
		this.invoiceNoDAO.getSession().flush();
        this.invoiceNoDAO.getSession().clear();
        
		return invoiceBook;
	}
    
    /**
     * 验证发票薄和发票薄中号码段中的发票号都是未打印
     * 
     * @param invoiceBookId
     * @param invoiceNoFm
     * @param invoiceNoTo
     * @return
     */
	private InvoiceBook validate2InvoiceLost(Long invoiceBookId, String invoiceNoFm, String invoiceNoTo) {
		//起始号码不能为空
		if (StringUtil.isEmpty(invoiceNoFm) || StringUtil.isEmpty(invoiceNoTo)) {
			throw new DeliveryException(DeliveryException.ERROR_INVOICENO_NULL);
		}
		//结束发票号码不能小于起始发票号码
		if (invoiceNoFm.compareTo(invoiceNoTo) > 0) {
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_NOFM_BIG_NOTO);
		}
		InvoiceBook invoiceBook = invoiceBookService.getInvoiceBookById(invoiceBookId);
		if (null == invoiceBook) {
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_INVOICEBOOK_NULL);
		}
		//起号码不能小于发票薄起始号
		if (invoiceNoFm.compareTo(invoiceBook.getInvoiceNoFrom()) < 0) {
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_INVOICENOFM_LESS_INVOICEBOOKFM, invoiceNoFm);
		}
		//止号码不能大于发票薄终止号
		if (invoiceNoTo.compareTo(invoiceBook.getInvoiceNoTo()) > 0) {
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_INVOICENOFM_BIGER_INVOICEBOOKTO, invoiceNoTo);
		}
		//发票薄中存在不是未打印状态的发票号则抛异常
		List<InvoiceNo> lostInvoiceNo = invoiceNoDAO.findInvoiceBooksInvoiceNoNotPrint(invoiceBook.getId(), 
				invoiceNoFm, invoiceNoTo, Constants.InvoiceNoStatus.INITIAL.getValue());
		if (!ListUtil.isNullOrEmpty(lostInvoiceNo)) {
			Object [] exParams = new Object[4];
			exParams[0] = invoiceBook.getInvoiceCode();
			exParams[1] = invoiceNoFm;
			exParams[2] = invoiceNoTo;
			exParams[3] = lostInvoiceNo.get(0).getInvoiceNo();
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_INVOICENOS_NOT_PRINT, exParams);
		}
		return invoiceBook;
	}
	
    
    /**
     * 发票号绑定单据
     */
	@Override
	@Transactional
	@Loggable
	public void bindInvoiceNoWithBill(String invoiceNo, String invoiceCode,
			String doNo, Integer billNo) {
        log.debug("bindInvoiceNoWithBill: invoiceNo = " + invoiceNo + ",invoiceCode = " + invoiceCode + ",doNo = "
                + doNo + ",billNo = " + billNo);
        InvoiceNo invoiceNumber = invoiceNoDAO.findInvoiceNoByNo(invoiceNo);

        InvoiceFilter invoiceFilter = new InvoiceFilter();
        invoiceFilter.setDoNo(doNo);
        invoiceFilter.setSortBy(billNo);
        List<InvoiceHeader> invoiceHeaderList = invoiceDao.findByFilter(invoiceFilter);
        if (ListUtil.isNullOrEmpty(invoiceHeaderList)) { // 发票序号要存在
            throw new DeliveryException(DeliveryException.INVOICE_SEQ_ERROR);
        }
        InvoiceHeader invoiceHeader = invoiceHeaderList.get(0);
        //绑定发票头与发票号码
        bind(invoiceHeader, invoiceNumber);
	}

    /**
	 * 根据开票号去 发票号码表 查询对应的发票号码信息
	 */
	@Override
	public InvoiceNo getInvoiceNoObjByInvNo(String InvNo) {
		return invoiceNoDAO.findInvoiceNoByNo(InvNo);
	}
	
	/**
     * 根据开票号、DO号 查询对应的发票号码信息
     */
	@Override
	public InvoiceNo getInvoiceNoObjByInvNoAndDoNo(String InvNo,String doNo) {
		return invoiceNoDAO.findInvoiceNoByInvNoAndDoNo(InvNo, doNo);
	}
	
	/**
     * 根据开票号、发票头ID 查询对应的发票号码信息
     */
	@Override
	public InvoiceNo getInvoiceNoObjByInvNoAndInvHeaderId(String InvNo,Long invHeaderId){
		return invoiceNoDAO.findInvoiceNoObjByInvNoAndInvHeaderId(InvNo, invHeaderId);
	}
	
    /**
     * 发票号码绑定发票头
     */
	@Override
	@Transactional
	public void bind(InvoiceHeader invoiceHeader, InvoiceNo invoiceNo) {
        // 发票号码是否未打印
        if (invoiceNo == null || !InvoiceNoStatus.INITIAL.getValue().equals(invoiceNo.getStatus())) {
            throw new DeliveryException(DeliveryException.INVOICE_STATUS_ERROR);
        }

        String existInvoiceNumber = invoiceHeader.getInvoiceNumber();
        if (StringUtil.isNotEmpty(existInvoiceNumber)) {
            InvoiceNo existInvoiceNo = invoiceNoDAO.findInvoiceNoByNo(existInvoiceNumber);
            if (existInvoiceNo != null && InvoiceNoStatus.PRINT.getValue().equals(existInvoiceNo.getStatus())) { 
                // 发票头已绑定发票号
                
                throw new DeliveryException(DeliveryException.INVOICE_HAS_BEEN_BOUND_ERROR, invoiceHeader.getId(),
                        existInvoiceNumber);
            }
        }
        // 修改发票头信息
        invoiceHeader.setInvoiceNumber(invoiceNo.getInvoiceNo());
        invoiceHeader.setUpdatedAt(DateUtil.getNowTime());
        invoiceHeader.setUpdatedBy(identity.getCredentials().getUsername());
        invoiceDao.update(invoiceHeader);
        // 检查do是否可以进行发票号操作
		DoInvoicePrintDTO dto = printInvoiceDAO.getInvoicePrintInfoOfDo(invoiceHeader.getDoHeaderId());
		if (DoStatus.CANCELED.getValue().equals(dto.getStatus()) || DoStatus.ALL_DELIVER.getValue().equals(dto.getStatus())) {
			throw new DeliveryException(DeliveryException.ERROR_INVOICE_BIND_DO_STATUS_NOT_BE_DEPLOY_CANCEL, dto.getDoNo());
		}
        // 设置发票号码信息
        invoiceNo.setStatus(InvoiceNoStatus.PRINT.getValue());
        invoiceNo.setDoNo(dto.getDoNo());
        invoiceNo.setInvoiceHeaderId(invoiceHeader.getId());
        invoiceNo.setLockBy(identity.getCredentials().getUsername());
        invoiceNo.setLockDoDate(new Date());
        invoiceNo.setUpdatedAt(DateUtil.getNowTime());
        invoiceNo.setUpdatedBy(identity.getCredentials().getUsername());
        invoiceNoDAO.update(invoiceNo);
        // 检验是否需要修改发票号码所在的发票薄状态
        InvoiceBook invoiceBook = invoiceBookService.getInvoiceBookById(invoiceNo.getInvoiceBookId());
        if (!InvoiceBookStatus.USING.getValue().equals(invoiceBook.getStatus())) {
            invoiceBook.setStatus(InvoiceBookStatus.USING.getValue());
            invoiceBookService.updateInvoiceBook(invoiceBook);
        }
    }

    /**
     * 根据filter查询条件查找符合条件的所有记录,不分页，返回查询结果的前500条记录
     */
    @Override
    public List<InvoiceNo> findByFilter(InvoiceNoFilter filter) {
        return invoiceNoDAO.findByFilter(filter);
    }
    
    /**
     * 根据发票代码、发票起止号码查询发票实体信息
     * @param invoiceCode
     * @param invoiceNoFrom
     * @param invoiceNoTo
     * @return
     */
    @Override
    public List<InvoiceNo> getInvoiceNoList(String invoiceCode, String invoiceNoFrom, String invoiceNoTo) {
        return invoiceNoDAO.getInvoiceNoList(invoiceCode, invoiceNoFrom, invoiceNoTo);
    }
}
