package com.daxia.wms.delivery.load.service;

import java.util.List;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.load.dto.CrossDockPackingDTO;
import com.daxia.wms.delivery.load.entity.CrossDockPacking;
import com.daxia.wms.delivery.load.filter.CrossDockPackingFilter;
import com.daxia.wms.delivery.print.dto.CrossDockCartonPrintDTO;

public interface CrossDockPackingService {
	/**
	 * 以分页面方式查询符合条件的CrdockHeader
	 * @param filter 查询条件过滤器
	 * @param startIndex 当前页
	 * @param pageSize 每页显示条数
	 * return 符合条件的CrdockHeader, 返回值不为空
	 */
	public DataPage<CrossDockPacking> queryCDHeaderPageInfo(CrossDockPackingFilter filter,
			int startIndex, int pageSize);
	
	public void delivery(List<CrossDockPackingDTO> crossDockPackingDTOList,Integer boxNum);
	
	public List<CrossDockCartonPrintDTO> getCrossDockCartonPrintDTOById(List<Long> ids);
}
