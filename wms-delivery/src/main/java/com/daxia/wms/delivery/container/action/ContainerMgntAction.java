package com.daxia.wms.delivery.container.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.container.filter.ContainerMgntFilter;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.master.entity.Container;
import com.daxia.wms.master.entity.ContainerType;
import com.daxia.wms.master.service.ContainerService;
import com.daxia.wms.master.service.ContainerTypeService;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 *	容器管理action，containerMgnt.xhtml页面使用
 */
@Name("com.daxia.wms.delivery.containerMgntAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ContainerMgntAction extends PagedListBean<Container> {

	private static final long serialVersionUID = 1L;
	/**
	 * 容器id
	 */
	private Long containerId;
	/**
	 * 容器号
	 */
	private String containerNo;
    /**
     * 容器类型
     */
    private String containerType;


    private Container container;
	
	/**
	 * 单据类型
	 */
	private String docType;
	/**
	 * 单据号
	 */
	private String docNo;
	
	private List<DeliveryOrderDetail> doDetails;
	/**
	 * 容器管理的filter
	 */
	private ContainerMgntFilter containerMgntFilter;

    @In
    private ContainerService containerService;

    @In
    private ContainerMgntService containerMgntService;
    
    @In
    private DeliveryOrderService deliveryOrderService;

	@In
	private ContainerTypeService containerTypeService;

	private boolean initialized = false;

	private List<SelectItem> containerTypeSelector = new ArrayList<SelectItem>();
    
    /**
     * 
     */
    private String showReleasePanlFlag = "0";
	
	public ContainerMgntAction() {
		super();
		containerMgntFilter = new ContainerMgntFilter();
	}
	
	@Override
	public void query() {
	    //目前只校验wms是否启用容器管理，若没有，则不能进行操作
        containerMgntService.vContainerMgntUsed();
		this.setOrderBy("containerNo");
		this.buildOrderFilterMap(containerMgntFilter);
		DataPage<Container> dataPage = containerService.queryContainerByFilter(containerMgntFilter, getStartIndex(), getPageSize());
		populateValues(dataPage);
	}

	public void initializePage() {
		if (!initialized) {// 确保初始化动作只执行一次
			initContainerTypeSelector();
			this.initialized = true;
		}
	}

	private void initContainerTypeSelector(){
		List<ContainerType> containerTypeList = containerTypeService.findAll();
		containerTypeSelector.clear();
		for (ContainerType c : containerTypeList) {
			SelectItem item = new SelectItem();
			item.setLabel(c.getName());
			item.setValue(c.getId());
			containerTypeSelector.add(item);
		}
	}

	/**
	 * 释放预处理
	 */
    public void preRelease() {
        container = containerService.getContainer(containerId);
        if (Constants.ContainerType.SORT_CONTAINER.getValue().equals(container.getContainerType().getContainerType())) {
            DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(container.getDocNo());
            if (doHeader == null) {
                showReleasePanlFlag = "2";
                this.sayMessage(MESSAGE_SUCCESS);
                return;
            }
            if (doHeader.getStatus().compareTo(Constants.DoStatus.ALL_CARTON.getValue()) >= 0) {
                showReleasePanlFlag = "0";
            }
            if (doHeader.getStatus().compareTo(Constants.DoStatus.ALL_CARTON.getValue()) < 0
                    && Constants.ReleaseStatus.RELEASE.getValue().equals(doHeader.getReleaseStatus())) {
                showReleasePanlFlag = "2";
                throw new DeliveryException(DeliveryException.CONTAINER_NOT_RELEASE);
            }
            if (doHeader.getStatus().compareTo(Constants.DoStatus.ALL_CARTON.getValue()) < 0
                    && Constants.ReleaseStatus.HOLD.getValue().equals(doHeader.getReleaseStatus())) {
                showReleasePanlFlag = "1";
            }
        } else {
            showReleasePanlFlag = "1";
        }
        containerNo = this.container.getContainerNo();
        docNo = this.container.getDocNo();
        docType = this.container.getDocType();
    }
	
	/**
	 * 容器释放
	 */
	public void release() {
	    this.setOperationStatus(INIT_OPERATION_ROW);		
		container = containerMgntService.releaseContainer(containerId);
        this.sayMessage(MESSAGE_SUCCESS);
        this.setOperationStatus(0);
	}

	public String getDocType() {
		return docType;
	}

	public void setDocType(String docType) {
		this.docType = docType;
	}

	public String getDocNo() {
		return docNo;
	}

	public void setDocNo(String docNo) {
		this.docNo = docNo;
	}

	public ContainerMgntFilter getContainerMgntFilter() {
		return containerMgntFilter;
	}

	public void setContainerMgntFilter(ContainerMgntFilter containerMgntFilter) {
		this.containerMgntFilter = containerMgntFilter;
	}

	public ContainerService getContainerService() {
		return containerService;
	}

	public void setContainerService(ContainerService containerService) {
		this.containerService = containerService;
	}

	public Long getContainerId() {
		return containerId;
	}

	public void setContainerId(Long containerId) {
		this.containerId = containerId;
	}

	public void setContainer(Container container) {
		this.container = container;
	}

	public Container getContainer() {
		return container;
	}

	public String getContainerNo() {
		return containerNo;
	}

	public void setContainerNo(String containerNo) {
		this.containerNo = containerNo;
	}

	public List<DeliveryOrderDetail> getDoDetails() {
		return doDetails;
	}

	public void setDoDetails(List<DeliveryOrderDetail> doDetails) {
		this.doDetails = doDetails;
	}
    
    public String getContainerType() {
        return containerType;
    }
    
    public void setContainerType(String containerType) {
        this.containerType = containerType;
    }

    
    public String getShowReleasePanlFlag() {
        return showReleasePanlFlag;
    }

    
    public void setShowReleasePanlFlag(String showReleasePanlFlag) {
        this.showReleasePanlFlag = showReleasePanlFlag;
    }

	public List<SelectItem> getContainerTypeSelector() {
		return containerTypeSelector;
	}

	public void setContainerTypeSelector(List<SelectItem> containerTypeSelector) {
		this.containerTypeSelector = containerTypeSelector;
	}

	public boolean isInitialized() {
		return initialized;
	}

	public void setInitialized(boolean initialized) {
		this.initialized = initialized;
	}
}
