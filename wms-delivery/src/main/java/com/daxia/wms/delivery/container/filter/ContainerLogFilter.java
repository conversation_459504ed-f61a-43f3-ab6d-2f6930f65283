package com.daxia.wms.delivery.container.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

import java.util.Date;

@lombok.extern.slf4j.Slf4j
public class ContainerLogFilter extends WhBaseQueryFilter {
    
    private Date operationTimeFm;
    
    private Date operationTimeTo;
    
    private String containerNo;
    
    private String operator;
    
    private String operation;

    private String docNo;
    
    public String getOperation() {
        return operation;
    }
    
    public void setOperation(String operation) {
        this.operation = operation;
    }
    
    public String getOperator() {
        return operator;
    }
    
    public void setOperator(String operator) {
        this.operator = operator;
    }
    
    @Operation(fieldName = "o.operationTime", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")
    public Date getOperationTimeFm() {
        return operationTimeFm;
    }
    
    public void setOperationTimeFm(Date operationTimeFm) {
        this.operationTimeFm = operationTimeFm;
    }
    
    @Operation(fieldName = "o.operationTime", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
    public Date getOperationTimeTo() {
        return operationTimeTo;
    }
    
    public void setOperationTimeTo(Date operationTimeTo) {
        this.operationTimeTo = operationTimeTo;
    }
    
    public String getContainerNo() {
        return containerNo;
    }
    
    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }

    public String getDocNo() {
        return docNo;
    }

    public void setDocNo(String docNo) {
        this.docNo = docNo;
    }
}
