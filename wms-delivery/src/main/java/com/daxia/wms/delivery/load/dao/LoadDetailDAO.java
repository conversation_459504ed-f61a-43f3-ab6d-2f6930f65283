package com.daxia.wms.delivery.load.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.load.dto.DoLoadReportDTO;
import com.daxia.wms.delivery.load.entity.LoadDetail;
import com.daxia.wms.delivery.load.entity.LoadHeader;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;

/**
 * 交接明细DAO
 */
@Name("com.daxia.wms.delivery.loadDetailDAO")
@lombok.extern.slf4j.Slf4j
public class LoadDetailDAO extends HibernateBaseDAO<LoadDetail, Long> {

    private static final long serialVersionUID = -349314227516872655L;

    /**
     * 查找对于给定的交接单List中每一个交接单对应的DO和箱子数量
     * @param loadHeaders
     * @return Map<doHeaderId, count(cartonHeader.id)>
     */
	@SuppressWarnings("rawtypes")
    public Map<Long, Integer> getDoPagedQty(List<LoadHeader> loadHeaders) {
        List<Long> ids = new ArrayList<Long>(loadHeaders.size());
        for (LoadHeader header : loadHeaders) {
            ids.add(header.getId());
        }

        String sqlStr = "select ch.do_header_id, count(ch.id) from doc_carton_header ch"
                + " inner join doc_load_detail ld on ch.id = ld.carton_header_id and ch.is_deleted = 0 and ld.is_deleted = 0"
                + " where ld.load_header_id in (:loadHeaderIds) and ch.warehouse_id = :warehouseId and ld.warehouse_id = :warehouseId group by ch.do_header_id";
        Query query = getSession().createSQLQuery(sqlStr).setParameterList("loadHeaderIds", ids).setLong("warehouseId", ParamUtil.getCurrentWarehouseId());

        Map<Long, Integer> doPagedQtyMap = new HashMap<Long, Integer>();

        List list = query.list();
        int size = list.size();
        for (int i = 0; i < size; i++) {
            Object[] obj = (Object[]) list.get(i);
            doPagedQtyMap.put(((BigInteger) obj[0]).longValue(), ((BigInteger) obj[1]).intValue());
        }

        return doPagedQtyMap;
    }

    public void changePickStockByDoHeaderId(Long doHeaderId, String updateUser){
        String sql = "UPDATE stk_picked o,tsk_pick pd " +
                "SET o.is_deleted = 1, " +
                " o.UPDATE_TIME = now(), o.UPDATE_BY = 1 " +
                "WHERE  o.id=pd.to_stock_id " +
                "AND pd.doc_id = :doHeaderId " +
                " AND pd.warehouse_id = :warehouseId " +
                " AND pd.is_deleted = 0 " +
                "AND o.warehouse_id = :warehouseId  " +
                "AND o.IS_DELETED = 0 ";
        Query query = createUpdateSqlQuery(sql, "o");
        query.setLong("doHeaderId", doHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 查询交接单对应的do_no和do对应的的箱子总数
     * @param loadHeaderId
     * @return List<DoLoadReportDTO>
     */
    @SuppressWarnings("unchecked")
    public List<DoLoadReportDTO> findCartonNumsGroupByDoNoInACar(Long loadHeaderId){
        StringBuilder sb = new StringBuilder();
        sb.append("select dh.do_no, count(ch.do_header_id) ");
        sb.append(" from doc_do_header dh, doc_carton_header ch  ");
        sb.append(" left join doc_load_detail ld on ch.id = ld.carton_header_id ");
        sb.append("                                     and ld.is_deleted = 0 and ld.warehouse_id = :warehouseId ");
        sb.append(" where dh.id = ch.do_header_id ");
        sb.append("  and ld.load_header_id = :loadHeaderId and dh.warehouse_id = :warehouseId and ch.warehouse_id = :warehouseId and ch.is_deleted = 0 ");
        sb.append(" group by dh.do_no  ");
        Query query = getSession().createSQLQuery(sb.toString());
        query.setLong("loadHeaderId", loadHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        List<Object[]> list = query.list();
        List<DoLoadReportDTO> dtoList = new ArrayList<DoLoadReportDTO>();
        for (Object[] objs : list) {
            DoLoadReportDTO  dto = new  DoLoadReportDTO();
            dto.setDoNo(objs[0] == null ? null : objs[0].toString());
            dto.setCartonCount(objs[1] == null ? Integer.valueOf(0) : Integer.valueOf(objs[1].toString()));
            dtoList.add(dto);
        }
        return dtoList;
    }

    @SuppressWarnings("unchecked")
    public List<Object[]> findLoadDetailsInAcarNew(Long loadHeaderId) {
        String sql = "select dh.address,dh.consignee_name,p.province_cname,c.city_cname,ct.county_cname," +
        		" ld.id,ld.load_header_id,ld.carton_header_id,ld.do_no,ld.tracking_no,ld.carrier_no,ld.do_header_id," +
        		" ld.create_time,ld.create_by,ld.update_time,ld.update_by " +
        		" from doc_do_header dh" +
        		" inner join doc_load_detail ld on dh.id = ld.do_header_id" +
        		" left join md_province p on dh.province = p.id and p.is_deleted = 0" +
        		" left join md_city c on dh.city = c.id and c.is_deleted = 0" +
        		" left join md_county ct on dh.county = ct.id  and ct.is_deleted = 0 " +
        		" where ld.load_header_id = :loadHeaderId and dh.warehouse_id = :warehouseId and ld.warehouse_id = :warehouseId " +
        		" and ld.is_deleted = 0";
        Query query = this.createSQLQuery(sql);
        query.setLong("loadHeaderId", loadHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 查询历史装车明细
     * @param loadHeaderId
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Object[]> findLoadDetailsHisInAcarNew(Long loadHeaderId) {
        String sql = "select dh.address,dh.consignee_name,p.province_cname,c.city_cname,ct.county_cname," +
                " ld.id,ld.load_header_id,ld.carton_header_id,ld.do_no,ld.tracking_no,ld.carrier_no,ld.do_header_id," +
                " ld.create_time,ld.create_by,ld.update_time,ld.update_by " +
                " from doc_do_header_his dh" +
                " inner join doc_load_detail_his ld on dh.id = ld.do_header_id" +
                " left join md_province p on dh.province = p.id and p.is_deleted = 0" +
                " left join md_city c on dh.city = c.id and c.is_deleted = 0" +
                " left join md_county ct on dh.county = ct.id  and ct.is_deleted = 0 " +
                " where ld.load_header_id = :loadHeaderId and dh.warehouse_id = :warehouseId and ld.warehouse_id = :warehouseId " +
                " and ld.is_deleted = 0";
        Query query = this.createSQLQuery(sql);
        query.setLong("loadHeaderId", loadHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 查找doHeaderId对应的最新的交接单明细的最晚创建时间
     * @param doHeaderId
     * @return
     */
    public Date getHandoverTime(Long doHeaderId){
        String hql = "select ld.createdAt from LoadDetail ld where ld.doHeaderId = :doHeaderId and ld.warehouseId = :warehouseId order by ld.createdAt desc ";
        Query query = createQuery(hql);
        query.setLong("doHeaderId", doHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (Date) query.uniqueResult();
    }

    /**
     * 按箱删除装车明细
     * @param cartonId
     * @param loadHeaderId
     */
    public void removeByCartonIdAndLoadHeaderId(Long cartonId,Long loadHeaderId) {
        String hql = "delete from LoadDetail ld where ld.cartonHeaderId = :cartonId and ld.loadHeaderId = :loadHeaderId";
        Query query = createQuery(hql);
        query.setParameter("cartonId", cartonId);
        query.setParameter("loadHeaderId", loadHeaderId);
        query.executeUpdate();
    }

    public List<Object[]> findLoadDetailByLoadId(Long loadId) {
        String sql = "select dd.consignee_name,dd.mobile,dd.address,d.way_bill,d.pack_material,m.length,m.height,m.width,d.actual_grossweight,dd.receivable "
                + " from doc_load_detail ld inner join doc_carton_header d on d.id = ld.carton_header_id "
                + " left join md_materials m on m.materials_no = d.pack_material "
                + " inner join doc_do_header dd on d.do_header_id = dd.id "
                + " where dd.warehouse_id = :warehouseId and ld.load_header_id = :loadId";
        Query query = createSQLQuery(sql);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("loadId", loadId);
        return query.list();
    }


    public Long getDocQtyByLoadId(Long loadId){
        String sql = "select count(distinct(ld.do_header_id)) from doc_load_detail ld where ld.load_header_id = :loadHeaderId and ld.warehouse_id = :warehouseId";
        Query query = createSQLQuery(sql);
        query.setLong("loadHeaderId", loadId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return Long.parseLong(query.uniqueResult().toString());
    }
    
    public String getLoadNoByDo(Long doId) {
        String hql = "SELECT ld.loadHeader.loadNo FROM LoadDetail ld WHERE ld.doHeaderId = :doId";
        List<String> loadNos = this.createQuery(hql).setParameter("doId", doId).setMaxResults(1).list();
        return ListUtil.isNullOrEmpty(loadNos) ? null : loadNos.get(0);
    }
    
    public String getLoadNoByDo(Long loadId, Long doId) {
        String hql = "SELECT ld.loadHeader.loadNo FROM LoadDetail ld WHERE ld.doHeaderId = :doId AND ld.loadHeaderId != :loadHeaderId";
        List<String> loadNos = this.createQuery(hql).setParameter("doId", doId).setParameter("loadHeaderId", loadId).setMaxResults(1).list();
        return ListUtil.isNullOrEmpty(loadNos) ? null : loadNos.get(0);
    }
}
