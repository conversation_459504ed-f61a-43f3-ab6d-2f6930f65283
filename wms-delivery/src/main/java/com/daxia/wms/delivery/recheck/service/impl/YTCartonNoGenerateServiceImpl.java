package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.delivery.deliveryorder.service.DoWaveExService;
import com.daxia.wms.delivery.recheck.dao.CartonHeaderDAO;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.waybill.WaybillException;
import com.daxia.wms.waybill.yt.dto.request.Receiver;
import com.daxia.wms.waybill.yt.dto.request.Sender;
import com.daxia.wms.waybill.yt.dto.request.YTRequest;
import com.daxia.wms.waybill.yt.dto.response.YTResponse;
import com.daxia.wms.waybill.yt.service.YTWaybillService;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.util.Date;

@Name("yTCartonNoGenerateService")
@lombok.extern.slf4j.Slf4j
public class YTCartonNoGenerateServiceImpl implements CartonNoGenerateService {
	@In
	private DoWaveExService doWaveExService;
	@In(create = true)
	YTWaybillService yTWaybillService;
	@In
	CartonHeaderDAO cartonHeaderDAO;
	@In
	WarehouseCarrierService warehouseCarrierService;
	@In
	WarehouseService warehouseService;
	@In
	private SequenceGeneratorService sequenceGeneratorService;

	@Override
    public void generatorCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
		DoWaveEx doWaveEx = doWaveExService.findByDoHeaderId(doHeader.getId());
		if (null == doWaveEx) {
			throw new WaybillException(WaybillException.YT_WAYBILL_ERROR);
		}

		WarehouseCarrier warehouseCarrier = warehouseCarrierService.getCarrierInfoByWarehouseIdAndCarrierIdAndType(doHeader
				.getWarehouseId(),
				doHeader
				.getCarrierId(),Constants.WaybillType.YTO.name());

		if (warehouseCarrier == null) {
			throw new WaybillException(WaybillException.YT_WAYBILL_ERROR);
		}

		Warehouse warehouse = warehouseService.getWarehouse(doHeader.getWarehouseId());

		//生成一个箱号作为业务单号
		String txLogisticId = doHeader.getDoNo() + sequenceGeneratorService.generateSequenceNo(Constants.SequenceName
				.CARTONNO.getValue(), ParamUtil.getCurrentWarehouseId());
		cartonHeader.setTrackingNo(txLogisticId);

		YTRequest request = new YTRequest();
		request.setClientID(warehouseCarrier.getExt1());
		request.setCustomerId(warehouseCarrier.getExt1());
		request.setTxLogisticID(txLogisticId);
		//0-COD,1-普通订单,2-便携式订单3-退货单
		request.setOrderType(doHeader.getReceivable().doubleValue() > 0 ? 0 : 1);
		request.setAgencyFund(doHeader.getReceivable().doubleValue());
		request.setSendStartTime(DateUtil.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
		request.setSendEndTime(DateUtil.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
		request.setGoodsValue(doHeader.getOrderAmount().doubleValue());
		request.setItemsValue(doHeader.getOrderAmount().doubleValue());
		request.setTotalValue(doHeader.getOrderAmount().doubleValue());
		request.setItemsWeight(doHeader.getTotalGrossWt().doubleValue());

		Sender sender = new Sender();
		sender.setName(warehouse.getContactor());
		sender.setPhone(warehouse.getPhone());
		sender.setMobile(warehouse.getMobile());
        sender.setProv(warehouse.getProvince().getProvinceCname());
        sender.setCity(warehouse.getCity().getCityCname() + (warehouse.getCounty() == null ? "" : "," + warehouse.getCounty().getCountyCname()));
        sender.setAddress(warehouse.getAddressName());
        request.setSender(sender);

        Receiver receiver = new Receiver();
        receiver.setName(doHeader.getConsigneeName());
        receiver.setPhone(DoUtil.decryptPhone(doHeader.getTelephone()));
        receiver.setMobile(DoUtil.decryptPhone(doHeader.getMobile()));
        receiver.setProv(StringUtils.isNotEmpty(doHeader.getProvinceName()) ? doHeader.getProvinceName() :
                doHeader.getProvinceInfo().getProvinceCname());
        String city = StringUtils.isNotEmpty(doHeader.getCityName()) ? doHeader.getCityName() :
                doHeader.getCityInfo().getCityCname();
        String county = StringUtils.isNotEmpty(doHeader.getCountyName()) ? doHeader.getCountyName() :
                doHeader.getCountyInfo().getCountyCname();
        receiver.setCity(city + (StringUtil.isBlank(county) ? "" : "," + county));
        receiver.setAddress(doHeader.getAddress());
        request.setReceiver(receiver);

		YTResponse response = null;
		try{
			response = yTWaybillService.generateWabybillNo(request,warehouseCarrier);
		}catch (Exception e) {
			throw  new WaybillException(WaybillException.YT_WAYBILL_ERROR);
		}
		if (response == null) {
			//接口调用失败
			throw  new WaybillException(WaybillException.YT_WAYBILL_ERROR);
		}
		if (!response.isSuccess()) {
			throw  new WaybillException(response.getReason());
		}
		
		if (response.getDistributeInfo() == null) {
			throw new WaybillException(WaybillException.YT_WAYBILL_DISTRIBUTE_NULL);
		}

		//运单号
		cartonHeader.setCartonNo(response.getMailNo());
		cartonHeader.setWayBill(response.getMailNo());

		//大头笔信息
		doWaveEx.setShortAddress(response.getDistributeInfo().getShortAddress());
		doWaveEx.setOriginCode(response.getDistributeInfo().getConsigneeBranchCode());
        doWaveEx.setDestinationCode(StringUtil.isNotBlank(response.getDistributeInfo().getShortAddress())
                ? response.getDistributeInfo().getShortAddress() : response.getDistributeInfo().getPackageCenterCode());
        doWaveEx.setDestinationName(response.getDistributeInfo().getPackageCenterName());
		doWaveEx.setPackageCenterName(response.getDistributeInfo().getPackageCenterCode());
		doWaveExService.update(doWaveEx);
	}
}