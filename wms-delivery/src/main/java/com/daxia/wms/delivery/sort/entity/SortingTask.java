package com.daxia.wms.delivery.sort.entity;

import com.daxia.framework.common.entity.BaseEntity;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.entity.Merchant;
import com.daxia.wms.master.entity.PackageInfoDetail;
import com.daxia.wms.master.entity.Sku;
import com.daxia.wms.stock.stock.entity.StockBatchAtt;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * 分拣任务
 */
@Entity
@Table(name = "trs_task")
@Where(clause = " IS_DELETED = 0 ")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@lombok.extern.slf4j.Slf4j
public class SortingTask extends BaseEntity{
    
    private static final long serialVersionUID = -5948208186940394908L;
    /**
     * 主键
     */
	private Long id;
	/**单据号
	 * 
	 */
	private String taskNo;
	/**
	 * 任务单状态
	 */
	private String taskStatus;
	/**
	 * 任务类型
	 */
	private String taskType;
	/**
	 * 货主ID
	 */
	private Long merchantId;
	/**商品ID
	 * 
	 */
	private Long skuId;
	/**
	 * 批次ID
	 */
	private Long lotId;
	/**
	 * LPN编号
	 */
	private String lpnNo;
	/**
	 * 拣货ID
	 */
	private Long pktNo;
	/**
	 * 单据ID
	 */
	private Long docId;
	/**
	 * 单据行ID
	 */
	private Long docLineId;
	/**
	 * From 库位ID
	 */
	private Long fmLocId;
	/**
	 * To 库位ID
	 */
	private Long toLocId;
	/**
	 * TO LPN
	 */
	private String toLpnNo;
	/**
	 * 优先级
	 */
	private Long priority;
	/**
	 * 包装ID
	 */
	private Long packId;
	/**
	 * 单位(取值为:EA/IP/PL/CS/OT)
	 */
	private String uom;
	/**
	 * 库存数
	 */
	private BigDecimal qty;
	/**
	 * 毛重
	 */
	private BigDecimal grossWeight;
	/**
	 * 净重
	 */
	private BigDecimal netWeight;
	/**
	 * 体积
	 */
	private BigDecimal volume;
	/**
	 * 原因代码
	 */
	private String reasonCode;
	/**
	 * 原因描述
	 */
	private String reasonDescr;
	/**
	 * 用户自定义1
	 */
    private String userdefine01;
	/**
	 * 用户自定义2
	 */
    private String userdefine02;
    /**
     * 用户自定义3
     */
    private String userdefine03;
    /**
     * 用户自定义4
     */
    private String userdefine04;
    /**
     * 用户自定义5
     */
    private String userdefine05;
    /**
     * 备注
     */
    private String notes;
    /**
     * 是否订单驱动
     */
    private Integer bySo;
    /**
     * 单据ID(上架单ID/拣货单ID/补货单ID)
     */
    private Long docOperId;
   
    /**
     * 实际上架库位
     */
    private String realLoc;
    /**
     * 包装明细ID
     */
    private Long packageDetailId;
    private PackageInfoDetail packageInfoDetail;
    /**
     * 库存批次属性
     */
    private StockBatchAtt stockBatchAtt;
    
    /**
     * 商品
     */    
    private Sku sku;
    /**
     * 供应商
     */
    private Merchant merchant;
    
    /**
     * 库位
     */
    private Location location;
        
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)  
    @Column(name = "ID")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name="TASK_NO")
	public String getTaskNo() {
		return taskNo;
	}

	public void setTaskNo(String taskNo) {
		this.taskNo = taskNo;
	}

	@Column(name="TASK_STATUS")
	public String getTaskStatus() {
		return taskStatus;
	}

	public void setTaskStatus(String taskStatus) {
		this.taskStatus = taskStatus;
	}

	@Column(name="TASK_TYPE")
	public String getTaskType() {
		return taskType;
	}

	public void setTaskType(String taskType) {
		this.taskType = taskType;
	}

	@Column(name="CARGO_OWNER_ID")
	public Long getMerchantId() {
		return merchantId;
	}

	public void setMerchantId(Long merchantId) {
		this.merchantId = merchantId;
	}

	@Column(name="SKU_ID")
	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	@Column(name="LOT_ID")
	public Long getLotId() {
		return lotId;
	}

	public void setLotId(Long lotId) {
		this.lotId = lotId;
	}

	@Column(name="LPN_NO")
	public String getLpnNo() {
		return lpnNo;
	}

	public void setLpnNo(String lpnNo) {
		this.lpnNo = lpnNo;
	}

	@Column(name="PKT_ID")
	public Long getPktNo() {
		return pktNo;
	}

	public void setPktNo(Long pktNo) {
		this.pktNo = pktNo;
	}

	@Column(name="DOC_ID")
	public Long getDocId() {
		return docId;
	}

	public void setDocId(Long docId) {
		this.docId = docId;
	}

	@Column(name="DOC_LINE_ID")
	public Long getDocLineId() {
		return docLineId;
	}

	public void setDocLineId(Long docLineId) {
		this.docLineId = docLineId;
	}

	@Column(name="FM_LOC_ID")
	public Long getFmLocId() {
		return fmLocId;
	}

	public void setFmLocId(Long fmLocId) {
		this.fmLocId = fmLocId;
	}

	@Column(name="TO_LOC_ID")
	public Long getToLocId() {
		return toLocId;
	}

	public void setToLocId(Long toLocId) {
		this.toLocId = toLocId;
	}
	
	@Column(name="PRIORITY")
	public Long getPriority() {
		return priority;
	}

	public void setPriority(Long priority) {
		this.priority = priority;
	}

	@Column(name="PACK_ID")
	public Long getPackId() {
		return packId;
	}

	public void setPackId(Long packId) {
		this.packId = packId;
	}

	@Column(name="UOM")
	public String getUom() {
		return uom;
	}

	public void setUom(String uom) {
		this.uom = uom;
	}

	@Column(name="QTY")
	public BigDecimal getQty() {
		return qty;
	}

	public void setQty(BigDecimal qty) {
		this.qty = qty;
	}

	@Column(name="GROSS_WEIGHT")
	public BigDecimal getGrossWeight() {
		return grossWeight;
	}

	public void setGrossWeight(BigDecimal grossWeight) {
		this.grossWeight = grossWeight;
	}
	
	@Column(name="NET_WEIGHT")
	public BigDecimal getNetWeight() {
		return netWeight;
	}

	public void setNetWeight(BigDecimal netWeight) {
		this.netWeight = netWeight;
	}

	@Column(name="VOLUME")
	public BigDecimal getVolume() {
		return volume;
	}

	public void setVolume(BigDecimal volume) {
		this.volume = volume;
	}

	@Column(name="REASON_CODE")
	public String getReasonCode() {
		return reasonCode;
	}

	public void setReasonCode(String reasonCode) {
		this.reasonCode = reasonCode;
	}

	@Column(name="REASON_DESCR")
	public String getReasonDescr() {
		return reasonDescr;
	}

	public void setReasonDescr(String reasonDescr) {
		this.reasonDescr = reasonDescr;
	}
	
	@Column(name="USERDEFINE01")
	public String getUserdefine01() {
		return userdefine01;
	}

	public void setUserdefine01(String userdefine01) {
		this.userdefine01 = userdefine01;
	}

	@Column(name="USERDEFINE02")
	public String getUserdefine02() {
		return userdefine02;
	}

	public void setUserdefine02(String userdefine02) {
		this.userdefine02 = userdefine02;
	}

	@Column(name="USERDEFINE03")
	public String getUserdefine03() {
		return userdefine03;
	}

	public void setUserdefine03(String userdefine03) {
		this.userdefine03 = userdefine03;
	}

	@Column(name="USERDEFINE04")
	public String getUserdefine04() {
		return userdefine04;
	}

	public void setUserdefine04(String userdefine04) {
		this.userdefine04 = userdefine04;
	}

	@Column(name="USERDEFINE05")
	public String getUserdefine05() {
		return userdefine05;
	}

	public void setUserdefine05(String userdefine05) {
		this.userdefine05 = userdefine05;
	}

	@Column(name="NOTES")
	public String getNotes() {
		return notes;
	}

	public void setNotes(String notes) {
		this.notes = notes;
	}
	


	@ManyToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="CARGO_OWNER_ID",insertable=false, updatable = false)
	public Merchant getMerchant() {
		return merchant;
	}

	public void setMerchant(Merchant merchant) {
		this.merchant = merchant;
	}

	@ManyToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="SKU_ID",referencedColumnName="ID",insertable=false, updatable = false)
	public Sku getSku() {
		return sku;
	}

	public void setSku(Sku sku) {
		this.sku = sku;
	}

	@OneToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="TO_LOC_ID",insertable=false, updatable = false)
	public Location getLocation() {
		return location;
	}

	public void setLocation(Location location) {
		this.location = location;
		if(location!=null){
			realLoc=location.getLocCode();
		}
	}
	
	//实际上架库位
	@Transient
	public String getRealLoc() {
		return realLoc;
	}

	public void setRealLoc(String realLoc) {
		this.realLoc = realLoc;
	}


	@Column(name="TO_LPN_NO")
	public String getToLpnNo() {
		return toLpnNo;
	}

	public void setToLpnNo(String toLpnNo) {
		this.toLpnNo = toLpnNo;
	}

	/**
	 * @return the bySo
	 */
	@Column(name="BY_SO")
	public Integer getBySo() {
		return bySo;
	}

	/**
	 * @param bySo the bySo to set
	 */
	public void setBySo(Integer bySo) {
		this.bySo = bySo;
	}
	
    public void setStockBatchAtt(StockBatchAtt stockBatchAtt) {
        this.stockBatchAtt = stockBatchAtt;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "LOT_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    public StockBatchAtt getStockBatchAtt() {
        return stockBatchAtt;
    }

	/**
	 * @return the docOperId
	 */
    @Column(name="DOC_OPER_ID")
	public Long getDocOperId() {
		return docOperId;
	}

	/**
	 * @param docOperId the docOperId to set
	 */
	public void setDocOperId(Long docOperId) {
		this.docOperId = docOperId;
	}

	/**
	 * @return the isDeleted
	 */
	/*@Column(name="IS_DELETED")
	public Integer getIsDeleted() {
		return isDeleted;
	}

	*//**
	 * @param isDeleted the isDeleted to set
	 *//*
	public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}*/
	
	@Column(name = "PACK_DETAIL_ID")
    public Long getPackageDetailId() {
        return packageDetailId;
    }

    public void setPackageDetailId(Long packageDetailId) {
        this.packageDetailId = packageDetailId;
    }

    public void setPackageInfoDetail(PackageInfoDetail packageInfoDetail) {
        this.packageInfoDetail = packageInfoDetail;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PACK_DETAIL_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    public PackageInfoDetail getPackageInfoDetail() {
        return packageInfoDetail;
    }
	
}
