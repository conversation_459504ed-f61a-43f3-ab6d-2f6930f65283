package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.delivery.deliveryorder.service.DoWaveExService;
import com.daxia.wms.delivery.recheck.dao.CartonHeaderDAO;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.master.dao.CityDao;
import com.daxia.wms.master.dao.CountyDao;
import com.daxia.wms.master.dao.ProvinceDao;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.waybill.zeny.dto.ZenyMainWaybillDetail;
import com.daxia.wms.waybill.zeny.dto.ZenyMainWaybillRequest;
import com.daxia.wms.waybill.zeny.service.ZenyWaybillService;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.util.ArrayList;
import java.util.List;

@Name("zenyCartonNoGenerateService")
@lombok.extern.slf4j.Slf4j
public class ZenyCartonNoGenerateServiceImpl implements CartonNoGenerateService {
    @In
    private DoWaveExService doWaveExService;
    @In(create = true)
    ZenyWaybillService zenyWaybillService;
    @In
    private WarehouseCarrierService warehouseCarrierService;
    @In
    private WarehouseService warehouseService;
    @In
    private ProvinceDao provinceDao;
    @In
    private CityDao cityDao;
    @In
    private CountyDao countyDao;
    @In
    private CartonHeaderDAO cartonHeaderDAO;
    @In
    private SequenceGeneratorService sequenceGeneratorService;


    @Override
    @Transactional
    public void generatorCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        WarehouseCarrier warehouseCarrier = warehouseCarrierService.getCarrierInfoByWarehouseIdAndCarrierId(doHeader
                .getWarehouseId(), doHeader.getCarrierId());
        Warehouse warehouse = warehouseService.getLocalWarehouse();
        DoWaveEx doWaveEx = doWaveExService.findByDoHeaderId(doHeader.getId());

//        if (StringUtils.isBlank(doWaveEx.getTrackingNo())) {
            //主单号接口
        //生成一个箱号作为业务单号
            String txLogisticId = doHeader.getDoNo() + sequenceGeneratorService.generateSequenceNo(Constants.SequenceName
                    .CARTONNO.getValue(), ParamUtil.getCurrentWarehouseId());
            cartonHeader.setTrackingNo(txLogisticId);

            ZenyMainWaybillRequest request = new ZenyMainWaybillRequest();
            request.setBizno(warehouseCarrier.getExt1());
            ZenyMainWaybillDetail mainWaybillDetail = new ZenyMainWaybillDetail();
            mainWaybillDetail.setBizno(warehouseCarrier.getExt1());
            mainWaybillDetail.setBizname(warehouseCarrier.getExt2());
            mainWaybillDetail.setBizorderno(txLogisticId);
            mainWaybillDetail.setBizwaybillno(txLogisticId);
            mainWaybillDetail.setReceiver(doHeader.getConsigneeName());
            mainWaybillDetail.setReceiveraddress(doHeader.getAddress().replaceAll(",","").replaceAll("\\*",""));
            if (StringUtil.isNotEmpty(doHeader.getProvinceName())) {
                mainWaybillDetail.setReceiverprovinces(doHeader.getProvinceName());
            } else {
                mainWaybillDetail.setReceiverprovinces(provinceDao.get(doHeader.getProvince()).getProvinceCname());
            }
            if (StringUtil.isNotEmpty(doHeader.getCityName())) {
                mainWaybillDetail.setReceivercity(doHeader.getCityName());
            } else {
                mainWaybillDetail.setReceivercity(cityDao.get(doHeader.getCity()).getCityCname());
            }
            if (StringUtil.isNotEmpty(doHeader.getCountyName())) {
                mainWaybillDetail.setReceiverdistrict(doHeader.getCountyName());
            } else {
                mainWaybillDetail.setReceiverdistrict(countyDao.get(doHeader.getCounty()).getCountyCname());
            }
        mainWaybillDetail.setReceivermobile(DoUtil.decryptPhone(StringUtils.isNotBlank(doHeader.getMobile()) ? doHeader.getMobile() :
                doHeader.getTelephone()));

            //oppo特殊逻辑
            if(ParamUtil.getCurrentTenantCode().equals("oppo")) {
                //oppo订单重量，unit * 0.5
                mainWaybillDetail.setTotalweight(String.valueOf((doHeader.getExpectedQty().doubleValue() > 10 ? 10 : doHeader.getExpectedQty().doubleValue())*0.5));
            }

            Integer orderNum = SystemConfig.getConfigValueInt("fee.receiverPay.unit.number", ParamUtil.getCurrentWarehouseId());
            if (orderNum != null && doHeader.getExpectedQty().intValue() < orderNum) {
                mainWaybillDetail.setPayments("到付现金");
                mainWaybillDetail.setFreights("15");
            }

            mainWaybillDetail.setSender(warehouse.getContactor());
            mainWaybillDetail.setSenderprovinces(warehouse.getProvince().getProvinceCname());
            mainWaybillDetail.setSendercity(warehouse.getCity().getCityCname());
            mainWaybillDetail.setSenderdistrict(warehouse.getCounty().getCountyCname());
            mainWaybillDetail.setSenderaddress(warehouse.getAddressName());
            mainWaybillDetail.setSendermobile(StringUtils.isNotBlank(warehouse.getMobile()) ? warehouse.getMobile() :
                    warehouse.getPhone());
            mainWaybillDetail.setCodamounts(doHeader.getReceivable().toString());
            List<ZenyMainWaybillDetail> data = new ArrayList<ZenyMainWaybillDetail>();
            data.add(mainWaybillDetail);
            request.setData(data);

            String mainNo = zenyWaybillService.getMainNo(request,warehouseCarrier.getContentUrl(),warehouseCarrier.getAppSecret());
            doWaveEx.setTrackingNo(mainNo);
            doWaveExService.update(doWaveEx);
            cartonHeader.setCartonNo(mainNo);
            cartonHeader.setWayBill(mainNo);
//        }
//        else {
//            if (!isDoMainCarrierNoCartoned(doWaveEx.getTrackingNo())) {
//                cartonHeader.setCartonNo(doWaveEx.getTrackingNo());
//                cartonHeader.setWayBill(doWaveEx.getTrackingNo());
//            } else {
//                //子单号接口
//                ZenySubWaibillRequest request = new ZenySubWaibillRequest();
//                request.setBizno(warehouseCarrier.getExt1());
//                ZenySubWaybillDetail detail = new ZenySubWaybillDetail();
//                detail.setBizno(warehouseCarrier.getExt1());
//                detail.setBizname(warehouseCarrier.getExt2());
//                detail.setBizorderno(doHeader.getDoNo());
//                detail.setBizwaybillno(doHeader.getDoNo());
//                detail.setWaybillno(doWaveEx.getTrackingNo());
//                List<ZenySubWaybillDetail> data = new ArrayList<ZenySubWaybillDetail>();
//                data.add(detail);
//                request.setData(data);
//
//                String subNo = zenyWaybillService.getSubNo(request,warehouseCarrier.getContentUrl(),warehouseCarrier.getAppSecret());
//                cartonHeader.setCartonNo(subNo);
//                cartonHeader.setWayBill(subNo);
//            }
//        }
    }

//    private boolean isDoMainCarrierNoCartoned(String doMainCarrierNo) {
//        Map<String, Object> params = new HashMap<String, Object>(2, 1);
//        params.put("cartonNo", doMainCarrierNo);
//        params.put("warehouseId", ParamUtil.getCurrentWarehouseId());
//        return cartonHeaderDAO.isExists(params, null);
//    }
}