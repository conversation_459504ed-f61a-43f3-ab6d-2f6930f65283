package com.daxia.wms.delivery.print.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.service.Dictionary;
import com.daxia.framework.common.service.ReportGenerator;
import com.daxia.framework.common.util.CompareUtil;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ListUtil.ListMegareOpr;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.framework.system.service.CfgWarehouseService;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.print.dto.PrintCfg;
import com.daxia.wms.delivery.print.dto.ReplPrintDTO;
import com.daxia.wms.delivery.print.service.PrintReplService;
import com.daxia.wms.delivery.task.replenish.dao.ReplenishHeaderDAO;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishHeader;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishTask;
import com.daxia.wms.delivery.task.replenish.service.ReplTaskService;
import com.daxia.wms.Constants.ReplStatus;
import com.daxia.wms.Constants.ReplType;
import com.daxia.wms.Constants.TaskStatus;
import com.daxia.wms.Constants.TaskType;
import com.daxia.wms.stock.task.entity.TrsTask;
import com.daxia.wms.stock.task.filter.TrsTaskFilter;

/**
 *  补货单打印业务实现类
 */
@Name("com.daxia.wms.delivery.printReplService")
@lombok.extern.slf4j.Slf4j
public class PrintReplServiceImpl implements PrintReplService {

    @In
    private ReplenishHeaderDAO replenishHeaderDAO;
    
    @In
    private CfgWarehouseService cfgWarehouseService;
    
    @In
    private ReportGenerator reportGenerator;
    
    @In
    private ReplTaskService replTaskService;
    
    /**
     * 补货单模板文件
     */
    public enum ReplReportName {
        REPL_REPORT("repl/replTask", null),REPL_REPORTA("repl/replHeaderA", "repl/replTaskA"),REPL_REPORT_A5("repl/replTaskA5", null);
        
        private String name;

        private String subName;

        ReplReportName(String name, String subName) {
            this.name = name;
            this.subName = subName;
        }

        public String getName() {
            return this.name;
        }

        public String getSubName() {
            return this.subName;
        }
    }
    
    /**
     * 补货单纸张大小
     * 
     * <pre>
     * 0---A4
     * 1---A5
     * </pre>
     */
    public enum PrintReplPaperSize {
        A4_0("0"), A5_1("1");

        private String value;

        PrintReplPaperSize(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
    
    /**
     * 导出补货单(多页，每页都有单头信息)
     */
    @Override
    public byte[] generatePDF(List<Long> idList) {
        if (ListUtil.isNullOrEmpty(idList)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        List<ReplenishHeader> replList = replenishHeaderDAO.findReplHeaderList(idList);
        byte[] exportData = null;
        List<String> errorList = new ArrayList<String>();
        boolean isContinue = true;// 是否继续构造打印数据标识
        for (ReplenishHeader replHeader : replList) {
            // 首先校验补货单头状态是否是【已发布】或【【进行中】
            if (StringUtil.isNotIn(replHeader.getStatus(), ReplStatus.RELEASED.getValue(),
                    ReplStatus.INPROGRESS.getValue())) {
                throw new DeliveryException(DeliveryException.REPL_STATUS_ERROR_FOR_PRINT, replHeader.getReplNo());

            }
            // 合并补货任务
            List<TrsTask> replTasks = getReportData(replHeader.getId());
            // 判断补货单下是否含有【已发布】的补货任务
            if (ListUtil.isNullOrEmpty(replTasks)) {
                errorList.add(replHeader.getReplNo());
                // 设置不在构造打印数据
                isContinue = false;
            }
            if (isContinue) {
            	byte[] data;
            	boolean isUseA5 = checkReplPaperSize(PrintReplPaperSize.A5_1.getValue());
            	if(isUseA5){
            		data = reportGenerator.generatePDF(ReplReportName.REPL_REPORT_A5.getName(), null,
                            getReportParam(replHeader), replTasks, false);
            	}else{
            		data = reportGenerator.generatePDF(ReplReportName.REPL_REPORT.getName(), null,
                            getReportParam(replHeader), replTasks, false);
            	}
                if (null == exportData) {
                    exportData = new byte[data.length];
                }
                exportData = byteMerger(data, exportData);
            }
        }
        if (ListUtil.isNotEmpty(errorList)) {
            throw new DeliveryException(DeliveryException.NO_PUB_TASK_IN_REPL_HEADER, errorList.toString());
        }
        return exportData;
    }
    
    /**
     * 打印补货单（多页，每页都有单头）
     */
    @Override
    public List<String> print(List<Long> idList) {
        if (ListUtil.isNullOrEmpty(idList)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        List<ReplenishHeader> replList = replenishHeaderDAO.findReplHeaderList(idList);
        List<String> printData = new ArrayList<String>();
        List<String> errorList = new ArrayList<String>();
        boolean isContinue = true;// 是否继续构造打印数据标识
        for (ReplenishHeader replHeader : replList) {
            // 首先校验补货单头状态是否是【已发布】或【【进行中】
            if (StringUtil.isNotIn(replHeader.getStatus(), ReplStatus.RELEASED.getValue(),
                    ReplStatus.INPROGRESS.getValue())) {
                throw new DeliveryException(DeliveryException.REPL_STATUS_ERROR_FOR_PRINT, replHeader.getReplNo());

            }
            // 合并补货任务
            List<TrsTask> replTasks = getReportData(replHeader.getId());
            // 判断补货单下是否含有【已发布】的补货任务
            if (ListUtil.isNullOrEmpty(replTasks)) {
                errorList.add(replHeader.getReplNo());
                // 设置不在构造打印数据
                isContinue = false;
            }
            if (isContinue) {
            	boolean isUseA5 = checkReplPaperSize(PrintReplPaperSize.A5_1.getValue());
            	if(isUseA5){
            		printData.addAll(reportGenerator.builtPrintDataNoSub(ReplReportName.REPL_REPORT_A5.getName(),
                            getReportParam(replHeader), replTasks));
            	}else{
            		printData.addAll(reportGenerator.builtPrintDataNoSub(ReplReportName.REPL_REPORT.getName(),
                            getReportParam(replHeader), replTasks));
            	}
                
            }
        }
        if (ListUtil.isNotEmpty(errorList)) {
            throw new DeliveryException(DeliveryException.NO_PUB_TASK_IN_REPL_HEADER, errorList.toString());
        }
        return printData;
    }
    
    /**
     * 设置补货单打印参数
     * @return
     */
    @Override
    public PrintCfg setDoPrintCfg() {
        PrintCfg replPrintCfg = new PrintCfg();    
        boolean isUseA5 = checkReplPaperSize(PrintReplPaperSize.A5_1.getValue());
        //若使用A5纸张，设置参数
        if (isUseA5) {
        	replPrintCfg.setPageType("A5");
        }
        return replPrintCfg;
    }

    /**
     * 打印补货单(多页只显示一个单头)
     */
    @Override
    public List<String> printReplA(List<Long> idList) {
        if (ListUtil.isNullOrEmpty(idList)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        List<ReplenishHeader> replList = replenishHeaderDAO.findReplHeaderList(idList);
        List<String> printData = new ArrayList<String>();
        List<String> errorList = new ArrayList<String>();
        boolean isContinue = true;//是否继续构造打印数据标识
        for (ReplenishHeader replHeader : replList) {
            //首先校验补货单头状态是否是【已发布】或【【进行中】
            if (StringUtil.isNotIn(replHeader.getStatus(), ReplStatus.RELEASED.getValue(), ReplStatus.INPROGRESS.getValue())) {
                throw new DeliveryException(DeliveryException.REPL_STATUS_ERROR_FOR_PRINT, replHeader.getReplNo());
                
            }
            //合并补货任务
            List<ReplenishTask> replTaskList = buildReplTasks(replHeader);
            //判断补货单下是否含有【已发布】的补货任务
            if (ListUtil.isNullOrEmpty(replTaskList)) {
                errorList.add(replHeader.getReplNo());
                //设置不在构造打印数据
                isContinue = false;
            }
            if (isContinue) {
                ReplPrintDTO replPrintDTO = buildPrintDTO(replHeader);
                replPrintDTO.setReplTaskList(replTaskList);
                List<ReplPrintDTO> dtoList = new ArrayList<ReplPrintDTO>();
                dtoList.add(replPrintDTO);
                printData.addAll(reportGenerator.builtPrintData(ReplReportName.REPL_REPORTA.getName(),
                        ReplReportName.REPL_REPORTA.getSubName(), getParams(), dtoList));
            }
        }
        if (ListUtil.isNotEmpty(errorList)) {
            throw new DeliveryException(DeliveryException.NO_PUB_TASK_IN_REPL_HEADER, errorList.toString());
        }
        return printData;
    }
    
    /**
     * 获取补货打印的参数集合
     * @param replHeader
     * @return
     */
    public Map<String, Object> getParams() {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("warehouseName", cfgWarehouseService.getCurrentWarehouseName());
        return params;
    }
    
    private ReplPrintDTO buildPrintDTO(ReplenishHeader replHeader) {
        ReplPrintDTO dto = new ReplPrintDTO();
        dto.setCreateTime(replHeader.getCreatedAt());
        dto.setReplHeaderNo(replHeader.getReplNo());
        dto.setSkuCounts(replHeader.getSkus());
        dto.setUnitCounts(replHeader.getUnits());
        dto.setEarliestPlanShipTime(replHeader.getEarlistPlanShipTime());
        // 设置即时补货业务类型信息
        if (ReplType.JP.getValue().equals(replHeader.getReplType())) {
            if (StringUtil.isNotEmpty(replHeader.getDoType())) {
                Map<String, String> jpTypes = Dictionary.getDictionary("REPL_DO_TYPE");
                dto.setJpType(jpTypes.get(replHeader.getDoType()));
            }
        }        
        return dto;
    }
    
    /**
     * 获取补货任务的数据
     * @param replHeaderId
     * @return
     */
    private List<ReplenishTask> buildReplTasks(ReplenishHeader replHeader) {
        List<ReplenishTask> resultTasks = new ArrayList<ReplenishTask>();
        for (ReplenishTask task : replHeader.getReplTaskList()) {
            if (TaskType.RP.getValue().equals(task.getTaskType()) && TaskStatus.RELEASED.getValue().equals(task.getTaskStatus())) {
                resultTasks.add(task);
            }
        }

        ListUtil.megareList(resultTasks, new ListMegareOpr<ReplenishTask>() {
            @Override
            public boolean isNeedMegare(ReplenishTask t1, ReplenishTask t2) {
                if (t1.getDocOperId().compareTo(t2.getDocOperId()) != 0) {
                    throw new DeliveryException(DeliveryException.REP_TASK_NO_NOT_EQUALS);
                }

                return CompareUtil.compare(t1.getSkuId(), t2.getSkuId())
                        && CompareUtil.compare(t1.getFmLocId(), t2.getFmLocId())
                        && CompareUtil.compare(t1.getToLocId(), t2.getToLocId());
            }

            @Override
            public void megareOpr(ReplenishTask t1, ReplenishTask t2) {
                t1.setQty(t1.getQty().add(t2.getQty()));
            }
        });
        return resultTasks;
    }
    
    

    /**
     * 获取补货任务的数据
     * @param replHeaderId
     * @return
     */
    private List<TrsTask> getReportData(Long replHeaderId) {
        TrsTaskFilter taskFilter = new TrsTaskFilter();
        taskFilter.setDocOperId(replHeaderId);
        taskFilter.setTaskStatus(TaskStatus.RELEASED.getValue());
        taskFilter.setTaskType(TaskType.RP.getValue());
//         List<TrsTask> tasks = replTaskService.queryReplTrs(taskFilter);// TODO 注释报错后续修改
        List<TrsTask> tasks = new ArrayList<TrsTask>();// TODO 注释报错后续修改
                ListUtil.megareList(tasks, new ListMegareOpr<TrsTask>() {
            @Override
            public boolean isNeedMegare(TrsTask t1, TrsTask t2) {
                if (t1.getDocOperId().compareTo(t2.getDocOperId()) != 0) {
                    throw new DeliveryException(DeliveryException.REP_TASK_NO_NOT_EQUALS);
                }

                return CompareUtil.compare(t1.getSkuId(), t2.getSkuId())
                        && CompareUtil.compare(t1.getFmLocId(), t2.getFmLocId())
                        && CompareUtil.compare(t1.getToLocId(), t2.getToLocId());
            }

            @Override
            public void megareOpr(TrsTask t1, TrsTask t2) {
                t1.setQty(t1.getQty().add(t2.getQty()));
            }
        });
        return tasks;
    }

    /**
     * 获取打印参数
     * @param replHeader
     * @return
     */
    private Map<String, Object> getReportParam(ReplenishHeader replHeader) {
        if (null == replHeader) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("warehouseName", cfgWarehouseService.getCurrentWarehouseName());
        parameters.put("replHeaderNo", replHeader.getReplNo());
        parameters.put("createTime", replHeader.getCreatedAt());
        parameters.put("skuCounts", replHeader.getSkus());
        parameters.put("unitCounts", replHeader.getUnits());
        parameters.put("ePlanShipTime", replHeader.getEarlistPlanShipTime());
        //设置即时补货业务类型信息
        if (ReplType.JP.getValue().equals(replHeader.getReplType())) {
            if (StringUtil.isNotEmpty(replHeader.getDoType())) {
                Map<String, String> jpTypes = Dictionary.getDictionary("REPL_DO_TYPE");
                parameters.put("jpType", jpTypes.get(replHeader.getDoType()));
            }
        }
        
        return parameters;
    }
    
    /**
     * 合并两个byte数组
     * @param byte_1
     * @param byte_2
     * @return
     */
    public byte[] byteMerger(byte[] byte_1, byte[] byte_2){  
        byte[] byte_3 = new byte[byte_1.length+byte_2.length];  
        System.arraycopy(byte_1, 0, byte_3, 0, byte_1.length);  
        System.arraycopy(byte_2, 0, byte_3, byte_1.length, byte_2.length);  
        return byte_3;
    }  

    /**
     * 判断给定补货单打印纸张大小和系统中配置的是否相等
     */
    @Override
    public boolean checkReplPaperSize(String PaperSize) {
        String cfgPaperSize = SystemConfig.getConfigValue("print.repl.useA5", ParamUtil.getCurrentWarehouseId());
        if (null != PaperSize && null != cfgPaperSize && PaperSize.equals(cfgPaperSize)) {
            return true;
        }
        return false;
    }
}
