package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.dao.CartonCancelLogDAO;
import com.daxia.wms.delivery.recheck.entity.CartonCancelLog;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonCancelLogService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.util.Date;

@Name("cartonCancelLogService")
@lombok.extern.slf4j.Slf4j
public class CartonCancelLogServiceImpl implements CartonCancelLogService {

    @In
    CartonCancelLogDAO cartonCancelLogDAO;

    @Override
    @Transactional
    public void add(DeliveryOrder<PERSON>ead<PERSON> doHeader, CartonHeader cartonHeader, Boolean isSuccess) {
        if(StringUtil.isEmpty(cartonHeader.getWayBill())){
            return;
        }

        CartonCancelLog cartonCancelLog = new CartonCancelLog();

        cartonCancelLog.setCancelTime(new Date());
        cartonCancelLog.setCartonNo(cartonHeader.getCartonNo());
        cartonCancelLog.setDoHeadaerId(doHeader.getId());
        cartonCancelLog.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        cartonCancelLog.setWaybill(cartonHeader.getWayBill());
        cartonCancelLog.setWaybillType(doHeader.getCarrier().getWaybillType());
        cartonCancelLog.setCarrierId(doHeader.getCarrierId());// 防止订单配送商被修改
        cartonCancelLog.setCancelSuccess(isSuccess ? Constants.YesNo.YES : Constants.YesNo.NO);

        cartonCancelLogDAO.save(cartonCancelLog);
    }
}