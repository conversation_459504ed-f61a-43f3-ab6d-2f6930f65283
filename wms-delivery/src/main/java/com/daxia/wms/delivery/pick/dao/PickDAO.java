package com.daxia.wms.delivery.pick.dao;

import java.math.BigDecimal;
import java.util.*;

import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto;
import com.daxia.wms.delivery.pick.dto.PickTaskDto;
import com.daxia.wms.delivery.pick.filter.PickHeaderFilter;
import com.daxia.wms.delivery.store.dto.StoreOutTaskDTO;
import com.daxia.wms.master.entity.MergeLoc;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;
import org.hibernate.Hibernate;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.pick.dto.PickInfoDTO;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.LaborForceSwitch;
import com.daxia.wms.Constants.PktMergeStatus;
import com.daxia.wms.master.entity.Partition;

@Name("com.daxia.wms.delivery.pickDAO")
@lombok.extern.slf4j.Slf4j
public class PickDAO extends HibernateBaseDAO<PickHeader, Long> {

    private static final long serialVersionUID = 5972850272494254957L;

    /**
     * 根据波次主键查询拣货单记录数
     *
     * @param waveHeadId 波次主键
     * @return 拣货单记录数
     */
    public Long findPktHeaderCount(Long waveHeadId) {
        Long count = Long.valueOf(0);
        String hql = "select count(o.id) from PickHeader o where o.waveHeadId =:waveHeadId and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setLong("waveHeadId", waveHeadId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        Object obj = query.uniqueResult();
        if (obj != null) {
            count = (Long) obj;
        }
        return count;
    }

    /**
     * <pre>
     * Description:根据拣货单的拣货单号获取拣货单
     * </pre>
     *
     * @param pktNo
     * @return
     */
    public PickHeader getPktHeaderByPktNo(String pktNo) {
        PickHeader pktHeader = null;
        String hql = "from PickHeader o where o.pktNo = ? and o.warehouseId = ?";
        Query query = this.createQuery(hql);
        query.setString(0, pktNo);
        query.setLong(1, ParamUtil.getCurrentWarehouseId());
        Object obj = query.uniqueResult();
        if (obj != null) {
            pktHeader = (PickHeader) obj;
        }
        return pktHeader;
    }

    /**
     * Description:根据拣货单的波次号获取拣货单
     *
     * @param waveId
     * @return
     */

    @SuppressWarnings("unchecked")
    public List<PickHeader> getPktHeadersByWaveId(Long waveId) {
        String hql = "from PickHeader o where o.waveHeadId = ? and o.warehouseId = ?";
        Query query = this.createQuery(hql);
        query.setLong(0, waveId);
        query.setLong(1, ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * Description:根据拣货单的波次号获取拣货单
     *
     * @param waveId
     * @return
     */

    @SuppressWarnings("unchecked")
    public List<PickHeader> getPktHeadersByWaveIdAndStatus(Long waveId, String status) {
        String hql = "from PickHeader o where o.waveHeadId = :waveId and o.status = :status and o.warehouseId = :whid";
        Query query = this.createQuery(hql);
        query.setLong("waveId", waveId);
        query.setString("status", status);
        query.setLong("whid", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 获取波次的拣货单数量
     *
     * @param waveIds
     * @return
     */
    @SuppressWarnings({"unchecked"})
    public Map<? extends Long, ? extends Integer> getPktCount(List<Long> waveIds) {
        Map<Long, Integer> map = new HashMap<Long, Integer>();

        String hql = "select o.waveHeadId, count(o.id) from PickHeader o where o.waveHeadId in ( :waveIds ) and o.warehouseId = :warehouseId group by o.waveHeadId";
        Query query = this.createQuery(hql).setParameterList("waveIds", waveIds).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        List<Object[]> numList = query.list();
        for (Object[] obj : numList) {
            map.put((Long) obj[0], ((Long) obj[1]).intValue());
        }
        return map;
    }

    /**
     * 根据集货状态获取波次的拣货单数量
     *
     * @param waveIds
     * @param merged
     * @return
     */
    @SuppressWarnings("unchecked")
    public Map<? extends Long, ? extends Integer> getPktMergeCount(List<Long> waveIds, PktMergeStatus merged) {
        Map<Long, Integer> map = new HashMap<Long, Integer>();

        String hql = "select o.waveHeadId, count(o.id) from PickHeader o where o.waveHeadId in (:waveIds) and o.mergeStatus = :mergeStatus and o.warehouseId = :warehouseId group by waveHeadId";
        Query query = this.createQuery(hql).setParameterList("waveIds", waveIds)
                .setParameter("mergeStatus", Integer.valueOf(merged.getValue())).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        List<Object[]> numList = query.list();
        for (Object[] obj : numList) {
            map.put((Long) obj[0], ((Long) obj[1]).intValue());
        }
        return map;
    }

    /**
     * 将波次id为waveId的拣货单集货出库
     *
     * @param waveId
     */
    public void completeMergeOut(Long waveId) {
        String hql = " update PickHeader o set o.mergeStatus = :mergeStatus " +
                " where o.waveHeadId = :waveId and o.warehouseId = :warehouseId";
        Query query = createUpdateQuery(hql);
        query.setInteger("mergeStatus", Integer.valueOf(PktMergeStatus.OUT_MERGE.getValue()));
        query.setLong("waveId", waveId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 根据发货单头获取拣货单。不要直接使用，请调用PickHeaderServiceImpl.getPktHeadersByDo(Long)
     *
     * @param doHeaderId
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Long> getPktHeadersByDo(Long doHeaderId) {
        String hql = " select o.pktHeaderId from PickTask o where o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId";
        Query query = createQuery(hql);
        query.setLong("doHeaderId", doHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 查找波次下的拣货单及对应的库区信息
     *
     * @param waveId
     * @return
     */
    @SuppressWarnings("unchecked")
    public Map<Long, List<String>> findPktPartitionCodesInWave(Long waveId) {
        Map<Long, List<String>> map = new HashMap<Long, List<String>>();
        String hql = "select tsk.pktHeaderId, p.partitionCode from PickTask tsk, Location loc, Partition p where tsk.waveHeaderId = :waveId and loc.warehouseId = :warehouseId and p.warehouseId = :warehouseId and tsk.warehouseId = :warehouseId and tsk.locId = loc.id and loc.partitionId = p.id group by tsk.pktHeaderId, p.partitionCode order by tsk.pktHeaderId asc, p.partitionCode asc";
        Query query = createQuery(hql);
        query.setLong("waveId", waveId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        List<Object[]> list = query.list();
        for (Object[] obj : list) {
            Long pktId = (Long) obj[0];
            String code = (String) obj[1];
            List<String> codeList = null;
            if (null != map.get(pktId)) {
                codeList = map.get(pktId);
            } else {
                codeList = new ArrayList<String>();
            }
            codeList.add(code);
            map.put(pktId, codeList);
        }
        return map;
    }

    /**
     * 根据拣货单号查找其所包含的库区
     *
     * @param pktNo
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<String> findPartitionsByPktNo(String pktNo) {
        Long whId = ParamUtil.getCurrentWarehouseId();
        String sql1 = "select tp.fm_loc_id from tsk_pick tp where exists (select 1 from doc_pkt_header ph where tp.pkt_h_id = ph.id "
                + " and ph.warehouse_id = :warehouseId and ph.pkt_no = :pktNo and ph.is_deleted = 0) and tp.is_deleted = 0 and tp.WAREHOUSE_ID = :warehouseId";
        Query query1 = createSQLQuery(sql1);
        query1.setString("pktNo", pktNo);
        query1.setLong("warehouseId", whId);
        List<BigDecimal> list1 = query1.list();
        if (ListUtil.isNullOrEmpty(list1)) {
            return null;
        }
        String sql2 = "select p.PARTION_CODE from md_partition p where exists (select 1 from md_location loc where loc.partition_id = p.id "
                + " and loc.warehouse_id = :warehouseId and loc.id in (:locId) and loc.is_deleted = 0) and p.is_deleted = 0 and p.warehouse_id = :warehouseId";
        Query query2 = createSQLQuery(sql2);
        query2.setParameterList("locId", list1);
        query2.setLong("warehouseId", whId);
        return query2.list();
    }

    /**
     * 根据id设置拣货单为已打印状态
     *
     * @param ids
     */
    public void setPickHeaderPrinted(List<Long> ids) {
        Query query = this
                .createUpdateQuery("UPDATE PickHeader o SET o.pickPrintFlag = :pickPrintFlag WHERE o.id in (:ids) and o.warehouseId = :warehouseId");
        query.setParameterList("ids", ids);
        query.setParameter("pickPrintFlag", Constants.YesNo.YES.getValue().intValue());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());

        query.executeUpdate();
    }

    /**
     * 根据Id集合查询拣货单
     *
     * @param ids
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<PickHeader> findPickHeaderByIds(Collection<Long> ids) {
        String hql = "from PickHeader o where o.id in (:ids) and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql).setParameterList("ids", ids).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 计算拣货单下的units总数
     *
     * @param pktHeaderId 拣货单id
     * @return
     */
    public BigDecimal calUnitsInPktHeader(Long pktHeaderId) {
        String hql = "select sum(o.qty) from PickTask o where o.pktHeaderId = :pktHeaderId and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setParameter("pktHeaderId", pktHeaderId);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (BigDecimal) query.uniqueResult();
    }

    /**
     * 计算拣货单的汇总信息，包括总units数，总毛重，总体积
     *
     * @param pktHeaderId
     * @return
     */
    public Map<String, BigDecimal> calcTotalInfo(Long pktHeaderId) {
        String hql = "SELECT SUM(tsk.qty) AS totalUnits, IFNULL(SUM(tsk.qty * s.grossweight),0) as totalGtWeight, IFNULL(SUM(tsk.qty * s.volume),0) as totalVolume " +
                "FROM tsk_pick tsk INNER JOIN md_sku as s ON tsk.sku_id = s.id WHERE tsk.pkt_h_id = :pktHeaderId AND tsk.warehouse_id = :warehouseId " +
                "AND tsk.is_deleted = 0 AND s.is_deleted = 0";
        SQLQuery query = this.createSQLQuery(hql);
        query.setParameter("pktHeaderId", pktHeaderId);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.addScalar("totalUnits", Hibernate.BIG_DECIMAL).addScalar("totalGtWeight", Hibernate.BIG_DECIMAL)
                .addScalar("totalVolume", Hibernate.BIG_DECIMAL);
        Object[] objs = (Object[]) query.uniqueResult();
        Map<String, BigDecimal> totalMap = new HashMap<String, BigDecimal>();
        if (objs != null) {
            totalMap.put("totalUnits", (BigDecimal) objs[0]);
            totalMap.put("totalGtWeight", (BigDecimal) objs[1]);
            totalMap.put("totalVolume", (BigDecimal) objs[2]);
        }
        return totalMap;
    }

    /**
     * 根据波次及拣货单状态查区域
     *
     * @param waveIds
     * @param status  拣货单状态
     * @return
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public List<List> findAreaByWaveIdAndStatus(List<Long> waveIds, List<String> status) {
        String hql = "select new list(ph.waveHeadId, r.regionCode) from PickHeader ph, Region r where r.id = ph.regionId and "
                + " ph.status in (:status) and ph.waveHeadId in(:waveIds) "
                + " and r.warehouseId = :warehouseId and ph.warehouseId  = :warehouseId ";
        Query query = this.createQuery(hql);
        query.setParameterList("status", status);
        query.setParameterList("waveIds", waveIds);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    @SuppressWarnings("unchecked")
    public List<Partition> findPartitionsInPktHeader(Long pktHeaderId) {
        String hql = "select distinct p from PickTask task, Location loc, Partition p where task.locId = loc.id " +
                " and loc.partitionId = p.id and task.pktHeaderId = :pktHeaderId " +
                " and task.warehouseId = :warehouseId and loc.warehouseId = :warehouseId and p.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setLong("pktHeaderId", pktHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 查询指定DO的波次下指定状态拣货单
     *
     * @param doId
     * @param status
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<String> queryPktNoInDosWave(Long doId, String status) {
        StringBuffer hqlSb = new StringBuffer();
        hqlSb.append("select pkt.pktNo from PickHeader pkt where pkt.warehouseId = :warehouseId and pkt.status=:status and pkt.waveHeadId = ( ")
                .append("select do.waveId from DeliveryOrderHeader do where do.id = :doId and do.warehouseId = :warehouseId)");
        Query query = this.createQuery(hqlSb.toString());
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setString("status", status);
        query.setLong("doId", doId);
        return query.list();
    }

    /**
     * 获取全仓未指派的拣货单（已发布状态）
     *
     * @return
     */
    public Long countAllPktHeader() {
        Long count = Long.valueOf(0L);
        // 剔除建货单下有完成状态的拣货任务情况（即拣货单在‘拣货中’）
        String hql = "select count(o.id) from PickHeader o where not exists "
                + "(select 1 from PickTask p where p.status > :status and p.pktHeaderId = o.id) "
                + "and o.status = :status and o.warehouseId = :warehouseId and o.createdAt >= ADDDATE(now(),-7) and o.operUserId is null and o.isAvailable = :isAvailable";
        Query query = this.createQuery(hql);
        query.setParameter("status", Constants.PktStatus.RELEASED.getValue());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("isAvailable", Boolean.TRUE);
        query.setMaxResults(1);
        Object obj = query.uniqueResult();
        if (obj != null) {
            count = (Long) obj;
        }
        return count;
    }

    /**
     * 查询能索取任务的个数
     *
     * @param regionIdList
     * @param typeMap
     * @return
     */
    public Long countOwnPktHeader(List<Long> regionIdList,
                                  Map<String, List<String>> typeMap) {
        StringBuilder subHql = new StringBuilder();
        List<String> type1List = new ArrayList<String>();
        subHql.append(buildTypeSQL(typeMap, type1List));

        // 剔除建货单下有完成状态的拣货任务情况（即拣货单在‘拣货中’）
        String hql = "select count(pkt.id) from PickHeader pkt  where not exists "
                + "(select 1 from PickTask p where p.status > :status and p.pktHeaderId = pkt.id) and pkt.status = :status "
                + "and pkt.warehouseId = :warehouseId and pkt.operUserId is null "
                + (ListUtil.isNotEmpty(regionIdList) ? " and pkt.isAvailable = :isAvailable and pkt.regionId in (:regionIdList) " : " and pkt.isAvailable = :isAvailable ")
                + subHql.toString()
                + "and pkt.createdAt >= ADDDATE(now(),-7) ";
        Query query = this.createQuery(hql);
        if (ListUtil.isNotEmpty(regionIdList)) {
            query.setParameterList("regionIdList", regionIdList);
        }
        if (ListUtil.isNotEmpty(type1List)) {
            query.setParameterList("type1List", type1List);
        }
        query.setParameter("isAvailable", Boolean.TRUE);
        query.setParameter("status", Constants.PktStatus.RELEASED.getValue());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (Long) query.uniqueResult();
    }

    public String buildTypeSQL(Map<String, List<String>> typeMap,
                               List<String> type1List) {
        StringBuilder subHql = new StringBuilder();
        //是否有2级分类标识
        Boolean hasType2 = false;
        if (MapUtils.isNotEmpty(typeMap)) {
            subHql.append("and (");
            Set<Map.Entry<String, List<String>>> set = typeMap.entrySet();
            for (Iterator<Map.Entry<String, List<String>>> it =
                 set.iterator(); it.hasNext(); ) {
                Map.Entry<String, List<String>> entry = (Map.Entry<String, List<String>>) it.next();
                List<String> type2List = entry.getValue();
                //有2级分类的单独拼SQL 没有2级分类的放一起
                if (ListUtil.isNotEmpty(type2List)) {
                    subHql.append("( pkt.waveHeader.waveType = " + entry.getKey());
                    String type2Str = null;
                    for (int i = 0; i < type2List.size(); i++) {
                        String type2 = type2List.get(i);
                        if (i == 0) {
                            type2Str = type2;
                        } else {
                            type2Str = type2Str + "," + type2;
                        }
                    }
                    subHql.append(" and pkt.waveHeader.autoType in ( " + type2Str + ") )");
                    //subHql中有2级分类
                    hasType2 = true;
                } else {
                    type1List.add(entry.getKey());
                }
            }
            if (ListUtil.isNotEmpty(type1List)) {
                if (hasType2) {
                    subHql.append(" or ");
                }
                subHql.append(" pkt.waveHeader.waveType in ( :type1List )");
            }
            subHql.append(") ");
        }
        return subHql.toString();

    }

    /**
     * 人工索取一条拣货单(根据区域索取并排序)
     *
     * @param laborSwitch
     * @return
     */
    public PickHeader findPktHeaderForDemand(List<Long> regionIdList, Map<String, List<String>> typeMap, Integer laborSwitch, Long waveId) {
        StringBuilder subHql = new StringBuilder();
        List<String> type1List = new ArrayList<String>();
        subHql.append(buildTypeSQL(typeMap, type1List));
        // 剔除建货单下有完成状态的拣货任务情况（即拣货单在‘拣货中’）
        String hql = "from PickHeader pkt  where not exists "
                + "(select 1 from PickTask p where p.status > :status and p.pktHeaderId = pkt.id) and pkt.status = :status "
                + "and pkt.warehouseId = :warehouseId and pkt.operUserId is null "
                + (ListUtil.isNotEmpty(regionIdList) ? " and pkt.isAvailable = :isAvailable and pkt.regionId in (:regionIdList) " : " and pkt.isAvailable = :isAvailable ")
                + subHql.toString()
                + "and pkt.createdAt >= ADDDATE(now(),-7) ";

        if (waveId != null) {
            hql += "and pkt.waveHeadId = :waveHeadId ";
        }
        if (LaborForceSwitch.RELAY.getValue().equals(laborSwitch)) {
            // region.pickOrder用来保证分区域接力
            hql = hql + " order by pkt.region.pickOrder, pkt.waveHeader.estDoFinishTime, pkt.waveHeader.priority, pkt.waveHeader.waveNo, pkt.createdAt ";
        } else if (LaborForceSwitch.PARALLEL.getValue().equals(laborSwitch)) {
            // waveHeader.mergeTime用来保证良好的并行度
            hql = hql + " order by pkt.waveHeader.estDoFinishTime, pkt.waveHeader.priority, pkt.waveHeader.mergeTime, pkt.createdAt ";
        }
        Query query = this.createQuery(hql);
        if (ListUtil.isNotEmpty(regionIdList)) {
            query.setParameterList("regionIdList", regionIdList);
        }
        if (ListUtil.isNotEmpty(type1List)) {
            query.setParameterList("type1List", type1List);
        }
        if (waveId != null) {
            query.setParameter("waveHeadId", waveId);
        }
        query.setParameter("isAvailable", Boolean.TRUE);
        query.setParameter("status", Constants.PktStatus.RELEASED.getValue());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (PickHeader) query.uniqueResult();
    }

    /**
     * 人工索取一条拣货单(根据区域索取并排序)
     *
     * @return
     */
    public Object[] findSH5PktHeaderForDemand(List<Long> regionIdList, Map<String, List<String>> typeMap) {
        StringBuffer sql = new StringBuffer();
        sql.append("select * from ( ");
        sql.append(" select dph.id,dph.pkt_no,");
        sql.append(" GROUP_CONCAT(mc.container_no) As Container_No,");
        sql.append(" dwh.e_do_finish_time from doc_pkt_header dph, doc_wave_header dwh");
        sql.append(" left join md_container mc on dwh.wave_no = mc.doc_no,md_region mr");
        sql.append(" where dph.wave_header_id = dwh.id");
        sql.append(" and not exists (select 1 from tsk_pick tp where tp.is_deleted = 0 and tp.status > :status and tp.pkt_h_id = dph.id )");
        sql.append(" and dph.status = :status and dph.oper_user_id is null and dph.is_available = :isAvailable ");
        if (ListUtil.isNotEmpty(regionIdList)) {
            sql.append(" and dph.region_id in (:regionIdList)");
        }

        StringBuilder subHql = new StringBuilder();
        List<String> type1List = new ArrayList<String>();
        if (MapUtils.isNotEmpty(typeMap)) {
            //是否有2级分类标识
            Boolean hasType2 = false;
            subHql.append("and (");
            Set<Map.Entry<String, List<String>>> set = typeMap.entrySet();
            for (Iterator<Map.Entry<String, List<String>>> it =
                 set.iterator(); it.hasNext(); ) {
                Map.Entry<String, List<String>> entry = (Map.Entry<String, List<String>>) it.next();
                List<String> type2List = entry.getValue();
                //有2级分类的单独拼SQL 没有2级分类的放一起
                if (ListUtil.isNotEmpty(type2List)) {
                    subHql.append("( dwh.WAVE_TYPE = '" + entry.getKey() + "'");
                    String type2Str = null;
                    for (int i = 0; i < type2List.size(); i++) {
                        String type2 = type2List.get(i);
                        if (i == 0) {
                            type2Str = type2;
                        } else {
                            type2Str = type2Str + "," + type2;
                        }
                    }
                    subHql.append(" and dwh.AUTO_TYPE in ( " + type2Str + ") )");
                    //subHql中有2级分类
                    hasType2 = true;
                } else {
                    type1List.add(entry.getKey());
                }
            }
            if (ListUtil.isNotEmpty(type1List)) {
                //subHql中 有包含2级类型sql 并且 无2级分类type2List不为空 则把他们2个用or 连起来
                if (hasType2) {
                    subHql.append(" or ");
                }
                subHql.append(" dwh.WAVE_TYPE in ( :type1List )");
            }
            subHql.append(")");
            sql.append(subHql.toString());
        }

        sql.append(" and dph.region_id = mr.id and dph.warehouse_id = dwh.warehouse_id");
        sql.append(" and dph.warehouse_id = :warehouseId and dwh.create_time >= ADDDATE(now(),-7) ");
        sql.append(" group by dwh.e_do_finish_time,dwh.priority, dph.pkt_no,dph.id,dph.create_time Order By mc.container_no ");
        sql.append(" ) t order by t.e_do_finish_time asc,t.Container_No asc,t.pkt_no,t.id");

        Query query = this.createSQLQuery(sql.toString());
        query.setParameter("isAvailable", Constants.YesNo.YES.getValue());
        query.setParameter("status", Constants.PktStatus.RELEASED.getValue());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        if (ListUtil.isNotEmpty(regionIdList)) {
            query.setParameterList("regionIdList", regionIdList);
        }
        if (ListUtil.isNotEmpty(type1List)) {
            query.setParameterList("type1List", type1List);
        }
        query.setMaxResults(1);
        return (Object[]) query.uniqueResult();
    }

    /**
     * 根据库位与波次查找拣货单
     *
     * @param locId
     * @param waveId
     * @return
     */
    public PickHeader findPktHeaderByLocAndWave(Long locId, Long waveId) {
        String hql = "select pkt from PickHeader pkt, Region r, Partition p, Location l where "
                + "pkt.regionId = r.id and p.regionId = r.id and p.id = l.partitionId and l.id = :locId "
                + "and pkt.waveHeadId = :waveId";
        Query query = this.createQuery(hql);
        query.setParameter("locId", locId);
        query.setParameter("waveId", waveId);
        query.setMaxResults(1);
        return (PickHeader) query.uniqueResult();
    }

    /**
     * 根据波次Id找到 拣货任务
     *
     * @param waveHeadId
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<PickHeader> findPktHeaderByWaveIdOrderByRegin(Long waveHeadId, Boolean isAvailable) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        StringBuffer hql = new StringBuffer();
        hql.append("select pkt from PickHeader pkt left join pkt.region r where");
        hql.append(" pkt.waveHeadId = :waveHeadId");
        hql.append(" and pkt.status = :status");
        hql.append(" and pkt.isAvailable = :isAvailable");
        hql.append(" and pkt.warehouseId = :warehouseId ");
        hql.append(" and r.pickOrder is not null");
        hql.append(" order by r.pickOrder");
        paramMap.put("waveHeadId", waveHeadId);
        paramMap.put("warehouseId", ParamUtil.getCurrentWarehouseId());
        paramMap.put("status", Constants.PktStatus.RELEASED.getValue());
        paramMap.put("isAvailable", isAvailable);
        Query query = this.createQuery(hql.toString());
        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            query.setParameter(entry.getKey(), entry.getValue());
        }
        return query.list();
    }

    public void clearCartonStatus(Long waveId, Long cartonId) {
        this.createUpdateQuery("UPDATE PickHeader o SET o.cartonId = null WHERE o.cartonId = :cartonId AND o.waveHeadId = :waveId AND o.warehouseId = :warehouseId").setParameter("cartonId", cartonId).setParameter("waveId", waveId).setParameter
                ("warehouseId", ParamUtil.getCurrentWarehouseId()).executeUpdate();
    }

    public void batchUpdatePickHeader4Pick(List<Long> batchIdList, String updateBy, Long waveId) {
        String sql = "update doc_pkt_header dd " +
                " inner join tsk_pick p on p.pkt_h_id = dd.id and dd.warehouse_id = p.warehouse_id and p.is_deleted =0 " +
                " left join ( SELECT  count(*) as total, tp.pkt_h_id,tp.warehouse_id " +
                " from tsk_pick tp where  tp.is_deleted =0 and tp.status not in (:pickTaskList) and tp.wave_h_id =:waveId " +
                " group by tp.pkt_h_id,tp.warehouse_id) t" +
                " on t.pkt_h_id = dd.id and t.warehouse_id = dd.warehouse_id " +
                " set dd.status = (case ifnull(t.total,0) when 0 then '99' else '50' end)," +
                " dd.pick_by=:updateBy,dd.update_time=now(),dd.pk_from_time=now(),dd.pk_end_time=now() " +
                " where dd.warehouse_id =:warehouseId and p.id in (:batchIdList) and dd.is_deleted =0";

        SQLQuery sqlQuery = this.createSQLQuery(sql);
        sqlQuery.setParameter("updateBy", updateBy);
        sqlQuery.setParameter("waveId", waveId);
        sqlQuery.setParameterList("batchIdList", batchIdList);
        sqlQuery.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        List<String> pickTaskList = Lists.newArrayList();
        pickTaskList.add(DoStatus.ALLPICKED.getValue());
        pickTaskList.add(DoStatus.CANCELED.getValue());
        sqlQuery.setParameterList("pickTaskList", pickTaskList);
        sqlQuery.executeUpdate();
    }

    public Boolean isPickFinished(MergeLoc mergeLoc) {
        String sql = "select tp.id from tsk_pick tp inner join doc_do_header dh on dh.id = tp.doc_id " +
                " where dh.do_no = :docNo and tp.status not in (:statusList) and tp.is_deleted = 0 and tp.warehouse_id =:warehouseId ";
        if (PickHeader.PKT_TYPE_PCS.equals(mergeLoc.getPktType())) {
            sql += " and tp.qty = tp.qty_unit ";
        } else {
            sql += " and tp.qty != tp.qty_unit ";
        }
        SQLQuery sqlQuery = this.createSQLQuery(sql);
        sqlQuery.setParameter("docNo", mergeLoc.getDocNo());
        sqlQuery.setParameterList("statusList", Arrays.asList(DoStatus.CANCELED.getValue(), DoStatus.ALLPICKED.getValue()));
        sqlQuery.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        List list = sqlQuery.list();
        if (ListUtil.isNullOrEmpty(list)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public Boolean isMergeInFinished(MergeLoc mergeLoc) {
        String sql = "select mc.id from md_container mc inner join doc_pkt_header ph on mc.ref_no_1 = ph.pkt_no and ph.is_deleted = 0 " +
                " where mc.doc_no = :docNo and mc.business_status != :businessStatus and mc.is_deleted = 0 and ph.pkt_type = :pktType " +
                " and mc.warehouse_id =:warehouseId and ph.warehouse_id =:warehouseId";
        SQLQuery sqlQuery = this.createSQLQuery(sql);
        sqlQuery.setParameter("docNo", mergeLoc.getDocNo());
        sqlQuery.setParameter("pktType", mergeLoc.getPktType());
        sqlQuery.setParameter("businessStatus", Constants.ContainerBusinessStatus.MERGED.getValue());
        sqlQuery.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        List list = sqlQuery.list();
        if (ListUtil.isNullOrEmpty(list)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public List<StoreOutTaskDTO> findStoreOutTask(String mergePartition) {
        String sql = " select ml.status finishedFlag,ml.merge_code storeLoc,group_concat(mc.container_no) turnoverBoxNos,ml.pkt_type  pktType" +
                " from md_merge_loc ml inner join md_container mc " +
                " on ml.doc_no = mc.doc_no and ml.doc_type = mc.doc_type " +
                " inner join doc_pkt_header ph on ph.pkt_no = mc.ref_no_1 ";
        if (StringUtil.isNotBlank(mergePartition)) {
            sql += " inner join md_merge_partition mp on mp.id = ml.merge_partition_id  and mp.id = :mergePartition ";
        }
        sql += " where ml.warehouse_id = :warehouseId and ml.is_deleted = 0 and mc.business_status = :businessStatus " +
                " and ph.pkt_type = ml.pkt_type " +
                " group by ml.merge_code,ml.status,ml.pkt_type order by ml.status desc,ml.pkt_type asc,ml.update_time ";
        SQLQuery sqlQuery = createSQLQuery(sql);
        sqlQuery.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        if (StringUtil.isNotBlank(mergePartition)) {
            sqlQuery.setParameter("mergePartition", Integer.valueOf(mergePartition));
        }
        sqlQuery.setParameter("businessStatus", Constants.ContainerBusinessStatus.MERGED.getValue());
        sqlQuery.addScalar("storeLoc", Hibernate.STRING).addScalar("turnoverBoxNos", Hibernate.STRING)
                .addScalar("finishedFlag", Hibernate.STRING).setResultTransformer(Transformers.aliasToBean(StoreOutTaskDTO.class));
        return sqlQuery.list();
    }
    
    public DataPage<PickInfoDTO> findPickInfo(PickHeaderFilter filter, int startIndex, int pageSize) {
        Long whId = ParamUtil.getCurrentWarehouseId();
        StringBuilder hql = new StringBuilder("select new com.daxia.wms.delivery.pick.dto.PickInfoDTO(" +
                "o.id, o.status, o.pktNo, r.regionCode, o.pickTimeFrom, o.pickTimeTo, o.pickBy, wh.waveNo" +
                ")");
        hql.append(" from PickHeader o ");
        hql.append(" left join o.waveHeader wh");
        hql.append(" left join o.region r");
        hql.append(" where o.warehouseId = " + whId.toString());
    
        StringBuilder countHql = new StringBuilder("select count(o.id) from PickHeader o where o.warehouseId = " + whId.toString() + " ");
    
        return (DataPage<PickInfoDTO>) this.executeQueryByFilter(hql.toString(), countHql.toString(), startIndex, pageSize, filter);
    }
    
    public List<PickTaskDto> findPickTask(Long pktId) {
        StringBuilder hql = new StringBuilder("select new com.daxia.wms.delivery.pick.dto.PickTaskDto(" +
                "sku.productCode,sku.productCname, loc.locCode, o.qty, o.pickedQty, o.containerNo, o.pickTime, o.pickWho" +
                ")");
        hql.append(" from PickTask o ");
        hql.append(" left join o.sku sku");
        hql.append(" left join o.location loc");
        hql.append(" where o.warehouseId = :warehouseId and o.pktHeaderId = :pktId");
        
        return this.createQuery(hql.toString()).setParameter("warehouseId",ParamUtil.getCurrentWarehouseId()).setParameter("pktId",pktId)
                .list();
    }
    
    public PickInfoDTO getPickInfoDTO(Long pktId) {
        StringBuilder hql = new StringBuilder("select new com.daxia.wms.delivery.pick.dto.PickInfoDTO(" +
                "o.id, o.status, o.pktNo, r.regionCode, o.pickTimeFrom, o.pickTimeTo, o.pickBy, wh.waveNo" +
                ")");
        hql.append(" from PickHeader o ");
        hql.append(" left join o.waveHeader wh");
        hql.append(" left join o.region r");
        hql.append(" where o.id = :pktId");
        
        return (PickInfoDTO)this.createQuery(hql.toString(),ImmutableMap.<String, Object>of("pktId", pktId)).setMaxResults(1).uniqueResult();
    }
}