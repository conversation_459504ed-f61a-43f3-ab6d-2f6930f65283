package com.daxia.wms.delivery.load.service;

import java.util.Date;

import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.load.entity.ReShipDo;

/**
 *自动交接service
 */
public interface AutoDeliverService {

    /**
     * 自动发货定时任务。
     * 
     * @param doHeaderId
     */
    public boolean reShip(ReShipDo reshipDo);

    /**
     * 自动交接发货
     * 
     * @param cartonNo
     * @param shipTime
     * @param loadMode
     * @throws DeliveryException
     */
    public void autoLoadAndDeliver(String cartonNo, Integer loadMode, Date shipTime);
}
