package com.daxia.wms.delivery.task.replenish.dao;


import java.math.BigInteger;
import java.util.Arrays;
import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.ReplStatus;
import com.daxia.wms.Constants.ReplTaskStatus;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishHeader;
import com.daxia.wms.delivery.task.replenish.filter.ReplHeaderFilter;

/**
 * 补货单头持久层
 */
@Name("com.daxia.wms.stock.replenishHeaderDAO")
@lombok.extern.slf4j.Slf4j
public class ReplenishHeaderDAO extends HibernateBaseDAO<ReplenishHeader, Long> {

	private static final long serialVersionUID = 900431779539810863L;
	
	/**
	 * 查询可用于补货的补货任务号
	 * @param replStatus 补货单头状态
	 * @param taskStatus 补货任务状态
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<Object> findAvailableReplNo(List<String> replStatus, List<String> taskStatus) {
		StringBuilder sbHql = null;
		if(ListUtil.isNotEmpty(taskStatus) &&
				(taskStatus.contains(ReplTaskStatus.OFFSHELF.getValue()) ||
				taskStatus.contains(ReplTaskStatus.INPROGRESS.getValue()))) {
			sbHql = new StringBuilder("select docOperNo, o.createdBy, o.toLpnNo from ReplenishTask o where o.warehouseId = :warehouseId ");
			
			if(ListUtil.isNotEmpty(taskStatus)) {
				sbHql.append(" and o.taskStatus in (:taskStatus) ");
			}
			if (ListUtil.isNotEmpty(taskStatus)) {
				sbHql.append(" group by o.docOperNo, o.createdBy, o.toLpnNo ");
			}
		} else {
			sbHql = new StringBuilder("select o.replNo, o.createdBy from ReplenishHeader o where o.warehouseId = :warehouseId ");
			if (!ListUtil.isNullOrEmpty(replStatus)) {
				sbHql.append(" and o.status  in (:status) ")
				.append(" and exists(select 1 from o.replTaskList rd where rd.taskStatus in (:availableTaskStatus) and rd.warehouseId = :warehouseId) ");
			}
			if(ListUtil.isNotEmpty(taskStatus)) {
				sbHql.append(" and exists(select 1 from o.replTaskList rd where rd.taskStatus in (:taskStatus) and rd.warehouseId = :warehouseId) ");
			}
			sbHql.append(" order by o.id asc ");
		}
		Query query = this.createQuery(sbHql.toString());
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		if (!ListUtil.isNullOrEmpty(replStatus)) {
			query.setParameterList("status", replStatus);
			query.setParameterList("availableTaskStatus",
					Arrays.asList(ReplTaskStatus.RELEASED.getValue(),ReplTaskStatus.INPROGRESS.getValue()));
		}
		if(ListUtil.isNotEmpty(taskStatus)) {
			query.setParameterList("taskStatus", taskStatus);
		}
		return query.list();
	}
	
	/**
	 * 根据补货单号查询补货单头
	 * @param replNo
	 * @return
	 */
	public ReplenishHeader queryReplenishHeaderByReplNo(String replNo) {
		String hql = "from ReplenishHeader o where o.replNo = :replNo and o.warehouseId = :warehouseId";
		return (ReplenishHeader) (this.createQuery(hql).setParameter("replNo", replNo)
				.setLong("warehouseId", ParamUtil.getCurrentWarehouseId()).setMaxResults(1).uniqueResult());
	}
	
	/**
	 * 查询补货单头
	 * @param replHeaderFilter
	 * @param startIndex
	 * @param pageSize
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public DataPage<ReplenishHeader> findReplenishHeaderByFilter(ReplHeaderFilter replHeaderFilter, int startIndex, int pageSize) {
		StringBuilder hql = new StringBuilder("from ReplenishHeader o");
        StringBuilder countHql = new StringBuilder("select count(o.id) from ReplenishHeader o");
        if (replHeaderFilter.getTransSkuFlag() != null && replHeaderFilter.getTransSkuFlag()) {
            countHql.append(" where exists (select 1 from TransSku tts,DoAllocateDetail ddd,DoAllocateHeader doh, DoAllocateDetail ddd1,ReplenishTask rt " +
            		                                "where doh.doType = '" + Constants.DoType.SELL.getValue() + "' and tts.id = ddd.skuId and ddd.doHeaderId=doh.id and doh.id = ddd1.doHeaderId and ddd1.skuId =rt.skuId and rt.docOperId=o.id ");
            countHql.append(" and tts.warehouseId = o.warehouseId and ddd.warehouseId = o.warehouseId and doh.warehouseId= o.warehouseId and ddd1.warehouseId = o.warehouseId and rt.warehouseId = o.warehouseId) ");
            
            hql.append(" where exists (select 1 from TransSku tts,DoAllocateDetail ddd,DoAllocateHeader doh, DoAllocateDetail ddd1,ReplenishTask rt " +
                    "where doh.doType = '" + Constants.DoType.SELL.getValue() + "' and tts.id = ddd.skuId and ddd.doHeaderId=doh.id and doh.id = ddd1.doHeaderId and ddd1.skuId =rt.skuId and rt.docOperId=o.id ");
            hql.append(" and tts.warehouseId = o.warehouseId and ddd.warehouseId = o.warehouseId and doh.warehouseId= o.warehouseId and ddd1.warehouseId = o.warehouseId and rt.warehouseId = o.warehouseId)");
        }
        return (DataPage<ReplenishHeader>)this.executeQueryByFilter(hql.toString(), countHql.toString(), startIndex, pageSize, replHeaderFilter);
	}
	
	/**
	 * 查询补货单集合
	 * @param idList
	 * @return
	 */
	@SuppressWarnings("unchecked")
    public List<ReplenishHeader> findReplHeaderList(List<Long> idList) {
	    String  hql = "from ReplenishHeader o where o.warehouseId = :warehouseId  and o.id in (:ids)";
	    Query query = this.createQuery(hql);
	    query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
	    query.setParameterList("ids", idList);
	    return query.list();
	}

	/**
	 * 求当前可被索取的补货单的数量
	 * @param partitionIdList
	 * @return
	 */
	public Long countAvailableDoc(List<Long> partitionIdList) {
		StringBuilder sql = new StringBuilder(
				" Select Count(1) From doc_rpl_header t " +
				" Where t.Oper_User_Id Is Null And t.Status = :status "+
				    " And t.Warehouse_Id = :warehouseId And t.Is_Deleted = 0 ");
		if (ListUtil.isNotEmpty(partitionIdList)) {
			sql.append(" And t.Src_partition_id In (:partitionIdList) ");
		}
		Query query = this.createSQLQuery(sql.toString());
		if (ListUtil.isNotEmpty(partitionIdList)) {
			query.setParameterList("partitionIdList", partitionIdList);
		}
		query.setParameter("status", ReplStatus.RELEASED.getValue());
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		return ((BigInteger) query.uniqueResult()).longValue();
	}

	public ReplenishHeader demandDoc(List<Long> partitionIdList) {
		StringBuilder hql = new StringBuilder(
				" from ReplenishHeader o where o.operUserId is null and o.status = :status and o.warehouseId = :warehouseId ");
		if (ListUtil.isNotEmpty(partitionIdList)) {
			hql.append(" and o.srcPartitionId in (:partitionIdList) ");
		}
		hql.append(" order by o.earlistPlanShipTime, o.priority, o.replType, o.createdAt ");
		Query query = this.createQuery(hql.toString());
		if (ListUtil.isNotEmpty(partitionIdList)) {
			query.setParameterList("partitionIdList", partitionIdList);
		}
		query.setParameter("status", ReplStatus.RELEASED.getValue());
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setMaxResults(1);
		return (ReplenishHeader) query.uniqueResult();
	}
}