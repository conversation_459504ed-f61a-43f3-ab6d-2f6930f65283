package com.daxia.wms.delivery.recheck.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.master.entity.Sku;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * 装箱明细
 */
@Entity
@Table(name = "doc_carton_detail")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = " update doc_carton_detail set is_deleted = 1 where id = ?  and version = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class CartonDetail extends WhBaseEntity {

	private static final long serialVersionUID = 2371325147240766159L;

	private Long id;

	private CartonHeader cartonHeader;

	/**
	 * 已核拣装箱数
	 */
	private BigDecimal packedNumber;

	private DeliveryOrderHeader doHeader;

	private Sku sku;
	
	private Long skuId;

    private Integer packQty;

    private Long lotId;

    private String lotatt05;

	private Long doDetailId;

	private Long crossDetailId;

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CARTON_HEADER_ID")
	@Where(clause = " IS_DELETED = 0 ")
	public CartonHeader getCartonHeader() {
		return cartonHeader;
	}

	public void setCartonHeader(CartonHeader cartonHeader) {
		this.cartonHeader = cartonHeader;
	}

	/**
	 * 
	 * @return 已核拣装箱数
	 */
	@Column(name = "QTY_PACKED")
	public BigDecimal getPackedNumber() {
		return packedNumber;
	}

	public void setPackedNumber(BigDecimal packedNumber) {
		this.packedNumber = packedNumber;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "DO_HEADER_ID")
	@Where(clause = " IS_DELETED = 0 ")
	public DeliveryOrderHeader getDoHeader() {
		return doHeader;
	}

	public void setDoHeader(DeliveryOrderHeader doHeader) {
		this.doHeader = doHeader;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SKU_ID",insertable=false,updatable=false)
	@Where(clause = " IS_DELETED = 0 ")
	public Sku getSku() {
		return sku;
	}

	public void setSku(Sku sku) {
		this.sku = sku;
	}

	@Column(name="SKU_ID")
	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

    @Column(name="PACK_QTY")
    public Integer getPackQty() {
        return packQty;
    }

    public void setPackQty(Integer packQty) {
        this.packQty = packQty;
    }

    @Column(name="LOT_ID")
    public Long getLotId() {
        return lotId;
    }

    public void setLotId(Long lotId) {
        this.lotId = lotId;
    }

    @Column(name="lotatt05")
    public String getLotatt05() {
        return lotatt05;
    }

    public void setLotatt05(String lotatt05) {
        this.lotatt05 = lotatt05;
    }

    @Column(name="DO_DETAIL_ID")
	public Long getDoDetailId() {
		return doDetailId;
	}

	public void setDoDetailId(Long doDetailId) {
		this.doDetailId = doDetailId;
	}

	@Column(name="CROSS_DETAIL_ID")
	public Long getCrossDetailId() {
		return crossDetailId;
	}

	public void setCrossDetailId(Long crossDetailId) {
		this.crossDetailId = crossDetailId;
	}
}
