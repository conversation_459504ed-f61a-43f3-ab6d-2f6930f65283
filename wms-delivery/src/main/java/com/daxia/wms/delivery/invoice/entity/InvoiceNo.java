package com.daxia.wms.delivery.invoice.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.CascadeType;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;

/**
 * 发票号实体
 */
@Entity
@Table(name = "invoice_no")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "UPDATE invoice_no SET IS_DELETED = 1 WHERE ID = ? and VERSION = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class InvoiceNo extends WhBaseEntity {

	private static final long serialVersionUID = 3020391368353173560L;
    
    /**
     *  主键
     */
    private Long id;
    
    /**
     * 发票代码
     */
    private String invoiceCode;
    
    /**
     * 发票号码
     */
    private String invoiceNo;
    
    /**
     * 发票薄
     */
    private Long invoiceBookId;
    
    /**
     * 订单号
     */
    private String doNo;
    
    /**
     * 发票头ID
     */
    private Long invoiceHeaderId;
    
    /**
     * 绑定DO时间
     */
    private Date lockDoDate;
    
    /**
     * 发票作废时间
     */
    private Date invalidDate;
    
    /**
     * 发票丢失时间
     */
    private Date loseDate;
    
    /**
     * 绑定人
     */
    private String lockBy;
    
    /**
     * 丢失责任人
     */
    private String loseBy; 
    
    /**
     * 发票作废人员
     */
    private String invalidBy;
    
    /**
     * 备注
     */
    private String notes;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 是否删除：0否，1是
     */
    private Integer isDeleted;
    
    /**
     * 网站
     */
    private Integer siteId;
    
    /**
     * 部门Id
     */
    private Integer departmentId;
    
    /**
     * 发票薄
     */
    private InvoiceBook invoiceBook;
    
    /**
     * 发票头
     */
    private InvoiceHeader invoiceHeader;
    
    private String sourceSystem;//来源系统，药网订单：YW
    
    private Long companyId; //子公司id
   
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)  
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "INVOICE_CODE")
    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    @Column(name = "INVOICE_NO")
    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    @Column(name = "INVOICE_BOOK_ID")
    public Long getInvoiceBookId() {
        return invoiceBookId;
    }

    public void setInvoiceBookId(Long invoiceBookId) {
        this.invoiceBookId = invoiceBookId;
    }

    @Column(name = "DO_NO")
    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    @Column(name = "INVOICE_HEADER_ID")
    public Long getInvoiceHeaderId() {
        return invoiceHeaderId;
    }

    public void setInvoiceHeaderId(Long invoiceHeaderId) {
        this.invoiceHeaderId = invoiceHeaderId;
    }

    @Column(name = "LOCK_DO_DATE")
    public Date getLockDoDate() {
        return lockDoDate;
    }

    public void setLockDoDate(Date lockDoDate) {
        this.lockDoDate = lockDoDate;
    }

    @Column(name = "INVALID_DATE")
    public Date getInvalidDate() {
        return invalidDate;
    }

    public void setInvalidDate(Date invalidDate) {
        this.invalidDate = invalidDate;
    }

    @Column(name = "LOSE_DATE")
    public Date getLoseDate() {
        return loseDate;
    }

    public void setLoseDate(Date loseDate) {
        this.loseDate = loseDate;
    }

    @Column(name = "LOCK_BY")
    public String getLockBy() {
        return lockBy;
    }

    public void setLockBy(String lockBy) {
        this.lockBy = lockBy;
    }

    @Column(name = "LOSE_BY")
    public String getLoseBy() {
        return loseBy;
    }

    public void setLoseBy(String loseBy) {
        this.loseBy = loseBy;
    }

    @Column(name = "INVALID_BY")
    public String getInvalidBy() {
        return invalidBy;
    }

    public void setInvalidBy(String invalidBy) {
        this.invalidBy = invalidBy;
    }

    @Column(name = "NOTES")
    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    @Column(name = "STATUS")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "IS_DELETED")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "INVOICE_BOOK_ID",insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    @Cascade(value={CascadeType.ALL}) 
    public InvoiceBook getInvoiceBook() {
        return invoiceBook;
    }

    public void setInvoiceBook(InvoiceBook invoiceBook) {
        this.invoiceBook = invoiceBook;
    }

    @OneToOne(targetEntity = InvoiceHeader.class)
    @JoinColumn(name = "INVOICE_HEADER_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public InvoiceHeader getInvoiceHeader() {
        return invoiceHeader;
    }

    public void setInvoiceHeader(InvoiceHeader invoiceHeader) {
        this.invoiceHeader = invoiceHeader;
    }

    @Column(name = "SITE_ID")
    public Integer getSiteId() {
        return siteId;
    }

    public void setSiteId(Integer siteId) {
        this.siteId = siteId;
    }

    @Column(name = "DEPARTMENT_ID")
    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }
    
    @Column(name="SOURCE_SYSTEM")
    public String getSourceSystem() {
        return sourceSystem;
    }
    
    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    @Column(name="COMPANY_ID")
    public Long getCompanyId() {
        return companyId;
    }

    
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
