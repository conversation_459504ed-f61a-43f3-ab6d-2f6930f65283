package com.daxia.wms.delivery.container.service;

import java.util.List;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.master.dto.ContainerBindDTO;
import com.daxia.wms.delivery.container.dto.Detail4BindDTO;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.Constants.ContainerBusinessStatus;
import com.daxia.wms.master.entity.Container;

public interface ContainerMgntService {
    
    /**
     * 绑定容器
     * @param containerId
     * @param docType
     * @param docNo
     * @param ids
     * @throws DeliveryException
     */
    public void bindContainer(Long containerId, String docType, String docNo, List<Long> ids) throws DeliveryException;

    /**
     * 释放容器信息
     * @param containerId
     * @return
     * @throws DeliveryException
     */
    public Container releaseContainer(Long containerId) throws DeliveryException;
    
    /**
     * 释放时更新容器信息，并记录日志
     * 
     * @param containerId
     * @throws DeliveryException
     */
    public void release(Container container) throws DeliveryException;

    /**
     * 根据单据类型和单据号，分页查找绑定dto
     * 
     * 
     * @param waveNo
     * @return 明细的list
     */
    public DataPage<Detail4BindDTO> findDetails4Bind(Long containerId, String docNo, String docType, int startIndex, int pageSize) throws DeliveryException;

    /** 
     * 校验WMS是否启用容器管理
     * 
     * @param containerId
     */
    public void vContainerMgntUsed() throws DeliveryException;
    
    /**
     * 获取wms是否启用容器管理配置项参数值,0--未启用，1---启用
     * @return
     * @throws DeliveryException 无此配置项抛异常
     */
    public Integer getContainerMgntStartFlag() throws DeliveryException;
    
    /**
     * 执行操作（绑定，释放）后更新容器的信息
     * 
     * @param container
     *            容器
     * @param docType
     *            单据类型
     * @param docNo
     *            单据号
     */
    public Container updateContainer(Container container, String docType, String docNo,String refNo1 ,
                                     String businessStatus, String operator,String gridNo, Integer isCurrentCarton);
    
    /**
     * 创建容器使用日志
     * 注：若操作人为空，则获取系统登录用户作为操作人，否则使用传入的值
     * @param containerNo 容器号
     * @param docType 单据类型
     * @param docNo 单据号
     * @param operation 容器操作类型
     * @param operator 操作人 
     */
    public void createContainerLog(String containerNo, String docType, String docNo, String operation, String operator);
    
    /**
     * 通过docNo,containerType,docType找到对应的绑定的容器
     * 
     * @param docNo
     * @param containerType
     * @param docType
     * @return
     */
    public List<Container> findContainerByDoc(String docNo, String containerType, String docType);
    
    /**
     * 
     * @param docNo
     * @param containerType
     * @param docType
     * @return
     */
    public boolean isDoBindContainer(String docNo, String containerType, String docType);
    
    /**
     * 分拣时释放容器，并记录日志
     * 
     * @param waveHeaderId
     * @throws DeliveryException
     */
    public void releaseContainerForSorting(WaveHeader waveHeader) throws DeliveryException;
    
    /**
     * 验证容器是否可以绑定指定单据
     * @param containerNo
     * @param docNo
     * @param businessStatus
     * @param docType
     * @return
     * @throws DeliveryException
     */
    public Container check4Bind(String containerNo, String docNo, String businessStatus, String docType) throws DeliveryException;
    
    /**
     * 验证已绑定业务单据的容器
     * @param containerNo
     * @param businessStatus
     * @param containerType
     * @param docNo 如不为空则容器绑定单号需和此号相同
     * @return
     */
    public Container checkBindedContainer(String containerNo, String businessStatus, String containerType, String docNo) throws DeliveryException;
    
    
    /**
     * 绑定容器并记录绑定日志
     * @param container
     * @param docType
     * @param docNo
     * @param businessStatus
     * @param operator 操作(0:绑定、1:释放)
     * @throws DeliveryException
     */
    public void bind(Container container, String docType, String docNo,String refNo1, String businessStatus, String operator) throws DeliveryException;

    /**
     * 绑定容器并记录绑定日志
     * @param container
     * @param docType
     * @param docNo
     * @param businessStatus
     * @param operator 操作(0:绑定、1:释放)
     * @throws DeliveryException
     */
    public void bind(Container container, String docType, String docNo,String refNo1,
                     String businessStatus, String operator,String gridNo,Integer isCurrentCarton) throws DeliveryException;

    /**
     * 根据波次及容器业务状态查找Container
     * @param waveIds
     * @param businessStatus
     */
    public List<List> findByWaveIdsAndStatus(List<Long> waveIds, List<String> businessStatus);
    
    /**
     * 根据已装箱发货单获取其装箱绑定的容器
     * @param doId
     * @return
     */
    public List<Container> getCartonedDoCantainer(Long doId);

    /**
     * 修改波次下容器的业务状态
     * @param id
     * @param outMerge
     */
    public void updateBusinessStatusByWaveId(Long id, ContainerBusinessStatus outMerge);

    public void manualRelease(Container container) throws DeliveryException;
    
    /**
     * 核拣完成后释放分拣筐
     * @param doNo
     * @throws DeliveryException
     */
    public void recheckReleaseContainer(String doNo) throws DeliveryException;
    /**
     * 核拣绑定分拣筐
     * @param doNo
     * @param sortContainerNo
     * @throws DeliveryException
     */
    public void recheckBindSortContainer(String doNo, String sortContainerNo) throws DeliveryException;
    
    /**
     * 绑定单据
     * @param container
     * @param docType
     * @param docNo
     * @param businessStatus
     * @param containerType
     */
    public void bindDoc(Container container, String docType, String docNo, String businessStatus, String containerType);
    
    /**
     * 根据波次释放容器
     * @param wave
     */
    public void releaseContainerByWave(WaveHeader wave, String containerType, String docType);

	/**
	 * 根据容器号查询集货信息
	 * @param containerNo
	 * @return
	 */
	public List<Object> queryMergeInfoByConNo(String waveNo);

    void releaseByDoAndPkt(String doNo, String pickHeaderNo);

    Container check4UnBoxing(String containerNo);

    void unBoxing(Container container, String docNo, String docType,String refNo,String updateBy);

    void recheckReleaseContainerByPackageType(String doNo, Integer packageType) throws DeliveryException;

    Container getcurrentContainerByDoNoAndPktNo(String doNo, String pktNo);

    List<Container> findByDoNo(String doNo);
    
    void releaseByPkt(String pktNo,String pktType);

    List<ContainerBindDTO> findContainerBindList(String containerNo);
}