package com.daxia.wms.delivery.wave.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants.AutoWaveType;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.PrintStatus;
import com.daxia.wms.Constants.ReleaseStatus;
import com.daxia.wms.delivery.wave.dto.BatchGroupWaveDTO;
import com.daxia.wms.delivery.wave.filter.BatchGroupWaveFilter;
import org.hibernate.Hibernate;
import org.hibernate.SQLQuery;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;

@Name("batchGroupWaveDAO")
@lombok.extern.slf4j.Slf4j
public class BatchGroupWaveDAO extends HibernateBaseDAO<BatchGroupWaveDTO, Long> {

	private static final long serialVersionUID = -9137479874292706572L;
	
	/**
	 * 促销单品波次及箱打印信息查看
	 * @param filter
	 * @param startIndex
	 * @param pageSize
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public DataPage<BatchGroupWaveDTO> query(BatchGroupWaveFilter filter,
			int startIndex, int pageSize) {
		StringBuilder sb = new StringBuilder();
		sb.append("select waveInfo.waveId, waveInfo.waveNo, waveInfo.status, waveInfo.eFinishTime, waveInfo.createTime, waveInfo.createBy,");
		sb.append("		count(carton.id) as totalCarton, sum(carton.is_printed) as totalPrintedCarton");
		sb.append("	from (select wave.id as waveId, wave.wave_no as waveNo, wave.status as status,");
		sb.append("				wave.e_do_finish_time as eFinishTime, wave.create_time as createTime, wave.create_by as createBy");
		sb.append("          from doc_wave_header wave");
		sb.append("         where wave.is_deleted = 0 ");
		//sb.append("			and wave.status = :waveStatus");
		sb.append("			and wave.auto_type = :autoWaveType");
		sb.append("			and wave.warehouse_id = :warehouseId");
		if(StringUtil.isNotEmpty(filter.getWaveNoFrom()) && StringUtil.isNotEmpty(filter.getWaveNoTo())) {
			sb.append("			and wave.wave_no between :fromWaveNo and :toWaveNo");
		} else if (StringUtil.isNotEmpty(filter.getWaveNoFrom()) && StringUtil.isEmpty(filter.getWaveNoTo())) {
			sb.append("			and wave.wave_no >= :fromWaveNo");
		} else if (StringUtil.isEmpty(filter.getWaveNoFrom()) && StringUtil.isNotEmpty(filter.getWaveNoTo())) {
			sb.append("			and wave.wave_no <= :toWaveNo");
		}
		sb.append("			and wave.create_time between :fromDate and :toDate");
		sb.append("			and not exists (select 1");
		sb.append("                  from doc_do_header do");
		sb.append("                 where do.wave_id = wave.id");
		sb.append("                   and do.release_status = :releaseStatus");
		sb.append("                   and do.is_deleted = 0");
		sb.append("                   and do.warehouse_id = :warehouseId");
		sb.append("                   and do.status < :doStatus)) waveInfo,");
		sb.append("       doc_do_header d,");
		sb.append("       doc_carton_header carton");
		sb.append(" where waveInfo.waveId = d.wave_id");
		sb.append("   and d.id = carton.do_header_id");
		sb.append("   and carton.is_deleted = 0");
		sb.append("   and carton.warehouse_id = :warehouseId");
		sb.append("   and d.release_status = :releaseStatus");
		sb.append("   and d.is_deleted = 0");
		sb.append("   and d.warehouse_id = :warehouseId");
		sb.append(" group by waveInfo.waveId, waveInfo.waveNo, waveInfo.status, waveInfo.eFinishTime, waveInfo.createTime, waveInfo.createBy");
		sb.append(" order by waveInfo.createTime desc");
		StringBuilder pageSql = new StringBuilder();
		pageSql.append("select a.waveId, a.waveNo, a.status, a.eFinishTime, a.createTime, a.createBy,");
		pageSql.append("		a.totalCarton, a.totalPrintedCarton from ( ");
		pageSql.append(sb.toString()).append(")a ");
		// 根据波次箱标签打印状态设置条件
		if (PrintStatus.NO.getValue().equals(filter.getCatornPrintStatus())) {
			pageSql.append(" where a.totalPrintedCarton = 0 and a.totalCarton > 0");
		} else if (PrintStatus.PARTIAL.getValue().equals(filter.getCatornPrintStatus())) {
			pageSql.append(" where a.totalPrintedCarton > 0");
			pageSql.append(" and a.totalPrintedCarton < a.totalCarton");
		} else if (PrintStatus.YES.getValue().equals(filter.getCatornPrintStatus())) {
			pageSql.append(" where a.totalPrintedCarton = a.totalCarton");
		}
		
        if (!(0 == startIndex && 0 == pageSize)) {
            pageSql.append(" limit  :minNum,:maxNum");
           
        }
        
		log.debug(pageSql.toString());
		
		StringBuilder countSql = new StringBuilder();
		countSql.append("select count(*) from (");
		countSql.append(sb.toString()).append(" ) a");
		if (PrintStatus.NO.getValue().equals(filter.getCatornPrintStatus())) {
			countSql.append(" where a.totalPrintedCarton = 0 and a.totalCarton > 0");
        } else if (PrintStatus.PARTIAL.getValue().equals(filter.getCatornPrintStatus())) {
        	countSql.append(" where a.totalPrintedCarton > 0");
        	countSql.append(" and a.totalPrintedCarton < a.totalCarton");
        } else if (PrintStatus.YES.getValue().equals(filter.getCatornPrintStatus())) {
        	countSql.append(" where a.totalPrintedCarton = a.totalCarton");
        }
		log.debug(countSql.toString());
		
		SQLQuery query = getSession().createSQLQuery(pageSql.toString());
		query.addScalar("waveId", Hibernate.LONG);
		query.addScalar("waveNo", Hibernate.STRING);
		query.addScalar("status", Hibernate.STRING);
		query.addScalar("eFinishTime", Hibernate.TIMESTAMP);
		query.addScalar("createTime", Hibernate.TIMESTAMP);
		query.addScalar("createBy", Hibernate.STRING);
		query.addScalar("totalCarton", Hibernate.BIG_DECIMAL);
		query.addScalar("totalPrintedCarton", Hibernate.BIG_DECIMAL);
		
		// query.setString("waveStatus", WaveStatus.ALLSORTED.getValue());
		query.setInteger("autoWaveType", AutoWaveType.BATCH_GROUP.getValue());
		query.setString("releaseStatus", ReleaseStatus.RELEASE.getValue());
		query.setString("doStatus", DoStatus.ALL_CARTON.getValue());
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setTimestamp("fromDate", filter.getFromDate());
		query.setTimestamp("toDate", filter.getToDate());
		if (StringUtil.isNotEmpty(filter.getWaveNoFrom())) {
			query.setString("fromWaveNo", filter.getWaveNoFrom());
		}
		if (StringUtil.isNotEmpty(filter.getWaveNoTo())) {
			query.setString("toWaveNo", filter.getWaveNoTo());
		}
		if (!(0 == startIndex && 0 == pageSize)) {
            query.setInteger("minNum", startIndex);
            query.setInteger("maxNum", startIndex + pageSize);
        }
		List list = query.list();
		
		SQLQuery countQuery = getSession().createSQLQuery(countSql.toString());
		//countQuery.setString("waveStatus", WaveStatus.ALLSORTED.getValue());
		countQuery.setInteger("autoWaveType", AutoWaveType.BATCH_GROUP.getValue());
		countQuery.setString("releaseStatus", ReleaseStatus.RELEASE.getValue());
		countQuery.setString("doStatus", DoStatus.ALL_CARTON.getValue());
		countQuery.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		countQuery.setTimestamp("fromDate", filter.getFromDate());
		countQuery.setTimestamp("toDate", filter.getToDate());
		if (StringUtil.isNotEmpty(filter.getWaveNoFrom())) {
			countQuery.setString("fromWaveNo", filter.getWaveNoFrom());
		}
		if (StringUtil.isNotEmpty(filter.getWaveNoTo())) {
			countQuery.setString("toWaveNo", filter.getWaveNoTo());
		}
		Long count =  ((BigInteger)countQuery.uniqueResult()).longValue();

		DataPage<BatchGroupWaveDTO> dataPage = new DataPage<BatchGroupWaveDTO>();
		dataPage.setTotalCount(count);
		BatchGroupWaveDTO dto = null;
		Object[] objs = null;
		for (int i = 0; i < list.size(); i++) {
			objs = (Object[])list.get(i);
			dto = new BatchGroupWaveDTO();
			dto.setWaveId((Long)objs[0]);
			dto.setWaveNo((String)objs[1]);
			dto.setWaveStatus(objs[2] == null ? null : objs[2].toString());
			dto.setEstDoFinishTime((Date)objs[3]);
			dto.setCreatetime((Date)objs[4]);
			dto.setCreateBy((String)objs[5]);
			dto.setTotalCartons(((BigDecimal)objs[6]).longValue());
			dto.setTotalPrintedCarton(((BigDecimal)objs[7]).longValue());
			if(dto.getTotalPrintedCarton() == 0 && dto.getTotalCartons() > 0) {
				dto.setCartonPrintStatus(PrintStatus.NO.getValue());
			} else if (dto.getTotalPrintedCarton() > 0 && dto.getTotalPrintedCarton() < dto.getTotalCartons()) {
				dto.setCartonPrintStatus(PrintStatus.PARTIAL.getValue());
			} else if (dto.getTotalPrintedCarton().intValue() ==  dto.getTotalCartons().intValue()) {
				dto.setCartonPrintStatus(PrintStatus.YES.getValue());
			}
			dataPage.getDataList().add(dto);
		}
		return dataPage; 
	}
	
	public boolean isBatchGroupDo(Long doId){
		StringBuilder sb = new StringBuilder();
		sb.append(" select 1 ");
		sb.append("     from doc_do_header ddh ");
		sb.append("   inner join doc_do_wave_ex ddwe ");
		sb.append("     on ddh.id = ddwe.do_h_id ");
		sb.append("   inner join doc_wave_header dwh ");
		sb.append("     on ddh.wave_id = dwh.id ");
		sb.append("   where ddwe.auto_wave_type = :autoWaveType ");
		sb.append("     and dwh.auto_type = :autoWaveType ");
		sb.append("     and ddh.id = :doId ");
		SQLQuery query = getSession().createSQLQuery(sb.toString());
		query.setLong("doId", doId);
		query.setLong("autoWaveType", AutoWaveType.BATCH_GROUP.getValue());
		return !query.list().isEmpty();
	}
}
