package com.daxia.wms.delivery.task.replenish.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

@lombok.extern.slf4j.Slf4j
public class ReplenishSkuDTO implements Serializable {
	private static final long serialVersionUID = 1L;
	
	private String doNo;
	private Long skuId;
	private Long replRegionId;
	private String lotatt04;
	private String lotatt06;
	private String lotatt08;
	private String lotatt05;
	private BigDecimal replQty;
	//需要补货的箱数量
	private BigDecimal replQtyUnit;
	private Long shopId;
	
	/**
	 * 缺货DO中最早导入WMS的时间
	 */
    private Timestamp earliestDoTime;
    
    /**
     * 缺货DO最早预计出库时间
     */
    private Date earliestPlanShipTime;
	/**
	 * 失效日期开始时间
	 */
    private Date minExp;
	/**
	 * 失效日期结束时间
	 */
    private Date maxExp;
	
	public ReplenishSkuDTO(String doNo, Long skuId, String lotatt04, String lotatt06, String lotatt08,String lotatt05, BigDecimal replQty) {
		super();
		this.doNo = doNo;
		this.skuId = skuId;
		this.lotatt04 = lotatt04;
		this.lotatt06 = lotatt06;
		this.lotatt08 = lotatt08;
		this.lotatt05 = lotatt05;
		this.replQty = replQty;
	}
	
	public ReplenishSkuDTO(String doNo, Timestamp earliestDoTime, Long skuId, String lotatt04, String lotatt06, String lotatt08, String lotatt05,BigDecimal replQty, Date earliestPlanShipTime) {
		this(doNo, skuId, lotatt04, lotatt06, lotatt08,lotatt05, replQty);
		this.earliestDoTime = earliestDoTime;
        this.earliestPlanShipTime = earliestPlanShipTime;
    }

	public Date getMinExp() {
		return minExp;
	}

	public void setMinExp(Date minExp) {
		this.minExp = minExp;
	}

	public Date getMaxExp() {
		return maxExp;
	}

	public void setMaxExp(Date maxExp) {
		this.maxExp = maxExp;
	}
	public Long getReplRegionId() {
		return replRegionId;
	}
	
	public void setReplRegionId(Long replRegionId) {
		this.replRegionId = replRegionId;
	}
	
	public Long getSkuId() {
		return skuId;
	}
	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}
	public String getLotatt04() {
		return lotatt04;
	}
	public void setLotatt04(String lotatt04) {
		this.lotatt04 = lotatt04;
	}
	public String getLotatt06() {
		return lotatt06;
	}
	public void setLotatt06(String lotatt06) {
		this.lotatt06 = lotatt06;
	}
	public String getLotatt08() {
		return lotatt08;
	}
	public void setLotatt08(String lotatt08) {
		this.lotatt08 = lotatt08;
	}
	public BigDecimal getReplQty() {
		return replQty;
	}
	public void setReplQty(BigDecimal replQty) {
		this.replQty = replQty;
	}
	public String getLotatt05() {
		return lotatt05;
	}
	public void setLotatt05(String lotatt05) {
		this.lotatt05 = lotatt05;
	}

	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}
    
    public Timestamp getEarliestDoTime() {
        return earliestDoTime;
    }
  
    public void setEarliestDoTime(Timestamp earliestDoTime) {
        this.earliestDoTime = earliestDoTime;
    }
	
    public Date getEarliestPlanShipTime() {
        return earliestPlanShipTime;
    }
    
    public void setEarliestPlanShipTime(Date earliestPlanShipTime) {
        this.earliestPlanShipTime = earliestPlanShipTime;
    }

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}
	
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((lotatt04 == null) ? 0 : lotatt04.hashCode());
		result = prime * result + ((lotatt06 == null) ? 0 : lotatt06.hashCode());
		result = prime * result + ((lotatt08 == null) ? 0 : lotatt08.hashCode());
		result = prime * result + ((lotatt05 == null) ? 0 : lotatt05.hashCode());
		result = prime * result + ((skuId == null) ? 0 : skuId.hashCode());
		result = prime * result + ((minExp == null) ? 0 : minExp.hashCode());
		result = prime * result + ((maxExp == null) ? 0 : maxExp.hashCode());
		return result;
	}
	
	@Override
	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}
			
		if (obj == null) {
			return false;
		}
			
		if (getClass() != obj.getClass()) {
			return false;
		}
			
		ReplenishSkuDTO other = (ReplenishSkuDTO) obj;
		if (lotatt04 == null) {
			if (other.lotatt04 != null) {
				return false;
			}
		} else if (!lotatt04.equals(other.lotatt04)) {
			return false;
		}
			
		if (lotatt06 == null) {
			if (other.lotatt06 != null) {
				return false;
			}
		} else if (!lotatt06.equals(other.lotatt06)) {
			return false;
		}
			
		if (lotatt08 == null) {
			if (other.lotatt08 != null) {
				return false;
			}
		} else if (!lotatt08.equals(other.lotatt08)) {
			return false;		
		}

		if (lotatt05 == null) {
			if (other.lotatt05 != null) {
				return false;
			}
		} else if (!lotatt05.equals(other.lotatt05)) {
			return false;
		}
			
		if (skuId == null) {
			if (other.skuId != null) {
				return false;
			}
		} else if (!skuId.equals(other.skuId)) {
			return false;
		}

		if (minExp == null) {
			if (other.minExp != null) {
				return false;
			}
		} else if (!minExp.equals(other.minExp)) {
			return false;
		}

		if (maxExp == null) {
			if (other.maxExp != null) {
				return false;
			}
		} else if (!maxExp.equals(other.maxExp)) {
			return false;
		}

		return true;
	}

	public BigDecimal getReplQtyUnit() {
		return replQtyUnit;
	}

	public void setReplQtyUnit(BigDecimal replQtyUnit) {
		this.replQtyUnit = replQtyUnit;
	}
	
	public static ReplenishSkuDTO getInstanceOf(ReplenishSkuDTO sku) {
		if (sku != null) {
			//String doNo, Long skuId, String lotatt04, String lotatt06, String lotatt08,String lotatt05, BigDecimal replQty
			ReplenishSkuDTO result = new ReplenishSkuDTO(sku.getDoNo(), sku.getSkuId(), sku.getLotatt04(), sku.getLotatt06(), sku.getLotatt08(),sku.getLotatt05(), sku.getReplQty());
			result.setReplQtyUnit(sku.getReplQtyUnit());
			result.setEarliestDoTime(sku.getEarliestDoTime());
			result.setEarliestPlanShipTime(sku.getEarliestPlanShipTime());
			result.setShopId(sku.getShopId());
			result.setReplRegionId(sku.getReplRegionId());
			result.setMinExp(sku.getMinExp());
			result.setMaxExp(sku.getMaxExp());
			return result;
		}
		return null;
	}
}
