package com.daxia.wms.delivery.task.replenish.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.entity.Sku;
import com.daxia.wms.stock.stock.entity.StockBatchAtt;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * 补货移位任务
 */
@Entity
@Table(name = "trs_repl_move_task")
@Where(clause = "IS_DELETED = 0")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@lombok.extern.slf4j.Slf4j
public class ReplMoveTask extends WhBaseEntity {

	private static final long serialVersionUID = 6016551460538644311L;
	
	private Long id;

	/**
	 * 40: 发布   99： 完成    90： 取消
	 */
	private String status;
	
	private Long skuId;
	
	private Long lotId;
	
	private String lpnNo;
	
	/**
	 * 补货单id
	 */
	private Long docId;
	
	/**
	 * 补货任务id
	 */
	private Long docLineId;
	
	/**
	 * 补货单号
	 */
	private String docNo;
	
	private Long fmLocId;
	
	private Long planLocId;
	
	private Long toLocId;

	private BigDecimal qtyUnit;
	
	private BigDecimal qty;
	
	private Long fmStockId;
	
	private Long toStockId;
	
	private String reasonCode;
	
	private String reasonDesc;
	
	private Location fmLocation;
	
	private Location planLocation;
	
	private Location toLocation;
	
	private Sku sku;
	
	private StockBatchAtt stockBatchAtt;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)  
    @Column(name = "ID")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "STATUS")
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "SKU_ID")
	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	@Column(name = "LOT_ID")
	public Long getLotId() {
		return lotId;
	}

	public void setLotId(Long lotId) {
		this.lotId = lotId;
	}

	@Column(name = "LPN_NO")
	public String getLpnNo() {
		return lpnNo;
	}

	public void setLpnNo(String lpnNo) {
		this.lpnNo = lpnNo;
	}

	@Column(name = "DOC_ID")
	public Long getDocId() {
		return docId;
	}

	public void setDocId(Long docId) {
		this.docId = docId;
	}

	@Column(name = "DOC_LINE_ID")
	public Long getDocLineId() {
		return docLineId;
	}

	public void setDocLineId(Long docLineId) {
		this.docLineId = docLineId;
	}

	@Column(name = "DOC_NO")
	public String getDocNo() {
		return docNo;
	}

	public void setDocNo(String docNo) {
		this.docNo = docNo;
	}

	@Column(name = "FM_LOC_ID")
	public Long getFmLocId() {
		return fmLocId;
	}

	public void setFmLocId(Long fmLocId) {
		this.fmLocId = fmLocId;
	}

	@Column(name = "PLAN_LOC_ID")
	public Long getPlanLocId() {
		return planLocId;
	}

	public void setPlanLocId(Long planLocId) {
		this.planLocId = planLocId;
	}

	@Column(name = "TO_LOC_ID")
	public Long getToLocId() {
		return toLocId;
	}

	public void setToLocId(Long toLocId) {
		this.toLocId = toLocId;
	}

	@Column(name = "QTY")
	public BigDecimal getQty() {
		return qty;
	}

	public void setQty(BigDecimal qty) {
		this.qty = qty;
	}


	@Column(name = "FM_STOCK_ID")
	public Long getFmStockId() {
		return fmStockId;
	}

	public void setFmStockId(Long fmStockId) {
		this.fmStockId = fmStockId;
	}

	@Column(name = "TO_STOCK_ID")
	public Long getToStockId() {
		return toStockId;
	}

	public void setToStockId(Long toStockId) {
		this.toStockId = toStockId;
	}
	
	@Column(name = "REASON_CODE")
	public String getReasonCode() {
		return reasonCode;
	}
	
	public void setReasonCode(String reasonCode) {
		this.reasonCode = reasonCode;
	}
	
	@Column(name = "REASON_DESCR")
	public String getReasonDesc() {
		return reasonDesc;
	}
	
	public void setReasonDesc(String reasonDesc) {
		this.reasonDesc = reasonDesc;
	}
	
    @ManyToOne(fetch = FetchType.LAZY)   
    @JoinColumn(name = "LOT_ID", referencedColumnName = "ID", insertable = false, updatable = false)   
    public StockBatchAtt getStockBatchAtt() {
        return stockBatchAtt;
    }

    public void setStockBatchAtt(StockBatchAtt stockBatchAtt) {
        this.stockBatchAtt = stockBatchAtt;
    }
    
    @ManyToOne(fetch = FetchType.LAZY, targetEntity = Location.class)
    @JoinColumn(name = "FM_LOC_ID", referencedColumnName = "ID", insertable = false, updatable = false)    
    public Location getFmLocation() {
		return fmLocation;
	}

    public void setFmLocation(Location fmLocation) {
		this.fmLocation = fmLocation;
	}
    
    @ManyToOne(fetch = FetchType.LAZY, targetEntity = Location.class)
    @JoinColumn(name = "PLAN_LOC_ID", referencedColumnName = "ID", insertable = false, updatable = false) 
    public Location getPlanLocation() {
		return planLocation;
	}
    
    public void setPlanLocation(Location planLocation) {
		this.planLocation = planLocation;
	}
    
    @ManyToOne(fetch = FetchType.LAZY, targetEntity = Location.class)
    @JoinColumn(name = "TO_LOC_ID", referencedColumnName = "ID", insertable = false, updatable = false) 
    public Location getToLocation() {
		return toLocation;
	}
    
    public void setToLocation(Location toLocation) {
		this.toLocation = toLocation;
	}
    
    @ManyToOne(fetch = FetchType.LAZY, targetEntity = Sku.class)
    @JoinColumn(name = "SKU_ID", referencedColumnName = "ID", insertable = false, updatable = false) 
    public Sku getSku() {
		return sku;
	}
    
    public void setSku(Sku sku) {
		this.sku = sku;
	}
	@Column(name = "QTY_UNIT")
	public BigDecimal getQtyUnit() {
		return qtyUnit;
	}

	public void setQtyUnit(BigDecimal qtyUnit) {
		this.qtyUnit = qtyUnit;
	}
}
