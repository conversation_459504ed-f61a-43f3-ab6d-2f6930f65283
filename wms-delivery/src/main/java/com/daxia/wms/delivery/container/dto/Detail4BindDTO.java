package com.daxia.wms.delivery.container.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *  存放处理的拣货任务中的商品信息，用以波次绑定容器
 */
@lombok.extern.slf4j.Slf4j
public class Detail4BindDTO implements Serializable {

    private static final long serialVersionUID = -4446652871405504204L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 商品名称
     */
    private String productCname;

    /**
     * 商品条码
     */
    private String productBarcode;

    /**
     * 商品类型
     */
    private String productType;

    /**
     * 分配数量
     */
    private BigDecimal qty;

    /**
     * 容器号(记录在拣货任务picktask的toLpnNo中)
     */
    private String containerNo;

    /**
     * 库位id
     */
    private String locCode;
    
    /**
     * 发运订单号
     */
    private String doNo;
    
    /**
     * 发运订单号状态
     */
    private String doStatus;
    
    /**
     * 释放状态
     */
    private String releaseStatus;
    
    /**
     * 预计出库时间
     */
    private Date expectedTime;
    
    /**
     * 限时达类型
     */
    private Integer isHalfDayDelivery;
    
    /**
     * 发货单类型
     */
    private String doType;

    public Detail4BindDTO() {
        
    }
    
    public Detail4BindDTO(Long id, String productCname, String productBarcode, String productType, BigDecimal qty,
            String containerNo, String locCode) {
        this.id = id;
        this.containerNo = containerNo;
        this.locCode = locCode;
        this.productBarcode = productBarcode;
        this.productCname = productCname;
        this.productType = productType;
        this.qty = qty;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getProductCname() {
        return productCname;
    }

    public void setProductCname(String productCname) {
        this.productCname = productCname;
    }

    public String getProductBarcode() {
        return productBarcode;
    }

    public void setProductBarcode(String productBarcode) {
        this.productBarcode = productBarcode;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }

    public String getLocCode() {
        return locCode;
    }

    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }

    
    public String getDoNo() {
        return doNo;
    }

    
    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    
    public String getDoStatus() {
        return doStatus;
    }

    
    public void setDoStatus(String doStatus) {
        this.doStatus = doStatus;
    }

    
    public String getReleaseStatus() {
        return releaseStatus;
    }

    
    public void setReleaseStatus(String releaseStatus) {
        this.releaseStatus = releaseStatus;
    }

    
    public Date getExpectedTime() {
        return expectedTime;
    }

    
    public void setExpectedTime(Date expectedTime) {
        this.expectedTime = expectedTime;
    }

    
    public Integer getIsHalfDayDelivery() {
        return isHalfDayDelivery;
    }

    
    public void setIsHalfDayDelivery(Integer isHalfDayDelivery) {
        this.isHalfDayDelivery = isHalfDayDelivery;
    }

    
    public String getDoType() {
        return doType;
    }

    
    public void setDoType(String doType) {
        this.doType = doType;
    }
}
