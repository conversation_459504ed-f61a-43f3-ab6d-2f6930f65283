package com.daxia.wms.delivery.deliveryorder.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import com.daxia.wms.delivery.deliveryorder.dao.DoPrintInfoDAO;
import com.daxia.wms.delivery.deliveryorder.service.DoPrintInfoService;
import com.daxia.wms.delivery.print.dto.DoPrintSub;

/**
 * 提供一些DO打印信息方面的查询服务
 */
@Name("com.daxia.wms.delivery.doPrintInfoService")
@lombok.extern.slf4j.Slf4j
public class DoPrintInfoServiceImpl implements DoPrintInfoService {

    @In
    private DoPrintInfoDAO doPrintInfoDAO;

    @Override
	public void setQtyDistributionForSub(DoPrintSub sub) {
		List<Object[]> locQtyInfo = doPrintInfoDAO.getQtyDistribution(sub);
		if (locQtyInfo != null) {
			Map<String, BigDecimal> qtyDistribution = sub.getQtyDistribution();
			for (Object[] objArr : locQtyInfo) {
				String locCode = objArr[0] == null ? null : (String) objArr[0];
				BigDecimal qty = objArr[1] == null ? BigDecimal.ZERO
						: (BigDecimal) objArr[1];
				if (locCode == null) {
					continue;
				}
				if (qtyDistribution.get(locCode) != null) {
					qtyDistribution.put(locCode, qtyDistribution.get(locCode)
							.add(qty));
				} else {
					qtyDistribution.put(locCode, qty);
				}
			}
		}
	}

}
