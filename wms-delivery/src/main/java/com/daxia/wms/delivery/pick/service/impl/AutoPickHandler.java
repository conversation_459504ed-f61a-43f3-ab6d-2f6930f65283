package com.daxia.wms.delivery.pick.service.impl;

import org.jboss.seam.annotations.In;

import com.daxia.wms.delivery.deliveryorder.service.DoAutoHandler;
import com.daxia.wms.delivery.pick.service.PickService;

/**
 * 微便利拣货责任链
 */
@lombok.extern.slf4j.Slf4j
public class AutoPickHandler extends DoAutoHandler {
    @In
    private PickService pickService;

	@Override
	public void handle(Long doId) {
	    pickService.doHandlePick(doId);
	}

	@Override
	public void setName() {
		this.name = "autoPickHandler";
	}
}
