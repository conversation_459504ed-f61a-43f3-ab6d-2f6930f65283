package com.daxia.wms.delivery.recheck.dto;

import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeaderHis;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 表示核拣时一个箱子装箱信息
 */
@lombok.extern.slf4j.Slf4j
public class CrossReCheckCartonInfo {
	private DeliveryOrderHeader doHeader;
	private DeliveryOrderHeaderHis doHeaderHis;//发货单历史查询用
	private String cartonNo;
	private List<CrossReCheckCartonDetail> details;

	private String doNo;
	/**
	 * 分拣格号
	 */
	private String sortGridNo;

	/**
	 * 已装箱的数量
	 */
	private BigDecimal unitsQty;

	private String containerNo;

	public CrossReCheckCartonInfo(DeliveryOrderHeader doHeader) {
		this.doHeader = doHeader;
		details = new ArrayList<CrossReCheckCartonDetail>();
	}

	public void setDoHeader(DeliveryOrderHeader doHeader) {
		this.doHeader = doHeader;
	}

	/**
	 * 增加当前箱内的核拣记录
	 * 
	 * @param record
	 * @param number
	 */
	public void addReCheckRecord(CrossReCheckRecord record, BigDecimal number) {
		if (record == null) {
			return;
		}
		CrossReCheckCartonDetail detail = null;
		for (CrossReCheckCartonDetail d : details) {
			if (d.getRecord().getCrossDetailId().equals(record.getCrossDetailId()) ){
				detail = d;
				break;
			}
		}
		if (detail == null) {
			detail = new CrossReCheckCartonDetail(this, record, number);
            detail.setPackQty(record.getFinishedCheckNumber().intValue());
			details.add(detail);
		} else {
			detail.increaseNumberInCarton(number);
		}
		record.addCheckNumber(number);
	}

	/**
	 * 按商品id删除当前箱内的核拣记录
	 * @param productId
	 */
	public void removeReCheckRecordByProductId(Long productId) {
		for (int index = 0; index < details.size(); index++) {
			CrossReCheckCartonDetail d = details.get(index);
			if (!productId.equals(d.getRecord().getCrossDetailId())) {
				continue;
			}
			d.setNumberInCarton(d.getNumberInCarton().subtract(BigDecimal.ONE));
			d.getRecord().decreaseCheckNumber(BigDecimal.ONE);
			if (d.getNumberInCarton().compareTo(BigDecimal.ZERO) == 0) {
				details.remove(index);
			}
			return;
		}
	}

	/**
	 * 按商品条码查询 一个箱子里面某一个商品的详细装箱记录
	 * @param barCode
	 * @return
	 */
	public List<CrossReCheckCartonDetail> findDetailByProductBarCode(String barCode) {
		if (barCode == null) {
			return null;
		}
		List<CrossReCheckCartonDetail> results = new ArrayList<CrossReCheckCartonDetail>();
		for (CrossReCheckCartonDetail d : details) {
			if (barCode.equals(d.getRecord().getProductBarCode())) {
				results.add(d);
			}
		}
		return results;
	}

	public List<CrossReCheckCartonDetail> getDetails() {
		return details;
	}

	public void setDetails(List<CrossReCheckCartonDetail> details) {
		this.details = details;
	}

	public String getCartonNo() {
		return cartonNo;
	}

	public void setCartonNo(String cartonNo) {
		this.cartonNo = cartonNo;
	}

	public DeliveryOrderHeader getDoHeader() {
		return doHeader;
	}

	/**
	 * 清除箱中所有已装入的商品
	 */
	public void clear() {
		for (CrossReCheckCartonDetail d : details) {
			if (d == null) {
				continue;
			}
			CrossReCheckRecord record = d.getRecord();
			record.setFinishedCheckNumber(record.getFinishedCheckNumber()
					.subtract(d.getNumberInCarton()));
		}
	}

	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}
    
    public BigDecimal getUnitsQty() {
        return unitsQty;
    }
    
    public void setUnitsQty(BigDecimal unitsQty) {
        this.unitsQty = unitsQty;
    }

    public DeliveryOrderHeaderHis getDoHeaderHis() {
        return doHeaderHis;
    }
    
    public void setDoHeaderHis(DeliveryOrderHeaderHis doHeaderHis) {
        this.doHeaderHis = doHeaderHis;
    }

	public String getSortGridNo() {
		return sortGridNo;
	}

	public void setSortGridNo(String sortGridNo) {
		this.sortGridNo = sortGridNo;
	}

	public String getContainerNo() {
		return containerNo;
	}

	public void setContainerNo(String containerNo) {
		this.containerNo = containerNo;
	}

}
