package com.daxia.wms.delivery.task.replenish.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.DownloadUtil;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.print.dto.PrintCfg;
import com.daxia.wms.delivery.print.service.PrintReplService;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishHeader;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishTask;
import com.daxia.wms.delivery.task.replenish.filter.ReplHeaderFilter;
import com.daxia.wms.delivery.task.replenish.filter.ReplTaskFilter;
import com.daxia.wms.delivery.task.replenish.service.ReplHeaderService;
import com.daxia.wms.delivery.task.replenish.service.ReplTaskService;
import com.daxia.wms.delivery.task.replenish.service.ReplenishTaskGenerateContext;
import com.daxia.wms.delivery.util.ExportTrsTaskUtil;
import com.daxia.wms.master.service.SkuService;
import com.daxia.wms.stock.task.dao.TrsTaskDAO;
import lombok.SneakyThrows;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.intercept.BypassInterceptors;
import org.json.JSONArray;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

/**
 * 补货单管理
 */
@Name("com.daxia.wms.delivery.replHeaderAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ReplHeaderAction extends PagedListBean<ReplenishHeader> {

    private static final long serialVersionUID = -6529313027336728713L;

    @In
    private ReplHeaderService replHeaderService;

    @In
    private ReplenishTaskGenerateContext replenishTaskGenerateContext;
    
    @In
    private PrintReplService printReplService;

    @In
    private ReplTaskService replTaskService;
    
    @In
    private SkuService skuService;
    
    @In
    private TrsTaskDAO trsTaskDAO;

    private ReplHeaderFilter replHeaderFilter;

    private String rowIndex;

    /**
     * 闲时补货查询条件
     */
    private String partition;

    private String categoryName;

    private Long categoryId;

    private String locCode;

    private Long carrierId;

    private String productCode;

    private String aisle;

    private String doType;

    /**
     * 需要补货，但计算得到补货数量为0，无法生成补货任务的sku
     */
    private Map<String,Set<String>> replFailedSku;
    
    //打印补货单数据
    private String printData = "[]";
    
    //补货单打印参数设置
    private PrintCfg replPrintCfg;

    @Create
    @Loggable
    public void init(){
        dataPage = new DataPage<ReplenishHeader>();
        replHeaderFilter = new ReplHeaderFilter();
    }

    /**
     * 查询补货单
     */
    @Override
    public void query() {
        replFailedSku = null;
        replHeaderFilter.getOrderByMap().put("createdAt", "desc");
        this.buildOrderFilterMap(replHeaderFilter);
        DataPage<ReplenishHeader> dataPage = replHeaderService.query(replHeaderFilter, getStartIndex(), getPageSize());
        this.populateValues(dataPage);
    }

    /**
     * 即时补货
     */
    @Loggable
    public void addReplTask() throws Exception {
        replFailedSku = replenishTaskGenerateContext.createJPReplenishTask(carrierId, doType, Boolean.FALSE);
        boolean haveFailedMsg = false;
        if (replFailedSku != null && !replFailedSku.isEmpty()) {
            for (Entry<String, Set<String>> entry : replFailedSku.entrySet()) {
                Set<String> skuCodes = entry.getValue();

                if (!skuCodes.isEmpty()) {
                    haveFailedMsg = true;
                    this.sayMessage(entry.getKey(), ListUtil.collection2String(skuCodes, ","));
                }
            }
        }
        
        if (!haveFailedMsg) {
            this.sayMessage(MESSAGE_SUCCESS);
        }

        this.query();
    }

    /**
     * 闲时补货
     */
    @Loggable
    public void addXPReplTask() throws Exception {
        replFailedSku = null;
        replenishTaskGenerateContext.createXPReplenishTask(partition, productCode, aisle, Boolean.FALSE);
        replHeaderService.addReplTask(Constants.ReplType.XP.getValue(),null,Boolean.FALSE);
        this.query();
        this.sayMessage(MESSAGE_SUCCESS);
    }
    
    public void printRepl() {
        this.printData = "[]";
        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }
        List<String> printPages = printReplService.print(ids);
        this.replPrintCfg = printReplService.setDoPrintCfg();
        this.printData = new JSONArray(printPages).toString();
    }

    public void cancelReplHeader(){
        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
            ReplenishHeader replenishHeader = this.replHeaderService.get((Long)id);
            if(!(Constants.ReplStatus.RELEASED.getValue().equals(replenishHeader.getStatus()))){
                throw new DeliveryException(DeliveryException.CANCEL_REPL_HEADER_STATUS_ERROR); // 补货单状态不正确
            }
        }
        for(Long replHeaderId : ids){
            replTaskService.cancelReplHeader(replHeaderId);
        }
        sayMessage(MESSAGE_SUCCESS);
    }
    @BypassInterceptors
    public ReplHeaderFilter getReplHeaderFilter() {
        return replHeaderFilter;
    }

    public void setReplHeaderFilter(ReplHeaderFilter replHeaderFilter) {
        this.replHeaderFilter = replHeaderFilter;
    }

    public String getRowIndex() {
        return rowIndex;
    }

    public void setRowIndex(String rowIndex) {
        this.rowIndex = rowIndex;
    }

    public Map<String, Set<String>> getReplFailedSku() {
        return replFailedSku;
    }

    
    public void setReplFailedSku(Map<String, Set<String>> replFailedSku) {
        this.replFailedSku = replFailedSku;
    }

    public String getPartition() {
        return partition;
    }

    public void setPartition(String partition) {
        this.partition = partition;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getLocCode() {
        return locCode;
    }

    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }

    public String getAisle() {
        return aisle;
    }

    public void setAisle(String aisle) {
        this.aisle = aisle;
    }

    public String getDoType() {
        return doType;
    }

    public void setDoType(String doType) {
        this.doType = doType;
    }
    
    public String getPrintData() {
        return printData;
    }
    
    public void setPrintData(String printData) {
        this.printData = printData;
    }

	public PrintCfg getReplPrintCfg() {
		return replPrintCfg;
	}

	public void setReplPrintCfg(PrintCfg replPrintCfg) {
		this.replPrintCfg = replPrintCfg;
	}

    private ReplTaskFilter replTaskFilter;
    @SneakyThrows
    public void export(){
        replTaskFilter=new ReplTaskFilter();
        ArrayList<Long> ids = new ArrayList<>();
        replTaskFilter.setDocOperIds(ids);
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }
        List<ReplenishTask> replenishTasks = replTaskService.queryReplTrs(replTaskFilter);
        byte[] bytes = ExportTrsTaskUtil.generateForTaskList(replenishTasks);
        DownloadUtil.writeToResponse(bytes, DownloadUtil.EXCEL, "补货任务.xls");

    }
}
