package com.daxia.wms.delivery.task.replenish.service;

import java.util.List;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishHeader;
import com.daxia.wms.delivery.task.replenish.filter.ReplHeaderFilter;
import com.daxia.wms.master.entity.LaborHumanRegion;

/**
 * 补货单接口
 */
public interface ReplHeaderService {

    /**
     * 配置项：每张补货单包含补货任务的最大数量（方便分工）
     */
    public static final String MAXIMUM_PER_REPL_HEADER = "MAXIMUM_PER_REPL_HEADER";
    
    /**
     * 配置项：一次生成补货单的最大数量
     */
    public static final String REPLENISH_HEADER_MAXNUM = "REPLENISH.HEADER.MAXNUM";
    
    /**
     * 配置项：是否把补货任务分源库区纳入补货单（方便找到要补的货物）
     */
    public static final String RPL_BREAK_BY_PARTITION = "RPL_BREAK_BY_PARTITION";

    /**
     * 配置项：是否把补货任务分目标库区纳入补货单（方便把要补的货物上架）
     */
    public static final String RPL_BREAK_BY_TO_PARTITION = "RPL_BREAK_BY_TO_PARTITION";

    /**
     * 查询补货单
     * 
     * @param replHeaderFilter
     * @param startIndex
     * @param pageSize
     * @return
     */
    public DataPage<ReplenishHeader> query(ReplHeaderFilter replHeaderFilter, int startIndex, int pageSize);

    /**
     * 发布补货任务--生成补货单
     * 
     * @param replType
     */
    public void addReplTask(String replType, String doType,Boolean isAuto);

    /**
     * 按获取补货单头状态和补货任务状态查询补货单信息
     * 
     * @param replStatus
     *            获取补货单头状态
     * @param taskStatus
     *            补货任务状态
     * @return
     */
    public List<Object> findAvailableReplNo(List<String> replStatus, List<String> taskStatus);

    /**
     * 根据id获取补货单头
     * 
     * @param replHeaderId
     * @return
     */
    public ReplenishHeader get(Long replHeaderId);

    /**
     * 根据补货单号查询补货单头
     * 
     * @return
     */
    public ReplenishHeader queryReplenishHeaderByReplNo(String replNo);

	public Long countAvailableDoc(List<LaborHumanRegion> regionList);

	public void update(ReplenishHeader rpHeader);

	public ReplenishHeader demandDoc(List<Long> partitionIdList, boolean canCross);

    void updateOperator(String taskNo, Long newOperUserId);
}