package com.daxia.wms.delivery.print.service.carton;

import com.alibaba.fastjson.JSON;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.wms.Constants.WaybillType;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.print.dto.BaseCartonPrintDTO;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.dto.CustomerPrintDTO;
import com.daxia.wms.delivery.print.service.PrintWaybillService;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.recheck.service.impl.carton.CainiaoWayBillPrint;
import com.daxia.wms.master.entity.Carrier;
import com.daxia.wms.master.entity.CarrierCainiaoEx;
import com.daxia.wms.master.service.CarrierCainiaoExService;
import com.daxia.wms.master.service.ShopInfoService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.print.dto.PrintCfg;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import org.apache.commons.lang3.tuple.Pair;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.List;

@Name("printCaiNiaoWaybillService")
@lombok.extern.slf4j.Slf4j
public class PrintCaiNiaoWaybillServiceImpl extends AbstractPrintWaybillService implements PrintWaybillService {
    // 面单打印的产品类型
    public enum ProductTypePrint {
        JJKD, // 经济快递(申通,宅急送),
        BZKD, // 标准快递(圆通)
        BZKJ, // 标准快件(韵达)
        DSHK, // 代收货款(圆通,韵达,中通)
        KDBG // 快递包裹（EMS）
    }

    @In
    CainiaoWayBillPrint cainiaoWayBillPrint;

    @In
    private CartonService cartonService;

    @In
    private CarrierCainiaoExService carrierCainiaoExService;

    @In
    private WarehouseService warehouseService;

    @In
    private DeliveryOrderService deliveryOrderService;

    @In
    private ShopInfoService shopInfoService;

    @Create
    public void init () {
        this.setWaybillType(WaybillType.CAINIAO);
    }


    @Override
    protected void buildDTO(List<PrintReportDto> printReportDtos, DeliveryOrderHeader doHeader, BaseCartonPrintDTO carton,
                            int index, int count) {
        CartonPrintDTO cartonPrintDTO = new CartonPrintDTO();
        //记录打印日志使用
        cartonPrintDTO.setCartonId(carton.getId());
        cartonPrintDTO.setCartonNo(carton.getCartonNo());
        cartonPrintDTO.setDoNo(doHeader.getDoNo());
        cartonPrintDTO.setOutRefNo(doHeader.getRefNo1());
        cartonPrintDTO.setSortGridNo(doHeader.getSortGridNo());
        cartonPrintDTO.setWaveNo(doHeader.getWaveHeader().getWaveNo());
        cartonPrintDTO.setOriginalSoCode(doHeader.getOriginalSoCode());
        //菜鸟打印数据
        cartonPrintDTO.setPrintData(carton.getPrintData());
        //设置商家自定义区打印数据
        Carrier carrier = doHeader.getCarrier();
        CarrierCainiaoEx carrierCainiaoEx = carrierCainiaoExService.getByCarrier(carrier.getId());
        if (carrierCainiaoEx != null) {
            cartonPrintDTO.setCustomerTemplateURL(carrierCainiaoEx.getCustomerTemplateURL());
        }
        CustomerPrintDTO dto = new CustomerPrintDTO();
        dto.setDoInfo(doHeader.getDoNo() + "_" + doHeader.getSortGridNo());
        dto.setDoNo(doHeader.getDoNo());
        dto.setSortGridNo(doHeader.getSortGridNo());
        dto.setOriginalSoCode(doHeader.getOriginalSoCode());
        dto.setWaveNo(doHeader.getWaveHeader().getWaveNo());
        dto.setIsPrinted(carton.getIsPrinted());
        dto.setCreateTime(DateUtil.dateToString(doHeader.getCreatedAt(), DateUtil.DATETIME_PATTERN));
        dto.setPayTime(DateUtil.dateToString(doHeader.getPayTime(), DateUtil.DATETIME_PATTERN));
        if (doHeader.getReceivable() != null && doHeader.getReceivable().compareTo(BigDecimal.ZERO) > 0) {
            dto.setServiceCodAmount("代收货款 金额：￥" + doHeader.getReceivable() + "元");
        }
        
        cartonPrintDTO.setCustomerPrintData(JSON.toJSONString(dto));
        printReportDtos.add(cartonPrintDTO);
    }


    @Override
    protected PrintData genPrintData(List<PrintReportDto> dtoList, DeliveryOrderHeader doHeader) {
        PrintData printData = new PrintData();
        printData.setDtoList(dtoList);
        printData.setPrintCfg(generateCaiNiaoPrintCfg());
        return printData;
    }

    private PrintCfg generateCaiNiaoPrintCfg() {
        PrintCfg config = new PrintCfg("wayBillCanniao", "100", "180");
        this.setPrintCfg(config);
        return config;
    }
}