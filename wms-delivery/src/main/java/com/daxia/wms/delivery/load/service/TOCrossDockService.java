package com.daxia.wms.delivery.load.service;

import java.util.List;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.load.entity.TOCrossDockDetail;
import com.daxia.wms.delivery.load.entity.TOCrossDockHeader;
import com.daxia.wms.delivery.load.filter.CrossDockHeaderFilter;

/**
 * 功能说明：crdockHeader 的业务操作接口
 * 申明的方法如下:
 * <ul>
 *     <li>以分页面方式查询符合条件的CrdockHeader</li>
 *     <li>根据ID查找CrdockHeader</li>
 *     <li>根据ID序列查找对应CrdockHeader</li>
 *     <li>创建CrdockHeader</li>
 *     <li>删除CrdockHeader</li>
 *     <li>修改CrdockHeader</li>
 * </ul>
 * 典型用法：
 * 示例程序如下：
 * 特殊用法：
 * 创建者：
 * 创建时间：2011-9-7
 * 版本：0.1
*/
public interface TOCrossDockService {
	
	/**
	 * 以分页面方式查询符合条件的CrdockHeader
	 * @param filter 查询条件过滤器
	 * @param startIndex 当前页
	 * @param pageSize 每页显示条数
	 * return 符合条件的CrdockHeader, 返回值不为空
	 */
	public DataPage<TOCrossDockHeader> findCrossDockHeaders(CrossDockHeaderFilter filter,
			int startIndex, int pageSize);

	/**
	 * 根据ID查询CrossDockHeader
	 * @param id The id of CrossDockHeader
	 * return ID对应的CrossDockHeader
	 */
	public TOCrossDockHeader get(Long id) ;

    /**
     * 通过filter查询
     * 
     * @param crossDockHeaderFilter
     *            查询Filter
     * @return TOCrossDockHeader的list
     */
    public List<TOCrossDockHeader> query(CrossDockHeaderFilter crossDockHeaderFilter);
    
    /**
     * 根据ID查询CrossDockDetail
     * @param id
     * @return
     */
    public TOCrossDockDetail getCrossDockDetail(Long id);
}

