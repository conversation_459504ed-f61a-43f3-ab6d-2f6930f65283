package com.daxia.wms.delivery.pick.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.Pagination;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.pick.dto.PickInfoDTO;
import com.daxia.wms.delivery.pick.dto.PickTaskDto;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.filter.PickHeaderFilter;
import com.daxia.wms.Constants.PktMergeStatus;
import com.daxia.wms.master.entity.LaborHumanRegion;
import com.daxia.wms.master.entity.Partition;

/**
 * 出库部分PKT业务Service接口:
 */
public interface PickHeaderService {
    
    public static final String NOT_BIND_CONTAINER = "NO";

    public DataPage<PickHeader> findPktHeaders(PickHeaderFilter filter, int startIndex, int pageSize) throws DeliveryException;
    
    public Pagination<PickInfoDTO> findPickInfo(PickHeaderFilter filter, int startIndex, int pageSize) throws DeliveryException;
    /**
     * 
     * <pre>
     * Description:根据主键获得pktHeader对象
     * </pre>
     * 
     * @param pktHeaderId
     * @return PktHeader
     */
    public PickHeader getPktHeader(Long pktHeaderId);

    /**
     * 
     * <pre>
     * Description:更新pktHeader对象
     * </pre>
     * 
     */
    public void updatePktHeader(PickHeader pktHeader);

    /**
     * 
     * <pre>
     * Description:根据拣货单的拣货单号获取拣货单
     * </pre>
     * 
     * @param pktNo
     * @return 拣货单
     */
    public PickHeader getPktHeaderByPktNo(String pktNo);

    /**
     * <pre>
     * PktHeader查询
     * </pre>
     * 
     * @param pktFilter
     * @return
     */
    public List<PickHeader> query(PickHeaderFilter pktFilter);
    
    /**
     * Description:根据拣货单的波次号获取拣货单
     * @param waveId
     * @return
     */

    public List<PickHeader> getPktHeadersByWaveId(Long waveId);
    
    /**
     * Description:根据拣货单的波次id和库区id创建拣货单
     * @param waveId
     * @param partitionId
     * @return
     */

    public PickHeader createPktHeader(Long waveId, Long partitionId, Boolean isAvailable, Integer pktType);
    
    /**
     * Description:根据集货状态获取波次的拣货单数量
     * @param waveIds
     * @param merged
     * @return
     */
    public Map<? extends Long, ? extends Integer> getPktMergeCount(List<Long> waveIds, PktMergeStatus merged);

    /**
     * 根据波次主键查询拣货单记录数
     * @param waveHeadId
     * @return 拣货单记录数
     */
    public Long findPktHeaderCount(Long waveHeadId);
    
    /**
     * 将波次id为waveId的拣货单集货出库
     * @param waveId
     */
    public void completeMergeOut(Long waveId);
    
    /**
     * 改变拣货单状态
     * @param pktHeader
     * @param updateFromTime 是否需要更新拣货开始时间
     */
    public void changePktHeaderStatus(PickHeader pktHeader, boolean updateFromTime,String updateBy);
    
    /**
     * 获取拣货单按容器拣货重量信息
     * @param pktHeader
     * @return
     */
    public Map<String, BigDecimal> getPickHeaderPicGrossweightInfo(PickHeader pktHeader);
	
    /**
     * 查询波次下的拣货单及对应的所有库区code信息
     * @param waveId
     * @return map 其中key存放拣货单id，value存放库区codes
     */
    public Map<Long, List<String>> findPktPartitionCodesInWave(Long waveId);
    
    /**
     * 根据拣货单查询拣货人
     * @param pickHeaderId
     * @return
     */
    public List<String> findPickWhoByPickHeaderId(Long pickHeaderId);
    
    /**
     * 查找拣货单所包含的库区信息
     * @param pktId
     * @return
     */
    public List<String> findPartitionsByPktNo(String pktNo);
    
    /**
     * 设置拣货单为已打印状态
     * @param ids
     */
    public void setPickHeaderPrinted(List<Long> ids);
    
    /**
     * 计算拣货单的汇总信息，包括总units数，总毛重，总体积
     */
    public Map<String, BigDecimal> calcTotalInfo(Long pktHeaderId);
    
    /**
     * 根据波次及拣货单状态查区域
     * @param waveIds
     * @param status 拣货单状态
     * @return
     */
    @SuppressWarnings({ "rawtypes" })
    public List<List> findAreaByWaveIdAndStatus(List<Long> waveIds, List<String> status);
    
    /**
     * 查询拣货单对应的所有库区信息
     * @param pktHeaderId
     * @return
     */
    public List<Partition> findPartitionsInPktHeader(Long pktHeaderId);
    
    /**
     * 查询DO的波次下的指定状态拣货单号（微便利使用）
     * @param doId
     * @return
     */
    public List<String> queryPktNoInDosWave(Long doId, String status);
    
    /**
     * 获取全仓未指派的拣货单（状态为已发布）
     * @return
     */
    public Long countAllPktHeader();
    
    /**
     * 查询员工能索取的拣货任务数量
     * @return
     */
    public Long countOwnPktHeader(List<LaborHumanRegion> regionList,
			Map<String, List<String>> typeMap);
    
    /**
     * 人工索取一条可指派的拣货任务(根据劳动力管理模式 接力/并行)
     * @return
     */
    public PickHeader findPktHeaderForDemand(List<Long> regionIdList,  Map<String, List<String>> typeMap,Long waveId);
    
    public void saveOrUpdate(PickHeader pickHeader);

    /**
     * 根据库位与波次查找拣货单
     * @param locId
     * @param waveId
     * @return
     */
    public PickHeader findPktHeaderByLocAndWave(Long locId, Long waveId);
    
    /**
     * 根据波次和状态查找拣货单
     */
    public List<PickHeader> getPktHeadersByWaveIdAndStatus(Long waveId, String status);

    /**
     * 清除拣货单头的装箱关联信息
     * @param waveId
     * @param cartonId
     */
    void clearCartonStatus(Long waveId, Long cartonId);

    /**
     * 根据拣货单批量更新拣货单头
     * @param batchIdList
     * @param updateBy
     */
    void batchChangePktHeaderStatus(List<Long> batchIdList, String updateBy,Long waveId);

    /**
     * 通过订单号,拣货单类型来判断拣货任务是否完成
     * @param doNo
     * @param pktType
     * @return
     */
    Boolean isPickFinishedByDpNoAndPkType(String doNo, Integer pktType);
    
    List<PickTaskDto> findPickTask(Long pktId);
    
    PickInfoDTO getPickInfoDTO(Long pktId);
}
