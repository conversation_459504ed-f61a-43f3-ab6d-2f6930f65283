package com.daxia.wms.delivery.load.action;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.json.JSONArray;

import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.wms.delivery.load.entity.ReversalHandoverDetail;
import com.daxia.wms.delivery.load.entity.ReversalHandoverHeader;
import com.daxia.wms.delivery.load.service.ReversalHandoverHeaderService;
import com.daxia.wms.delivery.print.service.PrintReversalHandoverService;

/**
 * 逆向交接单Action
 */
@SuppressWarnings("serial")
@Name("com.daxia.wms.delivery.reversalHandoverDetailAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ReversalHandoverDetailAction implements Serializable {

	@In
	private ReversalHandoverHeaderService reversalHandoverHeaderService;

	private ReversalHandoverHeader reversalHandoverHeader;

	private String cartonNo;

	private Long reversalHandoverId;

	private boolean initialized = false;
	
	private Integer isAddNew = 0;
	
	private String printData = "[]";
	
	@In
	private PrintReversalHandoverService printReversalHandoverService;

	public void initialize() {
		if (!initialized) {
			if (reversalHandoverId == null) {
				isAddNew = 1;
				reversalHandoverHeader = new ReversalHandoverHeader();
				reversalHandoverHeader.setReversalHandoverDetails(new ArrayList<ReversalHandoverDetail>(0));
				reversalHandoverHeader.setReversalNo(reversalHandoverHeaderService.generatorReversalHandoverNumber());
			} else {
				reversalHandoverHeader = reversalHandoverHeaderService.get(reversalHandoverId);
			}
			initialized = true;
		}
	}

	public void handoverCartonNo() {
		reversalHandoverHeaderService.cartonHandover(reversalHandoverHeader,cartonNo);
		cartonNo = "";
	}
	
	/**
	 * 打印逆向交接单
	 */
	public void print(){
	    this.printData = "[]";
	    List<String> pages = printReversalHandoverService.printReversalHandover(reversalHandoverHeader);
	    this.printData = new JSONArray(pages).toString();
	}

	public ReversalHandoverHeader getReversalHandoverHeader() {
		return reversalHandoverHeader;
	}

	public void setReversalHandoverHeader(ReversalHandoverHeader reversalHandoverHeader) {
		this.reversalHandoverHeader = reversalHandoverHeader;
	}

	public String getCartonNo() {
		return cartonNo;
	}

	public void setCartonNo(String cartonNo) {
		this.cartonNo = cartonNo;
	}

	public Long getReversalHandoverId() {
		return reversalHandoverId;
	}

	public void setReversalHandoverId(Long reversalHandoverId) {
		this.reversalHandoverId = reversalHandoverId;
	}

	public ReversalHandoverDetailAction() {
		super();
	}
	
	public void setIsAddNew(Integer isAddNew) {
		this.isAddNew = isAddNew;
	}

	public Integer getIsAddNew() {
		return isAddNew;
    }

    public String getPrintData() {
        return printData;
    }

    public void setPrintData(String printData) {
        this.printData = printData;
    }

}