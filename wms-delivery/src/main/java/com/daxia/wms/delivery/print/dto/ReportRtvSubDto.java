package com.daxia.wms.delivery.print.dto;

import java.math.BigDecimal;

@lombok.extern.slf4j.Slf4j
public class ReportRtvSubDto {
    private Long lotId;
    private String productCode;
    private String barCode;
    private String productName;
    //生产厂家
    private String manufacturer;
    //单位
    private String uom;
    //批号
    private String lotatt05;
    //生产日期
    private String lotatt01;
    //有效日期
    private String lotatt02;
    private BigDecimal qty;
    private BigDecimal actualQty;
    //进价
    private String lotatt09;
    //金额
    
    @SuppressWarnings("unused")
    private BigDecimal amount;
    private String notes;

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }

    public String getLotatt05() {
        return lotatt05;
    }

    public void setLotatt05(String lotatt05) {
        this.lotatt05 = lotatt05;
    }

    public String getLotatt01() {
        return lotatt01;
    }

    public void setLotatt01(String lotatt01) {
        this.lotatt01 = lotatt01;
    }

    public String getLotatt02() {
        return lotatt02;
    }

    public void setLotatt02(String lotatt02) {
        this.lotatt02 = lotatt02;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getLotatt09() {
        return lotatt09;
    }

    public void setLotatt09(String lotatt09) {
        this.lotatt09 = lotatt09;
    }

    public BigDecimal getAmount() {
        if(lotatt09 == null || actualQty == null) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(lotatt09).multiply(actualQty);
    }

    public Long getLotId() {
        return lotId;
    }

    public void setLotId(Long lotId) {
        this.lotId = lotId;
    }

    public BigDecimal getActualQty() {
        return actualQty;
    }

    public void setActualQty(BigDecimal actualQty) {
        this.actualQty = actualQty;
    }
}