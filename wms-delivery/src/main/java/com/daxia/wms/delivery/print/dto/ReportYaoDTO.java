package com.daxia.wms.delivery.print.dto;

import java.util.List;

@lombok.extern.slf4j.Slf4j
public class ReportYaoDTO {
    private String doNo;

    //打单员
    private String printer;
    private String picker;
    private String rechecker;
    private String printFirstName;

    private String salesPerson;

    //送货员
    private String sender;

    private String waveNo;

    private String title;

    private String doType;

    private String printDate;

    private String supplier;

    private String orderAmount;

    private String orderAmountRMB;

    private String targetWarehouseName;

    private String address;

    private String addressHY;

    //条码类型
    private String doNoCodeType;

    private String customerCode;

    private String customerName;

    //包裹数量
    private String totalPackage;

    private String totalQty;

    private String shipTime;

    private String refNo;

    private String merchantName;

    private String merchantCode;

    private String notes;

    private String huayuanNotes;

    private String consigneeName;

    private String consigneeMobile;

    private List<ReportYaoSubDTO> subDtoList;

    private String lineName;//线路名称

    private String docTime;

    private String origId;

    public String getAddressHY() {
        return addressHY;
    }

    public void setAddressHY(String addressHY) {
        this.addressHY = addressHY;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(String orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getOrderAmountRMB() {
        return orderAmountRMB;
    }

    public void setOrderAmountRMB(String orderAmountRMB) {
        this.orderAmountRMB = orderAmountRMB;
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public String getPrintDate() {
        return printDate;
    }

    public void setPrintDate(String printDate) {
        this.printDate = printDate;
    }

    public String getDoType() {
        return doType;
    }

    public void setDoType(String doType) {
        this.doType = doType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getPrinter() {
        return printer;
    }

    public void setPrinter(String printer) {
        this.printer = printer;
    }

    public List<ReportYaoSubDTO> getSubDtoList() {
        return subDtoList;
    }

    public void setSubDtoList(List<ReportYaoSubDTO> subDtoList) {
        this.subDtoList = subDtoList;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getTargetWarehouseName() {
        return targetWarehouseName;
    }

    public void setTargetWarehouseName(String targetWarehouseName) {
        this.targetWarehouseName = targetWarehouseName;
    }

    public String getRechecker() {
        return rechecker;
    }

    public void setRechecker(String rechecker) {
        this.rechecker = rechecker;
    }

    public String getPicker() {
        return picker;
    }

    public void setPicker(String picker) {
        this.picker = picker;
    }

    public String getSalesPerson() {
        return salesPerson;
    }

    public void setSalesPerson(String salesPerson) {
        this.salesPerson = salesPerson;
    }

    public String getDoNoCodeType() {
        return doNoCodeType;
    }

    public void setDoNoCodeType(String doNoCodeType) {
        this.doNoCodeType = doNoCodeType;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getTotalPackage() {
        return totalPackage;
    }

    public void setTotalPackage(String totalPackage) {
        this.totalPackage = totalPackage;
    }

    public String getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(String totalQty) {
        this.totalQty = totalQty;
    }

    public String getShipTime() {
        return shipTime;
    }

    public void setShipTime(String shipTime) {
        this.shipTime = shipTime;
    }

    public String getRefNo() {
        return refNo;
    }

    public void setRefNo(String refNo) {
        this.refNo = refNo;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getMerchantCode() {
        return merchantCode;
    }

    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getHuayuanNotes() {
        return huayuanNotes;
    }

    public void setHuayuanNotes(String huayuanNotes) {
        this.huayuanNotes = huayuanNotes;
    }

    public String getConsigneeMobile() {
        return consigneeMobile;
    }

    public void setConsigneeMobile(String consigneeMobile) {
        this.consigneeMobile = consigneeMobile;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public String getPrintFirstName() {
        return printFirstName;
    }

    public void setPrintFirstName(String printFirstName) {
        this.printFirstName = printFirstName;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getDocTime() {
        return docTime;
    }

    public void setDocTime(String docTime) {
        this.docTime = docTime;
    }

    public String getOrigId() {
        return origId;
    }

    public void setOrigId(String origId) {
        this.origId = origId;
    }
}
