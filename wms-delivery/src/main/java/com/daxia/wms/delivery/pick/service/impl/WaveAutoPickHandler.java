package com.daxia.wms.delivery.pick.service.impl;

import java.util.List;

import org.jboss.seam.annotations.In;

import com.daxia.framework.common.util.ListUtil;
import com.daxia.wms.delivery.pick.service.PickService;
import com.daxia.wms.delivery.wave.service.WaveAutoHandler;

@lombok.extern.slf4j.Slf4j
public class WaveAutoPickHandler extends Wave<PERSON>utoHandler {
    @In
    private PickService pickService;
	
	@Override
	protected void handle(Long waveId) {
		List<Long> doIds = deliveryOrderService.qureyDoHeaderIdsByWaveId(waveId);
		if (ListUtil.isNotEmpty(doIds)) {
			for (Long doId : doIds) {
				pickService.doHandlePick(doId);
			}
		}
	}

	@Override
	protected void setName() {
		this.name = "waveAutoPickHandler";
	}
}
