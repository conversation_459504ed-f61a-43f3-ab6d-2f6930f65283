package com.daxia.wms.delivery.task.replenish.service.impl;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.SequenceName;
import com.daxia.wms.Constants.TaskStatus;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoDetailDAO;
import com.daxia.wms.delivery.task.replenish.dao.ReplenishHeaderDAO;
import com.daxia.wms.delivery.task.replenish.dto.LocationReplInfoDTO;
import com.daxia.wms.delivery.task.replenish.dto.ReplenishSkuDTO;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishHeader;
import com.daxia.wms.delivery.task.replenish.service.FreeReplService;
import com.daxia.wms.delivery.task.replenish.service.ReplTaskService;
import com.daxia.wms.master.dto.SkuDTO;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.entity.PackageInfoDetail;
import com.daxia.wms.master.entity.Partition;
import com.daxia.wms.master.entity.RotationHead;
import com.daxia.wms.master.service.*;
import com.daxia.wms.stock.stock.dto.Stock2AllocateDTO;
import com.daxia.wms.stock.stock.service.StockService;
import com.daxia.wms.stock.task.dao.TrsTaskDAO;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

@Name("com.daxia.wms.delivery.freeReplService")
@lombok.extern.slf4j.Slf4j
public class FreeReplServiceImpl implements FreeReplService {

	@In
	private DoDetailDAO doDetailDAO;
	
    @In
    private ReplenishHeaderDAO replenishHeaderDAO;

	@In
	private SkuCache skuCache;

	@In
	private StockService stockService;
	
	@In
	private RotationService rotationService;
	
    @In
    private SequenceGeneratorService sequenceGeneratorService;
    
    @In
    private ReplTaskService replTaskService;
    
    @In
    private PartitionService partitionService;

	@In
	private PartitionRuleService partitionRuleService;

	@In
	private PackageInfoDetailService packageInfoDetailService;
	
	@In
	protected TrsTaskDAO trsTaskDAO;
	
	@Override
    @Transactional
	public void createOneRplHeader(String replType, int maximumPerDoc, Long partitionId, String partitionType, Long pId2, String doType,
			Boolean isAuto) {
		ReplenishHeader replHeader = createReplenishHeader(replType, doType);
		replHeader.setIsAuto(isAuto ? YesNo.YES.getValue() : YesNo.NO.getValue());
		if (TrsTaskDAO.FROM_AND_TO.equals(partitionType)) {
			replHeader.setSrcPartitionId(partitionId);
			replHeader.setPartitionId(pId2);
		} else if (TrsTaskDAO.TO.equals(partitionType)) {
			replHeader.setPartitionId(partitionId);
		} else if (TrsTaskDAO.FROM.equals(partitionType)) {
			replHeader.setSrcPartitionId(partitionId);
		}
		
        if (replHeader.getSrcPartitionId() != null) {
            Partition partition = partitionService.get(replHeader.getSrcPartitionId());
            replHeader.setRegionId(partition == null ? null : partition.getRegionId());
        }
		
		replenishHeaderDAO.save(replHeader);

		// 是否 分源库区 且 分目标库区 生成补货单
		if (TrsTaskDAO.FROM_AND_TO.equals(partitionType)) {
			trsTaskDAO.updateReplTask(replHeader.getReplNo(), replHeader.getId(), replType, maximumPerDoc, partitionId, partitionType, pId2, doType,
					isAuto);
		} else {
			trsTaskDAO.updateReplTask(replHeader.getReplNo(), replHeader.getId(), replType, maximumPerDoc, partitionId, partitionType, null, doType,
					isAuto);
		}
		replHeader.setSkus(trsTaskDAO.getSkusByReplHeaderId(replHeader.getId()));
		replHeader.setUnits(trsTaskDAO.getUnitsByReplHeaderId(replHeader.getId()));
//		// 即时补货，设置最早预计出库时间 --闲时补货也会有预计出库时间了
//		if (Constants.ReplType.JP.getValue().equals(replType)) {
		replHeader.setEarlistPlanShipTime(trsTaskDAO.getEarlistPlanShipTimeInReplHeader(replHeader.getId()));
		replHeader.setPriority(trsTaskDAO.getHightestPriorityInReplHeader(replHeader.getId()).intValue());
//		}
		replenishHeaderDAO.update(replHeader);
		replenishHeaderDAO.getSession().flush();
		replenishHeaderDAO.getSession().clear();
	}
	
	/**
	 * 生成补货单头
	 * 
	 * @param replType
	 * @return
	 */
	private ReplenishHeader createReplenishHeader(String replType, String doType) {
		// 生成补货单头
		ReplenishHeader replHeader = new ReplenishHeader();
		String repNo = sequenceGeneratorService.generateSequenceNo(SequenceName.REPLENISHMENTNO.getValue(), ParamUtil.getCurrentWarehouseId());
		if (Constants.ReplType.JP.getValue().equals(replType)) {
			repNo = "J".concat(repNo);
		} else if (Constants.ReplType.XP.getValue().equals(replType)) {
			repNo = "X".concat(repNo);
		}
		replHeader.setReplNo(repNo);
		replHeader.setStatus(TaskStatus.RELEASED.getValue());
		replHeader.setReplType(replType);
		if (StringUtils.isNotEmpty(doType)) {
			replHeader.setDoType(doType);
		}
		return replHeader;
	}
}
