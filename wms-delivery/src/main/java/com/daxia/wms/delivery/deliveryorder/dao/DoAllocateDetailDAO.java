package com.daxia.wms.delivery.deliveryorder.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.*;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoType;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.deliveryorder.dto.DoAlcDetailInfoDto;
import com.daxia.wms.delivery.deliveryorder.dto.ManualAllocDTO;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.master.dto.SkuDTO;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.helper.SysConfigHelper;
import com.daxia.wms.master.service.SkuCache;
import com.daxia.wms.stock.stock.entity.StockBatchAtt;
import com.google.common.collect.Lists;
import com.idanchuang.ims.common.enums.InventoryBatchGoodsGradeEnum;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Query;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;

@Name("com.daxia.wms.delivery.doAllocateDetailDAO")
@lombok.extern.slf4j.Slf4j
public class DoAllocateDetailDAO extends HibernateBaseDAO<DoAllocateDetail, Long> {
    private static final long serialVersionUID = 7287210483998799482L;

    public static final String STAGE_JG = "STAGE-JG";
    @In
    SkuCache skuCache;
    /**
     * 更新订单分配明细的需要补货数量为0，拣货数量为0，分配数量为0，状态为目标状态
     *
     * @param status
     * @param ids
     */
    public void cancelDodetailAssignByIds(String status, List<Long> ids) {
        String hql = "update DoAllocateDetail o set o.needReplQty=0, o.lineStatus =:status, o.allocatedQty = 0,  o.allocatedQtyPcs = 0, o.allocatedQtyUnit = 0 " +
                "where o.id in (:ids) and o.warehouseId = :warehouseId";
        Query query = this.createUpdateQuery(hql);
        query.setString("status", status);
        query.setParameterList("ids", ids);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 查询组合产品子产品未分配完成
     *
     * @param lineStatus
     * @param doAllocateHeaderId
     * @return
     */
    public Long findMixChildCountsByStatus(String lineStatus, Long doAllocateHeaderId) {
        Long count = Long.valueOf(0);
        String hql = "select  count(o.id) from DoAllocateDetail o where o.lineStatus <>:lineStatus" +
                " and o.parentId is not null and o.isDoLeaf =:isDoLeaf and o.doHeaderId =:doHeaderId and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setString("lineStatus", lineStatus);
        query.setInteger("isDoLeaf", Constants.YesNo.YES.getValue());
        query.setLong("doHeaderId", doAllocateHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        Object obj = query.uniqueResult();
        if (obj != null) {
            count = (Long) obj;
        }
        return count;
    }

    /**
     * <pre>
     * Description:根据订单Id 更新订单明细行状态为status
     * </pre>
     *
     * @param doAllocateHeaderId 分配发运订单头信息Id
     * @param status             状态值
     */
    public void updateDoDetailStatusByDoId(Long doAllocateHeaderId, String status) {
        String hql = "update DoAllocateDetail o set o.lineStatus =:status  where  o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId";
        Query query = this.createUpdateQuery(hql);
        query.setString("status", status);
        query.setLong("doHeaderId", doAllocateHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

	/**
	 * 删除订单的分配明细
	 * @param ids
	 */
    public void removeByHeaderId(List<Long> ids) {
        String sqlString = "delete detail from doc_alc_detail detail where detail.do_header_id in (:headerIds) and detail.warehouse_id = :warehouseId";
        Query query = this.createSQLQuery(sqlString);
        query.setParameterList("headerIds", ids);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }
    
    /**
     * 查询订单的某种状态的分配明细的数量 
     * @param status
     * @param doHeaderId
     * @return
     */
    public Long findDoAllocateDetailCountByStatus(String status, Long doHeaderId) {
        Long count = Long.valueOf(0);
        String hql = "select count(o.id) from DoAllocateDetail o where o.lineStatus =:status and o.doHeaderId =:doHeaderId and o.isDoLeaf = 1  and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setString("status", status);
        query.setLong("doHeaderId", doHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        Object obj = query.uniqueResult();
        if (obj != null) {
            count = (Long) obj;
        }
        return count;
    }

    /**
     * 构建ManualAllocDTO（人工分配）
     *
     * @param doAllocateHeader
     * @param doAllocateDetail
     * @param packIds
     *@param startRow
     * @param pageSize   @return
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public DataPage<ManualAllocDTO> queryManualAllocInfo(DoAllocateHeader doAllocateHeader, DoAllocateDetail doAllocateDetail, List<String> packIds, int startRow, int pageSize, String locType, String locCode) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        StringBuilder sbSql = new StringBuilder(" from ");
        sbSql.append("(select stock1.id as stockInfoId,");
        sbSql.append("bat.lot_no as lotNo,");
        sbSql.append("loc.loc_code as locCode,");
        sbSql.append("loc.loc_type as locType, ");
        sbSql.append("stock1.lpn_no as lpnNo,");
        sbSql.append("sku.product_code as proCode,");
        sbSql.append("sku.ean13 as ean13,");
        sbSql.append("sku.product_cname as proName,");
        sbSql.append("pa.partion_code as partionCode,");
        sbSql.append("stock1.qty_available as stockQty,");
        sbSql.append("stock1.qty_available_unit as stockQtyUnit,");
        sbSql.append("bat.lotatt01 as lotatt01,");
        sbSql.append("bat.lotatt02 as lotatt02,");
        sbSql.append("bat.lotatt03 as lotatt03,");
        sbSql.append("bat.lotatt05 as lotatt05,");
        sbSql.append("bat.lotatt14 as lotatt14,");
        sbSql.append("bat.lotatt16 as lotatt16,");
        sbSql.append("pkg_d_b.uom_descr as lotatt07,");
        sbSql.append("bat.lotatt08 as lotatt08,");
        sbSql.append("su.supplier_company_name as supplierCompanyName,");
        sbSql.append("me.descr_c as merchant,");
        sbSql.append("bat.lotatt10 as lotatt10, ");
        sbSql.append("loc.package_type as packageType ");
        sbSql.append(" from stk_batch_loc_lpn stock1 inner join stock_batch_att bat on stock1.lot_id = bat.id ");
        sbSql.append(" and stock1.sku_id = :skuId ");
        paramMap.put("skuId", doAllocateDetail.getSkuId());

        if (StringUtil.isNotEmpty(doAllocateDetail.getLotatt04()) && !(doAllocateHeader.getDoType().equals(Constants.DoType.RTV.getValue())
                && Config.isDefaultFalse(Keys.Delivery.allocate_rtvIngoreSupplier, Config.ConfigLevel.WAREHOUSE))) {
            sbSql.append(" and bat.lotatt04 = :lot4 ");
            paramMap.put("lot4", doAllocateDetail.getLotatt04());
        }
        if (!SysConfigHelper.getSwitchDefalutClosed("allocate.delivery.isMerchantShareStock")) {
            if (StringUtil.isNotEmpty(doAllocateDetail.getLotatt06())) {
                sbSql.append(" and bat.lotatt06 = :lot6 ");
                paramMap.put("lot6", doAllocateDetail.getLotatt06());
            }
        }
        if (doAllocateDetail.getGoodsGrade() < InventoryBatchGoodsGradeEnum.GOODS_GRADE_CP_MINOR.getBatchGoodsGradeType()){
            judgeExpire(doAllocateDetail.getSkuId(), sbSql, "bat", doAllocateDetail.getMinExp(), doAllocateDetail.getMaxExp());
        }

        //人工分配忽略批次信息
        if (!SysConfigHelper.getSwitchDefalutClosed("allocate.delivery.manual.ignore.batch")) {
            if (StringUtil.isNotEmpty(doAllocateDetail.getLotatt01())) {
                sbSql.append(" and bat.lotatt01 = :lot1 ");
                paramMap.put("lot1", doAllocateDetail.getLotatt01());
            }
            if (StringUtil.isNotEmpty(doAllocateDetail.getLotatt02())) {
                sbSql.append(" and bat.lotatt02 = :lot2 ");
                paramMap.put("lot2", doAllocateDetail.getLotatt02());
            }
            if (StringUtil.isNotEmpty(doAllocateDetail.getLotatt03())) {
                sbSql.append(" and bat.lotatt03 = :lot3 ");
                paramMap.put("lot3", doAllocateDetail.getLotatt03());
            }
            if (StringUtil.isNotEmpty(doAllocateDetail.getLotatt05())) {
                sbSql.append(" and bat.lotatt05 = :lot5 ");
                paramMap.put("lot5", doAllocateDetail.getLotatt05());
            }
            if (ListUtil.isNotEmpty(packIds)) {
                sbSql.append(" and bat.lotatt07 in(:lot7) ");
                paramMap.put("lot7", packIds);
            }
            if (StringUtil.isNotEmpty(doAllocateDetail.getLotatt08())) {
                sbSql.append(" and bat.lotatt08 = :lot8 ");
                paramMap.put("lot8", doAllocateDetail.getLotatt08());
            }
            if (StringUtil.isNotEmpty(doAllocateDetail.getLotatt09())) {
                sbSql.append(" and bat.lotatt09 = :lot9 ");
                paramMap.put("lot9", doAllocateDetail.getLotatt09());
            }
            if (StringUtil.isNotEmpty(doAllocateDetail.getLotatt10())) {
                sbSql.append(" and bat.lotatt10 = :lot10 ");
                paramMap.put("lot10", doAllocateDetail.getLotatt10());
            }
            if (StringUtil.isNotEmpty(doAllocateDetail.getLotatt11())) {
                sbSql.append(" and bat.lotatt11 = :lot11 ");
                paramMap.put("lot11", doAllocateDetail.getLotatt11());
            }
            if (StringUtil.isNotEmpty(doAllocateDetail.getLotatt13())) {
                sbSql.append(" and bat.lotatt13 = :lot13 ");
                paramMap.put("lot13", doAllocateDetail.getLotatt13());
            }
            if (doAllocateDetail.getGoodsGrade() != null && doAllocateDetail.getGoodsGrade()>=20) {
                sbSql.append(" and bat.lotatt14 = :lot14 ");
                paramMap.put("lot14", doAllocateDetail.getGoodsGrade().toString());
            }
            if (StringUtil.isNotEmpty(doAllocateDetail.getLotatt15())) {
                sbSql.append(" and bat.lotatt15 = :lot15 ");
                paramMap.put("lot15", doAllocateDetail.getLotatt15());
            }
            if (StringUtil.isNotEmpty(doAllocateDetail.getLotatt16())) {
                sbSql.append(" and bat.lotatt16 = :lot16 ");
                paramMap.put("lot16", doAllocateDetail.getLotatt16());
            }
            if (StringUtil.isNotEmpty(doAllocateDetail.getLotatt17())) {
                sbSql.append(" and bat.lotatt17 = :lot17 ");
                paramMap.put("lot17", doAllocateDetail.getLotatt17());
            }
            if (StringUtil.isNotEmpty(doAllocateDetail.getLotNo())) {
                sbSql.append(" and bat.lot_no = :lot18 ");
                paramMap.put("lot18", doAllocateDetail.getLotNo());
            }
        }


        if (StringUtil.isNotEmpty(doAllocateDetail.getLotatt12())) {
            sbSql.append(" and bat.lotatt12 = :lot12 ");
            paramMap.put("lot12", doAllocateDetail.getLotatt12());
        }
        sbSql.append(" left join md_sku sku on stock1.sku_id = sku.id ");
        sbSql.append(" left join md_location loc on stock1.loc_id = loc.id and loc.warehouse_id =:warehouseId and loc.use_status = 0 ");
        sbSql.append(" left join md_partition pa on loc.partition_id = pa.id and pa.warehouse_id = :warehouseId ");
        sbSql.append(" left join md_supplier su on bat.Lotatt04 = su.id ");
        sbSql.append(" left join md_merchant me on bat.lotatt06 = me.id ");
        sbSql.append(" left join md_package_d pkg_d_b on bat.lotatt07 = pkg_d_b.id ");
        sbSql.append(" where stock1.warehouse_id = :warehouseId and bat.warehouse_id = :warehouseId ");
        sbSql.append(" and bat.status >= " + StockBatchAtt.STATUS_NORMAL + " ");
    
//        List<String> locCodes = AllocateHelper.buildLoc4Alloc(doAllocateDetail);
//        if (ListUtil.isNotEmpty(locCodes)) {
//            sbSql.append(" and loc.loc_code in (:locCodes) ");
//            paramMap.put("locCodes", locCodes);
//        }
        if(StringUtils.isNotBlank(locCode)){
            sbSql.append(" and loc.loc_code = :locCodex ");
            paramMap.put("locCodex", locCode);
        }
        Integer isDamaged = doAllocateDetail.getIsDamaged();
        if (isDamaged == null || YesNo.NO.getValue().equals(isDamaged)) {
            List<String> locTypes;
            if (DoType.RTV.getValue().equals(doAllocateHeader.getDoType())) {
                //不是坏品，RTV类型订单不从坏品库位分配
                if(StringUtils.isNotBlank(locType)) {
                    sbSql.append(" and loc.loc_type in (:locTypes) ");
                    locTypes=Lists.newArrayList(locType);
                    paramMap.put("locTypes", locTypes);
                }else{
                    sbSql.append(" and loc.loc_type not in  (:locTypes )");
                    locTypes=Lists.newArrayList(Constants.LocType.DM.getValue(),Constants.LocType.EACB.getValue());
                    paramMap.put("locTypes", locTypes);
                }

            } else if (DoType.WHOLESALE.getValue().equals(doAllocateHeader.getDoType())) {
                sbSql.append(" and loc.loc_type in (:locTypes) ");
                if(StringUtils.isBlank(locType)) {
                    locTypes=Lists.newArrayList(Constants.LocType.EA.getValue(), Constants.LocType.ST.getValue());
                }else{
                    locTypes=Lists.newArrayList(locType);
                }
                paramMap.put("locTypes", locTypes);

            } else {
                //不是坏品，非RTV类型订单从EA分配
    
                sbSql.append(" and loc.loc_type in (:locTypes) ");
                locTypes = Lists.newArrayList(Constants.LocType.EA.getValue());
                //如果是团购订单且指定了库位从定向拣货位出库
                DoWaveEx doWaveEx = doAllocateHeader.getDoWaveEx();
                if (doWaveEx != null && Constants.AutoWaveType.BATCH_GROUP.getValue().equals(doWaveEx.getAutoWaveType())) {
                    locTypes.add(Constants.LocType.DEA.getValue());
                }
                if (DoType.ALLOT.getValue().equals(doAllocateHeader.getDoType())||DoType.MPS_OUT.getValue().equals(doAllocateHeader.getDoType())) {
					locTypes.add(Constants.LocType.ST.getValue());
                    locTypes.add(Constants.LocType.RS.getValue());
                    locTypes.add(Constants.LocType.DEA.getValue());
                }
                if(StringUtils.isNotBlank(locType)) {
                    locTypes.clear();
                    locTypes.add(locType);
                }
                paramMap.put("locTypes",locTypes);
            }
        } else if (Constants.YesNo.YES.getValue().equals(isDamaged)) {
            //坏品从坏品库位分配
    
            sbSql.append(" and loc.loc_type  in (:locTypes) and loc.loc_code <> :locCode ");
            List<String> locTypes = Lists.newArrayList();
            locTypes.add(Constants.LocType.DM.getValue());
            if (Constants.YesNo.YES.getValue().equals(doAllocateHeader.getIsDirect())) { //直配支持从暂存位分配库存
                locTypes.add(Constants.LocType.ST.getValue());
            }
            if(StringUtils.isNotBlank(locType)) {
                locTypes.clear();
                locTypes.add(locType);
            }
            paramMap.put("locTypes",locTypes);
            paramMap.put("locCode", Location.SpecialLocation.BHG.name());
        }

        //FF01-01-01-01  过滤STAGE-JG库位
        sbSql.append("  and loc.loc_code <> :locCode2 ");
        paramMap.put("locCode2", STAGE_JG);

        sbSql.append(" ) stockInfo left join ( ");
        sbSql.append(" select stock.id as allocStockId,pic.id as picId,ifnull(pic.qty, 0) as allocQty, ifnull(pic.qty_unit, 0) as allocQtyUnit ");
        sbSql.append(" from stk_batch_loc_lpn stock,stk_allocated alloc, tsk_pick pic ");
        sbSql.append(
                " where stock.id = alloc.stk_lpn_id and alloc.id = pic.fm_stock_id and pic.doc_line_id = :docLineId and stock.warehouse_id = :warehouseId and alloc.warehouse_id = :warehouseId and pic.warehouse_id = :warehouseId and pic.is_deleted = 0 ) " +
                        "allocStock ");
        paramMap.put("docLineId", doAllocateDetail.getId());
        sbSql.append(" on stockInfo.stockInfoId = allocStock.allocStockId ");
        sbSql.append(" left join (select allocating.stk_lpn_id as allocatingStockId, sum(allocating.qty_allocating) as allocatingQty, sum(allocating.qty_allocating_unit) as allocatingQtyUnit ");
        sbSql.append(" from stk_allocating allocating where allocating.qty_allocating > 0 and allocating.warehouse_id = :warehouseId and allocating.is_deleted = 0 ");
        sbSql.append(" group by allocating.stk_lpn_id ) allocatingInfo ");
        sbSql.append(" on stockInfo.stockInfoId = allocatingInfo.allocatingStockId ");
        sbSql.append(" where (ifnull(stockInfo.stockQty,0) + ifnull(allocStock.allocQty,0) - ifnull(allocatingInfo.allocatingQty,0)) > 0 ");

        StringBuilder sbQueryWhat = new StringBuilder();
        sbQueryWhat.append("select stockInfo.stockInfoId,")
                .append("stockInfo.lotNo,")
                .append("stockInfo.locCode,")
                .append("stockInfo.locType, ")
                .append("stockInfo.lpnNo,")
                .append("stockInfo.proCode,")
                .append("stockInfo.ean13,")
                .append("stockInfo.proName,")
                .append("stockInfo.partionCode,")
                .append("stockInfo.stockQty - ifnull(allocatingInfo.allocatingQty,0),")
                .append("stockInfo.lotatt01,")
                .append("stockInfo.lotatt02,")
                .append("stockInfo.lotatt03,")
                .append("stockInfo.lotatt05,")
                .append("stockInfo.lotatt07,")
                .append("stockInfo.lotatt08,")
                .append("stockInfo.supplierCompanyName,")
                .append("stockInfo.merChant,")
                .append("allocStock.picId as picId,")
                .append("ifnull(allocStock.allocQty,0),")
                .append("stockInfo.lotatt10 ")
                .append(", ifnull(allocStock.allocQtyUnit, 0) ")
                .append(", stockInfo.stockQtyUnit - ifnull(allocatingInfo.allocatingQtyUnit, 0), ")
                .append("stockInfo.packageType,")
                .append("stockInfo.lotatt16,")
                .append("stockInfo.lotatt14")
        ;
        sbQueryWhat.append(sbSql.toString());

        //根据订单类型对可用库存按效期进行排序
        if (StringUtil.isIn(doAllocateHeader.getDoType(), DoType.RTV.getValue())) {
            // RTV订单可用库存，需按效期排序，效期差的排在最前面。先按失效日期升序，失效日期相同的按入库日期升序排序
            // SLC的目前也照此逻辑排序
            sbQueryWhat.append(" order by stockInfo.lotatt02 asc, stockInfo.lotatt03 asc");
        } else if (Constants.DoType.ALLOT.getValue().equals(doAllocateHeader.getDoType())) {
            // 调拨订单可用库存，需按效期排序，效期好的排在最前面。先按失效日期降序，失效日期相同的按入库日期降序排序
            
            sbQueryWhat.append(" order by stockInfo.lotatt02 desc, stockInfo.lotatt03 desc");
        }
        paramMap.put("warehouseId", ParamUtil.getCurrentWarehouseId());
	    String selectString = sbQueryWhat.toString();

        String countString = "select count(*) " + (sbSql.toString());
        Query dataQuery = this.createSQLQuery(selectString);
        Query countQuery = this.createSQLQuery(countString);

        this.prepareParameter(paramMap, dataQuery, countQuery);

        dataQuery.setFirstResult(startRow);
        
        dataQuery.setMaxResults(pageSize);

        List<?> list = dataQuery.list();
        BigInteger cnt = (BigInteger) countQuery.uniqueResult();
        List<ManualAllocDTO> dataList = new ArrayList<ManualAllocDTO>();
        Object temp = null;
        for (Object obj : list) {
            ManualAllocDTO dto = new ManualAllocDTO();
            Object[] objArray = (Object[]) obj;
            dto.setStockId((temp = objArray[0]) == null ? null : ((BigInteger) temp).longValue());
            dto.setLotNo((temp = objArray[1]) == null ? null : (String) temp);
            dto.setLocCode((temp = objArray[2]) == null ? null : (String) temp);
            dto.setLocType((temp = objArray[3]) == null ? null : (String) temp);
            dto.setLpnNo((temp = objArray[4]) == null ? null : (String) temp);
            dto.setProductCode((temp = objArray[5]) == null ? null : (String) temp);
            dto.setEan13((temp = objArray[6]) == null ? null : (String) temp);
            dto.setProductName((temp = objArray[7]) == null ? null : (String) temp);
            dto.setPartion((temp = objArray[8]) == null ? null : (String) temp);
            dto.setStockQty((temp = objArray[9]) == null ? BigDecimal.ZERO : (BigDecimal) temp);
            dto.setLotatt01((temp = objArray[10]) == null ? null : (String) temp);
            dto.setLotatt02((temp = objArray[11]) == null ? null : (String) temp);
            dto.setLotatt03((temp = objArray[12]) == null ? null : (String) temp);
            dto.setLotatt05((temp = objArray[13]) == null ? null : (String) temp);
            dto.setLotatt07((temp = objArray[14]) == null ? null : (String) temp);
            dto.setLotatt08((temp = objArray[15]) == null ? null : (String) temp);
            dto.setSupplier((temp = objArray[16]) == null ? null : (String) temp);
            dto.setMerchant((temp = objArray[17]) == null ? null : (String) temp);
            dto.setPicTaskId((temp = objArray[18]) == null ? null : ((BigInteger) temp).longValue());
            dto.setAllocatedQty((temp = objArray[19]) == null ? BigDecimal.ZERO : (BigDecimal) temp);
            dto.setAllocatedQtyUnit((temp = objArray[21]) == null ? BigDecimal.ZERO : (BigDecimal) temp);
            dto.setAllocingQty(dto.getAllocatedQtyUnit());
            dto.setLotatt10((temp = objArray[20]) == null ? null : (String) temp);
            dto.setStockQtyUnit((temp = objArray[22]) == null ? BigDecimal.ZERO : (BigDecimal) temp);
            dto.setLocPackageType((temp = objArray[23]) == null ? null : (String) temp);
            dto.setLotatt16((temp = objArray[24]) == null ? null : (String) temp);
            dto.setLotatt14((temp = objArray[25]) == null ? null : (String) temp);
            dataList.add(dto);
        }
        return new DataPage(cnt.intValue(), startRow, pageSize, dataList);
    }

    /**
     * 删除SLC分配明细
     */
    public void deleteALcDetail(Long doHeaderId) {
        String sql = "delete from doc_alc_detail where do_header_id = :doHeaderId and warehouse_id = :whId";
        Query query = createSQLQuery(sql);
        query.setParameter("doHeaderId", doHeaderId);
        query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    // 统计SKU的个数（只包含叶子节点）
    public Integer countSkuNum(List<Long> doIdList) {
        String hql = "SELECT COUNT(DISTINCT o.skuId) FROM DoAllocateDetail o WHERE o.isDoLeaf = :isDoLeaf AND o.doHeaderId IN (:doIdList)";
        Query query = createQuery(hql);
        query.setParameterList("doIdList", doIdList);
        query.setParameter("isDoLeaf", YesNo.YES.getValue());
        Object object = query.setMaxResults(1).uniqueResult();
        return object == null ? 0 : ((Long) object).intValue();
    }

    public Map<Long, Long> getDoLeafSkuId(List<Long> doIds) {
        String hql = "SELECT dd.doHeaderId, dd.skuId FROM DoAllocateDetail dd WHERE dd.isDoLeaf = :isDoLeaf AND dd.doHeaderId in(:doIds) GROUP BY dd.skuId, dd.doHeaderId";
        Query query = createQuery(hql);
        query.setParameterList("doIds", doIds);
        query.setParameter("isDoLeaf", YesNo.YES.getValue());

        List<?> list = query.list();
        Map<Long, Long> resultMap = new HashMap<Long, Long>();
        for (Object obj : list) {
            Object[] objArray = (Object[]) obj;

            resultMap.put((Long) objArray[0], (Long) objArray[1]);
        }
        return resultMap;
    }
    
    /**
     * 按明细id删除SLC分配明细
     */
    public void deleteALcDetail(List<Long> alcDetailIds) {
        String sql = "delete from doc_alc_detail where id in (:alcDetailIds) and warehouse_id = :whId";
        Query query = createSQLQuery(sql);
        query.setParameterList("alcDetailIds", alcDetailIds);
        query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    @SuppressWarnings("unchecked")
    public List<Long> getAlcDetailIdsByDo(Long doId) {
        String hql = "select o.id from DoAllocateDetail o where o.doHeaderId = :doId and o.warehouseId = :whId";
        Query query = createQuery(hql);
        query.setParameter("doId", doId);
        query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
        return (List<Long>) query.list();
    }


    @SuppressWarnings("unchecked")
    public List<DoAlcDetailInfoDto> getAlcDetailInfosByDo(Long doId) {
        StringBuilder hql = new StringBuilder(" select  dd.id, dh.do_no, dh.status, dh.release_status, dd.expected_qty, ifnull(kk.totalQty,0), dh.create_time, ms.product_code, ms.product_cname, " +
                " kk.lotatt05, kk.lotatt01, kk.lotatt02, kk.lotatt09,ms.specification,ml.loc_code  " +
                "  from doc_do_detail  dd left join doc_do_header dh on dh.id = dd.do_header_id " +
                " left join (  SELECT tp.sku_id, tp.doc_line_id, SUM(tp.qty)AS totalQty, sba.lotatt05, sba.lotatt01, sba.lotatt02, sba.lotatt09 ,tp.fm_loc_id from tsk_pick tp " +
                " left join stock_batch_att sba on sba.id = tp.lot_id left join md_location ml on ml.id = tp.fm_loc_id and ml.use_status = 0 " +
                "  where tp.doc_id =:doHeaderId and sba.is_deleted = 0 and tp.is_deleted = 0 and tp.warehouse_id = :whId " +
                "group by tp.fm_loc_id,tp.sku_id, tp.doc_line_id, sba.lotatt05, sba.lotatt01, sba.lotatt02, sba.lotatt09) kk " +
                "  on kk.doc_line_id = dd.id LEFT JOIN md_sku ms ON ms.id = dd.sku_id LEFT JOIN md_location ml ON ml.id = kk.fm_loc_id and ml.use_status = 0 where dh.id = :doHeaderId and dh.is_deleted = 0 and dd.is_deleted = 0  and dh.warehouse_id = :whId ") ;
        Query query = createSQLQuery(hql.toString());
        query.setParameter("doHeaderId", doId);
        query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
         List<Object[]> results = query.list();
        List<DoAlcDetailInfoDto> dtos = new ArrayList<DoAlcDetailInfoDto>(results.size());
        for (Object[] objs : results) {
            DoAlcDetailInfoDto dto = new DoAlcDetailInfoDto();
            dto.setId(((BigInteger) objs[0]).longValue());
            dto.setDoNo((String) objs[1]);
            dto.setStatus((String) objs[2]);
            dto.setReleaseStatus((String) objs[3]);
            dto.setExpectedQty((BigDecimal) objs[4]);
            dto.setAlcQty((BigDecimal) objs[5]);
            dto.setDoCreateTime((java.util.Date) objs[6]);
            dto.setProductCode((String) objs[7]);
            dto.setProductCname((String) objs[8]);
            dto.setLotatt05((String) objs[9]);
            dto.setLotatt01((String) objs[10]);
            dto.setLotatt02((String) objs[11]);
            dto.setLotatt09((String) objs[12]);
            dto.setSpecification((String) objs[13]);
            dto.setLocCode((String) objs[14]);
            dtos.add(dto);
        }
        return dtos;
    }
    //是否需要按效期配置出库，比如药品的180天
    private void judgeExpire(Long skuId,  StringBuilder hql, String battchAtt, Date minExp, Date maxExp) {
        SkuDTO sku = skuCache.getSku(skuId);
        if (com.daxia.framework.system.constants.Constants.YesNo.YES.getValue().toString().equals(sku.getShelfLifeFlg())) {
            // 未指定效期范围
            if(Objects.isNull(minExp)){
                Integer validityDay = Config.getInt(Keys.Delivery.allocateDay, Config.ConfigLevel.WAREHOUSE, 30);
                if (validityDay < 0) {
                    return;
                }
                if (validityDay != null) {
                    hql.append("and " + battchAtt + ".lotatt02 >= DATE_FORMAT(TIMESTAMPADD(DAY, " + validityDay.intValue() + ", now()),'%Y-%m-%d') ");
                }
            }else{
                hql.append("and " + battchAtt + ".lotatt02 > '"+DateUtil.dateToString(minExp,
                        DateUtil.ISO_EXPANDED_DATE_FORMAT)+"'");
                if(Objects.nonNull(maxExp)){
                    hql.append("and " + battchAtt + ".lotatt02 <= '"+DateUtil.dateToString(maxExp,
                            DateUtil.ISO_EXPANDED_DATE_FORMAT)+"'");
                }
            }
        }
    }

}