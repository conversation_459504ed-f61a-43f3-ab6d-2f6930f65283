package com.daxia.wms.delivery.wave.job;

import com.daxia.framework.common.log.StopWatch;
import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.MailUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.wave.service.AutoWaveGenService;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.job.AbstractJob;
import com.daxia.wms.master.rule.entity.WaveRuleDetail;
import com.daxia.wms.master.rule.service.WaveRuleService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.util.AlarmUtil;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.*;
import org.jboss.seam.log.Log;

import java.util.List;

/**
 * 自动波次定时任务
 */
@Name("autoWaveJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class AutoWaveJob extends AbstractJob {
    


    @In
    private WarehouseService warehouseService;
    @In
    private AutoWaveGenService autoWaveGenService;
    @In
    private WaveRuleService waveRuleService;
    
    @Override
    protected void doRun() throws Exception {
        log.debug("Begin AutoWaveJob，Please Waiting ............... ");
        StopWatch stopWatch = new StopWatch("AutoWaveJob.run");
        try {
            // 每次操作执行前清空当前的warehouseId，防止被定时任务线程执行其他任务所应用
            List<Warehouse> whList = warehouseService.getRunningWh();
            //循环遍历每个仓库，执行自动波次
            for (Warehouse wh : whList) {
                ParamUtil.setCurrentWarehouseId(wh.getId());
                
                if (Config.isDefaultFalse(Keys.Delivery.wave_enableAuto, Config.ConfigLevel.WAREHOUSE)) {
                    log.info("Start generate wave in warehouse :" + wh.getWarehouseCode());
                    genAutoWave(wh.getId());
                    log.info("End generate wave in warehouse :" + wh.getWarehouseCode());
                }
            }
        } catch (Exception e) {
            log.error("Auto generate wave  failed", e);
        } finally {
            // 每次操作执行后清空当前的warehouseId，防止被定时任务线程执行其他任务所应用
            ParamUtil.setCurrentWarehouseId(null);
        }
        log.info(stopWatch.stop());
    }
    
    private void genAutoWave(Long warehouseId) {
        List<WaveRuleDetail> waveRuleDetails = waveRuleService.getAvailableRules();
        try {
            for (WaveRuleDetail waveRuleDetail : waveRuleDetails) {
                if (waveRuleDetail.isAvailable()) {
                    autoWaveGenService.genWave(waveRuleDetail);
                }
            }
        } catch (Exception e) {
            log.error("Auto generate wave failed", e);
            sendMail(e);
        }
    }
    
    /**
     * 发送自动波次异常订单提醒邮件
     */
    private void sendMail(Exception e) {
        AlarmUtil.sendEmail("自动波次失败", e.getMessage());
    }
}
