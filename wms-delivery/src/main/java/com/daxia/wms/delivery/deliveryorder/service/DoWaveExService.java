package com.daxia.wms.delivery.deliveryorder.service;

import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;

import java.util.List;

public interface DoWaveExService {
	
	/**
	 * 更新订单波次扩展表上的指定商品、库区波次规则为普通
	 * @param ruleId
	 * @return
	 */
	public void updateDoWaveEx2Normal(Long ruleId);
	
	/**
	 * 保存
	 * @param doWaveEx
	 */
	public void save(DoWaveEx doWaveEx);
	
	/**
	 * 更新
	 * @param doWaveEx
	 */
	public void update(DoWaveEx doWaveEx);
	
	/**
	 * 根据订单头id查询订单波次扩展实体
	 * @param doHeaderId
	 * @return
	 */
	public DoWaveEx findByDoHeaderId(Long doHeaderId);
	
	Constants.AutoWaveType findAutoWaveType(List<Long> doIds);
}
