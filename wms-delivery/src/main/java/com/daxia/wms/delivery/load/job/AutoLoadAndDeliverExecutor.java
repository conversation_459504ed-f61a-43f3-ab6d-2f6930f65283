package com.daxia.wms.delivery.load.job;

import java.util.List;

import org.jboss.seam.Component;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.log.Log;


import com.daxia.framework.common.log.StopWatch;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.load.service.AutoDeliverService;
import com.daxia.wms.delivery.load.service.impl.AutoDeliverServiceImpl;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.Constants.LoadMode;

@Name("autoLoadAndDeliverExecutor")
@lombok.extern.slf4j.Slf4j
public class AutoLoadAndDeliverExecutor {



    public void doLoadAndDeliver(List<CartonHeader> cartonList) {
		StopWatch stopWatch = new StopWatch(
				"autoLoadAndDeliverExecutor.doLoadAndDeliver");
		log.debug("start auto loadAndDeliver:{}", cartonList);
		AutoDeliverService autoDeliverService = ((AutoDeliverService) Component
				.getInstance(AutoDeliverServiceImpl.class));
		if (ListUtil.isNotEmpty(cartonList)) {
			for (CartonHeader ch : cartonList) {
				try {
					ParamUtil.setCurrentWarehouseId(ch.getWarehouseId());
					autoDeliverService.autoLoadAndDeliver(ch.getCartonNo(), LoadMode.AUTO.getValue(), DateUtil.getNowTime());
				} catch (Exception e) {
					log.error("error load carton:" + ch.getCartonNo(), e);
				}
			}
		}
		log.info(stopWatch.stop());
    }
}