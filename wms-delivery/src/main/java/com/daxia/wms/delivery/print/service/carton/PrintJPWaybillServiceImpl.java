package com.daxia.wms.delivery.print.service.carton;

import com.daxia.framework.common.util.*;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.WaybillType;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.print.dto.BaseCartonPrintDTO;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.helper.PrintCartonHelper;
import com.daxia.wms.delivery.print.service.PrintWaybillService;
import com.daxia.wms.master.entity.City;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.print.dto.PrintCfg;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.print.utils.PrintHelper;
import com.google.common.collect.ImmutableMap;
import com.jd.open.api.sdk.domain.kdgjapi.WaybillBigShotApi.BigShotDTO;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.List;

@Name("printJPWaybillService")
@lombok.extern.slf4j.Slf4j
public class PrintJPWaybillServiceImpl extends AbstractPrintWaybillService implements PrintWaybillService {

    @In
    private WarehouseService warehouseService;

    @In
    private WarehouseCarrierService warehouseCarrierService;

//    @In
//    private JpWaybillService jpWaybillService;

    @Create
    public void init () {
        this.setWaybillType(WaybillType.JP);
    }

    @Override
    protected void buildDTO(List<PrintReportDto> printReportDtos, DeliveryOrderHeader doHeader, BaseCartonPrintDTO carton,
                            int index, int count) {

        CartonPrintDTO cartonPrintDTO = new CartonPrintDTO();
        cartonPrintDTO.setCartonId(carton.getId());
        cartonPrintDTO.setDocId(doHeader.getId());
        cartonPrintDTO.setDoNo(doHeader.getDoNo());
        cartonPrintDTO.setDocNo(doHeader.getDoNo());
        cartonPrintDTO.setSortGridNo(doHeader.getSortGridNo());
        cartonPrintDTO.setWaveNo(doHeader.getWaveHeader().getWaveNo());
        cartonPrintDTO.setOriginalSoCode(doHeader.getOriginalSoCode());
        // 箱号
        cartonPrintDTO.setCartonNo(carton.getCartonNo());
        // 商家订单号
        cartonPrintDTO.setRefNo1(doHeader.getOriginalSoCode());
        // 运单号
        cartonPrintDTO.setWayBill(carton.getWayBill());
        // 寄件人地址
        Warehouse warehouse = warehouseService.getLocalWarehouse();
        City city = warehouse.getCity();
        if (city != null) {
            cartonPrintDTO.setSendCity(city.getCityCname());
        }
        // 寄件人信息
        // 设置寄件人地址信息
        setSendAddressInfo(doHeader, cartonPrintDTO);

        cartonPrintDTO.setCartonIndex(index);
        if (Constants.DoStatus.ALL_CARTON.getValue().equals(doHeader.getStatus()) || Constants.DoStatus.ALL_CARTON.getValue().equals(doHeader.getPcsStatus())) {
            cartonPrintDTO.setCartonCount(count);
        }
        // 客户电话
        cartonPrintDTO.setClientPhone(PrintCartonHelper.buildTelOrMobile(doHeader));

        // 设置图片路径
        cartonPrintDTO.setBasePrintImgPath(PrintHelper.getBasePrintImgPath());
        // 收货人
        cartonPrintDTO.setClientName(PrintCartonHelper.buildConsigneeName(doHeader));
        // 设置客户栏联系方式
        cartonPrintDTO.setTelOrMobile(PrintCartonHelper.buildTelOrMobile(doHeader));
        // 设置地址信息
        cartonPrintDTO.setClientAddress(PrintCartonHelper.buildAddress(doHeader));
        // 应收金额
        cartonPrintDTO.setReceivable(doHeader.getReceivable());
        //备注
        cartonPrintDTO.setRemark("");
        cartonPrintDTO.setCreateTime(DateUtil.dateToString(doHeader.getCreatedAt(), DateUtil.DATETIME_PATTERN));
        cartonPrintDTO.setPayTime(DateUtil.dateToString(doHeader.getPayTime(), DateUtil.DATETIME_PATTERN));
        //商家编码
        WarehouseCarrier warehouseCarrier = warehouseCarrierService.getCarrierInfoByWarehouseIdAndCarrierIdAndType(ParamUtil.getCurrentWarehouseId(), doHeader.getCarrierId(),Constants.WaybillType.JP.name());
        if (warehouseCarrier != null) {
//            cartonPrintDTO.setCardNo(warehouseCarrier.getExt1());
            //获取大头笔信息
            BigShotDTO bigShotDTO = null;
//          jpWaybillService.getBigShotDTO(warehouseCarrier,carton.getWayBill());
            cartonPrintDTO.setShortAddress(bigShotDTO.getBigShotName());//大头笔名称
            cartonPrintDTO.setSecondSectionCode(bigShotDTO.getSecondSectionCode());//二段码
            cartonPrintDTO.setThirdSectionCode(bigShotDTO.getThirdSectionCode());//三段码
//            cartonPrintDTO.setPackageCenterName(doWaveEx.getPackageCenterName());
//            cartonPrintDTO.setStartAddressName(doWaveEx.getOriginName());
//            cartonPrintDTO.setStartAddressCode(doWaveEx.getOriginCode());
            cartonPrintDTO.setDestAddressName(bigShotDTO.getGatherCenterName());//集包地名称
            cartonPrintDTO.setDestAddressCode(bigShotDTO.getGatherCenterCode());//集包地编码
            // 设置寄件人信息
//            setSendInfo(warehouseCarrier,cartonPrintDTO);
        }
        //保价逻辑
        String jdGuaranteeScript = SystemConfig.getConfigValue("delivery.waybill.jdGuaranteeScript", ParamUtil
                .getCurrentWarehouseId());
        try {
            if (StringUtil.isNotEmpty(jdGuaranteeScript) && Boolean.TRUE.equals(MvelUtil.eval(jdGuaranteeScript,
                    ImmutableMap.of("doHeader", (Object) doHeader)))) {
                cartonPrintDTO.setRemark("【保价" + doHeader.getOrderAmount() + "元】");
            }
        } catch (Exception e) {

        }
        // 是否需要待收货款
        boolean receivable = doHeader.getReceivable() != null && doHeader.getReceivable().compareTo(BigDecimal.ZERO) > 0;
        cartonPrintDTO.setNeedReceivable(receivable);
        if (receivable) {
            // 设置代收金额
            cartonPrintDTO.setServiceCodAmount(doHeader.getReceivable().toString());
            cartonPrintDTO.setProductSalesType("COD");
        }
        
        if (doHeader.getCarrier() != null) {
            cartonPrintDTO.setCarrierCorp(doHeader.getCarrier().getCarrierCorp());
            cartonPrintDTO.setCarrierName(doHeader.getCarrier().getDistSuppCompName());
        }
        cartonPrintDTO.setSkuUnitQty(getUnitQtyInCarton(doHeader,carton.getCartonNo()));
        cartonPrintDTO.setNotes(doHeader.getNotes());
        setProductInfo(doHeader, cartonPrintDTO);
        cartonPrintDTO.setIsPrinted(carton.getIsPrinted());
        printReportDtos.add(cartonPrintDTO);
    }

    private void  setSendInfo(WarehouseCarrier warehouseCarrier,CartonPrintDTO cartonPrintDTO){
        cartonPrintDTO.setSendName(warehouseCarrier.getExt10());
        cartonPrintDTO.setSendAddress(warehouseCarrier.getExt9());
        cartonPrintDTO.setSendPhone(warehouseCarrier.getExt11());
    }

    @Override
    protected PrintData genPrintData(List<PrintReportDto> dtoList, DeliveryOrderHeader doHeader) {
        PrintData printData = new PrintData();
        printData.setDtoList(dtoList);
        printData.setPrintCfg(generateSFPrintCfg());
        return printData;
    }

    private PrintCfg generateSFPrintCfg() {
        PrintCfg config = new PrintCfg("wayBill_jdAlpha", "100", "180");
        this.setPrintCfg(config);
        return config;
    }
}
