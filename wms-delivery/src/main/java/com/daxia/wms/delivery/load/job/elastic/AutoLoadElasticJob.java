package com.daxia.wms.delivery.load.job.elastic;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.load.job.AutoLoadAndDeliverExecutor;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.recheck.service.impl.CartonServiceImpl;
import com.daxia.wms.master.job.WarehouseSimpleJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.util.List;

/**
 * 自动发货的定时任务：<br>
 * 定时对 分拣环自动交接失败（异步自动发货失败）的DO重新发货
 * 支持自动交接类型的和流水交接类型的do。
 */
@Name("autoLoadElasticJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@Slf4j
public class AutoLoadElasticJob extends WarehouseSimpleJob {
    // 一次交接订单的数量
    private static final int DEFAULT_BATCH_LOAD_NUM = 500;

    /**
     * 筛选满足条件的DO进行自动分配
     */
    @Override
    protected void doRun() throws InterruptedException {
        List<CartonHeader> cartonHeaderList = this.findCartonList(ParamUtil.getCurrentWarehouseId());
        if (CollectionUtils.isEmpty(cartonHeaderList)) {
            return;
        }
        AutoLoadAndDeliverExecutor executor = ((AutoLoadAndDeliverExecutor) Component.getInstance(AutoLoadAndDeliverExecutor.class));
        executor.doLoadAndDeliver(cartonHeaderList);
    }

    private List<CartonHeader> findCartonList(Long warehouseId) {
        CartonService cartonService = ((CartonService) Component.getInstance(CartonServiceImpl.class));
        List<CartonHeader> cartonList = cartonService.findCartons4AutoLoad(warehouseId, DEFAULT_BATCH_LOAD_NUM);
        return cartonList;
    }

}
