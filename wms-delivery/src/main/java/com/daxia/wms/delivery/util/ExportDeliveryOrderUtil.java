package com.daxia.wms.delivery.util;

import com.daxia.dubhe.api.internal.util.StrUtils;
import com.daxia.framework.common.service.Dictionary;
import com.daxia.framework.common.util.AppConfig;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.system.util.ExcelUtil;
import com.daxia.wms.delivery.deliveryorder.dto.DoDetailDto;
import com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto;
import com.daxia.wms.delivery.invoice.entity.InvoiceDetail;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.hssf.usermodel.*;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@lombok.extern.slf4j.Slf4j
public class ExportDeliveryOrderUtil {

    private static final String ODO_TYPE = "ODO_TYPE";
    private static final String DO_STATUS = "DO_STATUS";
    private static final String RELEASE_STATUS = "RELEASE_STATUS";

    public static byte[] generateForDo(List<DoHeaderDto> dataList) throws IOException {
        HSSFWorkbook workbook = new HSSFWorkbook();
        boolean isEnglish = BooleanUtils.isTrue(AppConfig.useEnglish());
        HSSFSheet sheet = workbook.createSheet(isEnglish ? "Ship Order Report" : "发运订单");
        HSSFCellStyle style = ExcelUtil.setBorder(workbook);
        // 创建表头
        HSSFRow row = sheet.createRow(0);
        short index = 0;
        if (isEnglish) {
            extractedEnglishForDO(style, row, index);
        } else {
            extractedChineseForDO(style, row, index);
        }
        // 创建数据
        Map<String, String> doTypeDict = isEnglish? Dictionary.getDictionaryEn(ODO_TYPE) : Dictionary.getDictionary(ODO_TYPE);
        Map<String, String> doStatusDict = isEnglish ? Dictionary.getDictionaryEn(DO_STATUS) : Dictionary.getDictionary(DO_STATUS);
        Map<String, String> releaseStatusDict = isEnglish ? Dictionary.getDictionaryEn(RELEASE_STATUS) : Dictionary.getDictionary(RELEASE_STATUS);
        for (int i = 0; i < dataList.size(); i++) {
            DoHeaderDto obj = dataList.get(i);
            row = sheet.createRow(i + 1);
            index = 0;
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getDoNo())),style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getOriginalSoCode())),style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getRefNo1())),style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getTrackingNo())),style);
            // DO类型
            String doType = doTypeDict.get(obj.getDoType().toString());
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(doType == null ? obj.getDoType().toString() : doType)),style);

            String doStatus = doStatusDict.get(obj.getStatus().toString());
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(doStatus == null ? obj.getStatus().toString() : doStatus)),style);

            String releaseStatus = releaseStatusDict.get(obj.getReleaseStatus().toString());
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(releaseStatus == null ? obj.getReleaseStatus().toString() : releaseStatus)),style);

            ExcelUtil.createCell(row, index++, new HSSFRichTextString(DateUtil.dateToString(obj.getPayTime(), DateUtil.DATETIME_PATTERN)), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(DateUtil.dateToString(obj.getDoFinishTime(), DateUtil.DATETIME_PATTERN)), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(obj.getExpectedQty() == null ? "" : obj.getExpectedQty().toString()), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(obj.getShipQty() == null ? "" : obj.getShipQty().toString()), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getDistSuppCompName())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(obj.getInvoiceQty() == null ? "" : obj.getInvoiceQty().toString()), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(DateUtil.dateToString(obj.getDoCreateTime(), DateUtil.DATETIME_PATTERN)), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(DateUtil.dateToString(obj.getCreateTime(), DateUtil.DATETIME_PATTERN)), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(DateUtil.dateToString(obj.getPackEndTime(), DateUtil.DATETIME_PATTERN)), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getWaveNo())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(DateUtil.dateToString(obj.getShipTime(), DateUtil.DATETIME_PATTERN)), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(obj.getReceivable() == null ? "" : obj.getReceivable().toString()), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(obj.getGrossWt() == null ? "" : obj.getGrossWt().toString()), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(obj.getSortGridNo() == null ? "" : obj.getSortGridNo().toString()), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getProvince())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getCity())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getCounty())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getConsigneeName())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getAddress())), style);
            //ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getTelephone())+"/"+StringUtils.defaultString(obj.getMobile())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getHoldReason())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getHoldWho())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(DateUtil.dateToString(obj.getHoldTime(), DateUtil.DATETIME_PATTERN)), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getBuyerRemark())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getSellerRemark())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getNotes())), style);
        }

        int[] colWidth = {150, 120, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 200, 60, 60, 60, 60, 60, 60};
        for (int col = 0; col < colWidth.length; col++) {
            sheet.setColumnWidth((short) col, (short) (37 * colWidth[col]));//37为像素比
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        workbook.write(baos);
        return baos.toByteArray();
    }

    private static void extractedEnglish(HSSFCellStyle style, HSSFRow row, short index) {
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Shipping Order NO"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Relation Order NO"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Platform Order NO"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Tracking Order NO"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Shipping Order Type"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Shipping Order Status"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Freeze/Release"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Payment Time"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Estimated Delivery Time"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Quantity of Order"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Order Shipment Quantity"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Distributor"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Invoice Quantity"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Creation Time"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Wave Number"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Shipping Time"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Product Code"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Product Name"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Product English Name"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Product Specifications"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Product Quantity"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Province/State"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("City"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Area"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Consignee"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Shipping Address"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Reason for Freezing"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Freeze Operator"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Freeze Time"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Buyer's message"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Seller's message"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Remark"), style);
    }

    private static void extractedEnglishForDO(HSSFCellStyle style, HSSFRow row, short index) {
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Shipping Order NO"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Relation Order NO"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Platform Order NO"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Tracking Order NO"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Shipping Order Type"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Shipping Order Status"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Freeze/Release"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Payment Time"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Estimated Delivery Time"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Quantity of Order"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Order Shipment Quantity"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Distributor"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Invoice Quantity"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Creation Time"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Import Time"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Packaging completion time"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Wave Number"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Shipping Time"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Amount receivable"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Weight"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Sorting grid number"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Province/State"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("City"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Area"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Consignee"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Shipping Address"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Reason for Freezing"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Freeze Operator"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Freeze Time"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Buyer's message"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Seller's message"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("Remark"), style);
    }

    public static byte[] generateForDoDetail(List<DoDetailDto> dataList) throws IOException {
        HSSFWorkbook workbook = new HSSFWorkbook();
        boolean isEnglish = BooleanUtils.isTrue(AppConfig.useEnglish());
        HSSFSheet sheet = workbook.createSheet(isEnglish ? "Ship Order Report" : "发运订单");
        HSSFCellStyle style = ExcelUtil.setBorder(workbook);
        // 创建表头
        HSSFRow row = sheet.createRow(0);
        short index = 0;
        if (isEnglish) {
            extractedEnglish(style, row, index);
        } else {
            extractedChinese(style, row, index);
        }

        // 创建数据
        Map<String, String> doTypeDict = isEnglish ? Dictionary.getDictionaryEn(ODO_TYPE) : Dictionary.getDictionary(ODO_TYPE);
        Map<String, String> doStatusDict = isEnglish ? Dictionary.getDictionaryEn(DO_STATUS) : Dictionary.getDictionary(DO_STATUS);
        Map<String, String> releaseStatusDict = isEnglish ? Dictionary.getDictionaryEn(RELEASE_STATUS) : Dictionary.getDictionary(RELEASE_STATUS);
        for (int i = 0; i < dataList.size(); i++) {
            DoDetailDto obj = dataList.get(i);
            row = sheet.createRow(i + 1);
            index = 0;
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getDoNo())),style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getRefNo1())),style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getOriginalSoCode())),style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getTrackingNo())),style);
            String doType = doTypeDict.get(obj.getDoType().toString());
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(doType == null ? obj.getDoType().toString() : doType)),style);

            String doStatus = doStatusDict.get(obj.getStatus().toString());
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(doStatus == null ? obj.getStatus().toString() : doStatus)),style);
            String releaseStatus = releaseStatusDict.get(obj.getReleaseStatus().toString());
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(releaseStatus == null ? obj.getReleaseStatus().toString() : releaseStatus)),style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(DateUtil.dateToString(obj.getPayTime(), DateUtil.DATETIME_PATTERN)), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(DateUtil.dateToString(obj.getDoFinishTime(), DateUtil.DATETIME_PATTERN)), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(obj.getExpectedQty() == null ? "" : obj.getExpectedQty().toString()), style);

            ExcelUtil.createCell(row, index++, new HSSFRichTextString(obj.getShipQty() == null ? "" : obj.getShipQty().toString()), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getDistSuppCompName())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(obj.getInvoiceQty() == null ? "" : obj.getInvoiceQty().toString()), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(DateUtil.dateToString(obj.getDoCreateTime(), DateUtil.DATETIME_PATTERN)), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getWaveNo())), style);

            ExcelUtil.createCell(row, index++, new HSSFRichTextString(DateUtil.dateToString(obj.getShipTime(), DateUtil.DATETIME_PATTERN)), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(obj.getProductCode() == null ? "" : obj.getProductCode()), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(obj.getProductCname() == null ? "" : obj.getProductCname()), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(obj.getProductEname() == null ? "" : obj.getProductEname()), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(obj.getSpecification() == null ? "" : obj.getSpecification()), style);

            ExcelUtil.createCell(row, index++, new HSSFRichTextString(obj.getDetailExpectedQty()== null ? "" : obj.getDetailExpectedQty().toString()), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getProvince())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getCity())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getCounty())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getConsigneeName())), style);

            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getAddress())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getHoldReason())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getHoldWho())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(DateUtil.dateToString(obj.getHoldTime(), DateUtil.DATETIME_PATTERN)), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getBuyerRemark())), style);

            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getSellerRemark())), style);
            ExcelUtil.createCell(row, index++, new HSSFRichTextString(StringUtils.defaultString(obj.getNotes())), style);
        }

        int[] colWidth = {150, 120, 200, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 200, 60, 60, 60, 60, 60, 60};
        for (int col = 0; col < colWidth.length; col++) {
            sheet.setColumnWidth((short) col, (short) (37 * colWidth[col]));//37为像素比
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        workbook.write(baos);
        return baos.toByteArray();
    }

    private static void extractedChinese(HSSFCellStyle style, HSSFRow row, short index) {
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("发货单号"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("关联单号"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("平台单号"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("快递单号"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("发货单类型"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("发运订单状态"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("冻结/释放"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("付款时间"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("预计出库时间"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("订单数量"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("订单发货数量"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("配送商"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("发票数量"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("创建时间"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("波次号"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("发货时间"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("商品编码"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("商品中文名称"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("商品英文名称"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("商品规格"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("商品数量"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("省"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("市"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("区"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("收货方"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("收货地址"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("冻结原因"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("冻结人"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("冻结时间"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("买家留言"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("卖家留言"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("备注"), style);
    }

    private static void extractedChineseForDO(HSSFCellStyle style, HSSFRow row, short index) {
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("发货单号"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("平台单号"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("关联单号"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("快递单号"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("发货单类型"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("发运订单状态"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("冻结/释放"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("付款时间"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("预计出库时间"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("订货数量"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("发货数量"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("配送商"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("发票数量"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("创建时间"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("导入时间"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("打包完成时间"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("波次号"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("发货时间"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("应收金额"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("重量"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("分拣格号"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("省"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("市"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("区"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("收货方"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("收货地址"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("冻结原因"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("冻结人"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("冻结时间"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("买家留言"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("卖家留言"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("备注"), style);
    }

    public static byte[] generateForInvoiceDetail(List<InvoiceHeader> dataList) throws IOException {
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("发票明细");
        HSSFCellStyle style = ExcelUtil.setBorder(workbook);
        // 创建表头
        HSSFRow row = sheet.createRow(0);
        short index = 0;
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("发货单号"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("发票接收人"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("发票抬头"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("纳税人识别号"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("商品名称"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("规格"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("单位"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("数量"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("单价"), style);
        ExcelUtil.createCell(row, index++, new HSSFRichTextString("金额"), style);
        int loop = 1;
        for (int i = 0; i < dataList.size(); i++) {
            InvoiceHeader header = dataList.get(i);
            List<InvoiceDetail> detailList = header.getInvoiceDetails();
            for (int j = 0; j < detailList.size(); j++) {
                InvoiceDetail detail = detailList.get(j);
                row = sheet.createRow(loop++);
                index = 0;
                ExcelUtil.createCell(row, index++, new HSSFRichTextString(StrUtils.object2String(header.getDeliveryOrderHeader().getDoNo(), "")), style);
                ExcelUtil.createCell(row, index++, new HSSFRichTextString(StrUtils.object2String(header.getReceiverName(), "")), style);
                ExcelUtil.createCell(row, index++, new HSSFRichTextString(StrUtils.object2String(header.getInvoiceTitle(), "")), style);
                ExcelUtil.createCell(row, index++, new HSSFRichTextString(StrUtils.object2String(header.getTaxNo(), "")), style);
                ExcelUtil.createCell(row, index++, new HSSFRichTextString(StrUtils.object2String(detail.getSkuDescr(), "")), style);
                ExcelUtil.createCell(row, index++, new HSSFRichTextString(StrUtils.object2String(detail.getSkuType(), "")), style);
                ExcelUtil.createCell(row, index++, new HSSFRichTextString(StrUtils.object2String(detail.getUomDescr(), "")), style);
                ExcelUtil.createCell(row, index++, new HSSFRichTextString(StrUtils.object2String(detail.getQty(), "")), style);
                ExcelUtil.createCell(row, index++, new HSSFRichTextString(StrUtils.object2String(detail.getPrice(), "")), style);
                ExcelUtil.createCell(row, index++, new HSSFRichTextString(StrUtils.object2String(detail.getAmount(), "")), style);
            }
        }

        int[] colWidth = {150, 120, 60, 60, 60, 60, 60, 60, 60, 60};
        for (int col = 0; col < colWidth.length; col++) {
            sheet.setColumnWidth((short) col, (short) (37 * colWidth[col]));//37为像素比
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        workbook.write(baos);
        return baos.toByteArray();
    }
}
