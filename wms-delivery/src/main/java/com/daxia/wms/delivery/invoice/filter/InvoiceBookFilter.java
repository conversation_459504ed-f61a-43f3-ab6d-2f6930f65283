package com.daxia.wms.delivery.invoice.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

/**
 * 发票遗失-发票薄查询过滤器
 */
@lombok.extern.slf4j.Slf4j
public class InvoiceBookFilter extends WhBaseQueryFilter {

	private static final long serialVersionUID = -8515710990026855264L;
	
	private String invoiceCode;      //发票代码
	private String invoiceNoFm;      //发票号码FM
	private String invoiceNoTo;      //发票号码To
	private String invoiceBookStatus;//发票薄状态
	
    @Operation(fieldName = " o.invoiceCode", operationType = OperationType.EQUAL)
    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    @Operation(fieldName = "o.invoiceNoFrom", operationType = OperationType.NOT_GREAT_THAN)
    public String getInvoiceNoFm() {
        return invoiceNoFm;
    }

    public void setInvoiceNoFm(String invoiceNoFm) {
        this.invoiceNoFm = invoiceNoFm;
    }

    @Operation(fieldName = "o.invoiceNoTo", operationType = OperationType.NOT_LESS_THAN)
    public String getInvoiceNoTo() {
        return invoiceNoTo;
    }

    public void setInvoiceNoTo(String invoiceNoTo) {
        this.invoiceNoTo = invoiceNoTo;
    }

    @Operation(fieldName = "o.status", operationType = OperationType.EQUAL)
    public String getInvoiceBookStatus() {
        return invoiceBookStatus;
    }

    public void setInvoiceBookStatus(String invoiceBookStatus) {
        this.invoiceBookStatus = invoiceBookStatus;
    }
}
