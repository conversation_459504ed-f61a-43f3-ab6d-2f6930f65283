package com.daxia.wms.delivery.transport.service.impl;

import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.transport.dao.DoTransportReportDAO;
import com.daxia.wms.delivery.transport.entity.DoTransportReport;
import com.daxia.wms.delivery.transport.filter.DoTransportReportFilter;
import com.daxia.wms.delivery.transport.service.DoTransportReportService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.jboss.seam.log.Log;


@Name("doTransportReportService")
@lombok.extern.slf4j.Slf4j
public class DoTransportReportServiceImpl implements DoTransportReportService {


    @In
    private SequenceGeneratorService sequenceGeneratorService;
    @In(create = true)
    private DoTransportReportDAO doTransportReportDAO;

    @Override
    public DataPage<DoTransportReport> queryTransportRecord(DoTransportReportFilter doTransportReportFilter, int startIndex, int pageSize) {
        return doTransportReportDAO.findRangeByFilter(doTransportReportFilter, startIndex, pageSize);
    }

    @Override
    public DoTransportReport getById(Long transportRecordId) {
        return null;
    }

    @Override
    public DoTransportReport getDoTransportReportByLoadId(Long loadId) {
        return doTransportReportDAO.getDoTransportReportByLoadId(loadId);
    }

    @Override
    @Transactional
    public void saveDoTransportReport(DoTransportReport doTransportReport) {
        if (doTransportReport.getId() == null) {
            doTransportReport.setCreatedAt(DateUtil.getNowTime());
            doTransportReport.setCreatedBy(ParamUtil.getCurrentLoginName());
            String transportNo = sequenceGeneratorService.generateSequenceNo(Constants.SequenceName.TRSNAPORT_NO.getValue(), ParamUtil.getCurrentWarehouseId());
            doTransportReport.setTransportNo(transportNo);
        }
        doTransportReportDAO.saveOrUpdate(doTransportReport);

    }

    @Override
    @Transactional
    public void deleteById(Long id) {
        doTransportReportDAO.deleteById(id);
    }
}