/**
 * 
 */
package com.daxia.wms.delivery.deliveryorder.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.OrderLogConstants;
import com.daxia.wms.delivery.deliveryorder.entity.OrderLog;
import com.google.common.collect.ImmutableMap;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.jboss.seam.annotations.Name;

import java.util.List;

@Name("com.daxia.wms.delivery.orderLogDAO")
@lombok.extern.slf4j.Slf4j
public class OrderLogDAO extends HibernateBaseDAO<OrderLog, Long> {
	private static final long serialVersionUID = -8848420408782058842L;
    
    public void save(Long waveId, String operateType, String dispalyString) {
        String sql = "INSERT INTO doc_order_operate_log (DOC_ID, DOC_NO, OPERATE_TYPE, OPERATE_LOG, warehouse_id, OPERATE_TIME, OPERATE_BY) " +
                "SELECT id, do_no, :operateType, :dispalyString, :warehouseId, now(), :operateBy FROM doc_do_header WHERE " +
                "wave_id = :waveId AND status = :doStatus AND is_deleted = 0 AND warehouse_id = :warehouseId";
    
        String doStatus = "";
        if (OrderLogConstants.OrderLogType.PACK_COMPETE.getValue().equals(operateType) || OrderLogConstants.OrderLogType.SCAN_MATERIAL.getValue().equals(operateType)) {
            sql += " AND release_status = :releaseStatus";
            doStatus = Constants.DoStatus.ALLSORTED.getValue();
        } else if (OrderLogConstants.OrderLogType.SORT_WAVE.getValue().equals(operateType)) {
            doStatus = Constants.DoStatus.ALLPICKED.getValue();
        }
    
        Query query = this.createSQLQuery(sql, ImmutableMap.of("operateType", (Object) operateType, "dispalyString", dispalyString, "warehouseId", ParamUtil.getCurrentWarehouseId(), "operateBy", ParamUtil.getOperatorSafe(),
                "waveId", waveId)).setParameter("doStatus", doStatus).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        if (OrderLogConstants.OrderLogType.PACK_COMPETE.getValue().equals(operateType) || OrderLogConstants.OrderLogType.SCAN_MATERIAL.getValue().equals(operateType)) {
            query.setParameter("releaseStatus", Constants.ReleaseStatus.RELEASE.getValue());
        }
        query.executeUpdate();
    }
    public  void batchSaveLog(List<Long> doList, String operateType, String orderLog){
        String sql = "INSERT INTO doc_order_operate_log (DOC_ID, DOC_NO, OPERATE_TYPE, OPERATE_LOG, warehouse_id, OPERATE_TIME, OPERATE_BY," +
                " CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,VERSION) " +
                "SELECT dh.id, dh.do_no, :operateType, :orderLog, :warehouseId, now(), :operateBy,:operateBy,now(),:operateBy,now(),0  " +
                "FROM   doc_do_header dh   " +
                " WHERE dh.id in (:doList) and dh.is_deleted =0 and dh.warehouse_id =:warehouseId ";
        SQLQuery sqlQuery = this.createSQLQuery(sql);
        sqlQuery.setParameter("operateType",operateType);
        sqlQuery.setParameter("orderLog",orderLog);
        sqlQuery.setParameterList("doList",doList);
        sqlQuery.setParameter("warehouseId",ParamUtil.getCurrentWarehouseId());
        sqlQuery.setParameter("operateBy",ParamUtil.getCurrentLoginName());
        sqlQuery.executeUpdate();
    }

    public  void saveLog4PickList(List<Long> pickTaskIdList, String operateType, String orderLog,String operateBy){
        String sql = "INSERT INTO doc_order_operate_log (DOC_ID, DOC_NO, OPERATE_TYPE, OPERATE_LOG, warehouse_id, OPERATE_TIME, OPERATE_BY) " +
                "SELECT distinct dd.id, dd.do_no, :operateType, :orderLog, :warehouseId, now(), :operateBy " +
                "FROM tsk_pick t inner join doc_do_header dd on t.doc_id = dd.id and t.warehouse_id = dd.warehouse_id " +
                " WHERE t.id in (:pickTaskIdList) and dd.is_deleted =0 and dd.status =:status and dd.warehouse_id =:warehouseId ";
        SQLQuery sqlQuery = this.createSQLQuery(sql);
        sqlQuery.setParameter("operateType",operateType);
        sqlQuery.setParameter("orderLog",orderLog);
        sqlQuery.setParameterList("pickTaskIdList",pickTaskIdList);
        sqlQuery.setParameter("warehouseId",ParamUtil.getCurrentWarehouseId());
        sqlQuery.setParameter("operateBy",operateBy);
        sqlQuery.setParameter("status",Constants.DoStatus.ALLPICKED.getValue());
        sqlQuery.executeUpdate();
    }
}