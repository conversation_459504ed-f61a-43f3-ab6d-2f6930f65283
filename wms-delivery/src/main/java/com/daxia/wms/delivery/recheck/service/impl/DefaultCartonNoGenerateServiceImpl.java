package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.delivery.recheck.dao.CartonHeaderDAO;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants.SequenceName;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;

@Name("defaultCartonNoGenerateService")
@lombok.extern.slf4j.Slf4j
public class DefaultCartonNoGenerateServiceImpl implements CartonNoGenerateService {
	@In
	private SequenceGeneratorService sequenceGeneratorService;

    @In
    private CartonHeaderDAO cartonHeaderDAO;

    @Override
    public void generatorCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        String orderNo = doHeader.getDoNo();
        if (StringUtil.isEmpty(orderNo)) {
            throw new DeliveryException(DeliveryException.DO_NO_IS_NULL);
        }

        String cartonNo;
        if (SystemConfig.configIsOpen("delivery.carton.genNoFmDb", ParamUtil.getCurrentWarehouseId())) {
            cartonNo = orderNo + cartonHeaderDAO.getCartonSeqByDo(doHeader.getId());
        } else {
            cartonNo = orderNo + sequenceGeneratorService.generateSequenceNo(SequenceName.CARTONNO.getValue(), ParamUtil.getCurrentWarehouseId());
        }

        cartonHeader.setCartonNo(cartonNo);
    }
}
