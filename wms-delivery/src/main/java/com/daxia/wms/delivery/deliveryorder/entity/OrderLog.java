package com.daxia.wms.delivery.deliveryorder.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.Date;

/**
 * 订单日志
 */
@Entity
@Table(name = "doc_order_operate_log")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@BatchSize(size = 30)
@lombok.extern.slf4j.Slf4j
public class OrderLog extends WhBaseEntity {

    private static final long serialVersionUID = -1749169751191265722L;
    private Long id;
    /**
     * 订单ID
     */
    private Long docId;
    /**
     * 订单号
     */
    private String docNo;
    /**
     * 操作类型
     */
    private String operateType;
    /**
     * 操作日志
     */
    private String operateLog;
    /**
     * 操作时间
     */
    private Date operateTime;
    /**
     * 操作人
     */
    private String operateBy;

    /**
     * 单据类型：3订单 0波次
     */
    private String ext1;
    /**
     * 散/整 1散2整 pktType
     */
    private String ext2;
    /**
     * 集货位
     */
    private String ext3;
    /**
     * 关联单号 拣货单号
     */
    private String ext4;
    /**
     * 容器号
     */
    private String ext5;

    /**
     * DoHeader对象
     */
    private DeliveryOrderHeader doHeader;

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "autoIdGenerator")
    @GenericGenerator(name = "autoIdGenerator", strategy = "com.daxia.framework.common.dao.AutoIdentityGenerator")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "DOC_ID")
    public Long getDocId() {
        return docId;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }

    @Column(name = "DOC_NO")
    public String getDocNo() {
        return docNo;
    }

    public void setDocNo(String docNo) {
        this.docNo = docNo;
    }

    @Column(name = "OPERATE_TYPE")
    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType;
    }

    @Column(name = "OPERATE_LOG")
    public String getOperateLog() {
        return operateLog;
    }

    public void setOperateLog(String operateLog) {
        this.operateLog = operateLog;
    }

    @Column(name = "OPERATE_TIME")
    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    @Column(name = "OPERATE_BY")
    public String getOperateBy() {
        return operateBy;
    }

    public void setOperateBy(String operateBy) {
        this.operateBy = operateBy;
    }

    @Column(name = "EXT1")
    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    @Column(name = "EXT2")
    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    @Column(name = "EXT3")
    public String getExt3() {
        return ext3;
    }


    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    @Column(name = "EXT4")
    public String getExt4() {
        return ext4;
    }

    public void setExt4(String ext4) {
        this.ext4 = ext4;
    }

    @Column(name = "EXT5")
    public String getExt5() {
        return ext5;
    }

    public void setExt5(String ext5) {
        this.ext5 = ext5;
    }


    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DOC_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    public DeliveryOrderHeader getDoHeader() {
        return doHeader;
    }

    public void setDoHeader(DeliveryOrderHeader doHeader) {
        this.doHeader = doHeader;
    }
}
