/**
 * 
 */
package com.daxia.wms.delivery.recheck.entity;


import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;

/**
 * 临时箱表，用于提前下电子面单
 */
@Entity
@Table(name = "doc_temp_carton")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = " UPDATE doc_temp_carton SET IS_DELETED = 1 WHERE ID = ? and version = ? ")
@lombok.extern.slf4j.Slf4j
public class TempCarton extends WhBaseEntity
{
    private static final long serialVersionUID = 4766279729157852973L;
    private Long id;
    //箱号
    private String cartonNo;
    //业务单号
    private String trackingNo;
    //运单号
    private String wayBill;
    //打印信息
    private String printData;
    //订单表
    private Long doHeaderId;

    private DeliveryOrderHeader doHeader;
    //配送商ID
    private Long carrierId;
    //成功标识
    private Integer successFlag;
    //是否需要同步到平台
    private Integer isNeedSync;
    //错误消息
    private String errorMsg;
    //调用次数
    private Integer callCount;
    //打印标识
    private Integer isPrinted;


    public TempCarton() {
    }

    public TempCarton(Long id, String cartonNo, String trackingNo, String wayBill, String printData, Long doHeaderId, Long carrierId, Integer successFlag, String errorMsg, Integer callCount, Integer isPrinted) {
        this.id = id;
        this.cartonNo = cartonNo;
        this.trackingNo = trackingNo;
        this.wayBill = wayBill;
        this.printData = printData;
        this.doHeaderId = doHeaderId;
        this.carrierId = carrierId;
        this.successFlag = successFlag;
        this.errorMsg = errorMsg;
        this.callCount = callCount;
        this.isPrinted = isPrinted;
    }

    /**
     * 获取null
     * @return id
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.AUTO)  
    public Long getId() {
        return id;
    }
    /**
     * 设置null
     * @param id
     */
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "CARTON_NO")
    public String getCartonNo() {
        return cartonNo;
    }

    public void setCartonNo(String cartonNo) {
        this.cartonNo = cartonNo;
    }

    @Column(name = "TRACKING_NO")
    public String getTrackingNo() {
        return trackingNo;
    }

    public void setTrackingNo(String trackingNo) {
        this.trackingNo = trackingNo;
    }

    @Column(name = "WAY_BILL")
    public String getWayBill() {
        return wayBill;
    }

    public void setWayBill(String wayBill) {
        this.wayBill = wayBill;
    }

    @Column(name = "PRINT_DATA")
    public String getPrintData() {
        return printData;
    }

    public void setPrintData(String printData) {
        this.printData = printData;
    }

    @Column(name = "DO_HEADER_ID")
    public Long getDoHeaderId() {
        return doHeaderId;
    }

    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }

    @Column(name = "SUCCESS_FLAG")
    public Integer getSuccessFlag() {
        return successFlag;
    }

    public void setSuccessFlag(Integer successFlag) {
        this.successFlag = successFlag;
    }

    @Column(name = "ERROR_MSG")
    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    @Column(name = "CALL_COUNT")
    public Integer getCallCount() {
        return callCount;
    }

    public void setCallCount(Integer callCount) {
        this.callCount = callCount;
    }

    @Column(name = "CARRIER_ID")
    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    @Column(name = "IS_PRINTED")
    public Integer getIsPrinted() {
        return isPrinted;
    }

    public void setIsPrinted(Integer isPrinted) {
        this.isPrinted = isPrinted;
    }

    @OneToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "DO_HEADER_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    public DeliveryOrderHeader getDoHeader() {
        return doHeader;
    }

    public void setDoHeader(DeliveryOrderHeader doHeader) {
        this.doHeader = doHeader;
    }

    @Column(name = "IS_NEED_SYNC")
    public Integer getIsNeedSync() {
        return isNeedSync;
    }

    public void setIsNeedSync(Integer isNeedSync) {
        this.isNeedSync = isNeedSync;
    }
}
