package com.daxia.wms.delivery.load.entity;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.master.entity.Carrier;

/**
 * 交接单头历史信息
 */
@Entity
@Table(name = "doc_load_header_his")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = "IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_load_header_his set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class LoadHeaderHis extends WhBaseEntity {
    
    private static final long serialVersionUID = -2998935551178979665L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 交接单号
     */
    private String loadNo;
    /**
     * 交接单的类型
     */
    private String loadType;
    /**
     * 交接单状态
     */
    private String status;
    /**
     * 配送商
     */
    private Long carrierId;
    /**
     * 供应商
     */
    private Long supplierId;
    /**
     * 调拨目标仓库ID
     */
    private Long tranInWhID;
    /**
     * 车号
     */
    private String vechileNo;
    /**
     * 司机
     */
    private String driverName;
    /**
     * 总单数
     */
    private Long docQty;
    /**
     * 总箱数
     */
    private Long cartonQty;
    /**
     * 已扫描订单的总箱数
     */
    private Long totalCartonQty;
    /**
     * 总毛重
     */
    private BigDecimal totalGrossWeight;
    /**
     * 总体积
     */
    private BigDecimal totalVolume;
    /**
     * 总净重
     */
    private BigDecimal totalNetWeight;
    
    /**
     * 锁定时间
     */
    private Timestamp lockTime;
    
    /**
     * 出库时间
     */
    private Timestamp deliveryTime;
    
    /**
     * 备注
     */
    private String notes;

    /**
     * 配送商
     */
    private Carrier carrier;

    private List<LoadDetailHis> loadDetails;
    
    /**
     * 是否自动交接 ： 0：自动，1：人工，2：流水
     */
    private Integer isAuto;
    
    /**
     * 提货人
     */
    private String contact;
    
    /**
     * 提货人 证件号
     */
    private String contactId;
    
    /**
     * 提货人手机号
     */
    private String contactMobile;

    /**
     * Getter method for property <tt>id</tt>.
     * 
     * @return property value of id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)  
    public Long getId() {
        return id;
    }

    /**
     * Setter method for property <tt>id</tt>.
     * 
     * @param id value to be assigned to property id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * Getter method for property <tt>loadNo</tt>.
     * 
     * @return property value of loadNo
     */
    @Column(name = "LOAD_NO")
    public String getLoadNo() {
        return loadNo;
    }

    /**
     * Setter method for property <tt>loadNo</tt>.
     * 
     * @param loadNo value to be assigned to property loadNo
     */
    public void setLoadNo(String loadNo) {
        this.loadNo = loadNo;
    }

    /**
     * Getter method for property <tt>status</tt>.
     * 
     * @return property value of status
     */
    @Column(name = "STATUS")
    public String getStatus() {
        return status;
    }

    /**
     * Setter method for property <tt>status</tt>.
     * 
     * @param status value to be assigned to property status
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * Getter method for property <tt>carrierId</tt>.
     * 
     * @return property value of carrierId
     */
    @Column(name = "CARRIER_ID")
    public Long getCarrierId() {
        return carrierId;
    }

    /**
     * Setter method for property <tt>carrierId</tt>.
     * 
     * @param carrierId value to be assigned to property carrierId
     */
    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    /**
     * Getter method for property <tt>vechileNo</tt>.
     * 
     * @return property value of vechileNo
     */
    @Column(name = "VEHICLE_NO")
    public String getVechileNo() {
        return vechileNo;
    }

    /**
     * Setter method for property <tt>vechileNo</tt>.
     * 
     * @param vechileNo value to be assigned to property vechileNo
     */
    public void setVechileNo(String vechileNo) {
        this.vechileNo = vechileNo;
    }

    /**
     * Getter method for property <tt>driverName</tt>.
     * 
     * @return property value of driverName
     */
    @Column(name = "DRIVER_NAME")
    public String getDriverName() {
        return driverName;
    }

    /**
     * Setter method for property <tt>driverName</tt>.
     * 
     * @param driverName value to be assigned to property driverName
     */
    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    /**
     * Getter method for property <tt>docQty</tt>.
     * 
     * @return property value of docQty
     */
    @Column(name = "DOC_QTY")
    public Long getDocQty() {
        return docQty;
    }

    /**
     * Setter method for property <tt>docQty</tt>.
     * 
     * @param docQty value to be assigned to property docQty
     */
    public void setDocQty(Long docQty) {
        this.docQty = docQty;
    }

    /**
     * Getter method for property <tt>cartonQty</tt>.
     * 
     * @return property value of cartonQty
     */
    @Column(name = "CARTON_QTY")
    public Long getCartonQty() {
        return cartonQty;
    }

    /**
     * Setter method for property <tt>cartonQty</tt>.
     * 
     * @param cartonQty value to be assigned to property cartonQty
     */
    public void setCartonQty(Long cartonQty) {
        this.cartonQty = cartonQty;
    }

    /**
     * Getter method for property <tt>totalCartonQty</tt>.
     * 
     * @return property value of totalCartonQty
     */
    @Column(name = "TOTAL_CARTON_QTY")
    public Long getTotalCartonQty() {
        return totalCartonQty;
    }

    /**
     * Setter method for property <tt>totalCartonQty</tt>.
     * 
     * @param totalCartonQty value to be assigned to property totalCartonQty
     */
    public void setTotalCartonQty(Long totalCartonQty) {
        this.totalCartonQty = totalCartonQty;
    }

    /**
     * Getter method for property <tt>totalGrossWeight</tt>.
     * 
     * @return property value of totalGrossWeight
     */
    @Column(name = "TOTAL_GROSSWEIGHT")
    public BigDecimal getTotalGrossWeight() {
        return totalGrossWeight;
    }

    /**
     * Setter method for property <tt>totalGrossWeight</tt>.
     * 
     * @param totalGrossWeight value to be assigned to property totalGrossWeight
     */
    public void setTotalGrossWeight(BigDecimal totalGrossWeight) {
        this.totalGrossWeight = totalGrossWeight;
    }

    /**
     * Getter method for property <tt>totalVolume</tt>.
     * 
     * @return property value of totalVolume
     */
    @Column(name = "TOTAL_VOLUME")
    public BigDecimal getTotalVolume() {
        return totalVolume;
    }

    /**
     * Setter method for property <tt>totalVolume</tt>.
     * 
     * @param totalVolume value to be assigned to property totalVolume
     */
    public void setTotalVolume(BigDecimal totalVolume) {
        this.totalVolume = totalVolume;
    }

    /**
     * Getter method for property <tt>totalNetWeight</tt>.
     * 
     * @return property value of totalNetWeight
     */
    @Column(name = "TOTAL_NETWEIGHT")
    public BigDecimal getTotalNetWeight() {
        return totalNetWeight;
    }

    /**
     * Setter method for property <tt>totalNetWeight</tt>.
     * 
     * @param totalNetWeight value to be assigned to property totalNetWeight
     */
    public void setTotalNetWeight(BigDecimal totalNetWeight) {
        this.totalNetWeight = totalNetWeight;
    }
    
    @Column(name = "LOAD_FROM_TIME")
    public Timestamp getLockTime() {
        return lockTime;
    }

    public void setLockTime(Timestamp lockTime) {
        this.lockTime = lockTime;
    }

    @Column(name = "LOAD_END_TIME")
    public Timestamp getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Timestamp deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    /**
     * Getter method for property <tt>notes</tt>.
     * 
     * @return property value of notes
     */
    @Column(name = "NOTES")
    public String getNotes() {
        return notes;
    }

    /**
     * Setter method for property <tt>notes</tt>.
     * 
     * @param notes value to be assigned to property notes
     */
    public void setNotes(String notes) {
        this.notes = notes;
    }

    public void setLoadDetails(List<LoadDetailHis> loadDetails) {
        this.loadDetails = loadDetails;
    }

    @OneToMany(fetch = FetchType.LAZY,mappedBy = "loadHeader")
    @Where(clause = "IS_DELETED = 0 ")
    public List<LoadDetailHis> getLoadDetails() {
        return loadDetails;
    }

    public void setCarrier(Carrier carrier) {
        this.carrier = carrier;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CARRIER_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    public Carrier getCarrier() {
        return carrier;
    }

    public void setLoadType(String loadType) {
        this.loadType = loadType;
    }

    @Column(name = "LOAD_TYPE")
    public String getLoadType() {
        return loadType;
    }

    @Column(name = "SUPPLIER_ID")
    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }
    @Column(name = "TRAN_IN_WH_ID")
    public Long getTranInWhID() {
        return tranInWhID;
    }

    public void setTranInWhID(Long tranInWhID) {
        this.tranInWhID = tranInWhID;
    }
    
	@Column(name = "IS_AUTO")
    public Integer getIsAuto() {
        return isAuto;
    }
    
    public void setIsAuto(Integer isAuto) {
        this.isAuto = isAuto;
    }

    @Column(name = "CONTACT")
    public String getContact() {
        return contact;
    }

    
    public void setContact(String contact) {
        this.contact = contact;
    }

    @Column(name = "CONTACT_ID")
    public String getContactId() {
        return contactId;
    }

    
    public void setContactId(String contactId) {
        this.contactId = contactId;
    }

    @Column(name = "CONTACT_MOBILE")
    public String getContactMobile() {
        return contactMobile;
    }

    
    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }
}
