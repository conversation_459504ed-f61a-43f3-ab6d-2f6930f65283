package com.daxia.wms.delivery.print.service;

import com.daxia.wms.delivery.print.dto.PickLabelPrintDTO;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.master.service.PrintService;

import java.util.List;

/**
 *  拣货标签打印接口
 */
public interface PrintPickLabelService extends PrintService<PickLabelPrintDTO>{
    
    /**
     * 打印波次下的所有拣货标签
     * @param waveId
     * @return
     */
    public String printPickLabel(List<Long> waveId);
    /**
     * 构造波次下的所有拣货标签打印数据dto
     * @param waveId
     * @return
     */
    public List<PickLabelPrintDTO> buildPickLabelPrintDTO(Long waveId);
}
