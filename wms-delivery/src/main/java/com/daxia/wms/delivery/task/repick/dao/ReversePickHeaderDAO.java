package com.daxia.wms.delivery.task.repick.dao;

import java.util.ArrayList;
import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.task.repick.entity.ReversePickHeader;
import com.daxia.wms.Constants.TaskStatus;

/**
 * 反拣单头信息DAO
 */
@Name("com.daxia.wms.delivery.reversePickHeaderDAO")
@lombok.extern.slf4j.Slf4j
public class ReversePickHeaderDAO extends HibernateBaseDAO<ReversePickHeader,Long> {

	private static final long serialVersionUID = -4031238798951030537L;

	/**
	 * 获取订单下面需要完成的反拣任务
	 */
    public Long getNeedDoingTaskCount(Long docId) {
        List<String> taskStatuses = new ArrayList<String>();
        taskStatuses.add(TaskStatus.INITIALIZED.getValue());
        taskStatuses.add(TaskStatus.RELEASED.getValue());
        taskStatuses.add(TaskStatus.SUSPENDED.getValue());

        String hql = " select count(o.id) from ReversePickTask o where o.taskStatus in (:taskStatuses) and o.docId =:docId " +
        		" and o.warehouseId = :warehouseId ";
        Query query = this.createQuery(hql);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameterList("taskStatuses", taskStatuses);
        query.setLong("docId", docId);
        query.setMaxResults(1);
        return (Long) query.uniqueResult();
    }
}
