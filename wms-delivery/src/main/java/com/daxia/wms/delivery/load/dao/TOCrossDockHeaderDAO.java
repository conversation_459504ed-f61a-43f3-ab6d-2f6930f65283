package com.daxia.wms.delivery.load.dao;

import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.load.entity.TOCrossDockHeader;

@Name("com.daxia.wms.delivery.toCrossDockHeaderDAO")
@lombok.extern.slf4j.Slf4j
public class TOCrossDockHeaderDAO extends HibernateBaseDAO<TOCrossDockHeader, Long> {
    
    private static final long serialVersionUID = 2549916725961920032L;
    
    public TOCrossDockHeader findByOrigId(Long origId) {
        Criteria cri = this.getSession().createCriteria(TOCrossDockHeader.class);
        cri.add(Restrictions.eq("origId", origId));
        cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
        cri.setMaxResults(1);
        return (TOCrossDockHeader)cri.uniqueResult();
    }
    public TOCrossDockHeader findByCdNo(String doNo) {
        Criteria cri = this.getSession().createCriteria(TOCrossDockHeader.class);
        cri.add(Restrictions.eq("doNo", doNo));
        cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
        cri.setMaxResults(1);
        return (TOCrossDockHeader)cri.uniqueResult();
    }
}
