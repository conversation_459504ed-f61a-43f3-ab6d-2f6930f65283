package com.daxia.wms.delivery.wave.service.impl;

import com.daxia.wms.delivery.wave.service.WaveRecommend;
import com.daxia.wms.wave.vo.DoRecommendDTO;
import com.daxia.wms.wave.vo.DoRecommendLimit;
import com.daxia.wms.wave.vo.DoRecommendResult;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.*;

@Name("com.daxia.wms.delivery.waveRecommend")
@lombok.extern.slf4j.Slf4j
public class WaveRecommendImpl implements WaveRecommend {

    MathContext mc = new MathContext(5, RoundingMode.HALF_DOWN);
    
    @Override
    public DoRecommendResult recommend(List<DoRecommendDTO> dtos, DoRecommendLimit limit) {
        if (dtos.isEmpty()) {
            return new DoRecommendResult();
        }
        
        WaveCollection wave = new WaveCollection(limit);
    
        DoRecommendDTO seedDTO = findSeedDTO(dtos);
        dtos.remove(seedDTO);
        
        if (seedDTO != null) {
            if (wave.addDo(seedDTO)) {
                return buildResult(wave);
            }
        }
        
        //所有订单的通道信息为空，直接返回前recommendNum个订单
        if (wave.getAisles().isEmpty()) {
            for (DoRecommendDTO dto : dtos) {
                if (wave.addDo(dto)) {
                    break;
                }
            }
        } else {
            recommend(dtos, wave, null);
        }
        
        return buildResult(wave);
    }

    private void recommend(List<DoRecommendDTO> dtos, WaveCollection wave, Set<String> additionAisles) {
        if (dtos.isEmpty()) {
            return;
        }

        Set<String> standardAisles = new HashSet<String>();
        standardAisles.addAll(wave.getAisles());
        // 找出通道重合率最高的订单
        List<DoRecommendDTO> tops = topCoselectionRate(standardAisles, dtos, additionAisles, wave.getAdditionNum());
        additionAisles = new HashSet<String>();
        for (DoRecommendDTO top : tops) {
            if (wave.addDo(top)) {
                return;
            }
            dtos.remove(top);
            additionAisles.addAll(top.getAisles());
        }
        additionAisles.removeAll(standardAisles);

        if (!dtos.isEmpty()) {
            recommend(dtos, wave, additionAisles);
        }
    }
    
    private List<DoRecommendDTO> topCoselectionRate(Set<String> standardAisles, List<DoRecommendDTO> dtos, Set<String> additionAisles, int additionNum) {
        additionNum = additionNum < 1 ? 1 : additionNum;
        List<DoRecommendDTO> tops = new ArrayList<DoRecommendDTO>(additionNum);

        if (dtos.isEmpty()) {
            return tops;
        }
    
        //订单池中的订单小于等于每次添加订单数
        if (additionNum >= dtos.size()) {
            tops.addAll(dtos);
            dtos.clear();

            return tops;
        } else {
            for (DoRecommendDTO dto : dtos) {
                BigDecimal coselectionRate = coselectionRate(standardAisles, dto, additionAisles);
                dto.setCoselectionRate(coselectionRate);

                if (tops.isEmpty() || tops.size() < additionNum) {
                    tops.add(dto);
                } else {
                    if (tops.get(tops.size() - 1).getCoselectionRate().compareTo(coselectionRate) < 0) {
                        tops.remove(tops.size() - 1);
                        tops.add(dto);
                    }
                }

                if (tops.size() > 1) {
                    //保证列表按通道重合率降序
                    Collections.sort(tops, new Comparator<DoRecommendDTO>() {
                        @Override
                        public int compare(DoRecommendDTO o1, DoRecommendDTO o2) {
                            return 0 - o1.getCoselectionRate().compareTo(o2.getCoselectionRate());
                        }
                    });
                }
    
                //tops达到additionNum，且通道重合率都是100%
                if (tops.size() == additionNum && tops.get(additionNum - 1).getCoselectionRate().equals(BigDecimal.ONE)) {
                    break;
                }
            }
            return tops;
        }
    }

    private BigDecimal coselectionRate(Set<String> standardAisles, DoRecommendDTO dto, Set<String> additionAisles) {
        Set<String> aisles = dto.getAisles();
        if (aisles.isEmpty()) {
            return BigDecimal.ZERO;
        }

        if (standardAisles.isEmpty()) {
            return BigDecimal.ONE;
        }

        BigDecimal coselectionRate = BigDecimal.ZERO;
        if (dto.getCoselectionRate() != null && additionAisles != null) {
            if (additionAisles.isEmpty()) {
                coselectionRate = dto.getCoselectionRate();
            } else {
                coselectionRate = coselectionRate(additionAisles, aisles).add(dto.getCoselectionRate());
            }
        } else {
            coselectionRate = coselectionRate(standardAisles, aisles);
        }
        return coselectionRate;
    }

    private BigDecimal coselectionRate(Set<String> standardAisles, Set<String> aisles) {
        int i = 0;
        for (String additionAisle : standardAisles) {
            for (String aisle : aisles) {
                if (aisle.equals(additionAisle)) {
                    i++;
                }
            }
        }
        return BigDecimal.valueOf(i).divide(BigDecimal.valueOf(aisles.size()), mc);
    }

    private DoRecommendResult buildResult(WaveCollection wave) {
        if (wave.getDoIds().isEmpty()) {
            return new DoRecommendResult();
        }
        
        DoRecommendResult result = new DoRecommendResult();
        result.setOver(wave.isOver());
        result.setDoIds(wave.getDoIds());
        result.setUnitNum(wave.getTotalUnit());
        result.setVolume(wave.getTotalVolume());
        result.setWeight(wave.getTotalWeight());
        result.setAisles(wave.getAisles());
        return result;
    }

    private DoRecommendDTO findSeedDTO(List<DoRecommendDTO> dtos) {
        DoRecommendDTO seedDTO = null;
        for (int i = 0; i < dtos.size(); i++) {
            DoRecommendDTO dto = dtos.get(i);
            if (seedDTO == null || seedDTO.getAisleSize() < dto.getAisleSize()) {
                seedDTO = dto;
            }
        }
        return seedDTO;
    }
    
    
    @lombok.extern.slf4j.Slf4j
    public static class WaveCollection {
        // 推荐数量
        private int recommendNum;
        
        //一个波次最大unit数量
        private double maxUnitNum;
        
        //一个波次最大重量
        private double maxWeight;
        
        //一个波次最大体积
        private double maxVolume;
        
        //每次添加订单数
        private int additionNum;
        
        private BigDecimal totalUnit = BigDecimal.ZERO;
        private BigDecimal totalVolume = BigDecimal.ZERO;
        private BigDecimal totalWeight = BigDecimal.ZERO;
        
        private List<Long> doIds = new ArrayList<Long>();
        
        private Set<String> aisles = new HashSet<String>();
        
        public WaveCollection(DoRecommendLimit limit) {
            this.recommendNum = limit.getRecommendNum();
            this.maxUnitNum = limit.getMaxUnitNum();
            this.maxWeight = limit.getMaxWeight();
            this.maxVolume = limit.getMaxVolume();
            this.additionNum = limit.getAdditionNum();
        }
        
        public boolean addDo(DoRecommendDTO dto) {
            totalUnit = totalUnit.add(dto.getExpectedQty());
            totalVolume = totalVolume.add(dto.getVolume());
            totalWeight = totalWeight.add(dto.getWeight());
            
            doIds.add(dto.getDoHeaderId());
            
            aisles.addAll(dto.getAisles());
            
            return isOver();
        }
    
        public boolean isOver() {
            return totalUnit.doubleValue() >= maxUnitNum || totalVolume.doubleValue() >= maxVolume || totalWeight.doubleValue() >= maxWeight || doIds.size() >= recommendNum;
        }
        
        public Set<String> getAisles() {
            return aisles;
        }
        
        public void setAisles(Set<String> aisles) {
            this.aisles = aisles;
        }
        
        public List<Long> getDoIds() {
            return doIds;
        }
        
        public void setDoIds(List<Long> doIds) {
            this.doIds = doIds;
        }
        
        public int getAdditionNum() {
            return additionNum;
        }
        
        public void setAdditionNum(int additionNum) {
            this.additionNum = additionNum;
        }
        
        public BigDecimal getTotalUnit() {
            return totalUnit;
        }
        
        public void setTotalUnit(BigDecimal totalUnit) {
            this.totalUnit = totalUnit;
        }
        
        public BigDecimal getTotalVolume() {
            return totalVolume;
        }
        
        public void setTotalVolume(BigDecimal totalVolume) {
            this.totalVolume = totalVolume;
        }
        
        public BigDecimal getTotalWeight() {
            return totalWeight;
        }
        
        public void setTotalWeight(BigDecimal totalWeight) {
            this.totalWeight = totalWeight;
        }
    }
}