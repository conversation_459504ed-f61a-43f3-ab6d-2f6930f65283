package com.daxia.wms.delivery.crossorder.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import org.hibernate.annotations.*;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "doc_cross_seed_header")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@BatchSize(size = 20)
@SQLDelete(sql = "update doc_cross_seed_header set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class CrossSeedHeader extends WhBaseEntity {

    private static final long serialVersionUID = 3143543087643042222L;

    // 主键
    private Long id;

    /** 分播单号 */
    private String seedNo;

    /** 初始化00，分配中30， 预分配40，进行中50，已完成65 */
    /** 异常大类 */
    private String status;

    /** ASN单号 */
    private String asnNo;

    /** 发货单的数量 */
    private Long doNum;

    /** 分播明细所有数量之和 */
    private BigDecimal units;

    private BigDecimal actualUnits;

    /** 打印标记：0否 1是 */
    private Integer isPrinted;

    /** 分播开始时间 */
    private Date seedFromTime;

    /** 分播完成时间 */
    private Date seedToTime;

    private List<CrossSeedDetail> crossSeedDetails;

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "autoIdGenerator")
    @GenericGenerator(name = "autoIdGenerator", strategy = "com.daxia.framework.common.dao.AutoIdentityGenerator")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "seed_no")
    public String getSeedNo() {
        return seedNo;
    }

    public void setSeedNo(String seedNo) {
        this.seedNo = seedNo == null ? null : seedNo.trim();
    }

    @Column(name = "status")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    @Column(name = "asn_no")
    public String getAsnNo() {
        return asnNo;
    }

    public void setAsnNo(String asnNo) {
        this.asnNo = asnNo == null ? null : asnNo.trim();
    }

    @Column(name = "do_num")
    public Long getDoNum() {
        return doNum;
    }

    public void setDoNum(Long doNum) {
        this.doNum = doNum;
    }

    @Column(name = "units")
    public BigDecimal getUnits() {
        return units;
    }

    public void setUnits(BigDecimal units) {
        this.units = units;
    }

    @Column(name = "actual_units")
    public BigDecimal getActualUnits() {
        return actualUnits;
    }

    public void setActualUnits(BigDecimal actualUnits) {
        this.actualUnits = actualUnits;
    }

    @Column(name = "is_printed")
    public Integer getIsPrinted() {
        return isPrinted;
    }

    public void setIsPrinted(Integer isPrinted) {
        this.isPrinted = isPrinted;
    }

    @Column(name = "seed_from_time")
    public Date getSeedFromTime() {
        return seedFromTime;
    }

    public void setSeedFromTime(Date seedFromTime) {
        this.seedFromTime = seedFromTime;
    }

    @Column(name = "seed_to_time")
    public Date getSeedToTime() {
        return seedToTime;
    }

    public void setSeedToTime(Date seedToTime) {
        this.seedToTime = seedToTime;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "header")
    @Where(clause = " IS_DELETED = 0 ")
    @org.hibernate.annotations.OrderBy(clause = "id")
    @Cascade(value = {org.hibernate.annotations.CascadeType.SAVE_UPDATE})
    public List<CrossSeedDetail> getCrossSeedDetails() {
        return crossSeedDetails;
    }

    public void setCrossSeedDetails(List<CrossSeedDetail> crossSeedDetails) {
        this.crossSeedDetails = crossSeedDetails;
    }
}