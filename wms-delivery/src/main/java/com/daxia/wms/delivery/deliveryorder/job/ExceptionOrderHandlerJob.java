package com.daxia.wms.delivery.deliveryorder.job;

import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateService;
import com.daxia.wms.delivery.deliveryorder.service.impl.DoAllocateServiceImpl;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.job.AbstractJob;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.master.service.impl.WarehouseServiceImpl;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 *
 */
@Name("exceptionOrderHandlerJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class ExceptionOrderHandlerJob extends AbstractJob {

    @Override
    protected void doRun() throws Exception {

    }
}