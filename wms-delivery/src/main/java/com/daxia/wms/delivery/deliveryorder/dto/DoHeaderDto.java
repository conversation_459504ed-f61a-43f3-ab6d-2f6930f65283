package com.daxia.wms.delivery.deliveryorder.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 发货单头DTO
 */
@lombok.extern.slf4j.Slf4j
public class DoHeaderDto implements Serializable {

    private static final long serialVersionUID = 8316182654323875023L;

    private Long id;
    /**
     * DO单号
     */
    private String doNo;
    /**
     * 发货单状态
     */
    private String status;
    /**
     * 冻结状态 HOLD：冻结 RELEASE：释放
     */
    private String releaseStatus;
    /**
     * 订货数量 EXPECTED_QTY_EACH
     */
    private BigDecimal expectedQty;
    /**
     * 发货数量
     */
    private BigDecimal shipQty;
    /**
     * DO创建时间
     */
    private Date doCreateTime;
    /**
     * DOd导入时间
     */
    private Date createTime;
    /**
     * 发货日期
     */
    private Date shipTime;
    /**
     * DO类型
     */
    private String doType;
    /**
     * 是否半日达
     */
    private Integer isHalfDayDelivery;
    /**
     * 配送类型
     */
    private Integer deliveryLimitType;
    /**
     * 国家
     */
    private String country;
    /**
     * 省份
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区县
     */
    private String county;
    /**
     * 波次号
     */
    private String waveNo;
    /**
     * 分拣格号
     */
    private String sortGridNo;
    /**
     * 客户姓名
     */
    private String consigneeName;
    /**
     * 收货地址
     */
    private String address;
    /**
     * 邮编
     */
    private String postCode;
    /**
     * 电话
     */
    private String telephone;
    /**
     * 手机
     */
    private String mobile;
    /**
     * 配送公司名
     */
    private String distSuppCompName;
    /**
     * 发票数量
     */
    private Long invoiceQty;
    /**
     * 自定义字段1
     */
    private String userDeffine1;
    /**
     * 自定义字段2
     */
    private String userDeffine2;
    /**
     * 自定义字段3
     */
    private String userDeffine3;
    /**
     * 自定义字段4
     */
    private String userDeffine4;
    /**
     * 自定义字段5
     */
    private String userDeffine5;
    /**
     * 自定义字段5
     */
    private String userDeffine6;
    /**
     * 冻结原因代码
     */
    private String holdCode;
    /**
     * 冻结原因
     */
    private String holdReason;
    /**
     * 冻结人
     */
    private String holdWho;
    /**
     * 冻结时间
     */
    private Date holdTime;
    /**
     * 代收货款金额
     */
    private BigDecimal productAmount;
    /**
     * 客户支付配送费
     */
    private BigDecimal orderDeliveryFee;
    /**
     * 退换货标识
     */
    private Integer exchangeFlag;
    /**
     * 重量
     */
    private BigDecimal grossWt;
    /**
     * 应收款
     */
    private BigDecimal receivable;

    private BigDecimal volume;

    /**
     * 配送公司Id
     */
    private Long carrierId;

    private String notes;

    private Date createdAt;

    /**
     * 目标分拣中心
     */
    private String lastDcName;

    private String refNo1;

    private String refNo2;

    /**
     * 调拨类型
     */
    private Integer tranType;

    /**
     * 生鲜标记
     */
    private Integer isFresh;

    private Integer volumeType;

    /**
     * 贵重标记
     */
    private Integer isValuable;

    /**
     * 预计出库时间（标准）
     */
    private Date doFinishTime;

    private Date planShipTime;
    private String originalSoCode;

    /**
     * 最早配送时间(O2O)
     */
    private Date expectedArriveTime1;
    /**
     * 最晚时间(O2O)
     */
    private Date expectedArriveTime2;

    private String productCode;
    private String stationName;

    private BigDecimal orderAmount;

    private Date payTime;

    private String buyerRemark;// 买家备注
    private String sellerRemark;// 卖家备注

    private String orderSubType;

    private Long sourceAsnId;

    private Integer needCrossStock;

    private String businessCustomerName;

    /**
     * 紧急标记
     */
    private Integer emergencyFlag;

    private String groupRuleName;

    private String wayBill;

    private BigDecimal packedQty;

    private Integer printFlag;

    /**
     * 发货商品种数
     */
    private Integer skuCount;

    /**
     * 箱标签打印标识
     */
    private Integer cartonPrintFlag;

    private Date packEndTime;
    private String specialLabelCode;
    /**
     * 运输温度
     */
    private Integer transportWendy;
    private String trackingNo;

    private String channelCode;
    private String storeCode;

    private String channelName;
    private String storeName;
    /**
     * 订单货品等级标识 A1 10, A2 11, A3 12, A1+A2 15,A1+A3 16, A2+A3 17, A1+A2+A3 18, B1
     * 20, B2 21, C 22,
     * B1+B2 25, B1+C 26,B2+C 27, B1+B2+C 28 A+B/A+C 36
     */
    private Long cycleClass;
    @Setter
    @Getter
    private String aisles;

    public Long getCycleClass() {
        return cycleClass;
    }

    public void setCycleClass(Long cycleClass) {
        this.cycleClass = cycleClass;
    }

    public Integer getTransportWendy() {
        return transportWendy;
    }

    public void setTransportWendy(Integer transportWendy) {
        this.transportWendy = transportWendy;
    }

    public DoHeaderDto() {

    }

    private DoHeaderDto(Long id, String doNo, String status, String releaseStatus, BigDecimal expectedQty,
            BigDecimal shipQty, Date doCreateTime, Date shipTime, String doType, Integer isHalfDayDelivery,
            Integer deliveryLimitType, String country, String province, String city, String county, String waveNo,
            String sortGridNo, String consigneeName, String address, String postCode, String telephone, String mobile,
            String distSuppCompName, Long invoiceQty, String userDeffine1, String holdCode, String holdReason,
            String holdWho, Date holdTime, BigDecimal productAmount, BigDecimal orderDeliveryFee, Integer exchangeFlag,
            BigDecimal grossWt, BigDecimal receivable, String notes, Date createdAt, String lastDcName, String refNo1,
            Integer tranType) {
        this.id = id;
        this.doNo = doNo;
        this.status = status;
        this.releaseStatus = releaseStatus;
        this.expectedQty = expectedQty;
        this.shipQty = shipQty;
        this.doCreateTime = doCreateTime;
        this.shipTime = shipTime;
        this.doType = doType;
        this.isHalfDayDelivery = isHalfDayDelivery;
        this.deliveryLimitType = deliveryLimitType;
        this.country = country;
        this.province = province;
        this.city = city;
        this.county = county;
        this.waveNo = waveNo;
        this.sortGridNo = sortGridNo;
        this.consigneeName = consigneeName;
        this.address = address;
        this.postCode = postCode;
        this.telephone = telephone;
        this.mobile = mobile;
        this.distSuppCompName = distSuppCompName;
        this.invoiceQty = invoiceQty;
        this.userDeffine1 = userDeffine1;
        this.holdCode = holdCode;
        this.holdReason = holdReason;
        this.holdWho = holdWho;
        this.holdTime = holdTime;
        this.productAmount = productAmount;
        this.orderDeliveryFee = orderDeliveryFee;
        this.exchangeFlag = exchangeFlag;
        this.grossWt = grossWt;
        this.receivable = receivable;
        this.notes = notes;
        this.createdAt = createdAt;
        this.lastDcName = lastDcName;
        this.setRefNo1(refNo1);
        this.tranType = tranType;
    }

    private DoHeaderDto(Long id, String doNo, BigDecimal expectedQty, Date doCreateTime, String doType,
            Integer isHalfDayDelivery, Integer deliveryLimitType, String country, String province, String city,
            String county, String consigneeName, String address, String postCode, String telephone, String mobile,
            String distSuppCompName, Long invoiceQty, BigDecimal volume, BigDecimal receivable, String lastDcName,
            Integer tranType) {
        this.id = id;
        this.doNo = doNo;
        this.expectedQty = expectedQty;
        this.doCreateTime = doCreateTime;
        this.doType = doType;
        this.isHalfDayDelivery = isHalfDayDelivery;
        this.deliveryLimitType = deliveryLimitType;
        this.country = country;
        this.province = province;
        this.city = city;
        this.county = county;
        this.consigneeName = consigneeName;
        this.address = address;
        this.postCode = postCode;
        this.telephone = telephone;
        this.mobile = mobile;
        this.distSuppCompName = distSuppCompName;
        this.invoiceQty = invoiceQty;
        this.volume = volume;
        this.receivable = receivable;
        this.lastDcName = lastDcName;
        this.tranType = tranType;
    }

    /**
     * 专用于"发货单" 界面分页查询显示，即仅适用于：DoHeaderDAO的public DataPage<DoHeaderDto>
     * findDoHeaderPageInfo（）方法
     */
    public DoHeaderDto(Long id, String doNo, String status, String releaseStatus, BigDecimal expectedQty,
            BigDecimal shipQty, Date doCreateTime, Date shipTime, String doType, String province, String city,
            String county, String waveNo, String sortGridNo, String consigneeName, String address, String postCode,
            String telephone, String mobile, String distSuppCompName, Long invoiceQty, String userDeffine1,
            String holdCode, String holdReason,
            String holdWho, Date holdTime, BigDecimal productAmount, BigDecimal orderDeliveryFee, Integer exchangeFlag,
            BigDecimal grossWt, BigDecimal receivable, String notes, Date createdAt, String refNo1, Date doFinishTime,
            String buyerRemark, String sellerRemark, Date payTime, Date expectedArriveTime1, Date expectedArriveTime2,
            String orderSubType) {
        this(id, doNo, status, releaseStatus, expectedQty, shipQty, doCreateTime, shipTime, doType, null, null, null,
                province, city, county, waveNo, sortGridNo, consigneeName, address, postCode, telephone, mobile,
                distSuppCompName, invoiceQty, userDeffine1, holdCode, holdReason, holdWho, holdTime, productAmount,
                orderDeliveryFee, exchangeFlag, grossWt, receivable, notes, createdAt, null, refNo1, null);
        this.expectedArriveTime1 = expectedArriveTime1;
        this.expectedArriveTime2 = expectedArriveTime2;
        this.doFinishTime = doFinishTime;
        this.sellerRemark = sellerRemark;
        this.buyerRemark = buyerRemark;
        this.payTime = payTime;
        this.orderSubType = orderSubType;
    }

    /**
     * 专用于"发货单" 界面分页查询显示，即仅适用于：DoHeaderDAO的public DataPage<DoHeaderDto>
     * findDoHeaderPageInfo（）方法
     */
    public DoHeaderDto(Long id, String doNo, String status, String releaseStatus, BigDecimal expectedQty,
            BigDecimal shipQty, Date doCreateTime, Date shipTime, String doType, String province, String city,
            String county, String waveNo, String sortGridNo, String consigneeName, String address, String postCode,
            String telephone, String mobile, String distSuppCompName, Long invoiceQty, String userDeffine1,
            String holdCode, String holdReason,
            String holdWho, Date holdTime, BigDecimal productAmount, BigDecimal orderDeliveryFee, Integer exchangeFlag,
            BigDecimal grossWt, BigDecimal receivable, String notes, Date createdAt, String refNo1, Date doFinishTime,
            String buyerRemark, String sellerRemark, Date payTime, Date expectedArriveTime1, Date expectedArriveTime2,
            String orderSubType, Integer needCrossStock, Long sourceAsnId, String refNo2, String businessCustomerName,
            Integer emergencyFlag, Date planShipTime, String originalSoCode, BigDecimal packedQty,
            String udf1, String udf2, String udf3, String udf4, String udf5, String udf6, Integer printFlag) {
        this(id, doNo, status, releaseStatus, expectedQty, shipQty, doCreateTime, shipTime, doType, null, null, null,
                province, city, county, waveNo, sortGridNo, consigneeName, address, postCode, telephone, mobile,
                distSuppCompName, invoiceQty, userDeffine1, holdCode, holdReason, holdWho, holdTime, productAmount,
                orderDeliveryFee, exchangeFlag, grossWt, receivable, notes, createdAt, null, refNo1, null);
        this.expectedArriveTime1 = expectedArriveTime1;
        this.expectedArriveTime2 = expectedArriveTime2;
        this.doFinishTime = doFinishTime;
        this.sellerRemark = sellerRemark;
        this.buyerRemark = buyerRemark;
        this.payTime = payTime;
        this.orderSubType = orderSubType;

        this.needCrossStock = needCrossStock;
        this.sourceAsnId = sourceAsnId;
        this.refNo2 = refNo2;
        this.businessCustomerName = businessCustomerName;
        this.emergencyFlag = emergencyFlag;
        this.planShipTime = planShipTime;
        this.originalSoCode = originalSoCode;
        this.packedQty = packedQty;
        this.userDeffine1 = udf1;
        this.userDeffine2 = udf2;
        this.userDeffine3 = udf3;
        this.userDeffine4 = udf4;
        this.userDeffine5 = udf5;
        this.userDeffine6 = udf6;
        this.printFlag = printFlag;
    }

    /**
     * 专用于"生成波次" 界面分页查询显示
     */
    public DoHeaderDto(Long id, String doNo, BigDecimal expectedQty, Date doCreateTime, String doType,
            Integer isHalfDayDelivery, Integer deliveryLimitType, String country, String province, String city,
            String county, String consigneeName, String address, String postCode, String telephone, String mobile,
            String distSuppCompName, Long invoiceQty, BigDecimal volume, BigDecimal receivable, String lastDcName,
            Integer tranType, Integer isFresh, Integer isValuable, Date doFinishTime, BigDecimal grossWt,
            String stationName, BigDecimal orderAmount, Integer volumeType, String buyerRemark, String sellerRemark,
            Integer times,
            Date payTime, String businessCustomerName, Integer emergencyFlag, String groupRuleName,
            String originalSoCode) {
        this(id, doNo, expectedQty, doCreateTime, doType, isHalfDayDelivery, deliveryLimitType, country, province, city,
                county, consigneeName, address, postCode, telephone, mobile, distSuppCompName, invoiceQty, volume,
                receivable, lastDcName, tranType);
        this.orderAmount = orderAmount;
        this.volumeType = volumeType;
        this.buyerRemark = buyerRemark;
        this.sellerRemark = sellerRemark;
        this.times = times;
        this.payTime = payTime;
        this.isFresh = isFresh;
        this.isValuable = isValuable;
        this.doFinishTime = doFinishTime;
        this.grossWt = grossWt;
        this.stationName = stationName;
        this.businessCustomerName = businessCustomerName;
        this.emergencyFlag = emergencyFlag;
        this.groupRuleName = groupRuleName;
        this.originalSoCode = originalSoCode;
    }

    /**
     * 专用于"生成波次" 界面分页查询显示
     */
    public DoHeaderDto(Long id, String doNo, BigDecimal expectedQty, Date doCreateTime, String doType,
            Integer isHalfDayDelivery, Integer deliveryLimitType, String country, String province, String city,
            String county, String consigneeName, String address, String postCode, String telephone, String mobile,
            String distSuppCompName, Long invoiceQty, BigDecimal volume, BigDecimal receivable, String lastDcName,
            Integer tranType, Integer isFresh, Integer isValuable, Date doFinishTime, BigDecimal grossWt,
            String stationName, BigDecimal orderAmount, Integer volumeType, String buyerRemark, String sellerRemark,
            Integer times,
            Date payTime, String businessCustomerName, Integer emergencyFlag, String groupRuleName,
            String originalSoCode, String aisles) {
        this(id, doNo, expectedQty, doCreateTime, doType, isHalfDayDelivery, deliveryLimitType, country, province, city,
                county, consigneeName, address, postCode, telephone, mobile, distSuppCompName, invoiceQty, volume,
                receivable, lastDcName, tranType);
        this.orderAmount = orderAmount;
        this.volumeType = volumeType;
        this.buyerRemark = buyerRemark;
        this.sellerRemark = sellerRemark;
        this.times = times;
        this.payTime = payTime;
        this.isFresh = isFresh;
        this.isValuable = isValuable;
        this.doFinishTime = doFinishTime;
        this.grossWt = grossWt;
        this.stationName = stationName;
        this.businessCustomerName = businessCustomerName;
        this.emergencyFlag = emergencyFlag;
        this.groupRuleName = groupRuleName;
        this.originalSoCode = originalSoCode;
        this.aisles = aisles;
    }

    /**
     * 专用于"生成波次" 界面分页查询显示,增加导入时间createdAt
     */
    public DoHeaderDto(Long id, String doNo, BigDecimal expectedQty, Date doCreateTime, String doType,
            Integer isHalfDayDelivery, Integer deliveryLimitType, String country, String province, String city,
            String county, String consigneeName, String address, String postCode, String telephone, String mobile,
            String distSuppCompName, Long invoiceQty, BigDecimal volume, BigDecimal receivable, String lastDcName,
            Integer tranType, Integer isFresh, Integer isValuable, Date doFinishTime, BigDecimal grossWt,
            String stationName, BigDecimal orderAmount, Integer volumeType, String buyerRemark, String sellerRemark,
            Integer times,
            Date payTime, String businessCustomerName, Integer emergencyFlag, String groupRuleName,
            String originalSoCode, Date createdAt, String channelCode, String storeCode, Long cycleClass) {
        this(id, doNo, expectedQty, doCreateTime, doType, isHalfDayDelivery, deliveryLimitType, country, province, city,
                county, consigneeName, address, postCode, telephone, mobile, distSuppCompName, invoiceQty, volume,
                receivable, lastDcName, tranType);
        this.orderAmount = orderAmount;
        this.volumeType = volumeType;
        this.buyerRemark = buyerRemark;
        this.sellerRemark = sellerRemark;
        this.times = times;
        this.payTime = payTime;
        this.isFresh = isFresh;
        this.isValuable = isValuable;
        this.doFinishTime = doFinishTime;
        this.grossWt = grossWt;
        this.stationName = stationName;
        this.businessCustomerName = businessCustomerName;
        this.emergencyFlag = emergencyFlag;
        this.groupRuleName = groupRuleName;
        this.originalSoCode = originalSoCode;
        this.createdAt = createdAt;
        this.channelCode = channelCode;
        this.storeCode = storeCode;
        this.cycleClass = cycleClass;
        this.channelName = channelCode;
        this.storeName = storeCode;
    }

    /**
     * 专用于"生成波次" 界面分页查询显示,增加导入时间createdAt
     */
    public DoHeaderDto(Long id, String doNo, BigDecimal expectedQty, Date doCreateTime, String doType,
            Integer isHalfDayDelivery, Integer deliveryLimitType, String country, String province, String city,
            String county, String consigneeName, String address, String postCode, String telephone, String mobile,
            String distSuppCompName, Long invoiceQty, BigDecimal volume, BigDecimal receivable, String lastDcName,
            Integer tranType, Integer isFresh, Integer isValuable, Date doFinishTime, BigDecimal grossWt,
            String stationName, BigDecimal orderAmount, Integer volumeType, String buyerRemark, String sellerRemark,
            Integer times,
            Date payTime, String businessCustomerName, Integer emergencyFlag, String groupRuleName,
            String originalSoCode, Date createdAt, String channelCode, String storeCode, Long cycleClass,
            String aisles) {
        this(id, doNo, expectedQty, doCreateTime, doType, isHalfDayDelivery, deliveryLimitType, country, province, city,
                county, consigneeName, address, postCode, telephone, mobile, distSuppCompName, invoiceQty, volume,
                receivable, lastDcName, tranType);
        this.orderAmount = orderAmount;
        this.volumeType = volumeType;
        this.buyerRemark = buyerRemark;
        this.sellerRemark = sellerRemark;
        this.times = times;
        this.payTime = payTime;
        this.isFresh = isFresh;
        this.isValuable = isValuable;
        this.doFinishTime = doFinishTime;
        this.grossWt = grossWt;
        this.stationName = stationName;
        this.businessCustomerName = businessCustomerName;
        this.emergencyFlag = emergencyFlag;
        this.groupRuleName = groupRuleName;
        this.originalSoCode = originalSoCode;
        this.createdAt = createdAt;
        this.channelCode = channelCode;
        this.storeCode = storeCode;
        this.cycleClass = cycleClass;
        this.aisles = aisles;
        this.channelName = channelCode;
        this.storeName = storeCode;
    }

    /**
     * 专用于"生成波次" 界面分页查询显示,增加userDeffine5字段 - 43个参数的构造函数
     */
    public DoHeaderDto(Long id, String doNo, BigDecimal expectedQty, Date doCreateTime, String doType,
            Integer isHalfDayDelivery, Integer deliveryLimitType, String country, String province, String city,
            String county, String consigneeName, String address, String postCode, String telephone, String mobile,
            String distSuppCompName, Long invoiceQty, BigDecimal volume, BigDecimal receivable, String lastDcName,
            Integer tranType, Integer isFresh, Integer isValuable, Date doFinishTime, BigDecimal grossWt,
            String stationName, BigDecimal orderAmount, Integer volumeType, String buyerRemark, String sellerRemark,
            Integer times,
            Date payTime, String businessCustomerName, Integer emergencyFlag, String groupRuleName,
            String originalSoCode, Date createdAt, String channelCode, String storeCode, Long cycleClass,
            String aisles, String userDeffine5) {
        this(id, doNo, expectedQty, doCreateTime, doType, isHalfDayDelivery, deliveryLimitType, country, province, city,
                county, consigneeName, address, postCode, telephone, mobile, distSuppCompName, invoiceQty, volume,
                receivable, lastDcName, tranType);
        this.orderAmount = orderAmount;
        this.volumeType = volumeType;
        this.buyerRemark = buyerRemark;
        this.sellerRemark = sellerRemark;
        this.times = times;
        this.payTime = payTime;
        this.isFresh = isFresh;
        this.isValuable = isValuable;
        this.doFinishTime = doFinishTime;
        this.grossWt = grossWt;
        this.stationName = stationName;
        this.businessCustomerName = businessCustomerName;
        this.emergencyFlag = emergencyFlag;
        this.groupRuleName = groupRuleName;
        this.originalSoCode = originalSoCode;
        this.createdAt = createdAt;
        this.channelCode = channelCode;
        this.storeCode = storeCode;
        this.cycleClass = cycleClass;
        this.aisles = aisles;
        this.userDeffine5 = userDeffine5;
        this.channelName = channelCode;
        this.storeName = storeCode;
    }

    /**
     * 专用于"发货单" 界面分页查询显示，即仅适用于：DoHeaderDAO的public DataPage<DoHeaderDto>
     * findDoHeaderPageInfo（）方法
     */
    public DoHeaderDto(Long id, String doNo, String status, String releaseStatus, BigDecimal expectedQty,
            BigDecimal shipQty, Date doCreateTime, Date shipTime, String doType, String province, String city,
            String county, String waveNo, String sortGridNo, String consigneeName, String address, String postCode,
            String telephone, String mobile, String distSuppCompName, Long invoiceQty, String userDeffine1,
            String holdCode, String holdReason,
            String holdWho, Date holdTime, BigDecimal productAmount, BigDecimal orderDeliveryFee, Integer exchangeFlag,
            BigDecimal grossWt, BigDecimal receivable, String notes, Date createdAt, String refNo1, Date doFinishTime,
            String buyerRemark, String sellerRemark, Date payTime, Date expectedArriveTime1, Date expectedArriveTime2,
            String orderSubType, Integer needCrossStock, Long sourceAsnId, String refNo2, String businessCustomerName,
            Integer emergencyFlag, Date planShipTime, String originalSoCode, BigDecimal packedQty,
            String udf1, String udf2, String udf3, String udf4, String udf5, String udf6,
            Integer printFlag, Date packEndTime, Date createTime, String specialLabelCode,
            Integer transportWendy, String trackingNo, Long carrierId, String channelCode, String storeCode,
            Long cycleClass, Long skuCount, Integer cartonPrintFlag) {
        this(id, doNo, status, releaseStatus, expectedQty, shipQty, doCreateTime, shipTime, doType, null, null, null,
                province, city, county, waveNo, sortGridNo, consigneeName, address, postCode, telephone, mobile,
                distSuppCompName, invoiceQty, userDeffine1, holdCode, holdReason, holdWho, holdTime, productAmount,
                orderDeliveryFee, exchangeFlag, grossWt, receivable, notes, createdAt, null, refNo1, null);
        this.expectedArriveTime1 = expectedArriveTime1;
        this.expectedArriveTime2 = expectedArriveTime2;
        this.doFinishTime = doFinishTime;
        this.sellerRemark = sellerRemark;
        this.buyerRemark = buyerRemark;
        this.payTime = payTime;
        this.orderSubType = orderSubType;

        this.needCrossStock = needCrossStock;
        this.sourceAsnId = sourceAsnId;
        this.refNo2 = refNo2;
        this.businessCustomerName = businessCustomerName;
        this.emergencyFlag = emergencyFlag;
        this.planShipTime = planShipTime;
        this.originalSoCode = originalSoCode;
        this.packedQty = packedQty;
        this.userDeffine1 = udf1;
        this.userDeffine2 = udf2;
        this.userDeffine3 = udf3;
        this.userDeffine4 = udf4;
        this.userDeffine5 = udf5;
        this.userDeffine6 = udf6;
        this.printFlag = printFlag;
        this.packEndTime = packEndTime;
        this.createTime = createTime;
        this.specialLabelCode = specialLabelCode;
        this.transportWendy = transportWendy;
        this.trackingNo = trackingNo;
        this.carrierId = carrierId;
        this.channelCode = channelCode;
        this.storeCode = storeCode;
        this.cycleClass = cycleClass;
        this.skuCount = (skuCount == null) ? null : skuCount.intValue();
        this.cartonPrintFlag = cartonPrintFlag;
    }

    public String getSpecialLabelCode() {
        return specialLabelCode;
    }

    public void setSpecialLabelCode(String specialLabelCode) {
        this.specialLabelCode = specialLabelCode;
    }

    private Integer times;

    public Integer getTimes() {
        return times;
    }

    public void setTimes(Integer times) {
        this.times = times;
    }

    public String getBuyerRemark() {
        return buyerRemark;
    }

    public void setBuyerRemark(String buyerRemark) {
        this.buyerRemark = buyerRemark;
    }

    public String getSellerRemark() {
        return sellerRemark;
    }

    public void setSellerRemark(String sellerRemark) {
        this.sellerRemark = sellerRemark;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReleaseStatus() {
        return releaseStatus;
    }

    public void setReleaseStatus(String releaseStatus) {
        this.releaseStatus = releaseStatus;
    }

    public BigDecimal getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(BigDecimal expectedQty) {
        this.expectedQty = expectedQty;
    }

    public BigDecimal getShipQty() {
        return shipQty;
    }

    public void setShipQty(BigDecimal shipQty) {
        this.shipQty = shipQty;
    }

    public Date getDoCreateTime() {
        return doCreateTime;
    }

    public void setDoCreateTime(Date doCreateTime) {
        this.doCreateTime = doCreateTime;
    }

    public Date getShipTime() {
        return shipTime;
    }

    public void setShipTime(Date shipTime) {
        this.shipTime = shipTime;
    }

    public String getDoType() {
        return doType;
    }

    public void setDoType(String doType) {
        this.doType = doType;
    }

    public Integer getIsHalfDayDelivery() {
        return isHalfDayDelivery;
    }

    public void setIsHalfDayDelivery(Integer isHalfDayDelivery) {
        this.isHalfDayDelivery = isHalfDayDelivery;
    }

    public Integer getDeliveryLimitType() {
        return deliveryLimitType;
    }

    public void setDeliveryLimitType(Integer deliveryLimitType) {
        this.deliveryLimitType = deliveryLimitType;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getDistSuppCompName() {
        return distSuppCompName;
    }

    public void setDistSuppCompName(String distSuppCompName) {
        this.distSuppCompName = distSuppCompName;
    }

    public Long getInvoiceQty() {
        return invoiceQty;
    }

    public void setInvoiceQty(Long invoiceQty) {
        this.invoiceQty = invoiceQty;
    }

    public String getUserDeffine1() {
        return userDeffine1;
    }

    public void setUserDeffine1(String userDeffine1) {
        this.userDeffine1 = userDeffine1;
    }

    public String getHoldCode() {
        return holdCode;
    }

    public void setHoldCode(String holdCode) {
        this.holdCode = holdCode;
    }

    public String getHoldReason() {
        return holdReason;
    }

    public void setHoldReason(String holdReason) {
        this.holdReason = holdReason;
    }

    public String getHoldWho() {
        return holdWho;
    }

    public void setHoldWho(String holdWho) {
        this.holdWho = holdWho;
    }

    public Date getHoldTime() {
        return holdTime;
    }

    public void setHoldTime(Date holdTime) {
        this.holdTime = holdTime;
    }

    public BigDecimal getProductAmount() {
        return productAmount;
    }

    public void setProductAmount(BigDecimal productAmount) {
        this.productAmount = productAmount;
    }

    public BigDecimal getOrderDeliveryFee() {
        return orderDeliveryFee;
    }

    public void setOrderDeliveryFee(BigDecimal orderDeliveryFee) {
        this.orderDeliveryFee = orderDeliveryFee;
    }

    public void setGrossWt(BigDecimal grossWt) {
        this.grossWt = grossWt;
    }

    public BigDecimal getGrossWt() {
        return grossWt;
    }

    public Integer getExchangeFlag() {
        return exchangeFlag;
    }

    public void setExchangeFlag(Integer exchangeFlag) {
        this.exchangeFlag = exchangeFlag;
    }

    public BigDecimal getReceivable() {
        return receivable;
    }

    public void setReceivable(BigDecimal receivable) {
        this.receivable = receivable;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public Long getCarrierId() {
        return carrierId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public String getLastDcName() {
        return lastDcName;
    }

    public void setLastDcName(String lastDcName) {
        this.lastDcName = lastDcName;
    }

    public void setRefNo1(String refNo1) {
        this.refNo1 = refNo1;
    }

    public String getRefNo1() {
        return refNo1;
    }

    public Integer getTranType() {
        return tranType;
    }

    public void setTranType(Integer tranType) {
        this.tranType = tranType;
    }

    public Integer getIsFresh() {
        return isFresh;
    }

    public void setIsFresh(Integer isFresh) {
        this.isFresh = isFresh;
    }

    public Integer getIsValuable() {
        return isValuable;
    }

    public void setIsValuable(Integer isValuable) {
        this.isValuable = isValuable;
    }

    public Date getDoFinishTime() {
        return doFinishTime;
    }

    public void setDoFinishTime(Date doFinishTime) {
        this.doFinishTime = doFinishTime;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public Integer getVolumeType() {
        return volumeType;
    }

    public void setVolumeType(Integer volumeType) {
        this.volumeType = volumeType;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Date getExpectedArriveTime1() {
        return expectedArriveTime1;
    }

    public void setExpectedArriveTime1(Date expectedArriveTime1) {
        this.expectedArriveTime1 = expectedArriveTime1;
    }

    public Date getExpectedArriveTime2() {
        return expectedArriveTime2;
    }

    public void setExpectedArriveTime2(Date expectedArriveTime2) {
        this.expectedArriveTime2 = expectedArriveTime2;
    }

    public String getOrderSubType() {
        return orderSubType;
    }

    public void setOrderSubType(String orderSubType) {
        this.orderSubType = orderSubType;
    }

    public Long getSourceAsnId() {
        return sourceAsnId;
    }

    public void setSourceAsnId(Long sourceAsnId) {
        this.sourceAsnId = sourceAsnId;
    }

    public Integer getNeedCrossStock() {
        return needCrossStock;
    }

    public void setNeedCrossStock(Integer needCrossStock) {
        this.needCrossStock = needCrossStock;
    }

    public String getRefNo2() {
        return refNo2;
    }

    public void setRefNo2(String refNo2) {
        this.refNo2 = refNo2;
    }

    public String getBusinessCustomerName() {
        return businessCustomerName;
    }

    public void setBusinessCustomerName(String businessCustomerName) {
        this.businessCustomerName = businessCustomerName;
    }

    public Integer getEmergencyFlag() {
        return emergencyFlag;
    }

    public void setEmergencyFlag(Integer emergencyFlag) {
        this.emergencyFlag = emergencyFlag;
    }

    public String getGroupRuleName() {
        return groupRuleName;
    }

    public void setGroupRuleName(String groupRuleName) {
        this.groupRuleName = groupRuleName;
    }

    public Date getPlanShipTime() {
        return planShipTime;
    }

    public void setPlanShipTime(Date planShipTime) {
        this.planShipTime = planShipTime;
    }

    public String getOriginalSoCode() {
        return originalSoCode;
    }

    public void setOriginalSoCode(String originalSoCode) {
        this.originalSoCode = originalSoCode;
    }

    public String getWayBill() {
        return wayBill;
    }

    public void setWayBill(String wayBill) {
        this.wayBill = wayBill;
    }

    public BigDecimal getPackedQty() {
        return packedQty;
    }

    public void setPackedQty(BigDecimal packedQty) {
        this.packedQty = packedQty;
    }

    public String getUserDeffine2() {
        return userDeffine2;
    }

    public void setUserDeffine2(String userDeffine2) {
        this.userDeffine2 = userDeffine2;
    }

    public String getUserDeffine3() {
        return userDeffine3;
    }

    public void setUserDeffine3(String userDeffine3) {
        this.userDeffine3 = userDeffine3;
    }

    public String getUserDeffine4() {
        return userDeffine4;
    }

    public void setUserDeffine4(String userDeffine4) {
        this.userDeffine4 = userDeffine4;
    }

    public String getUserDeffine5() {
        return userDeffine5;
    }

    public void setUserDeffine5(String userDeffine5) {
        this.userDeffine5 = userDeffine5;
    }

    public Integer getPrintFlag() {
        return printFlag;
    }

    public void setPrintFlag(Integer printFlag) {
        this.printFlag = printFlag;
    }

    public String getUserDeffine6() {
        return userDeffine6;
    }

    public void setUserDeffine6(String userDeffine6) {
        this.userDeffine6 = userDeffine6;
    }

    public Date getPackEndTime() {
        return packEndTime;
    }

    public void setPackEndTime(Date packEndTime) {
        this.packEndTime = packEndTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getTrackingNo() {
        return trackingNo;
    }

    public void setTrackingNo(String trackingNo) {
        this.trackingNo = trackingNo;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    public Integer getCartonPrintFlag() {
        return cartonPrintFlag;
    }

    public void setCartonPrintFlag(Integer cartonPrintFlag) {
        this.cartonPrintFlag = cartonPrintFlag;
    }
    public String getUserDeffine5Formatted() {
        if (userDeffine5 != null) {
            return userDeffine5.replaceFirst("^0+(?!$)", "");
        }
        return "";
    }
        /**
     * 显示打印标识，参考WaveAction.showPrintFlag方法
     * 两个五角星分别表示：送货单/箱标签
     */
    public String getShowPrintFlag() {
        String flagStr = "";
        String fStr = "<i class=\"fa fa-star-o\" aria-hidden=\"true\"></i>";
        String tStr = "<i class=\"fa fa-star\" aria-hidden=\"true\"></i>";

        // 第一个五角星：送货单打印标识
        if (this.getPrintFlag() != null && this.getPrintFlag() == 1) {
            flagStr += "<span title=\"送货单\">" + tStr + "</span>";
        } else {
            flagStr += "<span title=\"送货单\">" + fStr + "</span>";
        }

        // 第二个五角星：箱标签打印标识（从doc_temp_carton表获取）
        if (this.getCartonPrintFlag() != null && this.getCartonPrintFlag() == 1) {
            flagStr += "<span title=\"箱标签\">" + tStr + "</span>";
        } else {
            flagStr += "<span title=\"箱标签\">" + fStr + "</span>";
        }

        return flagStr;
    }

    /**
     * 获取打印标识的HTML内容，用于在表格中正确渲染HTML
     * 这是getShowPrintFlag的别名方法，确保HTML内容能正确显示
     */
    public String getShowPrintFlagHtml() {
        return getShowPrintFlag();
    }

    public String getShipskuCount() {
        if(shipQty ==null || shipQty.compareTo(BigDecimal.ZERO) == 0){
            return "";
        }
        if (userDeffine5 != null) {
            return userDeffine5.replaceFirst("^0+(?!$)", "");
        }
        return "";
    }
}
