package com.daxia.wms.delivery.recheck.dao;

import com.daxia.dubhe.api.internal.util.NumberUtils;
import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.recheck.entity.CartonDetail;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.beanutils.DynaBean;
import org.apache.commons.beanutils.LazyDynaBean;
import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.criterion.Restrictions;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.*;

/**
 * 装箱明细DAO
 */
@Name("com.daxia.wms.delivery.cartonDetailDAO")
@lombok.extern.slf4j.Slf4j
public class CartonDetailDAO extends HibernateBaseDAO<CartonDetail, Long> {

    private static final long serialVersionUID = 2855569228688153045L;

    /**
     * do下的skuId及需要装箱的数量
     *
     * @param doId
     * @return do下的skuId及需要装箱的数量
     */
    public Map<Long, DynaBean> findNeedPackNumber(Long doId, String packageType) {
        String hql = "select o.skuId, sum(o.qtyPickedUnit), sum( case o.status when '60' then o.qtyPickedUnit else o.qtyUnit end) from PickTask o where o.doHeaderId=:doId and o.warehouseId = :warehouseId ";
        if (packageType.equals(Constants.PackageType.B.getValue())) {
            hql = hql + "and o.qty = o.qtyUnit group by o.skuId";
        } else {
            hql = hql + "and o.qty != o.qtyUnit group by o.skuId";
        }
        Query query = createQuery(hql);
        query.setLong("doId", doId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        @SuppressWarnings("unchecked")
        List<Object[]> result = (List<Object[]>) query.list();
        if (ListUtil.isNullOrEmpty(result)) {
            return null;
        }
        Map<Long, DynaBean> map = new HashMap<Long, DynaBean>();
        for (Object[] p : result) {
            DynaBean dynaBean = new LazyDynaBean();
            dynaBean.set("sortedQty", (BigDecimal) p[1]);
            dynaBean.set("expectedQty", (BigDecimal) p[2]);
            map.put((Long) p[0], dynaBean);
        }
        return map;
    }

    /**
     * 药网下查找 do下的skuId及需要装箱的数量以及药监码信息
     *
     * @param doId
     * @return
     */
    @SuppressWarnings("unchecked")
    public Map<String, DynaBean> findPackInfo4Lotatt05(Long doId, String packageType) {
        String sql = "select sku_id, sum(qty1), sum(qty2), Spvsnflag,packQty,lotatt05 from ("
                + " Select x.Sku_Id, " +
                " 			 x.Qty1, " +
                " 			 x.Qty2, " +
                " 			 ifnull((Select t.Is_Validation_Required " +
                " 					From doc_do_detail t " +
                " 				 Where t.Id = x.Id " +
                " 					 And t.Sku_Id = x.Sku_Id " +
                " 					 limit 1),0) As Spvsnflag,packQty,x.lotatt05 " + // Spvsnflag表示核拣是否需要扫药监码，对应明细上的Is_Validation_Required字段
                " 	From (Select o.doc_line_id As Id, " +
                " 							 o.Sku_Id, " +
                " 							 Sum(o.qty_picked_unit) As Qty1, " +
                " 							 Sum(o.qty_unit) As Qty2,pd.QTY packQty,att.lotatt05 " +
                " 					From tsk_pick o,md_package_d pd,stock_batch_att att " +
                " 				 Where o.pack_detail_id = pd.id and o.doc_id = :doId " +
                " 					 And o.warehouse_id = :warehouseId and att.id = o.lot_id" +
                " 					 And o.Is_Deleted = 0  ";
        if (packageType.equals(Constants.PackageType.B.getValue())) {
            sql = sql + " And o.qty = o.qty_unit ";
        } else {
            sql = sql + " And o.qty != o.qty_unit ";
        }
        sql = sql + " Group By o.doc_line_id, o.Sku_Id,att.lotatt05,pd.QTY) x) t" +
                "  group by sku_id,lotatt05, Spvsnflag,packQty ";
        Query query = createSQLQuery(sql);
        query.setLong("doId", doId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        List<Object[]> result = (List<Object[]>) query.list();
        if (ListUtil.isNullOrEmpty(result)) {
            return null;
        }
        Map<String, DynaBean> map = new HashMap<String, DynaBean>();
        for (Object[] p : result) {
            DynaBean dynaBean = new LazyDynaBean();
            dynaBean.set("sortedQty", (BigDecimal) p[1]);
            dynaBean.set("expectedQty", (BigDecimal) p[2]);
            dynaBean.set("spvsnCodeFlag", ((Integer) p[3]));
            dynaBean.set("packQty", ((BigDecimal) p[4]).intValue());
            dynaBean.set("lotatt05", p[5]);
            map.put((p[0] + "_" + StringUtil.notNullString(p[5])), dynaBean);
        }
        return map;
    }


    @SuppressWarnings("unchecked")
    public Map<String, DynaBean> findPackInfo(Long doId, String packageType) {
        String sql = "select sku_id, sum(qty1), sum(qty2), Spvsnflag,packQty from ("
                + " Select x.Sku_Id, " +
                " 			 x.Qty1, " +
                " 			 x.Qty2, " +
                " 			 ifnull((Select t.Is_Validation_Required " +
                " 					From doc_do_detail t " +
                " 				 Where t.Id = x.Id " +
                " 					 And t.Sku_Id = x.Sku_Id " +
                " 					 limit 1),0) As Spvsnflag,packQty " + // Spvsnflag表示核拣是否需要扫药监码，对应明细上的Is_Validation_Required字段
                " 	From (Select o.doc_line_id As Id, " +
                " 							 o.Sku_Id, " +
                " 							 Sum(o.qty_picked_unit) As Qty1, " +
                " 							 Sum(o.qty_unit) As Qty2,pd.QTY packQty " +
                " 					From tsk_pick o,md_package_d pd,stock_batch_att att " +
                " 				 Where o.pack_detail_id = pd.id and o.doc_id = :doId " +
                " 					 And o.warehouse_id = :warehouseId and att.id = o.lot_id" +
                " 					 And o.Is_Deleted = 0  ";
        if (packageType.equals(Constants.PackageType.B.getValue())) {
            sql = sql + " And o.qty = o.qty_unit ";
        } else {
            sql = sql + " And o.qty != o.qty_unit ";
        }
        sql = sql + " Group By o.doc_line_id, o.Sku_Id) x) t" +
                "  group by sku_id, Spvsnflag,packQty ";
        Query query = createSQLQuery(sql);
        query.setLong("doId", doId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        List<Object[]> result = (List<Object[]>) query.list();
        if (ListUtil.isNullOrEmpty(result)) {
            return null;
        }
        Map<String, DynaBean> map = new HashMap<String, DynaBean>();
        for (Object[] p : result) {
            DynaBean dynaBean = new LazyDynaBean();
            dynaBean.set("sortedQty", (BigDecimal) p[1]);
            dynaBean.set("expectedQty", (BigDecimal) p[2]);
            dynaBean.set("spvsnCodeFlag", ((Integer) p[3]));
            dynaBean.set("packQty", ((BigDecimal) p[4]).intValue());
            map.put(StringUtil.notNullString(p[0]), dynaBean);
        }
        return map;
    }
    /**
     * do下的skuId及已经装箱的数量
     *
     * @param doId
     * @return do下的skuId及已经装箱的数量
     */
    public Map<Long, BigDecimal> findPackedNumber(Long doId, String packageType) {
        String hql = "select o.sku.id, sum(o.packedNumber) from CartonDetail o where o.doHeader.id=:doId and o.warehouseId = :warehouseId and o.cartonHeader.ext1=:packageType group by o.sku.id";
        Query query = createQuery(hql);
        query.setLong("doId", doId);
        query.setParameter("packageType", packageType);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        @SuppressWarnings("unchecked")
        List<Object[]> result = (List<Object[]>) query.list();
        if (ListUtil.isNullOrEmpty(result)) {
            return null;
        }
        Map<Long, BigDecimal> map = new HashMap<Long, BigDecimal>();
        for (Object[] p : result) {
            Number n = (Number) p[1];
            map.put((Long) p[0], n != null ? NumberUtils.object2BigDecimal(n) : BigDecimal.ZERO);
        }
        return map;
    }


    /**
     * do下的skuId及已经装箱的数量
     *
     * @param doId
     * @return do下的skuId及已经装箱的数量
     */
    public Map<String, BigDecimal> findPackedNumber4Lotatt05(Long doId, String packageType) {
        String hql = "select o.sku.id, sum(o.packedNumber),o.lotatt05 from CartonDetail o where o.doHeader.id=:doId and o.warehouseId = :warehouseId and o.cartonHeader.ext1=:packageType group by o.sku.id,o.lotatt05 ";
        Query query = createQuery(hql);
        query.setLong("doId", doId);
        query.setParameter("packageType", packageType);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        @SuppressWarnings("unchecked")
        List<Object[]> result = (List<Object[]>) query.list();
        if (ListUtil.isNullOrEmpty(result)) {
            return null;
        }
        Map<String, BigDecimal> map = new HashMap<String, BigDecimal>();
        for (Object[] p : result) {
            Number n = (Number) p[1];
            map.put(p[0] + "_" + StringUtil.notNullString(p[2]), n != null ? NumberUtils.object2BigDecimal(n) : BigDecimal.ZERO);
        }
        return map;
    }

    /**
     * do下的skuId及已经装箱的数量
     *
     * @param doId
     * @return do下的skuId及已经装箱的数量
     */
    public Map<Long, BigDecimal> findPackedNumberByContainer(Long doId, String containerNo) {
        String hql = "select o.sku.id, sum(o.packedNumber) from CartonDetail o where o.doHeader.id=:doId and o.warehouseId = :warehouseId and " +
                " o.cartonHeader.lpnNo=:containerNo group by o.sku.id";
        Query query = createQuery(hql);
        query.setLong("doId", doId);
        query.setParameter("containerNo", containerNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        @SuppressWarnings("unchecked")
        List<Object[]> result = (List<Object[]>) query.list();
        if (ListUtil.isNullOrEmpty(result)) {
            return null;
        }
        Map<Long, BigDecimal> map = new HashMap<Long, BigDecimal>();
        for (Object[] p : result) {
            Number n = (Number) p[1];
            map.put((Long) p[0], n != null ? NumberUtils.object2BigDecimal(n) : BigDecimal.ZERO);
        }
        return map;
    }

    /**
     * do下的skuId及已经装箱的数量
     *
     * @param doId
     * @return do下的skuId及已经装箱的数量
     */
    public Map<String, BigDecimal> findPackedNumberByContainer4Lotatt05(Long doId, String containerNo) {
        String hql = "select o.sku.id, sum(o.packedNumber),o.lotatt05 from CartonDetail o where o.doHeader.id=:doId and o.warehouseId = :warehouseId and " +
                " o.cartonHeader.lpnNo=:containerNo group by o.sku.id,o.lotatt05 ";
        Query query = createQuery(hql);
        query.setLong("doId", doId);
        query.setParameter("containerNo", containerNo);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        @SuppressWarnings("unchecked")
        List<Object[]> result = (List<Object[]>) query.list();
        if (ListUtil.isNullOrEmpty(result)) {
            return null;
        }
        Map<String, BigDecimal> map = new HashMap<String, BigDecimal>();
        for (Object[] p : result) {
            Number n = (Number) p[1];
            map.put(p[0] + "_" + StringUtil.notNullString(p[2]), n != null ? NumberUtils.object2BigDecimal(n) : BigDecimal.ZERO);
        }
        return map;
    }
    /**
     * 根据cartonId和productBarCode得到装箱明细
     *
     * @param cartonId
     * @param productBarCode
     * @return
     */
    @SuppressWarnings({"unchecked"})
    public List<CartonDetail> findByCartonIdAndProductBarCode(Long cartonId, String productBarCode) {
        String hql = "select c from CartonDetail c inner join fetch c.sku where c.warehouseId = :warehouseId " +
                "and (exists (select barcode.id from ProductBarcode barcode where barcode.barcodeLevel1 = :barCode and barcode.skuId = c.sku.id)) " +
                "and c.cartonHeader.id = :cartonId";
        Query query = getSession().createQuery(hql);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("barCode", productBarCode);
        query.setParameter("cartonId", cartonId);
        List<CartonDetail> r = (List<CartonDetail>) query.list();
        if (r == null || r.isEmpty()) {
            return r;
        }
        return new ArrayList<CartonDetail>(new HashSet<CartonDetail>(r));
    }

    /**
     * 根据cartonId和skuId查询CartonDetail，查找出满足条件的一条记录即可
     *
     * @param cartonId
     * @param skuId
     * @return
     */
    public CartonDetail findOneByCartonIdAndProductId(Long cartonId, Long skuId) {
        Criteria cri = getSession().createCriteria(CartonDetail.class);
        cri.add(Restrictions.eq("cartonHeader.id", cartonId));
        cri.add(Restrictions.eq("sku.id", skuId));
        cri.add(Restrictions.eq("warehouseId", ParamUtil.getCurrentWarehouseId()));
        return (CartonDetail) cri.setMaxResults(1).uniqueResult();
    }

    /**
     * 根据箱号删除删除所有的装箱明细
     *
     * @param cartonId
     */
    public void deleteByCartonId(Long cartonId) {
        Query deleteQuery = createDeleteSqlQuery("CARTON_HEADER_ID = :cartonId and WAREHOUSE_ID = :warehouseId");
        deleteQuery.setLong("cartonId", cartonId);
        deleteQuery.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        deleteQuery.executeUpdate();
    }

    /**
     * 物理删除Do明细
     *
     * @param orderId
     */
    public void physicalDeleteByDoId(Long orderId) {
        String hql = "delete from CartonDetail o where o.doHeader.id = :doId and o.warehouseId =:whId ";
        Query deleteQuery = this.createQuery(hql);
        deleteQuery.setLong("doId", orderId);
        deleteQuery.setLong("whId", ParamUtil.getCurrentWarehouseId());
        deleteQuery.executeUpdate();
    }

    /**
     * 更新from头的明细为to头
     *
     * @param fromHeaderId
     * @param toHeaderId
     */
    public void updateDetailHeaderId(Long fromHeaderId, Long toHeaderId) {
        String hqlUpdate = "update CartonDetail o set o.cartonHeader.id = :toHeaderId where o.cartonHeader.id = :fromHeaderId and o.warehouseId = :warehouseId";
        Query query = getSession().createQuery(hqlUpdate);
        query.setLong("toHeaderId", toHeaderId);
        query.setLong("fromHeaderId", fromHeaderId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 更新明细的操作人
     *
     * @param headerId
     * @param operator
     */
    public void updateOperatorOfDetail(Long headerId, String operator) {
        String hqlUpdate = "update CartonDetail o set o.createdBy = :operator, o.updatedBy = :operator  where o.cartonHeader.id = :headerId and o.warehouseId = :warehouseId";
        Query query = getSession().createQuery(hqlUpdate);
        query.setParameter("operator", operator);
        query.setParameter("headerId", headerId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    public BigDecimal queryPackQty(Long doId, Long skuId) {
        String hql = "select sum(o.packedNumber*(case when o.packQty=null or o.packQty=0 then 1 else o.packQty end)) from CartonDetail o where o.doHeader.id = :doId and o.warehouseId = :warehouseId ";
        if (skuId != null) {
            hql += " and o.skuId = :skuId ";
        }
        Query query = this.createQuery(hql);
        query.setParameter("doId", doId);
        if (skuId != null) {
            query.setParameter("skuId", skuId);
        }
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        Object obj = query.uniqueResult();
        if (null == obj) {
            return BigDecimal.ZERO;
        } else {
            return NumberUtils.object2BigDecimal(obj);
        }
    }

    /**
     * 查询装箱头中装SKU
     *
     * @param cartonHeaderId
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Long> queryCartonSkus(Long cartonHeaderId) {
        String hql = "select distinct o.skuId from  CartonDetail o where o.cartonHeader.id =:cartonId  and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setParameter("cartonId", cartonHeaderId);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    @SuppressWarnings("unchecked")
    public Map<Long, List<Long>> findCartonSkuMap(Long doId) {
        String hql = "select distinct o.cartonHeader.id, o.skuId from CartonDetail o where o.doHeader.id = :doId and o.warehouseId = :warehouseId";
        Query query = createQuery(hql).setLong("doId", doId).setLong("warehouseId", ParamUtil.getCurrentWarehouseId());

        List<Object[]> result = (List<Object[]>) query.list();
        if (ListUtil.isNullOrEmpty(result)) {
            return Maps.newHashMap();
        }

        Map<Long, List<Long>> map = Maps.newHashMap();
        for (Object[] p : result) {
            Long cartonHeaderId = (Long) p[0];
            Long skuId = (Long) p[1];

            List<Long> skuIdList = map.get(cartonHeaderId);
            if (skuIdList == null) {
                skuIdList = Lists.newArrayList();
                map.put(cartonHeaderId, skuIdList);
            }
            skuIdList.add(skuId);
        }
        return map;
    }

    public BigDecimal sumSkuUnit(Long cartonId) {
        String hql = "select sum(packedNumber) from CartonDetail o where o.cartonHeader.id = :cartonId and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setParameter("cartonId", cartonId);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        BigDecimal result = (BigDecimal) query.uniqueResult();
        return result != null ? result : BigDecimal.ZERO;
    }

    public List<CartonDetail> getCartonDetailByHeaderId(Long cartonId) {
        String hql = "from CartonDetail o where o.cartonHeader.id = :cartonId and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setParameter("cartonId", cartonId);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    /**
     * 根据托盘查询 托盘上拣货商品信息
     * @param doId
     * @param packageType
     * @return
     */
    public Map<String, DynaBean> findPackInfo4Pallet(Long doId, String packageType) {
        String sql = " Select o.Sku_Id, " +
                " 	   Sum(o.qty_picked_unit) As Qty1, " +
                " 	  Sum(o.qty_unit) As Qty2,pd.QTY packQty,att.lotatt17 " +
                " 	From tsk_pick o,md_package_d pd,stock_batch_att att " +
                " 	Where o.pack_detail_id = pd.id and o.doc_id = :doId " +
                " 	And o.warehouse_id = :warehouseId and att.id = o.lot_id" +
                " 	And o.Is_Deleted = 0  ";
        if (packageType.equals(Constants.PackageType.B.getValue())) {
            sql = sql + " And o.qty = o.qty_unit ";
        } else {
            sql = sql + " And o.qty != o.qty_unit ";
        }
        sql = sql + " Group By o.Sku_Id,att.lotatt17,pd.QTY ";
        Query query = createSQLQuery(sql);
        query.setLong("doId", doId);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        List<Object[]> result = (List<Object[]>) query.list();
        if (ListUtil.isNullOrEmpty(result)) {
            return null;
        }
        Map<String, DynaBean> map = new HashMap<String, DynaBean>();
        for (Object[] p : result) {
            DynaBean dynaBean = new LazyDynaBean();
            dynaBean.set("sortedQty", (BigDecimal) p[1]);
            dynaBean.set("expectedQty", (BigDecimal) p[2]);
            dynaBean.set("packQty", ((BigDecimal) p[3]).intValue());
            dynaBean.set("lotatt17", p[4]);
            map.put((p[0] + "_" + StringUtil.notNullString(p[4])), dynaBean);
        }
        return map;
    }
}