package com.daxia.wms.delivery.recheck.action;

import java.util.ArrayList;
import java.util.List;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.ActionBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;

import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.recheck.service.ReCheckService;
import com.daxia.wms.Constants;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.master.entity.Sku;
import com.daxia.wms.master.service.SkuService;

@Name("com.daxia.wms.delivery.checkDmBySkuAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class CheckDmBySkuAction extends ActionBean {
    private static final long serialVersionUID = -6247060993943398160L;
    private String skuCode;
    private Long skuId;
    private String doNo;
    private Long doId;
    private DeliveryOrderHeader doHeader;
    private List<Sku> skuList = new ArrayList<Sku>();
    private boolean initPageFlag;
    private boolean multiSkuFlag;
    private String showFlag;
    
    @In
    private SkuService skuService;
    @In
    private ReCheckService reCheckService;
    @In
    private ExpFacadeService expFacadeService;
    @In
    private DeliveryOrderService deliveryOrderService;
    
    public void initialize() {
        if (!initPageFlag) {
            initPageFlag = true;
            showFlag = "1";
        }
    }
    
    public void getAndCheckDo() {
        if (StringUtil.isEmpty(doNo)) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        doHeader = deliveryOrderService.findDoHeaderByDoNo(doNo);
        if (doHeader == null) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        if (StringUtil.isNotIn(doHeader.getStatus(), Constants.DoStatus.ALLSORTED.getValue(), 
                Constants.DoStatus.PART_CARTON.getValue())) {
            throw new DeliveryException(DeliveryException.DO_IS_NOT_SORTED_TO_CHECKING);
        }
        this.doId = doHeader.getId();
    }
    
    public void queryOrDoDm() throws Exception {
        multiSkuFlag = false;
        skuId = null;
        getAndCheckDo();
        
        List<Long> skuIds = deliveryOrderService.querySkuIdsInDoByBarcode(doId, skuCode);
        if (skuIds.isEmpty()) {
            skuId = deliveryOrderService.querySkuIdsInDoByProcode(doId, skuCode);
        } else if (skuIds.size()== 1) {
            skuId = skuIds.get(0);
        } else {
            // 一码多品
            multiSkuFlag = true;
            if (ListUtil.isNotEmpty(skuIds)) {
                this.skuList = skuService.querySkuList(skuIds);
            }
            return;
        }
        if (skuId == null) {
            throw new DeliveryException(DeliveryException.NO_DETAIL_TO_CHECK_DM);
        }
        dmBySku();
    }
    
    
    public void dmBySku() {
        getAndCheckDo();
        boolean needCall = reCheckService.dmBySku(doId, skuId);
        if (needCall) {
            expFacadeService.sendDoReleaseOrHold2Scs(doId);
        }
        this.sayMessage(MESSAGE_SUCCESS);
    }

    
    public String getSkuCode() {
        return skuCode;
    }

    
    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    
    public Long getSkuId() {
        return skuId;
    }

    
    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    
    public String getDoNo() {
        return doNo;
    }

    
    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    
    public Long getDoId() {
        return doId;
    }

    
    public void setDoId(Long doId) {
        this.doId = doId;
    }

    
    public DeliveryOrderHeader getDoHeader() {
        return doHeader;
    }

    
    public void setDoHeader(DeliveryOrderHeader doHeader) {
        this.doHeader = doHeader;
    }

    
    public List<Sku> getSkuList() {
        return skuList;
    }

    
    public void setSkuList(List<Sku> skuList) {
        this.skuList = skuList;
    }

    
    public boolean isMultiSkuFlag() {
        return multiSkuFlag;
    }

    
    public void setMultiSkuFlag(boolean multiSkuFlag) {
        this.multiSkuFlag = multiSkuFlag;
    }

    
    public String getShowFlag() {
        return showFlag;
    }

    
    public void setShowFlag(String showFlag) {
        this.showFlag = showFlag;
    }
}
