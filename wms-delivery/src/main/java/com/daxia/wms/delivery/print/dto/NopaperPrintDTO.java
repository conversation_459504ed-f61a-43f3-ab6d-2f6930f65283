package com.daxia.wms.delivery.print.dto;

import java.math.BigDecimal;

@lombok.extern.slf4j.Slf4j
public class NopaperPrintDTO {
	/**
	 * 商品名称
	 */
	private String productCname;
	/**
	 * 数量 
	 * @return
	 */
	private BigDecimal qty;
	
	/**
	 * 是否组合商品
	 */
	private Boolean isDoLeaf;
	
	public String getProductCname() {
		return productCname;
	}
	public void setProductCname(String productCname) {
		this.productCname = productCname;
	}
	
	public BigDecimal getQty() {
		return qty;
	}
	public void setQty(BigDecimal qty) {
		this.qty = qty;
	}
	public Boolean getIsDoLeaf() {
		return isDoLeaf;
	}
	public void setIsDoLeaf(Boolean isDoLeaf) {
		this.isDoLeaf = isDoLeaf;
	}
}
