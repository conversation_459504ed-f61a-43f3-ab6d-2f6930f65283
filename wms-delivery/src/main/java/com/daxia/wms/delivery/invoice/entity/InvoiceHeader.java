package com.daxia.wms.delivery.invoice.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import org.hibernate.annotations.*;
import org.hibernate.annotations.CascadeType;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import java.util.List;

/**
 * 发票头实体
 */
@Entity
@Table(name = "doc_invoice_header")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_invoice_header set is_deleted = 1 where id = ? and version = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class InvoiceHeader extends WhBaseEntity {

	private static final long serialVersionUID = 6315574725795598096L;

    public static enum InvoiceStatus {
        INIT,//初始化
        UPLOAD,//已上传
        NEEDCANCEL,//待取消
        BILLED, //已开票
        PRINT, //已打印
        WRITE_BACK,//红冲
        CANCELLED
    }
	public static enum KpType {
		NORMAL("1"),//正常票
		CH("2");//冲红票

		KpType(String value) {
			this.value = value;
		}

		private final String value;

		public String getValue() {
			return value;
		}
	}
	/**
	 * 电子发票类型
	 * 1：普通开具 2：整单冲红 3：换票冲红
	 */
	public enum EInvoiceIssueType {
		NORMAL(1), WHOLE_CH(2), EXCHANGE_CH(3);

		private Integer value;

		EInvoiceIssueType(Integer value) {
			this.value = value;
		}

		public Integer getValue() {
			return value;
		}
	}
	public static enum OperateType {
		NORMAL("10"),//正常票
		ALL_WRITE_BACK("20");// 退货折让红票
		OperateType(String value) {
			this.value = value;
		}

		private final String value;

		public String getValue() {
			return value;
		}
	}

	public static enum InvoiceType {
		ELECTRONIC("10"),//电子发票
		NORMAL("20");//普通纸质发票,纸质发票仅在核检装箱的时候进行提示，用户自行打印处理；

		InvoiceType(String value) {
			this.value = value;
		}

		private final String value;

		public String getValue() {
			return value;
		}
	}

	/**
	 * 主键
	 */
	private Long id;

	/**
	 * 发票抬头
	 */
	private String invoiceTitle;

	/**
	 * 发票内容
	 */
	private String invoiceContent;

	/**
	 * 发票号码
	 */
	private String invoiceNumber;
	/**
	 * 上一张发票号码
	 */
	private String preInvoiceNumber;

	/**
	 * 发票金额， 以元为单位
	 */
	private Double invoiceAmount;
	
	/**
	 * 发票类型（0: 商品发票 1: 运费发票）
	 */
	private String invoiceType;

	/**
	 * 销售商Id
	 */
	private Long merchantId;
	
	/**
	 * DO单头信息实体
	 */	
	private DeliveryOrderHeader deliveryOrderHeader;
	
	/**
	 * SO_CODE
	 */
	private String soCode;
	
	/**
	 * 原始单据ID 
	 */
	private String origId;
	
	/**
	 * 发票打印标识
	 */
	private Integer invoicePrintFlag;
	
	/**
	 * 接收人地址
	 */
	private String receiverAddr;
	
	/**
	 * 接收人邮编
	 */
	private String receiverPostCode;
	
	/**
	 * 接收人手机
	 */
	private String receiverMobile;
	
	/**
	 * 发票接收人电话
	 */
	private String receiverTel;
	
	/**
	 * 发票接收人
	 */
	private String receiverName;
	
	/**
	 * 备注
	 */
	private String notes;
	
    /**
     * 发票金额， 大写
     */
    @SuppressWarnings("unused")
    private String invoiceAmountRbm;
	
    /**
     * 序号
     */
    private Integer sortBy;
    
    /**
     * 子公司代码
     */
    private String branchCode;
    
    /**
     * 子公司名称	
     */
    private String branchName;
    
    /**
     * 税号
     */
    private String taxNo;
	/**
	 * 购买方纳税人识别号
	 */
	private String buyerTaxNo;
    
	/**
	 * 发票明细
	 */
	private List<InvoiceDetail> invoiceDetails;
	
	/**
     * DoHeader的ID
     */
    private Long doHeaderId;

	//发票代码
	private String invoiceCode;
	//上一张发票代码
	private String preInvoiceCode;

	//校验码
	private String checkCode;

	//电子发票接口请求流水号
	private String reqSequenceNo;
	
	//电子发票接口请求流水号
		private String chedSequenceNo;

	//发票请求错误码（电子发票的上传、红冲接口的错误异常）
	private String reqErrorCode;

	//发票是否需要打印
	private Boolean needPrint;

    //失败次数，电子发票接口的调用失败次数
    private Integer failedNum;

    //发票状态
    private InvoiceStatus invoiceStatus;
    // 对应的正票id
    private Long positiveInvoiceId;
    
    // 1：普通开具 2：整单冲红 3：换票冲红 4：换票开具
    private Integer eInvoiceIssueType;
    
    private String invoiceUrl;
	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)  
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	@Column(name = "INVOICE_TITLE")
	public String getInvoiceTitle() {
		return invoiceTitle;
	}
	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}
	
	@Column(name = "INVOICE_CONTENT")
	public String getInvoiceContent() {
		return invoiceContent;
	}
	public void setInvoiceContent(String invoiceContent) {
		this.invoiceContent = invoiceContent;
	}
	
	@Column(name = "INVOICE_NUMBER")
	public String getInvoiceNumber() {
		return invoiceNumber;
	}
	public void setInvoiceNumber(String invoiceNumber) {
		this.invoiceNumber = invoiceNumber;
	}
	
	@Column(name = "INVOICE_AMOUNT")
	public Double getInvoiceAmount() {
		return invoiceAmount;
	}
	public void setInvoiceAmount(Double invoiceAmount) {
		this.invoiceAmount = invoiceAmount;
	}
	
	@Column(name = "INVOICE_TYPE")
	public String getInvoiceType() {
		return invoiceType;
	}
	public void setInvoiceType(String invoiceType) {
		this.invoiceType = invoiceType;
	}
	
	@Column(name = "MERCHANT_ID")
	public Long getMerchantId() {
		return merchantId;
	}
	public void setMerchantId(Long merchantId) {
		this.merchantId = merchantId;
	}
	
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "DO_HEADER_ID")
	//这里不要加insertable = false, updatable = false
	public DeliveryOrderHeader getDeliveryOrderHeader() {
		return deliveryOrderHeader;
	}
	public void setDeliveryOrderHeader(DeliveryOrderHeader deliveryOrderHeader) {
		this.deliveryOrderHeader = deliveryOrderHeader;
	}
	
	@Column(name = "SO_CODE")
	public String getSoCode() {
		return soCode;
	}
	public void setSoCode(String soCode) {
		this.soCode = soCode;
	}
	
	@Column(name = "ORIG_ID")
	public String getOrigId() {
		return origId;
	}
	public void setOrigId(String origId) {
		this.origId = origId;
	}
	
	@Column(name = "INVOICE_PRINT_FLAG")
	public Integer getInvoicePrintFlag() {
		return invoicePrintFlag;
	}
	public void setInvoicePrintFlag(Integer invoicePrintFlag) {
		this.invoicePrintFlag = invoicePrintFlag;
	}
	
	@Column(name = "RECEIVER_ADDR")
	public String getReceiverAddr() {
		return receiverAddr;
	}
	public void setReceiverAddr(String receiverAddr) {
		this.receiverAddr = receiverAddr;
	}
	
	@Column(name = "RECEIVER_POST_CODE")
	public String getReceiverPostCode() {
		return receiverPostCode;
	}
	public void setReceiverPostCode(String receiverPostCode) {
		this.receiverPostCode = receiverPostCode;
	}
	
	@Column(name = "RECEIVER_MOBILE")
	public String getReceiverMobile() {
		return receiverMobile;
	}
	public void setReceiverMobile(String receiverMobile) {
		this.receiverMobile = receiverMobile;
	}
	
	@Column(name = "RECEIVER_TEL")
	public String getReceiverTel() {
		return receiverTel;
	}
	public void setReceiverTel(String receiverTel) {
		this.receiverTel = receiverTel;
	}
	
	@Column(name = "RECEIVER_NAME")
	public String getReceiverName() {
		return receiverName;
	}
	public void setReceiverName(String receiverName) {
		this.receiverName = receiverName;
	}
	
	@Column(name = "NOTES")
	public String getNotes() {
		return notes;
	}
	public void setNotes(String notes) {
		this.notes = notes;
	}
	
    @Transient
    public String getInvoiceAmountRbm() {
        return StringUtil.numToRMBStr(invoiceAmount);
    }

    public void setInvoiceAmountRbm(String invoiceAmountRbm) {
        this.invoiceAmountRbm = invoiceAmountRbm;
    }
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "invoiceHeader")
	@OrderBy("sortBy ASC")
    @Where(clause = " IS_DELETED = 0 ")
    @Cascade(value={CascadeType.SAVE_UPDATE}) 
	public List<InvoiceDetail> getInvoiceDetails() {
		return invoiceDetails;
	}
	
	public void setInvoiceDetails(List<InvoiceDetail> invoiceDetails) {
		this.invoiceDetails = invoiceDetails;
	}
	@Column(name = "SORT_BY")
	public Integer getSortBy() {
		return sortBy;
	}
	public void setSortBy(Integer sortBy) {
		this.sortBy = sortBy;
	}
	@Column(name = "BRANCH_CODE")
	public String getBranchCode() {
		return branchCode;
	}
	public void setBranchCode(String branchCode) {
		this.branchCode = branchCode;
	}
	@Column(name = "BRANCH_NAME")
	public String getBranchName() {
		return branchName;
	}
	public void setBranchName(String branchName) {
		this.branchName = branchName;
	}
	@Column(name = "TAX_NO")
	public String getTaxNo() {
		return taxNo;
	}
	public void setTaxNo(String taxNo) {
		this.taxNo = taxNo;
	}
	
	@Column(name = "DO_HEADER_ID",insertable = false, updatable = false)
    public Long getDoHeaderId() {
        return doHeaderId;
    }
    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }

	@Column(name = "INVOICE_CODE")
	public String getInvoiceCode() {
		return invoiceCode;
	}

	public void setInvoiceCode(String invoiceCode) {
		this.invoiceCode = invoiceCode;
	}

	@Column(name = "CHECK_CODE")
	public String getCheckCode() {
		return checkCode;
	}

	public void setCheckCode(String checkCode) {
		this.checkCode = checkCode;
	}

    @Column(name = "REQ_SEQUENCE_NO")
    public String getReqSequenceNo() {
        return reqSequenceNo;
    }

    public void setReqSequenceNo(String reqSequenceNo) {
        this.reqSequenceNo = reqSequenceNo;
    }

    @Column(name = "REQ_ERROR_CODE")
    public String getReqErrorCode() {
        return reqErrorCode;
    }

    public void setReqErrorCode(String reqErrorCode) {
        this.reqErrorCode = reqErrorCode;
    }

    @Column(name = "NEED_PRINT")
    public Boolean getNeedPrint() {
        return needPrint;
    }

    public void setNeedPrint(Boolean needPrint) {
        this.needPrint = needPrint;
    }

    @Column(name = "FAILED_NUM")
    public Integer getFailedNum() {
        return failedNum;
    }

    public void setFailedNum(Integer failedNum) {
        this.failedNum = failedNum;
    }

    @Column(name = "INVOICE_STATUS")
    @Enumerated(EnumType.STRING)
    public InvoiceStatus getInvoiceStatus() {
        return invoiceStatus;
    }

    public void setInvoiceStatus(InvoiceStatus invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }
    
    @Column(name = "positive_invoice_id")
	public Long getPositiveInvoiceId() {
		return positiveInvoiceId;
	}
	public void setPositiveInvoiceId(Long positiveInvoiceId) {
		this.positiveInvoiceId = positiveInvoiceId;
	}
	
	@Column(name = "e_invoice_issue_type")
	public Integer geteInvoiceIssueType() {
		return eInvoiceIssueType;
	}
	public void seteInvoiceIssueType(Integer eInvoiceIssueType) {
		this.eInvoiceIssueType = eInvoiceIssueType;
	}
	
	@Column(name = "ched_sequence_no")
	public String getChedSequenceNo() {
		return chedSequenceNo;
	}
	public void setChedSequenceNo(String chedSequenceNo) {
		this.chedSequenceNo = chedSequenceNo;
	}
	@Column(name = "invoice_url")
	public String getInvoiceUrl() {
		return invoiceUrl;
	}
	public void setInvoiceUrl(String invoiceUrl) {
		this.invoiceUrl = invoiceUrl;
	}

	@Column(name = "buyer_tax_no")
	public String getBuyerTaxNo() {
		return buyerTaxNo;
	}

	public void setBuyerTaxNo(String buyerTaxNo) {
		this.buyerTaxNo = buyerTaxNo;
	}

    @Column(name = "pre_invoice_number")
    public String getPreInvoiceNumber() {
        return preInvoiceNumber;
    }

    public void setPreInvoiceNumber(String preInvoiceNumber) {
        this.preInvoiceNumber = preInvoiceNumber;
    }

    @Column(name = "pre_invoice_code")
    public String getPreInvoiceCode() {
        return preInvoiceCode;
    }

    public void setPreInvoiceCode(String preInvoiceCode) {
        this.preInvoiceCode = preInvoiceCode;
    }
}
