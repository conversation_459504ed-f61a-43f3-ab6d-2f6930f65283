package com.daxia.wms.delivery.print.dto;

import java.math.BigDecimal;

@lombok.extern.slf4j.Slf4j
public class PrintHoldDoLabelDetailDTO {
	private Long skuId;
	
	private String productName;
	
	private String productBarcode;

	private BigDecimal qty;
	
	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getProductBarcode() {
		return productBarcode;
	}

	public void setProductBarcode(String productBarcode) {
		this.productBarcode = productBarcode;
	}

	public BigDecimal getQty() {
		return qty;
	}

	public void setQty(BigDecimal qty) {
		this.qty = qty;
	}
}
