package com.daxia.wms.delivery.invoice.dto;

/**
 * 航天信息：开具发票头
 * <fp>
 * <FPZL></FPZL>
 * <GFMC></GFMC>
 * <GFSH></GFSH>
 * <GFDZDH></GFDZDH>
 * <GFYHZH></GFYHZH>
 * <BZ></BZ>
 * <SKR></SKR>
 * <FHR></FHR>
 * <KPR></KPR>
 * <XFYHZH></XFYHZH>
 * <XFDZDH></XFDZDH>
 * <QDBZ></QDBZ>
 * <XSDJBH></XSDJBH>
 * <KPBZ></KPBZ>
 * </fp>
 */
@lombok.extern.slf4j.Slf4j
public class InvoiceFp {
    public static enum InvoiceType {
        ZY("0"),//专用发票
        PT("2"),//普通发票
        HY("11"),//普通发票
        JD("12"),//机动车票
        JP("41"),//卷票
        DZ("51");//电子发票

        InvoiceType(String value) {
            this.value = value;
        }

        private final String value;

        public String getValue() {
            return value;
        }
    }

    private String FPZL = "";//发票种类,必填 固定值：0：专用发票 2：普通发票 11：货运发票 12：机动车票 51：电子发票
    private String GFMC = "";//购方名称,必填 100
    private String GFSH = "";//购方税号,必填 20
    private String GFDZDH = "";//购方地址电话,非必填 100
    private String GFYHZH = "";//购方银行账户,非必填 100
    /**
     * 专票开具红字发票时，该字段需要填写如下内容：
     * 开具红字增值税专用发票信息表编号XXXXXXXXXXXXXXXX
     * XXXX表示信息表编号，备注内容为以上那句话，不能有差错，否则无法开具红字发票。
     * 普票开具负数发票时，该字段需要填写如下内容：
     * 对应正数发票代码:YYYYYYYY号码:ZZZZZZ
     * YYYYY：是发票代码
     * ZZZZ:是发票号码
     * 普票负数票备注除这句内容外可以有其他内容
     */
    private String BZ = "";//备注,非必填 230
    private String SKR = "";//收款人,必填 8
    private String FHR = "";//复核人,必填 8
    private String KPR = "";//开票人,必填 8
    private String XFYHZH = "";//销方银行账户,非必填
    private String XFDZDH = "";//销方地址电话,非必填
    private String QDBZ = "";//清单标志,必填 固定值0：不开具清单 1：开具清单
    private String XSDJBH = "";//销售单据编号，非必填 开具红字发票时，该字段填写信息表编号，为必填项
    private String KPBZ = "";//开票标志，必填 固定值 0：开票 1：校验


    public String getFPZL() {
        return FPZL;
    }

    public void setFPZL(String FPZL) {
        this.FPZL = FPZL;
    }

    public String getGFMC() {
        return GFMC;
    }

    public void setGFMC(String GFMC) {
        this.GFMC = GFMC;
    }

    public String getGFSH() {
        return GFSH;
    }

    public void setGFSH(String GFSH) {
        this.GFSH = GFSH;
    }

    public String getGFDZDH() {
        return GFDZDH;
    }

    public void setGFDZDH(String GFDZDH) {
        this.GFDZDH = GFDZDH;
    }

    public String getGFYHZH() {
        return GFYHZH;
    }

    public void setGFYHZH(String GFYHZH) {
        this.GFYHZH = GFYHZH;
    }

    public String getBZ() {
        return BZ;
    }

    public void setBZ(String BZ) {
        this.BZ = BZ;
    }

    public String getSKR() {
        return SKR;
    }

    public void setSKR(String SKR) {
        this.SKR = SKR;
    }

    public String getFHR() {
        return FHR;
    }

    public void setFHR(String FHR) {
        this.FHR = FHR;
    }

    public String getKPR() {
        return KPR;
    }

    public void setKPR(String KPR) {
        this.KPR = KPR;
    }

    public String getXFYHZH() {
        return XFYHZH;
    }

    public void setXFYHZH(String XFYHZH) {
        this.XFYHZH = XFYHZH;
    }

    public String getXFDZDH() {
        return XFDZDH;
    }

    public void setXFDZDH(String XFDZDH) {
        this.XFDZDH = XFDZDH;
    }

    public String getQDBZ() {
        return QDBZ;
    }

    public void setQDBZ(String QDBZ) {
        this.QDBZ = QDBZ;
    }

    public String getXSDJBH() {
        return XSDJBH;
    }

    public void setXSDJBH(String XSDJBH) {
        this.XSDJBH = XSDJBH;
    }

    public String getKPBZ() {
        return KPBZ;
    }

    public void setKPBZ(String KPBZ) {
        this.KPBZ = KPBZ;
    }

}
