package com.daxia.wms.delivery.invoice.service;

import java.util.List;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.invoice.entity.InvoiceBook;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;
import com.daxia.wms.delivery.invoice.filter.InvoiceNoFilter;

/**
 * 发票号service
 */
public interface InvoiceNoService {
	
    /**
     * 根据发票薄Id分页查询发票号
     * 
     * @param invoiceBookId
     * @return
     */
	public DataPage<InvoiceNo> findInvoiceNoByInvoiceBookId(Long invoiceBookId, int startIndex, int pageSize);
	
    /**
     * 发票号申请遗失
     * 
     * @param invoiceNoFm
     * @param invoiceNoTo
     * @return
     */
	public InvoiceBook invoiceNoLost(Long invoiceBookId, String invoiceNoFm, String invoiceNoTo);

    /**
     * 通过发票号码获取发票号对象
     * 
     * @param InvNo
     *            发票号码
     * @return
     */
	public InvoiceNo getInvoiceNoObjByInvNo(String InvNo);

    /**
     * 发票号绑定单据
     * 
     * @param invoiceNo
     * @param invoiceCode
     * @param doNo
     * @param billNo
     */
	public void bindInvoiceNoWithBill(String invoiceNo, String invoiceCode,
			String doNo, Integer billNo);

    /**
     * 绑定发票头与发票号码
     */
    public void bind(InvoiceHeader invoiceHeader, InvoiceNo invoiceNo);

    /**
     * 根据filter查询发票信息
     * 
     * @param filter
     * @return
     */
    public List<InvoiceNo> findByFilter(InvoiceNoFilter filter);
    
    /**
     * 通过发票号码获取发票号对象
     * @param InvNo  发票号码
     * @param doNo   DO编号 
     * @return
     */
	public InvoiceNo getInvoiceNoObjByInvNoAndDoNo(String InvNo,String doNo);
	
    /**
     * 通过发票号码获取发票号对象
     * @param InvNo			发票号码
     * @param invHeaderId	invoiceHeaderId 
     * @return
     */
	public InvoiceNo getInvoiceNoObjByInvNoAndInvHeaderId(String InvNo,Long invHeaderId);
	
	/**
     * 根据发票代码、发票起止号码查询发票实体信息
     * @param invoiceCode
     * @param invoiceNoFrom
     * @param invoiceNoTo
     * @return
     */
    public List<InvoiceNo> getInvoiceNoList(String invoiceCode, String invoiceNoFrom, String invoiceNoTo);
}
