package com.daxia.wms.delivery.crossorder.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.master.entity.Sku;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "doc_cross_seed_detail")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@BatchSize(size = 20)
@SQLDelete(sql = "update doc_cross_seed_detail set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class CrossSeedDetail extends WhBaseEntity {

    private static final long serialVersionUID = -8268278131131376063L;

    // 主键
    private Long id;

    /**
     * 越库分播单头id
     */
    private Long headerId;

    /**
     * 订单明细id
     */
    private Long doHeaderId;

    /**
     * 订单明细id
     */
    private Long doDetailId;

    /**
     * 订单号
     */
    private String doNo;

    /**
     * 分播区格号
     */
    private String sortGridNo;

    /**
     * 分播区每个DO的容器号
     */
    private String containerNo;

    /**
     * 客户名
     */
    private String consigneeName;

    /**
     * 商品id
     */
    private Long skuId;

    /**
     * 批次号
     */
    private String lotNo;

    /**
     * 批号
     */
    private String lotatt05;

    /**
     * 生产日期
     */
    private String lotatt01;

    /**
     * 失效日期
     */
    private String lotatt02;

    /**
     * 期望数
     */
    private BigDecimal expectedQty;

    /**
     * 预分播数量
     */
    private BigDecimal allocatedQty;

    /**
     * 实际分播数量
     */
    private BigDecimal sortedQty;

    private Integer sortedFlag;

    /**
     * 实际复核数
     */
    private BigDecimal packedQty;

    private Long stockId;

    private CrossSeedHeader header;

    private DeliveryOrderDetail deliveryOrderDetail;

    private DeliveryOrderHeader deliveryOrderHeader;

    private Sku sku;

    private Integer isDeleted;

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "autoIdGenerator")
    @GenericGenerator(name = "autoIdGenerator", strategy = "com.daxia.framework.common.dao.AutoIdentityGenerator")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "header_id")
    public Long getHeaderId() {
        return headerId;
    }

    public void setHeaderId(Long headerId) {
        this.headerId = headerId;
    }

    @Column(name = "do_header_id")
    public Long getDoHeaderId() {
        return doHeaderId;
    }

    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }

    @Column(name = "do_detail_id")
    public Long getDoDetailId() {
        return doDetailId;
    }

    public void setDoDetailId(Long doDetailId) {
        this.doDetailId = doDetailId;
    }

    @Column(name = "do_no")
    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo == null ? null : doNo.trim();
    }

    @Column(name = "sort_grid_no")
    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo == null ? null : sortGridNo.trim();
    }

    @Column(name = "container_no")
    public String getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo == null ? null : containerNo.trim();
    }

    @Column(name = "consignee_name")
    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName == null ? null : consigneeName.trim();
    }

    @Column(name = "sku_id")
    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    @Column(name = "lot_no")
    public String getLotNo() {
        return lotNo;
    }

    public void setLotNo(String lotNo) {
        this.lotNo = lotNo == null ? null : lotNo.trim();
    }

    @Column(name = "lotatt05")
    public String getLotatt05() {
        return lotatt05;
    }

    public void setLotatt05(String lotatt05) {
        this.lotatt05 = lotatt05 == null ? null : lotatt05.trim();
    }

    @Column(name = "lotatt01")
    public String getLotatt01() {
        return lotatt01;
    }

    public void setLotatt01(String lotatt01) {
        this.lotatt01 = lotatt01 == null ? null : lotatt01.trim();
    }

    @Column(name = "lotatt02")
    public String getLotatt02() {
        return lotatt02;
    }

    public void setLotatt02(String lotatt02) {
        this.lotatt02 = lotatt02 == null ? null : lotatt02.trim();
    }

    @Column(name = "expected_qty")
    public BigDecimal getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(BigDecimal expectedQty) {
        this.expectedQty = expectedQty;
    }

    @Column(name = "allocated_qty")
    public BigDecimal getAllocatedQty() {
        return allocatedQty;
    }

    public void setAllocatedQty(BigDecimal allocatedQty) {
        this.allocatedQty = allocatedQty;
    }

    @Column(name = "sorted_qty")
    public BigDecimal getSortedQty() {
        return sortedQty;
    }

    public void setSortedQty(BigDecimal sortedQty) {
        this.sortedQty = sortedQty;
    }

    @Column(name = "sorted_flag")
    public Integer getSortedFlag() {
        return sortedFlag;
    }

    public void setSortedFlag(Integer sortedFlag) {
        this.sortedFlag = sortedFlag;
    }

    @Column(name = "packed_qty")
    public BigDecimal getPackedQty() {
        return packedQty;
    }

    public void setPackedQty(BigDecimal packedQty) {
        this.packedQty = packedQty;
    }

    @Column(name = "stock_id")
    public Long getStockId() {
        return stockId;
    }

    public void setStockId(Long stockId) {
        this.stockId = stockId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "header_id", insertable = false, updatable = false)
    @Where(clause = " is_deleted = 0 ")
    public CrossSeedHeader getHeader() {
        return header;
    }

    public void setHeader(CrossSeedHeader header) {
        this.header = header;
    }


    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "do_detail_id", insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public DeliveryOrderDetail getDeliveryOrderDetail() {
        return deliveryOrderDetail;
    }

    public void setDeliveryOrderDetail(DeliveryOrderDetail deliveryOrderDetail) {
        this.deliveryOrderDetail = deliveryOrderDetail;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "do_header_id", insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public DeliveryOrderHeader getDeliveryOrderHeader() {
        return deliveryOrderHeader;
    }

    public void setDeliveryOrderHeader(DeliveryOrderHeader deliveryOrderHeader) {
        this.deliveryOrderHeader = deliveryOrderHeader;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sku_id", insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public Sku getSku() {
        return sku;
    }

    public void setSku(Sku sku) {
        this.sku = sku;
    }

    @Column(name = "is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}