package com.daxia.wms.delivery.deliveryorder.action;


import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.wms.OrderLogConstants;
import com.daxia.wms.master.dto.OrderLogDTO;
import com.daxia.wms.master.filter.OrderLogFilter;
import com.daxia.wms.delivery.deliveryorder.service.OrderLogService;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import java.sql.Timestamp;
import java.util.Arrays;


/**
 * 订单日志查询页面
 */
@Name("com.daxia.wms.delivery.orderLogAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class OrderLogAction extends PagedListBean<OrderLogDTO> {

    private static final long serialVersionUID = -3634807178853785342L;

    private OrderLogFilter orderLogFilter;

    private String queryBy;

    @In
    private OrderLogService orderLogService;

    public OrderLogAction() {
        super();
        this.orderLogFilter = new OrderLogFilter();
        orderLogFilter.setOperateTimeFrom(DateUtil.dateAdd("dd", new Timestamp(System.currentTimeMillis()), -7));
    }

    @Override
    public void query() {
        this.buildOrderFilterMap(orderLogFilter);
        orderLogFilter.getOrderByMap().put("operateTime", "desc");
        if (OrderLogFilter.QueryType.BY_DO_LOG.getValue().equals(queryBy)) {
            orderLogFilter.setExt1(null);
            orderLogFilter.setExt2(null);
            orderLogFilter.setExt3(null);
            orderLogFilter.setExt4(null);
            orderLogFilter.setExt5(null);
            orderLogFilter.setOperateTypeList(null);
        } else {
            orderLogFilter.setOperateTypeList(Arrays.asList(OrderLogConstants.OrderLogType.MERGE_IN.getValue(), OrderLogConstants.OrderLogType.MERGE_OUT.getValue()));
            orderLogFilter.setStatusFrom(null);
            orderLogFilter.setStatusTo(null);
        }
        DataPage<OrderLogDTO> dataPage = orderLogService.query(orderLogFilter, getStartIndex(), getPageSize());
        populateValues(dataPage);
    }


    /**
     * 点击查询按钮
     */
    @Override
    public void buttonQuery() {
        this.dataPage.setPageIndex(-1);
        this.query();
    }

    public String getQueryBy() {
        return queryBy;
    }

    public void setQueryBy(String queryBy) {
        this.queryBy = queryBy;
    }

    public OrderLogFilter getOrderLogFilter() {
        return orderLogFilter;
    }

    public void setOrderLogFilter(OrderLogFilter orderLogFilter) {
        this.orderLogFilter = orderLogFilter;
    }
}
