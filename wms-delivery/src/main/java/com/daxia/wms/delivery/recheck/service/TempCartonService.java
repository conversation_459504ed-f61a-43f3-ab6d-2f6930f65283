package com.daxia.wms.delivery.recheck.service;

import com.daxia.wms.delivery.recheck.entity.TempCarton;
import org.jboss.seam.annotations.Transactional;

import java.util.List;

/**
 * 
 * TempCarton业务
 */
public interface TempCartonService {
 
    public TempCarton getByDoId(Long doHeaderId);

    List<TempCarton> findByDoIdList(List<Long> doIdList);

	void saveOrUpdate(TempCarton tempCarton);

	public TempCarton generateTempCarton(Long doId);

    TempCarton get(Long id);
    
    boolean existEmptyCartonNo(Long id);

    void removeByDoId(Long doId);

    void removeByDoId(Long doId, Long carrierId);

    void removeByWaveId(Long waveId);

    List<Long> findCartonIdsForPrint(Long doHeaderId);

    void updatePrintFlagByDoIdList(List<Long> doIdList);

    boolean existRepeatCartonNo(Long waveId);

    TempCarton getByCartonNo(String orderNo);

    void savePrintLog(List<Long> doIdList,String updateBy);

    boolean isExists(Long id);

    void refreshFailOrder();
}
