package com.daxia.wms.delivery.sort.action;

import com.daxia.framework.common.action.ActionBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.util.*;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.*;
import com.daxia.wms.PageConfig;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.container.entity.ContainerLog;
import com.daxia.wms.delivery.container.service.ContainerLogService;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.delivery.deliveryorder.dto.DoHeaderDto;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.print.helper.WaybillPrintHelper;
import com.daxia.wms.delivery.print.service.PrintDoService;
import com.daxia.wms.delivery.print.service.carton.PrintCartonDispatcher;
import com.daxia.wms.delivery.recheck.service.TempCartonService;
import com.daxia.wms.delivery.sort.dto.DoSortContainerDTO;
import com.daxia.wms.delivery.sort.dto.SortBinBindWaveDTO;
import com.daxia.wms.delivery.sort.service.SortingService;
import com.daxia.wms.delivery.util.SortContainerUtil;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.master.dto.AutoCompleteDTO;
import com.daxia.wms.master.entity.Container;
import com.daxia.wms.master.entity.Sku;
import com.daxia.wms.master.entity.SortingBin;
import com.daxia.wms.master.service.*;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.stock.stock.service.TrsPickLogService;
import com.daxia.wms.util.FileImportTemplate;
import com.daxia.wms.util.Switch;
import com.daxia.wms.util.SwitchUtils;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.security.Restrict;
import org.jboss.seam.security.Identity;

import java.math.BigDecimal;
import java.util.*;

import static com.daxia.framework.common.util.SystemConfig.getConfigValueInt;

/**
 * 分拣
 */
@Name("com.daxia.wms.delivery.sortingAction")
@Restrict("#{identity.hasPermission('delivery.sorting')}")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class SortingAction extends ActionBean {
	@In
    private ExpFacadeService expFacadeService;

	private static final long serialVersionUID = -3105842460469767185L;
	private String sotringBinNo;
	private String staffNo;
	private String waveNo;
	private String queryPickContainerWave;
	private String barcode;
	private String operateCode;
	private String skuCode;
	//分拣柜ID
	private Long sortingBinId;
	private WaveHeader wave;
	private SortingBin sortingBin;
	private List<DeliveryOrderDetail> doDetailList;
    private List<List<AutoCompleteDTO>> noEanSkuList = new ArrayList<List<AutoCompleteDTO>>();
	private Long sortGridNo = null;
	private Long skuId = null;
	private Integer dotFlag;
	private boolean isMultiProd = false;
	private boolean isWaveSortingCompleted = false;
	private boolean isContractPhoneDo = false;
	private List<Sku> skuList = new ArrayList<Sku>();
	private List<Long> skuIds = new ArrayList<Long>();
	private boolean isSorted = true;
	private boolean needDetail = false;
	private List<DeliveryOrderHeader> sortedNumList;
	private List<DeliveryOrderHeader> partSortedNumList;
	private List<DeliveryOrderHeader> unUsedSortedNumList;
	private List<DeliveryOrderHeader> holdGridNumList;
	private List<DeliveryOrderHeader> cancelGridNumList;
	private List<DoSortContainerDTO> doSortContainerDTOList = new ArrayList<DoSortContainerDTO>();
	private String pickContainerStr;
	private BigDecimal doCount;
	private String skuHelperData;
	
	/**
	 * 新分拣筐编号
	 */
	private String newSortContainer;
	
	/**
	 * 是否能换分拣筐标识
	 */
	private Boolean canChangeSortContainer = false;
	
	/**
	 * 分拣筐对应的do
	 */
	private DoHeaderDto doHeader;
	
	/**
	 * 是否换箱成功标识
	 */
	private Boolean isChangeSuccess = false;
	
	private String pickContainerNo4Print;
	
	/**
	 * 打印数据
	 */
	private String printData = "[]";

	private String printContent;
	
	/**
	 * 分拣柜绑定正确波次
	 */
	private boolean bindRightWave;

	/**
	 * 当前分拣的发货单Id
	 */
	private Long currentDoId;
	/**
	 * 当前分拣的发货单中正分拣的商品待分拣数量，大于10时，用户可以批量分拣
	 */
	private BigDecimal pendingSortingNumber;
	/**
	 * 批量分拣时，用户输入的分拣数量
	 */
	private BigDecimal batchNumber = BigDecimal.ONE;
	/**
	 * 本次分拣数量
	 */
	private BigDecimal currentSortQty = BigDecimal.ZERO;
	/**
	 * 是否使用分拣筐
	 */
	private boolean needSortContainer = false;
	/**
	 * 是否使用新打印方式
	 */
	private boolean printInNewWay = false;

	/**
	 * 是否绑定分拣筐
	 */
	private boolean needBindContainer = false;
	
	/**
	 * 容器管理配置项
	 */
	private Integer containerMntFlag;
	/**
	 * 分拣箱编号
	 */
	private String sortContainer;
	/**
	 * 用于标记波次标记是否正确
	 */
	private boolean waveFlag;
	/**
	 * 用于分拣筐语音播放数字
	 */
	private Long sortContainerNo = null;
	 /**
     * 单DO波次是否需要集货
     */
    private Integer singleDoMergeCfg;

	@In
	SortingService sortingService;
	@In
	WaveService waveService;
	@In
	SkuService skuService;
	@In
	DeliveryOrderService deliveryOrderService;
	@In
	SortingBinService sortingBinService;
	@In
    TrsPickLogService trsPickLogService;

	@In
	private ContainerMgntService containerMgntService;
	
	@In
	private ContainerService containerService;

	@In
	private ContainerLogService containerLogService;
	
	@In
	private LaborService laborService;
	
	@In
	private SkuScanService skuScanService;

	@In
	private PrintCartonDispatcher printCartonDispatcher;

	@In(create = true)
	private PrintDoService printDoService;

	@In
	private TempCartonService tempCartonService;
	
	private Long doId;
	
	private List<String> doNumbers;
	
	/**
	 * 取消格号
	*/
	private String cancalSortingNo;
	
	private Integer invoiceCodeLength;
	
	private Integer invoiceNoLength;
	
	private Integer countPerRoll;
	
	private String invoiceTipMsg;
	
	private SortBinBindWaveDTO sortBinBindWaveDTO;

	private Integer needBatchNumber;

	private String printCartonCode;

	private String printDoCode;

	private String SUCESSS_OPERATE_CODE="111111";

	private String FAIL_OPERATE_CODE="000000";
	
	@Create
	public void initialize() {
		// 分拣是否支持打印卷式发票标记
		needSortContainer = deliveryOrderService.getNeedSortContainer();
		printInNewWay = deliveryOrderService.needPrintInNewWay();
		needBindContainer = false;
		waveFlag = false;
		bindRightWave = true;
		cancalSortingNo = null;
		this.containerMntFlag = containerMgntService.getContainerMgntStartFlag();
		singleDoMergeCfg = getConfigValueInt(Switch.LABOR_FORCE_SWITCH_SORT, ParamUtil.getCurrentWarehouseId());
		Integer cartonPrintPoint = PageConfig.getInt(ConfigKeys.CARTON_PRINT_POINT, Config.ConfigLevel.WAREHOUSE.getValue());
		if (cartonPrintPoint != null && !CartonPrintPoint.RECHECK.getValue().equals(cartonPrintPoint)) {
			printCartonCode = PageConfig.get(ConfigKeys.CARTON_PRINT_CODE, Config.ConfigLevel.WAREHOUSE.getValue());
		}
		printDoCode = PageConfig.get(ConfigKeys.PRINT_DO_CODE, Config.ConfigLevel.WAREHOUSE.getValue());
	}

	public SortingAction() {
		super();
		if (StringUtil.isEmpty(this.staffNo)) {
			Identity identity = (Identity) Component.getInstance("org.jboss.seam.security.identity");
			this.staffNo = identity.getCredentials().getUsername();
		}
	}
	
	public void logWorkTime() {
		// 记录工时
		laborService.logWorkTime(Constants.JOB_CODE_SORT);
	}
	
	public void getAndCheckWave() {
		//打印之前需要清空打印数据
		printContent = "";
		if (StringUtil.isEmpty(this.sotringBinNo)) {
			throw new DeliveryException(DeliveryException.SORTING_BIN_NO_ERRER);
		}
		wave = waveService.queryByDoNo(waveNo);
		if (wave == null) {
			wave = waveService.queryWaveByContainerNo(waveNo);
		}
		if (wave == null) {
			wave = waveService.queryWaveByNo(waveNo);
		}else{
			waveNo = wave.getWaveNo();
		}
		// 非拣货完成、分拣中状态
		if (!WaveStatus.ALLSORTED.getValue().equals(wave.getWaveStatus()) &&
				StringUtil.isNotIn(wave.getWaveStatus(), WaveStatus.ALLPICKED.getValue(), WaveStatus.PARTSORTED.getValue())) {
			throw new DeliveryException(DeliveryException.WAVE_STS_ERRER);
		}

		// 波次打印发货单
		Boolean needPrint = PageConfig.is(ConfigKeys.PRINT_DO_BY_WAVE, Config.ConfigLevel.WAREHOUSE.getValue());
		if (needPrint && !FlagUtil.has(wave.getPrintFlag().intValue(), WaveHeader.FLAG_PRINT_DO)) {
			printDoByWave();
		}

		//已分拣完成再分拣提示该波次已经分拣完成
		if(WaveStatus.ALLSORTED.getValue().equals(wave.getWaveStatus())){
			throw new DeliveryException(DeliveryException.WAVE_IS_SORTTED);
		}


		//处理单DO波次
		doSortSingleDoWave();

		//查询波次商品信息
		if (needDetail) {
			this.queryToTalSortingInfo();
		}
		
		loadSkuHelperData(wave.getId());
        loadSkuNoEanData(wave.getId());
	}

    private void loadSkuNoEanData(Long waveId) {
		//改为查询所有商品
        List<AutoCompleteDTO> dtoList = waveService.loadSkuByWaveId(waveId);
        List<AutoCompleteDTO> dtoList1 = new ArrayList<AutoCompleteDTO>();
        List<AutoCompleteDTO> dtoList2 = new ArrayList<AutoCompleteDTO>();
        int i = 0;
        for (AutoCompleteDTO dto : dtoList) {
            if (i % 2 == 0) {
                dtoList1.add(dto);
            }
            if (i % 2 == 1) {
                dtoList2.add(dto);
            }
            i++;
        }
		noEanSkuList.clear();
        noEanSkuList.add(dtoList1);
        noEanSkuList.add(dtoList2);
    }
	
	private void loadSkuHelperData(Long waveId) {
		List<AutoCompleteDTO> autoCompleteDTOS = waveService.findSkuHelperData(waveId);
		this.skuHelperData = JsonUtil.objToString(autoCompleteDTOS, true);
	}

	public void printCartons(){
		this.printData = "[]";
		printContent = "";
		if (StringUtil.isEmpty(waveNo)) {
			throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
		}
		this.wave = waveService.queryWaveByNo(waveNo);
		if (this.wave == null) {
			throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
		}
		if(tempCartonService.existEmptyCartonNo(wave.getId())){
			throw new DeliveryException(DeliveryException.PRINT_TEMPCARTON_NOEXIST);
		}
		List<Long> ids = new ArrayList<Long>();
		ids.add(wave.getId());
		List<PrintData> resultList =  printCartonDispatcher.printTempCarton(deliveryOrderService.getIdListByWaveIds(ids));
		printData = new Gson().toJson(resultList);
		printContent = WaybillPrintHelper.getPrintJs(resultList);
		waveService.updateWavePrintFlag(ids, WaveHeader.FLAG_PRINT_CARTON);
	}

	public void printDoByWave(){
		printContent = "";
		if (StringUtil.isEmpty(waveNo)) {
			throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
		}
		this.wave = waveService.queryWaveByNo(waveNo);
		if (this.wave == null) {
			throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
		}
		List<Long> ids = new ArrayList<Long>();
		ids.add(wave.getId());
		printContent = printDoService.genDataByWave(ids);
		printDoService.genDataByWave(ids);
		waveService.updateWavePrintFlag(ids, WaveHeader.FLAG_PRINT_DO);
	}
	@Loggable
	public void doSortingStart() throws Exception {
		//long current = System.currentTimeMillis();
		isMultiProd = false;
		isWaveSortingCompleted = false;
		skuId = null;
		currentSortQty = BigDecimal.ZERO;

		if (StringUtil.isEmpty(waveNo)) {
			throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
		}
		//查找波次
		this.wave = waveService.queryWaveByNo(waveNo);
		if (this.wave == null) {
			throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
		}
		if (wave.getWaveStatus().compareTo(Constants.WaveStatus.ALLSORTED.getValue()) == 0) {
			throw new DeliveryException(DeliveryException.WAVE_IS_SORTTED);
		} else if (wave.getWaveStatus().compareTo(Constants.WaveStatus.ALLPICKED.getValue()) < 0) {
			throw new DeliveryException(DeliveryException.WAVE_IS_NOTPICK);
		}
		//校验分拣柜
		validateSortingBin();
		sortingBinId = sortingBinService.getSortingBinIdByNo(sotringBinNo);
		
		Long waveId = this.wave.getId();
		
		//序列号转换
		this.barcode = skuScanService.transformScanCode(this.barcode.trim());
		
		this.sortingService.checkSkuInWave(waveId, barcode);
		this.skuIds = sortingService.querySkuByBarcodeInWave(waveId, barcode);
		if (skuIds.isEmpty()) {
			skuId = this.sortingService.findSkyByCodeAndWaveId(waveId, barcode);
		} else if (skuIds.size() > 1) {
			// 一码多品
			isMultiProd = true;
			return;
		} else {
			skuId = skuIds.get(0);
		}
		if (skuId == null) {
			throw new DeliveryException(DeliveryException.PRODUCT_ALREADY_SORTING);
		}
		
		continueSortingForMultiProd();
	}

	public void continueSortingForMultiProd() {
		//long current = System.currentTimeMillis();
		isWaveSortingCompleted = false;
		sortGridNo = null;
		DeliveryOrderDetail doDetail = sortingService.queryDoDetail(wave.getId(), skuId);
		dotFlag = NumberUtil.coverNull(doDetail.getSku().getDotFlag());
		this.doId = doDetail.getDoHeaderId();
		//获得分拣格号
		sortGridNo = Long.valueOf(doDetail.getDoHeader().getSortGridNo());
		this.pendingSortingNumber = sortingService.countUnSortingNumber(doDetail.getDoHeader(), skuId);
		needBatchNumber = SystemConfig.getConfigValueInt("needBatch.sort.number", ParamUtil.getCurrentWarehouseId());
		if (null == needBatchNumber) {
			needBatchNumber = Integer.valueOf(5);
		}
		//判断波次是否需要批量分拣
		if (this.pendingSortingNumber.compareTo(BigDecimal.valueOf(needBatchNumber)) >= 0) {
			return;
		}
		//判断波次是否需要批量分拣
		if (this.pendingSortingNumber.compareTo(BigDecimal.ONE) < 0 && this.pendingSortingNumber.compareTo(BigDecimal.ZERO) > 0) {
			return;
		}
		currentSortQty = BigDecimal.ONE;
		//逐件分拣
		sortingService.oneByOneSorting(doDetail, sortingBinId, staffNo, wave, skuId);
		
		 //如果是最后处理的unit（分拣，破损） 需要释放分拣柜和波次关系
		sortingService.dealWaveLastUnit(wave.getId());
		// 判断波次是否已经完成
		if (wave.getWaveStatus().equals(
				Constants.DoStatus.ALLSORTED.getValue().toString())) {
			isWaveSortingCompleted = true;
		}
		setGridStatus();
		this.barcode = null;
		//System.out.println(System.currentTimeMillis() - current);
	}
	
    public void setGridStatus() {
        sortedNumList = new ArrayList<DeliveryOrderHeader>();
        partSortedNumList = new ArrayList<DeliveryOrderHeader>();
        unUsedSortedNumList = new ArrayList<DeliveryOrderHeader>();
        holdGridNumList = new ArrayList<DeliveryOrderHeader>();
        cancelGridNumList = new ArrayList<DeliveryOrderHeader>();
        List<DeliveryOrderHeader> doHeaders = deliveryOrderService.qureyDoHeaderByWaveId(wave.getId());
        Collections.sort(doHeaders, new Comparator<DeliveryOrderHeader>() {

            @Override
            public int compare(DeliveryOrderHeader o1, DeliveryOrderHeader o2) {
                if (o1.getSortGridNo() != null && o2.getSortGridNo() != null) {
                    return o1.getSortGridNo().compareTo(o2.getSortGridNo());
                } else {
                    return 0;
                }
            }
        });
        for (DeliveryOrderHeader doHeader : doHeaders) {
            if (ReleaseStatus.HOLD.getValue().equals(doHeader.getReleaseStatus())) {
                Integer isPicked = trsPickLogService.checkIsPicked(doHeader.getId());
                doHeader.setIsPicked(isPicked);
            	if (doHeader.getNeedCancel()) {
            		cancelGridNumList.add(doHeader);
            	} else {
            		holdGridNumList.add(doHeader);
            	}
            } else if (DoStatus.ALLSORTED.getValue().compareTo(doHeader.getStatus()) <= 0) {
                sortedNumList.add(doHeader);
            } else if (DoStatus.PARTSORTED.getValue().equals(doHeader.getStatus())) {
                partSortedNumList.add(doHeader);
            } else {
                unUsedSortedNumList.add(doHeader);
            }
        }
    }

	public void queryWave() {
		this.wave = sortingService.queryWave(waveNo);
	}

	/**
	 * 查找分拣信息
	 */
	public void querySortingInfo() {
		this.doDetailList = sortingService.querySortingDoDetailOfWave(this.waveNo , this.isSorted);
		this.wave = doDetailList.get(0).getDoHeader().getWaveHeader();
	}

	/**
	 * 汇总分拣信息
	 */
	public void queryToTalSortingInfo() {
		List<DeliveryOrderDetail> detailList= sortingService.querySortingDoDetailOfWave(this.waveNo , this.isSorted);
		Map<Long,DeliveryOrderDetail> detailMap = new HashMap<Long, DeliveryOrderDetail>();
		for (DeliveryOrderDetail detail: detailList){
			if(detailMap.get(detail.getSkuId())!=null){
				DeliveryOrderDetail d = detailMap.get(detail.getSkuId());
				detail.setAllocatedQty(detail.getAllocatedQty().add(d.getAllocatedQty()));
			}
			detailMap.put(detail.getSkuId(),detail);
		}
		doDetailList = new ArrayList<DeliveryOrderDetail>();
		for (Map.Entry<Long, DeliveryOrderDetail> map : detailMap.entrySet()) {
			doDetailList.add(map.getValue());
		}
		this.wave = doDetailList.get(0).getDoHeader().getWaveHeader();
	}

	/**
	 * 调用此方法进行批量分拣
	 */
	@Loggable
	public void batchSorting() {
		sortingService.batchSorting(sortingBinId, staffNo, doId, wave, skuId,
				batchNumber);
		currentSortQty = batchNumber;
		//如果是最后处理的unit（分拣，破损） 需要释放分拣柜和波次关系
		sortingService.dealWaveLastUnit(wave.getId());
		
		// 判断波次是否已经完成
		if (wave.getWaveStatus().equals(
				Constants.DoStatus.ALLSORTED.getValue().toString())) {
			isWaveSortingCompleted = true;
			sortGridNo=null;
		}
		batchNumber = BigDecimal.ONE;
		setGridStatus();
	}
	
	/**
	 * 查找条码信息
	 */
	public void queryBarcodeListByScan() {
		this.skuList.clear();
		if (!ListUtil.isNullOrEmpty(skuIds)) {
		    this.skuList = skuService.querySkuList(this.skuIds);
		}
	}

	public void compelWaveSorting()throws Exception{
		this.isWaveSortingCompleted = false;
		if(StringUtil.isEmpty(operateCode)){
			this.sayMessage("请输入操作码!");
		}
		if(SUCESSS_OPERATE_CODE.equals(operateCode)){
			compelSorting();
			this.operateCode = null;
			this.doCount = null;
			this.doDetailList = null;
		} else if(FAIL_OPERATE_CODE.equals(operateCode)){
			this.doDetailList = null;
			this.waveNo = null;
			this.operateCode = null;
			this.doCount = null;
			this.isWaveSortingCompleted = true;
		}else{
			this.sayMessage("操作码错误!");
		}


}
	@Loggable
	public void compelSorting() throws Exception {
		this.isWaveSortingCompleted = false;
		this.sortingService.compelSorting(this.waveNo, staffNo);
		wave = waveService.getWaveHeaderByWaveNum(waveNo);

		setGridStatus();

		//isContractPhoneDo = DoUtil.isDoNeedRegister(wave.getDoHeaders().get(0));
		this.staffNo = null;
		this.barcode = null;
		this.waveNo = null;
		this.cancalSortingNo = null;
		this.isWaveSortingCompleted = true;

		this.sayMessage("该波次分拣成功！");
	}
	
	/**
	 * 绑定分拣箱
	 */
	public void bindSortContainer(){
		sortContainer = SortContainerUtil.subStringSortContainer(sortContainer);
		Container container = containerService.getContainerByNo(sortContainer);
		sortContainerNo = null;
		if(null == container){
			throw new DeliveryException(DeliveryException.SORT_CONTAINER_NOT_EXIT);
		}else if(!Constants.ContainerType.SORT_CONTAINER.getValue().equals(container.getContainerType().getContainerType())){
			throw new DeliveryException(DeliveryException.SORTCONTAINER_TYPE_ERROR);
		}
		//校验分拣柜
		validateSortingBin();
		sortingBinId = sortingBinService.getSortingBinIdByNo(sotringBinNo);		
				
		sortContainerNo = Long.valueOf(sortingService.bindSortContainer(container, waveNo,doNumbers));
		showCancalSortingNo();
		validateBindContainer();
	}
	
	/**
	 * 判断波次是否已绑定分拣箱
	 */
	public void checkWaveSortContainer(){
		isWaveSortingCompleted = false;
		sortContainerNo = null;
		sortContainer = null;
		barcode = null;
		checkWave();
		waveFlag = true;

		//绑定分拣筐显示取消分拣格号
		if(!WaveType.WAVE_NORMAL.getValue().equals(wave.getWaveType())){
			needBindContainer = false;
			return;
		}
		
		showCancalSortingNo();
		//不区波次状态 有未绑定的do 分拣框就可用
		doNumbers = sortingService.doNeedBingContatiner(wave.getId());
		validateBindContainer();
		
	}
	
	private void showCancalSortingNo(){
		cancalSortingNo = ""; 
		Long waveId =wave.getId();
		List<BigDecimal> sortList = sortingService.getSortNoListByWaveId(waveId);
		if(ListUtil.isNotEmpty(sortList)){
			String sortListStr = sortList.toString();
			List<Integer> sortIntList = new ArrayList<Integer>();
			for (String sortStr : sortListStr.substring(1,sortListStr.length()-1).replace(" ","").split(",")) {
				sortIntList.add(Integer.valueOf(sortStr));
			}
			for(int i = 1,len = sortIntList.get(0) + 1 ; i < len; i ++){
				if(!sortIntList.contains(i)){
					if(StringUtil.isNotEmpty(cancalSortingNo)){
						cancalSortingNo += ",";
					}
					cancalSortingNo += i;
				}
			}
			if(StringUtil.isNotEmpty(cancalSortingNo)){
				cancalSortingNo = "(取消格号：" + cancalSortingNo + ")";
			}
		}
	}
	
	private void checkWave() {
		if (StringUtil.isEmpty(this.sotringBinNo)) {
			throw new DeliveryException(DeliveryException.SORTING_BIN_NO_ERRER);
		}
		if (StringUtil.isNotEmpty(waveNo)) {
			String tmpWaveNo = containerLogService.getLaskBindDocNo(waveNo, BindDocType.WAVE.getValue());
			if (StringUtil.isNotEmpty(tmpWaveNo)) {
				waveNo = tmpWaveNo;
			}
		}
		try {
			this.wave = waveService.queryWaveByNo(waveNo);
		} catch (DeliveryException e) {
			waveFlag = false;
			needBindContainer = false;
			throw e;
		}
		//已分拣完成再分拣提示该波次已经分拣完成
		if(WaveStatus.ALLSORTED.getValue().equals(wave.getWaveStatus()) ){
			waveFlag = false;
			needBindContainer = false;
			throw new DeliveryException(DeliveryException.WAVE_IS_SORTTED);
		}
		// 非分拣完成、部分拣货状态
		if (StringUtil.isNotIn(wave.getWaveStatus(), WaveStatus.ALLPICKED.getValue(), WaveStatus.PARTSORTED.getValue())) {
			waveFlag = false;
			needBindContainer = false;
			throw new DeliveryException(DeliveryException.WAVE_STS_ERRER);
		}
	}

	/**
	 * 检验是否需要再次绑定分拣箱
	 */
	private void validateBindContainer(){
		if (doNumbers.size() > 0) {
			needBindContainer = true;
		} else {
			needBindContainer = false;
			//处理单DO波次
			doSortSingleDoWave();
		}
	}
	
	/**
	 * 单DO波次直接分拣完成
	 */
	private void doSortSingleDoWave() {
		//查找波次
		wave = waveService.queryWaveByNo(waveNo);
		//如果波次类型不在在配置项里 则也走单DO波次直接分拣完成的流程
		Boolean isCfgWaveType = SwitchUtils.isInWaveTypeCfg(wave.getWaveType(), wave.getAutoType());
		if (Constants.YesNo.YES.getValue().equals(singleDoMergeCfg) || !isCfgWaveType) {
			Integer doCountInWave = wave.getDoCount();
			if (null != doCountInWave && doCountInWave.intValue() == 1) {
				isContractPhoneDo = false;
				this.isWaveSortingCompleted = false;
				this.sortingService.compelSorting(waveNo,staffNo);
				
				wave = waveService.getWaveHeaderByWaveNum(waveNo);
				//isContractPhoneDo = DoUtil.isDoNeedRegister(wave.getDoHeaders().get(0));
				this.isWaveSortingCompleted = true;
				this.barcode = null;
				this.waveNo = null;
				this.sortContainer = null;
				this.cancalSortingNo = null;
				this.sayMessage("分拣完成，单DO波次");
			}
		}
	}
	
	/**
	 * 响应页面点击缺货按钮的操作
	 * @throws Exception
	 */
	public void lack() throws Exception {
		List<String> doNoList = this.sortingService.lack(this.waveNo, this.staffNo);
		List<Long> doHeaderIdList = new ArrayList<Long>();
		for(String doNo : doNoList){
			DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(doNo);
			doHeaderIdList.add(doHeader.getId());
		}
		/**冻结do时调scs重新计算预计出库时间接口*/
		if(doHeaderIdList != null && doHeaderIdList.size() > 0){
			expFacadeService.sendDoReleaseOrHold2ScsBatch(doHeaderIdList);
		}
		sayMessage(doNoList+"已做缺货处理。");
	}
	
	public void dmBySku() {
	    isMultiProd = false;
	    skuIds.clear();
	    if (StringUtil.isEmpty(waveNo)) {
            throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
        }
        //查找波次
        this.wave = waveService.queryWaveByNo(waveNo);
        if (this.wave == null) {
            throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
        }
        
        if (wave.getWaveStatus().compareTo(Constants.WaveStatus.ALLSORTED.getValue()) == 0) {
            throw new DeliveryException(DeliveryException.WAVE_IS_SORTTED);
         } else if (wave.getWaveStatus().compareTo(Constants.WaveStatus.ALLPICKED.getValue()) < 0){
            throw new DeliveryException(DeliveryException.WAVE_IS_NOTPICK);
        }
        
        Long waveId = wave.getId();
	    this.sortingService.checkSkuInWave(waveId, skuCode);
        this.skuIds = sortingService.querySkuByBarcodeInWave(waveId, skuCode);
        if (skuIds.isEmpty()) {
            skuId = null;
            skuId = this.sortingService.findSkyByCodeAndWaveId(waveId, skuCode);
        } else if (skuIds.size()>1) {
            // 一码多品
            isMultiProd = true;
            return;
        } else {
            skuId = skuIds.get(0);
        }
        if (skuId == null) {
            throw new DeliveryException(DeliveryException.PRODUCT_ALREADY_SORTING);
        }
        doDmBySku();
	}
	
	private void doDmBySku() {
	    Long frozenId = sortingService.dmBySku(wave.getId(), skuId);
	    
	    //最后如果破损是最后1个unit 需要释放分拣柜和波次关系
	    sortingService.dealWaveLastUnit(wave.getId());
	    
	    if (frozenId != null) {
	        expFacadeService.sendDoReleaseOrHold2Scs(frozenId);
	    }
	    this.sayMessage(MESSAGE_SUCCESS);
	}
	
	public void getSortContainerByWave() {
	    doSortContainerDTOList.clear();
	    if (StringUtil.isEmpty(waveNo)) {
            throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
        }
	    wave = waveService.queryWaveByNo(waveNo);
	    if (null == wave) {
	            throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
	    }
	    if (StringUtil.isNotIn(wave.getWaveStatus(), 
	            Constants.WaveStatus.ALLPICKED.getValue(), Constants.WaveStatus.PARTSORTED.getValue())) {
	        throw new DeliveryException(DeliveryException.WAVE_STS_ERRER);
	    }
	    Integer doCountCfg = getConfigValueInt("WAVE_DOC_QTY", ParamUtil.getCurrentWarehouseId());
	    List<DeliveryOrderHeader> doHeaders = wave.getDoHeaders();
        if (ListUtil.isNullOrEmpty(doHeaders)) {
             throw new DeliveryException(DeliveryException.DO_HAS_NO_DETAIL);
         }
	    
	    if (null != doCountCfg && doCountCfg.intValue() >0) {
	        int maxDoCount = doCountCfg.intValue();
	        for(int i = 1; i <= maxDoCount; i++) {
	            DoSortContainerDTO dto = new DoSortContainerDTO();
	            dto.setSortBinNo(String.valueOf(i));
	            doSortContainerDTOList.add(dto);
	        }
	   
	        for (DeliveryOrderHeader doHeader : doHeaders) {
	            String sortBinNo = doHeader.getSortGridNo();
	            DoSortContainerDTO dto = doSortContainerDTOList.get(Integer.parseInt(sortBinNo)-1);
	            dto.setSortContainerNo(buildContainerInfo(doHeader));
	        }
	    } else {
	        for (DeliveryOrderHeader doHeader : doHeaders) {
	            DoSortContainerDTO dto = new DoSortContainerDTO();
                dto.setSortBinNo(doHeader.getSortGridNo());
                dto.setSortContainerNo(buildContainerInfo(doHeader));
                doSortContainerDTOList.add(dto);
            }
	    }
	}
	
	private String buildContainerInfo(DeliveryOrderHeader doHeader) {
		 List<Container> containers = containerMgntService.findContainerByDoc(doHeader.getDoNo(), 
                Constants.ContainerType.SORT_CONTAINER.getValue(), Constants.BindDocType.DELIVERYORDER.getValue());
		 if (ListUtil.isNotEmpty(containers)) {
        	StringBuffer cb = new StringBuffer();
        	boolean isFirst = true;
        	for (Container c : containers) {
        		if (isFirst) {
        			cb.append(c.getContainerNo());
        			isFirst = false;
        		} else {
        			cb.append(",").append(c.getContainerNo());
        		}
        	}
        	return cb.toString();
        } else {
       	 return null;
        }
	}
	
	
	/**
	 * 查询波次绑定的拣货框
	 */
	public void getPickContainerByWave() {
		pickContainerStr = null;
	    if (StringUtil.isEmpty(queryPickContainerWave)) {
            return;
        }
	    containerMntFlag = containerMgntService.getContainerMgntStartFlag();
	    if (Constants.YesNo.YES.getValue().equals(containerMntFlag)) {
	    	List<ContainerLog> pickContainerLogList = containerLogService.queryLogsByDocNo(queryPickContainerWave, 
	    			Constants.ContainerOperationType.BIND.getValue(), Constants.BindDocType.WAVE.getValue());
	    	if (ListUtil.isNotEmpty(pickContainerLogList)) {
	    		int i =1;
	    		StringBuffer sb = new StringBuffer();
	    		for (ContainerLog c : pickContainerLogList) {
	    			sb.append(c.getContainerNo()).append(",");
	    			if (i % 5 == 0) {
	    				sb.append("\n");
	    			}
	    			i++;
	    		}
	    		this.pickContainerStr = sb.substring(0, sb.lastIndexOf(","));
	    	}
	    } else {
	    	this.sayException(new DeliveryException(DeliveryException.WMS_NOT_START_CONTAINER_MGNT));
	    }
	}
	    
    /**
     * 分拣页面波次号回车的打印方法
     */
    public void printInvoice() {
		checkWave();
    }
    
    /**
	 * 根据分拣柜号:SortingBinNo检查分拣柜是否存在
	 */
	public void validateSortingBin(){
		sortingBin = sortingBinService.querySortingBinByNo(sotringBinNo);
		if (sortingBin == null) {
			sotringBinNo = null;
			throw new DeliveryException(DeliveryException.SORTING_BIN_NO_ERRER);
		}
	}
	
	/**
	 * 获得分拣格号
	 */
	public void toGetOldSortGridNo(){
		//校验 并找到分拣格号
		checkOldSortContainer();
	}
	
	/**
	 * 查找分拣格号
	 */
	private void checkOldSortContainer(){
		canChangeSortContainer = false;
		sortGridNo = null;
		doHeader = null;
		if (StringUtil.isEmpty(waveNo)) {
			throw new DeliveryException(DeliveryException.WAVE_NOT_EXIST);
		}
		wave = waveService.queryWaveByNo(waveNo);
		if (!wave.getWaveStatus().equals(DoStatus.PARTSORTED.getValue())){
			throw new DeliveryException(DeliveryException.WAVE_STS_ERRER);
		}
		sortContainer = SortContainerUtil.subStringSortContainer(sortContainer);
		Container container = containerService.getContainerByNo(sortContainer);
		if (container == null) {
			throw new DeliveryException(DeliveryException.OLD_SORT_CONTAINER_NOT_EXIT);
		}
		doHeader = deliveryOrderService.findByWaveIdAndConNo
				(wave.getId(), sortContainer, ContainerType.SORT_CONTAINER.getValue());
		if (doHeader == null) {
			throw new DeliveryException(DeliveryException.NEW_CONTAINER_AND_WAVE_NO_ERROR);
		}
		if (!doHeader.getStatus().equals(DoStatus.PARTSORTED.getValue())) {
			throw new DeliveryException("DO状态不是分拣中,不能换箱");
		}
		sortGridNo = Long.parseLong(doHeader.getSortGridNo());
		canChangeSortContainer = true;
	}
	
	/**
	 * 校验新分拣筐
	 */
	public Container checkNewContainerNo(){
		newSortContainer = SortContainerUtil.subStringSortContainer(newSortContainer);
		Container container = containerService.getContainerByNo(newSortContainer);
		if (null == container) {
			throw new DeliveryException(DeliveryException.NEW_SORT_CONTAINER_NOT_EXIT);
		} else if (!Constants.ContainerType.SORT_CONTAINER.getValue().equals(container.getContainerType().getContainerType())) {
			throw new DeliveryException(DeliveryException.NEW_SORTCONTAINER_TYPE_ERROR);
		}
		if (!Constants.ContainerBusinessStatus.IDLE.getValue().equals(container.getBusinessStatus())) {
            throw new DeliveryException(DeliveryException.NEW_CONTAINER_BUSINESSSTATUS_ERROR);
        }
		return container;
	}
	
	/**
	 * 执行换分拣筐
	 */
	public void doChangeNewContainer(){
        isChangeSuccess = false;
		//校验久分拣筐
		checkOldSortContainer();
		//校验新分拣筐
		Container container = checkNewContainerNo();
		//校验分拣柜
		validateSortingBin();
		//换箱
		sortingService.executeChangeSortContainer(doHeader,container);
        //操作完成清空新旧分拣筐和分拣柜号
        clearChangeContainer();
        isChangeSuccess = true;
        this.sayMessage(MESSAGE_SUCCESS);

	}
	
	/**
	 * 处理异常波次<p />
	 * 异常流程导致波次下的拣货箱都复核了,但是波次状态不是复核,则更新为复核
	 * @param waveHeader
	 */
	private void dealErrSortbinWave(WaveHeader waveHeader) {
	    List<Container> containerNos = containerService.getContainersByWaveId(waveHeader.getId());
    	List<String> notcheckedContainers = new ArrayList<String>();
	    for (Container con : containerNos) {
	    	if (!ContainerBusinessStatus.CHECKED.getValue().equals(con.getBusinessStatus())) {
	    		notcheckedContainers.add(con.getContainerNo());
	    	}
	    }
	    if (CollectionUtils.isEmpty(notcheckedContainers)){
	    	if (!WaveMergeStatus.CHECKED.getValue().equals(String.valueOf(waveHeader.getMergeStatus()))) {
		    	waveHeader.setMergeStatus(Integer.valueOf(WaveMergeStatus.CHECKED.getValue()));
	    		waveService.updateWaveHeader(waveHeader);
	    	}
	    }
	}
	
	public void doCheckContainer() {
		if (!isSortLaborOpen()) {
			return;
		}
		sortBinBindWaveDTO.setErrMsg("");
		if (StringUtil.isEmpty(sortBinBindWaveDTO.getCurScanContainer())) {
			sortBinBindWaveDTO.setErrMsg(FileImportTemplate.getExceptionDispalyMsg(
					new DeliveryException(DeliveryException.ERROR_SORT_NOT_INPUT_CONTAINER), MESSAGE_FAILED));
			return;
		}
		Container container = containerService.getContainerByNo(sortBinBindWaveDTO.getCurScanContainer());
		if (container == null) {
			sortBinBindWaveDTO.setErrMsg(FileImportTemplate.getExceptionDispalyMsg(
					new DeliveryException(DeliveryException.CONTAINER_NOT_EXIT), MESSAGE_FAILED));
			return;
		} else {
			if (StringUtil.isEmpty(container.getDocNo()) || 
					!container.getDocNo().equals(sortBinBindWaveDTO.getWaveNo())) {
				sortBinBindWaveDTO.setErrMsg(FileImportTemplate.getExceptionDispalyMsg(
						new DeliveryException(DeliveryException.ERROR_SORT_CONTAINER_NOT_MATCH_WAVE), MESSAGE_FAILED));
				return;
			}
			if (ContainerBusinessStatus.CHECKED.getValue().equals(container.getBusinessStatus())) {
				sortBinBindWaveDTO.setErrMsg(FileImportTemplate.getExceptionDispalyMsg(
						new DeliveryException(DeliveryException.ERROR_SORT_CONTAINER_ALREADY_CHECKED), MESSAGE_FAILED));
				return;
			}
		}
	}
	
    public boolean isSortLaborOpen() {
    	return SystemConfig.configIsOpen(Switch.LABOR_FORCE_SWITCH_SORT, ParamUtil.getCurrentWarehouseId());
    }
	
	public void clearChangeContainer(){
        sortGridNo = null;
        sortContainer = null;
        newSortContainer = null;
	}
	
	public String getSkuHelperData() {
		return skuHelperData;
	}
	
	public void setSkuHelperData(String skuHelperData) {
		this.skuHelperData = skuHelperData;
	}
	
	public String getSotringBinNo() {
		return sotringBinNo;
	}

	public void setSotringBinNo(String sotringBinNo) {
		this.sotringBinNo = sotringBinNo;
	}

	public String getStaffNo() {
		return staffNo;
	}

	public void setStaffNo(String staffNo) {
		this.staffNo = staffNo;
	}

	public String getWaveNo() {
		return waveNo;
	}

	public void setWaveNo(String waveNo) {
		this.waveNo = waveNo;
	}
    
    public String getQueryPickContainerWave() {
		return queryPickContainerWave;
	}

	public void setQueryPickContainerWave(String queryPickContainerWave) {
		this.queryPickContainerWave = queryPickContainerWave;
	}

	public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public String getBarcode() {
		return barcode;
	}

	public void setBarcode(String barcode) {
		this.barcode = barcode;
	}
	
    public Long getSortingBinId() {
        return sortingBinId;
    }
    
    public void setSortingBinId(Long sortingBinId) {
        this.sortingBinId = sortingBinId;
    }

    public Long getSortGridNo() {
		return sortGridNo;
	}

	public void setSortGridNo(Long sortGridNo) {
		this.sortGridNo = sortGridNo;
	}

	public SortingService getSortingService() {
		return sortingService;
	}

	public void setSortingService(SortingService sortingService) {
		this.sortingService = sortingService;
	}

	public WaveHeader getWave() {
		return wave;
	}

	public void setWave(WaveHeader wave) {
		this.wave = wave;
	}

	public SortingBin getSortingBin() {
		return sortingBin;
	}

	public void setSortingBin(SortingBin sortingBin) {
		this.sortingBin = sortingBin;
	}

	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	public boolean getIsMultiProd() {
		return isMultiProd;
	}

	public void setIsMultiProd(boolean isMultiProd) {
		this.isMultiProd = isMultiProd;
	}

	public boolean getIsWaveSortingCompleted() {
		return isWaveSortingCompleted;
	}

	public void setIsWaveSortingCompleted(boolean isWaveSortingCompleted) {
		this.isWaveSortingCompleted = isWaveSortingCompleted;
	}

	public List<DeliveryOrderDetail> getDoDetailList() {
		return doDetailList;
	}

	public void setDoDetailList(List<DeliveryOrderDetail> doDetailList) {
		this.doDetailList = doDetailList;
	}

	public BigDecimal getPendingSortingNumber() {
		return pendingSortingNumber;
	}

	public void setPendingSortingNumber(BigDecimal pendingSortingNumber) {
		this.pendingSortingNumber = pendingSortingNumber;
	}

	public Long getCurrentDoId() {
		return currentDoId;
	}

	public void setCurrentDoId(Long currentDoId) {
		this.currentDoId = currentDoId;
	}

	public BigDecimal getBatchNumber() {
		return batchNumber;
	}

	public void setBatchNumber(BigDecimal batchNumber) {
		this.batchNumber = batchNumber;
	}

	public List<Sku> getSkuList() {
		return skuList;
	}

	public void setSkuList(List<Sku> skuList) {
		this.skuList = skuList;
	}

	public List<Long> getSkuIds() {
		return skuIds;
	}

	public void setSkuIds(List<Long> skuIds) {
		this.skuIds = skuIds;
	}

	public boolean isSorted() {
		return isSorted;
	}

	public void setSorted(boolean isSorted) {
		this.isSorted = isSorted;
	}
	
	public List<DeliveryOrderHeader> getHoldGridNumList() {
		return holdGridNumList;
	}
	
	public void setHoldGridNumList(List<DeliveryOrderHeader> holdGridNumList) {
		this.holdGridNumList = holdGridNumList;
	}
	
	public List<DeliveryOrderHeader> getPartSortedNumList() {
		return partSortedNumList;
	}
	
	public void setPartSortedNumList(List<DeliveryOrderHeader> partSortedNumList) {
		this.partSortedNumList = partSortedNumList;
	}
	
	public List<DeliveryOrderHeader> getSortedNumList() {
		return sortedNumList;
	}
	
	public void setSortedNumList(List<DeliveryOrderHeader> sortedNumList) {
		this.sortedNumList = sortedNumList;
	}
	
	public List<DeliveryOrderHeader> getUnUsedSortedNumList() {
		return unUsedSortedNumList;
	}
	
	public void setUnUsedSortedNumList(List<DeliveryOrderHeader> unUsedSortedNumList) {
		this.unUsedSortedNumList = unUsedSortedNumList;
	}
    
    public List<DeliveryOrderHeader> getCancelGridNumList() {
		return cancelGridNumList;
	}
    
    public void setCancelGridNumList(List<DeliveryOrderHeader> cancleGridNumList) {
		this.cancelGridNumList = cancleGridNumList;
	}
	
    public List<DoSortContainerDTO> getDoSortContainerDTOList() {
        return doSortContainerDTOList;
    }

    
    public void setDoSortContainerDTOList(List<DoSortContainerDTO> doSortContainerDTOList) {
        this.doSortContainerDTOList = doSortContainerDTOList;
    }

    
    public String getPickContainerStr() {
		return pickContainerStr;
	}

	public void setPickContainerStr(String pickContainerStr) {
		this.pickContainerStr = pickContainerStr;
	}

	public boolean getIsContractPhoneDo() {
		return isContractPhoneDo; 
	}
	
	public void setIsContractPhoneDo(boolean isContractPhoneDo) {
		this.isContractPhoneDo = isContractPhoneDo;
    }

    public String getPrintData() {
        return printData;
    }

    public void setPrintData(String printData) {
        this.printData = printData;
    }

	public boolean isNeedSortContainer() {
		return needSortContainer;
	}

	public void setNeedSortContainer(boolean needSortContainer) {
		this.needSortContainer = needSortContainer;
	}
	
	public boolean isNeedBindContainer() {
		return needBindContainer;
	}

	public void setNeedBindContainer(boolean needBindContainer) {
		this.needBindContainer = needBindContainer;
	}
	
	public String getSortContainer() {
		return sortContainer;
	}

	public void setSortContainer(String sortContainer) {
		this.sortContainer = sortContainer;
	}
	

	public boolean isWaveFlag() {
		return waveFlag;
	}

	public void setWaveFlag(boolean waveFlag) {
		this.waveFlag = waveFlag;
	}
	
	public Long getSortContainerNo() {
		return sortContainerNo;
	}

	public void setSortContainerNo(Long sortContainerNo) {
		this.sortContainerNo = sortContainerNo;
	}

	public Integer getContainerMntFlag() {
		return containerMntFlag;
	}

	public void setContainerMntFlag(Integer containerMntFlag) {
		this.containerMntFlag = containerMntFlag;
	}

	public String getCancalSortingNo() {
		return cancalSortingNo;
	}

	public void setCancalSortingNo(String cancalSortingNo) {
		this.cancalSortingNo = cancalSortingNo;
	}

	public Integer getInvoiceCodeLength() {
		return invoiceCodeLength;
	}

	public void setInvoiceCodeLength(Integer invoiceCodeLength) {
		this.invoiceCodeLength = invoiceCodeLength;
	}

	public Integer getInvoiceNoLength() {
		return invoiceNoLength;
	}

	public void setInvoiceNoLength(Integer invoiceNoLength) {
		this.invoiceNoLength = invoiceNoLength;
	}

	public Integer getCountPerRoll() {
		return countPerRoll;
	}

	public void setCountPerRoll(Integer countPerRoll) {
		this.countPerRoll = countPerRoll;
	}

	public String getInvoiceTipMsg() {
		return invoiceTipMsg;
	}

	public void setInvoiceTipMsg(String invoiceTipMsg) {
		this.invoiceTipMsg = invoiceTipMsg;
	}

	public boolean isPrintInNewWay() {
		return printInNewWay;
	}

	public void setPrintInNewWay(boolean printInNewWay) {
		this.printInNewWay = printInNewWay;
	}
	
	public String getPickContainerNo4Print() {
		return pickContainerNo4Print;
	}

	public void setPickContainerNo4Print(String pickContainerNo4Print) {
		this.pickContainerNo4Print = pickContainerNo4Print;
	}

	public BigDecimal getDoCount() {
		return doCount;
	}

	public void setDoCount(BigDecimal doCount) {
		this.doCount = doCount;
	}

	public String getNewSortContainer() {
		return newSortContainer;
	}

	public void setNewSortContainer(String newSortContainer) {
		this.newSortContainer = newSortContainer;
	}

	public DoHeaderDto getDoHeader() {
		return doHeader;
	}

	public void setDoHeader(DoHeaderDto doHeader) {
		this.doHeader = doHeader;
	}

	public Boolean getCanChangeSortContainer() {
		return canChangeSortContainer;
	}

	public void setCanChangeSortContainer(Boolean canChangeSortContainer) {
		this.canChangeSortContainer = canChangeSortContainer;
	}

	public Boolean getIsChangeSuccess() {
		return isChangeSuccess;
	}

	public void setIsChangeSuccess(Boolean isChangeSuccess) {
		this.isChangeSuccess = isChangeSuccess;
	}

	public Integer getSingleDoMergeCfg() {
		return singleDoMergeCfg;
	}

	public void setSingleDoMergeCfg(Integer singleDoMergeCfg) {
		this.singleDoMergeCfg = singleDoMergeCfg;
	}
	public SortBinBindWaveDTO getSortBinBindWaveDTO() {
		return sortBinBindWaveDTO;
	}
	
	public void setSortBinBindWaveDTO(SortBinBindWaveDTO sortBinBindWaveDTO) {
		this.sortBinBindWaveDTO = sortBinBindWaveDTO;
	}
	
	public boolean getBindRightWave() {
		return bindRightWave;
	}
	
	public void setBindRightWave(boolean bindRightWave) {
		this.bindRightWave = bindRightWave;
	}

	public Integer getNeedBatchNumber() {
		return needBatchNumber;
	}

	public void setNeedBatchNumber(Integer needBatchNumber) {
		this.needBatchNumber = needBatchNumber;
	}

	public String getOperateCode() {
		return operateCode;
	}

	public void setOperateCode(String operateCode) {
		this.operateCode = operateCode;
	}

	public boolean isNeedDetail() {
		return needDetail;
	}

	public void setNeedDetail(boolean needDetail) {
		this.needDetail = needDetail;
	}

    public List<List<AutoCompleteDTO>> getNoEanSkuList() {
        return noEanSkuList;
    }

    public void setNoEanSkuList(List<List<AutoCompleteDTO>> noEanSkuList) {
        this.noEanSkuList = noEanSkuList;
    }

	public String getPrintCartonCode() {
		return printCartonCode;
	}

	public void setPrintCartonCode(String printCartonCode) {
		this.printCartonCode = printCartonCode;
	}

	public String getPrintContent() {
		return printContent;
	}

	public void setPrintContent(String printContent) {
		this.printContent = printContent;
	}

	public String getPrintDoCode() {
		return printDoCode;
	}

	public void setPrintDoCode(String printDoCode) {
		this.printDoCode = printDoCode;
	}

	public BigDecimal getCurrentSortQty() {
		return currentSortQty;
	}

	public void setCurrentSortQty(BigDecimal currentSortQty) {
		this.currentSortQty = currentSortQty;
	}

	public Integer getDotFlag() {
		return dotFlag;
	}

	public void setDotFlag(Integer dotFlag) {
		this.dotFlag = dotFlag;
	}
}