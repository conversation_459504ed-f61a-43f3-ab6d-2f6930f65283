package com.daxia.wms.delivery.recheck.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@lombok.extern.slf4j.Slf4j
public class CartonHeaderFilter extends WhBaseQueryFilter {

    private static final long serialVersionUID = 7442606027791494051L;

    private String doNo;

    private String doNoList;

    private String cartonNo;

    private String cartonNoList;

    private List<Long> waveIds;

    private Long waveId;

    private String waveNo;

    private String lpnNo;

    private Date fromDate;

    private Date toDate;

    private String createdBy;

    private String weighBy;

    private List<Long> doIds;

    private Date doCreateTimeFrom;

    private Date doCreateTimeTo;

    private Long carrierId;

    private String status;

    private String doStatus;

    private String doType;

    private Date doShipTimeFrom;

    private Date doShipTimeTo;

    private String originalSoCode;

    private String wayBill;

    private Long shopId;

    private Boolean isCainiao;

    private Integer isPrinted;

    private String packageType;
    
    private String productCode;

    private Long merchantId;

    private Long businessCustomerId;

    private String orderSubType;

    private List<String> doNoStrList;

    private List<String> cartonNoStrList;
    /**
     * 货物放行 0未放行 1 已放行'
     */
    private Integer  goodPass;
    @Operation(clause = " (o.doHeader.carrier.waybillType = 'CAINIAO' and o.wayBill != o.cartonNo) ", operationType = OperationType.CLAUSE)
    public Integer getCainiaoFlag() {
        if (this.getCainiao() == null) {
            return null;
        }
        return this.getCainiao() ? Constants.YesNo.YES.getValue() : null;
    }

    @Operation(operationType = OperationType.IGNORE)
    public Boolean getCainiao() {
        return isCainiao;
    }

    public void setCainiao(Boolean cainiao) {
        isCainiao = cainiao;
    }

    public void setCartonNo(String cartonNo) {
        this.cartonNo = cartonNo;
    }

    @Operation(fieldName = "cartonNo", operationType = OperationType.EQUAL)
    public String getCartonNo() {
        return cartonNo;
    }

    public void setWaveIds(List<Long> waveIds) {
        this.waveIds = waveIds;
    }

    @Operation(fieldName = "o.doHeader.waveId", operationType = OperationType.IN)
    public List<Long> getWaveIds() {
        return waveIds;
    }

    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }

    @Operation(fieldName = "o.doHeader.waveId", operationType = OperationType.EQUAL)
    public Long getWaveId() {
        return waveId;
    }

    @Operation(fieldName = "o.lpnNo", operationType = OperationType.EQUAL)
    public String getLpnNo() {
        return lpnNo;
    }

    public void setLpnNo(String lpnNo) {
        this.lpnNo = lpnNo;
    }

    @Operation(fieldName = "o.isPrinted", operationType = OperationType.EQUAL)
    public Integer getIsPrinted() {
        return isPrinted;
    }

    public void setIsPrinted(Integer isPrinted) {
        this.isPrinted = isPrinted;
    }

    @Operation(fieldName = "o.doHeader.doNo", operationType = OperationType.EQUAL)
    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    @Operation(fieldName = "o.createdAt", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")
    public Date getFromDate() {
        return fromDate;
    }

    public void setFromDate(Date fromDate) {
        this.fromDate = fromDate;
    }

    @Operation(fieldName = "o.createdAt", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
    public Date getToDate() {
        return toDate;
    }

    public void setToDate(Date toDate) {
        this.toDate = toDate;
    }

    @Operation(fieldName = "o.createdBy", operationType = OperationType.EQUAL)
    public String getCreatedBy() {
        return createdBy;
    }

    @Operation(fieldName = "o.weighBy", operationType = OperationType.EQUAL)
    public String getWeighBy() {
        return weighBy;
    }

    public void setWeighBy(String weighBy) {
        this.weighBy = weighBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Operation(fieldName = "o.doHeader.id", operationType = OperationType.IN)
    public List<Long> getDoIds() {
        return doIds;
    }

    public void setDoIds(List<Long> doIds) {
        this.doIds = doIds;
    }

    @Operation(fieldName = "o.doHeader.createdAt", operationType = OperationType.NOT_LESS_THAN, dataType =
            "datetime")
    public Date getDoCreateTimeFrom() {
        return doCreateTimeFrom;
    }

    public void setDoCreateTimeFrom(Date doCreateTimeFrom) {
        this.doCreateTimeFrom = doCreateTimeFrom;
    }

    @Operation(fieldName = "o.doHeader.createdAt", operationType = OperationType.NOT_GREAT_THAN, dataType =
            "datetime")
    public Date getDoCreateTimeTo() {
        return doCreateTimeTo;
    }

    public void setDoCreateTimeTo(Date doCreateTimeTo) {
        this.doCreateTimeTo = doCreateTimeTo;
    }

    @Operation(fieldName = "o.doHeader.carrierId", operationType = OperationType.EQUAL)
    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    @Operation(fieldName = "o.doHeader.doType", operationType = OperationType.EQUAL)
    public String getDoType() {
        return doType;
    }

    public void setDoType(String doType) {
        this.doType = doType;
    }

    @Operation(fieldName = "o.status", operationType = OperationType.EQUAL)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Operation(fieldName = "o.doHeader.status", operationType = OperationType.EQUAL)
    public String getDoStatus() {
        return doStatus;
    }

    public void setDoStatus(String doStatus) {
        this.doStatus = doStatus;
    }

    @Operation(fieldName = "o.doHeader.shipTime", operationType = OperationType.NOT_LESS_THAN, dataType =
            "datetime")
    public Date getDoShipTimeFrom() {
        return doShipTimeFrom;
    }

    public void setDoShipTimeFrom(Date doShipTimeFrom) {
        this.doShipTimeFrom = doShipTimeFrom;
    }

    @Operation(fieldName = "o.doHeader.shipTime", operationType = OperationType.NOT_GREAT_THAN, dataType =
            "datetime")
    public Date getDoShipTimeTo() {
        return doShipTimeTo;
    }

    public void setDoShipTimeTo(Date doShipTimeTo) {
        this.doShipTimeTo = doShipTimeTo;
    }

    @Operation(fieldName = "o.doHeader.originalSoCode", operationType = OperationType.EQUAL)
    public String getOriginalSoCode() {
        return originalSoCode;
    }

    public void setOriginalSoCode(String originalSoCode) {
        this.originalSoCode = originalSoCode;
    }

    @Operation(fieldName = "o.wayBill", operationType = OperationType.EQUAL)
    public String getWayBill() {
        return wayBill;
    }

    public void setWayBill(String wayBill) {
        this.wayBill = wayBill;
    }

    @Operation(fieldName = "o.doHeader.shopId", operationType = OperationType.EQUAL)
    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    @Operation(fieldName = "o.doHeader.waveHeader.waveNo", operationType = OperationType.EQUAL)
    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }
    @Operation(fieldName = "o.ext1", operationType = OperationType.EQUAL)
    public String getPackageType() {
        return packageType;
    }

    public void setPackageType(String packageType) {
        this.packageType = packageType;
    }

    @Operation(clause = " exists ( select 1 from CartonDetail cd where cd.cartonHeader.id = o.id and cd.sku.productCode = ? )", operationType = OperationType.CLAUSE)
    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    @Operation(fieldName = "o.doHeader.merchantId", operationType = OperationType.EQUAL)
    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    @Operation(fieldName = " o.doHeader.businessCustomerId", operationType = OperationType.EQUAL)
    public Long getBusinessCustomerId() {
        return businessCustomerId;
    }

    public void setBusinessCustomerId(Long businessCustomerId) {
        this.businessCustomerId = businessCustomerId;
    }

    @Operation(fieldName = "o.doHeader.orderSubType", operationType = OperationType.EQUAL)
    public String getOrderSubType() {
        return orderSubType;
    }

    public void setOrderSubType(String orderSubType) {
        this.orderSubType = orderSubType;
    }

    @Operation(operationType = OperationType.IGNORE)
    public String getDoNoList() {
        return doNoList;
    }

    public void setDoNoList(String doNoList) {
        this.doNoList = doNoList;
    }

    @Operation(operationType = OperationType.IGNORE)
    public String getCartonNoList() {
        return cartonNoList;
    }

    public void setCartonNoList(String cartonNoList) {
        this.cartonNoList = cartonNoList;
    }

    @JsonIgnore
    @Operation(fieldName = "o.doHeader.doNo", operationType = OperationType.IN)
    public List<String> getDoNoStrList() {
        if (StringUtil.isEmpty(this.getDoNoList())) {
            return null;
        }

        List<String> doNoList = StringUtil.splitToList(StringUtil.trimStr(this.getDoNoList()), ",");
        if (CollectionUtils.isEmpty(doNoList)) {
            return null;
        }
        return doNoList;
    }

    public void setDoNoStrList(List<String> doNoStrList) {
        this.doNoStrList = doNoStrList;
    }

    @JsonIgnore
    @Operation(fieldName = "o.cartonNo", operationType = OperationType.IN)
    public List<String> getCartonNoStrList() {
        if (StringUtil.isEmpty(this.getCartonNoList())) {
            return null;
        }

        List<String> cartonNoList = StringUtil.splitToList(StringUtil.trimStr(this.getCartonNoList()), ",");
        if (CollectionUtils.isEmpty(cartonNoList)) {
            return null;
        }
        return cartonNoList;
    }

    public void setCartonNoStrList(List<String> cartonNoStrList) {
        this.cartonNoStrList = cartonNoStrList;
    }
    @Operation(fieldName = "o.goodPass", operationType = OperationType.EQUAL)
    public Integer getGoodPass() {
        return goodPass;
    }

    public void setGoodPass(Integer goodPass) {
        this.goodPass = goodPass;
    }
}
