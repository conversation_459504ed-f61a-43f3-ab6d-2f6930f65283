package com.daxia.wms.delivery.task.repick.service.impl;

import com.daxia.wms.delivery.task.repick.dao.ReversePickContainerDAO;
import com.daxia.wms.delivery.task.repick.service.RePickContainerService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.util.List;

@Name("com.daxia.wms.delivery.rePickContainerService")
@lombok.extern.slf4j.Slf4j
public class RePickContainerServiceImpl implements RePickContainerService {
    
    @In
    ReversePickContainerDAO reversePickContainerDAO;
    
    @Override
	@Transactional
    public void bind4Carton(Long doId) {
        reversePickContainerDAO.createByCarton(doId);
    }
    
    @Override
	@Transactional
    public void bind4Pick(List<Long> pickTaskId) {
        reversePickContainerDAO.createByPick(pickTaskId);
    }
    
    @Override
    public void bind4Pick(Long doHeaderId) {
        reversePickContainerDAO.createByPick(doHeaderId);
    }
    
    @Override
    @Transactional
    public void bindRepickHeader(String pktType, Long doId, Long rePickHeaderId) {
        reversePickContainerDAO.bindRepickHeader(pktType,doId,rePickHeaderId);
    }
}
