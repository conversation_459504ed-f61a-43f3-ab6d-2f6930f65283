package com.daxia.wms.delivery.invoice.job;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Constants.InvoicePrintFlg;
import com.daxia.wms.delivery.invoice.service.InvoiceJinrenService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.master.job.AbstractJob;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.log.Log;

import java.util.ArrayList;
import java.util.List;

/**
 * 从金任系统读取发票号并进行绑定
 */
@Name("com.daxia.wms.delivery.invoiceJinrenWriteJob")
@AutoCreate
@lombok.extern.slf4j.Slf4j
public class ReadBindJinrenInvoiceJob extends AbstractJob {


	@In
	private InvoiceJinrenService invoiceJinrenService;
	@In
    private WaveService waveService;

	@Override
	protected void doRun() throws Exception {
			log.info("Begin Run ReadBindJinrenInvoiceJob, Please Waiting ............... ");
			try {
				// 查找仓库列表，如果没有配置，默认为所有仓库的数据。
				String strWhIds = SystemConfig.getConfigValue(
						"invoiceJinren.whIds", null);

				List<Long> whIdList = new ArrayList<Long>();
				if (!StringUtil.isEmpty(strWhIds)) {
					String[] whIds = strWhIds.trim().split(",");
					for (String whId : whIds) {
						whIdList.add(Long.valueOf(whId));
					}
				}
				for(Long warehouseId : whIdList){
					ParamUtil.setCurrentWarehouseId(warehouseId);
					// 查询波次
					List<WaveHeader> initialWaveHeaders = waveService
							.findWaveByPrintFlg(InvoicePrintFlg.SENDED.getValue());
					// 事物在for 循环级别。
					for (WaveHeader initialWaveHeader : initialWaveHeaders) {
						try{
							invoiceJinrenService.readBindJinrenInvoice(initialWaveHeader);
						} catch (Exception e) {
							log.error("ReadBindJinrenInvoiceJob encounter exception: ", e);
							continue;
						}
					}
				}
			}catch (Exception e) {
				log.error("ReadBindJinrenInvoiceJob encounter exception: ", e);
			}
			log.info("End ReadBindJinrenInvoiceJob. ");
	}
}
