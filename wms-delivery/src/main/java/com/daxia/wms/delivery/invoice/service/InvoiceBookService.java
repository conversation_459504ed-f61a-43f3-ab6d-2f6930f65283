package com.daxia.wms.delivery.invoice.service;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.invoice.entity.InvoiceBook;
import com.daxia.wms.delivery.invoice.filter.InvoiceBookFilter;

/**
 * 发票簿service
 */
public interface InvoiceBookService {
    /**
     * 根据发票薄filter查询发票薄并分页
     * 
     * @param invoiceBookFilter
     * @param startIndex
     * @param pageSize
     * @return
     */
	public DataPage<InvoiceBook> findInvoiceBookByFilter(
			InvoiceBookFilter invoiceBookFilter, int startIndex, int pageSize);
	
    /**
     * 根据Id查询发票薄
     * 
     * @param invoiceBookId
     * @return
     */
	public InvoiceBook getInvoiceBookById(Long invoiceBookId);
	
	/**
	 * 更新发票薄
	 */
	public void updateInvoiceBook(InvoiceBook invoiceBook);
	public void save(InvoiceBook invoiceBook);
}
