package com.daxia.wms.delivery.load.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.load.entity.ReShipDo;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import java.util.Arrays;
import java.util.List;

/**
 * 临时表EXP_SHIP_QUEUE对应的DAO
 */
@Name("com.daxia.wms.delivery.reShipDoDAO")
@lombok.extern.slf4j.Slf4j
public class ReShipDoDAO extends HibernateBaseDAO<ReShipDo, Long> {

    private static final long serialVersionUID = 7337900458396507191L;

    /**
     * 找到 仍需执行重新发货的 DO 的ID
     * 
     * @param failCeiling
     *            定时任务执行重新发货允许失败的次数上限
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<ReShipDo> findNeedReShipDo(Integer failCeiling, Integer maxCount, Long warehouseId) {
        String hql = "from ReShipDo o where o.count <= :failCount and o.count > 0 ";
        hql += " and o.deliveryOrderHeader.status in (:statusList) ";
        hql += " and o.nextInvokeTime <= :nextInvokeTime";
        if (warehouseId != null) {
            hql += " and o.warehouseId = :warehouseId ";
        }
        hql += " order by id ";
        Query query = this.createQuery(hql);
        query.setParameter("failCount", Long.valueOf(failCeiling));
        if (warehouseId != null) {
            query.setParameter("warehouseId", warehouseId);
        }
        query.setParameter("nextInvokeTime", DateUtil.getNowTime());
        query.setParameterList("statusList", Arrays.asList(Constants.DoStatus.ALL_LOAD.getValue(), Constants.DoStatus.PART_DELIVER.getValue()));
        return query.setMaxResults(maxCount).list();
    }

    /**
     * DO发货成功，从失败记录里去除该DO
     * @param doId
     * @return
     */
    public ReShipDo findReShipDoByDoId(Long doId, Integer loadMode) {
        Query query = this.createQuery("from ReShipDo o where o.docId = :doId and o.isAuto= :loadMode and o.warehouseId = :warehouseId ");
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("doId", doId);
        query.setParameter("loadMode", loadMode);
        query.setMaxResults(1);
        return (ReShipDo) query.uniqueResult();
    }

    public ReShipDo findReShipDoByDoId(Long doId) {
        Query query = this.createQuery("from ReShipDo o where o.docId = :doId and o.warehouseId = :warehouseId ");
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("doId", doId);
        query.setMaxResults(1);
        return (ReShipDo) query.uniqueResult();
    }
    /**
     * DO发货成功，从失败记录里去除该DO
     * @param doId
     * @return
     */
    public int removeReShipDoByDoId(Long doId) {
        Query query = this.createDeleteSqlQuery(" doc_id = :doId and warehouse_Id = :warehouseId ");
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setLong("doId", doId); 
        return query.executeUpdate();
    }
    
    public void addCallCountByLoadHeaderId(Long loadHeaderId) {
        StringBuffer sql = new StringBuffer();
        sql.append("UPDATE exp_ship_queue q INNER JOIN doc_load_detail ld ON q.DOC_ID = ld.do_header_id SET q.COUNT = q.COUNT + 1 WHERE ld.load_header_id = :loadHeaderId");
        Query query = this.createSQLQuery(sql.toString());
        query.setParameter("loadHeaderId", loadHeaderId);
        query.executeUpdate();
    }
}
