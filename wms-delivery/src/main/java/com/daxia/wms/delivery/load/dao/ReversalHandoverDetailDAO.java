package com.daxia.wms.delivery.load.dao;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.load.entity.ReversalHandoverDetail;

/**
 * 逆向交接单DAO方法
 */
@Name("com.daxia.wms.delivery.reversalHandoverDetailDAO")
@lombok.extern.slf4j.Slf4j
public class ReversalHandoverDetailDAO extends HibernateBaseDAO<ReversalHandoverDetail, Long> {
	private static final long serialVersionUID = -3731117149159716530L;

	public ReversalHandoverDetail findReversalHandoverDetailByCartonNo(String cartonNo) {
		String hql = "from ReversalHandoverDetail rhd where rhd.cartonNo = :cartonNo and rhd.warehouseId = :warehouseId " +
					 "order by rhd.createdAt desc ";
		Query query = createQuery(hql);
		query.setParameter("cartonNo", cartonNo);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setMaxResults(1);
		return (ReversalHandoverDetail) query.uniqueResult();
	}
}
