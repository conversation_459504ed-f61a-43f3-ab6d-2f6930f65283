package com.daxia.wms.delivery.load.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.delivery.load.dao.CrossDockDeliverDetailDao;
import com.daxia.wms.delivery.load.dao.CrossDockPackingCartonNoDAO;
import com.daxia.wms.delivery.load.dao.CrossDockPackingDAO;
import com.daxia.wms.delivery.load.dao.TOCrossDockHeaderDAO;
import com.daxia.wms.delivery.load.dto.CrossDockPackingDTO;
import com.daxia.wms.delivery.load.dto.StockCrossDockDTO;
import com.daxia.wms.delivery.load.entity.CrossDockPacking;
import com.daxia.wms.delivery.load.entity.CrossDockPackingCartonNo;
import com.daxia.wms.delivery.load.filter.CrossDockPackingFilter;
import com.daxia.wms.delivery.load.service.CrossDockDetailService;
import com.daxia.wms.delivery.load.service.CrossDockPackingService;
import com.daxia.wms.delivery.print.dto.CrossDockCartonPrintDTO;
import com.daxia.wms.Constants.SequenceName;
import com.daxia.wms.master.service.SkuService;

@Name("com.daxia.wms.delivery.crossDockPackingService")
@lombok.extern.slf4j.Slf4j
public class CrossDockPackingServiceImpl implements CrossDockPackingService {
	
	@In
	private CrossDockDetailService crossDockDetailService;
	
	@In
	private SequenceGeneratorService sequenceGeneratorService;
	
	@In
	private CrossDockPackingDAO crossDockPackingDAO;
	
	@In
	private CrossDockPackingCartonNoDAO crossDockPackingCartonNoDAO;

	@Override
	public DataPage<CrossDockPacking> queryCDHeaderPageInfo(CrossDockPackingFilter filter,
			int startIndex, int pageSize) {
		return crossDockPackingDAO.findRangeByFilter(filter, startIndex,pageSize);
	}

	@Override
	@Transactional
	public void delivery(List<CrossDockPackingDTO> crossDockPackingDTOList,Integer boxNum) {
		String[] cartonNos = new String[boxNum];
		String cartonNo = "";
		for (int i = 0; i < boxNum; i++) {
			cartonNos[i] = this.autoGenerateCommonLpn();
			if(i == 0){
				cartonNo =cartonNos[i];
			}else{
				cartonNo = cartonNo + "," + cartonNos[i];
			}
		}
		Map<Long,List<StockCrossDockDTO>> mapCDPackDTO = new HashMap<Long,List<StockCrossDockDTO>>();
		for (CrossDockPackingDTO crossDockPackingDTO : crossDockPackingDTOList) {
			if(mapCDPackDTO.get(crossDockPackingDTO.getCrossDockPacking().getCdHeaderId()) == null){
				mapCDPackDTO.put(crossDockPackingDTO.getCrossDockPacking().getCdHeaderId(),
						new ArrayList<StockCrossDockDTO>());
			}
			mapCDPackDTO.get(crossDockPackingDTO.getCrossDockPacking().getCdHeaderId())
				.addAll(crossDockPackingDTO.getStockCrossDockDTOList());
			CrossDockPacking crossDockPacking =crossDockPackingDTO.getCrossDockPacking();
			crossDockPacking.setCartonNo(cartonNo);
			List<Long> sku = new ArrayList<Long>();
			BigDecimal unitsQty = BigDecimal.ZERO;
			for (StockCrossDockDTO stockCrossDockDTO : crossDockPackingDTO.getStockCrossDockDTOList()) {
				sku.add(stockCrossDockDTO.getSkuId());
				unitsQty = unitsQty.add(stockCrossDockDTO.getQty());
			}
			crossDockPacking.setSkuQty(BigDecimal.valueOf(sku.size()));
			crossDockPacking.setUnitsQty(unitsQty);
			crossDockPackingDAO.save(crossDockPacking);
			for (int i = 0; i < boxNum; i++) {
				CrossDockPackingCartonNo crossDockPackingCartonNo = new CrossDockPackingCartonNo();
				crossDockPackingCartonNo.setCartonNo(cartonNos[i]);
				crossDockPackingCartonNo.setCdPackingId(crossDockPacking.getId());
				crossDockPackingCartonNoDAO.save(crossDockPackingCartonNo);
			}
		}
		for(Map.Entry<Long,List<StockCrossDockDTO>> entry:mapCDPackDTO.entrySet()){
			crossDockDetailService.delivery(entry.getKey(),entry.getValue(),true);
		}
	}
	
	/**
     * toWhId不为空的时候就是越库的。
     * @return
     */
    private String autoGenerateCommonLpn() {
        return sequenceGeneratorService.generateSequenceNo(SequenceName.CD_PACKING.getValue(),
        		ParamUtil.getCurrentWarehouseId());
    }

    /**
     * 获取箱标签打印实体
     * @param ids
     * @return
     */
	@Override
	public List<CrossDockCartonPrintDTO> getCrossDockCartonPrintDTOById(
			List<Long> ids) {
		return crossDockPackingDAO.getCrossDockCartonPrintDTOById(ids);
	}
}
