package com.daxia.wms.delivery.container.service.impl;

import com.daxia.framework.common.util.*;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.ContainerBusinessStatus;
import com.daxia.wms.Constants.ContainerType;
import com.daxia.wms.Constants.PktStatus;
import com.daxia.wms.Keys;
import com.daxia.wms.OrderLogConstants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.master.dto.ContainerBindDTO;
import com.daxia.wms.delivery.container.dto.Detail4BindDTO;
import com.daxia.wms.delivery.container.entity.ContainerLog;
import com.daxia.wms.delivery.container.service.ContainerLogService;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.delivery.container.service.PktContainerService;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.OrderLogService;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.pick.service.PickTaskService;
import com.daxia.wms.delivery.store.service.GoodsStoreService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.master.dao.ContainerDAO;
import com.daxia.wms.master.entity.Container;
import com.daxia.wms.master.entity.MergeLoc;
import com.daxia.wms.master.entity.Sku;
import com.daxia.wms.master.filter.ContainerFilter;
import com.daxia.wms.master.service.ContainerService;
import com.daxia.wms.master.service.MergeLocService;
import com.daxia.wms.master.service.PackingDeskSortLogService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *	容器管理(绑定，释放)业务实现类
 */
@Name("com.daxia.wms.delivery.containerMgntService")
@lombok.extern.slf4j.Slf4j
public class ContainerMgntServiceImpl implements ContainerMgntService {
    @In
    private WaveService waveService;
    
    @In
    private ContainerService containerService;

    @In
    private PickTaskService pickTaskService;

    @In
    private ContainerDAO containerDAO;

    @In
    private GoodsStoreService goodsStoreService;

    @In
    private MergeLocService mergeLocService;

    @In
    private ContainerLogService containerLogService;
    
    @In
    private DeliveryOrderService deliveryOrderService;

    @In
    private PktContainerService pktContainerService;

    @In
    private OrderLogService orderLogService;
    
    @In
    private PackingDeskSortLogService packingDeskSortLogService;

    /**
     * 释放绑定的容器
     */
    @Override
    @Transactional
    public Container releaseContainer(Long containerId) throws DeliveryException{
        Container container = containerService.getContainer(containerId);
        manualRelease(container);
        return container;
    }
    
    /**
     * 释放时更新容器信息，并记录日志
     * 
     * @param container
     * @throws DeliveryException
     */
    @Override
    @Transactional
    public void release(Container container) throws DeliveryException{
        // 判断容器是否存在
        if (null == container) {
            throw new DeliveryException(DeliveryException.CONTAINER_NOT_EXIT);
        }
        // 校验容器在此之前是否已经释放
        if (Constants.ContainerBusinessStatus.IDLE.getValue().equals(container.getBusinessStatus()) && StringUtil.isEmpty(container.getDocNo())) {
            throw new DeliveryException(DeliveryException.CONTAINER_ALREADY_RELEASED);
        }
        String docNo = container.getDocNo();
        String docType = container.getDocType();

        //清空容器明细
        pktContainerService.delByContainerNo(container.getContainerNo());

        container = updateContainer(container, null, null, null,Constants.ContainerBusinessStatus.IDLE.getValue(), null,null, Constants.YesNo.NO.getValue());
        // 记录释放日志
        createContainerLog(container.getContainerNo(), docType, docNo, Constants.ContainerOperationType.RELEASE.getValue(), null);
    
        if (Config.isDefaultFalse(Keys.Delivery.rechck_auto_allocate, Config.ConfigLevel.WAREHOUSE)) {
            packingDeskSortLogService.removeLog(container.getContainerNo());
        }
    }
    
    /**
     * 手动释放容器
     * 
     * @param container
     * @throws DeliveryException
     */
    @Override
    @Transactional
    public void manualRelease(Container container) throws DeliveryException {
        String containerType = container.getContainerType().getContainerType();
        String docType = container.getDocType();
        WaveHeader wh = null;
        
        if (ContainerType.WAVE_CONTAINER.getValue().equals(containerType) && Constants.BindDocType.WAVE.getValue().equals(docType)) {
            wh = waveService.getWaveHeaderByWaveNum(container.getDocNo());
        }
        
        release(container);
        if (wh != null) {
            waveService.updateWaveHeader(wh);
        }
    }

    /**
     * 执行操作（绑定，释放）后更新容器的信息
     * 
     * @param container
     *            容器
     * @param docType
     *            单据类型
     * @param docNo
     *            单据号
     * @param operator
     *            操作
     */
    @Override
    @Transactional
    public Container updateContainer(Container container, String docType, String docNo,String refNo1,
                                     String businessStatus, String operator,String gridNo, Integer isCurrentCarton) {
        container.setDocNo(docNo);
        container.setDocType(docType);
        container.setRefNo1(refNo1);
        container.setBusinessStatus(businessStatus);
        container.setOperationTime(DateUtil.getNowTime());
        container.setGridNo(gridNo);
        if (isCurrentCarton != null) {
            container.setIsCurrentCarton(isCurrentCarton);
        }
        if (StringUtil.isEmpty(operator)) {
            container.setOperator(containerDAO.getOperateUser());
        } else {
            container.setOperator(operator);
        }
        containerService.update(container);
        return container;
    }
    
    /**
     * 绑定容器到单据
     */
    @Override
    @Transactional
    public void bindContainer(Long containerId, String docType, String docNo, List<Long> ids) throws DeliveryException{
        
        Container container = containerService.getContainer(containerId);
        if (null == container) {
            throw new DeliveryException(DeliveryException.CONTAINER_NOT_EXIT);
        }
        
        if (Constants.ContainerType.WAVE_CONTAINER.getValue().equals(container.getContainerType().getContainerType())) {
            //检查是否没有勾选绑定数据明细
            if (ListUtil.isNullOrEmpty(ids)) {
                throw new DeliveryException(DeliveryException.NO_DATA_FOR_BIND);
            }
            // TODO
            if (!Constants.BindDocType.WAVE.getValue().equals(docType)) {
                throw new DeliveryException(DeliveryException.DOC_TYPE_CONTAINER_TYPE_NOT_CORRESPOND);
            }
            // 拣货箱绑定波次
            bindDoc(container,docType,docNo,Constants.ContainerBusinessStatus.PICKING.getValue(),
                    Constants.ContainerType.WAVE_CONTAINER.getValue());
            // 根据单据类型执行相应的绑定操作
            bindContainer2Wave(container.getContainerNo(), docNo, ids);
        } else if (Constants.ContainerType.SORT_CONTAINER.getValue().equals(container.getContainerType().getContainerType())) {
            // 分拣筐绑定DO
            if (!Constants.BindDocType.DELIVERYORDER.getValue().equals(docType)) {
                throw new DeliveryException(DeliveryException.DOC_TYPE_CONTAINER_TYPE_NOT_CORRESPOND);
            }
            validateDoHeader(docNo, docType);
            
            bindDoc(container,docType,docNo,Constants.ContainerBusinessStatus.SORT.getValue(),
                    Constants.ContainerType.SORT_CONTAINER.getValue());
        } else {
            // 暂支持上面两种容器类型
            throw new DeliveryException(DeliveryException.DOC_TYPE_CONTAINER_TYPE_NOT_CORRESPOND); 
        }
        
        // 此处是为了解决不刷新问题
        containerDAO.getSession().flush();
        containerDAO.getSession().clear();
        
    }
    
    /**
     * 绑定到单据
     * @param container
     * @param docType
     * @param docNo
     * @param businessStatus
     * @param containerType
     */
    @Override
    @Transactional
    public void bindDoc(Container container, String docType, String docNo, 
    		String businessStatus, String containerType) {
        // 只有启用状态的容器才能绑定
        if (Constants.YesNo.NO.getValue().equals(container.getIsAvailable())) {
            throw new DeliveryException(DeliveryException.CONTAINER_NOT_AVAILABLE);
        }
        //判断容器类型是否为所需要的类型
        if (!container.getContainerType().getContainerType().equals(containerType)) {
            throw new DeliveryException(DeliveryException.CONTAINER_BUSINESSTYPE_ERROR);
        }
        
        if(!Constants.ContainerBusinessStatus.IDLE.getValue().equals(container.getBusinessStatus())){
            throw new DeliveryException(DeliveryException.CONTAINER_BUSINESSSTATUS_ERROR);
        }
        // 绑定后修改容器信息
        container = updateContainer(container, docType, docNo,null,businessStatus, null,null,null);
        // 记录容器使用日志
        createContainerLog(container.getContainerNo(), docType, docNo, Constants.ContainerOperationType.BIND.getValue(), null);
    }
    
    /**
     * 创建容器使用日志
     * 注：若操作人为空，则获取系统登录用户作为操作人，否则使用传入的值
     * @param containerNo 容器号
     * @param docType 单据类型
     * @param docNo 单据号
     * @param operation 容器操作类型
     * @param operator 操作人 
     */
    @Override
    @Transactional
    public void createContainerLog(String containerNo, String docType, String docNo, String operation, String operator) {
        ContainerLog containerLog = new ContainerLog();
        containerLog.setContainerNo(containerNo);
        containerLog.setDocType(docType);
        containerLog.setDocNo(docNo);
        containerLog.setOperation(operation);
        containerLog.setOperationTime(DateUtil.getNowTime());
        if (StringUtil.isEmpty(operator)) {
            containerLog.setOperator(containerDAO.getOperateUser());  
        } else {
            containerLog.setOperator(operator); 
        }
        
        containerLogService.save(containerLog);
    }

    /**
     * 校验WMS是否启用容器管理
     */
    @Override
    public void vContainerMgntUsed() {
        // WMS是否启用容器管理
        if (Constants.YesNo.NO.getValue().equals(getContainerMgntStartFlag())) {
            throw new DeliveryException(DeliveryException.WMS_NOT_START_CONTAINER_MGNT);
        }
    }
    
    /**
     * 获取wms是否启用容器管理配置项参数值,0--未启用，1---启用
     * 如果无此参数抛异常
     * @return
     */
    @Override
    public Integer getContainerMgntStartFlag() throws DeliveryException {
        Integer containerFlag = SystemConfig.getConfigValueInt("containerMgntStartFlag", ParamUtil.getCurrentWarehouseId());
        if (null == containerFlag) {
            throw new DeliveryException(DeliveryException.WMS_CONTAINER_MGNT_FLAG_NULL);
        }
        return containerFlag;
    }

    /**
     * 根据波次号查询待绑定的明细(该明细是将拣货任务进行合并处理构造而成的Detail4BindDTO)
     * 
     * @param containerId
     * @param docNo
     * @param docType
     * @return
     */
    @Override
    public DataPage<Detail4BindDTO> findDetails4Bind(Long containerId, String docNo, String docType, int startIndex, int pageSize) {
        Container container = containerService.getContainer(containerId);
        // 只有启用状态的容器才能绑定。查询待绑定明细时，若容器已经停用，则不必进行后续绑定操作
        if (Constants.YesNo.NO.getValue().equals(container.getIsAvailable())) {
            throw new DeliveryException(DeliveryException.CONTAINER_NOT_AVAILABLE);
        }
        // 校验容器是否和本单据外的其他单据绑定
        if (StringUtil.isNotEmpty(container.getDocNo()) && !container.getDocNo().equals(docNo)) {
            throw new DeliveryException(DeliveryException.CONTAINER_ALREADY_BINDED);
        }
        // 目前只支持波次绑定容器
        if (null == docType || StringUtil.isNotIn(docType, Constants.BindDocType.WAVE.getValue(),
                Constants.BindDocType.DELIVERYORDER.getValue())) {
            throw new DeliveryException(DeliveryException.NOW_ONLY_SUPPORT_WAVE_BIND_CONTAINER);
        }
        // 根据绑定单据类型获取待绑定明细
        if (Constants.BindDocType.WAVE.getValue().equals(docType)) {
            // 判断波次号是否正确
            WaveHeader wave = waveService.getWaveHeaderByWaveNum(docNo);
            if (null == wave) {
                throw new DeliveryException(DeliveryException.DOC_NO_NOT_EXIT);
            }
            //取得波次下已发布的拣货任务
            DataPage<PickTask> dataPages = pickTaskService.findPickTask4BindList(wave.getId(), PktStatus.RELEASED, startIndex, pageSize);
            //校验是否已经拣货完成
            if (ListUtil.isNullOrEmpty(dataPages.getDataList())) {
                throw new DeliveryException(DeliveryException.WAVE_ALL_PICKED);
            }
            return buildDto4WaveBind(dataPages);
        } else if (Constants.BindDocType.DELIVERYORDER.getValue().equals(docType)) {
            // 绑定单据类型：发货单类型
            
            DeliveryOrderHeader doHeader = validateDoHeader(docNo,docType);
            
            //4.取数据页面显示
            return buildDto4WaveBind(doHeader);
        } else {
            // 其他绑定单据类型
            return null;
        }
    }

    /**
     * 校验发货单
     * @param docNo
     * @param docType
     */
    private DeliveryOrderHeader validateDoHeader(String docNo,String docType) {
        //1.取得订单头
        DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(docNo);
        
        if(null == doHeader){
            throw new DeliveryException(DeliveryException.DOC_NO_NOT_EXIT);
        }
        
        //2.状态为拣货完成到装箱完成
        if (StringUtil.isNotIn(doHeader.getStatus(), Constants.DoStatus.ALLPICKED.getValue(),
                Constants.DoStatus.PARTSORTED.getValue(), Constants.DoStatus.ALLSORTED.getValue(),
                Constants.DoStatus.PART_CARTON.getValue(),Constants.DoStatus.ALL_CARTON.getValue())) {
            throw new DeliveryException(DeliveryException.DOC_STATUS_NOT_RIGHT);
        }
        
        //3.未绑定分拣筐的DO号
        if(containerService.isExistsByDocNo(docNo, docType)){
            throw new DeliveryException(DeliveryException.DOC_ALREADY_BIND_CONTAINER);
        }
        
        return doHeader;
    }

    /**
     * 构建页面显示dataPage
     * @param doHeader
     * @return
     */
    private DataPage<Detail4BindDTO> buildDto4WaveBind(DeliveryOrderHeader doHeader) {
        List<Detail4BindDTO> detailDtos = new ArrayList<Detail4BindDTO>();
        
        Detail4BindDTO dto = new Detail4BindDTO();          
        dto.setId(doHeader.getId());//订单Id
        dto.setDoNo(doHeader.getDoNo());
        dto.setDoStatus(doHeader.getStatus());
        dto.setReleaseStatus(doHeader.getReleaseStatus());
        dto.setExpectedTime(doHeader.getPlanShipTime());
        dto.setIsHalfDayDelivery(doHeader.getIsHalfDayDelivery());
        dto.setDoType(doHeader.getDoType());
        
        detailDtos.add(dto);
        
        return new DataPage<Detail4BindDTO>(1, 0, 1, detailDtos);         
    }

    /**
     * 根据拣货任务构建绑定明细dto
     * 
     * @param dataPages
     * @return
     */
    public DataPage<Detail4BindDTO> buildDto4WaveBind(DataPage<PickTask> dataPages) {
        List<Detail4BindDTO> detailDtos = new ArrayList<Detail4BindDTO>();
        for (PickTask pickTask : dataPages.getDataList()) {
            Detail4BindDTO dto = new Detail4BindDTO();          
            dto.setId(pickTask.getId());
            dto.setQty(pickTask.getQty());
            Sku sku = pickTask.getSku();
            dto.setProductBarcode(sku.getEan13());
            dto.setProductCname(sku.getProductCname());
            dto.setContainerNo(pickTask.getContainerNo());
            dto.setLocCode(pickTask.getLocation().getLocCode());
            if (null != sku.getCategory()) {
                dto.setProductType(sku.getCategory().getCategoryName());
            }
            detailDtos.add(dto);
        }
        return new DataPage<Detail4BindDTO>((int)dataPages.getTotalCount(), dataPages.getStartIndex(), dataPages.getPageSize(), detailDtos);         
    }

    /**
     * 绑定容器到波次
     * @param containerNo
     * @param docNo
     * @param ids
     */
    private void bindContainer2Wave(String containerNo, String docNo, List<Long> ids) {
        WaveHeader waveHeader = waveService.getWaveHeaderByWaveNum(docNo);
        // 判断波次号是否正确
        if (null == waveHeader) {
            throw new DeliveryException(DeliveryException.DOC_NO_NOT_EXIT);
        }

        // 绑定到拣货任务
        bindContainer2PickTasks(ids, containerNo, waveHeader.getId());
    }

    /**
     * 绑定拣货任务到容器
     * 
     * @param ids
     *            勾选sku对应的拣货任务ids
     * @param containerNo 容器号
     * @param waveHeaderId 波次id
     */
    private void bindContainer2PickTasks(List<Long> ids, String containerNo, Long waveHeaderId) {
        for (Long id : ids) {
            PickTask pickTask = pickTaskService.getTaskById(id);
            // 看拣货任务是否还存在
            if (null == pickTask) {
                throw new DeliveryException(DeliveryException.PICK_TASK_IS_CANCELED);
            }
            // 拣货任务是否已经拣货
            if (!Constants.PktStatus.RELEASED.getValue().equals(pickTask.getStatus())) {
                throw new DeliveryException(DeliveryException.SKU_IS_PICKED);
            }
            // 拣货任务对应的原do是否已经移除波次或状态回退
            if (null == pickTask.getWaveHeaderId() || 0 != waveHeaderId.compareTo(pickTask.getWaveHeaderId())) {
                throw new DeliveryException(DeliveryException.SKU_IS_REMOVED_FROM_WAVE);
            }
            // 绑定之前校验商品是否已经绑定
            if (StringUtil.isNotEmpty(pickTask.getContainerNo())) {
                throw new DeliveryException(DeliveryException.SKU_IS_BINDED);
            }
            //采用实体更新，避免并发
            pickTask.setContainerNo(containerNo);
            pickTaskService.updatePickTask(pickTask);
        }
    }
    
    /**
     * 通过docNo,containerType,docType找到对应的绑定的容器
     * 
     * @param docNo
     * @param containerType
     * @param docType
     * @return
     */
    @Override
    public List<Container> findContainerByDoc(String docNo, String containerType, String docType) {
        return containerDAO.findByDocNo(docNo, containerType, docType);
    }
    
    /**
     * 核拣时释放容器，并记录日志
     *
     * @param doHeaderId
     * @throws DeliveryException
     */

    /**
     * 分拣时释放容器，并记录日志
     *
     * @param waveHeader
     * @throws DeliveryException
     */
    @Override
    @Transactional
    public void releaseContainerForSorting(WaveHeader waveHeader) throws DeliveryException {
        if (waveHeader == null) {
            return;
        }
            // 允许释放容器
            List<Container> list = containerDAO.findContainersByWaveHeaderId(waveHeader.getId());
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            for (Container container : list) {
                // 对容器解绑，记录容器日志
                release(container);
            }
    }
//    @Override
//    @Transactional
//    public void releaseContainerForReCheck(DeliveryOrderHeader doHeader) throws DeliveryException {
//        if (doHeader == null)
//            return;
//        List<Container> containerList = findDoBindedPickContainer(doHeader.getId());
//        if(!ListUtil.isNullOrEmpty(containerList)){
//            for(Container container : containerList){
//                release(container);
//            }
//        }
//    }
    
    /**
     * 验证是否允许释放容器
     * @param waveHeader
     * @return
     */
    private boolean validateIsCanReleseContainer(WaveHeader waveHeader) {
    	if (Constants.YesNo.YES.getValue().equals(getContainerMgntStartFlag())) {
    		if (Constants.WaveStatus.ALLPICKED.getValue().equals(waveHeader.getWaveStatus())) {
				return true;
			} else if (Constants.WaveStatus.PARTSORTED.getValue().equals(waveHeader.getWaveStatus())){
				//分拣中的波次还没释放容器 需要释放
				List<Container> containers = containerService.getContainersByDoNo
						(waveHeader.getWaveNo(),ContainerType.WAVE_CONTAINER);
				if (ListUtil.isNotEmpty(containers)) {
					return true;
				}
			}
    	}
       
        return false;
    }
    
    /**
     * 验证容器是否可以绑定指定单据
     * @param containerNo
     * @param docNo
     * @param businessStatus
     * @param containerType
     * @return
     * @throws DeliveryException
     */
    @Override
    public Container check4Bind(String containerNo, String docNo, String businessStatus, String containerType)
            throws DeliveryException {
        Container container = containerService.getContainerByNo(containerNo);
        if (null == container) {
            throw new DeliveryException(DeliveryException.CONTAINER_NOT_EXIT);
        }
        if (Constants.YesNo.NO.getValue().equals(container.getIsAvailable())) {
            throw new DeliveryException(DeliveryException.CONTAINER_NOT_AVAILABLE);
        }
        if (StringUtil.isNotEmpty(containerType) && !containerType.equals(container.getContainerType().getContainerType())) {
            throw new DeliveryException(DeliveryException.CONTAINER_TYPE_NOT_MATCH);
        }
        if (Constants.ContainerBusinessStatus.IDLE.getValue().equals(container.getBusinessStatus())) {
            return container;
        }
        else if (null != businessStatus && businessStatus.equals(container.getBusinessStatus()) &&
                Constants.BindDocType.WAVE.getValue().equals(container.getDocType()) &&
                docNo.equals(container.getDocNo())) {
                return container;
        }
        else {
            //如果未知原因导致容器未释放，在这里释放容器
            if (StringUtils.isNotEmpty(container.getDocNo())) {
                if (Constants.BindDocType.DELIVERYORDER.getValue().equals(container.getDocType())) {
                    DeliveryOrderHeader dh = deliveryOrderService.findDoHeaderByDoNo(container.getDocNo());
                    if (dh!=null && dh.getStatus().compareTo(Constants.DoStatus.ALL_CARTON.getValue()) >= 0) {
                        release(container);
                        return container;
                    }
                } else if (Constants.BindDocType.WAVE.getValue().equals(container.getDocType())) {
                    WaveHeader wh = waveService.getWaveHeaderByWaveNum(container.getDocNo());
                    if (wh != null &&
                            wh.getWaveStatus().compareTo(Constants.WaveStatus.ALLSORTED.getValue()) >= 0) {
                        if ( wh.getDoCount().intValue() > 1 ||
                                wh.getDoHeaders().get(0).getStatus().compareTo(Constants.DoStatus.ALL_CARTON.getValue()) >= 0) {
                            release(container);
                            return container;
                        }
                    }
                }
            }

            if (StringUtil.isNotEmpty(container.getDocNo())) {
                throw new DeliveryException(DeliveryException.CONTAINER_HAS_BIND, container.getDocNo());
            } else {
                throw new DeliveryException(DeliveryException.CONTAINER_BUSINESS_STATUS_ERROR);
            }
        }
    }

    /**
     * 验证已绑定业务单据的容器
     * @param containerNo
     * @param businessStatus
     * @param containerType
     * @param docNo
     * @return
     */
    @Override
    public Container checkBindedContainer(String containerNo, String businessStatus, String containerType, String docNo) throws DeliveryException {
        Container container = containerService.getContainerByNo(containerNo);
        if (null == container) {
            throw new DeliveryException(DeliveryException.CONTAINER_NOT_EXIT);
        }
        if (Constants.YesNo.NO.getValue().equals(container.getIsAvailable())) {
            throw new DeliveryException(DeliveryException.CONTAINER_NOT_AVAILABLE);
        }
        if (!containerType.equals(container.getContainerType().getContainerType())) {
            throw new DeliveryException(DeliveryException.CONTAINER_TYPE_NOT_MATCH);
        }
        if (Constants.ContainerBusinessStatus.IDLE.getValue().equals(container.getBusinessStatus())) {
            //未绑定抛异常
            throw new DeliveryException(DeliveryException.CONTAINER_NOT_BIND);
        } else if (!businessStatus.equals(container.getBusinessStatus())) {
            //不是指定业务类型抛异常
            throw new DeliveryException(DeliveryException.CONTAINER_BUSINESS_STATUS_ERROR);
        }
        if (StringUtil.isNotEmpty(docNo) && (!docNo.equals(container.getDocNo()))) {
            throw new DeliveryException(DeliveryException.CONTAINER_HAS_BIND, container.getDocNo());
        }
        return container;
    }

    /**
     * 绑定容器并记录绑定日志
     * @param container
     * @param docType
     * @param docNo
     * @param businessStatus
     * @throws DeliveryException
     */
    @Override
    @Transactional
    public void bind(Container container, String docType, String docNo,String refNo1, String businessStatus, String operator) throws DeliveryException {
        bind(container,docType,docNo,refNo1,businessStatus,operator,null,null);
    }

    @Override
    @Transactional
    public void bind(Container container, String docType, String docNo,String refNo1, String businessStatus,
                     String operator,String gridNo, Integer isCurrentCarton) throws DeliveryException {
        updateContainer(container, docType, docNo,refNo1 , businessStatus, operator,gridNo,isCurrentCarton);
        //如果按订单绑定，记录订单日志
        if (StringUtil.equals(Constants.BindDocType.DELIVERYORDER.getValue(),docType)){
            DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(docNo);
            if (doHeader != null) {
                orderLogService.saveLog(doHeader,
                        OrderLogConstants.OrderLogType.CONTAINER_BIND.getValue(),
                        ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_CONTAINER_BIND,null,container.getContainerNo()));
            }
        }
        // 记录容器使用日志
        this.createContainerLog(container.getContainerNo(), docType, docNo, Constants.ContainerOperationType.BIND.getValue(), operator);
    }
    
    @Override
    public List<List> findByWaveIdsAndStatus(List<Long> waveIds, List<String> businessStatus) {
        return containerDAO.findByWaveIdsAndStatus(waveIds, businessStatus);
    }

    @Override
    public List<Container> getCartonedDoCantainer(Long doId) {
        return containerDAO.getContainerByCartonedDo(doId);
    }

    @Override
    public void updateBusinessStatusByWaveId(Long id, ContainerBusinessStatus outMerge) {
        containerDAO.updateBusinessStatusByWaveId(id, outMerge);
    }
    
    @Override
    @Transactional
    public void recheckReleaseContainer(String doNo) throws DeliveryException {
        List<Container> containners = containerDAO.findByDocNo(doNo, Constants.ContainerType.SORT_CONTAINER.getValue(), Constants.BindDocType.DELIVERYORDER.getValue());
        if (ListUtil.isNotEmpty(containners)) {
            for (Container c : containners) {
                release(c);
            }
        }
    }

    @Override
    @Transactional
    public void recheckReleaseContainerByPackageType(String doNo, Integer packageType) throws DeliveryException {
        List<Container> containners = containerDAO.findContainerListByDoIdAndPktType(doNo,packageType);
        if (ListUtil.isNotEmpty(containners)) {
            for (Container c : containners) {
                release(c);
            }
        }
    }

    @Override
    @Transactional
    public void recheckBindSortContainer(String doNo, String sortContainerNo) throws DeliveryException {
        if (StringUtil.isEmpty(doNo)) {
            throw new DeliveryException(DeliveryException.DO_NO_IS_NULL);
        }
        if (StringUtil.isEmpty(sortContainerNo)) {
            throw new DeliveryException(DeliveryException.SORTCONTAINER_NULL);
        }
        DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(doNo);
        if (null == doHeader) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        if (StringUtil.isNotIn(doHeader.getStatus(), Constants.DoStatus.ALLSORTED.getValue(), 
                Constants.DoStatus.PART_CARTON.getValue(), Constants.DoStatus.ALL_CARTON.getValue())) {
            throw new DeliveryException(DeliveryException.DOC_STATUS_NOT_RIGHT);
        }
        Container sortContainer = containerDAO.getContainerByNo(sortContainerNo);
        if (null == sortContainer) {
            throw new DeliveryException(DeliveryException.SORTCONTAINER_NOT_EXISTS);
        }
        if (!Constants.YesNo.YES.getValue().equals(sortContainer.getIsAvailable())) {
            throw new DeliveryException(DeliveryException.CONTAINER_NOT_AVAILABLE);
        }
        if (!Constants.ContainerBusinessStatus.IDLE.getValue().equals(sortContainer.getBusinessStatus())) {
            throw new DeliveryException(DeliveryException.SORTCONTAINER_BIND_DO);
        }
        if (!Constants.ContainerType.SORT_CONTAINER.getValue().equals(sortContainer.getContainerType().getContainerType())) {
            throw new DeliveryException(DeliveryException.CONTAINER_NOT_SORT_CONTAINER);
        }
        Map<String,Object> params = new HashMap<String, Object>();
        params.put("docNo", doHeader.getDoNo());
        params.put("containerType", Constants.ContainerType.SORT_CONTAINER.getValue());
        params.put("warehouseId", ParamUtil.getCurrentWarehouseId());
        if (containerDAO.isExists(params, null)) {
            throw new DeliveryException(DeliveryException.DO_BIND_SORTCONTAINER);
        }
        this.bind(sortContainer, Constants.BindDocType.DELIVERYORDER.getValue(), doHeader.getDoNo(), null, Constants.ContainerBusinessStatus.SORT.getValue(), ParamUtil.getCurrentLoginName());
    }

    @Override
    @Transactional
    public void releaseContainerByWave(WaveHeader wave, String containerType, String docType) {
        if (Constants.YesNo.YES.getValue().equals(getContainerMgntStartFlag())) {
            List<Container> containerList = containerDAO.findContainers(wave.getWaveNo(), containerType, docType);
            if (ListUtil.isNotEmpty(containerList)) {
                for (Container c : containerList) {
                    this.release(c);
                }
            }
        }
    }

	@Override
	public List<Object> queryMergeInfoByConNo(String waveNo) {
		return containerDAO.queryMergeInfoByConNo(waveNo);
	}

    @Override
    @Transactional
    public void releaseByDoAndPkt(String doNo, String pickHeaderNo) {
        ContainerFilter containerFilter = new ContainerFilter();
        containerFilter.setDocType(Constants.BindDocType.DELIVERYORDER.getValue());
        containerFilter.setDocNo(doNo);
        containerFilter.setRefNo1(pickHeaderNo);
        List<Container> containerList = containerDAO.findByFilter(containerFilter);
        if (ListUtil.isNotEmpty(containerList)) {
            for (Container c : containerList) {
                this.release(c);
            }
        }
    }

    @Override
    public void releaseByPkt(String pktNo, String pktType) {
        ContainerFilter containerFilter = new ContainerFilter();
        containerFilter.setDocType(Constants.BindDocType.DELIVERYORDER.getValue());
        containerFilter.setRefNo1(pktNo);
        List<Container> containerList = containerDAO.findByFilter(containerFilter);
        if (ListUtil.isNotEmpty(containerList)) {
            for (Container c : containerList) {
                String docNo = c.getDocNo();
                String docType = c.getDocType();
                this.release(c);
                String mergeLocCode = mergeLocService.findMergeLoc(docNo, docType, pktType);
                MergeLoc mergeLoc = mergeLocService.findMergeLocByCode(mergeLocCode);
                if (mergeLoc != null) {
                    goodsStoreService.flushMergeLoc(mergeLoc);
                }
            }
        }
    }

    @Override
    public List<ContainerBindDTO> findContainerBindList(String containerNo) {
        return containerDAO.findContainerBindList(containerNo);
    }

    @Override
    @Transactional
    public void unBoxing(Container container, String docNo, String docType,String refNo,String updateBy) {
        Container currentContainer = getCurrentContainerByDocNo(docNo,refNo,docType);
        //刷新当前箱标记
        clearCurrentCartonFlag(currentContainer);
        //绑定容器
        bind(container,docType,docNo,refNo,ContainerBusinessStatus.PICKING.getValue(),
                updateBy,currentContainer.getGridNo(), Constants.YesNo.YES.getValue());
    }

    @Override
    public Container check4UnBoxing(String containerNo) {
        if (StringUtil.isEmpty(containerNo)) {
            throw new DeliveryException(DeliveryException.CONTAINER_NOT_EXIT);
        }
        Container container = containerService.getContainerByNo(containerNo);
        if (container == null) {
            throw new DeliveryException(DeliveryException.CONTAINER_NOT_EXIT);
        }

        if (Constants.YesNo.NO.getValue().equals(container.getIsAvailable())) {
            throw new DeliveryException(DeliveryException.CONTAINER_NOT_AVAILABLE);
        }

        if (!ContainerBusinessStatus.IDLE.getValue().equals(container.getBusinessStatus())) {
            throw new DeliveryException(DeliveryException.CONTAINER_ALREADY_BINDED);
        }

        return container;
    }

    @Override
    public Container getcurrentContainerByDoNoAndPktNo(String doNo, String pktNo) {
        ContainerFilter containerFilter = new ContainerFilter();
        containerFilter.setDocNo(doNo);
        containerFilter.setRefNo1(pktNo);
        containerFilter.setIsCurrentCarton(Constants.YesNo.YES.getValue());
        List<Container> list = containerDAO.findByFilter(containerFilter);
        return CollectionUtils.isEmpty(list)?null:list.get(0);
    }

    @Override
    public List<Container> findByDoNo(String doNo) {
        ContainerFilter containerFilter = new ContainerFilter();
        containerFilter.setDocNo(doNo);
        containerFilter.setDocType(Constants.BindDocType.DELIVERYORDER.getValue());
        return containerDAO.findByFilter(containerFilter);
    }

    private void clearCurrentCartonFlag(Container currentContainer) {
        containerDAO.clearCurrentCartonFlag(currentContainer.getDocNo(),currentContainer.getRefNo1());
    }

    private Container getCurrentContainerByDocNo(String docNo,String refNo,String docType) {
        ContainerFilter filter = new ContainerFilter();
        filter.setDocNo(docNo);
        if (Constants.BindDocType.DELIVERYORDER.getValue().equals(docType)){
            filter.setRefNo1(refNo);
        }
        List<Container> list = containerDAO.findByFilter(filter);
        if (CollectionUtils.isEmpty(list)){
            throw  new DeliveryException(DeliveryException.CONTAINER_NOT_EXIT);
        }
        return list.get(0);
    }

    @Override
	public boolean isDoBindContainer(String docNo, String containerType, String docType) {
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("docNo", docNo);
		params.put("containerType", containerType);
		params.put("docType", docType);
		params.put("warehouseId", ParamUtil.getCurrentWarehouseId());
		return containerDAO.isExists(params, null);
	}
}
