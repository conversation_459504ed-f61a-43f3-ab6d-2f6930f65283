package com.daxia.wms.delivery.print.dto;

import com.daxia.framework.common.util.AppConfig;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;

@lombok.extern.slf4j.Slf4j
public class PrintDoDetailDTO {

    private String productName;
    private String productEname;

    private String productCode;

    private String specification;

    private String merchantName;

    private Long skuId;

    private String lotNo;

    private String expiredTime;

    private BigDecimal price;

    private BigDecimal qty;

    private BigDecimal expectedQty;

    private BigDecimal allocatedQty;

    private BigDecimal amountPrice;

    private String parentId;

    private String origDetailId;

    private Boolean isDoLeaf;

    private String manufacturer;//生产厂家

    private String registerNo;//批准文号

    private String barcode;

    //单位
    private String uom;
    //备注
    private String notes;

    private String locCode;

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public BigDecimal getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(BigDecimal expectedQty) {
        this.expectedQty = expectedQty;
    }

    public BigDecimal getAllocatedQty() {
        return allocatedQty;
    }

    public void setAllocatedQty(BigDecimal allocatedQty) {
        this.allocatedQty = allocatedQty;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Boolean getDoLeaf() {
        return isDoLeaf;
    }

    public void setDoLeaf(Boolean doLeaf) {
        isDoLeaf = doLeaf;
    }

    public String getOrigDetailId() {
        return origDetailId;
    }

    public void setOrigDetailId(String origDetailId) {
        this.origDetailId = origDetailId;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getProductName() {
        if(AppConfig.useEnglish() && StringUtils.isNotBlank(this.getProductEname())){
            return getProductEname();
        }
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductEname() {
        return productEname;
    }

    public void setProductEname(String productEname) {
        this.productEname = productEname;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getLotNo() {
        return lotNo;
    }

    public void setLotNo(String lotNo) {
        this.lotNo = lotNo;
    }

    public String getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(String expiredTime) {
        this.expiredTime = expiredTime;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getAmountPrice() {
        return amountPrice;
    }

    public void setAmountPrice(BigDecimal amountPrice) {
        this.amountPrice = amountPrice;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getRegisterNo() {
        return registerNo;
    }

    public void setRegisterNo(String registerNo) {
        this.registerNo = registerNo;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getLocCode() {
        return locCode;
    }

    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }
}
