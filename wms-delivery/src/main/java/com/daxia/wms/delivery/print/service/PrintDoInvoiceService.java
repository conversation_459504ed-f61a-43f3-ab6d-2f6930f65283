package com.daxia.wms.delivery.print.service;

import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.print.PrintConstants.PrintInvoicePos;
import com.daxia.wms.print.dto.PrintData;

public interface PrintDoInvoiceService {
	
    /**
     * 按DO打印发票
     * @param doHeader
     * @return
     */
    public PrintData printInvoice(DeliveryOrderHeader doHeader, String printerCode, PrintInvoicePos pos);
}
