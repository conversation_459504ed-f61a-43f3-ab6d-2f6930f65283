package com.daxia.wms.delivery.container.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.container.entity.PktContainerDetail;
import com.daxia.wms.delivery.container.filter.PktContainerDetailFilter;
import com.daxia.wms.delivery.container.service.PktContainerService;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

@Name("com.daxia.wms.delivery.pktContainerDetailAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class PktContainerDetailAction extends PagedListBean<PktContainerDetail> {

	private static final long serialVersionUID = 3577077249294453549L;
	@In
	private PktContainerService pktContainerService;

	private PktContainerDetailFilter containerDetailFilter;


	public PktContainerDetailAction() {
		super();
		containerDetailFilter = new PktContainerDetailFilter();
	}
	
	@Override
	public void query() {
		this.buildOrderFilterMap(containerDetailFilter);
		DataPage<PktContainerDetail> dataPage = pktContainerService.queryByFilter(containerDetailFilter, getStartIndex(), getPageSize());
		populateValues(dataPage);
	}

	public PktContainerDetailFilter getContainerDetailFilter() {
		return containerDetailFilter;
	}

	public void setContainerDetailFilter(PktContainerDetailFilter containerDetailFilter) {
		this.containerDetailFilter = containerDetailFilter;
	}
}
