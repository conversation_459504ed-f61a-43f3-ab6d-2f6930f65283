package com.daxia.wms.delivery.print.helper;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import cn.hutool.poi.word.DocUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.WaybillType;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.master.entity.Carrier;
import com.daxia.wms.master.entity.Station;
import com.daxia.wms.print.utils.PrintHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;


/**
 * 箱标签打印帮助类
 */
@lombok.extern.slf4j.Slf4j
public class PrintCartonHelper {
	
	/**
     * 是否首次购买
     */
    public static final String IS_FIRST_TIME_BUY = "1";
    
    /**
     * 货到付现
     */
    public static final String[] codCheckFlags = {"货到付现金", "货到刷卡", "货到付款"};
        
    /**
     * 构造父SO号（若 拆单，则拼接并显示包含子SO数量）
     * @param doHeader
     * @return
     */
    public static String buildParentSoNum(DeliveryOrderHeader doHeader) {
        String parentSo = StringUtil.trim(doHeader.getUserDeffine5());
        String childSoNum = StringUtil.trim(doHeader.getUserDeffine6());
        // 子SO数量为null、0、1均表示无拆单情况
        if (null == childSoNum || "0".equals(childSoNum) || "1".equals(childSoNum)) {
            return parentSo == null ? doHeader.getRefNo1() : parentSo;
        }
        //若父SO为空，则直接用子SO号作为父SO号
        parentSo = (parentSo == null ? doHeader.getRefNo1() : parentSo);
        //对于拆单后的do，若其父so存在，则显示包含子so数量
        if (StringUtil.isNotEmpty(parentSo)) {
            //若拆单数量超过2位数，则显示00
            if (childSoNum.length() > 2) {
                return parentSo + "-00";
            } else if(childSoNum.length() == 2){ //若子so数量刚好为2位数，则直接显示数量
                return parentSo + "-" + childSoNum;
            } else if(childSoNum.length() == 1) { //若子so数量为1位数，则在数量前面补一个0
                return parentSo + "-0" + childSoNum;
            }
        }
        return null;
    }

    /**
     * 获取发货单的手机号码或电话号码，构造客户的联系方式信息
     * 规则：收货人电话有手机就只显示手机，没有手机的显示固话
     */
    public static String buildTelOrMobile(DeliveryOrderHeader doHeader) {
        String phone = doHeader.getTelephone();
        String mobile = doHeader.getMobile();
        return DoUtil.decryptPhone(StringUtil.isEmpty(mobile) ? (StringUtil.isEmpty(phone) ? "" : phone) : mobile);
    }
    
    /**
     * 设置收货人信息,及是否首购信息
     * @param doHeader
     * @return
     */
    public static String buildConsigneeName(DeliveryOrderHeader doHeader) {
        String consigneeName = StringUtil.trim(doHeader.getConsigneeName());        
        if (StringUtil.isNotEmpty(consigneeName)) {
            //首购订单，收货人超过6位的截取前6位
            if (IS_FIRST_TIME_BUY.equals(doHeader.getUserDeffine1())) {
                if (consigneeName.length() > 6) {
                    consigneeName = StringUtil.substring(consigneeName, 0, 6);
                }
                consigneeName = consigneeName + "(首购)";
            }
        }
        return consigneeName;
    }
    
    /**
     * 构造地址信息
     * @param doHeader
     * @return
     */
    public static String buildAddress(DeliveryOrderHeader doHeader) {
        StringBuilder address = new StringBuilder("");
        if (null != doHeader.getProvinceInfo() || StringUtils.isNotEmpty(doHeader.getProvinceName())){
            address.append(StringUtils.isNotEmpty(doHeader.getProvinceName())?doHeader.getProvinceName() :
                    doHeader.getProvinceInfo().getProvinceCname());
        }
        //二、三级地址加粗加下划线的一些特殊处理（用"|"做标记）
        if (null != doHeader.getCityInfo() || StringUtils.isNotEmpty(doHeader.getCityName())) {
            //address.append("|");
            address.append(StringUtils.isNotEmpty(doHeader.getCityName()) ? doHeader.getCityName():
                    doHeader.getCityInfo().getCityCname());
            if (null != doHeader.getCountyInfo() || StringUtils.isNotEmpty(doHeader.getCountyName())) {
                address.append(StringUtils.isNotEmpty(doHeader.getCountyName()) ? doHeader.getCountyName():
                        doHeader.getCountyInfo().getCountyCname());
            }
            //address.append("|");
        } else {
            if (null != doHeader.getCountyInfo() || StringUtils.isNotEmpty(doHeader.getCountyName())) {
                //address.append("|");
                address.append(StringUtils.isNotEmpty(doHeader.getCountyName()) ? doHeader.getCountyName():
                        doHeader.getCountyInfo().getCountyCname());
                //address.append("|");
            }
        }
        address.append(StringUtil.replaceLineBreaks(StringUtil.trim(doHeader.getAddress())));
        return address.toString();
    }
    
    /**
     * 获取站点、配送商信息
     * @param doHeader
     * @return
     */
    public static String buildCarrierInfo(DeliveryOrderHeader doHeader) {
        String carryInfo = "";
        Carrier carrier = doHeader.getCarrier();
        Station station = doHeader.getStation();
        // 优先从站点取
        if (null != station) {
            carryInfo = station.getStationName();
        } else {
            if (null != carrier) {
                carryInfo = carrier.getDistSuppCompName();
            }
        }
        return carryInfo;
    }
    
    /**
     * 按箱创建时间升序排列箱
     * @param cartons
     */
    public static void sortCartons(List<CartonHeader> cartons) {
        Collections.sort(cartons, new Comparator<CartonHeader>() {
            @Override
            public int compare(CartonHeader o1, CartonHeader o2) {
                return o1.getCreatedAt().compareTo(o2.getCreatedAt());
            }
        });
    }

    /**
     * 按箱创建时间升序排列箱
     * @param cartons
     */
    public static void sortCartonsByIdx(List<CartonHeader> cartons) {
        if (CollectionUtils.isEmpty(cartons)) {
            return;
        }
        Collections.sort(cartons, new Comparator<CartonHeader>() {
            @Override
            public int compare(CartonHeader o1, CartonHeader o2) {
                if (o1.getCartonIndex() == null) {
                    return -1;
                } else if (o2.getCartonIndex() == null) {
                    return 1;
                }
                return o1.getCartonIndex().compareTo(o2.getCartonIndex());
            }
        });
    }
    
    /**
     * DO按分拣格升序
     * @param doHeaders
     */
    public static void sortDoHeaders(List<DeliveryOrderHeader> doHeaders) {
        Collections.sort(doHeaders, new Comparator<DeliveryOrderHeader>() {
            @Override
            public int compare(DeliveryOrderHeader o1, DeliveryOrderHeader o2) {
            	return Long.valueOf(o1.getSortGridNo()).compareTo(Long.valueOf(o2.getSortGridNo()));
            }
        });
    }
    
	public static Boolean isCOD(DeliveryOrderHeader doHeader) {
        if (StringUtil.isInArrays(doHeader.getPaymentMethodName(), codCheckFlags)) {
            return true;
        }
        return false;
	}
	
    public static String getCarrierLogoPath(WaybillType waybillType, String name) {
        return PrintHelper.getGloablePath() + "/carton/" + waybillType.name().toLowerCase() + "_" + name + ".png";
    }

    /**
     * 构造省市区地址信息
     *
     * @param doHeader
     * @return
     */
    public static String buildProvinceAndCityAndCountyAddress(DeliveryOrderHeader doHeader,String str) {
        StringBuilder address = new StringBuilder("");

        address.append(getProvinceName(doHeader)).append(str).append(getCityCname(doHeader)).append(str).append(getCountyCname(doHeader)).append(str);
        return address.toString();
    }

    private static String getProvinceName(DeliveryOrderHeader doHeader) {
        if (StringUtils.isNotBlank(doHeader.getProvinceName())) {
            return doHeader.getProvinceName();
        }
        if (null != doHeader.getProvinceInfo()) {
            return doHeader.getProvinceInfo().getProvinceCname();
        }
        return StringUtils.EMPTY;
    }

    public static String getCityCname(DeliveryOrderHeader doHeader) {
        if (StringUtils.isNotBlank(doHeader.getCityName())) {
            return doHeader.getCityName();
        }
        if (null != doHeader.getCityInfo()) {
            return doHeader.getCityInfo().getCityCname();
        }
        return StringUtils.EMPTY;
    }

    public static String getCountyCname(DeliveryOrderHeader doHeader) {
        if (StringUtils.isNotBlank(doHeader.getCountyName())) {
            return doHeader.getCountyName();
        }
        if (null != doHeader.getProvinceInfo()) {
            return doHeader.getCountyInfo().getCountyCname();
        }
        return StringUtils.EMPTY;
    }
}
