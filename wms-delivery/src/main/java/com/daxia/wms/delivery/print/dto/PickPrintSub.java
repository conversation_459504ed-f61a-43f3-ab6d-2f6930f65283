package com.daxia.wms.delivery.print.dto;

import com.daxia.wms.master.entity.Location;

import java.math.BigDecimal;

/**
 * 打印拣货明细
 * 
 */
@lombok.extern.slf4j.Slf4j
public class PickPrintSub {

    //库位编码
    private String locationCode;
    //商品编码
    private String productCode;
    //商品条码
    private String skuBarcode;
    //商品名称
    private String skuName;
    /**
     * 商品英文名
     */
    private String productEname;
    //数量
    private BigDecimal qty;
    //备注
    private String notes;

    private Long skuId;

    private Long lotId;

    private Long locId;

    private Long toLocId;

    private String LpnNo;

    private Location location;
    
    private String lotNo;
    
    private String lotatt01;
    
    private String lotatt02;

    private String sortInfo;

    private String lotatt05;//批号

    private String lotatt08;//生产厂家
    /**
     * 货品等级
     */
    private String lotatt14;

    /**
     * 箱规
     */
    private Integer caseQty;
    /**
     * 拣货顺序
     */
    private String pickSeq;

    private String specification;//规格

    //
    public void setCaseQty(Integer caseQty) {
        this.caseQty = caseQty;
    }

    public Integer getCaseQty() {
        return caseQty;
    }
    
    public String getLotNo() {
        return lotNo;
    }

    public void setLotNo(String lotNo) {
        this.lotNo = lotNo;
    }

    public String getLotatt01() {
        return lotatt01;
    }

    public void setLotatt01(String lotatt01) {
        this.lotatt01 = lotatt01;
    }

    public String getLotatt02() {
        return lotatt02;
    }

    public void setLotatt02(String lotatt02) {
        this.lotatt02 = lotatt02;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getSkuBarcode() {
        return skuBarcode;
    }

    public void setSkuBarcode(String skuBarcode) {
        this.skuBarcode = skuBarcode;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Long getLotId() {
        return lotId;
    }

    public void setLotId(Long lotId) {
        this.lotId = lotId;
    }

    public Long getLocId() {
        return locId;
    }

    public void setLocId(Long locId) {
        this.locId = locId;
    }

    public String getLpnNo() {
        return LpnNo;
    }

    public void setLpnNo(String lpnNo) {
        LpnNo = lpnNo;
    }

    public void setToLocId(Long toLocId) {
        this.toLocId = toLocId;
    }

    public Long getToLocId() {
        return toLocId;
    }

    public void setLocation(Location location) {
        this.location = location;
    }

    public Location getLocation() {
        return location;
    }

    public String getSortInfo() {
        return sortInfo;
    }

    public void setSortInfo(String sortInfo) {
        this.sortInfo = sortInfo;
    }

    public String getPickSeq() {
        return pickSeq;
    }

    public void setPickSeq(String pickSeq) {
        this.pickSeq = pickSeq;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getLotatt05() {
        return lotatt05;
    }

    public void setLotatt05(String lotatt05) {
        this.lotatt05 = lotatt05;
    }

    public String getLotatt08() {
        return lotatt08;
    }

    public void setLotatt08(String lotatt08) {
        this.lotatt08 = lotatt08;
    }

    public String getProductEname() {
        return productEname;
    }

    public void setProductEname(String productEname) {
        this.productEname = productEname;
    }

    public String getLotatt14() {
        return lotatt14;
    }

    public void setLotatt14(String lotatt14) {
        this.lotatt14 = lotatt14;
    }
}
