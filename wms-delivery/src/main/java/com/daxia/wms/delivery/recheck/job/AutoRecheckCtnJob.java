package com.daxia.wms.delivery.recheck.job;

import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.recheck.service.ReCheckService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.job.AbstractJob;
import com.daxia.wms.master.service.WarehouseService;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.*;
import org.jboss.seam.log.Log;

import java.util.List;
import java.util.Map;

/**
 * 按箱发货时，自动核拣
 */
@Name("autoRecheckCtnJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class AutoRecheckCtnJob extends AbstractJob {
    @In
    private WarehouseService warehouseService;
    @In
    DeliveryOrderService deliveryOrderService;
    @In
    ReCheckService reCheckService;


    @Override
    protected void doRun() throws Exception {
        List<Warehouse> whList = warehouseService.getRunningWh();
        //循环遍历每个仓库，执行自动波次
        for (Warehouse wh : whList) {
            ParamUtil.setCurrentWarehouseId(wh.getId());
            log.info("Start AutoRecheckCtnJob wave in warehouse :" + wh.getWarehouseCode());
            List<Long> doIds = deliveryOrderService.findCtnReCheckDoIds();
            for (Long doId : doIds) {
                try {
                    reCheckService.quickCtnRecheck(doId);
                } catch (Exception e) {
                    log.error("AutoRecheckCtnJob error！", e);
                    super.processException(e);
                    deliveryOrderService.updateFailedNumber(doId);
                }
            }
            log.info("End AutoRecheckCtnJob wave in warehouse :" + wh.getWarehouseCode());
        }
    }

}