package com.daxia.wms.delivery.container.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

/**
 *  容器管理filter
 */
@lombok.extern.slf4j.Slf4j
public class ContainerMgntFilter extends WhBaseQueryFilter {
	
	private static final long serialVersionUID = 1L;

	private String containerNo;
	
	private String docNo;
	
	private Long containerTypeId;
	
	/**
	 * 容器业务状态
	 */
	private String businessStatus;
	
	@Operation(fieldName = "containerNo", operationType = OperationType.EQUAL)
	public String getContainerNo() {
		return containerNo;
	}

	public void setContainerNo(String containerNo) {
		this.containerNo = containerNo;
	}
	@Operation(fieldName = "docNo", operationType = OperationType.EQUAL)
	public String getDocNo() {
		return docNo;
	}

	public void setDocNo(String docNo) {
		this.docNo = docNo;
	}

	@Operation(fieldName = "containerType.id", operationType = OperationType.EQUAL)
	public Long getContainerTypeId() {
		return containerTypeId;
	}

	public void setContainerTypeId(Long containerTypeId) {
		this.containerTypeId = containerTypeId;
	}

	@Operation(fieldName = "businessStatus", operationType = OperationType.EQUAL)
	public String getBusinessStatus() {
		return businessStatus;
	}

	public void setBusinessStatus(String businessStatus) {
		this.businessStatus = businessStatus;
	}
}
