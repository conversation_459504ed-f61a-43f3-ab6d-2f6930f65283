package com.daxia.wms.delivery.store.service;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.store.dto.StoreInTaskDTO;
import com.daxia.wms.delivery.store.dto.StoreOutTaskDTO;
import com.daxia.wms.master.dto.MergeBoardDTO;
import com.daxia.wms.master.entity.Container;
import com.daxia.wms.master.entity.MergeLoc;
import com.daxia.wms.master.filter.MergeBoardFilter;

import java.util.List;

public interface GoodsStoreService {
    List<StoreInTaskDTO> findStoreInTask(String boxNo);

    Boolean flushMergeLoc(MergeLoc mergeLoc);

    List<StoreOutTaskDTO> findStoreOutTask(String mergePartition);

    DataPage<MergeBoardDTO> queryBoard(MergeBoardFilter mergeBoardFilter, int startIndex, int pageSize);

    void flushContainer4PickLack(String pktNo, String pktType, Long pktId);
}
