package com.daxia.wms.delivery.recheck.dto;

@lombok.extern.slf4j.Slf4j
public class TempCartonExceptionDTO {

	private Long doId;

	private String doNo;

	private String status;

	private String waveNo;

	private String carrierName;

	private String errorMsg;

	public TempCartonExceptionDTO(Long doId, String doNo, String status, String waveNo, String carrierName, String errorMsg) {
		this.doId = doId;
		this.doNo = doNo;
		this.status = status;
		this.waveNo = waveNo;
		this.carrierName = carrierName;
		this.errorMsg = errorMsg;
	}

	public TempCartonExceptionDTO() {
	}

	public Long getDoId() {
		return doId;
	}

	public void setDoId(Long doId) {
		this.doId = doId;
	}

	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}

	public String getWaveNo() {
		return waveNo;
	}

	public void setWaveNo(String waveNo) {
		this.waveNo = waveNo;
	}

	public String getCarrierName() {
		return carrierName;
	}

	public void setCarrierName(String carrierName) {
		this.carrierName = carrierName;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
}
