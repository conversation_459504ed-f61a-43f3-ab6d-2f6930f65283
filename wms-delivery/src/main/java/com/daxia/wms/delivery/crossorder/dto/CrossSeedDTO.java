package com.daxia.wms.delivery.crossorder.dto;

import com.daxia.framework.common.service.Dictionary;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

@lombok.extern.slf4j.Slf4j
public class CrossSeedDTO {


    // 主键
    private Long id;

    /** 分播单号 */
    private String seedNo;

    /** 初始化00，分配中30， 预分配40，进行中50，已完成65 */
    /** 异常大类 */
    private String status;

    private String statusName;

    /** ASN单号 */
    private String asnNo;

    /** 发货单的数量 */
    private Long doNum;

    /** 分播明细所有数量之和 */
    private BigDecimal units;

    private BigDecimal actualUnits;

    /** 打印标记：0否 1是 */
    private Integer isPrinted;

    private String isPrintedOr;

    /** 分播开始时间 */
    private Date seedFromTime;

    /** 分播完成时间 */
    private Date seedToTime;

    private String createdBy;
    private Date createdAt;
    private String updatedBy;
    private Date updatedAt;

    private String doNo;

    private String containerNo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSeedNo() {
        return seedNo;
    }

    public void setSeedNo(String seedNo) {
        this.seedNo = seedNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getAsnNo() {
        return asnNo;
    }

    public void setAsnNo(String asnNo) {
        this.asnNo = asnNo;
    }

    public Long getDoNum() {
        return doNum;
    }

    public void setDoNum(Long doNum) {
        this.doNum = doNum;
    }

    public BigDecimal getUnits() {
        return units;
    }

    public void setUnits(BigDecimal units) {
        this.units = units;
    }

    public BigDecimal getActualUnits() {
        return actualUnits;
    }

    public void setActualUnits(BigDecimal actualUnits) {
        this.actualUnits = actualUnits;
    }

    public Integer getIsPrinted() {
        return isPrinted;
    }

    public void setIsPrinted(Integer isPrinted) {
        this.isPrinted = isPrinted;
    }

    public String getIsPrintedOr() {
        return isPrintedOr;
    }

    public void setIsPrintedOr(String isPrintedOr) {
        this.isPrintedOr = isPrintedOr;
    }

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    public Date getSeedFromTime() {
        return seedFromTime;
    }

    public void setSeedFromTime(Date seedFromTime) {
        this.seedFromTime = seedFromTime;
    }

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    public Date getSeedToTime() {
        return seedToTime;
    }

    public void setSeedToTime(Date seedToTime) {
        this.seedToTime = seedToTime;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }

    public CrossSeedDTO(Long id, String seedNo, String status, String asnNo, Long doNum, BigDecimal units, BigDecimal actualUnits, Integer isPrinted, Date seedFromTime, Date seedToTime,
                        String createdBy, Date createdAt, String updatedBy, Date updatedAt) {
        this.id = id;
        this.seedNo = seedNo;
        this.status = status;
        this.asnNo = asnNo;
        this.doNum = doNum;
        this.units = units;
        this.actualUnits = actualUnits;
        this.isPrinted = isPrinted;
        this.seedFromTime = seedFromTime;
        this.seedToTime = seedToTime;
        this.createdBy = createdBy;
        this.createdAt = createdAt;
        this.updatedBy = updatedBy;
        this.updatedAt = updatedAt;

        this.statusName = Dictionary.getDictionaryValue("cross_seed_status", this.getStatus());
        this.isPrintedOr = Dictionary.getDictionaryValue("yes_no", this.getIsPrinted().toString());
    }

    public CrossSeedDTO(Long id, String seedNo, String status, String asnNo, Long doNum, BigDecimal units, BigDecimal actualUnits, Integer isPrinted, Date seedFromTime, Date seedToTime,
                        String createdBy, Date createdAt, String updatedBy, Date updatedAt,String doNo,String containerNo) {
        this.id = id;
        this.seedNo = seedNo;
        this.status = status;
        this.asnNo = asnNo;
        this.doNum = doNum;
        this.units = units;
        this.actualUnits = actualUnits;
        this.isPrinted = isPrinted;
        this.seedFromTime = seedFromTime;
        this.seedToTime = seedToTime;
        this.createdBy = createdBy;
        this.createdAt = createdAt;
        this.updatedBy = updatedBy;
        this.updatedAt = updatedAt;
        this.doNo = doNo;
        this.containerNo = containerNo;

        this.statusName = Dictionary.getDictionaryValue("cross_seed_status", this.getStatus());
        this.isPrintedOr = Dictionary.getDictionaryValue("yes_no", this.getIsPrinted().toString());
    }

}