package com.daxia.wms.delivery.deliveryorder.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;

/**
 * 缺货单头
 */
@Entity
@Table(name = "do_lack_header")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update do_lack_header set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class DoLackHeader extends WhBaseEntity {
    private static final long serialVersionUID = 8217288302827687819L;
    private Long id;
    private Long doHeaderId;
    private String lackPartitions;
    private String createStatus;
    private Integer isDeleted;
    
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)  
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    @Column(name = "DO_HEADER_ID")
    public Long getDoHeaderId() {
        return doHeaderId;
    }
    
    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }
    
    @Column(name = "LACK_PARTITIONS")
    public String getLackPartitions() {
        return lackPartitions;
    }
    
    public void setLackPartitions(String lackPartitions) {
        this.lackPartitions = lackPartitions;
    }
    
    @Column(name = "CREATE_STATUS")
    public String getCreateStatus() {
        return createStatus;
    }

    
    public void setCreateStatus(String createStatus) {
        this.createStatus = createStatus;
    }

    @Column(name = "IS_DELETED")
    public Integer getIsDeleted() {
        return isDeleted;
    }
    
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}
