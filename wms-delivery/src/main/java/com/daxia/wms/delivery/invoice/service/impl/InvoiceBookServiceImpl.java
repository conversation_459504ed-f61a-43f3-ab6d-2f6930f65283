package com.daxia.wms.delivery.invoice.service.impl;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.invoice.dao.InvoiceBookDAO;
import com.daxia.wms.delivery.invoice.entity.InvoiceBook;
import com.daxia.wms.delivery.invoice.filter.InvoiceBookFilter;
import com.daxia.wms.delivery.invoice.service.InvoiceBookService;

/**
 * 发票薄service实现
 */
@Name("com.daxia.wms.delivery.invoiceBookService")
@lombok.extern.slf4j.Slf4j
public class InvoiceBookServiceImpl implements InvoiceBookService {
	@In
	private InvoiceBookDAO  invoiceBookDAO;
    
    /**
     * 根据发票簿filter查询发票信息并分页
     */
	@Override
	public DataPage<InvoiceBook> findInvoiceBookByFilter(
			InvoiceBookFilter invoiceBookFilter, int startIndex, int pageSize) {
		return invoiceBookDAO.queryInvoiceBookByFilter(invoiceBookFilter, startIndex, pageSize);
	}

    /**
     * 根据Id查询发票簿
     */
	@Override
	public InvoiceBook getInvoiceBookById(Long invoiceBookId) {
		return invoiceBookDAO.get(invoiceBookId);
	}

    /**
     * 更新发票簿
     */
	@Override
	@Transactional
	public void updateInvoiceBook(InvoiceBook invoiceBook) {
		invoiceBookDAO.update(invoiceBook);
		
	}

	@Override
	@Transactional
	public void save(InvoiceBook invoiceBook) {
		invoiceBookDAO.save(invoiceBook);
		
	}

}
