package com.daxia.wms.delivery.backup;

import org.hibernate.Query;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.framework.HibernateEntityController;

import java.util.List;

@Name("backupTableDAO")
@AutoCreate
@lombok.extern.slf4j.Slf4j
public class BackupTableDAO extends HibernateEntityController {

    protected List<CfgBackupTable> findAllBackupTables() {
        String hql = "from CfgBackupTable order by group, ordinal";
        Query query = this.createQuery(hql);
        return query.list();
    }

    protected int batchInsert(String tableName) {
        batchDelete(tableName + "_his");
        String sql = "insert into " + tableName + "_his select * from " + tableName + " t where exists ( select 1 from temp_backup tb where tb.id = t.id ) ";
        Query query = this.createSQLQuery(sql);
        return query.executeUpdate();
    }


    protected int batchDelete(String tableName) {
        String sql = "delete t from " + tableName + " t where exists ( select 1 from temp_backup tb where tb.id = t.id ) ";
        Query query = this.createSQLQuery(sql);
        return query.executeUpdate();
    }

    public int batchInsertTemp(String tableName, String where) {
        String sql = "insert into temp_backup select id from " + tableName + " where " + where;
        Query query = this.createSQLQuery(sql);
        return query.executeUpdate();
    }

    public void batchDeleteTemp() {
        String sql = "delete from temp_backup ";
        Query query = this.createSQLQuery(sql);
        query.executeUpdate();
    }
}
