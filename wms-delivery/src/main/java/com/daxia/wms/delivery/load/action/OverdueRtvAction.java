package com.daxia.wms.delivery.load.action;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import jodd.util.StringUtil;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.DownloadUtil;
import com.daxia.wms.delivery.load.entity.OverdueRtv;
import com.daxia.wms.delivery.load.filter.OverdueRtvFilter;
import com.daxia.wms.delivery.load.service.OverdueRtvService;
import com.daxia.wms.Constants.OverdueRtvStatus;

/**
 * 功能说明：逾期退单管理
 */
@SuppressWarnings("serial")
@Name("com.daxia.wms.delivery.overdueRtvAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class OverdueRtvAction extends PagedListBean<OverdueRtv> {

	@In
	private OverdueRtvService overdueRtvService;

	private OverdueRtvFilter overdueRtvFilter;

	private OverdueRtv overdueRtv;

	private String notes;

	private Long overdueRtvId;
	
	private List<Long> selectIds;

	/**
	 * 查询
	 */
	@Override
	public void query() {
		overdueRtvFilter.getOrderByMap().put("updatedAt", "desc");
		this.buildOrderFilterMap(overdueRtvFilter);
		DataPage<OverdueRtv> dataPage = overdueRtvService
			.findOverdueRtvs(overdueRtvFilter, getStartIndex(), getPageSize());
		this.populateValues(dataPage);
	}

	/**
	 * 初始化页面
	 */
	public void initPage() {
		overdueRtvFilter = new OverdueRtvFilter();
	}

	/**
	 * 批量审核确认
	 */
	public void audit() {
		overdueRtvService.batchAuditOverdueRtvs(selectIds, notes);
		this.sayMessage(MESSAGE_SUCCESS);
	}
	
	/**
	 * 批量审核前
	 */
	public void preAudit(){
		selectIds = new ArrayList<Long>();
		for (Object id : getSelectedRowList()) {
			selectIds.add((Long) id);
		}
		notes = "";
	}
	
	public void export() throws IOException{
		List<Long> ids = new ArrayList<Long>();
		for (Object id : getSelectedRowList()) {
			ids.add((Long) id);
		}
		byte[] exportData = overdueRtvService.exportExcel(ids);
		DownloadUtil.writeToResponse(exportData, DownloadUtil.OTHER,
			"逾期退单管理"+DateUtil.dateToString(new Date(), "yyMMddHHmmss") +".xls");
	}

	/**
	 * 修改备注
	 */
	public void changeOverdueRtv() {
		for (OverdueRtv overdueRtv : this.dataPage.getDataList()) {
			if(overdueRtv.getId().equals(overdueRtvId)){
				if(StringUtil.isNotEmpty(overdueRtv.getDealWay())){
					overdueRtv.setStatus(OverdueRtvStatus.HANDLING.getValue());
				}
				overdueRtvService.saveOverdueRtv(overdueRtv);
				break;
			}
		}
	}

	public OverdueRtvFilter getOverdueRtvFilter() {
		return overdueRtvFilter;
	}

	public void setOverdueRtvFilter(OverdueRtvFilter overdueRtvFilter) {
		this.overdueRtvFilter = overdueRtvFilter;
	}

	public OverdueRtv getOverdueRtv() {
		return overdueRtv;
	}

	public void setOverdueRtv(OverdueRtv overdueRtv) {
		this.overdueRtv = overdueRtv;
	}

	public String getNotes() {
		return notes;
	}

	public void setNotes(String notes) {
		this.notes = notes;
	}
	
	public Long getOverdueRtvId() {
		return overdueRtvId;
	}

	public void setOverdueRtvId(Long overdueRtvId) {
		this.overdueRtvId = overdueRtvId;
	}
}