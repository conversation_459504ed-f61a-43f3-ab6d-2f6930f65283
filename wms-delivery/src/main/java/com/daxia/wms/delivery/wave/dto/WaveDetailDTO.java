package com.daxia.wms.delivery.wave.dto;

import java.util.Date;

@lombok.extern.slf4j.Slf4j
public class WaveDetailDTO {
    String sortGridNo;
    String doNo;
    Long doId;
    String doType;
    String doStatus;
    Date doCreateTime;
    String notes;
    String releaseStatus;
    String channelCode;  // 渠道编码
    String channelName;  // 渠道名称

    public WaveDetailDTO(Long doId, String doNo, String sortGridNo, String doType, String doStatus, Date doCreateTime, String notes, String releaseStatus, String channelCode) {
        this.sortGridNo = sortGridNo;
        this.doNo = doNo;
        this.doId = doId;
        this.doType = doType;
        this.doStatus = doStatus;
        this.doCreateTime = doCreateTime;
        this.notes = notes;
        this.releaseStatus = releaseStatus;
        this.channelCode = channelCode;
    }

    // 保留原有构造函数以兼容现有代码
    public WaveDetailDTO(Long doId, String doNo, String sortGridNo, String doType, String doStatus, Date doCreateTime, String notes, String releaseStatus) {
        this(doId, doNo, sortGridNo, doType, doStatus, doCreateTime, notes, releaseStatus, null);
    }
    
    public String getSortGridNo() {
        return sortGridNo;
    }
    
    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }
    
    public String getDoNo() {
        return doNo;
    }
    
    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }
    
    public Long getDoId() {
        return doId;
    }
    
    public void setDoId(Long doId) {
        this.doId = doId;
    }
    
    public String getDoType() {
        return doType;
    }
    
    public void setDoType(String doType) {
        this.doType = doType;
    }
    
    public String getDoStatus() {
        return doStatus;
    }
    
    public void setDoStatus(String doStatus) {
        this.doStatus = doStatus;
    }
    
    public Date getDoCreateTime() {
        return doCreateTime;
    }
    
    public void setDoCreateTime(Date doCreateTime) {
        this.doCreateTime = doCreateTime;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public String getReleaseStatus() {
        return releaseStatus;
    }
    
    public void setReleaseStatus(String releaseStatus) {
        this.releaseStatus = releaseStatus;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }
}
