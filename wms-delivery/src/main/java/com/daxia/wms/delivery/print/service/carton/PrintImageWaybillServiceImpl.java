package com.daxia.wms.delivery.print.service.carton;

import com.daxia.dubhe.api.wms.dto.LogisticsDTO;
import com.daxia.framework.common.util.AppConfig;
import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.WaybillType;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.dao.DoPrintDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoPrint;
import com.daxia.wms.delivery.print.dto.BaseCartonPrintDTO;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.service.PrintWaybillService;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.print.dto.PrintCfg;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import lombok.SneakyThrows;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.daxia.wms.delivery.print.service.carton.PrintPDFWaybillServiceImpl.sOkHttpClient;

@Name("printImageWaybillService")
@lombok.extern.slf4j.Slf4j
public class PrintImageWaybillServiceImpl extends AbstractPrintWaybillService implements PrintWaybillService {
    @In
    private DoPrintDAO doPrintDAO;
    @In
    WarehouseCarrierService warehouseCarrierService;
    @In
    private DoHeaderDAO doHeaderDAO;
    // 取图片后缀
    private final static Pattern PATTERN = Pattern.compile("\\..{3,5}\\?");

    @Create
    public void init () {
        this.setWaybillType(WaybillType.IMAGE);
    }

    @Override
    protected void buildDTO(List<PrintReportDto> printReportDtos, DeliveryOrderHeader doHeader, BaseCartonPrintDTO carton, int index, int count) {
        CartonPrintDTO cartonPrintDTO = new CartonPrintDTO();
        cartonPrintDTO.setCartonId(carton.getId());
        cartonPrintDTO.setCartonNo(carton.getCartonNo());
        cartonPrintDTO.setDoNo(doHeader.getDoNo());
        cartonPrintDTO.setCarrierId(doHeader.getCarrierId());
        cartonPrintDTO.setOutRefNo(doHeader.getRefNo1());

        processWayBillInfo(doHeader.getId(), cartonPrintDTO);

        printReportDtos.add(cartonPrintDTO);
    }

    @SneakyThrows
    private void processWayBillInfo(Long doId, CartonPrintDTO cartonPrintDTO) {
        DoPrint doPrint = doPrintDAO.findByDoHeaderId(doId, Constants.DoPrintInfoType.WAYBILL_JSON.getValue());

        // 优先取json信息
        if(Objects.nonNull(doPrint)){
            LogisticsDTO logisticsDTO = objectMapper.readValue(doPrint.getContent(), LogisticsDTO.class);
            String surfaceUrl=logisticsDTO.getSurfaceUrl();
            cartonPrintDTO.setImageUrl(dealSurface( surfaceUrl));
            return;
        }
        // 当json不存在 取img
        doPrint = doPrintDAO.findByDoHeaderId(doId, Constants.DoPrintInfoType.WAYBILL_IMAGE_URL.getValue());
        if (null == doPrint) {
            throw new DeliveryException(DeliveryException.WAYBILL_IMAGE_NOT_EXIST);
        }
        cartonPrintDTO.setImageUrl(dealSurface(doPrint.getContent()));

    }

    /**
     * 处理打印超时问题
     * @param surfaceUrl
     */
    private String dealSurface(String surfaceUrl) {
        if(Objects.isNull(surfaceUrl)) {
            return null;
        }
        if(Arrays.stream(AppConfig.getProperty("print.use.base64.warehouseIds").split(","))
                .anyMatch(x->x.equals(ParamUtil.getCurrentWarehouseId().toString()))){
            try {
                Request request = new Request.Builder()
                        .url(surfaceUrl).get().build();
                Response response = sOkHttpClient.newCall(request).execute();
                if(response.isSuccessful() ) {
                    Matcher matcher = PATTERN.matcher(surfaceUrl);
                    while (matcher.find()) {
                        String matchPart = matcher.group();
                        surfaceUrl = "data:image/"+matchPart.substring(1,matchPart.length()-1)+";base64,";
                    }
                    surfaceUrl += Base64.getEncoder().encodeToString(response.body().bytes());
                }
            } catch (Exception e) {
                log.error("获取图片资源失败,使用原始url", e);
            }
        }else{
            //处理obs https 地址无法打印问题
            String obsBefore = Config.get(Keys.Delivery.obs_before_url, Config.ConfigLevel.WAREHOUSE);
            String obsAfter = Config.get(Keys.Delivery.obs_after_url, Config.ConfigLevel.WAREHOUSE);
            if (StringUtils.isNotBlank(obsBefore) && StringUtils.isNotBlank(obsAfter)) {
                surfaceUrl = StringUtils.replace(surfaceUrl, obsBefore, obsAfter);
            }
        }
        return surfaceUrl;
    }

    @Override
    protected PrintData genPrintData(List<PrintReportDto> dtoList, DeliveryOrderHeader doHeader) {
        PrintData printData = new PrintData();
        printData.setDtoList(dtoList);
        printData.setPrintCfg(generatePrintCfg());
        return printData;
    }

    private PrintCfg generatePrintCfg() {
        PrintCfg config = new PrintCfg("waybillimage", "100", "150");
        this.setPrintCfg(config);
        return config;
    }

}
