package com.daxia.wms.delivery.task.laborforce.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.service.PickHeaderService;
import com.daxia.wms.delivery.task.laborforce.service.LaborForceService;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishHeader;
import com.daxia.wms.delivery.task.replenish.service.ReplHeaderService;
import com.daxia.wms.delivery.util.LaborForceHelper;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.Constants.TaskObtainType;
import com.daxia.wms.Constants.TaskType;
import com.daxia.wms.master.entity.LaborHumanRegion;
import com.daxia.wms.master.entity.TempLaborTask;
import com.daxia.wms.master.service.TempLaborTaskService;

@Name("com.daxia.wms.delivery.laborForceService")
@lombok.extern.slf4j.Slf4j
public class LaborForceServiceImpl implements LaborForceService{
	@In
	PickHeaderService pickHeaderService;
	@In
	ReplHeaderService replHeaderService;
	@In
	TempLaborTaskService tempLaborTaskService;
	@In
	WaveService waveService;
	
	@Override
	@Transactional
	public Map<String, Object> bindTaskByTypeAndName(String taskType, Long userId, List<LaborHumanRegion> regionList,
													 Map<String, List<String>> typeMap,Long waveId) {
		
		List<Long> regionIdList = LaborForceHelper.getIdOfRegionList(regionList);
		boolean canCross = LaborForceHelper.enableCross(regionList);
		
		
		Map<String, Object> map = new HashMap<String, Object>();
		// 根据任务类型 获取任务
		switch (TaskType.valueOf(taskType)) {
		case ALL:
		case PK:
			PickHeader pktHeader = pickHeaderService.findPktHeaderForDemand(regionIdList, typeMap,waveId);
			if (pktHeader == null) {
				if (!canCross) {
					throw new DeliveryException(DeliveryException.NO_TASK_TOBE_DEMAND_ERROR);
				} else {
					pktHeader = pickHeaderService.findPktHeaderForDemand(null, typeMap,waveId);
					if (pktHeader == null) {
						throw new DeliveryException(DeliveryException.NO_TASK_TOBE_DEMAND_ERROR);
					}
				}
			}
			map.put("id", pktHeader.getId());
			map.put("taskNo", pktHeader.getPktNo());
			WaveHeader waveHeader = waveService.getWave(pktHeader.getWaveHeadId());
			map.put("waveId",waveHeader.getId());
			map.put("estDoFinishTime", null == waveHeader ? null : waveHeader.getEstDoFinishTime());
			pktHeader.setOperUserId(userId);
			pickHeaderService.saveOrUpdate(pktHeader);
			break;
		case RP:
			List<Long> partitionIdList = LaborForceHelper.getIdOfPartitionList(regionList);
			ReplenishHeader rpHeader = replHeaderService.demandDoc(partitionIdList, canCross);
			if (rpHeader == null) {
				throw new DeliveryException(DeliveryException.NO_TASK_TOBE_DEMAND_ERROR);
			}
			map.put("id", rpHeader.getId());
			map.put("taskNo", rpHeader.getReplNo());
			map.put("estDoFinishTime", null == rpHeader ? null : rpHeader.getEarlistPlanShipTime());
			rpHeader.setOperUserId(userId);
			replHeaderService.update(rpHeader);
			break;
		default:
			break;
		}
		// 绑定任务，插入临时表
		TempLaborTask tempTask = new TempLaborTask();
		tempTask.setTaskId(Long.valueOf(map.get("id").toString()));
		tempTask.setTaskNo(map.get("taskNo").toString());
		tempTask.setTaskType(taskType);
		tempTask.setUserAcctId(userId);
		tempTask.setObtainType(TaskObtainType.OBTAINED_ACTIVELY.getValue());
		tempLaborTaskService.saveOrUpdate(tempTask);
		map.put("id", tempTask.getId().toString());
		map.put("taskNo", tempTask.getTaskNo());
		// 构造客户端返回数据
		return map;
	}
}
