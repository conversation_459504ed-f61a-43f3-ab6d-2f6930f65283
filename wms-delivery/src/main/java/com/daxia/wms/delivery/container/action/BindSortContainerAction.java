package com.daxia.wms.delivery.container.action;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.ActionBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.delivery.util.SortContainerUtil;

/**
 * 核拣绑定分拣筐
 */
@Name("com.daxia.wms.delivery.bindSortContainerAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class BindSortContainerAction extends ActionBean {
    private static final long serialVersionUID = 5745251805440467989L;
    
    private String doNo;
    private String sortContainerNo;
    
    @In
    private ContainerMgntService containerMgntService;
    
    
    public void bind() {
    	sortContainerNo = SortContainerUtil.subStringSortContainer(sortContainerNo);
        containerMgntService.recheckBindSortContainer(doNo, sortContainerNo);
        this.sayMessage(MESSAGE_SUCCESS);
    }
    
    public String getDoNo() {
        return doNo;
    }
    
    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }
    
    public String getSortContainerNo() {
        return sortContainerNo;
    }
    
    public void setSortContainerNo(String sortContainerNo) {
        this.sortContainerNo = sortContainerNo;
    }
}
