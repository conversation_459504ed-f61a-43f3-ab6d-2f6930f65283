package com.daxia.wms.delivery.invoice.service.impl;

import java.math.BigDecimal;
import java.util.*;

import com.daxia.framework.common.util.*;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.invoice.dto.*;
import com.daxia.wms.delivery.util.XmlUtil;
import com.daxia.wms.invoice.util.LBase64;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.jboss.seam.log.Log;
import org.jboss.seam.security.Identity;

import com.daxia.framework.common.service.Dictionary;
import com.daxia.framework.common.service.ReportGenerator;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.InvoiceNoStatus;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.invoice.dao.InvoiceBookDAO;
import com.daxia.wms.delivery.invoice.dao.InvoiceDao;
import com.daxia.wms.delivery.invoice.dao.InvoiceDetailDao;
import com.daxia.wms.delivery.invoice.dao.InvoiceNoDAO;
import com.daxia.wms.delivery.invoice.entity.InvoiceDetail;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;
import com.daxia.wms.delivery.invoice.filter.InvoiceFilter;
import com.daxia.wms.delivery.invoice.filter.InvoiceNoFilter;
import com.daxia.wms.delivery.invoice.service.InvoiceNoService;
import com.daxia.wms.delivery.invoice.service.InvoiceService;
import com.daxia.wms.delivery.print.service.PrintInvoiceNewService;
import com.daxia.wms.delivery.print.service.PrintInvoiceService;
import com.daxia.wms.print.PrintConstants.PrintInvoicePos;
import org.springframework.util.CollectionUtils;

/**
 * 发票信息service实现类
 */
@Name("com.daxia.wms.delivery.invoiceService")
@lombok.extern.slf4j.Slf4j
public class InvoiceServiceImpl implements InvoiceService {
    @In
    private InvoiceDao invoiceDao;

    @In
    private InvoiceDetailDao invoiceDetailDao;
    @In
    private Identity identity;
    @In
    private ReportGenerator reportGenerator;
    @In
    private InvoiceNoDAO invoiceNoDAO;
    @In
    private InvoiceBookDAO invoiceBookDAO;
    @In
    private InvoiceNoService invoiceNoService;

    @In(create = true)
    private PrintInvoiceNewService printInvoiceNewService;

    @In
    private PrintInvoiceService printInvoiceService;

    @In
    private DeliveryOrderService deliveryOrderService;

    @Logger
    protected Log log;

    /**
     * 查询发票头分页信息
     */
    @Override
    public DataPage<InvoiceHeader> findInvoiceByFilter(InvoiceFilter invoiceFilter, int startIndex, int pageSize) {
        return invoiceDao.findRangeByFilter(invoiceFilter, startIndex, pageSize);
    }

    /**
     * 获取指定Id的发票头对象
     */
    @Override
    public InvoiceHeader getInvoiceById(Long id) {
        return invoiceDao.get(id);
    }

    /**
     * 根据ids获取发票头list
     */
    @Override
    public List<InvoiceHeader> getInvoiceList(List<Long> ids) {
        return invoiceDao.getByKeys(ids);
    }

    /**
     * 是否所有发票都已绑定
     */
    @Override
    public boolean isALLBindedWithPrintedInv(String doNo) {

        InvoiceFilter invoiceFilter = new InvoiceFilter();
        // 根据开票号去 发票号码表 查询对应的发票号码信息
        invoiceFilter.setDoNo(doNo);
        List<InvoiceHeader> invoiceHeaders = invoiceDao
                .findByFilter(invoiceFilter);
        if (ListUtil.isNullOrEmpty(invoiceHeaders)) {
            return false;
        }
        for (InvoiceHeader invoiceHeader : invoiceHeaders) {
            if (!isBindedWithPrintedInv(invoiceHeader)) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param invoiceHeader
     * @return
     */
    private boolean isBindedWithPrintedInv(InvoiceHeader invoiceHeader) {
        if (StringUtil.isEmpty(invoiceHeader.getInvoiceNumber())) {
            return false;
        }
        // 根据开票号去 发票号码表 查询对应的发票号码信息
        InvoiceNoFilter invNoFilter = new InvoiceNoFilter();
        invNoFilter.setInvoiceHeaderId(invoiceHeader.getId());
        List<InvoiceNo> invoiceNoList = invoiceNoDAO.findByFilter(invNoFilter);
        if (invoiceNoList == null || invoiceNoList.isEmpty()) {// 开票信息都没有关联发票号
            return false;
        }
        for (InvoiceNo invoiceNo : invoiceNoList) {
            if (InvoiceNoStatus.PRINT.getValue().equals(invoiceNo.getStatus())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public String getBindedPrintedInvNo(InvoiceHeader invoiceHeader) {
        if (StringUtil.isEmpty(invoiceHeader.getInvoiceNumber())) {
            return null;
        }
        // 根据开票号去 发票号码表 查询对应的发票号码信息
        InvoiceNoFilter invNoFilter = new InvoiceNoFilter();
        invNoFilter.setInvoiceHeaderId(invoiceHeader.getId());
        //InvoiceNo invoiceNo = invoiceNoDAO.findByFilter(invNoFilter).get(0);
        List<InvoiceNo> invoiceNos = invoiceNoDAO.findByFilter(invNoFilter);
        if (invoiceNos == null) {// 开票信息都没有关联发票号
            return null;
        }
        for (InvoiceNo invoiceNo : invoiceNos) {
            if (InvoiceNoStatus.PRINT.getValue().equals(invoiceNo.getStatus())) {
                return invoiceNo.getInvoiceNo();
            }
        }
        return null;
    }

    @Override
    public InvoiceHeader getBill(String doNo, String billNo) {

        InvoiceFilter invoiceFilter = new InvoiceFilter();
        invoiceFilter.setDoNo(doNo);
        if (billNo != null) {
            invoiceFilter.setSortBy(Integer.valueOf(billNo));
        }
        List<InvoiceHeader> invoiceHeaders = invoiceDao
                .findByFilter(invoiceFilter);
        if (invoiceHeaders == null) {
            return null;
        }
        if (invoiceHeaders.isEmpty()) {
            throw new DeliveryException(
                    DeliveryException.ERROR_INVOICE_BIND_BILLNO_NOEXISTS,
                    billNo);
        }
        if (invoiceHeaders.size() > 1) {
            if (billNo != null) {
                throw new DeliveryException(DeliveryException.ERROR_INVOICE_BIND_BILLNO_DUP);
            } else {
                throw new DeliveryException(DeliveryException.ERROR_INVOICE_BIND_BILLNO_REQUIRED);
            }
        }
        if (invoiceHeaders.get(0).getSortBy() == null
                || StringUtil.isEmpty(String.valueOf(invoiceHeaders.get(0)
                .getSortBy()))) {
            //只输入doNo，不输入billNo（开票号），支持这种输入。
            //但仅适用于场景：该doNo只有一条开票信息，且其开票号不会为空。
            //这里根据doNo=xxxx查出唯一1条记录但开票号为空，视为数据异常。
            throw new DeliveryException(DeliveryException.ERROR_INVOICE_BIND_BILLNO_NULL_IN_DB);
        }
        return invoiceHeaders.get(0);
    }

    /**
     * 检查输入的发票信息是否正确
     */
    @Override
    public boolean checkInvoiceInfo(String invoiceNoFrom, String invoiceNoTo, String invoiceCode) {
        //发票起止号码或发票代码不能为空
        if (StringUtil.isEmpty(invoiceNoFrom) || StringUtil.isEmpty(invoiceNoTo)) {
            throw new DeliveryException(DeliveryException.ERROR_INVOICENO_NULL);
        }
        //发票代码不能为空
        if (StringUtil.isEmpty(invoiceCode)) {
            throw new DeliveryException(DeliveryException.ERROR_INVOICECODE_NULL);
        }
        //起始发票号码或发票代码输入错误
        if (invoiceNoDAO.findInvoiceNoByNo(invoiceNoFrom, invoiceCode) == null) {
            throw new DeliveryException(DeliveryException.ERROR_INVOICE_NOFM_NOTEXSIT);
        }
        //截止发票号码或发票代码输入错误
        if (invoiceNoDAO.findInvoiceNoByNo(invoiceNoTo, invoiceCode) == null) {
            throw new DeliveryException(DeliveryException.ERROR_INVOICE_NOTO_NOTEXSIT);
        }
        //截止发票号码不能小于起始发票号码
        if (invoiceNoFrom.compareTo(invoiceNoTo) > 0) {
            throw new DeliveryException(DeliveryException.ERROR_INVOICE_NOFM_BIG_NOTO);
        }
        long invoiceBookId = invoiceBookDAO.queryInvoiceBookByNo(invoiceNoFrom, invoiceNoTo, invoiceCode);
        if (-1 == invoiceBookId) {
            //发票号码段不在同一发票薄内
            throw new DeliveryException(DeliveryException.ERROR_INVOICE_NO_NOT_SAMEBOOK);
        }
        //发票段存在不是未打印的发票号码则报错
        List<InvoiceNo> notNoPrintInvoiceNos = invoiceNoDAO.findInvoiceBooksInvoiceNoNotPrint(invoiceBookId, invoiceNoFrom,
                invoiceNoTo, Constants.InvoiceNoStatus.INITIAL.getValue());
        if (!ListUtil.isNullOrEmpty(notNoPrintInvoiceNos)) {
            List<String> list = new ArrayList<String>();
            list.add(notNoPrintInvoiceNos.get(0).getInvoiceNo());
            list.add(Dictionary.getDictionary("INVOICE_STATUS").get(notNoPrintInvoiceNos.get(0).getStatus()));
            throw new DeliveryException(DeliveryException.ERROR_INVOICE_BOOK_PRINTED, list.get(0).toString(),
                    Dictionary.getDictionary("INVOICE_STATUS").get(notNoPrintInvoiceNos.get(0).getStatus()).toString());
        } else {
            return true;
        }
    }

    /**
     * 获取发票模式1参数
     */
    @Override
    public String getInvoiceLevel() {
        return SystemConfig.getConfigValue("delivery.invoice.invoiceModel", ParamUtil.getCurrentWarehouseId());
    }

    /**
     * 绑定并打印发票信息
     */
    @Override
    @Transactional
    public List<String> bindAndPrint(List<Long> ids, String invoiceCode, String invoiceNoFrom, String invoiceNoTo) {
        if (ids.isEmpty()) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }

        List<InvoiceHeader> invoiceList = getInvoiceListForPrint(ids);

        if (invoiceList.isEmpty()) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }

        //检验发票信息是否正确
        checkInvoiceInfo(invoiceNoFrom, invoiceNoTo, invoiceCode);

        InvoiceNoFilter filter = new InvoiceNoFilter();
        filter.setInvoiceCode(invoiceCode);
        filter.setInvoiceNoFrom(invoiceNoFrom);
        filter.setInvoiceNoTo(invoiceNoTo);

        Map<String, String> orderByMap = new LinkedHashMap<String, String>();
        orderByMap.put("invoiceNo", "asc");
        filter.setOrderByMap(orderByMap);

        List<InvoiceNo> invoiceNos = invoiceNoService.findByFilter(filter);

        if (invoiceNos.size() < invoiceList.size()) {
            throw new DeliveryException(DeliveryException.INVOICE_NO_IS_NOT_ENOUGH);
        }

        int i = 0;
        for (InvoiceHeader invoiceHeader : invoiceList) {
            invoiceNoService.bind(invoiceHeader, invoiceNos.get(i++));
        }

        invoiceDao.getSession().flush();
        invoiceDao.getSession().clear();

        // 判断是否是卷式发票模式
        Integer version = printInvoiceService.getPrintInvoiceVersion();
        if (version == 1) {
            return printInvoiceService.print(ids, "Printer001", null, PrintInvoicePos.INVOICE_SELECT);
        }
        return printInvoiceNewService.print(ids, invoiceCode);
    }

    /**
     * 根据ids获取发票头list
     */
    @Override
    public List<InvoiceHeader> getInvoiceListForPrint(List<Long> ids) {
        InvoiceFilter invoiceFilter = new InvoiceFilter();
        invoiceFilter.setIds(ids);

        Map<String, String> orderBy = new LinkedHashMap<String, String>();
        orderBy.put("sortBy", "asc");
        orderBy.put("deliveryOrderHeader.sortGridNo", "asc");
        orderBy.put("deliveryOrderHeader.waveId", "asc");
        invoiceFilter.setOrderByMap(orderBy);

        return invoiceDao.findByFilter(invoiceFilter);
    }


    /**
     * 根据ids获取发票头list
     */
    @Override
    public List<InvoiceHeader> getInvoiceListByDoIds(List<Long> ids) {
        if (ListUtil.isNullOrEmpty(ids)) {
            return Lists.newArrayList();
        }

        InvoiceFilter invoiceFilter = new InvoiceFilter();
        invoiceFilter.setDoIds(ids);

        return invoiceDao.findByFilter(invoiceFilter);
    }

    @Override
    public List<Long> getInvoiceHIdsByDoIds(List<Long> doIds) {
        return invoiceDao.getInvoiceHIdsByDoIds(doIds);
    }

    @Override
    /**
     * 获取波次下未打印的发票（排除已客户取消的订单的发票）
     */
    public List<InvoiceHeader> getInvoiceList4PrintByWave(String waveNo) {
        return invoiceDao.getInvoiceList4PrintByWave(waveNo);
    }

    @Override
    public List<InvoiceHeader> getInvoiceList4Print(List<Long> idList) {
        return invoiceDao.getInvoiceList4Print(idList);
    }

    @Override
    @Transactional
    public void saveOrUpdate(InvoiceHeader invoiceHeader) {
        invoiceDao.saveOrUpdate(invoiceHeader);
    }

    @Override
    @Transactional
    public void saveOrUpdate(InvoiceDetail invoiceDetail) {
        invoiceDetailDao.saveOrUpdate(invoiceDetail);
    }

    @Override
    public BigDecimal queryWaveInvoiceCount(String waveNo) {
        return invoiceDao.queryWaveInvoiceCount(waveNo);
    }

    @Override
    public List<InvoiceDetail> findInvoiceDetailsByHeaderId(Long invoiceHId) {
        return invoiceDetailDao.findByHeaderId(invoiceHId);
    }

    @Override
    public InvoiceHeader getByReqSequenceNo(String sequenceNo) {
        return invoiceDao.getByReqSequenceNo(sequenceNo);
    }

    @Override
    @Transactional
    public void issueInvoice(Long invoiceId) {
        String requestUrl = Config.get(Keys.Delivery.invoice_hangXinUrl, Config.ConfigLevel.TENANT);
        List<InvoiceHeader> headers = getInvoiceList4Print(Arrays.asList(invoiceId));
        InvoiceHeader header = headers.get(0);
        if (InvoiceHeader.InvoiceStatus.PRINT.equals(header.getInvoiceStatus())) {
            throw new BusinessException("已打印");
        }
        if (!InvoiceHeader.InvoiceStatus.INIT.equals(header.getInvoiceStatus())) {
            return;
        }
        String ip = SystemConfig.getConfigValueFmJson(ConfigKeys.HANG_XIN_INVOICE_CFG, "ip", ParamUtil.getCurrentWarehouseId());
        String port = SystemConfig.getConfigValueFmJson(ConfigKeys.HANG_XIN_INVOICE_CFG, "port", ParamUtil.getCurrentWarehouseId());
        String bmbbh = SystemConfig.getConfigValueFmJson(ConfigKeys.HANG_XIN_INVOICE_CFG, "bmbbh", ParamUtil.getCurrentWarehouseId());
        String skr = SystemConfig.getConfigValueFmJson(ConfigKeys.HANG_XIN_INVOICE_CFG, "kpr", ParamUtil.getCurrentWarehouseId());//收款人
        validatePre(header);
        DeliveryOrderHeader deliveryOrderHeader = header.getDeliveryOrderHeader();
        if (deliveryOrderHeader.getWaveId() == null) {
            throw new BusinessException("未生成波次");
        }
        InvoiceRecord record = new InvoiceRecord();
        InvoiceFp invoiceFp = new InvoiceFp();
        invoiceFp.setBZ(deliveryOrderHeader.getDoNo() + "_" + deliveryOrderHeader.getSortGridNo());
        record.setInvoiceFp(invoiceFp);
        List<InvoiceFpmx> invoiceFpmxes = new ArrayList<InvoiceFpmx>();
        record.setInvoiceFpmxes(invoiceFpmxes);
        invoiceFp.setFPZL(InvoiceFp.InvoiceType.JP.getValue());//卷票
        invoiceFp.setGFMC(StringUtil.notNullString(header.getReceiverName()));//购方名称
        invoiceFp.setGFSH("");//购方税号
        invoiceFp.setSKR(skr);//收款人
        invoiceFp.setFHR(skr);//复核人
        invoiceFp.setKPR(skr);//开票人
        invoiceFp.setQDBZ("0");//清单标志
        invoiceFp.setKPBZ("0");//开票标志
        List<InvoiceDetail> invoiceDetails = header.getInvoiceDetails();
        for (InvoiceDetail detail : invoiceDetails) {
            InvoiceFpmx fpmx = new InvoiceFpmx();
            fpmx.setSPMC(StringUtil.notNullString(detail.getSkuDescr()));
            fpmx.setHSBZ(detail.getTaxRate().compareTo(BigDecimal.ZERO) > 0 ? "1" : "0");
            fpmx.setSLV(detail.getTaxRate().divide(BigDecimal.valueOf(100)));
            fpmx.setJE(NumberUtil.bigDecimalRemoveZero(detail.getAmount()));
            fpmx.setDJ(NumberUtil.bigDecimalRemoveZero(detail.getPrice()));
            fpmx.setJLDW(StringUtil.notNullString(detail.getUomDescr()));
            fpmx.setGGXH(StringUtil.notNullString(detail.getSkuType()));
            fpmx.setSE(fpmx.getSLV().multiply(detail.getAmount()).divide(BigDecimal.ONE.add(fpmx.getSLV()), 2, BigDecimal.ROUND_HALF_UP));
            fpmx.setSL(detail.getQty());
            fpmx.setBMBBH(StringUtil.defaultIfEmpty(bmbbh, "13.0"));//编码版本号
            fpmx.setSSFLBM(StringUtil.notNullString(detail.getTaxCategoryCode()));//税收分类编码
            fpmx.setYHZC(StringUtil.isBlank(detail.getPreferentialPolicy()) ? "0" : "1");//享受优惠政策内容
            fpmx.setYHZCNR(StringUtil.notNullString(detail.getPreferentialPolicy()));
            invoiceFpmxes.add(fpmx);
        }
        InvoiceReq req = new InvoiceReq();
        req.setSid("1");
        req.setIp(ip);
        req.setPort(port);
        InvoiceData data = new InvoiceData();
        data.setRecord(record);
        req.setData(data);
        String recordXml = XMLUtil.toXML(req);
        log.info(recordXml);
        String baseRecordXml = LBase64.getBase64(recordXml);
        String issueXML = PrintTemplateUtil.process("invoiceXML", "issue.ftl", null);
        issueXML = issueXML.replace("数据内容", baseRecordXml);
        log.info(issueXML);
        String responseXml = null;
        try {
            responseXml = XmlUtil.processRequest(requestUrl, issueXML);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("请求航信开票错误");
        }
        Integer startIndex = responseXml.indexOf("<out>") + "<out>".length();
        Integer endIndex = responseXml.indexOf("</out>");
        responseXml = responseXml.substring(startIndex, endIndex).trim();
        if (StringUtil.isBlank(responseXml)) {
            throw new BusinessException("请求航信开票错误：无响应");
        }
        responseXml = responseXml.replace("#xD;", "");
        log.info(LBase64.getFromBase64(responseXml));
        InvoiceSrv srv = new InvoiceSrv();
        srv = (InvoiceSrv) XMLUtil.fromXML(LBase64.getFromBase64(responseXml), srv);
        InvoiceRefp refp = srv.getErr().getInvoiceRefp();
        if (InvoiceRefp.INVOIC_RETCODE.KPCG.getValue().equals(refp.getRETCODE())) {
            header.setInvoiceNumber(refp.getFPHM());
            header.setInvoiceCode(refp.getFPDM());
            header.setPreInvoiceNumber(refp.getSYZFPHM());
            header.setPreInvoiceCode(refp.getSYZFPDM());
            header.setInvoiceStatus(InvoiceHeader.InvoiceStatus.BILLED);
            header.setInvoiceType(invoiceFp.getFPZL());
            invoiceDao.update(header);
        } else {
            throw new BusinessException("请求航信开票错误：" + refp.getRETMSG() + ",错误码：" + refp.getRETCODE());
        }
    }

    @Override
    @Transactional
    public void printInvoice(Long invoiceId) {
        String requestUrl = Config.get(Keys.Delivery.invoice_hangXinUrl, Config.ConfigLevel.TENANT);
        List<InvoiceHeader> headers = getInvoiceList4Print(Arrays.asList(invoiceId));
        InvoiceHeader header = headers.get(0);
        if (!InvoiceHeader.InvoiceStatus.BILLED.equals(header.getInvoiceStatus())) {
            return;
        }
        validatePre(header);
        InvoiceRecord record = new InvoiceRecord();
        record.setFPZL(header.getInvoiceType());//普通发票
        record.setFPHM(header.getInvoiceNumber());
        record.setFPDM(header.getInvoiceCode());
        //record.setTCBZ("1");
        String ip = SystemConfig.getConfigValueFmJson(ConfigKeys.HANG_XIN_INVOICE_CFG, "ip", ParamUtil.getCurrentWarehouseId());
        String port = SystemConfig.getConfigValueFmJson(ConfigKeys.HANG_XIN_INVOICE_CFG, "port", ParamUtil.getCurrentWarehouseId());
        InvoiceReq req = new InvoiceReq();
        req.setSid("2");
        req.setIp(ip);
        req.setPort(port);
        InvoiceData data = new InvoiceData();
        req.setData(data);
        data.setRecord(record);
        String recordXml = XMLUtil.toXML(req);
        log.info("recordXml:" + recordXml);
        String baseRecordXml = LBase64.getBase64(recordXml);
        String issueXML = PrintTemplateUtil.process("invoiceXML", "print.ftl", null);
        issueXML = issueXML.replace("数据内容", baseRecordXml);
        log.info("issueXML:" + issueXML);
        String responseXml = null;
        try {
            responseXml = XmlUtil.processRequest(requestUrl, issueXML);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("请求航信打印发票错误");
        }
        Integer startIndex = responseXml.indexOf("<out>") + "<out>".length();
        Integer endIndex = responseXml.indexOf("</out>");
        responseXml = responseXml.substring(startIndex, endIndex).trim();
        if (StringUtil.isBlank(responseXml)) {
            throw new BusinessException("请求航信打印发票错误：无响应");
        }
        responseXml = responseXml.replace("#xD;", "");
        log.info(LBase64.getFromBase64(responseXml));
        InvoiceSrv srv = new InvoiceSrv();
        srv = (InvoiceSrv) XMLUtil.fromXML(LBase64.getFromBase64(responseXml), srv);
        InvoiceRefp refp = srv.getErr().getInvoiceRefp();
        if (InvoiceRefp.INVOIC_RETCODE.DYCG.getValue().equals(refp.getRETCODE())) {
            header.setInvoiceStatus(InvoiceHeader.InvoiceStatus.PRINT);
            invoiceDao.update(header);
        } else {
            throw new BusinessException("请求航信打印发票错误：" + refp.getRETMSG() + ",错误码：" + refp.getRETCODE());
        }
    }

    private void validatePre(InvoiceHeader header) {
        if (StringUtil.isNotBlank(header.getPreInvoiceCode()) || StringUtil.isNotBlank(header.getPreInvoiceNumber())) {
            Map<String, Object> params = Maps.newHashMap();
            params.put("invoiceCode", header.getPreInvoiceCode());
            params.put("invoiceNumber", header.getPreInvoiceNumber());
            List<InvoiceHeader> preHeaders = invoiceDao.find(params);
            if (CollectionUtils.isEmpty(preHeaders)) {
                throw new BusinessException("找不到前一个发票号码：" + StringUtil.notNullString(header.getPreInvoiceNumber())
                        + ",发票代码：" + StringUtil.notNullString(header.getPreInvoiceCode()));
            }
            if (preHeaders.get(0).getInvoiceStatus() != InvoiceHeader.InvoiceStatus.PRINT) {
                throw new BusinessException("前一个发票号码：" + StringUtil.notNullString(header.getPreInvoiceNumber())
                        + ",发票代码：" + StringUtil.notNullString(header.getPreInvoiceCode()) + "未打印完成");
            }
        }
    }

}