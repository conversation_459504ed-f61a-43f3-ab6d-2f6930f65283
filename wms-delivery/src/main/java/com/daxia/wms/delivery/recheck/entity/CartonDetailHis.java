package com.daxia.wms.delivery.recheck.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.master.entity.Sku;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;


/**
 * 装箱明细历史实体
 */
@Entity
@Table(name = "doc_carton_detail_his")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = " update doc_carton_detail_his set is_deleted = 1 where id = ?  and version = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class CartonDetailHis extends WhBaseEntity {

    private static final long serialVersionUID = -2867003761856528313L;

    private Long id;

    private CartonHeaderHis cartonHeader;

    /**
     * 已核拣装箱数
     */
    private BigDecimal packedNumber;

    private DeliveryOrderHeader doHeader;

    private Sku sku;
    
    private Long skuId;

    @Id
    @Column(name = "ID")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CARTON_HEADER_ID")
    @Where(clause = " IS_DELETED = 0 ")
    public CartonHeaderHis getCartonHeader() {
        return cartonHeader;
    }

    public void setCartonHeader(CartonHeaderHis cartonHeader) {
        this.cartonHeader = cartonHeader;
    }

    /**
     * 
     * @return 已核拣装箱数
     */
    @Column(name = "QTY_PACKED")
    public BigDecimal getPackedNumber() {
        return packedNumber;
    }

    public void setPackedNumber(BigDecimal packedNumber) {
        this.packedNumber = packedNumber;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DO_HEADER_ID")
    @Where(clause = " IS_DELETED = 0 ")
    public DeliveryOrderHeader getDoHeader() {
        return doHeader;
    }

    public void setDoHeader(DeliveryOrderHeader doHeader) {
        this.doHeader = doHeader;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SKU_ID",insertable=false,updatable=false)
    @Where(clause = " IS_DELETED = 0 ")
    public Sku getSku() {
        return sku;
    }

    public void setSku(Sku sku) {
        this.sku = sku;
    }

    @Column(name="SKU_ID")
    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }
}
