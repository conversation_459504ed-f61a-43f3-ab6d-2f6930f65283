package com.daxia.wms.delivery.recheck.dto;

import java.math.BigDecimal;
import java.util.Date;

@lombok.extern.slf4j.Slf4j
public class RecheckDetailInfoDTO {

    private String productCode;
    private String productBarCode;
    private String productName;
    private BigDecimal numberInCarton;
    private BigDecimal qty;
    private BigDecimal volume;
    private BigDecimal actualGrossWeight;
    private BigDecimal grossWeight;
    private String doNo;
    private String cartonNo;
    private String createBy;
    private Date createdAt;

    public RecheckDetailInfoDTO(String productCode, String productBarCode, String productName, BigDecimal numberInCarton,BigDecimal qty, BigDecimal volume, BigDecimal actualGrossWeight, BigDecimal grossWeight, String doNo, String cartonNo, String createBy, Date createdAt) {
        this.productCode = productCode;
        this.productBarCode = productBarCode;
        this.productName = productName;
        this.numberInCarton = numberInCarton;
        this.qty = qty;
        this.volume = volume;
        this.actualGrossWeight = actualGrossWeight;
        this.grossWeight = grossWeight;
        this.doNo = doNo;
        this.cartonNo = cartonNo;
        this.createBy = createBy;
        this.createdAt = createdAt;
    }

    public BigDecimal getActualGrossWeight() {
        return actualGrossWeight;
    }

    public void setActualGrossWeight(BigDecimal actualGrossWeight) {
        this.actualGrossWeight = actualGrossWeight;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductBarCode() {
        return productBarCode;
    }

    public void setProductBarCode(String productBarCode) {
        this.productBarCode = productBarCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getNumberInCarton() {
        return numberInCarton;
    }

    public void setNumberInCarton(BigDecimal numberInCarton) {
        this.numberInCarton = numberInCarton;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getCartonNo() {
        return cartonNo;
    }

    public void setCartonNo(String cartonNo) {
        this.cartonNo = cartonNo;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }
}
