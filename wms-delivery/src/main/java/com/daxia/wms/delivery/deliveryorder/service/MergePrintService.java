package com.daxia.wms.delivery.deliveryorder.service;

import com.alibaba.fastjson.JSONArray;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.deliveryorder.dto.MergePrintDTO;
import com.daxia.wms.delivery.deliveryorder.filter.MergePrintFilter;
import com.daxia.wms.print.dto.PrintData;

import java.util.List;

public interface MergePrintService {

    DataPage<MergePrintDTO> queryPageInfo(MergePrintFilter mergePrintFilter, int startIndex, int pageSize);

    List<PrintData> execute(MergePrintDTO mergePrintDTO);
}
