package com.daxia.wms.delivery.print.service;

import java.util.List;

import com.daxia.wms.delivery.print.dto.PrintCfg;

/**
 *  补货单打印接口
 */
public interface PrintReplService {
    
    /**
     * 导出pdf
     * @param idList
     * @return
     */
    public byte[] generatePDF(List<Long> idList);
        
    /**
     * 打印补货单（多页，每页都有单头）
     * @param idList
     * @return
     */
    public List<String> print(List<Long> idList);
    
    /**
     * 打印补货单(多页只显示一个单头)
     * @param idList
     * @return
     */
    public List<String> printReplA(List<Long> idList);

    /**
     * 设置补货单打印参数
     * @return
     */
    public PrintCfg setDoPrintCfg();

    /**
     * 判断给定补货单打印纸张大小和系统中配置的是否相等
     * @param PaperSize 给定补货单打印纸张大小
     * @return 若相等返回true，否则返回false
     */
    public boolean checkReplPaperSize(String PaperSize);;
	

}
