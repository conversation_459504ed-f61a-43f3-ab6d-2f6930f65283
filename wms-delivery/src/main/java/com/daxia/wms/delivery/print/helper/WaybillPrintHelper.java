package com.daxia.wms.delivery.print.helper;

import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/11/8.
 */
@lombok.extern.slf4j.Slf4j
public class WaybillPrintHelper {
    /**
     * 按打印模板分组
     * 按配送商ID分组
     * @param dataList
     * @return
     */
    public static List<PrintData> sortPrintDataByPrintTemplate(List<PrintData> dataList) {
        Map<Long,PrintData> resultMap = new HashMap<Long, PrintData>();
        List<PrintReportDto> dtoList;
        for (PrintData d : dataList) {
            if (resultMap.containsKey(d.getCarrierId())) {
                dtoList = resultMap.get(d.getCarrierId()).getDtoList();
                dtoList.addAll(d.getDtoList());
                resultMap.get(d.getCarrierId()).setDtoList(dtoList);
            } else {
                resultMap.put(d.getCarrierId(),d);
            }
        }
        return new ArrayList<PrintData>(resultMap.values());
    }

    public static String getPrintJs(List<PrintData> dataList) {
        Map<String, String> templateMap = new HashMap<String, String>();
        String templateJs = "";
        for (PrintData printData : dataList) {
            if (!templateMap.containsKey(printData.getPrintCfg().getLodopTemplate())) {
                templateJs = templateJs + printData.getTemplateJs();
                templateMap.put(printData.getPrintCfg().getLodopTemplate(), templateJs + printData.getTemplateJs());
            }
        }
        return templateJs;
    }
}
