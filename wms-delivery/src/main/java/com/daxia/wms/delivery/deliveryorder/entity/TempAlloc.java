package com.daxia.wms.delivery.deliveryorder.entity;

import com.daxia.framework.common.entity.BaseEntity;
import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.master.entity.*;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.CascadeType;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "temp_alloc")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class TempAlloc extends BaseEntity {

    private Long id;

    @Id
    @Column(name = "do_id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

}