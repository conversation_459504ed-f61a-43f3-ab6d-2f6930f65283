package com.daxia.wms.delivery.deliveryorder.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@lombok.extern.slf4j.Slf4j
public class DoDetailDto implements Serializable {

    private static final long serialVersionUID = 8316182654323875023L;

    private Long id;
    /**
     * DO单号
     */
    private String doNo;
    /**
     * 发货单状态
     */
    private String status;
    /**
     * 冻结状态 HOLD：冻结 RELEASE：释放
     */
    private String releaseStatus;
    /**
     * 订货数量 EXPECTED_QTY_EACH
     */
    private BigDecimal expectedQty;
    /**
     * 发货数量
     */
    private BigDecimal shipQty;
    /**
     * DO创建时间
     */
    private Date doCreateTime;
    /**
     * 发货日期
     */
    private Date shipTime;
    /**
     * DO类型
     */
    private String doType;
    /**
     * 是否半日达
     */
    private Integer isHalfDayDelivery;
    /**
     * 配送类型
     */
    private Integer deliveryLimitType;
    /**
     * 国家
     */
    private String country;
    /**
     * 省份
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区县
     */
    private String county;
    /**
     * 波次号
     */
    private String waveNo;
    /**
     * 分拣格号
     */
    private String sortGridNo;
    /**
     * 客户姓名
     */
    private String consigneeName;
    /**
     * 收货地址
     */
    private String address;
    /**
     * 邮编
     */
    private String postCode;
    /**
     * 电话
     */
    private String telephone;
    /**
     * 手机
     */
    private String mobile;
    /**
     * 配送公司名
     */
    private String distSuppCompName;
    /**
     * 发票数量
     */
    private Long invoiceQty;
    /**
     * 是否第一次购买
     */
    private String userDeffine1;
    /**
     * 冻结原因代码
     */
    private String holdCode;
    /**
     * 冻结原因
     */
    private String holdReason;
    /**
     * 冻结人
     */
    private String holdWho;
    /**
     * 冻结时间
     */
    private Date holdTime;
    /**
     * 代收货款金额
     */
    private BigDecimal productAmount;
    /**
     * 客户支付配送费
     */
    private BigDecimal orderDeliveryFee;
    /**
     * 退换货标识
     */
    private Integer exchangeFlag;
    /**
     * 重量
     */
    private BigDecimal grossWt;
    /**
     * 应收款
     */
    private BigDecimal receivable;

    private BigDecimal volume;

    /**
     * 配送公司Id
     */
    private Long carrierId;

    private String notes;

    private Date createdAt;

    /**
     * 目标分拣中心
     */
    private String lastDcName;

    private String refNo1;

    /**
     * 调拨类型
     */
    private Integer tranType;

    /**
     * 生鲜标记
     */
    private Integer isFresh;

    private Integer volumeType;

    /**
     * 贵重标记
     */
    private Integer isValuable;

    /**
     * 预计出库时间（标准）
     */
    private Date doFinishTime;


    /**
     * 最早配送时间(O2O)
     */
    private Date expectedArriveTime1;
    /**
     * 最晚时间(O2O)
     */
    private Date expectedArriveTime2;


    private String stationName;

    private BigDecimal orderAmount;

    private Date payTime;

    private String buyerRemark;//买家备注
    private String sellerRemark;//卖家备注

    private String orderSubType;

    private String productCode;
    private String productCname;
    private String productEname;
    private String specification;

    private BigDecimal detailExpectedQty;

    private String originalSoCode;

    private String trackingNo;

    public DoDetailDto() {

    }

    private DoDetailDto(Long id, String doNo, String status, String releaseStatus, BigDecimal expectedQty, BigDecimal shipQty, Date doCreateTime, Date shipTime, String doType, Integer isHalfDayDelivery, Integer
            deliveryLimitType, String country, String province, String city, String county, String waveNo, String sortGridNo, String consigneeName, String address, String postCode, String telephone, String mobile, String
                                distSuppCompName, Long invoiceQty, String userDeffine1, String holdCode, String holdReason, String holdWho, Date holdTime, BigDecimal productAmount, BigDecimal orderDeliveryFee, Integer exchangeFlag, BigDecimal
                                grossWt, BigDecimal receivable, String notes, Date createdAt, String lastDcName, String refNo1, Integer tranType) {
        this.id = id;
        this.doNo = doNo;
        this.status = status;
        this.releaseStatus = releaseStatus;
        this.expectedQty = expectedQty;
        this.shipQty = shipQty;
        this.doCreateTime = doCreateTime;
        this.shipTime = shipTime;
        this.doType = doType;
        this.isHalfDayDelivery = isHalfDayDelivery;
        this.deliveryLimitType = deliveryLimitType;
        this.country = country;
        this.province = province;
        this.city = city;
        this.county = county;
        this.waveNo = waveNo;
        this.sortGridNo = sortGridNo;
        this.consigneeName = consigneeName;
        this.address = address;
        this.postCode = postCode;
        this.telephone = telephone;
        this.mobile = mobile;
        this.distSuppCompName = distSuppCompName;
        this.invoiceQty = invoiceQty;
        this.userDeffine1 = userDeffine1;
        this.holdCode = holdCode;
        this.holdReason = holdReason;
        this.holdWho = holdWho;
        this.holdTime = holdTime;
        this.productAmount = productAmount;
        this.orderDeliveryFee = orderDeliveryFee;
        this.exchangeFlag = exchangeFlag;
        this.grossWt = grossWt;
        this.receivable = receivable;
        this.notes = notes;
        this.createdAt = createdAt;
        this.lastDcName = lastDcName;
        this.setRefNo1(refNo1);
        this.tranType = tranType;
    }

    private DoDetailDto(Long id, String doNo, BigDecimal expectedQty, Date doCreateTime, String doType, Integer isHalfDayDelivery, Integer deliveryLimitType, String country, String province, String city, String county, String
            consigneeName, String address, String postCode, String telephone, String mobile, String distSuppCompName, Long invoiceQty, BigDecimal volume, BigDecimal receivable, String lastDcName, Integer tranType) {
        this.id = id;
        this.doNo = doNo;
        this.expectedQty = expectedQty;
        this.doCreateTime = doCreateTime;
        this.doType = doType;
        this.isHalfDayDelivery = isHalfDayDelivery;
        this.deliveryLimitType = deliveryLimitType;
        this.country = country;
        this.province = province;
        this.city = city;
        this.county = county;
        this.consigneeName = consigneeName;
        this.address = address;
        this.postCode = postCode;
        this.telephone = telephone;
        this.mobile = mobile;
        this.distSuppCompName = distSuppCompName;
        this.invoiceQty = invoiceQty;
        this.volume = volume;
        this.receivable = receivable;
        this.lastDcName = lastDcName;
        this.tranType = tranType;
    }

    /**
     * 专用于"发货单" 界面分页查询显示，即仅适用于：DoHeaderDAO的public DataPage<DoDetailDto> findDoHeaderPageInfo（）方法
     */
    public DoDetailDto(Long id, String doNo, String status, String releaseStatus, BigDecimal expectedQty, BigDecimal shipQty, Date doCreateTime, Date shipTime, String doType, String province, String city, String county, String
            waveNo, String sortGridNo, String consigneeName, String address, String postCode, String telephone, String mobile, String distSuppCompName, Long invoiceQty, String userDeffine1, String holdCode, String holdReason,
                       String holdWho, Date holdTime, BigDecimal productAmount, BigDecimal orderDeliveryFee, Integer exchangeFlag, BigDecimal grossWt, BigDecimal receivable, String notes, Date createdAt, String refNo1, Date
                               doFinishTime, String buyerRemark, String sellerRemark, Date payTime, Date expectedArriveTime1, Date expectedArriveTime2, String orderSubType) {
        this(id, doNo, status, releaseStatus, expectedQty, shipQty, doCreateTime, shipTime, doType, null, null, null, province, city, county, waveNo, sortGridNo, consigneeName, address, postCode, telephone, mobile,
                distSuppCompName, invoiceQty, userDeffine1, holdCode, holdReason, holdWho, holdTime, productAmount, orderDeliveryFee, exchangeFlag, grossWt, receivable, notes, createdAt, null, refNo1, null);
        this.expectedArriveTime1 = expectedArriveTime1;
        this.expectedArriveTime2 = expectedArriveTime2;
        this.doFinishTime = doFinishTime;
        this.sellerRemark = sellerRemark;
        this.buyerRemark = buyerRemark;
        this.payTime = payTime;
        this.orderSubType = orderSubType;
    }

    public DoDetailDto(Long id, String doNo, String status, String releaseStatus, BigDecimal expectedQty, BigDecimal shipQty, Date doCreateTime, Date shipTime, String doType, String province, String city, String county, String
            waveNo, String sortGridNo, String consigneeName, String address, String postCode, String telephone, String mobile, String distSuppCompName, Long invoiceQty, String userDeffine1, String holdCode, String holdReason,
                       String holdWho, Date holdTime, BigDecimal productAmount, BigDecimal orderDeliveryFee, Integer exchangeFlag, BigDecimal grossWt, BigDecimal receivable, String notes, Date createdAt, String refNo1, Date
                               doFinishTime, String buyerRemark, String sellerRemark, Date payTime, Date expectedArriveTime1, Date expectedArriveTime2, String orderSubType, String productCode, String productCname, String productEname,
                       String specification, BigDecimal detailExpectedQty) {
        this(id, doNo, status, releaseStatus, expectedQty, shipQty, doCreateTime, shipTime, doType, null, null, null, province, city, county, waveNo, sortGridNo, consigneeName, address, postCode, telephone, mobile,
                distSuppCompName, invoiceQty, userDeffine1, holdCode, holdReason, holdWho, holdTime, productAmount, orderDeliveryFee, exchangeFlag, grossWt, receivable, notes, createdAt, null, refNo1, null);
        this.expectedArriveTime1 = expectedArriveTime1;
        this.expectedArriveTime2 = expectedArriveTime2;
        this.doFinishTime = doFinishTime;
        this.sellerRemark = sellerRemark;
        this.buyerRemark = buyerRemark;
        this.payTime = payTime;
        this.orderSubType = orderSubType;
        this.productCode = productCode;
        this.productCname = productCname;
        this.productEname = productEname;
        this.specification = specification;
        this.detailExpectedQty = detailExpectedQty;
    }

    /**
     * 专用于"生成波次" 界面分页查询显示
     */
    public DoDetailDto(Long id, String doNo, BigDecimal expectedQty, Date doCreateTime, String doType, Integer isHalfDayDelivery, Integer deliveryLimitType, String country, String province, String city, String county, String
            consigneeName, String address, String postCode, String telephone, String mobile, String distSuppCompName, Long invoiceQty, BigDecimal volume, BigDecimal receivable, String lastDcName, Integer tranType, Integer
                               isFresh, Integer isValuable, Date doFinishTime, BigDecimal grossWt, String stationName, BigDecimal orderAmount, Integer volumeType, String buyerRemark, String sellerRemark, Integer times, Date payTime) {
        this(id, doNo, expectedQty, doCreateTime, doType, isHalfDayDelivery, deliveryLimitType, country, province, city, county, consigneeName, address, postCode, telephone, mobile, distSuppCompName, invoiceQty, volume,
                receivable, lastDcName, tranType);
        this.orderAmount = orderAmount;
        this.volumeType = volumeType;
        this.buyerRemark = buyerRemark;
        this.sellerRemark = sellerRemark;
        this.times = times;
        this.payTime = payTime;
        this.isFresh = isFresh;
        this.isValuable = isValuable;
        this.doFinishTime = doFinishTime;
        this.grossWt = grossWt;
        this.stationName = stationName;
    }

    /**
     * description  发运订单-> 导出明细
     *
     * <AUTHOR>
     * @date 2021-01-11 10:28:05
     */
    public DoDetailDto(Long id, String doNo, String status, String releaseStatus, BigDecimal expectedQty, BigDecimal shipQty, Date doCreateTime, Date shipTime, String doType, String province, String city, String county, String
            waveNo, String sortGridNo, String consigneeName, String address, String postCode, String telephone, String mobile, String distSuppCompName, Long invoiceQty, String userDeffine1, String holdCode, String holdReason,
                       String holdWho, Date holdTime, BigDecimal productAmount, BigDecimal orderDeliveryFee, Integer exchangeFlag, BigDecimal grossWt, BigDecimal receivable, String notes, Date createdAt, String refNo1, Date
                               doFinishTime, String buyerRemark, String sellerRemark, Date payTime, Date expectedArriveTime1, Date expectedArriveTime2, String orderSubType, String productCode, String productCname,
                       String specification, BigDecimal detailExpectedQty, String originalSoCode, String trackingNo, Long carrierId, String productEname) {
        this(id, doNo, status, releaseStatus, expectedQty, shipQty, doCreateTime, shipTime, doType, null, null, null, province, city, county, waveNo, sortGridNo, consigneeName, address, postCode, telephone, mobile,
                distSuppCompName, invoiceQty, userDeffine1, holdCode, holdReason, holdWho, holdTime, productAmount, orderDeliveryFee, exchangeFlag, grossWt, receivable, notes, createdAt, null, refNo1, null);
        this.expectedArriveTime1 = expectedArriveTime1;
        this.expectedArriveTime2 = expectedArriveTime2;
        this.doFinishTime = doFinishTime;
        this.sellerRemark = sellerRemark;
        this.buyerRemark = buyerRemark;
        this.payTime = payTime;
        this.orderSubType = orderSubType;
        this.productCode = productCode;
        this.productCname = productCname;
        this.specification = specification;
        this.detailExpectedQty = detailExpectedQty;
        this.originalSoCode = originalSoCode;
        this.trackingNo = trackingNo;
        this.carrierId = carrierId;
        this.productEname = productEname;
    }

    private Integer times;

    public Integer getTimes() {
        return times;
    }

    public void setTimes(Integer times) {
        this.times = times;
    }

    public String getBuyerRemark() {
        return buyerRemark;
    }

    public void setBuyerRemark(String buyerRemark) {
        this.buyerRemark = buyerRemark;
    }

    public String getSellerRemark() {
        return sellerRemark;
    }

    public void setSellerRemark(String sellerRemark) {
        this.sellerRemark = sellerRemark;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReleaseStatus() {
        return releaseStatus;
    }

    public void setReleaseStatus(String releaseStatus) {
        this.releaseStatus = releaseStatus;
    }

    public BigDecimal getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(BigDecimal expectedQty) {
        this.expectedQty = expectedQty;
    }

    public BigDecimal getShipQty() {
        return shipQty;
    }

    public void setShipQty(BigDecimal shipQty) {
        this.shipQty = shipQty;
    }

    public Date getDoCreateTime() {
        return doCreateTime;
    }

    public void setDoCreateTime(Date doCreateTime) {
        this.doCreateTime = doCreateTime;
    }

    public Date getShipTime() {
        return shipTime;
    }

    public void setShipTime(Date shipTime) {
        this.shipTime = shipTime;
    }

    public String getDoType() {
        return doType;
    }

    public void setDoType(String doType) {
        this.doType = doType;
    }

    public Integer getIsHalfDayDelivery() {
        return isHalfDayDelivery;
    }

    public void setIsHalfDayDelivery(Integer isHalfDayDelivery) {
        this.isHalfDayDelivery = isHalfDayDelivery;
    }

    public Integer getDeliveryLimitType() {
        return deliveryLimitType;
    }

    public void setDeliveryLimitType(Integer deliveryLimitType) {
        this.deliveryLimitType = deliveryLimitType;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getDistSuppCompName() {
        return distSuppCompName;
    }

    public void setDistSuppCompName(String distSuppCompName) {
        this.distSuppCompName = distSuppCompName;
    }

    public Long getInvoiceQty() {
        return invoiceQty;
    }

    public void setInvoiceQty(Long invoiceQty) {
        this.invoiceQty = invoiceQty;
    }

    public String getUserDeffine1() {
        return userDeffine1;
    }

    public void setUserDeffine1(String userDeffine1) {
        this.userDeffine1 = userDeffine1;
    }

    public String getHoldCode() {
        return holdCode;
    }

    public void setHoldCode(String holdCode) {
        this.holdCode = holdCode;
    }

    public String getHoldReason() {
        return holdReason;
    }

    public void setHoldReason(String holdReason) {
        this.holdReason = holdReason;
    }

    public String getHoldWho() {
        return holdWho;
    }

    public void setHoldWho(String holdWho) {
        this.holdWho = holdWho;
    }

    public Date getHoldTime() {
        return holdTime;
    }

    public void setHoldTime(Date holdTime) {
        this.holdTime = holdTime;
    }

    public BigDecimal getProductAmount() {
        return productAmount;
    }

    public void setProductAmount(BigDecimal productAmount) {
        this.productAmount = productAmount;
    }

    public BigDecimal getOrderDeliveryFee() {
        return orderDeliveryFee;
    }

    public void setOrderDeliveryFee(BigDecimal orderDeliveryFee) {
        this.orderDeliveryFee = orderDeliveryFee;
    }

    public void setGrossWt(BigDecimal grossWt) {
        this.grossWt = grossWt;
    }

    public BigDecimal getGrossWt() {
        return grossWt;
    }

    public Integer getExchangeFlag() {
        return exchangeFlag;
    }

    public void setExchangeFlag(Integer exchangeFlag) {
        this.exchangeFlag = exchangeFlag;
    }

    public BigDecimal getReceivable() {
        return receivable;
    }

    public void setReceivable(BigDecimal receivable) {
        this.receivable = receivable;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public Long getCarrierId() {
        return carrierId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public String getLastDcName() {
        return lastDcName;
    }


    public void setLastDcName(String lastDcName) {
        this.lastDcName = lastDcName;
    }

    public void setRefNo1(String refNo1) {
        this.refNo1 = refNo1;
    }

    public String getRefNo1() {
        return refNo1;
    }

    public Integer getTranType() {
        return tranType;
    }

    public void setTranType(Integer tranType) {
        this.tranType = tranType;
    }

    public Integer getIsFresh() {
        return isFresh;
    }

    public void setIsFresh(Integer isFresh) {
        this.isFresh = isFresh;
    }

    public Integer getIsValuable() {
        return isValuable;
    }

    public void setIsValuable(Integer isValuable) {
        this.isValuable = isValuable;
    }

    public Date getDoFinishTime() {
        return doFinishTime;
    }

    public void setDoFinishTime(Date doFinishTime) {
        this.doFinishTime = doFinishTime;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }


    public String getProductCname() {
        return productCname;
    }

    public void setProductCname(String productCname) {
        this.productCname = productCname;
    }

    public String getProductEname() {
        return productEname;
    }

    public void setProductEname(String productEname) {
        this.productEname = productEname;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public Integer getVolumeType() {
        return volumeType;
    }

    public void setVolumeType(Integer volumeType) {
        this.volumeType = volumeType;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Date getExpectedArriveTime1() {
        return expectedArriveTime1;
    }

    public void setExpectedArriveTime1(Date expectedArriveTime1) {
        this.expectedArriveTime1 = expectedArriveTime1;
    }

    public Date getExpectedArriveTime2() {
        return expectedArriveTime2;
    }

    public void setExpectedArriveTime2(Date expectedArriveTime2) {
        this.expectedArriveTime2 = expectedArriveTime2;
    }

    public String getOrderSubType() {
        return orderSubType;
    }

    public void setOrderSubType(String orderSubType) {
        this.orderSubType = orderSubType;
    }

    public BigDecimal getDetailExpectedQty() {
        return detailExpectedQty;
    }

    public void setDetailExpectedQty(BigDecimal detailExpectedQty) {
        this.detailExpectedQty = detailExpectedQty;
    }

    public String getOriginalSoCode() {
        return originalSoCode;
    }

    public void setOriginalSoCode(String originalSoCode) {
        this.originalSoCode = originalSoCode;
    }

    public String getTrackingNo() {
        return trackingNo;
    }

    public void setTrackingNo(String trackingNo) {
        this.trackingNo = trackingNo;
    }
}
