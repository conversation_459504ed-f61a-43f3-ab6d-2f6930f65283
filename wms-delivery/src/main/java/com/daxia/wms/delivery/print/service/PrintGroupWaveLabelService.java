package com.daxia.wms.delivery.print.service;

import java.util.List;

import com.daxia.wms.delivery.print.dto.SingleWaveLabelPrintDTO;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.print.dto.PrintData;

/**
 * 团购波次标签打印
 */
public interface PrintGroupWaveLabelService {
	/**
	 * 打印一个波次的波次标签和箱标签
	 * @param wave
	 * @return
	 */
	public List<PrintData> printOneWaveCartonLabel(WaveHeader wave);
	
	public List<PrintData> printCartons(List<Long> ids);
	
	public List<PrintData> printCartonsByDos(List<Long> ids);
	public List<PrintData> printCartonsByDosForWave(List<Long> ids);

	/**
	 * 批量打印波次标签信息
	 * @param ids
	 * @return
	 */
	public PrintData printWaveCartonLabels(List<Long> ids);
}
