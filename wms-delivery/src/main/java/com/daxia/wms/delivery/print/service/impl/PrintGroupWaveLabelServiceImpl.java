package com.daxia.wms.delivery.print.service.impl;

import com.daxia.framework.common.log.StopWatch;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.PrintStatus;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.print.dto.SingleWaveLabelPrintDTO;
import com.daxia.wms.delivery.print.helper.WaybillPrintHelper;
import com.daxia.wms.delivery.print.service.PrintGroupWaveLabelService;
import com.daxia.wms.delivery.print.service.carton.PrintCartonDispatcher;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.wave.dao.BatchGroupWaveDAO;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.service.PrintLogService;
import com.daxia.wms.util.ExecutorUtil;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import org.jboss.seam.Component;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.jboss.seam.contexts.Lifecycle;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Name("printGroupWaveLabelService")
@lombok.extern.slf4j.Slf4j
public class PrintGroupWaveLabelServiceImpl implements PrintGroupWaveLabelService {
    @In
    private WaveService waveService;

    @In
    private CartonService cartonService;

    @In
    private WarehouseService warehouseService;

    @In
    private BatchGroupWaveDAO batchGroupWaveDAO;

    @In
    private PrintLogService printLogService;

    @In
    private DeliveryOrderService deliveryOrderService;

    @In
    private PrintCartonDispatcher printCartonDispatcher;


    @Override
    public PrintData printWaveCartonLabels(List<Long> ids) {
        StopWatch stopWatch = new StopWatch("PrintWaveCartonLabel");
        List<WaveHeader> waves = waveService.getWaveList(ids);
        List<PrintData> printDatas = new ArrayList<PrintData>();
        List<String> errorWaves = new ArrayList<String>();
        for (WaveHeader wave : waves) {
            try {
                printDatas.addAll(printOneWaveCartonLabel(wave));
            } catch (Exception e) {
                log.error("WaveCartonLabelPrint Error: waveId = " + wave.getId() + ", " + e.getMessage());
                errorWaves.add(wave.getWaveNo());
            }
        }

        List<PrintData> resultList = WaybillPrintHelper.sortPrintDataByPrintTemplate(printDatas);

        PrintData result = new PrintData();
        result.setData(new Gson().toJson(resultList));
        result.setTemplateJs(getPrintJs(resultList));
        // 设置提示信息
        if (errorWaves.size() > 0) {
            // 实际打印波次数量
            result.setUserDefine1(String.valueOf(waves.size()));
            result.setUserDefine2(String.valueOf(waves.size() - errorWaves.size()));
            result.setUserDefine3(Arrays.toString(errorWaves.toArray()));
        }

        log.info(stopWatch.stop());
        return result;
    }

    private String getPrintJs(List<PrintData> dataList) {
        Map<String, String> templateMap = new HashMap<String, String>();
        String templateJs = "";
        for (PrintData printData : dataList) {
            if (!templateMap.containsKey(printData.getPrintCfg().getLodopTemplate())) {
                templateJs = templateJs + printData.getTemplateJs();
                templateMap.put(printData.getPrintCfg().getLodopTemplate(), templateJs + printData.getTemplateJs());
            }
        }
        return templateJs;
    }

    @Override
    @Transactional
    public List<PrintData> printOneWaveCartonLabel(WaveHeader wave) {
        PrintStatus printStatus = cartonService.getCartonPrintStatusInWave(wave.getId());
        if (null == printStatus) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
//        if (PrintStatus.NO != printStatus) {
//            throw new DeliveryException(DeliveryException.THE_CARTON_IN_WAVE_PART_PRINTED);
//        }
        List<SingleWaveLabelPrintDTO> waveLabelDtoList = new ArrayList<SingleWaveLabelPrintDTO>();
        List<DeliveryOrderHeader> doHeaders = wave.getDoHeaders();
        List<PrintData> printDatas = Lists.newArrayList();
        // 普通箱、顺丰、雅玛多分开处理
        PrintData printData;
        for (DeliveryOrderHeader doHeader : doHeaders) {
            // 只打印非冻结订单
            if (!Constants.ReleaseStatus.RELEASE.getValue().equals(doHeader.getReleaseStatus())) {
                continue;
            }
            printData = printCartonDispatcher.print(doHeader.getId(), new ArrayList<Long>());
            if (printData != null) {
                printDatas.add(printData);
            }
        }

        // 添加促销单品波次标签
        //waveLabelDtoList.add(this.getPrintSingleWaveLabelDto(wave.getId()));

        // 没有可打印箱信息时，直接抛异常
        if (ListUtil.isNullOrEmpty(printDatas)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        wave.setUpdatedAt(DateUtil.getNowTime());
        waveService.updateWaveHeader(wave);
        return printDatas;
    }

    @SuppressWarnings("rawtypes")
    @Transactional
    @Override
    public List<PrintData> printCartons(List<Long> ids) {
        StopWatch stopWatch = new StopWatch("PrintCarton(SF)Label");

        List<CartonHeader> cartons = cartonService.findRLCartonsByCartonIds(ids);
        List<PrintData> printDatas = Lists.newArrayList();
        for (CartonHeader carton : cartons) {
            printDatas.add(printCartonDispatcher.print(null, Lists.newArrayList(carton.getId())));
        }

        // 没有可打印箱信息时，直接抛异常
        if (ListUtil.isNullOrEmpty(printDatas)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        log.info(stopWatch.stop());
        return printDatas;
    }

    @SuppressWarnings("rawtypes")
    @Transactional
    @Override
    public List<PrintData> printCartonsByDos(List<Long> ids) {
//        List<PrintData> printDatas = Lists.newArrayList();
        //根据DO 查找 箱头信息
        List<Long> cartonList = cartonService.findCartonIdByDoId(ids);
        Long warehouseId = ParamUtil.getCurrentWarehouseId();

        // 将所有future收集到一个列表中
        List<CompletableFuture<PrintData>> futures=new ArrayList<>();
        for (Long id : cartonList) {
            futures.add(
                    CompletableFuture.supplyAsync(()->{
                        try {
                            // 设置上下文
                            Lifecycle.beginCall();
                            ParamUtil.setCurrentWarehouseId(warehouseId);
                            return ((PrintCartonDispatcher) Component.getInstance(PrintCartonDispatcher.class)).print(null, Arrays.asList(id));
                        }finally {
                            Lifecycle.endCall();
                        }
                    }, ExecutorUtil.ioExecutor)
            );
        }

        return WaybillPrintHelper.sortPrintDataByPrintTemplate(futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
//        PrintData printData = printCartonDispatcher.print(null, cartonList);
//        printData.getDtoList().forEach(reportDto -> {
//            PrintData data = new PrintData();
//            BeanUtil.copyProperties(printData,data);
//            data.setDtoList(Lists.newArrayList(reportDto));
//            printDatas.add(data);
//        });

//        return Lists.newArrayList(printData);
    }
    @Override
    public List<PrintData> printCartonsByDosForWave(List<Long> ids) {
//        List<PrintData> printDatas = Lists.newArrayList();
        //根据DO 查找 箱头信息
        List<Long> cartonList = cartonService.findCartonIdByDoId(ids);
        Long warehouseId = ParamUtil.getCurrentWarehouseId();

        // 将所有future收集到一个列表中
        List<CompletableFuture<PrintData>> futures=new ArrayList<>();
        for (Long id : cartonList) {
            futures.add(
                    CompletableFuture.supplyAsync(()->{
                        try {
                            // 设置上下文
                            Lifecycle.beginCall();
                            ParamUtil.setCurrentWarehouseId(warehouseId);
                            return ((PrintCartonDispatcher) Component.getInstance(PrintCartonDispatcher.class)).printForWave(null, Arrays.asList(id));
                        }finally {
                            Lifecycle.endCall();
                        }
                    }, ExecutorUtil.ioExecutor)
            );
        }

        return WaybillPrintHelper.sortPrintDataByPrintTemplate(futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
//        PrintData printData = printCartonDispatcher.print(null, cartonList);
//        printData.getDtoList().forEach(reportDto -> {
//            PrintData data = new PrintData();
//            BeanUtil.copyProperties(printData,data);
//            data.setDtoList(Lists.newArrayList(reportDto));
//            printDatas.add(data);
//        });

//        return Lists.newArrayList(printData);
    }
}
