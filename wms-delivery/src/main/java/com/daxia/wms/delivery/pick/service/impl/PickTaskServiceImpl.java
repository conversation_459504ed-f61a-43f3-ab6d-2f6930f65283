package com.daxia.wms.delivery.pick.service.impl;

import com.daxia.framework.common.util.*;
import com.daxia.framework.system.util.WmsUtil;
import com.daxia.wms.Constants.*;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.context.DeliveryContext;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateDetail;
import com.daxia.wms.delivery.pick.dao.PickTaskDAO;
import com.daxia.wms.delivery.pick.dto.PickTaskDto;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.pick.filter.PickTaskFilter;
import com.daxia.wms.delivery.pick.service.PickTaskService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.master.MasterException;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.entity.Partition;
import com.daxia.wms.master.entity.SkuGroup;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.service.LocationService;
import com.daxia.wms.master.service.PartitionService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.pick.vo.PickLocOptDTO;
import com.daxia.wms.stock.stock.dto.Stock2AllocateDTO;
import com.daxia.wms.stock.stock.dto.Stock4CombiDoAllocateDTO;
import com.daxia.wms.stock.stock.dto.StockDTO;
import com.daxia.wms.stock.stock.entity.StockBatchLocLpn;
import com.daxia.wms.stock.stock.service.IOperator;
import com.daxia.wms.stock.stock.service.StockService;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;

/**
 * 拣货任务实现类
 */
@Name("com.daxia.wms.delivery.pickTaskService")
@lombok.extern.slf4j.Slf4j
public class PickTaskServiceImpl implements PickTaskService {
    @In
    private PickTaskDAO pickTaskDAO;
	@In
	private StockService stockService;
    @In
    private LocationService locationService;
    @In
    private PartitionService partitionService;
	@In("pickSplitOperator")
	private IOperator pickSplitOperator;
	@In
	private WarehouseService warehouseService;

    /**
	 * 根据id取得拣货任务
	 */
    @Override
	public PickTask getTaskById(Long taskId) {
        return pickTaskDAO.get(taskId);
    }
        
    /**
	 * 更新拣货任务
	 * 
	 * @param PickTask
	 */
    @Override
    @Transactional
    public void updatePickTask(PickTask PickTask) {
    	pickTaskDAO.update(PickTask);
    }

    /**
	 * 不分页查询拣货任务
	 * 
	 * @param pktTaskFilter
	 * @return
	 */
    @SuppressWarnings("deprecation")
	@Override
    public List<PickTask> query(PickTaskFilter pktTaskFilter) {
        return pickTaskDAO.findByFilter(pktTaskFilter);
    }

    /**
	 * 删除拣货任务
	 * 
	 * @param pickTask
	 */
    @Override
    @Transactional
    public void removeTask(PickTask pickTask) {
    	pickTaskDAO.remove(pickTask);
    }

    /**
	 * 根据DoHeaderId删除拣货任务
	 */
	@Override
	@Transactional
	public void cancelPickTaskByDoHeaderId(Long doHeaderId) {
		pickTaskDAO.cancelPickTaskByDoHeaderId(doHeaderId);
	}

	/**
	 * 创建拣货任务
	 */
	@Override
	@Transactional
    public PickTask createPickTask(PickTask task, BigDecimal qtyUnit, BigDecimal qty, Long newFrmStockId, String updateBy) {
        PickTask newTask = new PickTask();
        newTask.setDoHeaderId(task.getDoHeaderId());
        newTask.setDoDetailId(task.getDoDetailId());
        newTask.setWaveHeaderId(task.getWaveHeaderId());
        newTask.setPktHeaderId(task.getPktHeaderId());
        newTask.setLocId(task.getLocId());
        newTask.setLotId(task.getLotId());
        newTask.setLpnNo(task.getLpnNo());
        newTask.setPackDetailId(task.getPackDetailId());
        newTask.setPackId(task.getPackId());
        newTask.setNotes(newTask.getNotes());
        newTask.setQty(qty);
		newTask.setQtyUnit(qtyUnit);
        newTask.setSkuId(task.getSkuId());
        newTask.setToLocId(task.getToLocId());
        newTask.setToLpnNo(task.getToLpnNo());
        newTask.setFmStockId(newFrmStockId);
        newTask.setAllocatingId(task.getAllocatingId());
        newTask.setCreatedAt(DateUtil.getNowTime());
        newTask.setCreatedBy(updateBy);
        newTask.setUpdatedAt(DateUtil.getNowTime());
        newTask.setUpdatedBy(updateBy);
        newTask.setStatus(DoStatus.ALLALLOCATED.getValue());
        pickTaskDAO.save(newTask);
		return newTask;
	}

	/**
	 * 根据任务号和状态获取拣货任务
	 */
	@Override
	public PickTask getPickTask(String taskNum, TaskStatus taskStatus) {
		return pickTaskDAO.getPickTask(taskNum, taskStatus.getValue());
	}

	/**
	 * 根据do单头和明细获取拣货任务
	 */
	@Override
	public List<PickTask> getPickTasks(Long doId, Long doDetailId) {
		return pickTaskDAO.getPickTasks(doId, doDetailId);
	}
	
	/**
	 * 根据波次id和状态获取拣货任务
	 */
    @Override
	public List<PickTaskDto> getPickTasksByWaveIdGrouped(Long waveId, String status){
    	List<PickTask> pickTasks = pickTaskDAO.getPickTasksByWaveId(waveId, status);
        if(ListUtil.isNullOrEmpty(pickTasks)){
            throw new DeliveryException(DeliveryException.WAVE_ALL_PICKED);
        }
        return setPickTaskDtosGrouped(pickTasks);
    }
    
    @Override
    public Map<String, String> getPickPathOfWave(Long waveId) {
    	List<PickTask> pickTasks = pickTaskDAO.getPickTasksByWaveId(waveId);
        if(ListUtil.isNullOrEmpty(pickTasks)){
            return new HashMap<String, String>();
        }
		Map<String, List<PickLocOptDTO>> srcPathMap = getPathFromTasks(pickTasks);
 		//Map<String, String> dstncMap = PlusClientUtil.getService().getNormalPathLength(ParamUtil.getCurrentWarehouseId(), srcPathMap);
 		Map<String, String> dstncMap = new HashMap<String,String>();
		return getPathMapToShow(srcPathMap, dstncMap);
    }

	@Override
    public Map<String, String> getOptPickPathOfWave(Long waveId) {
    	List<PickTask> pickTasks = pickTaskDAO.getPickTasksByWaveId(waveId);
        if(ListUtil.isNullOrEmpty(pickTasks)){
            return new HashMap<String, String>();
        }
		Map<String, List<PickLocOptDTO>> srcPathMap = getPathFromTasks(pickTasks);
//		Map<String, List<PickLocOptDTO>> optPathMap = PlusClientUtil.getService().optimizePickLoc(ParamUtil.getCurrentWarehouseId(), srcPathMap);
		//Map<String, String> dstncMap = PlusClientUtil.getService().getOptimizedPathLength(ParamUtil.getCurrentWarehouseId(), srcPathMap);
		Map<String, String> dstncMap = new HashMap<String,String>();
 		return dstncMap;
    }

	private Map<String, List<PickLocOptDTO>> getPathFromTasks(List<PickTask> pickTasks) {
		List<PickTaskDto> detailDtos = sortPickTaskCommonly(pickTasks);
		return makeSrcPathMap(detailDtos);
	}
    
	private Map<String, String> getPathMapToShow(Map<String, List<PickLocOptDTO>> resultMap, Map<String, String> dstncMap) {
 		Map<String, String> finalMap= new HashMap<String, String>();
		for (Entry<String, List<PickLocOptDTO>> entry : resultMap.entrySet()) {
			if (dstncMap != null && dstncMap.get(entry.getKey()) != null) {
				finalMap.put(entry.getKey(), entry.getValue().toString() + dstncMap.get(entry.getKey()));
			} else {
				finalMap.put(entry.getKey(), entry.getValue().toString());
			}
 		}
		return finalMap;
	}

	/**
	 * 根据波次id和状态获取拣货任务
	 */
	@Override
	public List<PickTaskDto> getPickTasksByWaveIdGroupedOpt(WaveHeader wave, PktStatus status) {
		Long waveId = wave.getId();
		String[] lastPickedInfo = null;
		if (WaveStatus.ALLALLOCATED.getValue().compareTo(wave.getWaveStatus()) < 0) {
			lastPickedInfo = pickTaskDAO.getLastPickedLocOfWave(waveId);
		}
		List<PickTask> pickTasks = pickTaskDAO.getPickTasksByWaveId(waveId, status.getValue());
		if (ListUtil.isNullOrEmpty(pickTasks)) {
			throw new DeliveryException(DeliveryException.WAVE_ALL_PICKED);
		}
		return setPickTaskDtosGroupedOpt(pickTasks, lastPickedInfo);
	}
    
    /**
	 * 根据波次id和状态获取拣货任务
	 */
    @Override
	public List<PickTaskDto> getPickTasksByWaveId(Long waveId, String status){
        List<PickTask> pickTasks = pickTaskDAO.getPickTasksByWaveId(waveId, status);
        if(ListUtil.isNullOrEmpty(pickTasks)){
            throw new DeliveryException(DeliveryException.WAVE_ALL_PICKED);
        }
        return setPickTaskDtos(pickTasks);
    }

    /**
	 * 根据拣货头ID返回拣货明细，包括sku id，loc id， 需要拣货的数量（不区分批次，group by sku和库位）
	 */
	@Override
	public List<PickTaskDto> getPickTasksByPktIdGrouped(Long pktId, PktStatus status) {
		List<PickTask> pickTasks = pickTaskDAO.getPickTasksByPktId(pktId, status.getValue());
		if (ListUtil.isNullOrEmpty(pickTasks)) {
			throw new DeliveryException(DeliveryException.PICK_ERROR_TASK_NOT_EXIST);
		}
		return setPickTaskDtosGrouped(pickTasks);
	}

	@Override
	public List<PickTaskDto> getTspTasksByPktIdGrouped(PickHeader pickHeader, PktStatus status) {
		Long pickHId = pickHeader.getId();
		String[] lastPickedInfo = pickTaskDAO.getLastPickedLocOfPktH(pickHId);
		
		List<PickTask> pickTasks = pickTaskDAO.getPickTasksByPktId(pickHId, status.getValue());
		if (ListUtil.isNullOrEmpty(pickTasks)) {
			throw new DeliveryException(DeliveryException.PICK_ERROR_TASK_NOT_EXIST);
		}
		return SortPickTaskByTsp(pickTasks, lastPickedInfo);
	}
    
    /**
	 * 根据波次id、库位id、skuId、状态获取拣货任务
	 */
    @Override
	public List<PickTask> findByWaveId(Long skuId, Long locId, Long waveId, TaskStatus status,Long packageDetailId) {
    	List<PickTask> pickTasks = pickTaskDAO.getPickTasksByWave(waveId, locId, skuId, status.getValue(),packageDetailId);
        if (ListUtil.isNullOrEmpty(pickTasks)) {
            throw new DeliveryException(
                    DeliveryException.DELIVERYORDER_CANNOT_PICK_PICKTASKNOTEXIST);
        }
        return pickTasks;
    }
    
    /**
	 * 根据拣货单id、库位id、skuId和状态获取拣货任务
	 */
    @Override
	public List<PickTask> findByPktId(Long skuId, Long locId, Long pktId, TaskStatus status,Long packageDetailId) {
    	List<PickTask> pickTasks = pickTaskDAO.getPktTasksByPkt(pktId, locId, skuId, status.getValue(),packageDetailId);
        if (ListUtil.isNullOrEmpty(pickTasks)) {
            throw new DeliveryException(
                    DeliveryException.DELIVERYORDER_CANNOT_PICK_PICKTASKNOTEXIST);
        }
        return pickTasks;
    }
    
    /**
	 * 设置拣货任务
	 * 
	 * @param pickTasks
	 * @return
	 */
    private List<PickTaskDto> setPickTaskDtos(List<PickTask> pickTasks){
		// 按照拣货顺序排序
        Collections.sort(pickTasks, new Comparator<PickTask>(){
            @Override
            public int compare(PickTask o1, PickTask o2) {
                String key1 = o1.getLocation().getPickSeq() + "#" + o1.getSku().getProductCode();
                String key2 = o2.getLocation().getPickSeq() + "#" + o2.getSku().getProductCode();
                return key1.compareTo(key2);
            }
        });
        List<PickTaskDto> taskDtos = new ArrayList<PickTaskDto>();
        for(PickTask detail : pickTasks){
        	PickTaskDto dto = new PickTaskDto();
        	BeanUtils.copyProperties(detail, dto);
    		taskDtos.add(dto);
        }
        return taskDtos;
    }
    
    /**
	 * 合并且排序拣货任务，并进行拣货路径优化(wms打印、客户端打印，rf拣货有用到)
	 * 
	 * @param pickTasks
	 * @return
	 */
    @Override
	public List<PickTaskDto> setPickTaskDtosGrouped(List<PickTask> pickTasks){
		List<PickTaskDto> detailDtos = sortPickTaskCommonly(pickTasks);
    	Integer needOptimize = SystemConfig.getConfigValueInt("delivery.pick.needOpt", ParamUtil.getCurrentWarehouseId()); 
    	if (YesNo.YES.getValue().equals(needOptimize)) {
    		detailDtos = optimizePickLoc(detailDtos, null);
    	}
    	return detailDtos;
    }

	public List<PickTaskDto> SortPickTaskByTsp(List<PickTask> pickTasks, String[] lastPickedInfo) {
		List<PickTaskDto> detailDtos = sortPickTaskCommonly(pickTasks);
		return optimizePickLoc(detailDtos, lastPickedInfo);
	}
    
	private List<PickTaskDto> sortPickTaskCommonly(List<PickTask> pickTasks) {
		Map<String, PickTaskDto> map = new HashMap<String, PickTaskDto>();
		//拣货任务先按分拣格号排序,方便生成分拣信息
		Collections.sort(pickTasks, new Comparator<PickTask>() {
			@Override
			public int compare(PickTask o1, PickTask o2) {
				if (StringUtils.isEmpty(o1.getDoHeader().getSortGridNo())) {
                    return -1;
                }
				if (StringUtils.isEmpty(o2.getDoHeader().getSortGridNo())) {
                    return 1;
                }
				return Integer.valueOf(o1.getDoHeader().getSortGridNo())
						.compareTo(Integer.valueOf(o2.getDoHeader().getSortGridNo()));
			}
		});
		Warehouse warehouse = warehouseService.getWarehouse(ParamUtil.getCurrentWarehouseId());

		for (PickTask pickTask : pickTasks) {
			String key =
					pickTask.getStockStatus() + "#" + pickTask.getLocation().getPickSeq() + "#" + pickTask.getLocation().getLocCode() + "#" + pickTask.getSkuId() + "#" + pickTask.getStockBatchAtt().getLotatt14();
			if (WarehouseType.CDC.getValue().equals(warehouse.getWarehouseType())) {
				key =
						pickTask.getStockStatus() + "#" + pickTask.getLocation().getPickSeq() + "#" + pickTask.getLocation().getLocCode() + "#" + pickTask.getSkuId() + "#" + pickTask.getStockBatchAtt().getLotatt17();
			}

//			Map<Long, SkuGroup> skuGroupMap = Optional.ofNullable(DeliveryContext.getSkuGroup()).orElse(Maps.newHashMap());
//			SkuGroup skuGroup = skuGroupMap.get(pickTask.getSkuId());
//			if (skuGroup != null) {
//				Integer groupQty = skuGroup.getGroupQty();
//				if (pickTask.getQty().intValue() % groupQty == 0 && "0".equals(skuGroup.getTraceGoods())){
//					long skuId = Long.MAX_VALUE - pickTask.getSkuId();
//					key =
//							pickTask.getStockStatus() + "#" + pickTask.getLocation().getPickSeq() + "#" + pickTask.getLocation().getLocCode() + "#" + skuId + "#" + pickTask.getStockBatchAtt().getLotatt14();
//				}
//			}

			PickTaskDto value = map.get(key);
			if (value == null) {
				PickTaskDto dto = new PickTaskDto();
				BeanUtils.copyProperties(pickTask, dto);
				dto.setDoNeedQtyIsEquale(Boolean.TRUE);
				dto.getDoIdSet().add(pickTask.getDoHeaderId());
				dto.setDoNum(dto.getDoIdSet().size());
				//批号
 				dto.setLotNo(pickTask.getStockBatchAtt().getLotatt05());
 				dto.setLpnNo(pickTask.getStockBatchAtt().getLotatt17());
				dto.setLotId(pickTask.getLotId());
				dto.setLotatt14(pickTask.getStockBatchAtt().getLotatt14());
				dto.setSortInfo("["+pickTask.getDoHeader().getSortGridNo()+"]*"+pickTask.getQty());
//				if (skuGroup != null) {
//					Integer groupQty = skuGroup.getGroupQty();
//					if (dto.getQty().intValue() % groupQty == 0 && "0".equals(skuGroup.getTraceGoods())){
//						dto.setQty(BigDecimal.valueOf(dto.getQty().intValue() / groupQty));
//						Long skuId = Long.MAX_VALUE - dto.getSkuId();
//						DeliveryContext.getSkuId().putIfAbsent(skuId, dto.getSkuId());
//						dto.setSkuId(skuId);
//					}
//				}

 				map.put(key, dto);
			} else {// 合并相同库位同等级的商品的数量
                if (value.getDoNeedQtyIsEquale() && pickTask.getQty().compareTo(value.getQty().divide(BigDecimal.valueOf(value.getDoNum()), 0, BigDecimal.ROUND_DOWN)) != 0) {
                    value.setDoNeedQtyIsEquale(Boolean.FALSE);
                }
				value.getDoIdSet().add(pickTask.getDoHeaderId());
				value.setDoNum(value.getDoIdSet().size());
				value.setQty(value.getQty().add(pickTask.getQty()));
//				if (skuGroup != null) {
//					Integer groupQty = skuGroup.getGroupQty();
//					value.setQty(value.getQty().add(BigDecimal.valueOf(pickTask.getQty().intValue() / groupQty)));
//				}
				value.setQtyUnit(value.getQtyUnit().add(pickTask.getQtyUnit()));
				value.setPickedQty(value.getPickedQty().add(pickTask.getPickedQty()));
				value.setQtyPickedUnit(value.getQtyPickedUnit().add(pickTask.getQtyPickedUnit()));
				value.setSortInfo(value.getSortInfo() + "  ["+pickTask.getDoHeader().getSortGridNo()+"]*"+pickTask.getQty());
				if(value.getLotId() == null || !value.getLotId().equals(pickTask.getLotId())) {
					//相同批次不打合并标记
					value.setIsGrouped(Boolean.TRUE);// 设置合并标识，为true
				}
			}
		}
		List<PickTaskDto> detailDtos = new ArrayList<PickTaskDto>();
		List<String> keyList = new ArrayList<String>(map.keySet());
		// 按照任务异常状态、拣货顺序、库位、商品、货品等级排序
		Collections.sort(keyList);
		for (String key : keyList) {
			detailDtos.add(map.get(key));
		}
		return detailDtos;
	}

	/**
	 * 合并且排序拣货任务，并进行拣货路径优化(wms打印、客户端打印，rf拣货有用到)
	 * 
	 * @param pickTasks
	 * @param lastPickedInfo 
	 * @return
	 */
    public List<PickTaskDto> setPickTaskDtosGroupedOpt(List<PickTask> pickTasks, String[] lastPickedInfo){
        List<PickTaskDto> detailDtos = sortPickTaskCommonly(pickTasks);
        detailDtos = optimizePickLoc(detailDtos, lastPickedInfo);
        return detailDtos;
    }

    /**
	 * 取消并删除拣货任务
	 */
    @Override
    @Transactional
    public void cancelAndRemovePktTask(PickTask pickTask) {
        pickTask.setStatus(DoStatus.CANCELED.getValue());
        this.pickTaskDAO.remove(pickTask);
    }

    /**
	 * 根据发货单Id清空拣货任务和波次，拣货单头的关联，清空容器信息
	 */
    @Override
    @Transactional
    public void unrelate(Long doHeaderId) {
        this.pickTaskDAO.unrelate(doHeaderId);
    }

    /**
	 * 创建拣货任务(人工分配)
	 */
    @Override
    @Transactional
	public void createPickTask(DoAllocateDetail alcDetail, StockBatchLocLpn stockBatchLocLpn, BigDecimal allocateQty, BigDecimal allocateQtyUnit, Long packDetailId, Long fmStockId, Long allocatingId) {
		PickTask task = new PickTask();
        task.setLocId(stockBatchLocLpn.getLocId());
        task.setLotId(stockBatchLocLpn.getLotId());
        task.setLpnNo(stockBatchLocLpn.getLpnNo());

		createPickTask(alcDetail, task, allocateQty, allocateQtyUnit, packDetailId, fmStockId, allocatingId);
	}

    /**
	 * 创建拣货任务(分配)
	 */
    @Override
    @Transactional
	public void createPickTask(DoAllocateDetail alcDetail, Stock2AllocateDTO allocateStock, BigDecimal allocateQty, BigDecimal allocateQtyUnit, Long packDetailId, Long fmStockId, Long allocatingId) {
		PickTask task = new PickTask();
        task.setLocId(allocateStock.getLocId());
        task.setLotId(allocateStock.getLotId());
        task.setLpnNo(allocateStock.getLpnNo());

		createPickTask(alcDetail, task, allocateQty, allocateQtyUnit, packDetailId, fmStockId, allocatingId);
	}
	/**
	 * 创建拣货任务(分配)
	 */
	@Override
	@Transactional
	public void createPickTask(DoAllocateDetail alcDetail, Stock4CombiDoAllocateDTO allocateStock, BigDecimal allocateQty, BigDecimal allocateQtyUnit, Long packDetailId, Long fmStockId, Long allocatingId){
		PickTask task = new PickTask();
		task.setLocId(allocateStock.getLocId());
		task.setLotId(allocateStock.getLotId());
		task.setLpnNo(allocateStock.getLpnNo());
		createPickTask(alcDetail, task, allocateQty, allocateQtyUnit, packDetailId, fmStockId, allocatingId);
	}


	/**
	 * 创建拣货任务
	 * 
	 * @param alcDetail
	 * @param task
	 * @param allocateQty
	 * @param fmStockId
	 * @param allocatingId
	 */
	private void createPickTask(DoAllocateDetail alcDetail, PickTask task, BigDecimal allocateQty, BigDecimal allocateQtyUnit, Long packDetailId, Long fmStockId, Long allocatingId) {
		Location location = locationService.get(task.getLocId());
		Partition partition = partitionService.get(location.getPartitionId());
		if (partition == null || !alcDetail.getWarehouseId().equals(partition.getWarehouseId())) {
			throw new MasterException(MasterException.LOCATION_PARTITION_ERROR, location.getLocCode());
		}
		task.setToLpnNo(task.getLpnNo());
        task.setSkuId(alcDetail.getSkuId());
        task.setToLocId(task.getLocId());
        task.setQty(allocateQty);
		task.setQtyUnit(allocateQtyUnit);
        task.setPackId(alcDetail.getPackageId());
        task.setPackDetailId(packDetailId);
        task.setDoHeaderId(alcDetail.getDoHeaderId());
        task.setDoDetailId(alcDetail.getId());
        task.setStatus(DoStatus.ALLALLOCATED.getValue());
        task.setFmStockId(fmStockId);
        task.setAllocatingId(allocatingId);

        pickTaskDAO.save(task);
    }

    /**
	 * 根据do头id发货
	 */
    @Override
    @Transactional
    public void deliveryPktTaskByDoHeader(Long id, String updateUser) {
        pickTaskDAO.deliveryByDoHeader(id, updateUser);
    }

    @Override
    public List<Long> findRegionId(List<Long> doIdList){
        return pickTaskDAO.findRegionId(doIdList);
    }

    /**
	 * 根据任务状态和单号获取拣货任务的sku 先将单号当拣货单号查，有结果则返回 否则按照波次号查
	 */
    @Override
    public List<Long> getPktTaskSkus(String status, String docNo) {
        List<Long> skus = pickTaskDAO.getPickTaskSkusByPktNo(status, docNo);
        if (ListUtil.isNullOrEmpty(skus)) {
        	skus = pickTaskDAO.getPickTaskSkusByWaveNo(status, docNo);
        }
        return skus;
    }

    /**
	 * 分页查询
	 */
    @Override
    public DataPage<PickTask> query(PickTaskFilter filter, int startIndex, int pageSize) {
        return pickTaskDAO.findRangeByFilter(filter, startIndex, pageSize);
    }

    /**
	 * 关联拣货任务和波次
	 */
	@Override
	public void relate(Long waveHeaderId, Long pktHeaderId, List<Long> doIdList, Long regionId, boolean isEaTask) {
		pickTaskDAO.relate(waveHeaderId, pktHeaderId, doIdList, regionId, isEaTask);
	}

	/**
	 * 查询指定波次下已发布的拣货任务，用以绑定波次分页
	 */
	@Override
	public DataPage<PickTask> findPickTask4BindList(Long waveHeaderId, PktStatus status, int startIndex, int pageSize) {
	    return pickTaskDAO.findPickTask4BindList(waveHeaderId, status, startIndex, pageSize);
	}
	
	/**
	 * 根据波次id，获取已绑定容器的相关拣货任务
	 */
    @Override
    public List<PickTask> findBindPickTasksByWave(Long waveHeaderId) {
        return pickTaskDAO.findBindPickTasksByWave(waveHeaderId);
    }
    
    /**
	 * 根据容器号，获取已绑定该容器的相关拣货任务
	 */
    @Override
    public List<PickTask> findBindPickTasksByContainerNo(String containerNo,Long waveId) {
        return pickTaskDAO.findBindPickTasksByContainerNo(containerNo,waveId);
    }
	
    @Override
    public Map<Long, BigDecimal> getPickTaskSum(List<Long> waveIds, List<String> statusList, List<String> stockStatusList) {
        return pickTaskDAO.getPickTaskSum(waveIds, statusList, stockStatusList);
    }

    @Override
    public Map<Long, BigDecimal> getPktPickTaskSum(Long waveId, List<String> statusList, List<String> stockStatusList) {
        return pickTaskDAO.getPktPickTaskSum(waveId, statusList, stockStatusList);
    }

    @Override
    public Map<Long, BigDecimal> getPktPickTaskNumMap(Long waveId) {
        return pickTaskDAO.getPktPickTaskNumMap(waveId);
    }
    
    /**
	 * 查询拣货单下拣货、未拣货、缺货units数量
	 *
	 * @return
	 */
    @Override
    public List<Object> getPktPickTaskSumByPktHId(List<Long> pktHeaderIds) {
        return pickTaskDAO.getPktPickTaskSumByPktHId(pktHeaderIds);
    }
    
    /**
	 * 根据波次和容器查询拣货任务明细（合并同一商品）
	 * 
	 * @param containerNo
	 * @param waveId
	 * @return
	 */
    @Override
    public List<PickTaskDto> findPickTasksByWaveContainer(String containerNo, Long waveId) {
        List<Object> objList = pickTaskDAO.findPickTasksByWaveContainer(containerNo, waveId);
        if (objList.isEmpty()) {
            return Collections.emptyList();
        }

        List<PickTaskDto> dtoList = new ArrayList<PickTaskDto>();
        for (Object obj : objList) {
            int index = 0;
            Object[] o = (Object[]) obj;
            PickTaskDto dto = new PickTaskDto();
            dto.setProductCode((String) o[index++]);
            dto.setEan13((String) o[index++]);
            dto.setProductName((String) o[index++]);
            dto.setPickedQty(((BigDecimal) o[index++]));
            dtoList.add(dto);
        }
        return dtoList;
    }
    
    private List<PickTaskDto> optimizePickLoc(List<PickTaskDto> pickTaskDtos, String[] lastPickedInfo) {
    	List<PickTaskDto> returnDtos = new ArrayList<PickTaskDto>();
    	Map<Long, PickTaskDto> pickTaskDtoMap = new HashMap<Long, PickTaskDto>();
    	for (PickTaskDto pickTaskDto : pickTaskDtos) {
    		pickTaskDtoMap.put(pickTaskDto.getId(), pickTaskDto);
    	}
    	Map<String, List<PickLocOptDTO>> srcPathMap = makeSrcPathMap(pickTaskDtos);
		
		// 优化路径
		Long warehouseId = ParamUtil.getCurrentWarehouseId();
		Map<String, List<PickLocOptDTO>> optPath;
		try {
			if (lastPickedInfo != null && lastPickedInfo.length == 2 && StringUtil.isNotEmpty(lastPickedInfo[0])
					&& StringUtil.isNotEmpty(lastPickedInfo[1])) {
				lastPickedInfo[0] = getPkUnitCode(lastPickedInfo[0]);
			}
			//optPath = PlusClientUtil.getService().optimizePickLoc(warehouseId, srcPathMap, lastPickedInfo);
			optPath = new HashMap<String,List<PickLocOptDTO>>();
		} catch (Exception e) {
			optPath =  srcPathMap;
		}
		
		List<String> keyList = new ArrayList<String>(srcPathMap.keySet());
		// 按库区排序
    	Collections.sort(keyList);
        for(String key : keyList){
        	List<PickLocOptDTO> value = optPath.get(key); //库区的拣货任务；
        	if (ListUtil.isNullOrEmpty(value)) {
        		value = srcPathMap.get(key);
        	}
        	
        	for (PickLocOptDTO pickLocOptDTO : value) {  //按库区拼装
        		returnDtos.add(pickTaskDtoMap.get(pickLocOptDTO.getKey()));
        	}
        }
    	return returnDtos;
    }

	private String getPkUnitCode(String locCode) {
		String locCodeUnit = "";
		try {
			String[] locCodeSplit = locCode.split("-");
			if (locCodeSplit.length > 0) {
				if (locCodeSplit.length >= 2) {
					locCodeUnit = locCodeSplit[0] + "-" + locCodeSplit[1];
				} else {
					locCodeUnit = locCodeSplit[0];
				}
			}
		} catch (Exception e) {
			return "";
		}
		return locCodeUnit;
	}

	/**
	 * 把pickTaskDtos中的拣货任务按照库区分组，返回结果<库区-库区的拣货任务>
	 * @param pickTaskDtos
	 * @return
	 */
	private Map<String, List<PickLocOptDTO>> makeSrcPathMap(List<PickTaskDto> pickTaskDtos) {
		Map<String, List<PickLocOptDTO>> srcPath = new HashMap<String, List<PickLocOptDTO>>();
		for (PickTaskDto pickTaskDto : pickTaskDtos) {
			Location location = locationService.get(pickTaskDto.getLocId());
			String locCode = location.getLocCode();
			String unitCode = getPkUnitCode(locCode);

			String partitionCode = locationService.findPhysicalParByLocId(pickTaskDto.getLocId());
			if (StringUtil.isEmpty(partitionCode)) {
				throw new DeliveryException(DeliveryException.PICK_NO_PHYSICAL_PARTITION, locCode);
			}

			PickLocOptDTO pickLocOptDTO = new PickLocOptDTO();
			pickLocOptDTO.setUnitCode(unitCode);
			pickLocOptDTO.setLocCode(locCode);
			pickLocOptDTO.setKey(pickTaskDto.getId());
			List<PickLocOptDTO> pickLocOptDTOs = srcPath.get(partitionCode);
			if (ListUtil.isNullOrEmpty(pickLocOptDTOs)) {
				pickLocOptDTOs = new ArrayList<PickLocOptDTO>();
			}
			pickLocOptDTOs.add(pickLocOptDTO);
			srcPath.put(partitionCode, pickLocOptDTOs);
		}
		return srcPath;
	}

	@Override
	public BigDecimal getTotalNeedPickQty(List<PickTask> pickTasks) {
    	BigDecimal qty = BigDecimal.ZERO;
    	for (PickTask pickTask : pickTasks) {
    		qty = qty.add(pickTask.getQty());
    	}
    	return qty;
    }

    @Override
    public List<Partition> queryPartitionByPktHId(Long pktHeaderId) {
        return pickTaskDAO.queryPartitionByPktHId(pktHeaderId);
    }

	@Override
	public List<Partition> queryPartitionByWaveIdConNo(Long waveHeaderId,String containerNo) {
		return pickTaskDAO.queryPartitionByWaveIdConNo(waveHeaderId,containerNo);
	}

	@Override
	public String queryPktHNosByWaveIdConNo(Long waveHeaderId,String containerNo) {
		return pickTaskDAO.queryPktHNosByWaveIdConNo(waveHeaderId,containerNo);
	}

    @Override
    @Transactional
    public void updatePickTaskInfo(Long waveHeaderId, String updateBy){
    	pickTaskDAO.updatePickTaskInfo(waveHeaderId, updateBy);
    }
    
	/**
	 * 根据do单头id和do单明细id获取拣货任务,根据批次时间排序
	 */
	@Override
	public List<PickTask> getPickTasksIOrderByBatch(Long doId, Long doDetailId) {
		return pickTaskDAO.getPickTasksIOrderByBatch(doId, doDetailId);
	}

	@Override
	public boolean existUnitPickTask(Long regionId, List<Long> doIdList, boolean isEaTask) {
		return pickTaskDAO.existUnitPickTask(regionId, doIdList, isEaTask);
	}

	@Override
	public boolean existUnitPickTask(List<Long> regionIdList, List<Long> doIdList, boolean isEaTask) {
		return pickTaskDAO.existUnitPickTask(regionIdList, doIdList, isEaTask);
	}

	@Override
	public List<PickTask> findByPktId(Long pktHeaderId) {
		return pickTaskDAO.findByPktId(pktHeaderId);
	}
	
	@Override
	public List<String> findLotNoByDoDetail(Long detailId) {
		return pickTaskDAO.findLotNoByDoDetail(detailId);
	}
	
	/**
	 * doDetailId：[qtyPcs, qtyUnit]
	 * @param doId
	 * @return
	 */
	@Override
	public Map<Long, ImmutablePair<BigDecimal, BigDecimal>> countTask(Long doId) {
		return pickTaskDAO.countTask(doId);
	}
	
	@Override
	public List<Object[]> findCompletedTaskByWaveNo(String docNum) {
		return pickTaskDAO.findCompletedTaskByWaveNo( docNum);
	}
	
	@Override
	public List<Object[]> findCompletedTaskByPktNo(String docNum) {
		return pickTaskDAO.findCompletedTaskByPktNo(docNum);
	}

	@Override
	public List<Integer> countQtyByDoc4Group(Long pkHeaderId, Long locId, Long skuId) {
		return pickTaskDAO.countQtyByDoc4Group(pkHeaderId,locId,skuId);
	}

    @Override
    public List<PickTask> findPickTaskByDoIdAndSkuId(Long doId, Long skuId, String lotatt05, Integer pkType) {
        return pickTaskDAO.findPickTaskByDoIdAndSkuId(doId, skuId, lotatt05, pkType);
    }

	@Override
	public List<PickTask> findPickTaskByDoId(Long doId) {
		return pickTaskDAO.findPickTaskByDoId(doId);
	}

	@Override
	public Map<String, Long> findStatusCountByWave(Long waveId) {
		return pickTaskDAO.findStatusCountByWave(waveId);
	}
	
	@Override
	public boolean existTaskByDo(List<Long> genDoIds, Long regionId, boolean isEa) {
		return pickTaskDAO.existTaskByDo(genDoIds, regionId, isEa);
	}
	
	@Override
	public List<PickTaskDto> getTasksPreContainer(Long waveId) {
		return pickTaskDAO.getForPreContainer(waveId);
	}

	@Override
	public List<PickTask> findByContainer(Long skuId, Long locId, String containerNo, String taskStatus, String docNo) {
		return pickTaskDAO.findByContainer(skuId,locId,containerNo,taskStatus,docNo);
	}

	@Override
	public BigDecimal findPickTaskByDoIdAndSkuIdForQty(Long doId, Long skuId, String lotatt05) {
		return pickTaskDAO.findPickTaskByDoIdAndSkuIdForQty(doId, skuId, lotatt05);
	}

	@Override
	@Transactional
	public Long splitPickTask(Long taskId, BigDecimal newQty) {
		PickTask pickTask = pickTaskDAO.get(taskId);
		if (pickTask != null) {
			////不支持整箱的算容器；
			if (pickTask.getQty().compareTo(pickTask.getQtyUnit()) != 0) {
				throw new DeliveryException(DeliveryException.PACKAGE_TYPE_ERROR);
			}
			
			pickTask.setQty(pickTask.getQty().subtract(newQty));
			pickTask.setQtyUnit(pickTask.getQty());
			pickTaskDAO.update(pickTask);
			
			Long newStockId = splitStock(pickTask, newQty);
			
			return createPickTask(pickTask, newQty, newQty, newStockId, WmsUtil.getOperateBy()).getId();
		}
		return null;
	}
	
	private Long splitStock(PickTask pickTask, BigDecimal newQty) {
		Long fromStockId = pickTask.getFmStockId();
		
		StockDTO stockDTO = new StockDTO();
		stockDTO.setFmStockId(fromStockId);
		stockDTO.setPlanQty(pickTask.getQty());
		stockDTO.setPlanQtyUnit(pickTask.getQtyUnit());
		stockDTO.setActualQty(newQty);
		stockDTO.setActualQtyUnit(newQty);
		stockDTO.setFmLocId(pickTask.getLocId());
		stockDTO.setLotId(pickTask.getLotId());
		stockDTO.setLpnNo(pickTask.getLpnNo());
		stockDTO.setSkuId(pickTask.getSkuId());
		pickSplitOperator.setStockDto(stockDTO);
		Map<String, Long> map = stockService.operateStock(pickSplitOperator);
		return map.get(StockType.STOCK_ALLOC.getValue());
	}
}