package com.daxia.wms.delivery.load.job;

import java.util.List;
import java.util.concurrent.CountDownLatch;

import org.jboss.seam.Component;
import org.jboss.seam.contexts.Lifecycle;

import com.daxia.wms.delivery.recheck.entity.CartonHeader;

@lombok.extern.slf4j.Slf4j
public class AutoLoadAndDeliverTask extends Thread {

    private List<CartonHeader> cartonList; // 待交接箱

    private CountDownLatch threadSignal;

    public AutoLoadAndDeliverTask(int i, List<CartonHeader> cartonList, CountDownLatch threadSignal) {
        super("AutoLoadAndDeliverTask" + i);
        this.cartonList = cartonList;
        this.threadSignal = threadSignal;
    }

    private void setup() {
        Lifecycle.beginCall();
    }

    @Override
    public void run() {
        try {
            setup();

            AutoLoadAndDeliverExecutor executor = ((AutoLoadAndDeliverExecutor) Component.getInstance(AutoLoadAndDeliverExecutor.class));
            executor.doLoadAndDeliver(cartonList);
        } finally {
            threadSignal.countDown();
            cleanup();
        }
    }

    private void cleanup() {
        Lifecycle.endCall();
    }
}