package com.daxia.wms.delivery.print.dto;

import java.math.BigDecimal;

/**
 * 反拣单表单打印值对象
 */
@lombok.extern.slf4j.Slf4j
public class RepickPrint {
    private String locCode;
    private String productCode;
    private String barCode;
    private String productName;
    private String doNo;
    private BigDecimal qty;

    private Long skuId;
    private Long fmLocId;
    private Long toLocId;
    private String toLpnNo;

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Long getFmLocId() {
        return fmLocId;
    }

    public void setFmLocId(Long fmLocId) {
        this.fmLocId = fmLocId;
    }

    public Long getToLocId() {
        return toLocId;
    }

    public void setToLocId(Long toLocId) {
        this.toLocId = toLocId;
    }

    public String getLocCode() {
        return locCode;
    }

    public void setLocCode(String locCode) {
        this.locCode = locCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public void setToLpnNo(String toLpnNo) {
        this.toLpnNo = toLpnNo;
    }

    public String getToLpnNo() {
        return toLpnNo;
    }
}
