package com.daxia.wms.delivery.wave.dto;

import java.math.BigDecimal;

@lombok.extern.slf4j.Slf4j
public class SingleWaveInfo4PrintDTO {
	private Long waveId;
	private Long doId;
	private Long skuId;
	private Long locId;
	private Long tskPickId;
	private Long cartonId;
	private Long carrierId;
	private Long stationId;
	private Long singleRuleId;
	private String waveNo;
	private String planShipTime;	
	private String productCode;
	private String productName;
	private String productBarcode;
	private String pickLocCode;
	private String stationCode;
	private String carrierName;
	private BigDecimal expectedQty;
	private BigDecimal salesQty;
	public Long getWaveId() {
		return waveId;
	}
	public void setWaveId(Long waveId) {
		this.waveId = waveId;
	}
	public Long getDoId() {
		return doId;
	}
	public void setDoId(Long doId) {
		this.doId = doId;
	}
	public Long getSkuId() {
		return skuId;
	}
	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}
	public Long getLocId() {
		return locId;
	}
	public void setLocId(Long locId) {
		this.locId = locId;
	}
	public Long getTskPickId() {
		return tskPickId;
	}
	public void setTskPickId(Long tskPickId) {
		this.tskPickId = tskPickId;
	}
	public Long getCartonId() {
		return cartonId;
	}
	public void setCartonId(Long cartonId) {
		this.cartonId = cartonId;
	}
	public Long getCarrierId() {
		return carrierId;
	}
	public void setCarrierId(Long carrierId) {
		this.carrierId = carrierId;
	}
	public Long getStationId() {
		return stationId;
	}
	public void setStationId(Long stationId) {
		this.stationId = stationId;
	}
	
	public Long getSingleRuleId() {
		return singleRuleId;
	}
	public void setSingleRuleId(Long singleRuleId) {
		this.singleRuleId = singleRuleId;
	}
	public String getPlanShipTime() {
		return planShipTime;
	}
	public void setPlanShipTime(String planShipTime) {
		this.planShipTime = planShipTime;
	}
	public String getProductCode() {
		return productCode;
	}
	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}
	public String getProductName() {
		return productName;
	}
	public void setProductName(String productName) {
		this.productName = productName;
	}
	public String getProductBarcode() {
		return productBarcode;
	}
	public void setProductBarcode(String productBarcode) {
		this.productBarcode = productBarcode;
	}
	public String getPickLocCode() {
		return pickLocCode;
	}
	public void setPickLocCode(String pickLocCode) {
		this.pickLocCode = pickLocCode;
	}
	public String getStationCode() {
		return stationCode;
	}
	public void setStationCode(String stationCode) {
		this.stationCode = stationCode;
	}
	public String getCarrierName() {
		return carrierName;
	}
	public void setCarrierName(String carrierName) {
		this.carrierName = carrierName;
	}
	public BigDecimal getExpectedQty() {
		return expectedQty;
	}
	public void setExpectedQty(BigDecimal expectedQty) {
		this.expectedQty = expectedQty;
	}
	public BigDecimal getSalesQty() {
		return salesQty;
	}
	public void setSalesQty(BigDecimal salesQty) {
		this.salesQty = salesQty;
	}
	public String getWaveNo() {
		return waveNo;
	}
	public void setWaveNo(String waveNo) {
		this.waveNo = waveNo;
	}
}
