package com.daxia.wms.delivery.print.dto;

@lombok.extern.slf4j.Slf4j
public class RowDTO {
	
	private String row1;
	
	private String row2;
	
	private String unitPrice;
	
	private String qty;
	
	private String totalPrice;
	
	private String topOffset;

	/**
	 * 分割线
	 */
    private String content;

    public RowDTO() {
    	
    }
    
    public RowDTO(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

	public String getRow1() {
		return row1;
	}

	public void setRow1(String row1) {
		this.row1 = row1;
	}

	public String getRow2() {
		return row2;
	}

	public void setRow2(String row2) {
		this.row2 = row2;
	}

	public String getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(String unitPrice) {
		this.unitPrice = unitPrice;
	}

	public String getQty() {
		return qty;
	}

	public void setQty(String qty) {
		this.qty = qty;
	}

	public String getTotalPrice() {
		return totalPrice;
	}

	public void setTotalPrice(String totalPrice) {
		this.totalPrice = totalPrice;
	}

	public String getTopOffset() {
		return topOffset;
	}

	public void setTopOffset(String topOffset) {
		this.topOffset = topOffset;
	}
}
