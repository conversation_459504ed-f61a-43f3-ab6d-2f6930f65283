package com.daxia.wms.delivery.task.repick.service;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.task.repick.entity.ReversePickHeader;
import com.daxia.wms.delivery.task.repick.filter.ReversePickHeaderFilter;

/**
 * 反拣单头信息Service接口
 */
public interface ReversePickHeaderService {
	/**
	 * 查询反拣单头信息分页信息
	 * @param reversePickHeaderFilter 查询过滤器
	 * @param startIndex 起始页码
	 * @param pageSize 分页单位
	 * @return 反拣单头信息分页信息
	 */
	public DataPage<ReversePickHeader> query(ReversePickHeaderFilter reversePickHeaderFilter, int startIndex,
            int pageSize);
	
	/**
	 * 根据反拣任务起始库位所属的库区新增反拣单头信息
	 * @param partitionId 库区Id
	 * @throws DeliveryException 新增反拣单头信息出错时抛出的异常
	 */
	public void saveReversePickHeader(Long partitionId) throws DeliveryException;

	/**
	 * 
	 * <pre>
	 * 根据ID获取反拣单头
	 * </pre>
	 *
	 * @param reversePickHeaderId
	 * @return
	 */
    public ReversePickHeader get(Long reversePickHeaderId);

    /**
     * 未完待续的反拣任务(INITIALIZED,RELEASED,SUSPENDED)的个数
     */
    public Long getNeedDoingTaskCount(Long docId);
}
