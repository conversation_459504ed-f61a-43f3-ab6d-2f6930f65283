package com.daxia.wms.delivery.print.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.load.service.CrossDockPackingService;
import com.daxia.wms.delivery.print.dto.CrossDockCartonPrintDTO;
import com.daxia.wms.delivery.print.service.PrintCrossDockCartonService;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.print.PrintConstants.PrintType;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.print.service.impl.AbstractLodopPrint;
@Name("printCrossDockCartonService")
@lombok.extern.slf4j.Slf4j
public class PrintCrossDockCartonServiceImpl extends AbstractLodopPrint<CrossDockCartonPrintDTO> implements PrintCrossDockCartonService {

	 @In
	 private CrossDockPackingService crossDockPackingService;
	 @In
	 private WarehouseService warehouseService;
	
	/**
	 * 根据越库装箱实体ID打印箱标签
	 */
	@Override
	@Transactional
	public PrintData getCrossDockCartonPrintDTO(List<Long> ids) {
		this.paramMap.put(this.PRINT_NOTES, "crossdock");
		this.paramMap.put(this.PRINT_TYPE, PrintType.CARTON_LABEL);
		PrintData printData = this.buildPrintDtos(ids);
        return printData;
	}
	
	@Override
	protected List<CrossDockCartonPrintDTO> getSourceList(List<Long> ids) {
		List<CrossDockCartonPrintDTO> crossDockCartonPrintDTOs = crossDockPackingService.getCrossDockCartonPrintDTOById(ids);
		 if (ListUtil.isNullOrEmpty(crossDockCartonPrintDTOs)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
		return crossDockCartonPrintDTOs;
	}

	@Override
	protected List<PrintReportDto> buildPrintDataDto(CrossDockCartonPrintDTO cdCartonPrintDTO) {
		List<PrintReportDto> cartonPrintDTOs = new ArrayList<PrintReportDto>();
		String cartonNos = cdCartonPrintDTO.getCartonNos();
		if(StringUtil.isEmpty(cartonNos)){
			throw new DeliveryException(DeliveryException.CARTON_NOEXIST);
		}
		if(cartonNos.contains(",")){
			String[] cartonNoList = cartonNos.split(",");
			for(String cartonNo : cartonNoList){
				 buildPrintDTO(cartonPrintDTOs, cdCartonPrintDTO, cartonNo);
			}
		}else{
			buildPrintDTO(cartonPrintDTOs, cdCartonPrintDTO, cartonNos);
		}
		
		return cartonPrintDTOs;
	}

	/**
     * 构造打印数据DTO
     * @param cdCartonPrintDTO
     * @param carton
     */
	private void buildPrintDTO(List<PrintReportDto> cartonPrintDTOs, CrossDockCartonPrintDTO cdCartonPrintDTO, String cartonNo) {
		CrossDockCartonPrintDTO dto = new CrossDockCartonPrintDTO();
		dto.setCartonNo(cartonNo);
		//贵品标示
		dto.setValuableFlag(cdCartonPrintDTO.getValuableFlag());
		dto.setSkuQty(cdCartonPrintDTO.getSkuQty());
		dto.setUnitsQty(cdCartonPrintDTO.getUnitsQty());
		dto.setToWarehouse(combineFromToWhCode(cdCartonPrintDTO));
		//LPN数量
		dto.setLpnQty(cdCartonPrintDTO.getLpnQty());
		//dto.setLpnQty(BigDecimal.valueOf(2));
		dto.setLpnNo(cdCartonPrintDTO.getLpnNo());
		//多个LPN是否有相同的refNo1
		dto.setIsSingleRefNo1(cdCartonPrintDTO.getIsSingleRefNo1());
		dto.setRefNo1(cdCartonPrintDTO.getRefNo1());
		//打印日志
		dto.setDocId(cdCartonPrintDTO.getCdHeaderId());
		dto.setDocNo(cdCartonPrintDTO.getCdHeaderNo());
		dto.setRefNo(cartonNo);
		cartonPrintDTOs.add(dto);
	}
	
	
	private String combineFromToWhCode(CrossDockCartonPrintDTO cdCartonPrintDTO) {
	 	Warehouse fromWarehouse = warehouseService.getLocalWarehouse();
        String fmWhCode = fromWarehouse == null ? "" : fromWarehouse.getWarehouseCode();
        fmWhCode = StringUtil.trim(fmWhCode);
        if (3 < StringUtil.length(fmWhCode)){
        	fmWhCode = StringUtil.substring(fmWhCode, 0, 3);
        }
        Warehouse toWarehouse = warehouseService.getWarehouse(cdCartonPrintDTO.getToWhId());
    	String toWhCode = toWarehouse == null ? "" : toWarehouse.getWarehouseCode();
    	toWhCode = StringUtil.trim(toWhCode);
        if (3 < StringUtil.length(toWhCode)){
        	toWhCode = StringUtil.substring(toWhCode, 0, 3);
        }
    	return fmWhCode + "→" + toWhCode;
    }
}
