package com.daxia.wms.delivery.task.replenish.service.impl;


import com.daxia.framework.common.log.LogUtil;
import com.daxia.framework.common.util.Arith;
import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Constants;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.deliveryorder.dao.DoDetailDAO;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.task.replenish.dto.ReplGenterateDTO;
import com.daxia.wms.delivery.task.replenish.dto.ReplenishSkuDTO;
import com.daxia.wms.delivery.task.replenish.service.ReplHeaderService;
import com.daxia.wms.delivery.task.replenish.service.ReplTaskService;
import com.daxia.wms.master.dao.SkuPickLocAssnDAO;
import com.daxia.wms.master.dto.SkuDTO;
import com.daxia.wms.master.entity.*;
import com.daxia.wms.master.helper.SysConfigHelper;
import com.daxia.wms.master.service.*;
import com.daxia.wms.stock.locRecommend.dto.LocRecommendResultDTO;
import com.daxia.wms.stock.locRecommend.service.LocationRecommend;
import com.daxia.wms.stock.stock.dao.StockQueryDao;
import com.daxia.wms.stock.stock.dto.PutawayRecommendDTO;
import com.daxia.wms.stock.stock.dto.Stock2AllocateDTO;
import com.daxia.wms.stock.stock.entity.StockBatchLocLpn;
import com.daxia.wms.stock.stock.service.StockService;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

/**
 * 根据当前doDetail的缺货数量生成补货任务
 */
@Name("com.daxia.wms.delivery.jpReplenishTaskGenerator")
@lombok.extern.slf4j.Slf4j
public class JPReplenishTaskGenerator extends AbstractReplenishTaskGenerator {
    @In
    private DoDetailDAO doDetailDAO;

    @In
    private SkuCache skuCache;

    @In
    private RotationService rotationService;

    @In
    private StockService stockService;

    @In
    private SkuPickLocAssnDAO skuPickLocAssnDAO;

    @In
    private DeliveryOrderService deliveryOrderService;

    @In
    private LocationRecommend locationRecommend;

    @In
    private SkuPickLocAssnService skuPickLocAssnService;

    @In
    private PartitionRuleService partitionRuleService;

    @In
    private PackageInfoDetailService packageInfoDetailService;
    @In
    private StockQueryDao stockQueryDao;

    @In
    private ReplHeaderService replHeaderService;


    /**
     * 创建补货任务
     *
     * @param skus
     * @return 需要补货，但因库位空间不够所以补货数量为零的sku
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    @Transactional
    public Map<String, Set<String>> createTask(List<ReplenishSkuDTO> skus, String doType, Boolean isAuto) throws Exception {
        log.debug("CreateReplenishTask, doType: {}", doType);
        //保存需要补货，但因库位空间不够所以补货数量为零的sku
        Set<String> zeroReplQtySkus = new HashSet<String>();
        //没有默认拣货位的SKU
        Set<String> noPickLocSkus = new HashSet<String>();
        //没有分配规则的SKU
        Set<String> noAllocRuleSkus = new HashSet<String>();
        if (CollectionUtils.isEmpty(skus)) {
            return Collections.EMPTY_MAP;
        }
        List<ReplenishSkuDTO> pcsSkus = new ArrayList<ReplenishSkuDTO>();
        //将散件、整件需求数量拆分
        splitPcsAndUnit(skus, pcsSkus);
    
        //根据skuId和批属性合并补货数量
        skus = Lists.newArrayList();
        skus.addAll(mergeBySkuAndLotatts(pcsSkus, Boolean.FALSE));
        
        //hasErrorSku表示是否存在无法补货的sku。当存在这样的sku时，
        //将对应的doDetail的需补货数量清零，并修改doHeader的状态，使其重新满足自动分配的条件，
        //再次自动分配时因无库存，会冻结doHeadergv
        boolean hasErrorSku = false;
        List<Long> skuIds = new ArrayList<Long>();
        for (ReplenishSkuDTO replSku : skus) {
            SkuDTO sku = skuCache.getSku(replSku.getSkuId());
            // 规则不匹配
            RotationHead head = rotationService.get(sku.getReplenishmentRuleId());
            if (head == null || !"1".equals(head.getActiveFlag())) {
                noAllocRuleSkus.add(sku.getProductCode());
                continue;
            }
            //是否全仓商家库存共享。
            Boolean canMerchantShareStock = SysConfigHelper.getSwitchDefalutClosed("allocate.delivery.isMerchantShareStock");
            String lotatt06 = null;
            if (!canMerchantShareStock) {
                lotatt06 = replSku.getLotatt06();
            }
    
            //先查找目标补货库位
            //箱拣货位需要补货还是散件拣货位需要补货
            SkuPickLocAssn pickLoc = findReplLoc(replSku, sku, replSku.getReplQtyUnit().compareTo(BigDecimal.ZERO) > 0 ? Constants.PackageType.C : Constants.PackageType.B);
            
            if (pickLoc == null) {
                noPickLocSkus.add(sku.getProductCode());
                continue;
            }
            //得到可以补货的库存
            List<Stock2AllocateDTO> stockList = stockService.queryAvailableStockReplenish(head, replSku.getSkuId(), replSku.getShopId(), replSku.getLotatt04(), lotatt06, replSku.getLotatt08(),replSku.getLotatt05(), packageInfoDetailService
                    .findMainPackage(replSku.getSkuId()).getId(),replSku.getMinExp(),replSku.getMaxExp());

            //清空DoDetail补货数量，并记录需要补货的skuId
            if (CollectionUtils.isEmpty(stockList)) {
                deliveryOrderService.clearDoDetailReplNum(replSku.getSkuId(), replSku.getLotatt04(), lotatt06, replSku.getLotatt08());
                skuIds.add(replSku.getSkuId());
                hasErrorSku = true;
            }

            // 获取目标库位最大补货数量
            BigDecimal replQty = getReplenishQty(pickLoc, replSku);
            //创建补货任务
            if (replQty.compareTo(BigDecimal.ZERO) <= 0) {
                zeroReplQtySkus.add(sku.getProductCode());
            } else {
                this.createReplTask(new ReplGenterateDTO(stockList, replQty, sku, pickLoc.getLocId(), Constants.ReplType.JP.getValue(), doType, isAuto, 0, replSku.getReplQtyUnit().compareTo(BigDecimal.ZERO) > 0, replSku.getEarliestPlanShipTime()));
                doDetailDAO.getSession().flush();
                doDetailDAO.getSession().clear();
            }
        }

        if (hasErrorSku && Config.isDefaultFalse(Keys.Stock.repl_task_update_do_repl_status, Config.ConfigLevel.WAREHOUSE)) {
            deliveryOrderService.checkDoHeaderReplStatus(skuIds);
        }

        //创建补货任务单头
        replHeaderService.addReplTask(Constants.ReplType.JP.getValue(), doType, Boolean.FALSE);

        Map<String, Set<String>> resultMap = new HashMap<String, Set<String>>();
        resultMap.put("error.sku.noRsStock", zeroReplQtySkus);
        resultMap.put("error.repl.recommendLocIsNull", noPickLocSkus);
        resultMap.put("error.sku.noReplRule", noAllocRuleSkus);

        return resultMap;
    }

    // 查找目标补货库位
    // 构建一个默认拣货位实体（skuId,uplimit,locid）供补货使用
    private SkuPickLocAssn findReplLoc(ReplenishSkuDTO replSku, SkuDTO sku, Constants.PackageType pt) {
        String packageType = pt.getValue();
        if(StringUtils.isNotEmpty(replSku.getLotatt05())){
            String locType ="EA";
            Long locId = stockQueryDao.getStockBySkuAndBatch(sku.getId(),replSku.getLotatt06(),replSku.getLotatt04(),replSku.getLotatt05(),replSku.getLotatt08(),locType,packageType);
            if(null != locId){
                SkuPickLocAssn skuPickLocAssn = skuPickLocAssnDAO.getSkuDefaultPickLoc(sku.getId(), replSku.getReplRegionId(), packageType);
                if(null != skuPickLocAssn){
                    skuPickLocAssn.setLocId(locId);
                    return skuPickLocAssn;
                }
            }
        }

        if (SystemConfig.configIsClosed("delivery.repl.useLocationRecommend", ParamUtil.getCurrentWarehouseId())) {
            return skuPickLocAssnDAO.getSkuDefaultPickLoc(sku.getId(), replSku.getReplRegionId(), packageType);
        }

        PutawayRecommendDTO putawayRecommendDTO = new PutawayRecommendDTO();
        putawayRecommendDTO.setQty(replSku.getReplQty());
        putawayRecommendDTO.setSkuId(sku.getId());
        putawayRecommendDTO.setWeight(BigDecimal.valueOf(Arith.nullFilter(sku.getGrossweight())));
        putawayRecommendDTO.setVolume(BigDecimal.valueOf(Arith.nullFilter(sku.getVolume())));
        putawayRecommendDTO.setDocId(null);
        putawayRecommendDTO.setPackageType(packageType);

        LocRecommendResultDTO locRecommendResultDTO = locationRecommend.recommendForRepl(putawayRecommendDTO);

        if (locRecommendResultDTO.getLocId() == null) {
            return null;
        }

        SkuPickLocAssn skuPickLocAssn = skuPickLocAssnService.get(sku.getId(), locRecommendResultDTO.getLocId());
        if (skuPickLocAssn == null) {
            skuPickLocAssn = new SkuPickLocAssn();
            skuPickLocAssn.setLocId(locRecommendResultDTO.getLocId());
            skuPickLocAssn.setSkuId(sku.getId());
            skuPickLocAssn.setUplimit(skuPickLocAssnService.getReplLowUpLimit(sku.getId(), locRecommendResultDTO.getLocId()).get(1));
        }
        return skuPickLocAssn;
    }

    /**
     * 根据skuId和批属性合并补货数量
     *
     * @param skus
     * @return
     */
    private List<ReplenishSkuDTO> mergeBySkuAndLotatts(List<ReplenishSkuDTO> skus, Boolean isUnit) {
        HashMap<ReplenishSkuDTO, BigDecimal> results = new HashMap<ReplenishSkuDTO, BigDecimal>();
        HashMap<ReplenishSkuDTO, String> doNOs = new HashMap<ReplenishSkuDTO, String>();
        HashMap<ReplenishSkuDTO, Timestamp> earliestDoTimeMap = new HashMap<ReplenishSkuDTO, Timestamp>();
        HashMap<ReplenishSkuDTO, Date> earliestPlanShipTimeMap = new HashMap<ReplenishSkuDTO, Date>();
        // 整理数据,，整理后（ReplenishSkuDTO sku）结构如下：
        // 某货物（指定lot468的sku）———缺货订单（doNo1:doNo2...doNoN）———缺货总数量———缺货DO中最早导入WMS的时间——缺货DO的最早预计出库时间
        for (ReplenishSkuDTO sku : skus) {
            BigDecimal qty = results.get(sku);
            String doNo = doNOs.get(sku);
            Timestamp earliestDoTime = earliestDoTimeMap.get(sku);
            Date earliestPlanShipTime = earliestPlanShipTimeMap.get(sku);
            if (isUnit) {
                qty = qty == null ? sku.getReplQtyUnit() : qty.add(sku.getReplQtyUnit());
            } else {
                qty = qty == null ? sku.getReplQty() : qty.add(sku.getReplQty());
            }

            doNo = doNo == null ? sku.getDoNo() : doNo + ":" + sku.getDoNo();
            earliestDoTime = (Timestamp) (earliestDoTime == null ? sku.getEarliestDoTime() : getEarlierTime(earliestDoTime, sku.getEarliestDoTime()));
            earliestPlanShipTime = earliestPlanShipTime == null ? sku.getEarliestPlanShipTime() : getEarlierTime(earliestPlanShipTime, sku.getEarliestPlanShipTime());
            doNOs.put(sku, doNo);
            results.put(sku, qty);
            earliestDoTimeMap.put(sku, earliestDoTime);
            earliestPlanShipTimeMap.put(sku, earliestPlanShipTime);
        }
        for (Map.Entry<ReplenishSkuDTO, BigDecimal> entry : results.entrySet()) {
            ReplenishSkuDTO sku = entry.getKey();
            if (isUnit) {
                sku.setReplQtyUnit(entry.getValue());
            } else {
                sku.setReplQty(entry.getValue());
            }

            sku.setDoNo(doNOs.get(sku));
            sku.setEarliestDoTime(earliestDoTimeMap.get(sku));
            sku.setEarliestPlanShipTime(earliestPlanShipTimeMap.get(sku));
        }
        List<ReplenishSkuDTO> dtoList = new ArrayList<ReplenishSkuDTO>();
        for (ReplenishSkuDTO key : results.keySet()) {
            dtoList.add(key);
        }
        //按上述（ReplenishSkuDTO sku）的earliestPlanShipTime字段排序，届时根据这个顺序来创建补货任务
        Collections.sort(dtoList, new Comparator<ReplenishSkuDTO>() {
            @Override
            public int compare(ReplenishSkuDTO o1, ReplenishSkuDTO o2) {
                return compareTimestamp(o1.getEarliestPlanShipTime(), o2.getEarliestPlanShipTime());
            }
        });
        return dtoList;
    }

    private Date getEarlierTime(Date earliestDoTime, Date earliestDoTime2) {
        if (compareTimestamp(earliestDoTime, earliestDoTime2) < 0) {
            return earliestDoTime;
        } else {
            return earliestDoTime2;
        }
    }

    private int compareTimestamp(Date earliestDoTime, Date earliestDoTime2) {
        if (earliestDoTime == null) {
            return 1;
        }
        if (earliestDoTime2 == null) {
            return -1;
        }
        if (earliestDoTime.compareTo(earliestDoTime2) < 0) {
            return -1;
        } else if (earliestDoTime.compareTo(earliestDoTime2) == 0) {
            return 0;
        }
        else {
            return 1;
        }
    }

    /**
     * 将散件、整件需求数量拆分
     *  @param skus        需要补货的sku 集合
     * @param pcsSkus      散件补货集合
     */
    private void splitPcsAndUnit(List<ReplenishSkuDTO> skus, List<ReplenishSkuDTO> pcsSkus) {
        for (ReplenishSkuDTO sku : skus) {
            if (sku.getReplQty().compareTo(BigDecimal.ZERO) > 0) { //散件需求数
                ReplenishSkuDTO skuUint = ReplenishSkuDTO.getInstanceOf(sku);
                skuUint.setReplQtyUnit(BigDecimal.ZERO);
                pcsSkus.add(skuUint);
            }
        }
    }
}