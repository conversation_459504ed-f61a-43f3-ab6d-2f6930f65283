package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.MvelUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoType;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.entity.TempCarton;
import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;
import com.daxia.wms.delivery.recheck.service.TempCartonService;
import com.daxia.wms.master.entity.Carrier;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.util.ArrayList;
import java.util.List;

@Name("cartonNoGenerateDispatcher")
@lombok.extern.slf4j.Slf4j
public class CartonNoGenerateDispatcher {

	@In
	private CartonNoGenerateService jPCartonNoGenerateService;
	@In
	private CartonNoGenerateService caiNiaoCartonNoGenerateService;
	@In
	private CartonNoGenerateService defaultCartonNoGenerateService;
	@In
	private CartonNoGenerateService yTCartonNoGenerateService;
	@In
	private CartonNoGenerateService zenyCartonNoGenerateService;
	@In
	private CartonNoGenerateService zjsCartonNoGenerateService;
	@In
	private CartonNoGenerateService city100CartonNoGenerateService;
	@In
	private CartonNoGenerateService yundaCartonNoGenerateService;
	@In
	private CartonNoGenerateService youPaiCartonNoGenerateService;
    @In
    private CartonNoGenerateService stoCartonNoGenerateService;
	@In
	private CartonNoGenerateService bestCartonNoGenerateService;
	@In
	private CartonNoGenerateService postbImportCartonNoGenerateService;
	@In
	private TempCartonService tempCartonService;
	@In
	private CartonNoGenerateService imageCartonNoGenerateService;

	@In
	private CartonNoGenerateService tmsCartonNoGenerateServiceImpl;
	@In
	private CartonNoGenerateService sFCartonNoGenerateService;
	@In
	private CartonNoGenerateService jDCartonNoGenerateService;

	@In
	private CartonNoGenerateService fzgjCartonNoGenerateServiceImpl;
	/**

	 * 根据DO的配送商创建箱号
	 *
	 * @param doHeader
	 * @return
	 */
    public void genNewCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
		genNewCarton(doHeader,cartonHeader,true);
    }

	public void genNewCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader,boolean useTempcarton) {
		if (useTempcarton){
			//优先从tempCarton中取面单信息，若不存在，则下单
			TempCarton tempCarton = tempCartonService.getByDoId(doHeader.getId());
			if (tempCarton != null && Constants.YesNo.YES.getValue().equals(tempCarton.getSuccessFlag())) {
				cartonHeader.setCartonNo(tempCarton.getCartonNo());
				cartonHeader.setWayBill(tempCarton.getWayBill());
				cartonHeader.setTrackingNo(tempCarton.getTrackingNo());
				cartonHeader.setPrintData(tempCarton.getPrintData());
				//打印标记，面单前置打印时，生成的箱打印标记为1
				cartonHeader.setIsPrinted(tempCarton.getIsPrinted());
				//使用后删除tempCarton
				//tempCartonService.removeByDoId(tempCarton.getDoHeaderId());
			} else {
				getCartonNoGenerateServiceByDoHeader(doHeader).generatorCarton(doHeader, cartonHeader);
			}
		} else {
			getCartonNoGenerateServiceByDoHeader(doHeader).generatorCarton(doHeader, cartonHeader);
		}

	}

    private CartonNoGenerateService getCartonNoGenerateServiceByDoHeader(DeliveryOrderHeader doHeader) {
		String useDefaultNoScript = SystemConfig.getConfigValue(ConfigKeys.DELIVERY_CARTON_USE_DEFAULT_NO_SCRIPT, ParamUtil.getCurrentWarehouseId());
		if (StringUtil.isNotEmpty(useDefaultNoScript) && Boolean.TRUE.equals(MvelUtil.eval(useDefaultNoScript, ImmutableMap.of("doHeader", (Object) doHeader)))) {
			return defaultCartonNoGenerateService;
		}

		CartonNoGenerateService cartonNoGenerateService = defaultCartonNoGenerateService;
		if (null != doHeader && (DoType.SELL.getValue().equals(doHeader.getDoType()) || Constants.YesNo.YES.getValue().equals(doHeader.getWholesaleWaybillFlag()))) {
            Carrier carrier = doHeader.getCarrier();
			switch (carrier.getWaybillType()) {
			case KYE:
				cartonNoGenerateService = tmsCartonNoGenerateServiceImpl;
				break;
			case SF:
				cartonNoGenerateService = sFCartonNoGenerateService;
				break;
			case JD:
				cartonNoGenerateService = jDCartonNoGenerateService;
				break;
			case JP:
				cartonNoGenerateService = jPCartonNoGenerateService;
				break;
			case ZENY:
				cartonNoGenerateService = zenyCartonNoGenerateService;
				break;
			case CAINIAO:
				cartonNoGenerateService = caiNiaoCartonNoGenerateService;
				break;
//			case PDD:
//				cartonNoGenerateService = pddCartonNoGenerateService;
//				break;
			case ZJS:
				cartonNoGenerateService = zjsCartonNoGenerateService;
				break;
			case YTO:
				//圆通COD调用圆通下单接口
				if (doHeader.isCod() || isAllPrintByYto()) {
					cartonNoGenerateService = yTCartonNoGenerateService;
				} else {
					cartonNoGenerateService = caiNiaoCartonNoGenerateService;
				}
				break;
			case CITY100:
				cartonNoGenerateService = city100CartonNoGenerateService;
				break;
			case YOUPAI:
				cartonNoGenerateService = youPaiCartonNoGenerateService;
				break;
			case YUNDA:
				if (doHeader.isCod() && !getTaoBaoShopIds().contains(doHeader.getShopId())) {
					cartonNoGenerateService = yundaCartonNoGenerateService;
				} else {
					cartonNoGenerateService = caiNiaoCartonNoGenerateService;
				}
				break;
            case EMS:
                    cartonNoGenerateService = tmsCartonNoGenerateServiceImpl;
                    break;
            case JT:
                    cartonNoGenerateService = tmsCartonNoGenerateServiceImpl;
                    break;
            case STO:
                    cartonNoGenerateService = stoCartonNoGenerateService;
                    break;
			case HTKY:
					cartonNoGenerateService = bestCartonNoGenerateService;
					break;
			case ZTO:
					cartonNoGenerateService = tmsCartonNoGenerateServiceImpl;
					break;
			case POSTB_IMPORT:
					cartonNoGenerateService = postbImportCartonNoGenerateService;
					break;
			case IMAGE:
					cartonNoGenerateService = imageCartonNoGenerateService;
					break;
			case PDF:
					cartonNoGenerateService = imageCartonNoGenerateService;
					break;
            case FZGJ:
                cartonNoGenerateService = fzgjCartonNoGenerateServiceImpl;
                break;
            case FWSY:
					cartonNoGenerateService = tmsCartonNoGenerateServiceImpl;
					break;
			default:
				cartonNoGenerateService = defaultCartonNoGenerateService;
			}
		}
		return cartonNoGenerateService;
	}

	/**
	 * 圆通订单都使用圆通电子面单打印
	 * @return
     */
	private boolean isAllPrintByYto()
	{
		return SystemConfig.configIsOpen("print.all.print.by.yt", ParamUtil.getCurrentWarehouseId());
	}

	private List<Long> getTaoBaoShopIds() {
		String shopListStr = SystemConfig.getConfigValue("print.taobao.shop.ids", ParamUtil.getCurrentWarehouseId());
		if (StringUtils.isBlank(shopListStr)) {
			return null;
		}
		String[] shopArray = shopListStr.split(",");
		List<Long> shopList = new ArrayList<Long>();
		for (String str : shopArray) {
			shopList.add(Long.valueOf(str));
		}
		return shopList;
	}
}