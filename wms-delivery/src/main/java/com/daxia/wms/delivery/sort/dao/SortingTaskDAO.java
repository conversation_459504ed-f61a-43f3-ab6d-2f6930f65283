package com.daxia.wms.delivery.sort.dao;

import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.delivery.sort.entity.SortingTask;

/**
 * 分拣DAO
 */
@Name("com.daxia.wms.delivery.sortingTaskDAO")
@lombok.extern.slf4j.Slf4j
public class SortingTaskDAO extends HibernateBaseDAO<SortingTask, Long> {

	private static final long serialVersionUID = 2767292203887398558L;

	/**
     * 根据单据明细id查找分拣任务
     * @param docLineId
     * @return
     */
	@SuppressWarnings("unchecked")
	public List<SortingTask> getSortingTaskByDocLineId(Long docLineId) {
		String hql = "FROM SortingTask WHERE docLineId = :docLineId";
		Query query = this.getSession().createQuery(hql);
		query.setLong("docLineId", docLineId);
		return query.list();
	}
}
