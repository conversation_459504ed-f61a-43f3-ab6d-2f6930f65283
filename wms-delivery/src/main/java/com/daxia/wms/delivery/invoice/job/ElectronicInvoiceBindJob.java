package com.daxia.wms.delivery.invoice.job;

import java.util.List;

import org.jboss.seam.Component;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.wms.delivery.deliveryorder.service.impl.ElectronicInvoiceServiceImpl;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.service.ElectronicInvoiceService;
import com.daxia.wms.master.job.AbstractJob;

@Name("electronicInvoiceBindJob")
@AutoCreate
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class ElectronicInvoiceBindJob extends AbstractJob {
    private int DEFAULT_BATCH_NUM = 20;
    private int DEFAULT_FAILED_NUM = 5;

    @Override
    protected void doRun() throws Exception {
        ElectronicInvoiceService electronicInvoiceService = ((ElectronicInvoiceService) Component.getInstance(ElectronicInvoiceServiceImpl.class));

        List<InvoiceHeader> invoiceHeaderList = electronicInvoiceService.loadInvoiceToBind(DEFAULT_BATCH_NUM, DEFAULT_FAILED_NUM);
        for (InvoiceHeader invoiceHeader : invoiceHeaderList) {
        	try{
        		electronicInvoiceService.bind(invoiceHeader);
        	} catch (Exception e ){
        		e.printStackTrace();
        		continue;
        	}
            
        }
    }
}