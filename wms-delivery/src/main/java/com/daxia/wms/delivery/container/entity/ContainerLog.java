package com.daxia.wms.delivery.container.entity;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;

/**
 * 容器日志
 */
@Entity
@Table(name = "container_log")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " is_deleted = 0 ")
@SQLDelete(sql = "update container_log set is_deleted = 1 where id = ? and version = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class ContainerLog extends WhBaseEntity {

    private static final long serialVersionUID = -6027603962105373141L;

    private Long id;

    /**
     * 容器号
     */
    private String containerNo;

    /**
     * 与容器关联单据号
     */
    private String docNo;

    /**
     * 与容器关联单据类型
     */
    private String docType;

    /**
     * 操作
     */
    private String operation;

    /**
     * 操作时间
     */
    private Timestamp operationTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 是否删除 0:no 1:yes
     */
    private Long isDeleted;
    
    /**
     * 集货位号
     */
    private String mergeCode;
    
    private String operSource;   // 操作来源 (WMS/RF)
    
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)  
    @Column(name = "ID")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "CONTAINER_NO")
    public String getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }

    @Column(name = "DOC_NO")
    public String getDocNo() {
        return docNo;
    }

    public void setDocNo(String docNo) {
        this.docNo = docNo;
    }

    @Column(name = "DOC_TYPE")
    public String getDocType() {
        return docType;
    }

    public void setDocType(String docType) {
        this.docType = docType;
    }

    @Column(name = "OPERATION")
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Column(name = "OPERATION_TIME")
    public Timestamp getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Timestamp operationTime) {
        this.operationTime = operationTime;
    }

    @Column(name = "OPERATOR")
    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    @Column(name = "IS_DELETED")
    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Column(name = "MERGE_CODE")
    public String getMergeCode() {
        return mergeCode;
    }

    
    public void setMergeCode(String mergeCode) {
        this.mergeCode = mergeCode;
    }
    
    @Column(name="OPER_SOURCE")
	public String getOperSource() {
		return operSource;
	}
    
	public void setOperSource(String operSource) {
		this.operSource = operSource;
	}
}