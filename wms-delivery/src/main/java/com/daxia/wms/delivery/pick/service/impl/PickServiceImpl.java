package com.daxia.wms.delivery.pick.service.impl;


import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.log.LogUtil;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.entity.UserAccount;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.framework.system.service.UserAccountService;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.*;
import com.daxia.wms.Keys;
import com.daxia.wms.OrderLogConstants;
import com.daxia.wms.PageConfig;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.OrderFrozenException;
import com.daxia.wms.delivery.constant.DeliveryConstant;
import com.daxia.wms.delivery.container.entity.PktContainerDetail;
import com.daxia.wms.delivery.container.filter.PktContainerDetailFilter;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.delivery.container.service.PktContainerService;
import com.daxia.wms.delivery.deliveryorder.dao.DoDetailDAO;
import com.daxia.wms.delivery.deliveryorder.dao.DoHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.dao.DoLackDetailDAO;
import com.daxia.wms.delivery.deliveryorder.dto.DoExStatusOpDto;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoLackDetail;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateService;
import com.daxia.wms.delivery.deliveryorder.service.DoExceptionLogService;
import com.daxia.wms.delivery.deliveryorder.service.OrderLogService;
import com.daxia.wms.delivery.deliveryorder.service.impl.DoStatusRollbackListener;
import com.daxia.wms.delivery.invoice.service.ElectronicInvoiceService;
import com.daxia.wms.delivery.pick.dao.PickDAO;
import com.daxia.wms.delivery.pick.dao.PickTaskDAO;
import com.daxia.wms.delivery.pick.dto.BatchPickDTO;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.pick.service.PickHeaderService;
import com.daxia.wms.delivery.pick.service.PickService;
import com.daxia.wms.delivery.pick.service.PickTaskService;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.sort.service.SortingService;
import com.daxia.wms.delivery.store.service.GoodsStoreService;
import com.daxia.wms.delivery.task.repick.entity.ReversePickTask;
import com.daxia.wms.delivery.task.repick.service.RePickContainerService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.exp.delivery.srv.DoCancelExpSrv;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.master.dao.ContainerDAO;
import com.daxia.wms.master.entity.Container;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.entity.PackageInfoDetail;
import com.daxia.wms.master.filter.ContainerFilter;
import com.daxia.wms.master.service.*;
import com.daxia.wms.stock.stock.dto.OrderStockDTO;
import com.daxia.wms.stock.stock.dto.StockDTO;
import com.daxia.wms.stock.stock.entity.StockBatchAtt;
import com.daxia.wms.stock.stock.entity.TrsPickLog;
import com.daxia.wms.stock.stock.service.IOperator;
import com.daxia.wms.stock.stock.service.StockService;
import com.daxia.wms.stock.stock.service.TrsPickLogService;
import com.daxia.wms.stock.task.dao.TrsTaskDAO;
import com.daxia.wms.stock.util.BatchPropertyUtil;
import com.daxia.wms.util.Switch;
import com.daxia.wms.util.SwitchUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.Component;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.jboss.seam.core.Events;
import org.jboss.seam.security.Identity;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;

/**
 * <pre>
 * Description:拣货逻辑处理
 * 需求: 用户在拣完货后,需要变更系统相关的状态,同时系统的库存数也要变更.
 *
 * 实现: 拣货人员在拣完货后,在前台录入拣货单号,自己的工号和设备号后,提交系统处理
 *      1, 更改订单,订单明细,波茨,拣货明细,拣货单,拣货任务的状态.
 *      2, 更改库存.
 * </pre>
 */
@Name("com.daxia.wms.delivery.pickService")
@lombok.extern.slf4j.Slf4j
public class PickServiceImpl implements PickService, DoStatusRollbackListener {
    @In
    private PickHeaderService pickHeaderService;
    @In
    private DeliveryOrderService deliveryOrderService;
    @In
    private DoDetailDAO doDetailDAO;
    @In
    private DoHeaderDAO doHeaderDAO;
    @In
    private WaveService waveService;
    @In
    SortingService sortingService;
    @In
    private StockService stockService;
    @In
    private PickTaskService pickTaskService;
    @In
    private PickTaskDAO pickTaskDAO;
    @In
    private SequenceGeneratorService sequenceGeneratorService;
    @In
    private TrsPickLogService trsPickLogService;
    @In
    private PickDAO pickDAO;
    @In
    private TrsTaskDAO trsTaskDAO;
    @In
    private PackageInfoDetailService packageInfoDetailService;
    @In
    private DoExceptionLogService doExceptionLogService;
    @In
    private DoAllocateService doAllocateService;
    @In
    private SortingBinService sortingBinService;
    @In
    private LocationService locationService;
    @In
    private PktContainerService pktContainerService;
    @In
    private MergeLocService mergeLocService;
    @In
    private ContainerMgntService containerMgntService;
    @In
    private CartonService cartonService;

    @In
    RePickContainerService rePickContainerService;
    @In("pickOperator")
    private IOperator pickOperator;

    @In("allocateOperator")
    private IOperator allocateOperator;

    @In("lackOperator")
    private IOperator lackOperator;

    @In
    private DoCancelExpSrv doCancelExpSrv;

    @In
    private DoLackDetailDAO doLackDetailDAO;

    @In
    private ElectronicInvoiceService electronicInvoiceService;

    @In
    private OrderLogService orderLogService;

    @In
    private GoodsStoreService goodsStoreService;

    @In
    private UserAccountService userAccountService;

    @In
    private ContainerService containerService;
    @In
    private ContainerDAO containerDAO;

    @In
    private ExpFacadeService expFacadeService;

    /**
     * 强制拣货
     */
    @Override
    @Transactional
    @Loggable
    public void force2pick(Long doId) {
        log.debug("force2pick: doId = " + doId);
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(doId);

        DoExStatusOpDto doExStatusOpDto = new DoExStatusOpDto();
        doExStatusOpDto.setReleaseStatus(doHeader.getReleaseStatus());
        doExStatusOpDto.setStatus(doHeader.getStatus());
        doExStatusOpDto.setExceptionStatus(doHeader.getExceptionStatus());
        doExStatusOpDto.setOpType(Constants.ExOpType.FORCE_PICK.getValue());

        String updateBy = pickTaskDAO.getOperateUser();
        //强制拣缺货标记的拣货任务
        forcePickTsk(doHeader, updateBy);
        // 更新订单明细
        String fromDoStatus = doHeader.getStatus();
        boolean needUpdateWave = updateDo4ForcePick(fromDoStatus, doHeader);

        if (needUpdateWave) {
            // 更新波次
            Boolean autoSort = Config.isDefaultFalse(Keys.Delivery.sort_isWithPick, Config.ConfigLevel.WAREHOUSE);
            modifyWave(doHeader.getWaveHeader(), updateBy, false, autoSort);
        }

        Events.instance().raiseEvent("DELETE_FIND_LACK_EVENT", doId);
        // 记录异常日志
        doExceptionLogService.saveDoExceptionLog(doExStatusOpDto, doHeader);
    }

    @Override
    @Transactional
    public void forcePickTsk(DeliveryOrderHeader doHeader, String updateBy) {
        // 验证是否能够强制拣货
        List<PickTask> pickTaskList = validateForce2Pick(doHeader);
        Set<Long> pktHeaderIdList = new HashSet<Long>();

        // 更新拣货任务
        updatePickTasks4ForcePick(pickTaskList, pktHeaderIdList, updateBy);

        //更新拣货单
        for (Long pktHeaderId : pktHeaderIdList) {
            pickHeaderService.changePktHeaderStatus(pickHeaderService.getPktHeader(pktHeaderId), true, updateBy);
        }
    }

    /**
     * 根据订单状态判断强制拣货需要更新订单和订单明细的内容
     */
    private boolean updateDo4ForcePick(String fromDoStatus, DeliveryOrderHeader doHeader) {
        log.debug("updateDo4ForcePick do = " + doHeader.getId());
        boolean needUpdateWave = false;
        // 如果是分配完成或者部分拣货，则要更新订单状态为拣货完成，更新波次状态
        if (StringUtil.isIn(fromDoStatus, DoStatus.ALLALLOCATED.getValue(), DoStatus.PARTPICKED.getValue())) {
            log.debug("updateDo4ForcePick needUpdateWave = true do = " + doHeader.getId());
            needUpdateWave = true;
            doDetailDAO.updateDoDetailsAllPicked(doHeader.getId());
            String pcsStatus = pickTaskDAO.getDoPickStatus(doHeader.getId(), PackageType.B.getValue());
            String unitStatus = pickTaskDAO.getDoPickStatus(doHeader.getId(), PackageType.C.getValue());
            // 设置零散状态
            if (StringUtil.isNotEmpty(pcsStatus)) {
                if (StringUtil.isEmpty(doHeader.getPcsStatus()) || (pcsStatus.compareTo(doHeader.getPcsStatus()) > 0)) {
                    doHeader.setPcsStatus(pcsStatus);
                }

            }
            //设置整件状态
            if (StringUtil.isNotEmpty(unitStatus)) {
                if (StringUtil.isEmpty(doHeader.getUnitStatus()) || unitStatus.compareTo(doHeader.getUnitStatus()) > 0) {
                    doHeader.setUnitStatus(unitStatus);
                }
            }
            //设置订单状态
            if (StringUtil.isBlank(pcsStatus)) {
                doHeader.setStatus(doHeader.getUnitStatus());
            } else if (StringUtil.isBlank(unitStatus)) {
                doHeader.setStatus(doHeader.getPcsStatus());
            } else if (StringUtil.notNullString(doHeader.getPcsStatus(), "0").compareTo(StringUtil.notNullString(doHeader.getUnitStatus(), "0")) > 0) {
                doHeader.setStatus(doHeader.getPcsStatus());
            } else {
                doHeader.setStatus(doHeader.getUnitStatus());
            }
            if (doHeader.getPickStartTime() == null) {
                doHeader.setPickStartTime(DateUtil.getNowTime());
            }
            doHeader.setPickEndTime(DateUtil.getNowTime());

        } else {
            log.debug("updateDo4ForcePick needUpdateWave = false do = " + doHeader.getId());
            doDetailDAO.updateDoDetailsQtyAllPicked(doHeader.getId());
        }

        // 如果订单已经分拣完成，则更新分拣数量为拣货数量
        if (DoStatus.ALLSORTED.getValue().equals(fromDoStatus)) {
            doDetailDAO.updateDoDetailsQtyAllSorted(doHeader.getId());
        }
        doHeader.setReleaseStatus(ReleaseStatus.RELEASE.getValue());
        doHeader.setExceptionStatus(DoExpStatus.COMPLETE.getValue());
        doHeader.setLackStatus(YesNo.NO.getValue());
        doHeaderDAO.update(doHeader);
        return needUpdateWave;
    }

    private void updatePickTasks4ForcePick(List<PickTask> pickTaskList,
                                           Set<Long> pktHeaderIdList, String updateBy) {
        log.debug("updatePickTasks4ForcePick : begin");
        for (PickTask pickTask : pickTaskList) {
            log.debug("updatePickTasks4ForcePick : pickTask = " + pickTask.getId());
            pktHeaderIdList.add(pickTask.getPktHeaderId());
            BigDecimal pickedQty = pickTask.getQty().subtract(pickTask.getPickedQty());
            BigDecimal pickedQtyUnit = pickTask.getQtyUnit().subtract(pickTask.getQtyPickedUnit());
            //更改库存
            changePickStock(pickTask, pickedQty, pickedQtyUnit);
            //更新拣货任务
            pickTask.setStatus(DoStatus.ALLPICKED.getValue());
            pickTask.setPickWho(updateBy);
            pickTask.setToLpnNo(DeliveryConstant.LPN_DEFAULT_NO);
            pickTask.setPickedQty(pickTask.getQty());
            pickTask.setQtyPickedUnit(pickTask.getQtyUnit());
            pickTask.setStockStatus(StockStatus.NORMAL.getValue());
            pickTaskDAO.update(pickTask);
            //产生交易日志
            createPickTrs(pickTask, updateBy, pickedQty, pickedQtyUnit);
            //记录拣货日志
            orderLogService.saveLog(pickTask.getDoHeader(),
                    OrderLogConstants.OrderLogType.MODIFY_PICK_INFO.getValue(),
                    ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_MODIFY_PICK, null, pickTask.getSku().getProductCode(), pickedQty));
        }
    }

    /**
     * 验证是否能够强制拣货
     */
    private List<PickTask> validateForce2Pick(DeliveryOrderHeader doHeader) {
        List<PickTask> pickTaskList = pickTaskDAO.findLackPickTasksByDoId(doHeader.getId());
        if (ListUtil.isNullOrEmpty(pickTaskList)) {
            throw new DeliveryException(DeliveryException.DO_HAS_NOT_LACK);
        }

        //已移出波次；
        if (doHeader.getWaveId() == null) {
            throw new DeliveryException(DeliveryException.DO_NOT_IN_WAVE);
        }
        if (YesNo.YES.getValue().equals(doHeader.getNeedCancel())) {
            throw new DeliveryException(DeliveryException.DO_NEED_CANCEL);
        }

        String excStatus = doHeader.getExceptionStatus();
        String releaseStatus = doHeader.getReleaseStatus();
        String doNo = doHeader.getDoNo();
        //必须是待退回状态且必须是冻结的
        if (!(DoExpStatus.TO_BE_ROLLBACK.getValue().equals(excStatus)
                && ReleaseStatus.HOLD.getValue().equals(releaseStatus))) {
            throw new DeliveryException(DeliveryException.EXCEPTION_FORCE_PICK_ERROR, doNo);
        }

        // 不是缺货状态
        if (!YesNo.YES.getValue().equals(doHeader.getLackStatus())) {
            throw new DeliveryException(DeliveryException.EXCEPTION_FORCE_PICK_NOT_LACK, doNo);
        }
        return pickTaskList;
    }

    /**
     * (RF拣货)正常拣货
     */
    @Override
    @Transactional
    @Loggable
    public void pickByTask(List<PickTask> pickTasks, Container container, BigDecimal pickedQtyUnit, BigDecimal pickedQty,
                           WaveHeader waveHeader, String reason, String updateBy) {
        pickByTask(pickTasks,container,pickedQtyUnit,pickedQty,waveHeader,reason,updateBy,null);
    }

    /**
     * 拣货人为空时更新拣货人
     * @param pktHeaderId
     * @param updateBy
     */
    private void updatePktHeaderPickBy(Long pktHeaderId, String updateBy) {
        PickHeader pktHeader = pickHeaderService.getPktHeader(pktHeaderId);
        if (pktHeader != null && StringUtil.isEmpty(pktHeader.getPickBy())){
            pktHeader.setPickBy(updateBy);
            pickHeaderService.updatePktHeader(pktHeader);
        }
    }

    /**
     * (RF拣货)正常拣货
     */
    @Override
    @Transactional
    @Loggable
    public void pickByTask(List<PickTask> pickTasks, Container container, BigDecimal pickedQtyUnit, BigDecimal pickedQty,
                           WaveHeader waveHeader, String reason, String updateBy,String containerNo) {
        log.debug("normal pickByTask waveHeader = " + waveHeader.getId());
        //按照loc,sku,do排序，再依次分配实际拣货数量
        Collections.sort(pickTasks, new Comparator<PickTask>() {
            @Override
            public int compare(PickTask o1, PickTask o2) {
                return o1.getDoHeaderId().compareTo(o2.getDoHeaderId());
            }
        });
        List<BatchPickDTO> batchPickDTOList = Lists.newArrayList();
        for (PickTask pickTask : pickTasks) {
            if (pickTask == null) {
                throw new DeliveryException(DeliveryException.DELIVERYORDER_CANNOT_PICK_PICKTASKNOTEXIST);
            }
            //查询容器
            //            if (container == null) {
            container = getContainerByPickTask(pickTask);
            //            }
            //如果实际拣货数量小于当前明细需要拣货的数量，当前明细和任务按实际拣货数量处理，剩余明细和任务不再处理
            BigDecimal leftRequiredQtyUnit = pickTask.getQtyUnit().subtract(pickTask.getQtyPickedUnit());
            if (pickedQtyUnit.compareTo(leftRequiredQtyUnit) < 0) {
                partlyCompletePickTask(pickTask, container != null ? container.getContainerNo() : containerNo, pickedQtyUnit, pickedQty, reason, updateBy);
                saveContainerDetail(container,pickTask, pickedQty, pickedQtyUnit);
                break;
            } else if (pickedQtyUnit.compareTo(leftRequiredQtyUnit) >= 0) { //如果实际拣货数量大于等于当前明细需要拣货的数量，按需求拣货数量处理，实际拣货数量减少，继续处理剩余明细和任务
                BigDecimal leftRequiredQty = leftRequiredQtyUnit;
                BatchPickDTO dto = new BatchPickDTO();
                dto.setPickTask(pickTask);
                dto.setPickedQty(leftRequiredQty);
                dto.setPickedQtyUnit(leftRequiredQtyUnit);
                batchPickDTOList.add(dto);
                pickedQtyUnit = pickedQtyUnit.subtract(leftRequiredQtyUnit);
                pickedQty = pickedQty.subtract(leftRequiredQty);

                saveContainerDetail(container,pickTask, leftRequiredQty, leftRequiredQtyUnit);
                if (BigDecimal.ZERO.compareTo(pickedQtyUnit) == 0) {
                    break;
                }
            }
        }
        batchCompletePickTask(batchPickDTOList, container, reason, updateBy, waveHeader);

        //更新拣货人
        updatePktHeaderPickBy(pickTasks.get(0).getPktHeaderId(),updateBy);

        //更新波次
        Boolean autoSort = Config.isDefaultFalse(Keys.Delivery.sort_isWithPick, Config.ConfigLevel.WAREHOUSE);
        modifyWave(waveHeader, updateBy, false, autoSort);
    }

    private Container getContainerByPickTask(PickTask pickTask) {
        ContainerFilter filter = new ContainerFilter();
        if (!PageConfig.is("containerMgntStartFlag", Config.ConfigLevel.WAREHOUSE.getValue())) {
            return null;
        } else if (!PageConfig.is("delivery.sort.isWithPick", Config.ConfigLevel.WAREHOUSE.getValue())) {
            filter.setDocType(BindDocType.WAVE.getValue());
            filter.setDocNo(pickTask.getWave().getWaveNo());
        } else {
            filter.setDocType(BindDocType.DELIVERYORDER.getValue());
            filter.setDocNo(pickTask.getDoHeader().getDoNo());
        }
        filter.setRefNo1(pickTask.getPickHeader().getPktNo());
        filter.setIsCurrentCarton(YesNo.YES.getValue());
        List<Container> list = containerService.findByFilter(filter);
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }
    
    private void saveContainerDetail(Container container, PickTask pickTask, BigDecimal pickedQty, BigDecimal pickedQtyUnit) {
        if (container == null) {
            return;
        }
        pktContainerService.create(container, pickTask, pickedQty, pickedQtyUnit,pickTask.getPickHeader().getRegionId());
    }

    /**
     * (RF拣货)非正常拣货，点击缺货，不操作库存
     */
    @Override
    @Transactional
    @Loggable
    public void pickByTask(List<PickTask> pickTasks, String reason, String updateBy) {
        log.debug("lack pickByTask");
        markLocLacked(pickTasks.get(0).getLocId());
        WaveHeader waveHeader = null;
        PickHeader pickHeader = pickHeaderService.getPktHeader(pickTasks.get(0).getPktHeaderId());
        String pktNo = pickHeader.getPktNo();
        Long pktId = pickHeader.getId();
        String pktType = String.valueOf(pickHeader.getPktType());
        for (PickTask pickTask : pickTasks) {
            lackPickTask(pickTask, reason, updateBy);
            if (waveHeader == null) {
                waveHeader = waveService.getWave(pickTask.getWaveHeaderId());
            }
        }
        //更新拣货人
        updatePktHeaderPickBy(pickHeader.getId(),updateBy);
        //更新波次
        Boolean autoSort = Config.isDefaultFalse(Keys.Delivery.sort_isWithPick, Config.ConfigLevel.WAREHOUSE);
        modifyWave(waveHeader, updateBy, false, autoSort);
        goodsStoreService.flushContainer4PickLack(pktNo, pktType, pktId);
    }

    /**
     * 给拣货库位打上疑似缺货标识
     *
     * @param locId
     */
    private void markLocLacked(Long locId) {
        Location pickLoc = locationService.getLocation(locId);
        if (LocLackFlag.NORMAL.getValue().equals(pickLoc.getLackFlag()) && LockStatus.NORMAL.getValue().equals(pickLoc.getLockStatus())) {
            pickLoc.setLackFlag(LocLackFlag.LACK.getValue());
            locationService.saveUpdate(pickLoc);
        }
    }

    /**
     * 拣货缺货处理
     */
    private void lackPickTask(PickTask task, String reason, String updateBy) {
        //更新任务
        updatePickTask(task, null, reason, updateBy, BigDecimal.ZERO, BigDecimal.ZERO);
        //更新订单明细
        DeliveryOrderDetail doDetail = doDetailDAO.get(task.getDoDetailId());
        changeDoDetailStatus(task, doDetail, BigDecimal.ZERO, BigDecimal.ZERO, updateBy);
        String orderLogType;
        //批发、RTV、调拨拣货缺货，不冻结订单
        if (Arrays.asList(DoType.ALLOT.getValue(), DoType.RTV.getValue(), DoType.WHOLESALE.getValue()).contains(task.getDoHeader().getDoType()) &&
                YesNo.YES.getValue().equals(doDetail.getDoHeader().getLackShipFlag())) {
            orderLogType = OrderLogConstants.OrderLogType.PICK_LACK.getValue();
            //释放已分配库存
            OrderStockDTO stockDTO = new OrderStockDTO();
            stockDTO.setFmStockId(task.getFmStockId());
            stockDTO.setPlanQty(task.getQty());
            stockDTO.setPlanQtyUnit(task.getQtyUnit());
            stockDTO.setActualQty(task.getQty());
            stockDTO.setFmLocId(task.getLocId());
            stockDTO.setLotId(task.getLotId());
            stockDTO.setLpnNo(task.getLpnNo());
            stockDTO.setSkuId(task.getSkuId());
            stockDTO.setAllocatingId(task.getAllocatingId());
            allocateOperator.setStockDto(stockDTO);
            stockService.undo(allocateOperator);
            //删除拣货任务
            pickTaskService.removeTask(task);
        } else {
            orderLogType = OrderLogConstants.OrderLogType.ORDER_HOLD.getValue();
            //冻结订单
            deliveryOrderService.autoFrozenOnRFPick(task.getDoHeaderId(), task.getId(), updateBy, reason);
        }
        //更新定单状态
        DeliveryOrderHeader doHeader = doHeaderDAO.get(task.getDoHeaderId());
        changeDoHeaderStatus(doHeader, updateBy);
        //记录日志，拣货缺货
        orderLogService.saveLog(doHeader,
                orderLogType,
                ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_PICK_LACK, null,
                        task.getLocation().getLocCode(), task.getSku().getProductCode()));
        //更新拣货单
        pickHeaderService.changePktHeaderStatus(pickHeaderService.getPktHeader(task.getPktHeaderId()), true, updateBy);
    }

    /**
     * 完成一条拣货明细
     */
    private void completePickTask(PickTask task, Container container, BigDecimal pickedQtyUnit, BigDecimal pickedQty, String reason, String updateBy) {
        log.debug("completePickTask task = " + task.getId());
        //更改库存
        changePickStock(task, pickedQty, pickedQtyUnit);
        //更新任务
        updatePickTask(task, container, reason, updateBy, pickedQty, task.getQtyUnit());
        //更新订单明细
        DeliveryOrderDetail doDetail = doDetailDAO.get(task.getDoDetailId());
        changeDoDetailStatus(task, doDetail, pickedQty, pickedQtyUnit, updateBy);
        //更新定单状态
        DeliveryOrderHeader doHeader = doHeaderDAO.get(task.getDoHeaderId());
        changeDoHeaderStatus(doHeader, updateBy);
        //更新拣货单
        //产生交易日志，修改劳动力任务临时表信息
        pickHeaderService.changePktHeaderStatus(pickHeaderService.getPktHeader(task.getPktHeaderId()), true, updateBy);
        createPickTrs(task, updateBy, pickedQty, pickedQtyUnit);
    }

    /**
     * 批量更新拣货完成的任务
     *
     * @param container
     * @param reason
     * @param updateBy
     */
    private void batchCompletePickTask(List<BatchPickDTO> batchIdList, Container container, String reason, String updateBy, WaveHeader waveHeader) {
        if (CollectionUtils.isEmpty(batchIdList)) {
            return;
        }
        Long waveId = waveHeader.getId();
        List<Long> idList = Lists.newArrayList();
        for (BatchPickDTO dto : batchIdList) {
            PickTask task = dto.getPickTask();
            BigDecimal pickedQty = dto.getPickedQty();
            BigDecimal pickedQtyUnit = dto.getPickedQtyUnit();
            idList.add(task.getId());
            //更改库存
            changePickStock(task, pickedQty, pickedQtyUnit);
        }
        //批量更新拣货单状态
        batchUpdate4Pick(batchIdList, container, updateBy, reason);
        //更新订单明细
        deliveryOrderService.batchUpdateDoDetail4Pick(idList, updateBy, waveId);
        //更新订单头状态
        deliveryOrderService.batchUpdateDoHeader4Pick(idList, updateBy, waveId);
        // 拣货完成消息回写OMS系统
        List<Long> doHeaderList = deliveryOrderService.getDoListByPickTaskAndStatus(idList,DoStatus.ALLPICKED.getValue());
        if (CollectionUtils.isNotEmpty(doHeaderList)){
            // expFacadeService.sendDo2OmsForPick(doHeaderList,waveHeader.getWaveType());
        }
        //记录日志，拣货完成
        orderLogService.saveLog4PickList(idList,
                OrderLogConstants.OrderLogType.PICK_COMPLETE.getValue(),
                ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_PICK_COMPLETE),updateBy);
        //修改拣货单状态,劳动力管理
        pickHeaderService.batchChangePktHeaderStatus(idList, updateBy, waveId);
        //记录日志
        trsPickLogService.batchSavePickLog(idList, updateBy);
    }

    /**
     * 部分完成一条拣货明细
     */
    private void partlyCompletePickTask(PickTask task, String containerNo, BigDecimal pickedQtyUnit, BigDecimal pickedQty,
                                        String reason, String updateBy) {
        log.debug("partlyCompletePickTask task = " + task.getId());
        //需要拣货数量
        BigDecimal qty = task.getQty();
        //剩余拣货数量
        BigDecimal leftQty = qty.subtract(pickedQty);
        BigDecimal leftQtyUnit = task.getQtyUnit().subtract(pickedQtyUnit);
        //更改库存
        Map<String, Long> map = changePickStock(task, pickedQty, pickedQtyUnit);
        Long newFrmStockId = map.get(StockType.STOCK_ALLOC.getValue());
        //拆分拣货任务
        separatePickTask(task, containerNo, pickedQtyUnit, pickedQty, leftQtyUnit, leftQty, reason, newFrmStockId, updateBy);
        //更新订单明细
        DeliveryOrderDetail doDetail = doDetailDAO.get(task.getDoDetailId());
        changeDoDetailStatus(task, doDetail, pickedQty, pickedQtyUnit, updateBy);
        //更新定单状态
        DeliveryOrderHeader doHeader = doHeaderDAO.get(task.getDoHeaderId());
        changeDoHeaderStatus(doHeader, updateBy);
        //更新拣货单
        pickHeaderService.changePktHeaderStatus(pickHeaderService.getPktHeader(task.getPktHeaderId()), true, updateBy);
        //产生交易日志
        createPickTrs(task, updateBy, pickedQty, pickedQty);
    }

    /**
     * 更新现有任务的数量为实际拣货数量，将剩余数量生成新的拣货任务
     */
    private void separatePickTask(PickTask task, String containerNo, BigDecimal pickedQtyUnit, BigDecimal pickedQty, BigDecimal leftQtyUnit, BigDecimal leftQty, String reason, Long newFrmStockId, String updateBy) {
        log.debug("separatePickTask task = " + task.getId());
        if (BigDecimal.ZERO.compareTo(pickedQtyUnit) >= 0) {
            throw new DeliveryException(DeliveryException.PICK_TASK_SPLIT_ERROR);
        }
        task.setQty(pickedQty);
        task.setQtyUnit(pickedQtyUnit);
        task.setPickedQty(pickedQty);
        task.setQtyPickedUnit(pickedQtyUnit);
        task.setPickWho(updateBy);
        task.setPickTime(DateUtil.getNowTime());
        task.setStatus(DoStatus.ALLPICKED.getValue());
        task.setToLpnNo(DeliveryConstant.LPN_DEFAULT_NO);
        task.setStockStatus(reason);
        //记录容器号
        if (StringUtil.isNotEmpty(containerNo)) {
            task.setContainerNo(containerNo);
        }
        pickTaskService.updatePickTask(task);
        //将剩余数量生成新的拣货任务
        PickTask newTask = pickTaskService.createPickTask(task, leftQtyUnit, leftQty, newFrmStockId, updateBy);
        //启用了电子标签，拆分容器明细
        if (Config.isDefaultFalse(Keys.Delivery.pick_useDPS, Config.ConfigLevel.WAREHOUSE) &&
                StringUtil.isNotEmpty(containerNo)) {
            //TODO
            separatePickContainerDetail(task,newTask,containerNo,updateBy);
        }
    }

    /**
     * 拆分容器明细
     * @param task
     * @param newTask
     * @param containerNo
     * @param updateBy
     */
    private void separatePickContainerDetail(PickTask task, PickTask newTask, String containerNo, String updateBy) {
        PktContainerDetailFilter filter = new PktContainerDetailFilter();
        filter.setContainerNo(containerNo);
        filter.setTaskId(task.getId());
        List<PktContainerDetail> list = pktContainerService.findListByFilter(filter);
        if (CollectionUtils.isEmpty(list) || list.size() > 1) {
            throw new DeliveryException(DeliveryException.PICK_TASK_SPLIT_ERROR);
        }
        PktContainerDetail detail = list.get(0);
        detail.setQty(task.getQty());
        detail.setQtyUnit(task.getQtyUnit());
        pktContainerService.updateDetail(detail);

        PktContainerDetail newDetail = new PktContainerDetail();
        newDetail.setSkuId(detail.getSkuId());
        newDetail.setLotId(detail.getLotId());
        newDetail.setWarehouseId(detail.getWarehouseId());
        newDetail.setCreatedBy(updateBy);
        newDetail.setUpdatedBy(updateBy);
        newDetail.setHeaderId(detail.getHeaderId());
        newDetail.setLocId(detail.getLocId());
        newDetail.setTaskId(newTask.getId());
        newDetail.setQty(newTask.getQty());
        newDetail.setQtyUnit(newTask.getQtyUnit());
        pktContainerService.createDetail(newDetail);
    }

    /**
     * 正常拣货，更新拣货数量，状态，拣货人和容器
     */
    private void updatePickTask(PickTask task, Container container, String reason, String pickerNo, BigDecimal pickQty, BigDecimal pickQtyUnit) {
        log.debug("updatePickTask task = " + task.getId());
        task.setStatus(DoStatus.ALLPICKED.getValue());
        task.setPickedQty(pickQty);
        task.setQtyPickedUnit(pickQtyUnit);
        task.setToLpnNo(DeliveryConstant.LPN_DEFAULT_NO);
        //设置拣货人,拣货设施
        task.setPickWho(pickerNo);
        task.setPickTime(DateUtil.getNowTime());
        task.setStockStatus(reason);
        //记录容器
        if (null != container) {
            task.setContainerNo(container.getContainerNo());
        }
        pickTaskService.updatePickTask(task);
    }

    private String validatPicker(String pickerNo) {
        if (StringUtil.isEmpty(pickerNo) || pickerNo.equals("1")) {
            return ParamUtil.getCurrentLoginName();
        } else {
            UserAccount ua = userAccountService.getUserAccount(pickerNo);
            if (ua == null) {
                throw new DeliveryException(DeliveryException.PICKER_USER_NOT_EXIST);
            }
        }
        return pickerNo;
    }

    /**
     * 拣货业务(根据拣货单拣货)
     */
    @Override
    @Transactional
    @Loggable
    public void pick(String pktNo, String pickerNo,Boolean autoSort) throws DeliveryException {
        log.debug("pick pktNo = " + pktNo);
        // 校验操作人
        pickerNo = validatPicker(pickerNo);
        // 防止页面没有输入拣货单号
        if (StringUtil.isEmpty(pktNo)) {
            throw new DeliveryException(DeliveryException.DELIVERYORDER_CANNOT_PICK_NOTEXIST);
        }
        PickHeader pktHeader = pickHeaderService.getPktHeaderByPktNo(pktNo);
        if (pktHeader == null) {
            // 兼容扫描波次号
            WaveHeader waveHeader = waveService.queryWaveByNo(pktNo);
            if (null == waveHeader) {
                throw new DeliveryException(DeliveryException.DELIVERYORDER_CANNOT_PICK_NOTEXIST);
            }
            List<PickHeader> headerList = pickHeaderService.getPktHeadersByWaveId(waveHeader.getId());
            if (CollectionUtils.isEmpty(headerList) || headerList.size() != 1) {
                throw new DeliveryException(DeliveryException.DELIVERYORDER_CANNOT_PICK_NOTEXIST);
            }
            pktHeader = headerList.get(0);
        }
        if (!PktStatus.RELEASED.getValue().equals(pktHeader.getStatus())) {
            throw new DeliveryException(DeliveryException.DELIVERYORDER_CANNOT_PICK_WRONGSTATUS);
        }
        //获取拣货明细
        List<PickTask> pickTasks = pickTaskDAO.getPickTasksByPktHeaderIdAndStatus(pktHeader.getId(),
                Constants.DoStatus.ALLALLOCATED.getValue(), Constants.DoStatus.PARTPICKED.getValue());
        if (pickTasks.isEmpty()) {
            throw new DeliveryException(DeliveryException.DELIVERYORDER_CANNOT_PICK_PICKTASKNOTEXIST);
        }
        WaveHeader waveHeader = pktHeader.getWaveHeader();
        //缺发处理
        deliveryOrderService.updateUnAllocDoDetails(waveHeader);
        List<BatchPickDTO> batchPickDTOList = Lists.newArrayList();
        for (PickTask pickTask : pickTasks) {
            // 更新拣货单明细
            //为了防止RF部分拣货之后再到V3系统进行拣货确认，需要判断实际拣货数量
            BigDecimal qty = pickTask.getQty();
            BigDecimal qtyPicked = pickTask.getPickedQty();
            BigDecimal realPickedQty = qty.subtract(qtyPicked);
            BigDecimal realPickedQtyUnit = pickTask.getQtyUnit().subtract(pickTask.getQtyPickedUnit());
            BatchPickDTO dto = new BatchPickDTO();
            dto.setPickTask(pickTask);
            dto.setPickedQty(realPickedQty);
            dto.setPickedQtyUnit(realPickedQtyUnit);
            batchPickDTOList.add(dto);
        }
        // 批量拣货
        batchCompletePickTask(batchPickDTOList, null, StockStatus.NORMAL.getValue(), pickerNo, waveHeader);
        //修改波次状态
        modifyWave(waveHeader, pickerNo, false, autoSort);
    }

    /**
     * 修改波次信息
     *
     * @param waveHeader
     * @param allowDelete
     */
    @Override
    @Transactional
    @Loggable
    public void modifyWave(WaveHeader waveHeader, String updateBy, boolean allowDelete,Boolean autoSort) throws DeliveryException {
        if (waveHeader == null) {
            throw new DeliveryException(DeliveryException.DELIVERYORDER_CANNOT_PICK_WAVENOTEXIST);
        }

        Long waveId = waveHeader.getId();
        //看拣货任务
        Map<String, Long> map = pickTaskService.findStatusCountByWave(waveId);
        Long total = NumberUtil.coverNull(map.get("total"));
        Long relCount = NumberUtil.coverNull(map.get(DoStatus.ALLALLOCATED.getValue()));
        Long compCount = NumberUtil.coverNull(map.get(DoStatus.ALLPICKED.getValue()));
        Long cancleCount = NumberUtil.coverNull(map.get(DoStatus.CANCELED.getValue()));
        boolean updateFlag = true;
        if (total == 0) {
            log.debug("modifyWave total == 0 waveHeader = " + waveHeader.getId());
            // 没有do 取消波次
            //释放容器
            containerMgntService.releaseContainerByWave(waveHeader, Constants.ContainerType.WAVE_CONTAINER.getValue(), Constants.BindDocType.WAVE.getValue());
    
            if (allowDelete) {
                waveService.removeWaveHeader(waveHeader);
            }
            return;
        } else if (relCount.longValue() != 0) {
            // 还有发布状态的do

            if (relCount.equals(total)) {
                log.debug("modifyWave relCount.equals(total) waveHeader = " + waveHeader.getId());
                waveHeader.setWaveStatus(WaveStatus.ALLALLOCATED.getValue());
            } else {
                log.debug("modifyWave WaveStatus.PARTPICKED waveHeader = " + waveHeader.getId());
                waveHeader.setWaveStatus(WaveStatus.PARTPICKED.getValue());
            }
        } else {
            if (compCount + cancleCount == total) {
                // 全部拣货完成

                int waveDoCount = doHeaderDAO.countSortingNumberByWaveId(waveHeader.getId());
                // 如果一个波次下只有一个do
                // 则直接将波次、发货单、发货单明细的全部改为"分拣完成"
                // 因为一个波次下只有一个DO 没有分拣的必要
                if (waveDoCount == 1) {
                    log.debug("modifyWave waveDoCount == 1 waveHeader = " + waveHeader.getId());
                    Integer mergeCfg = SystemConfig.getConfigValueInt(Switch.LABOR_FORCE_SWITCH_SORT, ParamUtil.getCurrentWarehouseId());
                    //如果波次类型在配置项的波次类型里 则走直接到拣货完成
                    Boolean isCfgWaveType = SwitchUtils.isInWaveTypeCfg(waveHeader.getWaveType(), waveHeader.getAutoType());
                    if (Constants.YesNo.YES.getValue().equals(mergeCfg) && isCfgWaveType) {
                        log.debug("modifyWave WaveStatus.ALLPICKED waveHeader = " + waveHeader.getId());
                        waveHeader.setWaveStatus(WaveStatus.ALLPICKED.getValue());
                    } else {
                        if (waveHeader.getSortGridId() != null) {
                            sortingBinService.subWaveQty(waveHeader.getSortGridId());
                        }
                        waveHeader.setWaveStatus(Constants.WaveStatus.ALLSORTED.getValue());

                        // 将波次下的发货单改为分拣完成
                        DeliveryOrderHeader doHeader = this.doHeaderDAO.findDoHeaderByWaveId(waveId).get(0);
                        if (doHeader.getStatus().compareTo(DoStatus.ALLSORTED.getValue()) < 0) { //如果订单的整或散部分已经装箱则不需要修改订单状态
                            String status = Constants.DoStatus.ALLSORTED.getValue();
                            doHeader.setStatus(status);
                            if (StringUtil.isNotEmpty(doHeader.getPcsStatus())) {
                                doHeader.setPcsStatus(status);
                            }
                            if (StringUtil.isNotEmpty(doHeader.getUnitStatus())) {
                                doHeader.setUnitStatus(status);
                            }
                            //设置分拣开始时间 分拣结束时间 二者相同
                            doHeader.setSortStartTime(DateUtil.getNowTime());
                            doHeader.setSortTime(doHeader.getSortStartTime());
                            deliveryOrderService.updateDoHeader(doHeader);
                            // 将波次下的所有发货单明细改为分拣完成
                            doDetailDAO.updateDoDetailByWaveId(waveId, status, status);
                            //  将波次所有拣货任务改为拣货完成 ，分拣数量改为已拣数量
                            pickTaskDAO.updatePickTaskSortedByWaveId(waveId);

                            //记录日志,分拣完成
                            orderLogService.saveLog(doHeader,
                                    OrderLogConstants.OrderLogType.SORT_COMPETE.getValue(),
                                    ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_SORT_COMPETE));
                        }

                        log.debug("modifyWave WaveStatus.ALLSorted waveHeader = " + waveHeader.getId());
                    }
                } else if (autoSort){
                    waveHeader.setWaveStatus(WaveStatus.ALLPICKED.getValue());
                    waveHeader.setUpdatedAt(DateUtil.getNowTime());
                    waveHeader.setUpdatedBy(updateBy);
                    this.waveService.updateWaveHeader(waveHeader);
                    updateFlag = false;
                    //边拣边分模式
                    sortingService.compelSorting(waveHeader.getWaveNo(), updateBy);
                } else {
                    log.debug("modifyWave WaveStatus.ALLPICKED waveHeader = " + waveHeader.getId());
                    waveHeader.setWaveStatus(WaveStatus.ALLPICKED.getValue());
                }
            } else {
                waveHeader.setWaveStatus(WaveStatus.PARTPICKED.getValue());
            }
        }
        // 乐观锁防并发
        if (updateFlag) {
            waveHeader.setUpdatedAt(DateUtil.getNowTime());
            waveHeader.setUpdatedBy(updateBy);
            this.waveService.updateWaveHeader(waveHeader);
        }
    }

    /**
     * 改变订单头状态
     *
     * @param doHeader
     */
    private void changeDoHeaderStatus(DeliveryOrderHeader doHeader, String updateBy) {
        if (doHeader == null) {
            throw new DeliveryException(DeliveryException.DELIVERYORDER_CANNOT_PICK_DONOTEXIST);
        }
        log.debug("changeDoHeaderStatus doHeader = " + doHeader.getId());

        // 设置整件、零散状态
        String pcsStatus = pickTaskDAO.getDoPickStatus(doHeader.getId(), PackageType.B.getValue());
        String unitStatus = pickTaskDAO.getDoPickStatus(doHeader.getId(), PackageType.C.getValue());

        if (StringUtil.isEmpty(pcsStatus) || (pcsStatus.compareTo(StringUtil.defaultIfEmpty(doHeader.getPcsStatus(), "00")) > 0)) {
            doHeader.setPcsStatus(pcsStatus);
        }

        if (StringUtil.isEmpty(unitStatus) || unitStatus.compareTo(StringUtil.defaultIfEmpty(doHeader.getUnitStatus(), "00")) > 0) {
            doHeader.setUnitStatus(unitStatus);
        }
    
        if (StringUtil.isEmpty(doHeader.getPcsStatus()) && StringUtil.isEmpty(doHeader.getUnitStatus())) {
            doHeader.setStatus(DoStatus.ALLPICKED.getValue());
        } else {
            if (StringUtil.isEmpty(doHeader.getPcsStatus())) {
                doHeader.setStatus(doHeader.getUnitStatus());
            }
            if (StringUtil.isEmpty(doHeader.getUnitStatus())) {
                doHeader.setStatus(doHeader.getPcsStatus());
            }
        }
        
        //设置订单状态
        if (doHeader.getStatus().compareTo(DoStatus.ALLPICKED.getValue()) < 0) {
            //如果订单状态小于拣货完成，更新订单状态
            String pickStatus = pickTaskDAO.getDoPickStatus(doHeader.getId());
            doHeader.setStatus(pickStatus);
        }

        if (doHeader.getPickStartTime() == null) {//第一次更新DO

            doHeader.setPickStartTime(new Date());
            //开始拣货，记录日志
            orderLogService.saveLog(doHeader,
                    OrderLogConstants.OrderLogType.PICK_START.getValue(),
                    ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_PICK_START));
        }
        if (doHeader.getStatus().equals(DoStatus.ALLPICKED.getValue()) || isFinishedPick(pcsStatus, unitStatus)) {
            log.debug("changeDoHeaderStatus DoStatus.ALLPICKED do = " + doHeader.getId());
            doHeader.setPickEndTime(new Date());
            boolean hasLack = pickTaskDAO.isExistLackPickTask(doHeader.getId());
            //如果有缺货的拣货任务，订单需要增加缺货标识
            //调拨、RTV、批发订单，拣货缺货不打缺货标识
            if (hasLack && !Arrays.asList(DoType.RTV.getValue(), DoType.ALLOT.getValue(), DoType.WHOLESALE.getValue()).contains(doHeader.getDoType())) {
                doHeader.setLackStatus(YesNo.YES.getValue());
            }
            //记录日志，拣货完成
            orderLogService.saveLog(doHeader, OrderLogConstants.OrderLogType.PICK_COMPLETE.getValue(), ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_PICK_COMPLETE));
        }

        //乐观锁防并发
        doHeader.setUpdatedAt(DateUtil.getNowTime());
        doHeader.setUpdatedBy(updateBy);
        deliveryOrderService.updateDoHeader(doHeader);
        doHeaderDAO.getSession().flush();
    }

    private boolean isFinishedPick(String pcsStatus, String unitStatus) {
        if (StringUtil.isBlank(pcsStatus)) {
            return unitStatus.equals(DoStatus.ALLPICKED.getValue());
        }
        if (StringUtil.isBlank(unitStatus)) {
            return pcsStatus.equals(DoStatus.ALLPICKED.getValue());
        }
        return pcsStatus.equals(DoStatus.ALLPICKED.getValue()) && unitStatus.equals(DoStatus.ALLPICKED.getValue());
    }

    /**
     * 判断DO是否全部拣货完成
     *
     * @param doId
     * @return
     */
    private boolean isDoAllPicked(Long doId) {
        List<PickTask> list = pickTaskService.findPickTaskByDoId(doId);
        for (PickTask p : list) {
            if (!StringUtil.equals(DoStatus.ALLPICKED.getValue(), p.getStatus()) &&
                    !StringUtil.equals(DoStatus.CANCELED.getValue(), p.getStatus())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 改变订单明细状态
     *
     * @param pickTask
     * @param doDetail
     * @param pickedQty 本次拣货数量
     * @throws DeliveryException
     */
    private void changeDoDetailStatus(PickTask pickTask, DeliveryOrderDetail doDetail, BigDecimal pickedQty, BigDecimal realPickedQtyUnit, String updateBy) {
        if (doDetail == null) {
            throw new DeliveryException(DeliveryException.DELIVERYORDER_CANNOT_PICK_DODETAILNOTEXIST);
        }
        log.debug("changeDoDetailStatus doDetail = " + doDetail.getId());
        doDetailDAO.refresh(doDetail);

        Map<String, Long> map = pickTaskDAO.findPickTaskStatusCountByDoDetail(pickTask.getDoDetailId());
        Long totalCount = NumberUtil.coverNull(map.get("total"));
        Long allPickedCount = NumberUtil.coverNull(map.get(DoStatus.ALLPICKED.getValue()));
        Long canceledCount = NumberUtil.coverNull(map.get(DoStatus.CANCELED.getValue()));

        //更新订单明细状态
        if (totalCount.equals(allPickedCount + canceledCount)) {
            log.debug("changeDoDetailStatus DoStatus.PARTPICKED doDetail = " + doDetail.getId());
            doDetail.setLineStatus(DoStatus.ALLPICKED.getValue());
        } else {
            log.debug("changeDoDetailStatus DoStatus.PARTPICKED doDetail = " + doDetail.getId());
            doDetail.setLineStatus(DoStatus.PARTPICKED.getValue());
        }
        doDetail.setPickedQty(doDetail.getPickedQty().add(pickedQty));
        doDetail.setPickedQtyUnit(doDetail.getPickedQtyUnit().add(realPickedQtyUnit));
        doDetail.setPickQty(doDetail.getPickedQty());
        doDetail.setUpdatedAt(DateUtil.getNowTime());
        doDetail.setUpdatedBy(updateBy);
        deliveryOrderService.updateDoDetail(doDetail);
        doDetailDAO.getSession().flush();
    }

    /**
     * 根据pickTask更改库存
     *
     * @param task
     * @param pickedQty
     * @param pickedQty
     */
    @Loggable
    public Map<String, Long> changePickStock(PickTask task, BigDecimal pickedQty, BigDecimal pickedQtyUnit) {
        log.debug("changePickStock: task = " + task.getId() + " PlanQty = " + task.getQty() + " ActualQty = " + pickedQty);

        // 获取源库存
        Long fromStockId = task.getFmStockId();

        StockDTO stockDTO = new StockDTO();
        stockDTO.setFmStockId(fromStockId);
        stockDTO.setPlanQty(task.getQty());
        stockDTO.setPlanQtyUnit(task.getQtyUnit());
        stockDTO.setActualQty(pickedQty);
        stockDTO.setActualQtyUnit(pickedQtyUnit);
        stockDTO.setFmLocId(task.getLocId());
        stockDTO.setLotId(task.getLotId());
        stockDTO.setLpnNo(task.getLpnNo());
        stockDTO.setSkuId(task.getSkuId());
        pickOperator.setStockDto(stockDTO);
        Map<String, Long> map = stockService.operateStock(pickOperator);
        task.setToStockId(map.get(StockType.PICK_STAGE.getValue()));
        return map;
    }

    /**
     * 拣货回滚
     *
     * @param doHeader
     */
    @Transactional
    public void rollback(DeliveryOrderHeader doHeader) {
        Set<Long> pktHeaderIds = new HashSet<Long>();
        //将已拣货pickTask和未拣货pickTask分别存放起来
        List<PickTask> pickPickTasks = null;
        List<PickTask> unPickpickTasks = null;
        List<PickTask> pickTasks = doHeader.getPickTasks();
        if (!ListUtil.isNullOrEmpty(pickTasks)) {
            pickPickTasks = new ArrayList<PickTask>();
            unPickpickTasks = new ArrayList<PickTask>();
            for (PickTask pickTask : pickTasks) {
                Long pktHeaderId = pickTask.getPktHeaderId();
                if (pktHeaderId != null) {
                    pktHeaderIds.add(pktHeaderId);
                }
                String status = pickTask.getStatus();
                //拣货任务状态为拣货完成，并且不是缺货的，则需要返拣
                if (DoStatus.ALLPICKED.getValue().equals(status) && StockStatus.NORMAL.getValue().equals(pickTask.getStockStatus())) {
                    pickPickTasks.add(pickTask);
                } else if (DoStatus.ALLALLOCATED.getValue().equals(status) || (DoStatus.ALLPICKED.getValue().equals(status) && !StockStatus.NORMAL.getValue().equals(pickTask.getStockStatus()))) {
                    // 拣货任务状态为分配完成，或者状态为拣货完成且缺货，则不需要返拣
                    unPickpickTasks.add(pickTask);
                }
            }
        }
        //删除拣货任务并释放容器
        deliveryOrderService.unrelate(doHeader.getId(), true);

        //没有拣货pickTask取消分配
        cancelUnPickedPickTask(unPickpickTasks);

        // 已经拣货pickTask返拣
        createReverseTask(pickPickTasks, StringUtil.isNotBlank(doHeader.getHoldCode()) ? doHeader.getHoldCode() : doHeader.getFirstHoldCode());

        //判断pktHeader下是否有pickTask, 如果没有则删除该PktHeader
        modifyPktHeader(pktHeaderIds);

        String expStatus = doHeader.getExceptionStatus();
        // 如果没有返拣任务，则异常处理同分配完成时的状态回退
        if (ListUtil.isNullOrEmpty(pickPickTasks)) {
            if (Reason.CS_CANCLE.getValue().equals(doHeader.getHoldCode())) {
                expStatus = DoExpStatus.COMPLETE.getValue();
                // 如果是客服申请取消则状态回退时候将其取消
                doHeader.setStatus(DoStatus.CANCELED.getValue());
                doDetailDAO.updateDoDetailStatusByDoId(doHeader.getId(), DoStatus.CANCELED.getValue());
                //取消异步，改成定时任务
                //doCancelExpSrv.send(doHeader.getId(), true , null);

                doCancelExpSrv.createMsg(doHeader.getId(), "0", doHeader.getWarehouseId());

                //红冲电子发票
                electronicInvoiceService.writeBackInvoice(doHeader);
            } else {
                // 订单的状态更改为初始化
                doHeader.setStatus(DoStatus.INITIAL.getValue());
                expStatus = DoExpStatus.TO_BE_REPL.getValue();
                deliveryOrderService.doDoDetailBackToInit(doHeader.getId(), DoStatus.INITIAL.getValue());
            }
            doHeader.setExceptionStatus(expStatus);
        } else {
            // 订单的状态更改为初始化
            doHeader.setStatus(DoStatus.INITIAL.getValue());
            doHeader.setExceptionStatus(DoExpStatus.TO_BE_REPICK.getValue());
            // 发货单已拣货数量归零,分配数归零，状态变为初始化
            deliveryOrderService.doDoDetailBackToInit(doHeader.getId(), DoStatus.INITIAL.getValue());
        }

        //订单整散状态还原
        doHeader.setPcsStatus(null);
        doHeader.setUnitStatus(null);
        modifyDoHeaderAndWaveHeader(doHeader, true);

        //如果订单核拣装箱完成释放分拣筐
        if (Constants.YesNo.YES.getValue().equals(SystemConfig.getConfigValueInt("NEED_SORT_CONTAINER", ParamUtil.getCurrentWarehouseId()))) {
            containerMgntService.recheckReleaseContainer(doHeader.getDoNo());
        }

        if (!Reason.CS_CANCLE.getValue().equals(doHeader.getHoldCode())) {
            doAllocateService.sycDo2Alc(doHeader.getId());
        }
    }

    @Override
    @Transactional
    public int doRollBackPickTask(DeliveryOrderHeader doHeader) {
        int resault = 0;
        Set<Long> pktHeaderIds = new HashSet<Long>();
        Set<Long> doDetailIds = new HashSet<Long>();
        //将已拣货pickTask和未拣货pickTask分别存放起来
        List<PickTask> pickPickTasks = null;
        List<PickTask> unPickpickTasks = null;
        List<PickTask> pickTasks = doHeader.getPickTasks();
        if (!ListUtil.isNullOrEmpty(pickTasks)) {
            pickPickTasks = new ArrayList<PickTask>();
            unPickpickTasks = new ArrayList<PickTask>();
            for (PickTask pickTask : pickTasks) {
                Long pktHeaderId = pickTask.getPktHeaderId();
                if (pktHeaderId != null) {
                    pktHeaderIds.add(pktHeaderId);
                }
                doDetailIds.add(pickTask.getDoDetailId());
                String status = pickTask.getStatus();
                //拣货任务状态为拣货完成，并且不是缺货的，则需要返拣
                if (DoStatus.ALLPICKED.getValue().equals(status)
                        && StockStatus.NORMAL.getValue().equals(pickTask.getStockStatus())) {
                    pickPickTasks.add(pickTask);
                } else if (DoStatus.ALLALLOCATED.getValue().equals(status)
                        || (DoStatus.ALLPICKED.getValue().equals(status)
                        && !StockStatus.NORMAL.getValue().equals(pickTask.getStockStatus()))) {// 拣货任务状态为分配完成，或者状态为拣货完成且缺货，则不需要返拣
                    unPickpickTasks.add(pickTask);
                    //未真正拣货的直接删除
                    pickTaskDAO.deleteById(pickTask.getId());
                }
            }
        }
        //没有拣货pickTask取消分配
        cancelUnPickedPickTask(unPickpickTasks);

        //判断pktHeader下是否有pickTask, 如果没有则删除该PktHeader
        modifyPktHeader(pktHeaderIds);
        boolean removeDoFlag = false;
        if (ListUtil.isNullOrEmpty(pickPickTasks)) {
            //DO无真正拣货直接取消
            doHeader.setStatus(Constants.DoStatus.CANCELED.getValue());
            doHeader.setExceptionStatus(DoExpStatus.COMPLETE.getValue());
            doHeader.setLackStatus(Constants.YesNo.NO.getValue());
            doDetailDAO.updateDoDetailStatusByDoId(doHeader.getId(), Constants.DoStatus.CANCELED.getValue());
            //如果订单核拣装箱完成释放分拣筐
            if (Constants.YesNo.YES.getValue().equals(SystemConfig.getConfigValueInt("NEED_SORT_CONTAINER", ParamUtil.getCurrentWarehouseId()))) {
                containerMgntService.recheckReleaseContainer(doHeader.getDoNo());
            }

            //红冲电子发票
            electronicInvoiceService.writeBackInvoice(doHeader);

            removeDoFlag = true;
            resault = 1;
        } else {
            // 订单的状态更改为拣货完成
            doHeader.setStatus(Constants.DoStatus.ALLPICKED.getValue());
            doHeader.setExceptionStatus(DoExpStatus.TO_BE_ROLLBACK.getValue());
            doHeader.setLackStatus(Constants.YesNo.NO.getValue());
            // 发货单明细分配数=拣货数 状态为拣货完成
            for (Long doDetailId : doDetailIds) {
                BigDecimal pickedQty = pickTaskDAO.getDoDetailPickedCount(doDetailId);
                this.deliveryOrderService.updateDoDetailStatusAndQty(doDetailId, Constants.DoStatus.ALLPICKED.getValue(), pickedQty);
            }
            removeDoFlag = false;
            resault = 0;
        }

        modifyDoHeaderAndWaveHeader(doHeader, removeDoFlag);
        return resault;
    }

    /**
     * 回滚
     */
    @Override
    @Transactional
    public void rollback(Long doId, String startStatus, String fromStatus, String toStatus) throws DeliveryException {
        boolean isFromStatusMatch = fromStatus.equals(Constants.DoStatus.PARTPICKED.getValue()) || fromStatus.equals(Constants.DoStatus.ALLPICKED.getValue());
        if (!isFromStatusMatch) {
            return;
        }
        if (!toStatus.equals(Constants.DoStatus.INITIAL.getValue())) {
            return;
        }
        this.doHeaderDAO.getSession().flush();
        this.doHeaderDAO.getSession().clear();
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(doId);
        validateToRollback(doHeader);

        rollback(doHeader);

        //写do异常日志
        DoExStatusOpDto doExStatusOpDto = new DoExStatusOpDto();
        doExStatusOpDto.setExceptionStatus(DoExpStatus.TO_BE_ROLLBACK.getValue());
        doExStatusOpDto.setReleaseStatus(ReleaseStatus.HOLD.getValue());
        doExStatusOpDto.setStatus(startStatus);
        doExStatusOpDto.setOpType(Constants.ExOpType.ROLL_BACK.getValue());
        doExceptionLogService.saveDoExceptionLog(doExStatusOpDto, doHeader);
    }

    /**
     * 修改do单头和波次
     */
    @Override
    @Transactional
    public void modifyDoHeaderAndWaveHeader(DeliveryOrderHeader doHeader, boolean removeDo) {
        WaveHeader waveHeader = doHeader.getWaveHeader();
        // 去除缺货标识
        doHeader.setLackStatus(YesNo.NO.getValue());
        if (removeDo) {
            // 取消DO与波次关系
            doHeader.setWaveId(null);
            // 波次更改为未跑波次
            doHeader.setWaveFlag(Constants.YesNo.NO.getValue());
            doHeader.setSortGridNo(null);
        }
        if (waveHeader != null) {
            Long waveId = waveHeader.getId();

            // 更新波次发票信息
            deliveryOrderService.updateWaveInvoiceInfo(waveHeader, waveHeader.getDoHeaders(), doHeader.getId());

            //重新设置波次的拣货单数量
            Long count = pickHeaderService.findPktHeaderCount(waveId);
            waveHeader.setPktCount(count);

            //判断doHeader所属的波次下是否有doHeader, 如果没有则删除该波次
            int doCount = this.doHeaderDAO.countSortingNumberByWaveId(waveId);
            waveHeader.setDoCount(doCount);
            if (doCount == 0) {
                //如果波次被删除则释放容器(拣货箱子)
                containerMgntService.releaseContainerByWave(waveHeader,
                        Constants.ContainerType.WAVE_CONTAINER.getValue(), Constants.BindDocType.WAVE.getValue());
                this.waveService.removeWaveHeader(waveHeader);
            } else if (WaveStatus.PARTPICKED.getValue().equals(waveHeader.getWaveStatus())) {
                int allPickedDoCount = doHeaderDAO.countUnSortingDoHeader(waveId, new String[]{Constants.DoStatus.ALLPICKED.getValue()});
                int allAlloctedDoCount = doHeaderDAO.countUnSortingDoHeader(waveId, new String[]{Constants.DoStatus.ALLALLOCATED.getValue()});
                if (allPickedDoCount == doCount) {
                    waveHeader.setWaveStatus(Constants.DoStatus.ALLPICKED.getValue().toString());
                } else if (allAlloctedDoCount == doCount) {
                    waveHeader.setWaveStatus(Constants.DoStatus.ALLALLOCATED.getValue().toString());
                } else {
                    waveHeader.setWaveStatus(Constants.DoStatus.PARTPICKED.getValue().toString());
                }
                this.waveService.updateWaveHeader(waveHeader);
            } else if (WaveStatus.ALLPICKED.getValue().equals(waveHeader.getWaveStatus())) {
                //正常流程在拣货时候不会处理冻结DO，要到分拣处处理
                this.waveService.updateWaveHeader(waveHeader);
            } else if (WaveStatus.PARTSORTED.getValue().equals(waveHeader.getWaveStatus())) {
                int allSortedDoCount = doHeaderDAO.countUnSortingDoHeader(waveId, new String[]{DoStatus.ALLSORTED.getValue(), DoStatus.PART_CARTON.getValue(), DoStatus.ALL_CARTON.getValue(),
                        DoStatus.PART_LOAD.getValue(), DoStatus.ALL_LOAD.getValue(), DoStatus.LOAD_LOCKED.getValue(), DoStatus.ALL_DELIVER.getValue()});
                int allPickedDoCount = doHeaderDAO.countUnSortingDoHeader(waveId, new String[]{DoStatus.ALLPICKED.getValue()});
                if (allSortedDoCount == doCount) {
                    waveHeader.setWaveStatus(Constants.DoStatus.ALLSORTED.getValue().toString());
                } else if (allPickedDoCount == doCount) {
                    waveHeader.setWaveStatus(Constants.DoStatus.ALLPICKED.getValue().toString());
                } else {
                    waveHeader.setWaveStatus(Constants.DoStatus.PARTSORTED.getValue().toString());
                }
                this.waveService.updateWaveHeader(waveHeader);
            }
        }
        deliveryOrderService.updateDoHeader(doHeader);
    }

    /**
     * 修改拣货单头
     *
     * @param pktHeaderIds
     */
    private void modifyPktHeader(Set<Long> pktHeaderIds) {
        if (null != pktHeaderIds && pktHeaderIds.size() > 0) {
            List<PickHeader> pktHeaderList = this.pickDAO.findPickHeaderByIds(pktHeaderIds);
            for (PickHeader pktHeader : pktHeaderList) {
                pickHeaderService.changePktHeaderStatus(pktHeader, false, pktHeader.getPickBy());
            }
        }
    }

    /**
     * 创建反拣任务
     *
     * @param pickTasks
     * @param reversePickReason
     */
    private void createReverseTask(List<PickTask> pickTasks, String reversePickReason) {
        if (!ListUtil.isNullOrEmpty(pickTasks)) {
            List<Long> toDeleteTaskIds = new ArrayList<Long>(pickTasks.size());
            for (PickTask pickTask : pickTasks) {
                StockDTO stockDTO = new StockDTO();
                stockDTO.setFmStockId(pickTask.getToStockId());
                stockDTO.setPlanQty(pickTask.getQty());
                stockDTO.setPlanQtyUnit(pickTask.getQtyUnit());
                stockDTO.setActualQty(pickTask.getQty());
                stockDTO.setActualQtyUnit(pickTask.getQtyUnit());
                stockDTO.setFmLocId(pickTask.getLocId());
                stockDTO.setLotId(pickTask.getLotId());
                stockDTO.setLpnNo(pickTask.getLpnNo());
                stockDTO.setSkuId(pickTask.getSkuId());
                pickOperator.setStockDto(stockDTO);
                Map<String, Long> map = stockService.undo(pickOperator);
                // 生成反拣任务(从pickTask的拣货目标库位到退货区)
                createReverseTrs(pickTask, map, reversePickReason);
                toDeleteTaskIds.add(pickTask.getId());
            }
            rePickContainerService.bind4Pick(toDeleteTaskIds);

            pickTaskDAO.logicDeleteByIds(toDeleteTaskIds);
        }
    }

    /**
     * 取消未拣货的任务
     *
     * @param unPickPickTasks
     */
    private void cancelUnPickedPickTask(List<PickTask> unPickPickTasks) {
        if (!ListUtil.isNullOrEmpty(unPickPickTasks)) {
            for (PickTask pickTask : unPickPickTasks) {
                if (pickTask.getFmStockId() != null) {
                    OrderStockDTO stockDTO = new OrderStockDTO();
                    stockDTO.setFmStockId(pickTask.getFmStockId());
                    stockDTO.setPlanQty(pickTask.getQty());
                    stockDTO.setPlanQtyUnit(pickTask.getQtyUnit());
                    stockDTO.setActualQty(pickTask.getQty());
                    stockDTO.setFmLocId(pickTask.getLocId());
                    stockDTO.setLotId(pickTask.getLotId());
                    stockDTO.setLpnNo(pickTask.getLpnNo());
                    stockDTO.setSkuId(pickTask.getSkuId());
                    stockDTO.setAllocatingId(pickTask.getAllocatingId());
                    allocateOperator.setStockDto(stockDTO);
                    stockService.undo(allocateOperator);
                }
            }
        }
    }

    /**
     * <pre>
     * Description:检查拣货单是否可以回退，如果存在且状态为冻结则可以，否则抛出异常
     * </pre>
     *
     * @param doHeader
     * @return
     * @throws OrderFrozenException
     */
    private void validateToRollback(DeliveryOrderHeader doHeader) throws DeliveryException {
        if (doHeader == null) {
            throw new DeliveryException(DeliveryException.CAN_NOT_ROLLBACK_NOT_EXIST);
        }
        if (!Constants.ReleaseStatus.HOLD.getValue().equals(doHeader.getReleaseStatus())) {
            throw new DeliveryException(DeliveryException.CAN_NOT_ROLLBACK_NOT_HOLD);
        }
    }

    /**
     * <pre>
     * Description:生成反拣任务
     * </pre>
     *
     * @param pickTask          拣货明细
     * @param reversePickReason
     */
    private void createReverseTrs(PickTask pickTask, Map<String, Long> map, String reversePickReason) throws DeliveryException {
        ReversePickTask reversePickTask = new ReversePickTask();
        reversePickTask.setTaskNo(sequenceGeneratorService.generateSequenceNo(Constants.SequenceName.RK_TASKNO.getValue(), ParamUtil.getCurrentWarehouseId()));
        reversePickTask.setTaskStatus(Constants.TaskStatus.INITIALIZED.getValue());
        reversePickTask.setTaskType(Constants.TaskType.RK.getValue());
        //设置包装明细主键
        reversePickTask.setPackageDetailId(pickTask.getPackDetailId());
        reversePickTask.setSkuId(pickTask.getSkuId());
        reversePickTask.setLotId(pickTask.getLotId());
        reversePickTask.setLpnNo(pickTask.getToLpnNo());
        reversePickTask.setToLpnNo(pickTask.getLpnNo());
        //设置和do的关联
        reversePickTask.setDocId(pickTask.getDoHeaderId());
        reversePickTask.setDocNo(pickTask.getDoHeader().getDoNo());

        //反拣任务的源库位为拣货明细的toLoc
        reversePickTask.setFmLocId(pickTask.getToLocId());

        reversePickTask.setPlanLocId(pickTask.getLocId());

        reversePickTask.setDocType(DocType.SO.getValue());
        reversePickTask.setDocLineId(pickTask.getDoDetailId());
        reversePickTask.setPktNo(pickTask.getId());

        reversePickTask.setPackId(pickTask.getPackId());
        reversePickTask.setUom(BatchPropertyUtil.getUom(pickTask.getSku()));
        //设置任务拣货数量为pickTask的已捡数量
        reversePickTask.setQty(pickTask.getPickedQty());
        reversePickTask.setQtyUnit(pickTask.getQtyPickedUnit());
        //设置任务拣货数量EA为pickTask的已捡数量
//        reversePickTask.setQtyEach(pickTask.getQtyPickedEach());

        reversePickTask.setFmStockId(map.get(StockType.STOCK_PENDING.getValue()));
        reversePickTask.setToStockId(map.get(StockType.STOCK.getValue()));
        reversePickTask.setUserdefine02(reversePickReason);
        trsTaskDAO.save(reversePickTask);
    }

    /**
     * <pre>
     * Description:生成拣货交易
     * </pre>
     */
    private void createPickTrs(PickTask pickTask, String pickerNo, BigDecimal pickedQty, BigDecimal pickedQtyUnit) throws DeliveryException {
        if (pickTask == null) {
            throw new DeliveryException(DeliveryException.PICK_ERROR_TASK_NOT_EXIST);
        }
        log.debug("createPickTrs pickTask = " + pickTask.getId());
        StockBatchAtt stockBatchAtt = stockService.queryStockBatchAttById(pickTask.getLotId());
        if (stockBatchAtt == null) {
            throw new DeliveryException(DeliveryException.DELIVERYORDER_CANNOT_PICK_SUPNOTEXIST);
        }
        TrsPickLog trsPickLog = new TrsPickLog();
        trsPickLog.setOptFlag(pickTask.getOptFlag());
        trsPickLog.setTaskId(pickTask.getId());
        trsPickLog.setWaveId(pickTask.getWaveHeaderId());
        trsPickLog.setDocId(pickTask.getDoHeaderId());
        trsPickLog.setDocNo(pickTask.getDoHeader().getDoNo());
        trsPickLog.setDocLineId(pickTask.getDoDetailId());
        trsPickLog.setDocType(Constants.DocType.SO.getValue());
        trsPickLog.setTransactionType(Constants.TrsType.PK.getValue());
        //供应商From、供应商To
        String supplierIdStr = stockBatchAtt.getLotatt04();
        if (StringUtil.isEmpty(supplierIdStr)) {
            trsPickLog.setFmSupplierId(null);
            trsPickLog.setToSupplierId(null);
        } else {
            Long supplierId = Long.parseLong(supplierIdStr);
            trsPickLog.setFmSupplierId(supplierId);
            trsPickLog.setToSupplierId(supplierId);
        }
        //SKU From、SKU  To
        Long skuId = pickTask.getSkuId();
        trsPickLog.setFmSkuId(skuId);
        trsPickLog.setToSkuId(skuId);
        //FmLoc、ToLocId
        trsPickLog.setFmLocId(pickTask.getLocId());
        trsPickLog.setToLocId(pickTask.getToLocId());
        //FmLot 、ToLot
        Long lotId = pickTask.getLotId();
        trsPickLog.setFmLotId(lotId);
        trsPickLog.setToLotId(lotId);
        //FM_LPN_NO 、TO_LPN_NO
        trsPickLog.setFmLpnNo(pickTask.getLpnNo());
        trsPickLog.setToLpnNo(pickTask.getToLpnNo());
        //FM_QTY 、TO_QTY
        trsPickLog.setFmQty(pickTask.getQty());
        trsPickLog.setToQty(pickedQty);
        trsPickLog.setFmQtyUnit(pickTask.getQtyUnit());
        trsPickLog.setToQtyUnit(pickedQtyUnit);
        //PACK_ID
        trsPickLog.setPackId(pickTask.getPackId());
        //FM_PACK_DETAIL_ID、TO_PACK_DETAIL_ID
        Long packDetailId = pickTask.getPackDetailId();
        trsPickLog.setFmPackDetailId(packDetailId);
        trsPickLog.setToPackDetailId(packDetailId);
        //FM_UOM_QTY、TO_UOM_QTY
        PackageInfoDetail packDetail = packageInfoDetailService.get(packDetailId);
        BigDecimal uomQty = packDetail.getQty();
        trsPickLog.setToUomQty(uomQty);
        trsPickLog.setFmUomQty(uomQty);

        Long zero = Constants.YesNo.NO.getValue().longValue();
        trsPickLog.setIsDamage(zero);
        trsPickLog.setIsInverse(zero);
        trsPickLog.setOperationId(pickerNo);

        Identity identity = (Identity) Component.getInstance("org.jboss.seam.security.identity");
        String login = identity.getCredentials().getUsername();
        if (StringUtil.isEmpty(login)) {
            login = pickerNo;
        }
        trsPickLog.setCreatedBy(login);
        trsPickLog.setUpdatedBy(login);
        trsPickLog.setOperSource(ParamUtil.getCurrentDevice());    // 设置操作来源(WMS/RF)
        //商家id库存回写用
        trsPickLog.setMerchantId(StringUtil.isEmpty(stockBatchAtt.getLotatt06()) ? null : Long.parseLong(stockBatchAtt.getLotatt06()));

        trsPickLogService.save(trsPickLog);
    }

    @Override
    @Transactional
    public void doHandlePick(Long doId) {
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(doId);
        if (null == doHeader) {
            log.debug("AutoPickHandler.handle.doIsNull", doId);
            return;
        }
        if (StringUtil.isIn(doHeader.getStatus(),
                Constants.DoStatus.ALLALLOCATED.getValue(), Constants.DoStatus.PARTPICKED.getValue())) {
            List<String> pktNos = pickHeaderService.queryPktNoInDosWave(doId, Constants.PktStatus.RELEASED.getValue());
            log.debug("do pkts", pktNos);

            if (ListUtil.isNullOrEmpty(pktNos)) {
                return;
            }
            String pickWho = pickDAO.getOperateUser();
            Boolean autoSort = Config.isDefaultFalse(Keys.Delivery.sort_isWithPick, Config.ConfigLevel.WAREHOUSE);
            for (String pktNo : pktNos) {
                log.debug("do pick pkt", pktNo);
                pick(pktNo, pickWho, autoSort);
            }
        } else {
            log.debug("do not need pick", doHeader.getStatus());
        }
    }

    @Override
    public PickTask queryMaxCountByDetail(Long doDetailId) {
        PickTask task = pickTaskDAO.queryMaxCountByDetail(doDetailId);
        if (null == task) {
            throw new DeliveryException(DeliveryException.DODETAIL_HAS_NO_PICKTASK);
        }
        return task;
    }

    @Override
    public boolean isAllLackDo(DeliveryOrderHeader doHeader) {
        boolean result = false;
        if (StringUtil.isIn(doHeader.getStatus(),
                DoStatus.ALLPICKED.getValue(), DoStatus.ALLSORTED.getValue())
                && !pickTaskDAO.isExistNotLackPickTask(doHeader.getId())) {
            // 全部缺货
            result = true;
        }
        return result;
    }

    /**
     * 处理删除缺货明细的拣货任务
     */
    @Override
    @Transactional
    public boolean rollBackLackDoPickTask(DeliveryOrderHeader doHeader) throws DeliveryException {
        if (StringUtil.isIn(doHeader.getHoldCode(), Constants.Reason.PICK_LACK.getValue(),
                Constants.Reason.PICK_DAMAGE.getValue())) {
            //拣货缺货
            doPickDelete(doHeader.getId(), true);
            return false;
        } else {
            //处理拣货缺，拣货破损 货缺货明细
            doPickDelete(doHeader.getId(), false);
            pickTaskDAO.getSession().flush();

            //核拣缺货，分拣缺货
            List<DoLackDetail> doLackDetails = doLackDetailDAO.findDoLackDetail(doHeader.getId());
            if (ListUtil.isNullOrEmpty(doLackDetails)) {
                throw new DeliveryException(DeliveryException.DO_HAS_NO_LACKDETAIL);
            }
            Map<Long, BigDecimal> mergeDetailMap = mergeByDetail(doLackDetails);
            List<PickTask> toRePickTaskList = new ArrayList<PickTask>();
            for (Entry<Long, BigDecimal> detailEntry : mergeDetailMap.entrySet()) {
                List<PickTask> allPickTasks = pickTaskDAO.queryByDoDetail(detailEntry.getKey());
                operateLackPickTask(detailEntry.getValue(), allPickTasks, toRePickTaskList);
            }
            if (ListUtil.isNullOrEmpty(toRePickTaskList)) {
                return false;
            }
            createReverseTask(toRePickTaskList, StringUtil.isNotBlank(doHeader.getHoldCode()) ? doHeader.getHoldCode() : doHeader.getFirstHoldCode());

        }
        return true;
    }

    @Override
    public List<Object[]> getPickSortDetails(List<Long> pickTaskList) {
        return pickTaskDAO.getPickSortDetails(pickTaskList);
    }

    @Override
    public List<Object[]> getPickSortDetails(String docNum, Integer docType, Long skuId) {
        return pickTaskDAO.getPickSortDetails(docNum, docType, skuId);
    }

    /**
     * 按照发货明细(SKU)汇总缺货数 过滤拣货缺货的订单
     *
     * @param lackList
     * @return
     */
    private Map<Long, BigDecimal> mergeByDetail(List<DoLackDetail> lackList) {
        Map<Long, BigDecimal> detailMap = new HashMap<Long, BigDecimal>();
        for (DoLackDetail detail : lackList) {
            if (StringUtil.isIn(detail.getHoldReason(), "拣货缺货", "拣货破损")) {
                continue;
            }
            Long key = detail.getDoDetailId();
            BigDecimal currentQty = detail.getQty();
            BigDecimal value = detailMap.get(key);
            if (null == value) {
                detailMap.put(key, currentQty);
            } else {
                detailMap.put(key, value.add(currentQty));
            }
        }
        return detailMap;
    }


    /**
     * 查询缺货明细的拣货任务按拣货数倒序排序 并迭代
     * 如果小于缺货数则需要返拣，大于则将原任务数更新并拆分出一条供返拣
     *
     * @param lackQty
     * @param allPickTasks
     * @param toRepickTasks
     */
    private void operateLackPickTask(BigDecimal lackQty, List<PickTask> allPickTasks, List<PickTask> toRepickTasks) {
        for (int i = 0; i < allPickTasks.size() && lackQty.compareTo(BigDecimal.ZERO) > 0; i++) {
            PickTask pickTask = allPickTasks.get(i);
            BigDecimal packageQty = pickTask.getPackageInfoDetail().getQty();
            if (pickTask.getQty().compareTo(lackQty) <= 0) {
                toRepickTasks.add(pickTask);
                lackQty = lackQty.subtract(pickTask.getQty());
            } else {
                StockDTO stockDTO = new StockDTO();
                stockDTO.setFmStockId(pickTask.getFmStockId());
                stockDTO.setPlanQty(pickTask.getQty());
                stockDTO.setPackQty(pickTask.getQtyUnit());
                stockDTO.setActualQty(lackQty);
                stockDTO.setActualQtyUnit(lackQty.divide(packageQty));
                stockDTO.setToStockId(pickTask.getToStockId());
                lackOperator.setStockDto(stockDTO);
                Map<String, Long> map = stockService.undo(lackOperator);
                PickTask lackPickTask = updateLackPickTask(map, pickTask, lackQty);
                toRepickTasks.add(lackPickTask);
                break;//遇到大于缺货数的则拆分
            }
        }
    }

    /**
     * 更新原拣货任务数 为 原拣货数 - 缺货数
     * 创建新拆分的拣货任务后面生成返拣
     *
     * @param map
     * @param oriPickTask
     * @param lackQty
     * @return
     */
    private PickTask updateLackPickTask(Map<String, Long> map, PickTask oriPickTask, BigDecimal lackQty) {
        //更新原拣货任务
        oriPickTask.setQty(oriPickTask.getQty().subtract(lackQty));//设置拣货数为 原拣货数 - 缺货数
        BigDecimal packageQty = oriPickTask.getPackageInfoDetail().getQty();
        oriPickTask.setQtyUnit(oriPickTask.getQty().divide(packageQty, 0, BigDecimal.ROUND_DOWN));
//    	oriPickTask.setQty(oriPickTask.getQty());
        oriPickTask.setPickedQty(oriPickTask.getQty());
        pickTaskDAO.update(oriPickTask);

        //创建新拆分的要生成返拣的拣货任务
        Long lackPickStockId = map.get(StockType.PICK_STAGE.getValue());
        //Long lackAllocStockId = map.get(StockType.STOCK_ALLOC.getValue());
        PickTask lackPickTask = new PickTask();
        lackPickTask.setDoDetailId(oriPickTask.getDoDetailId());
        lackPickTask.setDoHeaderId(oriPickTask.getDoHeaderId());
        //lackPickTask.setFmStockId(lackPickStockId);//新拆分出来的拣货临时库存
        lackPickTask.setLocId(oriPickTask.getLocId());
        lackPickTask.setLotId(oriPickTask.getLotId());
        lackPickTask.setLpnNo(oriPickTask.getLpnNo());
        lackPickTask.setNotes("删除缺货明细拆分");
        lackPickTask.setPackDetailId(oriPickTask.getPackDetailId());
        lackPickTask.setPackId(oriPickTask.getPackId());
        lackPickTask.setPktHeaderId(oriPickTask.getPktHeaderId());
        lackPickTask.setDoHeader(oriPickTask.getDoHeader());
        lackPickTask.setQty(lackQty);//缺货数量
        lackPickTask.setQtyUnit(lackQty.divide(packageQty));
//    	lackPickTask.setQtyEach(lackQty);
        lackPickTask.setPickedQty(lackQty);
        lackPickTask.setSkuId(oriPickTask.getSkuId());
        lackPickTask.setStatus(oriPickTask.getStatus());
        lackPickTask.setToLocId(oriPickTask.getToLocId());
        lackPickTask.setToLpnNo(oriPickTask.getToLpnNo());
        lackPickTask.setToStockId(lackPickStockId);//新拆分出来的分配临时库存
        lackPickTask.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        lackPickTask.setWaveHeaderId(oriPickTask.getWaveHeaderId());
        String creater = pickTaskDAO.getOperateUser();
        lackPickTask.setCreatedBy(creater);
        lackPickTask.setUpdatedBy(creater);
        pickTaskDAO.save(lackPickTask);
        return lackPickTask;
    }

    /**
     * 处理拣货缺货 拣货破损的缺货明细
     * 拣货缺货的订单将没有拣货pickTask取消分配
     *
     * @param doHeaderId
     */
    private void doPickDelete(Long doHeaderId, boolean mustLack) {
        List<String> stockParams = new ArrayList<String>(1);
        stockParams.add(Constants.StockStatus.LACK.getValue());
        stockParams.add(Constants.StockStatus.BROKEN.getValue());
        List<PickTask> lackPickTasks = pickTaskDAO.getPickTasksByDoIdAndStockStatus(doHeaderId, stockParams);
        if (mustLack && ListUtil.isNullOrEmpty(lackPickTasks)) {
            throw new DeliveryException(DeliveryException.DO_HAS_NO_LACKDETAIL);
        }
        if (ListUtil.isNotEmpty(lackPickTasks)) {
//            if (mustLack) {
//                throw new DeliveryException(DeliveryException.DO_HAS_NO_LACKDETAIL);
//            }
            //没有拣货pickTask取消分配
            cancelUnPickedPickTask(lackPickTasks);
            List<Long> pickTaskIds = new ArrayList<Long>();
            for (PickTask temp : lackPickTasks) {
                pickTaskIds.add(temp.getId());
            }
            //删除缺货商品的拣货任务
            pickTaskDAO.logicDeleteByIds(pickTaskIds);
        }
    }

    // 订单缺货拣货
    @Transactional
    @Override
    public void completLackPick(Long doId) {
        DeliveryOrderHeader doHeader = deliveryOrderService.getDoHeaderById(doId);
        if (StringUtil.isNotIn(doHeader.getStatus(), DoStatus.ALLPICKED.getValue(), DoStatus.ALLSORTED.getValue())) {
            throw new DeliveryException(DeliveryException.DO_STATUS_ERROR);
        }
        if (!DoType.WHOLESALE.getValue().equals(doHeader.getDoType())) {
            throw new DeliveryException(DeliveryException.DO_TYPE_ERROR);
        }
        List<PickTask> pickTaskList = pickTaskDAO.findLackPickTasksByDoId(doHeader.getId());
        if (ListUtil.isNullOrEmpty(pickTaskList)) {
            throw new DeliveryException(DeliveryException.DO_HAS_NOT_LACK);
        }
        //已移出波次；
        if (doHeader.getWaveId() == null) {
            throw new DeliveryException(DeliveryException.DO_NOT_IN_WAVE);
        }
        if (YesNo.YES.getValue().equals(doHeader.getNeedCancel())) {
            throw new DeliveryException(DeliveryException.DO_NEED_CANCEL);
        }
        // 不是缺货状态
        if (!YesNo.YES.getValue().equals(doHeader.getLackStatus())) {
            throw new DeliveryException(DeliveryException.EXCEPTION_FORCE_PICK_NOT_LACK, doHeader.getDoNo());
        }

        DoExStatusOpDto doExStatusOpDto = new DoExStatusOpDto();
        doExStatusOpDto.setReleaseStatus(doHeader.getReleaseStatus());
        doExStatusOpDto.setStatus(doHeader.getStatus());
        doExStatusOpDto.setExceptionStatus(doHeader.getExceptionStatus());
        doExStatusOpDto.setOpType(Constants.ExOpType.COMPLETE_LACK_PICK.getValue());

        String updateBy = pickTaskDAO.getOperateUser();

        Set<Long> pktHeaderIdList = new HashSet<Long>();
        // 更新拣货任务
        for (PickTask pickTask : pickTaskList) {
            pktHeaderIdList.add(pickTask.getPktHeaderId());

            //还原库存
            OrderStockDTO stockDTO = new OrderStockDTO();
            stockDTO.setFmStockId(pickTask.getFmStockId());
            stockDTO.setPlanQty(pickTask.getQty());
            stockDTO.setPlanQtyUnit(pickTask.getQtyUnit());
            stockDTO.setActualQty(pickTask.getQty());
            stockDTO.setFmLocId(pickTask.getLocId());
            stockDTO.setLotId(pickTask.getLotId());
            stockDTO.setLpnNo(pickTask.getLpnNo());
            stockDTO.setSkuId(pickTask.getSkuId());
            stockDTO.setAllocatingId(pickTask.getAllocatingId());
            allocateOperator.setStockDto(stockDTO);
            stockService.undo(allocateOperator);

            //更新拣货任务
            pickTask.setIsDeleted(YesNo.YES.getValue());
            pickTask.setStatus(DoStatus.CANCELED.getValue());
            pickTask.setNotes("to canceled");
            pickTask.setPickWho(updateBy);
            pickTask.setToLpnNo(DeliveryConstant.LPN_DEFAULT_NO);
            pickTask.setPickedQty(BigDecimal.ZERO);
            pickTask.setQtyPickedUnit(BigDecimal.ZERO);
            //pickTask.setStockStatus(StockStatus.NORMAL.getValue());
            pickTaskDAO.update(pickTask);
            //产生交易日志
            //createPickTrs(pickTask, updateBy, pickedQty, pickedQty);
        }
        //更新拣货单
        for (Long pktHeaderId : pktHeaderIdList) {
            pickHeaderService.changePktHeaderStatus(pickHeaderService.getPktHeader(pktHeaderId), true, updateBy);
        }

        // 更新订单明细
        String fromDoStatus = doHeader.getStatus();
        boolean needUpdateWave = false;
        // 如果是分配完成或者部分拣货，则要更新订单状态为拣货完成，更新波次状态
        if (StringUtil.isIn(fromDoStatus, DoStatus.ALLALLOCATED.getValue(), DoStatus.PARTPICKED.getValue())) {
            log.debug("updateDo4ForcePick needUpdateWave = true do = " + doHeader.getId());
            needUpdateWave = true;
            doDetailDAO.updateDoDetailStatusByDoId(doHeader.getId(), DoStatus.ALLPICKED.getValue());
            String pcsStatus = pickTaskDAO.getDoPickStatus(doHeader.getId(), PackageType.B.getValue());
            String unitStatus = pickTaskDAO.getDoPickStatus(doHeader.getId(), PackageType.C.getValue());
            // 设置零散状态
            if (StringUtil.isNotEmpty(pcsStatus)) {
                if (StringUtil.isEmpty(doHeader.getPcsStatus()) || (pcsStatus.compareTo(doHeader.getPcsStatus()) > 0)) {
                    doHeader.setPcsStatus(pcsStatus);
                }

            }
            //设置整件状态
            if (StringUtil.isNotEmpty(unitStatus)) {
                if (StringUtil.isEmpty(doHeader.getUnitStatus()) || unitStatus.compareTo(doHeader.getUnitStatus()) > 0) {
                    doHeader.setUnitStatus(unitStatus);
                }
            }
            //设置订单状态
            if (StringUtil.isBlank(pcsStatus)) {
                doHeader.setStatus(doHeader.getUnitStatus());
            } else if (StringUtil.isBlank(unitStatus)) {
                doHeader.setStatus(doHeader.getPcsStatus());
            } else if (StringUtil.notNullString(doHeader.getPcsStatus(), "0").compareTo(StringUtil.notNullString(doHeader.getUnitStatus(), "0")) > 0) {
                doHeader.setStatus(doHeader.getPcsStatus());
            } else {
                doHeader.setStatus(doHeader.getUnitStatus());
            }
            if (doHeader.getPickStartTime() == null) {
                doHeader.setPickStartTime(DateUtil.getNowTime());
            }
            doHeader.setPickEndTime(DateUtil.getNowTime());
        } else {
            log.debug("updateDo4ForcePick needUpdateWave = false do = " + doHeader.getId());
            //doDetailDAO.updateDoDetailsQtyAllPicked(doHeader.getId());
        }

        doHeader.setReleaseStatus(ReleaseStatus.RELEASE.getValue());
        doHeader.setExceptionStatus(DoExpStatus.COMPLETE.getValue());
        doHeader.setLackStatus(YesNo.NO.getValue());
        doHeaderDAO.update(doHeader);
    
        if (needUpdateWave) {
            // 更新波次
            Boolean autoSort = Config.isDefaultFalse(Keys.Delivery.sort_isWithPick, Config.ConfigLevel.WAREHOUSE);
            modifyWave(doHeader.getWaveHeader(), updateBy, false, autoSort);
        }

        Events.instance().raiseEvent("DELETE_FIND_LACK_EVENT", doId);
        // 记录异常日志
        doExceptionLogService.saveDoExceptionLog(doExStatusOpDto, doHeader);
    }

    /**
     * 修改已经捡货的捡货数量
     *
     * @param
     */
    @Override
    @Transactional
    public void updatePickQty(PickTask pickTask, BigDecimal recheckQtyUnit, String reversePickReason) {
        Set<Long> pktHeaderIds = new HashSet<Long>();
        //将已拣货pickTask和未拣货pickTask分别存放起来
        List<PickTask> pickPickTasks = new ArrayList<PickTask>();

        Long pktHeaderId = pickTask.getPktHeaderId();
        if (pktHeaderId != null) {
            pktHeaderIds.add(pktHeaderId);
        }
        String status = pickTask.getStatus();
        BigDecimal pickPackQty = pickTask.getQty().divide(pickTask.getQtyUnit(), 0, BigDecimal.ROUND_DOWN);
        //拣货任务状态为拣货完成，并且不是缺货的，则需要返拣
        BigDecimal needReverseQtyUnit = pickTask.getQtyPickedUnit().subtract(recheckQtyUnit);
        BigDecimal needReverseQty = needReverseQtyUnit.multiply(pickPackQty);
        BigDecimal recheckQty = recheckQtyUnit.multiply(pickPackQty);
        if (DoStatus.ALLPICKED.getValue().equals(status)
                && StockStatus.NORMAL.getValue().equals(pickTask.getStockStatus())) {
            if (recheckQty.compareTo(BigDecimal.ZERO) == 0) {
                pickPickTasks.add(pickTask);
            } else {
                PickTask reverseTask = new PickTask();
                BeanUtils.copyProperties(pickTask, reverseTask);
                reverseTask.setId(null);
                reverseTask.setQty(needReverseQty);
                reverseTask.setQtyUnit(needReverseQtyUnit);
                reverseTask.setPickedQty(needReverseQty);
                reverseTask.setQtyPickedUnit(needReverseQtyUnit);
                reverseTask.setQtySorted(needReverseQty);
                reverseTask.setQtySortedUnit(needReverseQtyUnit);
                pickTaskDAO.save(reverseTask);
                pickTask.setQty(recheckQty);
                pickTask.setQtyUnit(recheckQtyUnit);
                pickTask.setPickedQty(recheckQty);
                pickTask.setQtyPickedUnit(recheckQtyUnit);
                pickTask.setQtySorted(recheckQty);
                pickTask.setQtySortedUnit(recheckQtyUnit);
                pickTaskDAO.update(pickTask);
                pickPickTasks.add(reverseTask);
            }
        }
        // 已经拣货pickTask返拣
        createReverseTask(pickPickTasks, reversePickReason);
        //判断pktHeader下是否有pickTask, 如果没有则删除该PktHeader
        modifyPktHeader(pktHeaderIds);
        //更新订单明细上的已拣货数量
        doDetailDAO.updatePickQty(pickTask.getDoDetailId(), needReverseQty);
        //记录拣货日志
        orderLogService.saveLog(pickTask.getDoHeader(),
                OrderLogConstants.OrderLogType.MODIFY_PICK_INFO.getValue(),
                ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_MODIFY_PICK, null, pickTask.getSku().getProductCode(), recheckQty));
    }

    @Override
    public Long isStockIsLock(Long doHeaderId) {
        return pickTaskDAO.isStockIsLock(doHeaderId);
    }

    @Override
    @Transactional
    public void batchUpdate4Pick(List<BatchPickDTO> batchIdList, Container container, String pickerNo, String reason) {
        pickTaskDAO.batchUpdate4Pick(batchIdList, container, pickerNo, reason);
    }

    @Override
    public boolean existCBType(Long doId) {
        return pickTaskDAO.existCBType(doId);
    }

    @Override
    public void flushSession() {
        pickTaskDAO.getSession().flush();
        pickTaskDAO.getSession().clear();
    }

    @Override
    @Transactional
    public void pickByWaveId(Long waveId) {
        List<PickHeader> pickHeaders = pickHeaderService.getPktHeadersByWaveId(waveId);
        Boolean autoSort = Config.isDefaultFalse(Keys.Delivery.sort_isWithPick, Config.ConfigLevel.WAREHOUSE);
        for (PickHeader pickHeader : pickHeaders) {
            pick(pickHeader.getPktNo(), "1", autoSort);
        }
    }
}