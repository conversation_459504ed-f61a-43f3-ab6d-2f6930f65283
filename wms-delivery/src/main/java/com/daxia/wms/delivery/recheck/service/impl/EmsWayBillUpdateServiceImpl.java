package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.EmsWayBillUpdateService;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.waybill.ems.dto.PrintData;
import com.daxia.wms.waybill.ems.dto.UpdatePrintDataRequest;
import com.daxia.wms.waybill.ems.service.EmsWaybillService;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Name("emsWayBillUpdateService")
@lombok.extern.slf4j.Slf4j
public class EmsWayBillUpdateServiceImpl implements EmsWayBillUpdateService {
    @In
    private WarehouseCarrierService warehouseCarrierService;
    @In
    private WarehouseService warehouseService;
    @In(create = true)
    private EmsWaybillService emsWaybillService;


    @Override
    public boolean request(DeliveryOrderHeader doHeader, CartonHeader cartonHeader, BigDecimal weight) {
        WarehouseCarrier warehouseCarrier = warehouseCarrierService.getCarrierInfoByWarehouseIdAndCarrierId(doHeader.getWarehouseId(), doHeader.getCarrierId());
        UpdatePrintDataRequest updateRequest = new UpdatePrintDataRequest();
        updateRequest.setAppKey(warehouseCarrier.getAppKey());
        updateRequest.setSysAccount(warehouseCarrier.getExt1());
        updateRequest.setPassWord(warehouseCarrier.getExt2());
        List<PrintData> printDatas = new ArrayList<PrintData>();
        updateRequest.setPrintDatas(printDatas);
        PrintData printData = new PrintData();
        printData.setBigAccountDataId(cartonHeader.getWayBill());
        printData.setBillno(cartonHeader.getWayBill());
        Warehouse warehouse = warehouseService.getWarehouse(doHeader.getWarehouseId());
        printData.setScontactor(warehouse.getContactor());
        printData.setScustAddr(warehouse.getAddressName());
        printData.setScustMobile(warehouse.getMobile());
        printData.setScustTelplus(warehouse.getPhone());
        printData.setScustProvince(warehouse.getProvince().getProvinceCname());
        printData.setScustCity(warehouse.getCity().getCityCname());
        printData.setScustCounty(warehouse.getCounty().getCountyCname());

        printData.setTcontactor(doHeader.getConsigneeName());

        printData.setTcustMobile(DoUtil.decryptPhone(doHeader.getMobile()));
        printData.setTcustTelplus(DoUtil.decryptPhone(doHeader.getTelephone()));
        printData.setTcustProvince(StringUtils.isNotEmpty(doHeader.getProvinceName()) ? doHeader.getProvinceName() :
                doHeader.getProvinceInfo().getProvinceCname());
        printData.setTcustCity(StringUtils.isNotEmpty(doHeader.getCityName()) ? doHeader.getCityName() :
                doHeader.getCityInfo().getCityCname());
        printData.setTcustCounty(StringUtils.isNotEmpty(doHeader.getCountyName()) ? doHeader.getCountyName() :
                doHeader.getCountyInfo().getCountyCname());
        printData.setTcustAddr(StringUtil.combine(new Object[]{printData.getTcustProvince(), printData.getTcustCity(), printData.getTcustCounty(), doHeader.getAddress()+" "}, ""));
        printData.setAddser_dshk(doHeader.getReceivable().doubleValue() > 0 ? "1" : "0");
        printData.setAddser_sjrff("0");
        printData.setBusinessType("1");
        printData.setFee(doHeader.getReceivable());
        printData.setWeight(weight);
        printDatas.add(printData);
        return emsWaybillService.updatePrintDatas(updateRequest, warehouseCarrier.getContentUrl(), doHeader.getDoNo());
    }

}