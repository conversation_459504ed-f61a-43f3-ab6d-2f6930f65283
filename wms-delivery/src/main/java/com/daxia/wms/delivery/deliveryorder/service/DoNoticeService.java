package com.daxia.wms.delivery.deliveryorder.service;

import com.daxia.wms.delivery.deliveryorder.entity.DoNotice;

import java.util.List;

public interface DoNoticeService {
    void addNotice(Long doId, String holdBy, String holdDesc);

    void addNotice(Long doId, String holdBy, String holdDesc,String noticeNode);

    public DoNotice getLast(Long doId);

    List<DoNotice> getNotices(Long doHeaderId);

    void cancelNotice(Long id, String holdBy);

    List<DoNotice> getNoticesById(Long doHeaderId, Long warehouseId);
}
