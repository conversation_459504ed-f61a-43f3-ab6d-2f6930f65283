package com.daxia.wms.delivery.deliveryorder.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.alibaba.fastjson.JSON;
import com.daxia.dubhe.api.internal.parser.Parser;
import com.daxia.dubhe.api.internal.parser.ParserFactory;
import com.daxia.dubhe.api.internal.util.NumberUtils;
import com.daxia.dubhe.api.wms.request.DoCancelRequest;
import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.dao.jedis.JedisDao;
import com.daxia.framework.common.service.Dictionary;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.framework.system.util.DataList;
import com.daxia.framework.system.util.ExcelUtil;
import com.daxia.framework.system.util.WmsUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.*;
import com.daxia.wms.Keys;
import com.daxia.wms.OrderLogConstants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.delivery.container.service.PktContainerService;
import com.daxia.wms.delivery.deliveryorder.dao.*;
import com.daxia.wms.delivery.deliveryorder.dto.*;
import com.daxia.wms.delivery.deliveryorder.entity.*;
import com.daxia.wms.delivery.deliveryorder.filter.BackExceptionFilter;
import com.daxia.wms.delivery.deliveryorder.filter.DoHeaderFilter;
import com.daxia.wms.delivery.deliveryorder.service.*;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;
import com.daxia.wms.delivery.invoice.service.CancelInvoiceService;
import com.daxia.wms.delivery.invoice.service.ElectronicInvoiceService;
import com.daxia.wms.delivery.load.dao.LoadDetailDAO;
import com.daxia.wms.delivery.load.dao.LoadDetailHisDAO;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.pick.filter.PickTaskFilter;
import com.daxia.wms.delivery.pick.service.PickHeaderService;
import com.daxia.wms.delivery.pick.service.PickService;
import com.daxia.wms.delivery.pick.service.PickTaskService;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.dto.DoPrintSub;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.entity.TempCarton;
import com.daxia.wms.delivery.recheck.filter.CartonHeaderFilter;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.recheck.service.ClearReCheckRecordService;
import com.daxia.wms.delivery.recheck.service.TempCartonService;
import com.daxia.wms.delivery.recheck.service.impl.CartonNoGenerateDispatcher;
import com.daxia.wms.delivery.task.repick.service.RePickContainerService;
import com.daxia.wms.delivery.task.repick.service.ReversePickHeaderService;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.delivery.wave.dao.WaveDAO;
import com.daxia.wms.delivery.wave.dto.WaveDetailDTO;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.exp.delivery.srv.DoCancelExpSrv;
import com.daxia.wms.exp.dto.DoExpDto;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.master.MasterException;
import com.daxia.wms.master.component.BusinessCenterComponent;
import com.daxia.wms.master.dao.AllocationRuleHeaderDAO;
import com.daxia.wms.master.dao.SImpSrvMsgDAO;
import com.daxia.wms.master.dto.AutoCompleteDTO;
import com.daxia.wms.master.dto.CellValue;
import com.daxia.wms.master.dto.SkuDTO;
import com.daxia.wms.master.entity.*;
import com.daxia.wms.master.service.*;
import com.daxia.wms.print.dao.PrintLogDAO;
import com.daxia.wms.stock.StockException;
import com.daxia.wms.stock.stock.dto.OrderStockDTO;
import com.daxia.wms.stock.stock.dto.Stock2AllocateDTO;
import com.daxia.wms.stock.stock.service.IOperator;
import com.daxia.wms.stock.stock.service.LimitedOperLogService;
import com.daxia.wms.stock.stock.service.StockService;
import com.daxia.wms.sys.entity.SImpSrvMsg;
import com.daxia.wms.util.EasyPoiUtil;
import com.daxia.wms.waybill.sf.service.SFOrderService;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.Workbook;
import org.hibernate.criterion.DetachedCriteria;
import org.jboss.seam.Component;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Observer;
import org.jboss.seam.annotations.Transactional;
import org.jboss.seam.security.Identity;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.daxia.wms.Constants.DoType.MPS_OUT;
import static javax.management.timer.Timer.ONE_DAY;

/**
 * <pre>
 * Description:发运订单头信息业务Service实现类
 * </pre>
 */
@Name("com.daxia.wms.delivery.deliveryOrderService")
@lombok.extern.slf4j.Slf4j
public class DeliveryOrderServiceImpl implements DeliveryOrderService, DoStatusRollbackListener {

    @In
    private DoHeaderDAO doHeaderDAO;
    @In
    private TempCartonService tempCartonService;
    @In
    private NoticeRuleService noticeRuleService;
    @In
    private LimitedOperLogService limitedOperLogService;
    @In
    private DoHeaderHisDAO doHeaderHisDAO;
    @In
    private DoAllocateHeaderDAO doAllocateHeaderDAO;
    @In
    private DoDetailDAO doDetailDAO;
    @In
    private DoAllocateDetailDAO doAllocateDetailDAO;
    @In
    private PickService pickService;
    @In
    private PickHeaderService pickHeaderService;
    @In
    private PickTaskService pickTaskService;
    @In
    private StockService stockService;
    @In
    private WaveDAO waveDAO;
    @In
    private DoLackDetailDAO doLackDetailDAO;
    @In
    private LoadDetailDAO loadDetailDAO;
    @In
    private LoadDetailHisDAO loadDetailHisDAO;
    @In
    private WaveService waveService;
    @In
    private DoCancelExpSrv doCancelExpSrv;
    @In
    private SkuCache skuCache;
    @In
    private DoAllocateService doAllocateService;
    @In
    private ReversePickHeaderService reversePickHeaderService;
    @In("allocateOperator")
    private IOperator allocateOperator;
    @In
    private DoExceptionLogService doExceptionLogService;
    @In
    private ContainerMgntService containerMgntService;
    @In
    private ExpFacadeService expFacadeService;
    @In
    private WarehouseService warehouseService;
    @In
    private Identity identity;
    @In
    private DoLackHeaderService doLackHeaderService;
    @In
    private DoWaveExService doWaveExService;
    @In
    private CarrierService carrierService;
    @In
    private ClearReCheckRecordService clearReCheckRecordService;
    @In
    private CartonService cartonService;
    @In
    private SFOrderService sFOrderService;
    @In
    private WarehouseCarrierService warehouseCarrierService;
    @In
    private CarrierLogDAO carrierLogDAO;
    @In
    private ElectronicInvoiceService electronicInvoiceService;
    @In(create = true)
    private CartonNoGenerateDispatcher cartonNoGenerateDispatcher;
    @In
    private SequenceGeneratorService sequenceGeneratorService;
    @In
    private OrderLogService orderLogService;
    @In
    RePickContainerService rePickContainerService;
    @In
    AllocationRuleHeaderDAO allocationRuleHeaderDAO;
    @In
    PrintLogDAO printLogDAO;
    @In(value = "jedisDao")
    private JedisDao jedisDao;

    @In
    private SImpSrvMsgDAO sImpSrvMsgDAO;

    @In
    private CancelInvoiceService cancelInvoiceService;

    @In
    PktContainerService pktContainerService;
    @In
    private SkuService skuService;

    @In
    private BusinessCenterComponent businessCenterComponent;
    @In
    private TrsMaterialsLogService trsMaterialsLogService;
    @In
    private MaterialsService materialsService;

    @Override
    public DataPage<DoHeaderDto> query(DoHeaderFilter doHeaderFilter, int startIndex, int pageSize) {

        DataPage<DoHeaderDto> page = doHeaderDAO.findDoHeaderPageInfo(doHeaderFilter, startIndex, pageSize);

        List<DoHeaderDto> dataList = page.getDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            return page;
        }

        Map<String, String> channelMap = businessCenterComponent.getChannelMap();
        Map<String, String> storeMap = businessCenterComponent.getStoreMap();

        List<DoHeaderDto> collect = page.getDataList().stream()
                .map(doHeaderDTO -> {
                    String channelName = doHeaderDTO.getChannelCode();
                    String storeName = doHeaderDTO.getStoreCode();
                    if (Objects.nonNull(doHeaderDTO.getChannelCode()) && Objects.nonNull(doHeaderDTO.getStoreCode())) {

                        channelName = channelMap.getOrDefault(doHeaderDTO.getChannelCode(),
                                doHeaderDTO.getChannelCode());
                        storeName = storeMap.getOrDefault(doHeaderDTO.getStoreCode(),
                                doHeaderDTO.getStoreCode());
                    }

                    doHeaderDTO.setChannelName(channelName);
                    doHeaderDTO.setStoreName(storeName);
                    return doHeaderDTO;
                })
                .collect(Collectors.toList());

        page.setDataList(collect);

        return page;
    }

    /*
     * 订单取消
     *
     */
    @Override
    @Transactional
    public void cancel(Long doHeaderId) throws DeliveryException {
        DeliveryOrderHeader deliveryOrder = this.doHeaderDAO.get(doHeaderId);
        DoAllocateHeader doAlcHeader = doAllocateHeaderDAO.get(doHeaderId);
        if (deliveryOrder == null) {
            throw new DeliveryException(DeliveryException.DELIVERYORDER_CANNOT_CANCEL_NOTEXIST);
        }
        if (DoStatus.CANCELED.getValue().equals(deliveryOrder.getStatus())) {
            throw new DeliveryException(DeliveryException.DO_IS_CANCELED);
        }
        // wms系统人员不能直接取消发货单，只有后台系统传达取消指令之后，然后将发货单状态回退至初始化状态，用手动取消发货单
        if (!deliveryOrder.getNeedCancel()) {
            throw new DeliveryException(DeliveryException.DO_CANCEL_FAILED_NOTNEEDCANCEL);
        }
        boolean isHold = deliveryOrder.getReleaseStatus().equals(Constants.ReleaseStatus.HOLD.getValue());// 是否冻结标记
        boolean isInitialization =
            deliveryOrder.getStatus().equals(Constants.DoStatus.INITIAL.getValue()) && null == doAlcHeader
                ? Boolean.TRUE : Constants.DoStatus.INITIAL.getValue().equals(doAlcHeader.getStatus());// 是否是初始化标记
        // 如果不是冻结状态则抛出异常,订单没被冻结,不能取消
        if (!isHold) {
            throw new DeliveryException(DeliveryException.DELIVERYORDER_CANNOT_CANCEL_NOTHOLD);
        }
        // 如果不是初始化状态则抛出异常,订单不是初始化状态,不能取消
        if (!isInitialization) {
            throw new DeliveryException(DeliveryException.DELIVERYORDER_CANNOT_CANCEL_NOTINIT);
        }

        // 校验返拣任务没有完成不能取消DO
        this.checkRePickTaskIsFinish(doHeaderId);

        // 红冲电子发票
        electronicInvoiceService.writeBackInvoice(deliveryOrder);

        // 获取已取消发货单的状态码
        String canceledStatus = Constants.DoStatus.CANCELED.getValue();
        // 设置订单状态为已取消状态
        deliveryOrder.setStatus(canceledStatus);
        // 修改doDetail行状态为取消状态
        doDetailDAO.updateDoDetailStatusByDoId(deliveryOrder.getId(), canceledStatus);
        doHeaderDAO.update(deliveryOrder);// 更新数据
        // 取消do后，要删除分配表
        if (null != doAlcHeader) {
            doAllocateService.removeAllocate(doAlcHeader.getId());
        }

        // 写do异常日志
        DoExStatusOpDto doExStatusOpDto = new DoExStatusOpDto();
        doExStatusOpDto.setExceptionStatus(deliveryOrder.getExceptionStatus());
        doExStatusOpDto.setReleaseStatus(ReleaseStatus.HOLD.getValue());
        doExStatusOpDto.setStatus(DoStatus.INITIAL.getValue());
        doExStatusOpDto.setOpType(Constants.ExOpType.CANCEL.getValue());
        doExceptionLogService.saveDoExceptionLog(doExStatusOpDto, deliveryOrder);

        // 订单取消 调用接口
        // doCancelExpSrv.send(doHeaderId, true , null);
        doCancelExpSrv.createMsg(doHeaderId, "0", deliveryOrder.getWarehouseId());
        this.doDetailDAO.getSession().flush();
        this.doDetailDAO.getSession().clear();
    }

    /**
     * RF拣货自动冻结
     */
    @Override
    public void autoFrozenOnRFPick(Long doHeaderId, Long taskId, String holdWho, String reason) {
        String srcReasonCode = "1".equals(reason) ? Reason.PICK_LACK.getValue() : Reason.PICK_DAMAGE.getValue();
        String reasonCode = srcReasonCode;
        // 判断是否可以冻结订单
        DeliveryOrderHeader doHeader = this.validate2Frozen(doHeaderId, reasonCode, true);
        String toExceptionStatus = DoExpStatus.TO_BE_ANNOUNCE.getValue();
        if (doHeader.getNeedCancel()) {
            return;
        }

        // 删除异常已经完成订单的缺货明细。
        if (DoExpStatus.COMPLETE.getValue().equals(doHeader.getExceptionStatus())) {
            doLackHeaderService.deleteLackInfoByDoId(doHeader.getId());
        }
        // 调拨/RTV 在RF拣货缺货/破损时自动冻结，添加异常记录。异常状态记为"待回退"，冻结原因直接写入"等待补货"。
        if (DoType.ALLOT.getValue().toString().equals(doHeader.getDoType())
            || DoType.RTV.getValue().toString().equals(doHeader.getDoType())) {
            reasonCode = Reason.WAIT_REPL.getValue();
        }
        String opType = null;
        if (reasonCode.equals(Constants.Reason.PICK_LACK.getValue())) {
            opType = Constants.ExOpType.PICK_LACK.getValue();
        } else {
            opType = Constants.ExOpType.PICK_DAMAGE.getValue();
        }
        // 如果配置自动通知客服，则缺货自动通知客服。
        Boolean lackAutoAnounceCs =
            com.daxia.wms.master.helper.SysConfigHelper.getSwitchDefalutOpen("alloc.lack.autoAnounceCs");
        if (lackAutoAnounceCs) {
            DoExpDto dto = new DoExpDto();
            dto.setId(doHeaderId);
            dto.setHoldCode(Constants.Reason.PICK_LACK.getValue());
            dto.setNotifyType(NotifyCSType.AUTO.getValue());
            expFacadeService.callCSCreateDatas(dto);

            if (SystemConfig.configIsClosed("notify.cs.notNeed.feedback", ParamUtil.getCurrentWarehouseId())) {
                if (DoStatus.INITIAL.getValue().equals(doHeader.getStatus())) {// 是否初始化
                    toExceptionStatus = DoExpStatus.TO_BE_REPL.getValue();
                } else {
                    toExceptionStatus = DoExpStatus.TO_BE_ROLLBACK.getValue();
                }
            } else {
                toExceptionStatus = DoExpStatus.TO_BE_FEEDBACK.getValue();
            }
        }
        this.frozen(doHeader, reasonCode, null, holdWho, toExceptionStatus, opType);

        PickTask pickTask = pickTaskService.getTaskById(taskId);
        Map<String, String> resonMap = Dictionary.getDictionary("REASON_HDD");
        Long skuId = pickTask.getSkuId();

        DoLackDetail lackDetail =
            doLackDetailDAO.findLackDoDetail(doHeaderId, pickTask.getDoDetailId(), skuId, pickTask.getLocId());
        if (lackDetail == null) {
            lackDetail = new DoLackDetail();
            lackDetail.setDoHeaderId(doHeaderId);
            lackDetail.setDoDetailId(pickTask.getDoDetailId());
            lackDetail.setSkuId(skuId);
            lackDetail.setLocId(pickTask.getLocId());
            lackDetail.setQty(pickTask.getQty().subtract(pickTask.getPickedQty()));
            lackDetail.setLocCodes(pickTask.getLocation().getLocCode());
            lackDetail.setCreatedBy(holdWho);
            lackDetail.setLotId(pickTask.getLotId());
            lackDetail.setLpnNo(pickTask.getLpnNo());
        } else {
            lackDetail.setQty(pickTask.getQty().subtract(pickTask.getPickedQty()).add(lackDetail.getQty()));
        }
        lackDetail.setHoldReason(resonMap.get(srcReasonCode));
        lackDetail.setUpdatedBy(holdWho);
        doLackDetailDAO.saveOrUpdate(lackDetail);

        doLackHeaderService.addOrUpdate(doHeaderId, pickTask.getLocation().getPartition().getPartitionCode());
    }

    /**
     * 更新订单明细
     */
    @Override
    @Transactional
    public void updateDoHeader(DeliveryOrderHeader doHeader) {
        doHeaderDAO.update(doHeader);
    }

    /**
     * 更新订单明细
     */
    @Override
    @Transactional
    public void logPackedBy(DeliveryOrderHeader doHeader) {
        doHeader = doHeaderDAO.load(doHeader.getId());
        doHeader.setPackedBy(ParamUtil.getCurrentLoginName());
        doHeaderDAO.update(doHeader);
    }

    /**
     * 根据波次ID查询订单明细
     */
    @Override
    public List<DeliveryOrderHeader> qureyDoHeaderByWaveId(Long waveId) {
        return doHeaderDAO.findDoHeaderByWaveId(waveId);
    }

    /**
     * 订单明细ID获取明细
     */
    @Override
    public DeliveryOrderDetail getDoDetail(Long doDetailId) {
        return doDetailDAO.get(doDetailId);
    }

    /**
     * 更新订单明细
     */
    @Override
    @Transactional
    public void updateDoDetail(DeliveryOrderDetail doDetail) {
        doDetailDAO.update(doDetail);
    }

    /**
     * 获取订单的单位（UOM）
     */
    @Override
    public String getUomByHeaderId(Long doHeaderId) {
        return doDetailDAO.getUomByHeaderId(doHeaderId);
    }

    /**
     * 更新订单
     */
    @Override
    @Transactional
    public void saveOrUpdateDoHeader(DeliveryOrderHeader deliveryOrderHeader) {
        doHeaderDAO.saveOrUpdate(deliveryOrderHeader);
    }

    /**
     * 更具订单获取订单明细
     */
    @Override
    public List<DeliveryOrderDetail> getDoDetailByDoHeaderId(Long doHeaderId) {

        return doDetailDAO.findDoDetailByHeaderId(doHeaderId);
    }

    /**
     * 更新订单中订单明细的状态
     *
     * @param doHeaderId
     * @param status
     */
    @Override
    public void updateDoDetailStatusByDoId(Long doHeaderId, String status) {

        doDetailDAO.updateDoDetailStatusByDoId(doHeaderId, status);
    }

    /**
     * 释放订单
     */
    @Transactional
    @Override
    public List<List<String>> releaseDO(List<Long> doIds) {
        List<List<String>> result = new ArrayList<List<String>>();
        List<String> canReleaseList = new ArrayList<String>();
        List<String> canNotReleaseList = new ArrayList<String>();
        List<Long> needClearAlcTimeDo = new ArrayList<Long>();
        List<Long> neednotClearAlcTimeDo = new ArrayList<Long>();
        String login = ParamUtil.getCurrentLoginName();
        for (Long doId : doIds) {
            DeliveryOrderHeader deliveryOrderHeader = doHeaderDAO.get(doId);
            // 客服申请取消订单不能释放
            if (deliveryOrderHeader.getNeedCancel()) {
                canNotReleaseList.add(deliveryOrderHeader.getDoNo());
                continue;
            }
            List<DeliveryOrderDetail> doDetails = doDetailDAO.findDoDetailByHeaderId(doId);// 找到所有的订单明细
            String excStatus = deliveryOrderHeader.getExceptionStatus();
            String releaseStatus = deliveryOrderHeader.getReleaseStatus();
            String doNo = deliveryOrderHeader.getDoNo();
            String doType = deliveryOrderHeader.getDoType();
            String dostatus = deliveryOrderHeader.getStatus();
            String holdCode = deliveryOrderHeader.getHoldCode();

            if (ReleaseStatus.HOLD.getValue().equals(releaseStatus) && DoType.SELL.getValue().toString().equals(doType)
                && DoExpStatus.TO_BE_ANNOUNCE.getValue().equals(excStatus)
                && !Reason.ALLOC_LACK.getValue().equals(holdCode)) {
                // 正常DO 且异常状态为[待通知客服], 目前 手动冻结 Or 拣货缺货 Or 拣货缺货后接着分拣 会造成这种情况

                canReleaseList.add(doId.toString());
                if (Constants.DoStatus.ALLALLOCATED.getValue().compareTo(dostatus) > 0) {
                    // 未分配完成的需清空分配时间 使之可以再被自动分配。（这里只会是初始化订单[可选:手动部分分配后]手动冻结造成的）
                    // 再次自动/手动分配会导致分配时间不是第一次分配的时间，但是没什么影响。
                    needClearAlcTimeDo.add(doId);
                } else {
                    // 已分配完成的不能清空分配时间
                    neednotClearAlcTimeDo.add(doId);
                }
                doExceptionLogService.saveReleaseExpLog(doId, login);
            } else if (ReleaseStatus.HOLD.getValue().equals(releaseStatus)
                && DoExpStatus.TO_BE_ROLLBACK.getValue().equals(excStatus)) {

                canReleaseList.add(doId.toString());
                neednotClearAlcTimeDo.add(doId);
                doExceptionLogService.saveReleaseExpLog(doId, login);
            } else if (ReleaseStatus.HOLD.getValue().equals(releaseStatus)
                && (DoExpStatus.TO_BE_REPL.getValue().equals(excStatus)
                    || (DoExpStatus.TO_BE_ANNOUNCE.getValue().equals(excStatus)
                        && Reason.ALLOC_LACK.getValue().equals(holdCode)))) {
                // 正常订单初始化释放，判断库存是否足够
                if (DoType.SELL.getValue().toString().equals(doType)) {

                    boolean isStockEnough = this.isStockEnough(canNotReleaseList, doDetails, doIds.size());
                    if (isStockEnough) {
                        // 所有明细库存足够
                        canReleaseList.add(doId.toString());
                        // 分配中或者初始化, 需清空分配时间 使之可以再被自动分配。
                        needClearAlcTimeDo.add(doId);// 此处可能有多余的更新：初始化手动冻结跳过客服的订单（本身就没有分配时间）, 但批量更新无所谓了。
                        doExceptionLogService.saveReleaseExpLog(doId, login);
                    } else if (doIds.size() > 1) {
                        // 多个DO时提示订单号
                        canNotReleaseList.add(doNo);
                    }
                } else {
                    canReleaseList.add(doId.toString());
                    needClearAlcTimeDo.add(doId);
                    doExceptionLogService.saveReleaseExpLog(doId, login);
                }
            }
        }

        // 能够释放的订单列表不为空
        if (!ListUtil.isNullOrEmpty(canReleaseList)) {
            List<Long> doIdList = new ArrayList<Long>();
            if (!ListUtil.isNullOrEmpty(needClearAlcTimeDo)) {
                doIdList.addAll(needClearAlcTimeDo);
                doAllocateHeaderDAO.rlsDoAndClearAlcTime(needClearAlcTimeDo);
                doHeaderDAO.updateReleaseStatus(Constants.ReleaseStatus.RELEASE.getValue(), needClearAlcTimeDo);
            }
            if (!ListUtil.isNullOrEmpty(neednotClearAlcTimeDo)) {
                doIdList.addAll(neednotClearAlcTimeDo);
                doAllocateHeaderDAO.updateReleaseStatus(Constants.ReleaseStatus.RELEASE.getValue(),
                    neednotClearAlcTimeDo);
                doHeaderDAO.updateReleaseStatus(Constants.ReleaseStatus.RELEASE.getValue(), neednotClearAlcTimeDo);
            }

            // 记录日志，订单释放
            List<DeliveryOrderHeader> doList = this.findDoByIds(doIdList);
            for (DeliveryOrderHeader doHeader : doList) {
                orderLogService.saveLog(doHeader, OrderLogConstants.OrderLogType.ORDER_RELEASE.getValue(),
                    ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_ORDER_RELEASE));
            }

            doHeaderDAO.getSession().flush();
            doHeaderDAO.getSession().clear();
        }
        result.add(canReleaseList);
        result.add(canNotReleaseList);

        return result;
    }

    @Override
    @Transactional
    public List<Long> releaseNotLackDos(List<Long> doIds) {
        List<Long> canReleaseList = new ArrayList<Long>();
        for (Long doId : doIds) {
            DeliveryOrderHeader doHeader = doHeaderDAO.get(doId);
            if (null == doHeader) {
                continue;
            }
            // 客服申请取消订单不能释放
            if (doHeader.getNeedCancel()) {
                continue;
            }
            if (!ReleaseStatus.HOLD.getValue().equals(doHeader.getReleaseStatus())) {
                continue;
            }
            // 订单释放条件：待回退，待补货,待通知客服
            if (StringUtil.isNotIn(doHeader.getExceptionStatus(), DoExpStatus.TO_BE_ROLLBACK.getValue(),
                DoExpStatus.TO_BE_REPL.getValue(), DoExpStatus.TO_BE_ANNOUNCE.getValue())) {
                continue;
            }
            // 订单释放条件：初始化,部分分配
            if (StringUtil.isNotIn(doHeader.getStatus(), Constants.DoStatus.INITIAL.getValue(),
                Constants.DoStatus.PARTALLOCATED.getValue())) {
                continue;
            }
            DoExStatusOpDto doExStatusOpDto = new DoExStatusOpDto();
            doExStatusOpDto.setReleaseStatus(doHeader.getReleaseStatus());
            doExStatusOpDto.setStatus(doHeader.getStatus());
            doExStatusOpDto.setExceptionStatus(doHeader.getExceptionStatus());

            doHeader.setReleaseStatus(Constants.ReleaseStatus.RELEASE.getValue());
            doHeader.setExceptionStatus(Constants.DoExpStatus.COMPLETE.getValue());

            doExStatusOpDto.setOpType(Constants.ExOpType.ARRIVAL_RL.getValue());
            // 记录日志
            doExceptionLogService.saveDoExceptionLog(doExStatusOpDto, doHeader);
            this.updateDoHeader(doHeader);
            canReleaseList.add(doId);

            orderLogService.saveLog(doHeader, OrderLogConstants.OrderLogType.ORDER_RELEASE.getValue(),
                ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_ORDER_RELEASE, null));
        }

        // 能够释放的订单列表不为空
        if (!ListUtil.isNullOrEmpty(canReleaseList)) {
            doAllocateHeaderDAO.rlsDoAndClearAlcTime(canReleaseList);
            // 记录自动释放日志
            // orderLogService.batchSaveLog(canReleaseList,OrderLogConstants.OrderLogType.ORDER_RELEASE.getValue(),
            // ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_ORDER_RELEASE) );
        }
        return canReleaseList;
    }

    private boolean isStockEnough(List<String> canNotReleaseList, List<DeliveryOrderDetail> doDetails, int doSize) {
        boolean isStockEnough = false;
        int enoughDetailNum = 0;// 记录 库存足够用于分配的明细条数
        for (DeliveryOrderDetail doDetail : doDetails) {
            // 组合商品是没有库存的，不作判断，在其子商品明细中会有体现。
            if (YesNo.NO.getValue().equals(doDetail.getIsDoLeaf())) {
                enoughDetailNum++;
                continue;
            }

            boolean isDetailLack = true;
            SkuDTO sku = skuCache.getSku(doDetail.getSkuId());
            DoAllocateDetail alcDetail = doAllocateDetailDAO.get(doDetail.getId());
            AllocationRuleHeader allocationRuleHeader = null;
            if (alcDetail != null) {
                allocationRuleHeader = allocationRuleHeaderDAO.get(alcDetail.getAllocationRule());
            }
            List<Stock2AllocateDTO> stockList =
                stockService.findStockForRepl(sku.getId(), doDetail.getDoHeader().getShopId(), doDetail.getLotatt04(),
                    doDetail.getLotatt06(), doDetail.getLotatt08(),
                    allocationRuleHeader == null ? YesNo.NO.getValue() : allocationRuleHeader.getExpireControl(),
                    doDetail.getDoHeader().getDoType());
            BigDecimal actQtySum = BigDecimal.ZERO;

            // 该for循环判断库存是否满足当前明细的分配要求
            for (Stock2AllocateDTO doDetailAllocateResult : stockList) {
                actQtySum =
                    actQtySum.add(doDetailAllocateResult.getActQty()).add(null == doDetailAllocateResult.getPendingQty()
                        ? BigDecimal.ZERO : doDetailAllocateResult.getPendingQty());

                int result = 0;
                if (null != alcDetail) {
                    result = actQtySum.compareTo(alcDetail.getExpectedQty().subtract(alcDetail.getAllocatedQty()));
                } else {
                    result = actQtySum.compareTo(doDetail.getExpectedQty().subtract(doDetail.getAllocatedQty()));
                }
                // 库存足够
                if (result >= 0) {
                    enoughDetailNum = enoughDetailNum + 1;
                    isDetailLack = false;
                    break;
                }
            }
            // 当前明细仍是全仓缺货的，即库存不够；且用户只操作释放一个DO。
            if (isDetailLack && doSize == 1) {
                // 页面提示该 DO下所有缺货的明细对应的商品编码
                canNotReleaseList.add(sku.getProductCode());
                isStockEnough = false;
            }
        }
        if (enoughDetailNum == doDetails.size()) {
            // 所有明细库存足够
            isStockEnough = true;
        } else if (doSize > 1) {
            // 多个DO时提示订单号
            isStockEnough = false;
        }
        return isStockEnough;
    }

    @Override
    public DataPage<DeliveryOrderHeader> query(DetachedCriteria queryCri, DetachedCriteria countCri, int startIndex,
        int pageSize) {
        return doHeaderDAO.query(queryCri, countCri, startIndex, pageSize);
    }

    /**
     * 将do单的订单明细已拣数量归零,分配数清零,状态变为初始化,需要补货数量清零
     */
    @Override
    public void doDoDetailBackToInit(Long doHeaderId, String lineStatus) {

        doDetailDAO.doDoDetailBackToInit(doHeaderId, lineStatus);
    }

    @Override
    @Transactional
    public void updateDoDetailStatusAndQty(Long doDetailId, String status, BigDecimal qty) {
        doDetailDAO.updateDoDetailStatusAndQty(doDetailId, status, qty);
    }

    /**
     * 订单状态回退,从分配完成或部分分配到初始化
     */
    @Override
    @Transactional
    public void rollback(Long doId, String startStatus, String fromStatus, String toStatus) {
        boolean isFromStatusMatch = fromStatus.equals(Constants.DoStatus.ALLALLOCATED.getValue())
            || fromStatus.equals(Constants.DoStatus.PARTALLOCATED.getValue());
        if (!isFromStatusMatch) {
            return;
        }
        if (!toStatus.equals(Constants.DoStatus.INITIAL.getValue())) {
            return;
        }

        DeliveryOrderHeader doHeader = doHeaderDAO.get(doId);
        this.doRollBackAllocDo2Init(doHeader);

        // 订单异常日志
        DoExStatusOpDto doExStatusOpDto = new DoExStatusOpDto();
        doExStatusOpDto.setExceptionStatus(DoExpStatus.TO_BE_ROLLBACK.getValue());
        doExStatusOpDto.setReleaseStatus(ReleaseStatus.HOLD.getValue());
        doExStatusOpDto.setStatus(startStatus);
        doExStatusOpDto.setOpType(Constants.ExOpType.ROLL_BACK.getValue());
        doExceptionLogService.saveDoExceptionLog(doExStatusOpDto, doHeader);

        // 同步订单信息到分配信息，没有则会创建
        if (Reason.CS_CANCLE.getValue().equals(doHeader.getHoldCode())) {
            // 取消异步调用，换成定时任务
            // doCancelExpSrv.send(doHeader.getId(), true , null);
            doCancelExpSrv.createMsg(doHeader.getId(), "0", doHeader.getWarehouseId());
        } else {
            doAllocateService.sycDo2Alc(doHeader.getId());
        }
    }

    @Override
    public void doRollBackAllocDo2Init(DeliveryOrderHeader doHeader) {
        PickTaskFilter pickTaskFilter = new PickTaskFilter();
        // 校验库存是否足够
        pickTaskFilter.setDoHeaderId(doHeader.getId());
        List<PickTask> pickTasks = pickTaskService.query(pickTaskFilter);
        if (ListUtil.isNullOrEmpty(pickTasks)) {
            return;
        }

        // 释放库存
        for (PickTask picktask : pickTasks) {
            OrderStockDTO stockDTO = new OrderStockDTO();
            stockDTO.setFmStockId(picktask.getFmStockId());
            stockDTO.setPlanQty(picktask.getQty());
            stockDTO.setPlanQtyUnit(picktask.getQtyUnit());
            stockDTO.setActualQty(picktask.getQty());
            stockDTO.setFmLocId(picktask.getLocId());
            stockDTO.setLotId(picktask.getLotId());
            stockDTO.setLpnNo(picktask.getLpnNo());
            stockDTO.setSkuId(picktask.getSkuId());
            stockDTO.setAllocatingId(picktask.getAllocatingId());
            allocateOperator.setStockDto(stockDTO);
            stockService.undo(allocateOperator);
        }

        // 删除拣货任务
        this.unrelate(doHeader.getId(), true);

        // 分配数量清零
        this.doDetailDAO.doDoDetailBackToInit(doHeader.getId(), Constants.DoStatus.INITIAL.getValue());
        // 修改发货单状态

        WaveHeader waveHeader = doHeader.getWaveHeader();

        // 如果分配完成已跑波次 需要清除DO与波次的关系
        doHeader.setWaveId(null);
        doHeader.setReplStatus(Constants.DoReplStatus.NONE.getValue());
        // 波次更改为未跑波次
        doHeader.setWaveFlag(Constants.YesNo.NO.getValue());
        String expStatus = doHeader.getExceptionStatus();
        if (Reason.CS_CANCLE.getValue().equals(doHeader.getHoldCode())) {
            expStatus = DoExpStatus.COMPLETE.getValue();
            // 如果是客服申请取消则状态回退时候将其取消
            doHeader.setStatus(Constants.DoStatus.CANCELED.getValue());
            doDetailDAO.updateDoDetailStatusByDoId(doHeader.getId(), Constants.DoStatus.CANCELED.getValue());
            DoAllocateHeader doAllocateHeader = doAllocateHeaderDAO.get(doHeader.getId());
            if (doAllocateHeader != null) {
                List<Long> headerIdList = new ArrayList<Long>();
                headerIdList.add(doAllocateHeader.getId());
                doAllocateService.removeAllocate(headerIdList);
            }

            electronicInvoiceService.writeBackInvoice(doHeader);
        } else {
            expStatus = DoExpStatus.TO_BE_REPL.getValue();
            doHeader.setStatus(Constants.DoStatus.INITIAL.getValue());
        }
        doHeader.setExceptionStatus(expStatus);
        doHeaderDAO.update(doHeader);

        if (null != waveHeader) {
            List<PickHeader> pktHeaderList = pickHeaderService.getPktHeadersByWaveId(waveHeader.getId());
            for (PickHeader pktHeader : pktHeaderList) {
                pickHeaderService.changePktHeaderStatus(pktHeader, false, pktHeader.getPickBy());
            }
            Long count = pickHeaderService.findPktHeaderCount(waveHeader.getId());
            waveHeader.setPktCount(count);
            waveHeader.setDoCount(doHeaderDAO.countSortingNumberByWaveId(waveHeader.getId()));

            // 更新波次发票信息
            this.updateWaveInvoiceInfo(waveHeader, waveHeader.getDoHeaders(), doHeader.getId());

            // 修改波次信息
            Boolean autoSort = Config.isDefaultFalse(Keys.Delivery.sort_isWithPick, Config.ConfigLevel.WAREHOUSE);
            pickService.modifyWave(waveHeader, doAllocateDetailDAO.getOperateUser(), false, autoSort);
        }
    }

    /**
     * 订单查询
     *
     * @param doHeaderFilter
     * @return
     */
    @Override
    public List<DeliveryOrderHeader> query(DoHeaderFilter doHeaderFilter) {
        return this.doHeaderDAO.findByFilter(doHeaderFilter);
    }

    @Override
    public List<String> queryNos(DoHeaderFilter doHeaderFilter) {
        return doHeaderDAO.findNosByFilter(doHeaderFilter);
    }

    /**
     * 从波次计划中撤销订单
     *
     * @see com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService#remove(java.lang.Long)
     */
    @Override
    @Transactional
    public void remove(Long doHeaderId) {
        if (doHeaderId == null) {
            throw new DeliveryException(DeliveryException.DELIVERYORDER_CANNOT_DELETEFROMWAVE_NOTEXIST);
        }
        DeliveryOrderHeader doHeader = this.doHeaderDAO.get(doHeaderId);
        if (doHeader == null) {
            throw new DeliveryException(DeliveryException.DELIVERYORDER_CANNOT_DELETEFROMWAVE_NOTEXIST);
        }
        boolean isAllocated = Constants.DoStatus.ALLALLOCATED.getValue().equals(doHeader.getStatus());
        boolean isRelase = Constants.ReleaseStatus.RELEASE.getValue().equals(doHeader.getReleaseStatus());
        boolean isInit = Constants.DoStatus.INITIAL.getValue().equals(doHeader.getStatus());
        if (!isRelase) {
            throw new DeliveryException(DeliveryException.DELIVERYORDER_CANNOT_DELETEFROMWAVE_NOTRELEASE);
        }
        // 只有 初始化、分配完成状态的DO才能撤销
        if (!(isAllocated || isInit)) {
            throw new DeliveryException(DeliveryException.DELIVERYORDER_CANNOT_DELETEFROMWAVE_WRONGSTATUS);
        }
        if (doHeader.getWaveId() == null) {
            throw new DeliveryException(DeliveryException.DO_NOT_IN_WAVE);
        }
        // 获取 do所属波次Id
        Long waveId = doHeader.getWaveId();
        List<Container> containers = containerMgntService.findByDoNo(doHeader.getDoNo());
        if (CollectionUtils.isNotEmpty(containers)) {
            List<String> containerNos = new ArrayList<String>();
            for (Container container : containers) {
                containerNos.add(container.getContainerNo());
            }
            throw new DeliveryException(DeliveryException.DELIVERYORDER_BIND_CONTAINER, containerNos.toString());
        }
        // 取消DO与波次关系
        doHeader.setWaveId(null);
        // 将DO分拣格号设置为空
        doHeader.setSortGridNo(null);
        // 设置波次状态为未跑
        doHeader.setWaveFlag(Constants.YesNo.NO.getValue());
        this.doHeaderDAO.saveOrUpdate(doHeader);

        // 从波次中撤销do，修改相应拣货任务状态，并做容器绑定的相应释放处理
        this.unrelate(doHeaderId, false);

        List<PickHeader> pktHeaderList = pickHeaderService.getPktHeadersByWaveId(waveId);
        for (PickHeader pktHeader : pktHeaderList) {
            pickHeaderService.changePktHeaderStatus(pktHeader, false, pktHeader.getPickBy());
        }

        List<DeliveryOrderHeader> doHeaders = doHeaderDAO.queryDoHeadersInWave(waveId);
        if (doHeaders == null || doHeaders.isEmpty()) {
            // 波次中不存在其他订单

            WaveHeader waveHeader = waveDAO.get(waveId);
            // 如果波次被删除则释放容器(拣货箱子)
            containerMgntService.releaseContainerByWave(waveHeader, Constants.ContainerType.WAVE_CONTAINER.getValue(),
                Constants.BindDocType.WAVE.getValue());

            // 启用了电子标签
            if (Config.isDefaultFalse(Keys.Delivery.pick_useDPS, Config.ConfigLevel.WAREHOUSE)) {
                pktContainerService.delByWave(waveHeader);
            }

            // 波次中不存在DO，删除波次
            waveService.removeWaveHeader(waveHeader);

            this.sendInvoice2Oms(waveHeader);
        } else {
            int doSize = doHeaders.size();
            int flagPartPick = 0;
            int flagAllPick = 0;
            int flagPartSort = 0;
            int flagAllSort = 0;
            // 波次中不能出现拣货和分拣状态并存的DO
            // 根据波次中DO的状态来更新波次状态
            // 波次中Do的状态 可能为 初始化 、 分配完成、 部分拣货 、拣货完成的混合 或者 为 初始化 、 分配完成 、部分分拣 、
            // 分拣完成的混合
            for (DeliveryOrderHeader deliveryOrderHeader : doHeaders) {
                if (deliveryOrderHeader.getStatus().equals(Constants.WaveStatus.PARTPICKED.getValue())) {
                    flagPartPick++;
                    break;
                } else if (deliveryOrderHeader.getStatus().equals(Constants.WaveStatus.ALLPICKED.getValue())) {
                    flagAllPick++;
                } else if (deliveryOrderHeader.getStatus().equals(Constants.WaveStatus.PARTSORTED.getValue())) {
                    flagPartSort++;
                    break;
                } else if (deliveryOrderHeader.getStatus().equals(Constants.WaveStatus.ALLSORTED.getValue())) {
                    flagAllSort++;
                }
            }
            if (flagPartPick > 0 || flagAllPick > 0 || flagPartSort > 0 || flagAllSort > 0) {
                if (flagPartPick > 0 || (flagAllPick > 0 && flagAllPick < doSize)) {
                    this.waveDAO.updateWaveStatusById(waveId, Constants.WaveStatus.PARTPICKED.getValue());
                } else if (flagAllPick == doSize) {
                    this.waveDAO.updateWaveStatusById(waveId, Constants.WaveStatus.ALLPICKED.getValue());
                } else if (flagPartSort > 0 || (flagAllSort > 0 && flagAllSort < doSize)) {
                    this.waveDAO.updateWaveStatusById(waveId, Constants.WaveStatus.PARTSORTED.getValue());
                } else if (flagAllSort == doSize) {
                    this.waveDAO.updateWaveStatusById(waveId, Constants.WaveStatus.ALLSORTED.getValue());
                }
            }
            WaveHeader waveHeader = waveDAO.get(waveId);
            boolean flagChange = false;
            // 如果移除的订单有发票，才影响波次的发票状态
            if (doHeader.getInvoiceFlag() != null
                && doHeader.getInvoiceFlag().intValue() == YesNo.YES.getValue().intValue()) {
                this.updateWaveInvoiceInfo(waveHeader, doHeaders, doHeaderId);
                flagChange = true;
            }

            Long count = pickHeaderService.findPktHeaderCount(waveId);
            waveHeader.setPktCount(count);
            waveHeader.setDoCount(doHeaders.size());
            waveDAO.update(waveHeader);
            if (flagChange) {
                this.sendInvoice2Oms(waveHeader);
            }
        }

        // 重新生成订单分配信息
        doAllocateService.createDoAllocateHeader(doHeader);
        List<DeliveryOrderDetail> deliveryOrderDetails = this.getDoDetailByDoHeaderId(doHeader.getId());
        doAllocateService.createDoAllocateDetail(deliveryOrderDetails);
        // 记录日志
        orderLogService.saveLog(doHeader, OrderLogConstants.OrderLogType.WAVE_CANCEL.getValue(),
            ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_WAVE_CANCEL));
    }

    @Override
    public void updateWaveInvoiceInfo(WaveHeader waveHeader, List<DeliveryOrderHeader> doHeaders, Long excludeId) {
        // 如果不是下列波次类型，则不需要设置发票标示：半日达普通波次、半日达大件波次、普通波次、普通达大件波次、团购波次
        if (StringUtil.isNotIn(waveHeader.getWaveType(), WaveType.WAVE_NORMAL.getValue())) {
            return;
        }

        int printedCount = 0;
        int totalPrintCount = 0;

        boolean invoiceFlag = false;
        PrintStatus printStatus = PrintStatus.NO;

        for (DeliveryOrderHeader doHeader : doHeaders) {
            if (excludeId != null && doHeader.getId().equals(excludeId)) {
                continue;
            }

            /* 一个订单有发票，那么波次标示为有发票 */
            invoiceFlag = invoiceFlag || (doHeader.getInvoiceFlag().intValue() == YesNo.YES.getValue());

            /* 全未打印 - 0  | 全部打印 - 1 | 部分打印 - 2 */
            for (InvoiceHeader invoiceHeader : doHeader.getInvoiceHeaders()) {
                printedCount += invoiceHeader.getInvoicePrintFlag();
                totalPrintCount++;

                if (invoiceHeader.getInvoicePrintFlag().equals(YesNo.YES.getValue())) {
                    printStatus = PrintStatus.PARTIAL;
                }
            }
        }

        if (invoiceFlag && printedCount == totalPrintCount) {
            printStatus = PrintStatus.YES;
        }

        waveHeader.setInvoiceFlag(invoiceFlag ? YesNo.YES.getValue() : YesNo.NO.getValue());
        waveHeader.setInvoicePrintFlag(printStatus.getValue());
    }

    /**
     * 根据发货单Id状态回退其拣货任务 1.isDelete==true删除拣货任务，否则清空拣货任务的和波次、拣货单头、容器的关联
     * 2.判断撤销do对应的拣货任务绑定的容器是否需要释放：若没有其他拣货任务绑定到相应的容器，则释放，否则不必释放
     *
     * @param doHeaderId
     * @param isDelete
     *            是否删除拣货任务标识
     */
    @Override
    public void unrelate(Long doHeaderId, boolean isDelete) {
        // 从波次中撤销do 需要将do相关的拣货任务从发布状态变为初始化状态，并释放该do对应的拣货任务上的容器信息
        if (isDelete) {
            rePickContainerService.bind4Pick(doHeaderId);

            pickTaskService.cancelPickTaskByDoHeaderId(doHeaderId);
        } else {
            pickTaskService.unrelate(doHeaderId);
        }
    }

    /**
     * 获取波次中某商品的部分分拣或拣货完成的订单明细
     */
    @Override
    public List<DeliveryOrderDetail> findSortingDoDetails(Long waveId, Long skuId) {
        return this.doDetailDAO.findSortingDoDetail(waveId, skuId,
            new String[] {Constants.DoStatus.PARTSORTED.getValue(), Constants.DoStatus.ALLPICKED.getValue()});
    }

    /**
     * 获取波次中分拣完成的订单明细
     */
    @Override
    public List<DeliveryOrderDetail> findSortingDoDetailInWave(String waveNo, boolean isSorted) {
        return this.doDetailDAO.findSortingDoDetailInWave(waveNo, Constants.DoStatus.ALLPICKED.getValue(), isSorted);
    }

    /**
     * 订单中没有分拣（分配余量与分拣数量不一致）的明细的数量
     */
    @Override
    public int countUnSortingDoDetail(Long doHeaderId) {
        return this.doDetailDAO.countUnSortingDoDetail(doHeaderId);
    }

    /**
     * 获取波次中部分分拣、拣货完成状态订单数量；
     */
    @Override
    public int countUnSortingDoHeader(Long waveId) {
        return this.doHeaderDAO.countUnSortingDoHeader(waveId,
            new String[] {Constants.DoStatus.PARTSORTED.getValue(), Constants.DoStatus.ALLPICKED.getValue()});
    }

    /**
     * 根据DO_NO查询订单DeliveryOrderHeader（不存在抛DO_NOT_EXIST异常）
     */
    @Override
    public DeliveryOrderHeader findDoHeaderByDoNo(String doNo) throws DeliveryException {
        DeliveryOrderHeader doHeader = null;
        if (StringUtil.isEmpty(doNo)) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        doHeader = this.doHeaderDAO.findByOrderCode(doNo);
        if (doHeader == null) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        return doHeader;
    }

    /**
     * 根据trackingNo查询订单DeliveryOrderHeader（不存在不抛异常）
     */
    @Override
    public DeliveryOrderHeader findDoHeaderByTrackingNo(String trackingNo) {
        if (StringUtil.isEmpty(trackingNo)) {
            return null;
        }
        return this.doHeaderDAO.findByTrackingNo(trackingNo);
    }

    /**
     * 根据DO_NO查询订单DeliveryOrderHeader（不存在范围为空）
     */
    @Override
    public DeliveryOrderHeader findDoHeaderByDoNoWithoutException(String doNo) {
        return doHeaderDAO.findByOrderCode(doNo);
    }

    /**
     * 更新波次订单为分拣完成
     */
    @Override
    public void updateDoHeaderByWaveId(Long waveId, String filterStatus) {
        this.doHeaderDAO.updateDoHeaderByWaveId(waveId, DoStatus.ALLSORTED.getValue(), DateUtil.getNowTime(),
            filterStatus);
    }

    /**
     * 更新波次订单明细为分拣完成
     */
    @Override
    public void updateDoDetailByWaveId(Long waveId, String filterStatus) {
        this.doDetailDAO.updateDoDetailByWaveId(waveId, DoStatus.ALLSORTED.getValue(), filterStatus);
    }

    /**
     * 拣货位库存变更事件触发，清楚订单的补货状态
     */
    @Override
    @Transactional
    @Observer("PICK_LOC_STOCK_ADD_EVENT")
    public void onPickLocationStockChange(Long skuId, String lotatt04, String lotatt06, String lotatt08) {
        if (com.daxia.wms.master.helper.SysConfigHelper
            .getSwitchDefalutClosed("allocate.delivery.isMerchantShareStock")) {
            lotatt06 = null; // 共享库存忽略商家属性；
        }
        this.clearDoDetailReplNum(skuId, lotatt04, lotatt06, lotatt08);
        doDetailDAO.clearNoStockFlag(skuId, lotatt04, lotatt06, lotatt08); // 更新分配明细缺货标记
        List<Long> skuIds = new ArrayList<Long>();
        skuIds.add(skuId);
        this.checkDoHeaderReplStatus(skuIds);
        doHeaderDAO.checkNoStockFlag(skuIds);// 更新分配头缺货标记
    }

    @Transactional
    @Observer("UPDATE_SKU_GIFT_EVENT")
    public void onSkuChange2Gift(Long skuId) {
        List<Warehouse> warehouses = warehouseService.findAllRunWhInTenant();
        if (org.springframework.util.CollectionUtils.isEmpty(warehouses)) {
            return;
        }
        for (Warehouse warehouse : warehouses) {
            ParamUtil.setCurrentWarehouseId(warehouse.getId());
            this.clearDoDetailReplNum(skuId);
            doDetailDAO.clearNoStockFlag(skuId); // 更新分配明细缺货标记
            List<Long> skuIds = new ArrayList<Long>();
            skuIds.add(skuId);
            this.checkDoHeaderReplStatus(skuIds);
            doHeaderDAO.checkNoStockFlag(skuIds);// 更新分配头缺货标记
        }
    }

    @Transactional
    @Loggable
    public void clearDoDetailReplNum(Long skuId) {
        doDetailDAO.clearReplNum(skuId);
    }

    /**
     * 清空DoDetail补货数量
     */
    @Override
    @Transactional
    @Loggable
    public void clearDoDetailReplNum(Long skuId, String lotatt04, String lotatt06, String lotatt08) {
        doDetailDAO.clearReplNum(skuId, lotatt04, lotatt06, lotatt08);
    }

    /**
     * 修改订单的状态为补货完成; 订单满足一下条件：
     * <ul>
     * <li>补货状态为等待补货</li>
     * <li>补货的明细补货完毕则修改补货状态为补货完成</li>
     * <li>订单包含某范围的商品</li>
     * </ul>
     */
    @Override
    @Transactional
    public void checkDoHeaderReplStatus(List<Long> skuIds) {
        doHeaderDAO.checkReplStatus(skuIds);
    }

    /**
     * 反拣任务订单
     *
     * @param docId
     * @throws DeliveryException
     */
    private void checkRePickTaskIsFinish(Long docId) throws DeliveryException {
        Long count = reversePickHeaderService.getNeedDoingTaskCount(docId);
        if (count.longValue() > 0) {
            throw new DeliveryException(DeliveryException.CANCEL_DO_FAILD_HASRKTASK);
        }
    }

    /**
     * 更新订单重量
     */
    @Override
    @Transactional
    public void upddateDoWeight(Long doId) {
        doHeaderDAO.upddateDoWeight(doId);
    }

    /**
     * 获取订单的DoTimeInfoDto
     */
    @Override
    public DoTimeInfoDto getDoTimeInfo(DeliveryOrderHeader doHeader) {
        // 取得订单进入波次时间
        Date waveTime = doHeaderDAO.getWaveTime(doHeader.getId());

        // 取得订单交接时间
        Date handoverTime = loadDetailDAO.getHandoverTime(doHeader.getId());
        DoTimeInfoDto result = new DoTimeInfoDto();
        result.setDoCreateTime(doHeader.getDoCreateTime());
        result.setCreateTime(doHeader.getCreatedAt());
        result.setAllocTime(doHeader.getAllocTime());
        result.setWaveTime(waveTime);
        result.setPickEndTime(doHeader.getPickEndTime());
        result.setSortTime(doHeader.getSortTime());
        result.setPackEndTime(doHeader.getPackEndTime());
        result.setHandoverTime(handoverTime);
        result.setShipTime(doHeader.getShipTime());
        result.setWeighTime(doHeader.getLastWeighTime());
        return result;
    }

    /**
     * 获取订单的DoTimeInfoDto
     */
    private DoTimeInfoDto getDoTimeInfoHis(DeliveryOrderHeaderHis doHeaderhis) {
        WaveHeader wvHeader = doHeaderhis.getWaveHeader();
        // 取得订单进入波次时间
        Date waveTime = null;
        if (wvHeader != null) {
            waveTime = wvHeader.getCreatedAt();
        }

        // 取得订单交接时间
        Date handoverTime = loadDetailHisDAO.getHandoverTime(doHeaderhis.getId());
        DoTimeInfoDto result = new DoTimeInfoDto();
        result.setDoCreateTime(doHeaderhis.getDoCreateTime());
        result.setCreateTime(doHeaderhis.getCreatedAt());
        result.setAllocTime(doHeaderhis.getAllocTime());
        result.setWaveTime(waveTime);
        result.setPickEndTime(doHeaderhis.getPickEndTime());
        result.setSortTime(doHeaderhis.getSortTime());
        result.setPackEndTime(doHeaderhis.getPackEndTime());
        result.setHandoverTime(handoverTime);
        result.setShipTime(doHeaderhis.getShipTime());
        return result;
    }

    /**
     * 获取订单的DoTimeInfoDto
     */
    @Override
    public DoTimeInfoDto getDoTimeInfo(Long doHeaderId, boolean isHis) {
        if (isHis) {
            return this.getDoTimeInfoHis(doHeaderHisDAO.get(doHeaderId));
        } else {
            return this.getDoTimeInfo(doHeaderDAO.get(doHeaderId));
        }
    }

    /**
     * 将doHeader改为已跑波次并更新doHeader所属波次
     */
    @Override
    @Transactional
    public void updateDOs4Wave(DeliveryOrderHeader doHeader, Long waveId, int sortGridNo) {
        // 设置分拣格号
        doHeader.setSortGridNo(String.valueOf(sortGridNo));
        doHeader.setWaveFlag(YesNo.YES.getValue());
        doHeader.setWaveId(waveId);
        doHeaderDAO.update(doHeader);
    }

    /**
     * 订单是否分拣完成
     */
    @Override
    public boolean isDoHeaderSorted(Long doHeaderId) {
        List<DeliveryOrderDetail> doDetails = this.getDoDetailByDoHeaderId(doHeaderId);
        for (DeliveryOrderDetail doDetail : doDetails) {
            if (doDetail.getIsDoLeaf().intValue() == 0) {
                continue;
            }
            if (!Constants.DoStatus.ALLSORTED.getValue().equals(doDetail.getLineStatus())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 处理缺发的订单明细（适用于调拨、RTV、RMA波次） ：<br/>
     * 更新 波次下分配数量为0，状态为分配完成的订单明细 为 拣货完成
     */
    @Override
    @Transactional
    public void updateUnAllocDoDetails(WaveHeader wave) {
        String waveType = wave.getWaveType();
        if (null != waveType && StringUtil.isIn(waveType, WaveType.WAVE_RTV.getValue(), WaveType.WAVE_TT.getValue(),
            WaveType.WAVE_2B.getValue())) {
            log.info("Be going to deal the unAllocated doDetail");
            doDetailDAO.updateUnAllocDoDetailStatus(wave.getId(), DoStatus.ALLPICKED.getValue());
        }
    }

    /**
     * 订单ID获取该订单的明细打印数据
     */
    @Override
    public List<DoPrintSub> getDoBatchDetail(Long id) {
        return doDetailDAO.getDoBatchDetail(id);
    }

    /**
     * 波次ID获取订单明细
     */
    @Override
    public List<DeliveryOrderDetail> findDoDetailByWaveId(Long waveId) {
        return doDetailDAO.findDoDetailByWaveId(waveId);
    }

    /**
     * 手动冻结
     */
    @Override
    @Transactional
    public void manualFrozen(Long doHeaderId, String reasonCode, String holdNotes, String frozenBy) {
        DeliveryOrderHeader doHeader = this.validate2Frozen(doHeaderId, reasonCode, false);
        this.frozenByDoHeader(doHeader, reasonCode, holdNotes, frozenBy);
    }

    private void frozenByDoHeader(DeliveryOrderHeader doHeader, String reasonCode, String holdNotes, String frozenBy) {
        // 如果do异常状态时已完成，再次手动冻结时候清空其缺货明细
        if (DoExpStatus.COMPLETE.getValue().equals(doHeader.getExceptionStatus())) {
            doLackHeaderService.deleteLackInfoByDoId(doHeader.getId());
        }
        // 如果发货单处于分拣完成之后 要根据配置项判断是否需要绑定分拣筐
        if (StringUtil.isIn(doHeader.getStatus(), Constants.DoStatus.ALLSORTED.getValue(),
            Constants.DoStatus.PART_CARTON.getValue(), Constants.DoStatus.ALL_CARTON.getValue())) {
            this.checkExDoNeedSortContainer(doHeader);
        }

        String expStatus = "";
        Boolean isNoNeedCsFeedBack =
            SystemConfig.configIsClosed("notify.cs.notNeed.feedback", ParamUtil.getCurrentWarehouseId());
        if (isNoNeedCsFeedBack) {
            if (DoStatus.INITIAL.getValue().equals(doHeader.getStatus())) {
                expStatus = DoExpStatus.TO_BE_REPL.getValue();
            } else {
                expStatus = DoExpStatus.TO_BE_ROLLBACK.getValue();
            }
        } else {
            expStatus = DoExpStatus.TO_BE_ANNOUNCE.getValue();
        }
        if (StringUtils.isEmpty(doHeader.getFirstHoldCode())) {
            doHeader.setFirstHoldCode(reasonCode);
        }
        this.frozen(doHeader, reasonCode, holdNotes, frozenBy, expStatus, ExOpType.MANUAL_HOLD.getValue());
    }

    @Override
    @Transactional
    public void manualFrozenForHigh(Long doHeaderId, String reasonCode, String holdNotes, String frozenBy) {
        DeliveryOrderHeader doHeader = this.validate2Frozen(doHeaderId, reasonCode, false);
        // 如果do异常状态时已完成，再次手动冻结时候清空其缺货明细
        if (DoExpStatus.COMPLETE.getValue().equals(doHeader.getExceptionStatus())) {
            doLackHeaderService.deleteLackInfoByDoId(doHeader.getId());
        }

        // 客服冻结的订单,状态改为待反馈,需要客服来释放.
        String expStatus = DoExpStatus.TO_BE_FEEDBACK.getValue();
        if (StringUtil.isIn(doHeader.getDoType(), DoType.RTV.getValue().toString(),
            DoType.ALLOT.getValue().toString())) {
            if (StringUtil.isEmpty(reasonCode)) {
                reasonCode = Reason.WAIT_REPL.getValue();
            }
        } else {
            if (StringUtils.isEmpty(doHeader.getFirstHoldCode())) {
                doHeader.setFirstHoldCode(reasonCode);
            }
        }
        this.frozen(doHeader, reasonCode, holdNotes, frozenBy, expStatus, ExOpType.CS_WAIT.getValue());
    }

    /**
     * 冻结操作公共方法
     *
     * @param frozenDoHeader
     * @param reasonCode
     * @param holdNotes
     * @param holdWho
     * @param toExceptionStatus
     */
    @Override
    @Transactional
    public void frozen(DeliveryOrderHeader frozenDoHeader, String reasonCode, String holdNotes, String holdWho,
        String toExceptionStatus, String opType) {
        // 记录日志from信息
        DoExceptionLog doExceptionLog = new DoExceptionLog();
        doExceptionLog.setDoHeaderId(frozenDoHeader.getId());
        doExceptionLog.setDoNo(frozenDoHeader.getDoNo());
        doExceptionLog.setDoType(frozenDoHeader.getDoType());
        doExceptionLog.setFmDoStatus(frozenDoHeader.getStatus());
        doExceptionLog.setFmReleaseStatus(frozenDoHeader.getReleaseStatus());
        doExceptionLog.setFmExceptionStatus(frozenDoHeader.getExceptionStatus());
        doExceptionLog.setCarrierId(frozenDoHeader.getCarrierId());

        // 冻结操作
        frozenDoHeader.setReleaseStatus(Constants.ReleaseStatus.HOLD.getValue());
        frozenDoHeader.setHoldTime(DateUtil.getNowTime());
        frozenDoHeader.setHoldCode(reasonCode);
        // 如果是第一次冻结，记录初始冻结原因
        if (StringUtils.isEmpty(frozenDoHeader.getFirstHoldCode())) {
            frozenDoHeader.setFirstHoldCode(reasonCode);
        }
        Map<String, String> resonMap = Dictionary.getDictionary("REASON_HDD");
        frozenDoHeader.setHoldReason(resonMap.get(reasonCode));
        frozenDoHeader.setExceptionStatus(toExceptionStatus);
        if (StringUtil.isEmpty(holdWho)) {
            Identity identity = (Identity)Component.getInstance("org.jboss.seam.security.identity");
            if (identity != null && identity.getCredentials() != null) {
                String userName = identity.getCredentials().getUsername();
                frozenDoHeader.setHoldWho(userName == null ? "" : userName);
            } else {
                frozenDoHeader.setHoldWho("");
            }
        } else {
            frozenDoHeader.setHoldWho(holdWho);
        }
        this.doHeaderDAO.update(frozenDoHeader);
        // 同时冻结分配订单头
        DoAllocateHeader allocateHeader = doAllocateHeaderDAO.get(frozenDoHeader.getId());
        if (allocateHeader != null) {
            // 分配缺货 ，变更缺货状态。
            if (ExOpType.ALLOC.getValue().equals(toExceptionStatus)) {
                allocateHeader.setNoStockFlag(YesNo.YES.getValue());
            }
            allocateHeader.setReleaseStatus(frozenDoHeader.getReleaseStatus());
            doAllocateHeaderDAO.update(allocateHeader);
        }

        // 记录日志to信息
        doExceptionLog.setToDoStatus(frozenDoHeader.getStatus());
        doExceptionLog.setToReleaseStatus(frozenDoHeader.getReleaseStatus());
        doExceptionLog.setToExceptionStatus(frozenDoHeader.getExceptionStatus());
        doExceptionLog.setHoldReason(frozenDoHeader.getHoldReason());
        doExceptionLog.setHoldTime(frozenDoHeader.getHoldTime());
        doExceptionLog.setOperator(frozenDoHeader.getHoldWho());
        doExceptionLog.setNotes(holdNotes);
        doExceptionLog.setOperationType(opType);
        doExceptionLogService.saveDoExceptionLog(doExceptionLog);

        // 记录订单日志,冻结
        orderLogService.saveLog(frozenDoHeader, OrderLogConstants.OrderLogType.ORDER_HOLD.getValue(),
            ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_ORDER_HOLD, null,
                resonMap.get(reasonCode) + " " + (StringUtil.isEmpty(holdNotes) ? "" : holdNotes)));
    }

    /**
     * 验证发货单是否可以手动冻结
     *
     * @param doHeaderId
     * @param reasonCode
     * @param isRF
     * @return
     */
    @Override
    public DeliveryOrderHeader validate2Frozen(Long doHeaderId, String reasonCode, boolean isRF) {
        if (null == doHeaderId) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        DeliveryOrderHeader doHeader = this.getDoHeaderById(doHeaderId);
        if (null == doHeader) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        // 初始化到装箱完成的才能冻结
        EnumSet<Constants.DoStatus> doStatus = EnumSet.range(Constants.DoStatus.INITIAL, Constants.DoStatus.ALL_CARTON);
        if (!doStatus.contains(Constants.DoStatus.fromString(doHeader.getStatus()))) {
            throw new DeliveryException(DeliveryException.DO_CANNOT_FROZEN);
        }

        // 验证冻结原因
        if (StringUtil.isEmpty(reasonCode)) {
            throw new DeliveryException(DeliveryException.DO_FROZEN_REAZEN_NULL);
        }

        // 已经冻结的不能再冻结
        if (Constants.ReleaseStatus.HOLD.getValue().equals(doHeader.getReleaseStatus())) {
            if (isRF) {
                return doHeader;
            } else {
                throw new DeliveryException(DeliveryException.DO_ALREADY_FROZEN);
            }
        }

        return doHeader;
    }

    /**
     * DO_ID获取订单的异常状态、释放冻结状态、订单号、订单类型
     */
    @Override
    public Object getDoHeaderExceptionStatus(Long doId) {
        return doHeaderDAO.getDoHeaderExceptionStatus(doId);
    }

    /**
     * 波次ID查询订单
     */
    @Override
    public List<Long> findDoIdByWaveId(List<Long> ids) {
        return doHeaderDAO.findDoIdByWaveId(ids);
    }

    /**
     * 订单ID获取订单
     */
    @Override
    public DeliveryOrderHeader getDoHeaderById(Long id) {
        return doHeaderDAO.get(id);
    }


    /**
     * 订单ID获取归档订单
     */
    @Override
    public DeliveryOrderHeaderHis getHeaderHisById(Long id) {
        return doHeaderHisDAO.get(id);
    }

    /**
     * 获取波次下的任意一个do
     *
     * @param waveId
     * @return
     */
    @Override
    public DeliveryOrderHeader getDoHeaderByWaveId(Long waveId) {
        return doHeaderDAO.getDoHeaderByWaveId(waveId);
    }

    /**
     * 记录异常日志 【doNo】在【createAt】由【createBy】入暂存位【DoLackLocationCode】
     *
     * @param doHeader
     */
    public void createDoExceptionLog(DeliveryOrderHeader doHeader, String doLackLocation, String notes, String type) {
        DoExceptionLog doExceptionLog = new DoExceptionLog();
        doExceptionLog.setDoHeaderId(doHeader.getId());
        doExceptionLog.setDoNo(doHeader.getDoNo());
        doExceptionLog.setDoType(doHeader.getDoType());
        doExceptionLog.setFmDoStatus(doHeader.getStatus());
        doExceptionLog.setFmReleaseStatus(doHeader.getReleaseStatus());
        doExceptionLog.setFmExceptionStatus(doHeader.getExceptionStatus());
        doExceptionLog.setCarrierId(doHeader.getCarrierId());
        doExceptionLog.setToDoStatus(doHeader.getStatus());
        doExceptionLog.setToReleaseStatus(doHeader.getReleaseStatus());
        doExceptionLog.setToExceptionStatus(doHeader.getExceptionStatus());
        doExceptionLog.setHoldReason(doHeader.getHoldReason());
        doExceptionLog.setHoldTime(doHeader.getHoldTime());
        doExceptionLog.setOperator(identity.getCredentials().getUsername());
        doExceptionLog.setNotes(notes);
        doExceptionLog.setOperationType(type);
        doExceptionLogService.saveDoExceptionLog(doExceptionLog);
    }

    /**
     * 将波次waveHeaderId下的DO的分拣柜更新为指定分拣柜Id
     *
     * @param doHeader
     * @param waveHeaderId
     * @param sortingBinId
     */
    @Override
    @Transactional
    public void updateDoSortingBinByWave(DeliveryOrderHeader doHeader, Long waveHeaderId, Long sortingBinId) {
        if (null == doHeader) {
            return;
        }
        if (!sortingBinId.equals(doHeader.getSortingBinId())) {
            List<Long> ids = doHeaderDAO.findDoHeaderIdsByWaveId(waveHeaderId);
            List<Long> updateIds = Lists.newArrayList();
            for (Long id : ids) {
                if (id.equals(doHeader.getId())) {
                    continue;
                }
                updateIds.add(id);
            }
            doHeaderDAO.updateDoSortingBinByIds(updateIds, sortingBinId);

            doHeader.setSortingBinId(sortingBinId);
        }
    }

    /**
     * 查询可释放的DO集合
     *
     * @return
     */
    @Override
    public List<Long> queryAllCanReleaseDos() {
        return doHeaderDAO.queryAllCanReleaseDos();
    }

    /**
     * 根据doHeaderId获取do打印明细数据
     */
    @Override
    public List<DoPrintSub> getDoDetails4Print(Long doHeaderId) {
        return doDetailDAO.getDoDetails4Print(doHeaderId);
    }

    @Override
    public List<String> getNotWavedDoNosByIds(List<Long> ids) {
        return doHeaderDAO.getNotWavedDoNosByIds(ids);
    }

    @Override
    @Transactional
    public void updateDetails2Sorted(Long doHeaderId) {
        doDetailDAO.updateDetails2Sorted(doHeaderId);
    }

    @Override
    @Transactional
    public Long saveDoHeader(DoHeaderDto doHeaderDto) {
        Long headerId = doHeaderDAO.saveDoHeader(doHeaderDto);
        // 验证header是否插入成功
        DeliveryOrderHeader doHeader = this.getDoHeaderById(headerId);
        if (doHeader == null) {
            throw new DeliveryException(DeliveryException.IMPORT_NO_RESULT);
        }

        // 插入订单波次扩展表信息
        this.addDoWaveEx(doHeader);

        // 插入alc header
        doAllocateService.createDoAllocateHeader(doHeader);
        return headerId;
    }

    /**
     * 调拨单导入时新增订单波次扩展表
     *
     * @param doHeader
     */
    private void addDoWaveEx(DeliveryOrderHeader doHeader) {
        DoWaveEx doWaveEx = new DoWaveEx();
        doWaveEx.setDoHeaderId(doHeader.getId());
        doWaveEx.setWarehouseId(doHeader.getWarehouseId());
        doWaveExService.save(doWaveEx);
    }

    @Override
    @Transactional
    public void saveDoDetail(Long doHeaderId, DataList dataList, Integer totalNum) {
        // 批量插入明细
        doDetailDAO.batchSaveDetail(dataList);

        // 验证是否全部插入成功
        List<DeliveryOrderDetail> doDetails = this.getDoDetailByDoHeaderId(doHeaderId);
        if (ListUtil.isNullOrEmpty(doDetails) || doDetails.size() != totalNum.intValue()) {
            throw new DeliveryException(DeliveryException.IMPORT_NO_RESULT);
        }

        // 插入alc detail
        doAllocateService.createDoAllocateDetail(doDetails);
    }

    @Override
    @Transactional
    public void deleteDo(Long doHeaderId) {
        doHeaderDAO.deleteDoHeader(doHeaderId);
        doDetailDAO.deleteDoDetail(doHeaderId);
        doAllocateHeaderDAO.deleteAlcHeader(doHeaderId);
        doAllocateDetailDAO.deleteALcDetail(doHeaderId);
    }

    @Override
    @Transactional
    public void updateHeaderTotalQtyAndValuable(Long doId, BigDecimal totalQty, boolean isValuableFlag) {
        doHeaderDAO.updateHeaderTotalQtyAndValuable(doId, totalQty, isValuableFlag);
        doAllocateHeaderDAO.updateHeaderTotalQtyAndValuable(doId, totalQty, isValuableFlag);
    }

    @Override
    public List<Long> querySkuIdsInDoByBarcode(Long doId, String skuCode) {
        return doDetailDAO.querySkuIdsInDoByBarcode(doId, skuCode);
    }

    @Override
    public Long querySkuIdsInDoByProcode(Long doId, String skuCode) {
        List<Long> skuIds = doDetailDAO.querySkuIdsInDoByProcode(doId, skuCode);
        if (ListUtil.isNullOrEmpty(skuIds)) {
            return null;
        } else {
            return skuIds.get(0);
        }
    }

    @Override
    public List<DeliveryOrderHeader> findDoByIds(List<Long> doIds) {
        return doHeaderDAO.findDoHeadersByIds(doIds, false);
    }

    @Override
    public Boolean getNeedSortContainer() {
        Integer cfg = SystemConfig.getConfigValueInt("NEED_SORT_CONTAINER", ParamUtil.getCurrentWarehouseId());
        if (null != cfg && cfg.intValue() == 1) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public boolean needPrintInNewWay() {
        return SystemConfig.configIsOpen("print.invoice.useNewRealization", ParamUtil.getCurrentWarehouseId());
    }

    @Override
    public List<Long> findSkuByProductCodeAndWaveId(Long waveId, String barcode, String[] statuses) {
        return doDetailDAO.findSkuByCodeAndWaveId(waveId, barcode, statuses);
    }

    @Override
    public List<Long> findSkuByBarcodeAndWave(Long waveId, String barcode, String[] statuses) {
        return doDetailDAO.findSkuInWave(waveId, barcode, statuses);
    }

    @Override
    public void checkExDoNeedSortContainer(DeliveryOrderHeader doHeader) {
        Integer useSortContainerCfg =
            SystemConfig.getConfigValueInt("NEED_SORT_CONTAINER", ParamUtil.getCurrentWarehouseId());
        if (Constants.YesNo.YES.getValue().equals(useSortContainerCfg)
            && Constants.DoType.SELL.getValue().toString().equals(doHeader.getDoType())) {
            boolean isDoBindSortContainer = containerMgntService.isDoBindContainer(doHeader.getDoNo(),
                Constants.ContainerType.SORT_CONTAINER.getValue(), Constants.BindDocType.DELIVERYORDER.getValue());
            if (!isDoBindSortContainer) {
                throw new DeliveryException(DeliveryException.NOT_BIND_SORTCONTAINER);
            }
        }
    }

    @Override
    public String getWaveNoByDoId(Long doId) {
        return doHeaderDAO.getWaveNoByDoId(doId);
    }

    @Override
    public List<Long> qureyDoHeaderIdsByWaveId(Long waveId) {
        return doHeaderDAO.findDoHeaderIdsByWaveId(waveId);
    }

    @Override
    @Transactional
    public List<DeliveryOrderHeader> findDoByCartonIds(List<Long> cartonIds) {
        return doHeaderDAO.finDoByCartonIds(cartonIds);
    }

    @Override
    public DeliveryOrderHeader findRtvByOrderCodeOrRefNo(String doNo, Long whId) {
        return doHeaderDAO.findRtvByOrderCodeOrRefNo(doNo, whId);
    }

    @Override
    public List<DeliveryOrderDetail> getDoLeafDetailByDoHeaderId(Long doHeaderId) {
        return doDetailDAO.getDoLeafDetailByDoHeaderId(doHeaderId);
    }

    @Override
    public DoHeaderDto findByWaveIdAndConNo(Long waveId, String containerNo, String containerType) {
        Object[] object = doHeaderDAO.findByWaveIdAndConNo(waveId, containerNo, containerType);
        if (object == null) {
            return null;
        }
        DoHeaderDto header = new DoHeaderDto();
        header.setStatus((String)object[0]);
        header.setSortGridNo((String)object[1]);
        header.setDoNo((String)object[2]);
        header.setDoType((String)object[3]);
        header.setId((Long)object[4]);

        return header;
    }

    @Override
    public List<DeliveryOrderHeader> queryDoHeadersInWaves(List<Long> waveIds) {
        return doHeaderDAO.queryDoHeadersInWaves(waveIds);
    }

    /**
     * 订单ID后取订单头
     */
    @Override
    public DeliveryOrderHeader getHeaderById(Long doHeaderId) {
        return doHeaderDAO.get(doHeaderId);
    }

    @Override
    @Transactional
    public void modifyCarrier(Long fmCarrierId, DeliveryOrderHeader doHeader) {

        // 1.验证状态 初始化到装箱完成可以直接修改配送商
        Carrier carrier = carrierService.getCarrier(doHeader.getCarrierId());
        if (carrier == null) {
            throw new DeliveryException(MasterException.CARRIER_IS_NOT_EXIST);
        }
        if (Config.isDefaultTrue(Keys.Delivery.change_carrier_check_sync_waybill, Config.ConfigLevel.WAREHOUSE)) {
            if (!doHeader.getCarrierId().equals(fmCarrierId)) {
                boolean waybillSync =
                    orderLogService.existsLog(doHeader.getId(), OrderLogConstants.OrderLogType.WAYBILL_SYNC.getValue());
                if (waybillSync) {
                    throw new DeliveryException(DeliveryException.DO_IS_WAYBILL_SYNC, doHeader.getDoNo());
                }
            }
        }
        // 2.初始化到装箱完成之前，直接修改配送商
        if (doHeader.getStatus().compareTo(Constants.DoStatus.PART_CARTON.getValue()) == 0
            || doHeader.getStatus().compareTo(Constants.DoStatus.ALL_CARTON.getValue()) == 0) {
            // 装箱完成，清空箱信息
            CartonHeaderFilter cartonHeaderFilter = new CartonHeaderFilter();
            cartonHeaderFilter.setDoNo(doHeader.getDoNo());
            List<CartonHeader> cartonList = cartonService.query(cartonHeaderFilter);
            for (CartonHeader c : cartonList) {
                // 清空箱信息
                clearReCheckRecordService.clearCarton(doHeader.getId(), c.getId());
            }
        }

        // 记录日志
        this.insertCarrierLog(fmCarrierId, doHeader.getCarrierId(), doHeader.getId());
        // 取消预下单信息
        cartonService.cancelTempCartonByDoId(doHeader.getId());

        // 统一更新配送商信息
        DoAllocateHeader doAllocateHeader = doAllocateHeaderDAO.get(doHeader.getId());
        if (null != doAllocateHeader) {
            doAllocateHeader.setCarrierId(doHeader.getCarrierId());
            doAllocateHeaderDAO.update(doAllocateHeader);
        }
        doHeaderDAO.update(doHeader);
        doHeaderDAO.getSession().flush();
        doHeaderDAO.getSession().refresh(doHeader);
        // 记录日志
        this.insertOrderLog4Modify(doHeader);

        // 对于已经装箱完成的,修改配送信息,需要将所有的箱子重新获取箱信息
        if (doHeader.getStatus().compareTo(Constants.DoStatus.PART_CARTON.getValue()) > 0) {
            // 装箱完成， 重新获取箱信息
            CartonHeaderFilter cartonHeaderFilter = new CartonHeaderFilter();
            cartonHeaderFilter.setDoNo(doHeader.getDoNo());
            List<CartonHeader> cartonList = cartonService.query(cartonHeaderFilter);
            for (CartonHeader cartonHeader : cartonList) {
                // 调用菜鸟接口
                cartonService.cancelCartonById(cartonHeader.getId());
                cartonNoGenerateDispatcher.genNewCarton(doHeader, cartonHeader);
                cartonService.saveOrUpdate(cartonHeader);
            }
        }

        List<String> printedCartonNos = this.findPrintedCartonNos(doHeader.getDoNo());
        if (CollectionUtils.isNotEmpty(printedCartonNos)) {
            if (Config.isDefaultTrue(Keys.Delivery.check_change_do_after_print_carton, Config.ConfigLevel.WAREHOUSE)
                && AutoWaveType.BATCH_GROUP.getValue().equals(doHeader.getDoWaveEx().getAutoWaveType())) {
                throw new DeliveryException(DeliveryException.GROUP_ORDER_CARTON_PRINTED, doHeader.getDoNo());
            }
        }
    }

    @Override
    @Transactional
    public void generateAndUpdateSfMainNo(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) throws Exception {
        Map<String, String> request = new HashMap<String, String>();
        Warehouse warehouse = warehouseService.getWarehouse(doHeader.getWarehouseId());

        WarehouseCarrier warehouseCarrier = warehouseCarrierService
            .getCarrierInfoByWarehouseIdAndCarrierId(doHeader.getWarehouseId(), doHeader.getCarrierId());

        if (warehouseCarrier == null) {
            throw new DeliveryException("顺丰电子面单帐号信息没配置!");
        }
        // 生成一个箱号作为业务单号
        String txLogisticId = doHeader.getDoNo() + sequenceGeneratorService
            .generateSequenceNo(Constants.SequenceName.CARTONNO.getValue(), ParamUtil.getCurrentWarehouseId());
        cartonHeader.setTrackingNo(txLogisticId);
        // 订单号
        request.put("orderCode", txLogisticId);
        // 发货地省
        request.put("origionProvince", warehouse.getProvince().getProvinceCname());
        // 发货地市
        request.put("origionCity", warehouse.getCity().getCityCname());
        // 发货地地址
        request.put("origionAddress", warehouse.getAddressName());

        // 地址匹配,先匹配中文,如果中文没有就使用地址库
        if (StringUtils.isNotBlank(doHeader.getProvinceName())) {
            // 目的地省
            request.put("destProvince", StringUtil.trimAll(doHeader.getProvinceName()));
        } else {
            // 目的地省
            request.put("destProvince", doHeader.getProvinceInfo().getProvinceCname());
        }
        if (StringUtils.isNotBlank(doHeader.getCityName())) {
            // 目的地市
            request.put("destCity", StringUtil.trimAll(doHeader.getCityName()));
        } else {
            // 目的地市
            request.put("destCity", doHeader.getCityInfo().getCityCname());
        }

        if (StringUtils.isNotBlank(doHeader.getCountyName())) {
            // 目的区县
            request.put("destCounty", StringUtil.trimAll(doHeader.getCountyName()));
        } else {
            // 目的区县
            request.put("destCounty", doHeader.getCountyInfo().getCountyCname());
        }

        // 目的地地址
        request.put("destAddress", StringUtil.trimAll(doHeader.getAddress()));
        // 联系人
        request.put("contactName", doHeader.getConsigneeName());
        // 联系人电话
        request.put("contactPhone", DoUtil.decryptPhone(
            StringUtil.isNotEmpty(doHeader.getMobile()) ? doHeader.getMobile() : doHeader.getTelephone()));
        // 月结账号
        request.put("custId", warehouseCarrier.getExt1());

        // 货到付款账号
        if (StringUtil.isNotEmpty(warehouseCarrier.getExt6())) {
            request.put("codAccount", warehouseCarrier.getExt6());
        }

        // 货到付款
        if (doHeader.getReceivable().compareTo(BigDecimal.ZERO) > 0) {
            request.put("isCOD", "1");
            request.put("toCollectAmount", doHeader.getReceivable().toString());
        }

        // 发货公司
        request.put("jCompany", warehouse.getContactor());

        // 发货联系人
        request.put("jContact", warehouse.getContactor());

        // 发货联系人电话
        request.put("jTel", warehouse.getPhone());
        request.put("jMobile", warehouse.getMobile());
        request.put("parcelQuantity", doHeader.getParcelQuantity());

        // 快递类型
        request.put("sfExpressType", warehouseCarrier.getExt4());
        // 付款方式
        Integer orderNum =
            SystemConfig.getConfigValueInt("fee.receiverPay.unit.number", ParamUtil.getCurrentWarehouseId());
        if (orderNum != null && doHeader.getExpectedQty().intValue() < orderNum) {
            request.put("payMethod", "2");
        }
        // oppo特殊逻辑
        if (ParamUtil.getCurrentTenantCode().equals("oppo")) {
            // oppo订单重量，unit * 0.5
            request.put("cargoTotalWeight", String.valueOf(
                (doHeader.getExpectedQty().doubleValue() > 10 ? 10 : doHeader.getExpectedQty().doubleValue()) * 0.5));
        }
        // 是否设置默认重量
        Integer defaultWeight =
            SystemConfig.getConfigValueInt("delivery.order.sf.default.weight", ParamUtil.getCurrentWarehouseId());
        if (defaultWeight != null) {
            request.put("cargoTotalWeight", defaultWeight.toString());
        }

        // 保价
        boolean isInsure = Config.isDefaultFalse(Keys.Master.sf_waybill_is_insure, Config.ConfigLevel.WAREHOUSE);
        if (isInsure) {
            request.put("isInsure", "1");

            // 是否使用默认报价金额
            // boolean isUseDefaultInsureAmount = YesNo.YES.getValue().toString().equals(warehouseCarrier.getExt14());
            //// boolean isUseDefaultInsureAmount = Config.isDefaultFalse(
            //// Keys.Master.sf_waybill_use_default_insure_amount, Config.ConfigLevel.WAREHOUSE
            //// );
            // if (isUseDefaultInsureAmount) {
            // request.put("insureAmount", warehouseCarrier.getExt15());
            // } else {
            // request.put("insureAmount", doHeader.getAmountPayable().toString());
            // }
        }

        Map<String, String> result = sFOrderService.getMainNo(request, warehouseCarrier);
        if (result != null && result.get("mailNo") != null && null == result.get("destCode")) {
            throw new DeliveryException(DeliveryException.ERROR_SF_ADDEWSS);
        }
        if (result != null && result.get("mailNo") != null && result.get("originCode") != null
            && result.get("destCode") != null) {
            // 更新顺丰主单号、始发地代码、目的地代码
            DoWaveEx doWaveEx = doWaveExService.findByDoHeaderId(doHeader.getId());
            if (doWaveEx != null) {
                doWaveEx.setTrackingNo(result.get("mailNo").split(",")[0]);
                doWaveEx.setOriginCode(result.get("originCode"));
                doWaveEx.setDestinationCode(result.get("destCode"));
                doWaveExService.update(doWaveEx);
            }
            cartonHeader.setCartonNo(result.get("mailNo"));
            cartonHeader.setWayBill(result.get("mailNo"));
        }
    }

    /**
     * 记录配送商变更日志
     *
     * @param fmCarrierId
     * @param toCarrierId
     * @param doHeaderId
     */
    private void insertCarrierLog(Long fmCarrierId, Long toCarrierId, Long doHeaderId) {
        CarrierLog carrierLog = new CarrierLog();
        carrierLog.setDoHeaderId(doHeaderId);
        carrierLog.setCarrierId(fmCarrierId);
        carrierLog.setToCarrierId(toCarrierId);

        Timestamp nowTime = DateUtil.getNowTime();
        nowTime.setNanos(0);

        carrierLog.setCreatedAt(nowTime);
        carrierLog.setUpdatedAt(nowTime);
        carrierLog.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        carrierLogDAO.save(carrierLog);
    }

    @Override
    public String findFrozenOrdersByLoadHeaderId(Long loadHeaderId) {
        return doHeaderDAO.findFrozenOrdersByLoadHeaderId(loadHeaderId);
    }

    @Override
    public void updateStateByLoadHeaderId(Long id, String value) {
        doHeaderDAO.updateStateByLoadHeaderId(id, value);
    }

    @Override
    public Map<ReleaseStatus, Integer> countByReleaseStatus(Long waveId) {
        return doHeaderDAO.countByReleaseStatus(waveId);
    }

    @Override
    public String getMaxStatus(Long waveId) {
        return doHeaderDAO.getMaxStatus(waveId);
    }

    @Override
    public BigDecimal getAllocateQty(Long doHeaderId) {
        return doHeaderDAO.getAllocateQty(doHeaderId);
    }

    @Override
    public void updateWeightByWaveId(Long waveId, BigDecimal weight) {
        doHeaderDAO.updateWeightByWaveId(waveId, weight);
    }

    @Override
    public List<Long> findList4TempCarton(Integer rows, Long warehouseId, boolean localWaybillFlag,
        String jobParameter) {
        return doHeaderDAO.findList4TempCarton(warehouseId, rows, localWaybillFlag, jobParameter);
    }

    @Override
    public void insertOrderLog4Modify(DeliveryOrderHeader doHeader, boolean isOperatorNull) {
        // 记录日志,修改地址
        String carrierName = StringUtil.EMPTY;
        if (null != doHeader.getCarrier() && null != doHeader.getCarrier().getDistSuppCompName()) {
            carrierName = doHeader.getCarrier().getDistSuppCompName();
        }

        StringBuffer logInfo = new StringBuffer(carrierName);
        logInfo.append(",").append(doHeader.getConsigneeName());
        String telephone = DoUtil.decryptPhone(doHeader.getTelephone());
        if (StringUtils.isNotEmpty(telephone)) {
            logInfo.append(",").append(this.entryPhone(telephone));
        }

        String mobile = DoUtil.decryptPhone(doHeader.getMobile());
        if (StringUtils.isNotEmpty(mobile)) {
            logInfo.append(",").append(this.entryPhone(mobile));
        }

        if (logInfo.length() > 0) {
            logInfo.append(",");
        }
        logInfo.append(doHeader.getProvince() == null ? doHeader.getProvinceName()
            : doHeader.getProvinceInfo().getProvinceCname());
        logInfo.append(doHeader.getCity() == null ? doHeader.getCityName() : doHeader.getCityInfo().getCityCname());
        logInfo.append(
            doHeader.getCounty() == null ? doHeader.getCountyName() : doHeader.getCountyInfo().getCountyCname());
        logInfo.append(doHeader.getAddress());
        if (isOperatorNull) {
            orderLogService.saveLog(doHeader, OrderLogConstants.OrderLogType.MODIFY_DELIVERY_INFO.getValue(),
                ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_MODIFY_DELIVERY_INFO, null,
                    logInfo.toString()),
                null);
        } else {
            orderLogService.saveLog(doHeader, OrderLogConstants.OrderLogType.MODIFY_DELIVERY_INFO.getValue(),
                ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_MODIFY_DELIVERY_INFO, null,
                    logInfo.toString()));
        }
    }

    @Override
    public void insertOrderLog4Modify(DeliveryOrderHeader doHeader) {
        this.insertOrderLog4Modify(doHeader, false);
    }

    public void insertBatchOrderLog4Modify(List<Long> doHeaderIds, Long toCarrierId) {
        Carrier carrier = carrierService.getCarrier(toCarrierId);
        // 记录日志,修改地址
        StringBuffer logInfo = new StringBuffer(carrier.getDistSuppCompName());
        orderLogService.batchSaveLog(doHeaderIds, OrderLogConstants.OrderLogType.MODIFY_DELIVERY_INFO.getValue(),
            ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_MODIFY_DELIVERY_INFO, null,
                logInfo.toString()));
    }

    @Override
    public byte[] exportDeliveryOrderHeaderReport(DoHeaderFilter doHeaderFilter) {
        Integer maxCount =
            SystemConfig.getConfigValueInt("MAX_EXPORT_TRANS_LOG_NUM", ParamUtil.getCurrentWarehouseId());
        // 判断是否有配置最大允许导出记录数
        if (null == maxCount) {
            throw new StockException(StockException.NO_MAX_EXPORT_TRS_LOG_NUM);
        }
        DataPage<DoHeaderDto> dataPage = this.query(doHeaderFilter, 0, 0);
        // 判断待导出记录条数是否超过最大允许导出条数
        if (dataPage.getDataList().size() > maxCount) {
            throw new StockException(StockException.EXPORT_NUM_OUT_OF_MAX_NUM);
        }

        byte[] bytes = null;
        try {
            bytes = generateDeliveryOrderHeaderReport(dataPage.getDataList());
        } catch (IOException e) {
            throw new StockException(StockException.GEN_FILE_FAILED);
        }
        limitedOperLogService.createLog(LimitedOperType.DO_EXPORT.getValue(),
            "查询条数:" + dataPage.getDataList().size() + "; " + doHeaderFilter.toString());
        return bytes;
    }

    public static byte[] generateDeliveryOrderHeaderReport(List<DoHeaderDto> dtoList) throws IOException {
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("出库单数据");
        HSSFCellStyle style = ExcelUtil.setBorder(workbook);

        // 创建表头
        HSSFRow row = sheet.createRow(0);
        int index = 0;
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("发运订单号"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("关联单号"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("发货单类型"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("收货方"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("发运订单状态"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("冻结/释放"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("订货数量"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("发货数量"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("配送商"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("发票数量"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("创建时间"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("波次号"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("预计出库时间"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("发货时间"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("应收金额"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("重量"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("分拣格号"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("省"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("市"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("区"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("收货地址"), style);
        // ExcelUtil.createCell(row, (short) index++, new HSSFRichTextString("电话"), style);
        // ExcelUtil.createCell(row, (short) index++, new HSSFRichTextString("手机"), style);
        ExcelUtil.createCell(row, (short)index++, new HSSFRichTextString("备注"), style);
        // 创建数据
        for (int i = 0; i < dtoList.size(); i++) {
            DoHeaderDto dto = dtoList.get(i);
            row = sheet.createRow(i + 1);
            int index1 = 0;
            ExcelUtil.createCell(row, (short)index1++, new HSSFRichTextString(dto.getDoNo()), style); // 发运订单号
            ExcelUtil.createCell(row, (short)index1++, new HSSFRichTextString(dto.getRefNo1()), style);// 关联单号
            Map<String, String> doTypeDic = Dictionary.getDictionary("ODO_TYPE");
            ExcelUtil.createCell(row, (short)index1++, new HSSFRichTextString(doTypeDic.get(dto.getDoType())), style);// 发货单类型
            ExcelUtil.createCell(row, (short)index1++, new HSSFRichTextString(dto.getConsigneeName()), style);// 收货方
            Map<String, String> doStatusDic = Dictionary.getDictionary("DO_STATUS");
            ExcelUtil.createCell(row, (short)index1++, new HSSFRichTextString(doStatusDic.get(dto.getStatus())), style);// 发运订单状态
            Map<String, String> releaseStatusDic = Dictionary.getDictionary("RELEASE_STATUS");
            ExcelUtil.createCell(row, (short)index1++,
                new HSSFRichTextString(releaseStatusDic.get(dto.getReleaseStatus()).toString()), style); // 冻结/释放
            ExcelUtil.createCell(row, (short)index1++, new HSSFRichTextString(dto.getExpectedQty().toString()), style); // 订货数量
            ExcelUtil.createCell(row, (short)index1++,
                new HSSFRichTextString(dto.getShipQty() == null ? "" : dto.getShipQty().toString()), style); // 发货数量

            ExcelUtil.createCell(row, (short)index1++, new HSSFRichTextString(dto.getDistSuppCompName()), style);// 配送商
            ExcelUtil.createCell(row, (short)index1++, new HSSFRichTextString(dto.getInvoiceQty().toString()), style);// 发票数量
            ExcelUtil.createCell(row, (short)index1++,
                new HSSFRichTextString(DateUtil.dateToString(dto.getDoCreateTime(), DateUtil.DATETIME_PATTERN)), style); // 创建时间
            ExcelUtil.createCell(row, (short)index1++, new HSSFRichTextString(dto.getWaveNo()), style);// 波次号
            ExcelUtil.createCell(row, (short)index1++,
                new HSSFRichTextString(DateUtil.dateToString(dto.getDoFinishTime(), DateUtil.DATETIME_PATTERN)), style); // 预计出库时间
            ExcelUtil.createCell(row, (short)index1++,
                new HSSFRichTextString(DateUtil.dateToString(dto.getShipTime(), DateUtil.DATETIME_PATTERN)), style); // 发货时间
            ExcelUtil.createCell(row, (short)index1++, new HSSFRichTextString(dto.getReceivable().toString()), style); // 应收金额
            ExcelUtil.createCell(row, (short)index1++, new HSSFRichTextString(dto.getGrossWt().toString()), style);// 重量
            ExcelUtil.createCell(row, (short)index1++, new HSSFRichTextString(dto.getSortGridNo()), style);// 分拣格号
            ExcelUtil.createCell(row, (short)index1++, new HSSFRichTextString(dto.getProvince()), style);// 省
            ExcelUtil.createCell(row, (short)index1++, new HSSFRichTextString(dto.getCity()), style);// 市
            ExcelUtil.createCell(row, (short)index1++, new HSSFRichTextString(dto.getCounty()), style);// 区
            ExcelUtil.createCell(row, (short)index1++, new HSSFRichTextString(dto.getAddress()), style);// 收货地址
            // ExcelUtil.createCell(row, (short) index1++, new HSSFRichTextString(dto.getTelephone()), style);// 电话
            // ExcelUtil.createCell(row, (short) index1++, new HSSFRichTextString(dto.getMobile()), style);// 手机
            ExcelUtil.createCell(row, (short)index1++, new HSSFRichTextString(dto.getNotes()), style);// 备注
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        workbook.write(baos);
        return baos.toByteArray();
    }

    @Override
    public BigDecimal countAllResults(DoHeaderFilter doHeaderFilter) {
        return BigDecimal.valueOf(doHeaderDAO.countAllResults(doHeaderFilter));
    }

    @Override
    public List<DoHeaderDto> getAllResults(DoHeaderFilter doHeaderFilter) {
        DataPage<DoHeaderDto> dataPage = doHeaderDAO.findDoHeaderPageInfo(doHeaderFilter, 0,
            SystemConfig.getConfigValueInt("normal.maxNum.export", ParamUtil.getCurrentWarehouseId()));
        return dataPage.getDataList();
    }

    @Override
    public List<AutoCompleteDTO> findSkuHelperData(Long doId) {
        return doHeaderDAO.findSkuHelperData(doId);
    }

    @Override
    @Transactional
    public void batchModifyCarrier(List<Long> idList, Long toCarrierId) {
        if (ListUtil.isNullOrEmpty(idList)) {
            return;
        }
        Integer defaultCarrierId =
            SystemConfig.getConfigValueInt("dubhe.default.carrier.id", ParamUtil.getCurrentWarehouseId());
        if (defaultCarrierId == null) {
            defaultCarrierId = -9999;
        }
        if (toCarrierId.equals(defaultCarrierId.longValue())) {
            throw new DeliveryException(DeliveryException.DEFAULT_CARRIER_CAN_NOT_CHECK);
        }
        List<DeliveryOrderHeader> deliveryOrderHeaders = doHeaderDAO.findDoHeadersByIds(idList, false);
        Carrier carrier = carrierService.getCarrier(toCarrierId);
        for (DeliveryOrderHeader header : deliveryOrderHeaders) {
            if (DoStatus.PART_CARTON.getValue().compareTo(header.getStatus()) <= 0) {
                throw new DeliveryException(DeliveryException.DO_IS_ALL_CARTON, header.getDoNo());
            }
            if (!YesNo.YES.getValue().equals(carrier.getIsCod())
                && header.getReceivable().compareTo(BigDecimal.ZERO) > 0) {
                throw new DeliveryException(DeliveryException.DO_IS_COD, header.getDoNo());
            }

            if (toCarrierId.equals(header.getCarrierId())
                || Long.valueOf(defaultCarrierId).equals(header.getCarrierId())) {
                continue;
            } else {
                if (Config.isDefaultTrue(Keys.Delivery.change_carrier_check_sync_waybill,
                    Config.ConfigLevel.WAREHOUSE)) {
                    boolean waybillSync = orderLogService.existsLog(header.getId(),
                        OrderLogConstants.OrderLogType.WAYBILL_SYNC.getValue());
                    if (waybillSync) {
                        throw new DeliveryException(DeliveryException.DO_IS_WAYBILL_SYNC, header.getDoNo());
                    }
                }
                List<String> printedCartonNos = this.findPrintedCartonNos(header.getDoNo());
                if (CollectionUtils.isNotEmpty(printedCartonNos)) {
                    if (Config.isDefaultTrue(Keys.Delivery.check_change_do_after_print_carton,
                        Config.ConfigLevel.WAREHOUSE)
                        && AutoWaveType.BATCH_GROUP.getValue().equals(header.getDoWaveEx().getAutoWaveType())) {
                        throw new DeliveryException(DeliveryException.GROUP_ORDER_CARTON_PRINTED, header.getDoNo());
                    }
                }
                cartonService.cancelTempCartonByDoId(header.getId());
            }
        }
        // 记录交易日志
        this.insertBatchOrderLog4Modify(idList, toCarrierId);
        carrierLogDAO.insertCarrierLog(idList, toCarrierId);
        doHeaderDAO.updateCarrier(idList, toCarrierId);
        doAllocateHeaderDAO.updateCarrier(idList, toCarrierId);

    }

    @Override
    public void sendInvoice2Oms(WaveHeader waveHeader) {
        expFacadeService.sendInvoice2Oms(waveHeader.getId(), waveHeader.getWaveNo(), waveHeader.getWarehouseId());
    }

    @Override
    @Transactional
    public void batchUpdateDoDetail4Pick(List<Long> batchIdList, String updateBy, Long waveId) {
        doDetailDAO.batchUpdateDoDetail4Pick(batchIdList, updateBy, waveId);
    }

    @Override
    @Transactional
    public void batchUpdateDoHeader4Pick(List<Long> batchIdList, String updateBy, Long waveId) {
        doHeaderDAO.batchUpdateDoHeader4Pick(batchIdList, updateBy, waveId);
    }

    @Override
    public List<Long> getHoldDoListByPickTask(List<Long> batchIdList) {
        return doHeaderDAO.getHoldDoListByPickTask(batchIdList);
    }

    @Override
    public List<WaveDetailDTO> qureyWaveDetail(Long waveId) {
        return doHeaderDAO.qureyWaveDetail(waveId);
    }

    @Override
    public String getNoticeDesc(DeliveryOrderHeader doHeader, String operateType) {
        if (doHeader == null) {
            return null;
        }
        List<NoticeRule> ruleList = noticeRuleService.findListByOperateType(operateType);
        if (CollectionUtils.isEmpty(ruleList)) {
            return null;
        }
        for (NoticeRule noticeRule : ruleList) {
            try {
                String script = noticeRule.getScript();
                if (StringUtil.isNotEmpty(script)
                    && Boolean.TRUE.equals(MvelUtil.eval(script, ImmutableMap.of("doHeader", (Object)doHeader)))) {
                    return MvelUtil.eval(noticeRule.getNoticeDesc(),
                        ImmutableMap.of("doHeader", (Object)doHeader, "noticeRule", (Object)noticeRule)).toString();
                }
            } catch (Exception e) {
                continue;
            }
        }
        return null;
    }

    @Override
    public List<Long> getIdListByWaveIds(List<Long> ids) {
        return doHeaderDAO.getIdListByWaveIds(ids);
    }

    @Override
    public DeliveryOrderHeader getByTempCartonNo(String orderNo) {
        TempCarton tempCarton = tempCartonService.getByCartonNo(orderNo);
        if (tempCarton == null) {
            return null;
        }
        return doHeaderDAO.get(tempCarton.getDoHeaderId());
    }

    @Override
    public List<Long> getIdListByWaveIdAndSrotGrid(Long waveId, String sortGridFm, String sortGridTo) {
        return doHeaderDAO.getIdListByWaveIdAndSrotGrid(waveId, sortGridFm, sortGridTo);
    }

    @Override
    public DataPage<DeliveryOrderHeader> queryExceptionDo(BackExceptionFilter backExceptionFilter, int startIndex,
        int pageSize) {
        Integer defaultCarrierId =
            SystemConfig.getConfigValueInt("dubhe.default.carrier.id", ParamUtil.getCurrentWarehouseId());
        if (defaultCarrierId == null) {
            defaultCarrierId = -9999;
        }
        backExceptionFilter.setDefaultCarrierId(defaultCarrierId.longValue());
        return doHeaderDAO.findRangeByFilter(backExceptionFilter, startIndex, pageSize);
    }

    @Override
    @Transactional
    public void cleanGroup(Long doId) throws Exception {
        DeliveryOrderHeader deliveryOrderHeader = this.getDoHeaderById(doId);
        DoAllocateHeader doAllocateHeader = doAllocateService.getHeader(deliveryOrderHeader.getId());
        DoWaveEx doWaveEx = doAllocateHeader.getDoWaveEx();
        // if(!AutoWaveType.BATCH_GROUP.getValue().equals(doWaveEx.getAutoWaveType())){
        // return;
        // }
        doAllocateService.cancelAssign(doAllocateHeader, true, true);
        doWaveEx.setAutoWaveType(null);
        doWaveEx.setWaveCriteriaExId(0L);
        doWaveEx.setGroupTimes(0);
        doWaveExService.update(doWaveEx);
        doAllocateService.autoAllocate(doId);
    }

    @Override
    public List<DoDetailDto> getAllDetailResults(DoHeaderFilter doHeaderFilter) {
        DataPage<DoDetailDto> dataPage = doHeaderDAO.findDoDetailPageInfo(doHeaderFilter, 0,
            SystemConfig.getConfigValueInt("normal.maxNum.export", ParamUtil.getCurrentWarehouseId()));
        return dataPage.getDataList();
    }

    @Override
    public Long getNeedSyncSerialNoCount(Long doId) {
        return doHeaderDAO.getNeedSyncSerialNoCount(doId);
    }

    @Override
    public BigDecimal countAllDetailResults(DoHeaderFilter doHeaderFilter) {
        return BigDecimal.valueOf(doHeaderDAO.countAllDetailResults(doHeaderFilter));
    }

    @Override
    public Long getLineId(Long id) {
        return doHeaderDAO.getLineId(id);
    }

    @Override
    public List<CartonPrintDTO.ProductInfo> getGiftProductInfo(DeliveryOrderHeader doHeader) {
        List<DeliveryOrderDetail> details = doHeader.getDoDetails();
        ListUtil.megareList(details, new ListUtil.ListMegareOpr<DeliveryOrderDetail>() {
            @Override
            public boolean isNeedMegare(DeliveryOrderDetail t1, DeliveryOrderDetail t2) {
                return CompareUtil.compare(t1.getSkuId(), t2.getSkuId())
                    && CompareUtil.compare(t1.getPrice(), t2.getPrice());
            }

            @Override
            public void megareOpr(DeliveryOrderDetail t1, DeliveryOrderDetail t2) {
                t1.setPrintExceptedQty(t1.getPrintExceptedQty().add(t2.getExpectedQty()));
            }
        });
        Collections.sort(details, new Comparator<DeliveryOrderDetail>() {
            @Override
            public int compare(DeliveryOrderDetail o1, DeliveryOrderDetail o2) {
                int result = NumberUtils.object2BigDecimal(o2.getPrice(), BigDecimal.ZERO)
                    .compareTo(NumberUtils.object2BigDecimal(o1.getPrice(), BigDecimal.ZERO));
                if (result == 0) {
                    result = o2.getPrintExceptedQty().compareTo(o1.getPrintExceptedQty());
                }
                return result;
            }
        });
        List<CartonPrintDTO.ProductInfo> productInfos = Lists.newArrayList();
        details.forEach(deliveryOrderDetail -> {
            SkuDTO skuDTO = skuCache.getSku(deliveryOrderDetail.getSkuId());
            if (Constants.ProductType.GIFT.getValue().equals(skuDTO.getProductType())) {
                productInfos.add(new CartonPrintDTO.ProductInfo(skuDTO.getProductCname(),
                        deliveryOrderDetail.getPrintExceptedQty(), deliveryOrderDetail.getIsDoLeaf(),
                        deliveryOrderDetail.getOrigDetailId(), deliveryOrderDetail.getParentId(),
                        deliveryOrderDetail.getSkuId()));
            }
        });

        return productInfos;
    }

    public List<CartonPrintDTO.ProductInfo> sortDetail(List<CartonPrintDTO.ProductInfo> productInfos) {
        List<CartonPrintDTO.ProductInfo> resultSubList = new ArrayList<CartonPrintDTO.ProductInfo>();
        // 处理组合商品，使组合商品的父商品和字商品排在一起
        Map<String, List<CartonPrintDTO.ProductInfo>> childrenMap =
            new HashMap<String, List<CartonPrintDTO.ProductInfo>>();
        Map<String, CartonPrintDTO.ProductInfo> parentMap = new HashMap<String, CartonPrintDTO.ProductInfo>();
        for (CartonPrintDTO.ProductInfo sub : productInfos) {
            String origDetailId = sub.getOrigDetailId();
            String parentId = sub.getParentId();

            if (sub.getIsDoLeaf() != null && sub.getIsDoLeaf().intValue() == 1) {
                // qty = null,表示该记录尚无对应拣货任务，此时数量从do明细上取
                if (null == sub.getQty()) {
                    sub.setQty(BigDecimal.ZERO);
                }

                // 处理组合商品逻辑
                List<CartonPrintDTO.ProductInfo> children = childrenMap.get(parentId);
                if (children == null) {
                    children = new ArrayList<CartonPrintDTO.ProductInfo>();
                    childrenMap.put(parentId, children);
                }
                // 是组合商品，且是子商品
                if (parentId != null) {
                    children.add(sub);
                } else {
                    // 不是组合商品
                    resultSubList.add(sub);
                }
            } else {
                // 组合商品的父商品(父商品的数量从doDetail上取)
                sub.setQty(sub.getQty());
                parentMap.put(origDetailId, sub);
            }
        }
        Set<Map.Entry<String, CartonPrintDTO.ProductInfo>> parentEntrySet = parentMap.entrySet();
        for (Map.Entry<String, CartonPrintDTO.ProductInfo> entry : parentEntrySet) {
            resultSubList.add(entry.getValue());

            List<CartonPrintDTO.ProductInfo> children = childrenMap.get(entry.getKey());
            if (children != null) {
                resultSubList.addAll(children);

                childrenMap.remove(entry.getKey());
            }
        }
        if (childrenMap.size() > 0) {// 剩余有parentId，但是找不到的商品；
            for (Map.Entry<String, List<CartonPrintDTO.ProductInfo>> entry : childrenMap.entrySet()) {
                resultSubList.addAll(entry.getValue());
            }
        }
        return resultSubList;
    }

    @Override
    public String queryProductInfo(DeliveryOrderHeader doHeader) {
        List<DeliveryOrderDetail> details = doHeader.getDoDetails();
        ListUtil.megareList(details, new ListUtil.ListMegareOpr<DeliveryOrderDetail>() {
            @Override
            public boolean isNeedMegare(DeliveryOrderDetail t1, DeliveryOrderDetail t2) {
                return CompareUtil.compare(t1.getSkuId(), t2.getSkuId())
                    && CompareUtil.compare(t1.getPrice(), t2.getPrice());
            }

            @Override
            public void megareOpr(DeliveryOrderDetail t1, DeliveryOrderDetail t2) {
                t1.setPrintExceptedQty(t1.getPrintExceptedQty().add(t2.getExpectedQty()));
            }
        });
        Collections.sort(details, new Comparator<DeliveryOrderDetail>() {
            @Override
            public int compare(DeliveryOrderDetail o1, DeliveryOrderDetail o2) {
                int result = NumberUtils.object2BigDecimal(o2.getPrice(), BigDecimal.ZERO)
                    .compareTo(NumberUtils.object2BigDecimal(o1.getPrice(), BigDecimal.ZERO));
                if (result == 0) {
                    result = o2.getPrintExceptedQty().compareTo(o1.getPrintExceptedQty());
                }
                return result;
            }
        });
        int length = Config.getInt(Keys.Print.product_name_sub_length, Config.ConfigLevel.TENANT, 20);
        int rows = Config.getInt(Keys.Print.sub_rows, Config.ConfigLevel.TENANT, 4);
        StringBuffer s = new StringBuffer();
        for (int i = 0; i < details.size();) {
            s.append(StringUtil.cutWithTextSize(details.get(i).getSku().getProductCname(), length) + " 数量: "
                + details.get(i).getPrintExceptedQty());
            i++;
            if (i + 1 > rows) {
                break;
            }
            if (i < details.size()) {
                s.append("\r\n");
            }
        }

        return s.toString();
    }

    @Override
    public void checkEmergencyOrder4GenerateWave(List<Long> selectedDOIdList) {
        // 如果紧急波次按region分拣货单，允许混打波次
        if (!Config.isDefaultTrue(Keys.Delivery.wave_emergency_not_spit_region, Config.ConfigLevel.WAREHOUSE)) {
            return;
        }
        List<DeliveryOrderHeader> doList = this.findDoByIds(selectedDOIdList);
        Integer emergencyFlag = null;
        Set<String> specialCodeList = Sets.newHashSet();
        for (DeliveryOrderHeader doHeader : doList) {
            if (emergencyFlag == null) {
                emergencyFlag = doHeader.getEmergencyFlag();
                continue;
            }
            if (!doHeader.getEmergencyFlag().equals(emergencyFlag)) {
                // 存在紧急订单混打，抛异常
                throw new DeliveryException(DeliveryException.WAVE_EMERGENCY_ORDER_CAN_NOT_MIX);
            }
            //校验标记订单不能混打波次
            String specialCode = doHeader.getDoWaveEx().getSpecialLabelCode();
            specialCode = StringUtils.isBlank(specialCode) ? "DEF" : specialCode;
            specialCodeList.add(specialCode);
        }
        if (specialCodeList.size() > 1){
            throw new DeliveryException(DeliveryException.WAVE_SPECIAL_ORDER_CAN_NOT_MIX);
        }
    }

    @Override
    public List<String> findDoNoList4WavePickByPartDoNo(String doNo) {
        return doHeaderDAO.findDoNoList4WavePickByPartDoNo(doNo);
    }

    @Override
    public void clearSession() {
        doHeaderDAO.getSession().clear();
    }

    @Override
    @Transactional
    public void batchManualFrozen(List<Long> idList, String holdCode, String holdNotes, String operateBy) {
        List<DeliveryOrderHeader> doHeaderList = this.validate2BatchFrozen(idList, holdCode, holdNotes, operateBy);
        for (DeliveryOrderHeader doHeader : doHeaderList) {
            this.frozenByDoHeader(doHeader, holdCode, holdNotes, operateBy);
        }
    }

    @Override
    public List<String> findPrintedCartonNos(String doNo) {
        return printLogDAO.findPrintedCartonNos(doNo);
    }

    @Override
    public List<String> findDoNos4RecheckByPartDoNo(String doNo) {
        return doHeaderDAO.findDoNos4RecheckByPartDoNo(doNo);
    }

    @Override
    @Transactional
    public void updatePrintFlag(List<Long> doIds) {
        doHeaderDAO.updatePrintFlag(doIds);
    }

    @Override
    public List<DeliveryOrderHeader> getBySourceAsnIds(List<Long> asnHeaderIds) {
        return doHeaderDAO.getBySourceAsnIds(asnHeaderIds);
    }

    private List<DeliveryOrderHeader> validate2BatchFrozen(List<Long> idList, String holdCode, String holdNotes,
        String operateBy) {
        if (CollectionUtils.isEmpty(idList)) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        List<DeliveryOrderHeader> doHeaderList = this.findDoByIds(idList);
        if (CollectionUtils.isEmpty(doHeaderList)) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        // 验证冻结原因
        if (StringUtil.isEmpty(holdCode)) {
            throw new DeliveryException(DeliveryException.DO_FROZEN_REAZEN_NULL);
        }

        // 初始化到装箱完成的才能冻结
        EnumSet<Constants.DoStatus> doStatus = EnumSet.range(Constants.DoStatus.INITIAL, Constants.DoStatus.ALL_CARTON);
        for (DeliveryOrderHeader doHeader : doHeaderList) {
            if (!doStatus.contains(Constants.DoStatus.fromString(doHeader.getStatus()))) {
                throw new DeliveryException(DeliveryException.DO_CANNOT_FROZEN_DO_NO, doHeader.getDoNo());
            }
            // 已经冻结的不能再冻结
            if (Constants.ReleaseStatus.HOLD.getValue().equals(doHeader.getReleaseStatus())) {
                throw new DeliveryException(DeliveryException.DO_ALREADY_FROZEN_DO_NO, doHeader.getDoNo());
            }
        }
        return doHeaderList;
    }

    @Override
    @Transactional
    public boolean doCancelIntercept(Long doId) {
        DeliveryOrderHeader doHeader = this.getDoHeaderById(doId);
        if (null == doHeader || DoStatus.CANCELED.getValue().equals(doHeader.getStatus())) {
            return true;
        }

        SImpSrvMsg impSrvMsg = this.sImpSrvMsgDAO.getByRefId(doHeader.getOrigId(), doHeader.getWarehouseId());
        if (null == impSrvMsg) {
            impSrvMsg = this.sImpSrvMsgDAO.getByRefNo(doHeader.getDoNo(), doHeader.getWarehouseId());
        }
        if (null == impSrvMsg) {
            return false;
        }

        if (StringUtil.isEmpty(impSrvMsg.getImpData())) {
            return true;
        }

        Parser parser = ParserFactory.getParser("json");
        DoCancelRequest request = parser.stringToBean(impSrvMsg.getImpData(), DoCancelRequest.class);

        doHeader.setUpdatedBy(request.getCancelOperator());
        doHeader.setHoldWho(request.getCancelOperator());
        doHeader.setHoldTime(DateUtil.getNowTime());
        doHeader.setHoldCode(Reason.CS_CANCLE.getValue());
        // 如果是第一次冻结，记录初始冻结原因
        if (StringUtils.isEmpty(doHeader.getFirstHoldCode())) {
            doHeader.setFirstHoldCode(Reason.CS_CANCLE.getValue());
        }
        doHeader.setHoldReason(Dictionary.getDictionary("REASON_HDD").get(doHeader.getHoldCode()));
        doHeader.setNeedCancel(true);
        doHeader.setNotes("出库单取消拦截");
        doHeader.setReleaseStatus(ReleaseStatus.HOLD.getValue());
        // 保存异常日志
        DoExceptionLog doExceptionLog = this.changeDoExceptionLog(doHeader);

        // 分配时拦截
        if (DoStatus.PARTALLOCATED.getValue().equals(doHeader.getStatus())) {
            List<Long> doDetailIds = new ArrayList<Long>();
            for (DeliveryOrderDetail detail : doHeader.getDoDetails()) {
                doDetailIds.add(detail.getId());
            }
            // 释放已分配的库存
            doAllocateService.releaseAssignedStock(doHeader.getId(), doDetailIds);
        }

        if (StringUtil.isIn(doHeader.getStatus(), DoStatus.UNCHECKED.getValue(), DoStatus.INITIAL.getValue(),
            DoStatus.PARTALLOCATED.getValue())) {
            doExceptionLog.setToExceptionStatus(Constants.DoExpStatus.COMPLETE.getValue());
            doExceptionLog.setToDoStatus(Constants.DoStatus.CANCELED.getValue());
            doExceptionLog.setToReleaseStatus(doHeader.getReleaseStatus());
            doExceptionLogService.saveDoExceptionLog(doExceptionLog);

            doHeader.setStatus(Constants.DoStatus.CANCELED.getValue());
            doHeader.setExceptionStatus(Constants.DoExpStatus.COMPLETE.getValue());
            // 明细lineStatus 也设置成取消 90 状态
            doDetailDAO.updateDoDetailStatusByDoId(doHeader.getId(), Constants.DoStatus.CANCELED.getValue());

            doAllocateService.removeAllocate(doId);
        }

        // 核拣时拦截
        if (DoStatus.ALLSORTED.getValue().equals(doHeader.getStatus())) {
            doExceptionLog.setToExceptionStatus(Constants.DoExpStatus.TO_BE_ROLLBACK.getValue());// 异常状态为待回退
            doExceptionLog.setToDoStatus(doHeader.getStatus());
            doExceptionLog.setToReleaseStatus(Constants.ReleaseStatus.HOLD.getValue());
            // 保存异常日志
            doExceptionLogService.saveDoExceptionLog(doExceptionLog);

            // 部分拣货--装箱中，前台可申请取消，NEED_CANCEL=1,RELEASE='HD',STATUS不变,hold_code='03',Hold_Reason='客服申请取消订单'
            doHeader.setReleaseStatus(Constants.ReleaseStatus.HOLD.getValue());
            doHeader.setExceptionStatus(Constants.DoExpStatus.TO_BE_ROLLBACK.getValue());
        }
        doHeaderDAO.update(doHeader);

        // 判断是否有发票 有则调用作废发票接口
        this.cancleInvoice(doHeader);

        sImpSrvMsgDAO.deleteById(impSrvMsg.getId());

        orderLogService.saveLog(doHeader, OrderLogConstants.OrderLogType.ORDER_CANCEL.getValue(),
            ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_ORDER_CANCEL), doHeader.getHoldWho());

        return true;
    }

    private DoExceptionLog changeDoExceptionLog(DeliveryOrderHeader doHeader) {
        DoExceptionLog doExceptionLog = new DoExceptionLog();
        doExceptionLog.setCarrierId(doHeader.getCarrierId());
        doExceptionLog.setDoHeaderId(doHeader.getId());
        doExceptionLog.setDoNo(doHeader.getDoNo());
        doExceptionLog.setDoType(doHeader.getDoType());
        doExceptionLog.setFmDoStatus(doHeader.getStatus());
        doExceptionLog.setFmReleaseStatus(doHeader.getReleaseStatus());
        doExceptionLog.setFmExceptionStatus(doHeader.getExceptionStatus());
        doExceptionLog.setOperator("CS:" + StringUtil.notNullString(doHeader.getHoldWho()));
        doExceptionLog.setHoldReason(doHeader.getHoldReason());
        doExceptionLog.setHoldTime(DateUtil.getNowTime());
        doExceptionLog.setNotes("出库单取消拦截");
        doExceptionLog.setWarehouseId(doHeader.getWarehouseId());
        doExceptionLog.setOperationType(Constants.ExOpType.CS_CANCEL.getValue());
        return doExceptionLog;
    }

    public void cancleInvoice(DeliveryOrderHeader doHeader) {
        // 判断是否有发票 有则调用作废发票接口
        List<InvoiceNo> invoiceNoList = null;
        if (YesNo.YES.getValue().equals(doHeader.getInvoiceFlag())) {
            invoiceNoList = cancelInvoiceService.getInvoiceNoByDoNo(doHeader.getDoNo());
        }

        if (ListUtil.isNotEmpty(invoiceNoList)) {
            for (InvoiceNo invoiceNo : invoiceNoList) {
                cancelInvoiceService.cancelSingleInvoice(invoiceNo.getId());
            }
        }
        // 红冲电子发票
        electronicInvoiceService.writeBackInvoice(doHeader);
    }

    @Override
    @Transactional
    public void completePackForCrossDo(Long doHeaderId) {
        doHeaderDAO.updateStatus(doHeaderId, DoStatus.ALL_CARTON.getValue());
        doDetailDAO.updateDetails2Packed(doHeaderId);
    }

    @Override
    public List<Long> findCtnReCheckDoIds() {
        return doHeaderDAO.findCtnReCheckDoIds();
    }

    @Override
    @Transactional
    public void updateFailedNumber(Long doId) {
        DeliveryOrderHeader doHeader = doHeaderDAO.get(doId);
        doHeader.setFailedNumber(doHeader.getFailedNumber() + 1);
        doHeaderDAO.update(doHeader);
    }

    @Override
    public void modifyCarrier(List<Long> doIdList, Long carrierId) {
        doHeaderDAO.modifyCarrier(doIdList, carrierId);
        doHeaderDAO.getSession().flush();
        doHeaderDAO.getSession().clear();
    }

    @Override
    public boolean checkDoStatus(List<Long> doIdList, String fmStatus, String toStatus) {
        return doHeaderDAO.checkDoStatus(doIdList, fmStatus, toStatus);
    }

    @Override
    public List<DeliveryOrderHeader> queryByNos(List<String> doNoList, Long warehouseId) {
        return doHeaderDAO.queryByDoNos(doNoList, warehouseId);
    }

    @Override
    public List<Long> getDoListByPickTaskAndStatus(List<Long> batchIdList, String status) {
       return doHeaderDAO.getDoListByPickTaskAndStatus(batchIdList, status);
    }

    /**
     * 更新订单称重标识
     *
     */
    @Override
    @Transactional
    public void autoUpdateWeightFlag() {
        int batchSize = Config.getInt(Keys.Delivery.update_weight_flag_batch_num, Config.ConfigLevel.GLOBAL, 30);
        DoHeaderFilter filter = new DoHeaderFilter();
        filter.setStatusFrom(Constants.DoStatus.ALL_LOAD.getValue());
        filter.setStatusTo(Constants.DoStatus.ALL_DELIVER.getValue());
        filter.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        filter.setWeightFlag(Constants.YesNo.NO.getValue());
        filter.setDoType(Constants.DoType.SELL.getValue());
        List<DeliveryOrderHeader> list = doHeaderDAO.findByFilter(filter, 0, batchSize);
        if(list.isEmpty()){
            return;
        }
        List<Long> ids = new ArrayList<>();
        out:
        for (int i = 0; i < list.size(); i++) {
            DeliveryOrderHeader header = list.get(i);
            List<CartonHeader> cartons = header.getCartonHeaders();
            for (CartonHeader carton : cartons) {
                //需要将出库多日未称重订单进行批量更新称重标识
                if (
                    // 已出库
                        Objects.equals(header.getStatus(), Constants.DoStatus.ALL_DELIVER.getValue())
                        // 需要处理异常订单
                        && Objects.equals(Constants.YesNo.YES.name(),
                        AppConfig.getProperty("update.all_deliver.no_weight.flag", "NO"))
                        // 异常时间超过阈值
                        && (new Date().getTime() - header.getShipTime().getTime()) >
                        Integer.valueOf(AppConfig.getProperty("update.all_deliver.no_weight.day", "7")) * ONE_DAY
                ) {
                    ids.add(header.getId());
                    continue out;
                }

                // 存在未称重的则跳过
                if (!validateWeight(carton)) {
                    continue out;
                }
            }
            ids.add(header.getId());
        }
        if (!ids.isEmpty()) {
            doHeaderDAO.updateDoWeightFlag(ids);
        }
    }

    /**
     * 更新订单称重标识
     *
     * @param ids
     */
    @Override
    @Transactional
    public void updateWeightFlag(List<Long> ids) {
        if (!ids.isEmpty()) {
            doHeaderDAO.updateDoWeightFlag(ids);
        }
    }

    /**
     * 自动交接
     *
     * @param doId
     */
    @Override
    @Transactional
    public void autoLoad(Long doId) {
        DeliveryOrderHeader orderHeader = doHeaderDAO.get(doId);
        // apollo 配置允许人工触发自动交接的出库单类型
        String manualAutoLoadTypes = "manual.autoLoad.doTypes";
        String canAutoLoad = Arrays.stream(AppConfig.getProperty(manualAutoLoadTypes,MPS_OUT.getValue()).split(","))
                .filter(x -> Objects.equals(orderHeader.getDoType(), x))
                .findAny().get();
        if(Objects.nonNull(canAutoLoad)){
            for (CartonHeader cartonHeader : orderHeader.getCartonHeaders()) {
                cartonHeader.setAutoDeliveryFlag(YesNo.YES.getValue());
                cartonService.update(cartonHeader);
            }
        }
    }

    /**
     * 马来仓生成发票
     * @param ids
     */
    @Override
    public void geneInvoices(List<Long> ids){
        // 一次最多处理200条记录
        if(ids.size()>200){
            throw new DeliveryException(DeliveryException.DO_INVOICE_NUM_OVER);
        }
        List<DeliveryOrderHeader> orders = findDoByIds(ids);
        // 迭代下标
        Map<String,Object> paramMap=new HashMap<>();
        // 构建参数
        buildInvoiceParam(orders, paramMap);
        // 生成入参长度个sheet模板
        List<CellValue> valueList=new ArrayList<>();
        valueList.add(new CellValue(4,6,"{{invoiceNo","}}"));
        valueList.add(new CellValue(5,6,"{{date","}}"));
        valueList.add(new CellValue(9,3,"{{addr","}}"));
        valueList.add(new CellValue(10,3,"{{cityAndPost","}}"));
        valueList.add(new CellValue(11,3,"{{province","}}"));
        valueList.add(new CellValue(12,3,"Attn: {{consigneeInfo","}}"));
        valueList.add(new CellValue(16,3,"{{$fe: goodsList"," t.id"));
        valueList.add(new CellValue(19,8,"{{n:total","}}"));
        valueList.add(new CellValue(21,8,"{{n:total","}}"));
        String targetPath = "template/EastMY_invoice" + System.nanoTime() + ".xlsx";
        Object sheetNames = paramMap.get("sheetNames");
        String prefixUrl = AppConfig.getProperty("remote.print.templatePath");
        if(prefixUrl.endsWith("/")){
            prefixUrl=prefixUrl.substring(0,prefixUrl.length()-1);
        }
        EasyPoiUtil.copySheetAndReplaceKey(prefixUrl +"/template/EastMY_invoice.xlsx",ids.size()-1, targetPath,valueList, (List<String>)sheetNames);
        // 填充Excel
        TemplateExportParams params = new TemplateExportParams(DeliveryOrderServiceImpl.class.getClassLoader().getResource(targetPath).getPath());
        params.setScanAllsheet(true);
        Workbook workBook = ExcelExportUtil.exportExcel(params, paramMap);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            workBook.write(baos);
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 写入response
        try {
            DownloadUtil.writeToResponse(baos.toByteArray(), DownloadUtil.EXCEL, "发运订单发票.xlsx");
        } catch (IOException e) {
            throw new DeliveryException(DeliveryException.WRITE_INVOICE_ERROR);
        }
        // 删除文件
        FileUtil.delFile(DeliveryOrderServiceImpl.class.getClassLoader().getResource(targetPath).getPath());
    }
    /**
     * 计算指定SKU在订单明细中的总数量
     *
     * @param doDetails 订单明细列表
     * @param skuId 需要计算的SKU ID
     * @return 该SKU在订单中的总数量
     */
    private BigDecimal getSkuQuantityInOrder(List<DeliveryOrderDetail> doDetails, Long skuId) {
        if (doDetails == null || skuId == null) {
            return BigDecimal.ZERO;
        }
        return doDetails.stream()
                .filter(detail -> skuId.equals(detail.getSkuId()))
                .map(detail -> {
                    // 使用订单发货数量
                    BigDecimal qty = detail.getShippedQty();
                    return qty != null ? qty : BigDecimal.ZERO;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    @Override
    public String fixGroupWaveDoMaterialsLog(Long waveId) {
        // 订单必须已出库
        List<DeliveryOrderHeader> headers = qureyDoHeaderByWaveId(waveId).stream()
                .filter(header -> header.getStatus().equals(DoStatus.ALL_DELIVER.getValue()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(headers)) {
            return "waveId："+ waveId + "没有找到对应的出库单";
        }
        Map<Long, Long> materialMap =new HashMap<>();
        List<DeliveryOrderDetail> doDetails = headers.get(0).getDoDetails();
        if (CollectionUtils.isNotEmpty(doDetails)) {
            // Get all skuIds in one operation
            List<Long> skuIds = doDetails.stream()
                    .map(DeliveryOrderDetail::getSkuId)
                    .collect(Collectors.toList());

            // Fetch all SKUs in one database call
            List<Sku> skuList = skuService.querySkuList(skuIds);

            // Create a predicate for filtering SKUs with valid materialsNo
            Predicate<Sku> hasMaterials = sku -> org.apache.commons.lang3.StringUtils.isNotBlank(sku.getMaterialsNo()) &&
                    CollectionUtils.isNotEmpty(new Gson().fromJson(sku.getMaterialsNo(),
                            new TypeToken<List<String>>() {}.getType()));

            // Collect all material numbers in one pass
            Set<String> allMaterialsNo = skuList.stream()
                    .filter(hasMaterials)
                    .flatMap(sku -> JSON.parseArray(sku.getMaterialsNo(), String.class).stream())
                    .collect(Collectors.toSet());

            // Only fetch materials if we have material numbers
            if (!allMaterialsNo.isEmpty()) {
                // Fetch all materials in one database call
                List<Materials> materials1 = materialsService.getByMaterialNo(new ArrayList<>(allMaterialsNo));

                // Create mapping once
                Map<String, Long> materialNoToIdMap = materials1.stream()
                        .collect(Collectors.toMap(Materials::getMaterialsNo, Materials::getId, (a, b) -> a));
                // Process all SKUs with materials and update the materialMap
                skuList.stream()
                        .filter(hasMaterials)
                        .forEach(sku -> {
                            // 获取该SKU在订单中的数量
                            BigDecimal skuQty = getSkuQuantityInOrder(doDetails, sku.getId());
                            List<String> materialNos = JSON.parseArray(sku.getMaterialsNo(), String.class);
                            for (String materialNo : materialNos) {
                                Long materialId = materialNoToIdMap.get(materialNo);
                                if (materialId != null) {
                                    materialMap.merge(materialId, skuQty.longValue(), Long::sum);
                                }
                            }
                        });
                trsMaterialsLogService.batchSaveMaterialsOutInfo(waveId, materialMap, ParamUtil.getCurrentWarehouseId(), WmsUtil.getOperateBy());
            }
        }
        return "waveId："+  waveId + " 处理完成,增加了耗材日志条数："+materialMap.size()*headers.size();
    }

    /**
     * 构建发票参数
     * @param orders
     * @param paramMap
     */
    private void buildInvoiceParam(List<DeliveryOrderHeader> orders, Map<String, Object> paramMap) {
        int i=0;
        String date=LocalDate.now().format(DateTimeFormatter.ofPattern("dd/MM/YYYY"));
        List<DeliveryOrderDetail> doDetails;
        DeliveryOrderDetail detail;
        BigDecimal total;

        List<String> sheetNames=new ArrayList<>(orders.size());
        // sheetName
        paramMap.put("sheetNames",sheetNames);
        Sku sku;
        for (DeliveryOrderHeader order : orders) {
            sheetNames.add(order.getDoNo()+"_invoice");

            // 发票号
            paramMap.put("invoiceNo"+i,String.format("CVNACG%04d",jedisDao.incr("invoiceNo")));
            // 日期
            paramMap.put("date"+i,date);
            ArrayList<DoInvoiceDTO> list = new ArrayList<>();
            doDetails = order.getDoDetails();
            total=BigDecimal.ZERO;
            for (int j = 0; j < doDetails.size(); j++) {
                detail = doDetails.get(j);
                sku = detail.getSku();
                BigDecimal subSum = detail.getExpectedQty().multiply(sku.getPrice());
                list.add(new DoInvoiceDTO(j + 1, sku.getProductEname(),
                        detail.getExpectedQty(),  sku.getPrice(),
                         subSum));
                total=total.add(subSum);
            }
            // 商品列表
            paramMap.put("goodsList"+ i, list);
            // 收件人地址
            paramMap.put("addr"+ i, order.getAddress());
            // 收件人城市与邮编
            paramMap.put("cityAndPost"+ i, order.getCityName()+" "+order.getPostCode());
            // 收件人省份
            paramMap.put("province"+ i, order.getProvinceName());
            // 收件人名+电话
            paramMap.put("consigneeInfo"+ i, order.getConsigneeName()+" "+DoUtil.decryptPhone(order.getMobile()));
            // 总价
            paramMap.put("total"+ i++, total);

        }
    }

    /**
     * 验证包箱是否称重
     *
     * @param cartonHeader
     * @return
     */
    private boolean validateWeight(CartonHeader cartonHeader) {
        return Objects.equals(Constants.YesNo.YES.getValue(), cartonHeader.getIsWeight().intValue());
    }
    @Override
    @Transactional
    public DeliveryOrderHeader getDoHeaderByContainerNo(String containerNo) {
        return doHeaderDAO.getDoHeaderByContainerNo(containerNo);
    }

    private String entryPhone(String phone) {
        if (phone.indexOf("-") == -1) {
            return phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        }
        return phone.replaceAll("(\\d{3,4}-\\d{3})\\d{4,5}", "$1****");

    }


}