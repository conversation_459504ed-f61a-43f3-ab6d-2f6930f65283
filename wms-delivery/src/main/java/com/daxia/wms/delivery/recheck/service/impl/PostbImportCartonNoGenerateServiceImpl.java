package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;
import com.daxia.wms.master.service.SubDocNoService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

/**
 * Created by szy on 2016/8/9.
 */
@Name("postbImportCartonNoGenerateService")
@lombok.extern.slf4j.Slf4j
public class PostbImportCartonNoGenerateServiceImpl implements CartonNoGenerateService {

    @In
    private SubDocNoService subDocNoService;

    @Override
    public void generatorCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        String orderNo = doHeader.getDoNo();
        if (StringUtil.isEmpty(orderNo)) {
            throw new DeliveryException(DeliveryException.DO_NO_IS_NULL);
        }
        String cartonNo = subDocNoService.useOneByCarrierCorpId(Constants.CarrierCorp.POSTB.getValue(), null, null);
        if (StringUtil.isEmpty(cartonNo)) {
            throw new DeliveryException("邮政小包单号不足，请联系管理员导入！");
        }
        cartonHeader.setCartonNo(cartonNo);
        cartonHeader.setWayBill(cartonNo);
    }
}
