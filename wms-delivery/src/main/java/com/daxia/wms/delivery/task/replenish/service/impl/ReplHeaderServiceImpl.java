package com.daxia.wms.delivery.task.replenish.service.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Observer;
import org.jboss.seam.annotations.Transactional;

import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.TaskStatus;
import com.daxia.wms.Constants.TaskType;
import com.daxia.wms.delivery.task.replenish.dao.ReplenishHeaderDAO;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishHeader;
import com.daxia.wms.delivery.task.replenish.filter.ReplHeaderFilter;
import com.daxia.wms.delivery.task.replenish.service.FreeReplService;
import com.daxia.wms.delivery.task.replenish.service.ReplHeaderService;
import com.daxia.wms.delivery.util.LaborForceHelper;
import com.daxia.wms.master.entity.LaborHumanRegion;
import com.daxia.wms.stock.StockException;
import com.daxia.wms.stock.task.dao.TrsTaskDAO;

/**
 * 补货单业务实现类
 */
@Name("com.daxia.wms.stock.replHeaderService")
@lombok.extern.slf4j.Slf4j
public class ReplHeaderServiceImpl implements ReplHeaderService {

	private final static int ACHIEVE_MAX_COUNT= -1;
	
    @In
    private ReplenishHeaderDAO replenishHeaderDAO;
    
    @In
    private TrsTaskDAO trsTaskDAO;
    
    @In
    private FreeReplService freeReplService;

    @Override
	public DataPage<ReplenishHeader> query(ReplHeaderFilter replHeaderFilter, int startIndex, int pageSize) {
        return replenishHeaderDAO.findReplenishHeaderByFilter(replHeaderFilter, startIndex, pageSize);
    }

    /**
     * 发布补货任务--生成补货单
     * @param replType
     */
    @Override
	public void addReplTask(String replType, String doType, Boolean isAuto) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("warehouseId", ParamUtil.getCurrentWarehouseId());
        params.put("taskStatus", TaskStatus.INITIALIZED.getValue());
        //如果用户所选业务类型不为空，只查询当前业务类型的补货任务
        if (StringUtils.isNotEmpty(doType)) {
            params.put("doType", doType);
        }
        if (isAuto) {
            params.put("isAuto", Integer.valueOf(1));
        } else {
            params.put("isAuto", Integer.valueOf(0));
		}
        if (!this.trsTaskDAO.isTaskExists(params, TaskType.RP.getValue())) {
            throw new StockException(StockException.NO_INIT_REPL_TASK_EXIST);
        }

        String isBySrcPartition = SystemConfig.getConfigValue(RPL_BREAK_BY_PARTITION, ParamUtil.getCurrentWarehouseId());
        String isByToPartition = SystemConfig.getConfigValue(RPL_BREAK_BY_TO_PARTITION, ParamUtil.getCurrentWarehouseId());
        int maximumPerDoc = SystemConfig.getConfigValueInt(MAXIMUM_PER_REPL_HEADER, ParamUtil.getCurrentWarehouseId());
        
        // 是否分(From/To/FromTo)库区 的四种情况
		if (isBySrcPartition.equals("N") && isByToPartition.equals("N")) {
			addReplenishHeader(replType, maximumPerDoc, null, null, null, doType, isAuto, 0);
		} else if (isBySrcPartition.equals("Y") && isByToPartition.equals("N")) {
			addReplHeaderByPartition(replType, maximumPerDoc, TrsTaskDAO.FROM, doType, isAuto);
		} else if (isBySrcPartition.equals("N") && isByToPartition.equals("Y")) {
			addReplHeaderByPartition(replType, maximumPerDoc, TrsTaskDAO.TO, doType, isAuto);
		} else {
			addReplHeaderByPartition(replType, maximumPerDoc, TrsTaskDAO.FROM_AND_TO, doType, isAuto);
		}

    }
    
	/**
	 * 新增补货单，并把相应补货任务分(From/To/FromTo)库区纳入补货单，<br/>
	 * 返回 调用该方法生成了多少个补货单。<br/>
	 * 返回 ACHIEVE_MAX_COUNT=-1 表示 生成的单据 已经达到系统配置的最大数量
	 * 
	 * @param replType
	 *            补货类型
	 * @param maximumPerDoc
	 *            每张补货单含任务的最大数量
	 * @param partitionId
	 *            （From/To）库区ID
	 * @param partitionType
	 *            库区类别(From/To/FromTo)
	 * @param pId2
	 *            To库区ID（此参数不为Null时，partitionId 代表的是 From，partitionType为FromTo）
	 * @param isAuto
	 */
	private int addReplenishHeader(String replType, int maximumPerDoc, Long partitionId, String partitionType, Long pId2, String doType,
			Boolean isAuto, int docMadeCount) {
		int taskCount = trsTaskDAO.countReplTask(partitionId, replType, partitionType, pId2, doType, isAuto);
		int docCount = (taskCount % maximumPerDoc == 0) ? taskCount / maximumPerDoc : taskCount / maximumPerDoc + 1;
		int maxCount = getMaxDocCountCfg();
		for (int k = 0; k < docCount; k++) {
			if (k + docMadeCount >= maxCount) {
				// 生成的单据 已经达到系统配置的最大数量
				return ACHIEVE_MAX_COUNT;
			}
			freeReplService.createOneRplHeader(replType, maximumPerDoc, partitionId, partitionType, pId2, doType, isAuto);
		}
		return docCount;
	}

	private int getMaxDocCountCfg() {
		Integer maxCountCfg = SystemConfig.getConfigValueInt(REPLENISH_HEADER_MAXNUM, ParamUtil.getCurrentWarehouseId());
		return maxCountCfg == null ? 100 : maxCountCfg.intValue();// 默认值取100，即一个很大的值，相当于不限制数量
	}

	/**
     * 新增补货单，并把相应补货任务分(From/To/FromTo)库区纳入补货单
     * @param replType
     * @param maximumPerDoc
     * @param partitionType
     * @param isAuto 
     */
    @SuppressWarnings("rawtypes")
	private void addReplHeaderByPartition(String replType, int maximumPerDoc, String partitionType, String doType, Boolean isAuto) {
		// 是否 分源库区 且 分目标库区 生成补货单
		if (TrsTaskDAO.FROM_AND_TO.equals(partitionType)) {
			addReplHeaderByFmAndTo(replType, maximumPerDoc, doType, isAuto);
		} else {
			List partitions = trsTaskDAO.findPartition(partitionType);
			if (ListUtil.isNullOrEmpty(partitions)) {
				addReplenishHeader(replType, maximumPerDoc, null, null, null, doType, isAuto, 0);
			} else {
				// 一般情况，都是跑这个分支；if分支只是以防万一。
				addDocByPartition(partitions,replType,maximumPerDoc,partitionType,doType,isAuto);
			}
		}
	}

	/**
	 * 按库区生成补货单
	 * 
	 * @param partitions
	 * @param replType
	 * @param maximumPerDoc
	 * @param partitionType
	 * @param doType
	 * @param isAuto
	 */
	@SuppressWarnings("rawtypes")
	private void addDocByPartition(List partitions, String replType, int maximumPerDoc, String partitionType, String doType, Boolean isAuto) {
		int docMadeCount = 0;
		for (int i = 0; i < partitions.size(); i++) {
			Long pid = ((BigDecimal) partitions.get(i)).longValue();
			int newDocCount = addReplenishHeader(replType, maximumPerDoc, pid, partitionType, null, doType, isAuto, docMadeCount);
			if (newDocCount == ACHIEVE_MAX_COUNT) {
				// 单据数量已达到最大值
				break;
			} else {
				docMadeCount = docMadeCount + newDocCount;
			}
		}
	}

	/**
     * 新增补货单，并把相应补货任务分源和目标(From和To)库区纳入补货单
     * @param replType
     * @param maximumPerDoc
     */
    @SuppressWarnings("rawtypes")
    private void addReplHeaderByFmAndTo(String replType, int maximumPerDoc, String doType, Boolean isAuto) {
        List srcPartitions = trsTaskDAO.findPartition(TrsTaskDAO.FROM);
        List toPartitions = trsTaskDAO.findPartition(TrsTaskDAO.TO);
        if (ListUtil.isNullOrEmpty(srcPartitions) && ListUtil.isNullOrEmpty(toPartitions)) {
            addReplenishHeader(replType, maximumPerDoc, null, null, null, doType, isAuto, 0);
        } else if (!ListUtil.isNullOrEmpty(srcPartitions) && ListUtil.isNullOrEmpty(toPartitions)) {
			addDocByPartition(srcPartitions,replType,maximumPerDoc,TrsTaskDAO.FROM,doType,isAuto);
        } else if (ListUtil.isNullOrEmpty(srcPartitions) && !ListUtil.isNullOrEmpty(toPartitions)) {
			addDocByPartition(toPartitions,replType,maximumPerDoc,TrsTaskDAO.TO,doType,isAuto);
        } else {
			// 一般情况，都是跑这个分支；上述三个分支只是以防万一。
			int docMadeCount = 0;
			for (int i = 0; i < srcPartitions.size(); i++) {
				Long srcPid = ((BigInteger) srcPartitions.get(i)).longValue();
				for (int j = 0; j < toPartitions.size(); j++) {
					Long toPid = ((BigInteger) toPartitions.get(j)).longValue();
					int newDocCount = addReplenishHeader(replType, maximumPerDoc, srcPid, TrsTaskDAO.FROM_AND_TO, toPid, doType, isAuto, docMadeCount);
					if (newDocCount == ACHIEVE_MAX_COUNT) {
						// 单据数量已达到最大值
						break;
					} else {
						docMadeCount = docMadeCount + newDocCount;
					}
				}
			}
		}
    }
    
    /**
     * 按获取补货单头状态和补货任务状态查询补货单信息
     * @param replStatus 获取补货单头状态
     * @param taskStatus 补货任务状态
     * @return
     */
	@Override
	public List<Object> findAvailableReplNo(List<String> replStatus, List<String> taskStatus) {
		return replenishHeaderDAO.findAvailableReplNo(replStatus, taskStatus);
	}

	/**
     * 根据id获取补货单头
     * @param replHeaderId
     * @return
     */
    @Override
    public ReplenishHeader get(Long replHeaderId) {
        return replenishHeaderDAO.get(replHeaderId);
    }

    /**
     * 根据补货单号查询补货单头
     * @return
     */
	@Override
	public ReplenishHeader queryReplenishHeaderByReplNo(String replNo) {
		return replenishHeaderDAO.queryReplenishHeaderByReplNo(replNo);
	}

	@Override
	public Long countAvailableDoc(List<LaborHumanRegion> regionList) {
		if (LaborForceHelper.enableCross(regionList)) {
			return replenishHeaderDAO.countAvailableDoc(null);
		} else {
			List<Long> partitionIdList = LaborForceHelper.getIdOfPartitionList(regionList);
			return replenishHeaderDAO.countAvailableDoc(partitionIdList);
		}
	}

	@Override
	public void update(ReplenishHeader rpHeader) {
		replenishHeaderDAO.save(rpHeader);
	}
	
    @Override
    @Observer("TASK_RP_OPERATOR_CHANGE_EVENT")
    @Transactional
    public void updateOperator(String taskNo, Long newOperUserId) {
        ReplenishHeader header = this.queryReplenishHeaderByReplNo(taskNo);
        header.setOperUserId(newOperUserId);    
    }

	@Override
	public ReplenishHeader demandDoc(List<Long> partitionIdList, boolean canCross) {
		ReplenishHeader rpHeader = replenishHeaderDAO.demandDoc(partitionIdList);
		if (rpHeader == null && canCross) {
			rpHeader = replenishHeaderDAO.demandDoc(null);
		}
		return rpHeader;
	}
	
}
