package com.daxia.wms.delivery.invoice.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

/**
 * 发票号查询过滤器
 */
@lombok.extern.slf4j.Slf4j
public class InvoiceNoFilter extends WhBaseQueryFilter {

	private static final long serialVersionUID = 1511651915865204039L;

    private String doNo;            // DO单据号
    private String invoiceNo;       // 发票号
    private Long invoiceHeaderId;   // 开票信息ID
    private String invoiceCode;     // 发票代码
    private String invoiceNoFrom;   // 起始发票号码
    private String invoiceNoTo;     // 截止发票号码
    private String status;          // 发票号状态

	
	@Operation(fieldName = "doNo", operationType = OperationType.EQUAL)
	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}
	
	@Operation(fieldName = "invoiceCode", operationType = OperationType.EQUAL)
	public String getInvoiceCode() {
		return invoiceCode;
	}

	public void setInvoiceCode(String invoiceCode) {
		this.invoiceCode = invoiceCode;
	}

	@Operation(fieldName = "invoiceNo", operationType = OperationType.NOT_LESS_THAN)
	public String getInvoiceNoFrom() {
		return invoiceNoFrom;
	}

	public void setInvoiceNoFrom(String invoiceNoFrom) {
		this.invoiceNoFrom = invoiceNoFrom;
	}

	@Operation(fieldName = "invoiceNo", operationType = OperationType.NOT_GREAT_THAN)
	public String getInvoiceNoTo() {
		return invoiceNoTo;
	}

	public void setInvoiceNoTo(String invoiceNoTo) {
		this.invoiceNoTo = invoiceNoTo;
	}
	
	@Operation(fieldName = "status", operationType = OperationType.EQUAL)
	public String getStatus() {
		return status;
	}

	public void setStatus(String invoiceNoStatus) {
		this.status = invoiceNoStatus;
	}

	@Operation(fieldName = "invoiceNo", operationType = OperationType.EQUAL)
	public String getInvoiceNo() {
		return invoiceNo;
	}

	public void setInvoiceNo(String invoiceNo) {
		this.invoiceNo = invoiceNo;
	}
	
	@Operation(fieldName = "invoiceHeaderId", operationType = OperationType.EQUAL)
	public Long getInvoiceHeaderId() {
		return invoiceHeaderId;
	}

	public void setInvoiceHeaderId(Long invoiceHeaderId) {
		this.invoiceHeaderId = invoiceHeaderId;
	}

}
