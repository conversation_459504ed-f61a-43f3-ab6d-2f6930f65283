package com.daxia.wms.delivery.recheck.dto;

@lombok.extern.slf4j.Slf4j
public class CainiaoWaybillDTO {
    private String cpCode;
    private String branchName;
    private String allocatedQuantity;
    private String printQuantity;
    private String cancelQuantity;
    private String quantity;
    private String province;
    private String city;
    private String area;
    private String town;
    private String addressDetail;

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getAllocatedQuantity() {
        return allocatedQuantity;
    }

    public void setAllocatedQuantity(String allocatedQuantity) {
        this.allocatedQuantity = allocatedQuantity;
    }

    public String getPrintQuantity() {
        return printQuantity;
    }

    public void setPrintQuantity(String printQuantity) {
        this.printQuantity = printQuantity;
    }

    public String getCancelQuantity() {
        return cancelQuantity;
    }

    public void setCancelQuantity(String cancelQuantity) {
        this.cancelQuantity = cancelQuantity;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getAddressDetail() {
        return addressDetail;
    }

    public void setAddressDetail(String addressDetail) {
        this.addressDetail = addressDetail;
    }
}
