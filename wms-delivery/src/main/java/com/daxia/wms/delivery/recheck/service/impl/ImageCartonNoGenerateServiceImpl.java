package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoPrintDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoPrint;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.service.CartonNoGenerateService;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

@Name("imageCartonNoGenerateService")
@lombok.extern.slf4j.Slf4j
public class ImageCartonNoGenerateServiceImpl implements CartonNoGenerateService {

    @In
    private DoPrintDAO doPrintDAO;

    @Override
    public void generatorCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        String orderNo = doHeader.getDoNo();
        if (StringUtil.isEmpty(orderNo)) {
            throw new DeliveryException(DeliveryException.DO_NO_IS_NULL);
        }

        DoPrint doPrintInfo = doPrintDAO.findByDoHeaderId(doHeader.getId(), Constants.DoPrintInfoType.WAYBILL_IMAGE_URL.getValue());
        DoPrint doPrintInfo1 = doPrintDAO.findByDoHeaderId(doHeader.getId(),
                Constants.DoPrintInfoType.WAYBILL_JSON.getValue());
        if ((doPrintInfo == null && doPrintInfo1== null) || StringUtil.isEmpty(doHeader.getTrackingNo())) {
            throw new DeliveryException(DeliveryException.WAYBILL_IMAGE_NOT_EXIST);
        }

        String cartonNo = doHeader.getTrackingNo();
        String split = Config.get(Keys.Delivery.carton_split_value, Config.ConfigLevel.WAREHOUSE);
        split = StringUtils.isBlank(split) ? "" : split;
        cartonHeader.setCartonNo(cartonNo + split);
        cartonHeader.setWayBill(cartonNo);
    }
}
