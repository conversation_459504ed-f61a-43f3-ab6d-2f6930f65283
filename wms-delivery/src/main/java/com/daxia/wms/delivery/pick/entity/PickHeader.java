package com.daxia.wms.delivery.pick.entity;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.framework.system.entity.UserAccount;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.master.entity.Partition;
import com.daxia.wms.master.entity.Region;

/**
 * 拣货单头信息
 */

@Entity
@Table(name = "doc_pkt_header")
@Where(clause = "IS_DELETED = 0 ")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@SQLDelete(sql = "update doc_pkt_header set is_deleted = 1 where id = ? and version = ?")
@BatchSize(size = 30)
@lombok.extern.slf4j.Slf4j
public class PickHeader extends WhBaseEntity {

    private static final long serialVersionUID = 8790775216775892023L;

    public static final Integer PKT_TYPE_PCS = 1; //B
    public static final Integer PKT_TYPE_UNIT = 2; //C

    /**
     * 主键
     */
    private Long id;

    /**
     * 状态（00：初始化；40：已发布；99：完成；90：取消）
     */
    private String status;

    /**
     * 拣货单号
     */
    private String pktNo;
    /**
     * 区域ID
     */
    private Long regionId;
    /**
     * 库区ID
     */
    private Long partitionId;
    /**
     * 波次Id
     */
    private Long waveHeadId;

    /**
     * 拣货人
     */
    private String pickBy;
    /**
     * 劳动力(指派人)
     */
    private Long operUserId;
    
    private Integer mergeStatus;
    /**
     * 拣货开始时间
     */
    private Date pickTimeFrom;
    /**
     * 拣货结束时间
     */
    private Date pickTimeTo;
    /**
     * 是否打印（1：是，0：否）
     */
    private Integer pickPrintFlag;
    

    /**
     * 拣货单类型
     */
    private Integer pktType;

    private Long cartonId;
    private WaveHeader waveHeader;

    private Partition partition;
    
    private Region region;
    
    private List<PickTask> pickTasks;

    private UserAccount operUser;
    
    /**
     * 是否可索取（默认可以索取）
     */
    private Boolean isAvailable;
    
    /**
     * Getter method for property <tt>id</tt>.
     * 
     * @return property value of id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)  
    public Long getId() {
        return id;
    }

    /**
     * Setter method for property <tt>id</tt>.
     * 
     * @param id
     *            value to be assigned to property id
     */
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "STATUS")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "carton_id")
    public Long getCartonId() {
        return cartonId;
    }

    public void setCartonId(Long cartonId) {
        this.cartonId = cartonId;
    }

    /**
     * Getter method for property <tt>partitionId</tt>.
     * 
     * @return property value of partitionId
     */
    @Column(name = "PARTITION_ID")
    public Long getPartitionId() {
        return partitionId;
    }

    /**
     * Setter method for property <tt>partitionId</tt>.
     * 
     * @param partitionId
     *            value to be assigned to property partitionId
     */
    public void setPartitionId(Long partitionId) {
        this.partitionId = partitionId;
    }

    @Column(name = "pkt_type")
    public Integer getPktType() {
        return pktType;
    }

    public void setPktType(Integer pktType) {
        this.pktType = pktType;
    }

    /**
     * Getter method for property <tt>pktNo</tt>.
     * 
     * @return property value of pktNo
     */
    @Column(name = "PKT_NO")
    public String getPktNo() {
        return pktNo;
    }

    /**
     * Setter method for property <tt>pktNo</tt>.
     * 
     * @param pktNo
     *            value to be assigned to property pktNo
     */
    public void setPktNo(String pktNo) {
        this.pktNo = pktNo;
    }

    @Column(name = "REGION_ID")
    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    @Column(name = "WAVE_HEADER_ID")
    public Long getWaveHeadId() {
        return waveHeadId;
    }

    public void setWaveHeadId(Long waveHeadId) {
        this.waveHeadId = waveHeadId;
    }

    public void setWaveHeader(WaveHeader waveHeader) {
        this.waveHeader = waveHeader;
    }

    @ManyToOne(targetEntity = WaveHeader.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "WAVE_HEADER_ID", insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
    public WaveHeader getWaveHeader() {
        return waveHeader;
    }

    public void setPartition(Partition partition) {
        this.partition = partition;
    }

    @ManyToOne(targetEntity = Partition.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "PARTITION_ID", insertable = false, updatable = false)
    public Partition getPartition() {
        return partition;
    }

    public void setRegion(Region region) {
        this.region = region;
    }

    @ManyToOne(targetEntity = Region.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "REGION_ID", insertable = false, updatable = false)
    public Region getRegion() {
        return region;
    }

    @Column(name = "PICK_BY")
	public String getPickBy() {
		return pickBy;
	}

	public void setPickBy(String pickBy) {
		this.pickBy = pickBy;
	}

	@Column(name = "PK_FROM_TIME")
	public Date getPickTimeFrom() {
		return pickTimeFrom;
	}

	public void setPickTimeFrom(Date pickTimeFrom) {
		this.pickTimeFrom = pickTimeFrom;
	}

	@Column(name = "PK_END_TIME")
	public Date getPickTimeTo() {
		return pickTimeTo;
	}

	public void setPickTimeTo(Date pickTimeTo) {
		this.pickTimeTo = pickTimeTo;
	}

	@Column(name = "MERGE_STATUS")
	public Integer getMergeStatus() {
		return mergeStatus;
	}

	public void setMergeStatus(Integer mergeStatus) {
		this.mergeStatus = mergeStatus;
	}

    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "PKT_H_ID")
    @Where(clause = " IS_DELETED = 0 ")
    public List<PickTask> getPickTasks() {
        return pickTasks;
    }

    public void setPickTasks(List<PickTask> pickTasks) {
        this.pickTasks = pickTasks;
    }

    @Column(name = "PICK_PRINT_FLAG")
    public Integer getPickPrintFlag() {
        return pickPrintFlag;
    }
    
    public void setPickPrintFlag(Integer pickPrintFlag) {
        this.pickPrintFlag = pickPrintFlag;
    }
    
    @Column(name = "OPER_USER_ID")
	public Long getOperUserId() {
		return operUserId;
	}

	public void setOperUserId(Long operUserId) {
		this.operUserId = operUserId;
	}

    @ManyToOne(targetEntity = UserAccount.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "OPER_USER_ID", insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
	public UserAccount getOperUser() {
		return operUser;
	}

	public void setOperUser(UserAccount operUser) {
		this.operUser = operUser;
	}

	@Column(name = "IS_AVAILABLE")
	public Boolean getIsAvailable() {
		return isAvailable;
	}

	public void setIsAvailable(Boolean isAvailable) {
		this.isAvailable = isAvailable;
	}
}