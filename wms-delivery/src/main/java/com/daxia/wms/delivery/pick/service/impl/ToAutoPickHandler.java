package com.daxia.wms.delivery.pick.service.impl;

import org.jboss.seam.annotations.In;

import com.daxia.wms.delivery.deliveryorder.service.ToAutoHandler;
import com.daxia.wms.delivery.pick.service.PickService;

@lombok.extern.slf4j.Slf4j
public class ToAutoPickHandler extends ToAutoHandler {
	@In
    private PickService pickService;
	
	@Override
	protected void handle(Long doId) {
		pickService.doHandlePick(doId);
	}

	@Override
	protected void setName() {
		this.name = "toAutoPickHandler";
	}
}
