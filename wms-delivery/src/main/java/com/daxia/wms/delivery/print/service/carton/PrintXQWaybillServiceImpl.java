package com.daxia.wms.delivery.print.service.carton;

import com.daxia.wms.Constants.WaybillType;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.print.dto.BaseCartonPrintDTO;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.helper.PrintCartonHelper;
import com.daxia.wms.delivery.print.service.PrintWaybillService;
import com.daxia.wms.master.entity.BusinessCustomer;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.print.dto.PrintCfg;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.print.utils.PrintHelper;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.List;

@Name("printXQWaybillService")
@lombok.extern.slf4j.Slf4j
public class PrintXQWaybillServiceImpl extends AbstractPrintWaybillService implements PrintWaybillService {
	@In
	WarehouseCarrierService warehouseCarrierService;

	@Create
	public void init () {
		this.setWaybillType(WaybillType.XQ);
	}

	@Override
    protected void buildDTO(List<PrintReportDto> printReportDtos, DeliveryOrderHeader doHeader, BaseCartonPrintDTO carton,
                            int index, int count) {
		CartonPrintDTO cartonPrintDTO = new CartonPrintDTO();
		cartonPrintDTO.setIsPrinted(carton.getIsPrinted());
		cartonPrintDTO.setCartonId(carton.getId());
		cartonPrintDTO.setCartonNo(carton.getCartonNo());
		cartonPrintDTO.setDoNo(doHeader.getDoNo());
		cartonPrintDTO.setOutRefNo(doHeader.getRefNo1());
		cartonPrintDTO.setIsCOD(PrintCartonHelper.isCOD(doHeader));
		cartonPrintDTO.setSortGridNo(doHeader.getSortGridNo());
		cartonPrintDTO.setWaveNo(doHeader.getWaveHeader().getWaveNo());
		cartonPrintDTO.setOriginalSoCode(doHeader.getOriginalSoCode());
		// 设置送货单号
		cartonPrintDTO.setDoNo(doHeader.getDoNo());
		// 设置商户信息
		BusinessCustomer customer = doHeader.getBusinessCustomer();
		if (customer != null) {
			cartonPrintDTO.setCustomerName(customer.getCustomerName());
		}
		// 设置重量,体积,件数
		if (doHeader.getGrossWt() != null) {
			cartonPrintDTO.setWeight(doHeader.getGrossWt().toString());
		}
		if (doHeader.getVolume() != null) {
			cartonPrintDTO.setVolume(doHeader.getVolume().toString());
		}
		if (doHeader.getExpectedQty() != null) {
			cartonPrintDTO.setNumber(doHeader.getExpectedQty().toString());
		}
		// 是否需要待收货款
		boolean receivable = doHeader.getReceivable() != null
				&& doHeader.getReceivable().compareTo(BigDecimal.ZERO) > 0;
		cartonPrintDTO.setNeedReceivable(receivable);
		if (receivable) {
			// 设置代收金额
			cartonPrintDTO.setServiceCodAmount(doHeader.getReceivable().toString());
		}
		// 设置图片路径
		cartonPrintDTO.setBasePrintImgPath(PrintHelper.getBasePrintImgPath());

		printReportDtos.add(cartonPrintDTO);
	}

	@Override
    protected PrintData genPrintData(List<PrintReportDto> dtoList, DeliveryOrderHeader doHeader) {
		PrintData printData = new PrintData();
		printData.setDtoList(dtoList);
		printData.setPrintCfg(generateSFPrintCfg());
		return printData;
	}

	private PrintCfg generateSFPrintCfg() {
		PrintCfg config = new PrintCfg("wayBillXQ", "80", "80");
		this.setPrintCfg(config);
		return config;
	}
}
