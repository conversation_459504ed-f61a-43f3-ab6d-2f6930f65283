package com.daxia.wms.delivery.constant;

import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.deliveryorder.service.IDoAllocate;

import java.util.HashMap;
import java.util.Map;

/**
 * 出库管理-常量类
 */
@lombok.extern.slf4j.Slf4j
public class DeliveryConstant {
	
	/**
	 * 定义自动分配返回值与提示消息键值对
	 */
	private static final Map<String, String> ALLOCATE_MESSAGE_MAP = new HashMap<String, String>();
	
	static {
		ALLOCATE_MESSAGE_MAP.put(IDoAllocate.ALC_RESULT_NODO, "allocate.failed.doDetailNotExsit");
		ALLOCATE_MESSAGE_MAP.put(IDoAllocate.ALC_RESULT_NOSTOCK, "allocate.failed.noStock");
		ALLOCATE_MESSAGE_MAP.put(IDoAllocate.ALC_RESULT_SUCCESS, "allocate.success");
		ALLOCATE_MESSAGE_MAP.put(IDoAllocate.ALC_RESULT_NORULE, "allocate.failed.noRule");
		ALLOCATE_MESSAGE_MAP.put(IDoAllocate.SKU_NO_DEFAULT_LOC, "allocate.failed.skuNoDefaultPickLoc");
		ALLOCATE_MESSAGE_MAP.put(IDoAllocate.STOCK_REPL_QTY_ENOUGH, "allocate.failed.qtyNotEnough");
		ALLOCATE_MESSAGE_MAP.put(IDoAllocate.NO_ENOUGH_STOCK_QTY, "allocate.failed.stockloc.noStock");
		ALLOCATE_MESSAGE_MAP.put(IDoAllocate.REP_SUCCESS, "allocate.replenish.success");
		ALLOCATE_MESSAGE_MAP.put(IDoAllocate.NEED_REPL, "allocate.replenish.pending");
		ALLOCATE_MESSAGE_MAP.put(IDoAllocate.NO_REPL_RULE, "allocate.replenish.failed.noReplRule");
		ALLOCATE_MESSAGE_MAP.put(IDoAllocate.REPL_TASK_IS_EXIST, "allocate.replenish.failed.taskAllreadyIsExist");
		ALLOCATE_MESSAGE_MAP.put(IDoAllocate.DO_CANCEL, "allocate.failed.doCancel");
	}
	
	/**
	 * 根据自动分配返回值找出提示消息值
	 * @param returnValue  自动分配返回值
	 * @return 提示消息值
	 */
	public static String getAllocatMessage(String returnValue){
		if(StringUtil.isEmpty(returnValue)){
			return returnValue;
		}
		return ALLOCATE_MESSAGE_MAP.get(returnValue);
	}
	
	public static final String STOCK_NOT_ENOUGH_FOR_AALOT ="allocate.failed.stock.noStock";
	public static final String STOCK_NOT_ENOUGH_FOR_AALOT_TO_CS ="allocate.failed.stockloc.noStock"; //给通知客服用的常量
	
	/**
	 * 退货区(实际是一个库位)默认的库位编码 
	 */
	public static final String RETRUNLOCATION_DEFAULT_CODE ="RETURN"; 
	/**
	 * LPN固定编号
	 */
	public static final String LPN_DEFAULT_NO = "*";

    /**
     * 一个波次中所包含的最大发货单数量
     */
	public static final Long DELIVERY_DO_MAXCOUNTS = Long.valueOf(15);
	/**
	 * 大波次默认pageSize
	 */
	public static final int DEFAULT_PAGESIZE_FOR_BIGWAVE = 50;
	
	/**
	 * 波次生成成功信息提示代码
	 */
	public static final String GENERATE_SUCCESS = "generate.wave.success";
	
	public static final String GENERATE_SUCCESS_NO_NUM = "generate.wave.success.no.num";

    public static final String GENERATE_SUCCESS_ONE = "generate.wave.success.one";

	public static final String GENERATE_FAILED = "generate.wave.failed";
	
	public static final String GENERATE_NO_WAVE = "error.delivery.no.wave";
    
	public static final String GROUP_BUY_WAVE = "group_buy_wave";
	
	//生成完毕,团购成功:{0},失败:{1};普通成功:{2},失败:{3},订单池数量:{4}
    public static final String SEMI_AUTO_WAVE_OVER = "generate.semiAutoWave.over";
	
	public static final String A_CLASS_WAVE = "a_class_wave";

	public static final String NORMAL_WAVE = "normal_wave";
	
	//生成促销单品波次时, Sku数量不能大于1！
	public static final String GENERATE_SKU_NUMBER_OVER = "generate.singlePromoteWave.skuNumOver";

	public static final String JD_CARTON_SPLIT = "-";
}
