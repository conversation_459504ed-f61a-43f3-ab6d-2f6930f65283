package com.daxia.wms.delivery.util;

import com.daxia.framework.common.cfg.CainiaoWaybillCfg;
import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.JsonUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Keys;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.master.service.impl.WarehouseCarrierServiceImpl;
import com.daxia.wms.waybill.cainiao.CainiaoConfig;
import com.daxia.wms.waybill.cainiao.CainiaoConstants;
import com.taobao.api.internal.util.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.jboss.seam.Component;
import org.jboss.seam.contexts.Lifecycle;
import org.jboss.seam.servlet.ContextualHttpServletRequest;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
@lombok.extern.slf4j.Slf4j
public class TaobaoOauthServlet extends HttpServlet {
    public static final String P_CODE = "code";
    public static final String P_STATE = "state";
    public static final String P_ERROR = "error";
    public static final String P_ERROR_DESCRIPTION = "error_description";


    @Override
    protected void service(final HttpServletRequest request, final HttpServletResponse response)
            throws ServletException, IOException {
        new ContextualHttpServletRequest(request) {
            @Override
            public void process() throws Exception {
                doWork(request, response);
            }
        }.run();
    }

    protected void doWork(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        String code = request.getParameter(P_CODE);
        String state = request.getParameter(P_STATE);

        if (StringUtil.isEmpty(code)) {
            String error = request.getParameter(P_ERROR);
            String errorDescription = request.getParameter(P_ERROR_DESCRIPTION);
            if (StringUtil.isNotEmpty(error)) {
                log.error("Taobao oauth error: " + error + ", description: " + errorDescription);
            }
        } else {
            log.error("Taobao oauth code: " + code);

            if (StringUtil.isNotEmpty(state)) {
                String[] codeStr = state.split("_");
                Long warehouseId = Long.valueOf(codeStr[0]);
                Long carrierId = Long.valueOf(codeStr[1]);
                String cpCode = codeStr[2];
                ParamUtil.setCurrentWarehouseId(warehouseId);
                CainiaoConfig cainiaoConfig = CainiaoConstants.getCainiaoConfig();
                String appKey = Config.getFmJson(Keys.Master.waybill_cainiao_cfg, Config.ConfigLevel.WAREHOUSE, CainiaoWaybillCfg.clientId);
                String appSecret = Config.getFmJson(Keys.Master.waybill_cainiao_cfg, Config.ConfigLevel.WAREHOUSE, CainiaoWaybillCfg.clientSecret);
                String redirectUri = Config.getFmJson(Keys.Master.waybill_cainiao_cfg, Config.ConfigLevel.WAREHOUSE, CainiaoWaybillCfg.redirectUrl);

                String url = cainiaoConfig.getTokenUrl();
                Map<String, String> props = new HashMap<String, String>();
                props.put("grant_type", "authorization_code");
                props.put("code", code);
                props.put("client_id", appKey);
                props.put("client_secret", appSecret);
                props.put("redirect_uri", redirectUri);
                props.put("view", "web");

                try {
                    String s = WebUtils.doPost(url, props, 30000, 30000);

                    response.setContentType("text/html");
                    response.setCharacterEncoding("UTF-8");
                    Lifecycle.beginCall();
                    WarehouseCarrierService warehouseCarrierService = (WarehouseCarrierService) Component.getInstance(WarehouseCarrierServiceImpl.class);
                    Lifecycle.endCall();
                    if (StringUtil.isNotBlank(JsonUtil.getField(s, "access_token"))
                            && StringUtil.isNotBlank(JsonUtil.getField(s, "taobao_user_id"))) {
                        String accessToken = JsonUtil.getField(s, "access_token");
                        String sellId = JsonUtil.getField(s, "taobao_user_id");
                        warehouseCarrierService.cainiaoAuth(accessToken, carrierId, sellId, cpCode);
                    }
                } catch (IOException e) {
                    response.getWriter().write(e.getMessage());

                    log.error("",e);
                }
            }
        }
    }
}
