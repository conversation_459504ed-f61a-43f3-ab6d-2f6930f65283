package com.daxia.wms.delivery.util;

import java.util.ArrayList;
import java.util.List;

import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.system.constants.Constants.YesNo;
import com.daxia.wms.master.entity.LaborHumanRegion;

@lombok.extern.slf4j.Slf4j
public class LaborForceHelper {

	public static List<Long> getIdOfRegionList(List<LaborHumanRegion> regionList) {
		List<Long> regionIdList = new ArrayList<Long>();
		if (ListUtil.isNotEmpty(regionList)) {
			for (LaborHumanRegion laborHumanRegion : regionList) {
				if (null != laborHumanRegion.getRegionId()) {
					regionIdList.add(laborHumanRegion.getRegionId());
				}
			}
		}
		return regionIdList;
	}
	
	
	public static List<Long> getIdOfPartitionList(List<LaborHumanRegion> regionList) {
		List<Long> partitionIdList = new ArrayList<Long>();
		if (ListUtil.isNotEmpty(regionList)) {
			for (LaborHumanRegion laborHumanRegion : regionList) {
				if ( null != laborHumanRegion.getPartitionId()) {
					partitionIdList.add(laborHumanRegion.getPartitionId());
				}
			}
		}
		return partitionIdList;
	}

	public static boolean enableCross(List<LaborHumanRegion> regionList) {
		boolean canCross = false;
		if (ListUtil.isNotEmpty(regionList)) {
			canCross = YesNo.YES.getValue().equals(regionList.get(0).getEnableAcross());
		}
		return canCross;
	}

}
