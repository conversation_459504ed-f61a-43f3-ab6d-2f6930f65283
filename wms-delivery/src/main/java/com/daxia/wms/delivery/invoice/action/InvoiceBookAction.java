package com.daxia.wms.delivery.invoice.action;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.invoice.entity.InvoiceBook;
import com.daxia.wms.delivery.invoice.filter.InvoiceBookFilter;
import com.daxia.wms.delivery.invoice.service.InvoiceBookService;

/**
 * 发票薄Action
 */
@Name("com.daxia.wms.delivery.invoiceBookAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class InvoiceBookAction extends PagedListBean<InvoiceBook> {

	private static final long serialVersionUID = 2540569869731754100L;
	private InvoiceBookFilter invoiceBookFilter;
	
	@In
	private InvoiceBookService invoiceBookService;

	public InvoiceBookAction() {
		invoiceBookFilter = new InvoiceBookFilter();
	}
	
    /**
     * 查询发票薄信息
     */	
	@Override
	public void query() {
		DataPage<InvoiceBook> dataPage = invoiceBookService.findInvoiceBookByFilter(invoiceBookFilter, getStartIndex(), getPageSize());
		this.populateValues(dataPage);
	}
	
	public void initPage() {
		
	}

	public InvoiceBookFilter getInvoiceBookFilter() {
		return invoiceBookFilter;
	}

	public void setInvoiceBookFilter(InvoiceBookFilter invoiceBookFilter) {
		this.invoiceBookFilter = invoiceBookFilter;
	}
}
