package com.daxia.wms.delivery.task.replenish.dto;

import java.io.Serializable;
import java.math.BigDecimal;


@lombok.extern.slf4j.Slf4j
public class LocationReplInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long locId;
    private Long skuId;
    private BigDecimal uplimit;
    private BigDecimal lowerlimit; 
    private BigDecimal actQty;
	private String packageType;
	private BigDecimal packageQty;
	private Long partitionId;
    
    
	public Long getLocId() {
		return locId;
	}
	public void setLocId(Long locId) {
		this.locId = locId;
	}
	public Long getSkuId() {
		return skuId;
	}
	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}
	public BigDecimal getUplimit() {
		return uplimit;
	}
	public void setUplimit(BigDecimal uplimit) {
		this.uplimit = uplimit;
	}
	public BigDecimal getLowerlimit() {
		return lowerlimit;
	}
	public void setLowerlimit(BigDecimal lowerlimit) {
		this.lowerlimit = lowerlimit;
	}
	public BigDecimal getActQty() {
		return actQty;
	}
	public void setActQty(BigDecimal actQty) {
		this.actQty = actQty;
	}

	public String getPackageType() {
		return packageType;
	}

	public void setPackageType(String packageType) {
		this.packageType = packageType;
	}

	public BigDecimal getPackageQty() {
		return packageQty;
	}

	public void setPackageQty(BigDecimal packageQty) {
		this.packageQty = packageQty;
	}

	public Long getPartitionId() {
		return partitionId;
	}

	public void setPartitionId(Long partitionId) {
		this.partitionId = partitionId;
	}
}