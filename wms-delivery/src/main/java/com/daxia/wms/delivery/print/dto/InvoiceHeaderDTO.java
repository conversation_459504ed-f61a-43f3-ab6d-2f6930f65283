/**
 * 
 */
package com.daxia.wms.delivery.print.dto;

import java.util.List;

import com.daxia.wms.print.dto.PrintReportDto;

@lombok.extern.slf4j.Slf4j
public class InvoiceHeaderDTO extends PrintReportDto {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -1084458440595662123L;

	private String invoiceNo;
	
	private String soCode;
	
	private String doNo;
	
	private String total;
	
	private String totalRmb;
	
	private String receiver;
	
	private String machineNo;
	
	private String receiveCom;
	
	private String registerNum;
	
	private String date;
	
	private String payer;
	
	private String sortGridNo;
	
	private String waveNo;
	
	private List<RowDTO> rowDtoList;

	public String getInvoiceNo() {
		return invoiceNo;
	}

	public void setInvoiceNo(String invoiceNo) {
		this.invoiceNo = invoiceNo;
	}

	public String getSoCode() {
		return soCode;
	}

	public void setSoCode(String soCode) {
		this.soCode = soCode;
	}

	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}

	public String getTotal() {
		return total;
	}

	public void setTotal(String total) {
		this.total = total;
	}

	public String getTotalRmb() {
		return totalRmb;
	}

	public void setTotalRmb(String totalRmb) {
		this.totalRmb = totalRmb;
	}

	public String getReceiver() {
		return receiver;
	}

	public void setReceiver(String receiver) {
		this.receiver = receiver;
	}

	public String getMachineNo() {
		return machineNo;
	}

	public void setMachineNo(String machineNo) {
		this.machineNo = machineNo;
	}

	public String getReceiveCom() {
		return receiveCom;
	}

	public void setReceiveCom(String receiveCom) {
		this.receiveCom = receiveCom;
	}

	public String getRegisterNum() {
		return registerNum;
	}

	public void setRegisterNum(String registerNum) {
		this.registerNum = registerNum;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public String getPayer() {
		return payer;
	}

	public void setPayer(String payer) {
		this.payer = payer;
	}

	public String getSortGridNo() {
		return sortGridNo;
	}

	public void setSortGridNo(String sortGridNo) {
		this.sortGridNo = sortGridNo;
	}

	public String getWaveNo() {
		return waveNo;
	}

	public void setWaveNo(String waveNo) {
		this.waveNo = waveNo;
	}

	public List<RowDTO> getRowDtoList() {
		return rowDtoList;
	}

	public void setRowDtoList(List<RowDTO> rowDtoList) {
		this.rowDtoList = rowDtoList;
	}
}
