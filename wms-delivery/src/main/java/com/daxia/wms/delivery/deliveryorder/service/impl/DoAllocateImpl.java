package com.daxia.wms.delivery.deliveryorder.service.impl;


import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.log.LogUtil;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.delivery.AllocateException;
import com.daxia.wms.delivery.deliveryorder.dao.DoDetailDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateHeader;
import com.daxia.wms.delivery.deliveryorder.service.IDoAllocate;
import com.daxia.wms.delivery.pick.service.PickTaskService;
import com.daxia.wms.delivery.util.AllocateHelper;
import com.daxia.wms.master.dto.SkuDTO;
import com.daxia.wms.master.entity.AllocationRuleHeader;
import com.daxia.wms.master.entity.SkuCombiDetail;
import com.daxia.wms.master.entity.SkuCombiHeader;
import com.daxia.wms.master.service.AllocationRuleHeaderService;
import com.daxia.wms.master.service.PackageInfoDetailService;
import com.daxia.wms.master.service.SkuCache;
import com.daxia.wms.master.service.SkuCombiService;
import com.daxia.wms.stock.stock.dto.OrderStockDTO;
import com.daxia.wms.stock.stock.dto.Stock2AllocateDTO;
import com.daxia.wms.stock.stock.dto.Stock4CombiDoAllocateDTO;
import com.daxia.wms.stock.stock.dto.StockBatchDTO;
import com.daxia.wms.stock.stock.service.IOperator;
import com.daxia.wms.stock.stock.service.StockService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.daxia.wms.master.action.SkuCombiAddAction.getMD5Hash;
import static com.idanchuang.ims.common.enums.InventoryBatchGoodsGradeEnum.*;
import static com.idanchuang.ims.common.enums.InventoryBatchGoodsGradeEnum.GOODS_GRADE_DAMAGE;

/**
 * 实现对DoDetail的库存分配功能
 */
@Name("com.daxia.wms.delivery.doAllocate")
@lombok.extern.slf4j.Slf4j
public class DoAllocateImpl implements IDoAllocate{
     
    @In
    protected DoDetailDAO doDetailDAO;
    
    @In
    private AllocationRuleHeaderService allocationRuleHeaderService;

    @In
    private StockService stockService;
    
    @In
    private IOperator allocateOperator;
    
    @In
    private SkuCache skuCache;
    
    @In
    private PickTaskService pickTaskService;

    @In
    private PackageInfoDetailService packageInfoDetailService;
    final String A1 = GOODS_GRADE_ZP_BEST.getBatchGoodsGradeType().toString();
    final String A2 = GOODS_GRADE_ZP_NOTIFICATION.getBatchGoodsGradeType().toString();
    final Set<String> BEST_GOODS_GRADE=new HashSet<>();
    {
        BEST_GOODS_GRADE.add(A1);
        BEST_GOODS_GRADE.add(A2);
    }
    /**
     * 执行明细分配
     */
    @Override
    @Transactional
    @Loggable
    public String executeAllocate(DoAllocateDetail alcDetail, String regionId) throws AllocateException, Exception {
        log.debug(
            "Allocate do detail:[id: {}, lineStatus: {}, allocatedQty: {}, needReplQty: {}]",
                alcDetail.getId(), alcDetail.getLineStatus(), alcDetail.getAllocatedQty(), alcDetail.getNeedReplQty());

        if(DoStatus.ALLALLOCATED.getValue().equals(alcDetail.getLineStatus())){
        	return ALC_RESULT_SUCCESS;
        }
        SkuDTO sku = skuCache.getSku(alcDetail.getSkuId());
        // 该明细还剩余未分配的qty
        BigDecimal notAllocQty = alcDetail.getExpectedQty().subtract(alcDetail.getAllocatedQty());
        
        if (BigDecimal.ZERO.compareTo(notAllocQty) == 0) {//无数量需要分配 
            alcDetail.setLineStatus(DoStatus.ALLALLOCATED.getValue());
        	return ALC_RESULT_SUCCESS;
        }
        if (!Integer.valueOf(1).equals(alcDetail.getIsDoLeaf())) {//只有isDoLeaf需要分配
            return ALC_RESULT_SUCCESS;
        }
        DoAllocateHeader doAllocateHeader = alcDetail.getDoAllocateHeader();
        String doType = doAllocateHeader.getDoType();
        Integer allotType = doAllocateHeader.getTranType();
        if(!allocationRuleHeaderService.isRuleMatch(alcDetail.getAllocationRule(), doType, allotType)){
        	return ALC_RESULT_NORULE;
        }
        AllocationRuleHeader allocationRuleHeader = allocationRuleHeaderService.get(alcDetail.getAllocationRule());
        allocationRuleHeader.setDoType(doType);
        //控制如果是调拨的坏品, 则只分配坏品位上的sku

        Integer isDamaged = alcDetail.getIsDamaged();
        List<Stock2AllocateDTO> stockAllocateResults = null;

        // 获取可分配库存类型
        List<String> locTypes = AllocateHelper.buildLocTypes4Alloc(alcDetail, allocationRuleHeader);

        // 获取可分配库存的批次属性
        StockBatchDTO stockBatchDTO = AllocateHelper.buildBatchDTOForAllocate(alcDetail,alcDetail.getPackageInfoDetail());
        List<String> packTypes = AllocateHelper.getPackTypes(allocationRuleHeader, true, false);
        List<String> locCodes = AllocateHelper.getLocCodes(doAllocateHeader);
        // 获取可分配库存
        stockAllocateResults = stockService.queryStockForAllocate(allocationRuleHeader, regionId, locTypes, packTypes, stockBatchDTO, doAllocateHeader.getShopId(), true, locCodes);

        AllocateHelper.sortStock(alcDetail.getDoAllocateHeader(), allocationRuleHeader, stockAllocateResults);

        // 如果没有库存，则根据存储区库存设置doDetail的补货数量
        if (stockAllocateResults == null || stockAllocateResults.isEmpty()) {
        	//如果是坏品，直接返回，不用补货
        	if(YesNo.YES.getValue().equals(isDamaged)){
        		return null;
        	}
    
            return this.setReplenishStatus(alcDetail, notAllocQty, regionId);
        }
        
        //拣货位可用于分配的库存总数
        BigDecimal actQtySum = BigDecimal.ZERO;
        List<Stock2AllocateDTO> noZeroResult = new ArrayList<Stock2AllocateDTO>();
        for (Stock2AllocateDTO doDetailAllocateResult : stockAllocateResults) {
            actQtySum = actQtySum.add(doDetailAllocateResult.getActQty()).add(doDetailAllocateResult.getPendingQty());
            if (doDetailAllocateResult.getActQty().compareTo(BigDecimal.ZERO) > 0) {
            	noZeroResult.add(doDetailAllocateResult);
			}
        }
        
        //拣货位可用库存不足
        if(actQtySum.compareTo(notAllocQty) < 0) {
        	if(YesNo.YES.getValue().equals(isDamaged)){
        	    //如果是坏品，坏品库存为0，直接返回，不用补货;不为0需要分配
        		if(actQtySum.compareTo(BigDecimal.ZERO)==0){
        			return null;
        		}
        	} else {
        	    //不是坏品，需要查看存储区库存
            	String status = setReplenishStatus(alcDetail, notAllocQty.subtract(actQtySum), regionId);
            	if(status != null && IDoAllocate.NEED_REPL != status) { //先部分分配了，再生成补货标识
            		return status;
            	}
        	}
        }
        notAllocQty = doAllocate(alcDetail, noZeroResult, notAllocQty, sku);
        // 更新订单和订单头---------------------------------------------------
        BigDecimal allocatedQty = alcDetail.getExpectedQty().subtract(notAllocQty);
        alcDetail.setAllocatedQty(allocatedQty);
        alcDetail.setAllocatedQtyPcs(allocatedQty);

        //完成分配
        if (notAllocQty.compareTo(BigDecimal.ZERO) == 0) {
            alcDetail.setLineStatus(DoStatus.ALLALLOCATED.getValue());       
            return ALC_RESULT_SUCCESS;
        } else if(allocatedQty.compareTo(BigDecimal.ZERO) == 0){
            alcDetail.setLineStatus(DoStatus.INITIAL.getValue());
        } else {
            alcDetail.setLineStatus(DoStatus.PARTALLOCATED.getValue());
        }
        //没有分配完成的情况下，如果是坏品，返回null，不提示补货
        if(YesNo.YES.getValue().equals(isDamaged)){
        	return null;
        } else {
            alcDetail.setNeedReplQty(notAllocQty);
            alcDetail.setNeedReplQtyPcs(notAllocQty);
            return IDoAllocate.NEED_REPL;
        }
    }

    /**
     * 组合品订单分配库存
     *
     * @param alcHeader
     * @param allocateResult
     * @return
     */
    @In
    private SkuCombiService skuCombiService;

    /**
     * 组合品库存分配
     * @param alcHeader
     * @param allocateResult
     * @return
     */
    @Override
    @Transactional
    public String skuCombiStockAllocate(DoAllocateHeader alcHeader, Map<String, List<String>> allocateResult) {
        String error = "not.sku.combi.stock";
        List existsList=new ArrayList<>();
        List<DoAllocateDetail> details = alcHeader.getDoAllocateDetails();
        for (DoAllocateDetail detail : details) {
            // 分配完成直接返回
            if (Objects.equals(DoStatus.ALLALLOCATED.getValue(),detail.getLineStatus())) {
                return error;
            }
            // 规则存的全是A2  如果订单明细是A1要转成A2查询
            String goodsGrade =String.valueOf( detail.getGoodsGrade());
            if(Objects.equals(goodsGrade,A1)){
                goodsGrade=A2;
            }
            String key=detail.getSku().getEan13()+"*"+ goodsGrade +"*"+detail.getExpectedQty();
            if(existsList.contains(key)){
                return error;
            }else {
                existsList.add(key);
            }
        }
        String str = (String) existsList.stream().sorted().collect(Collectors.joining(", ", "[", "]"));
        String md5 = getMD5Hash(str);
        // 根据签名查找组合
        SkuCombiHeader skuCombiHeader = skuCombiService.queryBySign(md5);
        if(Objects.isNull(skuCombiHeader)) {
            return error;
        }
        // 找库存项
        List<Long> skuIds = skuCombiHeader.getDetails().stream()
                .map(SkuCombiDetail::getSkuId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(skuIds)) {
            return error;
        }
        List<Stock4CombiDoAllocateDTO> allocateDTOS =
                stockService.queryStockForAllocateCombiSkuOrder(skuIds, skuCombiHeader.getLocCode());
        // 准备库存操作数据
        Map<DoAllocateDetail,List<Stock4CombiDoAllocateDTO>> detailStocksMap = new HashMap<>();
        for (DoAllocateDetail detail : details) {
            Iterator<Stock4CombiDoAllocateDTO> iterator = allocateDTOS.iterator();
            while (iterator.hasNext()) {
                Stock4CombiDoAllocateDTO dto = iterator.next();
                if(StringUtils.isBlank(dto.getLotAtt14() )){
                    dto.setLotAtt14(A1);
                }
                if(Objects.equals(dto.getSkuId(),detail.getSkuId())) {
                    // A1 A2规则等级不必完全相等，只要满足订单明细等级和规则等级同时为A1或A2 其他必须相等
                    if(BEST_GOODS_GRADE.contains(dto.getLotAtt14())&& BEST_GOODS_GRADE.contains(String.valueOf(detail.getGoodsGrade()))
                            ||Objects.equals(dto.getLotAtt14(),String.valueOf(detail.getGoodsGrade()))){
                        BigDecimal needQty = detail.getExpectedQty().subtract(detail.getAllocatedQty());
                        if(Objects.equals(needQty,BigDecimal.ZERO)){
                            break;
                        }
                        // 可用库存量大于订单所需分配量 已用量改为【订单量-已分配量】 订单分配量设为【期望量】
                        BigDecimal actQty = dto.getActQty();
                        if(actQty.compareTo(needQty)>0){
                            dto.setUsedQty(needQty);
                            detail.setAllocatedQty(detail.getExpectedQty());
                            detail.setAllocatedQtyUnit(detail.getExpectedQtyUnit());
                        }
                        // 可用库存量小于等于订单所需分配量 已用量改为【可用库存量】订单分配量设为【已分配量+可用库存量】
                        else {
                            dto.setUsedQty(actQty);
                            BigDecimal allocatedQty = detail.getAllocatedQty().add(actQty);
                            detail.setAllocatedQty(allocatedQty);
                            detail.setAllocatedQtyUnit(allocatedQty);
                        }
                        dto.setPackageId(detail.getPackageId());
                        dto.setAlcDetailId(detail.getId());
                        dto.setDocId(detail.getDoHeaderId());
                        List<Stock4CombiDoAllocateDTO> stock4CombiDoAllocateDTOS = detailStocksMap.get(detail);
                        if(Objects.isNull(stock4CombiDoAllocateDTOS)) {
                            stock4CombiDoAllocateDTOS=new ArrayList<>();
                            detailStocksMap.put(detail,stock4CombiDoAllocateDTOS);
                        }
                        stock4CombiDoAllocateDTOS.add(dto);
                    }
                    // 同商品 不同等级剔除
                    else{
                        iterator.remove();
                    }
                }
            }
        }
        boolean allAllocated = true;
        // 没有一次性分配完成 都不能算组合单
        for (DoAllocateDetail detail : details) {
            if(detail.getAllocatedQty().compareTo(detail.getExpectedQty())<0) {
                allAllocated =false;
                break;
            }
        }
        // 没有一次性分配完成 就清空分配数据 并返回
        if(!allAllocated){
            for (DoAllocateDetail detail : details) {
                detail.setAllocatedQty(BigDecimal.ZERO);
                detail.setAllocatedQtyUnit(BigDecimal.ZERO);
            }
            return error;
        }
        Iterator<Map.Entry<DoAllocateDetail, List<Stock4CombiDoAllocateDTO>>> iterator1 = detailStocksMap.entrySet().iterator();
        while (iterator1.hasNext()){
            Map.Entry<DoAllocateDetail, List<Stock4CombiDoAllocateDTO>> entry = iterator1.next();
            // 满足条件情况下 依次增加占用库存项 增加在途 和拣货任务
            DoAllocateDetail allocateDetail = entry.getKey();
            operateStock(allocateDetail,entry.getValue());
            // 修改状态 分配完成不更新出库单明细行状态
            allocateDetail.setLineStatus(DoStatus.ALLALLOCATED.getValue());
            DoAllocateHeader allocateHeader = allocateDetail.getDoAllocateHeader();
            allocateHeader.setStatus(DoStatus.ALLALLOCATED.getValue());
            DeliveryOrderHeader orderHeader = allocateHeader.getDeliveryOrderHeader();
            orderHeader.setStatus(DoStatus.ALLALLOCATED.getValue());

            // 打订单标记
            allocateHeader.setCheckFlag(Constants.OrderRegisterFlag.COMBI_SKU.getValue());
            orderHeader.setEmergencyFlag(YesNo.YES.getValue());
            orderHeader.setUserDeffine3(skuCombiHeader.getCombiBarcode());
            orderHeader.setCheckFlag(Constants.OrderRegisterFlag.COMBI_SKU.getValue());
            allocateHeader.setUserDeffine3(skuCombiHeader.getCombiBarcode());
            allocateHeader.setCheckFlag(Constants.OrderRegisterFlag.COMBI_SKU.getValue());
        }


        return IDoAllocate.ALC_RESULT_SUCCESS;
    }
    /**
     * 开始进行分配
     * @param alcDetail
     * @param stockAllocateResults 可进行分配的库存信息
     * @return 返回还未分配的数量
     */
    private void operateStock(DoAllocateDetail alcDetail, List<Stock4CombiDoAllocateDTO> stockAllocateResults) {

        for (Stock4CombiDoAllocateDTO allocateStock : stockAllocateResults) {

            // 更新库存
            OrderStockDTO stockDto = new OrderStockDTO();
            stockDto.setFmLocId(allocateStock.getLocId());
            stockDto.setLotId(allocateStock.getLotId());
            stockDto.setLpnNo(allocateStock.getLpnNo());
            stockDto.setSkuId(allocateStock.getSkuId());
            stockDto.setPlanQty(allocateStock.getUsedQty().negate());
            stockDto.setPlanQtyUnit(allocateStock.getUsedQty().negate());
            stockDto.setStockLpnId(allocateStock.getStockId());
            stockDto.setDocId(allocateStock.getDocId());
            stockDto.setDocLineId(allocateStock.getAlcDetailId());
            allocateOperator.setStockDto(stockDto);
            Map<String, Long> stockIdMap = stockService.operateStock(allocateOperator);

            // 新建拣货任务
            pickTaskService.createPickTask(alcDetail, allocateStock, allocateStock.getUsedQty(), allocateStock.getUsedQty(), packageInfoDetailService.findEaDetailByPackageId(alcDetail.getPackageId()).getId(), stockIdMap.get(Constants.StockType.STOCK_ALLOC
                    .getValue()), stockIdMap.get(Constants.StockType.STOCK_ALLOCING.getValue()));

            doDetailDAO.getSession().flush();
        }
    }
    /**
     * 拣货位库存不足时，调用此方法检查存储位库存，根据存储位库存的信息设置DoDetail的状态
     * @param alcDetail
     * @param replQty
     * @return
     */
    @Loggable
    private String setReplenishStatus(DoAllocateDetail alcDetail, BigDecimal replQty, String regionId) throws Exception {
        log.debug("SetReplenishStatus, alcId: {}", alcDetail.getId());

        //忽略包装
        StockBatchDTO stockBatchDTO = AllocateHelper.buildBatchDTOForAllocate(alcDetail, null);
    
        List<String> locTypeList = Lists.newArrayList(Constants.LocType.DEA.getValue(), Constants.LocType.RS.getValue(), Constants.LocType.ST.getValue(), Constants.LocType.EA.getValue());
        AllocationRuleHeader allocationRuleHeader = allocationRuleHeaderService.get(alcDetail.getAllocationRule());
        allocationRuleHeader.setDoType(alcDetail.getDoAllocateHeader().getDoType());
        List<String> locCodes = AllocateHelper.getLocCodes(alcDetail.getDoAllocateHeader());
        List<Stock2AllocateDTO> stockList = stockService.queryStockForAllocate(allocationRuleHeader, null, locTypeList, Lists.<String>newArrayList(), stockBatchDTO, alcDetail.getDoAllocateHeader().getShopId(), true, locCodes);
    
        // 全仓库存不够
        if (!isStockEnough(stockList, alcDetail.getExpectedQty())) {
            alcDetail.setNoStockFlag(YesNo.YES.getValue());
            throw new AllocateException(IDoAllocate.NO_ENOUGH_STOCK_QTY);
        }

        alcDetail.setNeedReplQty(replQty);
        alcDetail.setNeedReplQtyPcs(replQty);
        alcDetail.getDoAllocateHeader().setReplStatus(Constants.DoReplStatus.WAIT.getValue());
        alcDetail.getDoAllocateHeader().setReplStartTime(DateUtil.getNowTime());
        if (StringUtil.isNotEmpty(regionId)) {
            alcDetail.getDoAllocateHeader().setReplRegionId(Long.valueOf(regionId));
        }
        return IDoAllocate.NEED_REPL;
    }
    
    //库存是否足够: sum(actQty + pendingQty) > needQty，actQty已经减过allocatingQty
    private boolean isStockEnough(List<Stock2AllocateDTO> stockList, BigDecimal needQty) {
        BigDecimal actQtySum = BigDecimal.ZERO;
        for (Stock2AllocateDTO doDetailAllocateResult : stockList) {
            actQtySum = actQtySum.add(doDetailAllocateResult.getActQty()).add(doDetailAllocateResult.getPendingQty());
            if (actQtySum.compareTo(needQty) >= 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 开始进行分配
     * @param alcDetail 等分配的定单明细
     * @param sku 等分配的sku
     * @param stockAllocateResults 可进行分配的库存信息
     * @param notAllocQty 等分配的数量
     * @return 返回还未分配的数量
     */
    private BigDecimal doAllocate(DoAllocateDetail alcDetail, List<Stock2AllocateDTO> stockAllocateResults, BigDecimal notAllocQty, SkuDTO sku) {
        log.debug("DoAllocate, alcId: {}", alcDetail.getId());
        
        for (Stock2AllocateDTO allocateStock : stockAllocateResults) {

            // 得到的分配数量为：待分配数量与allocateStock中可分配数量的较小值
            BigDecimal allocateQty = allocateStock.getActQty().compareTo(notAllocQty) >= 0 ? notAllocQty : allocateStock.getActQty();
            if (allocateQty.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }

            // 更新库存
            OrderStockDTO stockDto = new OrderStockDTO();
            stockDto.setFmLocId(allocateStock.getLocId());
            stockDto.setLotId(allocateStock.getLotId());
            stockDto.setLpnNo(allocateStock.getLpnNo());
            stockDto.setSkuId(sku.getId());
            stockDto.setPlanQty(allocateQty.negate());
            stockDto.setPlanQtyUnit(allocateQty.negate());
            stockDto.setStockLpnId(allocateStock.getStockId());
            stockDto.setDocId(alcDetail.getDoHeaderId());
            stockDto.setDocLineId(alcDetail.getId());
            allocateOperator.setStockDto(stockDto);
            Map<String, Long> stockIdMap = stockService.operateStock(allocateOperator);
    
            // 新建拣货任务
            pickTaskService.createPickTask(alcDetail, allocateStock, allocateQty, allocateQty, packageInfoDetailService.findEaDetailByPackageId(alcDetail.getPackageId()).getId(), stockIdMap.get(Constants.StockType.STOCK_ALLOC
                    .getValue()), stockIdMap.get(Constants.StockType.STOCK_ALLOCING.getValue()));

            // 更新已分配数量
            notAllocQty = notAllocQty.subtract(allocateQty);
    
            // 表示到此为止,库存已经满足了此订单明细,此时应该退出,不再分配
            if (notAllocQty.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }

            doDetailDAO.getSession().flush();
        }
        return notAllocQty;
    }
}
