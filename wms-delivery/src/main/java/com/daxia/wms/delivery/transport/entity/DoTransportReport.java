package com.daxia.wms.delivery.transport.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.load.entity.LoadHeader;
import com.daxia.wms.master.entity.Warehouse;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "doc_do_transport_report")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = "IS_DELETED = 0 ")
@SQLDelete(sql = "UPDATE doc_do_transport_report SET IS_DELETED = 1 WHERE ID = ? AND VERSION = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class DoTransportReport extends WhBaseEntity {

    private static final long serialVersionUID = 1L;

    private Long id;
    // 目的地仓库
    private Long toWarehouseId;
    //运输单号
    private String transportNo;
    //运输日期
    private Date transportDate;
    //交接单号
    private String loadNo;
    //交接单ID
    private Long loadId;
    //运输工具
    private String transportTool;
    //温控方式
    private String temperatureType;
    //运输车辆
    private String transportCar;
    //签收人
    private String receiveBy;
    //驾驶人
    private String driveBy;
    // 运输员
    private String transportBy;
    //起运时间
    private Date transportStartDate;
    //运达时间
    private Date transportEndDate;
    //到达温度
    private BigDecimal arriveTemperature;
    //收货温度
    private BigDecimal receiveTemperature;
    //运输过程中温度
    private BigDecimal transportTemperature;
    //发运设备温度
    private BigDecimal equipmentTemperature;
    //预警时长
    private Integer warningTime;
    //备注
    private String remark;
    //温度检测装置
    private String temperatureCheckType;

    private LoadHeader loadHeader;

    private Warehouse warehouse;

    private Warehouse toWarehouse;

    private BigDecimal goodsQty;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "to_warehouseId")
    public Long getToWarehouseId() {
        return toWarehouseId;
    }

    public void setToWarehouseId(Long toWarehouseId) {
        this.toWarehouseId = toWarehouseId;
    }

    @Column(name = "transport_no")
    public String getTransportNo() {
        return transportNo;
    }

    public void setTransportNo(String transportNo) {
        this.transportNo = transportNo;
    }

    @Column(name = "transport_date")
    public Date getTransportDate() {
        return transportDate;
    }

    public void setTransportDate(Date transportDate) {
        this.transportDate = transportDate;
    }

    @Column(name = "load_no")
    public String getLoadNo() {
        return loadNo;
    }

    public void setLoadNo(String loadNo) {
        this.loadNo = loadNo;
    }

    @Column(name = "transport_tool")
    public String getTransportTool() {
        return transportTool;
    }

    public void setTransportTool(String transportTool) {
        this.transportTool = transportTool;
    }

    @Column(name = "temperature_type")
    public String getTemperatureType() {
        return temperatureType;
    }

    public void setTemperatureType(String temperatureType) {
        this.temperatureType = temperatureType;
    }

    @Column(name = "transport_car")
    public String getTransportCar() {
        return transportCar;
    }

    public void setTransportCar(String transportCar) {
        this.transportCar = transportCar;
    }

    @Column(name = "receive_by")
    public String getReceiveBy() {
        return receiveBy;
    }

    public void setReceiveBy(String receiveBy) {
        this.receiveBy = receiveBy;
    }

    @Column(name = "drive_by")
    public String getDriveBy() {
        return driveBy;
    }

    public void setDriveBy(String driveBy) {
        this.driveBy = driveBy;
    }

    @Column(name = "transport_start_date")
    public Date getTransportStartDate() {
        return transportStartDate;
    }

    public void setTransportStartDate(Date transportStartDate) {
        this.transportStartDate = transportStartDate;
    }

    @Column(name = "transport_end_date")
    public Date getTransportEndDate() {
        return transportEndDate;
    }

    public void setTransportEndDate(Date transportEndDate) {
        this.transportEndDate = transportEndDate;
    }

    @Column(name = "arrive_temperature")
    public BigDecimal getArriveTemperature() {
        return arriveTemperature;
    }

    public void setArriveTemperature(BigDecimal arriveTemperature) {
        this.arriveTemperature = arriveTemperature;
    }

    @Column(name = "receive_temperature")
    public BigDecimal getReceiveTemperature() {
        return receiveTemperature;
    }

    public void setReceiveTemperature(BigDecimal receiveTemperature) {
        this.receiveTemperature = receiveTemperature;
    }

    @Column(name = "transport_temperature")
    public BigDecimal getTransportTemperature() {
        return transportTemperature;
    }

    public void setTransportTemperature(BigDecimal transportTemperature) {
        this.transportTemperature = transportTemperature;
    }

    @Column(name = "equipment_temperature")
    public BigDecimal getEquipmentTemperature() {
        return equipmentTemperature;
    }

    public void setEquipmentTemperature(BigDecimal equipmentTemperature) {
        this.equipmentTemperature = equipmentTemperature;
    }

    @Column(name = "warning_time")
    public Integer getWarningTime() {
        return warningTime;
    }

    public void setWarningTime(Integer warningTime) {
        this.warningTime = warningTime;
    }

    @Column(name = "remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Column(name = "temperature_check_type")
    public String getTemperatureCheckType() {
        return temperatureCheckType;
    }

    public void setTemperatureCheckType(String temperatureCheckType) {
        this.temperatureCheckType = temperatureCheckType;
    }

    @Column(name = "load_id")
    public Long getLoadId() {
        return loadId;
    }

    public void setLoadId(Long loadId) {
        this.loadId = loadId;
    }

    @Column(name = "transport_by")
    public String getTransportBy() {
        return transportBy;
    }

    public void setTransportBy(String transportBy) {
        this.transportBy = transportBy;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "load_id", insertable = false, updatable = false)
    public LoadHeader getLoadHeader() {
        return loadHeader;
    }

    public void setLoadHeader(LoadHeader loadHeader) {
        this.loadHeader = loadHeader;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "warehouse_id", insertable = false, updatable = false)
    public Warehouse getWarehouse() {
        return warehouse;
    }

    public void setWarehouse(Warehouse warehouse) {
        this.warehouse = warehouse;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "to_warehouseId", insertable = false, updatable = false)
    public Warehouse getToWarehouse() {
        return toWarehouse;
    }

    public void setToWarehouse(Warehouse toWarehouse) {
        this.toWarehouse = toWarehouse;
    }

    @Column(name = "goods_qty")
    public BigDecimal getGoodsQty() {
        return goodsQty;
    }

    public void setGoodsQty(BigDecimal goodsQty) {
        this.goodsQty = goodsQty;
    }
}