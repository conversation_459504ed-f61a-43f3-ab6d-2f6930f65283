package com.daxia.wms.delivery.load.service.impl;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;


import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.load.dao.ReversalHandoverDetailDAO;
import com.daxia.wms.delivery.load.dao.ReversalHandoverHeaderDAO;
import com.daxia.wms.delivery.load.entity.ReversalHandoverDetail;
import com.daxia.wms.delivery.load.entity.ReversalHandoverHeader;
import com.daxia.wms.delivery.load.filter.ReversalHandoverDetailFilter;
import com.daxia.wms.delivery.load.service.ReversalHandoverDetailService;
import com.daxia.wms.Constants;

/**
 * 逆向交接单接口实现类
 */
@Name("com.daxia.wms.delivery.reversalHandoverDetailService")
@lombok.extern.slf4j.Slf4j
public class ReversalHandoverDetailServiceImpl implements ReversalHandoverDetailService, Serializable {

	private static final long serialVersionUID = 73805189912603333L;

	@In
	private ReversalHandoverHeaderDAO reversalHandoverHeaderDAO;

	@In
	private ReversalHandoverDetailDAO reversalHandoverDetailDAO;

	public ReversalHandoverHeader get(Long id) {
		return reversalHandoverHeaderDAO.get(id);
	}

	@Override
	public DataPage<ReversalHandoverDetail> findReversalHandoverDetails(
			ReversalHandoverDetailFilter filter, int startIndex, int pageSize) {
		return reversalHandoverDetailDAO.findRangeByFilter(filter, startIndex,pageSize);
	}

	@Override
	@Transactional
	public void add(String cartonNo, DeliveryOrderHeader doHeader) {
		ReversalHandoverDetail reversalHandoverDetail = new ReversalHandoverDetail();
		reversalHandoverDetail.setCartonNo(cartonNo);
		reversalHandoverDetail.setDoNo(doHeader.getDoNo());
		reversalHandoverDetail.setDoStatus(doHeader.getStatus());
		reversalHandoverDetail.setReversalStatus(Constants.ReversalStatus.WAIT_RECEIVE.getValue());
		reversalHandoverDetail.setReleaseStatus(doHeader.getReleaseStatus());
		reversalHandoverDetailDAO.save(reversalHandoverDetail);
	}

	@Override
	public boolean isCartonInReversalHandoverDetail(String cartonNo, Long whId) {
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("cartonNo", cartonNo);
		params.put("warehouseId", whId);
		return reversalHandoverDetailDAO.isExists(params, null);
	}
}
