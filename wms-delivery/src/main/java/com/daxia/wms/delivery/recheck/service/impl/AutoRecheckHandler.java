package com.daxia.wms.delivery.recheck.service.impl;

import org.jboss.seam.annotations.In;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.delivery.deliveryorder.service.DoAutoHandler;
import com.daxia.wms.delivery.recheck.service.ReCheckService;
import com.daxia.wms.Constants.YesNo;

@lombok.extern.slf4j.Slf4j
public class AutoRecheckHandler extends DoAutoHandler {
    @In
    private ReCheckService reCheckService;

	@Override
	public void handle(Long doId) {
		Integer convenienceMode = SystemConfig.getConfigValueInt("delivery.mode.convenience.tms", ParamUtil.getCurrentWarehouseId());
		if (YesNo.YES.getValue().equals(convenienceMode)) {
			reCheckService.saveOneCarton4WBL(doId);
		}
	}

	@Override
	public void setName() {
		this.name = "autoRecheckHandler";
	}
}
