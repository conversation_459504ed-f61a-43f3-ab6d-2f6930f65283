package com.daxia.wms.delivery.print.service.carton;

import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DeliveryLimitType;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.print.dto.BaseCartonPrintDTO;
import com.daxia.wms.delivery.print.dto.CartonDetailPrintDTO;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.helper.PrintCartonHelper;
import com.daxia.wms.delivery.print.service.PrintWaybillService;
import com.daxia.wms.delivery.recheck.entity.CartonDetail;
import com.daxia.wms.master.entity.Supplier;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.service.SupplierService;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.print.dto.PrintCfg;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.print.utils.PrintHelper;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 废弃的类，后期需要全部使用lodop实现
 */
@Name("printRTVWaybillService")
@lombok.extern.slf4j.Slf4j
public class PrintRTVWaybillServiceImpl extends AbstractPrintWaybillService implements PrintWaybillService {
    @In
    private WarehouseService warehouseService;
    @In
    private SupplierService supplierService;

    @Create
    public void init () {
        this.setWaybillType(Constants.WaybillType.JD);
    }

    @Override
    protected void buildDTO(List<PrintReportDto> printReportDtos, DeliveryOrderHeader doHeader, BaseCartonPrintDTO carton, int index, int count) {
        CartonPrintDTO cartonPrintDTO = setBaseCartonInfo(doHeader, carton, count, index);
        cartonPrintDTO.setIsPrinted(carton.getIsPrinted());
        cartonPrintDTO.setRefNo1(doHeader.getRefNo1());
        cartonPrintDTO.setUserDeffine5(doHeader.getUserDeffine5());
        cartonPrintDTO.setParentDoNo(doHeader.getUserDeffine3());
        cartonPrintDTO.setReceivable(doHeader.getReceivable());
        cartonPrintDTO.setExpectedArriveTime1(doHeader.getExpectedArriveTime1());
        cartonPrintDTO.setPostCode(doHeader.getPostCode());
        cartonPrintDTO.setOrderTime(doHeader.getCreatedAt());
        cartonPrintDTO.setRemark(doHeader.getNotes());

        cartonPrintDTO.setOrderSubType(doHeader.getOrderSubType());
        String expectedArriveTimeStr1 = DateUtil.dateToString(doHeader.getExpectedArriveTime1(),DateUtil.DATETIME_PATTERN);
        String expectedArriveTimeStr2 = DateUtil.dateToString(doHeader.getExpectedArriveTime2(),DateUtil.DATETIME_PATTERN);
        cartonPrintDTO.setExpectedArriveTimeStr1(StringUtil.isEmpty(expectedArriveTimeStr1)?"":expectedArriveTimeStr1);
        cartonPrintDTO.setExpectedArriveTimeStr2(StringUtil.isEmpty(expectedArriveTimeStr2)?"":expectedArriveTimeStr2);

        //设置仓库code
        cartonPrintDTO.setWarehouseCode(buildWarehouseCode());
        // 设置图片路径
        cartonPrintDTO.setBasePrintImgPath(PrintHelper.getBasePrintImgPath());
        //设置公司logo
        String comLogo = SystemConfig.getConfigValue("print.carton.imgLogo", ParamUtil.getCurrentWarehouseId());
        cartonPrintDTO.setComLogo(comLogo);

        //设置客户栏联系方式
        cartonPrintDTO.setTelOrMobile(PrintCartonHelper.buildTelOrMobile(doHeader));
        //设置送货时间段
        cartonPrintDTO.setExpectReceiveTime(StringUtil.trim(doHeader.getExpectedReceiveTime()));
        //设置是否准时送标识，用以控制箱标签上打印代码上下是否显示“横线”
        if (DeliveryLimitType.ONTIME.getValue().equals(doHeader.getIsHalfDayDelivery())) {
            cartonPrintDTO.setIsOnTimeFlag(true);
        } else {
            cartonPrintDTO.setIsOnTimeFlag(false);
        }
        //设置收货人信息
        cartonPrintDTO.setClientName(doHeader.getConsigneeName());
        //设置父SO号及包含子SO数量
        cartonPrintDTO.setParentSo(PrintCartonHelper.buildParentSoNum(doHeader));
        //设置预计发车时间(形如：13:00)
        cartonPrintDTO.setDepartureTime(StringUtil.trim(doHeader.getDepartureTime()));
        cartonPrintDTO.setStationName(doHeader.getStationName());
        cartonPrintDTO.setInvoiceFlag(doHeader.getInvoiceFlag());
        cartonPrintDTO.setSortGridNo(doHeader.getSortGridNo());
        cartonPrintDTO.setWaveNo(doHeader.getWaveHeader().getWaveNo());
        cartonPrintDTO.setOriginalSoCode(doHeader.getOriginalSoCode());
        // 设置供应商或者仓库信息信息
        setCilentInfo(cartonPrintDTO, doHeader);
        // 是否需要待收货款
        boolean receivable = doHeader.getReceivable() != null
                && doHeader.getReceivable().compareTo(BigDecimal.ZERO) > 0;
        cartonPrintDTO.setNeedReceivable(receivable);
        if (receivable) {
            // 设置代收金额
            cartonPrintDTO.setServiceCodAmount(doHeader.getReceivable().toString());
        }
        // 收件人信息
        cartonPrintDTO.setClientName(PrintCartonHelper.buildConsigneeName(doHeader));
        cartonPrintDTO.setClientAddress(PrintCartonHelper.buildAddress(doHeader));
        cartonPrintDTO.setClientPhone(PrintCartonHelper.buildTelOrMobile(doHeader));
        cartonPrintDTO.setRefNo(StringUtil.notNullString(doHeader.getOriginalSoCode(),""));
        setProductInfo(doHeader, cartonPrintDTO);
    
        buildExtAttr(genPrintData(null, doHeader), doHeader, cartonPrintDTO);
    
        printReportDtos.add(cartonPrintDTO);
    }

    /**
     * 设置箱标签打印dto的基本信息
     *
     * @param doHeader
     * @param carton
     * @param count    总箱数
     * @param index    当前第几箱
     * @return 箱标签打印dto
     */
    private CartonPrintDTO setBaseCartonInfo(DeliveryOrderHeader doHeader, BaseCartonPrintDTO carton, int count, int index) {
        CartonPrintDTO cartonPrintDTO = new CartonPrintDTO();
        cartonPrintDTO.setCartonNo(carton.getCartonNo());
        cartonPrintDTO.setDoNo(doHeader.getDoNo());
        cartonPrintDTO.setCartonId(carton.getId());
        cartonPrintDTO.setOutRefNo(doHeader.getRefNo1());
        cartonPrintDTO.setPackageType(carton.getPackageType());

        // 设置寄件人地址信息
        setSendAddressInfo(doHeader,cartonPrintDTO);
        //设置地址信息
        cartonPrintDTO.setClientAddress(PrintCartonHelper.buildAddress(doHeader));
        cartonPrintDTO.setClientName(doHeader.getConsigneeName());
        cartonPrintDTO.setCartonIndex(index);
        if (Constants.DoStatus.ALL_DELIVER.getValue().equals(doHeader.getStatus()) ||
                Constants.DoStatus.ALL_CARTON.getValue().equals(doHeader.getStatus())
                || Constants.DoStatus.ALL_CARTON.getValue().equals(doHeader.getPcsStatus())
                || Constants.PackageType.C.getValue().equals(carton.getPackageType())) {
            cartonPrintDTO.setCartonCount(count);
        }

        //设置箱明细信息
        List<CartonDetail> detailList = cartonService.getCartonDetailByHeaderId(carton.getId());
        List<CartonDetailPrintDTO> subDtoList = new ArrayList<CartonDetailPrintDTO>();
        if(CollectionUtils.isNotEmpty(detailList)){
            for (CartonDetail cartonDetail : detailList) {
                CartonDetailPrintDTO dto = new CartonDetailPrintDTO();
                dto.setProductCode(cartonDetail.getSku().getProductCode());
                dto.setProductName(cartonDetail.getSku().getProductCname());
                subDtoList.add(dto);
            }
        }
        cartonPrintDTO.setSubDtoList(subDtoList);
        return cartonPrintDTO;
    }

    /**
     * 设置收货相关信息
     */
    private void setCilentInfo(CartonPrintDTO cartonPrintDTO, DeliveryOrderHeader doHeader) {
        if (null != doHeader && Constants.DoType.ALLOT.getValue().equals(doHeader.getDoType()) && StringUtil.isNotEmpty(doHeader.getEdi2())) {
            Warehouse house = warehouseService.getWarehouse(Long.valueOf(doHeader.getEdi2()));
            if (house != null) {
                cartonPrintDTO.setToInfo(house.getNickName());
            }
        } else if (null != doHeader && Constants.DoType.RTV.getValue().equals(doHeader.getDoType()) && null != doHeader.getSupplierId()) {
            Supplier supplier = supplierService.getSupplier(doHeader.getSupplierId());
            if (supplier != null) {
                cartonPrintDTO.setToInfo("RTV");
            }
        }else if (null!=doHeader&&Constants.DoType.SELL.getValue().equals(doHeader.getDoType())){
            cartonPrintDTO.setToInfo(doHeader.getCarrier().getDistSuppCompName());
        }
    }

    @Override
    protected PrintData genPrintData(List<PrintReportDto> dtoList, DeliveryOrderHeader doHeader) {
        PrintData printData = new PrintData();
        printData.setDtoList(dtoList);
        printData.setPrintCfg(generateNormalPrintCfg(doHeader));
        return printData;
    }

    private PrintCfg generateNormalPrintCfg(DeliveryOrderHeader doHeader) {
        PrintCfg config = new PrintCfg("wayBillNormal", "100", "150");
        if(Constants.DoType.ALLOT.getValue().equals(doHeader.getDoType())){
            config = new PrintCfg("wayBillTO", "100", "150");
        }
        this.setPrintCfg(config);
        return config;
    }
}