package com.daxia.wms.delivery.print.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@lombok.extern.slf4j.Slf4j
public class LoadPrint implements Serializable {

	private static final long serialVersionUID = -2526443736489797385L;

	private Integer cartonQty;

    private String loadNo;
    
    /**
     * 总订单数
     */
    private Long doQty;
    
    /**
     * 司机姓名
     */
    private String driverName;
    
    /**
     * 车牌号
     */
    private String vechileNo;
    
    /**
     * 发货方
     */
    private String shipper;
    
    /**
     * 配送商
     */
    private String carrierName;
    
    /**
     * 大约时间
     */
    private Date printTime;
    
    /**
     * 发运时间
     */
    private Date shipTime;
    
    

    private List<LoadPrintSub> subs = new ArrayList<LoadPrintSub>();

    public Integer getCartonQty() {
        return cartonQty;
    }

    public void setCartonQty(Integer cartonQty) {
        this.cartonQty = cartonQty;
    }

    public String getLoadNo() {
        return loadNo;
    }

    public void setLoadNo(String loadNo) {
        this.loadNo = loadNo;
    }

    public List<LoadPrintSub> getSubs() {
        return subs;
    }

    public void setSubs(List<LoadPrintSub> subs) {
        this.subs = subs;
    }

	public Long getDoQty() {
		return doQty;
	}

	public void setDoQty(Long doQty) {
		this.doQty = doQty;
	}

	public String getDriverName() {
		return driverName;
	}

	public void setDriverName(String driverName) {
		this.driverName = driverName;
	}

	public String getVechileNo() {
		return vechileNo;
	}

	public void setVechileNo(String vechileNo) {
		this.vechileNo = vechileNo;
	}

	public String getShipper() {
		return shipper;
	}

	public void setShipper(String shipper) {
		this.shipper = shipper;
	}

	public String getCarrierName() {
		return carrierName;
	}

	public void setCarrierName(String carrierName) {
		this.carrierName = carrierName;
	}

	public Date getPrintTime() {
		return printTime;
	}

	public void setPrintTime(Date printTime) {
		this.printTime = printTime;
	}

	public Date getShipTime() {
		return shipTime;
	}

	public void setShipTime(Date shipTime) {
		this.shipTime = shipTime;
	}
	
}