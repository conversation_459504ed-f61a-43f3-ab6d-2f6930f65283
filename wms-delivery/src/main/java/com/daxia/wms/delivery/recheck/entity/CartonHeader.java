package com.daxia.wms.delivery.recheck.entity;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.master.entity.PackingDesk;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 装箱头信息
 */
@Entity
@Table(name = "doc_carton_header")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_carton_header set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class CartonHeader extends WhBaseEntity {

    private static final long serialVersionUID = 7880994350324178974L;

    private Long id;

    /**
     * 箱号
     */
    private String cartonNo;

    private DeliveryOrderHeader doHeader;

    /**
     * 毛重
     */
    private BigDecimal grossWeight;

    /**
     * 实测重量
     */
    private BigDecimal actualGrossWeight;

    /**
     * 体积
     */
    private BigDecimal volume;

    /**
     * 运单号
     */
    private String trackingNo;

    //关联单号，目前菜鸟电子面单会用序列号生成一个号码，用户GET/CANCEL/PRINT接口
    private String refNo;

    /**
     * 实测体积
     */
    private BigDecimal actualVolume;

    /**
     * 是否已经称重，0：未称重；1：已经称重
     */
    private int weightFlag;

    /**
     * 净重
     */
    private BigDecimal netWeight;

    /**
     * 运单号 （第三方运单号）
     */
    private String wayBill;

    private List<CartonDetail> cartonDetails;

    /**
     * 装箱单状态 40 ：装箱完成 90 ： 取消
     */
    private String status;

    private String lpnNo;// LPN_NO

    private Integer isPrinted;// 是否已打印

    /**
     * 包材编码
     */
    private String packMaterialNo;

    /**
     * 是否需要自动发运
     */
    private Integer autoDeliveryFlag;

    // 大头笔（菜鸟电子面单）
    private String shortAddress;

    // 集包地名（菜鸟电子面单）
    private String packageCenterName;

    // 集包地编码（菜鸟电子面单）
    private String packageCenterCode;

    // 发货网点名称（菜鸟电子面单）
    private String shippingBranchName;

    // 发货网点代码（菜鸟电子面单）
    private String shippingBranchCode;

    // 打印配置，客户端打印调用
    private String printConfig;

    //运费
    private BigDecimal freight;

    //核检人(药检)
    private String checkBy;

    private String weighBy;

    private Date weighTime;
    //菜鸟电子面单打印数据
    private String printData;

    private PackingDesk packingDesk;

    //扩展字段, 装箱类型 C or B
    private String ext1;

    private BigDecimal unitsQty;
    private BigDecimal qty;

    private Integer cartonIndex;

    private Integer totalIndex;

    private Integer wholesaleWaybillFlag;
    /**
     * 货物放行 0未放行 1 已放行'
     */
    private Integer  goodPass;
    @Column(name = "good_pass")
    public Integer getGoodPass() {
        return goodPass;
    }

    public void setGoodPass(Integer goodPass) {
        this.goodPass = goodPass;
    }

    /**
     * 核拣装箱录像地址
     */
    private String videoUrl;
    @Column(name = "video_url")
    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "CARTON_NO")
    public String getCartonNo() {
        return cartonNo;
    }

    public void setCartonNo(String cartonNo) {
        if (Constants.YesNo.YES.getValue().equals(wholesaleWaybillFlag) && StringUtil.isNotBlank(this.cartonNo)) {
            return;
        }
        this.cartonNo = cartonNo;

    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DO_HEADER_ID")
    @Where(clause = " IS_DELETED = 0 ")
    public DeliveryOrderHeader getDoHeader() {
        return doHeader;
    }

    public void setDoHeader(DeliveryOrderHeader doHeader) {
        this.doHeader = doHeader;
    }

    @Transient
    public Long getDoHeaderId() {
        return doHeader != null ? doHeader.getId() : null;
    }

    public void setDoHeaderId(Long doHeaderId) {
        this.doHeader = new DeliveryOrderHeader();
        doHeader.setId(doHeaderId);
    }

    @Column(name = "GROSSWEIGHT")
    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }

    @Column(name = "ACTUAL_GROSSWEIGHT")
    public BigDecimal getActualGrossWeight() {
        return actualGrossWeight;
    }

    public void setActualGrossWeight(BigDecimal actualGrossWeight) {
        this.actualGrossWeight = actualGrossWeight;
    }

    @Column(name = "VOLUME")
    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    @Column(name = "ACTUAL_VOLUME")
    public BigDecimal getActualVolume() {
        return actualVolume;
    }

    public void setActualVolume(BigDecimal actualVolume) {
        this.actualVolume = actualVolume;
    }

    @Column(name = "IS_WEIGHT")
    public int getWeightFlag() {
        return weightFlag;
    }

    public void setWeightFlag(int weightFlag) {
        this.weightFlag = weightFlag;
    }

    @Transient
    public boolean isWeight() {
        return this.weightFlag == 1;
    }

    public void setWeight(boolean weight) {
        this.weightFlag = weight ? 1 : 0;
    }

    @Transient
    public Long getIsWeight() {// TODO check later
        return Long.valueOf(weightFlag);
    }

    public void setIsWeight(Long isWeight) {
        this.weightFlag = isWeight.intValue();
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "cartonHeader")
    @Where(clause = " IS_DELETED = 0 ")
    public List<CartonDetail> getCartonDetails() {
        return cartonDetails;
    }

    public void setCartonDetails(List<CartonDetail> cartonDetails) {
        this.cartonDetails = cartonDetails;
    }

    @Column(name = "STATUS")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "WAY_BILL")
    public String getWayBill() {
        return wayBill;
    }

    public void setWayBill(String wayBill) {
        this.wayBill = wayBill;
    }

    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    @Column(name = "NETWEIGHT")
    public BigDecimal getNetWeight() {
        return netWeight;
    }

    @Column(name = "LPN_NO")
	public String getLpnNo() {
		return lpnNo;
	}

	public void setLpnNo(String lpnNo) {
		this.lpnNo = lpnNo;
	}

	@Column(name = "IS_PRINTED")
	public Integer getIsPrinted() {
		return isPrinted;
	}

	public void setIsPrinted(Integer isPrinted) {
		this.isPrinted = isPrinted;
	}

	@Column(name = "PACK_MATERIAL")
	public String getPackMaterialNo() {
		return packMaterialNo;
	}

	public void setPackMaterialNo(String packMaterialNo) {
		this.packMaterialNo = packMaterialNo;
	}

	@Column(name = "AUTO_DELIVERY_FLAG")
	public Integer getAutoDeliveryFlag() {
		return autoDeliveryFlag;
	}

	public void setAutoDeliveryFlag(Integer autoDeliveryFlag) {
		this.autoDeliveryFlag = autoDeliveryFlag;
	}

	@Column(name = "tracking_no")
	public String getTrackingNo() {
		return trackingNo;
	}

	public void setTrackingNo(String trackingNo) {
		this.trackingNo = trackingNo;
	}

    @Column(name = "short_address")
    public String getShortAddress() {
        return shortAddress;
    }

    public void setShortAddress(String shortAddress) {
        this.shortAddress = shortAddress;
    }

    @Column(name = "package_center_name")
    public String getPackageCenterName() {
        return packageCenterName;
    }

    public void setPackageCenterName(String packageCenterName) {
        this.packageCenterName = packageCenterName;
    }

    @Column(name = "package_center_code")
    public String getPackageCenterCode() {
        return packageCenterCode;
    }

    public void setPackageCenterCode(String packageCenterCode) {
        this.packageCenterCode = packageCenterCode;
    }

    @Column(name = "shipping_branch_name")
    public String getShippingBranchName() {
        return shippingBranchName;
    }

    public void setShippingBranchName(String shippingBranchName) {
        this.shippingBranchName = shippingBranchName;
    }

    @Column(name = "shipping_branch_code")
    public String getShippingBranchCode() {
        return shippingBranchCode;
    }

    public void setShippingBranchCode(String shippingBranchCode) {
        this.shippingBranchCode = shippingBranchCode;
    }

    @Column(name = "print_config")
    public String getPrintConfig() {
        return printConfig;
    }

    public void setPrintConfig(String printConfig) {
        this.printConfig = printConfig;
    }

    @Column(name = "ref_no")
    public String getRefNo() {
        return refNo;
    }

    public void setRefNo(String refNo) {
        this.refNo = refNo;
    }

    @Column(name = "freight")
    public BigDecimal getFreight() {
        return freight;
    }

    public void setFreight(BigDecimal freight) {
        this.freight = freight;
    }

    @Column(name = "check_by")
    public String getCheckBy() {
        return checkBy;
    }

    public void setCheckBy(String checkBy) {
        this.checkBy = checkBy;
    }

    @Column(name = "weigh_time")
    public Date getWeighTime() {
        return weighTime;
    }

    public void setWeighTime(Date weighTime) {
        this.weighTime = weighTime;
    }

    @Column(name = "weigh_by")
    public String getWeighBy() {
        return weighBy;
    }

    public void setWeighBy(String weighBy) {
        this.weighBy = weighBy;
    }
    @Column(name = "print_data")
    public String getPrintData() {
        return printData;
    }

    public void setPrintData(String printData) {
        this.printData = printData;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PACKING_DESK_ID")
    @Where(clause = " IS_DELETED = 0 ")
    public PackingDesk getPackingDesk() {
        return packingDesk;
    }

    public void setPackingDesk(PackingDesk packingDesk) {
        this.packingDesk = packingDesk;
    }

    @Column(name = "ext_1")
    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }
    @Transient
    public BigDecimal getUnitsQty() {
        return unitsQty;
    }

    public void setUnitsQty(BigDecimal unitsQty) {
        this.unitsQty = unitsQty;
    }

    @Transient
    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    @Transient
    public Integer getCartonIndex() {
        return cartonIndex;
    }

    public void setCartonIndex(Integer cartonIndex) {
        this.cartonIndex = cartonIndex;
    }
    @Transient
    public Integer getTotalIndex() {
        return totalIndex;
    }

    public void setTotalIndex(Integer totalIndex) {
        this.totalIndex = totalIndex;
    }

    @Transient
    public Integer getWholesaleWaybillFlag() {
        return wholesaleWaybillFlag;
    }

    public void setWholesaleWaybillFlag(Integer wholesaleWaybillFlag) {
        this.wholesaleWaybillFlag = wholesaleWaybillFlag;
    }
}
