package com.daxia.wms.delivery.load.dto;

import java.io.Serializable;

import org.json.JSONException;
import org.json.JSONObject;

@lombok.extern.slf4j.Slf4j
public class LoadScanDTO implements Serializable{

	private static final long serialVersionUID = -7382067611634275439L;
	private Long loadDetailId;//交接单明细ID
	private Long doHeaderId;
	private String doNo; //订单号
	private String trackingNo;//运单号
	private String cartonNo;//箱号
	private String lpnNo;//托盘号
	
	private String consigneeName;//收货人
	private String provinceCname;//省
	private String cityCname;//市
	private String countyCname;//区
	private String address;//收货地址
	
	public Long getLoadDetailId() {
		return loadDetailId;
	}
	public void setLoadDetailId(Long loadDetailId) {
		this.loadDetailId = loadDetailId;
	}
	
	public String getDoNo() {
		return doNo;
	}
	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}
	public String getTrackingNo() {
		return trackingNo;
	}
	public void setTrackingNo(String trackingNo) {
		this.trackingNo = trackingNo;
	}
	
	public String getCartonNo() {
		return cartonNo;
	}
	public void setCartonNo(String cartonNo) {
		this.cartonNo = cartonNo;
	}
	public String getLpnNo() {
		return lpnNo;
	}
	public void setLpnNo(String lpnNo) {
		this.lpnNo = lpnNo;
	}
	public String getConsigneeName() {
		return consigneeName;
	}
	public void setConsigneeName(String consigneeName) {
		this.consigneeName = consigneeName;
	}
	public String getProvinceCname() {
		return provinceCname;
	}
	public void setProvinceCname(String provinceCname) {
		this.provinceCname = provinceCname;
	}
	public String getCityCname() {
		return cityCname;
	}
	public void setCityCname(String cityCname) {
		this.cityCname = cityCname;
	}
	public String getCountyCname() {
		return countyCname;
	}
	public void setCountyCname(String countyCname) {
		this.countyCname = countyCname;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	
	public Long getDoHeaderId() {
		return doHeaderId;
	}
	public void setDoHeaderId(Long doHeaderId) {
		this.doHeaderId = doHeaderId;
	}
	public JSONObject toJSONObject() throws JSONException{   
        JSONObject json = new JSONObject();   
        json.put("loadDetailId", loadDetailId);   
        json.put("doHeaderId", doHeaderId);   
        json.put("doNo", doNo);
        json.put("trackingNo", trackingNo);
        json.put("cartonNo", cartonNo);
        json.put("lpnNo", lpnNo);
        json.put("consigneeName", consigneeName);
        json.put("provinceCname", provinceCname);
        json.put("cityCname", cityCname);
        json.put("countyCname", countyCname);
        json.put("address", address);
       return json;   
    }   

}
