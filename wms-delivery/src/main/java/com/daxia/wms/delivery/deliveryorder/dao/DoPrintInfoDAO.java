package com.daxia.wms.delivery.deliveryorder.dao;

import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.print.dto.DoPrintSub;

/**
 * 提供一些DO打印信息方面的查询方法
 */
@Name("com.daxia.wms.delivery.doPrintInfoDAO")
@lombok.extern.slf4j.Slf4j
public class DoPrintInfoDAO extends HibernateBaseDAO<DeliveryOrderHeader, Long> {

    private static final long serialVersionUID = 2749242810108826315L;

    /**
     * 获取DO明细的商品在不同库位上的数量分布
     * 
     * @param sub
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Object[]> getQtyDistribution(DoPrintSub sub) {
        String sql = "Select l.Loc_Code, t.Qty From tsk_pick t, md_location l "
                + " Where t.Fm_Loc_Id = l.id And t.Doc_Line_Id = :doDetailId "
                + " And t.Warehouse_Id = :whId And l.Warehouse_Id = :whId "
                + " And t.is_deleted = 0 And l.is_deleted = 0 ";
        Query query = createSQLQuery(sql);
        query.setParameter("doDetailId", sub.getDoDetailId());
        query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

}