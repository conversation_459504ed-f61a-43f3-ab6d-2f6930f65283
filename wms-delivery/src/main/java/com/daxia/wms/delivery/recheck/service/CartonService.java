package com.daxia.wms.delivery.recheck.service;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.Constants.PrintStatus;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.recheck.dto.CombineBindingInfo;
import com.daxia.wms.delivery.recheck.entity.CartonDetail;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeaderHis;
import com.daxia.wms.delivery.recheck.filter.CartonHeaderFilter;
import com.daxia.wms.delivery.wave.dto.BatchGroupDoCartonInfoDTO;
import com.daxia.wms.delivery.wave.filter.BatchGroupDoCartonFilter;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * 装箱单业务
 */
public interface CartonService {
 
    /**
     * 
     * <pre>
     * 根据装箱单ID取得装箱单信息
     * </pre>
     *
     * @param cartonHeaderId  装箱单ID
     * @return
     */
    public CartonHeader getCartonHeader(Long cartonHeaderId);

    /**
     * <pre>
     * 查询装箱单信息。
     * </pre>
     *
     * @param filter
     * @return
     */
    public List<CartonHeader> query(CartonHeaderFilter filter);

    /**
     * 
     * <pre>
     * 根据NO获取装箱头信息
     * </pre>
     *
     * @param carrierNo
     * @return
     */
    public CartonHeader getCartonByNo(String carrierNo);
	/**
	 * <pre>
	 * 根据lpnNO获取装箱头信息
	 * </pre>
	 *
	 * @param cartonNo
	 * @return
	 */
    CartonHeader findOneByLpnNo(String cartonNo);

    CartonHeader findByCartonNoForYouBei(String cartonNo);
	CartonHeader findByWayBillForYouBei(String cartonNo);

	/**
     *
     * <pre>
     * 根据NO获取装箱头信息
     * </pre>
     *
     * @param carrierNo
     * @return
     */
    public CartonHeader getCartonByNoForYouBei(String carrierNo);

    /**
     * 
     * <pre>
     * 更新装箱头信息
     * </pre>
     *
     * @param cartonHeader
     */
    public void update(CartonHeader cartonHeader);

    /**
     * 保存carton重量
     * @param cartonHeader
     * @param weight
     */
    public int saveCartonWeight(CartonHeader cartonHeader,BigDecimal weight,String operator);

	/**
	 * 团购波次称重
	 * @param cartonHeader
	 * @param weight
	 * @param operator
     */
	public void saveCartonWeight4GroupWave(CartonHeader cartonHeader,BigDecimal weight,String operator);
    /**
     * 根据do num查询箱头
     * @param doNum
     * @return 
     */
    public List<CartonHeader> findByDoNo(String doNum);
    
    /**
     * 拼箱
     * @param fromCartonId
     * @param toCartonId
     */
    public void boxing(Long fromCartonId, Long toCartonId);
    
    /**
     * 取消箱信息（目前菜鸟调用，具体看实现类）
     * @param fromCartonId
     */
    public void cancelCartonById(Long fromCartonId);

	/**
	 * 取消预下单箱信息
	 * @param doId
     */
	public void cancelTempCartonByDoId(Long doId);
    
    /**
     * 拆箱
     * @param fromCartonId
     * @param cartonCount 拆成多少箱
     */
    public void unBoxing(Long fromCartonId, int cartonCount);
    
    /**
     * 根据doId找到一个未装车的箱头
     * @param doId
     * @return CartonHeader
     */
    public CartonHeader findANotLoadedCartonHeaderByDoId(Long doId);
    
    /**
     * 找到doId下有多少箱
     * @param doId
     * @return 
     */
    public int countCartonNumOfADo(Long doId);
    
    /**
     * 根据doId找到其中一个箱头
     * @param doId
     * @return CartonHeader
     */
    public CartonHeader findCartonHeaderByDoId(Long doId) ;
    
    /**
     * 根据doNum找到未称重的箱头
     * @param doNum
     * @return
     */
    public String findUnWeighCartonHeader(String doNum);
    
    
    /**
     * 检查一个订单是否对应多个箱子。
     * @param doHeaderId 订单号
     * @return
     */
    public boolean isMultiCartonOfADo(Long doHeaderId);
    
    /**
     * 判断订单下是否存在未交接的箱子
     * 
     * @param doHeaderId
     * @return
     */
    public Boolean isDoCartonsAllLoaded(Long doHeaderId);
    
    /**
     * 新增箱（包含箱号，运单号等）
     * @param doHeader
     * @param cartonHeader
     */
    public CartonHeader genNewCarton(DeliveryOrderHeader doHeader);

    
    /**
     * 获取订单下已交接/未交接的包裹号
     * @param doHeaderId 订单头Id
     * @param allLoad True为查询已交接的包裹，False为查询未交接的包裹
     * @return
     */
    public List<String> getCartonNoByDoId(Long doHeaderId,Boolean allLoad);
    
    /**
     * 根据ID查询装箱头历史
     * @param doId
     * @return
     */
    public CartonHeaderHis queryCartonHeaderHisById(Long cartonHeaderId);

    /**
     * 给接口调用的，要加仓库id
     * @param cartonNo
     * @param valueOf
     * @return
     */
	public CartonHeader getCartonByNoAndWhId(String cartonNo, Long warehouseID);
	
	/**
	 * 物理删除缺货DO的装箱信息（装箱头和装箱明细）
	 * @param doHeaderId
	 */
	public void phyDelDoCartonInfoByDoId(Long doHeaderId);

	/**
	 * 
	 * @param doHeader
	 */
	public void insertCartonClient(DeliveryOrderHeader doHeader);
	
	/**
	 * 根据波次号查询箱号
	 * @param waveNum
	 * @return
	 */
	public String findCartonNoByWaveNum(String waveNum);
	
	/**
	 * 根据波次号查询箱Id
	 * @param waveNum
	 * @return
	 */
	public List<Long> findCartonIdByWaveNum(String waveNum);
	
	/**
	 * 根据doId list 查询箱Id
	 * @param doIds
	 * @return
	 */
	public List<Long> findCartonIdByDoId(List<Long> doIds);
	
	/**
	 * 根据波次号查询箱数
	 * @param waveNum
	 * @return
	 */
	public Long getCartonCountByWaveNum(String waveNum);
	
	/**
	 * 根据促销单品波次号获取箱信息及他的do信息
	 * @param waveNum
	 * @return
	 */
	public List<BatchGroupDoCartonInfoDTO> findCartonAndDoInfoByWave(String waveNum, BatchGroupDoCartonFilter doCartonFilter);
	/**
	 * 根据促销单品波次号获取do信息及箱统计信息
	 * @param waveNum
	 * @return
	 */
	public List<BatchGroupDoCartonInfoDTO> findDoAndCartonInfoByWave(String waveNum, BatchGroupDoCartonFilter doCartonFilter);
	
	/**
	 * 查询供自动交接的箱子。
	 * @return
	 */
	public List<CartonHeader> findCartons4AutoLoad(Long warehouseId, Integer maxNum);
	
	/**
	 * 获取波次标签打印状态
	 * 
	 * @param waveId
	 * @return null标示没有找到数据
	 */
	public PrintStatus getCartonPrintStatusInWave(Long waveId);
	
	/**
	 * 设置箱标签的打印状态
	 * @param cartonIds
	 */
	public void setCartonPrinted(List<Long> cartonIds);
	
	/**
	 * 判断是否所有的箱子都未打印箱标签
	 * @param ids
	 * @param byType 统计方式：按DO或按CARTON
	 * @return
	 */
	public Boolean isAllCartonNotPrinted(List<Long> ids, String byType);
	
	/**
	 * 根据箱id查询释放订单对应的箱子
	 * @param ids
	 * @return
	 */
	public List<CartonHeader> findRLCartonsByCartonIds(List<Long> ids);
	
	/**
	 * 根据ids查询箱子
	 * @param ids
	 * @return
	 */
	public List<CartonHeader> findCartonsByIds(List<Long> ids);
	
	public List<CartonHeader> findByDoId(Long doHeaderId);

	/**
	 * 更新运单号
	 * @param cartonNum
	 * @param wayBill
	 * @return
	 */
	public int updateCartonWayBill(String cartonNum, String wayBill);
	
	/**
	 * 
	 * @param cartonHeader
	 */
	public void saveOrUpdate(CartonHeader cartonHeader);

	/**
	 * 仓库发货时，查询遗漏的箱子
	 * @param loadHeaderId
	 * @return
	 */
	public String findUnLoadCartonsByLoadHeaderId(Long loadHeaderId);

	/**
	 * 绑定3PL运单号时，查询箱信息
	 * @param cartonNo
	 * @return
	 */
	public CartonHeader findCartonInfo4Binding(String cartonNo);

	/**
	 * 查询订单的物流单号
	 * @param doHeaderId
	 * @return
	 */
	public String getWayBillByDoId(DeliveryOrderHeader doHeader);

    String getWayBillByDoId(Long doHeaderId);

    public DataPage<CartonHeader> queryDataPage(CartonHeaderFilter filter, int startIndex, int pageSize);

	//加载需要计算运费的箱子
	List<CartonHeader> loadCartonToCalculateFreght();

	/**
	 * 合单发货查询箱信息
	 * @param doNo
     * @return
     */
	CombineBindingInfo findCartonInfo4Combine(String doNo);

	void combineBind(String doNo, String waybills);

	void updateCartonFreight(Long id, BigDecimal freight);

	List<CartonHeader> findByWaybill(String waybill);

	List<CartonHeader> findByWaybillForYouBei(String waybill);

	/**
	 * 同步重量到菜鸟
     * @param ch
     * @param weight
     */
	void syncWeightToCainiao(CartonHeader ch, BigDecimal weight);

    BigDecimal sumSkuUnit(Long id);

	public List<CartonHeader> findCartonByLpnNo(String lpnNo);

	public int clearLpnNoByLpnNo(String lpnNo);

	List<CartonDetail> getCartonDetailByHeaderId(Long cartonId);

	List<Long> findIdList4PrintByOrderIds(List<Long> ids,Long carrierId);

	List<Long> findCarrierIdList4PrintByOrderIds(List<Long> ids);

	List<Long> findCartonIdByDoId(List<Long> doIds, String packageType);

    SXSSFWorkbook createTplShippingInfoWorkBook(CartonHeaderFilter cartonHeaderFilter, Integer count);

    Integer findCount4Export(CartonHeaderFilter cartonHeaderFilter);

    void createTplShippingInfoExcel() throws IOException;

	String findChuteByCartonNo(String cartonNo);

	/**
	 * 汇总波次订单数
	 * @param goodPass
	 * @param weightFlag
	 * @param doIds 出库单id集合
	 * @return
	 */
	Long countByDoIds(Integer  goodPass, Integer weightFlag, List<Long> doIds);

	/**
	 * 根据箱id 查询 doheader 信息
	 * @param id 箱 id
	 * @return doheader 信息
	 */
	DeliveryOrderHeader getDoHeaderById(Long id);

	/**
	 * 处理交接异常数据
	 * @param id
	 */
	void autoFixShip(Long id );
}
