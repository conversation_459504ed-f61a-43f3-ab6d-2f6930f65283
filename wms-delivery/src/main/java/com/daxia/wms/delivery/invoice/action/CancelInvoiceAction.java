package com.daxia.wms.delivery.invoice.action;

import java.util.Iterator;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Logger;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.log.Log;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.wms.delivery.invoice.entity.InvoiceBook;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;
import com.daxia.wms.delivery.invoice.filter.InvoiceNoFilter;
import com.daxia.wms.delivery.invoice.service.CancelInvoiceService;
import com.daxia.wms.exp.service.ExpFacadeService;

/**
 * 发票号管理-发票号作废Action, invoiceManage.xhtml页面使用
 */
@Name("com.daxia.wms.delivery.cancelInvoiceAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class CancelInvoiceAction extends PagedListBean<InvoiceNo>{
	private static final long serialVersionUID = -4481597425325889332L;
	private InvoiceNo invoiceNo;
	private InvoiceBook invoiceBook;

	private InvoiceNoFilter invoiceNoFilter;
    private Long invoiceNoId;   // 发票号ID
    @In
    private CancelInvoiceService cancelInvoiceService;
    @In
    private ExpFacadeService expFacadeService;
    


    public CancelInvoiceAction() {
        super();
        this.invoiceNoFilter = new InvoiceNoFilter();
        this.invoiceNo = new InvoiceNo();
        this.invoiceBook = new InvoiceBook();

    }

    /**
     * @see com.daxia.framework.common.action.PagedListBean#query()
     */
    @Override
    public void query() {
    	invoiceNoFilter.getOrderByMap().put("invoiceNo", "asc");
        this.buildOrderFilterMap(invoiceNoFilter);
        DataPage<InvoiceNo> dp = cancelInvoiceService.findInvoiceNoByFilter(invoiceNoFilter, getStartIndex(), getPageSize());
        this.populateValues(dp);
    }
    
    /**
     * 单个作废发票
     */
    
    public void cancelSingleInvoice() {
    	cancelInvoiceService.cancelSingleInvoice(invoiceNoId);
    	selectedMap.clear();
    	this.query();
    	this.sayMessage(MESSAGE_SUCCESS);
    }
    
    /**
     * 批量作废发票
     */
    
    public void cancelBatchInvoice() {
    	Iterator<Object> it = selectedMap.keySet().iterator();
    	Long invoiceId = null;
    	//如果有遍历中的发票有问题，之前的作废的发票允许事物提交。提示有问题的发票号信息。
    	while (it.hasNext()) {
    		invoiceId = (Long)it.next();
    		if (selectedMap.get(invoiceId)) {
    			cancelInvoiceService.cancelBatchInvoice(invoiceId);
    		}
    	}
    	selectedMap.clear();
    	this.query();
    	this.sayMessage(MESSAGE_SUCCESS);
    }

    /**
     * 批量删除发票
     */
    
    public void deleteInvoice() {
    	Iterator<Object> it = selectedMap.keySet().iterator();
    	Long invoiceId = null;
    	while (it.hasNext()) {
    		invoiceId = (Long)it.next();
    		if (selectedMap.get(invoiceId)) {
    			int rowNum = ListUtil.getPosFromListIdValue(dataPage.getDataList(), invoiceId, "id");
    			invoiceNo = dataPage.getDataList().remove(rowNum);	
    			cancelInvoiceService.remove(invoiceId);
    		}
    	}
    	selectedMap.clear();
    	this.query();
    	this.sayMessage(MESSAGE_SUCCESS);
    }
	/**
	 * 进入新增
	 */
	public void preAdd() {
		this.operation = OPERATION_ADD;
	//	this.invoiceNoFilter = new InvoiceNoFilter();
        invoiceNo = new InvoiceNo();
        InvoiceBook invoiceBook = new InvoiceBook();
        InvoiceHeader invoiceHeader = new InvoiceHeader();
        invoiceNo.setInvoiceBook(invoiceBook);
        invoiceNo.setInvoiceHeader(invoiceHeader);
	}
	
	/**
	 * 保存
	 */
	public void add() {
        this.setOperationStatus(INIT_OPERATION_ROW);
        cancelInvoiceService.save(invoiceBook);
        dataPage.getDataList().add(invoiceNo);
        this.setOperationStatus(1);
        this.sayMessage(MESSAGE_SUCCESS);
    }
    //**********************************************  get and set   start  ********************
	

	public Long getInvoiceNoId() {
		return invoiceNoId;
	}

	public InvoiceNoFilter getInvoiceNoFilter() {
		return invoiceNoFilter;
	}

	public void setInvoiceNoFilter(InvoiceNoFilter invoiceNoFilter) {
		this.invoiceNoFilter = invoiceNoFilter;
	}

	public void setInvoiceNoId(Long invoiceNoId) {
		this.invoiceNoId = invoiceNoId;
	}

	public InvoiceNo getInvoiceNo() {
		return invoiceNo;
	}

	public void setInvoiceNo(InvoiceNo invoiceNo) {
		this.invoiceNo = invoiceNo;
	}

	public InvoiceBook getInvoiceBook() {
		return invoiceBook;
	}

	public void setInvoiceBook(InvoiceBook invoiceBook) {
		this.invoiceBook = invoiceBook;
	} 
	
	
}
