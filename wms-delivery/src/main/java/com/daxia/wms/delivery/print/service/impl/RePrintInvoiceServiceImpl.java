package com.daxia.wms.delivery.print.service.impl;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.PrintInvoiceDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.invoice.dao.InvoiceDao;
import com.daxia.wms.delivery.invoice.entity.InvoiceDetail;
import com.daxia.wms.delivery.invoice.entity.InvoiceHeader;
import com.daxia.wms.delivery.invoice.service.InvoiceService;
import com.daxia.wms.delivery.print.dto.DoInvoicePrintDTO;
import com.daxia.wms.delivery.print.dto.InvoiceHeaderDTO;
import com.daxia.wms.delivery.print.dto.RowDTO;
import com.daxia.wms.delivery.print.service.RePrintInvoiceService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.Constants;
import com.daxia.wms.print.PrintConstants.PrintInvoicePos;
import com.daxia.wms.print.PrintConstants.PrintType;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.print.service.impl.AbstractLodopPrint;
import com.daxia.wms.print.utils.PrintHelper;

@Name("com.daxia.wms.delivery.rePrintInvoiceService")
@lombok.extern.slf4j.Slf4j
public class RePrintInvoiceServiceImpl extends AbstractLodopPrint<InvoiceHeader> implements
		RePrintInvoiceService {
	@In
	private DeliveryOrderService deliveryOrderService;
	@In
	private InvoiceService invoiceService;
	@In
	private PrintInvoiceDAO printInvoiceDAO;
	@In
	private WaveService waveService;
	@In
	private InvoiceDao invoiceDao;
	
	@Override
	public PrintData rePrintInvocieByWaveNo(String waveNo, String printerCode) {
		WaveHeader waveHeader = waveService.queryWaveByNo(waveNo);
		if(null == waveHeader){ 
			throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
		}
		if(waveHeader.getInvoiceFlag() != 1 || waveHeader.getInvoicePrintFlag() != 1){
			throw new DeliveryException(DeliveryException.ERROR_PRINT_STATUS);
		}
		List<Long> invoiceHIds = invoiceDao.getInvoiceHeaderIdByWaveId(waveHeader.getId());
		
		this.paramMap.put("printerCode", printerCode == null ? "Printer001" : printerCode);
		this.paramMap.put(this.PRINT_NOTES, PrintInvoicePos.SORT_E.getValue() + "_lodop");
		this.paramMap.put(this.PRINT_TYPE, PrintType.INVOICE);
		
		PrintData printData = this.buildPrintDtos(invoiceHIds);
		return printData;
	}

	@Override
	public PrintData rePrintInvocieByDoNo(String doNo, String printerCode) {
		DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(doNo);
		if(null == doHeader){
			throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
		}
		Boolean flag = true;
		List<Long> invoiceHIds = new ArrayList<Long>();
		List<InvoiceHeader> invoiceHeaders = doHeader.getInvoiceHeaders();
		if(ListUtil.isNotEmpty(invoiceHeaders)){
			for(InvoiceHeader invoice : invoiceHeaders){
				if(Constants.YesNo.NO.getValue().equals(invoice.getInvoicePrintFlag())){
					flag = false;
					break;
				}
				invoiceHIds.add(invoice.getId());
			}
		}else{
			flag = false;
		}
		
		if (doHeader.getInvoiceFlag() != 1 || !flag) {
    		throw new DeliveryException(DeliveryException.ERROR_PRINT_STATUS);
    	}
		
		this.paramMap.put("printerCode", printerCode == null ? "Printer001" : printerCode);
		this.paramMap.put(this.PRINT_NOTES, PrintInvoicePos.SORT_E.getValue() + "_lodop");
		this.paramMap.put(this.PRINT_TYPE, PrintType.INVOICE);
		
		PrintData printData = this.buildPrintDtos(invoiceHIds);
		return printData;
	}

	@Override
	protected List<InvoiceHeader> getSourceList(List<Long> ids) {
		List<InvoiceHeader> invoiceList = invoiceService.getInvoiceList4Print(ids);
		if (invoiceList.isEmpty()) {
			throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
		}
		return invoiceList;
	}

	@Override
	protected List<PrintReportDto> buildPrintDataDto(InvoiceHeader invoice) {
		String waveNo = "";
		List<PrintReportDto> invoiceHeaderDTOList = new ArrayList<PrintReportDto>();
		DoInvoicePrintDTO invoicePrintInfo = printInvoiceDAO.getInvoicePrintInfoOfDo(invoice.getDoHeaderId());
		if(StringUtil.isEmpty(waveNo)){
			waveNo = printInvoiceDAO.getWaveNoByDoId(invoice.getDoHeaderId());
		}
		List<RowDTO> rows = new ArrayList<RowDTO>();
		int index = 0;
		List<InvoiceDetail> details = invoiceService.findInvoiceDetailsByHeaderId(invoice.getId());
		for(InvoiceDetail  detail : details){
			if(StringUtil.isEmpty(detail.getSkuDescr())){//当发票明细的商品名称描述为空时，放弃该条明细
				continue;
			}
			rows.add(buildInvoiceDetailDataInNewWay(detail, index));
			index = index + 1;
		}
		InvoiceHeaderDTO invoiceHeaderDTO = new InvoiceHeaderDTO();
		invoiceHeaderDTO.setInvoiceNo(invoice.getInvoiceNumber());
		invoiceHeaderDTO.setSoCode(invoice.getSoCode());
		invoiceHeaderDTO.setDoNo(invoicePrintInfo.getDoNo());
		invoiceHeaderDTO.setTotal(new DecimalFormat("###0.00")
				.format(invoice.getInvoiceAmount()));
		invoiceHeaderDTO.setTotalRmb(StringUtil.numToRMBStr(invoice
				.getInvoiceAmount()));
		invoiceHeaderDTO.setReceiver(PrintHelper.getNotNullOperateUser());
		invoiceHeaderDTO.setMachineNo((String)this.paramMap.get("printerCode"));
		invoiceHeaderDTO.setReceiveCom(invoice.getBranchName());
		invoiceHeaderDTO.setRegisterNum(invoice.getTaxNo());
		invoiceHeaderDTO.setDate(DateUtil.dateToString(
				DateUtil.getTodayDate(), "yyyy-MM-dd"));
		invoiceHeaderDTO.setPayer(invoice.getInvoiceTitle());
		invoiceHeaderDTO.setSortGridNo(invoicePrintInfo.getSortGridNo());
		invoiceHeaderDTO.setWaveNo(waveNo);
		invoiceHeaderDTO.setRowDtoList(rows);
		invoiceHeaderDTO.setDocId(invoice.getId());
		invoiceHeaderDTO.setDocNo(invoice.getInvoiceNumber());
		invoiceHeaderDTO.setRefNo(invoiceHeaderDTO.getDoNo());
		invoiceHeaderDTO.setOutRefNo(invoice.getSoCode());
		invoiceHeaderDTOList.add(invoiceHeaderDTO);
		return invoiceHeaderDTOList;
	}
	
	
	private RowDTO buildInvoiceDetailDataInNewWay(InvoiceDetail detail, int index) {
		RowDTO rowDTO = new RowDTO();
		rowDTO.setRow1(detail.getSkuDescr().length() > 26 ? detail
				.getSkuDescr().substring(0, 26) : detail.getSkuDescr());
		rowDTO.setTopOffset((49.5 + index * 2 * 3.5) + "mm");
		if (detail.getSkuDescr().length() >= 26) {
			rowDTO.setRow2(detail.getSkuDescr().substring(26));
		} else {
			rowDTO.setRow2("");
		}

		if (rowDTO.getRow2().length() > 8) {
			rowDTO.setRow2(rowDTO.getRow2().substring(0, 8));
		} else if (rowDTO.getRow2().length() < 8) {
			rowDTO.setRow2(rowDTO.getRow2());
		}
		DecimalFormat df = new DecimalFormat("###0.00");
		rowDTO.setUnitPrice(df.format(detail.getPrice() == null ? BigDecimal.ZERO
				: detail.getPrice()));
		rowDTO.setQty(String.valueOf(detail.getQty() == null ? 0 : detail
				.getQty()));
		rowDTO.setTotalPrice(df.format(detail.getAmount() == null ? BigDecimal.ZERO
				: detail.getAmount()));
		rowDTO.setTopOffset((52.5 + index * 2 * 3.5) + "mm");
		return rowDTO;
	}

}
