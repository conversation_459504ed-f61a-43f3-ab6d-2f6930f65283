package com.daxia.wms.delivery.recheck.action;

import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.cfg.AutoLoadAndDeliverCfg;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.service.UserAccountService;
import com.daxia.wms.Constants;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.pick.service.PickHeaderService;
import com.daxia.wms.delivery.print.service.PrintGroupWaveLabelService;
import com.daxia.wms.delivery.recheck.service.ReCheckService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.master.entity.Sku;
import com.daxia.wms.print.dto.PrintData;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.security.Restrict;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 对核拣装箱进行操作
 */
@Name("com.daxia.wms.delivery.recheckFor2BAction")
@Restrict("#{identity.hasPermission('delivery.recheckFor2B')}")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class RecheckFor2BAction implements Serializable {

    private static final long serialVersionUID = -5692990914975736264L;

    private String docNo;

    private String skuQty;

    private List<PickTask> pickTasks;

    private BigDecimal sumQtyPick;
    private BigDecimal sumQtyPickUnit;

    private Integer isSpecialCheck = 0;

    private String printDatas = "[]";
    private String printContent = "";

    private String loginName;
    private String password;
    private String recheckBy2;

    @In
    ReCheckService reCheckService;
    @In
    private UserAccountService userAccountService;
    @In
    DeliveryOrderService deliveryOrderService;
    @In
    PrintGroupWaveLabelService printGroupWaveLabelService;
    @In
    PickHeaderService pickHeaderService;
    @In
    WaveService waveService;
    @In
    ContainerMgntService containerMgntService;

    public void loadDetail() {
        if (StringUtil.isEmpty(docNo)) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }

        WaveHeader waveHeader = waveService.getWaveHeaderByWaveNum(docNo);
        if (waveHeader == null) {
            throw new DeliveryException(DeliveryException.WAVE_NO_ERRER);
        }

        //订单数过多
        List<DeliveryOrderHeader> deliveryOrderHeaders = waveHeader.getDoHeaders();
//        if (deliveryOrderHeaders.size() > 1) {
//            throw new DeliveryException(DeliveryException.WAVE_HAS_MORE_DO);
//        }

        DeliveryOrderHeader deliveryOrderHeader = deliveryOrderHeaders.get(0);
        for (DeliveryOrderHeader doHeader : deliveryOrderHeaders) {
            if (Constants.DoType.WHOLESALE.getValue().equals(doHeader.getDoType())&& Constants.DoStatus.ALLPICKED.getValue().equals(doHeader.getStatus())) {
                //如果是批发订单并且租户相同,可以捡货完成之后就做核减
                if (null == doHeader.getBusinessCustomerId() || null == deliveryOrderHeader.getBusinessCustomerId()) {
                    throw new DeliveryException(DeliveryException.RECHECK_DO_STATUS_ERROR);
                }
                if (!doHeader.getBusinessCustomerId().equals(deliveryOrderHeader.getBusinessCustomerId())) {
                    throw new DeliveryException(DeliveryException.RECHECK_DO_STATUS_ERROR);
                }
            } else if (StringUtil.isNotIn(doHeader.getStatus(), Constants.DoStatus.PART_CARTON.getValue(), Constants.DoStatus.ALLSORTED.getValue())) {
                // 订单不处于可以核拣状态
                throw new DeliveryException(DeliveryException.RECHECK_DO_STATUS_ERROR);
            }
        }


        if (Constants.ReleaseStatus.HOLD.getValue().equals(deliveryOrderHeader.getReleaseStatus())) {
            throw new DeliveryException(DeliveryException.DO_ALREADY_FROZEN);
        }

        List<PickHeader> pktHeaders = waveHeader.getPktHeaders();
        sumQtyPickUnit = BigDecimal.ZERO;
        sumQtyPick = BigDecimal.ZERO;
        int isSpecialCheckCount = 0;
        pickTasks = Lists.newArrayList();
        for (PickHeader pickHeader : pktHeaders) {
            for (PickTask pickTask : pickHeader.getPickTasks()) {
                if (PickHeader.PKT_TYPE_PCS.equals(pickHeader.getPktType())) {
                    sumQtyPick = sumQtyPick.add(pickTask.getPickedQty());
                } else {
                    sumQtyPickUnit = sumQtyPickUnit.add(pickTask.getQtyPickedUnit());
                }
                Sku sku = pickTask.getSku();
                if (null != sku && Constants.YesNo.YES.getValue().equals(sku.getIsSpecialCheck())) {
                    isSpecialCheckCount++;
                }
            }
            pickTasks.addAll(pickHeader.getPickTasks());
        }

        // ListUtil.megareList(pickTasks, new ListUtil.ListMegareOpr<PickTask>() {
        //     @Override
        //     public boolean isNeedMegare(PickTask t1, PickTask t2) {
        //         return t1.getSkuId().equals(t2.getSkuId()) && t1.getLotId().equals(t2.getLotId());
        //     }
        //
        //     @Override
        //     public void megareOpr(PickTask t1, PickTask t2) {
        //         t1.setQtyPickedUnit(t1.getQtyPickedUnit().add(t2.getQtyPickedUnit()));
        //     }
        // });

        if (isSpecialCheckCount > 0) {
            isSpecialCheck = 1;
        }
        //释放容器
        releaseContainer(waveHeader);
    }

    // 释放拣货容器（波次下只有一个DO时，会跳过分拣，在核拣输入DO号时释放拣货容器）
    private void releaseContainer(WaveHeader waveHeader) {
        containerMgntService.releaseContainerByWave(waveHeader, Constants.ContainerType.WAVE_CONTAINER.getValue(),
                Constants.BindDocType.WAVE.getValue());
    }

    public void validateUser() {
        this.recheckBy2 = userAccountService.verifyPassword(this.loginName, this.password).getLoginName();
    }

    public void pack() {
        printDatas = "[]";
        printContent = "";

        if (StringUtil.isEmpty(docNo)) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }

        WaveHeader waveHeader = waveService.getWaveHeaderByWaveNum(docNo);
        if (waveHeader == null) {
            throw new DeliveryException(DeliveryException.WAVE_NO_ERRER);
        }

        if (StringUtil.isEmpty(skuQty)) {
            throw new DeliveryException(DeliveryException.RECHECK_SKU_COUNT_LH_ZERO);
        }
        String autoNode = Config.getFmJson(Keys.Delivery.auto_load_and_deliver_cfg, Config.ConfigLevel.WAREHOUSE, AutoLoadAndDeliverCfg.type);
        Boolean needAutoDelivery = StringUtil.equals(autoNode, Constants.AutoLoadAndDeliverNode.PACK.getValue());
        List<Long> cartonHeaderIds = reCheckService.recheckByWave(waveHeader.getId(), skuQty, needAutoDelivery, recheckBy2);

       if(SystemConfig.configIsOpen("print.waybill.wholesale.autoPrint", ParamUtil.getCurrentWarehouseId())){
            List<PrintData> dataList = printGroupWaveLabelService.printCartons(cartonHeaderIds);
            printDatas = new Gson().toJson(dataList);
            printContent = getPrintJs(dataList);
        }
        clearData();
    }

    private String getPrintJs(List<PrintData> dataList) {
        Map<String, String> templateMap = new HashMap<String, String>();
        String templateJs = "";
        for (PrintData printData : dataList) {
            if (!templateMap.containsKey(printData.getPrintCfg().getLodopTemplate())) {
                templateJs = templateJs + printData.getTemplateJs();
                templateMap.put(printData.getPrintCfg().getLodopTemplate(), templateJs + printData.getTemplateJs());
            }
        }
        return templateJs;
    }

    private void clearData() {
        this.recheckBy2 = "";
        this.isSpecialCheck = 0;
    }

    public BigDecimal getSumQtyPick() {
        return sumQtyPick;
    }

    public void setSumQtyPick(BigDecimal sumQtyPick) {
        this.sumQtyPick = sumQtyPick;
    }

    public List<PickTask> getPickTasks() {
        return pickTasks;
    }

    public void setPickTasks(List<PickTask> pickTasks) {
        this.pickTasks = pickTasks;
    }

    public BigDecimal getSumQtyPickUnit() {
        return sumQtyPickUnit;
    }

    public void setSumQtyPickUnit(BigDecimal sumQtyPickUnit) {
        this.sumQtyPickUnit = sumQtyPickUnit;
    }

    public String getDocNo() {
        return docNo;
    }

    public void setDocNo(String docNo) {
        this.docNo = docNo;
    }

    public String getSkuQty() {
        return skuQty;
    }

    public void setSkuQty(String skuQty) {
        this.skuQty = skuQty;
    }

    public String getPrintDatas() {
        return printDatas;
    }

    public void setPrintDatas(String printDatas) {
        this.printDatas = printDatas;
    }

    public String getPrintContent() {
        return printContent;
    }

    public void setPrintContent(String printContent) {
        this.printContent = printContent;
    }

    public Integer getIsSpecialCheck() {
        return isSpecialCheck;
    }

    public void setIsSpecialCheck(Integer isSpecialCheck) {
        this.isSpecialCheck = isSpecialCheck;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRecheckBy2() {
        return recheckBy2;
    }

    public void setRecheckBy2(String recheckBy2) {
        this.recheckBy2 = recheckBy2;
    }
}
