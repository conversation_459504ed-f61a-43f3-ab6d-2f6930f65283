package com.daxia.wms.delivery.print.dto;

import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@lombok.extern.slf4j.Slf4j
public class PrintDoDTO implements Serializable {

    private Long doId;
    private Long doCount;

    private String doNo;

    private String doType;

    //条码类型
    private String doNoCodeType;

    private String waveNo;

    // 分拣格号
    private String sortGridNo;

    //打印人
    private String printer;

    //订购日期
    private Date doCreateTime;

    //收货方
    private String consigneeName;

    //平台订单号
    private String originalSoCode;

    private String carrierShortName;

    private String whNickName;

    private String shopNickName;

    //发票数量
    private Integer invoiceQty;

    private String invoiceType;
    // 发票网址
    private String invoiceUrl;

    //订单总额
    private BigDecimal orderAmount;
    //货品总额
    private BigDecimal productAmount;
    // 已收款
    private BigDecimal amountPayable;
    //运费
    private BigDecimal orderDeliveryFee;
    //应收款
    private BigDecimal receivable;
    //优惠金额
    private BigDecimal disCountAmount;

    //发货数量
    private BigDecimal expectedQty;

    private String buyerRemark;
    private String sellerRemark;

    private String notes;

    //是否处方药
    private Boolean isCfy;

    private BigDecimal deliveryServiceFee;

    List<PrintDoDetailDTO> detailDtos;

    private String customerName;

    private String merchantName;
    //地址
    private String address;

    private String province;

    private String city;

    private String county;

    private String udf1;

    private String waybill;

    private String mobile;

    private String telephone;

    private Date shipTime;

    //发货数量
    private BigDecimal qty;
    //发货数量
    private BigDecimal totalAmount;

    private String udfPrint;

    private JSONObject object;


    /**
     * 图片存放路径
     */
    private String basePrintPath;

    /**
     * 是否自提
     */
    private String isPickUp;

    private String orderSubType;

    /**
     * 是否代购
     */
    private String isPurchasingAgent;

    private Integer indexPage;

    private Integer totalPage;


    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 店铺编码
     */
    private String storeCode;

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public Integer getIndexPage() {
        return indexPage;
    }

    public void setIndexPage(Integer indexPage) {
        this.indexPage = indexPage;
    }

    public Integer getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(Integer totalPage) {
        this.totalPage = totalPage;
    }

    public String getShopNickName() {
        return shopNickName;
    }

    public void setShopNickName(String shopNickName) {
        this.shopNickName = shopNickName;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    public Boolean getCfy() {
        return isCfy;
    }

    public void setCfy(Boolean cfy) {
        isCfy = cfy;
    }

    public BigDecimal getDisCountAmount() {
        return disCountAmount;
    }

    public void setDisCountAmount(BigDecimal disCountAmount) {
        this.disCountAmount = disCountAmount;
    }

    public String getBuyerRemark() {
        return buyerRemark;
    }

    public void setBuyerRemark(String buyerRemark) {
        this.buyerRemark = buyerRemark;
    }

    public String getSellerRemark() {
        return sellerRemark;
    }

    public void setSellerRemark(String sellerRemark) {
        this.sellerRemark = sellerRemark;
    }

    public BigDecimal getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(BigDecimal expectedQty) {
        this.expectedQty = expectedQty;
    }

    public Date getDoCreateTime() {
        return doCreateTime;
    }

    public void setDoCreateTime(Date doCreateTime) {
        this.doCreateTime = doCreateTime;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public Integer getInvoiceQty() {
        return invoiceQty;
    }

    public void setInvoiceQty(Integer invoiceQty) {
        this.invoiceQty = invoiceQty;
    }

    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getProductAmount() {
        return productAmount;
    }

    public void setProductAmount(BigDecimal productAmount) {
        this.productAmount = productAmount;
    }

    public BigDecimal getAmountPayable() {
        return amountPayable;
    }

    public void setAmountPayable(BigDecimal amountPayable) {
        this.amountPayable = amountPayable;
    }

    public BigDecimal getOrderDeliveryFee() {
        return orderDeliveryFee;
    }

    public void setOrderDeliveryFee(BigDecimal orderDeliveryFee) {
        this.orderDeliveryFee = orderDeliveryFee;
    }

    public BigDecimal getReceivable() {
        return receivable;
    }

    public void setReceivable(BigDecimal receivable) {
        this.receivable = receivable;
    }

    public String getPrinter() {
        return printer;
    }

    public void setPrinter(String printer) {
        this.printer = printer;
    }

    public String getOriginalSoCode() {
        return originalSoCode;
    }

    public void setOriginalSoCode(String originalSoCode) {
        this.originalSoCode = originalSoCode;
    }

    public String getCarrierShortName() {
        return carrierShortName;
    }

    public void setCarrierShortName(String carrierShortName) {
        this.carrierShortName = carrierShortName;
    }

    public String getWhNickName() {
        return whNickName;
    }

    public void setWhNickName(String whNickName) {
        this.whNickName = whNickName;
    }

    public String getInvoiceUrl() {
        return invoiceUrl;
    }

    public void setInvoiceUrl(String invoiceUrl) {
        this.invoiceUrl = invoiceUrl;
    }

    public List<PrintDoDetailDTO> getDetailDtos() {
        return detailDtos;
    }

    public void setDetailDtos(List<PrintDoDetailDTO> detailDtos) {
        this.detailDtos = detailDtos;
    }

    public Long getDoId() {
        return doId;
    }

    public void setDoId(Long doId) {
        this.doId = doId;
    }

    public String getDoNo() {
        return doNo;
    }

    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }

    public String getDoType() {
        return doType;
    }

    public void setDoType(String doType) {
        this.doType = doType;
    }

    public String getWaveNo() {
        return waveNo;
    }

    public void setWaveNo(String waveNo) {
        this.waveNo = waveNo;
    }

    public String getDoNoCodeType() {
        return doNoCodeType;
    }

    public void setDoNoCodeType(String doNoCodeType) {
        this.doNoCodeType = doNoCodeType;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getDeliveryServiceFee() {
        return deliveryServiceFee;
    }

    public void setDeliveryServiceFee(BigDecimal deliveryServiceFee) {
        this.deliveryServiceFee = deliveryServiceFee;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public Date getShipTime() {
        return shipTime;
    }

    public void setShipTime(Date shipTime) {
        this.shipTime = shipTime;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getUdf1() {
        return udf1;
    }

    public void setUdf1(String udf1) {
        this.udf1 = udf1;
    }

    public String getWaybill() {
        return waybill;
    }

    public void setWaybill(String waybill) {
        this.waybill = waybill;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public JSONObject getObject() {
        return object;
    }

    public void setObject(JSONObject object) {
        this.object = object;
    }

    public String getUdfPrint() {
        return udfPrint;
    }

    public void setUdfPrint(String udfPrint) {
        this.udfPrint = udfPrint;
    }

    public String getBasePrintPath() {
        return basePrintPath;
    }

    public void setBasePrintPath(String basePrintPath) {
        this.basePrintPath = basePrintPath;
    }

    public String getIsPickUp() {
        return isPickUp;
    }

    public void setIsPickUp(String isPickUp) {
        this.isPickUp = isPickUp;
    }

    public String getOrderSubType() {
        return orderSubType;
    }

    public void setOrderSubType(String orderSubType) {
        this.orderSubType = orderSubType;
    }

    public String getIsPurchasingAgent() {
        return isPurchasingAgent;
    }

    public void setIsPurchasingAgent(String isPurchasingAgent) {
        this.isPurchasingAgent = isPurchasingAgent;
    }

    public Long getDoCount() {
        return doCount;
    }

    public void setDoCount(Long doCount) {
        this.doCount = doCount;
    }
}
