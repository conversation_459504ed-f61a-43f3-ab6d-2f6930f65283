package com.daxia.wms.delivery.print.dto;

import java.math.BigDecimal;
/**
 * 分拣单打印dto
 */
@lombok.extern.slf4j.Slf4j
public class SttPrintSub {
    /**
     * 订单明细ID
     */
    private Long doDetailId;
    /**
     * 数量
     */
    private BigDecimal qty;
    /**
     * 产品代码
     */
    private String skuCode;
    /**
     * 产品条码
     */
    private String skuBarCode;
    /**
     * 产品中文名
     */
    private String skuCname;
    /**
     * 产品英文名
     */
    private String skuEname;
    /**
     * 分拣柜号
     */
    private String sortGridNo;
    
    /**
     * 拣货顺序
     */
    private String pickSeq;
    /**
     * 拣货库位编码
     */
    private String pickLocCode;
    
    /**
     * 拣货库区编码
     */
    private String partitionCode;
    /**
     * 发货单号
     */
    private String doNo;

    public void setDoDetailId(Long doDetailId) {
        this.doDetailId = doDetailId;
    }

    public Long getDoDetailId() {
        return doDetailId;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public String getSkuBarCode() {
        return skuBarCode;
    }

    public void setSkuBarCode(String skuBarCode) {
        this.skuBarCode = skuBarCode;
    }

    public String getSkuCname() {
        return skuCname;
    }

    public void setSkuCname(String skuCname) {
        this.skuCname = skuCname;
    }

    public String getSkuEname() {
        return skuEname;
    }

    public void setSkuEname(String skuEname) {
        this.skuEname = skuEname;
    }

    public String getSortGridNo() {
        return sortGridNo;
    }

    public void setSortGridNo(String sortGridNo) {
        this.sortGridNo = sortGridNo;
    }

    public String getPickSeq() {
        return pickSeq;
    }

    public void setPickSeq(String pickSeq) {
        this.pickSeq = pickSeq;
    }

    public String getPickLocCode() {
        return pickLocCode;
    }

    public void setPickLocCode(String pickLocCode) {
        this.pickLocCode = pickLocCode;
    }
    
    public String getPartitionCode() {
        return partitionCode;
    }
    
    public void setPartitionCode(String partitionCode) {
        this.partitionCode = partitionCode;
    }

	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}
    
}