package com.daxia.wms.delivery.deliveryorder.service.impl;

import com.daxia.wms.delivery.deliveryorder.dao.DoNoticeDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DoNotice;
import com.daxia.wms.delivery.deliveryorder.service.DoNoticeService;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.util.List;

@Name("doNoticeService")
@lombok.extern.slf4j.Slf4j
public class DoNoticeServiceImpl implements DoNoticeService {

    @In
    private DoNoticeDAO doNoticeDAO;

    @Override
    @Transactional
    public void addNotice(Long doId, String holdBy, String holdDesc) {
        DoNotice doNotice = new DoNotice();
        doNotice.setCreatedBy(holdBy);
        doNotice.setNoticeContent(holdDesc);
        doNotice.setDoId(doId);
        doNoticeDAO.save(doNotice);
    }

    @Override
    @Transactional
    public void addNotice(Long doId, String holdBy, String holdDesc, String noticeNode) {
        DoNotice doNotice = new DoNotice();
        doNotice.setCreatedBy(holdBy);
        doNotice.setNoticeContent(holdDesc);
        doNotice.setDoId(doId);
        doNotice.setNoticeNode(noticeNode);
        doNoticeDAO.save(doNotice);
    }

    @Override
    public DoNotice getLast(Long doId) {
        return doNoticeDAO.getLast(doId);
    }

    @Override
    public List<DoNotice> getNotices(Long doHeaderId) {
        return doNoticeDAO.getNotices(doHeaderId);
    }

    @Override
    public void cancelNotice(Long dooId, String holdBy) {
        doNoticeDAO.cancelNotice(dooId, holdBy);
    }

    @Override
    public List<DoNotice> getNoticesById(Long doHeaderId, Long warehouseId) {
        return doNoticeDAO.getNoticesById(doHeaderId,warehouseId);
    }
}
