package com.daxia.wms.delivery.util;

import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.LocType;
import com.daxia.wms.Keys;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DoAllocateHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.master.MasterException;
import com.daxia.wms.master.entity.AllocationRuleDetail;
import com.daxia.wms.master.entity.AllocationRuleHeader;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.entity.PackageInfoDetail;
import com.daxia.wms.master.helper.SysConfigHelper;
import com.daxia.wms.stock.stock.dto.Stock2AllocateDTO;
import com.daxia.wms.stock.stock.dto.StockBatchDTO;
import com.google.common.collect.Lists;
import org.apache.commons.beanutils.BeanComparator;
import org.apache.commons.collections.ComparatorUtils;
import org.apache.commons.collections.comparators.ComparableComparator;
import org.apache.commons.collections.comparators.ComparatorChain;
import org.apache.commons.collections.comparators.FixedOrderComparator;
import org.apache.commons.collections.comparators.ReverseComparator;

import java.util.*;

import static com.daxia.wms.master.entity.AllocationRuleHeader.*;

/**
 * 分配帮助类
 */
@lombok.extern.slf4j.Slf4j
public class AllocateHelper {
	
	/**
	 * 获取订单明细的批次属性
	 * @param alcDetail
	 * @return
	 */
	public static StockBatchDTO buildBatchDTO(DoAllocateDetail alcDetail) {
        StockBatchDTO dto = new StockBatchDTO();
        dto.setSkuId(alcDetail.getSkuId());
        if(StringUtil.isNotEmpty(alcDetail.getLotatt01())){
            dto.setLotatt01(alcDetail.getLotatt01());
        }
        if(StringUtil.isNotEmpty(alcDetail.getLotatt02())){
            dto.setLotatt02(alcDetail.getLotatt02());
        }
        if(StringUtil.isNotEmpty(alcDetail.getLotatt03())){
            dto.setLotatt03(alcDetail.getLotatt03());
        }
        if(StringUtil.isNotEmpty(alcDetail.getLotatt04())){
            dto.setLotatt04(alcDetail.getLotatt04());
        }
        if(StringUtil.isNotEmpty(alcDetail.getLotatt05())){
            dto.setLotatt05(alcDetail.getLotatt05());
        }
        if(StringUtil.isNotEmpty(alcDetail.getLotatt06())){
            dto.setLotatt06(alcDetail.getLotatt06());
        }
        if(StringUtil.isNotEmpty(alcDetail.getLotatt07())){
            dto.setLotatt07(alcDetail.getLotatt07());
        }
        if(StringUtil.isNotEmpty(alcDetail.getLotatt08())){
            dto.setLotatt08(alcDetail.getLotatt08());
        }
        if(StringUtil.isNotEmpty(alcDetail.getLotatt09())){
            dto.setLotatt09(alcDetail.getLotatt09());
        }
        if(StringUtil.isNotEmpty(alcDetail.getLotatt10())){
            dto.setLotatt10(alcDetail.getLotatt10());
        }
        if(StringUtil.isNotEmpty(alcDetail.getLotatt11())){
            dto.setLotatt11(alcDetail.getLotatt11());
        }
        if(StringUtil.isNotEmpty(alcDetail.getLotatt12())){
            dto.setLotatt12(alcDetail.getLotatt12());
        }
        if(StringUtil.isNotEmpty(alcDetail.getLotatt13())){
            dto.setLotatt13(alcDetail.getLotatt13());
        }
        if(StringUtil.isNotEmpty(alcDetail.getLotatt14())){
            dto.setLotatt14(alcDetail.getLotatt14());
        }
        if(StringUtil.isNotEmpty(alcDetail.getLotatt15())){
            dto.setLotatt15(alcDetail.getLotatt15());
        }
        if(StringUtil.isNotEmpty(alcDetail.getLotatt16())){
            dto.setLotatt16(alcDetail.getLotatt16());
        }
        if(StringUtil.isNotEmpty(alcDetail.getLotatt17())){
            dto.setLotatt17(alcDetail.getLotatt17());
        }
        if(StringUtil.isNotEmpty(alcDetail.getLotNo())){
            dto.setLotNo(alcDetail.getLotNo());
        }
        dto.setIsDamaged(alcDetail.getIsDamaged());
        return dto;
    }

    public static StockBatchDTO buildBatchDTOForAllocate(DoAllocateDetail alcDetail, PackageInfoDetail packageInfo) {
        StockBatchDTO stockBatchDTO = buildBatchDTO(alcDetail);
        if (SysConfigHelper.getSwitchDefalutClosed("allocate.delivery.isMerchantShareStock")) {
            stockBatchDTO.setLotatt06(null); // 共享库存忽略商家属性；
        }
        if (alcDetail.getDoAllocateHeader().getDoType().equals(Constants.DoType.SELL.getValue())) { // 销售订单只看4、6、8属性
            stockBatchDTO.setLotatt01(null);
            stockBatchDTO.setLotatt02(null);
            stockBatchDTO.setLotatt03(null);
            //2018-6-5 2C指定批次出库
//            stockBatchDTO.setLotatt05(null);
            stockBatchDTO.setLotatt07(null);
            stockBatchDTO.setLotatt09(null);
            stockBatchDTO.setLotatt10(null);
            stockBatchDTO.setLotatt11(null);
            stockBatchDTO.setLotatt12(null);
        } else if (alcDetail.getDoAllocateHeader().getDoType().equals(Constants.DoType.RTV.getValue())
                && Config.isDefaultFalse(Keys.Delivery.allocate_rtvIngoreSupplier, Config.ConfigLevel.WAREHOUSE)) {
            stockBatchDTO.setLotatt04(null);
        }

        //按包装分配
        if (packageInfo != null) {
            stockBatchDTO.setLotatt07(packageInfo.getId().toString());
        } else {
            stockBatchDTO.setLotatt07(null);
        }
        // 良品根据效期控制筛选范围
        stockBatchDTO.setMinExp(alcDetail.getMinExp());
        stockBatchDTO.setMaxExp(alcDetail.getMaxExp());
        // 次品时 根据等级控制筛选范围
        if(Constants.YesNo.YES.getValue().equals(alcDetail.getIsDamaged())&& Objects.nonNull(alcDetail.getGoodsGrade())){
            stockBatchDTO.setLotatt14(String.valueOf(alcDetail.getGoodsGrade()));
        }
        return stockBatchDTO;
    }
	
	/**
	 * 构造订单的可分配库存类型
	 * @param alcDetail
	 * @param allocationRule
     * @return
	 */
    public static List<String> buildLocTypes4Alloc(DoAllocateDetail alcDetail, AllocationRuleHeader allocationRule) {
        List<String> locTypes = new ArrayList<String>();
        DoAllocateHeader alcHeader = alcDetail.getDoAllocateHeader();
        String doType = alcHeader.getDoType();
        // 残次品只能在坏品区拣货
        if (isDamaged(alcDetail)) {
            locTypes.add(LocType.DM.getValue());
            return locTypes;
        } else {
            //如果是团购订单且指定了库位从定向拣货位出库
            DoWaveEx doWaveEx = alcHeader.getDoWaveEx();
            if (doWaveEx != null && Constants.AutoWaveType.BATCH_GROUP.getValue().equals(doWaveEx.getAutoWaveType())) {
                if (doWaveEx.getGroupRule() == null) {
                    throw new DeliveryException(MasterException.GROUP_RULE_NOT_EXIST, doWaveEx.getWaveCriteriaExId());
                }
                locTypes.add(LocType.DEA.getValue());
                if (doWaveEx.getGroupRule().getDeaOnly() != null && doWaveEx.getGroupRule().getDeaOnly()) {
                    return locTypes;
                }
            }
            locTypes.add(LocType.EA.getValue());
            if (StringUtil.isIn(doType, Constants.DoType.ALLOT.getValue(), Constants.DoType.RTV.getValue(), Constants.DoType.WHOLESALE.getValue()) && allocationRule.getSupportRs()) {
                locTypes.add(LocType.RS.getValue());
            }
        }
        if (Constants.YesNo.YES.getValue().equals(alcDetail.getDoAllocateHeader().getIsDirect())) { //直配支持从暂存位分配库存
            locTypes.add(LocType.ST.getValue());
        }
        return locTypes;
    }
    
    /**
     * 判断是否需要出库坏品
     * @param alcDetail
     * @return
     */
	public static boolean isDamaged(DoAllocateDetail alcDetail) {
		return alcDetail.getIsDamaged() != null
				&& Constants.YesNo.YES.getValue().equals(alcDetail.getIsDamaged());
	}

    public static List<String> getPackTypes(AllocationRuleHeader allocationRule, boolean isEaPackage, boolean ingorePackageType) {
        if (isEaPackage) {
            List packTypes = Lists.newArrayList(Constants.PackageType.B.getValue());
            if (ingorePackageType) {
                packTypes.add(Constants.PackageType.C.getValue());
            }
        
            return packTypes;
        } else {
            List packTypes = null;
            if (allocationRule.getSupportRs()) {
                packTypes = Lists.newArrayList(Constants.PackageType.C.getValue(), Constants.PackageType.P.getValue());
            } else {
                packTypes = Lists.newArrayList(Constants.PackageType.C.getValue());
            }

            if (ingorePackageType) {
                packTypes.add(Constants.PackageType.B.getValue());
            }
            return packTypes;
        }
    }
    
    public static boolean needHold(DoAllocateDetail alcDetail) {
        return Constants.DoType.SELL.getValue().equals(alcDetail.getDoAllocateHeader().getDoType());
    }
    
    public static void sortStock(DoAllocateHeader doAllocateHeader, AllocationRuleHeader allocationRuleHeader, List<Stock2AllocateDTO> results) {
        List<AllocationRuleDetail> ruleDetails = allocationRuleHeader.getAllocationRuleDetails();
        ComparatorChain compChain = new ComparatorChain();
    
        //按B/C/P优先分配
        String[] packageTypes = {Constants.PackageType.B.getValue(), Constants.PackageType.C.getValue(), Constants.PackageType.P.getValue()};
        compChain.addComparator(new BeanComparator("packageType", new FixedOrderComparator(packageTypes)));
    
        //团购订单优先从DEA库位分配
        if (Constants.AutoWaveType.BATCH_GROUP.getValue().equals(doAllocateHeader.getDoWaveEx().getAutoWaveType())) {
            compChain.addComparator(new BeanComparator("locType", new Comparator() {
                @Override
                public int compare(Object o1, Object o2) {
                    if (o1.equals(o2)) {
                        return 0;
                    }
                    
                    if (o1.equals(LocType.DEA.getValue())) {
                        return -1;
                    } else if (o2.equals(LocType.DEA.getValue())) {
                        return 1;
                    }
                    return 0;
                }
            }));
        }
    
        if (Constants.YesNo.YES.getValue().equals(SystemConfig.getConfigValueInt("order.allocate.lotatt11.first", ParamUtil.getCurrentWarehouseId()))) {
            compChain.addComparator(new BeanComparator("lotAtt11", new Comparator() {
                @Override
                public int compare(Object o1, Object o2) {
                    if (o1 instanceof String && o2 instanceof String) {
                        String o11 = (String) o1;
                        String o22 = (String) o2;
    
                        if (o11.equals(o22)) {
                            return 0;
                        }
                    
                        if (StringUtil.isEmpty(o11)) {
                            return 1;
                        }
                        if (StringUtil.isEmpty(o22)) {
                            return -1;
                        }
                    }
                    return 0;
                }
            }));
        }
    
        //对于每条分配规则明细，读取其批次属性作为排序关键字
        for (AllocationRuleDetail allocationRuleDetail : ruleDetails) {
            boolean isDesc = "D".equals(allocationRuleDetail.getSortBy());
        
            String key = allocationRuleDetail.getLotattName().substring(0, 1).toLowerCase() + allocationRuleDetail.getLotattName().substring(1);
        
            //允许null, 如果是降序，null优先级高，排最前；如果是升序，null优先级低，也排最前
            Comparator<String> nullCmp = isDesc ? ComparatorUtils.nullHighComparator(ComparableComparator.getInstance()) : ComparatorUtils.nullLowComparator(ComparableComparator.getInstance());
            compChain.addComparator(isDesc ? new ReverseComparator(new BeanComparator(key, nullCmp)) : new BeanComparator(key, nullCmp));
        }

        //策略排序
        if (allocationRuleHeader.getStockStrategy() == null) {
            allocationRuleHeader.setStockStrategy(STOCK_STRATEGY_RANDOM);
        }
        switch (allocationRuleHeader.getStockStrategy()) {
            case STOCK_STRATEGY_LESS:
                compChain.addComparator(new BeanComparator("actQty"));
                break;
            case STOCK_STRATEGY_MORE:
                compChain.addComparator(new ReverseComparator(new BeanComparator("actQty")));
                break;
            case STOCK_STRATEGY_RANDOM:
                compChain.addComparator(new Comparator() {
                                           @Override
                    public int compare(Object o1, Object o2) {

                            return Math.random() > 0.5d ? 1 : -1;
                    }
                });
                break;
        }
        Collections.sort(results, compChain);
    }
    
    public static List<String> getLocCodes(DoAllocateHeader doAllocateHeader) {
        List locCodes = Lists.newArrayList();
        if (Constants.YesNo.YES.getValue().equals(doAllocateHeader.getIsDirect()) ) {
            locCodes.add(Location.LOC_DIRECT);
        }else {
            // 批量团购
            DoWaveEx doWaveEx = doAllocateHeader.getDoWaveEx();
            if (doWaveEx != null && Constants.AutoWaveType.BATCH_GROUP.getValue().equals(doWaveEx.getAutoWaveType())) {
                if (doWaveEx.getGroupRule() == null) {
                    throw new DeliveryException(MasterException.GROUP_RULE_NOT_EXIST, doWaveEx.getWaveCriteriaExId());
                }

                if (StringUtil.isNotEmpty(doWaveEx.getGroupRule().getLocCode())) {
                    locCodes.add(doWaveEx.getGroupRule().getLocCode());

                }
            }
        }
        return locCodes;
    }
}
