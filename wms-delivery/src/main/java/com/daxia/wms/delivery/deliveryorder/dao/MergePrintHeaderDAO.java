package com.daxia.wms.delivery.deliveryorder.dao;

import com.daxia.dubhe.api.internal.util.NumberUtils;
import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.dto.MergePrintDTO;
import com.daxia.wms.delivery.deliveryorder.entity.MergePrintHeader;
import com.daxia.wms.delivery.deliveryorder.filter.MergePrintFilter;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.jboss.seam.annotations.Name;

import java.math.BigInteger;
import java.util.*;

/**
 * 合单打印DAO
 */
@Name("com.daxia.wms.delivery.mergePrintHeaderDAO")
@lombok.extern.slf4j.Slf4j
public class MergePrintHeaderDAO extends HibernateBaseDAO<MergePrintHeader, Long> {

    private static final long serialVersionUID = -7097418598638672053L;

    public String builderHqlByAll(MergePrintFilter mergePrintFilter, Map<String, Object> paramMap) {
        String sql = " select dh.do_no,dh.consignee_name,dh.create_time,count(*) carton_num,c.dist_supp_comp_name carrier_name,c.id carrier_id, ";
        sql += " ph.way_bill,ph.group_no,ph.do_num,dh.id,ph.print_data,dh.status,mc.customer_name from doc_do_header dh ";
        sql += " inner join doc_carton_header ch on ch.do_header_id = dh.id and ch.is_deleted = 0 ";
        sql += " left join md_carrier c on c.id = dh.carrier_id ";
        sql += " left join doc_merge_print_detail pd on pd.do_header_id = dh.id and pd.is_deleted = 0 ";
        sql += " left join doc_merge_print_header ph on ph.id = pd.header_id and ph.is_deleted = 0 ";
        sql += " left join md_business_customer mc on mc.id = dh.business_customer_id ";
        sql += " where dh.status>='67' and dh.status<='80' and dh.need_cancel = 0 and dh.release_status='RL' ";
        sql += " and dh.is_deleted =0 and dh.do_type='6'";
        sql += " and dh.warehouse_id = :warehouseId  ";
        paramMap.put("warehouseId", ParamUtil.getCurrentWarehouseId());
        if (StringUtil.isNotBlank(mergePrintFilter.getDoNo())) {
            paramMap.put("doNo", mergePrintFilter.getDoNo());
            sql += " and dh.do_no = :doNo  ";
        }
        if (StringUtil.isNotBlank(mergePrintFilter.getConsigneeName())) {
            paramMap.put("consigneeName", mergePrintFilter.getConsigneeName());
            sql += " and dh.consignee_name = :consigneeName  ";
        }
        if (mergePrintFilter.getIsPrinted() != null) {
            if (Constants.YesNo.YES.getValue().equals(mergePrintFilter.getIsPrinted())) {
                sql += " and ph.group_no is not null  ";
            } else {
                sql += " and ph.group_no is null  ";
            }
        }
        if (CollectionUtils.isNotEmpty(mergePrintFilter.getDoIdList())) {
            paramMap.put("doIdList", mergePrintFilter.getDoIdList());
            sql += " and dh.id in ( :doIdList )  ";
        }
        if (mergePrintFilter.getCarrierId() != null) {
            paramMap.put("carrierId", mergePrintFilter.getCarrierId());
            sql += " and c.id = :carrierId ";
        }
        if (mergePrintFilter.getBusinessCustomerId() != null) {
            paramMap.put("businessCustomerId", mergePrintFilter.getBusinessCustomerId());
            sql += " and dh.business_customer_id = :businessCustomerId ";
        }
        if (mergePrintFilter.getDoCreateTimeFm() != null) {
            paramMap.put("doCreateTimeFm", mergePrintFilter.getDoCreateTimeFm());
            sql += " and dh.create_time >= :doCreateTimeFm ";
        }
        if (mergePrintFilter.getDoCreateTimeTo() != null) {
            paramMap.put("doCreateTimeTo", mergePrintFilter.getDoCreateTimeTo());
            sql += " and dh.create_time <= :doCreateTimeTo ";
        }
        if (StringUtil.isNotBlank(mergePrintFilter.getStatusFm())) {
            paramMap.put("statusFm", mergePrintFilter.getStatusFm());
            sql += " and dh.status >= :statusFm ";
        }
        if (StringUtil.isNotBlank(mergePrintFilter.getStatusTo())) {
            paramMap.put("statusTo", mergePrintFilter.getStatusTo());
            sql += " and dh.status <= :statusTo ";
        }
        if (StringUtil.isNotBlank(mergePrintFilter.getGroupNo())) {
            paramMap.put("groupNo", mergePrintFilter.getGroupNo());
            sql += " and ph.group_no = :groupNo ";
        }
        sql += " group by dh.id order by dh.business_customer_id,dh.consignee_name,dh.id desc ";
        return sql;
    }

    public DataPage<MergePrintDTO> queryData(String sql, String countSql, Map<String, Object> paramMap, int startIndex, int pageSize) {
        SQLQuery query = createSQLQuery(sql);
        SQLQuery countQuery = createSQLQuery(countSql);

        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            if (entry.getValue() instanceof List) {
                query.setParameterList(entry.getKey(), (Collection) entry.getValue());
                countQuery.setParameterList(entry.getKey(), (Collection) entry.getValue());
            } else {
                query.setParameter(entry.getKey(), entry.getValue());
                countQuery.setParameter(entry.getKey(), entry.getValue());
            }
        }
        if (pageSize > 0) {
            query.setFirstResult(startIndex);
            query.setMaxResults(pageSize);
        }
        List list = query.list();
        List<MergePrintDTO> mergePrintDTOS = new ArrayList<MergePrintDTO>();
        setDtoInfos(list, mergePrintDTOS);
        Long cnt = ((BigInteger) countQuery.uniqueResult()).longValue();
        return new DataPage<MergePrintDTO>(cnt.intValue(), startIndex, pageSize, mergePrintDTOS);
    }

    private void setDtoInfos(List objs, List<MergePrintDTO> mergePrintDTOS) {
        for (Object obj : objs) {
            Object[] o = (Object[]) obj;
            MergePrintDTO dto = new MergePrintDTO();
            dto.setDoNo((String) o[0]);
            dto.setConsigneeName((String) o[1]);
            dto.setDoCreateTime((Date) o[2]);
            dto.setCartonNum(NumberUtils.object2Integer(o[3]));
            dto.setCarrierName((String) o[4]);
            dto.setCarrierId(NumberUtils.object2Long(o[5]));
            dto.setWayBill((String) o[6]);
            dto.setIsPrinted(StringUtil.isBlank((String) o[7]) ? Constants.YesNo.NO.getValue() : Constants.YesNo.YES.getValue());
            dto.setGroupNo((String) o[7]);
            dto.setDoNum(NumberUtils.object2Integer(o[8]));
            dto.setId(((BigInteger) o[9]).longValue());
            dto.setPrintData((String) o[10]);
            dto.setStatus((String) o[11]);
            dto.setCustomerName((String) o[12]);
            mergePrintDTOS.add(dto);
        }
    }

    private String findWayBillByDoHeaderId(Long doHeaderId) {
        String hql = " select o.wayBill from MergePrintHeader o,MergePrintDetail d where o.id = d.headerId and d.doHeaderId = :doHeaderId ";
        Query query = createQuery(hql);
        query.setParameter("doHeaderId", doHeaderId);
        query.setMaxResults(1);
        return (String) query.uniqueResult();
    }
}