package com.daxia.wms.delivery.load.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.LoadStatus;
import com.daxia.wms.Constants.LoadType;
import com.daxia.wms.Constants.dealType;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.load.entity.LoadDetail;
import com.daxia.wms.delivery.load.entity.LoadHeader;
import com.google.common.collect.Lists;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * Description:交接业务DAO
 */
@Name("com.daxia.wms.delivery.loadDAO")
@lombok.extern.slf4j.Slf4j
public class LoadDAO extends HibernateBaseDAO<LoadHeader, Long> {

    private static final long serialVersionUID = 1282536479846514016L;

    /**
     * 根据doHeader更新DO明细的行状态，发货数量
     *
     * @param doHeaderId
     * @param status
     */
    public void updateDoDetailByDoHeaderId(Long doHeaderId, String status) {
        StringBuilder sql = new StringBuilder("update doc_do_detail dd set ");
        sql.append(" dd.linestatus = :status ");
        sql.append(" , dd.shipped_qty = dd.picked_qty ");
        sql.append(" where dd.do_header_id = :doHeaderId and dd.WAREHOUSE_ID = :warehouseId");

        Query query = this.createUpdateSqlQuery(sql.toString());
        query.setLong("doHeaderId", doHeaderId);
        query.setString("status", status);
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 更新DoHeader
     *
     * @param doHeader
     * @param status
     */
    public Integer updateDoHeaderById(DeliveryOrderHeader doHeader, String status) {
        StringBuilder sql = new StringBuilder("update doc_do_header dh inner join("
                + "select dd.do_header_id, sum(dd.picked_qty) sumQty from "
                + "doc_do_detail dd where dd.warehouse_id = :warehouseId"
                + "  and dd.do_header_id=:doHeaderId group by dd.do_header_id"
                + ") temp on temp.do_header_id = dh.id ");
        sql.append("set dh.status = :status, dh.ship_qty = temp.sumQty, dh.ship_time = :nowTime, dh.update_time = :nowTime, dh.update_by = :updateBy, dh.version = dh.version + 1 ");
        sql.append("where dh.id = :doHeaderId and dh.is_deleted = 0  and dh.WAREHOUSE_ID = :warehouseId");
        Query query = this.createSQLQuery(sql.toString());
        query.setLong("doHeaderId", doHeader.getId());
        query.setParameter("status", status);
        query.setParameter("updateBy", getOperateUser());
        query.setParameter("nowTime", DateUtil.getNowTime());
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.executeUpdate();
    }

    /**
     * 交接单自动交接后的DO出库
     *
     * @param doHeader 订单
     * @param shipTime WCS调用DTS执行自动发货的时间
     */
    public Integer autoDeliverDo(DeliveryOrderHeader doHeader, Date shipTime) {
        StringBuilder sql = new StringBuilder("update doc_do_header dh set");
        sql.append(" dh.status = :status, dh.ship_time = :shipTime,dh.version = dh.version+1, dh.ship_qty = (select sum(dd.picked_qty) from doc_do_detail dd  where dd.do_header_id = dh.id and dd.warehouse_id = :warehouseId) ");
        sql.append(" where dh.id = :doHeaderId  and dh.WAREHOUSE_ID = :warehouseId");

        Query query = this.createSQLQuery(sql.toString());
        query.setString("status", DoStatus.ALL_DELIVER.getValue());
        query.setParameter("shipTime", shipTime);
        query.setLong("doHeaderId", doHeader.getId());
        query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.executeUpdate();
    }

    /**
     * 查找交接中的自动交接单。
     *
     * @return
     */
    public LoadHeader findAutoLoadLoadH(Integer loadMode) {
        StringBuilder hql = new StringBuilder("");
        hql.append("from LoadHeader lh where lh.status =:status");
        hql.append(" and  lh.isAuto = :isAuto and lh.createdAt > TIMESTAMPADD(DAY,-7,CURRENT_DATE()) and lh.warehouseId = :warehouseId");

        Query query = this.createQuery(hql.toString());
        query.setParameter("status", LoadStatus.LOADING.getValue());
        query.setInteger("isAuto", loadMode);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (LoadHeader) query.uniqueResult();
    }

    /**
     * @return
     */
    public void updateLoadHeaderForAuto(Long id, BigDecimal totalGrossWeight, BigDecimal totalNetWeight, BigDecimal totalVolume, Long cartonNum) {
        StringBuilder hql = new StringBuilder("");
        hql.append(" update LoadHeader o set o.cartonQty = o.cartonQty + :cartonNum, ");
        hql.append("    o.totalGrossWeight = o.totalGrossWeight + :totalGrossWeight,");
        hql.append("    o.totalNetWeight = o.totalNetWeight + :totalNetWeight,");
        hql.append("    o.totalVolume = o.totalVolume + :totalVolume, ");
        hql.append("    o.status = :status ");
        hql.append(" where o.id = :id and o.warehouseId = :warehouseId");
        Query query = this.createUpdateQuery(hql.toString());
        query.setParameter("id", id);
        query.setParameter("cartonNum", cartonNum);
        query.setParameter("totalGrossWeight", totalGrossWeight);
        query.setParameter("totalNetWeight", totalNetWeight);
        query.setParameter("totalVolume", totalVolume);
        query.setParameter("status", LoadStatus.LOADING.getValue());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 检查并完成自动生成的交接单
     *
     * @return
     */
    public void updateLoadHeaderForAutoClose(Long id, Integer loadHeaderCapacity) {
        StringBuilder hql = new StringBuilder("");
        hql.append(" update LoadHeader o set o.status = :status, ");
        hql.append("    o.lockTime = :nowTime, o.deliveryTime = :nowTime ");
        hql.append(" where o.id = :id ");
        hql.append(" and o.cartonQty+1 >= :loadHeaderCapacity");
        hql.append(" and o.status = :partLoadStatus and o.warehouseId = :warehouseId");
        Query query = this.createUpdateQuery(hql.toString());
        query.setParameter("id", id);
        query.setParameter("nowTime", DateUtil.getNowTime());
        query.setParameter("status", LoadStatus.DELIVERYED.getValue());
        query.setParameter("partLoadStatus", LoadStatus.LOADING.getValue());
        query.setParameter("loadHeaderCapacity", Long.valueOf(loadHeaderCapacity));
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }
    /**
     * 检查并完成自动生成的交接单
     *
     * @return
     */
    public void updateLoadHeaderForAutoClose(Timestamp deaLine) {
        StringBuilder hql = new StringBuilder("");
        hql.append(" update LoadHeader o set o.status = :status, ");
        hql.append("    o.lockTime = :nowTime, o.deliveryTime = :nowTime ");
        hql.append(" where o.createdAt < :deaLine");
        hql.append(" and o.status = :partLoadStatus and o.warehouseId = :warehouseId");
        Query query = this.createUpdateQuery(hql.toString());
        query.setParameter("nowTime", DateUtil.getNowTime());
        query.setParameter("status", LoadStatus.DELIVERYED.getValue());
        query.setParameter("partLoadStatus", LoadStatus.LOADING.getValue());
        query.setParameter("deaLine", deaLine);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    public boolean isOverDueRtvLoadHeaer(Long doId) {
        StringBuilder hql = new StringBuilder("");
        hql.append(" SELECT COUNT(*) FROM doc_load_header DLH									");
        hql.append(" 	INNER JOIN doc_load_detail DLD ON DLH.ID = DLD.LOAD_HEADER_ID			");
        hql.append(" WHERE DLH.WAREHOUSE_ID = :warehouseId AND DLD.WAREHOUSE_ID = :warehouseId	");
        hql.append(" 	AND DLH.LOAD_TYPE = :loadType AND DLH.DEAL_TYPE = :dealType				");
        hql.append(" 	AND DLD.DO_HEADER_ID = :doId											");
        Query query = this.createSQLQuery(hql.toString());
        query.setParameter("loadType", LoadType.RTV.getValue());
        query.setParameter("dealType", dealType.OVERDUE_RTV.getValue());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("doId", doId);
        return ((BigInteger) query.uniqueResult()).compareTo(BigInteger.ZERO) > 0
                ? Boolean.TRUE : Boolean.FALSE;
    }

    public void updateLoadHeaderForUnload(Long loadHeaderId, String status) {
        StringBuilder hql = new StringBuilder("");
        hql.append(" update LoadHeader o set o.cartonQty = o.cartonQty - 1, ");
        hql.append("    o.status = :status ");
        hql.append(" where o.id = :id and o.warehouseId = :warehouseId");
        Query query = this.createUpdateQuery(hql.toString());
        query.setParameter("id", loadHeaderId);
        query.setParameter("status", status);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    public List<LoadHeader> findUnFinished(int size) {
        StringBuilder hql = new StringBuilder("");
        hql.append("FROM LoadHeader lh WHERE lh.status in (:status) AND lh.isAuto = :isAuto AND lh.createdAt > TIMESTAMPADD(DAY, -7, CURRENT_DATE()) AND lh.warehouseId = :warehouseId");

        Query query = this.createQuery(hql.toString());
        query.setParameterList("status", Lists.newArrayList(LoadStatus.LOADING.getValue(), LoadStatus.INITIAL.getValue()));
        query.setInteger("isAuto", 0);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(size);
        return (List<LoadHeader>) query.list();
    }

    public List<LoadDetail> getLastDetails(Long loadId, int size) {
        return this.createQuery("FROM LoadDetail ld WHERE ld.loadHeaderId = :loadId ORDER BY createdAt DESC").setParameter("loadId", loadId).setMaxResults(size).list();
    }
    /**
     * 批量设置交接单车牌号
     * @param ids 主键集合
     * @param vechileNo 车牌号
     */
    public void batchSetVechileNo(List<Long> ids, String vechileNo) {
        String sql =" update LoadHeader o set o.vechileNo = :vechileNo where o.id in (:ids)";
        Query query = this.createUpdateQuery(sql);
        query.setParameterList("ids", ids);
        query.setString("vechileNo", vechileNo);
        query.executeUpdate();
    }
}
