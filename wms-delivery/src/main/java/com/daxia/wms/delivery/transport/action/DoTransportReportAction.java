package com.daxia.wms.delivery.transport.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.load.entity.LoadHeader;
import com.daxia.wms.delivery.load.service.LoadService;
import com.daxia.wms.delivery.transport.entity.DoTransportReport;
import com.daxia.wms.delivery.transport.filter.DoTransportReportFilter;
import com.daxia.wms.delivery.transport.service.DoTransportReportService;
import com.daxia.wms.master.service.WarehouseService;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

@Name("com.daxia.wms.delivery.doTransportReportAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class DoTransportReportAction extends PagedListBean<DoTransportReport> {
    private static final long serialVersionUID = 1L;

    private DoTransportReportFilter doTransportReportFilter;
    @In(create = true)
    private DoTransportReportService doTransportReportService;

    @In
    private LoadService loadService;

    @In
    private WarehouseService warehouseService;

    private Long transportId;

    private DoTransportReport doTransportReport;

    private Long loadId;

    private LoadHeader loadHeader;

    /**
     * 初始化页面信息
     */
    @Create
    public void initialize() {
        doTransportReportFilter = new DoTransportReportFilter();
    }

    public DoTransportReportAction() {
        doTransportReport = new DoTransportReport();
        loadHeader = new LoadHeader();
    }


    public void reviewDetail() {
        if (null != loadId) {
            loadHeader = loadService.getLoadHeader(loadId);
        }
        if (null == loadHeader) {
            loadHeader = new LoadHeader();
        }
        doTransportReport = doTransportReportService.getDoTransportReportByLoadId(loadId);
        if (null == doTransportReport) {
            doTransportReport = new DoTransportReport();
            if (loadHeader != null) {
                doTransportReport.setWarehouseId(loadHeader.getWarehouseId());
                doTransportReport.setWarehouse(warehouseService.getWarehouse(loadHeader.getWarehouseId()));
                doTransportReport.setToWarehouseId(loadHeader.getTranInWhID());
                doTransportReport.setToWarehouse(warehouseService.getWarehouse(loadHeader.getTranInWhID()));
                doTransportReport.setLoadId(loadHeader.getId());
                doTransportReport.setLoadNo(loadHeader.getLoadNo());
                doTransportReport.setLoadHeader(loadHeader);
            }
        }
    }

    @Override
    public void query() {
        doTransportReportFilter.getOrderByMap().put("createdAt", "desc");
        this.buildOrderFilterMap(doTransportReportFilter);
        DataPage<DoTransportReport> dataPage = doTransportReportService
                .queryTransportRecord(doTransportReportFilter, getStartIndex(),
                        getPageSize());
        this.populateValues(dataPage);
    }

    public void deleteTransportRecord() {
        doTransportReportService.deleteById(transportId);
        query();
        this.sayMessage(MESSAGE_SUCCESS);
    }

    public void savedoTransportReport() {
        doTransportReportService.saveDoTransportReport(doTransportReport);
        loadHeader = null;
        this.sayMessage(MESSAGE_SUCCESS);
    }


    public DoTransportReportFilter getDoTransportReportFilter() {
        return doTransportReportFilter;
    }

    public void setDoTransportReportFilter(DoTransportReportFilter doTransportReportFilter) {
        this.doTransportReportFilter = doTransportReportFilter;
    }

    public Long getTransportId() {
        return transportId;
    }

    public void setTransportId(Long transportId) {
        this.transportId = transportId;
    }

    public DoTransportReport getDoTransportReport() {
        return doTransportReport;
    }

    public void setDoTransportReport(DoTransportReport doTransportReport) {
        this.doTransportReport = doTransportReport;
    }

    public Long getLoadId() {
        return loadId;
    }

    public void setLoadId(Long loadId) {
        this.loadId = loadId;
    }

    public LoadHeader getLoadHeader() {
        return loadHeader;
    }

    public void setLoadHeader(LoadHeader loadHeader) {
        this.loadHeader = loadHeader;
    }
}