package com.daxia.wms.delivery.print.service.carton;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.common.util.SystemConfig;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.deliveryorder.dao.DoDetailDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.print.dto.BaseCartonPrintDTO;
import com.daxia.wms.delivery.print.dto.CartonDetailPrintDTO;
import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.daxia.wms.delivery.print.helper.PrintCartonHelper;
import com.daxia.wms.delivery.print.helper.SFPrintCartonHelper;
import com.daxia.wms.delivery.print.service.PrintWaybillService;
import com.daxia.wms.delivery.recheck.entity.CartonDetail;
import com.daxia.wms.master.dao.ManufacturerDao;
import com.daxia.wms.master.entity.BusinessCustomer;
import com.daxia.wms.master.entity.Manufacturer;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.service.WarehouseService;
import com.daxia.wms.print.dto.PrintCfg;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.print.dto.PrintReportDto;
import com.daxia.wms.stock.stock.dao.StockBatchAttDAO;
import com.daxia.wms.stock.stock.entity.StockBatchAtt;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 批发订单箱标签打印服务类
 */
@Name("printWholesaleWaybillService")
@lombok.extern.slf4j.Slf4j
public class PrintWholesaleWaybillServiceImpl extends AbstractPrintWaybillService implements PrintWaybillService {
    @In
    private WarehouseService warehouseService;

    @In
    private DoDetailDAO doDetailDAO;

    @In
    private StockBatchAttDAO stockBatchAttDAO;

    @In
    private ManufacturerDao manufacturerDao;

    @Create
    public void init () {
        this.setWaybillType(Constants.WaybillType.JD);
    }

    @Override
    protected void buildDTO(List<PrintReportDto> printReportDtos, DeliveryOrderHeader doHeader, BaseCartonPrintDTO carton, int index, int count) {
        CartonPrintDTO cartonPrintDTO = setBaseCartonInfo(doHeader, carton, count, index);
        cartonPrintDTO.setIsPrinted(carton.getIsPrinted());
        cartonPrintDTO.setRefNo1(doHeader.getRefNo1());
        cartonPrintDTO.setUserDeffine5(doHeader.getUserDeffine5());
        cartonPrintDTO.setParentDoNo(doHeader.getUserDeffine3());
        cartonPrintDTO.setReceivable(doHeader.getReceivable());
        cartonPrintDTO.setExpectedArriveTime1(doHeader.getExpectedArriveTime1());
        cartonPrintDTO.setPostCode(doHeader.getPostCode());
        cartonPrintDTO.setOrderTime(doHeader.getCreatedAt());
        //设置仓库code
        printReportDtos.add(cartonPrintDTO);
    }

    /**
     * 设置箱标签打印dto的基本信息
     *
     * @param doHeader
     * @param carton
     * @param count    总箱数
     * @param index    当前第几箱
     * @return 箱标签打印dto
     */
    private CartonPrintDTO setBaseCartonInfo(DeliveryOrderHeader doHeader, BaseCartonPrintDTO carton, int count, int index) {
        CartonPrintDTO cartonPrintDTO = new CartonPrintDTO();
        cartonPrintDTO.setCartonNo(carton.getCartonNo());
        cartonPrintDTO.setDoNo(doHeader.getDoNo());
        cartonPrintDTO.setCartonId(carton.getId());
        cartonPrintDTO.setOutRefNo(doHeader.getRefNo1());
        cartonPrintDTO.setRemark(doHeader.getNotes());
    
        // 设置寄件人地址信息
        Warehouse warehouse = warehouseService.getLocalWarehouse();
        cartonPrintDTO.setSendName(StringUtil.notNullString(warehouse.getWarehouseName()));
        // 收件人信息
        BusinessCustomer customer = doHeader.getBusinessCustomer();
        if (customer != null) {
            cartonPrintDTO.setClientName(customer.getCustomerName());
        } else {
            cartonPrintDTO.setClientName(doHeader.getConsigneeName());
        }
        if(null != doHeader.getShipTime()){
            cartonPrintDTO.setExtSendDate(String.valueOf(doHeader.getShipTime()));
        }
        cartonPrintDTO.setCarrierName(doHeader.getCarrier().getShortName());
        cartonPrintDTO.setConsigneeName(doHeader.getConsigneeName());
        cartonPrintDTO.setClientProvinceAndCityAndCountyAddress(PrintCartonHelper.buildProvinceAndCityAndCountyAddress(doHeader,","));
        cartonPrintDTO.setClientAddress(PrintCartonHelper.buildAddress(doHeader));
        cartonPrintDTO.setClientPhone(PrintCartonHelper.buildTelOrMobile(doHeader));
        cartonPrintDTO.setCartonIndex(index);
        String isPrintCartonByCarton = SystemConfig.getConfigValue("print.carton.isPrintCartonByCarton", ParamUtil.getCurrentWarehouseId());
        if (StringUtil.isNotBlank(isPrintCartonByCarton) && isPrintCartonByCarton.equals("1")) {
            if (Constants.DoStatus.ALL_CARTON.getValue().equals(doHeader.getStatus()) || Constants.DoStatus.ALL_CARTON.getValue().equals(doHeader.getPcsStatus()) || Constants.PackageType.C.getValue().equals(carton
                    .getPackageType())) {
                cartonPrintDTO.setCartonCount(count);
            }
        } else {
            cartonPrintDTO.setCartonCount(count);
        }
        cartonPrintDTO.setPackageType(carton.getPackageType());
        //设置箱明细信息
        List<CartonDetail> detailList = cartonService.getCartonDetailByHeaderId(carton.getId());
        List<CartonDetailPrintDTO> subDtoList = new ArrayList<CartonDetailPrintDTO>();
        BigDecimal skuUnitQty = BigDecimal.ZERO;
        Set<String> productCodeList = new HashSet<String>();

        if(CollectionUtils.isNotEmpty(detailList)){
            for (CartonDetail cartonDetail : detailList) {
                CartonDetailPrintDTO dto = new CartonDetailPrintDTO();
                dto.setNumber(cartonDetail.getPackedNumber());
                dto.setProductCode(cartonDetail.getSku().getProductCode());
                dto.setEan13(cartonDetail.getSku().getEan13());
                dto.setProductName(cartonDetail.getSku().getProductCname());
                skuUnitQty = skuUnitQty.add(cartonDetail.getPackedNumber());
                subDtoList.add(dto);
                productCodeList.add(cartonDetail.getSku().getProductCode());
            }
        }
        if (productCodeList.size() == 1) {
            cartonPrintDTO.setProductNameInfo(detailList.get(0).getSku().getProductCname());
        }
        List<DeliveryOrderDetail> details = doHeader.getDoDetails();
        DeliveryOrderDetail detail = details.get(0);
        //cartonPrintDTO.setCustomerName(detail.getMerchant().getDescrC());
        cartonPrintDTO.setShortAddress(SFPrintCartonHelper.getCityCname(doHeader) + "," + SFPrintCartonHelper.getCountyCname(doHeader));
        cartonPrintDTO.setSkuUnitQty(skuUnitQty);
        cartonPrintDTO.setSubDtoList(subDtoList);
        //箱明细
        if(CollectionUtils.isNotEmpty(detailList) && detailList.size() == 1){
            Long lotId = detailList.get(0).getLotId();
            StockBatchAtt stockBatchAtt = null;
            if(null != lotId){
                stockBatchAtt = stockBatchAttDAO.get(lotId);
            }

            if (null != stockBatchAtt) {
                if (StringUtil.isNotEmpty(stockBatchAtt.getLotatt08())) {
                    Manufacturer manufacturer = manufacturerDao.get(Long.valueOf(stockBatchAtt.getLotatt08()));
                    if (null != manufacturer) {
                        cartonPrintDTO.setManufacturerName(manufacturer.getDescrC());//厂家
                    }
                }
                cartonPrintDTO.setLotatt05(stockBatchAtt.getLotatt05());//批号
            }
            cartonPrintDTO.setProductName(detailList.get(0).getSku().getProductCname());//商品名称
            cartonPrintDTO.setPackedQty(detailList.get(0).getPackedNumber());//数量
        }
    
        buildExtAttr(genPrintData(null, doHeader), doHeader, cartonPrintDTO);
        
        return cartonPrintDTO;
    }

    @Override
    protected PrintData genPrintData(List<PrintReportDto> dtoList, DeliveryOrderHeader doHeader) {
        PrintData printData = new PrintData();
        printData.setDtoList(dtoList);
        printData.setPrintCfg(new PrintCfg("waybill_wholesale", "100", "180"));
        return printData;
    }
}