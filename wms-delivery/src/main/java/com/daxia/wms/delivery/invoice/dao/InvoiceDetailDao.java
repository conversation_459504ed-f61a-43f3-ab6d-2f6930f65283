package com.daxia.wms.delivery.invoice.dao;

import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.delivery.invoice.entity.InvoiceDetail;
import com.daxia.wms.delivery.invoice.entity.InvoiceNo;


/**
 * 发票信息DAO
 */
@Name("com.daxia.wms.delivery.invoiceDetailDao")
@lombok.extern.slf4j.Slf4j
public class InvoiceDetailDao extends HibernateBaseDAO<InvoiceDetail, Long> {

	private static final long serialVersionUID = -1487519590084131915L;
	
	@SuppressWarnings("unchecked")
	public List<InvoiceDetail> findByHeaderId(Long invoiceHId) {
		String hql = "from InvoiceDetail o where o.invoiceHeaderId = :invoiceHeaderId  and o.warehouseId = :warehouseId";
		
		Query query = this.createQuery(hql);
		query.setParameter("invoiceHeaderId", invoiceHId);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		return query.list();
	}
}