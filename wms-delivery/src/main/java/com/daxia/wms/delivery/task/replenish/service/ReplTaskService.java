package com.daxia.wms.delivery.task.replenish.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.task.replenish.entity.ReplenishTask;
import com.daxia.wms.Constants.TaskStatus;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.stock.task.entity.TrsTask;
import com.daxia.wms.stock.task.filter.TrsTaskFilter;

public interface ReplTaskService {

    /**
     * 查询补货任务页面
     * @param trsTaskFilter
     * @param startIndex
     * @param pageSize
     * @return
     */
    public DataPage<TrsTask> queryReplTrs(TrsTaskFilter trsTaskFilter, int startIndex, int pageSize);

    /**
     * 查询补货任务页面
     * @param trsTaskFilter
     * @return
     */
    public List<ReplenishTask> queryReplTrs(TrsTaskFilter trsTaskFilter);
    

    /**
     * 根据给定的任务idList选择任务列表
     * @param ids
     * @return
     */
    public List<ReplenishTask> getTasksByIds(List<Long> ids);

    /**
     * 执行补货任务
     * @throws Exception
     */
    public Long executeRepl(Long id, BigDecimal realQtyUnit, String realLoc, String reasonCode,String reasonDsr, boolean ignorePending) throws Exception;


    /**
     * 取消补货任务
     */
    public Date cancelRepl(Long ids);

    /**
     * 
     * <pre>
     * 导出补货任务的PDF数据
     * </pre>
     *
     * @param replHeaderId
     * @return
     */
    public byte[] exportPDF(Long replHeaderId);

    /**
     * 打印补货任务的数据
     * @param replHeaderId
     * @return
     */
    public List<String> print(Long replHeaderId);

    /**
     * 验证库位
     * @param locCode
     * @param skuId
     * @param lotId
     * @return
     */
    public Location checkLocation(String locCode, Long skuId, Long lotId, boolean ignorePending);
    
    /**
     * 获取补货任务，按照上架库位顺序排序
     */
    public List<ReplenishTask> findReplTasks(Long skuId, List<String> status);
    
    /**
     * @return 获取指定状态的补货任务的skuId
     */
    public List<Long> findReplTaskSkus(TaskStatus status);
    
    /**
     * @return 获取指定状态的补货任务
     */
    public List<ReplenishTask> findReplTasks(List<String> status);
	
	/**
	 * 根据toLpnNo、任务状态skuId获取补货任务
	 * @param lpnNum
	 * @param taskStatus
	 * @param skuId
	 * @return
	 */
	public List<ReplenishTask> findReplTasks(String lpnNum, List<String> status, Long skuId);
	
	 /**
     * 根据taskId和toLpnNo更新补货任务
     * @param taskId
     * @param status
     * @return
     */
	public void updateReplTaskLpn(Long taskId, String lpnNum);
	
	/**
	 * 根据补货单号，任务状态获取补货任务
	 * @param replNo
	 * @param taskStatus
	 * @return
	 */
	public List<ReplenishTask> findReplTasksByReplNoStatus(String replNo, List<String> taskStatus);
	
	/**
	 * 根据中转容器查询补货任务
	 * @param lpnNo
	 * @return
	 */
	public List<ReplenishTask> findReplTasksByLpnNo(String lpnNo , List<String> taskStatus);
	
	/**
	 * 执行下架
	 * @param taskId
	 * @param qty
	 * @param reasonCode
	 * @param reasonDescr
	 * @param updateBy
	 * @throws Exception 
	 */
	public Long executeOffShelfReplTask(Long taskId, BigDecimal qty, String lpnNo, String reasonCode, String reasonDescr) throws Exception;

	/**
	 * 根据补货单id完成补货
	 * @param replHeaderId
	 * @return
	 */
    void confirmReplHeader(Long replHeaderId);

    /**
	 * 根据补货单id取消补货单
	 * @param replHeaderId
	 * @return
	 */
    void cancelReplHeader(Long replHeaderId);
    
    public ReplenishTask getReplenishTaskById(Long taskId);
    
    /**
     * 根据配置项，是否能超补
     */
    public boolean canOverReplenish();
    
    /**
     * 取消补货移位任务
     */
    public void cancelReplMoveTask(Long replTaskId);
    
	/**
	 * 根据sku查询所有初始化状态的闲时补货任务总qty
	 */
	public BigDecimal findFreeReplQtyBySku(Long skuId);
	
	/**
	 * 根据商品查发布的即时补货任务
	 */
	public List<BigDecimal> getReleasedJPTask(Long skuId);
}
