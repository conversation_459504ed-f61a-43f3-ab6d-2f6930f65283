package com.daxia.wms.delivery.recheck.service.impl;

import com.daxia.dubhe.api.internal.util.StrUtils;
import com.daxia.framework.common.cfg.AutoLoadAndDeliverCfg;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.util.ExcelUtil;
import com.daxia.wms.*;
import com.daxia.wms.Constants.*;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.DoWaveExService;
import com.daxia.wms.delivery.deliveryorder.service.OrderLogService;
import com.daxia.wms.delivery.load.dao.ReShipDoDAO;
import com.daxia.wms.delivery.load.entity.ReShipDo;
import com.daxia.wms.delivery.recheck.dao.CartonDetailDAO;
import com.daxia.wms.delivery.recheck.dao.CartonHeaderDAO;
import com.daxia.wms.delivery.recheck.dao.CartonHeaderHisDAO;
import com.daxia.wms.delivery.recheck.dto.CombineBindingInfo;
import com.daxia.wms.delivery.recheck.entity.CartonDetail;
import com.daxia.wms.delivery.recheck.entity.CartonHeader;
import com.daxia.wms.delivery.recheck.entity.CartonHeaderHis;
import com.daxia.wms.delivery.recheck.entity.TempCarton;
import com.daxia.wms.delivery.recheck.filter.CartonHeaderFilter;
import com.daxia.wms.delivery.recheck.service.*;
import com.daxia.wms.delivery.recheck.service.impl.carton.CainiaoWayBillCancel;
import com.daxia.wms.delivery.recheck.service.impl.carton.CainiaoWayBillUpdate;
import com.daxia.wms.delivery.wave.dto.BatchGroupDoCartonInfoDTO;
import com.daxia.wms.delivery.wave.filter.BatchGroupDoCartonFilter;
import com.daxia.wms.exp.delivery.srv.WaybillChangeExpSrv;
import com.daxia.wms.master.entity.Carrier;
import com.daxia.wms.master.entity.WarehouseCarrier;
import com.daxia.wms.master.service.WarehouseCarrierService;
import com.daxia.wms.waybill.htky.dto.BillCodeFeedbackRequest;
import com.daxia.wms.waybill.htky.dto.PrintFeedback;
import com.daxia.wms.waybill.htky.service.BestWaybillService;
import com.daxia.wms.waybill.zeny.dto.ZenyCancelMainWaybillDetail;
import com.daxia.wms.waybill.zeny.dto.ZenyCancelMainWaybillRequest;
import com.daxia.wms.waybill.zeny.dto.ZenyCancelSubWaybillDetail;
import com.daxia.wms.waybill.zeny.dto.ZenyCancelSubWaybillRequest;
import com.daxia.wms.waybill.zeny.service.ZenyWaybillService;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 装箱单业务
 */
@Name("com.daxia.wms.delivery.cartonService")
@lombok.extern.slf4j.Slf4j
public class CartonServiceImpl implements CartonService {

    @In
    CartonHeaderDAO cartonHeaderDAO;

    @In
    CartonDetailDAO cartonDetailDAO;

    @In
    private CartonHeaderHisDAO cartonHeaderHisDAO;

    @In(create = true)
    private CartonNoGenerateDispatcher cartonNoGenerateDispatcher;

    @In
    private CainiaoWayBillCancel cainiaoWayBillCancel;

    @In
    private CartonCancelLogService cartonCancelLogService;

    @In
    private ReCheckService reCheckService;

    @In
    private ReShipDoDAO reShipDoDAO;

    @In(create = true)
    private ZenyWaybillService zenyWaybillService;

    @In
    private DoWaveExService doWaveExService;

    @In
    private WarehouseCarrierService warehouseCarrierService;

    @In
    private CainiaoWayBillUpdate cainiaoWayBillUpdate;

    @In
    private EmsWayBillUpdateService emsWayBillUpdateService;

    @In(create = true)
    private BestWaybillService bestWaybillService;

    @In
    private DeliveryOrderService deliveryOrderService;

    @In
    private TempCartonService tempCartonService;

    @In
    private OrderLogService orderLogService;

    @In
    private WaybillChangeExpSrv waybillChangeExpSrv;

    /**
     * <pre>
     * 根据装箱单ID取得装箱单信息
     * </pre>
     *
     * @param cartonHeaderId 装箱单ID
     * @return
     */
    @Override
    public CartonHeader getCartonHeader(Long cartonHeaderId) {
        return cartonHeaderDAO.get(cartonHeaderId);
    }

    /**
     * <pre>
     * 查询装箱单信息。
     * </pre>
     *
     * @param filter
     * @return
     */
    @Override
    public List<CartonHeader> query(CartonHeaderFilter filter) {
        return cartonHeaderDAO.findByFilter(filter);
    }

    /**
     * <pre>
     * 根据NO获取装箱头信息
     * </pre>
     *
     * @param cartonNo
     * @return
     */
    @Override
    public CartonHeader getCartonByNo(String cartonNo) {
        return cartonHeaderDAO.findByCartonNo(cartonNo);
    }
    /**
     * <pre>
     * 根据lpnNO获取装箱头信息
     * </pre>
     *
     * @param cartonNo
     * @return
     */
    @Override
    public CartonHeader findOneByLpnNo(String cartonNo) {
        return cartonHeaderDAO.findOneByLpnNo(cartonNo);
    }

    /**
     * <pre>
     * 根据NO获取装箱头信息
     * </pre>
     *
     * @param cartonNo
     * @return
     */
    @Override
    public CartonHeader findByCartonNoForYouBei(String cartonNo) {
        return cartonHeaderDAO.findByCartonNoForYouBei(cartonNo);
    }
    /**
     * <pre>
     * 根据waybill获取装箱头信息
     * </pre>
     *
     * @param cartonNo
     * @return
     */
    @Override
    public CartonHeader findByWayBillForYouBei(String cartonNo) {
        return cartonHeaderDAO.findByWayBillForYouBei(cartonNo);
    }
    /**
     * <pre>
     * 优贝仓根据NO获取装箱头信息
     * </pre>
     *
     * @param cartonNo
     * @return
     */
    @Override
    public CartonHeader getCartonByNoForYouBei(String cartonNo) {
        return cartonHeaderDAO.getCartonByNoForYouBei(cartonNo);
    }

    /**
     * <pre>
     * 更新装箱头信息
     * </pre>
     *
     * @param cartonHeader
     */
    @Override
    public void update(CartonHeader cartonHeader) {
        cartonHeaderDAO.update(cartonHeader);
    }

    /**
     * 保存carton重量
     *
     * @param cartonHeader
     * @param weight
     */
    @Override
    @Transactional
    public int saveCartonWeight(CartonHeader cartonHeader, BigDecimal weight, String operator) {
        //记录日志,称重
        orderLogService.saveLog(cartonHeader.getDoHeader(), OrderLogConstants.OrderLogType.CARTON_WEIGHT.getValue(),
                ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_CARTON_WEIGHT, null, cartonHeader.getCartonNo(), weight));

        cartonHeader.setIsWeight(YesNo.YES.getValue().longValue());
        if (check4AutoShip(cartonHeader)) {
            autoShip(cartonHeader, DateUtil.getNowTime());
        }
        return cartonHeaderDAO.saveCartonWeight(cartonHeader, weight, YesNo.YES.getValue(), operator);
    }

    @Override
    @Transactional
    public void saveCartonWeight4GroupWave(CartonHeader cartonHeader, BigDecimal weight, String operator) {
        //1。验证是否团购波次
        if (!AutoWaveType.BATCH_GROUP.getValue().equals(cartonHeader.getDoHeader().getWaveHeader().getAutoType())) {
            //非团购波次，异常
            throw new DeliveryException(DeliveryException.CARTON_WEIGHT_ERROR_NOT_GROUP_WAVE);
        }

        if (PageConfig.is(ConfigKeys.IS_GROUP_WEIGHT_SYNC, Config.ConfigLevel.WAREHOUSE.getValue())) {
            List<CartonHeader> cartonHeaderList = cartonHeaderDAO.findListByWaveId(cartonHeader.getDoHeader().getWaveId());
            for (CartonHeader c : cartonHeaderList) {
                syncWeightToCainiao(c, weight);
            }
        }

        //批量更新箱重量
        cartonHeaderDAO.updateWeightByWaveId(cartonHeader.getDoHeader().getWaveId(), weight, operator);
        //批量更新订单重量
        deliveryOrderService.updateWeightByWaveId(cartonHeader.getDoHeader().getWaveId(), weight);
    }

    /**
     * 根据do num查询箱头
     *
     * @param doId
     * @return
     */
    @Override
    public CartonHeader findANotLoadedCartonHeaderByDoId(Long doId) {
        return cartonHeaderDAO.findACartonListByDoId(doId);
    }

    /**
     * 找到doId下有多少箱
     *
     * @param doId
     * @return
     */
    @Override
    public int countCartonNumOfADo(Long doId) {
        return cartonHeaderDAO.countCartonNumOfADo(doId);
    }

    /**
     * 根据do num查询箱头
     *
     * @param doNum
     * @return
     */
    @Override
    public List<CartonHeader> findByDoNo(String doNum) {
        return cartonHeaderDAO.findByDoNo(doNum);
    }

    /**
     * 拼箱
     *
     * @param fromCartonId
     * @param toCartonId
     */
    @Override
    @Transactional
    public void boxing(Long fromCartonId, Long toCartonId) {
        CartonHeader header = cartonHeaderDAO.get(fromCartonId);
        CartonHeader toCartonHeader = cartonHeaderDAO.get(toCartonId);

        DeliveryOrderHeader doHeader = header.getDoHeader();

        //订单装箱完成之前，拼箱不允许跨整散
        if (DoStatus.ALL_CARTON.getValue().compareTo(doHeader.getStatus()) > 0 &&
                !StringUtil.equals(header.getExt1(), toCartonHeader.getExt1())) {
            throw new DeliveryException(DeliveryException.DO_STATUS_CANNOT_BOXING);
        }
        cartonDetailDAO.updateDetailHeaderId(fromCartonId, toCartonId);
        // 更新目标箱的重量体积等信息。
        addCartonVolumeAndGrossWeight(fromCartonId, toCartonId);

        cancelCartonById(fromCartonId);

        cartonHeaderDAO.removeById(fromCartonId);
        if (DoStatus.ALL_CARTON.getValue().equals(doHeader.getStatus())) {
            // 拼箱后京东的重设箱号。
            reCheckService.resetJDCartonNo(doHeader.getId());
        }
        //拼箱删除预下单信息
        tempCartonService.removeByDoId(header.getDoHeaderId());
        //记录日志，拼箱
        orderLogService.saveLog(doHeader,
                OrderLogConstants.OrderLogType.CARTON_BOXING.getValue(),
                ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_CARTON_BOXING, null,
                        toCartonHeader.getCartonNo(), header.getCartonNo()));
    }

    /**
     * 去掉箱调用（目前只有菜鸟电子面单会使用）
     */
    @Override
    public void cancelCartonById(Long cartonId) {
        // 取消的箱信息调用菜鸟的取消接口
        CartonHeader cartonHeader = this.getCartonHeader(cartonId);

        if (StringUtil.isEmpty(cartonHeader.getWayBill())) {
            return;
        }

        DeliveryOrderHeader doHeader = cartonHeader.getDoHeader();
        if (null != doHeader && DoType.SELL.getValue().equals(doHeader.getDoType())) {
            cancelCarton(doHeader, cartonHeader);
        }
    }

    @Override
    @Transactional
    public void cancelTempCartonByDoId(Long doId) {
        DeliveryOrderHeader deliveryOrderHeader = deliveryOrderService.getDoHeaderById(doId);
        TempCarton tempCarton = tempCartonService.getByDoId(doId);
        if (tempCarton == null || !YesNo.YES.getValue().equals(tempCarton.getSuccessFlag())) {
            //防止数据异常，还是刷一次tempCarton
            tempCartonService.removeByDoId(doId);
            return;
        }
        CartonHeader cartonHeader = new CartonHeader();
        cartonHeader.setCartonNo(tempCarton.getCartonNo());
        cartonHeader.setTrackingNo(tempCarton.getTrackingNo());
        cartonHeader.setWayBill(tempCarton.getWayBill());
        cartonHeader.setPrintData(tempCarton.getPrintData());
        Integer defaultCarrierId = SystemConfig.getConfigValueInt("dubhe.default.carrier.id", ParamUtil.getCurrentWarehouseId());
        if (defaultCarrierId == null) {
            defaultCarrierId = -9999;
        }
        if (deliveryOrderHeader.getCarrierId() != null && !Long.valueOf(defaultCarrierId).equals(deliveryOrderHeader.getCarrierId())) {
            cancelCarton(deliveryOrderHeader, cartonHeader);
        }

        //取消后删除预下单信息
        tempCartonService.removeByDoId(tempCarton.getDoHeaderId());
    }

    private void cancelCarton(DeliveryOrderHeader doHeader, CartonHeader cartonHeader) {
        if (useCustomWayBill(doHeader)) {
            return;
        }
        Carrier carrier = doHeader.getCarrier();
        if (WaybillType.CAINIAO == carrier.getWaybillType() && !useCustomWayBill(doHeader)) {
            cainiaoWayBillCancel.reqeust(doHeader, cartonHeader);
        } else if (WaybillType.ZENY.equals(carrier.getWaybillType())) {
            DoWaveEx doWaveEx = doWaveExService.findByDoHeaderId(doHeader.getId());
//				if (StringUtils.equals(doWaveEx.getTrackingNo(),cartonHeader.getCartonNo())) {
            //取消主单号
            try {
                cancelZenyMainNo(doHeader, cartonHeader, doWaveEx);
            } catch (Exception e) {
                //取消异常
            }
        } else if (WaybillType.HTKY.equals(carrier.getWaybillType())) {
            DoWaveEx doWaveEx = doWaveExService.findByDoHeaderId(doHeader.getId());
            cancelBestMainNo(doHeader, cartonHeader, doWaveEx);
        }
        // 记录日志
        cartonCancelLogService.add(doHeader, cartonHeader, Boolean.TRUE);
    }

    /**
     * 增益子单号取消
     *
     * @param doHeader
     * @param cartonHeader
     */
    private void cancelZenySubNo(DeliveryOrderHeader doHeader, CartonHeader cartonHeader, DoWaveEx doWaveEx) {
        WarehouseCarrier warehouseCarrier = warehouseCarrierService.getCarrierInfoByWarehouseIdAndCarrierId(doHeader
                .getWarehouseId(), doHeader.getCarrier().getId());
        ZenyCancelSubWaybillRequest request = new ZenyCancelSubWaybillRequest();
        request.setBizno(warehouseCarrier.getExt1());
        ZenyCancelSubWaybillDetail detail = new ZenyCancelSubWaybillDetail();
        detail.setWaybillno(doWaveEx.getTrackingNo());
        detail.setSubwaybillno(cartonHeader.getCartonNo());
        detail.setBizorderno(doHeader.getDoNo());
        detail.setBizno(warehouseCarrier.getExt1());
        detail.setBizname(warehouseCarrier.getExt2());
        List<ZenyCancelSubWaybillDetail> data = new ArrayList<ZenyCancelSubWaybillDetail>();
        data.add(detail);
        request.setData(data);
        zenyWaybillService.cancelSubNo(request, warehouseCarrier.getContentUrl(), warehouseCarrier.getAppSecret());
    }

    /**
     * 增益主单号取消
     *
     * @param doHeader
     * @param cartonHeader
     */
    private void cancelZenyMainNo(DeliveryOrderHeader doHeader, CartonHeader cartonHeader, DoWaveEx doWaveEx) {
        WarehouseCarrier warehouseCarrier = warehouseCarrierService.getCarrierInfoByWarehouseIdAndCarrierId(doHeader
                .getWarehouseId(), doHeader.getCarrier().getId());
        ZenyCancelMainWaybillRequest request = new ZenyCancelMainWaybillRequest();
        request.setBizno(warehouseCarrier.getExt1());
        ZenyCancelMainWaybillDetail detail = new ZenyCancelMainWaybillDetail();
        detail.setWaybillno(cartonHeader.getCartonNo());
        detail.setBizorderno(cartonHeader.getTrackingNo());
        detail.setBizno(warehouseCarrier.getExt1());
        detail.setBizname(warehouseCarrier.getExt2());
        List<ZenyCancelMainWaybillDetail> data = new ArrayList<ZenyCancelMainWaybillDetail>();
        data.add(detail);
        request.setData(data);
        zenyWaybillService.cancelMainNo(request, warehouseCarrier.getContentUrl(), warehouseCarrier.getAppSecret());
        doWaveEx.setTrackingNo(null);
        doWaveExService.update(doWaveEx);
    }

    /**
     * 百世汇通主单号取消
     *
     * @param doHeader
     * @param cartonHeader
     */
    private void cancelBestMainNo(DeliveryOrderHeader doHeader, CartonHeader cartonHeader, DoWaveEx doWaveEx) {
        WarehouseCarrier warehouseCarrier = warehouseCarrierService.getCarrierInfoByWarehouseIdAndCarrierId(doHeader
                .getWarehouseId(), doHeader.getCarrier().getId());
        PrintFeedback t = new PrintFeedback();
        t.setMailNo(cartonHeader.getWayBill());
        BillCodeFeedbackRequest request = new BillCodeFeedbackRequest();
        List<PrintFeedback> infoList = new ArrayList<PrintFeedback>();
        infoList.add(t);
        request.setRemovePrintFeedbackList(infoList);
        bestWaybillService.cancalWabybillNo(request, warehouseCarrier);
        //大头笔信息
        doWaveEx.setShortAddress(null);
        //集包地
        doWaveEx.setPackageCenterName(null);
        //始发站点名称
        doWaveEx.setOriginName(null);
        doWaveEx.setOriginCode(null);
        //末端分拣号,名称
        doWaveEx.setDestinationCode(null);
        doWaveEx.setDestinationName(null);
        doWaveExService.update(doWaveEx);
    }

    private boolean useCustomWayBill(DeliveryOrderHeader doHeader) {
        String useCustomWayBillScript = SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_USE_CUSTOM_SCRIPT, ParamUtil.getCurrentWarehouseId());
        return StringUtil.isNotEmpty(useCustomWayBillScript) && Boolean.TRUE.equals(MvelUtil.eval(useCustomWayBillScript, ImmutableMap.of("doHeader", (Object) doHeader)));
    }

    /**
     * 更新目标箱的重量体积等信息。
     *
     * @param fromCartonId
     * @param toCartonId
     */
    @Transactional
    public void addCartonVolumeAndGrossWeight(Long fromCartonId, Long toCartonId) {
        CartonHeader fromCarton = cartonHeaderDAO.get(fromCartonId);
        CartonHeader toCarton = cartonHeaderDAO.get(toCartonId);
        toCarton.setVolume(fromCarton.getVolume().add(toCarton.getVolume()));
        toCarton.setActualVolume(fromCarton.getActualVolume().add(toCarton.getActualVolume()));
        toCarton.setGrossWeight(fromCarton.getGrossWeight().add(toCarton.getGrossWeight()));
        toCarton.setActualGrossWeight(fromCarton.getActualGrossWeight().add(toCarton.getActualGrossWeight()));
        cartonHeaderDAO.saveOrUpdate(toCarton);
    }

    /**
     * 拆箱
     *
     * @param fromCartonId
     * @param cartonCount  拆成多少箱
     */
    @Override
    @Transactional
    public void unBoxing(Long fromCartonId, int cartonCount) {
        CartonHeader header = cartonHeaderDAO.get(fromCartonId);
        DeliveryOrderHeader doHeader = header.getDoHeader();
        for (int i = 0; i < cartonCount; i++) {
            CartonHeader newHeader = genNewCarton(doHeader);
            newHeader.setActualGrossWeight(BigDecimal.ZERO);
            newHeader.setActualVolume(BigDecimal.ZERO);
            newHeader.setDoHeader(doHeader);
            newHeader.setGrossWeight(BigDecimal.ZERO);
            newHeader.setIsWeight(Long.valueOf(0));
            //newHeader.setLpnNo(header.getLpnNo());
            newHeader.setNetWeight(BigDecimal.ZERO);
            newHeader.setStatus(header.getStatus());
            newHeader.setVolume(BigDecimal.ZERO);
            newHeader.setWeightFlag(0);
            newHeader.setAutoDeliveryFlag(header.getAutoDeliveryFlag());
            newHeader.setExt1(header.getExt1());

            cartonHeaderDAO.save(newHeader);
        }
        if (DoStatus.ALL_CARTON.getValue().equals(doHeader.getStatus())) {
            // 拆箱后京东的重设箱号。
            reCheckService.resetJDCartonNo(doHeader.getId());
        }
        //记录日志，拆箱
        orderLogService.saveLog(doHeader,
                OrderLogConstants.OrderLogType.CARTON_UNBOXING.getValue(),
                ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_CARTON_UNBOXING, null, header.getCartonNo(), cartonCount + 1));
    }

    /**
     * 根据doId找到其中一个箱头
     *
     * @param doId
     * @return CartonHeader
     */
    @Override
    public CartonHeader findCartonHeaderByDoId(Long doId) {
        return this.cartonHeaderDAO.findCartonHeaderByDoId(doId);
    }

    /**
     * 根据doNum找到未称重的箱头
     *
     * @param doNum
     * @return
     */
    @Override
    public String findUnWeighCartonHeader(String doNum) {
        return cartonHeaderDAO.findUnWeighCartonHeader(doNum);
    }

    /**
     * 检查一个订单是否对应多个箱子。
     *
     * @param doHeaderId 订单号
     * @return
     */
    @Override
    public boolean isMultiCartonOfADo(Long doHeaderId) {
        return cartonHeaderDAO.isMultiCartonOfADo(doHeaderId);
    }

    /**
     * 订单是否已经全部交接完成
     */
    @Override
    public Boolean isDoCartonsAllLoaded(Long doHeaderId) {
        List<CartonHeader> cartonHeaderList = cartonHeaderDAO.findNotLoadedCarton(doHeaderId);
        if (ListUtil.isNullOrEmpty(cartonHeaderList)) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public List<CartonHeader> findByDoId(Long doHeaderId) {
        return cartonHeaderDAO.findByDoId(doHeaderId);
    }

    @Override
    public CartonHeader genNewCarton(DeliveryOrderHeader doHeader) {
        CartonHeader cartonHeader = new CartonHeader();

        cartonNoGenerateDispatcher.genNewCarton(doHeader, cartonHeader);

        return cartonHeader;
    }

    /**
     * 获取订单下已交接/未交接的包裹号
     *
     * @param doHeaderId 订单头Id
     * @param allLoad    True为查询已交接的包裹，False为查询未交接的包裹
     * @return
     */
    @Override
    public List<String> getCartonNoByDoId(Long doHeaderId, Boolean allLoad) {
        List<String> cartonNoList;
        if (allLoad) {
            cartonNoList = cartonHeaderDAO.findLoadedCartonNo(doHeaderId);
        } else {
            cartonNoList = cartonHeaderDAO.findNotLoadedCartonNo(doHeaderId);
        }
        return cartonNoList;
    }

    @Override
    public CartonHeaderHis queryCartonHeaderHisById(Long cartonHeaderId) {
        return cartonHeaderHisDAO.get(cartonHeaderId);
    }

    /**
     * 给接口调用的，要加仓库id
     */
    @Override
    public CartonHeader getCartonByNoAndWhId(String cartonNo, Long warehouseID) {
        CartonHeaderFilter filter = new CartonHeaderFilter();
        filter.setCartonNo(cartonNo);
        filter.setWarehouseId(warehouseID);
        List<CartonHeader> cartons = this.query(filter);

        if (cartons.isEmpty()) {
            return null;
        } else {
            return cartons.get(0);
        }
    }

    @Override
    @Transactional
    public void phyDelDoCartonInfoByDoId(Long doHeaderId) {
        List<CartonHeader> cartonHeaderList = findByDoId(doHeaderId);
        for (CartonHeader cartonHeader : cartonHeaderList) {
            //调用菜鸟接口
            cancelCartonById(cartonHeader.getId());
        }
        cartonHeaderDAO.physicalDeleteByDoId(doHeaderId);
        cartonDetailDAO.physicalDeleteByDoId(doHeaderId);

    }

    @Override
    @Transactional
    public void insertCartonClient(DeliveryOrderHeader doHeader) {
        CartonHeader cartonHeader = this.genNewCarton(doHeader);
        cartonHeader.setDoHeader(doHeader);
        cartonHeader.setStatus(Constants.CartonStatus.PACK_OVER.getValue());
        // cartonHeader.setDoHeaderId(doHeader.getId());
        cartonHeaderDAO.save(cartonHeader);
    }

    @Override
    public String findCartonNoByWaveNum(String waveNum) {
        List<String> cartonList = cartonHeaderDAO.findCartonNoByWaveNum(waveNum);
        StringBuilder sb = new StringBuilder();
        for (String ch : cartonList) {
            if (ch != null && StringUtil.isNotEmpty(ch)) {
                sb.append(ch);
                sb.append(",");
            }
        }
        if (StringUtil.isEmpty(sb.toString())) {
            return null;
        } else {
            return sb.substring(0, sb.length() - 1);
        }
    }

    @Override
    public Long getCartonCountByWaveNum(String waveNum) {
        return cartonHeaderDAO.getCartonCountByWaveNum(waveNum);
    }

    @Override
    public List<Long> findCartonIdByWaveNum(String waveNum) {
        return cartonHeaderDAO.findCartonIdByWaveNum(waveNum);
    }

    @Override
    public List<Long> findCartonIdByDoId(List<Long> doIds) {
        return cartonHeaderDAO.findCartonIdByDoId(doIds);
    }

    @Override
    public List<Long> findCartonIdByDoId(List<Long> doIds, String packageType) {
        return cartonHeaderDAO.findCartonIdByDoId(doIds, packageType);
    }


    @Override
    public SXSSFWorkbook createTplShippingInfoWorkBook(CartonHeaderFilter cartonHeaderFilter, Integer count) {
        Integer pageSize = 10000;
        Integer exportTimes = count % pageSize > 0 ? count / pageSize
                + 1 : count / pageSize;
        Integer startIndex = 0;
        SXSSFWorkbook workbook = new SXSSFWorkbook(pageSize);
        Sheet sheet = workbook.createSheet("物流对账");
        CellStyle style = ExcelUtil.setBorder(workbook);
        Row row = sheet.createRow(0);
        generateForTplShippingInfoHead(style, row);
        for (int j = 0; j <= exportTimes; j++) {
            List<Object[]> objects = cartonHeaderDAO.find4Export(cartonHeaderFilter, startIndex, pageSize);
            generateForTplShippingInfoDetail(sheet, style, objects, startIndex);
            startIndex += pageSize;
            objects.clear();
        }
        return workbook;
    }

    public static void generateForTplShippingInfoHead(CellStyle style, Row row) {
        Short index = 0;
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("发货单号"), style);
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("平台单号"), style);
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("店铺名称"), style);
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("运单号"), style);
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("配送商"), style);
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("支付方式"), style);
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("代收金额"), style);
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("发运时间"), style);
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("省"), style);
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("市"), style);
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("区"), style);
//        ExcelUtil.createCell(row, index++, new XSSFRichTextString("收货地址"), style);
//        ExcelUtil.createCell(row, index++, new XSSFRichTextString("收货人"), style);
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("重量"), style);
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("预估运费"), style);
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("买家备注"), style);
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("卖家备注"), style);
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("复核人"), style);
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("商品数量"), style);
        ExcelUtil.createCell(row, index++, new XSSFRichTextString("商品明细"), style);
    }

    public static void generateForTplShippingInfoDetail(org.apache.poi.ss.usermodel.Sheet sheet, CellStyle style, List<Object[]> objects, Integer startIndex) {
        Row row;
        for (int i = 0; i < objects.size(); i++) {
            Object[] obj = objects.get(i);
            row = sheet.createRow(startIndex + i + 1);
            short index = 0;
            ExcelUtil.createCell(row, index++, new XSSFRichTextString(StringUtils.defaultString((String) obj[17])),
                    style);
            ExcelUtil.createCell(row, index++, new XSSFRichTextString(StringUtils.defaultString((String) obj[0])), style);
            ExcelUtil.createCell(row, index++, new XSSFRichTextString((String) obj[1]), style);
            ExcelUtil.createCell(row, index++, new XSSFRichTextString((String) obj[2]), style);
            ExcelUtil.createCell(row, index++, new XSSFRichTextString((String) obj[3]), style);
            ExcelUtil.createCell(row, index++, new XSSFRichTextString((String) obj[11]), style);
            ExcelUtil.createCell(row, index++, new XSSFRichTextString(((BigDecimal) obj[12]).toString()), style);
            ExcelUtil.createCell(row, index++, new XSSFRichTextString(DateUtil.convertTsToStrWithSecs((Timestamp)
                    obj[4])), style);
            ExcelUtil.createCell(row, index++, new XSSFRichTextString((String) obj[5]), style);
            ExcelUtil.createCell(row, index++, new XSSFRichTextString((String) obj[6]), style);
            ExcelUtil.createCell(row, index++, new XSSFRichTextString((String) obj[7]), style);
//            ExcelUtil.createCell(row, index++, new XSSFRichTextString((String) obj[8]), style);
//            ExcelUtil.createCell(row, index++, new XSSFRichTextString((String) obj[18]), style);
            ExcelUtil.createCell(row, index++, new XSSFRichTextString(((BigDecimal) obj[9]).toString()), style);
            if (obj[10] != null) {
                ExcelUtil.createCell(row, index++, new XSSFRichTextString(((BigDecimal) obj[10]).toString()), style);
            } else {
                ExcelUtil.createCell(row, index++, new XSSFRichTextString(""), style);
            }
            ExcelUtil.createCell(row, index++, new XSSFRichTextString((String) obj[13]), style);
            ExcelUtil.createCell(row, index++, new XSSFRichTextString((String) obj[14]), style);
            ExcelUtil.createCell(row, index++, new XSSFRichTextString((String) obj[15]), style);
            ExcelUtil.createCell(row, index++, new XSSFRichTextString(((BigDecimal) obj[16]).toString()), style);
            ExcelUtil.createCell(row, index++, new XSSFRichTextString((String) obj[19]), style);
        }
        int[] colWidth = {100, 100, 200, 150, 100, 150, 150, 150, 100, 100, 100, 300, 100, 100, 100, 100, 100, 100, 100,200};
        for (int col = 0; col < colWidth.length; col++) {
            sheet.setColumnWidth((short) col, (short) (37 * colWidth[col]));//37为像素比
        }
    }

    @Override
    public Integer findCount4Export(CartonHeaderFilter cartonHeaderFilter) {
        return cartonHeaderDAO.findCount4Export(cartonHeaderFilter);
    }

    @Override
    @Transactional
    public void createTplShippingInfoExcel() throws IOException {
        String parentFileName = Config.get(Keys.System.excel_path, Config.ConfigLevel.GLOBAL);
        File parentFile = org.springframework.util.ResourceUtils.getFile(parentFileName);
        if (!parentFile.exists()) {
            parentFile.mkdirs();
        }
        String fileName = Config.get(Keys.System.excel_fileName_pwd, Config.ConfigLevel.TENANT) + ParamUtil.getCurrentWarehouseId() +
                DateUtil.dateToString(DateUtil.getMonthDate(DateUtil.getNowTime(), 1, -1), "yyyy-MM") + ".xlsx";
        File file = new File(parentFile, fileName);
        if (file.exists()) {
            return;
        }
        CartonHeaderFilter cartonHeaderFilter = new CartonHeaderFilter();
        cartonHeaderFilter.getOrderByMap().put("doHeader.shipTime", "asc");
        cartonHeaderFilter.setDoType(Constants.DoType.SELL.getValue());
        cartonHeaderFilter.setDoStatus(Constants.DoStatus.ALL_DELIVER.getValue());
        cartonHeaderFilter.setDoShipTimeFrom(DateUtil.getMonthDate(DateUtil.getNowTime(), 1, -1));
        cartonHeaderFilter.setDoShipTimeTo(DateUtil.getMonthDate(DateUtil.getNowTime(), 1, 0));
        Integer count = findCount4Export(cartonHeaderFilter);
        if (count == 0) {
            return;
        }
        SXSSFWorkbook workbook = createTplShippingInfoWorkBook(cartonHeaderFilter, count);
        FileOutputStream out = new FileOutputStream(file);
        workbook.write(out);
        out.close();
    }

    @Override
    public String findChuteByCartonNo(String cartonNo) {
        if (StringUtil.isEmpty(cartonNo)) {
            throw new BusinessException("cartonNo can not be null!");
        }
        CartonHeader cartonHeader = cartonHeaderDAO.findByCartonNo(cartonNo);
        if (cartonHeader == null) {
            throw new BusinessException("carton not exists!");
        }
        return cartonHeader.getDoHeader().getCarrier().getChute();
    }

    @Override
    public List<BatchGroupDoCartonInfoDTO> findCartonAndDoInfoByWave(String waveNum,
                                                                     BatchGroupDoCartonFilter doCartonFilter) {
        if (StringUtil.isEmpty(waveNum)) {
            throw new DeliveryException(DeliveryException.WAVE_NO);
        }
        return cartonHeaderDAO.findCartonAndDoInfoByWave(waveNum, doCartonFilter);
    }

    @Override
    public List<BatchGroupDoCartonInfoDTO> findDoAndCartonInfoByWave(String waveNum,
                                                                     BatchGroupDoCartonFilter doCartonFilter) {
        if (StringUtil.isEmpty(waveNum)) {
            throw new DeliveryException(DeliveryException.WAVE_NO);
        }
        return cartonHeaderDAO.findDoAndCartonInfoByWave(waveNum, doCartonFilter);
    }

    @Override
    public List<CartonHeader> findCartons4AutoLoad(Long warehouseId, Integer maxNum) {
        String strDoTypes = Config.getFmJson(Keys.Delivery.auto_load_and_deliver_cfg, Config.ConfigLevel.WAREHOUSE, AutoLoadAndDeliverCfg.doTypes);
        List<String> doTypeList;
        if (StringUtil.isEmpty(strDoTypes)) {
            doTypeList = Lists.newArrayList(DoType.SELL.getValue());
        } else {
            doTypeList = Lists.newArrayList(strDoTypes.trim().split(","));
        }
        List<CartonHeader> cartonList = cartonHeaderDAO.findCartons4AutoLoad(doTypeList, warehouseId, maxNum);
        return cartonList;
    }
    @Override
    public PrintStatus getCartonPrintStatusInWave(Long waveId) {
        return cartonHeaderDAO.getCartonPrintStatusInWave(waveId);
    }

    @Override
    @Transactional
    public void setCartonPrinted(List<Long> cartonIds) {
        cartonHeaderDAO.setCartonPrinted(cartonIds);
    }

    @Override
    public Boolean isAllCartonNotPrinted(List<Long> ids, String byType) {
        return cartonHeaderDAO.isAllCartonNotPrinted(ids, byType);
    }

    @Override
    public List<CartonHeader> findRLCartonsByCartonIds(List<Long> ids) {
        return cartonHeaderDAO.findRLCartonsByCartonIds(ids);
    }

    @Override
    public List<CartonHeader> findCartonsByIds(List<Long> ids) {
        return cartonHeaderDAO.findByIds(ids);
    }

    @Override
    @Transactional
    public int updateCartonWayBill(String cartonNo, String wayBill) {
        //验证运单格式
        CartonHeader c = cartonHeaderDAO.findByCartonNo(cartonNo);
        //验证订单状态
        validateForBind(c);
        if(StringUtil.isBlank(wayBill)){
            throw new DeliveryException(DeliveryException.ERROR_WAYBILL_IS_NULL);
        }
        if (c.getDoHeader().getCarrier() == null) {
            throw new DeliveryException(DeliveryException.DO_HAS_NO_CARRIER);
        }
        if (c == null || !checkFormat4Bind(c.getDoHeader().getCarrier().getUdf2(), wayBill)) {
            //运单号格式错误
            throw new DeliveryException(DeliveryException.BIND_WAY_BILL_FORMAT_ERROR);
        }
        // 记录日志,运单号绑定
        saveLog4WaybillBind(c.getDoHeader(), cartonNo, c.getWayBill(), wayBill);

        c.setWayBill(wayBill);
        if (check4AutoShip(c)) {
            autoShip(c, null);
        }
        int couount = cartonHeaderDAO.updateCartonWayBill(cartonNo, wayBill);


        if (Constants.DoStatus.ALL_DELIVER.getValue().equals(c.getDoHeader().getStatus())) {
            try {
                waybillChangeExpSrv.send(c.getDoHeader().getId(), wayBill);
            } catch (Exception e) {
                throw new DeliveryException(DeliveryException.BIND_WAY_BILL_CALL_ERP_ERROR);
            }
        }
        return couount;
    }

    /**
     * 记录日志,运单号绑定
     *
     * @param doHeader
     * @param cartonNo
     * @param wayBill
     */
    private void saveLog4WaybillBind(DeliveryOrderHeader doHeader, String cartonNo, String fmWayBill, String wayBill) {
        orderLogService.saveLog(doHeader,
                OrderLogConstants.OrderLogType.WAYBILL_BIND.getValue(),
                ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_WAYBILL_BIND, null, cartonNo, StrUtils.object2String(fmWayBill, "无"), wayBill));
    }

    private void validateForBind(CartonHeader cartonHeader) {
        if (cartonHeader == null) {
            throw new DeliveryException(DeliveryException.CARTON_NOEXIST);
        }
        if (Constants.ReleaseStatus.HOLD.getValue().equals(cartonHeader.getDoHeader().getReleaseStatus())) {
            throw new DeliveryException(DeliveryException.ERROR_CARTONNO_STATUSERROR, cartonHeader.getCartonNo());
        }
//        if (Constants.DoStatus.ALL_DELIVER.getValue().equals(cartonHeader.getDoHeader().getStatus())) {
//            throw new DeliveryException(DeliveryException.ERROR_CARTONNO_STATUSERROR, cartonHeader.getCartonNo());
//        }
    }

    private void autoShip(CartonHeader c, Date updateShipDateTime) {
        ReShipDo reShipDo = reShipDoDAO.findReShipDoByDoId(c.getDoHeaderId());
        if (reShipDo != null &&
                reShipDo.getCount().equals(0L)) {
            reShipDo.setCount(1L);
            if (updateShipDateTime != null) {
                reShipDo.setOpTime(updateShipDateTime);
            }
            reShipDoDAO.saveOrUpdate(reShipDo);
        }
    }

    /**
     * 判断称重、绑面单时是否需要触发发货逻辑
     *
     * @param c
     * @return
     */
    private boolean check4AutoShip(CartonHeader c) {
        if (Constants.DoStatus.ALL_DELIVER.getValue().equals(c.getDoHeader().getStatus())) {
            return false;
        }
        //必须先绑定运单号再发货，避免漏绑运单问题
        if (StringUtil.isEmpty(c.getWayBill()) && YesNo.YES.getValue().equals(Integer.valueOf(c.getIsWeight().toString()))) {
            //自提除外
            String carrierBySelf = SystemConfig.getConfigValue("carrier.by.self", c.getWarehouseId());
            if (StringUtil.isEmpty(carrierBySelf)) {
                return false;
            }
            String[] carrierBySelfs = carrierBySelf.split(",");
            if (!Arrays.asList(carrierBySelfs).contains(c.getDoHeader().getCarrierId().toString())) {
                return false;
            }
        }

        List<CartonHeader> cartonList = c.getDoHeader().getCartonHeaders();
        //仅此一箱，且已经称重,发货
        if (cartonList.size() <= 1 && validateWaybillAndWeight(c)) {
            return true;
        }
        //多箱情况
        for (CartonHeader carton : cartonList) {
            //只要有一箱未称重、未绑定面单，不发货
            if (carton.getCartonNo().equals(c.getCartonNo())) {
                if (!validateWaybillAndWeight(c)) {
                    return false;
                }
            } else {
                if (!validateWaybillAndWeight(carton)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 验证包箱是否称重且绑了面单
     *
     * @param cartonHeader
     * @return
     */
    private boolean validateWaybillAndWeight(CartonHeader cartonHeader) {
        if (StringUtils.isEmpty(cartonHeader.getWayBill()) ||
                StringUtil.equals(YesNo.NO.getValue().toString(), cartonHeader.getIsWeight().toString())) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 验证运单号格式
     *
     * @param configStr
     * @param wayBill
     * @return
     */
    private boolean checkFormat4Bind(String configStr, String wayBill) {
        if (StringUtils.isBlank(configStr) || !configStr.contains(";")) {
            //无配置时，不校验格式
            return true;
        }
        //箱号格式正则
        String regex = configStr.split(";")[1];
        if (StringUtils.isBlank(regex)) {
            //无配置时，不校验格式
            return true;
        }

        return Pattern.compile(regex).matcher(wayBill).find();
    }

    @Override
    @Transactional
    public void saveOrUpdate(CartonHeader cartonHeader) {
        cartonHeaderDAO.saveOrUpdate(cartonHeader);
    }

    @Override
    public String findUnLoadCartonsByLoadHeaderId(Long loadHeaderId) {
        return cartonHeaderDAO.findUnLoadCartonsByLoadHeaderId(loadHeaderId);
    }

    @Override
    public CartonHeader findCartonInfo4Binding(String cartonNo) {
        if (StringUtils.isBlank(cartonNo)) {
            throw new DeliveryException(DeliveryException.CARTON_NOEXIST);
        }
        CartonHeader carton = this.cartonHeaderDAO.findByCartonNo(cartonNo);
        if (carton == null) {
            throw new DeliveryException(DeliveryException.CARTON_NOEXIST);
        }

        String cartonStatus = carton.getStatus().trim();
        //只有已装箱状态的箱子可以绑定运单号
        if (!cartonStatus.equals(Constants.CartonStatus.PACK_OVER.getValue()) &&
                !cartonStatus.equals(Constants.CartonStatus.ALL_LOAD.getValue())) {
            throw new DeliveryException(DeliveryException.CARTON_STATUS_ERROR_BINDING);
        }
        return carton;
    }

    @Override
    public String getWayBillByDoId(DeliveryOrderHeader doHeader) {

        return doHeader.getTrackingNo();
//        //装箱完成之前，无物流单号
//        if (doHeader.getStatus().compareTo(Constants.DoStatus.ALL_CARTON.getValue()) < 0) {
//            //装箱完成之前，物流单展示上游物流单号
//            return doHeader.getTrackingNo();
//        }
//        List<CartonHeader> cartonList = this.findByDoId(doHeader.getId());
//        if (CollectionUtils.isEmpty(cartonList)) {
//            return null;
//        }
//
//        Set<String> wayBillSet = new HashSet<String>();
//        for (CartonHeader c : cartonList) {
//            if (StringUtils.isNotBlank(c.getWayBill())) {
//                wayBillSet.add(c.getWayBill());
//            }
//        }
//
//        if (CollectionUtils.isEmpty(wayBillSet)) {
//            return null;
//        }
//
//        StringBuffer wayBill = new StringBuffer();
//        for (String s : wayBillSet) {
//            wayBill.append(s).append(",");
//        }
//        wayBill = wayBill.deleteCharAt(wayBill.length() - 1);
//        return wayBill.toString();
    }

    @Override
    public String getWayBillByDoId(Long doHeaderId) {
        DeliveryOrderHeader header = deliveryOrderService.getDoHeaderById(doHeaderId);
        //装箱完成之前，无物流单号
        if (header.getStatus().compareTo(Constants.DoStatus.ALL_CARTON.getValue()) < 0) {
            return null;
        }
        List<CartonHeader> cartonList = this.findByDoId(header.getId());
        if (CollectionUtils.isEmpty(cartonList)) {
            return null;
        }

        Set<String> wayBillSet = new HashSet<String>();
        for (CartonHeader c : cartonList) {
            if (StringUtils.isNotBlank(c.getWayBill())) {
                wayBillSet.add(c.getWayBill());
            } else {
                wayBillSet.add(c.getCartonNo());
            }
        }
        if (CollectionUtils.isEmpty(wayBillSet)) {
            return null;
        }

        StringBuffer wayBill = new StringBuffer();
        for (String s : wayBillSet) {
            wayBill.append(s).append(",");
        }
        wayBill = wayBill.deleteCharAt(wayBill.length() - 1);
        return wayBill.toString();
    }

    @Override
    public DataPage<CartonHeader> queryDataPage(CartonHeaderFilter filter, int startIndex, int pageSize) {
        return cartonHeaderDAO.findRangeByFilter(filter, startIndex, pageSize);
    }

    @Override
    public List<CartonHeader> loadCartonToCalculateFreght() {
        return cartonHeaderDAO.loadCartonToCalculateFreght();
    }

    /**
     * 合单发货查询箱信息
     *
     * @return
     */
    @Override
    public CombineBindingInfo findCartonInfo4Combine(String doNo) {
        if (StringUtil.isEmpty(doNo)) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(doNo);
        if (doHeader == null) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        if (doHeader.getStatus().compareTo(Constants.DoStatus.ALL_CARTON.getValue()) < 0) {
            throw new DeliveryException(DeliveryException.COMBINE_ERROR_DO_NOT_PACK_OVER, doHeader.getDoNo());
        }
        List<DeliveryOrderHeader> doList = deliveryOrderService.qureyDoHeaderByWaveId(doHeader.getWaveId());
        Long carrierId = doHeader.getCarrierId();
        Long cartonCount = 0L;
        for (DeliveryOrderHeader d : doList) {
            if (d.getStatus().compareTo(Constants.DoStatus.ALL_CARTON.getValue()) < 0) {
                throw new DeliveryException(DeliveryException.COMBINE_ERROR_DO_NOT_PACK_OVER, d.getDoNo());
            }
            if (!carrierId.equals(d.getCarrierId())) {
                throw new DeliveryException(DeliveryException.CARTON_COMBINE_CARRIER_NOT_SAME);
            }
            cartonCount += d.getCartonHeaders().size();
        }

        CombineBindingInfo result = new CombineBindingInfo();
        result.setCartonCount(cartonCount);
        result.setOrderCount(Long.valueOf(doList.size()));
        result.setCarrierName(doHeader.getCarrier().getDistSuppCompName());
        String cfg = doHeader.getCarrier().getUdf2();
        //运单号正则
        if (StringUtil.isNotEmpty(cfg) && cfg.contains(";") && StringUtil.isNotEmpty(cfg.split(";")[1])) {
            result.setWaybillRegx(cfg.split(";")[1]);
        }
        return result;
    }


    @Override
    @Transactional
    public void combineBind(String doNo, String waybills) {
        if (StringUtil.isBlank(doNo)) {
            throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
        }
        if (StringUtil.isBlank(waybills)) {
            return;
        }
        waybills = waybills.replaceAll("\n", "");

        List<DeliveryOrderHeader> doList = deliveryOrderService.qureyDoHeaderByWaveId(deliveryOrderService
                .findDoHeaderByDoNo(doNo).getWaveId());
        List<String> waybillList = Arrays.asList(waybills.split(","));
        List<CartonHeader> cartonList = new ArrayList<CartonHeader>();
        for (DeliveryOrderHeader d : doList) {
            cartonList.addAll(d.getCartonHeaders());
        }

        int cartonCount = cartonList.size();
        int waybillCount = waybillList.size();

        CartonHeader cartonHeader;
        String waybillNo;
        for (int idx = 0; idx < cartonCount; idx++) {
            cartonHeader = cartonList.get(idx);
            waybillNo = waybillList.get(idx % waybillCount);
            cartonHeaderDAO.updateCartonWayBill(cartonHeader.getCartonNo(), waybillNo);
            saveLog4WaybillBind(cartonHeader.getDoHeader(), cartonHeader.getCartonNo(), "", waybillNo);
        }
    }

    @Override
    @Transactional
    public void updateCartonFreight(Long id, BigDecimal freight) {
        cartonHeaderDAO.updateCartonFreight(id, freight);
    }

    @Override
    public List<CartonHeader> findByWaybill(String waybill) {
        return cartonHeaderDAO.findByWaybill(waybill);
    }
    @Override
    public List<CartonHeader> findByWaybillForYouBei(String waybill) {
        return cartonHeaderDAO.findByWaybillForYouBei(waybill);
    }

    /**
     * 同步重量到菜鸟
     *
     * @param ch
     * @param weight
     */
    @Override
    @Transactional
    public void syncWeightToCainiao(CartonHeader ch, BigDecimal weight) {
        if (!PageConfig.is(ConfigKeys.IS_WEIGHT_SYNC, Config.ConfigLevel.WAREHOUSE.getValue(), true)) {
            return;
        }
        //自定义面单，return
        String useCustomWayBillScript = SystemConfig.getConfigValue(ConfigKeys.PRINT_WAYBILL_USE_CUSTOM_SCRIPT,
                ParamUtil.getCurrentWarehouseId());
        if (StringUtil.isNotEmpty(useCustomWayBillScript) && Boolean.TRUE.equals(MvelUtil.eval
                (useCustomWayBillScript, ImmutableMap.of("doHeader", (Object) ch.getDoHeader())))) {
            return;
        }
        //非菜鸟订单，return
        if (ch.getDoHeader().getCarrier().getWaybillType().equals(WaybillType.CAINIAO)) {
            cainiaoWayBillUpdate.reqeust(ch.getDoHeader(), ch);
        }
        //EMS订单，return
        if (ch.getDoHeader().getCarrier().getWaybillType().equals(WaybillType.EMS)) {
            boolean isSuccess = emsWayBillUpdateService.request(ch.getDoHeader(), ch, weight);
            if (!isSuccess) {
                throw new BusinessException("EMS更新电子面单失败：" + ch.getWayBill());
            }
        }

    }

    @Override
    public BigDecimal sumSkuUnit(Long cartonId) {
        return this.cartonDetailDAO.sumSkuUnit(cartonId);
    }

    @Override
    public List<CartonHeader> findCartonByLpnNo(String lpnNo) {
        return cartonHeaderDAO.findByLpnNo(lpnNo);
    }

    @Override
    @Transactional
    public int clearLpnNoByLpnNo(String lpnNo) {
        return cartonHeaderDAO.clearLpnNoByLpnNo(lpnNo);
    }

    @Override
    public List<CartonDetail> getCartonDetailByHeaderId(Long cartonId) {
        return cartonDetailDAO.getCartonDetailByHeaderId(cartonId);
    }

    @Override
    public List<Long> findIdList4PrintByOrderIds(List<Long> ids, Long carrierId) {
        return cartonHeaderDAO.findIdList4PrintByOrderIds(ids, carrierId);
    }

    @Override
    public List<Long> findCarrierIdList4PrintByOrderIds(List<Long> ids) {
        return cartonHeaderDAO.findCarrierIdList4PrintByOrderIds(ids);
    }
    @Override
    public Long countByDoIds(Integer  goodPass, Integer weightFlag, List<Long> doIds) {
        return cartonHeaderDAO.countByDoIds(goodPass, weightFlag, doIds);
    }

    @Override
    public DeliveryOrderHeader getDoHeaderById(Long id) {
        CartonHeader cartonHeader = cartonHeaderDAO.get(id);
        if(cartonHeader == null) {
            return null;
        }
        return cartonHeader.getDoHeader();
    }

    @Override
    @Transactional
    public void autoFixShip(Long id) {
        ReShipDo reShipDo = reShipDoDAO.get(id);
        if (reShipDo != null ){
            if(reShipDo.getCount().equals(0L)) {
                reShipDo.setCount(1L);
            }else{
                reShipDo.setCount(0L);
            }
            reShipDo.setOpTime(new Date());
            reShipDoDAO.saveOrUpdate(reShipDo);
        }
    }
}