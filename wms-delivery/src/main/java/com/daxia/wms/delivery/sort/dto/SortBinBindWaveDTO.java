package com.daxia.wms.delivery.sort.dto;

import java.io.Serializable;
import java.util.Date;

@lombok.extern.slf4j.Slf4j
public class SortBinBindWaveDTO implements Serializable {
	private static final long serialVersionUID = 4492751945364955274L;
	
	private String waveNo;
	
	private String sortBin;
	
	private String mergeArea;
	
	private String mergeLoc;
	
	private String containers;
	
	private boolean binded;
	
	private String scanedContainers;
	
	private String notScanCotainers;
	
	private String curScanContainer;
	
	private String errMsg;
	
	/**
     * 波次最早预计出库时间
     */
    private Date estDoFinishTime;

	public String getWaveNo() {
		return waveNo;
	}

	public void setWaveNo(String waveNo) {
		this.waveNo = waveNo;
	}

	public String getSortBin() {
		return sortBin;
	}

	public void setSortBin(String sortBin) {
		this.sortBin = sortBin;
	}

	public String getMergeArea() {
		return mergeArea;
	}

	public void setMergeArea(String mergeArea) {
		this.mergeArea = mergeArea;
	}

	public String getMergeLoc() {
		return mergeLoc;
	}

	public void setMergeLoc(String mergeLoc) {
		this.mergeLoc = mergeLoc;
	}

	public String getContainers() {
		return containers;
	}

	public void setContainers(String containers) {
		this.containers = containers;
	}
	
	public boolean getBinded() {
		return binded;
	}
	
	public void setBinded(boolean binded) {
		this.binded = binded;
	}

	public Date getEstDoFinishTime() {
		return estDoFinishTime;
	}

	public void setEstDoFinishTime(Date estDoFinishTime) {
		this.estDoFinishTime = estDoFinishTime;
	}

	public String getScanedContainers() {
		return scanedContainers;
	}

	public void setScanedContainers(String scanedContainers) {
		this.scanedContainers = scanedContainers;
	}

	public String getNotScanCotainers() {
		return notScanCotainers;
	}

	public void setNotScanCotainers(String notScanCotainers) {
		this.notScanCotainers = notScanCotainers;
	}

	public String getCurScanContainer() {
		return curScanContainer;
	}

	public void setCurScanContainer(String curScanContainer) {
		this.curScanContainer = curScanContainer;
	}

	public String getErrMsg() {
		return errMsg;
	}

	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}
	
}
