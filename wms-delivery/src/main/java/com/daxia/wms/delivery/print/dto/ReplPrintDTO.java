package com.daxia.wms.delivery.print.dto;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import com.daxia.wms.delivery.task.replenish.entity.ReplenishTask;


/**
 *  补货单打印数据dto
 */
@lombok.extern.slf4j.Slf4j
public class ReplPrintDTO implements Serializable {

    private static final long serialVersionUID = 7778356799255379359L;

    private String jpType;
    
    private String replHeaderNo;
    
    private Timestamp createTime;
    
    private Long skuCounts;
    
    private Long unitCounts;
    
    private Date earliestPlanShipTime;
    
    private List<ReplenishTask> replTaskList;

    
    public String getJpType() {
        return jpType;
    }

    
    public void setJpType(String jpType) {
        this.jpType = jpType;
    }

    
    public String getReplHeaderNo() {
        return replHeaderNo;
    }

    
    public void setReplHeaderNo(String replHeaderNo) {
        this.replHeaderNo = replHeaderNo;
    }

    
    public Timestamp getCreateTime() {
        return createTime;
    }

    
    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    
    public Long getSkuCounts() {
        return skuCounts;
    }

    
    public void setSkuCounts(Long skuCounts) {
        this.skuCounts = skuCounts;
    }

    
    public Long getUnitCounts() {
        return unitCounts;
    }

    
    public void setUnitCounts(Long unitCounts) {
        this.unitCounts = unitCounts;
    }
    
    public Date getEarliestPlanShipTime() {
        return earliestPlanShipTime;
    }
    
    public void setEarliestPlanShipTime(Date earliestPlanShipTime) {
        this.earliestPlanShipTime = earliestPlanShipTime;
    }


    public List<ReplenishTask> getReplTaskList() {
        return replTaskList;
    }

    
    public void setReplTaskList(List<ReplenishTask> replTaskList) {
        this.replTaskList = replTaskList;
    }
}
