package com.daxia.wms.delivery.task.repick.filter;

import java.util.Date;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

/**
 * 反拣单头信息过滤器
 */
@lombok.extern.slf4j.Slf4j
public class ReversePickHeaderFilter extends WhBaseQueryFilter  {
   
    private static final long serialVersionUID = -918605295585591361L;
    /**
     * 返拣单状态
     */
    private String rpStatus;
    /**
     * 返拣单号单号
     */
    private String rpNo;
    /**
     * 发货单号
     * 返拣单号关联到返拣任务的操作单据号，用返拣任务的单据号过滤
     */
    private String doNo;
    
	private Date fmCreateTime;
	private Date toCreateTime;
	
	private String productBarCode;
	private String productCode;
 
	private String containerNo;
			
    @Operation(fieldName = "status", operationType = OperationType.EQUAL)
	public String getRpStatus() {
		return rpStatus;
	}
	
	public void setRpStatus(String rpStatus) {
		this.rpStatus = rpStatus;
	}
	
    @Operation(fieldName = "rpNo", operationType = OperationType.EQUAL)
	public String getRpNo() {
		return rpNo;
	}
	
	public void setRpNo(String rpNo) {
		this.rpNo = rpNo;
	}
	
	
	/**
	 * @return the fmCreateTime
	 */
	@Operation(fieldName = "createdAt", operationType = OperationType.NOT_LESS_THAN, dataType = "datetime")
	public Date getFmCreateTime() {
		return fmCreateTime;
	}

	/**
	 * @param fmCreateTime the fmCreateTime to set
	 */
	public void setFmCreateTime(Date fmCreateTime) {
		this.fmCreateTime = fmCreateTime;
	}

	/**
	 * @return the toCreateTime
	 */
	@Operation(fieldName = "createdAt", operationType = OperationType.NOT_GREAT_THAN, dataType = "datetime")
	public Date getToCreateTime() {
		return toCreateTime;
	}

	/**
	 * @param toCreateTime the toCreateTime to set
	 */
	public void setToCreateTime(Date toCreateTime) {
		this.toCreateTime = toCreateTime;
	}
	
	/**
	 * @return the doNo
	 */
	@Operation(
		clause = " o.rpNo in (select retask.docOperNo from ReversePickTask retask where retask.docNo = ? )"
		, operationType = OperationType.CLAUSE)
	public String getDoNo() {
		return doNo;
	}
	/**
	 * @param doNo
	 */
	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}
	
	/**
	 * @return the productCode
	 */
    @Operation(operationType = OperationType.CLAUSE ,clause = " o.id in ( select task.docOperId from ReversePickTask task, Sku sku where task.skuId = sku.id and sku.productCode = ? )")
	public String getProductCode() {
		return productCode;
	}
	
	/**
	 * @param productCode the productCode to set
	 */
	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}
	
    @Operation(operationType = OperationType.CLAUSE ,clause = " o.id in ( select task.docOperId from ReversePickTask task, Sku sku,ProductBarcode barCode  where  barCode.skuId = sku.id and task.skuId = sku.id and barCode.barcodeLevel1 = ? ) ")
	public String getProductBarCode() {
        return productBarCode ;
    }

    public void setProductBarCode(String productBarCode) {
        this.productBarCode = productBarCode;
    }
	
	@Operation(operationType = OperationType.CLAUSE ,clause = "EXISTS(SELECT rpc.id FROM ReversePickContainer rpc WHERE rpc.containerNo = ? AND o.id = rpc.repickHeaderId AND rpc.warehouseId = :warehouseId)")
	public String getContainerNo() {
		return containerNo;
	}
	
	public void setContainerNo(String containerNo) {
		this.containerNo = containerNo;
	}
}
