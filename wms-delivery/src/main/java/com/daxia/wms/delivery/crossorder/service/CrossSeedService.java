package com.daxia.wms.delivery.crossorder.service;

import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.Pagination;
import com.daxia.wms.delivery.crossorder.dto.CrossSeedDTO;
import com.daxia.wms.delivery.crossorder.dto.CrossSeedDetailDTO;
import com.daxia.wms.delivery.crossorder.entity.CrossSeedDetail;
import com.daxia.wms.delivery.crossorder.entity.CrossSeedHeader;
import com.daxia.wms.delivery.crossorder.filter.CrossSeedFilter;

import com.daxia.wms.delivery.crossorder.entity.CrossSeedHeader;

import java.math.BigDecimal;
import java.util.List;

public interface CrossSeedService {
    List<String> findAsnForCreateAndAllocate();

    void createCrossSeedByDoList(String asnNo, List<String> doNoList);

    CrossSeedHeader getCrossSeedByAsnNo(String asnNo);

    void doAllocate(Long crossSeedHeaderId, Long asnId);

    Pagination<CrossSeedDTO> findCrossSeedInfo(CrossSeedFilter crossSeedFilter, Integer startIndex, Integer pageSize);

    CrossSeedDTO getCrossHeaderDTO(Long crossSeedHeaderId);

    Pagination<CrossSeedDetailDTO> findCrossDetailByHeaderId(Long crossSeedHeaderId);

    CrossSeedHeader getByNo(String seedNo);

    CrossSeedHeader getCrossSeedHeaderById(Long crossSeedHeaderId);

    List<CrossSeedDetail> findCrossSeedDetails(String seedNo, List<Long> skuIds, String lotatt05, String lotatt01, String lotatt02, Integer sortedFlag);

    Integer doSeed(String updateBy, BigDecimal sortQty, Long crossSeedDetailId);

    void completeSeed(String updateBy, String seedNo, String asnNo);

    CrossSeedHeader getByContainerNo(String containerNo);

    List<Long> findCanShipDoIdList();

    void ship(Long canShipDoId);

    List<CrossSeedDetail> getCrossDetailsBycontainerNo(String containerNo);

    CrossSeedDetail getCrossDetailByDetailId(Long crossDetailId);

    void updateByHeader(CrossSeedHeader crossSeedHeader);

    BigDecimal queryQtyByDoHeaderId(Long doHeaderId);
}
