#DoHeader \u51BB\u7ED3\u72B6\u6001\u5E38\u91CF
recheck.invalid.do_status=The delivery order is not currently in the picking status.
recheck.invalid.do_status.all.deliver=The delivery order has already been delivered; do not deliver it again.
recheck.invalid.do_status.all.carton=The delivery order has been packed; do not pack it again.
recheck.not.all.container.arrive=Not all containers have arrived at the packing station.
error.delivery.skuNeedSpvsnCode=There are products in the delivery order that require scanning of pharmaceutical supervision codes.
recheck.do.userInfoNotFinish=Contract phone/net card order; please fill in customer information!
recheck.product.notmatch=Error! This product does not belong to this delivery order.
recheck.product.notexist=Error! This product is not in this delivery order.
recheck.material.volumeScale.notExist=Packaging material volume ratio parameter not set.
recheck.number.exceed=Error! The picking quantity exceeds the quantity to be picked.
recheck.number.zero=The product has been fully picked.
recheck.do.finished=There are no items left to pick in the delivery order.
recheck.do.recheck.frozen=The delivery order is frozen and cannot be picked!
recheck.do.recheck.frozen.doNo=Delivery order {0} is frozen and cannot be picked!
error.delivery.packDesk.not.exist=Packing station does not exist!
recheck.do.recheck.frozen.you=The delivery order is frozen and has picking; cannot be picked!
recheck.do.recheck.frozen.wu=The delivery order is frozen and has no picking; cannot be picked!
recheck.do.recheck.lack=The delivery order is out of stock and cannot be picked!
recheck.do.recheck.cancel=The delivery order has been canceled; cannot be picked!
recheck.do.frozen.error=The delivery order is frozen, and the operation has failed!
recheck.syserror=System error, operation failed.
recheck.carton.notexist=No packing information available.
recheck.product_carton.notmatch=Product barcode does not exist in this carton!
recheck.serial_carton.notmatch=Serial number does not exist in this carton!
recheck.serial_do.notmatch=Error! The entered serial number cannot be associated with the delivery order.
recheck.serial.required=Error! Serial number scanning is required!
recheck.carton.required=Error! Carton scanning is required!
recheck.carton_do.notmatch=Error! The entered carton number cannot be associated with the delivery order.
recheck.serial.full=Error! The serial number for this carton is already full.
recheck.serial.duplicate=Error! Serial numbers cannot be added duplicately.
recheck.serial.invalid=Error! Invalid serial number.
recheck.serial_product.notmatch=Error! Serial number cannot be associated with the product.
recheck.do.notexist=Delivery order does not exist.
recheck.isSpecialCheck.RecheckBy.none=The delivery order contains special products; the second checker cannot be empty!
recheck.pktnumber.error=Packing failed! The picking quantity has been modified by another operator. Please refresh the delivery order and try again.
recheck.serial.error=Packing failed! Another operator has modified the product serial number information. Please refresh the delivery order and try again.
recheck.product.numerror=Error! Another user has modified the pending packing quantity. Please refresh the interface and repack after refreshing.
cross.recheck.product.numerror=Error! Another user has modified the pending packing quantity. Please refresh the interface and repack after refreshing.
cross.recheck.empty.cartonfail=Cross-docking recheck does not allow empty carton shipment!
cross.recheck.containerNo.notExist=Container number does not exist!
recheck.do.forzen.cartonfail=The delivery order is frozen and cannot be packed!
recheck.do.recheck.status.error=Incorrect order status; cannot perform clear operation!
recheck.product.numoverflow=Error! The picking quantity cannot be greater than the expected delivery quantity!
error.delivery.contanerNotExit=Container does not exist.
error.container.notSortContainer=Not a sorting bin; please re-enter.
error.container.docTypeNotDo=Incorrect document type bound to the sorting bin.
error.sortContainer.notBindDo=Sorting bin is not bound to a delivery order.
error.container.notBindDo=Picking box is not bound to a picking order.
error.rechek.skuNotInWaveOrCartoned=This product does not belong to this wave or has already been cartoned.
error.container.sortContainerNotExists=Sorting bin does not exist.
error.container.sortContainerBindDo=Sorting bin is already bound to a delivery order.
error.container.sortContainerNoNull=Please enter a sorting bin.
error.delivery.doBindCantainer=This delivery order is already bound to a sorting bin.
error.delivery.doNotBindSortContainer=This delivery order is not bound to a sorting bin.
error.delivery.containerBusinessStatusError=Container business status error!
error.delivery.waveNotExist=Wave does not exist!
error.delivery.WaveSts.Error=Wave status is incorrect!
error.delivery.pickContanerNotExit=Picking box does not exist.
recheck.notFindDoInWave=The DO required for this wave is either frozen or has been checked by someone else.
error.delivery.notAllSorted=The delivery order is not in a fully sorted status.
error.delivery.recheckSkuCount.LhZero=The total recheck count is less than 0.
error.delivery.hasNoDetail=The delivery order has no shipping details.
error.delivery.skuNeedSerail=There are products in the delivery order that require serial numbers.
error.delivery.skuCountNeNeed=The pending recheck quantity must match the required delivery quantity.
error.delivery.alreadyFrozen=The delivery order is already frozen!
error.delivery.doTypeError=Incorrect order type.
error.delivery.hasCallCs=This delivery order has already notified customer service. Please wait for feedback from customer service before proceeding.
error.recheck.doHasLacked=This order has already been marked as lacking or damaged.
error.delivery.doHasNoCarrier=The delivery order has no carrier assigned.
error.delivery.doHasNoCartonHNo=Unable to retrieve the main waybill number.
error.delivery.recheck.doWeightError=The weight of the delivery order should be a floating-point number greater than 0.
recheck.packMaterial.isNull=Please enter packaging material.
recheck.packMaterial.notExist=Packaging material does not exist.
recheck.innerMaterial.notExist=The inner material does not exist.
recheck.material.notPack=Please enter the correct packaging material.
recheck.scaned.packMaterial.less=The entered packaging material is less than required.
recheck.no.packMaterial=Recommended packaging material failed; please maintain packaging material data.
error.delivery.doStatusErrorCannotCancel=Incorrect order status; cannot perform cancel operation.
recheck.autoDeliveryMode.notLocked=Please lock the automatic parcel handover option first.
error.master.carrierHasNoSubNo=The carrier's sub-order numbers are insufficient. Please apply for more number resources or combine them into one box.
error.carrier.hasNoEx=The carrier has no extended information.
error.recheck.doPackedNotEq=The delivery order has been checked by someone else.
recheck.do.invocieNotBilled=Electronic invoice status not bound.
error.sf.address=SF delivery address error or out of delivery range.
error.waybill.is.null=Waybill number is empty.
delivery.sync.unknown.error=Unknown synchronization platform error.
error.delivery.doWeightError=Weighting error.
reset.pack.material.error=Resetting packaging material error.
