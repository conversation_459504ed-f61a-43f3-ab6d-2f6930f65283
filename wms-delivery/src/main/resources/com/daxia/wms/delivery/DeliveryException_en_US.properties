#Wed Aug 07 11:47:03 CST 2013
error.delivery.invoiceCodeNoDuplicated=Invoice code already exists!
error.delivery.noDataForBind=No data available for binding!
error.entruck.cartonQtyLimited=Maximum carton quantity for the loading order has been reached, packing is not allowed!
error.cartonNo.notExist=Carton number {0} does not exist, please confirm!
error.entruck.statusError=Loading order {0} has an incorrect status, please verify!
error.cartonNo.statusError=Carton number {0} has an incorrect status, please verify!
carton.qty.beyond.double.units=The carton quantity cannot exceed twice the Units quantity
error.delivery.waveHasNoInvoice=This wave does not have an invoice
error.delivery.nowIsNotRollTypeInvoiceModel=Current mode is not roll-type invoice model, cannot print roll-type invoice!
error.delivery.printerCurrentInvoiceNoError=Current invoice number on the printer is incorrect, it must be within the invoice start and end numbers!
error.delivery.printerIsNotBindInvoice=Printer is not bound to an invoice, please bind an invoice to the printer first!
error.delivery.printerIsNotAvaliable=Printer system status is not available, please check!
error.delivery.printerIsNotExist=Printer does not exist, please check!
error.delivery.printerCodeIsNull=Printer code is empty, printing is not allowed. Please fill in the printer code first!
error.delivery.waveStatusCanNotAutoPrintInvoice=Wave status is not picking complete, cannot automatically print roll-type invoices!
error.delivery.bindedInvoiceUsedUpPleaseChange=The bound invoice has been used up, please bind a new invoice for printing!
client.print.wave.version.error=The wave may have been modified or printed by another user, please confirm!
error.reversal.cartonNotExist=This carton does not exist.
pick.task.split.error=Exception in picking, please contact the administrator!
error.reversal.cartonHadHandover=This carton has already been handed over and cannot be handed over again.
error.delivery.pktPartitionNotSpecial=The picking order corresponds to a non-special product partition!
error.delivery.theDoMustAloneBigWave=Contract phone orders, real-name network card orders, and number selection orders must generate waves separately. Please check the "Contract Phone/Network Card Order" checkbox and try again!
error.exception.doStatus.lessThanAllPicked=The delivery order status is not after picking is completed
error.recheck.doIsNotPartCarton=The delivery order status is not partial packing
error.status.error=Picking order status error!
error.delivery.comfirmReplHeaderStatusError=Only replenishment orders in the Released and Replenishing status can be completed!
error.invoice.lock.noto.notexsit=The lock number or invoice code is entered incorrectly!
error.delivery.cartonCountError=The carton split quantity can only be a positive integer greater than 0
error.delivery.containerBusinessNotBind=The container is not bound to a picking order
error.delivery.skuIsPicked=Some of the products you selected have already been picked. Please query again.
info.delivery.tooManyCartonsInACar=A single handover order can only contain up to {0} packages. Please confirm!
error.delivery.wmsNotStartContainerMgnt=The current system has not enabled container management, and operations are not allowed.
error.invoice.bind.inv.printed=The invoice number has already been printed and cannot be used!
error.delivery.transDetailStatusCanNotPrint=The transfer task {0} is in the Initialization or Canceled status and cannot print or preview transfer labels!
error.invoice.bind.do.allbinded=All invoices have been bound. Please check the serial numbers!
error.delivery.notAllowDeiveryByShippedCD=The cross-docking transfer order has been shipped and cannot be shipped again.
error.repl.qtyIsNull=Replenishment quantity cannot be empty!
error.alloc.data.changed=The data has been changed. Please refresh the page.
error.invoice.invoiceNo.null=Invoice number start and end cannot be empty.
error.delivery.backordersStatusError=Order status [{0}] does not allow entry into the temporary storage area.
error.delivery.alreadyFrozen=The delivery order has already been frozen!
error.delivery.alreadyFrozen.doNo=Delivery order {0} has already been frozen!
error.delivery.maxNum=Up to 200 records can be processed!
error.delivery.invoice.write=Error writing back the invoice file
error.delivery.doIsCanceled=This order has been canceled!
error.delivery.wave.cannotDeleteFromWave.notRelease=Cannot be deleted, the delivery order is not released!
error.serial.notAllowScanByScanned=The same serial number cannot be scanned repeatedly. Please scan again.
error.delivery.not.allCartonStatus=Delivery order {0} is not fully confirmed and packed.
error.invoice.invoiceBookNull=The invoice book where the invoice number is located does not exist.
error.delivery.deliveryOrder.cannotCancel.notHold=Cannot cancel the delivery order because it is not on hold!
error.delivery.doCannotCancelLoadForStatusWrong=Order {0} cannot be deleted because its status is incorrect!
error.delivery.containerAlreadyReleased=The container has already been released and cannot be released again.
error.delivery.wrongStatus=Forced sorting can only be performed on waves in the Picking Completed status!
error.delivery.sortingInfo.Null=Sorting information is empty!
error.delivery.doDetailStatusErrorCannotForceSorting=The order detail status is incorrect, and forced sorting is not allowed.
error.delivery.pick.pickTaskNotExist=Pick task does not exist, pick confirmation operation failed!
error.delivery.noSortBin=No sorting bin found in the warehouse. Please set up a sorting bin.
error.delivery.tempLocation.typeNotLack=Temporary location {0} is not a lack location.
error.invoice.bind.header.id.noexists=Invoice ID {0} does not exist!
error.invoice.cancel.mustBeNoPrint=Invoice number {0} must be in the "Not Printed" status. Please check the page for confirmation!
error.alloc.DoAclDetail.notExist=The detail does not exist or has been modified. Please refresh the page.
error.delivery.statusError.lockLoadOrder.failed=The status of the handover order is incorrect, and locking is not allowed!
error.delivery.wmsContainerMgntFlagNull=The current system has not set a flag for enabling or disabling containers.
error.delivery.waveNotEqual=The order number does not belong to the current wave and cannot be collected for outbound.
error.delivery.locationCanNotUse=The temporary storage location is not available.
error.delivery.replTaskStatusIlleagl=Replenishment task status is incorrect.
error.invoice.bind.billno.binded=The current billing information has already been bound to invoice number {0} and cannot be bound again!
error.invoice.bind.inv.lost=The invoice number has been lost and cannot be used!
error.delivery.waveNotAllMergeIn=The wave is not fully merged yet. Container {0} has not entered the merge area, so it cannot be collected for outbound.
error.delivery.locationNotExist=Temporary storage location does not exist.
error.delivery.noLackDetail=There are no shortage details for this order!
error.delivery.doStatus.reWriteError=Delivery order status is incorrect. The order has not been shipped yet!
error.invoice.bind.billno.noexists=Invoice sequence number {0} does not exist!
error.deliver.cancelLoadHeader.failed=The handover order is not in the "Initialized" state and cannot be canceled!
error.sorting.doStatus.wrong=The delivery order is not in the "Sorting In Progress" or "Picking Completed" state, and forced sorting has failed.
error.delivery.print.doTypeError=Delivery orders of different types cannot be mixed for printing!
error.delivery.print.doType.not.wholesell=Only wholesale delivery orders can be printed with accompanying waybills.
error.sorting.gridNo.Null=Sorting grid number is empty!
error.delivery.assignNumError=The assigned quantity exceeds the required quantity!
error.delivery.assignError.isParent=Combination products cannot be assigned as parent products!
error.delivery.load.noDoByCarrierNo=There is no corresponding DO number for the carrier number '{0}' in this load order!
error.delivery.load.doCannotMixLoad=Other boxes of the DO order already exist in the handover order: {0}!
error.delivery.containerBusinessStatusError=Container business status is incorrect!
error.delivery.newContainerBusinessStatusError=New container business status is incorrect!
error.delivery.dataAlreadyChange=The data has been modified, and the operation cannot continue. Please confirm!
error.sorting.doHold=Cannot force sorting for frozen delivery orders.
error.trans.transDetailPutawayStatusErr=The task is not in the "Released" state, so it cannot be put away!
error.delivery.wave.cannotDeleteFromWave.wrongStatus=The shipping order is not in the "Allocation Completed" state, so it cannot be deleted!
error.delivery.tempLocation.typeNotRTV=Temporary location {0} is not an RTV temporary location.
error.delivery.relateLpnDataError=Incomplete or incorrect associated LPN data.
error.delivery.load.doStatusCannotLoad1=Order '{0}' has an incorrect status and cannot be handed over. Please confirm!
error.delivery.skuIsRemoveFmWave=Some of the selected products have been removed from the wave. Please refresh the query.
error.delivery.noRecordSelected=No records selected. Operation cannot be completed.
error.delivery.doTypeError=Order type is incorrect!
error.invoice.lock.book.printed=There are invoice numbers within the invoice segment: [{0}] with a status of [{1}], which cannot be bound. Please re-enter.
error.invoice.cancel.invoiceIdNoExists=Invoice number ID {0} does not exist!
exception.send.order.id.required=Error! Shipment ID is empty.
exception.send.dateformat.error=Invalid date format.
error.delivery.pktWrong=Incorrect picking ticket number entered.
error.invoice.bind.new.invoiceno.must.be.noprint=The new bound invoice number {0} must be in the "Not Printed" status!
error.delivery.waveRecommendNotSupport=Wave recommendation is not supported for this order type!
error.delivery.wholesale.need.businessCustomer=For wholesale and store sales, the customer must be the same!
error.invoice.bind.do.noinvoice=This delivery order has no invoice!
error.delivery.serialNoNotExist=Serial number does not exist or has been deleted. Please confirm.
exception.force.pick.not.lack=Delivery order {0} is not in a lack status, cannot be forcibly picked.
carton.status.error.binding=The carton has already been loaded or shipped and cannot be bound to a waybill number!
error.delivery.partitionCanNotUse=The temporary storage area where the temporary location is located is not available.
error.delivery.replRealQtyIlleaglExpect=Actual replenishment quantity cannot be greater than the expected quantity.
error.delivery.replRealQtyIlleaglStock=Actual replenishment quantity cannot be greater than the inventory quantity.
error.delivery.locCannotMixBatch=This location cannot mix batches!
error.delivery.locPackageIsB=This location does not support whole case products!
error.trans.transHeaderPutawayStatusError=Error updating the putaway status of the transfer order!
error.delivery.doGenertateWaveError=Failed to generate a wave for order {0}. Please cancel the allocation for this order.
error.delivery.stockQtyLessThanChange=Available quantity in stock '{0}' is less than the changed quantity '{1}'.
error.delivery.pick.cannotPick.notExist=The picking ticket does not exist, and the picking confirmation operation has failed!
error.delivery.backordersIsOut=The order has already been moved out of the temporary storage area!
error.checkAssign.wrongStatus=Only orders with a partially allocated status can be assigned and audited!
error.delivery.mergeTypeByPkt=The container management system is not enabled in the current system. Please go to the container inbound or container outbound page for container handling.
error.delivery.cartonNotExist=The carton '{0}' does not exist.
error.delivery.cartonNotWeighted=The carton '{0}' has not been weighed. Please confirm.
error.delivery.cartonNotBind=The carton '{0}' is not bound to a courier waybill. Please confirm.
error.trans.toLocationNotLock=The target location must be a locked location!
recheck.serial.isAlreadyUsed=The associated serial number '{0}' does not exist in the inventory data or has already been used.
error.delivery.serialNoCountNotMatch=The scanned serial number quantity is abnormal. Please check.
error.delivery.load.doStatusCannotLoad=Carton '{0}' cannot be handed over because the order associated with this carton has an incorrect status!
error.repl.statusNotRight4Print=Replenishment order {0} is not in the "Published" or "In Progress" status, so it cannot be printed, previewed, or exported!
error.repl.noPubTaskInRepl4Print=There are no replenishment tasks in the "Published" status under replenishment order {0}, so it cannot be printed, previewed, or exported!
error.delivery.comfirmReplHeaderDetailStatusError=Replenishment orders with offloading tasks cannot be completed!
exception.send.cancel.error=System error, failed to cancel the delivery order.
error.reversePickHeader.notExist=The reverse picking order does not exist, operation failed!
error.delivery.lpnNotSuitForCrossDock=The LPN number does not exist or there is no corresponding cross-docking inventory record.
error.delivery.containerBTypeWrong=The container is not bound to a picking business and cannot be consolidated. Please confirm the container number and re-enter!
error.delivery.cancelReplHeaderStatusError=Only replenishment orders in the "Published" status can be canceled!
error.delivery.pick.cannotPick.doDetailNotExist=The delivery order details associated with the picking order do not exist, and the picking confirmation operation has failed!
error.deliver.doAlreadyInWave=Waybill number {0} has already been assigned to a wave, and a new wave cannot be generated.
error.delivery.doStationNull=The station for self-delivery orders cannot be empty.
error.invoice.cancel.doStatutIsDeploy=Invoice number {0} is bound to a delivery order that has been shipped.
error.delivery.doStatusErrorCannotBoxing=Delivery order '{0}' is not in the "Packaging" or "Packaging Completed" status, so it cannot be split or merged!
boxing.error.carton.type.not.same=Whole case and loose case packages cannot be mixed and merged!
error.delivery.doSupplierDonotMatchLoadSupplier=Carton '{0}' cannot be handed over because the supplier information of this order does not match the supplier information of the handover order!
error.delivery.replTaskStatusIlleaglForCancel=Tasks that are currently being executed or have already completed replenishment cannot be canceled.
error.delivery.notmerged=The wave has not been consolidated yet.
error.delivery.mergedout=The wave has already been moved out of the consolidation area.
error.delivery.invoiceSeqError=The invoice sequence number is incorrect.
error.invoice.bind.inv.deserted=The invoice number has been invalidated and cannot be used!
error.delivery.uploadExlRowIsGreater=The number of rows in the uploaded Excel file exceeds {0}!
error.trans.skuNotExists=The transferred goods do not exist in this warehouse!
error.invoice.dataError=There is an error in the invoice information. Please contact the system administrator.
error.delivery.carton.noExist=The carton does not exist!
error.delivery.print.tempCarton.noExist=Box information is missing. Please try again later!
task.not.release.error=The task is not in the "Released" status.
error.reversePickHeader.noTask=There are no reverse picking tasks in the reverse picking order!
error.sku.noDefPicLoc=Product {0} does not have a default picking location, so replenishment tasks cannot be generated!
error.delivery.waveNotAllPicked=Wave number: {0} has not been fully picked, and consolidation out of the area is not allowed.
error.invoice.bind.old.invoiceno.must.be.print=The status of the originally bound invoice number {0} must be "Printed"!
error.delivery.waveRecommendNotConnect=The wave recommendation service cannot be connected!
error.delivery.noLocNeedFreeTimeRepl=There are no locations that require off-peak replenishment!
error.trans.transDetailNotExists=Transfer task details do not exist!
error.delivery.doStatusErrorCannotCancel=The order status is incorrect, and the clearance operation cannot be performed.
error.trans.toLocationNotExists=The actual storage location does not exist or is disabled!
error.invoice.invoiceNo.bigerThan.invoiceBookTo=Invoice number: {0} is greater than the maximum invoice number in the invoice book.
error.deliver.loadTypeNotExist=The handover type for handover order '{0}' does not exist!
error.delivery.replNumError=The offloading quantity should be greater than 0 and less than or equal to the expected quantity.
error.invoice.invoiceNos.notPrint=Invoice numbers from {1} to {2} in invoice book {0} contain invoice numbers that are not in the "Unprinted" status: {3}.
error.trans.putawayFailed=An error occurred during the putaway process, and some putaways failed. Failed location: {0}.
error.delivery.WaveNo.Error=Wave number does not exist!
error.delivery.waveNotAllSorted=The wave number is incorrect.
error.trans.transHeaderPutawayStatusErr=The transfer order is not in the "Published" or "In Progress" status and cannot be put away!
error.delivery.callCSContentToLong=The content for notifying customer service is too long!
error.invoice.invoiceNo.lessThan.invoiceBookFm=Invoice number: {0} is less than the minimum invoice number in the invoice book.
error.delivery.notRTV=Delivery order {0} is not of RTV type.
error.delivery.containerAlreadyBinded=The container is in use. Please select again.
exception.release.error.skuCode=Product: {0} has insufficient inventory and cannot be released!
exception.release.error=Order: {0} cannot be released.
error.delivery.product.notNeedSorting=The scanned product does not belong to the current wave and cannot be sorted!
error.delivery.canNotSupportCarrier=Carrier information does not exist, and waybills cannot be printed or previewed!
error.delivery.load.carrierStatusCannotLoad1=The status of the handover order is incorrect, and handover cannot continue!
error.delivery.notAllowDeiveryByCanceldCD=The cross-docking transfer order has been canceled and cannot be shipped.
error.invoice.number.duplicate=Invoice number: {0} is duplicated in the system. Please contact the system administrator!
exception.force.pick.error=Delivery order: {0} has an incorrect status and cannot be forcibly picked.
error.delivery.serialNoDuplicatedInSystem=Serial number information is duplicated in the system. Please contact the administrator for processing. Duplicate serial number: '{0}'.
error.delivery.assignNumCannotBeNegative=The assigned quantity for the order must be a number and cannot be less than 0. Please confirm!
error.repl.locationMustBeUnlock=Replenishment cannot be performed to a locked location: {0}.
error.repl.locationCannotBeEACB=Replenishment cannot be performed to a EACB type location\uFF1A{0}
error.delivery.offShelf.ReplTaskStatus.error=Replenishment order task is not in the "Published" status.
error.invoice.bind.billno.required=This DO has multiple invoicing information. Please enter the invoice number to be bound!
error.crossDock.printStatusError=Cross-docking order status is incorrect, and printing, previewing, and exporting are not allowed!
error.delivery.cannot.rollback.notHold=Rollback exception, this order is not frozen!
error.delivery.transSkuImportSizeNotExist=The maximum row configuration for import of transfer plans does not exist!
error.delivery.maxCartonCountConfigInfoNotExist=System configuration information 'Maximum number of packages per handover order' does not exist!
error.delivery.checkAssign.wrongStatus=There are combination products that have not been fully assigned, so assignment cannot be audited!
do.can.not.lack.ship=Orders cannot be shipped as partial shipments are not allowed!
error.delivery.pick.cannotPick.wrongStatus=Picking order status is incorrect, and the picking confirmation operation failed!
error.delivery.waveNotExist=The wave does not exist!
error.delivery.cartonNoIsNull=The carton number is empty!
error.delivery.doHasNotLack=The order has no shortage details!
exception.send.release.error=System error, release of the delivery order failed.
error.delivery.load.carrierNoIsLoaded=Carton/Plate number '{0}' cannot be handed over because it has already been handed over!
error.delivery.mergeTypeByContainer=Container management is enabled in the current system. Please go to the container collection area or container unloading area to collect.
error.delivery.WaveSts.Error=Wave status is incorrect!
error.sku.noAllocRule=Product {0} has no allocation rule and cannot generate replenishment tasks!
error.delivery.merge.canNotRease=The current wave has not been collected or has been collected from the collection area, so it does not need to be released.
error.delivery.deliveryOrder.cannotCancel.notExist=Cannot cancel the delivery order, the delivery order does not exist!
error.delivery.repTaskNoNotEquals=The replenishment task's replenishment order number is inconsistent!
error.delivery.skuIsBinded=The product is already bound to a container and cannot be bound again!
exception.send.rollback.error=System error, rollback failed!
error.delivery.cancelAssign.failed.pktIsNull=Failed to cancel assignment, assignment details do not exist!
error.delivery.invoiceStatusError=Invoice status is incorrect or the invoice number does not exist.
error.serial.notExistOrIsAlreadyUsed=The serial number does not exist or has already been used.
error.delivery.waveIsSortted=The wave has already been sorted.
error.delivery.containerHasBind=The container is in use, using order: {0}
error.delivery.system=System error occurred, please contact the system administrator!
error.delivery.idStock.notExist=The write-back information corresponding to the order does not exist!
info.delivery.loadDetaiBeenRevoed=The packaging for order '{0}' with carton '{1}' has been deleted, {2}/{3}.
error.delivery.loadIsLoadingCannotUpdate=The handover order already has handover details, so it cannot be modified!
error.delivery.containerIsEmpty=The container is empty and cannot be collected into the collection area.
error.mergeIn.wave.hasNoSortingBin=Wave {0} has no sorting bins.
error.delivery.carrierIsNotExist=Carrier information for DO order does not exist!
error.invoice.bind.billno.dup=Data error: The current invoice number corresponds to multiple invoicing information! Please contact the administrator for processing!
error.delivery.serialNoDuplicated=Serial number is duplicated.
error.delivery.cartonAlreadyBoundLpn=Carton '{0}' is already bound to a pallet. Please unbind it before performing the operation!
error.delivery.cancelDo.hasRKTask=There are still returns picking tasks pending for this delivery order, so it cannot be canceled!
error.delivery.cannotFrozen=Only orders from initialization to packaging completion can be frozen.
error.delivery.cannotFrozen.doNo=Order {0} can only be frozen from initialization to packaging completion.
error.delivery.cannotFrozen.carton=Delivery orders that have been packaged cannot be frozen.
error.delivery.noMerge=The wave has not entered the collection area.
error.reversePicktask.locationNotExist=Actual returns picking location does not exist!
error.delivery.print.noConfigOption=Please configure the system printing parameters first!
error.invoice.lock.nos.notsamebook=Invoice start and end numbers do not belong to the same invoice book!
error.delivery.carton.stationNoIsNull=Distribution station cannot be empty!
error.delivery.pick.cannotPick.waveDetailNotExist=Wave details do not exist, picking confirmation operation failed!
error.delivery.beforeDeliveryShouldScannedAllLpn=All LPNs must be scanned before delivery.
error.delivery.containerTypeNotMatch=Container type does not match.
error.delivery.pick.cannotPick.pickTaskNotExist=Picking task does not exist, picking confirmation operation failed!
error.delivery.locAreNotReturnLocation=During transfer, only products can be picked to the RETURN location! (Non-RETURN location: '{0}')
error.reversePicktask.resonIsNull=Exception reason cannot be empty!
error.delivery.containerBStatusWrong=Container business status is incorrect, please enter the container number again!
error.delivery.SortingBinNo.MustLog=Sorting bin must be registered.
error.delivery.doNotExist=The delivery order does not exist, the operation cannot be completed!
clear.carton.num.error=Clear carton details quantity error!
cross.detail.not.exist=Cross-docking details do not exist!
error.delivery.cancelAssign.failed.inWave=The delivery order has already been assigned to a wave and cannot be canceled.
error.delivery.notAllowDeiveryByZeroSku=Delivery quantity cannot be 0.
error.postSerialToDTSError=Error synchronizing serial numbers to DTS! Please contact the administrator!
error.delivery.deliveryOrder.cannotCancel.notinit=Unable to cancel the delivery order, the delivery order is not in the initialized state!
error.delivery.load.doTypeError=Carton '{0}' cannot be handed over because the order type associated with the carton does not match the handover order type!
carrier.donot.match.load.carrier=The carrier in the order does not match the carrier in the handover!
error.line.donot.match.load.line=The line in the order does not match the line in the handover!
loadHeader.status.error.when.unload=Handover status error, cannot cancel loading!
ship.confirm.status.error=Handover status error, cannot ship!
error.ship.confirm.order.hd=Order {0} is frozen and cannot be shipped!
error.ship.confirm.multi.carton=Multiple cartons missing: {0}
error.lpnCartonBindingService.070=The carton has already been handed over and cannot be unbound from LPN '{0}'!
error.invoice.cancel.noBookInForInvoiceNo=Invoice number {0} has no corresponding invoice book information!
error.delivery.sendMailError=Error sending email!
error.serial.allHasScanned=Serial numbers have all been scanned.
error.delivery.docNoNotExit=Document number does not exist, please enter it again.
error.delivery.repllpnNoNull=Transit container cannot be empty!
error.invoice.bind.invoice.noexists=Invoice number {0} does not exist!
error.delivery.frozenReazen.null=Reason for freezing cannot be empty.
error.delivery.cartonNumCanNotEmpty=Carton number cannot be empty!
error.wave.not.merged.out=The wave has not been merged for picking and cannot be sorted!
error.delivery.replTaskQtyIlleagl=Incorrect replenishment quantity!
error.invoice.bind.billno.dbnull=Invoice serial number is incorrect, please contact the system administrator for assistance!
error.delivery.waveIsAllPicked=The wave has been fully picked.
error.lpnCartonBindingService.060=Carton '{0}' belongs to a different destination warehouse than the first carton!
error.delivery.SortingBinNo.Error=Sorting bin number is incorrect or has been deactivated.
error.delivery.doIsHoldCannotLoad=Carton '{0}' cannot be handed over because the order associated with the carton is already frozen!
error.delivery.doTranInWHDonotMatchLoadTranInWH=Carton '{0}' cannot be handed over because the warehouse transfer information of the order does not match the handover order's warehouse transfer information!
error.delivery.locCannotMixSku=This location cannot store mixed SKUs!
error.delivery.notAllowDeiveryByScannedLpn=The LPN associated with the cross-docking shipment has already been shipped, and cannot be shipped again.
error.delivery.waveHasOnePkt=There is only one picking order in this wave, and there is no need to consolidate shipments.
error.lpnCartonBindingService.050=Carton '{0}' belongs to a different supplier than the first carton!
error.trans.transPutawayCallInterfaceError=An error occurred when calling the interface for putaway, putaway failed.
error.delivery.wave.cannotDeleteFromWave.notExist=Cannot delete, the delivery order does not exist.
error.delivery.pick.cannotPick.doNotExist=The associated delivery order does not exist, and the picking confirmation operation has failed.
error.delivery.print.dataIsEmpty=Print data is empty!!!
error.delivery.print.waveDoCountLeMax=The number of orders in the wave exceeds the maximum limit {0}.
error.delivery.doStatus.notReWrite=Delivery order status is incorrect, the order has not been written back!
error.lpnHasScanned.notRelatedCrossDock=This LPN number cannot be associated with this delivery order number or has already been scanned.
error.delivery.exceptionStateMustBeToCallCS=The exception state of the delivery order should be "Pending Notification to Customer Service"!
error.delivery.pktNo=Picking order number cannot be empty.
error.reversePicktask.qtyIlleagl=Actual return quantity is illegal.
error.delivery.wave.sortingBin.Error=This wave has already been sorted in a different sorting bin.
error.delivery.doSortRingNotSame=The selected DOs are not on the same sorting ring.
error.delivery.contanerNotExit=Container does not exist.
error.delivery.backordersTypeError=Only "Normal Shipment" orders are allowed to be placed in temporary storage.
error.serial.notAllowNull=Serial number cannot be empty.
error.lpnCartonBindingService.040=Carton '{0}' has a different DO type than the first carton!
error.first.pleaseScanLpn=Please scan the LPN number first.
error.delivery.doCannotAssign=The order is not in the initialized, allocation in progress state, or has already been frozen!
error.doPrint.noonFlagTimeZoneFormatError=The "noon" flag parameter is not configured or is in the wrong format (correct format is like 20:15-10:00).
error.delivery.rtvCanNotChangeSupplier=Return order cannot change the supplier!
error.delivery.needCancel=The order has been canceled or will be canceled, and further operations are not possible!
error.delivery.waveHasOneContainer=No need to enter the area, consolidation is completed.
error.delivery.doTypeNotEqualError=Order types do not match!
error.alloc.picTask.notExist=Picking task does not exist or has been modified, please refresh the page.
error.deliver.loadHeader.notExist=Delivery order does not exist, cannot be canceled!
error.cancelAssign.wrongStatus=Only partially allocated or fully allocated delivery orders can be canceled.
error.delivery.doStatusErrorCannotPrintFace=Please print the carton label after packing is completed.
error.delivery.loadHeaderCapacityConfigNotExist=Maximum package quantity parameter for automatic loading is not configured, please contact the administrator.
error.delivery.cartonNumCanNotSame=Cannot select the same carton number!
error.lpnCartonBindingService.031=Carton '{0}' has a different carrier than the first carton!
error.lpnCartonBindingService.030=Carton '{0}' has a different delivery station than the currently selected delivery station!
error.invoice.bind.do.status.not.be.deploy.cancel=DO number {0} cannot be in the "Shipped" or "Canceled" status!
error.delivery.cancelAssign.failed.notEnoughStock=Insufficient inventory, cannot be canceled!
error.delivery.notBindReady=Order '{0}' is not fully bound to a pallet, please check!
error.delivery.waveNo=Wave number cannot be empty.
error.delivery.invoiceHasBeenBoundError=Invoice information with ID {0} has already been bound to invoice number {1}, and cannot be bound again.
error.delivery.cancelAssign.failed=Canceling allocation failed!
error.checkAssign.wrongType=Sales type delivery orders cannot undergo allocation review.
error.delivery.doNoIsNull=DO number is empty!
error.delivery.serialNoNotMatch=The system already has this serial number and it does not match the selected product, please confirm.
error.pick.wrongDodetailCount=The picked quantity in the delivery order details is greater than the allocated quantity, picking failed!
error.invoice.invoiceNoFmBigerThanTo=The ending invoice number cannot be smaller than the starting number.
error.delivery.carton.noStationInfo=Delivery station is not assigned!
error.delivery.rtv.isInTemploc=Delivery order {0} is already in temporary storage location {1}.
error.lpnCartonBindingService.022=Carton '{0}' has already been handed over and cannot be bound now!
error.lpnCartonBindingService.021=Carton '{0}' does not exist!
error.lpnCartonBindingService.020=Carton '{0}' in LPN '{1}' has already been handed over, and the current operation cannot be executed!
error.delivery.notAllowDeiveryByChangedStockCD=Cross-docking data has been modified and delivery is not allowed.
error.delivery.doCountsOverload=The selected delivery order quantity exceeds the maximum value and cannot generate a wave.
error.delivery.pick.cannotPick.supNotExist=Supplier does not exist, picking confirmation operation failed!
error.delivery.defaultCutOffTimeNoExist=Default estimated delivery time does not exist!
error.invoice.bind.old.invoiceno.noexists=The originally bound invoice number {0} does not exist!
error.lpnCartonBindingService.010=The carton has already been bound to LPN '{0}'!
exception.rollback.status.error=Order {0} status is incorrect, cannot be rolled back.
error.delivery.replTaskReasonCodeIsNull=The reason code for the replenishment task is empty.
error.delivery.merged=The picking order has been consolidated.
error.delivery.crossDockNotRelatedLpn=The cross-docking delivery order is not associated with an LPN and cannot be shipped.
error.invoice.lock.nofrom.notexsit=Starting number or invoice code is entered incorrectly!
error.delivery.product.alreadySorting=The scanned product has already been sorted.
delivery.wave_merged_out=Wave number {0} has been successfully merged out.
error.trans.transHeaderPutawayVerifyErr=The warehouse transfer order has been verified and shelving is not allowed!
error.delivery.doAlreadyModifiedByOther=The order has been modified by others!
error.do.cancel.failed.notNeedCancel=Operation failed, no need to cancel the delivery order!
error.delivery.notAllowDeiveryByAllToLocal=The cross-docking transfer order is associated with cross-docking inventory that has been transferred and cannot be shipped.
error.invoice.lock.icnull=Invoice code cannot be empty!
error.delivery.doStatusErrorCannotForceSorting=The DO order status is incorrect and cannot be forcibly sorted.
error.delivery.cannot.rollback.notExist=Cannot roll back, the order does not exist!
error.delivery.doHaveNoCarton=Delivery order {0} has no associated carton.
error.delivery.waveIsNotPick=The wave is not in the waiting for sorting state, it may not have been confirmed for picking.
error.delivery.replHeaderStatusIllegal=The replenishment order status is incorrect.
error.delivery.recheckQtyInputError=Incorrect input for checking the quantity of products.
error.mergeIn.MergePartition.hasNoMergeLocation=No consolidation location under the consolidation area {0}.
error.delivery.invoiceNoIsNotEnough=Insufficient invoice quantity, please relock or select an invoice!
error.delivery.pickTaskIsCancel=Binding failed, the picking task has been canceled.
error.delivery.notAllowDeiveryByCanceldLpn=The cross-docking delivery inventory associated with this LPN has been canceled and cannot be shipped.
error.delivery.cancelReplHeaderDetailStatusError=Replenishment orders with shelving or unshelving tasks cannot be canceled!
error.delivery.replRealQtyIlleaglOffshelf=The actual replenishment quantity cannot be greater than the quantity already unshelved.
error.mergeIn.waveSlide.hasNoMergePartition=No consolidation area under wave slide {0} or no consolidation location under the consolidation area.
error.do.not.in.wave=The delivery order is no longer in this wave.
error.do.bind.container=The order is already bound to container {0}.
error.invoice.cancel.mustBePrintOrNoPrint=Invoice number {0} must be in "Not Printed" or "Printed" status, please check the page for confirmation!
error.delivery.cartonAlreadyLoaded=Parcel '{0}' has already been handed over and cannot be handed over again.
error.delivery.doStatus.notMatch=There are delivery orders that are not in the released or completed allocation state, and a wave cannot be generated.
error.delivery.containerNotAvailable=The container is not enabled and cannot be bound!
error.delivery.nowOnlySupportWaveBindContainer=Currently, only wave or delivery orders can be bound to containers.
error.pkt.print.sizeError=The number of sheets for the delivery order should be greater than 1!
error.sorting.notExist=The delivery order does not exist.
error.delivery.load.loadTypeCannotLoad=DO order type cannot be handed over!
error.delivery.doIsHold=Order '{0}' is frozen!
error.delivery.transModeClosed=Warehouse transfer mode is not enabled!
error.delivery.serialShouldAllScanned=Serial numbers for line {0} {1} are not all scanned, please scan serial numbers first.
error.reversePickHeader.add.Failed.noTask=No reverse pick tasks, failed to generate reverse pick order!
error.delivery.cartonStatusErrForLoad=Parcel '{0}' has an incorrect status and cannot be handed over!
group.order.carton.printed=Group order {0} has already printed shipping labels and cannot modify delivery information.
error.delivery.rePrintInvoiceBeforeRePrintDo=This order requires an invoice. Please reprint the invoice before reprinting the delivery order.
error.delivery.serialShouldAllScanned1=There are products with unscanned serial numbers, so packing cannot be completed. Please confirm.
error.delivery.supervisionCodeShouldAllScanned=There are products with unscanned supervision codes, so packing cannot be completed. Please confirm.
error.delivery.semiAutoWaveClosed=Currently in manual mode, automatic wave generation is not allowed.
error.delivery.semiAutoWaveOpen=Currently in automatic mode, manual wave generation is not allowed.
error.exception.doExceptionStatus.isNotRePrint=The delivery order's exception status is not "Awaiting Reprint."
error.delivery.allocateRuleError=Allocation rule error!
error.delivery.no.allocateRule=No allocation rule.
error.delivery.no.replRule=No replenishment rule.
error.exception.do.isnot.DeleteLackDetail=The reason for freezing the delivery order is not to delete out-of-stock items, so reprinting the delivery order is not allowed.
error.pick.noPhysicalPartition=Location {0} is not associated with a physical zone.
error.delivery.exceptionStatus.isNotRollBack=Only exceptions in the "Awaiting Rollback" status can be released.
error.delivery.exceptionStatus.isNotCallCs=The order's exception status is not "Awaiting Customer Service Notification."
error.exception.doIsNotHold=The delivery order is not in a frozen state.
error.delivery.status.isNotPickedPartChecked=The delivery order is not in the "Picked to Partially Checked" status.
error.import.no.po=PO number must be entered.
error.import.no.receiver=Receiver must be entered.
error.import.no.address=Shipping address must be entered.
error.import.no.tel.pho=Either phone or mobile must be entered.
error.po.exists=The PO number already exists and cannot be imported.
error.import.no.result=Failed to import DO.
error.import.no.barcode=Either product barcode or code must be entered.
error.import.barcode.not.exists=The product barcode or code does not exist.
error.import.qty.wrong=The shipped quantity must be a number greater than 0.
error.delivery.invoiceNoNotEnoughPleaseChange=This roll of invoices has run out! A total of {0} invoices were printed this time, and there are {1} invoices left to print. Please bind a new invoice for printing!
error.delivery.waveInvoicePrintStatusError=The wave invoices have been printed or partially printed!
error.delivery.sort.lackAndSortQty.lh.allocQty=The delivery order has been notified to Customer Service, and the total of the shortage quantity and sorted quantity cannot be greater than the allocation quantity.
error.delivery.rechedk.lackAndSortQty.lh.sortedQty=The delivery order has been notified to Customer Service, and the total of the shortage quantity and checked quantity cannot be greater than the allocation quantity.
error.delivery.sort.noDetailToDM=This product has already been sorted.
error.delivery.recheck.noDetailToDM=This product has already been checked or does not belong to this delivery order.
error.delivery.doIsNotPickedToSorting=The delivery order is not in the "Picked" or "Sorting" state.
error.delivery.doIsNotSortedToChecking=The delivery order is not in the "Sorted" or "Checking" state.
error.delivery.hasNoLackDetail=The delivery order has no shortage details.
error.delivery.printMixCfy=Medicine network exported delivery PDF cannot mix prescription and non-prescription drugs.
error.delivery.detailHasNoPickTask=The delivery details have no pick tasks.
error.delivery.noDoSelect4Lack=Please select the out-of-stock orders.
error.delivery.hasFindLack=The task has been generated and cannot be regenerated.
error.findLackHeader.docNoNull=Unable to generate a finding task number.
error.delivery.status.cannotCreateFindLack=Delivery order {0} is not in the Picked, Sorted, Checked, Shortage, or Damaged status.
error.delivery.expstatus.isNotToBeAnn=The delivery order {0} is not in the "To Be Announced to Customer Service" status.
error.delivery.statusLePicked=The delivery order {0} is not in the "Picked" status.
error.findLackHeader.docNoNotExist=Finding task does not exist!
error.findLackDetail.notExist=Finding task detail does not exist.
error.findLackTask.notExist=There are no finding tasks under this finding task.
error.delivery.expstatus.isNotToBeRoll=The delivery order {0} is in the "To Be Rolled Back" status.
error.recheck.doCarton.cannot.beNull=Order number, carton number, and wave number cannot be empty at the same time.
error.delivery.status.notWaitRepl=The delivery order {0} is not in the "Awaiting Replenishment" status.
error.delivery.docTypeContainerTypeNotCorrespond=Document type does not correspond to container type.
error.delivery.not.release=The container cannot be released.
error.delivery.doStatus.not.right=The order status is incorrect. Please enter it again!
error.delivery.doc.alreadyBindContainer=The order is already bound to a container. Please enter it again!
error.container.notSortContainer=This is not a sorting bin. Please enter it again.
error.container.docTypeNotDo=Incorrect document type for sorting bin binding.
error.container.notBindDo=The picking bin is not bound to a picking order.
error.rechek.skuNotInWaveOrCartoned=This product does not belong to this wave or has already been checked.
error.container.sortContainerNotExists=The sorting bin does not exist.
error.container.sortContainerBindDo=The sorting bin is already bound to a DO.
error.container.sortContainerNoNull=Please enter the sorting bin.
error.delivery.doBindCantainer=The delivery order is already bound to a sorting bin.
error.delivery.doNotBindSortContainer=The delivery order is not bound to a sorting bin.

error.delivery.doWaveExIsAlreadyExist=The wave extension data for this order already exists.
error.delivery.waveStatusLePicked=The wave status is not "Picked."
error.delivery.waveStatusLePartSorted=The wave status is not "Sorting in Progress" or later.
error.cantainer.sortContainerTypeError=Incorrect sorting bin type.
error.cantainer.newSortContainerTypeError=Incorrect new sorting bin type.
error.delivery.notaskTobeDemand=There are no tasks to be claimed.
error.delivery.sortContanerNotExit=The sorting bin does not exist.
error.delivery.oldSortContanerNotExit=The original sorting bin does not exist.
error.delivery.newSortContanerNotExit=The new sorting bin does not exist.
error.invoice.getCurOperateUserFailure=Failed to retrieve current user information. Please log in again and try the operation.
error.delivery.hasCallCs=The delivery order has already been notified to Customer Service. Please proceed after receiving feedback from Customer Service.
error.container.isNotAvaliable=The sorting bin is disabled.
error.delivery.noTaskCancel=There are no tasks to cancel.

#Recheck 
recheck.invalid.do_status=The delivery order is not currently in the checking status.
recheck.do.userInfoNotFinish=Contract phone/phone card order, please fill in customer information!
recheck.product.notmatch=Error! This product does not belong to this delivery order.
recheck.product.notexist=Error! This delivery order does not contain this product.
recheck.number.exceed=Error! The checked quantity exceeds the quantity to be checked.
recheck.number.zero=This product has been checked completely.
recheck.do.finished=There are no products to be checked in the delivery order.
recheck.do.recheck.frozen=The delivery order is in a frozen state and cannot be checked!
recheck.do.recheck.lack=The delivery order is in a shortage state and cannot be checked!
recheck.do.recheck.cancel=The delivery order has been canceled and cannot be checked!
recheck.do.frozen.error=The delivery order is in a frozen state, and the operation failed!
recheck.syserror=System error, operation failed.
recheck.carton.notexist=No packing information is available.
recheck.product_carton.notmatch=The product barcode does not exist in this carton!
recheck.serial_carton.notmatch=The serial number does not exist in this carton!
recheck.serial_do.notmatch=Error! The entered serial number cannot be associated with the delivery order.
recheck.serial_merchant.notmatch=Error! The entered serial number does not match the merchant.
recheck.serial.required=Error! Serial number scanning is required!
recheck.carton.required=Error! Carton scanning is required.
recheck.carton_do.notmatch=Error! The entered carton number cannot be associated with the delivery order.
recheck.serial.full=Error! The serial number corresponding to this carton is already full.
recheck.serial.duplicate=Error! Serial numbers cannot be added repeatedly.
recheck.serial.invalid=Error! Invalid serial number.
recheck.serial_product.notmatch=Error! The serial number cannot be associated with the product.
recheck.do.notexist=The delivery order does not exist.
recheck.pktnumber.error=Packing failed! The checked quantity has been modified by another operator. Please refresh the page and try again after requerying this delivery order.
recheck.serial.error=Packing failed! Another operator has modified the product serial number information. Please refresh the page and try again after requerying this delivery order.
recheck.product.numerror=Error! Another user has modified the expected packing quantity. Please refresh the page and repack.
cross.recheck.product.numerror=Error! Another user has modified the expected packing quantity. Please refresh the page and repack.
cross.recheck.empty.cartonfail=Cross-docking recheck does not allow empty carton shipping!
cross.recheck.containerNo.notExist=Container number does not exist!
elivery.recheck.stockIsLock=Error! This order contains discontinued products. Please freeze and return.
recheck.do.forzen.cartonfail=The delivery order is in a frozen state and cannot be packed!
recheck.do.recheck.status.error=The order status is incorrect, and the clear operation cannot be performed!
recheck.product.numoverflow=Error! The checked quantity cannot be greater than the expected shipment quantity!
error.sortContainer.notBindDo=The sorting bin is not bound to a delivery order.
error.delivery.pickContanerNotExit=The picking bin does not exist.
error.delivery.doReRoRtsErr=Interface re-invocation failed!
recheck.notFindDoInWave=The DOs for this product in this wave have been frozen or checked by someone else.
error.delivery.notAllSorted=The delivery order is not in the "Sorted" status.
error.delivery.notPicked=The delivery order is not in the "Picked" status.
error.delivery.recheckStockIsLocked=The product with code {0} in the order is discontinued!
error.delivery.recheckSkuCount.LhZero=Total checked quantity is less than 0.
error.delivery.hasNoDetail=There are no delivery details in the delivery order.
error.delivery.skuNeedSerail=There are products in the delivery order that require serial numbers.
error.delivery.skuCountNeNeed=The number of items to be checked must match the required quantity in the delivery order.
error.recheck.doHasLacked=This order has already been processed for shortages or damages.
error.delivery.doHasNoCarrier=The delivery order has no carrier.
error.delivery.doHasNoCartonHNo=Unable to obtain the waybill master number.
#Recheck
error.wave.carrierInfoMiss=Order SF main order number, origin code, and destination code cannot be empty.
error.print.cartonLabelTemplatePathNotCfg=The carton label template file path is not configured. Please check!
recheck.loginInfo.exception=Unable to retrieve user information for {0}, which may affect your KPI. Please log in again.
error.delivery.overseaDoNotAuthorized=The order has not been declared and cannot be handed over for shipment.
error.delivery.theCartonInWavePartPrinted=Some carton labels in this wave have already been printed!
error.delivery.recheck.doWeightError=The weight of the delivery order should be a positive floating-point number.
error.delivery.doStatusErrorCanNotOperate=The order status is incorrect, and this operation cannot be performed!
error.delivery.noCombinedOrder=No combined orders without waves were found in the system!
error.delivery.operateErrorWithFailNum=This operation affects a total of {0} orders, among which {1} failed.
error.delivery.doInvoiceAllPrinted=All invoices for this order have already been printed!
recheck.material.volumeScale.notExist=Packing material volume ratio parameter is not set.
recheck.packMaterial.isNull=Please enter the packing material.
recheck.packMaterial.notExist=The packing material does not exist.
recheck.innerMaterial.notExist=The inner material does not exist.
recheck.material.notPack=Please enter the correct packing material.
recheck.scaned.packMaterial.less=The entered packing material is less than required.
recheck.no.packMaterial=Packing material recommendation failed. Please maintain packing material data.

error.notAllow.lpnHasScanned=This LPN has already been scanned.
error.lpn.warehouseNotMatch=The LPN corresponds to a different target warehouse.
error.lpn.valueableFlagNotMatch=The valuable flag for this LPN is different.
productCode.error.pealseInputAgain=Incorrect product barcode. Please re-enter.
error.lpn.notScanned=There are products in LPN on line {0} that have not been scanned.
error.serial.notScanned=There are unscanned product serial numbers in LPN on line {0}.
error.proudctCode.hasScanned=The product barcode has already been scanned.
error.lpn.hasDumpNotAllowPacking=This LPN has been fully transferred to local inventory and cannot be packed.
error.code.matchManyProucts=One code, multiple products. Please enter the code again.

error.overdueRtv.onlyAuditHandlingBill=Only overdue return orders in the handling state can be audited.
error.overdueRtv.mustChoseDealWayBeforeAudit=There are overdue return orders that have not selected a handling method.

error.delivery.newContainerAndWaveNoError=Cannot find the corresponding DO based on the wave number and new sorting bin. Please confirm!
error.sortbin.not.bind.wave=Sorting bin is not bound to the wave.
error.sort.not.input.container=Please enter the picking container number.
error.sort.container.not.match.wave=The picking container does not belong to this wave.
error.sort.wave.already.checked=The wave has already been checked.
error.sort.container.already.checked=The picking container has already been checked.
error.sort.sortbin.not.bind.wave=Please fetch the wave before sorting.
error.sort.input.wave.not.match_bind=The scanned wave/picking container does not have a relationship with the sorting bin. Please confirm.
error.sort.wave.not.checked=Please check the picking container before sorting.
error.can.not.get.wave=The configuration item is closed; you cannot fetch the wave.
error.bin.sort.wave=Error in fetching the sorting wave; please fetch it again.
error.waybill.is.null=Waybill number is empty.
error.print.status=Print status error. Please confirm.
error.entruck.status.not.print=Loading order status error; printing is not allowed!
error.delivery.noCartonToLoadInTheDo=No unhandled carton information found.
error.waybill.cainiaoPrintError=Cainiao label printing error: {0}
error.waybill.cainiaoGetError=Cainiao waybill number retrieval error: {0}
error.waybill.pddGetError=Pinduoduo waybill number retrieval error: {0}
waybill.sf.main.no.error=Failed to call SF main order number!
bind.way.bill.format.error=Waybill number format error!
delivery.sync.unknown.error=Unknown exception occurred during platform synchronization!
error.receiver.addressError=Recipient address information does not exist or is incorrect; please confirm.
recheck.do.invocieNotBilled=Electronic invoice status not bound.
error.sf.address=SF delivery address error or out of delivery range.
error.print.waveIsMis=Group buying waves and non-group buying waves cannot be mixed for printing.
carton.combine.carrier.not.same=Different carriers; cannot combine shipments!
order.check.status.error=Shipping audit did not pass; cannot ship!
combine.error.do.not.pack.over=Order {0} is not packed yet; cannot proceed!
carton.weight.error.not.group.wave=Non-group buying wave; please weigh each box separately!
error.do.notice.add.doStatusError=Order has started packing and does not allow adding or deleting notifications.
bind.way.bill.call.erp.error=Error notifying the ERP system!
location.package.type.error=Different unloading methods between the target location and the actual location!
error.delivery.packageTypeError=Packaging types do not match!
error.delivery.waveHasMoreDo=Too many orders in the wave.
error.delivery.pickIsRechecked=The picking list has been rechecked.
error.delivery.print.dataStatusError=Incorrect print data status.
error.delivery.print.notWave=Order has not generated a wave and cannot be printed (Order number: {0}).
error.delivery.waveNot2B=Must be a wholesale type wave.
error.delivery.allocatedMulitLotNo=Single lot order allocated multiple lots.
dubhe.default.carrier.empty=Please configure the default carrier for the interface.
default.carrier.can.not.check=Cannot select the interface's default carrier.
do.is.all.carton=Order {0} has been packed.
do.is.waybill.sync=Order {0} has been receipted.
do.is.cod=Order {0} is Cash on Delivery (COD) but the carrier does not support COD.
error.delivery.pickerNotExist=The user you entered does not exist; please confirm and try again.
rtv.do.can.not.change.carrier=RTV orders cannot change the carrier.
error.delivery.doWeightError=Weighting error.
group.order.can.not.change.carrier=Group buying order {0} has already been placed.
error.wave.cannot.mixCarrier=Wave does not allow mixing carriers.
error.wave.cannot.mixChannel=Wave does not allow mixing channels.
error.delivery.allocateOverStock=There is over-allocated stock for the order; please cancel the allocation.
error.crossDock.statusError=Cross-docking order status error; cannot ship!
error.crossDock.stock.statusError=Cross-docking stock status error; cannot ship!
error.crossDock.stock.numberError=Cross-docking order quantity does not match stock quantity; cannot ship!
cod.multi.carton.not.allow=Cash on delivery orders cannot be unpacked!
wave.emergency.order.can.not.mix=Emergency orders and regular orders cannot be mixed in a wave!
wave.special.order.can.not.mix=Special marked orders cannot be mixed in a wave!
pick.lack.can.not.release=Pickup shortage cannot be released!
do.no.already.in=Duplicate order number!
plan.qty.error=The shipping quantity exceeds available stock or is not a multiple of the packaging quantity.
pick.partition.region.error=Picking task location, partition, and region association error.
container.pick.confirm.qty.over=Picking quantity exceeds demand; picking failed!
dot.flag.error=This product {0} does not support decimals.
dot.flag.location.error=Both source and target locations must be loose items to allow decimals.
detail.has.seed=This detail has already been partially seeded.
detail.not.all.seed=Not all details have been fully seeded.
wholesale.do.waybill.none=Wholesale order {0} has not been consolidated and printed.
waybill.image.not.exist=Waybill information does not exist!
waybill.logistics.not.exist=Waybill information does not exist!
pack.material.equal.error=Packaging material codes are the same.
carton.size.not.one.error=There are multiple carton entries for this shipment; please reset using the carton number.
wave.lock.exist.error=Only one wave generation task is allowed at a time.
waybill.logistics.fetch.error=Failed to retrieve waybill numbers!
