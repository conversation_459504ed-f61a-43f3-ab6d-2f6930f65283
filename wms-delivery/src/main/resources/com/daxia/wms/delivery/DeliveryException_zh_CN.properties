#Wed Aug 07 11:47:03 CST 2013
error.delivery.invoiceCodeNoDuplicated=\u53D1\u7968\u4EE3\u7801\u5DF2\u5B58\u5728\uFF01
error.delivery.noDataForBind=\u6CA1\u6709\u53EF\u4F9B\u7ED1\u5B9A\u7684\u6570\u636E\uFF01
error.entruck.cartonQtyLimited=\u88C5\u8F7D\u5355\u7BB1\u6570\u5DF2\u8FBE\u6700\u5927\u7BB1\u6570\uFF0C\u4E0D\u5141\u8BB8\u88C5\u7BB1\uFF01
error.cartonNo.notExist=\u7BB1\u53F7{0}\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4\uFF01
error.entruck.statusError=\u88C5\u8F7D\u5355{0}\u72B6\u6001\u9519\u8BEF\uFF0C\u8BF7\u786E\u8BA4\uFF01
error.cartonNo.statusError=\u7BB1\u53F7{0}\u72B6\u6001\u9519\u8BEF\uFF0C\u8BF7\u786E\u8BA4\uFF01
carton.qty.beyond.double.units=\u7BB1\u6570\u4E0D\u80FD\u5927\u4E8EUnits\u6570\u4E24\u500D
error.delivery.waveHasNoInvoice=\u8BE5\u6CE2\u6B21\u6CA1\u6709\u53D1\u7968
error.delivery.nowIsNotRollTypeInvoiceModel=\u5F53\u524D\u4E0D\u662F\u5377\u5F0F\u53D1\u7968\u6A21\u5F0F\uFF0C\u4E0D\u80FD\u6253\u5370\u5377\u5F0F\u53D1\u7968\uFF01
error.delivery.printerCurrentInvoiceNoError=\u6253\u5370\u673A\u5F53\u524D\u53D1\u7968\u53F7\u7801\u9519\u8BEF\uFF0C\u5FC5\u987B\u4ECB\u4E8E\u53D1\u7968\u8D77\u6B62\u53F7\u7801\u4E4B\u95F4\uFF01
error.delivery.printerIsNotBindInvoice=\u6253\u5370\u673A\u5C1A\u672A\u7ED1\u5B9A\u53D1\u7968\uFF0C\u8BF7\u5148\u7ED1\u5B9A\u53D1\u7968\u5230\u6253\u5370\u673A\uFF01
error.delivery.printerIsNotAvaliable=\u6253\u5370\u673A\u7CFB\u7EDF\u72B6\u6001\u4E3A\u4E0D\u53EF\u7528\uFF0C\u8BF7\u68C0\u67E5\uFF01
error.delivery.printerIsNotExist=\u6253\u5370\u673A\u4E0D\u5B58\u5728\uFF0C\u8BF7\u68C0\u67E5\uFF01
error.delivery.printerCodeIsNull=\u6253\u5370\u673A\u7F16\u53F7\u4E3A\u7A7A\uFF0C\u4E0D\u80FD\u6253\u5370\u3002\u8BF7\u5148\u586B\u5199\u6253\u5370\u673A\u7F16\u53F7\!
error.delivery.waveStatusCanNotAutoPrintInvoice=\u6CE2\u6B21\u72B6\u6001\u4E0D\u662F\u62E3\u8D27\u5B8C\u6210\uFF0C\u4E0D\u80FD\u81EA\u52A8\u6253\u5370\u5377\u5F0F\u53D1\u7968\uFF01
error.delivery.bindedInvoiceUsedUpPleaseChange=\u7ED1\u5B9A\u53D1\u7968\u5DF2\u7528\u5C3D\uFF0C\u8BF7\u7ED1\u5B9A\u65B0\u53D1\u7968\u8FDB\u884C\u6253\u5370\u64CD\u4F5C\uFF01
client.print.wave.version.error=\u6CE2\u6B21\u53EF\u80FD\u5DF2\u88AB\u5176\u4ED6\u7528\u6237\u4FEE\u6539\u6216\u6253\u5370\uFF0C\u8BF7\u786E\u8BA4\uFF01
error.reversal.cartonNotExist=\u8BE5\u7BB1\u53F7\u4E0D\u5B58\u5728\u3002
pick.task.split.error=\u62E3\u8D27\u5F02\u5E38\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\uFF01
error.reversal.cartonHadHandover=\u8BE5\u7BB1\u53F7\u5DF2\u4EA4\u63A5\uFF0C\u4E0D\u53EF\u91CD\u590D\u4EA4\u63A5\u3002
error.delivery.pktPartitionNotSpecial=\u62E3\u8D27\u5355\u5BF9\u5E94\u7684\u5E93\u533A\u4E0D\u662F\u7279\u6B8A\u54C1\u5E93\u533A\uFF01
error.delivery.theDoMustAloneBigWave=\u5408\u7EA6\u673A\u8BA2\u5355\u3001\u5B9E\u540D\u5236\u7F51\u5361\u8BA2\u5355\u3001\u9009\u53F7\u5165\u7F51\u8BA2\u5355\u5FC5\u987B\u5355\u72EC\u751F\u6210\u6CE2\u6B21,\u8BF7\u52FE\u9009\u201C\u5408\u7EA6\u673A/\u7F51\u5361\u8BA2\u5355\u201D\u52FE\u9009\u6846\uFF0C\u518D\u91CD\u65B0\u64CD\u4F5C\uFF01
error.exception.doStatus.lessThanAllPicked=\u53D1\u8D27\u5355\u72B6\u6001\u4E0D\u662F\u62E3\u8D27\u5B8C\u6210\u4E4B\u540E
error.recheck.doIsNotPartCarton=\u53D1\u8D27\u5355\u72B6\u6001\u4E0D\u662F\u90E8\u5206\u88C5\u7BB1
error.status.error=\u62E3\u8D27\u5355\u72B6\u6001\u9519\u8BEF\!
error.delivery.comfirmReplHeaderStatusError=\u53EA\u6709\u53D1\u5E03\u548C\u8865\u8D27\u4E2D\u72B6\u6001\u7684\u8865\u8D27\u5355\u624D\u80FD\u5B8C\u6210\!
error.invoice.lock.noto.notexsit=\u622A\u6B62\u53F7\u7801\u6216\u53D1\u7968\u4EE3\u7801\u8F93\u5165\u9519\u8BEF\!
error.delivery.cartonCountError=\u62C6\u7BB1\u6570\u53EA\u80FD\u4E3A\u5927\u4E8E0\u7684\u6574\u6570
error.delivery.containerBusinessNotBind=\u5BB9\u5668\u672A\u7ED1\u5B9A\u62E3\u8D27\u5355
error.delivery.skuIsPicked=\u60A8\u9009\u4E2D\u7684\u90E8\u5206\u5546\u54C1\u5DF2\u62E3\u8D27\uFF0C\u8BF7\u91CD\u65B0\u67E5\u8BE2
info.delivery.tooManyCartonsInACar=\u4E00\u4E2A\u4EA4\u63A5\u5355\u6700\u591A\u53EA\u5141\u8BB8\u88C5\u5165{0}\u4E2A\u5305\u88F9\uFF0C\u8BF7\u786E\u8BA4\!
error.delivery.wmsNotStartContainerMgnt=\u5F53\u524D\u7CFB\u7EDF\u672A\u542F\u7528\u5BB9\u5668\u7BA1\u7406\uFF0C\u4E0D\u5141\u8BB8\u64CD\u4F5C
error.invoice.bind.inv.printed=\u53D1\u7968\u53F7\u5DF2\u6253\u5370\uFF0C\u4E0D\u80FD\u4F7F\u7528\u8BE5\u53D1\u7968\u53F7\uFF01
error.delivery.transDetailStatusCanNotPrint=\u642C\u4ED3\u4EFB\u52A1{0}\u72B6\u6001\u4E3A\u521D\u59CB\u5316\u6216\u5DF2\u53D6\u6D88\uFF0C\u4E0D\u80FD\u6253\u5370\u3001\u9884\u89C8\u642C\u4ED3\u6807\u7B7E\uFF01
error.invoice.bind.do.allbinded=\u6240\u6709\u53D1\u7968\u90FD\u5DF2\u7ED1\u5B9A\uFF0C\u8BF7\u6838\u5BF9\u5E8F\u53F7\uFF01
error.delivery.notAllowDeiveryByShippedCD=\u8BE5\u8D8A\u5E93\u8C03\u62E8\u5355\u5DF2\u53D1\u8D27\uFF0C\u4E0D\u5141\u8BB8\u518D\u6B21\u53D1\u8D27
error.repl.qtyIsNull=\u8865\u8D27\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A\!
error.alloc.data.changed=\u6570\u636E\u5DF2\u88AB\u66F4\u6539\uFF0C\u8BF7\u5237\u65B0\u9875\u9762
error.invoice.invoiceNo.null=\u53D1\u7968\u53F7\u7801\u8D77\u6B62\u4E0D\u80FD\u4E3A\u7A7A
error.delivery.backordersStatusError=\u8BA2\u5355\u72B6\u6001\u3010{0}\u3011\uFF0C\u4E0D\u5141\u8BB8\u5165\u6682\u5B58\u4F4D
error.delivery.alreadyFrozen=\u53D1\u8D27\u5355\u5DF2\u7ECF\u51BB\u7ED3\!
error.delivery.alreadyFrozen.doNo=\u53D1\u8D27\u5355{0}\u5DF2\u7ECF\u51BB\u7ED3\uFF01
error.delivery.maxNum=\u6700\u591A\u80FD\u5904\u7406200\u6761\u8BB0\u5F55\uFF01
error.delivery.invoice.write=\u56DE\u5199\u53D1\u7968\u6587\u4EF6\u9519\u8BEF
error.delivery.doIsCanceled=\u8BE5\u8BA2\u5355\u5DF2\u53D6\u6D88\!
error.delivery.wave.cannotDeleteFromWave.notRelease=\u65E0\u6CD5\u5220\u9664\uFF0C\u53D1\u8D27\u5355\u672A\u91CA\u653E\!
error.serial.notAllowScanByScanned=\u540C\u4E00\u5E8F\u5217\u53F7\u4E0D\u80FD\u91CD\u590D\u626B\u63CF,\u8BF7\u91CD\u65B0\u626B\u63CF
error.delivery.not.allCartonStatus=\u53D1\u8D27\u5355{0}\u672A\u6838\u62E3\u88C5\u7BB1\u5B8C\u6210
error.invoice.invoiceBookNull=\u53D1\u7968\u53F7\u6240\u5728\u53D1\u7968\u8584\u4E0D\u5B58\u5728
error.delivery.deliveryOrder.cannotCancel.notHold=\u65E0\u6CD5\u53D6\u6D88\u53D1\u8D27\u5355,\u53D1\u8D27\u5355\u6CA1\u88AB\u51BB\u7ED3\!
error.delivery.doCannotCancelLoadForStatusWrong=\u8BA2\u5355\u53F7'{0}'\u65E0\u6CD5\u5220\u9664\uFF0C\u56E0\u4E3A\u8BA2\u5355\u72B6\u6001\u4E0D\u6B63\u786E\uFF01
error.delivery.containerAlreadyReleased=\u5BB9\u5668\u5DF2\u7ECF\u91CA\u653E\uFF0C\u4E0D\u5141\u8BB8\u518D\u6B21\u91CA\u653E
error.delivery.wrongStatus=\u53EA\u6709\u62E3\u8D27\u5B8C\u6210\u72B6\u6001\u7684\u6CE2\u6B21,\u624D\u80FD\u5F3A\u5236\u5206\u62E3\!
error.delivery.sortingInfo.Null=\u5206\u62E3\u4FE1\u606F\u4E3A\u7A7A\!
error.delivery.doDetailStatusErrorCannotForceSorting=\u8BA2\u5355\u660E\u7EC6\u72B6\u6001\u4E0D\u6B63\u786E\uFF0C\u65E0\u6CD5\u5F3A\u5236\u5206\u62E3
error.delivery.pick.pickTaskNotExist=\u62E3\u8D27\u4EFB\u52A1\u4E0D\u5B58\u5728,\u62E3\u8D27\u786E\u8BA4\u64CD\u4F5C\u5931\u8D25\!
error.delivery.noSortBin=\u4ED3\u5E93\u6CA1\u6709\u627E\u5230\u5206\u62E3\u67DC, \u8BF7\u8BBE\u7F6E\u5206\u62E3\u67DC
error.delivery.tempLocation.typeNotLack=\u6682\u5B58\u4F4D{0}\u7C7B\u578B\u4E0D\u662F\u7F3A\u8D27\u6682\u5B58\u4F4D
error.invoice.bind.header.id.noexists=\u5F00\u7968ID{0}\u4E0D\u5B58\u5728\uFF01
error.invoice.cancel.mustBeNoPrint=\u53D1\u7968\u53F7{0}\u5FC5\u987B\u662F\u3010\u672A\u6253\u5370\u3011\u72B6\u6001,\u8BF7\u91CD\u65B0\u67E5\u8BE2\u9875\u9762\u8FDB\u884C\u786E\u8BA4\uFF01
error.alloc.DoAclDetail.notExist=\u660E\u7EC6\u4E0D\u5B58\u5728\u6216\u5DF2\u88AB\u4FEE\u6539\uFF0C\u8BF7\u5237\u65B0\u9875\u9762
error.delivery.statusError.lockLoadOrder.failed=\u4EA4\u63A5\u5355\u72B6\u6001\u4E0D\u6B63\u786E\uFF0C\u65E0\u6CD5\u4EA4\u63A5\u9501\u5B9A\!
error.delivery.wmsContainerMgntFlagNull=\u5F53\u524D\u7CFB\u7EDF\u672A\u8BBE\u7F6E\u5BB9\u5668\u542F\u7528\u505C\u7528\u6807\u8BC6
error.delivery.waveNotEqual=\u5355\u53F7\u4E0D\u5C5E\u4E8E\u5F53\u524D\u6CE2\u6B21\uFF0C\u4E0D\u5141\u8BB8\u96C6\u8D27\u51FA\u533A
error.delivery.locationCanNotUse=\u6682\u5B58\u4F4D\u4E0D\u53EF\u7528
error.delivery.replTaskStatusIlleagl=\u8865\u8D27\u4EFB\u52A1\u72B6\u6001\u4E0D\u6B63\u786E\u3002
error.invoice.bind.billno.binded=\u5F53\u524D\u5F00\u7968\u4FE1\u606F\u5DF2\u7ECF\u7ED1\u5B9A\u5230\u53D1\u7968\u53F7\u7801{0}\uFF0C\u4E0D\u5141\u8BB8\u518D\u7ED1\u5B9A\uFF01
error.invoice.bind.inv.lost=\u53D1\u7968\u53F7\u5DF2\u9057\u5931\uFF0C\u4E0D\u80FD\u4F7F\u7528\u8BE5\u53D1\u7968\u53F7\uFF01
error.delivery.waveNotAllMergeIn=\u6CE2\u6B21\u8FD8\u672A\u96C6\u8D27\u5B8C\u6210\uFF0C\u5BB9\u5668\u53F7{0}\u8FD8\u672A\u5165\u96C6\u8D27\u533A\uFF0C\u4E0D\u80FD\u96C6\u8D27\u51FA\u533A
error.delivery.locationNotExist=\u6682\u5B58\u4F4D\u4E0D\u5B58\u5728
error.delivery.noLackDetail=\u8BE5\u8BA2\u5355\u65E0\u7F3A\u8D27\u660E\u7EC6\!
error.delivery.doStatus.reWriteError=\u53D1\u8D27\u5355\u72B6\u6001\u9519\u8BEF\uFF0C\u8BA2\u5355\u672A\u53D1\u8D27\u5B8C\u6210\!
error.invoice.bind.billno.noexists=\u5F00\u7968\u5E8F\u53F7{0}\u4E0D\u5B58\u5728\uFF01
error.deliver.cancelLoadHeader.failed=\u4EA4\u63A5\u5355\u72B6\u6001\u4E0D\u4E3A\u521D\u59CB\u5316,\u4E0D\u80FD\u53D6\u6D88\!
error.sorting.doStatus.wrong=\u53D1\u8D27\u5355\u4E0D\u662F\u5206\u62E3\u4E2D\u6216\u62E3\u8D27\u5B8C\u6210\u72B6\u6001\uFF0C\u5F3A\u5236\u5206\u62E3\u5931\u8D25
error.delivery.print.doTypeError=DO\u4E0D\u80FD\u6DF7\u7C7B\u578B\u6253\u5370\!
error.delivery.print.doType.not.wholesell=\u53EA\u6709\u6279\u53D1\u51FA\u5E93\u5355\u624D\u80FD\u6253\u5370\u968F\u8D27\u901A\u884C\u5355
error.sorting.gridNo.Null=\u5206\u62E3\u683C\u53F7\u4E3A\u7A7A\!
error.delivery.assignNumError=\u5206\u914D\u6570\u91CF\u8D85\u8FC7\u6240\u9700\u6570\u91CF\!
error.delivery.assignError.isParent=\u7EC4\u5408\u4EA7\u54C1\u7236\u4EA7\u54C1\u4E0D\u80FD\u5206\u914D\!
error.delivery.load.noDoByCarrierNo=\u8BE5\u88C5\u7BB1\u5355\u53F7'{0}'\u65E0\u5BF9\u5E94\u7684DO\u5355\u53F7\!
error.delivery.load.doCannotMixLoad=DO\u5355\u5176\u4ED6\u7BB1\u5DF2\u5B58\u5728\u4E8E\u4EA4\u63A5\u5355\uFF1A{0}\uFF01
error.delivery.containerBusinessStatusError=\u5BB9\u5668\u4E1A\u52A1\u72B6\u6001\u9519\u8BEF\uFF01
error.delivery.newContainerBusinessStatusError=\u65B0\u5BB9\u5668\u4E1A\u52A1\u72B6\u6001\u9519\u8BEF\uFF01
error.delivery.dataAlreadyChange=\u6570\u636E\u5DF2\u7ECF\u4FEE\u6539 \uFF0C\u64CD\u4F5C\u65E0\u6CD5\u7EE7\u7EED\uFF0C\u8BF7\u786E\u8BA4\!
error.sorting.doHold=\u4E0D\u80FD\u5F3A\u5236\u5206\u62E3\u51BB\u7ED3\u72B6\u6001\u7684\u53D1\u8D27\u5355
error.trans.transDetailPutawayStatusErr=\u4EFB\u52A1\u4E0D\u662F\u5DF2\u53D1\u5E03\u72B6\u6001\uFF0C\u4E0D\u80FD\u4E0A\u67B6\uFF01
error.delivery.wave.cannotDeleteFromWave.wrongStatus=\u53D1\u8FD0\u8BA2\u5355\u4E0D\u4E3A\u5206\u914D\u5B8C\u6210\u72B6\u6001,\u65E0\u6CD5\u5220\u9664\!
error.delivery.tempLocation.typeNotRTV=\u6682\u5B58\u4F4D{0}\u4E0D\u662FRTV\u6682\u5B58\u4F4D
error.delivery.relateLpnDataError=\u5173\u8054LPN\u4E0D\u5168\u6216\u5B58\u5728\u9519\u8BEF
error.delivery.load.doStatusCannotLoad1=\u8BA2\u5355'{0}'\u72B6\u6001\u4E0D\u6B63\u786E\uFF0C\u65E0\u6CD5\u4EA4\u63A5\uFF0C\u8BF7\u786E\u8BA4\!
error.delivery.skuIsRemoveFmWave=\u60A8\u9009\u4E2D\u7684\u90E8\u5206\u5546\u54C1\u5DF2\u88AB\u79FB\u9664\u6CE2\u6B21\uFF0C\u8BF7\u91CD\u65B0\u67E5\u8BE2
error.delivery.noRecordSelected=\u6CA1\u6709\u9009\u4E2D\u8BB0\u5F55,\u64CD\u4F5C\u65E0\u6CD5\u5B8C\u6210\!
error.delivery.doTypeError=\u8BA2\u5355\u7C7B\u578B\u9519\u8BEF\uFF01
error.invoice.lock.book.printed=\u53D1\u7968\u6BB5\u5185\u5B58\u5728\u53D1\u7968\u53F7\u7801\uFF1A\u3010{0}\u3011\u7684\u72B6\u6001\u4E3A\u3010{1}\u3011\uFF0C\u4E0D\u5141\u8BB8\u7ED1\u5B9A\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165\!
error.invoice.cancel.invoiceIdNoExists=\u53D1\u7968\u53F7ID{0}\u4E0D\u5B58\u5728\uFF01
exception.send.order.id.required=\u9519\u8BEF\uFF01\u53D1\u8D27\u5355Id\u4E3A\u7A7A
exception.send.dateformat.error=\u65E5\u671F\u683C\u5F0F\u4E0D\u5408\u6CD5
error.delivery.pktWrong=\u8F93\u5165\u7684\u62E3\u8D27\u5355\u53F7\u9519\u8BEF
error.invoice.bind.new.invoiceno.must.be.noprint=\u65B0\u7ED1\u5B9A\u7684\u53D1\u7968\u53F7\u7801{0}\u72B6\u6001\u5FC5\u987B\u662F\u3010\u672A\u6253\u5370\u3011\uFF01
error.delivery.waveRecommendNotSupport=\u6CE2\u6B21\u63A8\u8350\u4E0D\u652F\u6301\u8BE5\u8BA2\u5355\u7C7B\u578B\!
error.delivery.wholesale.need.businessCustomer=\u6279\u53D1\u548C\u95E8\u5E97\u9500\u552E\uFF0C\u5BA2\u6237\u5FC5\u987B\u76F8\u540C\!
error.invoice.bind.do.noinvoice=\u8BE5\u53D1\u8D27\u5355\u6CA1\u6709\u53D1\u7968\uFF01
error.delivery.serialNoNotExist=\u5E8F\u5217\u53F7\u4E0D\u5B58\u5728\u6216\u8005\u5DF2\u7ECF\u88AB\u5220\u9664\uFF0C\u8BF7\u786E\u8BA4
exception.force.pick.not.lack=\u53D1\u8D27\u5355\: {0}\u4E0D\u662F\u7F3A\u8D27\u72B6\u6001\uFF0C\u65E0\u6CD5\u5F3A\u5236\u62E3\u8D27
carton.status.error.binding=\u7BB1\u5B50\u5DF2\u88C5\u8F66\u6216\u51FA\u5E93\uFF0C\u4E0D\u80FD\u7ED1\u5B9A\u8FD0\u5355\u53F7\uFF01
error.delivery.partitionCanNotUse=\u6682\u5B58\u4F4D\u6240\u5728\u6682\u5B58\u533A\u4E0D\u53EF\u7528
error.delivery.replRealQtyIlleaglExpect=\u5B9E\u8865\u6570\u91CF\u4E0D\u80FD\u5927\u4E8E\u671F\u671B\u91CF
error.delivery.replRealQtyIlleaglStock=\u5B9E\u8865\u6570\u91CF\u4E0D\u80FD\u5927\u4E8E\u5E93\u5B58\u91CF
error.delivery.locCannotMixBatch=\u8BE5\u5E93\u4F4D\u4E0D\u80FD\u6DF7\u6279\u6B21\!
error.delivery.locPackageIsB=\u8BE5\u5E93\u4F4D\u4E0D\u652F\u6301\u6574\u4EF6\u5546\u54C1\!
error.trans.transHeaderPutawayStatusError=\u66F4\u65B0\u642C\u4ED3\u5355\u72B6\u6001\u51FA\u9519\uFF01
error.delivery.doGenertateWaveError=\u8BA2\u5355{0}\u751F\u6210\u6CE2\u6B21\u5931\u8D25,\u8BF7\u53D6\u6D88\u5206\u914D\u8BE5\u8BA2\u5355\!
error.delivery.stockQtyLessThanChange=\u5E93\u5B58\u7684\u53EF\u7528\u6570'{0}'\u5C0F\u4E8E\u53D8\u66F4\u6570'{1}'\!
error.delivery.pick.cannotPick.notExist=\u62E3\u8D27\u5355\u4E0D\u5B58\u5728,\u62E3\u8D27\u786E\u8BA4\u64CD\u4F5C\u5931\u8D25\!
error.delivery.backordersIsOut=\u8BA2\u5355\u5DF2\u51FA\u6682\u5B58\u4F4D\uFF01
error.checkAssign.wrongStatus=\u53EA\u6709\u90E8\u5206\u5206\u914D\u72B6\u6001\u7684\u53D1\u8D27\u5355\u624D\u80FD\u8FDB\u884C\u5206\u914D\u5BA1\u6838\u64CD\u4F5C\!
error.delivery.mergeTypeByPkt=\u5F53\u524D\u7CFB\u7EDF\u672A\u542F\u7528\u5BB9\u5668\u7BA1\u7406\uFF0C\u8BF7\u5230\u96C6\u8D27\u5165\u533A\u6216\u96C6\u8D27\u51FA\u533A\u9875\u9762\u96C6\u8D27
error.delivery.cartonNotExist=\u8BE5\u88C5\u7BB1'{0}'\u4E0D\u5B58\u5728
error.delivery.cartonNotWeighted=\u8BE5\u88C5\u7BB1'{0}'\u672A\u79F0\u91CD\uFF0C\u8BF7\u786E\u8BA4\u3002
error.delivery.cartonNotBind=\u8BE5\u88C5\u7BB1'{0}'\u672A\u7ED1\u5B9A\u5FEB\u9012\u8FD0\u5355\uFF0C\u8BF7\u786E\u8BA4\u3002
error.trans.toLocationNotLock=\u76EE\u6807\u5E93\u4F4D\u5FC5\u987B\u662F\u9501\u5B9A\u5E93\u4F4D\uFF01
recheck.serial.isAlreadyUsed=\u5173\u8054\u5E8F\u5217\u53F7\u4E3A{0}\u7684\u5E8F\u5217\u53F7\u5E93\u5B58\u6570\u636E\u4E0D\u5B58\u5728\u6216\u5DF2\u88AB\u4F7F\u7528
error.delivery.serialNoCountNotMatch=\u5DF2\u626B\u63CF\u5E8F\u5217\u53F7\u6570\u91CF\u5F02\u5E38\uFF0C\u8BF7\u68C0\u67E5\uFF01
error.delivery.load.doStatusCannotLoad=\u7BB1\u53F7'{0}'\u4E0D\u80FD\u4EA4\u63A5\uFF0C\u56E0\u4E3A\u8BE5\u7BB1\u5BF9\u5E94\u7684\u8BA2\u5355\u72B6\u6001\u4E0D\u6B63\u786E\!
error.repl.statusNotRight4Print=\u8865\u8D27\u5355{0}\u72B6\u6001\u4E0D\u662F\u3010\u5DF2\u53D1\u5E03\u3011\u6216\u3010\u8FDB\u884C\u4E2D\u3011\uFF0C\u4E0D\u80FD\u6253\u5370\u3001\u9884\u89C8\u6216\u5BFC\u51FA\uFF01
error.repl.noPubTaskInRepl4Print=\u8865\u8D27\u5355{0}\u4E0B\u6CA1\u6709\u3010\u5DF2\u53D1\u5E03\u3011\u72B6\u6001\u7684\u8865\u8D27\u4EFB\u52A1\uFF0C\u4E0D\u80FD\u6253\u5370\u3001\u9884\u89C8\u6216\u5BFC\u51FA\uFF01
error.delivery.comfirmReplHeaderDetailStatusError=\u5B58\u5728\u5DF2\u4E0B\u67B6\u4EFB\u52A1\u7684\u8865\u8D27\u5355\u4E0D\u80FD\u5B8C\u6210\!
exception.send.cancel.error=\u7CFB\u7EDF\u9519\u8BEF\uFF0C\u53D6\u6D88\u53D1\u8D27\u5355\u5931\u8D25
error.reversePickHeader.notExist=\u8FD4\u62E3\u5355\u4E0D\u5B58\u5728,\u64CD\u4F5C\u5931\u8D25\!
error.delivery.lpnNotSuitForCrossDock=\u8BE5LPN\u53F7\u4E0D\u5B58\u5728\u6216\u4E0D\u5B58\u5728\u5BF9\u5E94\u7684\u8D8A\u5E93\u5E93\u5B58\u8BB0\u5F55
error.delivery.containerBTypeWrong=\u5BB9\u5668\u4E0D\u662F\u7ED1\u5B9A\u62E3\u8D27\u4E1A\u52A1\uFF0C\u4E0D\u80FD\u96C6\u8D27\uFF0C\u8BF7\u786E\u8BA4\u5BB9\u5668\u53F7\u518D\u91CD\u65B0\u8F93\u5165\uFF01
error.delivery.cancelReplHeaderStatusError=\u53EA\u6709\u53D1\u5E03\u4E2D\u7684\u8865\u8D27\u5355\u624D\u80FD\u53D6\u6D88\!
error.delivery.pick.cannotPick.doDetailNotExist=\u62E3\u8D27\u5355\u5173\u8054\u7684\u53D1\u8D27\u5355\u660E\u7EC6\u4E0D\u5B58\u5728,\u62E3\u8D27\u786E\u8BA4\u64CD\u4F5C\u5931\u8D25\!
error.deliver.doAlreadyInWave=\u5355\u53F7\u4E3A{0}\u7684\u8FD0\u5355\u5DF2\u8DD1\u6CE2\u6B21,\u4E0D\u80FD\u751F\u6210\u6CE2\u6B21
error.delivery.doStationNull=\u81EA\u914D\u9001\u8BA2\u5355\u7684\u7AD9\u70B9\u4E0D\u80FD\u4E3A\u7A7A
error.invoice.cancel.doStatutIsDeploy=\u53D1\u7968\u53F7{0}\u7ED1\u5B9A\u7684do\u5355\u5DF2\u51FA\u5E93
error.delivery.doStatusErrorCannotBoxing=\u53D1\u8D27\u5355'{0}'\u4E0D\u5728\u88C5\u7BB1\u4E2D\u6216\u8005\u88C5\u7BB1\u5B8C\u6210\u72B6\u6001\uFF0C\u65E0\u6CD5\u62C6\u62FC\u7BB1\uFF01
boxing.error.carton.type.not.same=\u6574\u6563\u4EF6\u5305\u88F9\u4E4B\u95F4\u4E0D\u80FD\u76F8\u4E92\u62FC\u7BB1\uFF01
error.delivery.doSupplierDonotMatchLoadSupplier=\u7BB1\u53F7'{0}'\u65E0\u6CD5\u4EA4\u63A5\uFF0C\u56E0\u4E3A\u8BE5\u8BA2\u5355\u7684\u4F9B\u5E94\u5546\u4FE1\u606F\u4E0E\u4EA4\u63A5\u5355\u7684\u4F9B\u5E94\u5546\u4FE1\u606F\u4E0D\u4E00\u81F4\!
error.delivery.replTaskStatusIlleaglForCancel=\u5B58\u5728\u6B63\u5728\u6267\u884C\u7684\u4EFB\u52A1\u6216\u8005\u5DF2\u7ECF\u5B8C\u6210\u8865\u8D27\u7684\u4EFB\u52A1\uFF1B\u4E0D\u5141\u8BB8\u53D6\u6D88\u3002
error.delivery.notmerged=\u6CE2\u6B21\u8FD8\u672A\u96C6\u8D27\u5B8C\u6210
error.delivery.mergedout=\u6CE2\u6B21\u5DF2\u51FA\u96C6\u8D27\u533A
error.delivery.invoiceSeqError=\u53D1\u7968\u5E8F\u53F7\u4E0D\u6B63\u786E
error.invoice.bind.inv.deserted=\u53D1\u7968\u53F7\u5DF2\u4F5C\u5E9F\uFF0C\u4E0D\u80FD\u4F7F\u7528\u8BE5\u53D1\u7968\u53F7\uFF01
error.delivery.uploadExlRowIsGreater=\u4E0A\u4F20\u7684excel\u6587\u4EF6\u884C\u6570\u5927\u4E8E{0}\!
error.trans.skuNotExists=\u642C\u4ED3\u5546\u54C1\u5728\u672C\u4ED3\u5E93\u4E0D\u5B58\u5728\uFF01
error.invoice.dataError=\u5F00\u7968\u4FE1\u606F\u6709\u8BEF\uFF0C\u8BF7\u8054\u7CFB\u7CFB\u7EDF\u7BA1\u7406\u5458
error.delivery.carton.noExist=\u7BB1\u53F7\u4E0D\u5B58\u5728\!
error.delivery.print.tempCarton.noExist=\u7BB1\u4FE1\u606F\u7F3A\u5931\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5\uFF01
task.not.release.error=\u4EFB\u52A1\u4E0D\u662F\u53D1\u5E03\u72B6\u6001
error.reversePickHeader.noTask=\u8FD4\u62E3\u5355\u4E2D\u6CA1\u6709\u8FD4\u62E3\u4EFB\u52A1\!
error.sku.noDefPicLoc=\u5546\u54C1{0}\u65E0\u9ED8\u8BA4\u62E3\u8D27\u4F4D\uFF0C\u65E0\u6CD5\u751F\u6210\u8865\u8D27\u4EFB\u52A1\!
error.delivery.waveNotAllPicked=\u6CE2\u6B21\u53F7\uFF1A{0}\u672A\u62E3\u8D27\u5B8C\u6210\uFF0C\u4E0D\u80FD\u96C6\u8D27\u51FA\u533A
error.invoice.bind.old.invoiceno.must.be.print=\u539F\u7ED1\u5B9A\u7684\u53D1\u7968\u53F7\u7801{0}\u72B6\u6001\u5FC5\u987B\u662F\u3010\u5DF2\u6253\u5370\u3011\uFF01
error.delivery.waveRecommendNotConnect=\u6CE2\u6B21\u63A8\u8350\u670D\u52A1\u65E0\u6CD5\u8FDE\u63A5\!
error.delivery.noLocNeedFreeTimeRepl=\u6CA1\u6709\u5E93\u4F4D\u9700\u8981\u8FDB\u884C\u95F2\u65F6\u8865\u8D27\!
error.trans.transDetailNotExists=\u642C\u4ED3\u4EFB\u52A1\u660E\u7EC6\u4E0D\u5B58\u5728\uFF01
error.delivery.doStatusErrorCannotCancel=\u8BA2\u5355\u72B6\u6001\u4E0D\u6B63\u786E\uFF0C\u65E0\u6CD5\u6267\u884C\u6E05\u9664\u64CD\u4F5C
error.trans.toLocationNotExists=\u5B9E\u9645\u4E0A\u67B6\u5E93\u4F4D\u4E0D\u5B58\u5728\u6216\u7981\u7528\uFF01
error.invoice.invoiceNo.bigerThan.invoiceBookTo=\u53D1\u7968\u53F7\uFF1A{0}\u5927\u4E8E\u53D1\u7968\u8584\u4E2D\u6700\u5927\u53D1\u7968\u53F7
error.deliver.loadTypeNotExist=\u4EA4\u63A5\u5355'{0}'\u4EA4\u63A5\u7C7B\u578B\u4E0D\u5B58\u5728\uFF01
error.delivery.replNumError=\u4E0B\u67B6\u6570\u91CF\u5E94\u5927\u4E8E0\u4E14\u5C0F\u4E8E\u7B49\u4E8E\u671F\u671B\u6570\u91CF
error.invoice.invoiceNos.notPrint=\u53D1\u7968\u8584\:{0}\u7684\u53D1\u7968\u53F7\u4ECE{1}\u81F3{2}\u4E2D\u5B58\u5728\u4E0D\u662F\u672A\u6253\u5370\u72B6\u6001\u7684\u53D1\u7968\u53F7\:{3}
error.trans.putawayFailed=\u6267\u884C\u4E0A\u67B6\u8FC7\u7A0B\u4E2D\u51FA\u9519\uFF0C\u90E8\u5206\u4E0A\u67B6\u5931\u8D25\uFF0C\u5931\u8D25\u5E93\u4F4D\:{0}\u3002
error.delivery.WaveNo.Error=\u6CE2\u6B21\u53F7\u4E0D\u5B58\u5728\!
error.delivery.waveNotAllSorted=\u6CE2\u6B21\u53F7\u4E0D\u6B63\u786E
error.trans.transHeaderPutawayStatusErr=\u642C\u4ED3\u5355\u4E0D\u662F\u3010\u5DF2\u53D1\u5E03\u3011\u6216\u8005\u3010\u642C\u4ED3\u4E2D\u3011\u72B6\u6001\uFF0C\u4E0D\u5141\u8BB8\u4E0A\u67B6\uFF01
error.delivery.callCSContentToLong=\u901A\u77E5\u5BA2\u670D\u7684\u5185\u5BB9\u8FC7\u957F\!
error.invoice.invoiceNo.lessThan.invoiceBookFm=\u53D1\u7968\u53F7\uFF1A{0}\u5C0F\u4E8E\u53D1\u7968\u8584\u4E2D\u6700\u5C0F\u53D1\u7968\u53F7
error.delivery.notRTV=\u53D1\u8D27\u5355{0}\u4E0D\u662FRTV\u7C7B\u578B
error.delivery.containerAlreadyBinded=\u5BB9\u5668\u5728\u4F7F\u7528\u4E2D\uFF0C\u8BF7\u91CD\u65B0\u9009\u62E9
exception.release.error.skuCode=\u5546\u54C1\uFF1A{0}\u5E93\u5B58\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u91CA\u653E\uFF01
exception.release.error=\u8BA2\u5355\:{0}\u65E0\u6CD5\u91CA\u653E
error.delivery.product.notNeedSorting=\u6240\u626B\u5546\u54C1\u4E0D\u5C5E\u4E8E\u5F53\u524D\u6CE2\u6B21,\u4E0D\u80FD\u5206\u62E3\!
error.delivery.canNotSupportCarrier=\u627F\u8FD0\u5546\u4FE1\u606F\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u6253\u5370\u6216\u9884\u89C8\u8FD0\u5355\!
error.delivery.load.carrierStatusCannotLoad1=\u4EA4\u63A5\u5355\u72B6\u6001\u9519\u8BEF\uFF0C\u4E0D\u80FD\u7EE7\u7EED\u4EA4\u63A5\!
error.delivery.notAllowDeiveryByCanceldCD=\u8BE5\u8D8A\u5E93\u8C03\u62E8\u5355\u5DF2\u53D6\u6D88\uFF0C\u4E0D\u5141\u8BB8\u53D1\u8D27
error.invoice.number.duplicate=\u53D1\u7968\u53F7\:{0}\u5728\u7CFB\u7EDF\u4E2D\u6709\u91CD\u590D\uFF0C\u8BF7\u8054\u7CFB\u7CFB\u7EDF\u7BA1\u7406\u5458\uFF01
exception.force.pick.error=\u53D1\u8D27\u5355\: {0}\u72B6\u6001\u4E0D\u6B63\u786E\uFF0C\u65E0\u6CD5\u5F3A\u5236\u62E3\u8D27
error.delivery.serialNoDuplicatedInSystem=\u7CFB\u7EDF\u4E2D\u5E8F\u5217\u53F7\u4FE1\u606F\u91CD\u590D\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u5904\u7406\u3002\u91CD\u590D\u5E8F\u5217\u53F7'{0}'\u3002
error.delivery.assignNumCannotBeNegative=\u8BA2\u5355\u5206\u914D\u6570\u91CF\u5FC5\u987B\u4E3A\u6570\u5B57\u4E14\u4E0D\u80FD\u5C0F\u4E8E0\uFF0C\u8BF7\u786E\u8BA4\uFF01
error.repl.locationMustBeUnlock=\u4E0D\u80FD\u8865\u8D27\u5230\u9501\u5B9A\u5E93\u4F4D{0}
error.repl.locationCannotBeEACB=\u4E0D\u80FD\u8865\u8D27\u5230\u7EC4\u5408\u5E93\u4F4D{0}
error.delivery.offShelf.ReplTaskStatus.error=\u8865\u8D27\u5355\u4EFB\u52A1\u4E0D\u662F\u5DF2\u53D1\u5E03\u72B6\u6001
error.invoice.bind.billno.required=\u8BE5DO\u6709\u591A\u6761\u5F00\u7968\u4FE1\u606F\uFF0C\u8BF7\u8F93\u5165\u6B32\u7ED1\u5B9A\u7684\u5F00\u7968\u53F7\uFF01
error.crossDock.printStatusError=\u8D8A\u5E93\u5355\u72B6\u6001\u9519\u8BEF\uFF0C\u65E0\u6CD5\u6253\u5370\u3001\u9884\u89C8\u3001\u5BFC\u51FA\!
error.delivery.cannot.rollback.notHold=\u56DE\u9000\u5F02\u5E38\uFF0C\u8BE5\u8BA2\u5355\u672A\u51BB\u7ED3\!
error.delivery.transSkuImportSizeNotExist=\u642C\u4ED3\u8BA1\u5212\u5BFC\u5165\u6700\u5927\u884C\u6570\u914D\u7F6E\u9879\u4E0D\u5B58\u5728\uFF01
error.delivery.maxCartonCountConfigInfoNotExist=\u7CFB\u7EDF\u914D\u7F6E\u4FE1\u606F'\u4EA4\u63A5\u5355\u6700\u5927\u5305\u88F9\u6570'\u4E0D\u5B58\u5728\!
error.delivery.checkAssign.wrongStatus=\u6709\u7EC4\u5408\u4EA7\u54C1\uFF0C\u672A\u5168\u90E8\u5206\u914D\u5B8C\u6210\uFF0C\u4E0D\u80FD\u5206\u914D\u5BA1\u6838\!
do.can.not.lack.ship=\u8BA2\u5355\u4E0D\u5141\u8BB8\u7F3A\u53D1\uFF01
error.delivery.pick.cannotPick.wrongStatus=\u62E3\u8D27\u5355\u72B6\u6001\u9519\u8BEF,\u62E3\u8D27\u786E\u8BA4\u64CD\u4F5C\u5931\u8D25\!
error.delivery.waveNotExist=\u6CE2\u6B21\u4E0D\u5B58\u5728\!
error.delivery.cartonNoIsNull=\u7BB1\u53F7\u4E3A\u7A7A\!
error.delivery.doHasNotLack=\u8BA2\u5355\u6CA1\u6709\u7F3A\u8D27\u660E\u7EC6\!
exception.send.release.error=\u7CFB\u7EDF\u9519\u8BEF\uFF0C\u91CA\u653E\u53D1\u8D27\u5355\u5931\u8D25
error.delivery.load.carrierNoIsLoaded=\u7BB1\u53F7/\u677F\u53F7'{0}'\u4E0D\u80FD\u4EA4\u63A5\uFF0C\u56E0\u4E3A\u8BE5\u7BB1\u5DF2\u7ECF\u4EA4\u63A5\!
error.delivery.mergeTypeByContainer=\u5F53\u524D\u7CFB\u7EDF\u5DF2\u542F\u7528\u5BB9\u5668\u7BA1\u7406\uFF0C\u8BF7\u5230\u5BB9\u5668\u96C6\u8D27\u5165\u533A\u6216\u5BB9\u5668\u96C6\u8D27\u51FA\u533A\u9875\u9762\u96C6\u8D27
error.delivery.WaveSts.Error=\u6CE2\u6B21\u72B6\u6001\u4E0D\u6B63\u786E\!
error.sku.noAllocRule=\u5546\u54C1{0}\u65E0\u5206\u914D\u89C4\u5219\uFF0C\u65E0\u6CD5\u751F\u6210\u8865\u8D27\u4EFB\u52A1\!
error.delivery.merge.canNotRease=\u5F53\u524D\u6CE2\u6B21\u672A\u96C6\u8D27\u6216\u5DF2\u96C6\u8D27\u51FA\u533A\uFF0C\u4E0D\u7528\u91CA\u653E
error.delivery.deliveryOrder.cannotCancel.notExist=\u65E0\u6CD5\u53D6\u6D88\u53D1\u8D27\u5355,\u53D1\u8D27\u5355\u4E0D\u5B58\u5728\!
error.delivery.repTaskNoNotEquals=\u8865\u8D27\u4EFB\u52A1\u7684\u8865\u8D27\u5355\u53F7\u4E0D\u4E00\u81F4\uFF01
error.delivery.skuIsBinded=\u5546\u54C1\u5DF2\u7ECF\u7ED1\u5B9A\u5BB9\u5668\uFF0C\u4E0D\u80FD\u518D\u6B21\u7ED1\u5B9A\uFF01
exception.send.rollback.error=\u7CFB\u7EDF\u9519\u8BEF\uFF0C\u56DE\u9000\u5931\u8D25\uFF01
error.delivery.cancelAssign.failed.pktIsNull=\u53D6\u6D88\u5206\u914D\u5931\u8D25,\u5206\u914D\u660E\u7EC6\u4E0D\u5B58\u5728\!
error.delivery.invoiceStatusError=\u53D1\u7968\u72B6\u6001\u4E0D\u5BF9\u6216\u8005\u53D1\u7968\u53F7\u7801\u4E0D\u5B58\u5728
error.serial.notExistOrIsAlreadyUsed=\u8BE5\u5E8F\u5217\u53F7\u4E0D\u5B58\u5728\u6216\u5DF2\u88AB\u4F7F\u7528
error.delivery.waveIsSortted=\u8BE5\u6CE2\u6B21\u5DF2\u7ECF\u5206\u62E3\u5B8C\u6210
error.delivery.containerHasBind=\u5BB9\u5668\u6B63\u5728\u4F7F\u7528\u4E2D\uFF0C\u4F7F\u7528\u5355\u53F7\uFF1A{0}
error.delivery.system=\u53D1\u751F\u7CFB\u7EDF\u9519\u8BEF,\u8BF7\u8054\u7CFB\u7CFB\u7EDF\u7BA1\u7406\u5458\!
error.delivery.idStock.notExist=\u8BA2\u5355\u5BF9\u5E94\u7684\u56DE\u5199\u4FE1\u606F\u4E0D\u5B58\u5728\!
info.delivery.loadDetaiBeenRevoed=\u5DF2\u7ECF\u5220\u9664\u8BA2\u5355'{0}'\u4E0B\u7BB1\u53F7\u4E3A'{1}'\u7684\u88C5\u7BB1,{2}/{3}
error.delivery.loadIsLoadingCannotUpdate=\u8BE5\u4EA4\u63A5\u5355\u5DF2\u6709\u4EA4\u63A5\u660E\u7EC6\uFF0C\u4E0D\u5141\u8BB8\u4FEE\u6539\!
error.delivery.containerIsEmpty=\u5BB9\u5668\u4E3A\u7A7A\uFF0C\u4E0D\u5141\u8BB8\u96C6\u8D27\u5165\u533A
error.mergeIn.wave.hasNoSortingBin=\u6CE2\u6B21{0}\u65E0\u5206\u62E3\u67DC
error.delivery.carrierIsNotExist=DO\u5355\u5BF9\u5E94\u7684\u627F\u8FD0\u5546\u4FE1\u606F\u4E0D\u5B58\u5728\!
error.invoice.bind.billno.dup=\u6570\u636E\u9519\u8BEF\uFF1A\u5F53\u524D\u5F00\u7968\u53F7\u5BF9\u5E94\u591A\u6761\u5F00\u7968\u4FE1\u606F\uFF01\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u5904\u7406\uFF01
error.delivery.serialNoDuplicated=\u5E8F\u5217\u53F7\u91CD\u590D
error.delivery.cartonAlreadyBoundLpn=\u7BB1\u53F7'{0}'\u5DF2\u7ECF\u7ED1\u5B9A\u6258\u76D8\uFF0C\u8BF7\u6309\u6258\u76D8\u4EA4\u63A5\u6216\u8005\u5148\u89E3\u9664\u7ED1\u5B9A\!
error.delivery.cancelDo.hasRKTask=\u8BE5\u53D1\u8D27\u5355\u8FD8\u6709\u8FD4\u62E3\u4EFB\u52A1\u6CA1\u6709\u5B8C\u6210,\u4E0D\u80FD\u53D6\u6D88\!
error.delivery.cannotFrozen=\u53EA\u80FD\u51BB\u7ED3\u521D\u59CB\u5316\u5230\u88C5\u7BB1\u5B8C\u6210\u7684\u53D1\u8D27\u5355
error.delivery.cannotFrozen.doNo={0}\u53EA\u80FD\u51BB\u7ED3\u521D\u59CB\u5316\u5230\u88C5\u7BB1\u5B8C\u6210\u7684\u53D1\u8D27\u5355
error.delivery.cannotFrozen.carton=\u88C5\u7BB1\u5B8C\u6210\u4EE5\u540E\u7684\u53D1\u8D27\u5355\u4E0D\u80FD\u51BB\u7ED3
error.delivery.noMerge=\u6CE2\u6B21\u672A\u8FDB\u5165\u96C6\u8D27\u533A
error.reversePicktask.locationNotExist=\u5B9E\u9645\u8FD4\u62E3\u5E93\u4F4D\u4E0D\u5B58\u5728\!
error.delivery.print.noConfigOption=\u8BF7\u5148\u914D\u7F6E\u7CFB\u7EDF\u6253\u5370\u53C2\u6570\!
error.invoice.lock.nos.notsamebook=\u53D1\u7968\u8D77\u6B62\u53F7\u7801\u548C\u622A\u6B62\u53F7\u7801\u4E0D\u5C5E\u4E8E\u540C\u4E00\u53D1\u7968\u8584\!
error.delivery.carton.stationNoIsNull=\u914D\u9001\u7AD9\u70B9\u4E0D\u80FD\u4E3A\u7A7A\!
error.delivery.pick.cannotPick.waveDetailNotExist=\u6CE2\u6B21\u660E\u7EC6\u4E0D\u5B58\u5728,\u62E3\u8D27\u786E\u8BA4\u64CD\u4F5C\u5931\u8D25\!
error.delivery.beforeDeliveryShouldScannedAllLpn=\u5173\u8054LPN\u626B\u63CF\u5B8C\u6210\u624D\u80FD\u53D1\u8D27
error.delivery.containerTypeNotMatch=\u5BB9\u5668\u7C7B\u578B\u4E0D\u5339\u914D
error.delivery.pick.cannotPick.pickTaskNotExist=\u62E3\u8D27\u4EFB\u52A1\u4E0D\u5B58\u5728,\u62E3\u8D27\u786E\u8BA4\u64CD\u4F5C\u5931\u8D25\!
error.delivery.locAreNotReturnLocation=\u642C\u4ED3\u671F\u95F4\uFF0C\u53EA\u5141\u8BB8\u5546\u54C1\u8FD4\u62E3\u5230RETURN\u5E93\u4F4D\uFF01\uFF08\u975ERETURN\u5E93\u4F4D\uFF1A'{0}'\uFF09
error.reversePicktask.resonIsNull=\u5F02\u5E38\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A\!
error.delivery.containerBStatusWrong=\u5BB9\u5668\u4E1A\u52A1\u72B6\u6001\u4E0D\u6B63\u786E \uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165\u5BB9\u5668\u53F7\uFF01
error.delivery.SortingBinNo.MustLog=\u5FC5\u987B\u767B\u8BB0\u5206\u62E3\u67DC
error.delivery.doNotExist=\u53D1\u8D27\u5355\u4E0D\u5B58\u5728,\u64CD\u4F5C\u65E0\u6CD5\u5B8C\u6210\!
clear.carton.num.error=\u6E05\u7BB1\u660E\u7EC6\u6570\u91CF\u9519\u8BEF\uFF01
cross.detail.not.exist=\u5206\u64AD\u660E\u7EC6\u4E0D\u5B58\u5728\uFF01
error.delivery.cancelAssign.failed.inWave=\u53D1\u8D27\u5355\u5DF2\u8DD1\u6CE2\u6B21,\u4E0D\u80FD\u53D6\u6D88\u5206\u914D\!
error.delivery.notAllowDeiveryByZeroSku=\u53D1\u8D27\u6570\u91CF\u4E0D\u80FD\u4E3A0
error.postSerialToDTSError=\u540C\u6B65\u5E8F\u5217\u53F7\u81F3DTS\u9519\u8BEF\!\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\!
error.delivery.deliveryOrder.cannotCancel.notinit=\u65E0\u6CD5\u53D6\u6D88\u53D1\u8D27\u5355\uFF0C\u53D1\u8D27\u5355\u4E0D\u662F\u521D\u59CB\u5316\u72B6\u6001\!
error.delivery.load.doTypeError=\u7BB1\u53F7'{0}'\u4E0D\u80FD\u4EA4\u63A5\uFF0C\u56E0\u4E3A\u8BE5\u7BB1\u5BF9\u5E94\u7684\u8BA2\u5355\u7C7B\u578B\u4E0E\u4EA4\u63A5\u5355\u7C7B\u578B\u4E0D\u5339\u914D\!
carrier.donot.match.load.carrier=\u8BA2\u5355\u4E0E\u4EA4\u63A5\u5355\u914D\u9001\u5546\u4E0D\u4E00\u81F4\uFF01
error.line.donot.match.load.line=\u8BA2\u5355\u4E0E\u4EA4\u63A5\u5355\u7EBF\u8DEF\u4E0D\u4E00\u81F4\uFF01
loadHeader.status.error.when.unload=\u4EA4\u63A5\u5355\u72B6\u6001\u9519\u8BEF\uFF0C\u4E0D\u80FD\u53D6\u6D88\u88C5\u8F66\uFF01
ship.confirm.status.error=\u4EA4\u63A5\u5355\u72B6\u6001\u9519\u8BEF\uFF0C\u4E0D\u80FD\u53D1\u8FD0\uFF01
error.ship.confirm.order.hd=\u8BA2\u5355{0}\u5DF2\u51BB\u7ED3\uFF0C\u4E0D\u80FD\u53D1\u8FD0\uFF01
error.ship.confirm.multi.carton=\u591A\u7BB1\u9057\u6F0F\uFF1A{0}
error.lpnCartonBindingService.070=\u7BB1\u5B50\u5DF2\u7ECF\u4EA4\u63A5\uFF0C\u4E0D\u80FD\u89E3\u9664\u4E0ELPN'{0}'\u7684\u7ED1\u5B9A\!
error.invoice.cancel.noBookInForInvoiceNo=\u53D1\u7968\u53F7{0}\u6CA1\u6709\u5BF9\u5E94\u7684\u53D1\u7968\u8584\u4FE1\u606F\uFF01
error.delivery.sendMailError=\u53D1\u9001\u90AE\u4EF6\u5F02\u5E38\!
error.serial.allHasScanned=\u5E8F\u5217\u53F7\u5DF2\u626B\u63CF\u5B8C\u6BD5
error.delivery.docNoNotExit=\u5355\u636E\u53F7\u4E0D\u5B58\u5728\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165
error.delivery.repllpnNoNull=\u4E2D\u8F6C\u5BB9\u5668\u4E0D\u80FD\u4E3A\u7A7A\!
error.invoice.bind.invoice.noexists=\u53D1\u7968\u53F7\u7801{0}\u4E0D\u5B58\u5728\uFF01
error.delivery.frozenReazen.null=\u51BB\u7ED3\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A
error.delivery.cartonNumCanNotEmpty=\u7BB1\u53F7\u4E0D\u80FD\u4E3A\u7A7A\!
error.wave.not.merged.out=\u6CE2\u6B21\u672A\u96C6\u8D27\u51FA\u533A\uFF0C\u4E0D\u5141\u8BB8\u5206\u62E3\uFF01
error.delivery.replTaskQtyIlleagl=\u8865\u8D27\u6570\u91CF\u9519\u8BEF\!
error.invoice.bind.billno.dbnull=\u5F00\u7968\u5E8F\u53F7\u6709\u8BEF\uFF0C\u8BF7\u8054\u7CFB\u8FD0\u7EF4\u4EBA\u5458\u5904\u7406\uFF01
error.delivery.waveIsAllPicked=\u6CE2\u6B21\u5DF2\u7ECF\u62E3\u8D27\u5B8C\u6210
error.lpnCartonBindingService.060=\u7BB1'{0}'\u6240\u5C5E\u76EE\u6807\u4ED3\u5E93\u4E0E\u7B2C\u4E00\u7BB1\u4E0D\u4E00\u81F4\!
error.delivery.SortingBinNo.Error=\u5206\u62E3\u67DC\u53F7\u4E0D\u6B63\u786E\u6216\u5DF2\u505C\u7528
error.delivery.doIsHoldCannotLoad=\u7BB1\u53F7'{0}'\u4E0D\u80FD\u4EA4\u63A5\uFF0C\u56E0\u4E3A\u8BE5\u7BB1\u5BF9\u5E94\u7684\u8BA2\u5355\u5DF2\u7ECF\u51BB\u7ED3\!
error.delivery.doTranInWHDonotMatchLoadTranInWH=\u7BB1\u53F7'{0}'\u65E0\u6CD5\u4EA4\u63A5\uFF0C\u56E0\u4E3A\u8BE5\u8BA2\u5355\u7684\u8C03\u62E8\u5230\u4ED3\u5E93\u4FE1\u606F\u4E0E\u4EA4\u63A5\u5355\u7684\u8C03\u62E8\u5230\u4ED3\u5E93\u4FE1\u606F\u4E0D\u4E00\u81F4\!
error.delivery.locCannotMixSku=\u8BE5\u5E93\u4F4D\u4E0D\u80FD\u6DF7sku\u5B58\u653E\!
error.delivery.notAllowDeiveryByScannedLpn=\u8BE5LPN\u5173\u8054\u8D8A\u5E93\u53D1\u8D27\u5355\u5DF2\u51FA\u5E93\uFF0C\u4E0D\u80FD\u91CD\u590D\u53D1\u8D27
error.delivery.waveHasOnePkt=\u8BE5\u6CE2\u6B21\u53EA\u6709\u4E00\u4E2A\u62E3\u8D27\u5355\uFF0C\u65E0\u9700\u96C6\u8D27
error.lpnCartonBindingService.050=\u7BB1'{0}'\u6240\u5C5E\u4F9B\u5E94\u5546\u4E0E\u7B2C\u4E00\u7BB1\u4E0D\u4E00\u81F4\!
error.trans.transPutawayCallInterfaceError=\u4E0A\u67B6\u8C03\u7528\u63A5\u53E3\u65F6\u53D1\u751F\u9519\u8BEF\uFF0C\u4E0A\u67B6\u5931\u8D25\u3002
error.delivery.wave.cannotDeleteFromWave.notExist=\u65E0\u6CD5\u5220\u9664\uFF0C\u53D1\u8D27\u5355\u4E0D\u5B58\u5728\!
error.delivery.pick.cannotPick.doNotExist=\u5173\u8054\u7684\u53D1\u8D27\u5355\u4E0D\u5B58\u5728,\u62E3\u8D27\u786E\u8BA4\u64CD\u4F5C\u5931\u8D25\!
error.delivery.print.dataIsEmpty=\u6253\u5370\u6570\u636E\u4E3A\u7A7A\uFF01\uFF01\uFF01
error.delivery.print.waveDoCountLeMax=\u6253\u5370\u6CE2\u6B21\u8BA2\u5355\u6570\u91CF\u8D85\u8FC7\u6700\u5927\u9650\u5236 {0}
error.delivery.doStatus.notReWrite=\u53D1\u8D27\u5355\u72B6\u6001\u9519\u8BEF\uFF0C\u8BA2\u5355\u672A\u56DE\u5199\!
error.lpnHasScanned.notRelatedCrossDock=\u8BE5LPN\u53F7\u4E0D\u80FD\u5173\u8054\u6B64\u53D1\u8D27\u5355\u53F7\u6216\u5DF2\u626B\u63CF
error.delivery.exceptionStateMustBeToCallCS=\u53D1\u8D27\u5355\u7684\u5F02\u5E38\u72B6\u6001\u5E94\u8BE5\u4E3A\u5F85\u901A\u77E5\u5BA2\u670D\!
error.delivery.pktNo=\u62E3\u8D27\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A
error.reversePicktask.qtyIlleagl=\u5B9E\u9645\u8FD4\u62E3\u6570\u91CF\u975E\u6CD5\!
error.delivery.wave.sortingBin.Error=\u8BE5\u6CE2\u6B21\u5DF2\u7ECF\u5728\u522B\u7684\u5206\u62E3\u67DC\u5206\u62E3\!
error.delivery.doSortRingNotSame=\u6240\u9009do\u4E0D\u5728\u540C\u4E00\u4E2A\u5206\u62E3\u73AF\u4E0A
error.delivery.contanerNotExit=\u5BB9\u5668\u4E0D\u5B58\u5728
error.delivery.backordersTypeError=\u53EA\u5141\u8BB8\u3010\u6B63\u5E38\u51FA\u5E93\u3011\u8BA2\u5355\u5165\u6682\u5B58\u4F4D
error.serial.notAllowNull=\u5E8F\u5217\u53F7\u4E0D\u80FD\u4E3A\u7A7A
error.lpnCartonBindingService.040=\u7BB1'{0}'\u4E0E\u7B2C\u4E00\u7BB1\u7684DO\u7C7B\u578B\u4E0D\u4E00\u81F4\!
error.first.pleaseScanLpn=\u8BF7\u5148\u626B\u63CFLPN\u53F7
error.delivery.doCannotAssign=\u8BA2\u5355\u4E0D\u662F\u521D\u59CB\u5316\u3001\u5206\u914D\u4E2D\u72B6\u6001\u6216\u5DF2\u51BB\u7ED3\!
error.doPrint.noonFlagTimeZoneFormatError=\u201C\u5348\u201D\u6807\u8BB0\u53C2\u6570\u6CA1\u6709\u914D\u7F6E\u6216\u683C\u5F0F\u9519\u8BEF\uFF08\u6B63\u786E\u683C\u5F0F\u5982\uFF0C20\:15-10\:00\uFF09
error.delivery.rtvCanNotChangeSupplier=\u9000\u8D27\u5355\u4E0D\u80FD\u66F4\u6539\u914D\u9001\u5546\!
error.delivery.needCancel=\u8BA2\u5355\u5DF2\u53D6\u6D88\u6216\u5C06\u88AB\u53D6\u6D88\uFF0C\u65E0\u6CD5\u7EE7\u7EED\u64CD\u4F5C\!
error.delivery.waveHasOneContainer=\u4E0D\u5FC5\u5165\u533A,\u5DF2\u96C6\u8D27\u5B8C\u6210
error.delivery.doTypeNotEqualError=\u8BA2\u5355\u7C7B\u578B\u4E0D\u4E00\u81F4\uFF01
error.alloc.picTask.notExist=\u62E3\u8D27\u4EFB\u52A1\u4E0D\u5B58\u5728\u6216\u5DF2\u88AB\u4FEE\u6539\uFF0C\u8BF7\u5237\u65B0\u9875\u9762
error.deliver.loadHeader.notExist=\u4EA4\u63A5\u5355\u4E0D\u5B58\u5728,\u4E0D\u80FD\u53D6\u6D88\!
error.cancelAssign.wrongStatus=\u53EA\u6709\u90E8\u5206\u5206\u914D\u6216\u8005\u5206\u914D\u5B8C\u6210\u72B6\u6001\u7684\u53D1\u8D27\u5355\u624D\u80FD\u53D6\u6D88\u5206\u914D\!
error.delivery.doStatusErrorCannotPrintFace=\u8BF7\u88C5\u7BB1\u5B8C\u6210\u540E\u518D\u6253\u5370\u7BB1\u6807\u7B7E\!
error.delivery.loadHeaderCapacityConfigNotExist=\u81EA\u52A8\u88C5\u8F66\u5355\u6700\u5927\u5305\u88F9\u6570\u91CF\u53C2\u6570\u672A\u914D\u7F6E\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u3002
error.delivery.cartonNumCanNotSame=\u4E0D\u80FD\u9009\u62E9\u76F8\u540C\u7684\u7BB1\u53F7\!
error.lpnCartonBindingService.031=\u7BB1'{0}'\u627F\u8FD0\u5546\u4E0E\u7B2C\u4E00\u7BB1\u627F\u8FD0\u5546\u4E0D\u76F8\u540C\!
error.lpnCartonBindingService.030=\u7BB1'{0}'\u914D\u9001\u7AD9\u4E0E\u5F53\u524D\u6240\u9009\u914D\u9001\u7AD9\u4E0D\u76F8\u540C\!
error.invoice.bind.do.status.not.be.deploy.cancel=DO\u53F7{0}\u72B6\u6001\u4E0D\u80FD\u662F\u3010\u51FA\u5E93\u3011\u6216\u8005\u3010\u5DF2\u53D6\u6D88\u3011\uFF01
error.delivery.cancelAssign.failed.notEnoughStock=\u5E93\u5B58\u4E0D\u8DB3,\u4E0D\u80FD\u53D6\u6D88\!
error.delivery.notBindReady=\u8BA2\u5355'{0}'\u672A\u5B8C\u5168\u7ED1\u5B9A\u6258\u76D8\uFF0C\u8BF7\u68C0\u67E5\uFF01
error.delivery.waveNo=\u6CE2\u6B21\u53F7\u4E0D\u80FD\u4E3A\u7A7A
error.delivery.invoiceHasBeenBoundError=\u5F00\u7968\u4FE1\u606F\u7F16\u53F7{0}\uFF0C\u5DF2\u7ED1\u5B9A\u53D1\u7968\u53F7\u7801{1}\uFF0C\u4E0D\u5141\u8BB8\u518D\u6B21\u7ED1\u5B9A 
error.delivery.cancelAssign.failed=\u53D6\u6D88\u5206\u914D\u5931\u8D25\!
error.checkAssign.wrongType=\u9500\u552E\u7C7B\u578B\u53D1\u8D27\u5355\u4E0D\u80FD\u8FDB\u884C\u5206\u914D\u5BA1\u6838\u64CD\u4F5C\!
error.delivery.doNoIsNull=do\u5355\u53F7\u4E3A\u7A7A\!    
error.delivery.serialNoNotMatch=\u7CFB\u7EDF\u5DF2\u7ECF\u5B58\u5728\u8BE5\u5E8F\u53F7\u5E76\u4E14\u4E0E\u60A8\u9009\u62E9\u7684\u5546\u54C1\u4E0D\u5339\u914D\uFF0C\u8BF7\u786E\u8BA4
error.pick.wrongDodetailCount=\u53D1\u8D27\u5355\u660E\u7EC6\u5DF2\u62E3\u6570\u91CF\u5927\u4E8E\u5206\u914D\u6570\u91CF,\u62E3\u8D27\u5931\u8D25\!
error.invoice.invoiceNoFmBigerThanTo=\u7ED3\u675F\u53D1\u7968\u53F7\u7801\u4E0D\u80FD\u5C0F\u4E8E\u8D77\u59CB\u53F7\u7801
error.delivery.carton.noStationInfo=\u914D\u9001\u7AD9\u70B9\u672A\u5206\u914D\!
error.delivery.rtv.isInTemploc=\u53D1\u8D27\u5355{0}\u5DF2\u5165\u8BE5\u6682\u5B58\u4F4D{1}
error.lpnCartonBindingService.022=\u7BB1\u5B50'{0}'\u5DF2\u7ECF\u4EA4\u63A5,\u65E0\u6CD5\u5B9E\u73B0\u7ED1\u5B9A\!
error.lpnCartonBindingService.021=\u7BB1'{0}'\u4E0D\u5B58\u5728\!
error.lpnCartonBindingService.020=LPN'{0}'\u4E2D\u7BB1\u5B50\u5DF2\u7ECF\u4EA4\u63A5\uFF0C\u4E0D\u80FD\u6267\u884C\u5F53\u524D\u64CD\u4F5C\!
error.delivery.notAllowDeiveryByChangedStockCD=\u8D8A\u5E93\u6570\u636E\u5DF2\u88AB\u4FEE\u6539\uFF0C\u4E0D\u5141\u8BB8\u53D1\u8D27 
error.delivery.doCountsOverload=\u6240\u9009\u53D1\u8D27\u5355\u6570\u91CF\u8D85\u8FC7\u6700\u5927\u503C,\u4E0D\u80FD\u751F\u6210\u6CE2\u6B21\!
error.delivery.pick.cannotPick.supNotExist=\u4F9B\u5E94\u5546\u4E0D\u5B58\u5728,\u62E3\u8D27\u786E\u8BA4\u64CD\u4F5C\u5931\u8D25\!
error.delivery.defaultCutOffTimeNoExist=\u9ED8\u8BA4\u9884\u8BA1\u51FA\u5E93\u65F6\u95F4\u4E0D\u5B58\u5728\uFF01
error.invoice.bind.old.invoiceno.noexists=\u539F\u7ED1\u5B9A\u7684\u53D1\u7968\u53F7\u7801{0}\u4E0D\u5B58\u5728\uFF01
error.lpnCartonBindingService.010=\u7BB1\u5B50\u5DF2\u7ECF\u7ED1\u5B9A\u4E86LPN'{0}'\! 
exception.rollback.status.error=\u8BA2\u5355{0}\u72B6\u6001\u4E0D\u6B63\u786E\uFF0C\u65E0\u6CD5\u56DE\u9000
error.delivery.replTaskReasonCodeIsNull=\u8865\u8D27\u4EFB\u52A1\u7684\u539F\u56E0\u4EE3\u7801\u4E3A\u7A7A
error.delivery.merged=\u62E3\u8D27\u5355\u5DF2\u96C6\u8D27\u5B8C\u6210
error.delivery.crossDockNotRelatedLpn=\u8BE5\u8D8A\u5E93\u53D1\u8D27\u5355\u672A\u5173\u8054LPN\u4E0D\u80FD\u53D1\u8D27
error.invoice.lock.nofrom.notexsit=\u8D77\u59CB\u53F7\u7801\u6216\u53D1\u7968\u4EE3\u7801\u8F93\u5165\u9519\u8BEF\!
error.delivery.product.alreadySorting=\u6240\u626B\u5546\u54C1\u5DF2\u7ECF\u5206\u62E3\u5B8C\u6210\!
delivery.wave_merged_out=\u6CE2\u6B21\u53F7{0}\u96C6\u8D27\u51FA\u533A\u6210\u529F
error.trans.transHeaderPutawayVerifyErr=\u642C\u4ED3\u5355\u5DF2\u5BA1\u6838\uFF0C\u4E0D\u5141\u8BB8\u4E0A\u67B6\uFF01
error.delivery.doAlreadyModifiedByOther=\u8BA2\u5355\u5DF2\u7ECF\u88AB\u4FEE\u6539\uFF01
error.do.cancel.failed.notNeedCancel=\u64CD\u4F5C\u5931\u8D25,\u53D1\u8D27\u5355\u4E0D\u9700\u8981\u53D6\u6D88\! 
error.delivery.notAllowDeiveryByAllToLocal=\u8BE5\u8D8A\u5E93\u8C03\u62E8\u5355\u5173\u8054\u8D8A\u5E93\u5E93\u5B58\u5DF2\u8F6C\u50A8\uFF0C\u4E0D\u5141\u8BB8\u53D1\u8D27
error.invoice.lock.icnull=\u53D1\u7968\u4EE3\u7801\u4E0D\u80FD\u4E3A\u7A7A\!
error.delivery.doStatusErrorCannotForceSorting=do\u8BA2\u5355\u72B6\u6001\u4E0D\u6B63\u786E\uFF0C\u4E0D\u80FD\u8FDB\u884C\u5F3A\u5236\u5206\u62E3
error.delivery.cannot.rollback.notExist=\u4E0D\u80FD\u56DE\u9000\uFF0C\u8BE5\u8BA2\u5355\u4E0D\u5B58\u5728\!
error.delivery.doHaveNoCarton=\u53D1\u8D27\u5355{0}\u6CA1\u6709\u5173\u8054\u7684\u7BB1\u53F7\!
error.delivery.waveIsNotPick=\u6CE2\u6B21\u4E0D\u5904\u4E8E\u5F85\u5206\u62E3\u72B6\u6001,\u53EF\u80FD\u662F\u672A\u62E3\u8D27\u786E\u8BA4\!
error.delivery.replHeaderStatusIllegal=\u8865\u8D27\u5355\u72B6\u6001\u4E0D\u6B63\u786E
error.delivery.recheckQtyInputError=\u6838\u68C0\u5546\u54C1\u6570\u91CF\u8F93\u5165\u9519\u8BEF
error.mergeIn.MergePartition.hasNoMergeLocation=\u96C6\u8D27\u533A{0}\u4E0B\u65E0\u96C6\u8D27\u4F4D
error.delivery.invoiceNoIsNotEnough=\u53D1\u7968\u6570\u91CF\u4E0D\u8DB3,\u8BF7\u91CD\u65B0\u9501\u5B9A\u6216\u8005\u9009\u62E9\u53D1\u7968\!
error.delivery.pickTaskIsCancel=\u7ED1\u5B9A\u5931\u8D25\uFF0C\u8BE5\u62E3\u8D27\u4EFB\u52A1\u5DF2\u53D6\u6D88
error.delivery.notAllowDeiveryByCanceldLpn=\u8BE5LPN\u5173\u8054\u8D8A\u5E93\u53D1\u8D27\u5E93\u5B58\u5DF2\u53D6\u6D88\uFF0C\u4E0D\u80FD\u53D1\u8D27
error.delivery.cancelReplHeaderDetailStatusError=\u5B58\u5728\u5DF2\u4E0A\u67B6\u6216\u5DF2\u4E0B\u67B6\u4EFB\u52A1\u7684\u8865\u8D27\u5355\u4E0D\u80FD\u53D6\u6D88\!
error.delivery.replRealQtyIlleaglOffshelf=\u5B9E\u8865\u6570\u91CF\u4E0D\u80FD\u5927\u4E8E\u5DF2\u4E0B\u67B6\u6570\u91CF
error.mergeIn.waveSlide.hasNoMergePartition=\u6CE2\u6B21\u6ED1\u9053{0}\u4E0B\u65E0\u96C6\u8D27\u533A\u6216\u96C6\u8D27\u533A\u4E0B\u65E0\u96C6\u8D27\u4F4D
error.do.not.in.wave=\u53D1\u8D27\u5355\u5DF2\u4E0D\u5728\u8BE5\u6CE2\u6B21\u4E2D
error.do.bind.container=\u8BA2\u5355\u5DF2\u7ED1\u5B9A\u5BB9\u5668{0}
error.invoice.cancel.mustBePrintOrNoPrint=\u53D1\u7968\u53F7{0}\u5FC5\u987B\u662F\u3010\u672A\u6253\u5370\u3011\u6216\u8005\u3010\u5DF2\u6253\u5370\u3011\u72B6\u6001,\u8BF7\u91CD\u65B0\u67E5\u8BE2\u9875\u9762\u8FDB\u884C\u786E\u8BA4\uFF01
error.delivery.cartonAlreadyLoaded=\u5305\u88F9'{0}'\u5DF2\u7ECF\u4EA4\u63A5\uFF0C\u4E0D\u80FD\u91CD\u590D\u4EA4\u63A5\u3002
error.delivery.doStatus.notMatch=\u6709\u975E\u91CA\u653E\u72B6\u6001\u6216\u8005\u975E\u5B8C\u6210\u5206\u914D\u72B6\u6001\u7684\u53D1\u8D27\u5355,\u4E0D\u80FD\u751F\u6210\u6CE2\u6B21\!
error.delivery.containerNotAvailable=\u5BB9\u5668\u6CA1\u6709\u542F\u7528\uFF0C\u4E0D\u80FD\u7ED1\u5B9A\uFF01
error.delivery.nowOnlySupportWaveBindContainer=\u76EE\u524D\u53EA\u652F\u6301\u3010\u6CE2\u6B21,\u53D1\u8D27\u5355\u3011\u7ED1\u5B9A\u5BB9\u5668
error.pkt.print.sizeError=\u9001\u8D27\u5355\u7684\u5F20\u6570\u5E94\u8BE5\u5927\u4E8E1\!
error.sorting.notExist=\u53D1\u8D27\u5355\u4E0D\u5B58\u5728
error.delivery.load.loadTypeCannotLoad=DO\u5355\u7C7B\u578B\u4E0D\u80FD\u8FDB\u884C\u4EA4\u63A5\!
error.delivery.doIsHold=\u8BA2\u5355'{0}'\u5DF2\u51BB\u7ED3\!
error.delivery.transModeClosed=\u642C\u4ED3\u6A21\u5F0F\u6CA1\u6709\u542F\u7528\uFF01
error.delivery.serialShouldAllScanned=\u7B2C{0}\u884C{1}\u5E8F\u5217\u53F7\u626B\u63CF\u672A\u5B8C\u6210\uFF0C\u8BF7\u5148\u626B\u63CF\u5E8F\u5217\u53F7
error.reversePickHeader.add.Failed.noTask=\u6CA1\u6709\u8FD4\u62E3\u4EFB\u52A1,\u751F\u6210\u8FD4\u62E3\u5355\u5931\u8D25\!
error.delivery.cartonStatusErrForLoad=\u5305\u88F9'{0}'\u72B6\u6001\u4E0D\u6B63\u786E\uFF0C\u4E0D\u5141\u8BB8\u4EA4\u63A5\!
group.order.carton.printed=\u56E2\u8D2D\u8BA2\u5355{0}\u5DF2\u7ECF\u6253\u5370\u9762\u5355\uFF0C\u4E0D\u5141\u8BB8\u4FEE\u6539\u914D\u9001\u4FE1\u606F
error.delivery.rePrintInvoiceBeforeRePrintDo=\u8BE5\u8BA2\u5355\u9700\u8981\u53D1\u7968\uFF0C\u91CD\u6253\u53D1\u8D27\u5355\u524D\uFF0C\u8BF7\u5148\u91CD\u6253\u53D1\u7968
error.delivery.serialShouldAllScanned1=\u5B58\u5728\u672A\u626B\u63CF\u5E8F\u5217\u53F7\u7684\u5546\u54C1\uFF0C\u65E0\u6CD5\u5B8C\u6210\u88C5\u7BB1\uFF0C\u8BF7\u786E\u8BA4\u3002
error.delivery.supervisionCodeShouldAllScanned=\u5B58\u5728\u672A\u626B\u63CF\u76D1\u7BA1\u7801\u7684\u5546\u54C1\uFF0C\u65E0\u6CD5\u5B8C\u6210\u88C5\u7BB1\uFF0C\u8BF7\u786E\u8BA4\u3002
error.delivery.semiAutoWaveClosed=\u5F53\u524D\u4E3A\u624B\u52A8\u6A21\u5F0F\uFF0C\u4E0D\u5141\u8BB8\u81EA\u52A8\u751F\u6210\u6CE2\u6B21
error.delivery.semiAutoWaveOpen=\u5F53\u524D\u4E3A\u81EA\u52A8\u6A21\u5F0F\uFF0C\u4E0D\u5141\u8BB8\u624B\u52A8\u751F\u6210\u6CE2\u6B21
error.exception.doExceptionStatus.isNotRePrint=\u53D1\u8D27\u5355\u5F02\u5E38\u72B6\u6001\u4E0D\u662F\u5F85\u6253\u5370
error.delivery.allocateRuleError=\u5206\u914D\u89C4\u5219\u9519\u8BEF\!
error.delivery.no.allocateRule=\u6CA1\u6709\u5206\u914D\u89C4\u5219
error.delivery.no.replRule=\u6CA1\u6709\u8865\u8D27\u89C4\u5219
error.exception.do.isnot.DeleteLackDetail=\u53D1\u8D27\u5355\u51BB\u7ED3\u539F\u56E0\u4E0D\u662F\u5220\u9664\u7F3A\u8D27\u5546\u54C1\uFF0C\u4E0D\u80FD\u91CD\u6253\u53D1\u8D27\u5355
error.pick.noPhysicalPartition=\u5E93\u4F4D{0}\u6CA1\u6709\u5173\u8054\u7269\u7406\u5E93\u533A
error.delivery.exceptionStatus.isNotRollBack=\u53EA\u80FD\u91CA\u653E\u5F85\u56DE\u9000\u72B6\u6001\u7684\u5F02\u5E38\u8BA2\u5355
error.delivery.exceptionStatus.isNotCallCs=\u8BA2\u5355\u5F02\u5E38\u72B6\u6001\u4E0D\u662F\u5F85\u901A\u77E5\u5BA2\u670D\u3002
error.exception.doIsNotHold=\u53D1\u8D27\u5355\u4E0D\u662F\u51BB\u7ED3\u72B6\u6001
error.delivery.status.isNotPickedPartChecked=\u53D1\u8D27\u5355\u4E0D\u662F\u62E3\u8D27\u5B8C\u6210\u5230\u90E8\u5206\u6838\u62E3\u72B6\u6001
error.import.no.po=PO\u53F7\u5FC5\u987B\u8F93\u5165
error.import.no.receiver=\u6536\u8D27\u65B9\u5FC5\u987B\u8F93\u5165
error.import.no.address=\u6536\u8D27\u5730\u5740\u5FC5\u987B\u8F93\u5165
error.import.no.tel.pho=\u7535\u8BDD\u548C\u624B\u673A\u5FC5\u987B\u8F93\u5165\u5176\u4E2D\u4E00\u4E2A
error.po.exists=\u8BE5PO\u53F7\u5DF2\u5B58\u5728\uFF0C\u65E0\u6CD5\u5BFC\u5165
error.import.no.result=\u5BFC\u5165DO\u5931\u8D25
error.import.no.barcode=\u5546\u54C1\u6761\u7801\u548C\u5546\u54C1\u7F16\u7801\u5FC5\u987B\u8F93\u5165
error.import.barcode.not.exists=\u5546\u54C1\u6761\u7801\u6216\u7F16\u7801\u4E0D\u5B58\u5728
error.import.qty.wrong=\u51FA\u8D27\u6570\u91CF\u5FC5\u987B\u662F\u5927\u4E8E0\u7684\u6570\u5B57
error.delivery.invoiceNoNotEnoughPleaseChange=\u6B64\u5377\u53D1\u7968\u5DF2\u7528\u5B8C\uFF01\u672C\u6B21\u5171\u6253\u5370{0}\u5F20\u53D1\u7968\uFF0C\u8FD8\u6709{1}\u5F20\u53D1\u7968\u672A\u6253\u5370\uFF0C\u8BF7\u7ED1\u5B9A\u65B0\u53D1\u7968\u8FDB\u884C\u6253\u5370\u64CD\u4F5C\uFF01
error.delivery.waveInvoicePrintStatusError=\u6CE2\u6B21\u53D1\u7968\u5DF2\u6253\u5370\u6216\u90E8\u5206\u6253\u5370\uFF01
error.delivery.sort.lackAndSortQty.lh.allocQty=\u53D1\u8D27\u5355\u5DF2\u901A\u77E5\u5BA2\u670D\u6216\u7F3A\u8D27\u603B\u6570\u548C\u5DF2\u5206\u62E3\u6570\u4E4B\u548C\u4E0D\u80FD\u5927\u4E8E\u5206\u914D\u6570
error.delivery.rechedk.lackAndSortQty.lh.sortedQty=\u53D1\u8D27\u5355\u5DF2\u901A\u77E5\u5BA2\u670D\u6216\u7F3A\u8D27\u603B\u6570\u548C\u5DF2\u6838\u62E3\u6570\u4E4B\u548C\u4E0D\u80FD\u5927\u4E8E\u5206\u914D\u6570
error.delivery.sort.noDetailToDM=\u6B64\u5546\u54C1\u5DF2\u5206\u62E3\u5B8C
error.delivery.recheck.noDetailToDM=\u6B64\u5546\u54C1\u5DF2\u6838\u62E3\u5B8C\u6210\u6216\u6B64\u5546\u54C1\u4E0D\u5C5E\u4E8E\u6B64\u53D1\u8D27\u5355
error.delivery.doIsNotPickedToSorting=\u53D1\u8D27\u5355\u4E0D\u662F\u62E3\u8D27\u5B8C\u6210\u6216\u5206\u62E3\u4E2D
error.delivery.doIsNotSortedToChecking=\u53D1\u8D27\u5355\u4E0D\u662F\u5206\u62E3\u5B8C\u6210\u6216\u6838\u62E3\u4E2D
error.delivery.hasNoLackDetail=\u53D1\u8D27\u5355\u65E0\u7F3A\u8D27\u660E\u7EC6
error.delivery.printMixCfy=\u836F\u7F51\u5BFC\u51FA\u9001\u8D27\u5355PDF\u4E0D\u80FD\u6DF7\u5904\u65B9\u836F\u4E0E\u975E\u5904\u65B9\u836F
error.delivery.detailHasNoPickTask=\u53D1\u8D27\u660E\u7EC6\u65E0\u62E3\u8D27\u4EFB\u52A1
error.delivery.noDoSelect4Lack=\u8BF7\u9009\u62E9\u7F3A\u8D27\u8BA2\u5355
error.delivery.hasFindLack=\u4EFB\u52A1\u5DF2\u751F\u6210\uFF0C\u4E0D\u53EF\u91CD\u590D\u751F\u6210
error.findLackHeader.docNoNull=\u65E0\u6CD5\u751F\u6210\u627E\u8D27\u5355\u53F7
error.delivery.status.cannotCreateFindLack=\u53D1\u8D27\u5355\:{0}\u4E0D\u662F\u62E3\u8D27\u5206\u62E3\u6838\u62E3\u7F3A\u8D27\u6216\u7834\u635F
error.delivery.expstatus.isNotToBeAnn=\u53D1\u8D27\u5355{0}\u5F02\u5E38\u72B6\u6001\u4E0D\u662F\u5F85\u901A\u77E5\u5BA2\u670D
error.delivery.statusLePicked=\u53D1\u8D27\u5355{0}\u72B6\u6001\u4E0D\u662F\u62E3\u8D27\u5B8C\u6210\u4E4B\u540E
error.findLackHeader.docNoNotExist=\u627E\u8D27\u5355\u4E0D\u5B58\u5728\uFF01
error.findLackDetail.notExist=\u627E\u8D27\u660E\u7EC6\u4E0D\u5B58\u5728
error.findLackTask.notExist=\u8BE5\u627E\u8D27\u5355\u4E0B\u65E0\u627E\u8D27\u4EFB\u52A1\uFF01
error.delivery.expstatus.isNotToBeRoll=\u53D1\u8D27\u5355{0}\u5F02\u5E38\u72B6\u6001\u662F\u5F85\u56DE\u9000
error.recheck.doCarton.cannot.beNull=\u8BA2\u5355\u53F7,\u7BB1\u53F7,\u6CE2\u6B21\u7F16\u53F7\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
error.delivery.status.notWaitRepl=\u53D1\u8D27\u5355{0}\u5F02\u5E38\u72B6\u6001\u4E0D\u662F\u5F85\u8865\u8D27
error.delivery.docTypeContainerTypeNotCorrespond=\u5355\u636E\u7C7B\u578B\u4E0E\u5BB9\u5668\u7C7B\u578B\u4E0D\u4E00\u81F4
error.delivery.not.release=\u5BB9\u5668\u4E0D\u80FD\u91CA\u653E
error.delivery.doStatus.not.right=\u8BA2\u5355\u72B6\u6001\u4E0D\u6B63\u786E\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165\uFF01
error.delivery.doc.alreadyBindContainer=\u8BA2\u5355\u5DF2\u7ECF\u7ED1\u5B9A\u5BB9\u5668\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165\uFF01
error.container.notSortContainer=\u4E0D\u662F\u5206\u62E3\u7B50,\u8BF7\u91CD\u65B0\u8F93\u5165
error.container.docTypeNotDo=\u5206\u62E3\u7B50\u7ED1\u5B9A\u7684\u5355\u636E\u7C7B\u578B\u9519\u8BEF
error.container.notBindDo=\u62E3\u8D27\u7BB1\u672A\u7ED1\u5B9A\u62E3\u8D27\u5355
error.rechek.skuNotInWaveOrCartoned=\u6B64\u5546\u54C1\u4E0D\u5C5E\u4E8E\u6B64\u6CE2\u6B21\u6216\u5DF2\u6838\u62E3\u5B8C\u6210
error.container.sortContainerNotExists=\u5206\u62E3\u7B50\u4E0D\u5B58\u5728
error.container.sortContainerBindDo=\u5206\u62E3\u7B50\u5DF2\u7ED1DO
error.container.sortContainerNoNull=\u8BF7\u8F93\u5165\u5206\u62E3\u7B50
error.delivery.doBindCantainer=\u8BE5\u53D1\u8D27\u5355\u5DF2\u7ED1\u5206\u62E3\u7B50
error.delivery.doNotBindSortContainer=\u8BE5\u53D1\u8D27\u5355\u672A\u7ED1\u5B9A\u5206\u62E3\u7B50

error.delivery.doWaveExIsAlreadyExist=\u8BE5\u8BA2\u5355\u6CE2\u6B21\u6269\u5C55\u6570\u636E\u5DF2\u7ECF\u5B58\u5728
error.delivery.waveStatusLePicked=\u6CE2\u6B21\u72B6\u6001\u4E0D\u662F\u62E3\u8D27\u5B8C\u6210\u4E4B\u540E
error.delivery.waveStatusLePartSorted=\u6CE2\u6B21\u72B6\u6001\u4E0D\u662F\u5206\u62E3\u4E2D\u53CA\u4EE5\u540E\u7684\u72B6\u6001
error.cantainer.sortContainerTypeError=\u5206\u62E3\u7B50\u7C7B\u578B\u9519\u8BEF 
error.cantainer.newSortContainerTypeError=\u65B0\u5206\u62E3\u7B50\u7C7B\u578B\u9519\u8BEF 
error.delivery.notaskTobeDemand=\u6CA1\u6709\u4EFB\u52A1\u53EF\u4EE5\u7D22\u53D6
error.delivery.sortContanerNotExit=\u5206\u62E3\u7B50\u4E0D\u5B58\u5728
error.delivery.oldSortContanerNotExit=\u539F\u5206\u62E3\u7B50\u4E0D\u5B58\u5728
error.delivery.newSortContanerNotExit=\u65B0\u5206\u62E3\u7B50\u4E0D\u5B58\u5728
error.invoice.getCurOperateUserFailure=\u83B7\u53D6\u5F53\u524D\u7528\u6237\u4FE1\u606F\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55\u7CFB\u7EDF\u540E\uFF0C\u518D\u5C1D\u8BD5\u8BE5\u64CD\u4F5C
error.delivery.hasCallCs=\u8BE5\u53D1\u8D27\u5355\u5DF2\u901A\u77E5\u5BA2\u670D\uFF0C\u8BF7\u5BA2\u670D\u53CD\u9988\u540E\u518D\u64CD\u4F5C
error.container.isNotAvaliable=\u5206\u62E3\u7B50\u5DF2\u505C\u7528
error.delivery.noTaskCancel=\u6CA1\u6709\u53EF\u4EE5\u53D6\u6D88\u7684\u4EFB\u52A1

#Recheck 
recheck.invalid.do_status=\u53D1\u8D27\u5355\u5F53\u524D\u4E0D\u5904\u4E8E\u6838\u62E3\u72B6\u6001
recheck.do.userInfoNotFinish=\u5408\u7EA6\u673A/\u7F51\u5361\u8BA2\u5355\uFF0C\u8BF7\u586B\u5199\u987E\u5BA2\u4FE1\u606F\uFF01
recheck.product.notmatch=\u9519\u8BEF\uFF01\u8BE5\u5546\u54C1\u4E0D\u5C5E\u4E8E\u8BE5\u53D1\u8D27\u5355
recheck.product.notexist=\u9519\u8BEF\uFF01\u8BE5\u53D1\u8D27\u5355\u4E2D\u65E0\u6B64\u5546\u54C1
recheck.number.exceed=\u9519\u8BEF\uFF01\u6838\u62E3\u6570\u91CF\u8D85\u8FC7\u5F85\u6838\u62E3\u7684\u6570\u91CF
recheck.number.zero=\u8BE5\u5546\u54C1\u5DF2\u6838\u62E3\u5B8C\u6BD5
recheck.do.finished=\u53D1\u8D27\u5355\u5DF2\u65E0\u6838\u62E3\u5546\u54C1
recheck.do.recheck.frozen=\u53D1\u8D27\u5355\u5904\u4E8E\u51BB\u7ED3\u72B6\u6001\uFF0C\u65E0\u6CD5\u6838\u62E3\uFF01
recheck.do.recheck.lack=\u53D1\u8D27\u5355\u5904\u4E8E\u7F3A\u8D27\u72B6\u6001\uFF0C\u65E0\u6CD5\u6838\u62E3\uFF01
recheck.do.recheck.cancel=\u53D1\u8D27\u5355\u5DF2\u53D6\u6D88\uFF0C\u65E0\u6CD5\u6838\u62E3\uFF01
recheck.do.frozen.error=\u53D1\u8D27\u5355\u5904\u4E8E\u51BB\u7ED3\u72B6\u6001\uFF0C\u64CD\u4F5C\u5931\u8D25\uFF01
recheck.syserror=\u7CFB\u7EDF\u9519\u8BEF\uFF0C\u64CD\u4F5C\u5931\u8D25
recheck.carton.notexist=\u65E0\u88C5\u7BB1\u4FE1\u606F
recheck.product_carton.notmatch=\u5546\u54C1\u6761\u7801\u5728\u8BE5\u88C5\u7BB1\u4E2D\u4E0D\u5B58\u5728\!
recheck.serial_carton.notmatch=\u5E8F\u5217\u53F7\u5728\u8BE5\u7BB1\u4E2D\u4E0D\u5B58\u5728\uFF01
recheck.serial_do.notmatch=\u9519\u8BEF\uFF01\u8F93\u5165\u7684\u5E8F\u5217\u53F7\u4E0D\u80FD\u4E0E\u53D1\u8D27\u5355\u5173\u8054
recheck.serial_merchant.notmatch=\u9519\u8BEF\uFF01\u8F93\u5165\u7684\u5E8F\u5217\u53F7\u4E0E\u5546\u5BB6\u4E0D\u4E00\u81F4
recheck.serial.required=\u9519\u8BEF\uFF01\u9700\u8981\u626B\u63CF\u5E8F\u5217\u53F7\!
recheck.carton.required=\u9519\u8BEF\uFF01\u9700\u8981\u626B\u63CF\u7BB1\u53F7
recheck.carton_do.notmatch=\u9519\u8BEF\uFF01\u8F93\u5165\u7684\u7BB1\u53F7\u4E0D\u80FD\u4E0E\u53D1\u8D27\u5355\u5173\u8054
recheck.serial.full=\u9519\u8BEF\uFF01\u8BE5\u7BB1\u53F7\u5BF9\u5E94\u7684\u5E8F\u5217\u53F7\u5DF2\u6EE1
recheck.serial.duplicate=\u9519\u8BEF\uFF01\u5E8F\u5217\u53F7\u4E0D\u80FD\u91CD\u590D\u6DFB\u52A0
recheck.serial.invalid=\u9519\u8BEF\uFF01\u65E0\u6548\u7684\u5E8F\u5217\u53F7
recheck.serial_product.notmatch=\u9519\u8BEF\uFF01\u5E8F\u5217\u53F7\u4E0D\u80FD\u4E0E\u5546\u54C1\u5173\u8054
recheck.do.notexist=\u53D1\u8D27\u5355\u4E0D\u5B58\u5728
recheck.pktnumber.error=\u88C5\u7BB1\u5931\u8D25\uFF01\u6838\u62E3\u6570\u91CF\u88AB\u5176\u4ED6\u64CD\u4F5C\u5458\u8FDB\u884C\u4E86\u4FEE\u6539\uFF0C\u8BF7\u5728\u91CD\u65B0\u67E5\u8BE2\u8BE5\u53D1\u8D27\u5355\u540E\u518D\u8FDB\u884C\u64CD\u4F5C\u3002
recheck.serial.error=\u88C5\u7BB1\u5931\u8D25\uFF01\u5176\u4ED6\u64CD\u4F5C\u5458\u4FEE\u6539\u4E86\u5546\u54C1\u5E8F\u5217\u53F7\u4FE1\u606F\uFF0C\u8BF7\u5728\u91CD\u65B0\u67E5\u8BE2\u8BE5\u53D1\u8D27\u5355\u540E\u518D\u8FDB\u884C\u64CD\u4F5C\u3002
recheck.product.numerror=\u9519\u8BEF\uFF01\u5176\u4ED6\u7528\u6237\u4FEE\u6539\u4E86\u5F85\u88C5\u7BB1\u6570\u91CF\uFF0C\u8BF7\u5237\u65B0\u754C\u9762\u540E\u91CD\u65B0\u88C5\u7BB1
cross.recheck.product.numerror=\u9519\u8BEF\uFF01\u5176\u4ED6\u7528\u6237\u4FEE\u6539\u4E86\u5F85\u88C5\u7BB1\u6570\u91CF\uFF0C\u8BF7\u5237\u65B0\u754C\u9762\u540E\u91CD\u65B0\u88C5\u7BB1
cross.recheck.empty.cartonfail=\u8D8A\u5E93\u590D\u6838\u4E0D\u5141\u8BB8\u7A7A\u7BB1\u53D1\u8D27\uFF01
cross.recheck.containerNo.notExist=\u5BB9\u5668\u53F7\u4E0D\u5B58\u5728!
elivery.recheck.stockIsLock=\u9519\u8BEF\uFF01\u8BE5\u8BA2\u5355\u5305\u542B\u505C\u9500\u662F\u5546\u54C1\uFF0C\u8BF7\u51BB\u7ED3\u5E76\u8FD4\u62E3
recheck.do.forzen.cartonfail=\u53D1\u8D27\u5355\u5904\u4E8E\u51BB\u7ED3\u72B6\u6001\uFF0C\u65E0\u6CD5\u88C5\u7BB1\uFF01
recheck.do.recheck.status.error=\u8BA2\u5355\u72B6\u6001\u4E0D\u6B63\u786E\uFF0C\u65E0\u6CD5\u6267\u884C\u6E05\u9664\u64CD\u4F5C\uFF01
recheck.product.numoverflow=\u9519\u8BEF\uFF01\u6838\u62E3\u6570\u91CF\u4E0D\u5141\u8BB8\u5927\u4E8E\u671F\u671B\u53D1\u8D27\u6570\u91CF\uFF01
error.sortContainer.notBindDo=\u5206\u62E3\u7B50\u672A\u7ED1\u5B9A\u53D1\u8D27\u5355
error.delivery.pickContanerNotExit=\u62E3\u8D27\u7BB1\u4E0D\u5B58\u5728
error.delivery.doReRoRtsErr=\u63A5\u53E3\u91CD\u8C03\u5931\u8D25\uFF01
recheck.notFindDoInWave=\u8BE5\u6CE2\u6B21\u4E0B\u9700\u8981\u6B64\u5546\u54C1\u7684DO\u5DF2\u51BB\u7ED3\u6216\u5DF2\u88AB\u5176\u4ED6\u4EBA\u6838\u68C0
error.delivery.notAllSorted=\u53D1\u8D27\u5355\u4E0D\u662F\u5206\u62E3\u5B8C\u6210\u72B6\u6001
error.delivery.notPicked=\u53D1\u8D27\u5355\u4E0D\u662F\u62E3\u8D27\u5B8C\u6210\u72B6\u6001
error.delivery.recheckStockIsLocked=\u8BA2\u5355\u4E2D\u7684\u5546\u54C1[\u7F16\u7801\uFF1A{0}]\u88AB\u505C\u9500\!
error.delivery.recheckSkuCount.LhZero=\u603B\u6838\u62E3\u6570\u5C0F\u4E8E0
error.delivery.hasNoDetail=\u53D1\u8D27\u5355\u65E0\u53D1\u8D27\u660E\u7EC6
error.delivery.skuNeedSerail=\u53D1\u8D27\u5355\u4E2D\u6709\u9700\u8981\u5E8F\u5217\u53F7\u7684\u5546\u54C1
error.delivery.skuCountNeNeed=\u5F85\u6838\u62E3\u6570\u5FC5\u987B\u548C\u53D1\u8D27\u5355\u9700\u6C42\u6570\u4E00\u81F4
error.recheck.doHasLacked=\u8BE5\u8BA2\u5355\u5DF2\u505A\u7F3A\u8D27\u6216\u7834\u635F\u5904\u7406
error.delivery.doHasNoCarrier=\u53D1\u8D27\u5355\u65E0\u914D\u9001\u5546
error.delivery.doHasNoCartonHNo=\u65E0\u6CD5\u83B7\u53D6\u8FD0\u5355\u4E3B\u5355\u53F7
#Recheck
error.wave.carrierInfoMiss=\u8BA2\u5355\u987A\u4E30\u4E3B\u5355\u53F7\u3001\u59CB\u53D1\u5730\u4EE3\u7801\u3001\u76EE\u7684\u5730\u4EE3\u7801\u90FD\u4E0D\u80FD\u4E3A\u7A7A
error.print.cartonLabelTemplatePathNotCfg=\u7BB1\u6807\u7B7E\u6A21\u677F\u6587\u4EF6\u8DEF\u5F84\u672A\u914D\u7F6E\uFF0C\u8BF7\u68C0\u67E5\uFF01
recheck.loginInfo.exception=\u65E0\u6CD5\u53D6\u5230\u767B\u5F55\u4EBA{0}\u7684\u7528\u6237\u4FE1\u606F\uFF0C\u8FD9\u53EF\u80FD\u4F1A\u5F71\u54CD\u4F60\u7684KPI\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55
error.delivery.overseaDoNotAuthorized=\u8BA2\u5355\u5C1A\u672A\u901A\u8FC7\u7533\u62A5\uFF0C\u4E0D\u80FD\u4EA4\u63A5\u51FA\u5E93\u3002
error.delivery.theCartonInWavePartPrinted=\u8BE5\u6CE2\u6B21\u90E8\u5206\u7BB1\u6807\u7B7E\u5DF2\u6253\u5370\uFF01
error.delivery.recheck.doWeightError=\u53D1\u8D27\u5355\u91CD\u91CF\u5E94\u4E3A\u5927\u4E8E0\u7684\u6D6E\u70B9\u6570
error.delivery.doStatusErrorCanNotOperate=\u8BA2\u5355\u72B6\u6001\u4E0D\u6B63\u786E\uFF0C\u65E0\u6CD5\u6267\u884C\u6B64\u64CD\u4F5C\uFF01
error.delivery.noCombinedOrder=\u7CFB\u7EDF\u4E2D\u6CA1\u6709\u627E\u5230\u672A\u751F\u6210\u6CE2\u6B21\u7684\u5408\u5355\u8BA2\u5355\uFF01
error.delivery.operateErrorWithFailNum=\u672C\u6B21\u5171\u64CD\u4F5C\u3010{0}\u3011\u4E2A\u8BA2\u5355\uFF0C\u5176\u4E2D\u6709\u3010{1}\u3011\u4E2A\u5931\u8D25
error.delivery.doInvoiceAllPrinted=\u8BE5\u8BA2\u5355\u53D1\u7968\u5DF2\u5168\u90E8\u6253\u5370\uFF01
recheck.material.volumeScale.notExist=\u5305\u6750\u4F53\u79EF\u6BD4\u4F8B\u53C2\u6570\u672A\u8BBE\u7F6E
recheck.packMaterial.isNull=\u8BF7\u8F93\u5165\u5305\u6750
recheck.packMaterial.notExist=\u5305\u6750\u4E0D\u5B58\u5728
recheck.innerMaterial.notExist=\u5185\u7F6E\u8017\u6750\u4E0D\u5B58\u5728
recheck.material.notPack=\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u5305\u6750
recheck.scaned.packMaterial.less=\u8F93\u5165\u7684\u5305\u6750\u5C0F\u4E8E\u9700\u6C42\u6570
recheck.no.packMaterial=\u63A8\u8350\u5305\u6750\u5931\u8D25\uFF0C\u8BF7\u7EF4\u62A4\u5305\u6750\u6570\u636E

error.notAllow.lpnHasScanned=\u8BE5LPN\u53F7\u5DF2\u626B\u63CF
error.lpn.warehouseNotMatch=\u8BE5LPN\u53F7\u5BF9\u5E94\u7684\u76EE\u6807\u4ED3\u5E93\u4E0D\u540C
error.lpn.valueableFlagNotMatch=\u8BE5LPN\u53F7\u5BF9\u5E94\u7684\u8D35\u54C1\u6807\u8BC6\u4E0D\u540C
productCode.error.pealseInputAgain=\u5546\u54C1\u6761\u7801\u9519\u8BEF\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165
error.lpn.notScanned=\u7B2C{0}\u884CLPN\u5B58\u5728\u5546\u54C1\u672A\u626B\u63CF
error.serial.notScanned=\u7B2C{0}\u884CLPN\u5B58\u5728\u5546\u54C1\u5E8F\u5217\u53F7\u672A\u626B\u63CF
error.proudctCode.hasScanned=\u5546\u54C1\u6761\u7801\u5DF2\u626B\u63CF
error.lpn.hasDumpNotAllowPacking=\u8BE5LPN\u5DF2\u5168\u90E8\u8F6C\u672C\u5730\u5E93\u5B58\uFF0C\u4E0D\u5141\u8BB8\u88C5\u7BB1
error.code.matchManyProucts=\u4E00\u7801\u591A\u54C1\uFF0C\u8BF7\u8F93\u5165\u7F16\u7801

error.overdueRtv.onlyAuditHandlingBill=\u53EA\u80FD\u5BA1\u6838\u5904\u7406\u4E2D\u7684\u903E\u671F\u9000\u5355\u3002
error.overdueRtv.mustChoseDealWayBeforeAudit=\u5B58\u5728\u672A\u9009\u62E9\u5904\u7406\u65B9\u5F0F\u7684\u903E\u671F\u9000\u5355\u3002

error.delivery.newContainerAndWaveNoError=\u6839\u636E\u6CE2\u6B21\u53F7\u548C\u65B0\u5206\u62E3\u7B50\u65E0\u6CD5\u627E\u5230\u5BF9\u5E94\u7684do,\u8BF7\u786E\u8BA4\uFF01
error.sortbin.not.bind.wave=\u5206\u62E3\u67DC\u6CA1\u6709\u7ED1\u5B9A\u6CE2\u6B21
error.sort.not.input.container=\u8BF7\u8F93\u5165\u62E3\u8D27\u7BB1\u53F7
error.sort.container.not.match.wave=\u62E3\u8D27\u7BB1\u4E0D\u5C5E\u4E8E\u8BE5\u6CE2\u6B21
error.sort.wave.already.checked=\u6CE2\u6B21\u5DF2\u7ECF\u590D\u6838\u5B8C\u6210
error.sort.container.already.checked=\u62E3\u8D27\u7BB1\u5DF2\u7ECF\u590D\u6838\u5B8C\u6210
error.sort.sortbin.not.bind.wave=\u8BF7\u5148\u7D22\u53D6\u6CE2\u6B21\u518D\u5206\u62E3
error.sort.input.wave.not.match_bind=\u626B\u63CF\u7684\u6CE2\u6B21/\u62E3\u8D27\u7BB1\u548C\u5206\u62E3\u67DC\u5173\u7CFB\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
error.sort.wave.not.checked=\u8BF7\u5148\u590D\u6838\u62E3\u8D27\u7BB1\u518D\u5206\u62E3
error.can.not.get.wave=\u914D\u7F6E\u9879\u5DF2\u5173\u95ED ,\u4E0D\u80FD\u7D22\u53D6\u6CE2\u6B21
error.bin.sort.wave=\u5206\u62E3\u7D22\u53D6\u5F02\u5E38,\u8BF7\u91CD\u65B0\u7D22\u53D6
error.waybill.is.null=\u8FD0\u5355\u53F7\u4E3A\u7A7A
error.print.status=\u6253\u5370\u72B6\u6001\u9519\u8BEF\uFF0C\u8BF7\u786E\u8BA4
error.entruck.status.not.print=\u88C5\u8F7D\u5355\u72B6\u6001\u9519\u8BEF\uFF0C\u4E0D\u5141\u8BB8\u6253\u5370\uFF01
error.delivery.noCartonToLoadInTheDo=\u6CA1\u6709\u627E\u5230\u672A\u4EA4\u63A5\u7684\u7BB1\u4FE1\u606F
error.waybill.cainiaoPrintError=\u83DC\u9E1F\u6253\u5370\u9762\u5355\u5F02\u5E38\uFF1A{0}
error.waybill.cainiaoGetError=\u83DC\u9E1F\u53D6\u9762\u5355\u53F7\u5F02\u5E38\uFF1A{0}
error.waybill.pddGetError==\u62FC\u591A\u591A\u53D6\u9762\u5355\u53F7\u5F02\u5E38\uFF1A{0}
waybill.sf.main.no.error=\u987A\u4E30\u4E3B\u5355\u53F7\u8C03\u7528\u5931\u8D25\uFF01
bind.way.bill.format.error=\u8FD0\u5355\u53F7\u683C\u5F0F\u9519\u8BEF\uFF01
delivery.sync.unknown.error=\u540C\u6B65\u5230\u5E73\u53F0\u53D1\u751F\u672A\u77E5\u5F02\u5E38\uFF01
error.receiver.addressError=\u6536\u4EF6\u4EBA\u5730\u5740\u4FE1\u606F\u4E0D\u5B58\u5728\u6216\u8005\u6709\u8BEF\uFF0C\u8BF7\u786E\u8BA4\u3002
recheck.do.invocieNotBilled=\u7535\u5B50\u53D1\u7968\u72B6\u6001\u672A\u7ED1\u5B9A
error.sf.address=\u987A\u4E30\u914D\u9001\u5730\u5740\u9519\u8BEF\u6216\u8005\u8D85\u51FA\u914D\u9001\u8303\u56F4
error.print.waveIsMis=\u56E2\u8D2D\u6CE2\u6B21\u548C\u975E\u56E2\u8D2D\u6CE2\u6B21\u4E0D\u80FD\u6DF7\u5408\u6253\u5370
carton.combine.carrier.not.same=\u914D\u9001\u5546\u4E0D\u540C\uFF0C\u4E0D\u80FD\u5408\u5E76\u53D1\u8D27\uFF01
order.check.status.error=\u53D1\u8D27\u5BA1\u6838\u672A\u901A\u8FC7\uFF0C\u4E0D\u80FD\u53D1\u8D27\uFF01
combine.error.do.not.pack.over=\u8BA2\u5355{0}\u6CA1\u6709\u88C5\u7BB1\u5B8C\u6210\uFF0C\u4E0D\u80FD\u64CD\u4F5C\uFF01
carton.weight.error.not.group.wave=\u975E\u56E2\u8D2D\u6CE2\u6B21\uFF0C\u8BF7\u9010\u7BB1\u79F0\u91CD\uFF01
error.do.notice.add.doStatusError=\u8BA2\u5355\u5DF2\u5F00\u59CB\u88C5\u7BB1\u4E0D\u5141\u8BB8\u6DFB\u52A0\u6216\u5220\u9664\u901A\u77E5
bind.way.bill.call.erp.error=\u901A\u77E5ERP\u7CFB\u7EDF\u51FA\u9519!
location.package.type.error=\u76EE\u6807\u5E93\u4F4D\u4E0E\u5B9E\u9645\u5E93\u4F4D\u7684\u4E0B\u67B6\u65B9\u5F0F\u4E0D\u4E00\u81F4!
error.delivery.packageTypeError=\u5305\u88C5\u7C7B\u578B\u4E0D\u5339\u914D!
error.delivery.waveHasMoreDo=\u6CE2\u6B21\u7684\u8BA2\u5355\u6570\u8FC7\u591A
error.delivery.pickIsRechecked=\u62E3\u8D27\u5355\u5DF2\u6838\u68C0
error.delivery.print.dataStatusError=\u6253\u5370\u6570\u636E\u72B6\u6001\u4E0D\u6B63\u786E
error.delivery.print.notWave=\u8BA2\u5355\u672A\u751F\u6210\u6CE2\u6B21,\u4E0D\u80FD\u6253\u5370(\u5355\u53F7:{0})
error.delivery.waveNot2B=\u5FC5\u987B\u4E3A\u6279\u53D1\u7C7B\u578B\u7684\u6CE2\u6B21
error.delivery.allocatedMulitLotNo=\u5355\u4E00\u6279\u53F7\u8BA2\u5355\u5206\u914D\u591A\u4E2A\u6279\u53F7
dubhe.default.carrier.empty=\u8BF7\u914D\u7F6E\u63A5\u53E3\u9ED8\u8BA4\u914D\u7F6E\u5546
default.carrier.can.not.check=\u4E0D\u80FD\u9009\u62E9\u63A5\u53E3\u9ED8\u8BA4\u914D\u9001\u5546
do.is.all.carton=\u8BA2\u5355{0}\u5DF2\u88C5\u7BB1
do.is.waybill.sync=\u8BA2\u5355{0}\u5DF2\u56DE\u5355
do.is.cod=\u8BA2\u5355{0}\u4E3A\u8D27\u5230\u4ED8\u6B3E\u4F46\u662F\u914D\u9001\u5546\u4E0D\u652F\u6301COD
error.delivery.pickerNotExist=\u60A8\u8F93\u5165\u7684\u7528\u6237\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4\u540E\u91CD\u65B0\u64CD\u4F5C\u3002
rtv.do.can.not.change.carrier=RTV\u8BA2\u5355\u4E0D\u80FD\u4FEE\u6539\u914D\u9001\u5546
error.delivery.doWeightError=\u79F0\u91CD\u9519\u8BEF
group.order.can.not.change.carrier=\u56E2\u8D2D\u8BA2\u5355{0}\u5DF2\u4E0B\u5355
error.wave.cannot.mixCarrier=\u6CE2\u6B21\u4E0D\u5141\u8BB8\u6DF7\u914D\u9001\u5546
error.wave.cannot.mixChannel=\u6CE2\u6B21\u4E0D\u5141\u8BB8\u6DF7\u6E20\u9053
error.delivery.allocateOverStock=\u8BA2\u5355\u5B58\u5728\u5E93\u5B58\u8D85\u5206\uFF0C\u8BF7\u53D6\u6D88\u5206\u914D
error.crossDock.statusError=\u8D8A\u5E93\u5355\u72B6\u6001\u9519\u8BEF\uFF0C\u65E0\u6CD5\u53D1\u8D27\uFF01
error.crossDock.stock.statusError =\u8D8A\u5E93\u5E93\u5B58\u72B6\u6001\u9519\u8BEF\uFF0C\u65E0\u6CD5\u53D1\u8D27\uFF01
error.crossDock.stock.numberError=\u8D8A\u5E93\u5355\u6570\u91CF\u4E0E\u5E93\u5B58\u6570\u91CF\u4E0D\u4E00\u81F4\uFF0C\u65E0\u6CD5\u53D1\u8D27\uFF01
cod.multi.carton.not.allow=\u8D27\u5230\u4ED8\u6B3E\u8BA2\u5355\u4E0D\u5141\u8BB8\u62C6\u7BB1\uFF01
wave.emergency.order.can.not.mix=\u7D27\u6025\u8BA2\u5355\u548C\u666E\u901A\u8BA2\u5355\u4E0D\u80FD\u6DF7\u6253\u6CE2\u6B21\uFF01
wave.special.order.can.not.mix=\u7279\u6B8A\u6807\u8BB0\u8BA2\u5355\u4E0D\u80FD\u6DF7\u6253\u6CE2\u6B21\uFF01
pick.lack.can.not.release=\u62E3\u8D27\u7F3A\u8D27\u4E0D\u80FD\u91CA\u653E\uFF01
do.no.already.in=\u8BA2\u5355\u53F7\u91CD\u590D\uFF01
plan.qty.error=\u53D1\u8D27\u6570\u91CF\u5927\u4E8E\u53EF\u7528\u5E93\u5B58\u6216\u6570\u91CF\u4E0D\u662F\u5305\u88C5\u6574\u6570\u500D
pick.partition.region.error=\u62E3\u8D27\u4EFB\u52A1\u5E93\u4F4D\u3001\u5E93\u533A\u3001\u533A\u57DF\u5173\u8054\u9519\u8BEF
container.pick.confirm.qty.over=\u62E3\u8D27\u6570\u91CF\u8D85\u8FC7\u9700\u6C42\u91CF\uFF0C\u62E3\u8D27\u5931\u8D25\uFF01
dot.flag.error=\u8BE5\u5546\u54C1{0}\u4E0D\u652F\u6301\u5C0F\u6570
dot.flag.location.error=\u6E90\u5E93\u4F4D\u548C\u76EE\u6807\u5E93\u4F4D\u5FC5\u987B\u90FD\u662F\u6563\u4EF6\u624D\u80FD\u5C0F\u6570
detail.has.seed=\u8BE5\u660E\u7EC6\u5DF2\u5206\u64AD\u5B8C\u6210
detail.not.all.seed=\u660E\u7EC6\u672A\u5168\u90E8\u5206\u64AD\u5B8C\u6210
wholesale.do.waybill.none=\u6279\u53D1\u8BA2\u5355{0}\u672A\u5408\u5355\u6253\u5370
waybill.image.not.exist=\u8FD0\u5355\u4FE1\u606F\u4E0D\u5B58\u5728\uFF01
waybill.logistics.not.exist=\u8FD0\u5355\u4FE1\u606F\u4E0D\u5B58\u5728\uFF01
pack.material.equal.error=\u5305\u6750\u7F16\u7801\u76F8\u540C
carton.size.not.one.error=\u8BE5\u53D1\u8D27\u5355\u4E0B\u5B58\u5728\u591A\u6761\u7BB1\u4FE1\u606F,\u8BF7\u4F7F\u7528\u7BB1\u53F7\u8FDB\u884C\u91CD\u7F6E
wave.lock.exist.error=\u540C\u4E00\u65F6\u95F4\u53EA\u5141\u8BB8\u5B58\u5728\u4E00\u4E2A\u6CE2\u6B21\u751F\u6210\u4EFB\u52A1
waybill.logistics.fetch.error=\u83B7\u53D6\u8FD0\u5355\u53F7\u5931\u8D25\uFF01

random.check.cancel.reason.empty=\u53D6\u6D88\u62BD\u68C0\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A
random.check.cancel.finished={0}\u5DF2\u62BD\u68C0\u53D6\u6D88\u6216\u62BD\u68C0\u5B8C\u6210\uFF0C\u8BF7\u52FF\u91CD\u590D\u64CD\u4F5C
random.check.not.exists={0}\u4E0D\u5B58\u5728\uFF0C\u8BF7\u6838\u5B9E\u5355\u53F7
random.check.order.num.empty=\u7269\u6D41\u5355\u53F7\u3001P\u5355\u53F7\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
random.check.order.repeat={0}\u5DF2\u62BD\u68C0\uFF0C\u8BF7\u52FF\u91CD\u65B0\u62BD\u68C0