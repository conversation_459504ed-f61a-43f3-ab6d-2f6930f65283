#DoHeader \u51BB\u7ED3\u72B6\u6001\u5E38\u91CF
#titile.delivery.doHeaderRelease.hold=HOLD
#titile.delivery.doHeaderRelease.release=RELEASE
########DoHeader \u72B6\u6001\u5E38\u91CF  \u5F00\u59CB########
#\u521D\u59CB\u5316
#titile.delivery.doHeaderStatus.initialization=00
#\u90E8\u5206\u5206\u914D
#titile.delivery.doHeaderStatus.partAllot=30
#\u5206\u914D\u5B8C\u6210
#titile.delivery.doHeaderStatus.partAllot=40
#\u90E8\u5206\u62E3\u8D27
#titile.delivery.doHeaderStatus.partPick=50
#\u62E3\u8D27\u5B8C\u6210
#titile.delivery.doHeaderStatus.pickCompleted=60
#\u90E8\u5206\u5206\u62E3
#titile.delivery.doHeaderStatus.partSorting=64
#\u5206\u62E3\u5B8C\u6210
#titile.delivery.doHeaderStatus.sortingCompleted=65
#\u90E8\u5206\u6838\u62E3\u88C5\u7BB1
#titile.delivery.doHeaderStatus.partCheckBoxUp=67
#\u88C5\u7BB1\u6838\u62E3\u5B8C\u6210
#titile.delivery.doHeaderStatus.checkBoxUpCompleted=68
#\u5DF2\u53D6\u6D88
#titile.delivery.doHeaderStatus.canceled=90
########DoHeader \u72B6\u6001\u5E38\u91CF   \u7ED3\u675F########


#DoHeade\u72B6\u6001 \u5206\u914D\u5B8C\u6210\u72B6\u6001
#title.delivery.doHeaderStatus.completeAssign=40

#\u4E00\u4E2A\u6CE2\u6B21\u6700\u591A\u5305\u542B\u7684DO\u6570\u91CF
#title.delivery.maxDoCounts=15


#\u6CE2\u6B21\u751F\u6210\u6210\u529F\u63D0\u793A\u4FE1\u606F
generate.wave.success=Wave generated successfully, wave number: {0}
generate.wave.success.one=Order {0} successfully generated a wave, order {1} failed
error.trans.transDetailNotAllocatedErr=There are unallocated warehouse transfer tasks; cancellation of allocation failed.
error.trans.transDetailNotInitialErr=There are non-initialized warehouse transfer tasks; allocation failed.
error.delivery.sortingBinNotExistsError=There is no sorting bin corresponding to the DO on the sorting loop; cannot generate waves!
error.deliver.doTypeErrorAtWave=Inconsistent delivery order types; please reselect.
error.delivery.doNotSellInDeliverErr=DO is not of the "Normal Outbound" type; cannot perform handover.
error.delivery.containerBusinessTypeError=Incorrect container type.
error.delivery.doNotExistsErr=This order number does not exist.
error.delivery.doReleaseStatusErr=DO is already frozen and not allowed for handover.
error.delivery.doStatusNoAllcartonErr=DO is not in the "Packed" state; sorting loop handover operation cannot be performed.
error.delivery.cartonNotExistsErr=Parcel box does not exist.
error.delivery.cartonNotExist=Carton number {0} does not exist.
error.delivery.doWeightError=Weighting error.
error.delivery.doNotSellErr=Currently, only DO parcel binding is supported.
error.delivery.cartonStatusErr=Incorrect parcel status; not allowed to bind parcel container.
error.delivery.doStatusError=The order is not in "Packed" or "Handover" status; container binding is not allowed.
error.delivery.cartonIsBinded=This parcel box is already bound.
error.delivery.cartonNotInDocHeader=Parcel box number does not belong to this delivery order.
error.delivery.containerTypeError=Container type is not the required type for the current business.
error.trans.transHeaderIsVerifiedErr=The corresponding warehouse transfer inbound order is not verified; this operation cannot be performed.
error.trans.locationAllocExistsErr=Warehouse location {0} has allocated warehouse transfer tasks in "allocated" status; allocation failed.
error.trans.transDetailNumInpublishErr=The number of warehouse transfer details exceeds the limit; publishing is not possible.
transExpException=Interface exception, operation failed: ({0})
transHeaderStatusAdddetailErr=The warehouse transfer order is not in the "Initialized," "Task Generation," "Allocating," or "Allocated" state; new tasks cannot be added.
error.trans.fmLocationStatusExists=Location {0} has incomplete warehouse transfer tasks in other warehouse transfer orders; new tasks cannot be added.
error.trans.transDetailNumErr=The number of warehouse transfer tasks exceeds the limit; new tasks cannot be added.
error.trans.fmLocationExists={0} This location already exists in the warehouse transfer details.
error.trans.transHeaderPublishSkuErr=There are products in the warehouse transfer tasks on location {0} that require serial number management; allocation failed.
error.trans.transHeaderPublishStatusErr=Only in the "Allocated" state can you execute the publish operation.
error.trans.transHeaderCancelStatusErr=Only in the "Initialized," "Task Generation," "Allocating," "Allocated," or "Published" state can you perform the cancellation operation.
error.trans.transHeaderCloseStatusErr=Only in the "In Transit" state can you perform the close operation.
error.trans.transHeaderStatusErr=Warehouse transfer outbound order does not exist or is not in "Task Generation," "Allocating," or "Allocated" state; deletion failed.
error.trans.transDetailStatusErr=There are warehouse transfer tasks that are not in the "Initialized" or "Allocated" state; deletion failed.
error.trans.fmLocationNotExists=Source location does not exist.
error.delivery.data=System data error, please contact the system administrator!
error.trans.fmLocationNotLock=Source location is not a locked location.
error.trans.fmLocationIsDM=Source location cannot be a bad product location.
error.trans.toLocationIsDM=Target location cannot be a bad product location.
error.wave.no.lack.do=There are no "Picking Complete" or "Partial Sorting" released DOs in the current wave!
error.sorting.doHold=The order is frozen and cannot be sorted.
error.sorting.do.needCancle=The order needs to be canceled and cannot be sorted.
error.sorting.waveStatus.wrong=The wave status in which the delivery order is located must be "Picking Complete" or "Partial Sorting".
generate.wave.success.no.num=Wave generated successfully.
generate.wave.failed=Wave generation failed.
generate.semiAutoWave.over=Generation completed. Group purchase successful: {0}, failed: {1}; Regular successful: {2}, failed: {3}; Order pool quantity: {4}
delivery.no.stock.to.allocate.sub={0}, DO {1} is frozen due to stock shortage, please handle it.
delivery.no.stock.to.allocate.content=<b>Freezing Reason</b>: Insufficient stock<br/><b>Insufficient Stock Products</b>: {0}<br/><font color="red">Please contact the customer for further processing.</font>
error.trans.transHeaderPublishLocationErr=Location {0} in the warehouse transfer tasks is not in a locked state or is a bad product location; allocation failed.
error.trans.transHeaderCancelorcloseLocationErr=Location {0} in the warehouse transfer tasks is not in a locked state; operation failed.
frozen.reasonDesc.sortError=Missing products during sorting.
frozen.reasonDesc.customServiceDefault=Customer service applied for order cancellation.
frozen.reasonDesc.assignDefault=Stock shortage.
frozen.holdWho.default=system

allocate.failed.doDetailNotExsit=Allocation failed, reason: Allocation detail does not exist! Product code: {0}
allocate.failed.noStock=No matching stock found according to allocation rules! Product code: {0}
allocate.success=Allocation successful, product code: {0}
allocate.failed.noRule=Allocation failed, reason: No allocation rule! Product code: {0}
allocate.failed.skuNoDefaultPickLoc=Replenishment failed, reason: Default picking location not found! Product code: {0}
allocate.failed.qtyNotEnough=No replenishment needed, reason: Sufficient replenishment in transit! Product code: {0}
allocate.failed.stockloc.noStock=Insufficient warehouse stock, product code: {0}!
allocate.failed.stock.noStock=Insufficient storage area stock, product code: {0}!
allocate.replenish.success=Replenishment successful, product code: {0}
allocate.replenish.failed.noReplRule=Replenishment failed, reason: No replenishment rule! Product code: {0}
allocate.replenish.failed.partReplenish=Partial replenishment successful! Product code: {0}
allocate.replenish.failed.taskAllreadyIsExist=Replenishment task already exists, product code: {0}
allocate.replenish.pending=Please replenish! Product code: {0}
allocate.failed.doCancel=Outbound order has been canceled.

error.corssDock.shipQtyUnequal=CROSSDOCK outbound quantity does not match transfer quantity, transfer outbound order number: {0}
error.corssDock.statusError=CROSSDOCK status error, transfer outbound order number: {0}
error.corssDock.postSerialToDTSError=Error synchronizing serial numbers to DTS! Please contact the administrator! Transfer outbound order number: {0}
error.corssDock.toIsNotBind Transfer order is not bound!
error.delivery.moveDateImcomplete=Incomplete critical data for transfer, please contact the administrator.
error.delivery.no.wave=No wave generated.
error.delivery.doCannotAssign=Order is not in the "Initialized," "Allocating," or "Frozen" state!
delivery.operator.error=Operation failed, order number: {0}

delivery.invoice.isNotLock=Please "lock" and define the range of invoice numbers!
delivery.invoice.bindInfo=The current printed invoice number range is {0}-{1}, starting number for the next print: {3}, please verify if the current print is correct!
error.delivery.fail.exceed=The number of failed reshipments exceeds the maximum limit for this order.
error.delivery.status.error=Reshipment handover order status is not correct.
error.delivery.noSku=No corresponding product found!

error.sku.noRsStock=The product's corresponding picking location is full, unable to replenish: {0}
error.sku.noDefPicLoc=The product is missing a default picking location: {0}
error.sku.noReplRule=The product is missing a replenishment rule: {0}
error.repl.recommendLocIsNull=Recommended location is empty: {0}
error.sku.noPartitionRule=The product is missing a zone replenishment rule: {0}

error.rf.pick.cannotPick.pickTaskTotallyCanceled=The picking task has been canceled or the picking quantity has changed. This operation was not successful. The system has refreshed the picking task. Please continue the operation!
error.rf.pick.cannotPick.pickTaskPartlyCanceled=The picking task has been canceled or the picking quantity has changed. This operation was not successful. The system has refreshed the picking task. Please continue the operation!

error.delivery.carrierNoExist=The packing '{0}' does not exist.
error.delivery.load.noDoByCarrierNo=There is no corresponding DO number for the packing number '{0}'!
error.delivery.alreadyFrozen=The delivery order has already been frozen!
error.delivery.needCancel=The order has been canceled and is not allowed for handover!
error.delivery.cartonStatusErrForLoad=The status of the package '{0}' is incorrect and cannot be handed over!
error.delivery.load.doTypeError=The box '{0}' cannot be handed over because the order type corresponding to this box does not match the handover order type!
error.container.notBindDo=The sorting bin is not bound to a delivery order.
error.sortContainer.notBindDo=The sorting bin is not bound to a delivery order.
error.report.noResult=There are no records to export in the report!
error.delivery.notAllallocated=The DO order must be in the "Allocated" state to preview, print, or export!
error.do.autoReleaseJob.config.notExist=The configuration item for automatic DO release job does not exist each time it is released.
error.report.genFileFailed=An error occurred while generating the report. Please contact the system administrator!
error.delivery.sort.lackAndSortQty.lh.allocQty=The delivery order has been notified to customer service or the total of shortage and sorted quantity cannot be greater than the allocated quantity.
generate.singlePromoteWave.skuNumOver=When generating a single promotion wave for a product, the SKU quantity cannot be greater than 1!