<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rtv" pageWidth="756" pageHeight="310" columnWidth="756" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="1.5026296018031648"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="subReport" class="net.sf.jasperreports.engine.JasperReport">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="breakPage" class="java.lang.Boolean"/>
	<parameter name="barcodeUrl" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="subDtoList" class="java.util.List"/>
	<field name="doNo" class="java.lang.String"/>
	<field name="waveNo" class="java.lang.String"/>
	<field name="title" class="java.lang.String"/>
	<field name="doType" class="java.lang.String"/>
	<field name="printer" class="java.lang.String"/>
	<field name="printDate" class="java.lang.String"/>
	<field name="address" class="java.lang.String"/>
	<field name="sender" class="java.lang.String"/>
	<field name="targetWarehouseName" class="java.lang.String"/>
	<variable name="lastPage" class="java.lang.Integer" resetType="None" incrementType="Report">
		<variableExpression><![CDATA[new java.lang.Integer($V{PAGE_NUMBER}.intValue() + (new Integer(1).intValue()))]]></variableExpression>
	</variable>
	<pageHeader>
		<band height="56">
			<frame>
				<reportElement x="0" y="32" width="756" height="24"/>
				<staticText>
					<reportElement x="1" y="1" width="76" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[门店：]]></text>
				</staticText>
				<textField>
					<reportElement x="77" y="1" width="325" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{targetWarehouseName}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="684" y="2" width="32" height="20"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{PAGE_NUMBER}.toString()  + " 页,"]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement x="720" y="2" width="28" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["/ " + $V{PAGE_NUMBER}.toString() + " 页"]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="402" y="1" width="36" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[地址：]]></text>
				</staticText>
				<textField>
					<reportElement x="438" y="1" width="246" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{address}]]></textFieldExpression>
				</textField>
			</frame>
			<staticText>
				<reportElement x="60" y="0" width="624" height="32"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="微软雅黑" size="20" isBold="true" isItalic="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[北京国泰永康大药房有限公司配送单]]></text>
			</staticText>
		</band>
	</pageHeader>
	<detail>
		<band height="52" splitType="Stretch">
			<break>
				<reportElement x="0" y="1" width="769" height="1">
					<printWhenExpression><![CDATA[($P{breakPage} && $V{REPORT_COUNT} > 1) ? Boolean.TRUE: Boolean.FALSE]]></printWhenExpression>
				</reportElement>
			</break>
			<subreport>
				<reportElement stretchType="RelativeToBandHeight" x="-1" y="22" width="756" height="30"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{subDtoList})]]></dataSourceExpression>
				<subreportExpression class="net.sf.jasperreports.engine.JasperReport"><![CDATA[$P{subReport}]]></subreportExpression>
			</subreport>
			<staticText>
				<reportElement x="438" y="2" width="246" height="20"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[*代表麻黄碱药品，#代表抗生素药品]]></text>
			</staticText>
			<staticText>
				<reportElement x="402" y="2" width="36" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[注：]]></text>
			</staticText>
			<textField>
				<reportElement x="300" y="2" width="102" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{printDate}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="228" y="2" width="72" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[业务日期：]]></text>
			</staticText>
			<textField>
				<reportElement x="77" y="2" width="151" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{doNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="0" width="76" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[出库单号：]]></text>
			</staticText>
		</band>
	</detail>
	<pageFooter>
		<band height="40" splitType="Stretch">
			<staticText>
				<reportElement x="37" y="0" width="54" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[打单员：]]></text>
			</staticText>
			<textField>
				<reportElement x="91" y="0" width="113" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{printer}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="660" y="0" width="54" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[验收员：]]></text>
			</staticText>
			<staticText>
				<reportElement x="204" y="0" width="54" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[配送员：]]></text>
			</staticText>
			<staticText>
				<reportElement x="348" y="0" width="54" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[复核员：]]></text>
			</staticText>
			<staticText>
				<reportElement x="509" y="0" width="54" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[送货员：]]></text>
			</staticText>
			<staticText>
				<reportElement x="-1" y="20" width="460" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[备注:  1联仓库留存，2联门店留存]]></text>
			</staticText>
			<textField>
				<reportElement x="563" y="0" width="97" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{sender}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
