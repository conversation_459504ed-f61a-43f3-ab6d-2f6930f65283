<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rtvSub" pageWidth="756" pageHeight="160" columnWidth="756" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="1.6105100000000105"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="frameStyle" mode="Transparent" forecolor="#000000" fill="Solid" pattern="" fontName="微软雅黑" fontSize="13" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
			<pen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="detailStyle" fontName="微软雅黑" fontSize="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box>
			<pen lineWidth="1.0"/>
			<topPen lineWidth="0.0"/>
			<leftPen lineWidth="1.0"/>
			<bottomPen lineWidth="1.0"/>
			<rightPen lineWidth="1.0"/>
		</box>
	</style>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="uom" class="java.lang.String"/>
	<field name="lotatt05" class="java.lang.String"/>
	<field name="lotatt01" class="java.lang.String"/>
	<field name="lotatt02" class="java.lang.String"/>
	<field name="notes" class="java.lang.String"/>
	<field name="lotatt09" class="java.lang.String"/>
	<field name="manufacturer" class="java.lang.String"/>
	<field name="productCode" class="java.lang.String"/>
	<field name="barCode" class="java.lang.String"/>
	<field name="productName" class="java.lang.String"/>
	<field name="amount" class="java.math.BigDecimal"/>
	<field name="actualQty" class="java.math.BigDecimal"/>
	<field name="registerNo" class="java.lang.String"/>
	<field name="doSage" class="java.lang.String"/>
	<field name="specification" class="java.lang.String"/>
	<variable name="sumAmount" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{amount}]]></variableExpression>
		<initialValueExpression><![CDATA[BigDecimal.ZERO]]></initialValueExpression>
	</variable>
	<variable name="sumActualQty" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{actualQty}]]></variableExpression>
		<initialValueExpression><![CDATA[BigDecimal.ZERO]]></initialValueExpression>
	</variable>
	<columnHeader>
		<band height="40" splitType="Stretch">
			<frame>
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="0" y="0" width="756" height="40" forecolor="#000000" backcolor="#FFFFFF"/>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="0" y="0" width="30" height="40"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[行号]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="30" y="0" width="66" height="40"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品编码]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="200" y="0" width="106" height="40"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[厂家]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="96" y="0" width="104" height="40"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品名称]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="306" y="0" width="58" height="40"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[批准文号]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="415" y="0" width="34" height="40"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[单位]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="449" y="0" width="56" height="40"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[批号]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="505" y="0" width="69" height="20"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[生产日期]]></text>
				</staticText>
				<staticText>
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="505" y="20" width="69" height="20"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[有效日期]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="662" y="0" width="44" height="40"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[进价]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="706" y="0" width="50" height="40"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[小计]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="574" y="0" width="48" height="40"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[剂型]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="622" y="0" width="40" height="40"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[数量]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="364" y="0" width="51" height="40"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[规格]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="41" splitType="Prevent">
			<frame>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="756" height="41"/>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="0" y="0" width="30" height="41" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{REPORT_COUNT}.toString()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="30" y="0" width="66" height="41" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{productCode}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="200" y="0" width="106" height="41" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{manufacturer}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="96" y="0" width="104" height="41" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{productName}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="306" y="0" width="58" height="41" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{registerNo}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="415" y="0" width="34" height="41" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{uom}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="449" y="0" width="56" height="41" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{lotatt05}]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="505" y="0" width="69" height="41" isPrintWhenDetailOverflows="true"/>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement x="0" y="0" width="69" height="20" isPrintWhenDetailOverflows="true"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
						</textElement>
						<textFieldExpression class="java.lang.String"><![CDATA[$F{lotatt01}]]></textFieldExpression>
					</textField>
					<line>
						<reportElement x="0" y="20" width="69" height="1"/>
					</line>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement stretchType="RelativeToBandHeight" x="0" y="21" width="69" height="20" isPrintWhenDetailOverflows="true"/>
						<textElement textAlignment="Center" verticalAlignment="Middle">
							<font size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
						</textElement>
						<textFieldExpression class="java.lang.String"><![CDATA[$F{lotatt02}]]></textFieldExpression>
					</textField>
				</frame>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="662" y="0" width="44" height="41" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{lotatt09}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="706" y="0" width="50" height="41" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{amount}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="574" y="0" width="48" height="41" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{doSage}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="622" y="0" width="40" height="41" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{actualQty}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="364" y="0" width="51" height="41" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{specification}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
