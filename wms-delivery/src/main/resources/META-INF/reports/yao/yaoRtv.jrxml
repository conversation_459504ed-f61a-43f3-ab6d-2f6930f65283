<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rtv" pageWidth="756" pageHeight="310" columnWidth="756" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="1.3660269107301488"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="subReport" class="net.sf.jasperreports.engine.JasperReport">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="breakPage" class="java.lang.Boolean"/>
	<parameter name="barcodeUrl" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="subDtoList" class="java.util.List"/>
	<field name="doNo" class="java.lang.String"/>
	<field name="waveNo" class="java.lang.String"/>
	<field name="title" class="java.lang.String"/>
	<field name="doType" class="java.lang.String"/>
	<field name="printer" class="java.lang.String"/>
	<field name="printDate" class="java.lang.String"/>
	<field name="supplier" class="java.lang.String"/>
	<field name="orderAmount" class="java.lang.String"/>
	<field name="orderAmountRMB" class="java.lang.String"/>
	<variable name="lastPage" class="java.lang.Integer" resetType="None" incrementType="Report">
		<variableExpression><![CDATA[new java.lang.Integer($V{PAGE_NUMBER}.intValue() + (new Integer(1).intValue()))]]></variableExpression>
	</variable>
	<pageHeader>
		<band height="63">
			<frame>
				<reportElement x="0" y="36" width="756" height="24"/>
				<staticText>
					<reportElement x="1" y="2" width="57" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[供应商：]]></text>
				</staticText>
				<staticText>
					<reportElement x="260" y="2" width="67" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[退货日期：]]></text>
				</staticText>
				<staticText>
					<reportElement x="458" y="2" width="103" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[采购退货单据号：]]></text>
				</staticText>
				<textField>
					<reportElement x="58" y="2" width="202" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{supplier}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="327" y="2" width="131" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{printDate}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="561" y="2" width="185" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{doNo}]]></textFieldExpression>
				</textField>
			</frame>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="96" y="3" width="526" height="32" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="20" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{title}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement x="0" y="0" width="756" height="3"/>
			</frame>
			<textField evaluationTime="Report">
				<reportElement x="684" y="8" width="50" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["共 " + $V{PAGE_NUMBER}.toString() + " 页"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="635" y="8" width="49" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["第 "  + $V{PAGE_NUMBER}.toString()  + " 页,"]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="52" splitType="Stretch">
			<break>
				<reportElement x="0" y="1" width="769" height="1">
					<printWhenExpression><![CDATA[($P{breakPage} && $V{REPORT_COUNT} > 1) ? Boolean.TRUE: Boolean.FALSE]]></printWhenExpression>
				</reportElement>
			</break>
			<subreport>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="2" width="756" height="49"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{subDtoList})]]></dataSourceExpression>
				<subreportExpression class="net.sf.jasperreports.engine.JasperReport"><![CDATA[$P{subReport}]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<pageFooter>
		<band height="47" splitType="Stretch">
			<staticText>
				<reportElement x="2" y="0" width="56" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[金额合计：]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="24" width="56" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[打单员：]]></text>
			</staticText>
			<staticText>
				<reportElement x="176" y="24" width="42" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[采购员：]]></text>
			</staticText>
			<staticText>
				<reportElement x="337" y="24" width="55" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[复核员：]]></text>
			</staticText>
			<staticText>
				<reportElement x="507" y="24" width="79" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[供应商收货员：]]></text>
			</staticText>
			<textField>
				<reportElement x="58" y="24" width="118" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{printer}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="58" y="0" width="160" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{orderAmount}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="218" y="0" width="71" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[金额合计大写：]]></text>
			</staticText>
			<textField>
				<reportElement x="289" y="0" width="160" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{orderAmountRMB}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
