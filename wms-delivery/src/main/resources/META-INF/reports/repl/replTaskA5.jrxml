<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="repTask" pageWidth="773" pageHeight="510" whenNoDataType="AllSectionsNoDetail" columnWidth="753" leftMargin="10" rightMargin="10" topMargin="0" bottomMargin="0" isFloatColumnFooter="true">
	<property name="ireport.zoom" value="1.8150000000000004"/>
	<property name="ireport.x" value="495"/>
	<property name="ireport.y" value="23"/>
	<style name="frameStyle" mode="Transparent" forecolor="#000000" fill="Solid" pattern="" fontName="微软雅黑" fontSize="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
			<pen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="detailStyle" fontName="微软雅黑" fontSize="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box>
			<pen lineWidth="1.0"/>
			<topPen lineWidth="0.0"/>
			<leftPen lineWidth="1.0"/>
			<bottomPen lineWidth="1.0"/>
			<rightPen lineWidth="1.0"/>
		</box>
	</style>
	<parameter name="replHeaderNo" class="java.lang.String"/>
	<parameter name="warehouseName" class="java.lang.String"/>
	<parameter name="barcodeUrl" class="java.lang.String"/>
	<parameter name="createTime" class="java.sql.Timestamp"/>
	<parameter name="skuCounts" class="java.lang.Long"/>
	<parameter name="unitCounts" class="java.lang.Long"/>
	<parameter name="jpType" class="java.lang.String"/>
	<parameter name="ePlanShipTime" class="java.util.Date"/>
	<field name="sku" class="com.daxia.wms.master.entity.Sku"/>
	<field name="notes" class="java.lang.String"/>
	<field name="fromLocation" class="com.daxia.wms.master.entity.Location"/>
	<field name="planLocation" class="com.daxia.wms.master.entity.Location"/>
	<field name="qty" class="java.math.BigDecimal"/>
	<field name="planShipTime" class="java.util.Date"/>
	<pageHeader>
		<band height="115">
			<staticText>
				<reportElement x="320" y="15" width="159" height="49"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="22" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[补货单]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="69" width="60" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[补货单号：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="61" y="91" width="139" height="22" isPrintInFirstWholeBand="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{warehouseName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="200" y="69" width="85" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[开始补货时间：]]></text>
			</staticText>
			<staticText>
				<reportElement x="610" y="69" width="44" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[补货人:]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="1" y="91" width="60" height="22" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="微软雅黑" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[仓库：]]></text>
			</staticText>
			<staticText>
				<reportElement x="410" y="69" width="85" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[完成补货时间：]]></text>
			</staticText>
			<staticText>
				<reportElement x="285" y="69" width="125" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<image isUsingCache="false" isLazy="true">
				<reportElement x="556" y="15" width="195" height="49"/>
				<imageExpression class="java.lang.String"><![CDATA[$P{barcodeUrl} + $P{replHeaderNo}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="true">
				<reportElement x="61" y="69" width="139" height="22" isPrintInFirstWholeBand="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{replHeaderNo}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement x="1" y="0" width="1028" height="10"/>
			</frame>
			<staticText>
				<reportElement x="200" y="91" width="85" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[创建时间：]]></text>
			</staticText>
			<textField pattern="yyyy-MM-dd HH:mm:ss" isBlankWhenNull="true">
				<reportElement x="285" y="91" width="125" height="22" isPrintInFirstWholeBand="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.sql.Timestamp"><![CDATA[$P{createTime}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="410" y="91" width="85" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[商品总数量：]]></text>
			</staticText>
			<staticText>
				<reportElement x="610" y="91" width="85" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[UNITS总数量：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="495" y="91" width="115" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$P{skuCounts}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="695" y="91" width="58" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$P{unitCounts}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1" y="21" width="105" height="24"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{jpType}]]></textFieldExpression>
			</textField>
			<textField pattern="yyyy-MM-dd HH:mm:ss" isBlankWhenNull="true">
				<reportElement x="111" y="45" width="170" height="24" isPrintInFirstWholeBand="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$P{ePlanShipTime}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="45" width="110" height="24">
					<printWhenExpression><![CDATA[$P{ePlanShipTime} != null]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[最早预计出库时间：]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="22" splitType="Stretch">
			<frame>
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="0" y="0" width="1050" height="22" forecolor="#000000" backcolor="#FFFFFF"/>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="0" y="0" width="28" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="11" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[序号]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="28" y="0" width="75" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品编码]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="103" y="0" width="95" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="11" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品条码]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="198" y="0" width="160" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="11" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品名称]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="358" y="0" width="110" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[补货源库位]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="468" y="0" width="45" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="11" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[期望补货]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="513" y="0" width="106" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[补货目标库位]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="619" y="0" width="45" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="11" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[下架数量]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="664" y="0" width="89" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[预计出库时间]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="25" splitType="Prevent">
			<frame>
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="1050" height="25"/>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="28" y="0" width="75" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="11" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{sku}.getProductCode()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="103" y="0" width="95" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="11" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{sku}.getEan13().replaceAll(",",", ")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="198" y="0" width="160" height="25"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="11" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{sku}.getProductCname()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="358" y="0" width="110" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{fromLocation}.getLocCode()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="468" y="0" width="45" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="11" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{qty}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="513" y="0" width="106" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{planLocation}.getLocCode()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="0" y="0" width="28" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="11" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{REPORT_COUNT}.toString()]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="619" y="0" width="45" height="25"/>
					<textElement>
						<font fontName="微软雅黑" size="11" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="yyyy-MM-dd HH:mm:ss" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="664" y="0" width="89" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="14" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.util.Date"><![CDATA[$F{planShipTime}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
	<pageFooter>
		<band height="22" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="1050" height="22"/>
				<staticText>
					<reportElement x="22" y="0" width="103" height="22"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[上架人签名：]]></text>
				</staticText>
				<staticText>
					<reportElement x="317" y="0" width="130" height="22"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[下架人签名：]]></text>
				</staticText>
				<textField>
					<reportElement x="603" y="0" width="77" height="22"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["第"  + $V{PAGE_NUMBER}.toString()  + "/"]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement x="680" y="0" width="50" height="22"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{PAGE_NUMBER}.toString() + "页"]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</pageFooter>
</jasperReport>
