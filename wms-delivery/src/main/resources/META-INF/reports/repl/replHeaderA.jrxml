<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="replHeaderA" pageWidth="1070" pageHeight="720" whenNoDataType="AllSectionsNoDetail" columnWidth="1030" leftMargin="20" rightMargin="20" topMargin="0" bottomMargin="0" isFloatColumnFooter="true">
	<property name="ireport.zoom" value="0.8250000000000035"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="frameStyle" mode="Transparent" forecolor="#000000" fill="Solid" pattern="" fontName="微软雅黑" fontSize="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
			<pen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="detailStyle" fontName="微软雅黑" fontSize="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box>
			<pen lineWidth="1.0"/>
			<topPen lineWidth="0.0"/>
			<leftPen lineWidth="1.0"/>
			<bottomPen lineWidth="1.0"/>
			<rightPen lineWidth="1.0"/>
		</box>
	</style>
	<parameter name="warehouseName" class="java.lang.String"/>
	<parameter name="barcodeUrl" class="java.lang.String"/>
	<parameter name="subReport" class="net.sf.jasperreports.engine.JasperReport">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<field name="replHeaderNo" class="java.lang.String"/>
	<field name="jpType" class="java.lang.String"/>
	<field name="skuCounts" class="java.lang.Long"/>
	<field name="unitCounts" class="java.lang.Long"/>
	<field name="createTime" class="java.util.Date"/>
	<field name="replTaskList" class="java.util.List"/>
	<field name="ePlanShipTime" class="java.util.Date">
		<fieldDescription><![CDATA[earliestPlanShipTime]]></fieldDescription>
	</field>
	<detail>
		<band height="162" splitType="Prevent">
			<textField isBlankWhenNull="true">
				<reportElement x="1" y="47" width="105" height="24"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="16" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{jpType}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="66" y="71" width="186" height="22" isPrintInFirstWholeBand="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{replHeaderNo}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement x="1" y="2" width="1028" height="10"/>
			</frame>
			<staticText>
				<reportElement x="537" y="93" width="93" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[创建时间：]]></text>
			</staticText>
			<textField pattern="yyyy-MM-dd HH:mm:ss" isBlankWhenNull="true">
				<reportElement x="630" y="93" width="186" height="22" isPrintInFirstWholeBand="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.sql.Timestamp"><![CDATA[$F{createTime}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="817" y="71" width="95" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[商品总数量：]]></text>
			</staticText>
			<staticText>
				<reportElement x="817" y="93" width="95" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[UNITS总数量：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="912" y="71" width="117" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{skuCounts}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="912" y="93" width="117" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{unitCounts}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="93" width="65" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[补货人:]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="252" y="93" width="97" height="22" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="微软雅黑" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[仓库：]]></text>
			</staticText>
			<staticText>
				<reportElement x="537" y="71" width="93" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[完成补货时间：]]></text>
			</staticText>
			<staticText>
				<reportElement x="350" y="71" width="186" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<image isUsingCache="false" isLazy="true">
				<reportElement x="763" y="17" width="195" height="49"/>
				<imageExpression class="java.lang.String"><![CDATA[$P{barcodeUrl} + $F{replHeaderNo}]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="424" y="17" width="159" height="49"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="24" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[补货单]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="71" width="65" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[补货单号：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="350" y="93" width="186" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{warehouseName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="252" y="71" width="97" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[开始补货时间：]]></text>
			</staticText>
			<subreport>
				<reportElement x="1" y="115" width="1028" height="47"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{replTaskList})]]></dataSourceExpression>
				<subreportExpression class="net.sf.jasperreports.engine.JasperReport"><![CDATA[$P{subReport}]]></subreportExpression>
			</subreport>
			<textField pattern="yyyy-MM-dd HH:mm:ss" isBlankWhenNull="true">
				<reportElement x="227" y="47" width="186" height="24" isPrintInFirstWholeBand="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.sql.Timestamp"><![CDATA[$F{ePlanShipTime}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="107" y="47" width="120" height="24"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[最早预计出库时间：]]></text>
			</staticText>
		</band>
	</detail>
	<pageFooter>
		<band height="22" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="1050" height="22"/>
				<staticText>
					<reportElement x="22" y="0" width="103" height="22"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[上架人签名：]]></text>
				</staticText>
				<staticText>
					<reportElement x="414" y="0" width="130" height="22"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[下架人签名：]]></text>
				</staticText>
				<textField>
					<reportElement x="893" y="0" width="77" height="22"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["第"  + $V{PAGE_NUMBER}.toString()  + "/"]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement x="972" y="0" width="50" height="22"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{PAGE_NUMBER}.toString() + "页"]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</pageFooter>
</jasperReport>
