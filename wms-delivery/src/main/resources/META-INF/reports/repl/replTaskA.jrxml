<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="repTaskA" pageWidth="1070" pageHeight="720" whenNoDataType="AllSectionsNoDetail" columnWidth="1070" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true">
	<property name="ireport.zoom" value="1.6076916075000032"/>
	<property name="ireport.x" value="380"/>
	<property name="ireport.y" value="0"/>
	<style name="frameStyle" mode="Transparent" forecolor="#000000" fill="Solid" pattern="" fontName="微软雅黑" fontSize="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
			<pen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="detailStyle" fontName="微软雅黑" fontSize="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box>
			<pen lineWidth="1.0"/>
			<topPen lineWidth="0.0"/>
			<leftPen lineWidth="1.0"/>
			<bottomPen lineWidth="1.0"/>
			<rightPen lineWidth="1.0"/>
		</box>
	</style>
	<field name="sku" class="com.daxia.wms.master.entity.Sku"/>
	<field name="notes" class="java.lang.String"/>
	<field name="fromLocation" class="com.daxia.wms.master.entity.Location"/>
	<field name="planLocation" class="com.daxia.wms.master.entity.Location"/>
	<field name="qty" class="java.math.BigDecimal"/>
	<field name="planShipTime" class="java.util.Date"/>
	<columnHeader>
		<band height="22" splitType="Stretch">
			<frame>
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="0" y="0" width="1070" height="22" forecolor="#000000" backcolor="#FFFFFF"/>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="0" y="0" width="48" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[序号]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="48" y="0" width="119" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品编码]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="167" y="0" width="121" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品条码]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="288" y="0" width="211" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品名称]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="499" y="0" width="94" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[补货源库位]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="593" y="0" width="70" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[期望补货]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="663" y="0" width="101" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[补货目标库位]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="764" y="0" width="64" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[下架数量]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="979" y="0" width="91" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[备注]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="828" y="0" width="65" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[上架数量]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="893" y="0" width="86" height="22"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[预计出库时间]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="25" splitType="Prevent">
			<frame>
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="1070" height="25"/>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="48" y="0" width="119" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{sku}.getProductCode()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="167" y="0" width="121" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{sku}.getEan13().replaceAll(",",", ")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="288" y="0" width="211" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{sku}.getProductCname()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="499" y="0" width="94" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{fromLocation}.getLocCode()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="593" y="0" width="70" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{qty}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="663" y="0" width="101" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{planLocation}.getLocCode()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="979" y="0" width="91" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="0" y="0" width="48" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{REPORT_COUNT}.toString()]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="764" y="0" width="64" height="25"/>
					<textElement>
						<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="828" y="0" width="65" height="25"/>
					<textElement>
						<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" pattern="yyyy-MM-dd HH:mm:ss" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="893" y="0" width="86" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.util.Date"><![CDATA[$F{planShipTime}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
