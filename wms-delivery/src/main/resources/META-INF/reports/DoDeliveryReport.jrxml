<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DoDeliveryReport" pageWidth="1600" pageHeight="842" columnWidth="1600" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="288"/>
	<property name="ireport.y" value="0"/>
	<field name="doNo" class="java.lang.String"/>
	<field name="doType" class="java.lang.String"/>
	<field name="shipTime" class="java.util.Date"/>
	<field name="shipQty" class="java.lang.Integer"/>
	<field name="packedQty" class="java.lang.Integer"/>
	<field name="grossWt" class="java.lang.Double"/>
	<field name="province" class="java.lang.String"/>
	<field name="city" class="java.lang.String"/>
	<field name="consigneeName" class="java.lang.String"/>
	<field name="postCode" class="java.lang.String"/>
	<field name="productAmount" class="java.lang.Double"/>
	<field name="orderDeliveryFee" class="java.lang.Double"/>
	<field name="isBack" class="java.lang.String"/>
	<field name="distSuppCompName" class="java.lang.String"/>
	<field name="updateBy" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="14" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="100" height="14"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement/>
				<text><![CDATA[DO单号]]></text>
			</staticText>
			<staticText>
				<reportElement x="1000" y="0" width="100" height="14"/>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement/>
				<text><![CDATA[邮编]]></text>
			</staticText>
			<staticText>
				<reportElement x="500" y="0" width="100" height="14"/>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement/>
				<text><![CDATA[重量]]></text>
			</staticText>
			<staticText>
				<reportElement x="200" y="0" width="100" height="14"/>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement/>
				<text><![CDATA[发货日期]]></text>
			</staticText>
			<staticText>
				<reportElement x="1300" y="0" width="100" height="14"/>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement/>
				<text><![CDATA[是否退货]]></text>
			</staticText>
			<staticText>
				<reportElement x="1500" y="0" width="100" height="14"/>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement/>
				<text><![CDATA[锁车操作人]]></text>
			</staticText>
			<staticText>
				<reportElement x="400" y="0" width="100" height="14"/>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement/>
				<text><![CDATA[箱数]]></text>
			</staticText>
			<staticText>
				<reportElement x="1400" y="0" width="100" height="14"/>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement/>
				<text><![CDATA[配送方式]]></text>
			</staticText>
			<staticText>
				<reportElement x="1100" y="0" width="100" height="14"/>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement/>
				<text><![CDATA[代收货款金额]]></text>
			</staticText>
			<staticText>
				<reportElement x="300" y="0" width="100" height="14"/>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement/>
				<text><![CDATA[发货数量]]></text>
			</staticText>
			<staticText>
				<reportElement x="800" y="0" width="100" height="14"/>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement/>
				<text><![CDATA[市]]></text>
			</staticText>
			<staticText>
				<reportElement x="600" y="0" width="100" height="14"/>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement/>
				<text><![CDATA[Do单总重量]]></text>
			</staticText>
			<staticText>
				<reportElement x="900" y="0" width="100" height="14"/>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement/>
				<text><![CDATA[客户姓名]]></text>
			</staticText>
			<staticText>
				<reportElement x="1200" y="0" width="100" height="14"/>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement/>
				<text><![CDATA[客户支付配送费]]></text>
			</staticText>
			<staticText>
				<reportElement x="100" y="0" width="100" height="14"/>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement/>
				<text><![CDATA[DO类型]]></text>
			</staticText>
			<staticText>
				<reportElement x="700" y="0" width="100" height="14"/>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement/>
				<text><![CDATA[省份]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="14" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="700" y="0" width="100" height="14"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{province}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="100" y="0" width="100" height="14"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{doType}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="900" y="0" width="100" height="14"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{consigneeName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="100" height="14"/>
				<box>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{doNo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1200" y="0" width="100" height="14"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{orderDeliveryFee}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1500" y="0" width="100" height="14"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{updateBy}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="800" y="0" width="100" height="14"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{city}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1400" y="0" width="100" height="14"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{distSuppCompName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="yyyy-MM-dd HH:mm:ss" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="200" y="0" width="100" height="14"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{shipTime}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="300" y="0" width="100" height="14"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{shipQty}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="400" y="0" width="100" height="14"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{packedQty}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="600" y="0" width="100" height="14"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{grossWt}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="500" y="0" width="100" height="14"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{grossWt}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1300" y="0" width="100" height="14"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{isBack}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1000" y="0" width="100" height="14"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{postCode}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="1100" y="0" width="100" height="14"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{productAmount}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
