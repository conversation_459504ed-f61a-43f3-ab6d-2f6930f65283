<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="allotDetail" pageWidth="350" pageHeight="100" columnWidth="350" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isIgnorePagination="true">
	<property name="ireport.zoom" value="1.5394743546921223"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="frameStyle" mode="Transparent" forecolor="#000000" fill="Solid" pattern="" fontSize="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
			<pen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="detailStyle" fontSize="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box>
			<pen lineWidth="1.0"/>
			<topPen lineWidth="0.0"/>
			<leftPen lineWidth="1.0"/>
			<bottomPen lineWidth="1.0"/>
			<rightPen lineWidth="1.0"/>
		</box>
	</style>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="doNo" class="java.lang.String"/>
	<field name="cartonCount" class="java.lang.Integer"/>
	<variable name="countDo" class="java.lang.String" calculation="Count"/>
	<variable name="totalCount" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{cartonCount}]]></variableExpression>
	</variable>
	<columnHeader>
		<band height="20" splitType="Stretch">
			<frame>
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="0" y="0" width="350" height="20" forecolor="#000000" backcolor="#FFFFFF"/>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="0" y="0" width="50" height="20"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[行号]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="50" y="0" width="150" height="20"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[订单号]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="200" y="0" width="150" height="20"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[交接箱数]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<frame>
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="350" height="20"/>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" mode="Transparent" x="50" y="0" width="150" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font fontName="微软雅黑" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{doNo}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" mode="Transparent" x="200" y="0" width="150" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font fontName="微软雅黑" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$F{cartonCount}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement style="detailStyle" x="0" y="0" width="50" height="20"/>
					<textElement textAlignment="Center" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{REPORT_COUNT}.toString()]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
	<columnFooter>
		<band height="20">
			<frame>
				<reportElement x="0" y="0" width="350" height="20"/>
				<textField>
					<reportElement style="detailStyle" x="200" y="0" width="150" height="20"/>
					<textElement textAlignment="Center"/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{totalCount}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="detailStyle" x="0" y="0" width="50" height="20"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" isBold="true"/>
					</textElement>
					<text><![CDATA[总计：]]></text>
				</staticText>
				<textField>
					<reportElement style="detailStyle" x="50" y="0" width="150" height="20"/>
					<textElement textAlignment="Center" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{COLUMN_COUNT}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</columnFooter>
</jasperReport>
