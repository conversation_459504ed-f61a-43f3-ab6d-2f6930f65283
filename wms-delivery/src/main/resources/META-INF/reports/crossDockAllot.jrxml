<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rePkt" pageWidth="745" pageHeight="1060" whenNoDataType="AllSectionsNoDetail" columnWidth="705" leftMargin="20" rightMargin="20" topMargin="0" bottomMargin="0" isFloatColumnFooter="true">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.2100000000000073"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="subReport" class="net.sf.jasperreports.engine.JasperReport"/>
	<parameter name="breakPage" class="java.lang.Boolean"/>
	<parameter name="warehouseName" class="java.lang.String"/>
	<parameter name="barcodeUrl" class="java.lang.String"/>
	<parameter name="nowDate" class="java.util.Date"/>
	<parameter name="operateUser" class="java.lang.String"/>
	<field name="doNo" class="java.lang.String"/>
	<field name="shipTime" class="java.sql.Timestamp"/>
	<field name="createdBy" class="java.lang.String"/>
	<field name="wareHouse" class="com.daxia.wms.master.entity.Warehouse">
		<fieldDescription><![CDATA[wareHouse]]></fieldDescription>
	</field>
	<field name="crossDockDetails" class="java.util.List">
		<fieldDescription><![CDATA[toDetails]]></fieldDescription>
	</field>
	<field name="refNo1" class="java.lang.String"/>
	<detail>
		<band height="268" splitType="Stretch">
			<staticText>
				<reportElement x="219" y="65" width="74" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[发货日期：]]></text>
			</staticText>
			<staticText>
				<reportElement x="171" y="23" width="263" height="32"/>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="24" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[越库调拨出库单]]></text>
			</staticText>
			<image isUsingCache="false" isLazy="true">
				<reportElement x="462" y="15" width="135" height="50"/>
				<imageExpression class="java.lang.String"><![CDATA[$P{barcodeUrl} + $F{refNo1}]]></imageExpression>
			</image>
			<textField pattern="yyyy-MM-dd" isBlankWhenNull="true">
				<reportElement x="293" y="65" width="163" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.sql.Timestamp"><![CDATA[$F{shipTime}]]></textFieldExpression>
			</textField>
			<textField pattern="yyyy-MM-dd" isBlankWhenNull="true">
				<reportElement x="524" y="65" width="181" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="65" width="95" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[调拨出库单号：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="95" y="65" width="124" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{doNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="85" width="95" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[调出仓库：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="95" y="85" width="124" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{warehouseName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="219" y="85" width="74" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[制单人：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="293" y="85" width="163" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{operateUser}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="105" width="95" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[调入仓库：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="95" y="105" width="124" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{wareHouse}.getWarehouseName()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="219" y="105" width="74" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[仓库联系人：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="293" y="105" width="163" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{wareHouse}.getContactor()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="456" y="105" width="68" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[联系电话：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="524" y="105" width="181" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{wareHouse}.getPhone() == null ? "" : $F{wareHouse}.getPhone()) + "   " + ($F{wareHouse}.getMobile() == null ? "" : $F{wareHouse}.getMobile())]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="125" width="95" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[调入仓库地址：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="95" y="125" width="610" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{wareHouse}.getCity() == null ? "" : $F{wareHouse}.getCity().getCityCname())
+ ($F{wareHouse}.getCounty() == null ? "" :  $F{wareHouse}.getCounty().getCountyCname())
+ $F{wareHouse}.getAddressName()]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement x="0" y="164" width="650" height="59"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{crossDockDetails})]]></dataSourceExpression>
				<subreportExpression class="net.sf.jasperreports.engine.JasperReport"><![CDATA[$P{subReport}]]></subreportExpression>
			</subreport>
			<frame>
				<reportElement x="0" y="0" width="705" height="15"/>
			</frame>
			<staticText>
				<reportElement x="456" y="65" width="68" height="20"/>
				<textElement>
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[打印日期：]]></text>
			</staticText>
			<image isUsingCache="false" isLazy="true">
				<reportElement x="0" y="15" width="135" height="50"/>
				<imageExpression class="java.lang.String"><![CDATA[$P{barcodeUrl} + $F{doNo} + "&qz=4"]]></imageExpression>
			</image>
		</band>
	</detail>
	<pageFooter>
		<band height="33" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="555" height="20"/>
			</frame>
			<textField>
				<reportElement x="562" y="0" width="77" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["第"  + $V{PAGE_NUMBER}.toString()  + "/"]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="641" y="0" width="50" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{PAGE_NUMBER}.toString() + "页"]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
