<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rtv" pageWidth="745" pageHeight="1020" columnWidth="725" leftMargin="10" rightMargin="10" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="0.8481952367449703"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="subReport" class="net.sf.jasperreports.engine.JasperReport">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="breakPage" class="java.lang.Boolean"/>
	<parameter name="barcodeUrl" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="doDetails" class="java.util.List"/>
	<field name="notes" class="java.lang.String"/>
	<field name="doNo" class="java.lang.String"/>
	<field name="refNo1" class="java.lang.String"/>
	<field name="consigneeName" class="java.lang.String"/>
	<field name="waveNo" class="java.lang.String">
		<fieldDescription><![CDATA[waveHeader.waveNo]]></fieldDescription>
	</field>
	<variable name="lastPage" class="java.lang.Integer" resetType="None" incrementType="Report">
		<variableExpression><![CDATA[new java.lang.Integer($V{PAGE_NUMBER}.intValue() + (new Integer(1).intValue()))]]></variableExpression>
	</variable>
	<pageHeader>
		<band height="112">
			<frame>
				<reportElement x="0" y="65" width="705" height="45"/>
				<staticText>
					<reportElement x="1" y="1" width="76" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[RTV编码：]]></text>
				</staticText>
				<staticText>
					<reportElement x="321" y="1" width="56" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[PO号：]]></text>
				</staticText>
				<staticText>
					<reportElement x="501" y="1" width="73" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[打印日期：]]></text>
				</staticText>
				<textField pattern="yyyy-MM-dd" isBlankWhenNull="true">
					<reportElement x="574" y="1" width="117" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="1" y="22" width="76" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[供应商名称：]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="321" y="22" width="56" height="20" forecolor="#000000" backcolor="#FFFFFF"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font fontName="微软雅黑" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[备注：]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="377" y="22" width="314" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{notes}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="77" y="1" width="244" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{doNo}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="377" y="1" width="124" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{refNo1}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="77" y="22" width="244" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{consigneeName}]]></textFieldExpression>
				</textField>
			</frame>
			<image isUsingCache="false" isLazy="true">
				<reportElement x="520" y="13" width="151" height="49">
					<printWhenExpression><![CDATA[$F{doNo} != null]]></printWhenExpression>
				</reportElement>
				<imageExpression class="java.lang.String"><![CDATA[$P{barcodeUrl} + $F{doNo}]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="292" y="15" width="130" height="33"/>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="22" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[供应商退货单]]></text>
			</staticText>
			<frame>
				<reportElement x="0" y="0" width="725" height="10"/>
			</frame>
			<image isUsingCache="false" isLazy="true">
				<reportElement x="60" y="13" width="151" height="49">
					<printWhenExpression><![CDATA[$F{waveNo} != null]]></printWhenExpression>
				</reportElement>
				<imageExpression class="java.lang.String"><![CDATA[$P{barcodeUrl} + $F{waveNo}]]></imageExpression>
			</image>
		</band>
	</pageHeader>
	<detail>
		<band height="53" splitType="Stretch">
			<break>
				<reportElement x="0" y="0" width="705" height="1">
					<printWhenExpression><![CDATA[($P{breakPage} && $V{REPORT_COUNT} > 1) ? Boolean.TRUE: Boolean.FALSE]]></printWhenExpression>
				</reportElement>
			</break>
			<subreport>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="2" width="725" height="49"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{doDetails})]]></dataSourceExpression>
				<subreportExpression class="net.sf.jasperreports.engine.JasperReport"><![CDATA[$P{subReport}]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<pageFooter>
		<band height="22" splitType="Stretch">
			<textField>
				<reportElement x="562" y="0" width="77" height="22"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["第"  + $V{PAGE_NUMBER}.toString()  + "/"]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="641" y="0" width="50" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{PAGE_NUMBER}.toString() + "页"]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
