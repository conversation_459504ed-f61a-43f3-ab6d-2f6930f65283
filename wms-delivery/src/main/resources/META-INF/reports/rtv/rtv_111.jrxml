<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rtv" pageWidth="756" pageHeight="477" columnWidth="756" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="1.366026910730145"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="subReport" class="net.sf.jasperreports.engine.JasperReport">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="breakPage" class="java.lang.Boolean"/>
	<parameter name="barcodeUrl" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="rtvSubDtoList" class="java.util.List"/>
	<field name="doNo" class="java.lang.String"/>
	<field name="poNo" class="java.lang.String"/>
	<field name="consigneeName" class="java.lang.String"/>
	<field name="waveNo" class="java.lang.String"/>
	<variable name="lastPage" class="java.lang.Integer" resetType="None" incrementType="Report">
		<variableExpression><![CDATA[new java.lang.Integer($V{PAGE_NUMBER}.intValue() + (new Integer(1).intValue()))]]></variableExpression>
	</variable>
	<pageHeader>
		<band height="129">
			<frame>
				<reportElement x="0" y="65" width="756" height="62"/>
				<staticText>
					<reportElement x="1" y="1" width="76" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[RTV编码：]]></text>
				</staticText>
				<staticText>
					<reportElement x="288" y="1" width="98" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[PO号：]]></text>
				</staticText>
				<staticText>
					<reportElement x="553" y="1" width="73" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[页次：]]></text>
				</staticText>
				<staticText>
					<reportElement x="1" y="22" width="76" height="40"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[供应商名称：]]></text>
				</staticText>
				<staticText>
					<reportElement mode="Transparent" x="288" y="22" width="98" height="40" forecolor="#000000" backcolor="#FFFFFF"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font fontName="微软雅黑" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[下仓处理日期：]]></text>
				</staticText>
				<textField>
					<reportElement x="77" y="1" width="211" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{doNo}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="386" y="1" width="167" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{poNo}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="77" y="22" width="211" height="40" isPrintWhenDetailOverflows="true"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{consigneeName}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="553" y="22" width="73" height="40"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[打印日期：]]></text>
				</staticText>
				<textField pattern="yyyy-MM-dd" isBlankWhenNull="true">
					<reportElement x="626" y="22" width="130" height="40"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="626" y="1" width="49" height="19"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["第 "  + $V{PAGE_NUMBER}.toString()  + " 页,"]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement x="675" y="1" width="50" height="19"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["共 " + $V{PAGE_NUMBER}.toString() + " 页"]]></textFieldExpression>
				</textField>
			</frame>
			<image isUsingCache="false" isLazy="true">
				<reportElement x="555" y="13" width="195" height="49">
					<printWhenExpression><![CDATA[$F{doNo} != null]]></printWhenExpression>
				</reportElement>
				<imageExpression class="java.lang.String"><![CDATA[$P{barcodeUrl} + $F{doNo}]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="250" y="15" width="279" height="40"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="20" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[供应商退货单]]></text>
			</staticText>
			<frame>
				<reportElement x="0" y="0" width="756" height="10"/>
			</frame>
			<image isUsingCache="false" isLazy="true">
				<reportElement x="7" y="13" width="231" height="49">
					<printWhenExpression><![CDATA[$F{waveNo} != null]]></printWhenExpression>
				</reportElement>
				<imageExpression class="java.lang.String"><![CDATA[$P{barcodeUrl} + $F{waveNo}]]></imageExpression>
			</image>
		</band>
	</pageHeader>
	<detail>
		<band height="52" splitType="Stretch">
			<break>
				<reportElement x="0" y="1" width="769" height="1">
					<printWhenExpression><![CDATA[($P{breakPage} && $V{REPORT_COUNT} > 1) ? Boolean.TRUE: Boolean.FALSE]]></printWhenExpression>
				</reportElement>
			</break>
			<subreport>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="2" width="756" height="49"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{rtvSubDtoList})]]></dataSourceExpression>
				<subreportExpression class="net.sf.jasperreports.engine.JasperReport"><![CDATA[$P{subReport}]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<pageFooter>
		<band height="46" splitType="Stretch">
			<staticText>
				<reportElement x="10" y="0" width="372" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[ 注：白色为仓库联，红色为财务联，绿色为客户联（未经仓库盖章无效）]]></text>
			</staticText>
			<staticText>
				<reportElement x="420" y="0" width="105" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[质管部意见：]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="24" width="56" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[验收员：]]></text>
			</staticText>
			<staticText>
				<reportElement x="107" y="24" width="56" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[复核人：]]></text>
			</staticText>
			<staticText>
				<reportElement x="215" y="24" width="56" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[采购：]]></text>
			</staticText>
			<staticText>
				<reportElement x="326" y="24" width="56" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[财务：]]></text>
			</staticText>
			<staticText>
				<reportElement x="433" y="24" width="64" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[供应商签字：]]></text>
			</staticText>
			<staticText>
				<reportElement x="550" y="24" width="42" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[理货员：]]></text>
			</staticText>
			<staticText>
				<reportElement x="649" y="24" width="40" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[主管：]]></text>
			</staticText>
		</band>
	</pageFooter>
</jasperReport>
