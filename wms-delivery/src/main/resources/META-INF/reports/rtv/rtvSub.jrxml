<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rtvSub" pageWidth="725" pageHeight="975" columnWidth="725" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="1.0000000000000009"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="frameStyle" mode="Transparent" forecolor="#000000" fill="Solid" pattern="" fontName="微软雅黑" fontSize="13" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
			<pen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="detailStyle" fontName="微软雅黑" fontSize="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box>
			<pen lineWidth="1.0"/>
			<topPen lineWidth="0.0"/>
			<leftPen lineWidth="1.0"/>
			<bottomPen lineWidth="1.0"/>
			<rightPen lineWidth="1.0"/>
		</box>
	</style>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="sku" class="com.daxia.wms.master.entity.Sku"/>
	<field name="expectedQty" class="java.math.BigDecimal"/>
	<field name="price" class="java.math.BigDecimal"/>
	<field name="allocatedQty" class="java.math.BigDecimal"/>
	<variable name="sumQty" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{expectedQty}]]></variableExpression>
		<initialValueExpression><![CDATA[BigDecimal.ZERO]]></initialValueExpression>
	</variable>
	<variable name="recvQty" class="java.math.BigDecimal" resetType="Column">
		<variableExpression><![CDATA[$F{price}.multiply($F{allocatedQty})]]></variableExpression>
		<initialValueExpression><![CDATA[BigDecimal.ZERO]]></initialValueExpression>
	</variable>
	<variable name="sumRecvQty" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$V{recvQty}]]></variableExpression>
		<initialValueExpression><![CDATA[BigDecimal.ZERO]]></initialValueExpression>
	</variable>
	<variable name="sumAllocatedQty" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$F{allocatedQty}]]></variableExpression>
		<initialValueExpression><![CDATA[BigDecimal.ZERO]]></initialValueExpression>
	</variable>
	<columnHeader>
		<band height="25" splitType="Stretch">
			<frame>
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="0" y="0" width="725" height="25" forecolor="#000000" backcolor="#FFFFFF"/>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="0" y="0" width="37" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[行号]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="37" y="0" width="94" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="SansSerif" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品编码]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="131" y="0" width="111" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品条码]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="242" y="0" width="146" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品名称]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="388" y="0" width="89" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[类型]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="477" y="0" width="63" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[单价]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="540" y="0" width="61" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[数量]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="659" y="0" width="66" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[小计]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="601" y="0" width="58" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[实际数量]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="25" splitType="Prevent">
			<frame>
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="725" height="25"/>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="37" y="0" width="94" height="25" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{sku}.getProductCode()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="131" y="0" width="111" height="25" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{sku}.getEan13().replaceAll(",",", ")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="242" y="0" width="146" height="25" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{sku}.getProductCname()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="477" y="0" width="63" height="25" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{price}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="540" y="0" width="61" height="25" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{expectedQty}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="659" y="0" width="66" height="25" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$V{recvQty}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="0" y="0" width="37" height="25" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{REPORT_COUNT}.toString()]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="388" y="0" width="89" height="25" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{sku}.getCategory().getCategoryName()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="601" y="0" width="58" height="25" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{allocatedQty}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
	<summary>
		<band height="25" splitType="Stretch">
			<frame>
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="725" height="25"/>
				<textField isStretchWithOverflow="true" evaluationTime="Page" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="601" y="0" width="58" height="25" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$V{sumAllocatedQty}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="0" y="0" width="477" height="25"/>
					<textElement textAlignment="Center">
						<font pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="477" y="0" width="63" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[合计：]]></text>
				</staticText>
				<textField isStretchWithOverflow="true" evaluationTime="Page" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="540" y="0" width="61" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$V{sumQty}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Page" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToTallestObject" x="659" y="0" width="66" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$V{sumRecvQty}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</summary>
</jasperReport>
