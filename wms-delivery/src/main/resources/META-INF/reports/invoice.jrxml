<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="invoice" language="groovy" pageWidth="840" pageHeight="450" columnWidth="840" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="1.0000000000000044"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="sysDate" class="java.util.Date"/>
	<parameter name="operater" class="java.lang.String"/>
	<parameter name="barcode" class="java.lang.String"/>
	<parameter name="barcodeUrl" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="consigneeName" class="java.lang.String">
		<fieldDescription><![CDATA[deliveryOrderHeader.consigneeName]]></fieldDescription>
	</field>
	<field name="invoiceAmount" class="java.lang.Double">
		<fieldDescription><![CDATA[invoiceAmount]]></fieldDescription>
	</field>
	<field name="invoiceContent" class="java.lang.String">
		<fieldDescription><![CDATA[invoiceContent]]></fieldDescription>
	</field>
	<field name="invoiceAmountRbm" class="java.lang.String">
		<fieldDescription><![CDATA[invoiceAmountRbm]]></fieldDescription>
	</field>
	<field name="deliveryOrderHeader" class="com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader"/>
	<field name="invoiceTitle" class="java.lang.String"/>
	<field name="invoiceDetails" class="java.util.List"/>
	<detail>
		<band height="450">
			<frame>
				<reportElement x="0" y="0" width="840" height="120"/>
			</frame>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="610" y="213" width="203" height="20"/>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{deliveryOrderHeader}.getDoNo()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="610" y="173" width="203" height="20"/>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{deliveryOrderHeader}.getWaveHeader().getWaveNo()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="130" y="121" width="336" height="40"/>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{invoiceTitle}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="yyyy" isBlankWhenNull="true">
				<reportElement x="538" y="121" width="72" height="20"/>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$P{sysDate}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="MM" isBlankWhenNull="true">
				<reportElement x="610" y="121" width="31" height="20"/>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$P{sysDate}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd" isBlankWhenNull="true">
				<reportElement x="641" y="121" width="40" height="20"/>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$P{sysDate}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="130" y="333" width="480" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{invoiceAmountRbm}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="354" y="410" width="132" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{operater}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="610" y="253" width="203" height="20"/>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{deliveryOrderHeader}.getSortGridNo()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="173" width="363" height="20" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 0]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{invoiceDetails}.get(0).getSkuDescr()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="193" width="363" height="20" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 1]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{invoiceDetails}.get(1).getSkuDescr()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="213" width="363" height="20" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 2]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{invoiceDetails}.get(2).getSkuDescr()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="233" width="363" height="20" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 3]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{invoiceDetails}.get(3).getSkuDescr()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="253" width="363" height="20" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 4]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{invoiceDetails}.get(4).getSkuDescr()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="273" width="363" height="20" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 5]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{invoiceDetails}.get(5).getSkuDescr()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="293" width="363" height="20" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 6]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{invoiceDetails}.get(6).getSkuDescr()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="313" width="363" height="20" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 7]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{invoiceDetails}.get(7).getSkuDescr()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="538" y="193" width="72" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 1]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{invoiceDetails}.get(1).getAmount()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="538" y="173" width="72" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 0]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{invoiceDetails}.get(0).getAmount()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="538" y="213" width="72" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 2]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{invoiceDetails}.get(2).getAmount()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="538" y="233" width="72" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 3]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{invoiceDetails}.get(3).getAmount()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="538" y="253" width="72" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 4]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{invoiceDetails}.get(4).getAmount()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="538" y="273" width="72" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 5]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{invoiceDetails}.get(5).getAmount()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="538" y="293" width="72" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 6]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{invoiceDetails}.get(6).getAmount()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="538" y="313" width="72" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 7]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{invoiceDetails}.get(7).getAmount()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="417" y="173" width="49" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 0]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{invoiceDetails}.get(0).getQty()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="466" y="173" width="72" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 0]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{invoiceDetails}.get(0).getPrice()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="466" y="193" width="72" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 1]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{invoiceDetails}.get(1).getPrice()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="466" y="213" width="72" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 2]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{invoiceDetails}.get(2).getPrice()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="466" y="233" width="72" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 3]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{invoiceDetails}.get(3).getPrice()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="466" y="253" width="72" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 4]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{invoiceDetails}.get(4).getPrice()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="466" y="273" width="72" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 5]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{invoiceDetails}.get(5).getPrice()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="466" y="293" width="72" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 6]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{invoiceDetails}.get(6).getPrice()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="466" y="313" width="72" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 7]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{invoiceDetails}.get(7).getPrice()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="417" y="193" width="49" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 1]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{invoiceDetails}.get(1).getQty()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="417" y="213" width="49" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 2]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{invoiceDetails}.get(2).getQty()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="417" y="233" width="49" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 3]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{invoiceDetails}.get(3).getQty()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="417" y="253" width="49" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 4]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{invoiceDetails}.get(4).getQty()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="417" y="273" width="49" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 5]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{invoiceDetails}.get(5).getQty()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="417" y="293" width="49" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 6]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{invoiceDetails}.get(6).getQty()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="417" y="313" width="49" height="20">
					<printWhenExpression><![CDATA[$F{invoiceDetails}.size() > 7]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{invoiceDetails}.get(7).getQty()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
