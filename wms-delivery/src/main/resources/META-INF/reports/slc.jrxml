<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="slc" pageWidth="745" pageHeight="1020" columnWidth="725" leftMargin="10" rightMargin="10" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="1.1000000000000036"/>
	<property name="ireport.x" value="1"/>
	<property name="ireport.y" value="0"/>
	<parameter name="subReport" class="net.sf.jasperreports.engine.JasperReport">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="breakPage" class="java.lang.Boolean"/>
	<parameter name="barcodeUrl" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="doNo" class="java.lang.String">
		<fieldDescription><![CDATA[doNo]]></fieldDescription>
	</field>
	<field name="refNo1" class="java.lang.String">
		<fieldDescription><![CDATA[refNo1]]></fieldDescription>
	</field>
	<field name="notes" class="java.lang.String"/>
	<field name="createdBy" class="java.lang.String"/>
	<field name="consigneeName" class="java.lang.String"/>
	<field name="telephone" class="java.lang.String"/>
	<field name="mobile" class="java.lang.String"/>
	<field name="address" class="java.lang.String"/>
	<field name="cityInfo" class="com.daxia.wms.master.entity.City"/>
	<field name="countyInfo" class="com.daxia.wms.master.entity.County"/>
	<field name="expectedArriveTime1" class="java.util.Date"/>
	<field name="userDeffine3" class="java.lang.String"/>
	<field name="waveNo" class="java.lang.String">
		<fieldDescription><![CDATA[waveNo]]></fieldDescription>
	</field>
	<field name="provinceInfo" class="com.daxia.wms.master.entity.Province"/>
	<field name="tranType" class="java.lang.Integer"/>
	<field name="slcTaskDTO" class="java.util.List">
		<fieldDescription><![CDATA[slcTaskDTO]]></fieldDescription>
	</field>
	<field name="warehouseName" class="java.lang.String"/>
	<variable name="lastPage" class="java.lang.Integer" resetType="None" incrementType="Report">
		<variableExpression><![CDATA[new java.lang.Integer($V{PAGE_NUMBER}.intValue() + (new Integer(1).intValue()))]]></variableExpression>
	</variable>
	<detail>
		<band height="262" splitType="Stretch">
			<break>
				<reportElement x="0" y="0" width="705" height="1">
					<printWhenExpression><![CDATA[($P{breakPage} && $V{REPORT_COUNT} > 1) ? Boolean.TRUE: Boolean.FALSE]]></printWhenExpression>
				</reportElement>
			</break>
			<image isUsingCache="false" isLazy="true">
				<reportElement x="519" y="20" width="151" height="49">
					<printWhenExpression><![CDATA[$F{doNo} != null]]></printWhenExpression>
				</reportElement>
				<imageExpression class="java.lang.String"><![CDATA[$P{barcodeUrl} + $F{doNo}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="true">
				<reportElement x="79" y="130" width="97" height="19" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{refNo1}]]></textFieldExpression>
			</textField>
			<textField pattern="yyyy-MM-dd" isBlankWhenNull="true">
				<reportElement x="266" y="130" width="99" height="19" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{expectedArriveTime1}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="365" y="130" width="100" height="19" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="微软雅黑" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[打印日期：]]></text>
			</staticText>
			<textField pattern="yyyy-MM-dd" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="465" y="130" width="193" height="19" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="微软雅黑" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="79" y="149" width="97" height="19" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{doNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="266" y="149" width="99" height="19" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{warehouseName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="365" y="149" width="100" height="19" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="微软雅黑" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[制单人：]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="465" y="149" width="193" height="19" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="微软雅黑" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{createdBy}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="79" y="168" width="97" height="19" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{consigneeName}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="266" y="168" width="99" height="19" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="微软雅黑" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{userDeffine3}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="209" width="725" height="49" isPrintWhenDetailOverflows="true"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{slcTaskDTO})]]></dataSourceExpression>
				<subreportExpression class="net.sf.jasperreports.engine.JasperReport"><![CDATA[$P{subReport}]]></subreportExpression>
			</subreport>
			<textField isBlankWhenNull="true">
				<reportElement x="465" y="168" width="193" height="19" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mobile} == null ?  ($F{telephone} == null ? "" : $F{telephone})
: $F{mobile}  + ($F{telephone} == null ? "" : "/" + $F{telephone})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="79" y="187" width="626" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{provinceInfo} == null ? "" : ($F{provinceInfo}.getProvinceCname() == null ? "" : $F{provinceInfo}.getProvinceCname())) +
($F{cityInfo} == null ? "" : ($F{cityInfo}.getCityCname() == null ? "" : $F{cityInfo}.getCityCname())) +
($F{countyInfo}==null?"" : ($F{countyInfo}.getCountyCname() == null ? "" : $F{countyInfo}.getCountyCname())) +
($F{address} == null ? "" : $F{address})]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement x="0" y="3" width="725" height="10"/>
			</frame>
			<image isUsingCache="false" isLazy="true">
				<reportElement x="39" y="20" width="151" height="49">
					<printWhenExpression><![CDATA[$F{refNo1} != null]]></printWhenExpression>
				</reportElement>
				<imageExpression class="java.lang.String"><![CDATA[$P{barcodeUrl} + $F{refNo1}]]></imageExpression>
			</image>
			<image isUsingCache="false" isLazy="true">
				<reportElement x="519" y="76" width="151" height="49">
					<printWhenExpression><![CDATA[$F{waveNo} != null]]></printWhenExpression>
				</reportElement>
				<imageExpression class="java.lang.String"><![CDATA[$P{barcodeUrl} + $F{waveNo}]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="2" y="130" width="77" height="19" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[PO单号：]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="149" width="77" height="19" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[DO单号：]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="168" width="77" height="19" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[收货方：]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="187" width="77" height="19" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[收货地址：]]></text>
			</staticText>
			<staticText>
				<reportElement x="176" y="130" width="90" height="19" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[期望发货日期：]]></text>
			</staticText>
			<staticText>
				<reportElement x="176" y="149" width="90" height="19" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[发货仓库：]]></text>
			</staticText>
			<staticText>
				<reportElement x="176" y="168" width="90" height="19" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[联系人：]]></text>
			</staticText>
			<staticText>
				<reportElement x="365" y="168" width="100" height="19" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[联系电话：]]></text>
			</staticText>
			<staticText>
				<reportElement x="231" y="20" width="256" height="45"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="24" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[SLC出库单]]></text>
			</staticText>
		</band>
	</detail>
	<pageFooter>
		<band height="20" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="555" height="20"/>
			</frame>
			<textField>
				<reportElement x="562" y="0" width="77" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["第"  + $V{PAGE_NUMBER}.toString()  + "/"]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="641" y="0" width="50" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$V{PAGE_NUMBER}.toString() + "页"]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
