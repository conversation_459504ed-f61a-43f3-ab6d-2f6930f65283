<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rePkt" pageWidth="745" pageHeight="1060" whenNoDataType="AllSectionsNoDetail" columnWidth="705" leftMargin="20" rightMargin="20" topMargin="0" bottomMargin="0" isFloatColumnFooter="true">
	<property name="ireport.zoom" value="1.2078825000000004"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="frameStyle" mode="Transparent" forecolor="#000000" fill="Solid" pattern="" fontSize="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
			<pen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="detailStyle" fontSize="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box>
			<pen lineWidth="1.0"/>
			<topPen lineWidth="0.0"/>
			<leftPen lineWidth="1.0"/>
			<bottomPen lineWidth="1.0"/>
			<rightPen lineWidth="1.0"/>
		</box>
	</style>
	<parameter name="reversalNo" class="java.lang.String"/>
	<parameter name="count" class="java.lang.Integer"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="cartonNo" class="java.lang.String"/>
	<field name="receiveBy" class="java.lang.String"/>
	<field name="doNo" class="java.lang.String"/>
	<field name="receiveTime" class="java.util.Date"/>
	<pageHeader>
		<band height="96">
			<staticText>
				<reportElement x="296" y="18" width="121" height="32"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="22" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[逆向交接单]]></text>
			</staticText>
			<frame>
				<reportElement x="0" y="1" width="705" height="15"/>
			</frame>
			<staticText>
				<reportElement x="57" y="66" width="100" height="25"/>
				<textElement>
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[逆向交接单号：]]></text>
			</staticText>
			<textField>
				<reportElement x="157" y="66" width="186" height="25"/>
				<textElement>
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{reversalNo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="594" y="66" width="75" height="25"/>
				<textElement>
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$P{count}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="494" y="66" width="100" height="25"/>
				<textElement>
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[交接收货箱数：]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="25" splitType="Stretch">
			<frame>
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="0" y="0" width="705" height="25" forecolor="#000000" backcolor="#FFFFFF"/>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="187" y="0" width="156" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[箱号]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="343" y="0" width="150" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[收货人]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="493" y="0" width="157" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[收货时间]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="57" y="0" width="130" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[订单号]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="25" splitType="Prevent">
			<frame>
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="705" height="25"/>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" mode="Transparent" x="57" y="0" width="130" height="25" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font fontName="微软雅黑" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{doNo}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" mode="Transparent" x="187" y="0" width="156" height="25" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font fontName="微软雅黑" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{cartonNo}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" mode="Transparent" x="343" y="0" width="150" height="25" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font fontName="微软雅黑" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{receiveBy}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="yyyy-MM-dd HH:mm:ss" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" mode="Transparent" x="493" y="0" width="157" height="25" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font fontName="微软雅黑" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.util.Date"><![CDATA[$F{receiveTime}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
	<pageFooter>
		<band height="35" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="705" height="35"/>
				<textField>
					<reportElement x="296" y="0" width="77" height="35"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["第"  + $V{PAGE_NUMBER}.toString()  + "/"]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement x="375" y="0" width="50" height="35"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{PAGE_NUMBER}.toString() + "页"]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</pageFooter>
</jasperReport>
