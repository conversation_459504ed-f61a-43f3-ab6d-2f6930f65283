<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="asnDetail_3.7.4" pageWidth="840" pageHeight="150" columnWidth="840" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="sku" class="com.daxia.wms.master.entity.Sku"/>
	<field name="packageInfoDetail" class="com.daxia.wms.master.entity.PackageInfoDetail"/>
	<field name="price" class="java.math.BigDecimal"/>
	<field name="notes" class="java.lang.String"/>
	<field name="shippedQty" class="java.math.BigDecimal"/>
	<detail>
		<band height="20" splitType="Stretch">
			<frame>
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="840" height="20"/>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="139" y="0" width="161" height="20"/>
					<textElement verticalAlignment="Middle">
						<font size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{sku}.getProductCname()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="332" y="0" width="113" height="20"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{shippedQty}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="607" y="0" width="100" height="20"/>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{notes}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="514" y="0" width="93" height="20"/>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{shippedQty}.multiply( $F{price})]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="445" y="0" width="69" height="20"/>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{price}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="300" y="0" width="32" height="20"/>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{packageInfoDetail}.getPackUom()]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
