<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="doDetail_yaowang" pageWidth="745" pageHeight="100" columnWidth="745" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="2.853116706110089"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="frameStyle" mode="Transparent" forecolor="#000000" fill="Solid" pattern="" fontSize="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
			<pen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="detailStyle" fontSize="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box topPadding="2" bottomPadding="2">
			<pen lineWidth="1.0"/>
			<topPen lineWidth="0.0"/>
			<leftPen lineWidth="1.0"/>
			<bottomPen lineWidth="1.0"/>
			<rightPen lineWidth="1.0"/>
		</box>
	</style>
	<style name="lineStyle" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true">
		<box topPadding="0" leftPadding="0" bottomPadding="4"/>
	</style>
	<parameter name="productAmount" class="java.math.BigDecimal"/>
	<parameter name="orderDeliveryFee" class="java.math.BigDecimal"/>
	<parameter name="amountPayable" class="java.math.BigDecimal"/>
	<parameter name="receivable" class="java.math.BigDecimal"/>
	<parameter name="shipQty" class="java.math.BigDecimal"/>
	<parameter name="displayPrice" class="java.lang.Integer"/>
	<parameter name="disCountAmount" class="java.math.BigDecimal"/>
	<field name="sku" class="com.daxia.wms.master.entity.Sku"/>
	<field name="expectedQty" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[qty]]></fieldDescription>
	</field>
	<field name="price" class="java.math.BigDecimal"/>
	<field name="parentId" class="java.lang.Long"/>
	<field name="isDoLeaf" class="java.lang.Boolean"/>
	<field name="isPromote" class="java.lang.Boolean"/>
	<field name="lotNo" class="java.lang.String"/>
	<variable name="summary" class="java.math.BigDecimal">
		<variableExpression><![CDATA[($F{isDoLeaf}  && $F{parentId} != null) ? BigDecimal.ZERO : $F{price}.multiply($F{expectedQty})]]></variableExpression>
	</variable>
	<variable name="qty" class="java.math.BigDecimal">
		<variableExpression><![CDATA[!$F{isDoLeaf} ?  BigDecimal.ZERO : $F{expectedQty}]]></variableExpression>
	</variable>
	<variable name="qtySum" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$V{qty}]]></variableExpression>
	</variable>
	<variable name="sumSummary" class="java.math.BigDecimal" calculation="Sum">
		<variableExpression><![CDATA[$V{summary}]]></variableExpression>
	</variable>
	<columnHeader>
		<band height="30" splitType="Prevent">
			<frame>
				<reportElement x="0" y="0" width="744" height="10"/>
			</frame>
			<frame>
				<reportElement x="0" y="10" width="745" height="20"/>
				<line>
					<reportElement x="0" y="0" width="745" height="1"/>
				</line>
				<staticText>
					<reportElement x="47" y="1" width="90" height="18"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品编码]]></text>
				</staticText>
				<staticText>
					<reportElement x="655" y="1" width="90" height="18"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[金额（元）]]></text>
				</staticText>
				<staticText>
					<reportElement x="238" y="1" width="271" height="18"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品名称]]></text>
				</staticText>
				<staticText>
					<reportElement x="509" y="1" width="79" height="18"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[单价（元）]]></text>
				</staticText>
				<staticText>
					<reportElement x="588" y="1" width="67" height="18"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[数量]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="1" width="47" height="18"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[序号]]></text>
				</staticText>
				<line>
					<reportElement x="0" y="19" width="745" height="1"/>
				</line>
				<staticText>
					<reportElement x="137" y="1" width="101" height="18"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品条码]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Prevent">
			<frame>
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="745" height="20"/>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="lineStyle" stretchType="RelativeToBandHeight" x="0" y="0" width="47" height="20" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Top" markup="styled">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.Integer"><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement style="lineStyle" stretchType="RelativeToBandHeight" x="47" y="0" width="90" height="20"/>
					<textElement verticalAlignment="Top" markup="styled">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{sku}.getProductCode()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement style="lineStyle" stretchType="RelativeToBandHeight" x="655" y="0" width="90" height="20"/>
					<textElement textAlignment="Right" verticalAlignment="Top" markup="styled">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$P{displayPrice} == 0 ? null : (($F{isDoLeaf} && $F{parentId} != null) ? null : $V{summary})]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement style="lineStyle" stretchType="RelativeToBandHeight" x="509" y="0" width="79" height="20"/>
					<textElement textAlignment="Right" verticalAlignment="Top" markup="styled">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$P{displayPrice} == 0 ? null : (($F{isDoLeaf} && $F{parentId} != null) ?  null : $F{price})]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement style="lineStyle" stretchType="RelativeToBandHeight" x="238" y="0" width="271" height="20" isPrintWhenDetailOverflows="true"/>
					<textElement verticalAlignment="Top" markup="none">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{isPromote} ? "(促)" : "") + $F{sku}.getProductCname() + (!$F{isDoLeaf} ? "[ " + $F{expectedQty} + " 组]" : "")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement style="lineStyle" stretchType="RelativeToBandHeight" x="588" y="0" width="67" height="20"/>
					<textElement textAlignment="Right" verticalAlignment="Top" markup="styled">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[!$F{isDoLeaf} ?  null : $F{expectedQty}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="lineStyle" stretchType="RelativeToBandHeight" x="137" y="0" width="101" height="20" isPrintWhenDetailOverflows="true"/>
					<textElement verticalAlignment="Top" markup="styled">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[!$F{isDoLeaf} ? null : ($F{sku}.getEan13() != null ? $F{sku}.getEan13().replaceAll(",",", ") : "")]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
	<summary>
		<band height="63">
			<frame>
				<reportElement x="0" y="0" width="745" height="20"/>
			</frame>
			<frame>
				<reportElement x="0" y="20" width="745" height="42"/>
				<line>
					<reportElement x="0" y="40" width="744" height="1"/>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement x="327" y="2" width="165" height="18">
						<printWhenExpression><![CDATA[$P{displayPrice} == 1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$P{disCountAmount}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="571" y="20" width="173" height="18">
						<printWhenExpression><![CDATA[$P{displayPrice} == 1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$P{receivable} == null ? BigDecimal.ZERO : $P{receivable}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="492" y="20" width="79" height="18"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[应付（元）：]]></text>
				</staticText>
				<textField isBlankWhenNull="false">
					<reportElement x="571" y="2" width="174" height="18"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$V{qtySum}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="327" y="20" width="165" height="18">
						<printWhenExpression><![CDATA[$P{displayPrice} == 1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$P{amountPayable} == null ? BigDecimal.ZERO : $P{amountPayable}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="101" y="20" width="148" height="18">
						<printWhenExpression><![CDATA[$P{displayPrice} == 1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$P{orderDeliveryFee} == null ? BigDecimal.ZERO : $P{orderDeliveryFee}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="101" y="2" width="148" height="18">
						<printWhenExpression><![CDATA[$P{displayPrice} == 1]]></printWhenExpression>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$P{productAmount} == null ? BigDecimal.ZERO : $P{productAmount}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="1" width="745" height="1"/>
				</line>
				<staticText>
					<reportElement x="0" y="2" width="101" height="18"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品金额（元）：]]></text>
				</staticText>
				<staticText>
					<reportElement x="0" y="20" width="101" height="18"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[运费（元）：]]></text>
				</staticText>
				<staticText>
					<reportElement x="249" y="20" width="78" height="18"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[已付（元）：]]></text>
				</staticText>
				<staticText>
					<reportElement x="492" y="2" width="79" height="18"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品总数：]]></text>
				</staticText>
				<staticText>
					<reportElement x="249" y="2" width="78" height="18"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[优惠（元）：]]></text>
				</staticText>
			</frame>
		</band>
	</summary>
</jasperReport>
