<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="stt" pageWidth="705" pageHeight="1020" whenNoDataType="AllSectionsNoDetail" columnWidth="685" leftMargin="10" rightMargin="10" topMargin="0" bottomMargin="0" isFloatColumnFooter="true">
	<property name="ireport.zoom" value="1.210000000000001"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="frameStyle" mode="Transparent" forecolor="#000000" fill="Solid" pattern="" fontSize="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
			<pen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="detailStyle" fontSize="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box>
			<pen lineWidth="1.0"/>
			<topPen lineWidth="0.0"/>
			<leftPen lineWidth="1.0"/>
			<bottomPen lineWidth="1.0"/>
			<rightPen lineWidth="1.0"/>
		</box>
	</style>
	<parameter name="breakPage" class="java.lang.Boolean"/>
	<parameter name="subReport" class="net.sf.jasperreports.engine.JasperReport">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="barcodeUrl" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="sttPrintSubs" class="java.util.List"/>
	<field name="waveNo" class="java.lang.String"/>
	<pageHeader>
		<band height="85">
			<staticText>
				<reportElement x="293" y="13" width="121" height="36"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="22" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[分拣单]]></text>
			</staticText>
			<frame>
				<reportElement x="1" y="60" width="490" height="23" isRemoveLineWhenBlank="true"/>
				<staticText>
					<reportElement x="1" y="2" width="65" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[波次号：]]></text>
				</staticText>
				<textField>
					<reportElement mode="Transparent" x="66" y="2" width="153" height="20" forecolor="#000000" backcolor="#FFFFFF"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font fontName="微软雅黑" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{waveNo}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="220" y="2" width="73" height="20"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[创建时间：]]></text>
				</staticText>
				<textField pattern="yyyy-MM-dd" isBlankWhenNull="true">
					<reportElement mode="Transparent" x="293" y="2" width="172" height="20" forecolor="#000000" backcolor="#FFFFFF"/>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font fontName="微软雅黑" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
				</textField>
			</frame>
			<image isUsingCache="false" isLazy="true">
				<reportElement x="481" y="9" width="195" height="49"/>
				<imageExpression class="java.lang.String"><![CDATA[$P{barcodeUrl} + $F{waveNo}]]></imageExpression>
			</image>
			<frame>
				<reportElement x="0" y="2" width="685" height="5"/>
			</frame>
		</band>
	</pageHeader>
	<detail>
		<band height="55" splitType="Stretch">
			<break>
				<reportElement x="0" y="0" width="685" height="1">
					<printWhenExpression><![CDATA[($P{breakPage} && $V{REPORT_COUNT} > 1) ? Boolean.TRUE: Boolean.FALSE]]></printWhenExpression>
				</reportElement>
			</break>
			<subreport>
				<reportElement x="0" y="1" width="685" height="54"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{sttPrintSubs})]]></dataSourceExpression>
				<subreportExpression class="net.sf.jasperreports.engine.JasperReport"><![CDATA[$P{subReport}]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<pageFooter>
		<band height="38" splitType="Stretch">
			<frame>
				<reportElement x="0" y="0" width="685" height="38"/>
				<staticText>
					<reportElement x="22" y="0" width="103" height="38"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[操作人：]]></text>
				</staticText>
				<staticText>
					<reportElement x="361" y="0" width="130" height="38"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[验收人：]]></text>
				</staticText>
				<textField>
					<reportElement x="552" y="0" width="77" height="38"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["第"  + $V{PAGE_NUMBER}.toString()  + "/"]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Report">
					<reportElement x="631" y="0" width="50" height="38"/>
					<textElement verticalAlignment="Middle">
						<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{PAGE_NUMBER}.toString() + "页"]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</pageFooter>
</jasperReport>
