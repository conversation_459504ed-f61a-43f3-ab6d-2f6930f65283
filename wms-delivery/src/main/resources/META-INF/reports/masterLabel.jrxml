<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="masterLabel" language="groovy" pageWidth="340" pageHeight="190" columnWidth="340" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="1.9487171000000048"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="barcodeUrl" class="java.lang.String"/>
	<parameter name="cartonNum" class="java.lang.Integer"/>
	<parameter name="doNum" class="java.lang.Integer"/>
	<parameter name="station" class="java.lang.String"/>
	<parameter name="lpnNo" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="60" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="14" width="165" height="33"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="21" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[箱绑托盘标签]]></text>
			</staticText>
			<image isLazy="true">
				<reportElement x="170" y="8" width="160" height="50"/>
				<imageExpression class="java.lang.String"><![CDATA[$P{barcodeUrl}+$P{lpnNo}]]></imageExpression>
			</image>
			<frame>
				<reportElement x="0" y="1" width="340" height="7"/>
			</frame>
		</band>
	</pageHeader>
	<detail>
		<band height="90" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="112" y="0" width="228" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="16" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{station}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="0" width="112" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="16" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[站点名称：]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="25" width="112" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="16" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[订单数：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="112" y="25" width="228" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="16" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{doNum}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="50" width="112" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="16" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[总箱数：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="112" y="50" width="228" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="16" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{cartonNum}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="20" splitType="Stretch"/>
	</pageFooter>
</jasperReport>
