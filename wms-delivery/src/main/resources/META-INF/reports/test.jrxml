<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="test" language="groovy" pageWidth="326" pageHeight="440" columnWidth="326" leftMargin="0" rightMargin="0" topMargin="3" bottomMargin="3">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="style1" forecolor="#666600" backcolor="#CCCCCC" fill="Solid" isBold="true" isItalic="true" isUnderline="true">
		<box leftPadding="0">
			<pen lineWidth="1.0"/>
			<topPen lineWidth="1.0"/>
			<leftPen lineWidth="1.0"/>
			<bottomPen lineWidth="1.0"/>
			<rightPen lineWidth="1.0"/>
		</box>
	</style>
	<detail>
		<band height="434" splitType="Stretch">
			<staticText>
				<reportElement style="style1" x="0" y="0" width="326" height="434"/>
				<textElement/>
				<text><![CDATA[ssss]]></text>
			</staticText>
		</band>
	</detail>
</jasperReport>
