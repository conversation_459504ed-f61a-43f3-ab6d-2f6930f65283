<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="invoice" language="groovy" pageWidth="230" pageHeight="464" columnWidth="230" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="2.853116706110137"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="296"/>
	<parameter name="invoiceNo" class="java.lang.String"/>
	<parameter name="soCode" class="java.lang.String"/>
	<parameter name="doNo" class="java.lang.String"/>
	<parameter name="totalRmb" class="java.lang.String"/>
	<parameter name="total" class="java.lang.String"/>
	<parameter name="receiver" class="java.lang.String"/>
	<parameter name="machineNo" class="java.lang.String"/>
	<parameter name="receiveCom" class="java.lang.String"/>
	<parameter name="registerNum" class="java.lang.String"/>
	<parameter name="date" class="java.lang.String"/>
	<parameter name="payer" class="java.lang.String"/>
	<parameter name="sortGridNo" class="java.lang.String"/>
	<parameter name="waveNo" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="content" class="java.lang.String"/>
	<pageHeader>
		<band height="184">
			<line>
				<reportElement mode="Transparent" x="0" y="0" width="230" height="1"/>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement x="165" y="52" width="65" height="17"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{receiver}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="36" y="69" width="194" height="21"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{receiveCom}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="36" y="52" width="108" height="17"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{machineNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="55" y="109" width="175" height="15"/>
				<textElement>
					<font fontName="微软雅黑" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{registerNum}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="55" y="140" width="175" height="30"/>
				<textElement>
					<font fontName="微软雅黑" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{payer}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="55" y="125" width="175" height="14"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{date}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="168" y="18" width="62" height="32"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="24" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{sortGridNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="91" width="99" height="15"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="11" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{invoiceNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="99" y="91" width="130" height="15"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="11" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{waveNo}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="14">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="230" height="14" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{content}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="67">
			<textField isBlankWhenNull="true">
				<reportElement x="36" y="14" width="63" height="14"/>
				<textElement>
					<font fontName="微软雅黑" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{total}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="36" y="33" width="194" height="14"/>
				<textElement>
					<font fontName="微软雅黑" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{totalRmb}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="99" y="14" width="131" height="14"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{soCode}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="47" width="230" height="19"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{doNo}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement mode="Transparent" x="0" y="66" width="230" height="1"/>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</line>
		</band>
	</pageFooter>
</jasperReport>
