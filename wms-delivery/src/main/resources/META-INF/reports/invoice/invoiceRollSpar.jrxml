<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="invoice" language="groovy" pageWidth="230" pageHeight="460" columnWidth="230" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="2.5937424601001244"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="91"/>
	<parameter name="invoiceNo" class="java.lang.String"/>
	<parameter name="soCode" class="java.lang.String"/>
	<parameter name="doNo" class="java.lang.String"/>
	<parameter name="totalRmb" class="java.lang.String"/>
	<parameter name="total" class="java.lang.String"/>
	<parameter name="receiver" class="java.lang.String"/>
	<parameter name="welcomeInfo" class="java.lang.String"/>
	<parameter name="receiveCom" class="java.lang.String"/>
	<parameter name="registerNum" class="java.lang.String"/>
	<parameter name="date" class="java.lang.String"/>
	<parameter name="payer" class="java.lang.String"/>
	<parameter name="sortGridNo" class="java.lang.String"/>
	<parameter name="returnTips" class="java.lang.String"/>
	<parameter name="weibo" class="java.lang.String"/>
	<parameter name="webSite" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="content" class="java.lang.String"/>
	<pageHeader>
		<band height="106">
			<line>
				<reportElement mode="Transparent" x="0" y="0" width="230" height="1"/>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement x="50" y="39" width="180" height="14"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{receiveCom}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="60" y="53" width="170" height="14"/>
				<textElement>
					<font fontName="微软雅黑" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{registerNum}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="199" y="16" width="30" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{sortGridNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="98" y="92" width="56" height="14"/>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[数量]]></text>
			</staticText>
			<staticText>
				<reportElement x="174" y="92" width="55" height="14"/>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[小计]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="92" width="90" height="14"/>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[商品名称]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="53" width="60" height="14"/>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[纳税识别码：]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="39" width="50" height="14"/>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[企业名称：]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement x="1" y="67" width="229" height="24"/>
				<textElement verticalAlignment="Top">
					<font fontName="微软雅黑" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["付款单位(个人)：" + ($P{payer} == null ? "" : $P{payer})]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="15">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="230" height="15" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{content}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="160">
			<textField isBlankWhenNull="true">
				<reportElement x="157" y="4" width="72" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="微软雅黑" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{total}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="35" y="4" width="78" height="14"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{doNo}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement mode="Transparent" x="0" y="159" width="230" height="1"/>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement x="113" y="4" width="44" height="14"/>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[应付金额:]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="4" width="34" height="14"/>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[流水号:]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="1" width="230" height="1"/>
				<graphicElement>
					<pen lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="0" y="3" width="230" height="1"/>
				<graphicElement>
					<pen lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement x="0" y="32" width="50" height="14"/>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[开票日期:]]></text>
			</staticText>
			<textField pattern="yyyy-MM-dd HH:mm:ss" isBlankWhenNull="true">
				<reportElement x="50" y="32" width="107" height="14"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="18" width="34" height="14"/>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[开票人:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="35" y="18" width="122" height="14"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{receiver}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
