<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="invoice" language="groovy" pageWidth="840" pageHeight="480" columnWidth="840" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="2.3579476910001045"/>
	<property name="ireport.x" value="239"/>
	<property name="ireport.y" value="0"/>
	<parameter name="sysDate" class="java.util.Date"/>
	<parameter name="operater" class="java.lang.String"/>
	<parameter name="barcode" class="java.lang.String"/>
	<parameter name="barcodeUrl" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="serialNum" class="java.lang.String"/>
	<field name="payer" class="java.lang.String"/>
	<field name="receiver" class="java.lang.String"/>
	<field name="total" class="java.math.BigDecimal"/>
	<field name="totalRmb" class="java.lang.String"/>
	<field name="doNo" class="java.lang.String"/>
	<field name="website" class="java.lang.String"/>
	<field name="operator" class="java.lang.String"/>
	<field name="detailList" class="java.util.List"/>
	<field name="receiverCodeId" class="java.lang.String"/>
	<field name="printDate" class="java.lang.String"/>
	<field name="industryType" class="java.lang.String"/>
	<field name="waveNo" class="java.lang.String"/>
	<field name="sortGridNo" class="java.lang.String"/>
	<field name="invoiceCode" class="java.lang.String"/>
	<field name="invoiceNo" class="java.lang.String"/>
	<detail>
		<band height="480">
			<textField isBlankWhenNull="true">
				<reportElement x="180" y="366" width="265" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{totalRmb}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="354" y="428" width="142" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{operator}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="173" width="390" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 0]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(0).getItem()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="189" width="390" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 1]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(1).getItem()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="205" width="390" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 2]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(2).getItem()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="221" width="390" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 3]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(3).getItem()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="237" width="390" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 4]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(4).getItem()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="253" width="390" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 5]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(5).getItem()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="269" width="390" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 6]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(6).getItem()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="285" width="390" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 7]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(7).getItem()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="609" y="189" width="82" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 1]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(1).getTotalPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="609" y="173" width="82" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 0]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(0).getTotalPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="609" y="205" width="82" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 2]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(2).getTotalPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="609" y="221" width="82" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 3]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(3).getTotalPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="609" y="237" width="82" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 4]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(4).getTotalPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="609" y="253" width="82" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 5]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(5).getTotalPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="609" y="269" width="82" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 6]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(6).getTotalPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="609" y="285" width="82" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 7]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(7).getTotalPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="497" y="173" width="50" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 0]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{detailList}.get(0).getQty()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="548" y="173" width="60" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 0]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(0).getUnitPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="548" y="189" width="60" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 1]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(1).getUnitPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="548" y="205" width="60" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 2]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(2).getUnitPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="548" y="221" width="60" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 3]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(3).getUnitPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="548" y="237" width="60" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 4]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(4).getUnitPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="548" y="253" width="60" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 5]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(5).getUnitPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="548" y="269" width="60" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 6]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(6).getUnitPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="548" y="285" width="60" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 7]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(7).getUnitPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="497" y="189" width="50" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 1]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{detailList}.get(1).getQty()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="497" y="205" width="50" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 2]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{detailList}.get(2).getQty()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="497" y="221" width="50" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 3]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{detailList}.get(3).getQty()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="497" y="237" width="50" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 4]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{detailList}.get(4).getQty()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="497" y="253" width="50" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 5]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{detailList}.get(5).getQty()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="497" y="269" width="50" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 6]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{detailList}.get(6).getQty()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="497" y="285" width="50" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 7]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{detailList}.get(7).getQty()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="54" y="111" width="112" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[付款单位（个人）名称：]]></text>
			</staticText>
			<staticText>
				<reportElement x="54" y="132" width="112" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[收款单位（个人）名称：]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="166" y="111" width="252" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{payer}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="166" y="132" width="252" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{receiver}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="418" y="111" width="78" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[纳税人识别码：]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="496" y="132" width="195" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{receiverCodeId}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="418" y="132" width="78" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[纳税人识别码：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="301" width="390" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 8]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(8).getItem()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="333" width="390" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 10]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(10).getItem()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="317" width="390" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 9]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(9).getItem()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="54" y="349" width="390" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 11]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(11).getItem()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="497" y="301" width="50" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 8]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{detailList}.get(8).getQty()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="497" y="317" width="50" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 9]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{detailList}.get(9).getQty()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="497" y="333" width="50" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 10]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{detailList}.get(10).getQty()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="497" y="349" width="50" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 11]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{detailList}.get(11).getQty()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="548" y="317" width="60" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 9]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(9).getUnitPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="548" y="333" width="60" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 10]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(10).getUnitPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="548" y="301" width="60" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() >8]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(8).getUnitPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="548" y="349" width="60" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() >11]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(11).getUnitPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="609" y="333" width="82" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 10]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(10).getTotalPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="609" y="301" width="82" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 8]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(8).getTotalPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="609" y="317" width="82" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 9]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(9).getTotalPrice()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="609" y="349" width="82" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 11]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{detailList}.get(11).getTotalPrice()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="57" y="387" width="22" height="40"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<text><![CDATA[备注]]></text>
			</staticText>
			<staticText>
				<reportElement x="419" y="407" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<text><![CDATA[信息]]></text>
			</staticText>
			<staticText>
				<reportElement x="83" y="387" width="57" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<text><![CDATA[订单号：]]></text>
			</staticText>
			<staticText>
				<reportElement x="83" y="407" width="57" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<text><![CDATA[网址：]]></text>
			</staticText>
			<staticText>
				<reportElement x="54" y="428" width="179" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<text><![CDATA[收款单位（个人）名称（章）]]></text>
			</staticText>
			<staticText>
				<reportElement x="300" y="428" width="54" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<text><![CDATA[开票人：]]></text>
			</staticText>
			<staticText>
				<reportElement x="497" y="428" width="111" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<text><![CDATA[复核人：]]></text>
			</staticText>
			<staticText>
				<reportElement x="54" y="366" width="126" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<text><![CDATA[合计人民币（大写）]]></text>
			</staticText>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement x="497" y="366" width="194" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{total}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="140" y="407" width="278" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{website}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="140" y="387" width="278" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{doNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="445" y="366" width="51" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<text><![CDATA[￥]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="445" y="221" width="51" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 3]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(3).getUom()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="445" y="269" width="51" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 6]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(6).getUom()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="445" y="173" width="51" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 0]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(0).getUom()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="445" y="317" width="51" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 9]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(9).getUom()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="445" y="205" width="51" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 2]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(2).getUom()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="445" y="333" width="51" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 10]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(10).getUom()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="445" y="189" width="51" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 1]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(1).getUom()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="445" y="253" width="51" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 5]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(5).getUom()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="445" y="349" width="51" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() >11]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(11).getUom()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="445" y="237" width="51" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 4]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(4).getUom()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="445" y="301" width="51" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() >8]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(8).getUom()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="445" y="285" width="51" height="16" isPrintWhenDetailOverflows="true">
					<printWhenExpression><![CDATA[$F{detailList}.size() > 7]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{detailList}.get(7).getUom()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="450" y="53" width="107" height="28"/>
				<textElement>
					<font fontName="微软雅黑" size="20" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{serialNum}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement x="0" y="0" width="840" height="6"/>
			</frame>
			<staticText>
				<reportElement x="54" y="153" width="390" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<text><![CDATA[项目]]></text>
			</staticText>
			<staticText>
				<reportElement x="445" y="153" width="51" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<text><![CDATA[单位]]></text>
			</staticText>
			<staticText>
				<reportElement x="497" y="153" width="50" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<text><![CDATA[数量]]></text>
			</staticText>
			<staticText>
				<reportElement x="548" y="153" width="60" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<text><![CDATA[单价]]></text>
			</staticText>
			<staticText>
				<reportElement x="609" y="153" width="82" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<text><![CDATA[金额]]></text>
			</staticText>
			<staticText>
				<reportElement x="419" y="387" width="26" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<text><![CDATA[机打]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="115" y="81" width="227" height="30" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{printDate}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="342" y="81" width="154" height="30"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{industryType}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="445" y="387" width="51" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{sortGridNo}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="497" y="387" width="194" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="13"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{waveNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="552" y="26" width="202" height="20"/>
				<textElement>
					<font fontName="微软雅黑" size="13" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{invoiceNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="552" y="6" width="202" height="20"/>
				<textElement>
					<font fontName="微软雅黑" size="13" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{invoiceCode}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
