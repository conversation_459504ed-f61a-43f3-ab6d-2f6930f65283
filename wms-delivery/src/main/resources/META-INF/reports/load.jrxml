<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="load" pageWidth="780" pageHeight="1070" columnWidth="770" leftMargin="5" rightMargin="5" topMargin="0" bottomMargin="0" isFloatColumnFooter="true">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="349"/>
	<property name="ireport.y" value="217"/>
	<style name="frameStyle" mode="Transparent" forecolor="#000000" fill="Solid" pattern="" fontSize="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
			<pen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="detailStyle" fontSize="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box>
			<pen lineWidth="1.0"/>
			<topPen lineWidth="0.0"/>
			<leftPen lineWidth="1.0"/>
			<bottomPen lineWidth="1.0"/>
			<rightPen lineWidth="1.0"/>
		</box>
	</style>
	<parameter name="subReport" class="net.sf.jasperreports.engine.JasperReport"/>
	<parameter name="breakPage" class="java.lang.Boolean"/>
	<parameter name="barcodeUrl" class="java.lang.String"/>
	<field name="loadNo" class="java.lang.String"/>
	<field name="cartonQty" class="java.lang.Integer"/>
	<field name="subs" class="java.util.List"/>
	<field name="doQty" class="java.lang.Long"/>
	<field name="driverName" class="java.lang.String"/>
	<field name="vechileNo" class="java.lang.String"/>
	<field name="shipper" class="java.lang.String"/>
	<field name="carrierName" class="java.lang.String"/>
	<field name="printTime" class="java.util.Date"/>
	<field name="shipTime" class="java.util.Date"/>
	<pageHeader>
		<band height="154">
			<staticText>
				<reportElement x="7" y="60" width="70" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[交接单号：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="77" y="60" width="176" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{loadNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="316" y="60" width="60" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[总箱数：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="376" y="60" width="134" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{cartonQty}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="65" y="90" width="190" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{shipper}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="376" y="90" width="134" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{carrierName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="316" y="90" width="60" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[配送商：]]></text>
			</staticText>
			<staticText>
				<reportElement x="7" y="119" width="70" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[打印时间：]]></text>
			</staticText>
			<textField pattern="yyyy-MM-dd HH:mm" isBlankWhenNull="true">
				<reportElement x="78" y="119" width="175" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{printTime}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement x="5" y="0" width="1025" height="10"/>
			</frame>
			<staticText>
				<reportElement x="301" y="13" width="184" height="38"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="28" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[出库交接单]]></text>
			</staticText>
			<image isUsingCache="false" isLazy="true">
				<reportElement x="546" y="92" width="184" height="49"/>
				<imageExpression class="java.lang.String"><![CDATA[$P{barcodeUrl} + $F{loadNo}]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="8" y="90" width="57" height="22"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[发货方：]]></text>
			</staticText>
		</band>
	</pageHeader>
	<detail>
		<band height="56" splitType="Stretch">
			<break>
				<reportElement x="0" y="1" width="1049" height="1">
					<printWhenExpression><![CDATA[($P{breakPage} && $V{REPORT_COUNT} > 1) ? Boolean.TRUE: Boolean.FALSE]]></printWhenExpression>
				</reportElement>
			</break>
			<subreport>
				<reportElement stretchType="RelativeToBandHeight" x="5" y="3" width="730" height="50"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{subs})]]></dataSourceExpression>
				<subreportExpression class="net.sf.jasperreports.engine.JasperReport"><![CDATA[$P{subReport}]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<pageFooter>
		<band height="167">
			<textField evaluationTime="Report">
				<reportElement x="683" y="140" width="61" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["共" + $V{PAGE_NUMBER}.toString() + "页"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="602" y="140" width="77" height="25"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["第"  + $V{PAGE_NUMBER}.toString()  + "页"]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<lastPageFooter>
		<band height="167" splitType="Stretch">
			<textField evaluationTime="Report">
				<reportElement x="683" y="139" width="61" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["共" + $V{PAGE_NUMBER}.toString() + "页"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="602" y="139" width="77" height="25"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["第"  + $V{PAGE_NUMBER}.toString()  + "页"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="14" y="60" width="103" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[实收箱数：]]></text>
			</staticText>
			<staticText>
				<reportElement x="14" y="0" width="75" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[发运时间：]]></text>
			</staticText>
			<staticText>
				<reportElement x="275" y="0" width="72" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[司机姓名：]]></text>
			</staticText>
			<staticText>
				<reportElement x="546" y="26" width="70" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[司机签字：]]></text>
			</staticText>
			<staticText>
				<reportElement x="546" y="0" width="70" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[车牌号：]]></text>
			</staticText>
			<staticText>
				<reportElement x="275" y="60" width="103" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[破损箱数：]]></text>
			</staticText>
			<staticText>
				<reportElement x="546" y="60" width="70" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[丢包箱数：]]></text>
			</staticText>
			<staticText>
				<reportElement x="275" y="86" width="103" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[运抵时间：]]></text>
			</staticText>
			<staticText>
				<reportElement x="546" y="86" width="92" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[收货人签字：]]></text>
			</staticText>
			<textField pattern="yyyy-MM-dd HH:mm" isBlankWhenNull="true">
				<reportElement x="89" y="0" width="175" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{shipTime}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="347" y="0" width="163" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{driverName}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="616" y="1" width="124" height="25"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{vechileNo}]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
</jasperReport>
