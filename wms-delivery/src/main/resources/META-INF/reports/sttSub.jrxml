<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="stt" pageWidth="685" pageHeight="975" whenNoDataType="AllSectionsNoDetail" columnWidth="685" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true">
	<property name="ireport.zoom" value="1.210000000000001"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="frameStyle" mode="Transparent" forecolor="#000000" fill="Solid" pattern="" fontSize="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
			<pen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="detailStyle" fontSize="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<box>
			<pen lineWidth="1.0"/>
			<topPen lineWidth="0.0"/>
			<leftPen lineWidth="1.0"/>
			<bottomPen lineWidth="1.0"/>
			<rightPen lineWidth="1.0"/>
		</box>
	</style>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="sortGridNo" class="java.lang.String"/>
	<field name="qty" class="java.math.BigDecimal"/>
	<field name="skuCode" class="java.lang.String"/>
	<field name="skuBarCode" class="java.lang.String"/>
	<field name="skuCname" class="java.lang.String"/>
	<field name="skuEname" class="java.lang.String"/>
	<field name="doNo" class="java.lang.String"/>
	<columnHeader>
		<band height="25" splitType="Stretch">
			<frame>
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="0" y="0" width="685" height="25" forecolor="#000000" backcolor="#FFFFFF"/>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="0" y="0" width="48" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[序号]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="48" y="0" width="100" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品编码]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="148" y="0" width="81" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品条码]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="229" y="0" width="154" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[商品名称]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="383" y="0" width="78" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[规格]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="461" y="0" width="63" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[分拣格]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="524" y="0" width="60" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[数量]]></text>
				</staticText>
				<staticText>
					<reportElement style="frameStyle" stretchType="RelativeToTallestObject" x="584" y="0" width="99" height="25"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<text><![CDATA[发货单号]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="25" splitType="Prevent">
			<frame>
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="685" height="25"/>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" mode="Transparent" x="524" y="0" width="60" height="25" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font fontName="微软雅黑" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{qty}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" mode="Transparent" x="383" y="0" width="78" height="25" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font fontName="微软雅黑" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{skuEname}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" mode="Transparent" x="461" y="0" width="63" height="25" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font fontName="微软雅黑" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{sortGridNo}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" mode="Transparent" x="148" y="0" width="81" height="25" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font fontName="微软雅黑" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{skuBarCode}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" x="0" y="0" width="48" height="25" isPrintWhenDetailOverflows="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="微软雅黑" size="13" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{REPORT_COUNT}.toString()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" mode="Transparent" x="48" y="0" width="100" height="25" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font fontName="微软雅黑" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{skuCode}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" mode="Transparent" x="229" y="0" width="154" height="25" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font fontName="微软雅黑" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{skuCname}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
					<reportElement style="detailStyle" stretchType="RelativeToBandHeight" mode="Transparent" x="584" y="0" width="99" height="25" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF"/>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
						<font fontName="微软雅黑" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{doNo}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
