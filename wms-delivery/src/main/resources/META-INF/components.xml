<?xml version="1.0" encoding="UTF-8"?>
<components xmlns="http://jboss.com/products/seam/components"
	xmlns:core="http://jboss.com/products/seam/core" 
	xmlns:persistence="http://jboss.com/products/seam/persistence"
	xmlns:security="http://jboss.com/products/seam/security"
	xmlns:transaction="http://jboss.com/products/seam/transaction"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://jboss.com/products/seam/core http://jboss.com/products/seam/core-2.0.xsd                   
		http://jboss.com/products/seam/persistence http://jboss.com/products/seam/persistence-2.0.xsd                  
		http://jboss.com/products/seam/security http://jboss.com/products/seam/security-2.0.xsd                  
		http://jboss.com/products/seam/mail http://jboss.com/products/seam/mail-2.0.xsd                  
		http://jboss.com/products/seam/async  http://jboss.com/products/seam/async-2.0.xsd                       
		http://jboss.com/products/seam/bpm http://jboss.com/products/seam/bpm-2.0.xsd                             
		http://jboss.com/products/seam/transaction http://jboss.com/products/seam/transaction-2.0.xsd                  
		http://jboss.com/products/seam/web http://jboss.com/products/seam/web-2.0.xsd
		http://jboss.com/products/seam/components http://jboss.com/products/seam/components-2.0.xsd">
	
	<import>com.daxia.wms.delivery</import>
	
	<!-- 微便利、SLC自动处理流程 -->
	<component class="com.daxia.wms.delivery.load.service.impl.AutoLoadHandler" 
		name="autoLoadHandler" auto-create="true">
	</component>
	
	<component class="com.daxia.wms.delivery.recheck.service.impl.AutoRecheckHandler" 
		name="autoRecheckHandler" auto-create="true">
		<property name="nextHandler">#{autoLoadHandler}</property>
	</component>
	
	<component class="com.daxia.wms.delivery.sort.service.impl.AutoSortHandler" 
		name="autoSortHandler" auto-create="true">
		<property name="nextHandler">#{autoRecheckHandler}</property>
	</component>
	
	<component class="com.daxia.wms.delivery.pick.service.impl.AutoPickHandler" 
		name="autoPickHandler" auto-create="true">
		<property name="nextHandler">#{autoSortHandler}</property>
	</component>

	<component class="com.daxia.wms.delivery.wave.service.impl.AutoWaveHandler" 
		name="autoWaveHandler" auto-create="true">
		<property name="nextHandler">#{autoPickHandler}</property>
	</component>
	
	
	<!-- 团购自动处理流程 -->
	<component class="com.daxia.wms.delivery.recheck.service.impl.WaveAutoRecheckHandler"
		name="waveAutoRecheckHandler" auto-create="true">
	</component>

	<component class="com.daxia.wms.delivery.sort.service.impl.WaveAutoSortHandler" 
		name="waveAutoSortHandler" auto-create="true">
		<property name="nextHandler">#{waveAutoRecheckHandler}</property>
	</component>

	<component class="com.daxia.wms.delivery.pick.service.impl.WaveAutoPickHandler" 
		name="waveAutoPickHandler" auto-create="true">
		<property name="nextHandler">#{waveAutoSortHandler}</property>
	</component>
	
	<!-- 雷购自动处理流程 -->
	<component class="com.daxia.wms.delivery.load.service.impl.ToAutoLoadHandler" 
		name="toAutoLoadHandler" auto-create="true">
	</component>
	
	<component class="com.daxia.wms.delivery.recheck.service.impl.ToAutoRecheckHandler" 
		name="toAutoRecheckHandler" auto-create="true">
		<property name="nextHandler">#{toAutoLoadHandler}</property>
	</component>
	
	<component class="com.daxia.wms.delivery.sort.service.impl.ToAutoSortHandler" 
		name="toAutoSortHandler" auto-create="true">
		<property name="nextHandler">#{toAutoRecheckHandler}</property>
	</component>

	<component class="com.daxia.wms.delivery.pick.service.impl.ToAutoPickHandler" 
		name="toAutoPickHandler" auto-create="true">
		<property name="nextHandler">#{toAutoSortHandler}</property>
	</component>
	
	<component class="com.daxia.wms.delivery.wave.service.impl.ToAutoWaveHandler" 
		name="toAutoWaveHandler" auto-create="true">
		<property name="nextHandler">#{toAutoPickHandler}</property>
	</component>
</components>