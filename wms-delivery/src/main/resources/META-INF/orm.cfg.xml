<?xml version="1.0" encoding="UTF-8"?>
<mapping>
	<class>com.daxia.wms.delivery.deliveryorder.entity.MpsOrder</class>
	<class>com.daxia.wms.delivery.deliveryorder.entity.MpsOrderGoods</class>
	<class>com.daxia.wms.delivery.deliveryorder.entity.MpsOrderBom</class>
	<class>com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail</class>
	<class>com.daxia.wms.delivery.deliveryorder.entity.DoPrint</class>
	<class>com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader</class>
	<class>com.daxia.wms.delivery.deliveryorder.entity.OtherDoDetail</class>
	<class>com.daxia.wms.delivery.deliveryorder.entity.OtherDoHeader</class>

	<class>com.daxia.wms.delivery.wave.entity.WaveHeader</class>
	<class>com.daxia.wms.delivery.pick.entity.PickHeader</class>
	<class>com.daxia.wms.delivery.pick.entity.PickTask</class>
	<class>com.daxia.wms.delivery.sort.entity.SortingTask</class>
	
	<class>com.daxia.wms.delivery.load.entity.LoadHeader</class>
	<class>com.daxia.wms.delivery.load.entity.LoadDetail</class>
	
	<class>com.daxia.wms.delivery.recheck.entity.CartonHeader</class>
	<class>com.daxia.wms.delivery.recheck.entity.CartonDetail</class>
	
	<class>com.daxia.wms.delivery.task.repick.entity.ReversePickHeader</class>
	<class>com.daxia.wms.delivery.task.repick.entity.ReversePickTask</class>
	<class>com.daxia.wms.delivery.task.repick.entity.ReversePickContainer</class>
	
	<class>com.daxia.wms.delivery.task.replenish.entity.ReplenishHeader</class>
	<class>com.daxia.wms.delivery.task.replenish.entity.ReplenishTask</class>
	<class>com.daxia.wms.delivery.task.replenish.entity.ReplMoveTask</class>

	<class>com.daxia.wms.delivery.crossorder.entity.CrossSeedHeader</class>
	<class>com.daxia.wms.delivery.crossorder.entity.CrossSeedDetail</class>

	<!-- CROSS DOCK -->
	<class>com.daxia.wms.delivery.load.entity.TOCrossDockHeader</class>
	<class>com.daxia.wms.delivery.load.entity.TOCrossDockDetail</class>	
	
	<class>com.daxia.wms.delivery.invoice.entity.InvoiceHeader</class>
	<class>com.daxia.wms.delivery.invoice.entity.InvoiceDetail</class>
	
	<class>com.daxia.wms.delivery.deliveryorder.entity.DoExceptionLog</class>
	<!--订单缺货明细-->
	<class>com.daxia.wms.delivery.deliveryorder.entity.DoLackDetail</class>
	<class>com.daxia.wms.delivery.deliveryorder.entity.DoLackHeader</class>
	<class>com.daxia.wms.delivery.invoice.entity.InvoiceNo</class>
	<class>com.daxia.wms.delivery.invoice.entity.InvoiceBook</class>
	
	<!-- 订单分配 -->
	<class>com.daxia.wms.delivery.deliveryorder.entity.DoAllocateHeader</class>
	<class>com.daxia.wms.delivery.deliveryorder.entity.DoAllocateDetail</class>
	<class>com.daxia.wms.delivery.deliveryorder.entity.CarrierLog</class>
	
	<!-- 容器使用日志实体 -->
	<class>com.daxia.wms.delivery.container.entity.ContainerLog</class>
	<class>com.daxia.wms.delivery.container.entity.PktContainerDetail</class>
	<class>com.daxia.wms.delivery.container.entity.PktContainerHeader</class>
	<class>com.daxia.wms.delivery.load.entity.ReShipDo</class>
	<!-- 发货单历史数据 -->
	<class>com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeaderHis</class>
	<!-- 发货单明细历史数据 -->
	<class>com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetailHis</class>
	<!-- 装箱单历史数据 -->
	<class>com.daxia.wms.delivery.recheck.entity.CartonHeaderHis</class>
	<!-- 装箱明细历史数据 -->
	<class>com.daxia.wms.delivery.recheck.entity.CartonDetailHis</class>
	<!--装车单头历史信息实体 -->
	<class>com.daxia.wms.delivery.load.entity.LoadHeaderHis</class>
	<!--装车单明细历史信息实体 -->
	<class>com.daxia.wms.delivery.load.entity.LoadDetailHis</class>
	<!-- 逆向交接单 -->
	<class>com.daxia.wms.delivery.load.entity.ReversalHandoverHeader</class>
	<!-- 逆向交接单明细 -->
	<class>com.daxia.wms.delivery.load.entity.ReversalHandoverDetail</class>
	<!-- 订单波次扩展表实体 -->
	<class>com.daxia.wms.delivery.deliveryorder.entity.DoWaveEx</class>
	<!-- 订单打印扩展表 -->
	<class>com.daxia.wms.delivery.deliveryorder.entity.DoHeaderPrintEx</class>
	<!--包材推荐日志 -->
	<class>com.daxia.wms.delivery.recheck.entity.PackMaterialLog</class>
	<!--包材推荐日志 -->
	<class>com.daxia.wms.delivery.recheck.entity.PackMaterialLog</class>
	<!--包材推荐日志 -->
	<class>com.daxia.wms.delivery.recheck.entity.PackMaterialLog</class>
	<!--DO单预约信息 -->
	<class>com.daxia.wms.delivery.deliveryorder.entity.DoReservationEx</class>
	<!--越库装箱实体类 -->
	<class>com.daxia.wms.delivery.load.entity.CrossDockPacking</class>
	<class>com.daxia.wms.delivery.load.entity.CrossDockPackingCartonNo</class>
	<!--逾期退单管理 -->
	<class>com.daxia.wms.delivery.load.entity.OverdueRtv</class>
	<class>com.daxia.wms.delivery.deliveryorder.entity.SpecialDoLabel</class>
	<!-- 取消箱日志 -->
	<class>com.daxia.wms.delivery.recheck.entity.CartonCancelLog</class>
	<class>com.daxia.wms.delivery.deliveryorder.entity.DoNotice</class>
	<class>com.daxia.wms.delivery.transport.entity.DoTransportReport</class>
	<class>com.daxia.wms.delivery.backup.CfgBackupTable</class>
	<!-- 临时箱表 -->
	<class>com.daxia.wms.delivery.recheck.entity.TempCarton</class>
	<class>com.daxia.wms.delivery.recheck.entity.WaveRandomCheck</class>
	<class>com.daxia.wms.delivery.recheck.entity.WaveRandomCheckDetail</class>

    <class>com.daxia.wms.delivery.deliveryorder.entity.OrderLog</class>
    <class>com.daxia.wms.delivery.deliveryorder.entity.TempAlloc</class>

    <class>com.daxia.wms.delivery.deliveryorder.entity.MergePrintHeader</class>
    <class>com.daxia.wms.delivery.deliveryorder.entity.MergePrintDetail</class>
</mapping>