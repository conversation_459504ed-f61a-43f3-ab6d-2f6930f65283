<?xml version="1.0" encoding="UTF-8"?>
<pages login-view-id="/login.xhtml"
       no-conversation-view-id="/home.xhtml"
       xmlns="http://jboss.com/products/seam/pages"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://jboss.com/products/seam/pages http://jboss.com/products/seam/pages-2.0.xsd">
    <page login-required="true" view-id="/delivery/*"/>
    <page action="#{assignDetailAction.initializePage()}" view-id="/delivery/manualReservationList.xhtml">
        <param name="doDetailId" value="#{assignDetailAction.doDetailId}"/>
    </page>
    <!--设置点击deliveryOrderList页面do编号时的导航规则 -->
    <page action="#{deliveryOrderAction.view()}" view-id="/delivery/deliveryOrderDetailList.xhtml">
        <param name="doHeaderId" value="#{deliveryOrderAction.doHeaderId}"/>
        <param name="doNo" value="#{deliveryOrderAction.doNo}"/>
        <param name="isHis" value="#{deliveryOrderAction.doHeaderFilter.queryHis}"/>
    </page>
    <page action="#{mpsOrderAction.preEdit()}" view-id="/delivery/mpsOrderDetail.xhtml">
        <param name="id" value="#{mpsOrderAction.id}"/>
    </page>
    <page action="#{mpsOrderAction.showBom()}" view-id="/delivery/mpsOrderGoodsBom.xhtml">
        <param name="id" value="#{mpsOrderAction.mpsOrderGoodsId}"/>
    </page>

    <page action="#{otherDoDetailAction.initializePage()}" view-id="/delivery/otherDoDetailList.xhtml">
        <param name="doHeaderId" value="#{otherDoDetailAction.doHeaderId}"/>
    </page>
    <page action="#{clearDeskContainerAction.selectContainerInfo()}" view-id="/delivery/clearContainer.xhtml">
        <param name="packingDeskNo" value="#{clearDeskContainerAction.packingDeskNo}"/>
    </page>
    <page action="#{otherDoDetailAction.preAdd()}" view-id="/delivery/doOtherDetailEdit.xhtml">
        <param name="doHeaderId" value="#{otherDoDetailAction.doHeaderId}"/>
    </page>

    <page action="#{deliveryOrderAction.viewAlcDetailInfo()}" view-id="/delivery/alcDetailList.xhtml">
        <param name="doHeaderId" value="#{deliveryOrderAction.doHeaderId}"/>
        <param name="isHis" value="#{deliveryOrderAction.doHeaderFilter.queryHis}"/>
    </page>
    <page action="#{crossDockDeliverAction.view()}" view-id="/delivery/cdDetailList.xhtml">
        <param name="crossDockHeaderId" value="#{crossDockDeliverAction.crossDockHeaderId}"/>
    </page>
    <!--设置点击deliveryOrderFrozen页面do编号时的导航规则 -->
    <page action="#{deliveryOrderAction.initializeFrozenPage()}" view-id="/delivery/deliveryOrderFrozen.xhtml">
        <param name="doHeaderId" value="#{deliveryOrderAction.doHeaderId}"/>
    </page>
    <!-- 设置点击deliveryOrderModifyCarrier页面do编号时的导航规则  -->
    <page action="#{deliveryOrderAction.initializeModifyCarrierPage()}"
          view-id="/delivery/deliveryOrderModifyCarrier.xhtml">
        <param name="doHeaderId" value="#{deliveryOrderAction.doHeaderId}"/>
    </page>
    <!-- 设置点击deliveryOrderAddLog页面do编号时的导航规则  -->
    <page action="#{deliveryOrderAction.initializeAddLogPage()}"
          view-id="/delivery/deliveryOrderAddLog.xhtml">
        <param name="doHeaderId" value="#{deliveryOrderAction.doHeaderId}"/>
    </page>
    <!--设置点击deliveryOrderDetailList页面分配明细时的导航规则 -->
    <page action="#{pktAction.initializePage()}" view-id="/delivery/deliveryOrderAssignDetailList.xhtml">
        <param name="doDetailId" value="#{pktAction.doDetailId}"/>
    </page>
    设置点击waveList页面波次编号时的导航规则 -->
    <page action="#{waveAction.view()}" view-id="/delivery/waveDetailList.xhtml">
        <param name="waveHeaderId" value="#{waveAction.waveId}"/>
    </page>
    <!--设置点击waveDetailList页面do编号时的导航规则 -->
    <page action="#{deliveryOrderAction.view()}" view-id="/delivery/deliveryOrderDetailListView.xhtml">
        <param name="doHeaderId" value="#{deliveryOrderAction.doHeaderId}"/>
        <param name="isHis" value="#{deliveryOrderAction.doHeaderFilter.queryHis}"/>
    </page>
    <page action="#{pktAction.initializePage()}" view-id="/delivery/deliveryOrderAssignDetailListView.xhtml">
        <param name="doDetailId" value="#{pktAction.doDetailId}"/>
    </page>
    <!--查找分拣信息  -->
    <page action="#{sortingAction.querySortingInfo()}" view-id="/delivery/sortingInfo.xhtml">
        <param name="waveNo" value="#{sortingAction.waveNo}"/>
        <param name="sorted" value="#{sortingAction.sorted}"/>
    </page>
    <!--查找分拣信息  -->
    <page action="#{deliveryOrderAction.initialize4SortedPage()}" view-id="/delivery/deliveryOrderList.xhtml">
        <param name="sortGridNo" value="#{deliveryOrderAction.sortGridNo}"/>
        <param name="waveNo" value="#{deliveryOrderAction.waveNo}"/>
        <param name="pageFm" value="#{deliveryOrderAction.pageFm}"/>
    </page>
    <page action="#{loadAction.editLoad()}" view-id="/delivery/entruckLoadingEditCareNoCarrier.xhtml">
        <param name="loadHeaderId" value="#{loadAction.loadHeaderId}"/>
        <param name="loadType" value="#{loadAction.loadType}"/>
        <param name="queryHistory" value="#{loadAction.queryHistory}"/>
    </page>

    <!-- 仓库发货装车明细  -->
    <page action="#{loadDetailAction.warehouseLoadingDetail()}" view-id="/delivery/warehouseLoadingDetail.xhtml">
        <param name="loadHeaderId" value="#{loadDetailAction.loadDetailFilter.loadHeaderId}"/>
    </page>

    <!-- 仓库发货  -->
    <page action="#{loadAction.warehouseLoading()}" view-id="/delivery/warehouseLoading.xhtml">
        <param name="loadHeaderId" value="#{loadAction.loadHeaderId}"/>
        <param name="loadType" value="#{loadAction.loadType}"/>
    </page>
    <!-- 箱信息查询  -->
    <page action="#{queryReCheckInfoAction.initializePage()}" view-id="/delivery/queryReCheckInfo.xhtml">
        <param name="doId" value="#{queryReCheckInfoAction.hrefDoId}"/>
    </page>

    <!--进入 entruckLoadingList页面初始化
     <page action="#{loadAction.editLoad()}" view-id="/delivery/entruckLoadingList.xhtml"></page>   shizhiyuan 2011-6-1 注释 加载entruckLoadingList.xhtml无需调用初始化方法-->
    <page action="#{cartonAction.initializePage()}" view-id="/delivery/cartonDetailListView.xhtml">
        <param name="cartonNo" value="#{cartonAction.cartonNo}"/>
        <param name="queryHistory" value="#{cartonAction.queryHistory}"/>
    </page>
    <!--一码多品情况  -->
    <page action="#{productBarcodeAction.queryBarcodeListByScan()}" view-id="/master/barcodePopup.xhtml">
        <param name="barcode" value="#{productBarcodeAction.barcode}"/>
    </page>
    <page view-id="/delivery/clearCarton.xhtml">
        <param name="order" value="#{clearCartonAction.orderId}"/>
        <param name="orderNo" value="#{clearCartonAction.orderNo}"/>
    </page>
    <page view-id="/delivery/clearProduct.xhtml">
        <param name="order" value="#{clearProductAction.orderId}"/>
        <param name="orderNo" value="#{clearProductAction.orderNo}"/>
    </page>
    <!-- 补货任务 -->
    <page action="#{repTaskAction.query()}" view-id="/delivery/replTaskList.xhtml">
        <param name="replHeaderId" value="#{repTaskAction.replHeaderId}"/>
    </page>
    <page action="#{replMoveTaskAction.init()}" view-id="/delivery/replMoveTaskList.xhtml">
        <param name="taskId" value="#{replMoveTaskAction.taskId}"/>
    </page>
    <!-- 返拣任务  shizhiyuan 2011 -6 -30 add -->
    <page action="#{rePickTaskAction.initRePickListPage()}" view-id="/delivery/reverseTaskList.xhtml">
        <param name="reversePickHeaderId" value="#{rePickTaskAction.reversePickHeaderId}"/>
    </page>
    <!-- 查看拣货单  shizhiyuan 2011 -8 -30 add -->
    <page action="#{pktAction.initPktHeaderPage()}" view-id="/delivery/pktHeaderList.xhtml">
        <param name="waveId" value="#{pktAction.waveId}"/>
        <param name="isMerged" value="#{pktAction.isMerged}"/>
    </page>
    <!-- 查看拣货单明细 -->
    <page action="#{pickTaskAction.initPickTaskPage()}" view-id="/delivery/pickTaskList.xhtml">
        <param name="pktHeaderId" value="#{pickTaskAction.pktHeaderId}"/>
        <param name="queryType" value="#{pickTaskAction.queryType}"/>
        <param name="containerNo" value="#{pickTaskAction.containerNo}"/>
        <param name="pktNo" value="#{pickTaskAction.pktNo}"/>
    </page>
    <!-- 查看CROSS_DOCK明细  -->
    <page action="#{crossDockDeliverAction.showCroDockInfo()}" view-id="/delivery/crossDockInfo.xhtml">
        <param name="id" value="#{crossDockDeliverAction.crossDockHeaderId}"/>
    </page>
    <!-- 查看集货管理  -->
    <page action="#{mergeManageAction.initPage()}" view-id="/delivery/mergeManage.xhtml"/>
    <page action="#{mergeOutAction.initPage()}" view-id="/delivery/mergeOut.xhtml">
        <param name="mergeLocCode" value="#{mergeOutAction.mergeLocCode}"/>
        <param name="waveNo" value="#{mergeOutAction.waveNo}"/>
    </page>
    <!-- 集货入区查询对应的SKU拣货任务明细  -->
    <page action="#{mergeOutQueryAction.query()}" view-id="/delivery/containersToOut.xhtml"/>
    <!-- 集货入区查询对应的SKU拣货任务明细  -->
    <page action="#{mergeInByContainerAction.queryPickTasks()}" view-id="/delivery/mergeInpickTaskList.xhtml">
        <param name="waveHeaderId" value="#{mergeInByContainerAction.waveHeaderId}"/>
        <param name="containerNoForQuery" value="#{mergeInByContainerAction.containerNoForQuery}"/>
        <param name="queryType" value="#{mergeInByContainerAction.queryType}"/>
    </page>
    <!-- 发表薄遗失管理  -->
    <page action="#{invoiceBookAction.initPage()}" view-id="/delivery/invoiceBookList.xhtml"/>

    <page action="#{containerMgntAction.initializePage()}" view-id="/delivery/containerMgnt.xhtml"/>
    <!-- 发表遗失管理  -->
    <page action="#{invoiceLostAction.initPage()}" view-id="/delivery/invoiceLost.xhtml">
        <param name="invoiceBookId" value="#{invoiceLostAction.invoiceBookId}"/>
    </page>
    <page action="#{doInvoiceListAction.initPage()}" view-id="/delivery/doInvoiceList.xhtml">
        <param name="invoiceHeaderId" value="#{doInvoiceListAction.invoiceHeaderId}"/>
    </page>
    <page action="#{bindInvoiceActoin.initPage()}" view-id="/delivery/invoiceAndBillNo.xhtml">
        <param name="doNo" value="#{bindInvoiceActoin.invoiceFilter.doNo}"/>
    </page>
    <!-- 修改发票绑定  -->
    <page action="#{bindInvoiceActoin.initUpdatePage()}" view-id="/delivery/updateBindInvoice.xhtml">
        <param name="invoiceHeaderId" value="#{bindInvoiceActoin.invoiceHeaderId}"/>
        <param name="sortBy" value="#{bindInvoiceActoin.sortBy}"/>
        <param name="oldNo" value="#{bindInvoiceActoin.oldInvoiceNo}"/>
    </page>
    <!-- 订单分配页面 -->
    <page action="#{doAllocateAction.initialize()}" view-id="/delivery/doAllocate.xhtml"/>
    <page action="#{doAllocateDetailAction.initialize()}" view-id="/delivery/doAllocateDetail.xhtml">
        <param name="isException" value="#{doAllocateDetailAction.isException}"/>
        <param name="allocateHeaderId" value="#{doAllocateDetailAction.allocateHeaderId}"/>
    </page>
    <!-- 人工分配页面 -->
    <page action="#{manualAllocAction.initializePage()}" view-id="/delivery/manualAllocList.xhtml">
        <param name="isException" value="#{manualAllocAction.isException}"/>
        <param name="doAllocateDetailId" value="#{manualAllocAction.doAllocateDetailId}"/>
    </page>
    <!-- 查看发票开票明细信息 -->
    <page action="#{doInvoiceDetailAction.initPage()}" view-id="/delivery/doInvoiceDetailList.xhtml">
        <param name="invoiceHeaderId" value="#{doInvoiceDetailAction.invoiceHeaderId}"/>
    </page>
    <!-- 装箱明细信息页面 -->
    <page action="#{cartonDetailInfoAction.initPage()}" view-id="/delivery/cartonDetailInfo.xhtml">
        <param name="cartonHeaderId" value="#{cartonDetailInfoAction.cartonHeaderId}"/>
        <param name="queryMode" value="#{cartonDetailInfoAction.queryMode}"/>
    </page>
    <page action="#{invoiceBindErrorAction.queryInvoiceError()}" view-id="/delivery/genWaveError.xhtml"/>
    <!-- 越库调拨单发货 -->
    <page action="#{crossDockDetailAction.initPage()}" view-id="/delivery/crossDockDetailList.xhtml">
        <param name="toCrossDockId" value="#{crossDockDetailAction.toCrossDockId}"/>
    </page>
    <page action="#{doNotifyAction.initPage()}" view-id="/delivery/doNotify.xhtml"/>
    <!-- 逆向交接单 -->
    <page action="#{reversalHandoverDetailAction.initialize()}" view-id="/delivery/reversalHandoverHeaderEdit.xhtml">
        <param name="reversalHandoverId" value="#{reversalHandoverDetailAction.reversalHandoverId}"/>
    </page>
    <!-- 分拣破损 -->
    <page action="#{dmBySkuAction.initialize()}" view-id="/delivery/dmBySku.xhtml">
        <param name="waveNo" value="#{dmBySkuAction.waveNo}"/>
    </page>
    <!-- 核拣破损 -->
    <page action="#{checkDmBySkuAction.initialize()}" view-id="/delivery/reCheckDmBySku.xhtml">
        <param name="doNo" value="#{checkDmBySkuAction.doNo}"/>
    </page>
    <page view-id="/delivery/bindSortContainerPage.xhtml">
        <param name="doNo" value="#{bindSortContainerAction.doNo}"/>
    </page>
    <!-- 查看促销单品波次箱信息 -->
    <page action="#{batchGroupWaveAction.openSingleWaveCartonPage()}"
          view-id="/delivery/batchGroupWaveCartonView.xhtml">
        <param name="waveNo" value="#{batchGroupWaveAction.waveNo}"/>
    </page>
    <!-- 查看促销单品波次do信息 -->
    <page action="#{batchGroupWaveAction.openSingleWaveDoPage()}" view-id="/delivery/batchGroupWaveDoInfo.xhtml">
        <param name="waveNo" value="#{batchGroupWaveAction.waveNo}"/>
    </page>
    <!-- 查看促销单品波次carton信息 -->
    <page
            action="#{batchGroupWaveAction.openSingleWaveCartonInfoPage()}"
            view-id="/delivery/batchGroupWaveCartonInfo.xhtml">
        <param name="waveNo" value="#{batchGroupWaveAction.waveNo}"/>
    </page>
    <!-- 查看合单订单信息-->
    <page action="#{combinedOrderAction.initViewPage()}" view-id="/delivery/combinedOrderView.xhtml"/>
    <page action="#{crossDockPackingAction.initPage()}" view-id="/delivery/crossDockPackingInfo.xhtml"/>
    <page action="#{overdueRtvAction.initPage()}" view-id="/delivery/overdueRtvList.xhtml"/>
    <page action="#{forceSortingAction.init()}" view-id="/delivery/forceSorting.xhtml">
        <param name="sotringBinNo" value="#{forceSortingAction.sotringBinNo}"/>
    </page>

    <page action="#{doTransportReportAction.reviewDetail()}" view-id="/delivery/doTransportReportDetail.xhtml">
        <param name="loadId" value="#{doTransportReportAction.loadId}"/>
    </page>
    <page action="#{reCheckLackAction.initReCheckLackPage()}" view-id="/delivery/recheckLack.xhtml"/>
    <page action="#{waveCriterialAction.query()}" view-id="/delivery/waveCriterial.xhtml"/>

    <!-- 显示库存明细 -->
    <page action="#{pktContainerDetailAction.query()}" view-id="/delivery/pktContainerDetail.xhtml">
        <param name="containerNo" value="#{pktContainerDetailAction.containerDetailFilter.containerNo}"/>
        <param name="docNo" value="#{pktContainerDetailAction.containerDetailFilter.docNo}"/>
    </page>

    <page action="#{loadAction.getDoNo()}" view-id="/delivery/queryDoByLoadHeaderId.xhtml">
        <param name="loadHeaderId" value="#{loadAction.loadHeaderId}"/>
    </page>
    <page action="#{waveRandomCheckAction.detail()}" view-id="/delivery/randomCheckDetail.xhtml">
        <param name="taskId" value="#{waveRandomCheckAction.detailPageTaskId}"/>
    </page>
</pages>