<?xml version="1.0" encoding="UTF-8"?>
<diagram model-entity="SeamPagesDiagram">
 <item ID="#delivery#*" NAME="#xdelivery#x*" PATH="/delivery/*"
  SHAPE="24,17,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#manualReservationList.xhtml"
  NAME="#xdelivery#xmanualReservationList.xhtml"
  PATH="/delivery/manualReservationList.xhtml" SHAPE="344,17,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#deliveryOrderDetailList.xhtml"
  NAME="#xdelivery#xdeliveryOrderDetailList.xhtml"
  PATH="/delivery/deliveryOrderDetailList.xhtml" SHAPE="664,17,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#cdDetailList.xhtml"
  NAME="#xdelivery#xcdDetailList.xhtml"
  PATH="/delivery/cdDetailList.xhtml" SHAPE="984,17,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#deliveryOrderFrozen.xhtml"
  NAME="#xdelivery#xdeliveryOrderFrozen.xhtml"
  PATH="/delivery/deliveryOrderFrozen.xhtml" SHAPE="1304,17,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#deliveryOrderAssignDetailList.xhtml"
  NAME="#xdelivery#xdeliveryOrderAssignDetailList.xhtml"
  PATH="/delivery/deliveryOrderAssignDetailList.xhtml"
  SHAPE="1624,17,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#waveDetailList.xhtml"
  NAME="#xdelivery#xwaveDetailList.xhtml"
  PATH="/delivery/waveDetailList.xhtml" SHAPE="24,121,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#deliveryOrderDetailListView.xhtml"
  NAME="#xdelivery#xdeliveryOrderDetailListView.xhtml"
  PATH="/delivery/deliveryOrderDetailListView.xhtml" SHAPE="344,121,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#deliveryOrderAssignDetailListView.xhtml"
  NAME="#xdelivery#xdeliveryOrderAssignDetailListView.xhtml"
  PATH="/delivery/deliveryOrderAssignDetailListView.xhtml"
  SHAPE="664,121,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#sortingInfo.xhtml"
  NAME="#xdelivery#xsortingInfo.xhtml"
  PATH="/delivery/sortingInfo.xhtml" SHAPE="984,121,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#deliveryOrderList.xhtml"
  NAME="#xdelivery#xdeliveryOrderList.xhtml"
  PATH="/delivery/deliveryOrderList.xhtml" SHAPE="1304,121,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#entruckLoadingEditCareNoCarrier.xhtml"
  NAME="#xdelivery#xentruckLoadingEditCareNoCarrier.xhtml"
  PATH="/delivery/entruckLoadingEditCareNoCarrier.xhtml"
  SHAPE="1624,121,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#cartonDetailListView.xhtml"
  NAME="#xdelivery#xcartonDetailListView.xhtml"
  PATH="/delivery/cartonDetailListView.xhtml" SHAPE="24,225,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#master#barcodePopup.xhtml"
  NAME="#xmaster#xbarcodePopup.xhtml" PATH="/master/barcodePopup.xhtml"
  SHAPE="344,225,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#clearCarton.xhtml"
  NAME="#xdelivery#xclearCarton.xhtml"
  PATH="/delivery/clearCarton.xhtml" SHAPE="664,225,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#clearProduct.xhtml"
  NAME="#xdelivery#xclearProduct.xhtml"
  PATH="/delivery/clearProduct.xhtml" SHAPE="984,225,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#replTaskList.xhtml"
  NAME="#xdelivery#xreplTaskList.xhtml"
  PATH="/delivery/replTaskList.xhtml" SHAPE="1304,225,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#replMoveTaskList.xhtml"
  NAME="#xdelivery#xreplMoveTaskList.xhtml"
  PATH="/delivery/replMoveTaskList.xhtml" SHAPE="1624,225,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#reverseTaskList.xhtml"
  NAME="#xdelivery#xreverseTaskList.xhtml"
  PATH="/delivery/reverseTaskList.xhtml" SHAPE="24,329,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#pktHeaderList.xhtml"
  NAME="#xdelivery#xpktHeaderList.xhtml"
  PATH="/delivery/pktHeaderList.xhtml" SHAPE="344,329,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#pickTaskList.xhtml"
  NAME="#xdelivery#xpickTaskList.xhtml"
  PATH="/delivery/pickTaskList.xhtml" SHAPE="664,329,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#crossDockInfo.xhtml"
  NAME="#xdelivery#xcrossDockInfo.xhtml"
  PATH="/delivery/crossDockInfo.xhtml" SHAPE="984,329,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#mergeManage.xhtml"
  NAME="#xdelivery#xmergeManage.xhtml"
  PATH="/delivery/mergeManage.xhtml" SHAPE="1304,329,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#mergeOut.xhtml" NAME="#xdelivery#xmergeOut.xhtml"
  PATH="/delivery/mergeOut.xhtml" SHAPE="1624,329,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#containersToOut.xhtml"
  NAME="#xdelivery#xcontainersToOut.xhtml"
  PATH="/delivery/containersToOut.xhtml" SHAPE="24,433,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#mergeInpickTaskList.xhtml"
  NAME="#xdelivery#xmergeInpickTaskList.xhtml"
  PATH="/delivery/mergeInpickTaskList.xhtml" SHAPE="344,433,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#invoiceBookList.xhtml"
  NAME="#xdelivery#xinvoiceBookList.xhtml"
  PATH="/delivery/invoiceBookList.xhtml" SHAPE="664,433,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#invoiceLost.xhtml"
  NAME="#xdelivery#xinvoiceLost.xhtml"
  PATH="/delivery/invoiceLost.xhtml" SHAPE="984,433,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#doInvoiceList.xhtml"
  NAME="#xdelivery#xdoInvoiceList.xhtml"
  PATH="/delivery/doInvoiceList.xhtml" SHAPE="1304,433,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#invoiceAndBillNo.xhtml"
  NAME="#xdelivery#xinvoiceAndBillNo.xhtml"
  PATH="/delivery/invoiceAndBillNo.xhtml" SHAPE="1624,433,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#updateBindInvoice.xhtml"
  NAME="#xdelivery#xupdateBindInvoice.xhtml"
  PATH="/delivery/updateBindInvoice.xhtml" SHAPE="24,537,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#doAllocate.xhtml"
  NAME="#xdelivery#xdoAllocate.xhtml" PATH="/delivery/doAllocate.xhtml"
  SHAPE="344,537,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#doAllocateDetail.xhtml"
  NAME="#xdelivery#xdoAllocateDetail.xhtml"
  PATH="/delivery/doAllocateDetail.xhtml" SHAPE="664,537,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#manualAllocList.xhtml"
  NAME="#xdelivery#xmanualAllocList.xhtml"
  PATH="/delivery/manualAllocList.xhtml" SHAPE="984,537,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#doInvoiceDetailList.xhtml"
  NAME="#xdelivery#xdoInvoiceDetailList.xhtml"
  PATH="/delivery/doInvoiceDetailList.xhtml" SHAPE="1304,537,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#cartonDetailInfo.xhtml"
  NAME="#xdelivery#xcartonDetailInfo.xhtml"
  PATH="/delivery/cartonDetailInfo.xhtml" SHAPE="1624,537,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#SelfCarrierNullStation.xhtml"
  NAME="#xdelivery#xSelfCarrierNullStation.xhtml"
  PATH="/delivery/SelfCarrierNullStation.xhtml" SHAPE="24,641,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#containerLogs.xhtml"
  NAME="#xdelivery#xcontainerLogs.xhtml"
  PATH="/delivery/containerLogs.xhtml" SHAPE="344,641,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#crossDockDetailList.xhtml"
  NAME="#xdelivery#xcrossDockDetailList.xhtml"
  PATH="/delivery/crossDockDetailList.xhtml" SHAPE="664,641,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#doNotify.xhtml" NAME="#xdelivery#xdoNotify.xhtml"
  PATH="/delivery/doNotify.xhtml" SHAPE="984,641,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#reversalHandoverHeaderEdit.xhtml"
  NAME="#xdelivery#xreversalHandoverHeaderEdit.xhtml"
  PATH="/delivery/reversalHandoverHeaderEdit.xhtml" SHAPE="1304,641,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#dmBySku.xhtml" NAME="#xdelivery#xdmBySku.xhtml"
  PATH="/delivery/dmBySku.xhtml" SHAPE="1624,641,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#reCheckDmBySku.xhtml"
  NAME="#xdelivery#xreCheckDmBySku.xhtml"
  PATH="/delivery/reCheckDmBySku.xhtml" SHAPE="24,745,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#bindSortContainerPage.xhtml"
  NAME="#xdelivery#xbindSortContainerPage.xhtml"
  PATH="/delivery/bindSortContainerPage.xhtml" SHAPE="344,745,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#singleWaveCartonView.xhtml"
  NAME="#xdelivery#xsingleWaveCartonView.xhtml"
  PATH="/delivery/singleWaveCartonView.xhtml" SHAPE="664,745,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#singleWaveDoInfo.xhtml"
  NAME="#xdelivery#xsingleWaveDoInfo.xhtml"
  PATH="/delivery/singleWaveDoInfo.xhtml" SHAPE="984,745,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#singleWaveCartonInfo.xhtml"
  NAME="#xdelivery#xsingleWaveCartonInfo.xhtml"
  PATH="/delivery/singleWaveCartonInfo.xhtml" SHAPE="1304,745,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#combinedOrderView.xhtml"
  NAME="#xdelivery#xcombinedOrderView.xhtml"
  PATH="/delivery/combinedOrderView.xhtml" SHAPE="1624,745,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#crossDockPackingInfo.xhtml"
  NAME="#xdelivery#xcrossDockPackingInfo.xhtml"
  PATH="/delivery/crossDockPackingInfo.xhtml" SHAPE="344,849,0,0"
  TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#overdueRtvList.xhtml"
  NAME="#xdelivery#xoverdueRtvList.xhtml"
  PATH="/delivery/overdueRtvList.xhtml" SHAPE="664,849,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
 <item ID="#delivery#forceSorting.xhtml"
  NAME="#xdelivery#xforceSorting.xhtml"
  PATH="/delivery/forceSorting.xhtml" SHAPE="984,849,0,0" TYPE="page" model-entity="SeamPagesDiagramItem"/>
</diagram>
