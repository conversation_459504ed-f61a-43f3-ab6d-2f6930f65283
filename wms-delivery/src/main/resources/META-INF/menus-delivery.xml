<?xml version="1.0" encoding="UTF-8"?>
<menus>
    <menu id="delivery">
        <name>出库</name>
        <icon>sign-out</icon>
        <permissionCode>delivery</permissionCode>
        <description>出库管理主菜单</description>
        <subMenus>
            <menu>
                <name>订单</name>
                <subMenus>
                    <menu id="delivery.do">
                        <name>发运订单</name>
                        <url>/delivery/deliveryOrderList.xhtml</url>
                        <permissionCode>delivery.do</permissionCode>
                    </menu>
                    <menu id="other.delivery.do">
                        <name>杂项发运订单</name>
                        <url>/delivery/otherDoList.xhtml</url>
                        <permissionCode>other.delivery.do</permissionCode>
                    </menu>
                    <!--<menu id="delivery.overdueRtv">-->
                    <!--<name>逾期退单管理</name>-->
                    <!--<url>/delivery/overdueRtvList.xhtml</url>-->
                    <!--<permissionCode>delivery.overdueRtv</permissionCode>-->
                    <!--</menu>-->
                    <menu id="delivery.invoice">
                        <name>发票</name>
                        <url>/delivery/invoiceList.xhtml</url>
                        <permissionCode>delivery.invoiceNew</permissionCode>
                    </menu>
                    <menu id="delivery.orderLog">
                        <name>订单日志</name>
                        <url>/delivery/orderLog.xhtml</url>
                        <permissionCode>delivery.orderLog</permissionCode>
                    </menu>
                    <!--<menu id="delivery.cross.seed">-->
                        <!--<name>越库分播</name>-->
                        <!--<url>/delivery/crossSeed.xhtml</url>-->
                        <!--<permissionCode>delivery.cross.seed</permissionCode>-->
                    <!--</menu>-->
                    <menu id="delivery.cross.seed">
                        <name>越库分播</name>
                        <url>/w/delivery/crossSeed</url>
                        <permissionCode>delivery.cross.seed</permissionCode>
                    </menu>
                </subMenus>
            </menu>
            <menu>
                <name>波次</name>
                <subMenus>
                    <menu id="delivery.allocate">
                        <name>订单分配</name>
                        <url>/delivery/doAllocate.xhtml</url>
                        <permissionCode>delivery.allocate</permissionCode>
                    </menu>
                    <menu id="delivery.bigWave">
                        <name>生成波次</name>
                        <url>/delivery/bigWaveList.xhtml</url>
                        <permissionCode>delivery.wave</permissionCode>
                    </menu>
                    <menu id="delivery.orderConstruction">
                        <name>订单结构预览</name>
                        <url>/delivery/orderConstruction.xhtml</url>
                        <permissionCode>delivery.orderConstruction</permissionCode>
                    </menu>
                    <menu id="delivery.bigWave">
                        <name>生成提总波次</name>
                        <url>/delivery/batchGenWave.xhtml</url>
                        <permissionCode>delivery.wave</permissionCode>
                    </menu>
                    <menu id="delivery.batchWaveList">
                        <name>批量生成波次</name>
                        <url>/delivery/batchWaveList.xhtml</url>
                        <permissionCode>delivery.batchWaveList</permissionCode>
                    </menu>
                    <menu id="delivery.singlePromoteWave">
                        <name>团购生成波次</name>
                        <url>/delivery/batchGroup.xhtml</url>
                        <permissionCode>delivery.singlePromoteWave</permissionCode>
                    </menu>
                    <menu id="delivery.wave">
                        <name>波次计划</name>
                        <url>/delivery/wavesPlanList.xhtml</url>
                        <permissionCode>delivery.wave</permissionCode>
                    </menu>
                    <!--<menu id="delivery.wave">-->
                    <!--<name>团购波次及箱信息</name>-->
                    <!--<url>/delivery/batchGroupWaveList.xhtml</url>-->
                    <!--<permissionCode>delivery.singleWaveCarton</permissionCode>-->
                    <!--</menu>-->
                    <!-- <menu id="delivery.combineOrderWave">
                   <name>合单订单生成波次</name>
                   <url>/delivery/combineOrderList.xhtml</url>
                   <permissionCode>delivery.combineOrder</permissionCode>
                   <description></description>
               </menu>-->
                </subMenus>
            </menu>
            <menu>
                <name>拣货</name>
                <subMenus>
                    <menu id="delivery.pick">
                        <name>拣货确认</name>
                        <url>/delivery/pick.xhtml</url>
                        <permissionCode>delivery.picking</permissionCode>
                    </menu>
                    <menu id="delivery.pickInfo">
                        <name>拣货单</name>
                        <url>/w/delivery/pick</url>
                        <permissionCode>delivery.pickInfo</permissionCode>
                    </menu>
                </subMenus>
            </menu>
            <menu>
                <name>集货</name>
                <subMenus>
                    <menu id="delivery.pick">
                        <name>集货看板</name>
                        <url>/delivery/mergeBoard.xhtml</url>
                        <permissionCode>delivery.mergeBoard</permissionCode>
                    </menu>
                </subMenus>
            </menu>
            <menu>
                <name>分拣</name>
                <subMenus>
                    <menu id="delivery.sort">
                        <name>分拣</name>
                        <url>/delivery/sorting.xhtml</url>
                        <permissionCode>delivery.sorting</permissionCode>
                    </menu>
                    <menu id="delivery.sortWave">
                        <name>波次分拣</name>
                        <url>/delivery/sortingByWave.xhtml</url>
                        <permissionCode>delivery.sortWave</permissionCode>
                    </menu>
                </subMenus>
            </menu>
            <menu>
                <name>复核</name>
                <subMenus>
                    <menu id="delivery.recheck">
                        <name>核拣装箱</name>
                        <url>/delivery/reCheck.xhtml</url>
                        <permissionCode>delivery.recheck</permissionCode>
                    </menu>
                    <menu id="delivery.quickRecheck">
                        <name>快速核拣装箱</name>
                        <url>/delivery/quickRecheck.xhtml</url>
                        <permissionCode>delivery.quickRecheck</permissionCode>
                    </menu>
                    <menu id="delivery.recheckFor2B">
                        <name>批发业务核拣装箱</name>
                        <url>/delivery/recheckFor2B.xhtml</url>
                        <permissionCode>delivery.recheckFor2B</permissionCode>
                        <description></description>
                    </menu>
                    <menu id="delivery.cross.recheck">
                        <name>越库复核</name>
                        <url>/delivery/crossReCheck.xhtml</url>
                        <permissionCode>delivery.cross.recheck</permissionCode>
                    </menu>
                    <menu id="delivery.recheck.queryReCheckInfo">
                        <name>箱信息查询</name>
                        <url>/delivery/queryReCheckInfo.xhtml</url>
                        <permissionCode>delivery.recheck.record.query</permissionCode>
                    </menu>
                    <menu id="delivery.boxing">
                        <name>拆箱拼箱</name>
                        <url>/delivery/boxing.xhtml</url>
                        <permissionCode>delivery.boxing</permissionCode>
                    </menu>
                    <menu id="delivery.cainiaoWaybill">
                        <name>菜鸟接口</name>
                        <url>/delivery/cainiaoWaybill.xhtml</url>
                        <permissionCode>delivery.cainiaoWaybill</permissionCode>
                    </menu>
                    <menu id="delivery.mergePrint">
                        <name>批发合单打印</name>
                        <url>/delivery/mergePrintList.xhtml</url>
                        <permissionCode>delivery.mergePrint</permissionCode>
                    </menu>
                </subMenus>
            </menu>
            <menu>
                <name>发货</name>
                <subMenus>
                    <menu id="delivery.randomCheck">
                        <name>抽检任务</name>
                        <url>/delivery/randomCheck.xhtml</url>
                        <permissionCode>delivery.randomCheck</permissionCode>
                    </menu>
                    <menu id="delivery.bindTplShipmentNo">
                        <name>运单号绑定</name>
                        <url>/delivery/bindTplShipmentNo.xhtml</url>
                        <permissionCode>delivery.bindTplShipmentNo</permissionCode>
                    </menu>
                    <menu id="delivery.truckLoad">
                        <name>出库交接</name>
                        <url>/delivery/entruckLoadingList.xhtml</url>
                        <permissionCode>delivery.entruckNew</permissionCode>
                    </menu>
                    <menu id="delivery.doTransport">
                        <name>出库运输</name>
                        <url>/delivery/doTransportReport.xhtml</url>
                        <permissionCode>delivery.doTransport</permissionCode>
                    </menu>
                    <menu id="delivery.crossDock">
                        <name>越库发货</name>
                        <url>/delivery/crossDockHeaderList.xhtml</url>
                        <permissionCode>delivery.crossdock</permissionCode>
                        <description></description>
                    </menu>
                    <menu id="delivery.weight">
                        <name>包裹过秤</name>
                        <url>/delivery/doWeigh.xhtml</url>
                        <permissionCode>delivery.weight</permissionCode>
                        <description></description>
                    </menu>
                    <menu id="delivery.weight.groupWeigh">
                        <name>团购称重</name>
                        <url>/delivery/doGroupWeigh.xhtml</url>
                        <permissionCode>delivery.weight.groupWeigh</permissionCode>
                        <description></description>
                    </menu>
                </subMenus>
            </menu>
        </subMenus>
    </menu>
    <menu id="task">
        <name>任务</name>
        <icon>tasks</icon>
        <permissionCode>task</permissionCode>
        <subMenus>
            <menu>
                <name>通用</name>
                <subMenus>
                    <menu id="task.replenish">
                        <name>补货任务</name>
                        <url>/delivery/replTask.xhtml</url>
                        <permissionCode>task.repl</permissionCode>
                    </menu>
                    <menu id="task.repick">
                        <name>返拣任务</name>
                        <url>/delivery/taskRePick.xhtml</url>
                        <permissionCode>task.repick</permissionCode>
                    </menu>
                    <menu id="task.invoiceManage">
                        <name>发票号管理</name>
                        <url>/delivery/invoiceManage.xhtml</url>
                        <permissionCode>task.invoice</permissionCode>
                    </menu>
                    <menu id="task.containerMgnt">
                        <name>容器管理</name>
                        <url>/delivery/containerMgnt.xhtml</url>
                        <permissionCode>task.containerMgnt</permissionCode>
                    </menu>
                    <menu id="task.containerLog">
                        <name>容器日志</name>
                        <url>/delivery/containerLog.xhtml</url>
                        <permissionCode>task.containerLog</permissionCode>
                    </menu>
                </subMenus>
            </menu>
            <menu>
                <name>服务单</name>
                <subMenus>
                    <menu id="mpsOrder.list">
                        <name>服务单列表</name>
                        <url>/delivery/mpsOrder.xhtml</url>
                        <permissionCode>mpsOrder.list</permissionCode>
                    </menu>
                </subMenus>
            </menu>
        </subMenus>
    </menu>
</menus>