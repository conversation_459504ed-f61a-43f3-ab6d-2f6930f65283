package com.daxia.wms.delivery.deliveryorder.service.impl;

import com.daxia.wms.delivery.print.dto.CartonPrintDTO;
import com.google.common.collect.Lists;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.Assert.assertEquals;

@lombok.extern.slf4j.Slf4j
public class TestDeliveryOrderServiceImpl {
    @Test
    public void testSortDetail() {
        DeliveryOrderServiceImpl deliveryOrderService = new DeliveryOrderServiceImpl();
        
        List<CartonPrintDTO.ProductInfo> productInfos = Lists.newArrayList();
    
        //        1	null
        //        0	null
        //        1	not null
        productInfos.add(new CartonPrintDTO.ProductInfo("test_1", BigDecimal.valueOf(1L), 0, "1", null,1L));
        productInfos.add(new CartonPrintDTO.ProductInfo("test_2", BigDecimal.valueOf(2L), 1, "2", "1",2L));
        productInfos.add(new CartonPrintDTO.ProductInfo("test_3", BigDecimal.valueOf(3L), 1, "3", null,3L));
        productInfos.add(new CartonPrintDTO.ProductInfo("test_4", BigDecimal.valueOf(4L), 1, "4", "20",4L));
        
        List<CartonPrintDTO.ProductInfo> results = deliveryOrderService.sortDetail(productInfos);
    
        for (CartonPrintDTO.ProductInfo cp : results) {
            System.out.println(cp.getName() + ":" + cp.getQty() + ":" + cp.getIsDoLeaf());
        }
    
        assertEquals(4, results.size());
    
    }

    @Test
    public void testReplStatus(){

    }
}
