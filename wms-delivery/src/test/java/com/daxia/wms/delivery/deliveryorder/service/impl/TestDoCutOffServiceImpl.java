package com.daxia.wms.delivery.deliveryorder.service.impl;

import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.MvelUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.google.common.collect.Maps;
import org.joda.time.DateTime;
import org.junit.Test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import static org.junit.Assert.assertEquals;

@lombok.extern.slf4j.Slf4j
public class TestDoCutOffServiceImpl {
    
    private String testCalculateCutOffTime(String script, DeliveryOrderHeader doHeader) {
        Map maps = Maps.newHashMap();
        maps.put("doHeader", doHeader);
        return DateUtil.dateToString((Date) MvelUtil.eval(script, maps), DateUtil.DATETIME_PATTERN);
    }
    
    @Test
    //配置项：interface.do.finishTime
    public void testCalculateCutOffTime() {
        String script = "import org.joda.time.DateTime;\n" + "import java.util.Date;\n" + "\n" + "Date doTime = doHeader.getPayTime() == null ? (doHeader.getDoCreateTime() == null ? new Date() : doHeader.getDoCreateTime()) : "
                + "doHeader.getPayTime();\n" + "DateTime dateTime = new DateTime(doTime);\n" + "\n" + "int hour = dateTime.hourOfDay().get();\n" + "hour = (((Integer)hour/4) + 1) * 4;\n" + "\n" + "dateTime = dateTime" + "" +
                ".withHourOfDay(hour >= 24 ? 23 : hour).withSecondOfMinute(0).withMinuteOfHour(0);\n" + "if(doHeader.getShopId() != null && (doHeader.getShopId() == 1220 || doHeader.getShopId() == 1225 || doHeader.getShopId() " +
                "== 1229 || doHeader.getShopId() == 1228)){\n" + " dateTime  =  dateTime.plusDays(-2);\n" + "}\n" + "return dateTime.toDate();";
    
        DeliveryOrderHeader doHeader = new DeliveryOrderHeader();
        String bTime = "2018-04-28 23:59:59";
        doHeader.setPayTime(DateUtil.valueOf(bTime, DateUtil.DATETIME_PATTERN));
        assertEquals("2018-04-28 23:00:00", testCalculateCutOffTime(script, doHeader));
    
        doHeader = new DeliveryOrderHeader();
        try {
            doHeader.setPayTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2018-04-29 24:00:00"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        assertEquals("2018-04-30 04:00:00", testCalculateCutOffTime(script, doHeader));
    
        doHeader = new DeliveryOrderHeader();
        bTime = "2018-04-29 00:00:00";
        doHeader.setPayTime(DateUtil.valueOf(bTime, DateUtil.DATETIME_PATTERN));
        assertEquals("2018-04-29 04:00:00", testCalculateCutOffTime(script, doHeader));
    }
}
