package com.daxia.wms.exp.dao;

import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.exp.entity.ExpTimeSliceNew;

@Name("expTimeSliceNewDAO")
@lombok.extern.slf4j.Slf4j
public class ExpTimeSliceNewDAO extends HibernateBaseDAO<ExpTimeSliceNew, Long>{

    private static final long serialVersionUID = -7197010068906282460L;

    /**
     * 根据起始id查询时间片
     * 
     * @return
     */
    public ExpTimeSliceNew findTimeSliceNewBySEId(Long startTimeSliceId, Long endTimeSliceId,Long whId) {
        String hql = "from ExpTimeSliceNew o where o.id between :startTimeSliceId and :endTimeSliceId and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setParameter("startTimeSliceId", startTimeSliceId);
        query.setParameter("endTimeSliceId", endTimeSliceId);
        query.setParameter("warehouseId",whId);
        return (ExpTimeSliceNew)query.uniqueResult();
    }
    
    /**
     * 根据起始id,resId查询时间片
     * 
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Object[]> findTimeSliceNewBySEId(Long whId,Long resId) {
        String hql = "select o.start_time,o1.end_time from doc_reservation_header t inner join doc_time_slice_new o on t.res_start_time_id = o.id" +
        		" inner join doc_time_slice_new o1 on  t.res_end_time_id = o1.id  where t.id =:resId " +
        		" and t.warehouse_id = :warehouseId";
        Query query = createSQLQuery(hql);
        query.setParameter("warehouseId",whId);
        query.setParameter("resId",resId);
        return (List<Object[]>)query.list();
    }
}
