package com.daxia.wms.exp.dao.common;

import java.io.Serializable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.persistence.Table;

import org.hibernate.Query;
import org.hibernate.SQLQuery;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.exp.ExpMsgConstans.MsgType;

public abstract class ExpMsgBaseDAO<ExpMsgBase extends Object, ID extends Serializable> extends HibernateBaseDAO {
	
	private static final long serialVersionUID = 1L;
	protected static  String table;
	protected static  Class entityClass;
	
	private Class theEntityClass(){
		if(null==entityClass){
			entityClass=getEntityClass();
		}
		return entityClass;
	}
	
	private String theTable(){
		if(null==table){
			Class entityClass=theEntityClass();
			table=((Table)entityClass.getAnnotation(Table.class)).name();
		}
		return table;
	}

	/**
	 * 用本地化SQL 批量新建msg
	 * selectSql 中必须字段为 插入字段  MSG_REF_ID,MSG_REF_NO,MSG_TEXT,CREATE_TIME
	 * @param msgType
	 * @param selectSql
	 * @param params
	 */
	 protected  void insetMsgBySelectSQL(MsgType msgType,String selectSql,Map<String,Object> params) {
		String table=theTable();
		String sql=	"insert into "+table+"  (MSG_TYPE,MSG_REF_ID,MSG_REF_NO,MSG_TEXT,CREATE_TIME) "+
		"(:msgType,"+selectSql.replaceFirst("[sS][eE][lL][eE][cC][tT]", "")+")";
		SQLQuery q = getSession().createSQLQuery(sql);
		q.setParameter("msgType", msgType.getValue());
		Iterator<Entry<String, Object>> it = params.entrySet().iterator();
		while(it.hasNext()){
			Entry<String, Object> es = it.next();
			q.setParameter(es.getKey(), es.getValue());
		}
		q.executeUpdate();
	}
	/**
	 * 
	 */
	@SuppressWarnings("unchecked")
	public List<ExpMsgBase> findMsgByTypes(MsgType msgType,int maxFailOverTimes,int maxRs) {
		String hql = "from " + getEntityName()+
				" o where o.failOverTimes < ? and o.msgType= ? order by o.id";
		Query query = createQuery(hql);
		query.setParameter(0, maxFailOverTimes);
		query.setParameter(1, msgType.getValue());
		return query.setMaxResults(maxRs).list();
	}
	
	@SuppressWarnings("unchecked")
	public List<ExpMsgBase> findMsgByTypes(MsgType[] msgTypes,int maxFailOverTimes,int maxRs) {
		String hql = "from " + getEntityName()+
				" o where o.failOverTimes < :maxFailOverTimes and o.msgType in (:msgTypes) order by o.id";
		Query query = createQuery(hql);
		query.setParameter("maxFailOverTimes", maxFailOverTimes);
		query.setParameterList("msgTypes", MsgType.getValues(msgTypes));
		return query.setMaxResults(maxRs).list();
	}
	
	@SuppressWarnings("unchecked")
	public List<ExpMsgBase> findMsgByTypes(MsgType[] msgTypes,Integer[] maxFailOverTimes,int maxRs) {
		StringBuilder where=new StringBuilder(" o where (o.failOverTimes < ? and o.msgType = ? )"); ;
		if(msgTypes.length==0 || msgTypes.length!=maxFailOverTimes.length){
			throw new RuntimeException("the length of msgTypes and maxFailOverTimes should be same and larger than 1");
		}
		for(int i=0;i<msgTypes.length;i++){
			where.append(" or (o.failOverTimes < ? and o.msgType = ? )");

		}
		String hql = "from " + getEntityName()+ "" + where +
				" order by o.id";
		Query query = createQuery(hql);
		int index=0;
		for(MsgType msgType:msgTypes){
			query.setParameter(index, maxFailOverTimes);
			query.setParameter(++index, msgType.getValue());
			index++;
		}
		return query.setMaxResults(maxRs).list();
	}
}
