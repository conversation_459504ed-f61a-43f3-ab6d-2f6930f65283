package com.daxia.wms.exp.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.exp.entity.ExpWarehouse;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Name("expWarehouseDAO")
@lombok.extern.slf4j.Slf4j
public class ExpWarehouseDAO extends HibernateBaseDAO<ExpWarehouse, Long> {

    private static final long serialVersionUID = -4108690002228452142L;

    @SuppressWarnings("unchecked")
    public Map<String, String> findByWhId(List<Long> whidList) {
        Map<String, String> map = new HashMap<String, String>();
        String hql = "select o.id, o.warehouseName from ExpWarehouse o where o.id in (:whidList)";
        Query query = createQuery(hql);
        query.setParameterList("whidList", whidList);
        query.setMaxResults(1000);
        List<Object> list = query.list();
        if (!ListUtil.isNullOrEmpty(list)) {
            for (Object obj : list) {
                Object[] oArr = (Object[]) obj;
                String whId = ((Long) oArr[0]).toString();
                String whName = (String) oArr[1];
                map.put(whId, whName);
            }
        }
        return map;
    }

    @SuppressWarnings("unchecked")
    public List<ExpWarehouse> getRunningWh() {
        String hql = "FROM ExpWarehouse o where o.runFlag = :runFlag and o.tenantId = :tenantId";
        Query query = createQuery(hql);
        query.setParameter("runFlag", Long.valueOf(1L));
        query.setParameter("tenantId", ParamUtil.getCurrentTenantId());

        return query.list();
    }

    public boolean isCrossBorderWarehouse(Long warehouseId) {
        return false;
    }
}
