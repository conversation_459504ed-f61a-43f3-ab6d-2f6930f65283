package com.daxia.wms.exp.dao;


import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.exp.entity.ExpTrsShipLog;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@Name("expTrsShipLogDAO")
@lombok.extern.slf4j.Slf4j
public class ExpTrsShipLogDAO extends HibernateBaseDAO<ExpTrsShipLog, Long> {

	private static final long serialVersionUID = -5062591388213105090L;

	@SuppressWarnings("unchecked")
	@Loggable
	public List<ExpTrsShipLog> findShipTrslog(Long docId, Long warehouseId, List<String> transactionTypes) {
		String hql = " from ExpTrsShipLog where docId = :docId and transactionType in (:transactionTypes) and warehouseId = :warehouseId";
		return (List<ExpTrsShipLog>) this.createQuery(hql).setParameterList("transactionTypes", transactionTypes)
				.setParameter("docId", docId).setParameter("warehouseId", warehouseId).list();
	}


	public List<ExpTrsShipLog> findShipTrslog(Long docId, Long warehouseId) {
		String hql = " from ExpTrsShipLog where docId = :docId and warehouseId = :warehouseId";
		return (List<ExpTrsShipLog>) this.createQuery(hql).setParameter("docId", docId).setParameter("warehouseId", warehouseId).list();
	}

	@Loggable
	public List<ExpTrsShipLog> findShipTrslogBySql(Long docId, Long warehouseId, String transactionType) {
		String sql = " select o.ID, o.TASK_ID, o.DOC_ID, o.DOC_NO," + "o.DOC_LINE_ID, o.DOC_TYPE, o.TRANSACTION_TYPE,"
				+ "o.FM_CARGO_OWNER_ID, o.FM_GROSS_WEIGHT, o.FM_SKU_ID," + "o.FM_LOC_ID, o.FM_LPN_NO, o.FM_NET_WEIGHT,"
				+ "o.FM_QTY_EACH, o.FM_QTY, o.FM_UOM," + "o.FM_UOM_QTY, o.FM_VOLUME, o.TO_CARGO_OWNER_ID,"
				+ "o.TO_GROSS_WEIGHT, o.TO_LOC_ID, o.FM_SUPPLIER_ID,"
				+ "o.TO_LOT_ID, o.FM_LOT_ID, o.TO_LPN_NO, o.TO_NET_WEIGHT,"
				+ "o.TO_SKU_ID, o.TO_QTY, o.TO_QTY, o.TO_UOM," + "o.TO_UOM_QTY, o.TO_VOLUME, o.TO_SUPPLIER_ID,"
				+ "o.IS_DAMAGE, o.REASON_CODE, o.REASON_DESCR," + "o.IS_INVERSE, o.INVERSE_TRAN_NO, o.PACK_ID,"
				+ "o.FM_PACK_DETAIL_ID, o.TO_PACK_DETAIL_ID, o.OPERATION_ID,"
				+ "o.MERCHANT_ID, o.TO_WH_ID, o.EDI_SEND_FLAG," + "o.FM_LOC_TYPE, o.TO_LOC_TYPE, o.FM_LOCK_STATUS,"
				+ "o.TO_LOCK_STATUS, o.CREATE_TIME from trs_ship_log o where o.DOC_ID=:docId and o.TRANSACTION_TYPE=:transactionType and o.WAREHOUSE_ID=:warehouseId and o.IS_DELETED=0";
		// log.debug(sql.replaceFirst("[?]", "{0}").replaceFirst("[?]",
		// "{1}"),docId, Constants.DocType.SO.getValue());
		Query query = createSQLQuery(sql).setParameter("docId", docId).setString("transactionType", transactionType)
				.setLong("warehouseId", warehouseId);

		@SuppressWarnings("unchecked")
		List<Object[]> results = query.list();
		if (results == null) {
			return null;
		}
		List<ExpTrsShipLog> expTrsShipLogs = new ArrayList<ExpTrsShipLog>(results.size());

		for (Object[] o : results) {
			expTrsShipLogs.add(new ExpTrsShipLog(null == (Number) o[0] ? null : ((Number) o[0]).longValue(),
					null == (Number) o[1] ? null : ((Number) o[1]).longValue(),
					null == (Number) o[2] ? null : ((Number) o[2]).longValue(), (String) o[3],
					null == (Number) o[4] ? null : ((Number) o[4]).longValue(), (String) o[5], (String) o[6],
					null == (Number) o[7] ? null : ((Number) o[7]).longValue(), (BigDecimal) o[8],
					null == (Number) o[9] ? null : ((Number) o[9]).longValue(),
					null == (Number) o[10] ? null : ((Number) o[10]).longValue(), (String) o[11], (BigDecimal) o[12],
					(BigDecimal) o[13], (String) o[14], (BigDecimal) o[15], (BigDecimal) o[16],
					null == (Number) o[17] ? null : ((Number) o[17]).longValue(), (BigDecimal) o[18],
					null == (Number) o[19] ? null : ((Number) o[19]).longValue(),
					null == (Number) o[20] ? null : ((Number) o[20]).longValue(),
					null == (Number) o[21] ? null : ((Number) o[21]).longValue(),
					null == (Number) o[22] ? null : ((Number) o[22]).longValue(), (String) o[23], (BigDecimal) o[24],
					null == (Number) o[25] ? null : ((Number) o[25]).longValue(), (BigDecimal) o[26],
					(String) o[27], (BigDecimal) o[28], (BigDecimal) o[29],
					null == (Number) o[30] ? null : ((Number) o[30]).longValue(),
					null == (Number) o[31] ? null : ((Number) o[31]).longValue(), (String) o[32], (String) o[33],
					null == (Number) o[34] ? null : ((Number) o[34]).longValue(),
					null == (Number) o[35] ? null : ((Number) o[35]).longValue(),
					null == (Number) o[36] ? null : ((Number) o[36]).longValue(),
					null == (Number) o[37] ? null : ((Number) o[37]).longValue(),
					null == (Number) o[38] ? null : ((Number) o[38]).longValue(), (String) o[39],
					null == (Number) o[40] ? null : ((Number) o[40]).longValue(),
					null == (Number) o[41] ? null : ((Number) o[41]).intValue(),
					null == (Number) o[42] ? null : ((Number) o[42]).intValue(), (String) o[43], (String) o[44],
					null == (Number) o[45] ? null : ((Number) o[45]).intValue(),
					null == (Number) o[46] ? null : ((Number) o[46]).intValue(), (Timestamp) o[47]));
		}
		return expTrsShipLogs;
	}

}
