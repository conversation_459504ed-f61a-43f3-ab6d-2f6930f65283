package com.daxia.wms.exp.dao;

import java.math.BigDecimal;
import java.math.BigInteger;

import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.criterion.Restrictions;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.exp.entity.ExpTOCrossDockHeader;

/**
* CrossDockHeader实体访问对像
*/
@SuppressWarnings("serial")
@Name("expTOCrossDockHeaderDAO")
@lombok.extern.slf4j.Slf4j
public class ExpTOCrossDockHeaderDAO extends HibernateBaseDAO<ExpTOCrossDockHeader, Long> {

    public Long getSupplierIdById(Long id) {
        Query query = this
            .createSQLQuery("SELECT asn.supplier_id FROM doc_asn_header asn WHERE asn.id = (SELECT asn_header_id FROM doc_crdock_header o where o.id = :id)");
        query.setLong("id", id);
        query.setMaxResults(1);
        BigInteger supplierId =  (BigInteger) query.uniqueResult();
        return supplierId.longValue();
    }
    
    public ExpTOCrossDockHeader findBydoNo(String doNO){
        Criteria cri = this.getSession().createCriteria(ExpTOCrossDockHeader.class);
        cri.add(Restrictions.eq("doNo", doNO));
        cri.setMaxResults(1);
        return (ExpTOCrossDockHeader)cri.uniqueResult();
	}
}
