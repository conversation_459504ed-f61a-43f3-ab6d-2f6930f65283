package com.daxia.wms.exp.dao;

import com.daxia.dubhe.api.internal.util.NumberUtils;
import com.daxia.dubhe.api.internal.util.StrUtils;
import com.daxia.dubhe.api.wms.request.StockLedgerQueryRequest;
import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.exp.entity.ExpStockSendLog;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Name("expStockSendLogDAO")
@lombok.extern.slf4j.Slf4j
public class ExpStockSendLogDAO extends HibernateBaseDAO<ExpStockSendLog, Long> {

    private static final long serialVersionUID = 4614893445757853106L;


    public List<ExpStockSendLog> query(StockLedgerQueryRequest request){
        StringBuilder sql = new StringBuilder();
        sql.append(" select ms.original_id,merchant.original_id,supplier.original_id,esl.* ");
        sql.append(" from exp_stock_send_log esl ");
        sql.append(" inner join md_sku ms on esl.product_id = ms.id ");
        sql.append(" left  join md_merchant merchant on esl.merchant_id = merchant.id ");
        sql.append(" left join md_supplier supplier on esl.supplier_id = supplier.id ");
        sql.append(" where ms.original_id =:productId ");
        sql.append(" and transaction_time >= :startTime ");
        sql.append(" and transaction_time <= :endTime ");

        Map<String,Object> params = new HashMap<String,Object>();
        params.put("productId",request.getProductId());
        params.put("startTime",request.getStartTime());
        params.put("endTime",request.getEndTime());
        if(StringUtil.isNotEmpty(request.getMerchantId())){
            sql.append(" and merchant.original_id =:merchantId ");
            params.put("merchantId",request.getMerchantId());
        }

        if(null != request.getIsDamaged()){
            sql.append(" and esl.is_damaged =:isDamaged ");
            params.put("isDamaged",request.getIsDamaged());
        }

        if(StringUtil.isNotEmpty(request.getSupplierId())){
            sql.append(" and supplier.original_id =:supplierId ");
            params.put("supplierId",request.getSupplierId());
        }

        if(StringUtil.isNotEmpty(request.getLotNo())){
            sql.append(" and esl.lot_no =:lotNo ");
            params.put("lotNo",request.getLotNo());
        }

        if(null != request.getProductionTime()){
            sql.append(" and esl.production_time =:productionTime ");
            params.put("productionTime",request.getProductionTime());
        }

        if(null != request.getExpireTime()){
            sql.append(" and esl.expire_time =:expireTime ");
            params.put("expireTime",request.getExpireTime());
        }

        if(null != request.getWarehouseTime()){
            sql.append(" and esl.warehouse_time =:warehouseTime ");
            params.put("warehouseTime",request.getWarehouseTime());
        }

        if(StringUtil.isNotEmpty(request.getPoCode())){
            sql.append(" and esl.po_code =:poCode ");
            params.put("poCode",request.getPoCode());
        }

        if(null != request.getPurchasePrice()){
            sql.append(" and esl.purchase_price =:purchasePrice ");
            params.put("purchasePrice",request.getPurchasePrice());
        }

        if(StringUtil.isNotEmpty(request.getWmsBatchCode())){
            sql.append(" and esl.wms_batch_code =:wmsBatchCode ");
            params.put("wmsBatchCode",request.getWmsBatchCode());
        }

        sql.append(" and esl.warehouse_id =:warehouseId");
        params.put("warehouseId", ParamUtil.getCurrentWarehouseId());

        Query query = this.createSQLQuery(sql.toString());
        this.prepareParameter(params,query);
        Integer curPage = NumberUtils.object2Integer(request.getPage(),1);
        Integer pageSize = Math.min(1000,request.getPageSize());

        query.setFirstResult((curPage - 1) * pageSize);
        query.setMaxResults(pageSize);

        List<ExpStockSendLog> result = new ArrayList<ExpStockSendLog>();
        List<Object> list = query.list();
        for (Object obj : list) {
            Object[] objs = (Object[])obj;
            ExpStockSendLog expStockSendLog = new ExpStockSendLog();
            expStockSendLog.setId(NumberUtils.object2Long(objs[3]));
            expStockSendLog.setWarehouseId(NumberUtils.object2Long(objs[4]));
            expStockSendLog.setTrsId(NumberUtils.object2Long(objs[5]));
            expStockSendLog.setSeq(StrUtils.object2String(objs[6]));
            expStockSendLog.setTrsDocType(NumberUtils.object2Integer(objs[7]));
            expStockSendLog.setTransactionType(NumberUtils.object2Integer(objs[8]));
            expStockSendLog.setTransactionQty(NumberUtils.object2BigDecimal(objs[9]));
            expStockSendLog.setTransactionHoldQty(NumberUtils.object2BigDecimal(objs[10]));
            expStockSendLog.setDocType(NumberUtils.object2Integer(objs[11]));
            expStockSendLog.setDocId(StrUtils.object2String(objs[12]));
            expStockSendLog.setDocCode(StrUtils.object2String(objs[13]));
            expStockSendLog.setDocItemId(StrUtils.object2String(objs[14]));
            expStockSendLog.setExpStockId(NumberUtils.object2Long(objs[15]));
            expStockSendLog.setProductId(StrUtils.object2String(objs[0]));
            expStockSendLog.setMerchantId(StrUtils.object2String(objs[1]));
            expStockSendLog.setWmsBatchCode(StrUtils.object2String(objs[18]));
            expStockSendLog.setLotNo(StrUtils.object2String(objs[19]));
            expStockSendLog.setSupplierId(StrUtils.object2String(objs[2]));
            expStockSendLog.setExpireTime(DateUtil.valueOf(StrUtils.object2String(objs[21])));
            expStockSendLog.setDocTime(DateUtil.valueOf(StrUtils.object2String(objs[22]), DateUtil.DATETIME_PATTERN));
            expStockSendLog.setProductionTime(DateUtil.valueOf(StrUtils.object2String(objs[23])));
            expStockSendLog.setPoCode(StrUtils.object2String(objs[24]));
            expStockSendLog.setWarehouseTime(DateUtil.valueOf(StrUtils.object2String(objs[25])));
            expStockSendLog.setPurchasePrice(NumberUtils.object2BigDecimal(objs[26]));
            expStockSendLog.setSellPrice(NumberUtils.object2BigDecimal(objs[27]));
            expStockSendLog.setIsDamaged(NumberUtils.object2Integer(objs[28]));
            expStockSendLog.setIsFirst(NumberUtils.object2Integer(objs[29]));
            expStockSendLog.setSendTime(DateUtil.valueOf(StrUtils.object2String(objs[30]),DateUtil.DATETIME_PATTERN));
            expStockSendLog.setRewriteTime(DateUtil.valueOf(StrUtils.object2String(objs[31]),DateUtil.DATETIME_PATTERN));
            expStockSendLog.setTransactionTime(DateUtil.valueOf(StrUtils.object2String(objs[32]),DateUtil.DATETIME_PATTERN));
            result.add(expStockSendLog);
        }
        return result;

    }




}
