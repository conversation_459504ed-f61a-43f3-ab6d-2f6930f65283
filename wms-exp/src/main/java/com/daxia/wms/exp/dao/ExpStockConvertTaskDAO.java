package com.daxia.wms.exp.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.exp.entity.ExpStockConvertTask;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import java.util.List;


/**
 * 库存转换任务DAO
 */
@Name("expStockConvertTaskDAO")
@lombok.extern.slf4j.Slf4j
public class ExpStockConvertTaskDAO extends HibernateBaseDAO<ExpStockConvertTask,Long> {


    private static final long serialVersionUID = 1005998166618275624L;

    /**
     * 根据转换单ID查询转换任务
     * @param convertHeaderId
     * @return
     */
    public List<ExpStockConvertTask> findByHeaderId(Long convertHeaderId) {
        String hql = "select o from ExpStockConvertTask o where o.stockConvertDetail.convertHeaderId = :convertHeaderId and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql).setParameter("convertHeaderId", convertHeaderId).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (List<ExpStockConvertTask>) query.list();
    }
}
