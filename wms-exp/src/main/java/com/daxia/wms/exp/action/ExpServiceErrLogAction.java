package com.daxia.wms.exp.action;

import java.sql.Blob;
import java.util.List;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.wms.exp.entity.ExpServiceErrLog;
import com.daxia.wms.exp.filter.ExpServiceLogFilter;
import com.daxia.wms.exp.service.ExpServiceErrLogService;
import com.daxia.wms.exp.service.ExpServiceLogService;
import com.daxia.wms.exp.util.WmsExpUtil;
import com.daxia.wms.exp.util.WmsExpUtil.ServiceLogStatus;

@Name("expServiceErrLogAction")
@AutoCreate
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ExpServiceErrLogAction extends PagedListBean<ExpServiceErrLog> {
	private static final long serialVersionUID = -4786272426261217153L;
	private ExpServiceLogFilter expServiceLogFilter;
	private Long logId;
	private ExpServiceErrLog serviceErrLog;
	private String serviceData;
	private Boolean showBatchRecall;
	@In
	private ExpServiceErrLogService expServiceErrLogService;
	@In
	private ExpServiceLogService expServiceLogService;

	
	@Create
	public void initialize() {
		this.expServiceLogFilter = new ExpServiceLogFilter();
	}
	
	
	@Override
	public void query() {
		if(showBatchRecall){
			this.expServiceLogFilter.getOrderByMap().put("id", "asc");
//			this.buildOrderFilterMap(expServiceLogFilter);
//			expServiceLogFilter.setStatus(ServiceLogStatus.FAILED.getValue());
//			if()
//			expServiceLogFilter.setFromFailOverTimes(5);
		}else{
			this.expServiceLogFilter.getOrderByMap().put("id", "desc");
		}
		this.buildOrderFilterMap(expServiceLogFilter);
		DataPage<ExpServiceErrLog> dataPage = expServiceErrLogService.query(expServiceLogFilter, getStartIndex(), getPageSize());
		populateValues(dataPage);
	}
	
	public void view() {
		this.serviceErrLog = expServiceErrLogService.get(logId);
		Blob serviceData = expServiceLogService.get(logId).getServiceData();
		try {
			if (serviceData != null) {
				Object obj=WmsExpUtil.getServiceDataObj(serviceData);
				this.serviceData = WmsExpUtil.getXmlString(obj);
			} else {
				this.serviceData = null;
			}
		} catch (Exception e) {
			log.error("Error while recallService", e);
			this.serviceData = null;
		}
	}
	
	/**
	 * 按查询条件重新调用服务  只能调失败的且尝试次数大于5次的服务
	 */
	public void recallServices() {
		List<Object> list = getSelectedRowList();
        for (Object id : list) {
        	ExpServiceErrLog serviceErrLog = expServiceErrLogService.recallService((Long)id);
        	if(ServiceLogStatus.SUCCESS.getValue().equals(serviceErrLog.getStatus())){
        		this.selectedMap.remove(id);
        	}
    		int rowNum = ListUtil.getPosFromList(getQueryResult(), serviceErrLog, "id");
    		if(-1!=rowNum){
        		dataPage.getDataList().remove(rowNum);
        		dataPage.getDataList().add(rowNum, serviceErrLog);
    		}
        }
	//	this.buildOrderFilterMap(expServiceLogFilter);
	//	DataPage<ExpServiceErrLog> dataPage = expServiceErrLogService.query(expServiceLogFilter, getStartIndex(), getPageSize());
//		for (ExpServiceErrLog item : dataPage.getDataList()) {
//			expServiceErrLogService.recallService(item);
//		}
		//populateValues(dataPage);
	}

	/**
	 * 重新调用服务
	 */
	public void recallService() {
		ExpServiceErrLog serviceErrLog = expServiceErrLogService.recallService(logId);
		int rowNum = ListUtil.getPosFromList(getQueryResult(), serviceErrLog, "id");
		dataPage.getDataList().remove(rowNum);
		dataPage.getDataList().add(rowNum, serviceErrLog);
		this.setOperationStatus(rowNum);
		if (serviceErrLog.getStatus().equals(ServiceLogStatus.SUCCESS.getValue())) {
			sayMessage(MESSAGE_SUCCESS);
		} else {
			sayMessage(MESSAGE_FAILED);
		}
	}
	
	/**
	 * @return the expServiceLogFilter
	 */
	public ExpServiceLogFilter getExpServiceLogFilter() {
		return expServiceLogFilter;
	}

	/**
	 * @param expServiceLogFilter the expServiceLogFilter to set
	 */
	public void setExpServiceLogFilter(ExpServiceLogFilter expServiceLogFilter) {
		this.expServiceLogFilter = expServiceLogFilter;
	}

	public Long getLogId() {
		return logId;
	}

	public void setLogId(Long logId) {
		this.logId = logId;
	}


	public ExpServiceErrLog getServiceErrLog() {
		return serviceErrLog;
	}

	public void setServiceErrLog(ExpServiceErrLog serviceErrLog) {
		this.serviceErrLog = serviceErrLog;
	}

	public String getServiceData() {
		return serviceData;
	}

	public void setServiceData(String serviceData) {
		this.serviceData = serviceData;
	}

	public Boolean getShowBatchRecall() {
		return showBatchRecall;
	}

	public void setShowBatchRecall(Boolean showBatchRecall) {
		this.showBatchRecall = showBatchRecall;
	}
	
	
}
