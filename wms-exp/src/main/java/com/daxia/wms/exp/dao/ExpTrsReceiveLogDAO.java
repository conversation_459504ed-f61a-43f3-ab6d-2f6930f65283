package com.daxia.wms.exp.dao;

import com.daxia.dubhe.api.internal.util.NumberUtils;
import com.daxia.dubhe.api.internal.util.StrUtils;
import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.exp.entity.ExpTrsReceiveLog;
import com.daxia.wms.exp.stock.dto.ReceiveStockRewriteDTO;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Name("expTrsReceiveLogDAO")
@lombok.extern.slf4j.Slf4j
public class ExpTrsReceiveLogDAO extends HibernateBaseDAO<ExpTrsReceiveLog, Long> {
	private static final long serialVersionUID = 5063494722881541516L;
	
    /**
     * 根据asnid查询出按商品批次合并的收货信息
     * @param asnId
     */
    @SuppressWarnings("unchecked")
	public List<Object> getGroupedReceiveLogByDocId(Long asnId) {
    	String sql = " select o.fm_sku_id, o.to_lot_id, o.is_damage, sum(o.to_qty) from trs_receive_log o where o.doc_id = :docId and o.warehouse_id = :warehouseId "
    			   + " and o.is_deleted = 0 group by o.fm_sku_id, o.to_lot_id, o.is_damage ";
    	Query query = createSQLQuery(sql);
    	query.setLong("docId", asnId);
    	query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
    	return query.list();
    }

	public List<ExpTrsReceiveLog> findLogByAsnId(Long asnId,Long warehouseId) {
		String hql = "FROM ExpTrsReceiveLog o WHERE o.docId = :asnId and o.warehouseId = :warehouseId";
		Query query = this.createQuery(hql);
		query.setParameter("asnId", asnId);
		query.setParameter("warehouseId",warehouseId);
		return query.list();
	}

	public List<ReceiveStockRewriteDTO> findStockInfo(Long asnId,Long subReceiveId) {
		String sql = " SELECT ttl.id,"
				+ " ttl.create_time,"
				+ " (ttl.fm_qty- IFNULL(b.bb,0)),"
				+ " ttl.fm_sku_id,"
				+ " ttl.fm_lot_id,"
				+ " ttl.doc_line_id,"
				+ " ttl.is_damage,ttl.fm_loc_id,ttl.fm_loc_type "
				+ " FROM trs_transaction_log ttl "
				+ " LEFT JOIN ( "
				+ " SELECT inverse_tran_id, SUM(ttl1.fm_qty) AS bb "
				+ " FROM trs_transaction_log ttl1 "
				+ " WHERE ttl1.transaction_type = 'CI' AND ttl1.doc_id = :docId "
				+ " and ttl1.warehouse_id = :warehouseId "
				+ " GROUP BY ttl1.inverse_tran_id ) 	b "
				+ " ON ttl.id = b.inverse_tran_id "
				+ " inner join trs_receive_log trl on trl.TRANSACTION_ID = ttl.id"
				+ " INNER JOIN doc_sub_receive_detail dsb ON dsb.doc_id = trl.id AND dsb.type = 0 "
				+ " WHERE ttl.doc_id = :docId AND ttl.transaction_type = 'IN' "
				+ " AND dsb.is_deleted = 0 AND dsb.sub_header_id = :subReceiveId"
				+ " and ttl.warehouse_id = :warehouseId ";
		Query query = createSQLQuery(sql);
		query.setLong("docId", asnId);
		query.setLong("subReceiveId", subReceiveId);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		List<Object> resultObj = query.list();
		List<ReceiveStockRewriteDTO> result = new ArrayList<ReceiveStockRewriteDTO>();
		if (ListUtil.isNotEmpty(resultObj)) {
			for (Object o : resultObj) {
				Object[] obj = (Object[]) o;
				ReceiveStockRewriteDTO dto = new ReceiveStockRewriteDTO();
				dto.setTrsId(((BigInteger) obj[0]).longValue());
				dto.setCreateAt((Date) obj[1]);
				dto.setQty((BigDecimal) obj[2]);
				dto.setSkuId(((BigInteger) obj[3]).longValue());
				dto.setLotId(((BigInteger) obj[4]).longValue());
				dto.setAsnDetailId(((BigInteger) obj[5]).longValue());
				dto.setIsDamage(((Integer) obj[6]).longValue());
				dto.setLocId(NumberUtils.object2Long(obj[7]));
				dto.setLocType(StrUtils.object2String(obj[8]));
				result.add(dto);
			}
		}
		return result;
	}

	public ExpTrsReceiveLog findByTransactionId(Long transactionId) {
		String hql = "FROM ExpTrsReceiveLog o WHERE o.transactionId = :transactionId and o.warehouseId = :warehouseId";
		Query query = this.createQuery(hql);
		query.setParameter("transactionId", transactionId);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setMaxResults(1);
		return (ExpTrsReceiveLog) query.uniqueResult();

	}
}
