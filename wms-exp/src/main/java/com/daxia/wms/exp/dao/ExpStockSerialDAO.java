package com.daxia.wms.exp.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.Constants;
import com.daxia.wms.exp.entity.ExpStockSerial;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import java.util.List;

@Name("expStockSerialDAO")
@lombok.extern.slf4j.Slf4j
public class ExpStockSerialDAO extends HibernateBaseDAO<ExpStockSerial, Long> {

    public List<String> findSerialByConvertDetail(Long convertDetailId) {
        String sql = "SELECT sl.serial_no FROM trs_serial_log sl, trs_transaction_log tl WHERE " +
                "sl.doc_type = :docType AND sl.transaction_id = tl.id AND tl.doc_line_id = :convertDetailId AND tl.transaction_type = :trsType";
        Query query = this.createSQLQuery(sql.toString()).setString("docType", Constants.TrsType.CONVERT.getValue()).setParameter("convertDetailId", convertDetailId).setParameter("trsType", Constants.TaskType.CONVERT.getValue());

        return query.list();
    }
}
