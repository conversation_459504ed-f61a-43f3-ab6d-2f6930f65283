package com.daxia.wms.exp.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.exp.Constants;
import com.daxia.wms.exp.entity.ExpTrsTransactionLog;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import java.util.HashMap;
import java.util.List;
@Name("expTrsLogDAO")
@lombok.extern.slf4j.Slf4j
public class ExpTrsLogDAO extends HibernateBaseDAO<ExpTrsTransactionLog, Long>{
	private static final long serialVersionUID = 3400290352518522904L;

	public void updateEdiSendFlag(Integer flag,Long id){
    	createQuery("update ExpTrsTransactionLog set ediSendFlag=? where id=?")
    	.setParameter(0, flag)
    	.setParameter(1, id)
    	.executeUpdate();
    }
	
	@SuppressWarnings("unchecked")
	public List<ExpTrsTransactionLog> findShipTrslog(Long docId){
		return (List<ExpTrsTransactionLog>) executeQuery(" from ExpTrsTransactionLog where docId=? and transactionType=?", docId,Constants.DocType.SO.getValue());
	}

    public List<ExpTrsTransactionLog> findBomTrslog(Long docId, String transactionType) {
        return (List<ExpTrsTransactionLog>) executeQuery(" from ExpTrsTransactionLog where docId=? and docType = 'BOM' and transactionType=?", docId, transactionType);
    }

	public List<ExpTrsTransactionLog> findReceiveTrslog(Long docId, String transactionType, Long warehouseId) {
		return (List<ExpTrsTransactionLog>) executeQuery(" from ExpTrsTransactionLog where docId=? and docType = 'ASN' and transactionType=? and warehouseId=?", docId, transactionType,warehouseId);
	}
	public List<ExpTrsTransactionLog> findReceiveTrslog(Long docId, List<String> transactionType, Long warehouseId) {
		String hql = " from ExpTrsTransactionLog where docId=:docId and docType = 'ASN' and transactionType in (" +
				":transactionType ) and warehouseId=:warehouseId";
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("docId",docId);
		paramMap.put("transactionType",transactionType);
		paramMap.put("warehouseId",warehouseId);
		return createQuery(hql, paramMap).list();
	}

	public ExpTrsTransactionLog findReceiveTrsLogByTaskId(Long taskId) {

		String hql = "from ExpTrsTransactionLog where docType = 'ASN' and transactionType='IN' and taskId=?";
		Query query = createQuery(hql);
		query.setParameter(0, taskId);
		return (ExpTrsTransactionLog) query.uniqueResult();
	}

}
