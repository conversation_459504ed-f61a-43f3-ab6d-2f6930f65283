package com.daxia.wms.exp.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.exp.entity.ExpStockDestroy;
import org.hibernate.Query;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Name;

import java.util.List;

@Name("expStockDestroyDAO")
@AutoCreate
@lombok.extern.slf4j.Slf4j
public class ExpStockDestroyDAO extends HibernateBaseDAO<ExpStockDestroy, Long> {

    public List<ExpStockDestroy> findExpStockDestroy(Long stockDestoryId) {
        String hql = "from ExpStockDestroy where headerId=?";
        Query query = createQuery(hql);
        query.setParameter(0, stockDestoryId);
        return query.list();
    }
}
