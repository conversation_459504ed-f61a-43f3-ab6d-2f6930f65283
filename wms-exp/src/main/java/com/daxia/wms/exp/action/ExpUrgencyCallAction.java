package com.daxia.wms.exp.action;

import com.daxia.framework.common.action.ActionBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;

import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.ResourceUtils;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.exp.Constants;
import com.daxia.wms.exp.Constants.DoStatus;
import com.daxia.wms.exp.dao.ExpGalDAO;
import com.daxia.wms.exp.dao.ExpReceiveRecheckDAO;
import com.daxia.wms.exp.dao.ExpReservationHeaderDAO;
import com.daxia.wms.exp.dao.ExpTOCrossDockHeaderDAO;
import com.daxia.wms.exp.delivery.srv.*;
import com.daxia.wms.exp.entity.*;
import com.daxia.wms.exp.entity.base.ExpDoHeaderBase;
import com.daxia.wms.exp.job.SExpSrvMsgTmsJob;
import com.daxia.wms.exp.receive.srv.Asn2OmsExpSrv;
import com.daxia.wms.exp.service.AsnExpService;
import com.daxia.wms.exp.service.DoExpService;
import com.daxia.wms.exp.service.ExpStockLogService;
import com.daxia.wms.exp.util.WmsExpUtil;
import org.apache.commons.lang3.StringUtils;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Name("expUrgencyCallAction")
@AutoCreate
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ExpUrgencyCallAction extends ActionBean implements Serializable {

    private static final long serialVersionUID = -6633882086881644229L;

    private Long warehouseId;

    private String billNo;
    private String doNos;

    private String op;

    private Integer docType;

    private String docStatus;

    public String getOp() {
        return op;
    }

    public void setOp(String op) {
        this.op = op;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    @In
    private DoExpService doExpService;

    @In
    private AsnExpService asnExpService;

    @In
    private Asn2OmsExpSrv asn2OmsExpSrv;

    @In
    private DoSn2OmsExpSrv doSn2OmsExpSrv;

    @In
    private Do2TmsExpSrv do2TmsExpSrv;

    @In
    private DoStatusExpSrv doStatusExpSrv;

    @In
    private ExpTOCrossDockHeaderDAO expTOCrossDockHeaderDAO;

    @In
    private ToCd2PmsExpSrv toCd2PmsExpSrv;

    @In
    private ToCd2TmsExpSrv toCd2TmsExpSrv;

    @In
    private DoCancelExpSrv doCancelExpSrv;

    @In
    private Do2ScsExpSrv do2ScsExpSrv;

    @In
    private DoBatch2WmsExpSrv doBatch2WmsExpSrv;

    @In
    private ExpGalDAO expGalDAO;

    @In
    private ExpReservationHeaderDAO expReservationHeaderDAO;

    @In
    private SExpSrvMsgTmsJob sExpSrvMsgTmsJob;
    
    @In
    private ExpStockLogService expStockLogService;

    @In
    private ExpReceiveRecheckDAO expReceiveRecheckDAO;

    public void sExpSrvMsgTmsJobRun() {
        sExpSrvMsgTmsJob.run();
    }

    @Create
    public void Init() {
        warehouseId = ParamUtil.getCurrentWarehouseId();
    }

    public void call() {
        String[] billNos = billNo.split(",");
        if(StringUtils.isNoneBlank(doNos)){
            billNos=StringUtil.trimStr(this.getDoNos()).split(",");
        }
        if ("-".equals(op)) {
            sayMessage("please select a function！");
        } else if ("ASN_TMSV2".equals(op)) {
            sendAsn2TmsV2(billNos);
        } else if ("ASN_OMS".equals(op)) {
            sendAsn2Oms(billNos);
        } else if ("DO_CANCEL_OMS".equals(op)) {
            canceledDo2Oms(billNos);
        } else if ("DO_RELEASE_SCS".equals(op)) {
            sendDo2Scs(billNos);
        } else if ("DO_TMSV2".equals(op)) {
            send2TmsV2(billNos);
        } else if ("DO_OMS".equals(op)) {
            send2Oms(billNos);
        } else if ("DO_SN_OMS".equals(op)) {
            sendDoSN2Oms(billNo);
        } else if ("DO_CARD_OMS".equals(op)) {
            sendCard2Oms(billNos);
        } else if ("TO_CR_PMS".equals(op)) {
            sendToCr2Pms(billNos);
        } else if ("TO_CR_TMS".equals(op)) {
            sendToCr2Tms(billNos);// new
        } else if ("BATCHQTY_WMS".equals(op)) {
            sendSendBatchQty2WMS(billNo);
        } else if ("STOCK_OMS".equals(op)) {
            sendStock();
        }
    }

    /**
     * DO出库序列号回写
     * 
     * @param billNo
     */
    private void sendDoSN2Oms(String billNo) {
        ExpDoHeader dh = doExpService.findExpDoByDoNo(billNo.trim());
        if (null == dh) {
            sayMessage("DO不存在！doNo:" + billNo);
        } else {
            doSn2OmsExpSrv.sendNow(dh.getId());
        }
    }

    /**
     * 紧急调用：Do释放时调用SCS重新计算预计出库时间
     * 
     * @param billNos
     *            DO单号，多个用”，“分割
     */
    @Loggable
    private void sendDo2Scs(String[] billNos) {
        StringBuilder sb = new StringBuilder();
        List<Long> doReleaseList = new ArrayList<Long>();
        for (String billNo : billNos) {
            ExpDoHeader dh = doExpService.findExpDoByDoNo(billNo.trim());
            if (null == dh) {
                sb.append(ResourceUtils.getDispalyString("doExp.do_notfound", "messages_exp", "DO_NO:" + billNo))
                        .append(";");
            } else {
                doReleaseList.add(dh.getId());
            }
        }
        if (!ListUtil.isNullOrEmpty(doReleaseList)) {
            do2ScsExpSrv.send(doReleaseList);
        }
        if (0 != sb.length()) {
            sayMessage(sb.toString());
        }
    }

    @Loggable
    public void send2Oms(String[] billNos) {
        StringBuilder sb = new StringBuilder();
        for (String billNo : billNos) {
            ExpDoHeader dh = doExpService.findExpDoByDoNo(billNo.trim());
            if (null == dh) {
                sb.append(ResourceUtils.getDispalyString("doExp.do_notfound", "messages_exp", "DO_NO:" + billNo))
                        .append(";");
            } else {
                doStatusExpSrv.send(dh.getId(), null, null, null, null, null);
            }
            log.info("do状态同步 {}",billNo);
        }
        if (0 != sb.length()) {
            sayMessage(sb.toString());
        }
    }

    @Loggable
    public void send2TmsV2(String[] billNos) {
        StringBuilder sb = new StringBuilder();
        for (String billNo : billNos) {
            TMSDoHeader dh = doExpService.findTMSDoByDoNo(billNo.trim());
            if (null == dh) {
                sb.append(ResourceUtils.getDispalyString("doExp.do_notfound", "messages_exp", "DO_NO:" + billNo))
                        .append(";");
            } else {
                do2TmsExpSrv.send(dh.getDoType(), dh.getId(), dh.getDoNo(), null);
            }
        }
        if (0 != sb.length()) {
            sayMessage(sb.toString());
        }
    }

    @Loggable
    public void sendToCr2Tms(String[] billNos) {
        StringBuilder sb = new StringBuilder();
        for (String billNo : billNos) {
            ExpTOCrossDockHeader dh = expTOCrossDockHeaderDAO.findBydoNo(billNo.trim());
            if (null == dh) {
                sb.append(ResourceUtils.getDispalyString("asnExp.do_notfound", "messages_exp", "ASN_NO:" + billNo))
                        .append(";");
            } else {
                toCd2TmsExpSrv.send(dh.getId(), dh.getDoNo());
            }
        }
        if (0 != sb.length()) {
            sayMessage(sb.toString());
        }
    }

    @Loggable
    public void sendToCr2Pms(String[] billNos) {
        StringBuilder sb = new StringBuilder();
        for (String billNo : billNos) {
            ExpTOCrossDockHeader dh = expTOCrossDockHeaderDAO.findBydoNo(billNo.trim());
            if (null == dh) {
                sb.append(ResourceUtils.getDispalyString("asnExp.do_notfound", "messages_exp", "ASN_NO:" + billNo))
                        .append(";");
            } else {
                toCd2PmsExpSrv.send(dh.getId(), dh.getDoNo());
            }
        }
        if (0 != sb.length()) {
            sayMessage(sb.toString());
        }
    }

    @Loggable
    public void sendAsn2TmsV2(String[] billNos) {
        StringBuilder sb = new StringBuilder();
        if (billNos.length < 2) {
            sb.append("please input asnNo,isResArrived");
        } else {
            ExpAsn dh = asnExpService.findByAsnNo(billNos[0].trim(), warehouseId);
            if (null == dh) {
                sb.append(ResourceUtils.getDispalyString("asnExp.do_notfound", "messages_exp", "ASN_NO:" + billNo))
                        .append(";");
            } else {
            }
        }

        if (0 != sb.length()) {
            sayMessage(sb.toString());
        }
    }

    @Loggable
    public void canceledDo2Oms(String[] billNos) {
        StringBuilder sb = new StringBuilder();
        for (String billNo : billNos) {
            ExpDoHeader dh = doExpService.findExpDoByDoNo(billNo.trim());
            if (null == dh) {
                sb.append(ResourceUtils.getDispalyString("doExp.do_notfound", "messages_exp", "DO_NO:" + billNo))
                        .append(";");
            } else if (DoStatus.CANCELED.getValue().equals(dh.getStatus())) {
                doCancelExpSrv.send(dh.getId(), true, null);
            } else {
                sb.append(ResourceUtils.getDispalyString("doExp.expLog_errorState", "messages_exp", dh.getDoType(),
                        Constants.DoType.SELL.getValue().toString(), dh.getStatus(), DoStatus.CANCELED.getValue()))
                        .append(";");
            }
        }
        if (0 != sb.length()) {
            sayMessage(sb.toString());
        }
    }

    @Loggable
    public void sendCard2Oms(String[] billNos) {
        StringBuilder sb = new StringBuilder();
        for (String billNo : billNos) {
            ExpDoHeader dh = doExpService.findExpDoByDoNo(billNo.trim());
            if (null == dh) {
                sb.append(ResourceUtils.getDispalyString("doExp.do_notfound", "messages_exp", "DO_NO:" + billNo))
                        .append(";");
            } else if (Constants.DoType.SELL.getValue().toString().equals(dh.getDoType())
                    && DoStatus.ALL_DELIVER.getValue().equals(dh.getStatus())) {
            } else {
                sb.append(ResourceUtils.getDispalyString("doExp.expLog_errorState", "messages_exp", dh.getDoType(),
                        Constants.DoType.SELL.getValue().toString(), dh.getStatus(), DoStatus.ALL_DELIVER.getValue()))
                        .append(";");
            }
        }
        if (0 != sb.length()) {
            sayMessage(sb.toString());
        }
    }

    @Loggable
    public void sendAsn2Oms(String[] billNos) {
        StringBuilder sb = new StringBuilder();
        for (String billNo : billNos) {
            ExpAsn dh = asnExpService.findByAsnNo(billNo.trim(), warehouseId);
            if (null == dh) {
                sb.append(ResourceUtils.getDispalyString("asnExp.do_notfound", "messages_exp", "ASN_NO:" + billNo))
                        .append(";");
            } else {
                if(null == this.docStatus){
                    asn2OmsExpSrv.createMsg(dh.getId(),dh.getAsnStatus(),dh.getAsnNo(),dh.getSourceSystem());
                }else{
                    if(Constants.AsnStatus.RECHECKED.getValue().equals(this.docStatus)){
                        List<ExpReceiveRecheckHeader> headers = expReceiveRecheckDAO.findByDocId(dh.getId());
                        for (ExpReceiveRecheckHeader header : headers) {
                            asn2OmsExpSrv.createMsg(header.getId(),docStatus,dh.getAsnNo(),dh.getSourceSystem());
                        }
                    }else{
                        asn2OmsExpSrv.createMsg(dh.getId(),docStatus,dh.getAsnNo(),dh.getSourceSystem());
                    }
                }
            }
        }
        if (0 != sb.length()) {
            sayMessage(sb.toString());
        }
    }

    /**
     * 调拨单出库紧急调用WMS
     * 
     * @param doNo
     */
    @Loggable
    public void sendSendBatchQty2WMS(String doNo) {
        StringBuilder sb = new StringBuilder();
        ExpDoHeaderBase expDoHeader = null;
        expDoHeader = doExpService.findExpDoByDoNo(billNo.trim());
        if (null == expDoHeader) {
            expDoHeader = expTOCrossDockHeaderDAO.findBydoNo(billNo.trim());
        }
        if (null != expDoHeader) {
            //doBatch2WmsExpSrv.send(expDoHeader.getId());
        } else {
            sb.append(ResourceUtils.getDispalyString("doExp.do_notfound", "message_exp", "DO_NO:" + billNo))
                    .append(";");
        }
        if (0 != sb.length()) {
            sayMessage(sb.toString());
        }
    }
    
    private void sendStock(){
        if(null == this.docType || StringUtil.isEmpty(this.billNo)){
            sayMessage("单号和数据类型必填!");
        }else{
            ExpStock expStocklog = new ExpStock();
            expStocklog.setDocId(Long.valueOf(this.billNo));
            expStocklog.setDocType(this.docType);
            expStocklog.setStatus(WmsExpUtil.ExpStockLogStatus.INIT.getValue());
            expStocklog.setRwSys(ExpStock.SCS);
            expStockLogService.save(expStocklog);
            sayMessage("操作成功!");
        }
    }

    public Integer getDocType() {
        return docType;
    }

    public void setDocType(Integer docType) {
        this.docType = docType;
    }

    public String getDocStatus() {
        return docStatus;
    }

    public void setDocStatus(String docStatus) {
        this.docStatus = docStatus;
    }

    public String getDoNos() {
        return doNos;
    }

    public void setDoNos(String doNos) {
        this.doNos = doNos;
    }
}
