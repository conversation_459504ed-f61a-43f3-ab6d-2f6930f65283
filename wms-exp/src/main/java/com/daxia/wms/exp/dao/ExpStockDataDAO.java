package com.daxia.wms.exp.dao;

import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.exp.entity.ExpStockData;

@Name("expStockDataDAO")
@AutoCreate
@lombok.extern.slf4j.Slf4j
public class ExpStockDataDAO extends HibernateBaseDAO<ExpStockData, Long> {

	private static final long serialVersionUID = 3320741140514485115L;
	
	@SuppressWarnings("unchecked")
	public List<ExpStockData> findExpStockData(Long expStockId){
		String hql="from ExpStockData where expStockId=?";
        Query query = createQuery(hql);
        query.setParameter(0, expStockId);
        return query.list();
	}
}
