package com.daxia.wms.exp.dao;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.exp.entity.ExpStockBatchAtt;



@Name("expStockBatchAttDAO")
@lombok.extern.slf4j.Slf4j
public class ExpStockBatchAttDAO extends HibernateBaseDAO<ExpStockBatchAtt, Long>{

	private static final long serialVersionUID = -1331592243534567967L;
	
	
	public ExpStockBatchAtt getStockBatchAttByLotNo(String lotNo){
	    String hql = "from ExpStockBatchAtt o where o.lotNo = :lotNo ";
        Query query = this.createQuery(hql);
        query.setParameter("lotNo", lotNo);
        query.setMaxResults(1);
        return (ExpStockBatchAtt)query.uniqueResult();
	}

}
