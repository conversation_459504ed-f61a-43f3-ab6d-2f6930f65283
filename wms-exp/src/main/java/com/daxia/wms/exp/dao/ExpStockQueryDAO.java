package com.daxia.wms.exp.dao;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.exp.Constants;
import com.daxia.wms.exp.dto.DamageStockDto;
import com.daxia.wms.exp.dto.WarehouseRentDto;
import com.daxia.wms.exp.filter.DamageStockFilter;

@Name("expStockQueryDAO")
@lombok.extern.slf4j.Slf4j
public class ExpStockQueryDAO extends HibernateBaseDAO<Object, Long>{

	public static final Integer MAX_RESULT = 100;
	private static final long serialVersionUID = -7610155419527061489L;
	
	@SuppressWarnings("unchecked")
	public List<DamageStockDto> queryDamageByFilter(DamageStockFilter filter) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select sum(o.qty_available +o.qty_hold- ifnull(sa.qty_allocating, 0)) as actQty, o.lot_id, location.loc_code, mp.partition_type ");
		sql.append(" from stk_batch_loc_lpn o ");
		sql.append(" inner join stock_batch_att b on o.lot_id = b.id and b.is_deleted = 0 ");
		sql.append(" inner join md_location loc on o.loc_id = loc.id and loc.is_deleted = 0 ");
		sql.append(" inner join md_partition mp on loc.partition_id = mp.id" );
		sql.append(" left join (select sal.stk_lpn_id as stockid, sum(sal.qty_allocating) as qty_allocating ");
		sql.append(" from stk_allocating sal where sal.is_deleted = 0 group by sal.stk_lpn_id) sa ");
		sql.append(" on o.id = sa.stockid ");
		sql.append(" left join md_location location on o.loc_id = location.id ");
		sql.append(" where o.is_deleted = 0 ");
		sql.append(" and loc.loc_type = 'DM' and b.lotatt12 is not null ");
		sql.append(" and (o.qty_available + o.qty_hold) > 0 ");
		if (filter.getIsRvs() != null) {
			sql.append(" and exists (select 1 from md_partition mp where mp.id = loc.partition_id and mp.partition_type ")
			   .append(filter.getIsRvs() ? " = 1)" : " != 1)");
		}
		
		if(filter.getWarehouseId() != null) {
			sql.append(" and o.warehouse_id=:warehouseId ");
		}
		
		if(! filter.getLotNos().isEmpty()) {
			sql.append(" and b.lot_no in (:lotNos) ");
		}
		
		if(filter.getCreateDateFrom() != null) {
			sql.append(" and b.create_time > :createDateFrom ");
		}
		
		if(filter.getCreateDateTo() != null) {
			sql.append(" and b.create_time < :createDateTo ");
		}
		
		sql.append(" group by o.lot_id, location.loc_code, b.lot_no, mp.partition_type order by o.lot_id");
		
		Query query = this.createSQLQuery(sql.toString());

		if(filter.getWarehouseId() != null) {
			query.setParameter("warehouseId", filter.getWarehouseId());
		}
		
		if(! filter.getLotNos().isEmpty()) {
			query.setParameterList("lotNos", filter.getLotNos());
		}
		
		if(filter.getCreateDateFrom() != null) {
			query.setParameter("createDateFrom", filter.getCreateDateFrom());
		}
		
		if(filter.getCreateDateTo() != null) {
			query.setParameter("createDateTo", filter.getCreateDateTo());
		}
		
		List<Object[]> results = (List<Object[]>)query.setMaxResults(filter.getMaxResults()).list();
		
		List<DamageStockDto> damageStockDtos = new ArrayList<DamageStockDto>(results.size());
		for (Object[] obj : results) {
			DamageStockDto dto = new DamageStockDto();
			dto.setDamageQty((BigDecimal) obj[0]);
			dto.setLotId(((BigDecimal)obj[1]).longValue());
			dto.setLocCode((String)obj[2]);
			damageStockDtos.add(dto);
		}
		return damageStockDtos;
	}
	
	@SuppressWarnings("unchecked")
	public List<DamageStockDto> queryDamage(boolean isRvs, Date createFrom, Long whid, int startIndex) {
		String sql = buildDamageSQL(isRvs);
		
		Query query = createSQLQuery(sql);
		query.setParameter("createFrom", createFrom);
		query.setParameter("whid", whid);
		query.setParameter("partitionType", Constants.PartitionType.RVS.getValue());
		query.setFirstResult(startIndex);
		query.setMaxResults(MAX_RESULT);
		List<Object[]> results = (List<Object[]>)query.list();
		if(ListUtil.isNullOrEmpty(results)) {
            return Collections.emptyList();
        }
		List<DamageStockDto> damageStockDtos = new ArrayList<DamageStockDto>(results.size());
		for (Object[] obj : results) {
			DamageStockDto dto = new DamageStockDto();
			dto.setDamageQty((BigDecimal) obj[0]);
			dto.setLotId(((BigDecimal)obj[1]).longValue());
			dto.setLocCode((String)obj[2]);
			dto.setPartitionType((Integer)obj[3]);
			damageStockDtos.add(dto);
		}
		return damageStockDtos;
	}

	private String buildDamageSQL(boolean isRvs) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select sum(o.qty_available +o.qty_hold- ifnull(sa.qty_allocating, 0)) as actQty, o.lot_id, location.loc_code ");
		sql.append(" from stk_batch_loc_lpn o ");
		sql.append(" inner join stock_batch_att b on o.lot_id = b.id and b.is_deleted = 0 ");
		sql.append(" inner join md_location loc on o.loc_id = loc.id and loc.is_deleted = 0 ");
		sql.append(" left join (select sal.stk_lpn_id as stockid, sum(sal.qty_allocating) as qty_allocating ");
		sql.append(" from stk_allocating sal where sal.is_deleted = 0 group by sal.stk_lpn_id) sa ");
		sql.append(" on o.id = sa.stockid ");
		sql.append(" left join md_location location on o.loc_id = location.id ");
		sql.append(" where o.is_deleted = 0 ");
		sql.append(" and cast(b.create_time as date) = cast(:createFrom as date) ");
		sql.append(" and loc.loc_type = 'DM' and b.lotatt12 is not null ");
		sql.append(" and (o.qty_available + o.qty_hold) > 0 ");
		sql.append(" and o.warehouse_id=:whid ");
		sql.append(" and exists (select 1 from md_partition mp WHERE mp.id = loc.partition_id and "); 
		if (isRvs) {
			//不可保存区
			sql.append(" mp.partition_type = :partitionType) ");
		} else {
			//可保存区`
			sql.append(" mp.partition_type <> :partitionType) ");
		}
		sql.append(" group by o.lot_id, location.loc_code order by o.lot_id");
		return sql.toString();
	}
	
	public int queryDamageCount(boolean isRvs, Date createFrom, Long whid) {
		String sql = " select count(1) from ( "  + buildDamageSQL(isRvs) + " ) ";
		Query query = createSQLQuery(sql);
		query.setParameter("createFrom", createFrom);
		query.setParameter("whid", whid);
		query.setParameter("partitionType", Constants.PartitionType.RVS.getValue());
        Number number = (Number)query.uniqueResult();
        return number == null? 0 : number.intValue();
	}
	
	public Object getSkuAmountAndUnit() {
		StringBuilder sql = new StringBuilder();
		sql.append(" select sum(allstk.sku), sum(allstk.units) ")
		   .append(" from (select count(distinct stock.sku_id) sku, sum(stock.totalqty) units ")
	       .append(" from (select stk.sku_id, ")
	       .append(" sum(stk.qty_available + stk.qty_hold + ifnull(allocated.qty, 0) + ifnull(pend.qty, 0) - ")
	       .append(" ifnull(allocatinginfo.allocatingqty, 0)) as totalqty ")
	       .append(" from stk_batch_loc_lpn stk ")
	       .append(" left join (select alloc.stk_lpn_id as stkid, ")
	       .append(" sum(alloc.qty_allocated) as qty ")
	       .append(" from stk_allocated alloc ")
	       .append(" where alloc.is_deleted = 0 and alloc.warehouse_id = :whid and alloc.tsk_type in ('PK', 'TRANS_OUT') ")
	       .append(" group by alloc.stk_lpn_id) allocated ")
	       .append(" on stk.id = allocated.stkid ")
	       .append(" left join (select allocating.stk_lpn_id, ")
	       .append(" sum(allocating.qty_allocating) as allocatingqty ")
	       .append(" from stk_allocating allocating ")
	       .append(" where allocating.is_deleted = 0 and allocating.warehouse_id = :whid ")
	       .append(" group by allocating.stk_lpn_id) allocatinginfo ")
	       .append(" on stk.id = allocatinginfo.stk_lpn_id ")
           .append(" left join (select sp.stk_lpn_id, sum(sp.qty_pending) as qty ")
           .append(" from stk_pending sp ")
	       .append(" where sp.is_deleted = 0 and sp.tsk_type in ('PA', 'RK') and warehouse_id = :whid ")
	       .append(" group by sp.stk_lpn_id) pend ")
           .append(" on stk.id = pend.stk_lpn_id ")
	       .append(" where stk.warehouse_id = :whid and stk.is_deleted = 0 ")
	       .append(" group by stk.sku_id) stock ")
	       .append(" where stock.totalqty > 0 ")
	       .append(" union all ")
	       .append(" select 0 sku, sum(qty_picked) units ")
	       .append(" from stk_picked ")
	       .append(" where qty_picked > 0 and is_deleted = 0 and warehouse_id = :whid ")
	       .append(" ) allstk ");
		Query query = createSQLQuery(sql.toString());
		query.setParameter("whid", ParamUtil.getCurrentWarehouseId());
		query.setMaxResults(1);
		return query.uniqueResult();
	}
	
    @SuppressWarnings("unchecked")
    public List<WarehouseRentDto> findRentByLocation(String date) {
        String sql = "select sba.lotatt04, count(distinct loc.id) from stk_batch_loc_lpn o "
                + "inner join stock_batch_att sba on o.lot_id = sba.id and sba.is_deleted = 0 and sba.warehouse_id = :whId "
                + "inner join md_location loc on o.loc_id = loc.id and loc.is_deleted = 0 and loc.warehouse_id = :whId "
                + "left join (select sa.stk_lpn_id as stockid, sum(sa.qty_allocated) as qty_allocated"
                + "   from stk_allocated sa where sa.is_deleted = 0 and sa.warehouse_id = :whId"
                + "   group by sa.stk_lpn_id) sa on o.id = sa.stockid                "
                + "where o.is_deleted = 0 and loc.loc_type in (:locTypes) and o.warehouse_id = :whId "
                + "and o.qty_hold + o.qty_available + ifnull(sa.qty_allocated, 0) > 0 and sba.lotatt04 is not null "
                + "group by sba.lotatt04";

        Query query = createSQLQuery(sql);
        query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
        query.setParameterList("locTypes", Arrays.asList(new String[] { "EA", "RS", "DM" }));

        List<Object[]> objsList = query.list();

        if (ListUtil.isNullOrEmpty(objsList)) {
            return Collections.emptyList();
        }
        List<WarehouseRentDto> dtos = new ArrayList<WarehouseRentDto>(objsList.size());
        for (Object[] obj : objsList) {
            WarehouseRentDto dto = new WarehouseRentDto();
            dto.setSupplierId(Long.valueOf((String) obj[0]));
            dto.setQty((BigDecimal) obj[1]);
            dto.setChargeDate(date);
            dto.setType(WarehouseRentDto.TYPE_LOCATION);
            dto.setWhId(ParamUtil.getCurrentWarehouseId());

            dtos.add(dto);
        }
        return dtos;
    }

    /**
     * 返回供应商收货商品的数量
     * 
     * @param date
     * @param index
     * @return [supplierId, skuId, qty]的list
     */
    @SuppressWarnings("unchecked")
    public List<Object[]> findRentByLpn(String date, int firstResult) {
        String sql = "SELECT sba.lotatt04, trs.fm_sku_id, SUM(trs.fm_qty) as qty"
                + "     FROM trs_receive_log trs"
                + "     INNER JOIN stock_batch_att sba ON trs.fm_lot_id = sba.id AND sba.is_deleted = 0 AND sba.warehouse_id = :whId "
                + "     WHERE sba.lotatt04 IS NOT NULL AND trs.create_time >= :sDay AND trs.create_time <= :eDay AND trs.warehouse_id = :whId"
                + "     GROUP BY sba.lotatt04, trs.fm_sku_id ";

        Query query = createSQLQuery(sql);
        query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("sDay", DateUtil.valueOf(date + " 00:00:00", DateUtil.DATETIME_PATTERN, Boolean.FALSE));
        query.setParameter("eDay", DateUtil.valueOf(date + " 23:59:59", DateUtil.DATETIME_PATTERN, Boolean.FALSE));
        query.setFirstResult(firstResult);
        query.setMaxResults(MAX_RESULT);

        return query.list();
    }

    public Long findRentCountByLpn(String date) {
        String sql = "SELECT count(1) FROM ("
                + "    SELECT sba.lotatt04, trs.fm_sku_id, SUM(trs.fm_qty) as qty"
                + "     FROM trs_receive_log trs"
                + "     INNER JOIN stock_batch_att sba ON trs.fm_lot_id = sba.id AND sba.is_deleted = 0 AND sba.warehouse_id = :whId "
                + "     WHERE sba.lotatt04 IS NOT NULL AND trs.create_time >= :sDay AND trs.create_time <= :eDay AND trs.warehouse_id = :whId"
                + "     GROUP BY sba.lotatt04, trs.fm_sku_id) ";
        Query query = createSQLQuery(sql);
        query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
        query.setParameter("sDay", DateUtil.valueOf(date + " 00:00:00", DateUtil.DATETIME_PATTERN, Boolean.FALSE));
        query.setParameter("eDay", DateUtil.valueOf(date + " 23:59:59", DateUtil.DATETIME_PATTERN, Boolean.FALSE));

        return (Long) query.uniqueResult();
    }
    
    /**
     * 获取所有units体积之和
     * @return
     */
    public Object getTotalStockVolume(){
    	StringBuffer sql = new StringBuffer();
    	sql.append("select sum(stock.totalqty*sku.volume)/1000/1000");
    	sql.append(" from (select stk.sku_id,");
    	sql.append(" sum(stk.qty_available + stk.qty_hold + ifnull(allocated.qty, 0) +");
    	sql.append(" ifnull(pend.qty, 0) + ifnull(picked.qty, 0) - ifnull(allocatinginfo.allocatingqty, 0)) as totalqty");
    	sql.append(" from stk_batch_loc_lpn stk");
    	sql.append(" left join (select alloc.stk_lpn_id as stkid,sum(alloc.qty_allocated) as qty");
    	sql.append(" from stk_allocated alloc");
    	sql.append(" where alloc.is_deleted = 0");
    	sql.append(" and alloc.warehouse_id = :warehouse_id ");
    	sql.append(" and alloc.tsk_type in ('PK', 'TRANS_OUT')");
    	sql.append(" group by alloc.stk_lpn_id) allocated on stk.id = allocated.stkid ");
    	sql.append(" left join (select allocating.stk_lpn_id,sum(allocating.qty_allocating) as allocatingqty");
    	sql.append(" from stk_allocating allocating where allocating.is_deleted = 0");
    	sql.append(" and allocating.warehouse_id = :warehouse_id ");
    	sql.append(" group by allocating.stk_lpn_id) allocatinginfo");
    	sql.append(" on stk.id = allocatinginfo.stk_lpn_id");
    	sql.append(" left join (select sp.stk_lpn_id, sum(sp.qty_pending) as qty");
    	sql.append(" from stk_pending sp where sp.is_deleted = 0 and sp.tsk_type in ('PA', 'RK')");
    	sql.append(" and warehouse_id = :warehouse_id  group by sp.stk_lpn_id) pend");
    	sql.append(" on stk.id = pend.stk_lpn_id ");
    	sql.append(" left join (select spd.stk_lpn_id, sum(spd.qty_picked) as qty");
    	sql.append(" from stk_picked spd where spd.is_deleted = 0 ");
    	sql.append(" and spd.warehouse_id = :warehouse_id  group by spd.stk_lpn_id) picked");
    	sql.append(" on stk.id = picked.stk_lpn_id where stk.warehouse_id = :warehouse_id ");
    	sql.append(" group by stk.sku_id) stock");
    	sql.append(" left join md_sku sku on sku.id = stock.sku_id and sku.is_deleted = 0");
    	sql.append(" where stock.totalqty > 0");
    	Query query = createSQLQuery(sql.toString());
		query.setParameter("warehouse_id", ParamUtil.getCurrentWarehouseId());
		query.setMaxResults(1);
		return query.uniqueResult();
    }
    
    /**
     * 获取所有库位体积之和
     * @return
     */
    public Object getTotalLocVolume(){
    	StringBuffer sql = new StringBuffer();
    	sql.append("select sum(ml.volume)/1000/1000 from md_location ml ");
    	sql.append("where ml.is_deleted = 0 and ml.warehouse_id = :warehouse_id");
    	Query query = createSQLQuery(sql.toString());
		query.setParameter("warehouse_id", ParamUtil.getCurrentWarehouseId());
		query.setMaxResults(1);
		return query.uniqueResult();
    }
}
