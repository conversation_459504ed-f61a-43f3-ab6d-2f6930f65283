package com.daxia.wms.exp.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.wms.Keys;
import com.daxia.wms.exp.entity.ExpStock;
import com.daxia.wms.exp.util.WmsExpUtil;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.Query;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Name;

import java.util.Date;
import java.util.List;

@Name("expStockDAO")
@AutoCreate
@lombok.extern.slf4j.Slf4j
public class ExpStockDAO extends HibernateBaseDAO<ExpStock, Long> {

    private static final long serialVersionUID = -8936031707640157137L;

    public void updateStatus(int status, Long id) {
        createQuery(" update ExpStock set status=? where id=?").setParameter(0, status).setParameter(1, id).executeUpdate();
    }

    @SuppressWarnings("unchecked")
    public List<ExpStock> findAllIteratorBySys(Integer rwSys) {
        String hql = "from ExpStock o where o.status in (?,?) and o.failOverTimes < ? and o.rwSys = ?  and o.nextInvokeTime <= :nextInvokeTime order by o.id";
        Query query = createQuery(hql);
        query.setParameter(0, WmsExpUtil.ExpStockLogStatus.INIT.getValue());
        query.setParameter(1, WmsExpUtil.ExpStockLogStatus.FAILED.getValue());
        query.setParameter(2, WmsExpUtil.MAX_FAIL_OVER_TIMES);
        query.setParameter(3, rwSys);
        query.setParameter("nextInvokeTime", DateUtil.getNowTime());
        return query.setMaxResults(500).list();
    }

    public ExpStock getByErrorLogId(Long errorLogid) {
        String hql = "from ExpStock o where o.errorLogId = ?";
        Query query = createQuery(hql);
        query.setParameter(0, errorLogid);
        return (ExpStock) query.uniqueResult();
    }

    public Integer count(Long warehouseId) {
        String hql = "select count(o.id) from ExpStock o where o.failOverTimes > 0 and o.createdAt>=:createdAt and o.warehouseId =:warehouseId ";
        Query query = createQuery(hql);
        query.setParameter("warehouseId", warehouseId);
        query.setParameter("createdAt", DateUtil.dateAdd("dd", DateUtil.getNowTime(), -1 * Config.getInt(Keys.Interface.mail_fetch_error_days, Config.ConfigLevel.GLOBAL, 30)));
        Number result = (Number) (query.uniqueResult());
        return result.intValue();
    }

    public Integer count(Long warehouseId,Date startTime) {
        String hql = "select count(o.id) from ExpStock o where o.failOverTimes = 0 and o.createdAt>=:createdAtMin and o.warehouseId =:warehouseId and o.createdAt >= :createdAt";
        Query query = createQuery(hql);
        query.setParameter("warehouseId", warehouseId);
        query.setParameter("createdAtMin", DateUtil.dateAdd("dd", DateUtil.getNowTime(), -1 * Config.getInt(Keys.Interface.mail_fetch_error_days, Config.ConfigLevel.GLOBAL, 30)));
        query.setParameter("createdAt",startTime);
        Number result = (Number) (query.uniqueResult());
        return result.intValue();
    }

    public List<ExpStock> findAllIteratorBySys(Integer rwSys, List<Long> whIdList) {
        String hql = "from ExpStock o where o.status in (?,?) and o.failOverTimes < ? and o.rwSys = ? and o.nextInvokeTime <= :nextInvokeTime ";
        if (CollectionUtils.isNotEmpty(whIdList)) {
            hql += "and o.warehouseId in (:whIdList) ";
        }
        hql += "order by o.id";
        Query query = createQuery(hql);
        query.setParameter(0, WmsExpUtil.ExpStockLogStatus.INIT.getValue());
        query.setParameter(1, WmsExpUtil.ExpStockLogStatus.FAILED.getValue());
        query.setParameter(2, WmsExpUtil.MAX_FAIL_OVER_TIMES);
        query.setParameter(3, rwSys);
        query.setParameter("nextInvokeTime", DateUtil.getNowTime());
        if (CollectionUtils.isNotEmpty(whIdList)) {
            query.setParameterList("whIdList", whIdList);
        }
        return query.setMaxResults(500).list();
    }
}