package com.daxia.wms.exp.action;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.exp.entity.ExpStockData;
import com.daxia.wms.exp.filter.ExpStockDataFilter;
import com.daxia.wms.exp.stock.srv.ExpStockService;

@Name("expStockDataAction")
@AutoCreate
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ExpStockDataAction extends PagedListBean<ExpStockData>{
	/**
	 * 
	 */
	private static final long serialVersionUID = 7423093858130481268L;

	private ExpStockDataFilter expStockDataFilter;

	
	@In
	private ExpStockService expStockService;
	
	@Create
	public void initialize() {
		this.expStockDataFilter = new ExpStockDataFilter();
		this.expStockDataFilter.setWarehouseId(ParamUtil.getCurrentWarehouseId());
	}
	
	@Override
	public void query() {
		this.buildOrderFilterMap(expStockDataFilter);
		DataPage<ExpStockData> dataPage=expStockService.findRangeByFilter(expStockDataFilter, getStartIndex(), getPageSize());
		populateValues(dataPage);
	}

	/**
	 * @return the expStockDataFilter
	 */
	public ExpStockDataFilter getExpStockDataFilter() {
		return expStockDataFilter;
	}

	/**
	 * @param expStockDataFilter the expStockDataFilter to set
	 */
	public void setExpStockDataFilter(ExpStockDataFilter expStockDataFilter) {
		this.expStockDataFilter = expStockDataFilter;
	}
	
	
}
