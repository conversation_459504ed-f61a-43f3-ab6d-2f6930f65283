package com.daxia.wms.exp.dao;


import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.exp.entity.ExpTrsPickLog;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import java.util.List;

@Name("expTrsPickLogDAO")
@lombok.extern.slf4j.Slf4j
public class ExpTrsPickLogDAO extends HibernateBaseDAO<ExpTrsPickLog, Long> {

    public List<ExpTrsPickLog> getPickLogByDocId(Long docId, Long warehouseId){
        String hql="from ExpTrsPickLog o where o.docId=:docId and o.warehouseId = :warehouseId ";
        Query query=createQuery(hql);
        query.setLong("docId", docId);
        query.setLong("warehouseId", warehouseId);
        return query.list();
    }

}
