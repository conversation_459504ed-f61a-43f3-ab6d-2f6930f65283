package com.daxia.wms.exp.dao;

import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.exp.entity.TMSDoHeader;

@Name("tMSDoHeaderDAO")
@lombok.extern.slf4j.Slf4j
public class TMSDoHeaderDAO extends HibernateBaseDAO<TMSDoHeader, Long> {
	
	private static final long serialVersionUID = -2140057385887947295L;

	public TMSDoHeader findByDoNo(String doNo) {
        Criteria cri = this.getSession().createCriteria(TMSDoHeader.class);
        cri.add(Restrictions.eq("doNo", doNo));
        cri.setMaxResults(1);
        return (TMSDoHeader)cri.uniqueResult();
    }
}
