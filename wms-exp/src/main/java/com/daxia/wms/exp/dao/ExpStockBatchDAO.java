package com.daxia.wms.exp.dao;

import java.math.BigDecimal;
import java.util.List;

import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.exp.Constants.TrsType;
import com.daxia.wms.exp.entity.ExpStockBatch;
import com.daxia.wms.exp.util.WmsExpUtil;

@Name("expStockBatchDAO")
@AutoCreate
@lombok.extern.slf4j.Slf4j
public class ExpStockBatchDAO extends HibernateBaseDAO<ExpStockBatch, Long> {

	private static final long serialVersionUID = -8936031707640157137L;
	
	@SuppressWarnings("unchecked")
	public List<ExpStockBatch> findAllIterator() {
		String hql = "from ExpStockBatch o where o.status in (?,?) and o.failOverTimes < ? order by o.id";
		Query query = createQuery(hql);
		query.setParameter(0, WmsExpUtil.ExpStockLogStatus.INIT.getValue());
		query.setParameter(1, WmsExpUtil.ExpStockLogStatus.FAILED.getValue());
		query.setParameter(2, WmsExpUtil.MAX_FAIL_OVER_TIMES);
		return query.setMaxResults(500).list();
	}

	public  void updateStatus(Long id, int status) {
		createQuery(" update ExpStockBatch set status=? where id=?").setParameter(0, status).setParameter(1, id).executeUpdate();
	}
	
	private final static String STOCK_BATCH_QTY_SQL = 
		" select sum(sl.qty_available) as qty1" + 
		" from stk_batch_loc_lpn sl" + 
		" where sl.sku_id=:productId" +
		" and sl.lot_id =:lotId "+
		" and sl.warehouse_id =:warehouseId ";
	
	private final static String STOCK_BATCH_ALLOCATED_QTY_SQL = 
		" select sum(ifnull(qty_allocating, 0)) " +
		" from stk_allocating allocating " +
		" inner join stk_batch_loc_lpn sl" +
		" on allocating.stk_lpn_id = sl.ID " +
		" and allocating.is_deleted = 0 " +
		" where sl.sku_id=:productId" +
		" and sl.lot_id =:lotId "+
		" and sl.warehouse_id =:warehouseId ";
	
	public Long findAvailableQty(Long lotId,Long productId, Long warehouseId){
		SQLQuery queryQty = createSQLQuery(STOCK_BATCH_QTY_SQL);
		queryQty.setParameter("productId", productId);
		queryQty.setParameter("lotId", lotId);
		queryQty.setParameter("warehouseId", warehouseId);
		
		SQLQuery queryAllocatingQty = createSQLQuery(STOCK_BATCH_ALLOCATED_QTY_SQL);
		queryAllocatingQty.setParameter("productId", productId);
		queryAllocatingQty.setParameter("lotId", lotId);
		queryAllocatingQty.setParameter("warehouseId", warehouseId);
		
		BigDecimal qty=(BigDecimal) queryQty.uniqueResult();
		BigDecimal qtyAllocating = (BigDecimal)queryAllocatingQty.uniqueResult();
		
		if (null != qty && null != qtyAllocating) {
			return qty.subtract(qtyAllocating).longValue() < 0 ? 0:qty.subtract(qtyAllocating).longValue();
		} else if (null != qty && null == qtyAllocating) {
			return qty.longValue();
		} else {
			return 0L;
		}
	}
	
	/**
	 * 根据搬仓明细id生成交易日志回写中间表数据
	 * @param transDId   搬仓明细id
	 * @param trsType  搬仓任务对应交易日志类型  上架为   搬出为TRANS_IN，搬出为TRANS_OUT
	 */
	public  void saveTransDatas(Long transDId,TrsType trsType) {
//		getSession().flush();
		String sql=	"insert into exp_stock_batch (doc_id, doc_type,status,create_by,create_time) "+
		"(select o.id,:logDocType,:logStatus,o.create_by,o.create_time from trs_transaction_log o "+  
		"where  o.is_deleted=0 "+          
		 "and o.task_id  in (select o1.id from tsk_trans o1 "+
         					"where o1.is_deleted=0 "+ 
         					//  "and O1.STATUS=:status "+ 
         					"and o1.trans_d_id=:transDId ) "+  
         "and o.transaction_type =:docType )";
		getSession().createSQLQuery(sql)
		.setParameter("logDocType", WmsExpUtil.ExpStockLogDocType.TL.getValue())
		.setParameter("logStatus", WmsExpUtil.ExpStockLogStatus.INIT.getValue())
		//.setParameter("status", transDetailStatus.getValue())
		.setParameter("transDId", transDId)
		.setParameter("docType", trsType.getValue()).executeUpdate();
	}
}
