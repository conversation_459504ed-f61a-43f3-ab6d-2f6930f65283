package com.daxia.wms.exp.action;

import java.sql.Blob;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.wms.exp.entity.ExpServiceLog;
import com.daxia.wms.exp.filter.ExpServiceLogFilter;
import com.daxia.wms.exp.service.ExpServiceLogService;
import com.daxia.wms.exp.util.WmsExpUtil;
import com.daxia.wms.exp.util.WmsExpUtil.ServiceLogStatus;

@Name("expServiceLogAction")
@AutoCreate
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class ExpServiceLogAction extends PagedListBean<ExpServiceLog> {
	private static final long serialVersionUID = -4786272426261217153L;
	private ExpServiceLogFilter expServiceLogFilter;
	private Long logId;
	private ExpServiceLog serviceLog;
	private String serviceData;
	@In
	private ExpServiceLogService expServiceLogService;

	@Create
	public void initialize() {
		this.expServiceLogFilter = new ExpServiceLogFilter();
		this.expServiceLogFilter.getOrderByMap().put("id", "desc");
	}
	
	
	@Override
	public void query() {
		this.buildOrderFilterMap(expServiceLogFilter);

		DataPage<ExpServiceLog> dataPage = expServiceLogService.query(expServiceLogFilter, getStartIndex(), getPageSize());
		populateValues(dataPage);
	}
	
	public void view() {
		this.serviceLog = expServiceLogService.get(logId);
		Blob serviceData = serviceLog.getServiceData();
		try {
			if (serviceData != null) {
				Object obj=WmsExpUtil.getServiceDataObj(serviceData);
				this.serviceData = WmsExpUtil.getXmlString(obj);
			} else {
				this.serviceData = null;
			}
		} catch (Exception e) {
			log.error("Error while recallService", e);
			this.serviceData = null;
		}
	}

	/**
	 * 重新调用服务
	 */
	public void recallService() {
		ExpServiceLog serviceLog = expServiceLogService.recallService(logId);
		int rowNum = ListUtil.getPosFromList(getQueryResult(), serviceLog, "id");
		dataPage.getDataList().remove(rowNum);
		dataPage.getDataList().add(rowNum, serviceLog);
		this.setOperationStatus(rowNum);
		if (serviceLog.getStatus().equals(ServiceLogStatus.SUCCESS.getValue())) {
			sayMessage(MESSAGE_SUCCESS);
		} else {
			sayMessage(MESSAGE_FAILED);
		}
	}
	
	/**
	 * @return the expServiceLogFilter
	 */
	public ExpServiceLogFilter getExpServiceLogFilter() {
		return expServiceLogFilter;
	}

	/**
	 * @param expServiceLogFilter the expServiceLogFilter to set
	 */
	public void setExpServiceLogFilter(ExpServiceLogFilter expServiceLogFilter) {
		this.expServiceLogFilter = expServiceLogFilter;
	}

	public Long getLogId() {
		return logId;
	}

	public void setLogId(Long logId) {
		this.logId = logId;
	}

	public ExpServiceLog getServiceLog() {
		return serviceLog;
	}

	public void setServiceLog(ExpServiceLog serviceLog) {
		this.serviceLog = serviceLog;
	}

	public String getServiceData() {
		return serviceData;
	}

	public void setServiceData(String serviceData) {
		this.serviceData = serviceData;
	}
}
