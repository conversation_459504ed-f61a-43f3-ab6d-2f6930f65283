package com.daxia.wms.exp.component;

import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.wms.exp.ExpServiceNames;
import com.daxia.wms.exp.ExpMsgConstans.MsgType;
import com.daxia.wms.exp.sys.base.constans.ExpSrvLogConstans.LogStatus;

@Name("expComponent")
@Scope(ScopeType.APPLICATION)
@lombok.extern.slf4j.Slf4j
public class ExpComponent {
	private List<SelectItem> serviceNameItems = new ArrayList<SelectItem>();
	private List<SelectItem> logStatusItems = new ArrayList<SelectItem>();
	private List<SelectItem> msgTypeItems=new ArrayList<SelectItem>();
	
	@Create
	public void init(){
		initExpServiceNames();
		initlogStatusItems();
		initmsgTypeItems() ;
	}
	
	public void initExpServiceNames() {
		for (ExpServiceNames s :ExpServiceNames.values()) {
			SelectItem item = new SelectItem();
			item.setLabel(s.name());
			item.setValue(s.name());
			
			serviceNameItems.add(item);
		}
	}
	

	public void initlogStatusItems() {
		for (LogStatus s :LogStatus.values()) {
			SelectItem item = new SelectItem();
			item.setLabel(s.getDecs());
			item.setValue(s.getValue());
			
			logStatusItems.add(item);
		}
	}
	
	public void initmsgTypeItems() {
		for (MsgType s :MsgType.values()) {
			SelectItem item = new SelectItem();
			item.setLabel(s.name());
			item.setValue(s.getValue());
			
			msgTypeItems.add(item);
		}
	}


	/**
	 * @return the serviceNameItems
	 */
	public List<SelectItem> getServiceNameItems() {
		return serviceNameItems;
	}


	/**
	 * @param serviceNameItems the serviceNameItems to set
	 */
	public void setServiceNameItems(List<SelectItem> serviceNameItems) {
		this.serviceNameItems = serviceNameItems;
	}


	/**
	 * @return the logStatusItems
	 */
	public List<SelectItem> getLogStatusItems() {
		return logStatusItems;
	}


	/**
	 * @param logStatusItems the logStatusItems to set
	 */
	public void setLogStatusItems(List<SelectItem> logStatusItems) {
		this.logStatusItems = logStatusItems;
	}

	/**
	 * @return the msgTypeItems
	 */
	public List<SelectItem> getMsgTypeItems() {
		return msgTypeItems;
	}

	/**
	 * @param msgTypeItems the msgTypeItems to set
	 */
	public void setMsgTypeItems(List<SelectItem> msgTypeItems) {
		this.msgTypeItems = msgTypeItems;
	}
}