package com.daxia.wms.exp.dao;


import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.exp.entity.ExpTrsSupervisionCodeLog;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @date 2021-01-21 14:33:59
 */
@Name("expTrsSupervisionCodeDAO")
@lombok.extern.slf4j.Slf4j
public class ExpTrsSupervisionCodeDAO extends HibernateBaseDAO<ExpTrsSupervisionCodeLog, Long> {


    public List<ExpTrsSupervisionCodeLog> findByDocIdAndDocType(Long docId, String docType) {
        String hql = "from ExpTrsSupervisionCodeLog s where s.docId = :docId and s.docType = :docType and s.warehouseId=:warehouseId";
        Query query = createQuery(hql);
        query.setParameter("docId", docId);
        query.setParameter("docType", docType);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }
}