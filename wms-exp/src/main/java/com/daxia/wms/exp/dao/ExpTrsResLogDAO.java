package com.daxia.wms.exp.dao;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.exp.entity.ExpTrsResLog;

@Name("expTrsResLogDAO")
@lombok.extern.slf4j.Slf4j
public class ExpTrsResLogDAO extends HibernateBaseDAO<ExpTrsResLog, Long>{
    
    /**
     * 
     */
    private static final long serialVersionUID = 8059677766719306117L;

    /**
     * 根据resHeaderId 和asnHeaderId 查询预约交易日志
     * @param resHeaderId
     * @param asnHeaderId
     * @return
     */
    public ExpTrsResLog getResLogByResId(Long resHeaderId,Long asnHeaderId,Long whId){
        
        String hql="from ExpTrsResLog o where o.resHeaderId=:resHeaderId and o.asnHeaderId=:asnHeaderId and o.warehouseId =:whId";
        Query query = createQuery(hql);
        query.setLong("resHeaderId", resHeaderId);
        query.setLong("asnHeaderId", asnHeaderId);
        query.setLong("whId", whId);
        return (ExpTrsResLog)query.uniqueResult();
    }
    
    /**
     * 根据交易日志ID 查询预约交易日志
     * @param trsResId
     * @return
     */
    public ExpTrsResLog getResLogById(Long trsResId,Long whId){
        
        String hql="from ExpTrsResLog o where o.id=:trsResId ";
        if(null != whId){
            hql += " and o.warehouseId =:whId";
        }
        Query query = createQuery(hql);
        query.setLong("trsResId", trsResId);
        if(null != whId){
            query.setLong("whId", whId);
        }
        return (ExpTrsResLog)query.uniqueResult();
    }
}
