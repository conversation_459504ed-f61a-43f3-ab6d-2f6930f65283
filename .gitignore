#idea
.idea
.iml
target
*/target
classes
# Logs
logs
*.log
.svn
.svn/wc.db
.flattened-pom.xml

# Runtime data
pids
*.pid
*.seed
*.iml

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# node-waf configuration
.lock-wscript

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Dependency directory
# https://www.npmjs.org/doc/misc/npm-faq.html#should-i-check-my-node_modules-folder-into-git
node_modules

# Ignore build files
public

build
