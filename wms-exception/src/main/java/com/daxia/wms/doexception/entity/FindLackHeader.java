package com.daxia.wms.doexception.entity;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.CascadeType;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.framework.common.util.ListUtil;


/**
 * 找货单头
 */
@Entity
@Table(name = "doc_find_lack_h")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_find_lack_h set is_deleted = 1 where id = ? and version = ?")
@BatchSize(size = 20)
@lombok.extern.slf4j.Slf4j
public class FindLackHeader extends WhBaseEntity {

    private static final long serialVersionUID = -4064847741622807134L;
    private Long id;
    private String findLackHeaderNo;
    private String printStatus;
    private Integer isDeleted;
    
    private List<FindLackDetail> findLackDetails;
    private List<FindLackTask> findLackTasks;
    
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.AUTO)  
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    @Column(name = "FIND_LACK_H_NO")
    public String getFindLackHeaderNo() {
        return findLackHeaderNo;
    }
    
    public void setFindLackHeaderNo(String findLackHeaderNo) {
        this.findLackHeaderNo = findLackHeaderNo;
    }
    
    @Column(name = "PRINT_STATUS")
    public String getPrintStatus() {
        return printStatus;
    }
    
    public void setPrintStatus(String printStatus) {
        this.printStatus = printStatus;
    }
    
    @Column(name = "IS_DELETED")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "findLackHeader")
    @Where(clause = " IS_DELETED = 0 ")
    @Cascade(value={CascadeType.SAVE_UPDATE}) 
	public List<FindLackDetail> getFindLackDetails() {
		return findLackDetails;
	}

	public void setFindLackDetails(List<FindLackDetail> findLackDetails) {
		this.findLackDetails = findLackDetails;
	}

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "findLackHeader")
    @Where(clause = " IS_DELETED = 0 ")
    @Cascade(value={CascadeType.SAVE_UPDATE}) 
	public List<FindLackTask> getFindLackTasks() {
		return findLackTasks;
	}

	public void setFindLackTasks(List<FindLackTask> findLackTasks) {
		this.findLackTasks = findLackTasks;
	}
	
	/**
	 * 获取此单的sku总数
	 * @return
	 */
	@Transient
	public BigDecimal getTotalSkus() {
	    if (ListUtil.isNullOrEmpty(this.getFindLackTasks())) {
            return BigDecimal.ZERO;
        }
	    Set<Long> skuSet = new HashSet<Long>();
        for (FindLackTask ft : this.getFindLackTasks()) {
            skuSet.add(ft.getSkuId());
        }
        return BigDecimal.valueOf(skuSet.size());
	}
	
	/**
	 * 获取此单的取货总数
	 * @return
	 */
	@Transient
	public BigDecimal getTotalUnits() {
	    if (ListUtil.isNullOrEmpty(this.getFindLackDetails())) {
            return BigDecimal.ZERO;
        }
        BigDecimal totalUnits = BigDecimal.ZERO;
        for (FindLackDetail fd : getFindLackDetails()) {
            totalUnits = totalUnits.add(fd.getLackQty());
        }
        return totalUnits;
	}
	
	/**
	 * 获取此单的发货单数
	 * @return
	 */
	@Transient
	public BigDecimal getTotalDos() {
	    return this.getFindLackDetails() == null ? BigDecimal.ZERO : BigDecimal.valueOf(getFindLackDetails().size());
	}
}
