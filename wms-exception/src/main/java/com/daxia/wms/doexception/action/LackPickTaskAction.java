package com.daxia.wms.doexception.action;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.pick.entity.PickTask;
import com.daxia.wms.delivery.pick.filter.PickTaskFilter;
import com.daxia.wms.delivery.pick.service.PickTaskService;

@Name("com.daxia.wms.exception.lackPickTaskAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class LackPickTaskAction extends PagedListBean<PickTask> {
	private static final long serialVersionUID = 8176243321529043628L;
	private Long doDetailId;

    @In
    private PickTaskService pickTaskService;

    public void init() {
        this.buttonQuery();
    }

    @Override
    public void query() {
        PickTaskFilter filter = new PickTaskFilter();
        filter.setDoDetailId(doDetailId);
		DataPage<PickTask> dataPage = pickTaskService.query(filter, getStartIndex(), getPageSize());
		this.populateValues(dataPage);
    }

	public Long getDoDetailId() {
		return doDetailId;
	}
	
	public void setDoDetailId(Long doDetailId) {
		this.doDetailId = doDetailId;
	}
}