package com.daxia.wms.doexception.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 找货DTO
 */
@lombok.extern.slf4j.Slf4j
public class FindLackDto implements Serializable {

    private static final long serialVersionUID = 3987785209668285260L;
    private Long doId;                      //发货单ID
    private String doNo;                   //发货单号
    private String lackHstatus;
    private String findLackHNo;        //找货单头号
    private String holdReason;         //冻结原因
    private String planShipTime;        //预计出库时间
    private String doStatus;             //发货单状态
    private BigDecimal lackQty;       //缺货数
    private Date holdTime;              //冻结时间
    private String holdWho;            //冻结人
    private Date lackCreateTime;    //找货任务生成时间
    private String lackCreater;        //找货任务生成人
    private String pringStatus;       //打印状态
    private String lackPartition;     //缺货库区(如有一个则显示，多个则不显示)
    private  String firstHoldCode;    //初始冻结原因
    
    public Long getDoId() {
        return doId;
    }
    
    public void setDoId(Long doId) {
        this.doId = doId;
    }
    
    public String getDoNo() {
        return doNo;
    }
    
    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }
    
    
    public String getLackHstatus() {
        return lackHstatus;
    }

    
    public void setLackHstatus(String lackHstatus) {
        this.lackHstatus = lackHstatus;
    }

    public String getFindLackHNo() {
        return findLackHNo;
    }
    
    public void setFindLackHNo(String findLackHNo) {
        this.findLackHNo = findLackHNo;
    }
    
    public String getHoldReason() {
        return holdReason;
    }
    
    public void setHoldReason(String holdReason) {
        this.holdReason = holdReason;
    }
    
    public String getPlanShipTime() {
        return planShipTime;
    }
    
    public void setPlanShipTime(String planShipTime) {
        this.planShipTime = planShipTime;
    }
    
    public String getDoStatus() {
        return doStatus;
    }
    
    public void setDoStatus(String doStatus) {
        this.doStatus = doStatus;
    }
    
    public BigDecimal getLackQty() {
        return lackQty;
    }
    
    public void setLackQty(BigDecimal lackQty) {
        this.lackQty = lackQty;
    }
    
    public Date getHoldTime() {
        return holdTime;
    }
    
    public void setHoldTime(Date holdTime) {
        this.holdTime = holdTime;
    }
    
    public String getHoldWho() {
        return holdWho;
    }
    
    public void setHoldWho(String holdWho) {
        this.holdWho = holdWho;
    }
    
    public Date getLackCreateTime() {
        return lackCreateTime;
    }
    
    public void setLackCreateTime(Date lackCreateTime) {
        this.lackCreateTime = lackCreateTime;
    }
    
    public String getLackCreater() {
        return lackCreater;
    }
    
    public void setLackCreater(String lackCreater) {
        this.lackCreater = lackCreater;
    }
    
    public String getPringStatus() {
        return pringStatus;
    }
    
    public void setPringStatus(String pringStatus) {
        this.pringStatus = pringStatus;
    }
    
    public String getLackPartition() {
        return lackPartition;
    }
    
    public void setLackPartition(String lackPartition) {
        this.lackPartition = lackPartition;
    }

	public String getFirstHoldCode() {
		return firstHoldCode;
	}

	public void setFirstHoldCode(String firstHoldCode) {
		this.firstHoldCode = firstHoldCode;
	}
    
}
