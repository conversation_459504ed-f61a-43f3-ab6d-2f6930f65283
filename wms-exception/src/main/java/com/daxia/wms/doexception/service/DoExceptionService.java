package com.daxia.wms.doexception.service;

import java.util.List;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.delivery.deliveryorder.filter.BackExceptionFilter;
import com.daxia.wms.delivery.recheck.dto.TempCartonExceptionDTO;
import com.daxia.wms.doexception.filter.ExceptionDoFilter;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;

public interface DoExceptionService {

	/**
     * 
     * <pre>
     * 分页查询送货单信息。
     * </pre>
     * 
     * @param doNo         定单号
     * @param holdReason     冻结原因
     * @param firstHoldReason 初始冻结原因
     * @param frozenTimefrom 开始冻结时间
     * @param frozenTimeTo   结束冻结时间
     * @param createdFrom    开始创建时间
     * @param createdTo      结束创建时间
     * @param doType         定单类型  
     * @param fromExpStatus  异常状态From
     * @param toExpStatus    异常状态To  
     * @param startIndex     初始条目
     * @param pageSize       显示数据条数
     * @return
     */
    public DataPage<DeliveryOrderHeader> queryFrozenDo(ExceptionDoFilter exceptionDoFilter, int startIndex, int pageSize);
    
    /**
     * 释放列表中指定的定单
     * @param doIds
     */
    public List<List<String>> releaseDO(List<Long> doIds);
    
    /**
     * 分批释放所有可释放的DO
     * @return
     */
    public void releaseAllDos();
    
    /**
     * 根据do当前状态获得其可以回退到的其它状态
     * @param doStatus
     * @return
     */
    public List<String> getCanRollbackStatus(Long doId);
    
    
    public List<String> getCanRollbackStatus(String doStatus);
    
    /**
     * 请求对发货单状态进行回退
     * @param doId 发货单Id
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     */
    public void doRollback(Long doId, String toStatus);
    
    public DeliveryOrderHeader findDoById(Long id);
    
    /**
     * 对发货单进行取消操作
     * @param doId
     */
    public void doCancelDO(Long doId);
    
    /**
     * RF释放DO
     * @param doNo
     * @return
     */
    public Long rfReleaseDo(String doNo);
    
    public void callCS(Long doId, String notes);
    
    public void doSkipCS(Long doId);
    
    public void releaseAllNotLackDos();
	public List<Long> releaseNotLackDos(List<Long> doIds);

    List<TempCartonExceptionDTO> findTempCartonExceptionList(BackExceptionFilter backExceptionFilter, int startIndex, int pageSize);

    Long countTempCartonException(BackExceptionFilter backExceptionFilter);
}
