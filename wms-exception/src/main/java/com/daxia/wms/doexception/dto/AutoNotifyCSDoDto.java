package com.daxia.wms.doexception.dto;

import java.io.Serializable;

@lombok.extern.slf4j.Slf4j
public class AutoNotifyCSDoDto implements Serializable {

    private static final long serialVersionUID = 6283434489556144508L;

    private Long doId;

    private String holdCode;

    private Long whId;

    public Long getDoId() {
        return doId;
    }

    public void setDoId(Long doId) {
        this.doId = doId;
    }

    public String getHoldCode() {
        return holdCode;
    }

    public void setHoldCode(String holdCode) {
        this.holdCode = holdCode;
    }

    public Long getWhId() {
        return whId;
    }

    public void setWhId(Long whId) {
        this.whId = whId;
    }

}
