package com.daxia.wms.doexception.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.doexception.entity.FindLackHeader;
import com.daxia.wms.doexception.filter.FindLackFilter;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.jboss.seam.annotations.Name;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;


/**
 * 找货任务DAO
 */
@Name("com.daxia.wms.exception.findLackHeaderDAO")
@lombok.extern.slf4j.Slf4j
public class FindLackHeaderDAO extends HibernateBaseDAO<FindLackHeader,Long> {

    private static final long serialVersionUID = 3334736550969635075L;

    public FindLackHeader findHeaderByDocNo(String docNo) {
    	String hql = "from FindLackHeader o where o.findLackHeaderNo = :docNo and o.warehouseId = :warehouseId";
    	Query query = createQuery(hql);
    	query.setParameter("docNo", docNo);
    	query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
    	Object result = query.uniqueResult();
    	return result == null ? null : (FindLackHeader)result;
    }
    
    /**
     * 分页查询找货缺货任务信息
     * @param filter
     * @param startIndex
     * @param pageSize
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Object> queryFldInfoPageByFilter(FindLackFilter filter, int startIndex, int pageSize) {
        Query  dataQuery = this.getQuery(filter, false, startIndex, pageSize);
        return dataQuery.list();
    }
    
    /**
     * 找货缺货任务页面分页查询总条数
     * @param filter
     * @return
     */
    public int getFindLackCount(FindLackFilter filter) {
        Query countQuery = this.getQuery(filter, true, 0, 0);
        BigInteger cnt = (BigInteger) countQuery.uniqueResult();
        return cnt.intValue();
    }
    
    /**
     * 获取查询或查总条数Query
     * @param filter
     * @param countFlag
     * @param startIndex
     * @param pageSize
     * @return
     */
    private Query getQuery(FindLackFilter filter, boolean countFlag, int startIndex, int pageSize) {
        StringBuffer sqlSb = new StringBuffer();
        if (countFlag) {
            sqlSb.append(" select count(a.id) from ( ");
        }
        sqlSb.append(" select dh.id,dh.do_no,lh.create_status,fh.find_lack_h_no,dh.hold_reason,date_format(dh.plan_ship_time,'%Y-%m-%d %H:%i:%S'), ")
         .append(" dh.status, dh.hold_time,dh.hold_who,fd.create_time,fd.create_by,fh.print_status, lh.lack_partitions, ")
         .append(" (select sum(ifnull(t.qty,0)) from do_lack_detail t where t.do_header_id = dh.id and t.is_deleted = 0 and t.warehouse_id = :whId), ")
         .append(" dh.first_hold_code")
         .append(" from do_lack_header lh inner join doc_do_header dh on lh.do_header_id = dh.id ")
         .append(" left join doc_find_lack_d fd on dh.id = fd.do_header_id and fd.is_deleted = 0 and fd.warehouse_id = :whId  ")
         .append(" left join doc_find_lack_h fh on fd.find_lack_h_Id = fh.id and fh.is_deleted = 0 and fh.warehouse_id = :whId  ")
         .append(" where dh.is_deleted = 0 and lh.is_deleted = 0 ")
         .append(" and dh.do_type = '1' ")
         .append(" and dh.warehouse_id = :whId and lh.warehouse_id = :whId ");
        
        boolean hasPartition = StringUtil.isNotEmpty(filter.getLackPartition());
        boolean hasDoNo = StringUtil.isNotEmpty(filter.getDoNo());
        boolean hasFindLackHNo = StringUtil.isNotEmpty(filter.getFindLackHNo());
        boolean hasHoldCode = StringUtil.isNotEmpty(filter.getHoldCode());
        boolean taskCreateStatus = StringUtil.isNotEmpty(filter.getFindLackStatus());
        boolean hasPrintStatus = StringUtil.isNotEmpty(filter.getPrintStatus());
        boolean hasShipFm = filter.getDoPlanShipTimeFrom() != null;
        boolean hasShipTo = filter.getDoPlanShipTimeTo() != null;
        boolean hasFrozenFm = filter.getHoldTimeFm() != null;
        boolean hasFrozenTo = filter.getHoldTimeTo() != null;
        boolean hasFristHoldCode = StringUtil.isNotEmpty(filter.getFirstHoldCode());

        if (hasPartition) {
            sqlSb.append(" and lh.lack_partitions = :lackPartitions");
        }
        if (hasDoNo) {
            sqlSb.append(" and dh.do_no= :doNo ");
        }
        if (hasFindLackHNo) {
            sqlSb.append(" and fh.find_lack_h_no = :findLackHNo ");
        }
        if (hasHoldCode) {
            sqlSb.append(" and dh.hold_code = :holdCode  ");
        }
        if (hasFristHoldCode) {
            sqlSb.append(" and dh.first_hold_code = :firstHoldCode  ");
        }
        if (taskCreateStatus) {
            sqlSb.append(" and lh.create_status = :createStatus ");
        }
        
        if (hasPrintStatus) {
            sqlSb.append(" and fh.print_status = :printStatus ");
        }
        
        if (hasShipFm) {
            sqlSb.append(" and dh.plan_ship_time >= :planShipTimeFm ");
        }
        if (hasShipTo) {
            sqlSb.append(" and dh.plan_ship_time <= :planShipTimeTo ");
        }
        if (hasFrozenFm) {
            sqlSb.append(" and dh.hold_time >= :holdTimeFm ");
        }
        if (hasFrozenTo) {
            sqlSb.append(" and dh.hold_time <= :holdTimeTo ");
        }
        if (countFlag) {
            sqlSb.append(" ) a");
        } else {
            sqlSb.append(" order by lh.lack_partitions asc,  dh.plan_ship_time asc");
        }
        Query query = this.createSQLQuery(sqlSb.toString());
        if (hasPartition) {
            query.setParameter("lackPartitions", filter.getLackPartition());
        }
        if (hasDoNo) {
            query.setParameter("doNo", filter.getDoNo());
        }
        if (hasFindLackHNo) {
            query.setParameter("findLackHNo", filter.getFindLackHNo());
        }
        if (hasHoldCode) {
            query.setParameter("holdCode", filter.getHoldCode());
        }
        if (hasFristHoldCode) {
        	 query.setParameter("firstHoldCode", filter.getFirstHoldCode());
        }
        if (taskCreateStatus) {
            query.setParameter("createStatus", filter.getFindLackStatus());
        }
        if (hasPrintStatus) {
            query.setParameter("printStatus", filter.getPrintStatus());
        }
        if (hasShipFm) {
            query.setParameter("planShipTimeFm", filter.getDoPlanShipTimeFrom());
        }
        if (hasShipTo) {
            query.setParameter("planShipTimeTo", filter.getDoPlanShipTimeTo());
        }
        if (hasFrozenFm) {
            query.setParameter("holdTimeFm", filter.getHoldTimeFm());
        }
        if (hasFrozenTo) {
            query.setParameter("holdTimeTo", filter.getHoldTimeTo());
        }
        query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
        if (!countFlag) {
            query.setFirstResult(startIndex);
            query.setMaxResults(pageSize);
        }
        return query;
    }
    
    /**
     * 根据发货单查询找货单头
     * @param doId
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<FindLackHeader> getByDoIds(List<Long> doIds) {
        String hql = "select distinct o.findLackHeader from FindLackDetail o where o.doHeaderId in (:doIds) and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql)
                .setParameterList("doIds", doIds)
                .setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
        return  query.list();
    }

	/**
	 * 根据ID逻辑删除，更新updateBy
	 */
	public void deleteFindLackHeaderById(Long findLackHeaderId) {
	     String hql = "update FindLackHeader o set o.isDeleted = 1,o.updatedBy = :updatedBy where o.id =:id and o.warehouseId = :warehouseId";
	        Query query = this.createQuery(hql)
	                .setParameter("updatedBy", ParamUtil.getCurrentLoginName())
	                .setParameter("id", findLackHeaderId)
	                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
	  query.executeUpdate();
	}

	public Date getEarliestPlanShipTimeByHeaderNo(String headerNo) {
		String hql = " select do.planShipTime from DeliveryOrderHeader do, FindLackDetail d, FindLackHeader h "
				+ " where d.findLackHId = h.id and h.findLackHeaderNo = :headerNo and d.doHeaderId = do.id "
				+ " and do.planShipTime is not null and do.warehouseId = d.warehouseId and h.warehouseId = d.warehouseId and d.warehouseId = :warehouseId "
				+ " order by do.planShipTime ";
		return (Date)createQuery(hql).setParameter("warehouseId", ParamUtil.getCurrentWarehouseId())
				.setParameter("headerNo", headerNo).setMaxResults(1).uniqueResult();
	}

	/**
	 * 查询缺货找货任务
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<FindLackHeader> getFindLackHeaderList() {
		StringBuilder sb = new StringBuilder();
		sb.append(" select distinct dflh.id as id,dflh.find_lack_h_no as findLackHeaderNo,dflh.create_by as createdBy,dflh.create_time as createdAt from do_lack_header dlh,doc_do_header ddh,doc_find_lack_d dfld,doc_find_lack_h dflh ");
		sb.append(" where dlh.create_status = '1' and dlh.do_header_id = ddh.id and ddh.id = dfld.do_header_id ");
		sb.append(" and dfld.find_lack_h_id = dflh.id and dlh.is_deleted=0 and ddh.is_deleted=0 and dfld.is_deleted=0 and dflh.is_deleted=0 ");
		sb.append(" and dlh.warehouse_id = :whId and ddh.warehouse_id = :whId and dfld.warehouse_id = :whId and dflh.warehouse_id = :whId ");
		sb.append(" and dflh.create_time > DATE_ADD(sysdate(), interval -7 day) ");
		sb.append(" order by dflh.create_time asc ");
		
		SQLQuery query = this.createSQLQuery(sb.toString());
		query.setParameter("whId", ParamUtil.getCurrentWarehouseId());
		
		query.addScalar("id", new org.hibernate.type.LongType());
		query.addScalar("findLackHeaderNo", new org.hibernate.type.StringType());
		query.addScalar("createdBy", new org.hibernate.type.StringType());
		query.addScalar("createdAt", new org.hibernate.type.TimestampType());
        
        List<FindLackHeader> list =  query.setResultTransformer(Transformers.aliasToBean(FindLackHeader.class)).list();
        return list;
	}
}
