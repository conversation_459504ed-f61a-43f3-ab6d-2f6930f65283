package com.daxia.wms.doexception.service;

import java.math.BigDecimal;
import java.util.List;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.doexception.filter.DoLackDetailFilter;
import com.daxia.wms.delivery.deliveryorder.entity.DoLackDetail;

public interface DoLackDetailService {
    /**
     * 根据缺货明细查询某商品总共缺多少
     * @param skuId
     * @return
     */
    BigDecimal getSkuLackQty(Long skuId);

    /**
     * 查询订单缺货明细（分页）
     * @param filter
     * @param startIndex
     * @param pageSize
     * @return
     */
    DataPage<DoLackDetail> findDoLackDetail(DoLackDetailFilter filter, int startIndex, int pageSize);

    /**
     * 查找缺货订单总数
     * @param skuId
     * @return
     */
    Long findTotalLack(Long skuId);
    
    /**
     * 根据发货单ID查询缺货明细
     * @param doHeaderId
     * @return
     */
    public List<DoLackDetail> findDoLackDetail(Long doHeaderId);
    
    /**
     * 查询发货单中的sku数去重
     * @param doNos
     * @return
     */
    public BigDecimal findDisTSkuByDoNos(List<Long> doIds);
    
    /**
     * 查询发货单中的取货总数
     * @param doIds
     * @return
     */
    public BigDecimal findTotalCountByDos(List<Long> doIds);
    
    /**
     * 查询发货单缺货数
     * @param doHeaderId
     * @return
     */
    public BigDecimal findDoLackQty(Long doHeaderId);
}
