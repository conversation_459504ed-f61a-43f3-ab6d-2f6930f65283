package com.daxia.wms.doexception.action;

import java.util.ArrayList;
import java.util.List;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.doexception.filter.DoLackDetailFilter;
import com.daxia.wms.doexception.service.DoLackDetailService;
import com.daxia.wms.Constants.Reason;
import com.daxia.wms.Constants.ReleaseStatus;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DoLackDetail;
import com.daxia.wms.exp.Constants.DoExpStatus;
import com.daxia.wms.master.dto.SkuDTO;
import com.daxia.wms.master.entity.Sku;
import com.daxia.wms.master.service.SkuCache;
import com.daxia.wms.master.service.SkuService;

/**
 * 查询特定缺货订单Action
 */
@Name("com.daxia.wms.exception.lackDoQueryAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class LackDoQueryAction extends PagedListBean<DoLackDetail> {

    private static final long serialVersionUID = 8191165977704164507L;

    private Long skuId;

    private SkuDTO sku;

    private Long skuLackQty;

    private String barCode;

    private boolean isMultiProd = false;

    private List<Sku> skuList = new ArrayList<Sku>();

    private List<Long> skuIds = new ArrayList<Long>();

    @In
    DoLackDetailService doLackDetailService;

    @In
    SkuCache skuCache;

    @In
    SkuService skuService;

    /**
     * 每次弹框都初始化数据
     */
    public void init() {
        this.skuId = null;
        this.sku = null;
        this.skuLackQty = null;
        this.barCode = null;
        this.populateValues(new DataPage<DoLackDetail>());
        this.skuIds.clear();
        this.skuList.clear();
    }

    /**
     * 查询缺货订单，判断是否一码多品
     */
    @Override
    public void query() {
        skuIds = skuService.findSkuIdByCode(barCode);
        if (skuIds.size() <= 0) {
            //清空信息
            init();
            throw new DeliveryException(DeliveryException.NO_SKU);
        }
        if (skuIds.size() > 1) {
            this.isMultiProd = true;
            return;
        }
        skuId = skuIds.get(0);
        dealData(skuId);
    }

    /**
     * 选择正确的sku后继续查询
     */
    public void continueQuery() {
        dealData(skuId);
    }

    /**
     * 查询缺货订单，冻结原因：分拣冻结；异常状态：待通知客服；
     */
    private void dealData(Long skuId) {
        DoLackDetailFilter filter = new DoLackDetailFilter();
        if (null == skuId) {
            // 防止查出过多的数据
            throw new DeliveryException(DeliveryException.NO_SKU);
        }
        filter.setSkuId(skuId);
        filter.setReleaseStatus(ReleaseStatus.HOLD.getValue());
        filter.setHoldCode(Reason.SORT_LACK.getValue());
        filter.setExceptionStatus(DoExpStatus.TO_BE_ROLLBACK.getValue());
        filter.getOrderByMap().put("o.doHeader.createdAt", "asc");
        this.buildOrderFilterMap(filter);
        DataPage<DoLackDetail> dataPage = doLackDetailService.findDoLackDetail(filter, getStartIndex(), getPageSize());
        this.populateValues(dataPage);
        sku = skuCache.getSku(skuId);
        skuLackQty = doLackDetailService.findTotalLack(skuId);
        isMultiProd = false;
    }

    /**
     * 查找条码信息
     */
    public void queryBarcodeListByScan() {
        this.skuList.clear();
        this.skuList = skuService.querySkuList(this.skuIds);
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public SkuDTO getSku() {
        return sku;
    }

    public void setSku(SkuDTO sku) {
        this.sku = sku;
    }

    public Long getSkuLackQty() {
        return skuLackQty;
    }

    public void setSkuLackQty(Long skuLackQty) {
        this.skuLackQty = skuLackQty;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public boolean getIsMultiProd() {
        return isMultiProd;
    }

    public void setIsMultiProd(boolean isMultiProd) {
        this.isMultiProd = isMultiProd;
    }

    public List<Sku> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<Sku> skuList) {
        this.skuList = skuList;
    }

    public List<Long> getSkuIds() {
        return skuIds;
    }

    public void setSkuIds(List<Long> skuIds) {
        this.skuIds = skuIds;
    }
}