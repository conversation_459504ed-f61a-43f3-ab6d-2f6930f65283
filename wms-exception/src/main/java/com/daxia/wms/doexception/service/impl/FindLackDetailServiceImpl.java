package com.daxia.wms.doexception.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import com.daxia.framework.common.util.ListUtil;
import com.daxia.wms.doexception.dao.FindLackDetailDAO;
import com.daxia.wms.doexception.entity.FindLackDetail;
import com.daxia.wms.doexception.entity.FindLackHeader;
import com.daxia.wms.doexception.service.FindLackDetailService;
import com.daxia.wms.doexception.service.FindLackHeaderService;

/**
 * 找货明细service实现
 */
@Name("com.daxia.wms.exception.findLackDetailService")
@lombok.extern.slf4j.Slf4j
public class FindLackDetailServiceImpl implements FindLackDetailService {
    
    @In
    private FindLackDetailDAO findLackDetailDAO;
    @In
    private FindLackHeaderService findLackHeaderService;

    @Override
    public boolean isDoHasFindLack(List<Long> doIds) {
        List<FindLackDetail> lackDetails = findLackDetailDAO.findByDoIds(doIds);
        return ListUtil.isNotEmpty(lackDetails);
    }

    @Override
    @Transactional
    public void saveOrUpdate(FindLackDetail findLackDetail) {
        findLackDetailDAO.saveOrUpdate(findLackDetail);
    }

    @Override
    @Transactional
    public void deleteByDoIds(List<Long> doIds) {
        List<FindLackHeader> findLackHeaders = findLackHeaderService.getByDoIds(doIds);
        int deleteCount = findLackDetailDAO.deleteByDoIds(doIds);
        if (deleteCount > 0) {
            for (FindLackHeader findLackHeader : findLackHeaders) {
                if (!isExistsByFindLackHeader(findLackHeader.getId())) {
                    findLackHeaderService.deleteById(findLackHeader.getId());
                }
            }
        }
    }
    
    @Override
    @Transactional
    public void deleteByDoId(Long doId) {
        List<Long> doIds = new ArrayList<Long>(1);
        doIds.add(doId);
        deleteByDoIds(doIds);
    }
    
    /**
     * 查询找货单下是否存在找货明细
     * @param findLackHeaderId
     * @return
     */
    private boolean isExistsByFindLackHeader(Long findLackHeaderId) {
        return findLackDetailDAO.isExists("findLackHId", findLackHeaderId, null);
    }
}
