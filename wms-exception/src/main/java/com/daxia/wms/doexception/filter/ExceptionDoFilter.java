package com.daxia.wms.doexception.filter;

import java.util.Date;
import java.util.List;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

/**
 * 异常DO查询filter
 */
@lombok.extern.slf4j.Slf4j
public class ExceptionDoFilter extends WhBaseQueryFilter {

	private static final long serialVersionUID = -5978043970211367913L;

	private String doNo;
	
	private String waveNo;

	private String holdReason;

	private String doType;// 定单类型

	private String firstHoldReason;

	private String barCode;

	private String fromExpStatus;

	private String toExpStatus;

	private Date frozenTimeTo;

	private Date frozenTimeFrom;

	private Date createdFrom;

	private Date createdTo;

	private String doStatusFrom;

	private String doStatusTo;

	/**
	 * 分拣筐编号
	 */
	private String sortContainerNo;
	
	private String csReplyFlag;

	//平台单号
	private String originalSoCode;
	
	private List<String> statusNotIn;

	private Long businessCustomerId;
	
	public String getWaveNo() {
		return waveNo;
	}
	
	public void setWaveNo(String waveNo) {
		this.waveNo = waveNo;
	}
	
	@Operation(fieldName = "status", operationType = OperationType.NOTIN)
	public List<String> getStatusNotIn() {
		return statusNotIn;
	}
	
	public void setStatusNotIn(List<String> statusNotIn) {
		this.statusNotIn = statusNotIn;
	}
	
	public String getOriginalSoCode() {
		return originalSoCode;
	}

	public void setOriginalSoCode(String originalSoCode) {
		this.originalSoCode = originalSoCode;
	}

	public String getDoNo() {
		return doNo;
	}

	public void setDoNo(String doNo) {
		this.doNo = doNo;
	}

	public String getHoldReason() {
		return holdReason;
	}

	public void setHoldReason(String holdReason) {
		this.holdReason = holdReason;
	}

	public String getDoType() {
		return doType;
	}

	public void setDoType(String doType) {
		this.doType = doType;
	}

	public String getFirstHoldReason() {
		return firstHoldReason;
	}

	public void setFirstHoldReason(String firstHoldReason) {
		this.firstHoldReason = firstHoldReason;
	}

	public String getBarCode() {
		return barCode;
	}

	public void setBarCode(String barCode) {
		this.barCode = barCode;
	}

	public String getFromExpStatus() {
		return fromExpStatus;
	}

	public void setFromExpStatus(String fromExpStatus) {
		this.fromExpStatus = fromExpStatus;
	}

	public String getToExpStatus() {
		return toExpStatus;
	}

	public void setToExpStatus(String toExpStatus) {
		this.toExpStatus = toExpStatus;
	}

	public Date getFrozenTimeTo() {
		return frozenTimeTo;
	}

	public void setFrozenTimeTo(Date frozenTimeTo) {
		this.frozenTimeTo = frozenTimeTo;
	}

	public Date getFrozenTimeFrom() {
		return frozenTimeFrom;
	}

	public void setFrozenTimeFrom(Date frozenTimeFrom) {
		this.frozenTimeFrom = frozenTimeFrom;
	}

	public Date getCreatedFrom() {
		return createdFrom;
	}

	public void setCreatedFrom(Date createdFrom) {
		this.createdFrom = createdFrom;
	}

	public Date getCreatedTo() {
		return createdTo;
	}

	public void setCreatedTo(Date createdTo) {
		this.createdTo = createdTo;
	}

	public String getDoStatusFrom() {
		return doStatusFrom;
	}

	public void setDoStatusFrom(String doStatusFrom) {
		this.doStatusFrom = doStatusFrom;
	}

	public String getDoStatusTo() {
		return doStatusTo;
	}

	public void setDoStatusTo(String doStatusTo) {
		this.doStatusTo = doStatusTo;
	}

	public String getSortContainerNo() {
		return sortContainerNo;
	}

	public void setSortContainerNo(String sortContainerNo) {
		this.sortContainerNo = sortContainerNo;
	}

	public String getCsReplyFlag() {
		return csReplyFlag;
	}

	public void setCsReplyFlag(String csReplyFlag) {
		this.csReplyFlag = csReplyFlag;
	}

	public Long getBusinessCustomerId() {
		return businessCustomerId;
	}

	public void setBusinessCustomerId(Long businessCustomerId) {
		this.businessCustomerId = businessCustomerId;
	}
}
