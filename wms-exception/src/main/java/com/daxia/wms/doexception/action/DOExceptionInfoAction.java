package com.daxia.wms.doexception.action;

import java.util.ArrayList;
import java.util.List;


import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.ActionBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.wms.doexception.service.DoExceptionService;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.DoExpStatus;
import com.daxia.wms.Constants.DoStatus;
import com.daxia.wms.Constants.DoType;
import com.daxia.wms.Constants.Reason;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoExceptionLog;
import com.daxia.wms.delivery.deliveryorder.entity.DoLackDetail;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.DoExceptionLogService;
import com.daxia.wms.delivery.pick.service.PickService;
import com.daxia.wms.exp.service.ExpFacadeService;

@Name("com.daxia.wms.exception.doExceptionInfoAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class DOExceptionInfoAction extends ActionBean {

    private static final long serialVersionUID = 8191165977704164507L;

    private Long doHeaderId;
    private DeliveryOrderHeader deliveryOrderHeader;
    private List<String> exceptionLogList;
    private DoExceptionLog firstExpLog;
    private List<DoLackDetail> doLackDetails;
    
    private String initStatus = Constants.DoStatus.INITIAL.getValue();//仅用于显示效果控制
    private String allAllocatedStatus = Constants.DoStatus.ALLALLOCATED.getValue();
    private String partPickedStatus = Constants.DoStatus.PARTPICKED.getValue();
    
    @In
    DeliveryOrderService deliveryOrderService;

    @In
    DoExceptionLogService doExceptionLogService;
    
    @In
    private DoExceptionService doExceptionService;
    
    @In
    private PickService pickService;
    @In
    private ExpFacadeService expFacadeService;
    
    @Create
    public void init() {

    }

    public void show() {
        deliveryOrderHeader = this.deliveryOrderService.getDoHeaderById(doHeaderId);
        doLackDetails = deliveryOrderHeader.getDoLackDetails();
        
        List<DoExceptionLog> exceptionLogs = doExceptionLogService.findExceptionLog(doHeaderId);
        for (DoExceptionLog log : exceptionLogs) {
            if (firstExpLog == null || log.getCreatedAt().getTime() < firstExpLog.getCreatedAt().getTime()) {
                firstExpLog = log;
            }
        }
        exceptionLogList = doExceptionLogService.formatExceptionLogs(exceptionLogs);
    }
    
    public void cancel(){
        try{
            this.doExceptionService.doCancelDO(doHeaderId);
        }catch(DeliveryException de){
            throw de;
        }catch(Exception e){
            log.error(e.getMessage(), e);
            throw new DeliveryException("exception.send.cancel.error");
        }
        this.sayMessage(MESSAGE_SUCCESS);
    }
    
    public void release(){
        List<Long> do2Release = new ArrayList<Long>(1);
        do2Release.add(this.doHeaderId);
        List<List<String>> result = doExceptionService.releaseDO(do2Release);
        List<String> canReleaseList = result.get(0);
		List<String> canNotReleaseList = result.get(1);
		//调用scs冻结释放do接口。
		if(!ListUtil.isNullOrEmpty(canReleaseList)){
			List<Long> dolist = new ArrayList<Long>();
			for(String strDoId : canReleaseList){
				dolist.add(Long.valueOf(strDoId));
			}
        	expFacadeService.sendDoReleaseOrHold2ScsBatch(dolist);
        }
        if(!ListUtil.isNullOrEmpty(canNotReleaseList)){
            throw new DeliveryException("exception.release.error.skuCode", ListUtil.collection2String(canNotReleaseList, ", "));
        }
        this.sayMessage(MESSAGE_SUCCESS);
    }
    
    public void rollback(){
        try{
            this.doExceptionService.doRollback(doHeaderId,DoStatus.INITIAL.getValue());
        }catch(DeliveryException e){
            log.error(e.getMessage(), e);
            throw e;
        }catch(Exception e){
            log.error(e.getMessage(), e);
            throw new DeliveryException("exception.send.rollback.error");
        }
        this.sayMessage(MESSAGE_SUCCESS);
    }

    public Long getDoHeaderId() {
        return doHeaderId;
    }

    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }

    public DeliveryOrderHeader getDeliveryOrderHeader() {
        return deliveryOrderHeader;
    }

    public void setDeliveryOrderHeader(DeliveryOrderHeader deliveryOrderHeader) {
        this.deliveryOrderHeader = deliveryOrderHeader;
    }

    public List<String> getExceptionLogList() {
        return exceptionLogList;
    }

    public void setExceptionLogList(List<String> exceptionLogList) {
        this.exceptionLogList = exceptionLogList;
    }

    public DoExceptionLog getFirstExpLog() {
        return firstExpLog;
    }

    public void setFirstExpLog(DoExceptionLog firstExpLog) {
        this.firstExpLog = firstExpLog;
    }

    public List<DoLackDetail> getDoLackDetails() {
        return doLackDetails;
    }

    public void setDoLackDetails(List<DoLackDetail> doLackDetails) {
        this.doLackDetails = doLackDetails;
    }

	public String getInitStatus() {
		return initStatus;
	}

	public void setInitStatus(String initStatus) {
		this.initStatus = initStatus;
	}
    
	public String getAllAllocatedStatus() {
		return allAllocatedStatus;
	}

	public void setAllAllocatedStatus(String allAllocatedStatus) {
		this.allAllocatedStatus = allAllocatedStatus;
	}

	public String getPartPickedStatus() {
		return partPickedStatus;
	}

	public void setPartPickedStatus(String partPickedStatus) {
		this.partPickedStatus = partPickedStatus;
	}
	
	public void skipCS(){
		if(doHeaderId == null){
			return;
    	}
    	this.doExceptionService.doSkipCS(doHeaderId);
    	/**do跳过客服之后释放do时调scs接口 */
		expFacadeService.sendDoReleaseOrHold2Scs(doHeaderId);
	}

	/**
     * 强制拣货 
     */
    public void doForcePick(){
		pickService.force2pick(doHeaderId);
		/**do强制拣货之后释放do时调scs接口 */
		expFacadeService.sendDoReleaseOrHold2Scs(doHeaderId);
	}
    
    // 缺货 且 已打波次
    // 且 （普通订单 且 待回退 或者 调拨、RTV 且 待回退）
    public boolean canForcePick() {
        return !deliveryOrderHeader.getNeedCancel()
        		&& YesNo.YES.getValue().equals(deliveryOrderHeader.getLackStatus()) && deliveryOrderHeader.getWaveId() != null &&
                ((DoType.SELL.getValue().equals(deliveryOrderHeader.getDoType()) && DoExpStatus.TO_BE_ROLLBACK.getValue().equals(deliveryOrderHeader.getExceptionStatus())
                        || ((StringUtil.isIn(deliveryOrderHeader.getDoType(), DoType.ALLOT.getValue(), DoType.RTV.getValue()) && DoExpStatus.TO_BE_ROLLBACK.getValue().equals(deliveryOrderHeader.getExpectedArriveTime1())))));
    }
    
    // 普通订单，待通知客服或待补货
    //或 调拨、RTV，待回退或待补货
    public boolean canRelease() {
        return !deliveryOrderHeader.getNeedCancel() && 
        		((DoType.SELL.getValue().equals(deliveryOrderHeader.getDoType()) && StringUtil.isIn(deliveryOrderHeader.getExceptionStatus(), DoExpStatus.TO_BE_ANNOUNCE.getValue(), DoExpStatus.TO_BE_REPL.getValue()) && !Reason.PICK_LACK.getValue().equals(deliveryOrderHeader.getHoldCode()))
                || (StringUtil.isIn(deliveryOrderHeader.getDoType(), DoType.ALLOT.getValue(), DoType.RTV.getValue()) && StringUtil.isIn(deliveryOrderHeader.getExceptionStatus(), DoExpStatus.TO_BE_ROLLBACK.getValue(), DoExpStatus.TO_BE_REPL.getValue())));
    }
    
	public boolean canRollBack(){
		return (!DoStatus.INITIAL.getValue().equals(deliveryOrderHeader.getStatus())) //不是初始化状态 
					&& (DoStatus.PART_LOAD.getValue().compareTo(deliveryOrderHeader.getStatus()) > 0 //必须处于交接前的状态
					&& DoExpStatus.TO_BE_ROLLBACK.getValue().equals(deliveryOrderHeader.getExceptionStatus())); //必须是待回退状态
	}
}