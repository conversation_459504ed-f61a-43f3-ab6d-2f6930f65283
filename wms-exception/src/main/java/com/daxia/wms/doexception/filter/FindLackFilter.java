package com.daxia.wms.doexception.filter;

import java.util.Date;

import com.daxia.framework.common.filter.WhBaseQueryFilter;

/**
 * 找货任务filter
 */
@lombok.extern.slf4j.Slf4j
public class FindLackFilter extends WhBaseQueryFilter {

    private static final long serialVersionUID = -828544257763318803L;
    private String doNo;
    private String findLackHNo;
    private String holdCode;
    private String findLackStatus;
    private String printStatus;
    private Date doPlanShipTimeFrom;
    private Date doPlanShipTimeTo;
    private Date holdTimeFm;
    private Date holdTimeTo;
    private String lackPartition;
    private String firstHoldCode;
    
    public String getDoNo() {
        return doNo;
    }
    
    public void setDoNo(String doNo) {
        this.doNo = doNo;
    }
    
    public String getFindLackHNo() {
        return findLackHNo;
    }
    
    public void setFindLackHNo(String findLackHNo) {
        this.findLackHNo = findLackHNo;
    }
    
    public String getHoldCode() {
        return holdCode;
    }
    
    public void setHoldCode(String holdCode) {
        this.holdCode = holdCode;
    }
    
    public String getFindLackStatus() {
        return findLackStatus;
    }
    
    public void setFindLackStatus(String findLackStatus) {
        this.findLackStatus = findLackStatus;
    }
    
    public String getPrintStatus() {
        return printStatus;
    }
    
    public void setPrintStatus(String printStatus) {
        this.printStatus = printStatus;
    }
    
    
    public Date getDoPlanShipTimeFrom() {
        return doPlanShipTimeFrom;
    }

    
    public void setDoPlanShipTimeFrom(Date doPlanShipTimeFrom) {
        this.doPlanShipTimeFrom = doPlanShipTimeFrom;
    }

    
    public Date getDoPlanShipTimeTo() {
        return doPlanShipTimeTo;
    }

    
    public void setDoPlanShipTimeTo(Date doPlanShipTimeTo) {
        this.doPlanShipTimeTo = doPlanShipTimeTo;
    }

    public Date getHoldTimeFm() {
        return holdTimeFm;
    }
    
    public void setHoldTimeFm(Date holdTimeFm) {
        this.holdTimeFm = holdTimeFm;
    }
    
    public Date getHoldTimeTo() {
        return holdTimeTo;
    }
    
    public void setHoldTimeTo(Date holdTimeTo) {
        this.holdTimeTo = holdTimeTo;
    }

    
    public String getLackPartition() {
        return lackPartition;
    }

    
    public void setLackPartition(String lackPartition) {
        this.lackPartition = lackPartition;
    }

	public String getFirstHoldCode() {
		return firstHoldCode;
	}

	public void setFirstHoldCode(String firstHoldCode) {
		this.firstHoldCode = firstHoldCode;
	}

}
