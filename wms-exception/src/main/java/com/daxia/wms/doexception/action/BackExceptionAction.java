package com.daxia.wms.doexception.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.filter.BackExceptionFilter;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.recheck.dto.TempCartonExceptionDTO;
import com.daxia.wms.delivery.recheck.entity.TempCarton;
import com.daxia.wms.delivery.recheck.service.TempCartonService;
import com.daxia.wms.doexception.service.DoExceptionService;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.intercept.BypassInterceptors;

import java.util.*;

@Name("com.daxia.wms.exception.backExceptionAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class BackExceptionAction extends PagedListBean<TempCartonExceptionDTO> {

	private static final long serialVersionUID = 8191165977704164507L;
	@In
	private TempCartonService tempCartonService;
    @In
    private DoExceptionService doExceptionService;

	private BackExceptionFilter backExceptionFilter;

	private Long doId;

	/**
	 * 进行定单释放时多选的定单Id
	 */
	private Map<Long, Boolean> selectMap;

	@Create
	public void init() {
		backExceptionFilter = new BackExceptionFilter();
		selectMap = new HashMap<Long, Boolean>();
		backExceptionFilter.setCreatedTo(new Date());
		backExceptionFilter.setCreatedFrom(DateUtil.getDateAfterIDay(DateUtil.getNowDate(), -14));
	}

	@Override
	public void query() {
        List<TempCartonExceptionDTO> list = doExceptionService.findTempCartonExceptionList(backExceptionFilter,getStartIndex(),getPageSize());
        Long count = doExceptionService.countTempCartonException(backExceptionFilter);
        int total = count==null? 0 : count.intValue();
        DataPage<TempCartonExceptionDTO> dataPage = new DataPage<TempCartonExceptionDTO>(total, getStartIndex(), getPageSize(), list);
		populateValues(dataPage);
	}

    private List<Long> getSelectedDo(){
        List<Long> results = new ArrayList<Long>();
        for(Map.Entry<Long, Boolean> entry: selectMap.entrySet()){
            if(entry.getValue()){
                results.add(entry.getKey());
            }
        }
        selectMap.clear();
        return results;
    }

    public void releaseDO() {
        TempCarton tc = tempCartonService.generateTempCarton(doId);
        if (tc != null) {
            tempCartonService.saveOrUpdate(tc);
        }
    }

    public void refreshFailOrder(){
        tempCartonService.refreshFailOrder();
    }


    public BackExceptionFilter getBackExceptionFilter() {
        return backExceptionFilter;
    }

    public void setBackExceptionFilter(BackExceptionFilter backExceptionFilter) {
        this.backExceptionFilter = backExceptionFilter;
    }


    @BypassInterceptors
    public Map<Long, Boolean> getSelectMap() {
        return selectMap;
    }

    public void setSelectMap(Map<Long, Boolean> selectMap) {
        this.selectMap = selectMap;
    }

    public Long getDoId() {
        return doId;
    }

    public void setDoId(Long doId) {
        this.doId = doId;
    }
}
