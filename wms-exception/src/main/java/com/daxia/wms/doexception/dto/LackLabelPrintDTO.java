package com.daxia.wms.doexception.dto;

@lombok.extern.slf4j.Slf4j
public class LackLabelPrintDTO {
	private String taskNo;
	private Long skuAmount;
	private Long unitAmount;
	private Long doAmount;
	private String planShipTime;
	
	public LackLabelPrintDTO() {
	}
	
	public LackLabelPrintDTO(String taskNo, Long skuAmount, Long unitAmount, Long doAmount, String planShipTime) {
		this.taskNo = taskNo;
		this.skuAmount = skuAmount;
		this.unitAmount = unitAmount;
		this.doAmount = doAmount;
		this.planShipTime = planShipTime;
	}

	public String getTaskNo() {
		return taskNo;
	}

	public void setTaskNo(String taskNo) {
		this.taskNo = taskNo;
	}

	public Long getSkuAmount() {
		return skuAmount;
	}

	public void setSkuAmount(Long skuAmount) {
		this.skuAmount = skuAmount;
	}

	public Long getUnitAmount() {
		return unitAmount;
	}

	public void setUnitAmount(Long unitAmount) {
		this.unitAmount = unitAmount;
	}

	public Long getDoAmount() {
		return doAmount;
	}

	public void setDoAmount(Long doAmount) {
		this.doAmount = doAmount;
	}

	public String getPlanShipTime() {
		return planShipTime;
	}

	public void setPlanShipTime(String planShipTime) {
		this.planShipTime = planShipTime;
	}
}
