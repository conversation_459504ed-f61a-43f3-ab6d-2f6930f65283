package com.daxia.wms.doexception.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.service.Dictionary;
import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.DateUtil;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.job.Constant;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.*;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.dao.DoHeaderDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.pick.service.PickService;
import com.daxia.wms.delivery.util.DoUtil;
import com.daxia.wms.doexception.filter.ExceptionDoFilter;
import com.daxia.wms.doexception.service.DoExceptionService;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.master.entity.BusinessCustomer;
import com.daxia.wms.master.service.BusinessCustomerService;
import com.daxia.wms.master.service.WarehouseService;
import com.google.common.collect.Lists;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.intercept.BypassInterceptors;

import javax.faces.model.SelectItem;
import java.util.*;

@Name("com.daxia.wms.exception.doExceptionAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class DOExceptionAction  extends PagedListBean<DeliveryOrderHeader> {

	private static final long serialVersionUID = 8191165977704164507L;

	@In
	private DoExceptionService doExceptionService;

	@In
	private PickService pickService;

	@In
	private DeliveryOrderService deliveryOrderService;

	@In
	private DoHeaderDAO doHeaderDAO;

	@In
	private ExpFacadeService expFacadeService;
	@In
	private WarehouseService warehouseService;

	private ExceptionDoFilter exceptionDoFilter;

	@In
	private BusinessCustomerService businessCustomerService;

	/**
	 * 进行定单释放时多选的定单Id
	 */
	private Map<Long, Boolean> selectMap;

	private String releaseStatus = Constants.ReleaseStatus.RELEASE.getValue();//仅用于显示

	private String holdStatus = Constants.ReleaseStatus.HOLD.getValue();//仅用于显示

	private String initStatus = Constants.DoStatus.INITIAL.getValue();//仅用于显示效果控制
	private String partStatus = Constants.DoStatus.PARTALLOCATED.getValue();//仅用于显示效果控制

	private String closeStatus = Constants.DoStatus.CLOSED.getValue();//仅用于显示

	private String allAllocatedStatus = Constants.DoStatus.ALLALLOCATED.getValue();

	private String partPickedStatus = Constants.DoStatus.PARTPICKED.getValue();

	private List<String> rollbackChoice;

	/**
	 * 在界面中选择的需要进行状态回滚的doHeader id
	 */
	private Long needRollbackDoId;

	private Long needCancelDoId;

	private Long needReleaseDoId;

	private Long mailToDoId;

	private Long forcePickDoId;

	private Long doHeaderId;

	private Long needSkipCSDoId;

	private String customerName;

	@Create
	public void init() {
		exceptionDoFilter = new ExceptionDoFilter();
		exceptionDoFilter.setStatusNotIn(Lists.<String>newArrayList(DoStatus.ALL_DELIVER.getValue(), DoStatus.CANCELED.getValue(), DoStatus.CLOSED.getValue()));
		selectMap = new HashMap<Long, Boolean>();
		exceptionDoFilter.setCreatedTo(new Date());
		//默认查询两星期内的数据
		exceptionDoFilter.setCreatedFrom(DateUtil.getDateAfterIDay(DateUtil.getNowDate(), -14));
	}

	/**
	 * 客户端调用此方法查询冻结的Do
	 */
	@Override
	public void query() {
		//防止手输不回车直接点查询，这里需要再次解密一下
		decryptDoNo();
		DataPage<DeliveryOrderHeader> dataPage = doExceptionService.queryFrozenDo(exceptionDoFilter, getStartIndex(), getPageSize());
		populateValues(dataPage);
	}

	/**
	 * 客户端调用此方法对选择的Do进行释放
	 */
	public void releaseDO() {
		List<Long> do2Release = getSelectedDo();
		DeliveryOrderHeader deliveryOrderHeader = doHeaderDAO.get(do2Release.get(0));
		if (Reason.PICK_LACK.getValue().equals(deliveryOrderHeader.getHoldCode())) {
			throw new DeliveryException(DeliveryException.PICK_LACK_CAN_NOT_RELEASE);
		}
		List<List<String>> result = doExceptionService.releaseDO(do2Release);
		List<String> canReleaseList = result.get(0);
		List<String> canNotReleaseList = result.get(1);
		//调用scs冻结释放do接口。
		if(!ListUtil.isNullOrEmpty(canReleaseList)){
			List<Long> dolist = new ArrayList<Long>();
			for(String strDoId : canReleaseList){
				dolist.add(Long.valueOf(strDoId));
			}
			expFacadeService.sendDoReleaseOrHold2ScsBatch(dolist);
		}
		if(!ListUtil.isNullOrEmpty(canNotReleaseList)){
			if (canNotReleaseList.size() == 1 && DoExpStatus.TO_BE_REPL.getValue().equals(deliveryOrderHeader.getExceptionStatus()) && DoType.SELL.getValue().equals(deliveryOrderHeader.getDoType()) && !deliveryOrderHeader.getNeedCancel()) {
				throw new DeliveryException("exception.release.error.skuCode", ListUtil.collection2String(canNotReleaseList, ", "));
			} else {
				throw new DeliveryException("exception.release.error", ListUtil.collection2String(canNotReleaseList, ", "));
			}
		}
	}

	/**
	 * 一键分批释放所有可释放的DO
	 */
	public void releaseAllDos() {
		doExceptionService.releaseAllDos();
	}

	/**
	 * 客户端调用此方法对单一Do进行释放
	 */
	public void release(){
		List<Long> do2Release = new ArrayList<Long>();
		do2Release.add(needReleaseDoId);
		DeliveryOrderHeader deliveryOrderHeader = doHeaderDAO.get(needReleaseDoId);
		List<List<String>> result = doExceptionService.releaseDO(do2Release);
		List<String> canReleaseList = result.get(0);
		List<String> canNotReleaseList = result.get(1);
		//调用scs冻结释放do接口。
		if(!ListUtil.isNullOrEmpty(canReleaseList)){
			List<Long> dolist = new ArrayList<Long>();
			for(String strDoId : canReleaseList){
				dolist.add(Long.valueOf(strDoId));
			}
			expFacadeService.sendDoReleaseOrHold2ScsBatch(dolist);
		}
		if(!ListUtil.isNullOrEmpty(canNotReleaseList)){
			if (canNotReleaseList.size() == 1
					&& DoExpStatus.TO_BE_REPL.getValue().equals(deliveryOrderHeader.getExceptionStatus())
					&& DoType.SELL.getValue().equals(deliveryOrderHeader.getDoType()) && ! deliveryOrderHeader.getNeedCancel()) {
				throw new DeliveryException("exception.release.error.skuCode", ListUtil.collection2String(canNotReleaseList, ", "));
			} else {
				throw new DeliveryException("exception.release.error", ListUtil.collection2String(canNotReleaseList, ", "));
			}
		}
	}

	/**
	 * 客户端调用此方法进行定单状态回滚
	 */
	public void rollback(){
		try{
			this.doExceptionService.doRollback(needRollbackDoId, DoStatus.INITIAL.getValue());
		}catch(DeliveryException e){
			log.error(e.getMessage(), e);
			throw e;
		}catch(Exception e){
			log.error(e.getMessage(), e);
			throw new DeliveryException("exception.send.rollback.error");
		}
	}

	/**
	 * 批量状态回退
	 */
	public void batchRollBack() {
		List<Long> do2rollBack = getSelectedDo();
		StringBuffer errDoNoSb = new StringBuffer();
		for (Long tempRollBackDoId : do2rollBack) {
			this.doExceptionService.doSkipCS(tempRollBackDoId);
		}
	}

	/**
	 * 客户调用此方法进行定单取消操作
	 */
	public void doCancelDO(){
		if(needCancelDoId == null){
			return;
		}
		try{
			this.doExceptionService.doCancelDO(needCancelDoId);
		}catch(DeliveryException de){
			throw de;
		}catch(Exception e){
			log.error(e.getMessage(), e);
			throw new DeliveryException("exception.send.cancel.error");
		}
	}

	public boolean canRollBack(String doStatus, String doExpStatus){
		if(doStatus == null){
			return false;
		}
		return (!DoStatus.INITIAL.getValue().equals(doStatus)) //不是初始化状态
				&& (DoStatus.PART_LOAD.getValue().compareTo(doStatus) > 0 //必须处于交接前的状态
				&& DoExpStatus.TO_BE_ROLLBACK.getValue().equals(doExpStatus)); //必须是待回退状态
	}

	// 普通订单，待通知客服或待补货
	//或 调拨、RTV，待回退或待补货
	public boolean canRelease(String doType, String doExpStatus, String holdCode, Boolean needCancel, String status) {
		return !needCancel && (
				(StringUtil.isIn(doType, DoType.WHOLESALE.getValue(), DoType.SELL.getValue()) && StringUtil.isIn(doExpStatus, DoExpStatus.TO_BE_ROLLBACK.getValue(), DoExpStatus.TO_BE_ANNOUNCE.getValue(),
				DoExpStatus.TO_BE_REPL.getValue()) && (!holdCode.equals(Reason.PICK_LACK.getValue()) || DoStatus.INITIAL.getValue().equals(status)))
						|| (StringUtil.isIn(doType, DoType.ALLOT.getValue(), DoType.RTV.getValue()) && StringUtil.isIn(doExpStatus, DoExpStatus
				.TO_BE_ROLLBACK.getValue(), DoExpStatus.TO_BE_REPL.getValue()))
		);
	}

	// 缺货 且 已打波次
	// 且 （普通订单 且 待回退 或者 调拨、RTV 且 待回退）
	public boolean canForcePick(Long doId, Long waveId, String doType, String doExpStatus, Integer lackStatus,Boolean needCancel) {
		return !needCancel && YesNo.YES.getValue().equals(lackStatus) && waveId != null &&
				(((StringUtil.isIn(doType,DoType.WHOLESALE.getValue(),DoType.SELL.getValue()) && DoExpStatus.TO_BE_ROLLBACK.getValue().equals(doExpStatus)
						|| ((StringUtil.isIn(doType,DoType.WHOLESALE.getValue(), DoType.ALLOT.getValue(), DoType.RTV.getValue()) && DoExpStatus.TO_BE_ROLLBACK.getValue().equals(doExpStatus))))));
	}

	public boolean canCompletLackPick(String doStatus, Long waveId, String doType, String doExpStatus, Integer lackStatus, Boolean needCancel) {
		return !needCancel && YesNo.YES.getValue().equals(lackStatus) && waveId != null && StringUtil.isIn(doStatus, DoStatus.ALLPICKED.getValue(), DoStatus.ALLSORTED.getValue()) &&
				StringUtil.isIn(doType, DoType.ALLOT.getValue(), DoType.RTV.getValue(), DoType.WHOLESALE.getValue()) && DoExpStatus.TO_BE_ROLLBACK.getValue().equals(doExpStatus);
	}

	public void skipCS(){
		if(needSkipCSDoId == null){
			return;
		}
		this.doExceptionService.doSkipCS(needSkipCSDoId);

		/**do跳过客服之后释放do时调scs接口 */
		expFacadeService.sendDoReleaseOrHold2Scs(needSkipCSDoId);
	}
	/**
	 * 强制拣货
	 */
	public void doForcePick() {
		pickService.force2pick(forcePickDoId);

		/**do强制拣货之后释放do时调scs接口 */
		expFacadeService.sendDoReleaseOrHold2Scs(forcePickDoId);
	}

	// 完成缺货拣货
	public void completLackPick() {
		pickService.completLackPick(forcePickDoId);

		/**do缺货发货之后释放do时调scs接口 */
		expFacadeService.sendDoReleaseOrHold2Scs(forcePickDoId);
	}

	/**
	 * 取得初始冻结原因：去掉冻结原因中的等待补货
	 * @return
	 */
	public List<SelectItem> findFirstHoldReasons(){
		List<SelectItem> list = new ArrayList<SelectItem>();
		List<SelectItem> all = Dictionary.getSelectItems("REASON_HDD");
		for (SelectItem item:all) {
			if (!Constants.Reason.WAIT_REPL.getValue().equals(item.getValue())) {
				list.add(item);
			}
		}
		return list;
	}

	private List<Long> getSelectedDo(){
		List<Long> results = new ArrayList<Long>();
		for(Map.Entry<Long, Boolean> entry: selectMap.entrySet()){
			if(entry.getValue()){
				results.add(entry.getKey());
			}
		}
		selectMap.clear();
		return results;
	}


	public void receiveSelectBusinessCustomer(Long businessCustomerId) {
		BusinessCustomer businessCustomer = null;
		if (businessCustomerId == null) {
			businessCustomer = new BusinessCustomer();
		} else {
			businessCustomer = businessCustomerService.getBusinessCustomer(businessCustomerId);
		}
		this.exceptionDoFilter.setBusinessCustomerId(businessCustomerId);
		this.setCustomerName(businessCustomer.getCustomerName());

	}

	public void clearBusinessCustomer() {
		this.exceptionDoFilter.setBusinessCustomerId(null);
		this.setCustomerName(null);
	}

	/**
	 * 解密扫描至输入框的DO号，若为已加密形式，则调用该方法解密之再显示在页面上
	 *
	 * @return
	 */
	public void decryptDoNo() {
		exceptionDoFilter.setDoNo(DoUtil.decryptDoNo(exceptionDoFilter.getDoNo()));
	}

	public ExceptionDoFilter getExceptionDoFilter() {
		return exceptionDoFilter;
	}

	public void setExceptionDoFilter(ExceptionDoFilter exceptionDoFilter) {
		this.exceptionDoFilter = exceptionDoFilter;
	}

	public Long getForcePickDoId() {
		return forcePickDoId;
	}

	public void setForcePickDoId(Long forcePickDoId) {
		this.forcePickDoId = forcePickDoId;
	}

	public Long getDoHeaderId() {
		return doHeaderId;
	}

	public void setDoHeaderId(Long doHeaderId) {
		this.doHeaderId = doHeaderId;
	}

	public String getAllAllocatedStatus() {
		return allAllocatedStatus;
	}

	public void setAllAllocatedStatus(String allAllocatedStatus) {
		this.allAllocatedStatus = allAllocatedStatus;
	}

	public String getPartPickedStatus() {
		return partPickedStatus;
	}

	public void setPartPickedStatus(String partPickedStatus) {
		this.partPickedStatus = partPickedStatus;
	}

	@BypassInterceptors
	public Map<Long, Boolean> getSelectMap() {
		return selectMap;
	}

	public void setSelectMap(Map<Long, Boolean> selectMap) {
		this.selectMap = selectMap;
	}

	public String getReleaseStatus() {
		return releaseStatus;
	}

	public void setReleaseStatus(String releaseStatus) {
		this.releaseStatus = releaseStatus;
	}

	public String getHoldStatus() {
		return holdStatus;
	}

	public void setHoldStatus(String holdStatus) {
		this.holdStatus = holdStatus;
	}

	public String getInitStatus() {
		return initStatus;
	}

	public void setInitStatus(String initStatus) {
		this.initStatus = initStatus;
	}

    public String getPartStatus() {
        return partStatus;
    }

    public void setPartStatus(String partStatus) {
        this.partStatus = partStatus;
    }

    public String getCloseStatus() {
		return closeStatus;
	}

	public void setCloseStatus(String closeStatus) {
		this.closeStatus = closeStatus;
	}

	public List<String> getRollbackChoice() {
		return rollbackChoice;
	}

	public void setRollbackChoice(List<String> rollbackChoice) {
		this.rollbackChoice = rollbackChoice;
	}

	public Long getNeedRollbackDoId() {
		return needRollbackDoId;
	}

	public void setNeedRollbackDoId(Long needRollbackDoId) {
		this.needRollbackDoId = needRollbackDoId;
	}

	public Long getNeedCancelDoId() {
		return needCancelDoId;
	}

	public void setNeedCancelDoId(Long needCancelDoId) {
		this.needCancelDoId = needCancelDoId;
	}

	public Long getNeedReleaseDoId() {
		return needReleaseDoId;
	}

	public void setNeedReleaseDoId(Long needReleaseDoId) {
		this.needReleaseDoId = needReleaseDoId;
	}

	public void setMailToDoId(Long mailToDoId) {
		this.mailToDoId = mailToDoId;
	}

	public Long getMailToDoId() {
		return mailToDoId;
	}

	public Long getNeedSkipCSDoId() {
		return needSkipCSDoId;
	}

	public void setNeedSkipCSDoId(Long needSkipCSDoId) {
		this.needSkipCSDoId = needSkipCSDoId;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
}
