package com.daxia.wms.doexception.dao;

import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.doexception.entity.FindLackDetail;


/**
 * 找货明细DAO
 */
@Name("com.daxia.wms.exception.findLackDetailDAO")
@lombok.extern.slf4j.Slf4j
public class FindLackDetailDAO extends HibernateBaseDAO<FindLackDetail,Long> {

    private static final long serialVersionUID = 2514299890993847035L;
    
    /**
     * 根据发货单查找找货明细
     * @param doIds
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<FindLackDetail> findByDoIds(List<Long> doIds) {
        String hql = "from FindLackDetail o where o.doHeaderId in (:doIds) and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql)
                .setParameterList("doIds", doIds)
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }
    
    /**
     * 根据发货单删除找货明细
     * @param doIds
     * @return
     */
    public int deleteByDoIds(List<Long> doIds) {
        String hql = "update FindLackDetail o set o.isDeleted = 1, o.updatedBy = :updatedBy where o.doHeaderId in (:doIds) and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql)
                .setParameter("updatedBy", ParamUtil.getCurrentLoginName())
                .setParameterList("doIds", doIds)
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.executeUpdate();
    }
    
    /**
     * 根据发货单查询其找货明细
     * @param doHeaderId
     * @return
     */
    public FindLackDetail getByDoId(Long doHeaderId) {
        String hql = "from FindLackDetail o  where o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId";
        Query query = this.createQuery(hql)
                .setLong("doHeaderId", doHeaderId)
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (FindLackDetail) query.uniqueResult();
    }
}
