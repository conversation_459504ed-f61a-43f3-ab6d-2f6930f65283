package com.daxia.wms.doexception.helper;

import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletResponse;

import com.daxia.framework.common.service.Dictionary;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.delivery.deliveryorder.dto.LackDoDto;

/**
 * wms-exception模块的报表工具类
 */
public final class ReportExcelGeneratorCSV {

    private ReportExcelGeneratorCSV() {
    };

    /**
     * 导出csv文件
     * 
     * @param fileName
     *            csv文件名
     * @param dtoString
     *            csv内容
     * @throws IOException
     */
    public static void exportCSV(String fileName, String dtoString) throws IOException {
        FacesContext ctx = FacesContext.getCurrentInstance();
        ctx.responseComplete();
        ExternalContext context = ctx.getExternalContext();
        HttpServletResponse response = (HttpServletResponse) context.getResponse();
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment; filename=\"" + fileName + "\"");
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            out.write(dtoString.getBytes("GBK"));
            out.flush();
            ctx.responseComplete();
        } catch (IOException e) {
            throw e;
        } finally {
            try {
                if (null != out) {
                    out.close();
                }
            } catch (IOException e) {
                throw e;
            }
        }
    }

    /**
     * csv文字处理(csv按照","分割来生成excel的列，故需替换掉文本中原有的逗号),并去掉文字中的空格和换行符
     * 
     * @param strBd
     *            文字处理
     * @param value
     *            处理内容
     */
    public static void addCsvChar(final StringBuilder strBd, final String value) {
        strBd.append(value == null ? "" : value.trim().replace(",", "、").replace("\\s", "").replace("\n", "")).append(
                "\t,");
    }

    public static String makeCsvForLackDo(List<LackDoDto> dtoList) {
        StringBuilder strBuilder = new StringBuilder();
        addCsvChar(strBuilder, "订单号");
        addCsvChar(strBuilder, "分拣柜号");
        addCsvChar(strBuilder, "订单暂存位号");
        addCsvChar(strBuilder, "订单状态");
        addCsvChar(strBuilder, "缺货状态");
        addCsvChar(strBuilder, "冻结时间");
        addCsvChar(strBuilder, "订单预计出库时间");
        addCsvChar(strBuilder, "异常原因");
        addCsvChar(strBuilder, "异常状态");
        addCsvChar(strBuilder, "是否取消");
        strBuilder.append("\r\n");

        Map<String, String> doStatusMap = Dictionary.getDictionary("DO_STATUS");
        Map<String, String> expStatusMap = Dictionary.getDictionary("EXCEPTION_STATUS");
        Map<String, String> holdReasonMap = Dictionary.getDictionary("REASON_HDD");
        java.text.SimpleDateFormat sf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (LackDoDto dto : dtoList) {
            addCsvChar(strBuilder, dto.getDoNo());
            addCsvChar(strBuilder, dto.getSortBinNo());
            addCsvChar(strBuilder, dto.getLackLocCode());
            addCsvChar(strBuilder, doStatusMap.get(dto.getStatus()));
            addCsvChar(strBuilder, YesNo.YES.getValue().equals(dto.getLackStatus()) ? "是" : "否");
            addCsvChar(strBuilder, dto.getHoldTime() == null ? null : sf.format(dto.getHoldTime()));
            addCsvChar(strBuilder, dto.getDoFinishTime() == null ? null : sf.format(dto.getDoFinishTime()));
            addCsvChar(strBuilder, holdReasonMap.get(dto.getHoldCode()));
            addCsvChar(strBuilder, dto.getExceptionStatus() == null ? null : expStatusMap.get(dto.getExceptionStatus()));
            addCsvChar(strBuilder, Boolean.TRUE.equals(dto.getNeedCancel()) ? "是" : "否");
            strBuilder.append("\r\n");
        }
        return strBuilder.toString();
    }

}
