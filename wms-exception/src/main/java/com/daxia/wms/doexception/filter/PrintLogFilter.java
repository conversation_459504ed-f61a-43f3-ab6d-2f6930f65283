package com.daxia.wms.doexception.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;
import com.daxia.wms.print.PrintConstants.PrintType;

import java.util.Date;

/**
 * 打印日志查询filter
 */
@lombok.extern.slf4j.Slf4j
public class PrintLogFilter extends WhBaseQueryFilter {

    private static final long serialVersionUID = -828544257763318803L;
    
    private String docNo;
    
    private String refNo;
    
    private String outRefNo;
    
    private PrintType printType;

	private String createdBy;

	private Date createTimeFrom;

	private Date createTimeTo;

	public String getDocNo() {
		return docNo;
	}

	public void setDocNo(String docNo) {
		this.docNo = docNo;
	}

	public String getRefNo() {
		return refNo;
	}

	public void setRefNo(String refNo) {
		this.refNo = refNo;
	}

	public String getOutRefNo() {
		return outRefNo;
	}

	public void setOutRefNo(String outRefNo) {
		this.outRefNo = outRefNo;
	}

	public PrintType getPrintType() {
		return printType;
	}

	public void setPrintType(PrintType printType) {
		this.printType = printType;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	@Operation(fieldName = "o.createdAt", operationType = OperationType.NOT_GREAT_THAN, dataType =
			"datetime")
	public Date getCreateTimeTo() {
		return createTimeTo;
	}

	public void setCreateTimeTo(Date createTimeTo) {
		this.createTimeTo = createTimeTo;
	}

	@Operation(fieldName = "o.createdAt", operationType = OperationType.NOT_LESS_THAN, dataType =
			"datetime")
	public Date getCreateTimeFrom() {
		return createTimeFrom;
	}

	public void setCreateTimeFrom(Date createTimeFrom) {
		this.createTimeFrom = createTimeFrom;
	}
}
