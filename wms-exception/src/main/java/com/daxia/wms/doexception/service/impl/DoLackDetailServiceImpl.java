package com.daxia.wms.doexception.service.impl;

import java.math.BigDecimal;
import java.util.List;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.doexception.filter.DoLackDetailFilter;
import com.daxia.wms.doexception.service.DoLackDetailService;
import com.daxia.wms.delivery.deliveryorder.dao.DoLackDetailDAO;
import com.daxia.wms.delivery.deliveryorder.entity.DoLackDetail;

@Name("com.daxia.wms.delivery.doLackDetailService")
@lombok.extern.slf4j.Slf4j
public class DoLackDetailServiceImpl implements DoLackDetailService {
    @In
    private DoLackDetailDAO doLackDetailDAO;
    
    /**
     * 根据缺货明细查询某商品总共缺多少
     */
    @Override
    public BigDecimal getSkuLackQty(Long skuId) {
        return doLackDetailDAO.getSkuLackQty(skuId);
    }

    /**
     * 查询订单缺货明细（分页）
     */
    @Override
    public DataPage<DoLackDetail> findDoLackDetail(DoLackDetailFilter filter, int startIndex, int pageSize) {
        return doLackDetailDAO.findRangeByFilter(filter, startIndex, pageSize);
    }

    /**
     * 查找缺货订单总数
     * @param skuId
     * @return
     */
    @Override
    public Long findTotalLack(Long skuId) {
        BigDecimal count = doLackDetailDAO.findTotalLack(skuId);
        if (null == count) {
            count = BigDecimal.ZERO;
        }
        return count.longValue();
    }

	@Override
	public List<DoLackDetail> findDoLackDetail(Long doHeaderId) {
		return doLackDetailDAO.findDoLackDetail(doHeaderId);
	}

    @Override
    public BigDecimal findDisTSkuByDoNos(List<Long> doIds) {
        return doLackDetailDAO.findDisTSkuByDoNos(doIds);
    }

    @Override
    public BigDecimal findTotalCountByDos(List<Long> doIds) {
        return doLackDetailDAO.findTotalCountByDos(doIds);
    }

    @Override
    public BigDecimal findDoLackQty(Long doHeaderId) {
        return doLackDetailDAO.getDoLackQty(doHeaderId);
    }
}
