package com.daxia.wms.doexception.entity;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.master.entity.Location;
import com.daxia.wms.master.entity.Sku;

/**
 * 找货任务实体
 */
@Entity
@Table(name = "tsk_find_lack")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update tsk_find_lack set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class FindLackTask extends WhBaseEntity{
	
	private static final long serialVersionUID = 4181142205423450106L;
	
	private Long id;
	private Long findLackHId;
	private Long findLackDId;
	private Long doHeaderId;
	private Long skuId;
	private Long locId;
	private BigDecimal lackQty;
	
	private FindLackHeader findLackHeader;
	private DeliveryOrderHeader doHeader;
	private Location location;
	private Sku sku;
	
	private Integer isDeleted;
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.AUTO)  
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	@Column(name = "FIND_LACK_H_ID")
	public Long getFindLackHId() {
		return findLackHId;
	}
	public void setFindLackHId(Long findLackHId) {
		this.findLackHId = findLackHId;
	}
	
	@Column(name = "FIND_LACK_D_ID")
	public Long getFindLackDId() {
		return findLackDId;
	}
	public void setFindLackDId(Long findLackDId) {
		this.findLackDId = findLackDId;
	}
	
	@Column(name = "DO_HEADER_ID")
	public Long getDoHeaderId() {
		return doHeaderId;
	}
	public void setDoHeaderId(Long doHeaderId) {
		this.doHeaderId = doHeaderId;
	}
	
	@Column(name = "SKU_ID")
	public Long getSkuId() {
		return skuId;
	}
	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}
	
	@Column(name = "LOC_ID")
	public Long getLocId() {
		return locId;
	}
	public void setLocId(Long locId) {
		this.locId = locId;
	}
	
	@Column(name = "LACK_QTY")
	public BigDecimal getLackQty() {
		return lackQty;
	}
	public void setLackQty(BigDecimal lackQty) {
		this.lackQty = lackQty;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "FIND_LACK_H_ID", insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
	public FindLackHeader getFindLackHeader() {
		return findLackHeader;
	}
	public void setFindLackHeader(FindLackHeader findLackHeader) {
		this.findLackHeader = findLackHeader;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DO_HEADER_ID", insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
	public DeliveryOrderHeader getDoHeader() {
		return doHeader;
	}
	public void setDoHeader(DeliveryOrderHeader doHeader) {
		this.doHeader = doHeader;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "LOC_ID", insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
	public Location getLocation() {
		return location;
	}
	public void setLocation(Location location) {
		this.location = location;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SKU_ID", insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
	public Sku getSku() {
		return sku;
	}
	
	public void setSku(Sku sku) {
		this.sku = sku;
	}
	
	@Column(name = "IS_DELETED")
	public Integer getIsDeleted() {
		return isDeleted;
	}
	
	public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}
}