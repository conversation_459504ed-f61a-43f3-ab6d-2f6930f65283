package com.daxia.wms.doexception.filter;

import com.daxia.framework.common.annotation.Operation;
import com.daxia.framework.common.annotation.OperationType;
import com.daxia.framework.common.filter.WhBaseQueryFilter;

@lombok.extern.slf4j.Slf4j
public class DoLackDetailFilter extends WhBaseQueryFilter {

    private static final long serialVersionUID = -3708384585044622913L;

    private Long skuId;
    
    private String holdCode;
    
    private String releaseStatus;
    
    private String exceptionStatus;

    @Operation(fieldName = "skuId", operationType = OperationType.EQUAL)
    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }
    
    @Operation(fieldName = "o.doHeader.holdCode", operationType = OperationType.EQUAL)
    public String getHoldCode() {
        return holdCode;
    }
    
    public void setHoldCode(String holdCode) {
        this.holdCode = holdCode;
    }
    
    @Operation(fieldName = "o.doHeader.releaseStatus", operationType = OperationType.EQUAL)
    public String getReleaseStatus() {
        return releaseStatus;
    }
    
    public void setReleaseStatus(String status) {
        this.releaseStatus = status;
    }
    
    @Operation(fieldName = "o.doHeader.exceptionStatus", operationType = OperationType.EQUAL)
    public String getExceptionStatus() {
        return exceptionStatus;
    }
    
    public void setExceptionStatus(String exceptionStatus) {
        this.exceptionStatus = exceptionStatus;
    }

}
