package com.daxia.wms.doexception.service;

import java.util.List;

import com.daxia.wms.doexception.entity.FindLackDetail;


/**
 * 找货明细service
 */
public interface FindLackDetailService {
    
    /**
     * 查询DO是否缺货找货任务
     * @param doIds
     * @return
     */
    public boolean isDoHasFindLack(List<Long> doIds);
    
    /**
     * 保存找货明细
     * @param findLackDetail
     */
    public void saveOrUpdate(FindLackDetail findLackDetail);
    
    /**
     * 根据发货单号删除
     * @param doHeaderId
     */
    public void deleteByDoIds(List<Long> doIds);
    
    public void deleteByDoId(Long doId);
}
