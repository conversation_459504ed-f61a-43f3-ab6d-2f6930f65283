package com.daxia.wms.doexception.dao;

import java.util.List;

import org.hibernate.Query;
import org.jboss.seam.annotations.Name;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.doexception.entity.FindLackTask;

@Name("com.daxia.wms.exception.findLackTaskDAO")
@lombok.extern.slf4j.Slf4j
public class FindLackTaskDAO extends HibernateBaseDAO<FindLackTask, Long> {

    private static final long serialVersionUID = 180252104206773982L;
    
    /**
     * 根据发货单删除
     * @param doId
     */
    public void deleteByDoId(Long doId) {
        String hql = "update FindLackTask o set o.isDeleted = 1 where o.doHeaderId = :doHeaderId and o.warehouseId = :warehouseId ";
        Query query = this.createUpdateQuery(hql)
                .setParameter("doHeaderId", doId)
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }

    /**
     * 根据发货单删除
     * @param doId
     */
    public void deleteByDoIds(List<Long> doIds) {
        String hql = "update FindLackTask o set o.isDeleted = 1 where o.doHeaderId in (:doIds) and o.warehouseId = :warehouseId ";
        Query query = this.createUpdateQuery(hql)
                .setParameterList("doIds", doIds)
                .setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.executeUpdate();
    }
}
