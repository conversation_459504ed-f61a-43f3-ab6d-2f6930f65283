package com.daxia.wms.doexception.service.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Observer;
import org.jboss.seam.annotations.Transactional;

import com.daxia.framework.common.util.DataPage;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.framework.common.util.StringUtil;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoLackDetail;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.DoLackHeaderService;
import com.daxia.wms.delivery.pick.service.PickService;
import com.daxia.wms.doexception.dao.FindLackHeaderDAO;
import com.daxia.wms.doexception.dto.FindLackDto;
import com.daxia.wms.doexception.entity.FindLackDetail;
import com.daxia.wms.doexception.entity.FindLackHeader;
import com.daxia.wms.doexception.entity.FindLackTask;
import com.daxia.wms.doexception.filter.FindLackFilter;
import com.daxia.wms.doexception.service.DoLackDetailService;
import com.daxia.wms.doexception.service.FindLackDetailService;
import com.daxia.wms.doexception.service.FindLackHeaderService;
import com.daxia.wms.doexception.service.FindLackTaskService;
import com.daxia.wms.master.service.LocationService;
import com.daxia.wms.master.service.WarehouseService;

/**
 * 找货单头service实现类
 */
@Name("com.daxia.wms.exception.findLackHeaderService")
@lombok.extern.slf4j.Slf4j
public class FindLackHeaderServiceImpl implements FindLackHeaderService{

	@In
	FindLackHeaderDAO findLackHeaderDAO;
	@In
	FindLackDetailService findLackDetailService;
	@In
	DeliveryOrderService deliveryOrderService;
	@In
	DoLackDetailService doLackDetailService;
	@In
	PickService pickService;
	@In
	LocationService locationService;
	@In
	SequenceGeneratorService sequenceGeneratorService;
	@In
	FindLackTaskService findLackTaskService;
	@In
	WarehouseService warehouseService;
	@In
	DoLackHeaderService doLackHeaderService;
    
	
	@Override
	public FindLackHeader findHeaderByDocNo(String docNo) {
		return findLackHeaderDAO.findHeaderByDocNo(docNo);
	}

    @Override
    public DataPage<FindLackDto> findPageByFilter(FindLackFilter filter, int startIndex, int pageSize) {
        List<FindLackDto>data = new ArrayList<FindLackDto>();
        int totalCount = findLackHeaderDAO.getFindLackCount(filter);
        if (0 == totalCount) {
            return new DataPage<FindLackDto>(0, startIndex, pageSize, data);
        }
        List<Object> fdObjs = findLackHeaderDAO.queryFldInfoPageByFilter(filter, startIndex, pageSize);
        for (Object obj : fdObjs) {
            Object [] objArr = (Object []) obj;
            FindLackDto dto = new FindLackDto();
            dto.setDoId(((BigInteger) objArr[0]).longValue());
            dto.setDoNo((String) objArr[1]);
            dto.setLackHstatus((String) objArr[2]);
            dto.setFindLackHNo((String) objArr[3]);
            dto.setHoldReason((String) objArr[4]);
            dto.setPlanShipTime((String) objArr[5]);
            dto.setDoStatus((String) objArr[6]);
            dto.setHoldTime((Date) objArr[7]);
            dto.setHoldWho((String) objArr[8]);
            dto.setLackCreateTime((Date) objArr[9]);
            dto.setLackCreater((String) objArr[10]);
            dto.setPringStatus((String) objArr[11]);
            dto.setLackPartition((String) objArr[12]);
            dto.setLackQty((BigDecimal) objArr[13]);
            dto.setLackQty((BigDecimal) objArr[13]);
            dto.setFirstHoldCode((String) objArr[14]);
            data.add(dto);
        }
        return new DataPage<FindLackDto>(totalCount, startIndex, pageSize, data);
    }

    @Override
    @Transactional
    public void createLackTask(List<Long> doIds) {
        if (ListUtil.isNullOrEmpty(doIds)) {
            throw new DeliveryException(DeliveryException.NO_DO_SELECT4LACK);
        }
        if (findLackDetailService.isDoHasFindLack(doIds)) {
            throw new DeliveryException(DeliveryException.DO_HAS_FINDLACK);
        }
        
        String findLackNo = sequenceGeneratorService.generateSequenceNo(
               Constants.SequenceName.FIND_LACK_HEADER.getValue(), ParamUtil.getCurrentWarehouseId());
        FindLackHeader findLackHeader = new FindLackHeader();
        findLackHeader.setFindLackHeaderNo(findLackNo);
        findLackHeader.setPrintStatus(Constants.FindLackPrintStatus.NO_PRINT.getValue());
        findLackHeader.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        findLackHeaderDAO.save(findLackHeader);
        
        List<DeliveryOrderHeader> doHeaders = deliveryOrderService.findDoByIds(doIds);
        if (ListUtil.isNullOrEmpty(doHeaders)) {
            throw new DeliveryException(DeliveryException.NO_DO_SELECT4LACK);
        }
        
        for (DeliveryOrderHeader doHeader : doHeaders) {
            checkDo4CreateLack(doHeader);
            FindLackDetail fd = new FindLackDetail();
            fd.setFindLackHId(findLackHeader.getId());
            fd.setDoHeaderId(doHeader.getId());
            BigDecimal lackQty = doLackDetailService.findDoLackQty(doHeader.getId());
            fd.setLackQty(lackQty);
            fd.setWarehouseId(ParamUtil.getCurrentWarehouseId());
            findLackDetailService.saveOrUpdate(fd);
            
            List<DoLackDetail> lackDetails = doLackDetailService.findDoLackDetail(doHeader.getId());
            if (ListUtil.isNullOrEmpty(lackDetails)) {
                throw new DeliveryException(DeliveryException.NO_LACK_DETAIL);
            }
            for (DoLackDetail ld : lackDetails) {
                FindLackTask fdt = createLackTask(ld, findLackHeader.getId(), fd.getId());
                findLackTaskService.save(fdt);
            }
        }
        doLackHeaderService.updateCreateStatus(doIds, Constants.FindLackCreateStatus.CREATED.getValue());
    }
    
    /**
     * 验证发货单是否可生成找货单
     * @param doHeader
     */
    private void checkDo4CreateLack(DeliveryOrderHeader doHeader) {
        if (!Constants.DoExpStatus.TO_BE_ROLLBACK.getValue().equals(doHeader.getExceptionStatus())) {
            throw new DeliveryException(DeliveryException.DO_EXPSTATUS_IS_NOT_TOROLL, doHeader.getDoNo());
        }
        
        if (StringUtil.isNotIn(doHeader.getHoldCode(), 
                Constants.Reason.PICK_DAMAGE.getValue(), Constants.Reason.PICK_LACK.getValue(),
                Constants.Reason.SORT_DM.getValue(), Constants.Reason.SORT_LACK.getValue(),
                Constants.Reason.CHECK_DM.getValue(), Constants.Reason.RECHECK_LACK.getValue())) {
            throw new DeliveryException(DeliveryException.DO_STATUS_CAN_NOT_CREAT_LACK, doHeader.getDoNo());
        }
        
        if (!Constants.ReleaseStatus.HOLD.getValue().equals(doHeader.getReleaseStatus())) {
            throw new DeliveryException(DeliveryException.DO_IS_NOT_HOLD);
        }
      
        if (StringUtil.isNotIn(doHeader.getStatus(), 
                Constants.DoStatus.ALLPICKED.getValue(), Constants.DoStatus.PARTSORTED.getValue(),
                Constants.DoStatus.ALLSORTED.getValue(), Constants.DoStatus.PART_CARTON.getValue(),
                Constants.DoStatus.ALL_CARTON.getValue())) {
            throw new DeliveryException(DeliveryException.DO_STATUS_LE_PICKED, doHeader.getDoNo());
        }
    }
    
    private FindLackTask createLackTask(DoLackDetail ld, Long findLackHeaderId, Long findLackDId) {
        FindLackTask fdt = new FindLackTask();
        fdt.setDoHeaderId(ld.getDoHeaderId());
        fdt.setFindLackDId(findLackDId);
        fdt.setFindLackHId(findLackHeaderId);
        fdt.setLackQty(ld.getQty());
        fdt.setLocId(locationService.getLocationIdByLocCode(ld.getLocCodes()));
        fdt.setSkuId(ld.getSkuId());
        fdt.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        return fdt;
    }

    @Override
    public List<FindLackHeader> getByDoIds(List<Long> doIds) {
        return findLackHeaderDAO.getByDoIds(doIds);
    }

    @Override
    @Transactional
    public void deleteById(Long findLackHeaderId) {
        findLackHeaderDAO.deleteFindLackHeaderById(findLackHeaderId);
    }

    @Override
    @Transactional
    @Observer("DELETE_FIND_LACK_EVENT")
    public void clearFindLackInfo(Long doHeaderId) {
        findLackDetailService.deleteByDoId(doHeaderId);
        findLackTaskService.deleteByDoId(doHeaderId);
        doLackHeaderService.updateCreateStatusByDoId(doHeaderId, Constants.FindLackCreateStatus.COMPLETED.getValue());
    }

    @Override
    @Transactional
    public void clearFindLackInfoByDoIds(List<Long> doIds) {
        findLackDetailService.deleteByDoIds(doIds);
        findLackTaskService.deleteByDoIds(doIds);
        doLackHeaderService.updateCreateStatus(doIds, Constants.FindLackCreateStatus.COMPLETED.getValue());
    }

	@Override
	@Transactional
	public void setPrintStatusAsPrinted(String findLackHNo) {
		FindLackHeader header = findLackHeaderDAO.findHeaderByDocNo(findLackHNo);
		header.setPrintStatus(Constants.PrintStatus.YES.getValue().toString());
		findLackHeaderDAO.saveOrUpdate(header);
	}

	@Override
	public Date getEarliestPlanShipTimeByHeaderNo(String headerNo) {
		return findLackHeaderDAO.getEarliestPlanShipTimeByHeaderNo(headerNo);
	}

	@Override
	public List<FindLackHeader> getFindLackHeaderList() {
		return findLackHeaderDAO.getFindLackHeaderList();
	}
}
