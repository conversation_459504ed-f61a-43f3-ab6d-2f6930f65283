package com.daxia.wms.doexception.action;

import java.math.BigDecimal;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.doexception.filter.DoLackDetailFilter;
import com.daxia.wms.doexception.service.DoLackDetailService;
import com.daxia.wms.delivery.deliveryorder.entity.DoLackDetail;
import com.daxia.wms.delivery.deliveryorder.service.DoExceptionLogService;
import com.daxia.wms.master.dto.SkuDTO;
import com.daxia.wms.master.service.SkuCache;

@Name("com.daxia.wms.exception.lackDoAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class LackDoAction extends PagedListBean<DoLackDetail> {

    private static final long serialVersionUID = 8191165977704164507L;

    private Long skuId;
    private SkuDTO sku;
    private BigDecimal skuLackQty = BigDecimal.ZERO;

    @In
    DoLackDetailService doLackDetailService;

    @In
    DoExceptionLogService doExceptionLogService;

    @In
    SkuCache skuCache;

    public void init() {
        this.buttonQuery();
    }

    @Override
    public void query() {
        sku = skuCache.getSku(skuId);

        DoLackDetailFilter filter = new DoLackDetailFilter();
        filter.setSkuId(skuId);
        DataPage<DoLackDetail> dataPage = doLackDetailService.findDoLackDetail(filter, getStartIndex(), getPageSize());
        this.populateValues(dataPage);

        skuLackQty = doLackDetailService.getSkuLackQty(skuId);
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public SkuDTO getSku() {
        return sku;
    }

    public void setSku(SkuDTO sku) {
        this.sku = sku;
    }

    public BigDecimal getSkuLackQty() {
        return skuLackQty;
    }

    public void setSkuLackQty(BigDecimal skuLackQty) {
        this.skuLackQty = skuLackQty;
    }
}