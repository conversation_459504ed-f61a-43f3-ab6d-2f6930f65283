package com.daxia.wms.doexception.action;

import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.doexception.filter.PrintLogFilter;
import com.daxia.wms.print.PrintConstants.PrintType;
import com.daxia.wms.print.entity.PrintLog;
import com.daxia.wms.print.service.PrintLogService;

/**
 * 打印日志action
 */
@Name("com.daxia.wms.exception.printLogAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class PrintLogAction extends PagedListBean<PrintLog> {

    private static final long serialVersionUID = 8060581851892987313L;

    @In
    private PrintLogService printLogService;
    
    private PrintLogFilter printLogFilter;
    
    public PrintLogAction() {
    	super();
    	if (null == printLogFilter) {
    		printLogFilter = new PrintLogFilter();
    	}
    }

    @Override
    public void query() {
    	printLogFilter.getOrderByMap().put("createdAt", "desc");
        DataPage<PrintLog> dataPage = printLogService.queryByFilter(printLogFilter, this.getStartIndex(), this.getPageSize());
        this.populateValues(dataPage);
    }
    
    public List<SelectItem> getPrintTypes() {
		List<SelectItem> items = new ArrayList<SelectItem>();
		items.add(new SelectItem(PrintType.CARTON_LABEL.name(), PrintType.CARTON_LABEL.getDisplayName()));
		//items.add(new SelectItem(PrintType.INVOICE.name(), PrintType.INVOICE.getDisplayName()));
		items.add(new SelectItem(PrintType.DO.name(), PrintType.DO.getDisplayName()));
		return items;
	}

	public PrintLogFilter getPrintLogFilter() {
		return printLogFilter;
	}

	public void setPrintLogFilter(PrintLogFilter printLogFilter) {
		this.printLogFilter = printLogFilter;
	}
}
