package com.daxia.wms.doexception.service.impl;

import java.util.List;
import java.util.Map;

import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import com.google.common.collect.Maps;
import com.daxia.framework.common.service.ReportGenerator;
import com.daxia.wms.doexception.dto.LackLabelPrintDTO;
import com.daxia.wms.doexception.service.LackLabelPrintService;

@Name("com.daxia.wms.delivery.lackLabelPrintService")
@lombok.extern.slf4j.Slf4j
public class LackLabelPrintServiceImpl implements LackLabelPrintService {
	
	private static final String REPORT_NAME = "lackLabel";
	
	@In
    private ReportGenerator reportGenerator;
	
	@Override
    public List<String> print(List<LackLabelPrintDTO> dtos) {
		Map<String, Object> params = Maps.newHashMap();
		return reportGenerator.builtPrintDataNoSub(REPORT_NAME, params, dtos);
	}
}
