package com.daxia.wms.doexception.service.impl;

import com.daxia.framework.common.service.Dictionary;
import com.daxia.framework.common.util.*;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.*;
import com.daxia.wms.OrderLogConstants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.delivery.deliveryorder.dto.DoExStatusOpDto;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderDetail;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.entity.DoExceptionLog;
import com.daxia.wms.delivery.deliveryorder.filter.BackExceptionFilter;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateService;
import com.daxia.wms.delivery.deliveryorder.service.DoExceptionLogService;
import com.daxia.wms.delivery.deliveryorder.service.impl.DoStatusRollbackListener;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.service.PickService;
import com.daxia.wms.delivery.recheck.dao.TempCartonDAO;
import com.daxia.wms.delivery.recheck.service.CartonService;
import com.daxia.wms.delivery.recheck.service.ClearReCheckRecordService;
import com.daxia.wms.delivery.recheck.service.ReCheckService;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.delivery.recheck.dto.TempCartonExceptionDTO;
import com.daxia.wms.doexception.filter.ExceptionDoFilter;
import com.daxia.wms.doexception.service.DoExceptionService;
import com.daxia.wms.doexception.service.FindLackHeaderService;
import com.daxia.wms.exp.dto.DoExpDto;
import com.daxia.wms.exp.service.DoExpService;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.master.entity.Container;
import com.daxia.wms.delivery.deliveryorder.service.OrderLogService;
import com.daxia.wms.master.service.MergeLocService;
import com.daxia.wms.master.service.ProductBarcodeService;
import com.daxia.wms.master.service.SkuService;
import com.daxia.wms.serial.service.SpvsnCodeService;
import org.hibernate.FetchMode;
import org.hibernate.criterion.*;
import org.jboss.seam.Component;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Transactional;
import org.jboss.seam.security.Identity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@lombok.extern.slf4j.Slf4j
public class DoExceptionServiceImpl implements DoExceptionService {
	@In
	private DeliveryOrderService deliveryOrderService;

    @In
    DoExpService doExpService;
    
    @In
    DoExceptionLogService doExceptionLogService;
    
    @In
    ProductBarcodeService productBarcodeService;
    
    @In
    private OrderLogService orderLogService;
    
    @In
    private WaveService waveService;
    
    @In
    private FindLackHeaderService findLackHeaderService;
    
    @In
    private PickService pickService;
    
    @In
    private CartonService cartonService;

    @In
    private ClearReCheckRecordService clearReCheckRecordService;

    @In
    private ExpFacadeService expFacadeService; 
    
    @In
    private SkuService skuService;
    @In
    private DoAllocateService doAllocateService;
	@In
	private TempCartonDAO tempCartonDAO;
	@In
	ReCheckService reCheckService;
	@In
	ContainerMgntService containerMgntService;

    @In
    private MergeLocService mergeLocService;

    @In
	private SpvsnCodeService spvsnCodeService;

	/**
	 * 定单可以回滚到的状态列表
	 * 
	 * @see com.daxia.wms.Constants.DoStatus
	 */
	private static final List<String> rollbackChoiceTemplate;
	static {
		rollbackChoiceTemplate = new ArrayList<String>();
		rollbackChoiceTemplate.add(Constants.DoStatus.ALLSORTED.getValue());// 65
																			// 分拣完成
		rollbackChoiceTemplate.add(Constants.DoStatus.ALLPICKED.getValue());// 60
																			// 拣货完成
		rollbackChoiceTemplate.add(Constants.DoStatus.INITIAL.getValue());// 00
																			// 初始化
	}

	/**
	 * 定单回退事件监听器列表，key为监听的事件，value为监听者在seam中注册的名称<br/>
	 * 该Map在components.xml文件中配置其Value<br/>
	 * key的值有：
	 * <ul>
	 * <li>CARTON2SORTED 装箱复核完成（部分装箱复核） ~ 分拣完成
	 * <li>SORTED2PICKED 分拣完成（部分分拣） ~ 拣货完成
	 * <li>PICKED2INITIAL 拣货完成（部分拣货） ~ 初始化
	 * <li>ALLOCATED2INITIAL 分配完成（部分分配） ~ 初始化
	 * </ul>
	 * 
	 */
	private Map<String, String> rollbackListeners = new HashMap<String, String>();

	/**
	 * 所有可以执行的回退步骤
	 */
	private static final List<DoStatusRollbackStep> availRollbackSteps;
	static {
		availRollbackSteps = new ArrayList<DoStatusRollbackStep>();
		// 装箱复核完成（部分装箱复核） ~ 分拣完成
		availRollbackSteps.add(new DoStatusRollbackStep(new String[]{Constants.DoStatus.ALL_CARTON.getValue(), Constants.DoStatus.PART_CARTON.getValue()}, Constants.DoStatus.ALLSORTED.getValue(), "CARTON2SORTED"));

		// 分拣完成（部分分拣） ~ 拣货完成
		availRollbackSteps.add(new DoStatusRollbackStep(new String[]{Constants.DoStatus.ALLSORTED.getValue(), Constants.DoStatus.PARTSORTED.getValue()}, Constants.DoStatus.ALLPICKED.getValue(), "SORTED2PICKED"));

		// 拣货完成（部分拣货） ~ 初始化
		availRollbackSteps.add(new DoStatusRollbackStep(new String[]{Constants.DoStatus.ALLPICKED.getValue(), Constants.DoStatus.PARTPICKED.getValue()}, Constants.DoStatus.INITIAL.getValue(), "PICKED2INITIAL"));

		// 分配完成（部分分配） ~ 初始化
		availRollbackSteps.add(new DoStatusRollbackStep(new String[]{Constants.DoStatus.ALLALLOCATED.getValue(), Constants.DoStatus.PARTALLOCATED.getValue()}, Constants.DoStatus.INITIAL.getValue(), "ALLOCATED2INITIAL"));
	}

	public DoExceptionServiceImpl() {
	}

	public void setRollbackListeners(Map<String, String> rollbackListeners) {
		this.rollbackListeners = rollbackListeners;
	}

	@Override
	public DataPage<DeliveryOrderHeader> queryFrozenDo(ExceptionDoFilter exceptionDoFilter, int startIndex, int pageSize) {

	    Long whId = ParamUtil.getCurrentWarehouseId();
		DetachedCriteria queryCri = DetachedCriteria.forClass(DeliveryOrderHeader.class,"doHeader_Query");
		DetachedCriteria countCri = DetachedCriteria.forClass(DeliveryOrderHeader.class,"doHeader_Count");
		
		if (!StringUtil.isEmpty(exceptionDoFilter.getDoNo())) {
			queryCri.add(Restrictions.eq("doNo", exceptionDoFilter.getDoNo()));
			countCri.add(Restrictions.eq("doNo", exceptionDoFilter.getDoNo()));
		}
		if(StringUtil.isNotEmpty(exceptionDoFilter.getOriginalSoCode())){
			queryCri.add(Restrictions.eq("originalSoCode", exceptionDoFilter.getOriginalSoCode()));
			countCri.add(Restrictions.eq("originalSoCode", exceptionDoFilter.getOriginalSoCode()));
		}
		if (exceptionDoFilter.getFrozenTimeFrom() != null) {
			queryCri.add(Restrictions.ge("holdTime", DateUtil.convertDtToTs(exceptionDoFilter.getFrozenTimeFrom())));
			countCri.add(Restrictions.ge("holdTime", DateUtil.convertDtToTs(exceptionDoFilter.getFrozenTimeFrom())));
		}
		if (exceptionDoFilter.getFrozenTimeTo() != null) {
			queryCri.add(Restrictions.le("holdTime", DateUtil.convertDtToTs(exceptionDoFilter.getFrozenTimeTo())));
			countCri.add(Restrictions.le("holdTime", DateUtil.convertDtToTs(exceptionDoFilter.getFrozenTimeTo())));
		}
		
		queryCri.add(Restrictions.isNotNull("holdTime"));
		countCri.add(Restrictions.isNotNull("holdTime"));

		if (!StringUtil.isEmpty(exceptionDoFilter.getDoType())) { 
			queryCri.add(Restrictions.eq("doType", exceptionDoFilter.getDoType()));
			countCri.add(Restrictions.eq("doType", exceptionDoFilter.getDoType()));
		}
		if (StringUtil.isNotEmpty(exceptionDoFilter.getHoldReason())) {
			queryCri.add(Restrictions.eq("holdCode", exceptionDoFilter.getHoldReason()));
			countCri.add(Restrictions.eq("holdCode", exceptionDoFilter.getHoldReason()));
		}
		boolean fromExpStatusEmpty = StringUtil.isEmpty(exceptionDoFilter.getFromExpStatus());
		boolean toExpStatusEmpty = StringUtil.isEmpty(exceptionDoFilter.getToExpStatus());
		if(!fromExpStatusEmpty || !toExpStatusEmpty){
			if(!fromExpStatusEmpty){
				queryCri.add(Restrictions.ge("exceptionStatus", exceptionDoFilter.getFromExpStatus()));
				countCri.add(Restrictions.ge("exceptionStatus", exceptionDoFilter.getFromExpStatus()));
			}
			if(!toExpStatusEmpty){
				queryCri.add(Restrictions.le("exceptionStatus", exceptionDoFilter.getToExpStatus()));
				countCri.add(Restrictions.le("exceptionStatus", exceptionDoFilter.getToExpStatus()));
			}
			if(toExpStatusEmpty || (!toExpStatusEmpty && !Constants.DoExpStatus.COMPLETE.getValue().equals(exceptionDoFilter.getToExpStatus()))){
				queryCri.add(Restrictions.ne("exceptionStatus", Constants.DoExpStatus.COMPLETE.getValue()));
				countCri.add(Restrictions.ne("exceptionStatus", Constants.DoExpStatus.COMPLETE.getValue()));
			}
		}else if(fromExpStatusEmpty && toExpStatusEmpty){
			queryCri.add(Restrictions.ne("exceptionStatus", Constants.DoExpStatus.COMPLETE.getValue()));
			countCri.add(Restrictions.ne("exceptionStatus", Constants.DoExpStatus.COMPLETE.getValue()));
		}
		
		if (null != exceptionDoFilter.getCreatedFrom()) {
			queryCri.add(Restrictions.ge("createdAt", exceptionDoFilter.getCreatedFrom()));
			countCri.add(Restrictions.ge("createdAt", exceptionDoFilter.getCreatedFrom()));
		}
		if (null != exceptionDoFilter.getCreatedTo()) {
			queryCri.add(Restrictions.le("createdAt", exceptionDoFilter.getCreatedTo()));
			countCri.add(Restrictions.le("createdAt", exceptionDoFilter.getCreatedTo()));
		}
		if (StringUtil.isNotEmpty(exceptionDoFilter.getCsReplyFlag())) {
			List<String> expTypes = new ArrayList<String>();
			expTypes.add(Constants.ExOpType.CS_RL.getValue());
			expTypes.add(Constants.ExOpType.CS_WAIT.getValue());
			expTypes.add(Constants.ExOpType.CS_DELETE.getValue());
			
			DetachedCriteria subQueryCri = DetachedCriteria.forClass(DoExceptionLog.class, "Exp");
			subQueryCri.add(Property.forName("doHeader_Query.id").eqProperty("Exp.doHeaderId"));
			subQueryCri.add(Restrictions.in("Exp.operationType", expTypes));
			subQueryCri.add(Restrictions.eq("warehouseId", whId));

			DetachedCriteria subCountCri = DetachedCriteria.forClass(DoExceptionLog.class, "Exp");
			subCountCri.add(Property.forName("doHeader_Count.id").eqProperty("Exp.doHeaderId"));
			subCountCri.add(Restrictions.in("Exp.operationType", expTypes));
			subCountCri.add(Restrictions.eq("warehouseId", whId));

			if (exceptionDoFilter.getCsReplyFlag().equals("1")) {
				queryCri.add(Subqueries.exists(subQueryCri.setProjection(Projections.property("Exp.id"))));
				countCri.add(Subqueries.exists(subCountCri.setProjection(Projections.property("Exp.id"))));
			} else if (exceptionDoFilter.getCsReplyFlag().equals("0")) {
				queryCri.add(Subqueries.notExists(subQueryCri.setProjection(Projections.property("Exp.id"))));
				countCri.add(Subqueries.notExists(subCountCri.setProjection(Projections.property("Exp.id"))));
			}
		}
		if (StringUtil.isNotEmpty(exceptionDoFilter.getSortContainerNo())) {
			DetachedCriteria ctnrQueryCri = DetachedCriteria.forClass(Container.class, "container");
			ctnrQueryCri.add(Property.forName("doHeader_Query.doNo").eqProperty("container.docNo"));
			ctnrQueryCri.add(Restrictions.eq("warehouseId", whId));
			ctnrQueryCri.add(Restrictions.eq("containerType", ContainerType.SORT_CONTAINER.getValue()));
			ctnrQueryCri.add(Restrictions.eq("containerNo", exceptionDoFilter.getSortContainerNo()));
			ctnrQueryCri.add(Restrictions.eq("isAvailable", YesNo.YES.getValue()));

			DetachedCriteria ctnrCountCri = DetachedCriteria.forClass(Container.class, "container");
			ctnrCountCri.add(Property.forName("doHeader_Count.doNo").eqProperty("container.docNo"));
			ctnrCountCri.add(Restrictions.eq("warehouseId", whId));
			ctnrCountCri.add(Restrictions.eq("containerType", ContainerType.SORT_CONTAINER.getValue()));
			ctnrCountCri.add(Restrictions.eq("containerNo", exceptionDoFilter.getSortContainerNo()));
			ctnrCountCri.add(Restrictions.eq("isAvailable", YesNo.YES.getValue()));
			
			queryCri.add(Subqueries.exists(ctnrQueryCri.setProjection(Projections.property("container.id"))));
			countCri.add(Subqueries.exists(ctnrCountCri.setProjection(Projections.property("container.id"))));
		}
		
		if (StringUtil.isNotEmpty(exceptionDoFilter.getFirstHoldReason())) {
		    queryCri.add(Restrictions.eq("firstHoldCode", exceptionDoFilter.getFirstHoldReason()));
            countCri.add(Restrictions.eq("firstHoldCode", exceptionDoFilter.getFirstHoldReason()));
        }
		
		if (StringUtil.isNotEmpty(exceptionDoFilter.getWaveNo())) {
			queryCri.setFetchMode("waveHeader",FetchMode.JOIN);
			queryCri.createAlias("waveHeader","waveHeader");
			queryCri.add(Restrictions.eq("waveHeader.waveNo",exceptionDoFilter.getWaveNo()));
			
			countCri.setFetchMode("waveHeader",FetchMode.JOIN);
			countCri.createAlias("waveHeader","waveHeader");
			countCri.add(Restrictions.eq("waveHeader.waveNo",exceptionDoFilter.getWaveNo()));
		}
  
		if (StringUtil.isNotEmpty(exceptionDoFilter.getBarCode())) {
			List<Long> skuIds = skuService.findSkuIdByCode(exceptionDoFilter.getBarCode());
            if (ListUtil.isNullOrEmpty(skuIds)) {
                return new DataPage<DeliveryOrderHeader>(0, 0, 20, new ArrayList<DeliveryOrderHeader>());
            } else {
                DetachedCriteria subLackDoIdCriteria = DetachedCriteria.forClass(DeliveryOrderDetail.class, "dd");
                subLackDoIdCriteria.add(Restrictions.eq("warehouseId", whId));
                subLackDoIdCriteria.add(Restrictions.in("dd.sku.id", skuIds));
                subLackDoIdCriteria.setProjection(Property.forName("dd.doHeaderId"));
                queryCri.add(Property.forName("doHeader_Query.id").in(subLackDoIdCriteria));
                
                DetachedCriteria subCountLackDoIdCriteria = DetachedCriteria.forClass(DeliveryOrderDetail.class, "dd");
                subCountLackDoIdCriteria.add(Restrictions.eq("warehouseId", whId));
                subCountLackDoIdCriteria.add(Restrictions.in("dd.sku.id", skuIds));
                subCountLackDoIdCriteria.setProjection(Property.forName("dd.doHeaderId"));
                countCri.add(Property.forName("doHeader_Count.id").in(subCountLackDoIdCriteria));
            }
		}
		
	    if (StringUtil.isNotEmpty(exceptionDoFilter.getDoStatusFrom())) {
            queryCri.add(Restrictions.ge("status", exceptionDoFilter.getDoStatusFrom()));
            countCri.add(Restrictions.ge("status", exceptionDoFilter.getDoStatusFrom()));
        }
        if (StringUtil.isNotEmpty(exceptionDoFilter.getDoStatusTo())) {
            queryCri.add(Restrictions.le("status", exceptionDoFilter.getDoStatusTo()));
            countCri.add(Restrictions.le("status", exceptionDoFilter.getDoStatusTo()));
        }

		if(null != exceptionDoFilter.getBusinessCustomerId()){
			queryCri.add(Restrictions.eq("businessCustomerId", exceptionDoFilter.getBusinessCustomerId()));
			countCri.add(Restrictions.eq("businessCustomerId", exceptionDoFilter.getBusinessCustomerId()));
		}
		
		queryCri.add(Restrictions.eq("warehouseId", whId));
        countCri.add(Restrictions.eq("warehouseId", whId));
        
		queryCri.addOrder(Order.desc("holdTime"));
		countCri.addOrder(Order.desc("holdTime"));
		
		
		return deliveryOrderService.query(queryCri, countCri, startIndex, pageSize);
	}

	@Override
	@Transactional
	public List<List<String>> releaseDO(List<Long> doIds) {
		List<List<String>> result = deliveryOrderService.releaseDO(doIds);
		findLackHeaderService.clearFindLackInfoByDoIds(doIds);
		return result;
	}
	
	
	@Override
	public void releaseAllDos() {
	    List<Long> canReleaseDoIds = doAllocateService.findNotLacks();
        int releaseDoNum = canReleaseDoIds.size();
        if (releaseDoNum > 0) {
            Integer defaultReleaseDoBatch = SystemConfig.getConfigValueInt("defaultReleaseDoBatch", ParamUtil.getCurrentWarehouseId());
            if (null == defaultReleaseDoBatch || 0 >= defaultReleaseDoBatch) {
                throw new DeliveryException(DeliveryException.DO_AUTORELEASE_JOB_CONFIG_NOT_EXIST);
            }

            int batchSize = defaultReleaseDoBatch.intValue();
            int nowIndex = 0;
            for(; nowIndex < releaseDoNum; nowIndex += batchSize) {
                int endIndex = ((nowIndex + batchSize) > releaseDoNum) ? releaseDoNum : (nowIndex + batchSize);
                List<Long> nowReleaseIdList = canReleaseDoIds.subList(nowIndex, endIndex);
                releaseDO(nowReleaseIdList);
                List<List<String>> result = releaseDO(nowReleaseIdList);
                List<String> canReleaseList = result.get(0);
        		//调用scs冻结释放do接口。
        		if(!ListUtil.isNullOrEmpty(canReleaseList)){
        			List<Long> dolist = new ArrayList<Long>();
        			for(String strDoId : canReleaseList){
        				dolist.add(Long.valueOf(strDoId));
        			}
                	expFacadeService.sendDoReleaseOrHold2ScsBatch(dolist);
                }
            }
        }
	}
	
	@Override
	public void releaseAllNotLackDos() {
	    List<Long> canReleaseDoIds = doAllocateService.findNotLacks();
        int releaseDoNum = canReleaseDoIds.size();
        if (releaseDoNum > 0) {
            Integer defaultReleaseDoBatch = SystemConfig.getConfigValueInt("defaultReleaseDoBatch", ParamUtil.getCurrentWarehouseId());
            if (null == defaultReleaseDoBatch || 0 >= defaultReleaseDoBatch) {
                throw new DeliveryException(DeliveryException.DO_AUTORELEASE_JOB_CONFIG_NOT_EXIST);
            }
            
            int batchSize = defaultReleaseDoBatch.intValue();
            int nowIndex = 0;
            for(; nowIndex < releaseDoNum; nowIndex += batchSize) {
                int endIndex = ((nowIndex + batchSize) > releaseDoNum) ? releaseDoNum : (nowIndex + batchSize);
                List<Long> nowReleaseIdList = canReleaseDoIds.subList(nowIndex, endIndex);
                List<Long> releasedeDos = releaseNotLackDos(nowReleaseIdList);
        		if(!ListUtil.isNullOrEmpty(releasedeDos)){
        			expFacadeService.sendDoReleaseOrHold2ScsBatch(releasedeDos);
        		}
            }
        }
	}
	@Override
	@Transactional
	public List<Long> releaseNotLackDos(List<Long> doIds) {
		return deliveryOrderService.releaseNotLackDos(doIds);
	}

	@Override
	public List<String> getCanRollbackStatus(Long doId) {
		return getCanRollbackStatus(this.findDoById(doId).getStatus());
	}

	@Override
	public List<String> getCanRollbackStatus(String doStatus) {
		List<String> results = new ArrayList<String>();
		// 先得到所有可能的转换步骤
		List<DoStatusRollbackStep> steps = buildRollbackSteps(doStatus,
				Constants.DoStatus.INITIAL.getValue());

		// 根据转换步骤得到所有用户的可选项
		for (DoStatusRollbackStep step : steps) {
			for (String rollbackChoice : rollbackChoiceTemplate) {
				if (step.getToStatus().equals(rollbackChoice)) {
					results.add(rollbackChoice);
					break;
				}
			}
		}
		return results;
	}

	@Override
	@Transactional
	public void doRollback(Long doId, String toStatus) {
		Object[] object = (Object[])deliveryOrderService.getDoHeaderExceptionStatus(doId);
		String excStatus     = (String)object[0];
		String releaseStatus = (String)object[1];
		String doNo          = (String)object[2];
		if (!(DoExpStatus.TO_BE_ROLLBACK.getValue().equals(excStatus) && ReleaseStatus.HOLD.getValue().equals(releaseStatus))) {
			throw new DeliveryException(DeliveryException.ROLLBACK_STATUS_ERROR, doNo);
		}
		
		DeliveryOrderHeader doHeader = this.findDoById(doId);
		if (Constants.YesNo.YES.getValue().equals(containerMgntService.getContainerMgntStartFlag())) { //开启容器管理
			if (StringUtil.isIn(doHeader.getDoType(), DoType.ALLOT.getValue(), DoType.RTV.getValue(), DoType.WHOLESALE.getValue())) {
				if (doHeader.getStatus().compareTo(DoStatus.ALLALLOCATED.getValue()) > 0 && StringUtil.isNotIn(doHeader.getStatus(), DoStatus.ALL_CARTON.getValue(), DoStatus.ALLSORTED.getValue(), DoStatus.ALLPICKED.getValue())) {
					if (pickService.existCBType(doId)) { //存在整、散拣货任务；
						if (StringUtil.isIn(doHeader.getPcsStatus(), DoStatus.ALLPICKED.getValue(), DoStatus.ALLSORTED.getValue(), DoStatus.ALL_CARTON.getValue()) && StringUtil.isIn(doHeader.getUnitStatus(), DoStatus.ALLPICKED.getValue(), DoStatus.ALLSORTED.getValue(), DoStatus.ALL_CARTON.getValue())) {

							// 散、整任务的状态一个为拣货完成，一个装箱完成；
						} else {
							throw new DeliveryException(DeliveryException.ROLLBACK_STATUS_ERROR, doNo);
						}
					}
				}
			}
		}
		
		doRollback(doId, doHeader.getStatus(), toStatus);
		//记录日志，状态回退
		orderLogService.saveLog(doHeader, OrderLogConstants.OrderLogType.ROLL_BACK.getValue(), ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_ROLL_BACK));
		
		containerMgntService.recheckReleaseContainerByPackageType(doHeader.getDoNo(), PickHeader.PKT_TYPE_PCS);
		containerMgntService.recheckReleaseContainerByPackageType(doHeader.getDoNo(), PickHeader.PKT_TYPE_UNIT);
        mergeLocService.releaseMergerLoc(doHeader.getDoNo(),PickHeader.PKT_TYPE_PCS);
        mergeLocService.releaseMergerLoc(doHeader.getDoNo(),PickHeader.PKT_TYPE_UNIT);
		//电子监管码回退
		spvsnCodeService.removeSpvsnCode(DocType.SO.getValue(), doHeader.getDoNo());
    }

	private void doRollback(Long doId, String fromStatus, String toStatus) {
		String startStatus = fromStatus;
		List<DoStatusRollbackStep> steps = buildRollbackSteps(fromStatus, toStatus);
		for (DoStatusRollbackStep step : steps) {
			fromStatus = execute(step, doId, startStatus, fromStatus, step.getToStatus());
		}
	}

	private String execute(DoStatusRollbackStep step, Long doId, String startStatus, String fromStatus, String toStatus) {
		DoStatusRollbackListener listener = getRollbackListener(step.getStepKey());
		listener.rollback(doId, startStatus, fromStatus, toStatus);
		return step.getToStatus();
	}

	public DoStatusRollbackListener getRollbackListener(String key) {
		String componentName = rollbackListeners.get(key);
		return (DoStatusRollbackListener) (Component.getInstance(componentName));
	}

	/**
	 * 根据发货单初始状态和结束状态来构建转换步骤
	 * 
	 * @param fromStatus
	 * @param toStatus
	 * @return
	 */
	private List<DoStatusRollbackStep> buildRollbackSteps(String fromStatus, String toStatus) {
		List<DoStatusRollbackStep> steps = new ArrayList<DoStatusRollbackStep>();
		for (DoStatusRollbackStep step : availRollbackSteps) {
			if (step.match(fromStatus, toStatus)) {
				steps.add(step);
				break;
			}
			if (step.in(fromStatus)) {
				steps.add(step);
				fromStatus = step.getToStatus();
			}
		}
		return steps;
	}

	@Override
	public DeliveryOrderHeader findDoById(Long id) {
		return deliveryOrderService.getDoHeaderById(id);
	}

	@Override
	@Transactional
	public void doCancelDO(Long doId) {
		deliveryOrderService.cancel(doId);
	}

    @Override
    @Transactional
    public Long rfReleaseDo(String doNo) {
            if (StringUtil.isEmpty(doNo)) {
                throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
            }
            DeliveryOrderHeader doHeader = deliveryOrderService.findDoHeaderByDoNo(doNo);
            if (null == doHeader) {
                throw new DeliveryException(DeliveryException.DO_NOT_EXIST);
            }
            if (!ReleaseStatus.HOLD.getValue().equals(doHeader.getReleaseStatus())) {
                throw new DeliveryException(DeliveryException.DO_IS_NOT_HOLD);
            }
            
            //订单释放条件：待回退，待通知客服
            if (!(DoExpStatus.TO_BE_ANNOUNCE.getValue().equals(doHeader.getExceptionStatus()) 
            		|| DoExpStatus.TO_BE_ROLLBACK.getValue().equals(doHeader.getExceptionStatus()))) {
                throw new DeliveryException(DeliveryException.DO_EXCEPTION_IS_NOT_ROLLBACK);
            }
            
            //订单必须是拣货完成状态到分拣完成之间
            if (StringUtil.isNotIn(doHeader.getStatus(), 
                    Constants.DoStatus.ALLPICKED.getValue(), 
                    Constants.DoStatus.PARTSORTED.getValue(),
                    Constants.DoStatus.ALLSORTED.getValue(),
                    Constants.DoStatus.PART_CARTON.getValue())) {
                throw new DeliveryException(DeliveryException.DO_STATUS_IS_NOT_PICKED_PARTCHECK);
            }
            
            WaveHeader wave = doHeader.getWaveHeader();
            if (null != wave && StringUtil.isNotIn(wave.getWaveStatus(), 
                    Constants.WaveStatus.PARTSORTED.getValue(),
                    Constants.WaveStatus.ALLSORTED.getValue())
                    && wave.getDoCount().intValue() != 1) {
                throw new DeliveryException(DeliveryException.WAVE_STATUS_LE_PARTSORTED);
            } 
            
            DoExStatusOpDto doExStatusOpDto = new DoExStatusOpDto();
            doExStatusOpDto.setReleaseStatus(doHeader.getReleaseStatus());
            doExStatusOpDto.setStatus(doHeader.getStatus());
            doExStatusOpDto.setExceptionStatus(doHeader.getExceptionStatus());
            doExStatusOpDto.setOpType(Constants.ExOpType.RF_RL.getValue());
            
            //如果有缺货标记则强制拣货
            if (Constants.YesNo.YES.getValue().equals(doHeader.getLackStatus())) {
                //处理拣货任务拣货单
                pickService.forcePickTsk(doHeader, ParamUtil.getCurrentLoginName());
                //刷新DO缺货标识
                doHeader.setLackStatus(YesNo.NO.getValue());
            }
            
            //如果部分装箱则删除装箱明细
            if (Constants.DoStatus.PART_CARTON.getValue().equals(doHeader.getStatus())) {
                //核拣缺货先删除装箱头和明细
                cartonService.phyDelDoCartonInfoByDoId(doHeader.getId());
                //回退序列号
                clearReCheckRecordService.rollBackSerial(doHeader.getId());
             } else if (StringUtil.isIn(doHeader.getStatus(), Constants.DoStatus.ALLPICKED.getValue(), 
                     Constants.DoStatus.PARTSORTED.getValue(), Constants.DoStatus.ALLSORTED.getValue())) {
                 //拣货完成到分拣完成更新明细状态为分拣完成 分拣数,拣货数=分配数
                 deliveryOrderService.updateDetails2Sorted(doHeader.getId());
                if (null == doHeader.getSortStartTime()) {
                    doHeader.setSortStartTime(DateUtil.getNowTime());
                }
                doHeader.setSortTime(DateUtil.getNowTime());
             }
            //设置为分拣完成 
            doHeader.setStatus(Constants.DoStatus.ALLSORTED.getValue());
            doHeader.setReleaseStatus(Constants.ReleaseStatus.RELEASE.getValue());
            doHeader.setExceptionStatus(Constants.DoExpStatus.COMPLETE.getValue());
            deliveryOrderService.updateDoHeader(doHeader);
            //更新波次状态
            if (StringUtil.isIn(doExStatusOpDto.getStatus(), Constants.DoStatus.ALLPICKED.getValue(), 
                    Constants.DoStatus.PARTSORTED.getValue())) {
                waveService.updateWaveSortingStatus(doHeader.getWaveId());
            }
            //根据doId删除找货明细
            findLackHeaderService.clearFindLackInfo(doHeader.getId());
            //记录日志
            doExceptionLogService.saveDoExceptionLog(doExStatusOpDto, doHeader);
            return doHeader.getId();
    }
    
    @Override
    @Transactional
    public void callCS(Long doId, String notes) {
        if (notes != null && notes.length() > 200) {
            throw new DeliveryException("error.delivery.callCSContentToLong");
        }

        //异常状态必须为待通知客服
        DeliveryOrderHeader doHeader = deliveryOrderService.getHeaderById(doId);
        String fmExceptionStatus = doHeader.getExceptionStatus();
        if (!DoExpStatus.TO_BE_ANNOUNCE.getValue().equals(fmExceptionStatus)) {
            throw new DeliveryException("error.delivery.exceptionStateMustBeToCallCS");
        }
        
        String doStatus = doHeader.getStatus();
        //订单已取消
        if (DoStatus.CANCELED.getValue().equals(doStatus)) {
            throw new DeliveryException("error.delivery.doIsCanceled");
        }
		setExceptionStatusByCs(doHeader);

        deliveryOrderService.saveOrUpdateDoHeader(doHeader);
        
        //删除找货任务
        findLackHeaderService.clearFindLackInfo(doHeader.getId());
        
        DoExceptionLog exceptionLog = new DoExceptionLog();
        exceptionLog.setCarrierId(doHeader.getCarrierId());
        exceptionLog.setDoHeaderId(doHeader.getId());
        exceptionLog.setDoNo(doHeader.getDoNo());
        exceptionLog.setDoType(doHeader.getDoType());
        exceptionLog.setFmDoStatus(doHeader.getStatus());
        exceptionLog.setFmExceptionStatus(fmExceptionStatus);
        exceptionLog.setFmReleaseStatus(doHeader.getReleaseStatus());
        exceptionLog.setHoldReason(doHeader.getHoldReason());
        exceptionLog.setHoldTime(DateUtil.getNowTime());
        exceptionLog.setNotes(notes);
        exceptionLog.setOperator(getOperateUser());
        exceptionLog.setToDoStatus(doHeader.getStatus());
        exceptionLog.setToExceptionStatus(doHeader.getExceptionStatus());
        exceptionLog.setToReleaseStatus(doHeader.getReleaseStatus());
        exceptionLog.setOperationType(Constants.ExOpType.CALL_CS.getValue());
        doExceptionLogService.saveDoExceptionLog(exceptionLog);
        // 通知客服
        DoExpDto dto = new DoExpDto();
        dto.setId(doId);
        dto.setNotes(notes);
        dto.setHoldCode(deliveryOrderService.getHeaderById(doId).getHoldCode());
        dto.setNotifyType(NotifyCSType.MANNUAL.getValue());
        expFacadeService.callCSCreateDatas(dto);
    }
    private String getOperateUser() {
        Identity identity = (Identity) Component.getInstance("org.jboss.seam.security.identity");
        if (identity != null && identity.getCredentials() != null) {
            String userName = identity.getCredentials().getUsername();
            return userName == null ? "SYSTEM" : userName;
        }
        return "SYSTEM";
    }

	private  void  setExceptionStatusByCs(DeliveryOrderHeader doHeader){
		String doStatus = doHeader.getStatus();
		Boolean isNoNeedCsFeedBack = SystemConfig.configIsClosed("notify.cs.notNeed.feedback",ParamUtil.getCurrentWarehouseId());
		if (isNoNeedCsFeedBack){
			if (DoStatus.INITIAL.getValue().equals(doStatus)) {//是否初始化
				doHeader.setExceptionStatus(DoExpStatus.TO_BE_REPL.getValue());
			} else {
				doHeader.setExceptionStatus(DoExpStatus.TO_BE_ROLLBACK.getValue());
			}
		}else{
			String holdCode = doHeader.getHoldCode();
			if (Reason.MALICE_DO.getValue().equals(holdCode)) {
				doHeader.setExceptionStatus(DoExpStatus.TO_BE_FEEDBACK.getValue());
			} else if (StringUtil.isIn(holdCode,
					Reason.PICK_LACK.getValue(), Reason.PICK_DAMAGE.getValue(),
					Reason.SORT_LACK.getValue(), Reason.RECHECK_LACK.getValue(),
					Reason.SORT_DM.getValue(), Reason.CHECK_DM.getValue())) {
				//拣货缺货 拣货破损 分拣缺货 核拣缺货 分拣破损 核拣破损 必须拣货完成后  异常状态为待反馈
				if (StringUtil.isNotIn(doHeader.getStatus(), Constants.DoStatus.ALLSORTED.getValue(), Constants.DoStatus.PARTSORTED.getValue(),
						Constants.DoStatus.PART_CARTON.getValue(), Constants.DoStatus.ALLPICKED.getValue())) {
					throw new DeliveryException(DeliveryException.DO_STATUS_LESS_ALL_PICKED);
				}
				doHeader.setExceptionStatus(DoExpStatus.TO_BE_FEEDBACK.getValue());
			} else {
				if (DoStatus.INITIAL.getValue().equals(doStatus)) {//是否初始化
					doHeader.setExceptionStatus(DoExpStatus.TO_BE_FEEDBACK.getValue());
				} else {
					doHeader.setExceptionStatus(DoExpStatus.TO_BE_ROLLBACK.getValue());
				}
			}
		}
	}
    @Override
    @Transactional
    public void doSkipCS(Long doId){
    	DeliveryOrderHeader doHeader = deliveryOrderService.getHeaderById(doId);

		if (!Constants.DoExpStatus.TO_BE_ANNOUNCE.getValue().equals(doHeader.getExceptionStatus())
				&& !Constants.DoExpStatus.TO_BE_FEEDBACK.getValue().equals(doHeader.getExceptionStatus())) {
			throw new DeliveryException("error.delivery.exceptionStateMustBeToCallCS");
		}

		Map<String, String> resonMap = Dictionary.getDictionary("REASON_HDD");
    	String fmExceptionStatus = doHeader.getExceptionStatus();
    	
    	if(doHeader.getStatus().equals(Constants.DoStatus.INITIAL.getValue())){
    		doHeader.setExceptionStatus(DoExpStatus.TO_BE_REPL.getValue());
    	}else{
    		doHeader.setExceptionStatus(DoExpStatus.TO_BE_ROLLBACK.getValue());
    	}
		if (!Reason.MANUAL_LACK.getValue().equals(doHeader.getHoldCode()) &&
				!Reason.MALICE_DO.getValue().equals(doHeader.getHoldCode()) &&
				!Reason.BAD_QUALITY.getValue().equals(doHeader.getHoldCode()) &&
				!Reason.PICK_LACK.getValue().equals(doHeader.getHoldCode())) {
			//手动冻结、拣货缺货的订单不修改冻结原因
			doHeader.setHoldCode(Constants.Reason.WAIT_REPL.getValue());
			doHeader.setHoldReason(resonMap.get(Constants.Reason.WAIT_REPL.getValue()));
		}

		deliveryOrderService.saveOrUpdateDoHeader(doHeader);
		
		//删除找货任务
		findLackHeaderService.clearFindLackInfo(doHeader.getId());
		
		DoExceptionLog exceptionLog = new DoExceptionLog();
        exceptionLog.setCarrierId(doHeader.getCarrierId());
        exceptionLog.setDoHeaderId(doId);
        exceptionLog.setDoNo(doHeader.getDoNo());
        exceptionLog.setDoType(doHeader.getDoType());
        exceptionLog.setFmDoStatus(doHeader.getStatus());
        exceptionLog.setFmExceptionStatus(fmExceptionStatus);
        exceptionLog.setFmReleaseStatus(doHeader.getReleaseStatus());
        exceptionLog.setHoldReason(doHeader.getHoldReason());
        exceptionLog.setHoldTime(doHeader.getHoldTime());
        exceptionLog.setNotes(" ");
        exceptionLog.setOperator(getOperateUser());
        exceptionLog.setToDoStatus(doHeader.getStatus());
        exceptionLog.setToExceptionStatus(doHeader.getExceptionStatus());
        exceptionLog.setToReleaseStatus(doHeader.getReleaseStatus());
        exceptionLog.setOperationType(Constants.ExOpType.SKIP_CS.getValue());
        doExceptionLogService.saveDoExceptionLog(exceptionLog);
    }

	@Override
	public List<TempCartonExceptionDTO> findTempCartonExceptionList(BackExceptionFilter backExceptionFilter, int startIndex, int pageSize) {
		Integer defaultCarrierId = SystemConfig.getConfigValueInt("dubhe.default.carrier.id", ParamUtil.getCurrentWarehouseId());
		if(defaultCarrierId == null){
			defaultCarrierId = -9999;
		}
		backExceptionFilter.setDefaultCarrierId(defaultCarrierId.longValue());
		return tempCartonDAO.findTempCartonExceptionList(backExceptionFilter,startIndex,pageSize);
	}

	@Override
	public Long countTempCartonException(BackExceptionFilter backExceptionFilter) {
		Integer defaultCarrierId = SystemConfig.getConfigValueInt("dubhe.default.carrier.id", ParamUtil.getCurrentWarehouseId());
		if(defaultCarrierId == null){
			defaultCarrierId = -9999;
		}
		backExceptionFilter.setDefaultCarrierId(defaultCarrierId.longValue());
		return tempCartonDAO.countTempCartonException(backExceptionFilter);
	}
}