package com.daxia.wms.doexception.action;

import com.daxia.wms.exp.Constants;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;

import com.daxia.framework.common.action.ActionBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.wms.Constants.NotifyCSType;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.doexception.service.DoExceptionService;
import com.daxia.wms.exp.dto.DoExpDto;
import com.daxia.wms.exp.service.DoExpService;
import com.daxia.wms.exp.service.ExpFacadeService;

@Name("com.daxia.wms.exception.callCSAction")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class CallCSAction extends ActionBean {

    private static final long serialVersionUID = 8191165977704164507L;
    
    private Long doHeaderId;
    private String inputContent;
    
    @In
    private DeliveryOrderService deliveryOrderService;
    
    @In
    private DoExceptionService doExceptionService;

    @In
    DoExpService doExpService;
    
    @In
    private ExpFacadeService expFacadeService; 
    
    @Create
    public void init() {

    }

    public void show() {
        inputContent = "";
    }
    
    /*
     * 通知客服
     */
    public void call(){
        String holdCode = deliveryOrderService.getHeaderById(doHeaderId).getHoldCode();
        if(!Constants.Reason.ALLOC_LACK.getValue().equals(holdCode)){
            this.sayMessage("只有分配冻结才可通知客服");
            return;
        }
        doExceptionService.callCS(this.doHeaderId, this.inputContent);

        //调用接口通知客服；
        DoExpDto dto = new DoExpDto();
        dto.setId(doHeaderId);
        dto.setNotes(this.inputContent);
        dto.setHoldCode(holdCode);
        dto.setNotifyType(NotifyCSType.MANNUAL.getValue());
        expFacadeService.callCS(dto);
        
        this.sayMessage(MESSAGE_SUCCESS);
    }

    public Long getDoHeaderId() {
        return doHeaderId;
    }

    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }

    public String getInputContent() {
        return inputContent;
    }

    public void setInputContent(String inputContent) {
        this.inputContent = inputContent;
    }
}