package com.daxia.wms.doexception.service;

import java.util.Date;
import java.util.List;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.doexception.dto.FindLackDto;
import com.daxia.wms.doexception.entity.FindLackHeader;
import com.daxia.wms.doexception.filter.FindLackFilter;

/**
 * 找货单头service
 */
public interface FindLackHeaderService {
	public FindLackHeader findHeaderByDocNo(String docNo);
	
	public DataPage<FindLackDto> findPageByFilter(FindLackFilter filter, int startIndex,int pageSize);
	
	/**
	 * 创建缺货找货任务 单头+明细
	 * @param doIds
	 */
	public void createLackTask(List<Long> doIds);
	
	/**
	 * 根据发货单查询找货单头
	 * @param doId
	 * @return
	 */
	public List<FindLackHeader> getByDoIds(List<Long> doIds);
	
	/**
	 * 根据ID删除
	 * @param findLackHeaderId
	 */
	public void deleteById(Long findLackHeaderId);
	
	/**
	 * 根据发货单清除找货头找货任务信息
	 * @param doHeaderId
	 */
	public void clearFindLackInfo(Long doHeaderId);
	
	/**
	 * 根据发货单清除找货头，找货任务信息
	 * @param doIds
	 */
	public void clearFindLackInfoByDoIds(List<Long> doIds);

	/** 
	 * 设置找货头和明细的打印状态为已打印
	 */
	public void setPrintStatusAsPrinted(String findLackHNo);

	public Date getEarliestPlanShipTimeByHeaderNo(String headerNo);

	/**
	 * 查询缺货找货头list
	 * @return
	 */
	public List<FindLackHeader> getFindLackHeaderList();
}
