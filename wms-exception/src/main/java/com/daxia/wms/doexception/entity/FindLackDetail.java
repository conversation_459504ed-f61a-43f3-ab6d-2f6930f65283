package com.daxia.wms.doexception.entity;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import com.daxia.framework.common.entity.WhBaseEntity;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;


/**
 * 找货单明细
 */
@Entity
@Table(name = "doc_find_lack_d")
@org.hibernate.annotations.Entity(dynamicInsert = true, dynamicUpdate = true)
@Where(clause = " IS_DELETED = 0 ")
@SQLDelete(sql = "update doc_find_lack_d set is_deleted = 1 where id = ? and version = ?")
@lombok.extern.slf4j.Slf4j
public class FindLackDetail  extends WhBaseEntity {

    private static final long serialVersionUID = 3685727118707084579L;
    private Long id;
    private Long doHeaderId;
    private Long findLackHId;
    private BigDecimal lackQty;
    private Integer isDeleted;
    
    private FindLackHeader findLackHeader;
    private DeliveryOrderHeader doHeader;
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.AUTO)  
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    @Column(name = "DO_HEADER_ID")
    public Long getDoHeaderId() {
        return doHeaderId;
    }
    
    public void setDoHeaderId(Long doHeaderId) {
        this.doHeaderId = doHeaderId;
    }
    
    @Column(name = "FIND_LACK_H_ID")
    public Long getFindLackHId() {
        return findLackHId;
    }
    
    public void setFindLackHId(Long findLackHId) {
        this.findLackHId = findLackHId;
    }
    
    @Column(name = "LACK_QTY")
    public BigDecimal getLackQty() {
        return lackQty;
    }
    
    public void setLackQty(BigDecimal lackQty) {
        this.lackQty = lackQty;
    }
    
    @Column(name = "IS_DELETED")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "FIND_LACK_H_ID", insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
	public FindLackHeader getFindLackHeader() {
		return findLackHeader;
	}

	public void setFindLackHeader(FindLackHeader findLackHeader) {
		this.findLackHeader = findLackHeader;
	}

	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DO_HEADER_ID", insertable = false, updatable = false)
    @Where(clause = " IS_DELETED = 0 ")
	public DeliveryOrderHeader getDoHeader() {
		return doHeader;
	}

	public void setDoHeader(DeliveryOrderHeader doHeader) {
		this.doHeader = doHeader;
	}
}
