package com.daxia.wms.doexception.job;

import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.doexception.service.DoExceptionService;
import com.daxia.wms.master.entity.Warehouse;
import com.daxia.wms.master.job.AbstractJob;
import com.daxia.wms.master.service.WarehouseService;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.jboss.seam.Component;
import org.jboss.seam.annotations.AutoCreate;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import java.util.List;

/**
 * 批量释放分配中和分配完成的待补货状态的正常DO
 * 批量见配置项defaultReleaseDoBatch
 */
@Name("autoReleaseDoJob")
@AutoCreate
@lombok.extern.slf4j.Slf4j
public class AutoReleaseDoJob extends AbstractJob {
    
    @In
    private DoExceptionService doExceptionService;
    @In
    private WarehouseService warehouseService;

    @Override
    public void doRun() {

        log.debug("Begin Run AutoReleaseDoJob, Please Waiting ............... ");
        
        StringBuffer errSb = new StringBuffer();
       
        try {
            List<Warehouse> whList = warehouseService.getRunningWh();
            if (ListUtil.isNotEmpty(whList)) {
                for (Warehouse cfgWarehouse : whList) {
                    try {
                        ParamUtil.setCurrentWarehouseId(cfgWarehouse.getId());
                        log.info("current warehouse info: " );
                        log.info("cfgWarehouse.getId() = " + cfgWarehouse.getId());
                        log.info("cfgWarehouse.getWarehouseName() = ", cfgWarehouse.getWarehouseName());
                        log.info("ParamUtil.getCurrentWarehouseId() = " + ParamUtil.getCurrentWarehouseId());
                       
                        doExceptionService.releaseAllNotLackDos();
                        
                        log.info(cfgWarehouse.getWarehouseName()+" AutoReleaseDoJob done ");
                    } catch (Exception e) {
                        if (e instanceof HibernateException) {
                            try {
                                log.debug("rollback begin");

                                Session session = (Session) Component.getInstance("hibernateSession");
                                session.getTransaction().rollback();

                                log.debug("rollback end");
                                
                                errSb.append(cfgWarehouse.getWarehouseName()).append(" : ").append(e.getMessage()).append(", ");
                            } catch (Exception e2) {
                                log.error("rollback failed", e2);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("AutoReleaseDoJob release do failed", e);
        }
      
        log.info(errSb.toString());
        
        log.info("AutoReleaseDoJob done...\n");
    }
}
