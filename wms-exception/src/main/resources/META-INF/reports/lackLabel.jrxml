<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="face" language="groovy" pageWidth="340" pageHeight="180" columnWidth="340" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="2.0000000000000036"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="正文标签" hAlign="Center" vAlign="Middle" fontName="微软雅黑" fontSize="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H"/>
	<style name="正文字段" hAlign="Left" vAlign="Middle" fontName="微软雅黑" fontSize="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H"/>
	<parameter name="barcodeUrl" class="java.lang.String"/>
	<field name="doAmount" class="java.lang.String"/>
	<field name="skuAmount" class="java.lang.String"/>
	<field name="unitAmount" class="java.lang.String"/>
	<field name="taskNo" class="java.lang.String"/>
	<field name="planShipTime" class="java.lang.String"/>
	<detail>
		<band height="180">
			<frame>
				<reportElement x="0" y="0" width="340" height="2"/>
			</frame>
			<image isUsingCache="false" isLazy="true">
				<reportElement x="125" y="16" width="195" height="49"/>
				<imageExpression class="java.lang.String"><![CDATA[$P{barcodeUrl} + $F{taskNo}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="true">
				<reportElement style="正文字段" x="143" y="72" width="162" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{planShipTime}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement style="正文标签" x="6" y="103" width="56" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[订单数：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement style="正文字段" x="62" y="103" width="83" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Top">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{doAmount}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement style="正文字段" x="244" y="103" width="82" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{skuAmount}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement style="正文标签" x="150" y="103" width="94" height="20" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Top">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[缺货sku总数：]]></text>
			</staticText>
			<staticText>
				<reportElement style="正文标签" x="6" y="134" width="117" height="19" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[缺货units总数：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement style="正文字段" x="123" y="134" width="203" height="19" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Top">
					<font fontName="微软雅黑" size="14" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{unitAmount}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement style="正文标签" x="6" y="28" width="104" height="26"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" isBold="false" isItalic="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[找货任务号：]]></text>
			</staticText>
			<staticText>
				<reportElement style="正文标签" x="6" y="72" width="133" height="20"/>
				<textElement textAlignment="Left">
					<font size="14"/>
				</textElement>
				<text><![CDATA[最早预计出库时间：]]></text>
			</staticText>
		</band>
	</detail>
</jasperReport>
