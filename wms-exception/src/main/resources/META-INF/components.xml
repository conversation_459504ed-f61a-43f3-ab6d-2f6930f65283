<?xml version="1.0" encoding="UTF-8"?>
<components xmlns="http://jboss.com/products/seam/components"
	xmlns:core="http://jboss.com/products/seam/core" 
	xmlns:persistence="http://jboss.com/products/seam/persistence"
	xmlns:security="http://jboss.com/products/seam/security"
	xmlns:transaction="http://jboss.com/products/seam/transaction"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://jboss.com/products/seam/core http://jboss.com/products/seam/core-2.0.xsd                   
		http://jboss.com/products/seam/persistence http://jboss.com/products/seam/persistence-2.0.xsd                  
		http://jboss.com/products/seam/security http://jboss.com/products/seam/security-2.0.xsd                  
		http://jboss.com/products/seam/mail http://jboss.com/products/seam/mail-2.0.xsd                  
		http://jboss.com/products/seam/async  http://jboss.com/products/seam/async-2.0.xsd                       
		http://jboss.com/products/seam/bpm http://jboss.com/products/seam/bpm-2.0.xsd                             
		http://jboss.com/products/seam/transaction http://jboss.com/products/seam/transaction-2.0.xsd                  
		http://jboss.com/products/seam/web http://jboss.com/products/seam/web-2.0.xsd
		http://jboss.com/products/seam/components http://jboss.com/products/seam/components-2.0.xsd">
	
	<import>com.daxia.wms.exception</import>
	
	<component class="com.daxia.wms.doexception.service.impl.DoExceptionServiceImpl" 
		name="com.daxia.wms.exception.doExceptionService">
		<property name="rollbackListeners">
			<!-- 装箱复核完成（部分装箱复核） ~ 分拣完成 -->
			<key>CARTON2SORTED</key>
			<value>com.daxia.wms.delivery.clearReCheckRecordService</value>
			<!-- 分拣完成（部分分拣） ~  拣货完成 -->
			<key>SORTED2PICKED</key>
			<value>com.daxia.wms.delivery.sortingService</value>
			<!-- 拣货完成（部分拣货） ~  初始化-->
			<key>PICKED2INITIAL</key>
			<value>com.daxia.wms.delivery.pickService</value>
			<!-- 分配完成（部分分配） ~  初始化    -->
			<key>ALLOCATED2INITIAL</key>
			<value>com.daxia.wms.delivery.deliveryOrderService</value>
		</property>
	</component>
</components>
