<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:s="http://jboss.com/products/seam/taglib"
	xmlns:a4j="http://richfaces.org/a4j"
	xmlns:rich="http://richfaces.org/rich"
	xmlns:fw="http://fw.daxia.com/ui"
	template="/templates/dialog.xhtml">
	<ui:define name="title">
		<h:outputText value="通知客服" />
	</ui:define>
	<ui:define name="body">
		<style>
body {
	margin: 5px;
}
.infoNum,.errorNum{
	font-family: Constantia,Georgia;
    font-size: 22px;
    font-weight: 700;
}
.infoNum{
	color: #103667;
}
.errorNum{
    color: #DA0000;
}
</style>
		<link rel="stylesheet" type="text/css"
			href="${facesContext.externalContext.requestContextPath}/css/addition.css" />
		<script type="text/javascript" src="${facesContext.externalContext.requestContextPath}/js/jquery-limit.js"></script>
		<s:div id="bodyDiv">
			<a4j:form id="callCsForm">
				<rich:messages id="bodyRichMessage" showDetail="true"
					showSummary="false" />
				<h2 class="box-header">通知内容</h2>
				<div class="box-content">
					<LABEL style="line-height: 25px;">通知备注(限200字符以内,中文汉字或标点视为2个字符)：</LABEL>
					<h:inputTextarea styleClass="full form-input" id="inputContent"
						value="#{callCSAction.inputContent}"/>
					<SPAN style="height: 50px;line-height:50px">已输入<label id='contentLength' class="infoNum">0</label>个字符，还可以输入<label
						id='surplusLength' class="infoNum">200</label>个字符
					</SPAN>
					<div style="text-align: right;">
						<a4j:commandLink styleClass="btnCla" onclick="validate();return false;">
							<i class="fa fa-paper-plane fa-fw"/>
							<h:outputText value="发送"/>
						</a4j:commandLink>
						<a4j:commandLink styleClass="btnCla" onclick="window.close();return false;">
							<i class="fa fa-remove fa-fw"/>
							<h:outputText value="取消"/>
						</a4j:commandLink>
					</div>
				</div>
				<a4j:jsFunction name="callCs" action="#{callCSAction.call()}"
					requestDelay="1"/>
			</a4j:form>
		</s:div>
		<script>
		 //<![CDATA[        
			var maxLength = 200;

			jQuery(document).ready(function(){
				jQuery('textarea.form-input').limit(maxLength,false,function(contentLength,surplusLength){
					jQuery('#contentLength').text(contentLength);

					if (surplusLength <= 0){
						jQuery('#surplusLength').addClass('errorNum').removeClass('infoNum');
					}else{
						jQuery('#surplusLength').removeClass('errorNum').addClass('infoNum');
					}
						
					jQuery('#surplusLength').text(surplusLength);
				});
			})
			
			function validate() {
				var length = getLength();

				if (length > maxLength) {
					alert('字数超过限制！/ Word count exceeds limit!')
					return false;
				}

				callCs();
			}
			
			function getLength() {
				var str = jQuery('textarea.form-input').val();
				return str.replace(/[^\x00-\xff]/g, "**").length;
			}
			//]]>
		</script>
	</ui:define>
</ui:composition>