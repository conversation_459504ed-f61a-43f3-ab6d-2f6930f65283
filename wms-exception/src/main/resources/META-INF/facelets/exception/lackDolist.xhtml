<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:c="http://java.sun.com/jstl/core"
	xmlns:s="http://jboss.com/products/seam/taglib"
	xmlns:a4j="http://richfaces.org/a4j"
	xmlns:rich="http://richfaces.org/rich"
	xmlns:fw="http://fw.daxia.com/ui"
	template="/templates/popup.xhtml">
	<ui:define name="popTitle">
		<h:outputText value="SKU[#{lackDoAction.sku.productCode}]缺货订单列表" />
	</ui:define>
	<ui:define name="popBody">
		<s:div id="bodyDiv">
			<h2 class="box-header">SKU信息</h2>
			<div class="box-content">
				<table width="100%" class="info_table" border="0" cellSpacing="0"
					cellPadding="0">
					<tr>
						<td class="info_label font_4">商品条码:</td>
						<td class="info_value"><h:outputText
								value="#{lackDoAction.sku.ean13}" /></td>
						<td class="info_label wrap font_4">商品编码:</td>
						<td class="info_value">#{lackDoAction.sku.productCode}</td>
					</tr>
					<tr>
						<td class="info_label font_4">商品名称:</td>
						<td class="info_value"><h:outputText
								value="#{lackDoAction.sku.getProductCnameForI18n()}" /></td>
						<td class="info_label wrap font_4">缺货数量:</td>
						<td class="info_value"><font color="red"><h:outputText
									value="#{lackDoAction.skuLackQty}" /></font></td>
					</tr>
				</table>
			</div>
			<div height="10px"></div>
			<h2 class="box-header" style="margin-top: 10px;">缺货订单列表</h2>
			<div class="box-content" style="min-height: 300px;">
				<a4j:form id="selectForm">
					<s:div id="dataDiv" styleClass="tableContainer" style="overflow-y:auto;height:320px;"
						rendered="#{lackDoAction.dataPage.dataList.size() > 0}">
						<rich:dataTable value="#{lackDoAction.dataPage.dataList}"
							rowClasses="listTableSingular,listTableDual" var="item"
							width="100%" rowKeyVar="row">
							<h:column>
								<f:facet name="header">
									<h:outputText value="订单编码" />
								</f:facet>
								<h:outputText value="#{item.doHeader.doNo}" />
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputText value="缺货数量" />
								</f:facet>
								<h:outputText value="#{item.qty}" />
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputText value="缺货原因" />
								</f:facet>
								<h:outputText value="#{item.holdReason}" />
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputText value="缺货库位" />
								</f:facet>
								<h:outputText align="center" value="#{item.locCodes}"  escape="false"  style="width:180px;">
									<fw:wordwrap delimiter="," numPerRow ="2"/>
								</h:outputText>
							</h:column>
						</rich:dataTable>
					
					</s:div>
					<s:div styleClass="alignCenter"
						rendered="#{lackDoAction.dataPage.noDataFound}">
						<h:graphicImage value="/res/images/no_result.png" style="border:0px" />
					</s:div>
					<fw:pagination listBean="#{lackDoAction}" action="query"
						pageModel="#{lackDoAction.dataPage}" reRender="selectForm"
						hookFunction="isCheck()">
					</fw:pagination>
				</a4j:form>
			</div>
		</s:div>
	</ui:define>
</ui:composition>