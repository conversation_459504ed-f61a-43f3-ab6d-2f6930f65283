<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://java.sun.com/jsf/facelets"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:c="http://java.sun.com/jstl/core"
    xmlns:s="http://jboss.com/products/seam/taglib"
    xmlns:a4j="http://richfaces.org/a4j"
    xmlns:rich="http://richfaces.org/rich" 
    xmlns:fw="http://fw.daxia.com/ui"
    template="/templates/main.xhtml">
    <ui:define name="title">
        <ui:include src="/templates/include/navigation.xhtml">
            <ui:param name="level_1_menu" value="异常管理" />
            <ui:param name="level_2_menu" value="打印日志" />
        </ui:include>
    </ui:define>
    <ui:define name="buttonTool">
        <ui:include src="/templates/include/toggleSearchPanel.xhtml" />
    </ui:define>    
	<ui:define name="searchPanel">
		<style>
			.notes {
				font-size:12px;
				color:red;
			}
		</style>
		<table>
			<tr>
				<td style="text-align:right;"><h:outputLabel value="打印单据号" /></td>
				<td>					
					<h:inputText value="#{printLogAction.printLogFilter.docNo}" id="docNo" 
						style="width:120px" styleClass="auto-focus"/>
				</td>
				<td style="text-align:left;"><h:outputLabel class="notes" value="(如：箱号、发票号、波次号等)" /></td>
				<td><rich:spacer width="20"/></td>
				<td style="text-align:right;"><h:outputLabel value="打印类型" /></td>
				<td>
					<h:selectOneMenu value="#{printLogAction.printLogFilter.printType}" style="width:150px">
						<f:selectItem itemLabel="#{messages['global.title.pleaseChoice']}" />
						<f:selectItems value="#{printLogAction.printTypes}" />
                    </h:selectOneMenu>
                </td>
                <td><rich:spacer width="20"/></td>

				<td style="text-align:right;"><h:outputLabel value="打印人" /></td>
				<td>
					<h:inputText value="#{printLogAction.printLogFilter.createdBy}"
								 style="width:120px" styleClass="auto-focus"/>
				</td>
				<td><rich:spacer width="20"/></td>
				<td>
					<a4j:commandButton value="#{messages['button.query']}"
						rendered="#{identity.hasPermission('exception.printLog.query')}"
						id="btnQuery" action="#{printLogAction.buttonQuery()}"
						onclick="if(!daxia.util.checkForm(this.form,this)){return false;} daxia.util.startProcess(this)"
						oncomplete="daxia.util.endProcess(this);"
						styleClass="btnCla default-submit" reRender="resultList">
					</a4j:commandButton>
				</td>
			</tr>
			<tr>
				<td style="text-align:right;"><h:outputLabel value="关联外部单据号" /></td>
				<td>
					<h:inputText value="#{printLogAction.printLogFilter.outRefNo}" id="outRefNo" 
						style="width:120px">
					</h:inputText>
				</td>
				<td style="text-align:left;"><h:outputLabel class="notes" value="(如：SO号、调拨指令单号、PO号等)" /></td>
				<td><rich:spacer width="20"/></td>
				<td style="text-align:right;"><h:outputLabel value="关联WMS单据号" /></td>
				<td>					
					<h:inputText value="#{printLogAction.printLogFilter.refNo}" id="refNo" 
						style="width:146px" styleClass="auto-focus"/>
				</td>
				<td style="text-align:left;"><h:outputLabel class="notes" value="(如：DO号、ASN号等)" /></td>
				<td>打印时间From</td>
				<td>
					<h:inputText  value="#{printLogAction.printLogFilter.createTimeFrom}"
								 onclick="showDateTimePicker(this)" style="width:120px;">
						<s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss"/>
					</h:inputText>
				</td>
				<td>打印时间To</td>
				<td>
					<h:inputText  value="#{printLogAction.printLogFilter.createTimeTo}"
								 onclick="showDateTimePicker(this)" style="width:120px;">
						<s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss"/>
					</h:inputText>
				</td>
			</tr>
		</table>
	</ui:define>

	<ui:define name="content">
	<h:panelGroup id="resultArea">
		<a4j:form id="selectForm" style="margin: 5px 0 0; padding: 0; width: 100%;">
			<rich:panel id="resultList" styleClass="panelCla" bodyClass="panelBody" headerClass="panelHead">
           		<s:div id="dataDiv" styleClass="tableContainer" rendered="#{printLogAction.dataPage.dataList.size() > 0}">
					<rich:dataTable value="#{printLogAction.queryResult}"
						var="item"
						rowKeyVar="row" 
						style="width: 100%;"
						rowClasses="listTableSingular,listTableDual" >
						<rich:column style="width: 20px;">
							<f:facet name="header">
								<h:outputText value="#{messages['global.title.rowNo']}" />
							</f:facet>
							<h:outputText value="#{row + 1 + printLogAction.dataPage.startIndex}" />
						</rich:column>
						<rich:column>
							<f:facet name="header">
								<h:outputText value="打印类型" />
							</f:facet>
							<h:outputText value="#{item.printType.displayName}" />
						</rich:column>
						<rich:column style="width:400px">
							<f:facet name="header">
								<h:outputText value="打印单据号" />
							</f:facet>
							<h:outputText value="#{item.docNo}" />
						</rich:column>
						<rich:column style="width:400px">
							<f:facet name="header">
								<h:outputText value="关联WMS单号" />
							</f:facet>
							<h:outputText value="#{item.refNo}" />
						</rich:column>
						<rich:column style="width:400px">
							<f:facet name="header">
								<h:outputText value="关联外部单号" />
							</f:facet>
							<h:outputText value="#{item.outRefNo}" />
						</rich:column>
						<rich:column style="width:400px">
							<f:facet name="header">
								<h:outputText value="打印人" />
							</f:facet>
							<h:outputText value="#{item.createdBy}" />
						</rich:column>
						<rich:column style="width:400px">
							<f:facet name="header">
								<h:outputText value="打印时间" />
							</f:facet>
							<h:outputText value="#{item.createdAt}">
								<s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" />
							</h:outputText>
						</rich:column>
						<rich:column style="width:400px">
							<f:facet name="header">
								<h:outputText value="备注" />
							</f:facet>
							<h:outputText value="#{item.printTypeEx}"/>
						</rich:column>
						<rich:column style="width:400px">
							<f:facet name="header">
								<h:outputText value="操作来源" />
							</f:facet>
							<h:outputText value="#{item.deviceName}"/>
						</rich:column>
					</rich:dataTable>
					<script>daxia.wms.util.resizeBody();</script>
            	</s:div>
	            <s:div styleClass="alignCenter" rendered="#{printLogAction.dataPage.noDataFound}">
	                <h:graphicImage value="/res/images/no_result.png" style="border:0px" />
		        </s:div>        	
				<fw:pagination listBean="#{printLogAction}"
					action="query"
					pageModel="#{printLogAction.dataPage}"
					reRender="selectForm"
					hookFunction="isCheck()">
				</fw:pagination>
			</rich:panel>
		</a4j:form>
	</h:panelGroup>
	</ui:define>
</ui:composition>