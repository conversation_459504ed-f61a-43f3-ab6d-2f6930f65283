<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:c="http://java.sun.com/jstl/core"
				xmlns:s="http://jboss.com/products/seam/taglib"
				xmlns:a4j="http://richfaces.org/a4j"
				xmlns:rich="http://richfaces.org/rich"
				xmlns:fw="http://fw.daxia.com/ui"
				template="/templates/main.xhtml">
	<ui:define name="docTitle">下单回单异常_</ui:define>
	<ui:define name="title">
		<ui:include src="/templates/include/navigation.xhtml">
			<ui:param name="level_1_menu" value="异常管理" />
			<ui:param name="level_2_menu" value="下单回单异常" />
		</ui:include>
	</ui:define>

	<ui:define name="buttonTool">
		<a4j:commandLink rendered="#{identity.hasPermission('back.exception.reCall')}"
				onclick="refreshFailOrder();return false;">
			<i class="fa fa-star-half-o fa-fw"/>
			<h:outputText value="失败数据重调"/>
		</a4j:commandLink>
	</ui:define>

	<ui:define name="searchPanel">
		<c:if test="#{identity.hasPermission('back.exception.query')}">
			<style>#schPanel{height: 85px;}</style>
			<table>
				<tr>
                    <td width="80"><h:outputLabel value="创建时间From" /></td>
                    <td width="145">
                        <h:inputText value="#{backExceptionAction.backExceptionFilter.createdFrom}" id="createdTimeFrom" style="width:140px" onclick="showDateTimePicker(this);">
                            <s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss"/>
                        </h:inputText>
                    </td>
                    <td width="8"><h:outputLabel value="To" /></td>
                    <td width="150">
                        <h:inputText value="#{backExceptionAction.backExceptionFilter.createdTo}" id="createdTimeTo" style="width:140px" onclick="showDateTimePicker(this);">
                            <s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss"/>
                        </h:inputText>
                    </td>

					<td width="70"><h:outputLabel value="发货单号" /></td>
					<td width="145">
						<h:inputText value="#{backExceptionAction.backExceptionFilter.doNo}" id="doNo" style="width:140px"
									 autocomplete="off"  onkeydown="enterSubmit(event);"/>
					</td>
					<td width="70"><h:outputLabel value="波次号" /></td>
					<td width="145">
						<h:inputText value="#{backExceptionAction.backExceptionFilter.waveNo}" id="waveNo" style="width:140px"
									 autocomplete="off"  onkeydown="enterSubmit(event);"/>
					</td>
					<td width="60"><h:outputLabel value="配送商" /></td>
					<td width="145">
						<h:selectOneMenu
								value="#{backExceptionAction.backExceptionFilter.carrierId}"
								style="width:118px">
							<f:selectItem itemLabel="#{messages['global.title.pleaseChoice']}" />
							<f:selectItems value="#{carrierSelector}" />
						</h:selectOneMenu>
					</td>
					<td width="60"><h:outputLabel value="异常状态" /></td>
					<td width="110">
						<h:selectOneMenu value="#{backExceptionAction.backExceptionFilter.exceptionStatus}" id="doType" style="width:143px">
							<f:selectItems value="#{dictionary.getSelectItems('BACK_EXCEPTION_STATUS')}"/>
						</h:selectOneMenu>
					</td>

					<td>
						<rich:spacer width="20"/>
						<a4j:commandButton action="#{backExceptionAction.buttonQuery()}"  value="查询"
										   rendered="#{identity.hasPermission('back.exception.query')}"
										   styleClass="btnCla" id="searchBtn" reRender="resultArea,searchForm:doNo" oncomplete="focusDefault();daxia.util.endProcess(this);" />
					</td>
				</tr>
			</table>
			<script>
				//<![CDATA[

				//]]>
			</script>
		</c:if>
	</ui:define>
	<ui:define name="content">
		<h:panelGroup id="resultArea">
			<a4j:form id="selectForm" style="margin: 5px 0 0; padding: 0; width: 100%;">
				<rich:panel id="resultList" styleClass="panelCla" bodyClass="panelBody" headerClass="panelHead">
					<s:div id="dataDiv" styleClass="tableContainer" rendered="#{backExceptionAction.dataPage.dataList.size() > 0}">
						<rich:dataTable value="#{backExceptionAction.queryResult}"
										var="item"
										rowKeyVar="row"
										style="width: 100%;"
										id="resultTable"
										styleClass="css-table">
							<h:column style="width: 100px;">
								<f:facet name="header">操作</f:facet>
								<h:panelGroup rendered="#{identity.hasPermission('back.exception.reCall.manual')}">
									<a4j:commandLink onclick="doReCall('#{item.doId}');return false;" title="重调">
										<i class="fa fa-user-times fa-fw"/>
										<h:outputText value="重调"/>
									</a4j:commandLink>
								</h:panelGroup>
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputLabel value="订单号" />
								</f:facet>
                                <h:outputText value="#{item.doNo}" />
							</h:column>
							<h:column>
								<f:facet name="header"><h:outputLabel value="波次号" /></f:facet>
								<h:outputText value="#{item.waveNo}" />
							</h:column>
							<h:column style="width:70px">
								<f:facet name="header"><h:outputLabel value="订单状态" /></f:facet>
								<h:outputText value="#{item.status}" >
									<fw:dict dictionary="DO_STATUS" />
								</h:outputText>
							</h:column>
							<h:column>
								<f:facet name="header"><h:outputLabel value="配送商" /></f:facet>
								<h:outputText value="#{item.carrierName}" />
							</h:column>
							<h:column>
								<f:facet name="header"><h:outputLabel value="异常信息" /></f:facet>
								<h:outputText value="#{item.errorMsg}" />
							</h:column>
						</rich:dataTable>
						<script>daxia.wms.util.resizeBody();</script>
					</s:div>
					<s:div styleClass="alignCenter" rendered="#{backExceptionAction.dataPage.noDataFound}">
						<h:graphicImage value="/res/images/no_result.png" style="border:0px" />
					</s:div>
					<fw:pagination listBean="#{backExceptionAction}"
								   action="query"
								   pageModel="#{backExceptionAction.dataPage}"
								   reRender="selectForm"
								   hookFunction="isCheck()">
					</fw:pagination>
					<a4j:jsFunction action="#{backExceptionAction.releaseDO}" name="reCall"
									oncomplete="onDoQuery()" >
						<a4j:actionparam name="doId" assignTo="#{backExceptionAction.doId}" />
					</a4j:jsFunction>
					<a4j:jsFunction action="#{backExceptionAction.refreshFailOrder}" name="refreshFailOrder"
									oncomplete="alert('处理成功! / Processed successfully!');" >
					</a4j:jsFunction>
				</rich:panel>
				<script type="text/javascript">
					//<![CDATA[
					function onDoQuery(){
						jQuery("#searchForm\\:searchBtn").click();
					}

					function isError(){
						return jQuery(".notes-error").text().strip() != "";
					}

					function doReCall(doId){
						if(!doId){
							return;
						}
						reCall(doId);
					}
					//]]>
				</script>
			</a4j:form>
		</h:panelGroup>
		<ui:include src="/sound/soundRes.xhtml" />
	</ui:define>
</ui:composition>