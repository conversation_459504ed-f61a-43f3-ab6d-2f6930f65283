<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:c="http://java.sun.com/jstl/core"
	xmlns:s="http://jboss.com/products/seam/taglib"
	xmlns:a4j="http://richfaces.org/a4j"
	xmlns:rich="http://richfaces.org/rich"
	xmlns:fw="http://fw.daxia.com/ui"
	template="/templates/popup.xhtml">
	<ui:define name="popTitle">
		<h:outputText value="拣货任务列表" />
	</ui:define>
	<ui:define name="popBody">
		<s:div id="pickBodyDiv">
			<div class="box-content" style="min-height: 300px;">
				<a4j:form id="pickTaskForm">
					<s:div id="dataDiv" styleClass="tableContainer" style="overflow-y:auto; width:760px; height:260px;"
						rendered="#{lackPickTaskAction.dataPage.dataList.size() > 0}">
						<rich:dataTable value="#{lackPickTaskAction.dataPage.dataList}"
							rowClasses="listTableSingular,listTableDual" var="item"
							width="100%" rowKeyVar="row">
							<h:column>
								<f:facet name="header">
									<h:outputText value="商品名称" />
								</f:facet>
								<h:outputText value="#{item.sku.getProductCnameForI18n()}" />
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputText value="商品条码" />
								</f:facet>
								<h:outputText value="#{item.sku.ean13}" />
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputText value="商品编码" />
								</f:facet>
								<h:outputText value="#{item.sku.productCode}" />
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputText value="需求数量" />
								</f:facet>
								<h:outputText value="#{item.qty}" />
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputText value="拣货数量" />
								</f:facet>
								<h:outputText value="#{item.qtyPickedEach}" />
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputText value="库位" />
								</f:facet>
								<h:outputText value="#{item.location.locCode}" />
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputText value="拣货人" />
								</f:facet>
								<h:outputText value="#{item.pickWho}" />
							</h:column>
						</rich:dataTable>
					</s:div>
					<s:div styleClass="alignCenter"
						rendered="#{lackPickTaskAction.dataPage.noDataFound}">
						<h:graphicImage value="/res/images/no_result.png" style="border:0px" />
					</s:div>
				</a4j:form>
			</div>
		</s:div>
	</ui:define>
</ui:composition>