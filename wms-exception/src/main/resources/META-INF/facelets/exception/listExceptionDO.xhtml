<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:c="http://java.sun.com/jstl/core"
				xmlns:s="http://jboss.com/products/seam/taglib"
				xmlns:a4j="http://richfaces.org/a4j"
				xmlns:rich="http://richfaces.org/rich"
				xmlns:fw="http://fw.daxia.com/ui" xmlns:wms="http://wms.daxia.com/ui"
				template="/templates/main.xhtml">
	<ui:define name="docTitle">发货异常_</ui:define>
	<ui:define name="title">
		<ui:include src="/templates/include/navigation.xhtml">
			<ui:param name="level_1_menu" value="异常管理" />
			<ui:param name="level_2_menu" value="发货异常" />
		</ui:include>
	</ui:define>
	<ui:define name="searchPanel">
		<c:if test="#{identity.hasPermission('exception.send.query')}">
			<style>#schPanel{height: 125px;}</style>
			<table>
				<tr>
					<td width="80"><h:outputLabel value="发货单号" /></td>
					<td width="145">
						<h:inputText value="#{doExceptionAction.exceptionDoFilter.doNo}" id="doNo" style="width:140px" autocomplete="off"  onkeydown="enterSubmit(event);"/>
					</td>
					<td width="60"><h:outputLabel value="订单类型" /></td>
					<td width="110">
						<h:selectOneMenu value="#{doExceptionAction.exceptionDoFilter.doType}" id="doType" style="width:143px">
							<f:selectItem itemLabel="#{messages['global.title.pleaseChoice']}" />
							<f:selectItems value="#{dictionary.getSelectItems('ODO_TYPE')}"/>
						</h:selectOneMenu>
					</td>
					<td width="80"><h:outputLabel value="冻结原因" /></td>
					<td width="145">
						<h:selectOneMenu value="#{doExceptionAction.exceptionDoFilter.holdReason}" id="holdReason" style="width:143px">
							<f:selectItem itemLabel="#{messages['global.title.pleaseChoice']}" />
							<f:selectItems value="#{dictionary.getSelectItems('REASON_HDD')}"/>
						</h:selectOneMenu>
					</td>
					<td width="80"><h:outputLabel value="初始冻结原因" /></td>
					<td width="145">
						<h:selectOneMenu value="#{doExceptionAction.exceptionDoFilter.firstHoldReason}" id="firstHoldReason" style="width:143px">
							<f:selectItem itemLabel="#{messages['global.title.pleaseChoice']}" />
							<f:selectItems value="#{doExceptionAction.findFirstHoldReasons()}"/>
						</h:selectOneMenu>
					</td>
					<td width="120"><h:outputLabel value="客服是否反馈" /></td>
					<td width="145">
						<h:selectOneMenu value="#{doExceptionAction.exceptionDoFilter.csReplyFlag}" id="csReplyFlag" style="width:143px">
							<f:selectItem itemLabel="#{messages['global.title.pleaseChoice']}" />
							<f:selectItems value="#{dictionary.getSelectItems('YES_NO')}"/>
						</h:selectOneMenu>
					</td>

					<td>
						<rich:spacer width="20"/>
						<a4j:commandButton action="#{doExceptionAction.buttonQuery()}"  value="查询"
										   onclick="if(validateDatePic()){daxia.util.startProcess(this);}else{return false;}"
										   rendered="#{identity.hasPermission('exception.send.query')}"
										   styleClass="btnCla" id="searchBtn" reRender="resultArea,searchForm:doNo" oncomplete="focusDefault();daxia.util.endProcess(this);" />
					</td>
				</tr>
				<tr>
					<td width="80"><h:outputLabel value="平台单号" /></td>
					<td width="145">
						<h:inputText value="#{doExceptionAction.exceptionDoFilter.originalSoCode}" id="originalSoCode" style="width:140px" autocomplete="off"  onkeydown="enterSubmit(event);"/>
					</td>
					<td width="80"><h:outputLabel value="异常状态Fm" /></td>
					<td width="145">
						<h:selectOneMenu value="#{doExceptionAction.exceptionDoFilter.fromExpStatus}" id="fromExpStatus" style="width:143px" >
							<f:selectItem itemLabel="#{messages['global.title.pleaseChoice']}" />
							<f:selectItems value="#{dictionary.getSelectItems('EXCEPTION_STATUS')}"/>
						</h:selectOneMenu>
					</td>
					<td width="8"><h:outputLabel value="To" /></td>
					<td width="145">
						<h:selectOneMenu value="#{doExceptionAction.exceptionDoFilter.toExpStatus}" id="toExpStatus" style="width:143px" >
							<f:selectItem itemLabel="#{messages['global.title.pleaseChoice']}" />
							<f:selectItems value="#{dictionary.getSelectItems('EXCEPTION_STATUS')}"/>
						</h:selectOneMenu>
					</td>
					<td width="80"><h:outputLabel value="冻结时间Fm" /></td>
					<td width="145">
						<h:inputText value="#{doExceptionAction.exceptionDoFilter.frozenTimeFrom}" id="frozenTimeFrom" style="width:140px" onclick="showDateTimePicker(this);">
							<s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss"/>
						</h:inputText>
					</td>
					<td width="8"><h:outputLabel value="To" /></td>
					<td width="150">
						<h:inputText value="#{doExceptionAction.exceptionDoFilter.frozenTimeTo}" id="frozenTimeTo" style="width:140px" onclick="showDateTimePicker(this);">
							<s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss"/>
						</h:inputText>
					</td>
				</tr>
				<tr>
					<td width="80"><h:outputLabel value="订单状态Fm" /></td>
					<td width="145">
						<h:selectOneMenu value="#{doExceptionAction.exceptionDoFilter.doStatusFrom}" id="doStatusFrom" style="width:143px" >
							<f:selectItem itemLabel="#{messages['global.title.pleaseChoice']}" />
							<f:selectItems value="#{dictionary.getSelectItems('DO_STATUS')}" />
						</h:selectOneMenu>
					</td>
					<td width="8"><h:outputLabel value="To" /></td>
					<td>
						<h:selectOneMenu value="#{doExceptionAction.exceptionDoFilter.doStatusTo}" id="doStatusTo" style="width:143px" >
							<f:selectItem itemLabel="#{messages['global.title.pleaseChoice']}" />
							<f:selectItems value="#{dictionary.getSelectItems('DO_STATUS')}" />
						</h:selectOneMenu>
					</td>
					<td width="80"><h:outputLabel value="创建时间Fm" /></td>
					<td width="145">
						<h:inputText value="#{doExceptionAction.exceptionDoFilter.createdFrom}" id="createdTimeFrom" style="width:140px" onclick="showDateTimePicker(this);">
							<s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss"/>
						</h:inputText>
					</td>
					<td width="8"><h:outputLabel value="To" /></td>
					<td width="150">
						<h:inputText value="#{doExceptionAction.exceptionDoFilter.createdTo}" id="createdTimeTo" style="width:140px" onclick="showDateTimePicker(this);">
							<s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss"/>
						</h:inputText>
					</td>
					<td><h:outputLabel value="商品条码/编码" /></td>
					<td colspan="2"><h:inputText value="#{doExceptionAction.exceptionDoFilter.barCode}" style="width:140px;" /></td>
					<!-- <td width="80"><h:outputLabel value="分拣筐编号" /></td>
                    <td colspan="2"><h:inputText value="#{doExceptionAction.exceptionDoFilter.sortContainerNo}" style="width:140px;" /></td> -->
				</tr>
				<tr>
					<td><h:outputLabel value="商户" /></td>
					<td colspan="3">
						<wms:selectBusinessCustomer id="businessCustomerId"
													value="#{doExceptionAction.customerName}"
													targetBean="#{doExceptionAction}" reRender="businessCustomer"
													onblur="return true;" actionMethod="receiveSelectBusinessCustomer"
													clearMethod="clearBusinessCustomer">
						</wms:selectBusinessCustomer>
					</td>
					<td><h:outputLabel value="波次号" /></td>
					<td ><h:inputText value="#{doExceptionAction.exceptionDoFilter.waveNo}" style="width:140px;" /></td>
				</tr>
			</table>
			<a4j:jsFunction action="#{doExceptionAction.decryptDoNo}" name="decryptDoNo" reRender="searchForm:doNo"/>
			<script>
				//<![CDATA[
				var searchForm = $("searchForm");
				var doNo = $("searchForm:doNo");
				var doType = $("searchForm:doType");
				var holdReason = $("searchForm:holdReason");

				function enterSubmit(event) {
					if(event.keyCode == 13){
						var doNoVal = jQuery("[id='searchForm:doNo']")[0].value;
						if(doNoVal.indexOf('.')<0){
							doType.activate();
						} else{
							decryptDoNo();
							setTimeout(
									function(){
										doType.activate();
									},200
							);
						}
					}
				}

				doType.observe("keydown", function(event){
					if(event.keyCode == 13){
						Event.stop(event);
						holdReason.activate();
					}
				});
				holdReason.observe("keydown", function(event){
					if(event.keyCode == 13){
						checkAndSubmit(event);
					}
				});
				function checkAndSubmit(event){
					$("searchForm:searchBtn").click();
				}
				function focusDefault(){
					doNo.activate();
				}
				function validateDatePic() {
					if((validateTimeInput("[id^='searchForm:frozenTime']", false)&& validateTimeInput("[id^='searchForm:createdTime']", true))) {
                        if (daxia.util.checkForm('searchForm', $("searchForm:searchBtn"))) {
                            return true;
                        }
					} else {
						alert('请输入正确的时间 / Please enter the correct time');
						return false;
					}
				}

				function validateTimeInput(inputId,reqiredFlag) {
					var returnFlag = true;
					var oControls = jQuery(inputId);
					jQuery.each(oControls,function() {
						clearMsg(this);
						var oControlValue = this.value+"";
						var patternStr = '';
						if (reqiredFlag) {
							patternStr = '^(((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-'+
									'(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})'+
									'(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)) '+
									'(([0|1][0-9])|2[0-4]):(([0-5][0-9])|60):(([0-5][0-9])|60))$';
						} else {
							patternStr = '^((\s*)|((((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-'+
									'(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})'+
									'(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)) '+
									'(([0|1][0-9])|2[0-4]):(([0-5][0-9])|60):(([0-5][0-9])|60))))$';
						}
						var pattern = new RegExp(patternStr);
						var patternFlag = pattern.test(oControlValue);
						if (! patternFlag) {
							ShowWarning(this, 0, "请输入正确的时间,格式为yyyy-MM-dd HH:mm:ss");
							returnFlag = false;
						}
					});
					return returnFlag;
				}


				function clearMsg(oControl) {
					oControl.style.backgroundColor = "";
					oControl.removeAttribute("validate");
				}

				focusDefault();
				//]]>
			</script>
		</c:if>
	</ui:define>
	<ui:define name="buttonTool">
		<ui:include src="/templates/include/toggleSearchPanel.xhtml" />
		<a4j:commandLink value="回 退" id="btnQuery1"
						 onclick="if(daxia.util.checkBox.checkSelectOneMore(selectForm.id)){batchRollBack();};return false;"
						 oncomplete="daxia.util.endProcess(this);" reRender="resultList"
						 rendered="#{identity.hasPermission('exception.rollBack.batchRollBack')}"/>
		<a4j:commandLink value="释 放" id="btnQuery2"
						 onclick="if(daxia.util.checkBox.checkSelectOneMore(selectForm.id)){releaseDO();};return false;"
						 oncomplete="daxia.util.endProcess(this);" reRender="resultList" rendered="#{identity.hasPermission('exception.send.edit')}"/>
		<a4j:commandLink value="按商品找缺货订单" id="btnQuery3"
						 onclick="showDoLackQuery();return false;"
						 rendered="#{identity.hasPermission('exception.send.skuQueryDo')}"/>
		<a4j:commandLink value="一键释放" id="btnQuery4"
						 onclick="releaseAllDos();return false;"
						 oncomplete="daxia.util.endProcess(this);" reRender="resultList"
						 rendered="#{identity.hasPermission('exception.release.batchReleaseAll')}"/>
	</ui:define>
	<ui:define name="content">
		<h:panelGroup id="resultArea">
			<a4j:form id="selectForm" style="margin: 5px 0 0; padding: 0; width: 100%;">
				<rich:panel id="resultList" styleClass="panelCla" bodyClass="panelBody" headerClass="panelHead">
					<s:div id="dataDiv" styleClass="tableContainer" rendered="#{doExceptionAction.dataPage.dataList.size() > 0}">
						<rich:dataTable value="#{doExceptionAction.queryResult}"
										var="item"
										rowKeyVar="row"
										style="width: 100%;"
										id="resultTable"
										styleClass="css-table">
							<h:column  style="width:25px">
								<f:facet name="header">
									<input type="checkbox" onclick="daxia.util.checkBox.checkAll(this,this.form.id)"/>
								</f:facet>
								<h:selectBooleanCheckbox
										value="#{doExceptionAction.selectMap[item.id]}" />
							</h:column>
							<h:column style="width: 100px;">
								<f:facet name="header">操作</f:facet>
								<h:panelGroup
										rendered="#{identity.hasPermission('exception.send.notify') and ('0' eq item.exceptionStatus) and ('02' eq item.holdCode)}">
									<a4j:commandLink onclick="confirmCallCS('#{item.status}','#{item.id}','#{item.holdCode}');return false;"  title="通知客服，客服决定订单流转">
										<i class="fa fa-user fa-fw"/>
										<h:outputText value="通知"/>
									</a4j:commandLink>
								</h:panelGroup>
								<h:panelGroup rendered="#{identity.hasPermission('exception.send.jumpNotify') and (('0' eq item.exceptionStatus) or ('5' eq item.exceptionStatus) )}">
									<a4j:commandLink onclick="skipCS('#{item.id}');return false;" title="跳过客服，仓库自行处理">
										<i class="fa fa-user-times fa-fw"/>
										<h:outputText value="跳过"/>
									</a4j:commandLink>
								</h:panelGroup>
								<h:panelGroup rendered="#{identity.hasPermission('exception.send.add') and doExceptionAction.canRollBack(item.status, item.exceptionStatus)}">
									<a4j:commandLink onclick="requestListRollbackStatus('#{item.id}');return false;" title="订单状态回退到初始化">
										<i class="fa fa-backward fa-fw"/>
										<h:outputText value="回退"/>
									</a4j:commandLink>
								</h:panelGroup>

								<h:panelGroup rendered="#{identity.hasPermission('exception.send.edit') and doExceptionAction.canRelease(item.doType, item.exceptionStatus, item.holdCode,item.needCancel, item.status)}">
									<a4j:commandLink onclick="requestListReleaseStatus('#{item.id}');return false;" title="释放订单">
										<i class="fa fa-unlock fa-fw"/>
										<h:outputText value="释放"/>
									</a4j:commandLink>
								</h:panelGroup>
								<h:panelGroup rendered="#{(doExceptionAction.initStatus eq item.status) and item.needCancel and identity.hasPermission('exception.send.remove')}" >
									<a4j:commandLink onclick="tryCancelDO('#{item.id}');return false;" title="取消订单">
										<i class="fa fa-remove fa-fw"/>
										<h:outputText value="取消"/>
									</a4j:commandLink>
								</h:panelGroup>
								<h:panelGroup rendered="#{(doExceptionAction.initStatus eq item.status or doExceptionAction.partStatus eq item.status) and !item.needCancel and identity.hasPermission('exception.send.manualAlloc')}" >
									<a4j:commandLink onclick="tryManualAllocDO('#{item.id}');return false;" title="人工分配">
										<i class="fa fa-hand-paper-o fa-fw"/>
										<h:outputText value="人工分配"/>
									</a4j:commandLink>
								</h:panelGroup>

								<h:panelGroup
										rendered="#{identity.hasPermission('exception.send.forcePick') and doExceptionAction.canForcePick(item.id, item.waveId, item.doType, item.exceptionStatus, item.lackStatus,item.needCancel)}">
									<a4j:commandLink onclick="onForcePick('#{item.id}');return false;" title="强制拣货，实发出库">
										<i class="fa fa-shopping-basket fa-fw"/>
										<h:outputText value="强制"/>
									</a4j:commandLink>
								</h:panelGroup>
								<h:panelGroup
										rendered="#{identity.hasPermission('exception.send.completLackPick') and doExceptionAction.canCompletLackPick(item.status, item.waveId, item.doType, item.exceptionStatus, item.lackStatus,item.needCancel)}">
									<a4j:commandLink onclick="onCompletLackPick('#{item.id}');return false;" title="缺发出库">
										<i class="fa fa-shopping-bag fa-fw"/>
										<h:outputText value="缺发"/>
									</a4j:commandLink>
								</h:panelGroup>
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputLabel value="订单号" />
								</f:facet>
								<a href="#" title="#{item.holdCode}" onclick="if(!daxia.layout.showModelDialog('doExceptionInfo.jsf?doHeaderId=#{item.id}', 800, 590)){checkAndSubmit();}return false;"><h:outputText value="#{item.doNo}" /></a>
							</h:column>
							<h:column>
								<f:facet name="header"><h:outputLabel value="波次号" /></f:facet>
								<h:outputText value="#{item.waveHeader.waveNo}" />
							</h:column>
							<h:column style="width:70px">
								<f:facet name="header"><h:outputLabel value="发货单类型" /></f:facet>
								<h:outputText value="#{item.doType}" >
									<fw:dict dictionary="ODO_TYPE" />
								</h:outputText>
							</h:column>
							<h:column style="width:30px">
								<f:facet name="header"><h:outputLabel value="冻结/释放" /></f:facet>
								<h:outputText value="#{item.releaseStatus}" >
									<fw:dict dictionary="RELEASE_STATUS" />
								</h:outputText>
							</h:column>
							<h:column style="width:100px">
								<f:facet name="header"><h:outputLabel value="异常状态" /></f:facet>
								<h:outputText value="#{item.exceptionStatus}"><fw:dict dictionary="EXCEPTION_STATUS" /></h:outputText>
							</h:column>
							<h:column style="width:100px">
								<f:facet name="header"><h:outputLabel value="订单状态" /></f:facet>
								<h:outputText value="#{item.status}"><fw:dict dictionary="DO_STATUS" /></h:outputText>
							</h:column>
							<h:column  style="width:80px">
								<f:facet name="header"><h:outputLabel value="创建时间" /></f:facet>
								<h:outputText value="#{item.createdAt}" ><s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss"/></h:outputText>
							</h:column>
							<h:column style="width:170px">
								<f:facet name="header"><h:outputLabel value="期望发货时间" /></f:facet>
								<h:outputText value="#{item.expectedArriveTime1}"><s:convertDateTime pattern="MM-dd HH:mm"/></h:outputText> 至  <h:outputText value="#{item.expectedArriveTime2}"><s:convertDateTime pattern="hh-dd HH:mm"/></h:outputText>
							</h:column>
							<h:column  style="width:80px">
								<f:facet name="header"><h:outputLabel value="冻结人" /></f:facet>
								<h:outputText value="#{item.holdWho}" />
							</h:column>
							<h:column  style="width:80px">
								<f:facet name="header"><h:outputLabel value="冻结时间" /></f:facet>
								<h:outputText value="#{item.holdTime}" ><s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss"/></h:outputText>
							</h:column>
							<h:column>
								<f:facet name="header"><h:outputLabel value="冻结原因" /></f:facet>
								<h:outputText value="#{item.holdCode}">
									<fw:dict dictionary="REASON_HDD"></fw:dict>
								</h:outputText>
							</h:column>
							<h:column>
								<f:facet name="header"><h:outputLabel value="初始冻结原因" /></f:facet>
								<h:outputText value="#{item.firstHoldCode}">
									<fw:dict dictionary="FIRST_REASON_HDD"></fw:dict>
								</h:outputText>
							</h:column>
							<h:column>
								<f:facet name="header"><h:outputLabel value="配送商" /></f:facet>
								<h:outputText value="#{item.carrier.distSuppCompName}" />
							</h:column>
						</rich:dataTable>
						<script>daxia.wms.util.resizeBody();</script>
					</s:div>
					<s:div styleClass="alignCenter" rendered="#{doExceptionAction.dataPage.noDataFound}">
						<h:graphicImage value="/res/images/no_result.png" style="border:0px" />
					</s:div>
					<fw:pagination listBean="#{doExceptionAction}"
								   action="query"
								   pageModel="#{doExceptionAction.dataPage}"
								   reRender="selectForm"
								   hookFunction="isCheck()">
					</fw:pagination>
					<a4j:jsFunction name="doReleaseDO" action="#{doExceptionAction.releaseDO}" oncomplete="onDoRequestRelease()"></a4j:jsFunction>
					<a4j:jsFunction name="doBatchRollBack" action="#{doExceptionAction.batchRollBack}" oncomplete="onDoBatchRollBack()"></a4j:jsFunction>
					<a4j:jsFunction name="doReleaseAllDos" action="#{doExceptionAction.releaseAllDos}" oncomplete="onDoQuery('释放成功!/Release successfully!')"></a4j:jsFunction>
					<a4j:jsFunction action="#{doExceptionAction.doCancelDO}" name="doCancelDO" oncomplete="onDoCancelDO()">
						<a4j:actionparam name="doId" assignTo="#{doExceptionAction.needCancelDoId}" />
					</a4j:jsFunction>
					<h:inputHidden value="#{doExceptionAction.needRollbackDoId}" id="needRollbackDoId"/>
					<h:inputHidden value="#{doExceptionAction.needReleaseDoId}" id="needReleaseDoId"/>
					<h:inputHidden value="#{doExceptionAction.forcePickDoId}" id="forcePickDoId"/>
					<a4j:jsFunction action="#{doExceptionAction.rollback}" name="doRequestRollback"
									reRender="statusAfterRollback" oncomplete="onDoRequestRollback()"/>
					<a4j:jsFunction action="#{doExceptionAction.release}" name="doRequestRelease"
									oncomplete="onDoRequestRelease()"/>
					<a4j:jsFunction action="#{doExceptionAction.skipCS}" name="doSkipCS"
									oncomplete="onSkipCS()" >
						<a4j:actionparam name="doId" assignTo="#{doExceptionAction.needSkipCSDoId}" />
					</a4j:jsFunction>
					<a4j:jsFunction action="#{doExceptionAction.doForcePick}" name="doForcePick" oncomplete="onCompleteForcePick()"/>
					<a4j:jsFunction action="#{doExceptionAction.completLackPick}" name="doCompletLackPick" oncomplete="afterCompletLackPick()">
						<a4j:actionparam name="forcePickDoId" assignTo="#{doExceptionAction.forcePickDoId}" />
					</a4j:jsFunction>
					<a4j:jsFunction name="showDoLackQuery" action="#{lackDoQueryAction.init()}"
									oncomplete="Richfaces.showModalPanel('doLackQueryPanel');focusBarcode();" reRender="lackDoQuery"/>
					<a4j:jsFunction name="hideDoLackQuery" action="#{lackDoQueryAction.init()}"
									oncomplete="hideQueryPanel();" reRender="lackDoQuery"/>
					<a4j:jsFunction name="doRePrintDo" action="#{doExceptionAction.rePrintDo()}" requestDelay="1" reRender="printDataDiv,printDoCfgDiv"  oncomplete="doLodopRePrint();">
						<a4j:actionparam name="doId" assignTo="#{doExceptionAction.doHeaderId}" />
					</a4j:jsFunction>
				</rich:panel>
				<script type="text/javascript">
					//<![CDATA[
					function isEmpty(str){
						if((str == null)||(str.length == 0)) {
							return true;
						} else {
							return false;
						}
					}

					function batchRollBack() {
						if(confirm("是否确认对发货单进行回退？/ Do you want to confirm the return of the delivery order?")) {
							doBatchRollBack();
						}
					}

					function releaseDO(){
						var lastSeletedDO = new Array();
						var allCheckBox = $$("#selectForm input[type=checkbox]");
						var hasCheckedItem = false;
						allCheckBox.each(function(el){
							if(el.checked){
								lastSeletedDO.push(el);
							}
						});
						if(lastSeletedDO.length == 0){
							alert("请选择需要释放的发货单 / Please select the invoice to be released");
							return;
						}
						if(confirm("是否确认对发货单进行冻结释放？/ Do you want to confirm the freeze release of the delivery order?")){
							doReleaseDO();
						} else{
							lastSeletedDO = null;
						}
					}

					function releaseAllDos() {
						if(confirm("是否确认释放所有待补货发货单？/ Do you want to confirm the release of all invoices to be replenished?")) {
							doReleaseAllDos();
						}
					}
					function skipCS(id){
						if(confirm("跳过客服后将不能再通知客服，您确定跳过吗？/ After skipping customer service, you will no longer be able to notify customer service. Are you sure you want to skip it?")){
							doSkipCS(id);
						}
					}

					//通知客服 非“拣、分、核”缺货订单提示建议分拣完成后,否则在拣货完成后
					function confirmCallCS(status, doId, holdCode) {
						var statusNum = parseInt(status);
						if (statusNum < 65) {
							if ('03'==holdCode || '04'==holdCode || '08' == holdCode) {
								if (statusNum < 60) {
									alert("请在拣货完成之后再通知客服 /Please notify customer service after picking is completed.");
								} else {
									daxia.layout.showModelDialog('callCS.jsf?doHeaderId='+doId, 390, 390);
								}
							} else {
								if (confirm("DO尚未分拣完成,建议分拣完成后通知,确定要现在通知客服? / DO has not completed sorting yet. It is recommended to notify customer service after sorting is completed. Are you sure you want to notify customer service now?")) {
									daxia.layout.showModelDialog('callCS.jsf?doHeaderId='+doId, 390, 390);
								}
							}
						} else {
							daxia.layout.showModelDialog('callCS.jsf?doHeaderId='+doId, 390, 390);
						}
					}

					function tryManualAllocDO(id) {
                        //关闭模态窗口 时候重新查询父页面
                        if(!daxia.layout.showModelDialog('/delivery/doAllocateDetail.jsf?isException=1&allocateHeaderId='+id, 1200, 600)){
//                            $("searchForm:btnQuery").click();
                        }
					}

					function requestListRollbackStatus(id){
						jQuery("input[id$='needRollbackDoId']").val(id);
						if(confirm("您确定要将状态回退到初始化 / Are you sure you want to rollback the state to initialization?")){
							doRequestRollback();
						}
					}

					function requestListReleaseStatus(id){
						jQuery("input[id$='needReleaseDoId']").val(id);
						if(confirm("您确定要释放该订单? / Are you sure you want to release the order?")){
							doRequestRelease();
						}
					}

					function onForcePick(id){
						jQuery("input[id$='forcePickDoId']").val(id);
						if(confirm("您确定要对该订单执行“强制拣货”吗？/ Are you sure you want to perform \"force picking\" on this order?")){
							doForcePick();
						}
					}

					function onCompletLackPick(id){
						jQuery("input[id$='forcePickDoId']").val(id);
						if(confirm("您确定要对该订单执行“缺货出库”吗？/ Are you sure you want to perform \"out of stock\" on this order?")){
							doCompletLackPick(id);
						}
					}

					function onCompleteForcePick(){
						if(isError()){
							return;
						}
						alert("发货单已强制拣货 / The delivery order has been forced to pick");
						clickQueryButton();
					}

					function afterCompletLackPick(){
						if(isError()){
							return;
						}
						alert("发货单已完成强制拣货 / The delivery order has completed forced picking");
						clickQueryButton();
					}

					function onDoRequestRelease(){
						if(isError()){
							return;
						}
						alert("发货单状态已释放 / The delivery order status has been released");
						clickQueryButton();
					}

					function onSkipCS(){
						if(isError()){
							return;
						}
						alert("发货单操作成功 / The delivery order operation successful");
						clickQueryButton();
					}

					function onDoRequestRollback(){//请求回滚到指定状态后的回调方法
						if(isError()){
							return;
						}
						alert("发货单状态已回退 / The delivery order status has been returned");
						clickQueryButton();
					}

					function onDoQuery(msg){
						if(isError()){
							return;
						}
						alert(msg);
						clickQueryButton();
					}

					function isError(){
						return jQuery(".notes-error").text().strip() != "";
					}

					function tryCancelDO(doId){
						if(!confirm("确认取消该发货单吗？/ Are you sure to cancel this delivery order?")){
							return;
						}
						if(! doId){
							return;
						}
						doCancelDO(doId);
					}

					function onDoCancelDO(){
						if(isError()){
							return;
						}
						alert("发货单已取消 / The delivery order has been canceled");
						clickQueryButton();
					}

					function onDoBatchRollBack() {
						if(isError()){
							return;
						}
						alert("发货单已回退 / The delivery order has been returned");
						clickQueryButton();
					}

					function clickQueryButton(){
						jQuery("input[id$='searchBtn']").click();
					}

					var viewDoHeaderLink = '${facesContext.externalContext.requestContextPath}/delivery/deliveryOrderDetailList.jsf';
					function viewDoHeader(doHeaderId){
						daxia.layout.showModelDialog(viewDoHeaderLink+'?view=1&doHeaderId='+doHeaderId, 1200, 800)
					}

					function hideQueryPanel(){
						#{rich:component('doLackQueryPanel')}.hide();
					}

					function focusBarcode(){
						jQuery("input[id$='barCode']").focus();
					}

					//]]>
				</script>
			</a4j:form>
			<s:div id="lackDoQuery">
				<rich:modalPanel id="doLackQueryPanel" width="600" height="560">
					<ui:include src="lackDoQuery.xhtml" />
				</rich:modalPanel>
			</s:div>
		</h:panelGroup>
		<ui:include src="/sound/soundRes.xhtml" />
	</ui:define>
</ui:composition>