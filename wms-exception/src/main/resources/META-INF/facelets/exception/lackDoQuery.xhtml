<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:c="http://java.sun.com/jstl/core"
	xmlns:s="http://jboss.com/products/seam/taglib"
	xmlns:a4j="http://richfaces.org/a4j"
	xmlns:rich="http://richfaces.org/rich"
	xmlns:fw="http://fw.daxia.com/ui" template="/templates/popup.xhtml">

	<ui:define name="popTitle">
		<h:outputText value="扫描商品条码找缺货订单" />
	</ui:define>
	<ui:define name="popBody">
		<link rel="stylesheet" type="text/css"
			href="${facesContext.externalContext.requestContextPath}/js/tab/jquery-tab.css" />
		<s:div id="bodyDiv2">
			<h2 class="box-header">SKU信息</h2>
			<div class="box-content">
				<a4j:form id="baseForm">
					<h:inputHidden id="isMultiProdFlag"
						value="#{lackDoQueryAction.isMultiProd}" />
					<table width="100%" class="info_table" border="0" cellSpacing="0"
						cellPadding="0">
						<tr>
							<td class="info_label font_4" width="13%">商品条码：</td>
							<td align="left" width="47%"><h:inputText id="barCode"
									value="#{lackDoQueryAction.barCode}"
									onkeydown="queryLackDo(this,event);" />
							</td>
							<td class="info_label wrap font_4" align="right" width="19%">商品编码：</td>
							<td class="info_value" id="productCode" align="left">#{lackDoQueryAction.sku.productCode}</td>
						</tr>
						<tr>
							<td class="info_label font_4" width="13%">商品名称：</td>
							<td class="info_value" width="47%"><h:outputText
									id="productName" value="#{lackDoQueryAction.sku.getProductCnameForI18n()}" />
							</td>
							<td class="info_label wrap font_4" align="right" width="19%">缺货数量汇总：</td>
							<td class="info_value" align="left"><font color="red"><h:outputText
										id="totalLack" value="#{lackDoQueryAction.skuLackQty}" /> </font></td>
						</tr>
					</table>
					<input type="text" style="display:none" />
					<script language="javascript">
					//<![CDATA[ 				
						function queryLackDo(barCode, event) {
							event = event || window.event;
							if(event.keyCode == 13){
				            	if (barCode.value.trim().length == 0) {
				    				alert("商品条码不允许为空！/ Product barcode is not allowed to be empty");
				    				return false;
				    			}
				            	query(barCode.value);
							}
							return false;
					  	}
					  	
				        function focusBarcode(){
				        	jQuery("input[id$='barCode']").focus();
				        }
				        
				        function processForMultiProd(){
							//如果是一码多品
							var isMultiProd=$("baseForm:isMultiProdFlag").value;
							if(isMultiProd=="true"){
								soundAlert("sound_qxz");
								var barcode=$("baseForm:barCode").value;
								showMultiProDiv(barcode);
								return;
							}
							return false;
						}

						function changePopTitle(){
							document.getElementById('baseForm:multiProPanelHeader').innerHTML = '存在一码多品，请选择商品';
						}

						//给关闭操作添加确认操作
						var cObject = jQuery("[id='doLackQueryPanelContentTable'] tr:nth-child(2) td:nth-child(1) div a");				
						cObject[0].onclick = null;
						cObject.click(function(){hideDoLackQuery();});
						
					//]]>
					</script>
					<script language="text/javascript">
				       	function soundAlert(flag){
				       		var el = $(flag);
				       		if(el &amp;&amp; el.play){
				       			el.play();
				       		}
				       	}
					</script>
					<a4j:jsFunction name="query"
						action="#{lackDoQueryAction.buttonQuery()}"
						reRender="lackForm,baseForm"
						oncomplete="focusBarcode();processForMultiProd();return false;">
						<a4j:actionparam name="barCode"
							assignTo="#{lackDoQueryAction.barCode}" />
					</a4j:jsFunction>
					<a4j:jsFunction name="continueQuery"
						action="#{lackDoQueryAction.continueQuery()}"
						reRender="lackForm,baseForm"
						oncomplete="focusBarcode();return false;">
						<a4j:actionparam name="skuId" assignTo="#{lackDoQueryAction.skuId}" />
					</a4j:jsFunction>
					<a4j:jsFunction name="showMultiProDiv"
						action="#{lackDoQueryAction.queryBarcodeListByScan()}"
						reRender="multiProDiv"
						oncomplete="changePopTitle();Richfaces.showModalPanel('multiProPanel');">
						<a4j:actionparam name="barCode"
							assignTo="#{lackDoQueryAction.barCode}" />
					</a4j:jsFunction>
			 		<a4j:jsFunction name="processSelectedProd"
			                action="#{lackDoQueryAction.continueQuery()}"
			                reRender="lackForm,baseForm" oncomplete="focusBarcode();">
			                <a4j:actionparam name="skuId" assignTo="#{lackDoQueryAction.skuId}" />
			        </a4j:jsFunction>
        			<s:div id="multiProDiv" rendered="#{lackDoQueryAction.isMultiProd}">
						<rich:modalPanel id="multiProPanel" width="400" height="400">
							<ui:include src="barcodePopup.xhtml" />
						</rich:modalPanel>
					</s:div>
				</a4j:form>
			</div>

			<div>
				<table width="100%">
					<tr>
						<td><rich:messages showDetail="true" showSummary="false"
								styleClass="message" id="errorMessage" />
						</td>
						<td align="right"><input
							onclick="if(parent.hideQueryPanel){hideDoLackQuery();}" type="button"
							value="返&nbsp;回" class="btnCla" />
						</td>
					</tr>
				</table>
			</div>

			<h2 class="box-header" style="margin-top: 10px;">缺货订单列表</h2>
			<div class="box-content" style="min-height: 300px;">
				<a4j:form id="lackForm">
					<s:div id="dataDiv" styleClass="tableContainer">
						<rich:dataTable value="#{lackDoQueryAction.dataPage.dataList}"
							rowClasses="listTableSingular,listTableDual" var="item"
							width="100%" rowKeyVar="row">
							<h:column>
								<f:facet name="header">
									<h:outputText value="订单编码" />
								</f:facet>
								<h:outputText value="#{item.doHeader.doNo}"
									rendered="#{lackDoQueryAction.dataPage.dataList.size() > 0}" />
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputText value="缺货数量" />
								</f:facet>
								<h:outputText value="#{item.qty}"
									rendered="#{lackDoQueryAction.dataPage.dataList.size() > 0}" />
							</h:column>
						</rich:dataTable>
						<script>
							daxia.wms.util.resizeDataDiv();
						</script>
					</s:div>
					<s:div styleClass="alignCenter"
						rendered="#{lackDoQueryAction.dataPage.noDataFound}">
						<h:graphicImage value="/res/images/no_result.png" style="border:0px" />
					</s:div>
					<fw:pagination listBean="#{lackDoQueryAction}" action="query"
						pageModel="#{lackDoQueryAction.dataPage}" reRender="lackForm"
						hookFunction="isCheck()">
					</fw:pagination>

				</a4j:form>
			</div>
		</s:div>
	</ui:define>
</ui:composition>