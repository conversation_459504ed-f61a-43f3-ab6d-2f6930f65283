<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:c="http://java.sun.com/jstl/core"
	xmlns:s="http://jboss.com/products/seam/taglib"
	xmlns:a4j="http://richfaces.org/a4j"
	xmlns:rich="http://richfaces.org/rich"
	xmlns:fw="http://fw.daxia.com/ui"
	template="/templates/dialog.xhtml">
	<ui:define name="title">
		<h:outputText
			value="订单[#{doExceptionInfoAction.deliveryOrderHeader.doNo}]异常详细信息" />
	</ui:define>
	<ui:define name="body">
		<link rel="stylesheet" type="text/css"
			href="${facesContext.externalContext.requestContextPath}/js/tab/jquery-tab.css" />
		<link rel="stylesheet" type="text/css"
			href="${facesContext.externalContext.requestContextPath}/res/css/addition.css" />
		<script type="text/javascript"
			src="${facesContext.externalContext.requestContextPath}/js/tab/jquery-tab.js"></script>
		<style>
.exception-log {
	list-style: decimal outside none;
	padding: 0px;
	margin: 0px;
}

.exception-log li {
	line-height: 25px;
	color: #000000;
	text-decoration: none;
	cursor: default;
	//display: inline-block;
}

.exception-log li:hover {
	background-color: #ECECEC;
}

.tabDiv {
	height: 260px;
	overflow: auto;
}

body {
	margin: 5px;
}
</style>
		<s:div id="contentDiv">
			<rich:messages id="richMessage" showDetail="true" showSummary="false" />
			<h2 class="box-header">订单信息</h2>
			<div class="box-content">
				<table width="80%" class="info_table" border="0" cellSpacing="0"
					cellPadding="0">
					<tr>
						<td class="info_label">订单号:</td>
						<td class="info_value">
							<a4j:commandLink rendered="#{identity.hasPermission('delivery.do.query')}"
                            	value="#{doExceptionInfoAction.deliveryOrderHeader.doNo}" onclick="qry(#{doExceptionInfoAction.doHeaderId});return false;" />
                        </td>
						<td class="info_label wrap">订单类型:</td>
						<td class="info_value"><h:outputText
								value="#{doExceptionInfoAction.deliveryOrderHeader.doType}">
								<fw:dict dictionary="ODO_TYPE" />
							</h:outputText></td>
						<td class="info_label wrap">异常状态:</td>
						<td class="info_value"><h:outputText
								value="#{doExceptionInfoAction.deliveryOrderHeader.exceptionStatus}">
								<fw:dict dictionary="EXCEPTION_STATUS" />
							</h:outputText></td>
						<td class="info_label wrap">所在波次:</td>
						<td class="info_value"><h:outputText
								value="#{doExceptionInfoAction.deliveryOrderHeader.waveHeader.waveNo}">
							</h:outputText></td>
					</tr>
					<tr>
						<td class="info_label">初始状态:</td>
						<td class="info_value"><h:outputText
								value="#{doExceptionInfoAction.firstExpLog.fmDoStatus}">
								<fw:dict dictionary="DO_STATUS" />
							</h:outputText></td>
						<td class="info_label wrap">即时状态:</td>
						<td class="info_value"><h:outputText
								value="#{doExceptionInfoAction.deliveryOrderHeader.status}">
								<fw:dict dictionary="DO_STATUS" />
							</h:outputText></td>
						<td class="info_label wrap">冻结原因:</td>
						<td class="info_value">#{doExceptionInfoAction.deliveryOrderHeader.holdReason}</td>
					</tr>
				</table>
				<div style="margin-top: 10px;">
					<!-- 通知客服 -->
					<h:panelGroup rendered="#{identity.hasPermission('exception.send.notify') and ('0' eq doExceptionInfoAction.deliveryOrderHeader.exceptionStatus)}">
						<input type="button" value="通知客服" class="btnCla" onclick="confirmCallCS('#{doExceptionInfoAction.deliveryOrderHeader.status}','#{doExceptionInfoAction.doHeaderId}');return false;"
						styleClass="btnCla"/>
					</h:panelGroup>
					<!-- 跳过客服 -->
					<h:panelGroup rendered="#{identity.hasPermission('exception.send.notify') and ('0' eq doExceptionInfoAction.deliveryOrderHeader.exceptionStatus)}">
						<input type="button" value="跳过客服" class="btnCla" onclick="skipCS('#{doExceptionInfoAction.doHeaderId}')" />
					</h:panelGroup>
					<!-- 状态回退 -->
					<h:panelGroup
						rendered="#{identity.hasPermission('exception.send.add') and doExceptionInfoAction.canRollBack()}">
						<input type="button" value="#{messages['状态回退']}" class="btnCla" onclick="rollback();return false;" />
					</h:panelGroup>
					<!-- 释放 -->
					<a4j:commandButton value="#{messages['释放']}"
						onclick="release();return false;" styleClass="btnCla"
						rendered="#{identity.hasPermission('exception.send.edit') and doExceptionInfoAction.canRelease()}" />
					<!-- 取消订单 -->
					<h:panelGroup
						rendered="#{(doExceptionInfoAction.initStatus eq doExceptionInfoAction.deliveryOrderHeader.status) 
						and (doExceptionInfoAction.deliveryOrderHeader.needCancel) and (identity.hasPermission('exception.send.remove'))}">
						<input type="button" value="取消订单" class="btnCla"
							style="height: 21px !important; line-height: 21px;"
							onclick="tryCancelDO('#{doExceptionInfoAction.doHeaderId}')" />
					</h:panelGroup>
					<!-- 强制拣货 -->
					<h:panelGroup
						rendered="#{identity.hasPermission('exception.send.forcePick') and doExceptionInfoAction.canForcePick() }">
						<input type="button" value="强制拣货" class="btnCla"
							style="height: 21px !important; line-height: 21px;"
							onclick="forcePick('#{doExceptionInfoAction.doHeaderId}');return false;" />
					</h:panelGroup>
				    
					<!-- 关闭 -->
					<a4j:commandButton value="#{messages['button.close']}"
						onclick="window.close();return false;" styleClass="btnCla" />
				</div>
			</div>
			<div height="10px"></div>
			<h2 class="box-header" style="margin-top: 10px;">异常跟踪</h2>
			<div class="box-content">
				<div id="tabs">
					<ul>
						<li><a id="logTapLink">异常日志</a></li>
						<li><a id="lackTapLink">缺货明细</a></li>
					</ul>
					<div class="tabDiv">
						<ul class="exception-log">
							<c:forEach items="#{doExceptionInfoAction.exceptionLogList}"
								var="exceptionLog" varStatus="status">
								<li >
									<h:outputText escape="false" value="#{status.index + 1}.#{exceptionLog}" />
								
								</li>
							</c:forEach>
						</ul>
					</div>
					<div class="tabDiv">
						<table cellspacing="0" cellpadding="0" border="0" width="100%"
							class="rich-table">
							<colgroup span="24"></colgroup>
							<thead class="rich-table-thead">
								<tr class="rich-table-subheader ">
									<th scope="col" class="rich-table-subheadercell  ">商品名</th>
									<th scope="col" class="rich-table-subheadercell  ">商品编码</th>
									<th scope="col" class="rich-table-subheadercell  ">商品条码</th>
									<th scope="col" class="rich-table-subheadercell  ">缺货数量</th>
									<th scope="col" class="rich-table-subheadercell  ">缺货库位</th>
									<th scope="col" class="rich-table-subheadercell  ">缺货原因</th>
								</tr>
							</thead>
							<tbody>
								<c:forEach items="#{doExceptionInfoAction.doLackDetails}"
									var="doLackDetail">
									<tr
										class="rich-table-row rich-table-firstrow listTableSingular">
										<td class="rich-table-cell">
											#{doLackDetail.sku.getProductCnameForI18n()}</td>
										<td class="rich-table-cell ">
											<!-- <a href="#"
											onclick="daxia.layout.showModelDialog('lackDolist.jsf?skuId=#{doLackDetail.sku.id}', 800, 650)">#{doLackDetail.sku.productCode}</a> -->
											<a href="#" onclick="showDoLackList(#{doLackDetail.sku.id})">#{doLackDetail.sku.productCode}</a>
										</td>
										<td class="rich-table-cell ">#{doLackDetail.sku.ean13}</td>
										<td class="rich-table-cell ">#{doLackDetail.qty}</td>
										<td class="rich-table-cell ">
											<h:outputText align="center" value="#{doLackDetail.locCodes}"  escape="false"  style="width:180px;">
												<fw:wordwrap delimiter="," numPerRow ="2"/>
											</h:outputText>
										</td>
										<c:if test="#{doLackDetail.holdReason eq '分拣缺货'}">
										   	<td class="rich-table-cell ">
  												<a href="#" onclick="showPickTask(#{doLackDetail.doDetailId})">#{doLackDetail.holdReason}</a>
  											</td>
										</c:if>
										<c:if test="#{doLackDetail.holdReason ne '分拣缺货'}">
											<td class="rich-table-cell ">#{doLackDetail.holdReason}</td>
										</c:if>
										
									</tr>
								</c:forEach>
							</tbody>
						</table>
					</div>
				</div>
			</div>
			<script>
			//<![CDATA[
				jQuery(document).ready(function() {
					jQuery('#tabs').tab();
				})
				
				function tryCancelDO(doId){
							if(!confirm("确认取消该发货单吗？/ Are you sure to cancel this delivery order?")){
								return;
							}
							if(! doId){
								return;
							}
							cancel();
						}
						
				function onDoCancelDO(){
							if(isError()){
								return;
							}
							alert("发货单已取消 / Delivery order has been canceled");
							clickQueryButton();
						}

				function confirmCallCS(status, doId){
					var statusNum = parseInt(status);
					if(statusNum < 65){
						if(confirm("DO尚未分拣完成,建议分拣完成后通知,确定要现在通知客服? / DO has not completed sorting yet. It is recommended to notify customer service after sorting is completed. Are you sure you want to notify customer service now?")){
							daxia.layout.showModelDialog('callCS.jsf?doHeaderId='+doId, 390, 390);
							location.reload();
						}
					} else{
						daxia.layout.showModelDialog('callCS.jsf?doHeaderId='+doId, 390, 390);
						location.reload();
					}
				}
				
				function skipCS(id){
					if(confirm("跳过客服后将不能再通知客服，您确定跳过吗？/ After skipping customer service, you will no longer be able to notify customer service. Are you sure you want to skip it?")){
						doSkipCS(id);
					}

					function onSkipCS(){
						if(isError()){
							return;
						}
						alert("发货单操作成功 / Delivery order operation successful");
						clickQueryButton();
					}
				}

				//add by heliang 2012-5-28
				function forcePick(id){
					if(confirm("您确定要对该订单执行强制拣货吗？/ Are you sure you want to perform forced picking on this order?")){
						doForcePick(id);
					}

					function onCompleForcePick(){
						if(isError()){
							return;
						}
						alert("强制拣货操作成功 / Forced picking operation successful");
						clickQueryButton();
					}
				}
		       function qry(id){
				   //关闭模态窗口 时候重新查询父页面
				   if(!daxia.layout.showModelDialog('${facesContext.externalContext.requestContextPath}/delivery/deliveryOrderDetailList.jsf?doHeaderId='+id+'&isHis=false', 1200, 800)){
					 	//query();
						jQuery("#selectForm\\:refreshId").click();
				   }
			  }
				//]]>
			</script>
		</s:div>
		<s:div id="doLackList">
			<rich:modalPanel id="doLackListPanel" width="600" height="560">
				<ui:include src="lackDolist.xhtml" />
			</rich:modalPanel>
		</s:div>
		<s:div id="sortLackList">
			<rich:modalPanel id="sortLackListPanel" width="800" height="300">
				<ui:include src="lackPickTaskList.xhtml" />
			</rich:modalPanel>
		</s:div>
		<a4j:form>
			<a4j:jsFunction name="showDoLackList" action="#{lackDoAction.init()}"
				oncomplete="Richfaces.showModalPanel('doLackListPanel');"
				reRender="doLackList">
				<a4j:actionparam name="skuId" assignTo="#{lackDoAction.skuId}" />
			</a4j:jsFunction>
			<a4j:jsFunction name="showPickTask" action="#{lackPickTaskAction.init()}"
				oncomplete="Richfaces.showModalPanel('sortLackListPanel');"
				reRender="sortLackList">
				<a4j:actionparam name="doDetailId" assignTo="#{lackPickTaskAction.doDetailId}" />
			</a4j:jsFunction>
			<a4j:jsFunction name="cancel"
				action="#{doExceptionInfoAction.cancel()}" reRender="contentDiv" />
			<a4j:jsFunction name="rollback"
				action="#{doExceptionInfoAction.rollback()}" reRender="contentDiv" />
			<a4j:jsFunction name="release"
				action="#{doExceptionInfoAction.release()}" reRender="contentDiv" />
			<a4j:jsFunction action="#{doExceptionInfoAction.skipCS}" name="doSkipCS" reRender="contentDiv" >
					<a4j:actionparam name="doId" assignTo="#{doExceptionInfoAction.doHeaderId}" />
			</a4j:jsFunction>
			<a4j:jsFunction action="#{doExceptionInfoAction.doForcePick}" name="doForcePick" reRender="contentDiv" 
			oncomplete="onCompleForcePick()">
					<a4j:actionparam name="doId" assignTo="#{doExceptionInfoAction.doHeaderId}" />
			</a4j:jsFunction>
		</a4j:form>
	</ui:define>
</ui:composition>