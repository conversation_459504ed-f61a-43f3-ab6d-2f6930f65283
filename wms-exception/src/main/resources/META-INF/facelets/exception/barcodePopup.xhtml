<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
	xmlns:ui="http://java.sun.com/jsf/facelets" 
	xmlns:h="http://java.sun.com/jsf/html" 
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:s="http://jboss.com/products/seam/taglib" 
	xmlns:a4j="http://richfaces.org/a4j" 
	xmlns:rich="http://richfaces.org/rich" 
	xmlns:fw="http://fw.daxia.com/ui" 
	template="/templates/popup.xhtml">
	<ui:define name="popTitle">
		<h:outputText value="存在一码多品，请选择商品" />
	</ui:define>
	<ui:define name="popBody">
		<h:form id="multiProForm">
        <rich:messages styleClass="messageCla" />
        <rich:panel id="resultList1" styleClass="panelCla" bodyClass="panelBody" headerClass="panelHead">
            <s:div id="dataDiv1" styleClass="tableContainer" 
                rendered="#{lackDoQueryAction.skuList.size() > 0}">
                <rich:dataTable value="#{lackDoQueryAction.skuList}"
                    rowClasses="listTableSingular,listTableDual"
                    width="100%"
                    var="item"
                    onRowDblClick="selectProduct('#{item.id}');" rowKeyVar="row">

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="商品编码" style="width:100px,background-color:#eeeeee"/>
                        </f:facet>
                        <h:outputText value="#{item.productCode}" />
                    </h:column>
                    
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="商品名称" style="width:100px,background-color:#eeeeee"/>
                        </f:facet>
                        <h:outputText value="#{item.productCname}"/>
                    </h:column>
                </rich:dataTable>
                <h:inputHidden id="skuId" />
            </s:div>
        </rich:panel>
        </h:form>
        <h:form>
            <rich:contextMenu attached="false" id="menu" submitMode="ajax">
                <rich:menuItem onclick="selectProduct('{itemId}');">
                    <h:outputText value="选择" />
                </rich:menuItem>
            </rich:contextMenu>
        </h:form>
        <script>
            function selectProduct(itemId) {
                processSelectedProd(itemId);
                #{rich:component('multiProPanel')}.hide();
            }
        </script>
	</ui:define>
</ui:composition>