<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:c="http://java.sun.com/jstl/core"
	xmlns:s="http://jboss.com/products/seam/taglib"
	xmlns:a4j="http://richfaces.org/a4j"
	xmlns:rich="http://richfaces.org/rich"
	xmlns:fw="http://fw.daxia.com/ui" template="/templates/main.xhtml">
	<ui:define name="title">
		<ui:include src="/templates/include/navigation.xhtml">
			<ui:param name="level_1_menu" value="异常管理" />
			<ui:param name="level_2_menu" value="缺货找货任务" />
		</ui:include>
	</ui:define>
	<ui:define name="buttonTool">
		<script>
		//<![CDATA[ 
			function validate() {
					var selectedRows = jQuery("input:checked[type='checkbox'].rowSelectClass")
					/* If nothing is selected */
					if(selectedRows.size() === 0) {
						alert("请选择要预览或打印的记录！/ Please select a record to preview or print");
						return false;
					}
					
					/* If select list contains record without task number */
					var emptyFlag = false;
					jQuery(selectedRows).each(function(i){
						if(jQuery(selectedRows[i]).parent().next().next().text() === "") {
							emptyFlag = true;
						}
					});
					
					if (emptyFlag) {
						alert("选中的记录中包含未生成任务的记录！/ The selected records include records for which tasks have not been generated!")
						return false;
					}
					return true;
				}
			//]]>
			</script>
		<ui:include src="/templates/include/toggleSearchPanel.xhtml" />
		<a4j:commandLink rendered="#{identity.hasPermission('exception.findLack.add')}"
			onclick="if(daxia.util.checkBox.checkSelectOneMore(selectForm.id)){createFindTask();};return false;" id="addFindLack">
            <i class="fa fa-plus-square fa-fw"/>
			<h:outputLabel value="#{messages['生成找货任务']}" />
		</a4j:commandLink>
		<a4j:commandLink rendered="#{identity.hasPermission('exception.findLack.print')}"
			onclick="if(!validate()) return false; doPrint(); return false;">
			<i class="fa fa-eye fa-fw"/>
			<h:outputLabel value="#{messages['打印']}" />
		</a4j:commandLink>
		<a4j:commandLink rendered="#{identity.hasPermission('exception.findLack.rePrint')}"
			onclick="if(!validate()) return false; doPreview(); return false;">
			<i class="fa fa-eye fa-fw"/>
			<h:outputLabel value="#{messages['预览']}" />
		</a4j:commandLink>
	</ui:define>
	<ui:define name="searchPanel">
		<table width="80%">
			<tr>
				<td>
					<h:outputLabel value="订单号" />
				</td>
				<td>
					<h:inputText id="doNo" value="#{findLackAction.findLackFilter.doNo}"  style="width:118px"/>
				</td>
				<td>
					<h:outputLabel value="找货任务号" />
				</td>
				<td>
					<h:inputText id="findLackHNo" value="#{findLackAction.findLackFilter.findLackHNo}"  style="width:118px"/>
				</td>
				<td>
					<h:outputLabel value="初始冻结原因" />
				</td>
				<td>
					<h:selectOneMenu id="fristHoldReason" value="#{findLackAction.findLackFilter.firstHoldCode}" style="width:118px">
						<f:selectItem itemLabel="#{messages['global.title.pleaseChoice']}" />
						<f:selectItems value="#{findLackAction.findFirstHoldReasons()}" />
                    </h:selectOneMenu>
				</td>
				<td>
					<h:outputLabel value="任务生成状态" />
				</td>
				<td>	
					<h:selectOneMenu id="findLackStatus" value="#{findLackAction.findLackFilter.findLackStatus}" style="width:118px">
						<f:selectItem itemLabel="#{messages['global.title.pleaseChoice']}" />
                        <f:selectItems value="#{dictionary.getSelectItems('FIND_LACK_CREATE_STATUS')}" />
                    </h:selectOneMenu>
				</td>
				<td>
					<h:outputLabel value="标签打印状态" />
				</td>
				<td>	
					<h:selectOneMenu id="printStatus" value="#{findLackAction.findLackFilter.printStatus}">
						<f:selectItem itemLabel="#{messages['global.title.pleaseChoice']}" />
						<f:selectItems value="#{dictionary.getSelectItems('FIND_LACK_PRINT_STATUS')}" />
                    </h:selectOneMenu>
				</td>
			</tr>
			<tr>
				<td>
					<h:outputLabel value="预计出库时间FM" />
				</td>
				<td>
					<h:inputText style="width:118px" id="doPlanShipTimeFrom"
                        value="#{findLackAction.findLackFilter.doPlanShipTimeFrom}" onclick="showDateTimePicker(this)">
                        <s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" />
                    </h:inputText>
				</td>
				<td>
					<h:outputLabel value="预计出库时间TO" />
				</td>
				<td>
					<h:inputText style="width:118px" id="doPlanShipTimeTo"
                        value="#{findLackAction.findLackFilter.doPlanShipTimeTo}" onclick="showDateTimePicker(this)">
                        <s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" />
                    </h:inputText>
				</td>
				<td>
					<h:outputLabel value="当前冻结原因" />
				</td>
				<td>
					<h:selectOneMenu id="holdReason" value="#{findLackAction.findLackFilter.holdCode}" style="width:118px">
						<f:selectItem itemLabel="#{messages['global.title.pleaseChoice']}" />
						<f:selectItems value="#{findLackAction.exceptionStatusSelect}" />
                    </h:selectOneMenu>
				</td>
				<td>
					<h:outputLabel value="冻结时间FM" />
				</td>
				<td>
					<h:inputText style="width:118px" id="holdTimeFm"
                        value="#{findLackAction.findLackFilter.holdTimeFm}" onclick="showDateTimePicker(this)">
                        <s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" />
                    </h:inputText>
				</td>
				<td>
					<h:outputLabel value="冻结时间TO" />
				</td>
				<td>
					<h:inputText style="width:118px" id="holdTimeTo"
                        value="#{findLackAction.findLackFilter.holdTimeTo}" onclick="showDateTimePicker(this)">
                        <s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" />
                    </h:inputText>
				</td>
                <td colspan="2">
					<a4j:commandButton value="#{messages['button.query']}"
						id="btnQuery" action="#{findLackAction.buttonQuery()}"
						onclick="if(!daxia.util.checkForm(this.form,this)){return false;} daxia.util.startProcess(this)"
						oncomplete="daxia.util.endProcess(this);" styleClass="btnCla"
						reRender="resultList"
						rendered="#{identity.hasPermission('exception.findLack.query')}">
					</a4j:commandButton>
				</td>
			</tr>
			<tr>
				<td>
					<h:outputLabel value="缺货库区" />
				</td>
				<td colspan="9">
					<h:inputText style="width:350px" id="lackPartition" value="#{findLackAction.findLackFilter.lackPartition}"/>
				</td>
			</tr>
		</table>
	</ui:define>
	<ui:define name="content">
		<a4j:form id="selectForm">
			<a4j:jsFunction name="doPrint" action="#{findLackAction.print()}"
				requestDelay="1" reRender="printDataDiv,lackDataTable" oncomplete="doLodopPrint()" /> 
			<a4j:jsFunction name="doPreview" action="#{findLackAction.preview()}"
				requestDelay="1" reRender="printDataDiv,lackDataTable"
				oncomplete="doLodopPreview()" />
			<rich:panel id="resultList" styleClass="panelCla"
				bodyClass="panelBody" headerClass="panelHead">
				<h:panelGroup id="countInfo"
					rendered="#{findLackAction.dataPage.dataList.size() > 0}">
					<rich:spacer width="10" />
					<h:outputText value="订单数" style="font-size:15px" />
					<rich:spacer width="10" />
					<h:outputText styleClass="countSpan"
						value="#{findLackAction.totalDoCount}" id="totalDo"
						style="color:red;font-size:20px" />
					<rich:spacer width="10" />
					<h:outputText value="缺货Sku数" style="font-size:15px" />
					<rich:spacer width="10" />
					<h:outputText styleClass="totalSpan"
						value="#{findLackAction.totalSkuCount}" id="totalSku"
						style="color:red;font-size:20px" />
					<rich:spacer width="10" />
					<h:outputText value="缺货units数" style="font-size:15px" />
					<rich:spacer width="10" />
					<h:outputText styleClass="totalSpan"
						value="#{findLackAction.totalUnitsCount}" id="totalUnits"
						style="color:red;font-size:20px" />
				</h:panelGroup>
				<s:div id="dataDiv" styleClass="tableContainer"
					rendered="#{findLackAction.dataPage.dataList.size() > 0}">
					<rich:dataTable value="#{findLackAction.dataPage.dataList}"
						rowClasses="listTableSingular,listTableDual" var="item"
						width="100%" rowKeyVar="row" id="lackDataTable">
						<h:column headerClass="listTableHeadLeft">
							<f:facet name="header">
								<input type="checkbox" align="left" id="selectAll"
									onclick="daxia.util.checkBox.checkAll(this,this.form.id);buildContInfo();" />
							</f:facet>
							<h:selectBooleanCheckbox styleClass="rowSelectClass" onclick="buildContInfo();"
								value="#{findLackAction.selectedMap[item]}" rendered="#{item.lackHstatus ne 2}" />
						</h:column>
						<h:column>
							<f:facet name="header">
								<h:outputText value="订单号" />
							</f:facet>
							<h:outputText value="#{item.doNo}"
								rendered="#{!identity.hasPermission('delivery.do.query')}" />
							<a4j:commandLink
								rendered="#{identity.hasPermission('exception.findLack.query')}"
								value="#{item.doNo}" onclick="qry(#{item.doId});return false;" />
						</h:column>
						<h:column>
							<f:facet name="header">
								<h:outputText value="找货任务号" />
							</f:facet>
							<h:outputText value="#{item.findLackHNo}" />
						</h:column>
						<h:column>
							<f:facet name="header">
								<h:outputText value="任务状态" />
							</f:facet>
							<h:outputText value="#{item.lackHstatus}">
								<fw:dict dictionary="FIND_LACK_CREATE_STATUS" />
							</h:outputText>
						</h:column>
						<h:column>
							<f:facet name="header">
							<h:outputLabel value="初始冻结原因" />
							</f:facet>
							<h:outputText value="#{item.firstHoldCode}">
							     <fw:dict dictionary="FIRST_REASON_HDD"></fw:dict>
							</h:outputText>
						</h:column>	
						<h:column>
							<f:facet name="header">
								<h:outputText value="当前冻结原因" />
							</f:facet>
							<h:outputText value="#{item.holdReason}" />
						</h:column>
						<h:column>
							<f:facet name="header">
								<h:outputText value="预计出库时间" />
							</f:facet>
							<h:outputText value="#{item.planShipTime}"/>
						</h:column>
						<h:column>
							<f:facet name="header">
								<h:outputText value="订单状态" />
							</f:facet>
							<h:outputText value="#{item.doStatus}">
								<fw:dict dictionary="DO_STATUS" />
							</h:outputText>
						</h:column>
						<h:column>
							<f:facet name="header">
								<h:outputText value="缺货总数" />
							</f:facet>
							<h:outputText value="#{item.lackQty}" />
						</h:column>
						<h:column>
							<f:facet name="header">
								<h:outputText value="缺货库区" />
							</f:facet>
							<h:outputText value="#{item.lackPartition}" />
						</h:column>
						<h:column>
							<f:facet name="header">
								<h:outputText value="冻结时间" />
							</f:facet>
							<h:outputText value="#{item.holdTime}">
								<s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" />
							</h:outputText>
						</h:column>
						<h:column>
							<f:facet name="header">
								<h:outputText value="冻结人" />
							</f:facet>
							<h:outputText value="#{item.holdWho}" />
						</h:column>
						<h:column>
							<f:facet name="header">
								<h:outputText value="任务生成时间" />
							</f:facet>
							<h:outputText value="#{item.lackCreateTime}">
								<s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" />
							</h:outputText>
						</h:column>
						<h:column>
							<f:facet name="header">
								<h:outputText value="找货任务生成人" />
							</f:facet>
							<h:outputText value="#{item.lackCreater}" />
						</h:column>
						<h:column>
							<f:facet name="header">
								<h:outputText value="标签打印状态" />
							</f:facet>
							<h:outputText value="未打印"
								rendered="#{item.pringStatus eq null or item.pringStatus eq '0'}" />
							<h:outputText value="已打印" rendered="#{item.pringStatus eq '1'}" />
						</h:column>
					</rich:dataTable>
					<script>daxia.wms.util.resizeDataDiv();</script>
				</s:div>
				<s:div styleClass="alignCenter"
					rendered="#{findLackAction.dataPage.noDataFound}">
					<h:graphicImage value="/res/images/no_result.png" style="border:0px" />
				</s:div>
				<fw:pagination listBean="#{findLackAction}" action="query"
					pageModel="#{findLackAction.dataPage}" reRender="dataDiv"
					hookFunction="isCheck()">
				</fw:pagination>
			</rich:panel>
			<a4j:jsFunction name="buildContInfo"
				action="#{findLackAction.buildSelectInfo()}" reRender="countInfo">
			</a4j:jsFunction>
			<a4j:jsFunction name="createFindTask"
				action="#{findLackAction.createLack()}" reRender="dataDiv,countInfo" />
			<s:div id="printDataDiv">
				<script>
		    	var printData = <h:outputText value="#{findLackAction.printData}" escape="false"/>;            
		    </script>
			</s:div>
			<script>
                var lodopPrint = new LodopPrint({
                    'reportName': '缺货找货任务标签',
                    'useMirrorImage': true,
                    'pageWidth': '90mm',
                    'pageHeight': '50mm'
                },printData);
                //<![CDATA[
                function doLodopPreview(){
                    lodopPrint.setPrintData(printData);
                    lodopPrint.preview();
                }
                function doLodopPrint(){
                    lodopPrint.setPrintData(printData);
                    lodopPrint.print();
                    updatePrintStatus();
                }
                //]]>
            </script>
		</a4j:form>
		<!-- 控制模式对话框的参数 -->
		<s:div id="statusDiv" style="display:none">
			<h:inputText value="#{operationStatusWrap.operationStatus}" id="operationStatus" />
		</s:div>
		<script>
			//<![CDATA[
			    function qry(doId) {
			    	daxia.layout.showModelDialog('doExceptionInfo.jsf?doHeaderId='+doId, 1200, 600)
			    }
			//]]>
		</script>
	</ui:define>
</ui:composition>