<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.idanchuang</groupId>
        <artifactId>spring-cloud-starter-parent</artifactId>
        <!-- 定期升级组件喔, 最新版本见: https://git.acg.team/arch/spring-cloud-parent-all -->
        <!-- 升级版本号后 IDEA提示爆红不要紧张(只是显示bug，重启IDEA后就不红了)，可以在左侧 External Libraries 中看到目前组件的版本是否已经更新 -->
        <version>3.0.6</version>
        <relativePath/>
    </parent>
    <groupId>com.accesscorporate.app</groupId>
    <artifactId>wms-server</artifactId>
    <version>${revision}</version>

    <properties>
        <!-- 更新协议包版本时只需要修改此变量并deploy即可 -->
        <revision>0.0.1-SNAPSHOT</revision>
        <app.wms.server.version>${revision}</app.wms.server.version>
        <easyexcel.version>0.0.1-release</easyexcel.version>
        <druid.version>1.2.6</druid.version>

        <sso.external.client.version>1.3.5-RELEASE</sso.external.client.version>
        <sso.client.core.version>2.2.8-RELEASE</sso.client.core.version>
        <address.client.version>1.0.10-RELEASE</address.client.version>
        <abmau.service.open.warehouse.api>1.5.4-SNAPSHOT</abmau.service.open.warehouse.api>
        <hutool.all.version>5.8.24</hutool.all.version>
<!--        <jsqlparser.version>4.6</jsqlparser.version>-->
        <base.mq.version>1.1.0-RELEASE</base.mq.version>
        <excel.component.version>2.0.1</excel.component.version>

    </properties>

    <modules>
        <module>wms-server-api</module>
        <module>wms-server-biz</module>
        <module>wms-server-common</module>
        <module>wms-server-web</module>
        <module>wms-server-dal</module>
        <module>wms-server-integration</module>
    </modules>

    <!-- 不要在父pom里面添加任何非 provided 的依赖哦! (因为会对 api 模块添加负担) -->
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.idanchuang.scm</groupId>
                <artifactId>excel-component</artifactId>
                <version>${excel.component.version}</version>
            </dependency>
            <!--mq监听组件-->
            <dependency>
                <groupId>com.idanchuang.mqlistener</groupId>
                <artifactId>danchuang-base-mq</artifactId>
                <version>${base.mq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.accesscorporate.app</groupId>
                <artifactId>wms-server-api</artifactId>
                <version>${app.wms.server.version}</version>
            </dependency>
            <dependency>
                <groupId>com.accesscorporate.app</groupId>
                <artifactId>wms-server-common</artifactId>
                <version>${app.wms.server.version}</version>
            </dependency>
            <dependency>
                <groupId>com.accesscorporate.app</groupId>
                <artifactId>wms-server-dal</artifactId>
                <version>${app.wms.server.version}</version>
            </dependency>
            <dependency>
                <groupId>com.accesscorporate.app</groupId>
                <artifactId>wms-server-biz</artifactId>
                <version>${app.wms.server.version}</version>
            </dependency>
            <dependency>
                <groupId>com.accesscorporate.app</groupId>
                <artifactId>wms-server-web</artifactId>
                <version>${app.wms.server.version}</version>
            </dependency>
            <dependency>
                <groupId>com.accesscorporate.app</groupId>
                <artifactId>wms-server-integration</artifactId>
                <version>${app.wms.server.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dc-easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.idanchuang</groupId>
                <artifactId>sso-external-client</artifactId>
                <version>${sso.external.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.idanchuang</groupId>
                <artifactId>sso-client-core</artifactId>
                <version>${sso.client.core.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.all.version}</version>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.github.jsqlparser</groupId>-->
<!--                <artifactId>jsqlparser</artifactId>-->
<!--                <version>${jsqlparser.version}</version>-->
<!--            </dependency>-->
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>