# DocDoHeader 发货单头表 - MyBatis Plus 完整代码

本文档描述了根据 `doc_do_header` 表DDL生成的完整MyBatis Plus代码结构。

## 代码结构

### 1. 实体类 (Entity)
**文件位置**: `wms-server-dal/src/main/java/com/accesscorporate/app/wms/server/dal/entity/DocDoHeader.java`

- 继承 `WhBaseEntity`，包含仓库ID和基础字段
- 使用 `@TableName("doc_do_header")` 指定表名
- 使用 `@TableField` 注解映射数据库字段
- 使用 `@TableLogic` 实现逻辑删除
- 包含DDL中定义的所有字段

### 2. Mapper接口 (Data Access Layer)
**文件位置**: `wms-server-dal/src/main/java/com/accesscorporate/app/wms/server/dal/mapper/DocDoHeaderMapper.java`

- 继承 `BaseMapper<DocDoHeader>`
- 使用 `@Mapper` 注解
- 可扩展自定义查询方法

**XML配置**: `wms-server-dal/src/main/resources/mapper/DocDoHeaderMapper.xml`
- 包含常用的复杂查询SQL
- 支持条件查询、统计、批量更新等操作

### 3. Manager类 (数据库操作层)
**文件位置**: `wms-server-biz/src/main/java/com/accesscorporate/app/wms/server/biz/manager/impl/DocDoHeaderManager.java`

- 继承 `ServiceImpl<DocDoHeaderMapper, DocDoHeader>`
- 提供基础的CRUD操作
- 可扩展复杂的数据库操作逻辑

### 4. DTO类 (数据传输对象)

#### 保存请求类
**文件位置**: `wms-server-biz/src/main/java/com/accesscorporate/app/wms/server/biz/params/request/DocDoHeaderSaveRequest.java`
- 包含新增/修改发货单的字段
- 使用 `@NotBlank` 等验证注解
- 使用 `@Schema` 注解生成API文档

#### 响应类
**文件位置**: `wms-server-biz/src/main/java/com/accesscorporate/app/wms/server/biz/params/response/DocDoHeaderResponse.java`
- 包含查询返回的所有字段
- 包含基础实体的字段（创建时间、更新时间等）

#### 分页查询请求类
**文件位置**: `wms-server-biz/src/main/java/com/accesscorporate/app/wms/server/biz/params/request/DocDoHeaderPageQueryRequest.java`
- 继承 `PageDTO` 支持分页
- 包含常用的查询条件字段
- 支持时间范围查询

### 5. MapStruct转换器
**文件位置**: `wms-server-biz/src/main/java/com/accesscorporate/app/wms/server/biz/converter/DocDoHeaderMapStruct.java`

- 实现实体类与DTO之间的转换
- 使用 `@Mapping` 注解处理字段映射
- 自动忽略不需要转换的字段

### 6. Service层

#### Service接口
**文件位置**: `wms-server-biz/src/main/java/com/accesscorporate/app/wms/server/biz/service/DocDoHeaderService.java`
- 继承 `IService<DocDoHeader>`
- 定义业务方法接口

#### Service实现类
**文件位置**: `wms-server-biz/src/main/java/com/accesscorporate/app/wms/server/biz/service/impl/DocDoHeaderServiceImpl.java`
- 实现业务逻辑
- 集成仓库级别的数据隔离
- 提供丰富的查询方法

### 7. Controller层
**文件位置**: `wms-server-web/src/main/java/com/accesscorporate/app/wms/server/web/data/DocDoHeaderController.java`

- 提供完整的REST API接口
- 使用 `@Operation` 注解生成API文档
- 支持CRUD操作和多种查询方式

## API接口说明

### 基础CRUD操作
- `GET /api/doc-do-header/list` - 查询所有发货单
- `GET /api/doc-do-header/{id}` - 根据ID查询发货单
- `POST /api/doc-do-header` - 新增发货单
- `DELETE /api/doc-do-header/{id}` - 删除发货单
- `POST /api/doc-do-header/page` - 分页查询发货单

### 专用查询接口
- `GET /api/doc-do-header/by-do-no/{doNo}` - 根据发货单号查询
- `GET /api/doc-do-header/by-orig-id/{origId}` - 根据原始ID查询
- `GET /api/doc-do-header/by-tracking-no/{trackingNo}` - 根据运单号查询
- `GET /api/doc-do-header/by-mobile/{mobile}` - 根据手机号查询
- `GET /api/doc-do-header/by-wave-id/{waveId}` - 根据波次ID查询
- `GET /api/doc-do-header/by-status/{status}` - 根据状态查询
- `GET /api/doc-do-header/by-do-type/{doType}` - 根据订单类型查询

## 使用示例

### 1. 新增发货单
```java
@Autowired
private DocDoHeaderService docDoHeaderService;

DocDoHeaderSaveRequest request = new DocDoHeaderSaveRequest();
request.setDoNo("DO202501070001");
request.setStatus("00");
request.setDoType("1");
request.setConsigneeName("张三");
request.setMobile("13800138000");
// ... 设置其他字段

boolean result = docDoHeaderService.save(request);
```

### 2. 分页查询
```java
DocDoHeaderPageQueryRequest request = new DocDoHeaderPageQueryRequest();
request.setCurrent(1);
request.setSize(10);
request.setStatus("00");
request.setDoType("1");

Page<DocDoHeaderResponse> page = docDoHeaderService.page(request);
```

### 3. 根据发货单号查询
```java
DocDoHeader docDoHeader = docDoHeaderService.queryByDoNo("DO202501070001");
```

## 特性说明

1. **仓库级别数据隔离**: 所有查询都会自动加上当前仓库ID的条件
2. **逻辑删除**: 使用 `is_deleted` 字段实现逻辑删除
3. **自动填充**: 创建时间、更新时间等字段自动填充
4. **参数验证**: 使用Jakarta Validation进行参数校验
5. **API文档**: 使用Swagger注解自动生成API文档
6. **类型转换**: 使用MapStruct实现高效的对象转换

## 测试

测试文件位置: `wms-server-biz/src/test/java/com/accesscorporate/app/wms/server/biz/service/DocDoHeaderServiceTest.java`

包含以下测试用例：
- 保存和查询测试
- 分页查询测试
- 各种条件查询测试

## 注意事项

1. **字段冲突**: 原DDL中存在 `ordersource` 和 `order_source` 两个字段，已重命名为 `orderSourceOld` 和 `orderSource` 避免冲突
2. **用户上下文**: 需要确保在调用Service方法前设置好用户上下文（仓库ID等）
3. **事务管理**: 写操作已添加 `@Transactional` 注解
4. **异常处理**: 建议在Controller层添加统一的异常处理

## 扩展建议

1. 可以在Mapper XML中添加更多复杂查询
2. 可以在Service中添加业务相关的方法
3. 可以添加缓存支持提高查询性能
4. 可以添加审计日志记录数据变更
