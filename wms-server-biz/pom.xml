<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>wms-server</artifactId>
        <groupId>com.accesscorporate.app</groupId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>wms-server-biz</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.idanchuang.scm</groupId>
            <artifactId>excel-component</artifactId>
        </dependency>
        <!-- 实现api协议包 -->
        <dependency>
            <groupId>com.accesscorporate.app</groupId>
            <artifactId>wms-server-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.accesscorporate.app</groupId>
            <artifactId>wms-server-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.accesscorporate.app</groupId>
            <artifactId>wms-server-dal</artifactId>
        </dependency>
        <dependency>
            <groupId>com.accesscorporate.app</groupId>
            <artifactId>wms-server-integration</artifactId>
        </dependency>
        <dependency>
            <!-- (SpringCloud服务提供者强制依赖)提供者组件, 组件文档: http://git.acg.team/arch/spring-cloud-parent-all/tree/master/components/component-provider -->
            <groupId>com.idanchuang.component</groupId>
            <artifactId>component-provider</artifactId>
        </dependency>

        <dependency>
            <!-- redis组件使用说明: http://git.acg.team/arch/spring-cloud-parent-all/tree/master/components/component-redis -->
            <groupId>com.idanchuang.component</groupId>
            <artifactId>component-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.idanchuang.component</groupId>
            <artifactId>component-config-apollo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.idanchuang.component</groupId>
            <artifactId>component-xxl-job</artifactId>
        </dependency>

        <!-- Spring Context Support for RMI -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>5.2.2.RELEASE</version>
        </dependency>

        <!--        <dependency>-->
        <!--            &lt;!&ndash; RabbitMQ组件说明: http://git.acg.team/arch/spring-cloud-parent-all/tree/master/components/component-mq-amqp &ndash;&gt;-->
        <!--            <groupId>com.idanchuang.component</groupId>-->
        <!--            <artifactId>component-mq-amqp</artifactId>-->
        <!--        </dependency>-->

        <!-- 更多组件请见: http://git.acg.team/arch/spring-cloud-parent-all -->
        <!-- 完整DEMO项目(包含所有组件)请见: http://git.acg.team/arch/example-spring-cloud -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- deploy 时排除此模块 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <!--开启filtering功能  -->
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>