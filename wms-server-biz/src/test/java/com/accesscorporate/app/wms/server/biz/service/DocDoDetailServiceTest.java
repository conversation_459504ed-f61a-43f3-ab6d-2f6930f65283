package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.request.DocDoDetailPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.request.DocDoDetailSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocDoDetailResponse;
import com.accesscorporate.app.wms.server.common.context.UserContext;
import com.accesscorporate.app.wms.server.dal.entity.DocDoDetail;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 发货单明细表Service测试类
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class DocDoDetailServiceTest {

    @Resource
    private DocDoDetailService docDoDetailService;

    @BeforeEach
    void setUp() {
        // 设置测试用户上下文
        UserContext.setUserId(1L);
        UserContext.setCurrentWarehouseId(1L);
        UserContext.setTenantId(1L);
    }

    @AfterEach
    void tearDown() {
        // 清理用户上下文
        UserContext.removeAll();
    }

    @Test
    void testSaveAndGet() {
        // 创建测试数据
        DocDoDetailSaveRequest request = new DocDoDetailSaveRequest();
        request.setDoHeaderId(1L);
        request.setLinestatus("00");
        request.setSkuId(1001L);
        request.setExpectedQty(BigDecimal.valueOf(10));
        request.setExpectedQtyEach(BigDecimal.valueOf(10));
        request.setPackageId(1L);
        request.setPackDetailId(1L);
        request.setUom("PCS");
        request.setLotatt04("SUP001"); // 供应商ID
        request.setLotatt05("LOT001"); // 批号
        request.setLotatt06("OWNER001"); // 货主ID
        request.setPrice(BigDecimal.valueOf(100.00));

        // 保存
        boolean saveResult = docDoDetailService.save(request);
        assertTrue(saveResult, "保存应该成功");

        // 查询
        List<DocDoDetail> details = docDoDetailService.queryByDoHeaderId(request.getDoHeaderId());
        assertNotNull(details, "应该能查询到保存的数据");
        assertFalse(details.isEmpty(), "明细列表不应该为空");

        DocDoDetail saved = details.get(0);
        assertEquals(request.getDoHeaderId(), saved.getDoHeaderId(), "订单头ID应该一致");
        assertEquals(request.getSkuId(), saved.getSkuId(), "商品ID应该一致");

        // 根据ID查询
        DocDoDetailResponse response = docDoDetailService.get(saved.getId());
        assertNotNull(response, "应该能根据ID查询到数据");
        assertEquals(saved.getSkuId(), response.getSkuId(), "商品ID应该一致");
    }

    @Test
    void testPageQuery() {
        // 创建分页查询请求
        DocDoDetailPageQueryRequest request = new DocDoDetailPageQueryRequest();
        request.setCurrent(1);
        request.setSize(10);
        request.setLinestatus("00");

        // 执行分页查询
        Page<DocDoDetailResponse> page = docDoDetailService.page(request);
        assertNotNull(page, "分页结果不应该为空");
        assertTrue(page.getSize() > 0, "页面大小应该大于0");
        log.info("分页查询结果：总数={}, 当前页={}, 页面大小={}", 
                page.getTotal(), page.getCurrent(), page.getSize());
    }

    @Test
    void testListAll() {
        // 查询所有数据
        List<DocDoDetailResponse> list = docDoDetailService.listAll();
        assertNotNull(list, "查询结果不应该为空");
        log.info("查询到 {} 条发货单明细数据", list.size());
    }

    @Test
    void testQueryBySkuId() {
        // 根据商品ID查询
        List<DocDoDetail> list = docDoDetailService.queryBySkuId(1001L);
        assertNotNull(list, "查询结果不应该为空");
        log.info("根据商品ID查询到 {} 条数据", list.size());
    }

    @Test
    void testQueryByLinestatus() {
        // 根据明细状态查询
        List<DocDoDetail> list = docDoDetailService.queryByLinestatus("00");
        assertNotNull(list, "查询结果不应该为空");
        log.info("根据明细状态查询到 {} 条数据", list.size());
    }

    @Test
    void testQueryByPackageId() {
        // 根据包装ID查询
        List<DocDoDetail> list = docDoDetailService.queryByPackageId(1L);
        assertNotNull(list, "查询结果不应该为空");
        log.info("根据包装ID查询到 {} 条数据", list.size());
    }

    @Test
    void testQueryByLotNo() {
        // 根据批次编码查询
        List<DocDoDetail> list = docDoDetailService.queryByLotNo("LOT001");
        assertNotNull(list, "查询结果不应该为空");
        log.info("根据批次编码查询到 {} 条数据", list.size());
    }

    @Test
    void testQueryBySupplier() {
        // 根据供应商ID查询
        List<DocDoDetail> list = docDoDetailService.queryBySupplier("SUP001");
        assertNotNull(list, "查询结果不应该为空");
        log.info("根据供应商ID查询到 {} 条数据", list.size());
    }

    @Test
    void testQueryByCargoOwner() {
        // 根据货主ID查询
        List<DocDoDetail> list = docDoDetailService.queryByCargoOwner("OWNER001");
        assertNotNull(list, "查询结果不应该为空");
        log.info("根据货主ID查询到 {} 条数据", list.size());
    }

    @Test
    void testQueryByDamaged() {
        // 根据是否坏品查询
        List<DocDoDetail> list = docDoDetailService.queryByDamaged(0);
        assertNotNull(list, "查询结果不应该为空");
        log.info("根据是否坏品查询到 {} 条数据", list.size());
    }

    @Test
    void testQueryByValueables() {
        // 根据是否贵重品查询
        List<DocDoDetail> list = docDoDetailService.queryByValueables(0);
        assertNotNull(list, "查询结果不应该为空");
        log.info("根据是否贵重品查询到 {} 条数据", list.size());
    }

    @Test
    void testQueryByHaveCfy() {
        // 根据是否有处方药查询
        List<DocDoDetail> list = docDoDetailService.queryByHaveCfy(0);
        assertNotNull(list, "查询结果不应该为空");
        log.info("根据是否有处方药查询到 {} 条数据", list.size());
    }

    @Test
    void testQueryBySalesGrade() {
        // 根据销售等级查询
        List<DocDoDetail> list = docDoDetailService.queryBySalesGrade(1);
        assertNotNull(list, "查询结果不应该为空");
        log.info("根据销售等级查询到 {} 条数据", list.size());
    }

    @Test
    void testQueryByGoodsGrade() {
        // 根据货品等级查询
        List<DocDoDetail> list = docDoDetailService.queryByGoodsGrade(10);
        assertNotNull(list, "查询结果不应该为空");
        log.info("根据货品等级查询到 {} 条数据", list.size());
    }

    @Test
    void testSaveBatch() {
        // 创建批量保存数据
        List<DocDoDetailSaveRequest> requests = List.of(
                createTestRequest(1L, 1001L),
                createTestRequest(1L, 1002L),
                createTestRequest(1L, 1003L)
        );

        // 批量保存
        boolean result = docDoDetailService.saveBatch(requests);
        assertTrue(result, "批量保存应该成功");
        log.info("批量保存 {} 条明细数据成功", requests.size());
    }

    private DocDoDetailSaveRequest createTestRequest(Long doHeaderId, Long skuId) {
        DocDoDetailSaveRequest request = new DocDoDetailSaveRequest();
        request.setDoHeaderId(doHeaderId);
        request.setLinestatus("00");
        request.setSkuId(skuId);
        request.setExpectedQty(BigDecimal.valueOf(10));
        request.setExpectedQtyEach(BigDecimal.valueOf(10));
        request.setPackageId(1L);
        request.setPackDetailId(1L);
        request.setUom("PCS");
        request.setPrice(BigDecimal.valueOf(100.00));
        return request;
    }
}
