package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.request.DocDoHeaderPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.request.DocDoHeaderSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocDoHeaderResponse;
import com.accesscorporate.app.wms.server.common.context.UserContext;
import com.accesscorporate.app.wms.server.dal.entity.DocDoHeader;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 发货单头表Service测试类
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class DocDoHeaderServiceTest {

    @Resource
    private DocDoHeaderService docDoHeaderService;

    @BeforeEach
    void setUp() {
        // 设置测试用户上下文
        UserContext.setUserId(1L);
        UserContext.setCurrentWarehouseId(1L);
        UserContext.setTenantId(1L);
    }

    @AfterEach
    void tearDown() {
        // 清理用户上下文
        UserContext.removeAll();
    }

    @Test
    void testSaveAndGet() {
        // 创建测试数据
        DocDoHeaderSaveRequest request = new DocDoHeaderSaveRequest();
        request.setDoNo("DO" + System.currentTimeMillis());
        request.setStatus("00");
        request.setDoType("1");
        request.setConsigneeName("测试收货人");
        request.setMobile("13800138000");
        request.setAddress("测试地址");
        request.setExpectedQty(BigDecimal.valueOf(10));
        request.setShipQty(BigDecimal.valueOf(10));
        request.setDoCreateTime(LocalDateTime.now());

        // 保存
        boolean saveResult = docDoHeaderService.save(request);
        assertTrue(saveResult, "保存应该成功");

        // 查询
        DocDoHeader saved = docDoHeaderService.queryByDoNo(request.getDoNo());
        assertNotNull(saved, "应该能查询到保存的数据");
        assertEquals(request.getDoNo(), saved.getDoNo(), "发货单号应该一致");
        assertEquals(request.getConsigneeName(), saved.getConsigneeName(), "收货人应该一致");

        // 根据ID查询
        DocDoHeaderResponse response = docDoHeaderService.get(saved.getId());
        assertNotNull(response, "应该能根据ID查询到数据");
        assertEquals(saved.getDoNo(), response.getDoNo(), "发货单号应该一致");
    }

    @Test
    void testPageQuery() {
        // 创建分页查询请求
        DocDoHeaderPageQueryRequest request = new DocDoHeaderPageQueryRequest();
        request.setCurrent(1);
        request.setSize(10);
        request.setStatus("00");

        // 执行分页查询
        Page<DocDoHeaderResponse> page = docDoHeaderService.page(request);
        assertNotNull(page, "分页结果不应该为空");
        assertTrue(page.getSize() > 0, "页面大小应该大于0");
        log.info("分页查询结果：总数={}, 当前页={}, 页面大小={}", 
                page.getTotal(), page.getCurrent(), page.getSize());
    }

    @Test
    void testListAll() {
        // 查询所有数据
        List<DocDoHeaderResponse> list = docDoHeaderService.listAll();
        assertNotNull(list, "查询结果不应该为空");
        log.info("查询到 {} 条发货单数据", list.size());
    }

    @Test
    void testQueryByMobile() {
        // 根据手机号查询
        List<DocDoHeader> list = docDoHeaderService.queryByMobile("13800138000");
        assertNotNull(list, "查询结果不应该为空");
        log.info("根据手机号查询到 {} 条数据", list.size());
    }

    @Test
    void testQueryByStatus() {
        // 根据状态查询
        List<DocDoHeader> list = docDoHeaderService.queryByStatus("00");
        assertNotNull(list, "查询结果不应该为空");
        log.info("根据状态查询到 {} 条数据", list.size());
    }

    @Test
    void testQueryByDoType() {
        // 根据订单类型查询
        List<DocDoHeader> list = docDoHeaderService.queryByDoType("1");
        assertNotNull(list, "查询结果不应该为空");
        log.info("根据订单类型查询到 {} 条数据", list.size());
    }
}
