package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.dal.entity.SExpSrvLogDO;
import com.accesscorporate.app.wms.server.dal.entity.SExpSrvMsgDO;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.sql.Blob;

public class BaseExpServiceImpl {

    public void process(SExpSrvMsgDO srvMsg, SExpSrvLogDO serviceLog, boolean retryP) {
        long tStime = System.currentTimeMillis();
        long rStime = 0;
        Blob bData = null;


    }

}
