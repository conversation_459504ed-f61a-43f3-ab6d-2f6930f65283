package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.converter.DocDoHeaderMapStruct;
import com.accesscorporate.app.wms.server.biz.manager.impl.DocDoHeaderManager;
import com.accesscorporate.app.wms.server.biz.params.request.DocDoHeaderPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.request.DocDoHeaderSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocDoHeaderResponse;
import com.accesscorporate.app.wms.server.biz.service.DocDoHeaderService;
import com.accesscorporate.app.wms.server.common.constant.Constants;
import com.accesscorporate.app.wms.server.dal.entity.DocDoHeader;
import com.accesscorporate.app.wms.server.dal.mapper.DocDoHeaderMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import com.idanchuang.component.core.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 发货单头表Service实现类
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
public class DocDoHeaderServiceImpl extends ServiceImpl<DocDoHeaderMapper, DocDoHeader> implements DocDoHeaderService {

    @Autowired
    private DocDoHeaderManager docDoHeaderManager;

    @Override
    public List<DocDoHeaderResponse> listAll() {
        LambdaQueryWrapper<DocDoHeader> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        wrapper.eq(DocDoHeader::getIsDeleted, Boolean.FALSE);
        wrapper.orderByDesc(DocDoHeader::getCreateTime);
        return docDoHeaderManager.list(wrapper).stream()
                .map(DocDoHeaderMapStruct.INSTANCE::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public DocDoHeaderResponse get(Long id) {
        DocDoHeader docDoHeader = super.getById(id);
        return DocDoHeaderMapStruct.INSTANCE.convertToResponse(docDoHeader);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(DocDoHeaderSaveRequest request) {
        DocDoHeader docDoHeader = DocDoHeaderMapStruct.INSTANCE.convertToEntity(request);
        docDoHeader.setWarehouseId(UserContextAssistant.getCurrentWarehouseId());
        return super.save(docDoHeader);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        return super.removeById(id);
    }

    @Override
    public Page<DocDoHeaderResponse> page(DocDoHeaderPageQueryRequest request) {
        Page<DocDoHeader> page = new Page<>(request.getCurrent(), request.getSize());
        LambdaQueryWrapper<DocDoHeader> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        wrapper.eq(DocDoHeader::getIsDeleted, Boolean.FALSE);

        // 发货单号
        if (StringUtils.hasText(request.getDoNo())) {
            wrapper.like(DocDoHeader::getDoNo, request.getDoNo());
        }
        // 订单状态
        if (StringUtils.hasText(request.getStatus())) {
            wrapper.eq(DocDoHeader::getStatus, request.getStatus());
        }
        // 订单类型
        if (StringUtils.hasText(request.getDoType())) {
            wrapper.eq(DocDoHeader::getDoType, request.getDoType());
        }
        // 收货方
        if (StringUtils.hasText(request.getConsigneeName())) {
            wrapper.like(DocDoHeader::getConsigneeName, request.getConsigneeName());
        }
        // 手机号
        if (StringUtils.hasText(request.getMobile())) {
            wrapper.like(DocDoHeader::getMobile, request.getMobile());
        }
        // 运单号
        if (StringUtils.hasText(request.getTrackingNo())) {
            wrapper.like(DocDoHeader::getTrackingNo, request.getTrackingNo());
        }
        // 配送公司ID
        if (request.getCarrierId() != null) {
            wrapper.eq(DocDoHeader::getCarrierId, request.getCarrierId());
        }
        // 波次ID
        if (request.getWaveId() != null) {
            wrapper.eq(DocDoHeader::getWaveId, request.getWaveId());
        }
        // 原始ID
        if (StringUtils.hasText(request.getOrigId())) {
            wrapper.like(DocDoHeader::getOrigId, request.getOrigId());
        }
        // 供应商ID
        if (request.getSupplierId() != null) {
            wrapper.eq(DocDoHeader::getSupplierId, request.getSupplierId());
        }
        // 商家ID
        if (request.getMerchantId() != null) {
            wrapper.eq(DocDoHeader::getMerchantId, request.getMerchantId());
        }
        // 店铺ID
        if (request.getShopId() != null) {
            wrapper.eq(DocDoHeader::getShopId, request.getShopId());
        }
        // 企业客户ID
        if (request.getBusinessCustomerId() != null) {
            wrapper.eq(DocDoHeader::getBusinessCustomerId, request.getBusinessCustomerId());
        }
        // 原始SO编码
        if (StringUtils.hasText(request.getOriginalSoCode())) {
            wrapper.like(DocDoHeader::getOriginalSoCode, request.getOriginalSoCode());
        }
        // 订单子类型
        if (StringUtils.hasText(request.getOrderSubType())) {
            wrapper.eq(DocDoHeader::getOrderSubType, request.getOrderSubType());
        }
        // 订单来源系统
        if (StringUtils.hasText(request.getSourceSystem())) {
            wrapper.eq(DocDoHeader::getSourceSystem, request.getSourceSystem());
        }
        // 渠道编码
        if (StringUtils.hasText(request.getChannelCode())) {
            wrapper.eq(DocDoHeader::getChannelCode, request.getChannelCode());
        }
        // 店铺编码
        if (StringUtils.hasText(request.getStoreCode())) {
            wrapper.eq(DocDoHeader::getStoreCode, request.getStoreCode());
        }
        // 创建时间范围
        if (request.getCreateTimeStart() != null) {
            wrapper.ge(DocDoHeader::getCreateTime, request.getCreateTimeStart());
        }
        if (request.getCreateTimeEnd() != null) {
            wrapper.le(DocDoHeader::getCreateTime, request.getCreateTimeEnd());
        }
        // DO创建时间范围
        if (request.getDoCreateTimeStart() != null) {
            wrapper.ge(DocDoHeader::getDoCreateTime, request.getDoCreateTimeStart());
        }
        if (request.getDoCreateTimeEnd() != null) {
            wrapper.le(DocDoHeader::getDoCreateTime, request.getDoCreateTimeEnd());
        }
        // 发运时间范围
        if (request.getShipTimeStart() != null) {
            wrapper.ge(DocDoHeader::getShipTime, request.getShipTimeStart());
        }
        if (request.getShipTimeEnd() != null) {
            wrapper.le(DocDoHeader::getShipTime, request.getShipTimeEnd());
        }

        wrapper.orderByDesc(DocDoHeader::getCreateTime);

        Page<DocDoHeader> docDoHeaderPage = super.page(page, wrapper);
        List<DocDoHeaderResponse> responses = docDoHeaderPage.getRecords().stream()
                .map(DocDoHeaderMapStruct.INSTANCE::convertToResponse)
                .collect(Collectors.toList());
        return new Page<DocDoHeaderResponse>(docDoHeaderPage.getCurrent(), docDoHeaderPage.getSize(), docDoHeaderPage.getTotal())
                .setRecords(responses);
    }

    @Override
    public DocDoHeader queryByDoNo(String doNo) {
        return lambdaQuery()
                .eq(DocDoHeader::getDoNo, doNo)
                .eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoHeader::getIsDeleted, Boolean.FALSE)
                .one();
    }

    @Override
    public DocDoHeader queryByOrigId(String origId) {
        return lambdaQuery()
                .eq(DocDoHeader::getOrigId, origId)
                .eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoHeader::getIsDeleted, Boolean.FALSE)
                .one();
    }

    @Override
    public List<DocDoHeader> queryByTrackingNo(String trackingNo) {
        return lambdaQuery()
                .eq(DocDoHeader::getTrackingNo, trackingNo)
                .eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoHeader::getIsDeleted, Boolean.FALSE)
                .list();
    }

    @Override
    public List<DocDoHeader> queryByMobile(String mobile) {
        return lambdaQuery()
                .eq(DocDoHeader::getMobile, mobile)
                .eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoHeader::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoHeader::getCreateTime)
                .list();
    }

    @Override
    public List<DocDoHeader> queryByWaveId(Long waveId) {
        return lambdaQuery()
                .eq(DocDoHeader::getWaveId, waveId)
                .eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoHeader::getIsDeleted, Boolean.FALSE)
                .list();
    }

    @Override
    public List<DocDoHeader> queryByStatus(String status) {
        return lambdaQuery()
                .eq(DocDoHeader::getStatus, status)
                .eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoHeader::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoHeader::getCreateTime)
                .list();
    }

    @Override
    public List<DocDoHeader> queryByDoType(String doType) {
        return lambdaQuery()
                .eq(DocDoHeader::getDoType, doType)
                .eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoHeader::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoHeader::getCreateTime)
                .list();
    }
    @Transactional
    @Override
    public boolean doCancelIntercept(Long doId) {
        DocDoHeader doHeader = super.getById(doId);
        if (null == doHeader || Constants.DoStatus.CANCELED.getValue().equals(doHeader.getStatus())) {
            return true;
        }

        SImpSrvMsg impSrvMsg = this.sImpSrvMsgDAO.getByRefId(doHeader.getOrigId(), doHeader.getWarehouseId());
        if (null == impSrvMsg) {
            impSrvMsg = this.sImpSrvMsgDAO.getByRefNo(doHeader.getDoNo(), doHeader.getWarehouseId());
        }
        if (null == impSrvMsg) {
            return false;
        }

        if (StringUtil.isEmpty(impSrvMsg.getImpData())) {
            return true;
        }

        Parser parser = ParserFactory.getParser("json");
        DoCancelRequest request = parser.stringToBean(impSrvMsg.getImpData(), DoCancelRequest.class);

        doHeader.setUpdatedBy(request.getCancelOperator());
        doHeader.setHoldWho(request.getCancelOperator());
        doHeader.setHoldTime(DateUtil.getNowTime());
        doHeader.setHoldCode(Reason.CS_CANCLE.getValue());
        // 如果是第一次冻结，记录初始冻结原因
        if (StringUtils.isEmpty(doHeader.getFirstHoldCode())) {
            doHeader.setFirstHoldCode(Reason.CS_CANCLE.getValue());
        }
        doHeader.setHoldReason(Dictionary.getDictionary("REASON_HDD").get(doHeader.getHoldCode()));
        doHeader.setNeedCancel(true);
        doHeader.setNotes("出库单取消拦截");
        doHeader.setReleaseStatus(ReleaseStatus.HOLD.getValue());
        // 保存异常日志
        DoExceptionLog doExceptionLog = this.changeDoExceptionLog(doHeader);

        // 分配时拦截
        if (Constants.DoStatus.PARTALLOCATED.getValue().equals(doHeader.getStatus())) {
            List<Long> doDetailIds = new ArrayList<Long>();
            for (DeliveryOrderDetail detail : doHeader.getDoDetails()) {
                doDetailIds.add(detail.getId());
            }
            // 释放已分配的库存
            doAllocateService.releaseAssignedStock(doHeader.getId(), doDetailIds);
        }

        if (StringUtil.isIn(doHeader.getStatus(), Constants.DoStatus.UNCHECKED.getValue(), Constants.DoStatus.INITIAL.getValue(),
                Constants.DoStatus.PARTALLOCATED.getValue())) {
            doExceptionLog.setToExceptionStatus(Constants.DoExpStatus.COMPLETE.getValue());
            doExceptionLog.setToDoStatus(Constants.DoStatus.CANCELED.getValue());
            doExceptionLog.setToReleaseStatus(doHeader.getReleaseStatus());
            doExceptionLogService.saveDoExceptionLog(doExceptionLog);

            doHeader.setStatus(Constants.DoStatus.CANCELED.getValue());
            doHeader.setExceptionStatus(Constants.DoExpStatus.COMPLETE.getValue());
            // 明细lineStatus 也设置成取消 90 状态
            doDetailDAO.updateDoDetailStatusByDoId(doHeader.getId(), Constants.DoStatus.CANCELED.getValue());

            doAllocateService.removeAllocate(doId);
        }

        // 核拣时拦截
        if (DoStatus.ALLSORTED.getValue().equals(doHeader.getStatus())) {
            doExceptionLog.setToExceptionStatus(Constants.DoExpStatus.TO_BE_ROLLBACK.getValue());// 异常状态为待回退
            doExceptionLog.setToDoStatus(doHeader.getStatus());
            doExceptionLog.setToReleaseStatus(Constants.ReleaseStatus.HOLD.getValue());
            // 保存异常日志
            doExceptionLogService.saveDoExceptionLog(doExceptionLog);

            // 部分拣货--装箱中，前台可申请取消，NEED_CANCEL=1,RELEASE='HD',STATUS不变,hold_code='03',Hold_Reason='客服申请取消订单'
            doHeader.setReleaseStatus(Constants.ReleaseStatus.HOLD.getValue());
            doHeader.setExceptionStatus(Constants.DoExpStatus.TO_BE_ROLLBACK.getValue());
        }
        doHeaderDAO.update(doHeader);

        // 判断是否有发票 有则调用作废发票接口
        this.cancleInvoice(doHeader);

        sImpSrvMsgDAO.deleteById(impSrvMsg.getId());

        orderLogService.saveLog(doHeader, OrderLogConstants.OrderLogType.ORDER_CANCEL.getValue(),
                ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_ORDER_CANCEL), doHeader.getHoldWho());

        return true;
    }
}
