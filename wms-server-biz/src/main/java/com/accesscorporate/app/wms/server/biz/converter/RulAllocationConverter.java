package com.accesscorporate.app.wms.server.biz.converter;

import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationDRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationHRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulAllocationDResponse;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulAllocationHResponse;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulAllocationResponse;
import com.accesscorporate.app.wms.server.dal.entity.rule.RulAllocationDDO;
import com.accesscorporate.app.wms.server.dal.entity.rule.RulAllocationDO;
import com.accesscorporate.app.wms.server.dal.entity.rule.RulAllocationHDO;

/**
 * <AUTHOR>
 * @version JDK 17
 * @date 2025/2/26
 * @description
 */
public class RulAllocationConverter {

    public static RulAllocationResponse toRulAllocationResponse(RulAllocationDO rulAllocationDO) {

        return new RulAllocationResponse().setId(rulAllocationDO.getId()).setAllocationCode(rulAllocationDO.getAllocationCode())
                .setAllocationDescr(rulAllocationDO.getAllocationDescr())
                .setActiveFlag(rulAllocationDO.getActiveFlag());
    }

    public static RulAllocationDO toRulAllocationDO(RulAllocationRequest request){
        return new RulAllocationDO().setId(request.getId())
                .setAllocationCode(request.getAllocationCode())
                .setAllocationDescr(request.getAllocationDescr())
                .setActiveFlag(request.getActiveFlag());
    }

    public static RulAllocationHDO toRulAllocationHDO(RulAllocationHRequest request){
        return new RulAllocationHDO().setId(request.getId())
                .setLineno(request.getLineno())
                .setAllocationId(request.getAllocationId())
                .setAllocationDescr(request.getAllocationDescr())
                .setOrdertype(request.getOrdertype())
                .setPartitionCtrl(request.getPartitionCtrl())
                .setStorageCtrl(request.getStorageCtrl())
                .setAllotType(request.getAllotType())
                .setStockStrategy(request.getStockStrategy())
                .setSupportRs(request.getSupportRs())
                .setExpireControl(request.getExpireControl());

    }

    public static RulAllocationHResponse toRulAllocationHResponse(RulAllocationHDO rulAllocationHDO) {

        return new RulAllocationHResponse().setId(rulAllocationHDO.getId())
                .setLineno(rulAllocationHDO.getLineno())
                .setAllocationId(rulAllocationHDO.getAllocationId())
                .setAllocationDescr(rulAllocationHDO.getAllocationDescr())
                .setOrdertype(rulAllocationHDO.getOrdertype())
                .setPartitionCtrl(rulAllocationHDO.getPartitionCtrl())
                .setStorageCtrl(rulAllocationHDO.getStorageCtrl())
                .setAllotType(rulAllocationHDO.getAllotType())
                .setStockStrategy(rulAllocationHDO.getStockStrategy())
                .setSupportRs(rulAllocationHDO.getSupportRs())
                .setExpireControl(rulAllocationHDO.getExpireControl());
    }


    public static RulAllocationDDO toRulAllocationDDO(RulAllocationDRequest request) {
        return new RulAllocationDDO()
                .setId(request.getId())
                .setRHId(request.getRHId())
                .setLineno(request.getLineno())
                .setLotattname(request.getLotattname())
                .setSortby(request.getSortby());
    }

    public static RulAllocationDResponse toRulAllocationDResponse(RulAllocationDDO rulAllocationDDO) {
        return new RulAllocationDResponse()
                .setId(rulAllocationDDO.getId())
                .setRHId(rulAllocationDDO.getRHId())
                .setLineno(rulAllocationDDO.getLineno())
                .setLotattname(rulAllocationDDO.getLotattname())
                .setSortby(rulAllocationDDO.getSortby());
    }

}
