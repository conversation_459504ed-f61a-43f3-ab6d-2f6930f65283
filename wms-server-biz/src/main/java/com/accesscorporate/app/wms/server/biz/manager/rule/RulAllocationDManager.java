package com.accesscorporate.app.wms.server.biz.manager.rule;

import com.accesscorporate.app.wms.server.dal.entity.rule.RulAllocationDDO;
import com.accesscorporate.app.wms.server.dal.mapper.rule.RulAllocationDMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version JDK 17
 * @date 2025/2/27
 * @description
 */
@Component
public class RulAllocationDManager extends ServiceImpl<RulAllocationDMapper, RulAllocationDDO> {

    public List<RulAllocationDDO> queryByHId(Long hId) {
        return lambdaQuery()
                .eq(RulAllocationDDO::getRHId, hId)
                .eq(RulAllocationDDO::getIsDeleted, 0).list();
    }

    public Boolean createRulAllocationD(RulAllocationDDO rulAllocationDDO) {
        return save(rulAllocationDDO);
    }

    public Boolean updateRulAllocationD(RulAllocationDDO rulAllocationDDO) {
        return updateById(rulAllocationDDO);
    }

    public RulAllocationDDO selectById(Long id) {
        return getById(id);
    }

//    public Boolean deleteById(Long id) {
//        RulAllocationDDO rulAllocationDDO = new RulAllocationDDO()
//                .setId(id)
//                .setIsDeleted(1);
//        return updateById(rulAllocationDDO);
//    }
}
