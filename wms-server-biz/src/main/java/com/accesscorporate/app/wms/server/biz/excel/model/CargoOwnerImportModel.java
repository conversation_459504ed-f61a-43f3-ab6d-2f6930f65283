package com.accesscorporate.app.wms.server.biz.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 货主信息导入-model对象
 *
 * <AUTHOR>
 * 2025/2/24  10:05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class CargoOwnerImportModel implements Serializable {

    @Serial
    private static final long serialVersionUID = -7872181754597897325L;

    @ExcelProperty(value = "货主编码")
    private String cargoOwnerCode;

    @ExcelProperty(value = "货主名称")
    private String cargoOwnerName;

    @ExcelProperty(value = "货主英文名称")
    private String cargoOwnerEnName;

    @ExcelProperty(value = "联系人邮箱")
    private String contactEmail;

    @ExcelProperty(value = "联系人电话")
    private String contactTel;

    @ExcelProperty(value = "联系人姓名")
    private String contactName;

    @ExcelProperty(value = "备注")
    private String note;

    @ExcelProperty(value = "联系人传真")
    private String contactFax;

    @ExcelProperty(value = "联系人手机")
    private String contactMo;

    @ExcelProperty(value = "邮政编码")
    private String postcode;

    @ExcelProperty(value = "地址")
    private String address;

    @ExcelProperty(value = "纳税人名称")
    private String taxPayerName;

    @ExcelProperty(value = "发票请求接口地址")
    private String invoiceInterfaceUrl;

    @ExcelProperty(value = "商家平台编码")
    private String invoicePlatformCode;

    @ExcelProperty(value = "纳税人识别码")
    private String taxPayerCode;


    // 需要WMS有地址库的能力呢
//    /**
//     * 国家ID
//     */
//    private Long countryId;
//
//    /**
//     * 省份ID
//     */
//    private Long provinceId;
//
//    /**
//     * 城市ID
//     */
//    private Long cityId;

}
