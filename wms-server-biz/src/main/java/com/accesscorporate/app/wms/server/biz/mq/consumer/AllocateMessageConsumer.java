package com.accesscorporate.app.wms.server.biz.mq.consumer;

import com.accesscorporate.app.wms.server.biz.mq.message.AllocateMessage;
import com.accesscorporate.app.wms.server.biz.params.response.DocAlcHeaderResponse;
import com.accesscorporate.app.wms.server.biz.service.AllocateService;
import com.accesscorporate.app.wms.server.biz.service.DocAlcHeaderService;
import com.accesscorporate.app.wms.server.common.constant.MqConstant;
import com.accesscorporate.app.wms.server.common.context.UserContext;
import com.accesscorporate.app.wms.server.dal.entity.DocAlcHeader;
import com.alibaba.fastjson.JSON;
import com.idanchuang.sso.model.dto.system.UserConciseDTO;
import com.rabbitmq.client.Channel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 自动分配消息消费者
 * 监听分配消息队列，处理分配任务
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class AllocateMessageConsumer {

    @Resource
    private AllocateService allocateService;
    @Resource
    private DocAlcHeaderService docAlcHeaderService;
    

    @RabbitListener(queues = MqConstant.ALLOCATE_MESSAGE_QUEUE)
    public void receive(Channel channel, Message message, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        String msg = new String(message.getBody());
        log.info("接收到分配消息，消息内容:{}", msg);
        
        AllocateMessage allocateMessage = null;
        try {
            allocateMessage = JSON.parseObject(msg, AllocateMessage.class);
            Long doId = allocateMessage.getDocId();
            DocAlcHeaderResponse docAlcHeader = docAlcHeaderService.get(doId);
            if (allocateService.autoAllocate(doId).get(AllocateService.NO_ENOUGH_STOCK_QTY) != null) {
                // 调用接口通知客服；
                DoExpDto dto = new DoExpDto();
                dto.setId(doId);
                dto.setHoldCode(Constants.Reason.ALLOC_LACK.getValue());
                dto.setNotifyType(NotifyCSType.AUTO.getValue());
                // 如果配置自动通知客服，则缺货自动通知客服。
                Boolean lackAutoAnounceCs = SysConfigHelper.getSwitchDefalutOpen("alloc.lack.autoAnounceCs");
                if (lackAutoAnounceCs) {
                    expFacadeService.callCS(dto);
                }
            }
            // 验证消息内容
            if (allocateMessage == null || allocateMessage.getWarehouseId() == null) {
                log.error("分配消息格式错误，消息内容:{}", msg);
                channel.basicAck(tag, false);
                return;
            }
            UserContext.setCurrentWarehouseId(allocateMessage.getWarehouseId());
            // 执行分配业务逻辑
            allocateService.processAllocate(allocateMessage);
            
            log.info("分配任务处理成功，仓库ID:{}", allocateMessage.getWarehouseId());
            
        } catch (Exception e) {
            log.error("分配任务处理异常，消息内容:{}", msg, e);
            
            // 处理重试逻辑
            if (allocateMessage != null) {
                handleRetry(allocateMessage, e);
            }
        } finally {
            // 手动确认消息
            channel.basicAck(tag, false);
        }
    }
    
    /**
     * 处理重试逻辑
     */
    private void handleRetry(AllocateMessage allocateMessage, Exception e) {
        try {
            Integer retryCount = allocateMessage.getRetryCount();
            Integer maxRetryCount = allocateMessage.getMaxRetryCount();
            
            if (retryCount < maxRetryCount) {
                // 增加重试次数并重新发送消息
                allocateMessage.setRetryCount(retryCount + 1);
                
                // 这里可以添加延迟重试的逻辑
                // 例如使用延迟队列或者定时任务
                log.info("分配任务将进行第{}次重试，仓库ID:{}", allocateMessage.getRetryCount(), allocateMessage.getWarehouseId());
                
                // TODO: 实现重试机制，可以发送到延迟队列或者记录到数据库后续处理
                
            } else {
                log.error("分配任务重试次数已达上限，仓库ID:{}, 异常信息:{}", 
                    allocateMessage.getWarehouseId(), e.getMessage());
                
                // TODO: 可以发送告警或者记录到失败表
            }
        } catch (Exception retryException) {
            log.error("处理重试逻辑异常", retryException);
        }
    }
}
