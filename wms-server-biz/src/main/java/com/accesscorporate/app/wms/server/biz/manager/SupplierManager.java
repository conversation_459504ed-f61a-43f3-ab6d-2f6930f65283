package com.accesscorporate.app.wms.server.biz.manager;

import com.accesscorporate.app.wms.server.dal.entity.SupplierDO;
import com.accesscorporate.app.wms.server.dal.mapper.ISupplierMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idanchuang.component.core.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 供应商-数据聚合层-manager
 *
 * <AUTHOR>
 * 2025/2/8  16:33
 */
@Component
@RequiredArgsConstructor
public class SupplierManager extends ServiceImpl<ISupplierMapper, SupplierDO> {


    public Page<SupplierDO> querySupplierPage(String supplierCode,
                                              String supplierName,
                                              Long current,
                                              Long size) {

        return lambdaQuery()
                .eq(StringUtil.isNotBlank(supplierCode), SupplierDO::getSupplierCode, supplierCode)
                .like(StringUtil.isNotBlank(supplierName), SupplierDO::getSupplierCompanyName, supplierName)
                .eq(SupplierDO::getIsDeleted, 0)
                .page(new Page<>(current, size));
    }


    public Boolean modifySupplier(SupplierDO supplierDO) {
        lambdaUpdate()
                .eq(SupplierDO::getId, supplierDO.getId())
                .update(supplierDO);
        return true;
    }


}
