package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.dal.entity.CfgConfigurationDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 系统参数配置服务接口
 * 
 * <AUTHOR>
 */
public interface ICfgConfigurationService {

    /**
     * 分页查询配置
     * 
     * @param configType 配置类型
     * @param configNo 配置编号
     * @param descrC 配置名称
     * @param configLevel 配置级别
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @param current 当前页
     * @param size 页大小
     * @return 分页结果
     */
    Page<CfgConfigurationDO> queryPage(String configType, String configNo, String descrC,
                                        Integer configLevel, Long warehouseId, Long tenantId,
                                        Long current, Long size);

    /**
     * 根据ID查询配置
     * 
     * @param id 配置ID
     * @return 配置信息
     */
    CfgConfigurationDO queryById(Long id);

    /**
     * 创建配置
     * 
     * @param config 配置信息
     * @return 是否成功
     */
    Boolean createConfig(CfgConfigurationDO config);

    /**
     * 更新配置
     * 
     * @param config 配置信息
     * @return 是否成功
     */
    Boolean updateConfig(CfgConfigurationDO config);

    /**
     * 删除配置
     * 
     * @param id 配置ID
     * @return 是否成功
     */
    Boolean deleteConfig(Long id);

    /**
     * 根据配置编号获取配置值（支持优先级查询）
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置信息
     */
    CfgConfigurationDO getConfig(String configNo, Long warehouseId, Long tenantId);

    /**
     * 根据配置编号获取字符串值
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 字符串值
     */
    String getStringValue(String configNo, Long warehouseId, Long tenantId);

    /**
     * 根据配置编号获取字符串值（带默认值）
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @param defaultValue 默认值
     * @return 字符串值
     */
    String getStringValue(String configNo, Long warehouseId, Long tenantId, String defaultValue);

    /**
     * 根据配置编号获取整数值
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 整数值
     */
    Integer getIntValue(String configNo, Long warehouseId, Long tenantId);

    /**
     * 根据配置编号获取整数值（带默认值）
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @param defaultValue 默认值
     * @return 整数值
     */
    Integer getIntValue(String configNo, Long warehouseId, Long tenantId, Integer defaultValue);

    /**
     * 根据配置编号获取布尔值
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 布尔值
     */
    Boolean getBooleanValue(String configNo, Long warehouseId, Long tenantId);

    /**
     * 根据配置编号获取布尔值（带默认值）
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @param defaultValue 默认值
     * @return 布尔值
     */
    Boolean getBooleanValue(String configNo, Long warehouseId, Long tenantId, Boolean defaultValue);

    /**
     * 根据配置类型查询配置列表
     * 
     * @param configType 配置类型
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置列表
     */
    List<CfgConfigurationDO> listByConfigType(String configType, Long warehouseId, Long tenantId);

    /**
     * 根据配置级别查询配置列表
     * 
     * @param configLevel 配置级别
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置列表
     */
    List<CfgConfigurationDO> listByConfigLevel(Integer configLevel, Long warehouseId, Long tenantId);

    /**
     * 根据一级分类查询配置列表
     * 
     * @param levelOne 一级分类
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置列表
     */
    List<CfgConfigurationDO> listByLevelOne(String levelOne, Long warehouseId, Long tenantId);

    /**
     * 根据二级分类查询配置列表
     * 
     * @param levelTwo 二级分类
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置列表
     */
    List<CfgConfigurationDO> listByLevelTwo(String levelTwo, Long warehouseId, Long tenantId);

    /**
     * 根据配置标签查询配置列表
     * 
     * @param configTag1 配置标签1
     * @param configTag2 配置标签2
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置列表
     */
    List<CfgConfigurationDO> listByConfigTags(String configTag1, String configTag2, 
                                               Long warehouseId, Long tenantId);

    /**
     * 批量创建配置
     * 
     * @param configList 配置列表
     * @return 是否成功
     */
    Boolean batchCreateConfig(List<CfgConfigurationDO> configList);

    /**
     * 检查配置编号是否存在
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    Boolean existsByConfigNoAndWarehouseId(String configNo, Long warehouseId, Long excludeId);
}
