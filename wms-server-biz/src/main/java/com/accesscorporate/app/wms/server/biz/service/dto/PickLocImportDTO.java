package com.accesscorporate.app.wms.server.biz.service.dto;

import com.accesscorporate.app.wms.server.common.annotation.NonNullField;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-08-04 16:04
 * Description: 商品捡货位批量导入
 */
@Data
public class PickLocImportDTO {

    @ExcelProperty("商品编码")
    @NonNullField(desc = "商品编码")
    private String productCode;

    @ExcelProperty("库位编码")
    @NonNullField(desc = "库位编码")
    private String locCode;

    @ExcelProperty("补货单位")
    @NonNullField(desc = "补货单位")
    private String uom;

    @ExcelProperty("补货上限")
    @NonNullField(desc = "补货上限")
    private Integer upLimit;

    @ExcelProperty("补货下限")
    @NonNullField(desc = "补货下限")
    private Integer lowerLimit;

    @ExcelProperty("最小补货量")
    @NonNullField(desc = "最小补货量")
    private Integer minimumRplQty;



}
