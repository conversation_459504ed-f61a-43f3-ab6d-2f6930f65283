package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.request.goodspackage.GoodsPackageItemSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.request.goodspackage.GoodsPackageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.request.goodspackage.GoodsPackageSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.goodspackage.GoodsPackageEditResponse;
import com.accesscorporate.app.wms.server.biz.params.response.goodspackage.GoodsPackageQueryResponse;
import com.accesscorporate.app.wms.server.biz.params.response.goodspackage.PackageInfoDetailResponse;
import com.idanchuang.component.base.page.PageData;

import java.util.List;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-02-20 10:47
 * Description: 包装信息管理
 */
public interface GoodsPackageService {

    /**
     * 保存商品包裹明细
     */
    void saveOrUpdateGoodsPackage(GoodsPackageSaveRequest request);


    /**
     * 分页查询
     */
    PageData<GoodsPackageQueryResponse> pageQuery(GoodsPackageQueryRequest queryParam);

    /**
     * 通过包装代码编辑商品包装信息
     */
    GoodsPackageEditResponse editGoodsPackage(Long packageId);

    /**
     * 通过商品编码编辑商品包装明细信息
     */
    List<PackageInfoDetailResponse> editGoodsPackageQueryItems(Long packageId);

    /**
     * 新增&编辑包装明细
     */
    void saveOrUpdateGoodsPackageItem(GoodsPackageItemSaveRequest request);

    /**
     * 删除包装明细
     */
    void deleteGoodsPackageItem(Long packageDetailId);

}
