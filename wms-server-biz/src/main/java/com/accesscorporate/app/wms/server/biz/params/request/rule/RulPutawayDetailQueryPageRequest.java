package com.accesscorporate.app.wms.server.biz.params.request.rule;

import com.idanchuang.component.base.page.PageDTO;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @author: g<PERSON><PERSON>
 * @date: 2025-02-26 10:39
 * @desc:
 */
@Data
@Accessors(chain = true)
public class RulPutawayDetailQueryPageRequest extends PageDTO {

    /**
     * 上架规则id
     */
    @NotNull
    private Long rulHeaderId;

}
