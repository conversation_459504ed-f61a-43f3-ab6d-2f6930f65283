package com.accesscorporate.app.wms.server.biz.manager.rule;

import com.accesscorporate.app.wms.server.dal.entity.rule.RulAllocationHDO;
import com.accesscorporate.app.wms.server.dal.mapper.rule.RulAllocationHMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version JDK 17
 * @date 2025/2/26
 * @description
 */
@Component
public class RulAllocationHManager extends ServiceImpl<RulAllocationHMapper, RulAllocationHDO> {

    public Page<RulAllocationHDO> selectPage(Long AllocationId, Long current, Long size) {
        return lambdaQuery().eq(RulAllocationHDO::getAllocationId, AllocationId)
                .eq(RulAllocationHDO::getIsDeleted, 0)
                .page(new Page<>(current, size));

    }

    public RulAllocationHDO selectById(Long id) {
        return getById(id);
    }

    public Boolean updateRulAllocationH(RulAllocationHDO rulAllocationHDO) {
        return updateById(rulAllocationHDO);
    }

    public Boolean createRulAllocationH(RulAllocationHDO rulAllocationHDO) {
        return save(rulAllocationHDO);
    }

    public Boolean deleteRulAllocationH(Long id) {
        RulAllocationHDO rulAllocationHDO = new RulAllocationHDO();
        rulAllocationHDO.setId(id);
        rulAllocationHDO.setIsDeleted(1);
        return updateById(rulAllocationHDO);
    }
}
