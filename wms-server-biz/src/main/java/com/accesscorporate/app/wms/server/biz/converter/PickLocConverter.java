package com.accesscorporate.app.wms.server.biz.converter;

import com.accesscorporate.app.wms.server.biz.manager.CfgCodeDetailCacheService;
import com.accesscorporate.app.wms.server.biz.params.response.pickloc.PickLocPageQueryResponse;
import com.accesscorporate.app.wms.server.common.enums.MasterCodeEnum;
import com.accesscorporate.app.wms.server.dal.entity.pickloc.PickLocPageQueryResponseDO;
import com.idanchuang.component.config.apollo.util.SpringContextUtil;
import com.idanchuang.component.core.util.CopyUtil;

import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.toList;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-24 13:43
 * Description: 捡货位类型转换
 */
public class PickLocConverter {

    public static List<PickLocPageQueryResponse> converterToPageQueryResponse(List<PickLocPageQueryResponseDO> records){
        CfgCodeDetailCacheService cfgCodeDetailCacheService = SpringContextUtil.getBean(CfgCodeDetailCacheService.class);
        Map<String, String> locTypeMap = cfgCodeDetailCacheService.getDictionary(MasterCodeEnum.LOC_TYPE.name());
        Map<String, String> packageTypeMap = cfgCodeDetailCacheService.getDictionary(MasterCodeEnum.PACKAGE_TYPE.name());
        return records.stream().map(record->{
            PickLocPageQueryResponse response = CopyUtil.copy(record, PickLocPageQueryResponse.class);
            response.setProductCnameForI18n(record.getProductCnameForI18n());
            //库位类型
            String locTypeDesc = locTypeMap.get(record.getLocType());
            response.setLocTypeDesc(locTypeDesc);
            //下架方式
            String packageTypeDesc = packageTypeMap.get(record.getPackageType());
            response.setPackageTypeDesc(packageTypeDesc);
            response.setUpLimit(record.getUpLimit().toString());
            response.setLowerLimit(record.getLowerLimit().toString());
            response.setMinimumRplQty(record.getMinimumRplQty().toString());
            return response;
        }).collect(toList());


    }


}
