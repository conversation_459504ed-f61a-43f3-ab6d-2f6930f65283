package com.accesscorporate.app.wms.server.biz.converter;

import com.accesscorporate.app.wms.server.biz.params.request.DocAlcHeaderSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocAlcHeaderResponse;
import com.accesscorporate.app.wms.server.dal.entity.DocAlcHeader;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface DocAlcHeaderMapStruct {

    DocAlcHeaderMapStruct INSTANCE = Mappers.getMapper(DocAlcHeaderMapStruct.class);

    DocAlcHeaderResponse convertToResponse(DocAlcHeader docAlcHeader);

    DocAlcHeader convertToEntity(DocAlcHeaderResponse response);

    DocAlcHeader convertToEntity(DocAlcHeaderSaveRequest saveRequest);
}
