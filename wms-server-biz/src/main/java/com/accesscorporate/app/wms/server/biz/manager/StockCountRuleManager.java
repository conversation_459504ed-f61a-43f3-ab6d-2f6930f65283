package com.accesscorporate.app.wms.server.biz.manager;

import cn.hutool.core.util.StrUtil;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import com.accesscorporate.app.wms.server.dal.entity.StockCountRuleDO;
import com.accesscorporate.app.wms.server.dal.mapper.IStockCountRuleMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 库存盘点规则 - 数据 - manager
 *
 * <AUTHOR>
 * 2025/2/14  13:13
 */
@Component
@RequiredArgsConstructor
public class StockCountRuleManager extends ServiceImpl<IStockCountRuleMapper, StockCountRuleDO> {


    public Page<StockCountRuleDO> queryStockCountRulePage(String ruleCode,
                                                          String ruleName,
                                                          long current,
                                                          long size) {

        return lambdaQuery()
                .eq(StrUtil.isNotBlank(ruleCode), StockCountRuleDO::getCode, ruleCode)
                .like(StrUtil.isNotBlank(ruleName), StockCountRuleDO::getName, ruleName)
                .eq(StockCountRuleDO::getIsDeleted, 0)
                .eq(StockCountRuleDO::getTenantId, UserContextAssistant.getTenantId())
                .eq(StockCountRuleDO::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .page(new Page<>(current, size));
    }


    public StockCountRuleDO queryStockCountRuleDetailBy(Long id) {
        return lambdaQuery()
                .eq(StockCountRuleDO::getId, id)
                .eq(StockCountRuleDO::getTenantId, UserContextAssistant.getTenantId())
                .eq(StockCountRuleDO::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .one();
    }

    public Boolean modifyStockCountRule(StockCountRuleDO stockCountRuleDO) {
        return updateById(stockCountRuleDO);
    }


    public Boolean createStockCountRule(StockCountRuleDO stockCountRuleDO) {
        stockCountRuleDO.setCreateBy(UserContextAssistant.getUsername())
                .setTenantId(UserContextAssistant.getTenantId())
                .setWarehouseId(UserContextAssistant.getCurrentWarehouseId())
                .setCreateTime(LocalDateTime.now())
                .setUpdateTime(LocalDateTime.now());
        return save(stockCountRuleDO);
    }



}
