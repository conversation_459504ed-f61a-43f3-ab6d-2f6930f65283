package com.accesscorporate.app.wms.server.biz.excel.listener;

import com.alibaba.excel.exception.ExcelDataConvertException;
import com.idanchuang.component.base.exception.common.ErrorCode;
import com.idanchuang.component.base.exception.core.ExFactory;

/**
 * 监听器公共-方法集
 *
 * <AUTHOR>
 * 2025/2/24  11:09
 */
public class CommonListenerFunc {

    public static void onExceptionProcess(Exception exception) {
        int columnIndex = ((ExcelDataConvertException) exception).getColumnIndex() + 1;
        int rowIndex = ((ExcelDataConvertException) exception).getRowIndex() + 1;
        String message = "第" + rowIndex + "行，第" + columnIndex + "列" + "数据有误，请核实";
        throw ExFactory.throwWith(ErrorCode.BUSINESS_ERROR, message);
    }

}
