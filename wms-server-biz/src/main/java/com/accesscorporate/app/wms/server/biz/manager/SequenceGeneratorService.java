package com.accesscorporate.app.wms.server.biz.manager;

import com.accesscorporate.app.wms.server.dal.entity.SequenceRule;

import java.util.List;

/**
 * 序列号生成器服务
 *
 * <AUTHOR>
 * @description
 * @date 2025/2/5 下午6:31 星期三
 */
public interface SequenceGeneratorService {

    /**
     * 根据序列号名称生成序列号
     *
     * @param sequenceName
     * @return
     */
    String generateSequenceNo(String sequenceName, Long warehouseId);

    /**
     * 批量获取序列号
     *
     * @param sequenceName
     * @param seqNum       一次获取的数量
     * @param warehouseId
     * @return
     */
    List<String> generateSequenceNo(String sequenceName, Integer seqNum, Long warehouseId);

    /**
     * 生成全局的数据ID（代替ORACLE的SEQUENCE）
     *
     * @param sequenceName
     * @return
     */
    Long generateSequenceId(String sequenceName);


    List<String> generateSequenceId(String transactionLogId, int size);

    /**
     * 当外部修改了序列规则时，需要清空缓存的序列号规则
     */
    void clearCache();

    /**
     * 从序列号服务器获取配置缓存信息
     *
     * @param sequenceName
     * @return
     */
    SequenceRule getRuleCache(String sequenceName, Long warehouseId);

    /**
     * 更新序列号服务器缓存信息
     *
     * @param rule
     */
    void updateCache(SequenceRule rule, Long warehouseId);
}
