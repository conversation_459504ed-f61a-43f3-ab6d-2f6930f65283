package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.response.CfgCodeMasterResponse;
import com.accesscorporate.app.wms.server.dal.entity.CfgCodeMasterDO;

import java.util.List;

/**
 * @TableName cfg_code_master
 */
public interface CfgCodeMasterService {
    /**
     * group name去重返回
     *
     * @author: sxp
     * @date: 2025/2/5 下午2:01
     * @return: List<String>
     **/
    List<String> findCodeCategoryGroupNames();

    /**
     * 根据groupName查询关联的所有字典数据
     *
     * @param groupName
     * @author: sxp
     * @date: 2025/2/5 下午2:02
     * @return: List<CfgCodeMasterDO>
     **/
    List<CfgCodeMasterResponse> getCodeMasters(String groupName);

}