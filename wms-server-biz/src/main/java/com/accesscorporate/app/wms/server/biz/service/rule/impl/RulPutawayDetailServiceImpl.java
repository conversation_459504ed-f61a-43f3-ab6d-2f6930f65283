package com.accesscorporate.app.wms.server.biz.service.rule.impl;

import com.accesscorporate.app.wms.server.biz.converter.RulPutawayConverter;
import com.accesscorporate.app.wms.server.biz.manager.rule.RulPutawayDetailManager;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayDetailQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayDetailRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulPutawayDetailResponse;
import com.accesscorporate.app.wms.server.biz.service.rule.IRulPutawayDetailService;
import com.accesscorporate.app.wms.server.dal.entity.rule.RulPutawayDetailDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.page.PageData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RulPutawayDetailServiceImpl implements IRulPutawayDetailService {

    private final RulPutawayDetailManager rulPutawayDetailManager;

    @Override
    public PageData<RulPutawayDetailResponse> queryPage(RulPutawayDetailQueryPageRequest request) {

        Page<RulPutawayDetailDO> page = rulPutawayDetailManager.selectPage(request.getRulHeaderId(),
                request.getCurrent(),
                request.getSize());
        List<RulPutawayDetailResponse> responses = page.getRecords().stream().map(RulPutawayConverter::toRulPutawayDetailResponse).toList();

        return PageData.of(
                responses,
                request.getCurrent(),
                request.getSize(),
                page.getTotal());
    }

    @Override
    public RulPutawayDetailResponse queryById(Long id) {
        RulPutawayDetailDO rulPutawayDetailDO = rulPutawayDetailManager.selectById(id);
        if (rulPutawayDetailDO == null) {
            return null;
        }
        return RulPutawayConverter.toRulPutawayDetailResponse(rulPutawayDetailDO);
    }

    @Override
    public Boolean save(RulPutawayDetailRequest request) {
        RulPutawayDetailDO rulPutawayDetailDO = RulPutawayConverter.toRulPutawayDetailDO(request);
        if (rulPutawayDetailDO.getId() != null) {
            return rulPutawayDetailManager.updateRulPutawayDetail(rulPutawayDetailDO);
        }
        return rulPutawayDetailManager.createRulPutawayDetail(rulPutawayDetailDO);
    }
    @Override
    public Boolean deleteById(Long id) {
        return rulPutawayDetailManager.deleteById(id);
    }
}
