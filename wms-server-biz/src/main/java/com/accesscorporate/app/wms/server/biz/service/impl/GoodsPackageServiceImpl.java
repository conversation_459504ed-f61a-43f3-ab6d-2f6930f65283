package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.params.request.goodspackage.GoodsPackageItemSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.request.goodspackage.GoodsPackageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.request.goodspackage.GoodsPackageSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.goodspackage.GoodsPackageEditResponse;
import com.accesscorporate.app.wms.server.biz.params.response.goodspackage.GoodsPackageQueryResponse;
import com.accesscorporate.app.wms.server.biz.params.response.goodspackage.PackageInfoDetailResponse;
import com.accesscorporate.app.wms.server.biz.service.GoodsPackageService;
import com.accesscorporate.app.wms.server.biz.service.IMdSkuService;
import com.accesscorporate.app.wms.server.common.enums.WhetherEnum;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import com.accesscorporate.app.wms.server.dal.dto.GoodsPackageQueryParam;
import com.accesscorporate.app.wms.server.dal.entity.goodspackage.PackageSkuDO;
import com.accesscorporate.app.wms.server.dal.entity.MdSku;
import com.accesscorporate.app.wms.server.dal.entity.goodspackage.MdPackageDDO;
import com.accesscorporate.app.wms.server.dal.entity.goodspackage.MdPackageHDO;
import com.accesscorporate.app.wms.server.dal.mapper.goodspackage.MdPackageHMapper;
import com.accesscorporate.app.wms.server.dal.repository.goodspackage.MdPackageDRepository;
import com.accesscorporate.app.wms.server.dal.repository.goodspackage.MdPackageHRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.exception.core.ExFactory;
import com.idanchuang.component.base.page.PageData;
import com.idanchuang.component.core.util.CopyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.util.Collections.emptyList;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-02-20 11:18
 * Description: 包装信息管理
 */
@Service
public class GoodsPackageServiceImpl implements GoodsPackageService {
    @Autowired
    private MdPackageHRepository mdPackageHRepository;
    @Autowired
    private IMdSkuService mdSkuService;
    @Autowired
    private MdPackageHMapper mdPackageHMapper;
    @Autowired
    private MdPackageDRepository mdPackageDRepository;

    public void saveOrUpdateGoodsPackage(GoodsPackageSaveRequest request) {
        String userName = UserContextAssistant.getUsername();
        Long tenantId = UserContextAssistant.getTenantId();
        // 查询商品SKU表主键id
        String productCode = request.getProductCode();
        MdSku mdSku = mdSkuService.querySkuByGoodsCode(productCode);
        if (Objects.isNull(mdSku)) {
            throw ExFactory.throwBusiness("保存商品包装信息使用货品编码查询货品信息为空，货品编码:{}" + productCode);
        }
        // 判断是新增还是更新
        if (request.getPackageId() != null) {
            // 更新
            MdPackageHDO exist = mdPackageHRepository.getById(request.getPackageId());
            if (exist == null) {
                throw ExFactory.throwBusiness("根据商品包装信息ID查询包装信息，记录不存在，无法更新，packageId=" + request.getPackageId());
            }
            //更新字段（建议用BeanUtils.copyProperties覆盖指定字段，这里手动赋值）
            exist.setSkuId(mdSku.getId());
            exist.setPkgCode(request.getPkgCode());
            exist.setPkgDesc(request.getPkgDesc());
            exist.setUpdateBy(userName);
            exist.setUpdateTime(new Date());
            mdPackageHRepository.updateById(exist);
        } else {
            // 新增
            //校验该商品是否已经存在
            Long skuId = mdSku.getId();
            MdPackageHDO exist = mdPackageHRepository.queryMdPackageHeaderBySkuId(skuId);
            if (exist != null) {
                throw ExFactory.throwBusiness("该货品包装信息已经存在，请勿重复添加，货品编码:{}", productCode);
            }
            MdPackageHDO mdPackageHDO = new MdPackageHDO();
            mdPackageHDO.setSkuId(mdSku.getId());
            mdPackageHDO.setPkgCode(request.getPkgCode());
            mdPackageHDO.setPkgDesc(request.getPkgDesc());
            mdPackageHDO.setCreateBy(userName);
            mdPackageHDO.setTenantId(tenantId);
            mdPackageHRepository.save(mdPackageHDO);
        }
    }

    @Override
    public PageData<GoodsPackageQueryResponse> pageQuery(GoodsPackageQueryRequest queryParam) {
        Page<PackageSkuDO> page = new Page<>(queryParam.getCurrent(), queryParam.getSize());
        GoodsPackageQueryParam packageQueryParam = CopyUtil.copy(queryParam, GoodsPackageQueryParam.class);
        IPage<PackageSkuDO> packageSkuDTOIPage = mdPackageHMapper.pageQueryPackageSku(page, packageQueryParam);
        List<PackageSkuDO> records = packageSkuDTOIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return PageData.empty();
        }
        List<GoodsPackageQueryResponse> queryResponseList = CopyUtil.copyList(records, GoodsPackageQueryResponse.class);
        return PageData.of(queryResponseList, queryParam.getCurrent(), queryParam.getSize(), packageSkuDTOIPage.getTotal());
    }

    @Override
    public GoodsPackageEditResponse editGoodsPackage(Long packageId) {
        MdPackageHDO exist = mdPackageHRepository.getById(packageId);
        if (exist == null) {
            throw ExFactory.throwBusiness("编辑商品包装信息查询包装信息，记录不存在，无法更新，packageId=" + packageId);
        }
        Long skuId = exist.getSkuId();
        MdSku mdSku = mdSkuService.getById(skuId);
        if (mdSku == null) {
            throw ExFactory.throwBusiness("编辑商品包装信息查询商品信息，记录不存在，无法更新，packageId=" + packageId);
        }
        return GoodsPackageEditResponse.builder()
                .productCode(mdSku.getProductCode())
                .pkgCode(exist.getPkgCode())
                .pkgDesc(exist.getPkgDesc())
                .build();
    }

    @Override
    public List<PackageInfoDetailResponse> editGoodsPackageQueryItems(Long packageId) {
        List<MdPackageDDO> mdPackageDDOS = mdPackageDRepository.queryPackageDetailListByPackageId(packageId);
        if (CollectionUtils.isEmpty(mdPackageDDOS)) {
            return emptyList();
        }
        return mdPackageDDOS.stream().map(detail -> {
            PackageInfoDetailResponse response = CopyUtil.copy(detail, PackageInfoDetailResponse.class);
            response.setPackageDetailId(detail.getId());
            response.setMainFlag(WhetherEnum.getDescByCode(Integer.valueOf(detail.getMainFlag())));
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public void saveOrUpdateGoodsPackageItem(GoodsPackageItemSaveRequest request) {
        String userName = UserContextAssistant.getUsername();
        Long tenantId = UserContextAssistant.getTenantId();
        if (request.getPackageDetailId() != null) {
            //编辑
            MdPackageDDO mdPackageDetailDO = mdPackageDRepository.getById(request.getPackageDetailId());
            if (mdPackageDetailDO == null) {
                throw ExFactory.throwBusiness("根据商品包装明细信息ID查询包装明细信息，记录不存在，无法更新，packageDetailId="
                        + request.getPackageDetailId());
            }
            mdPackageDetailDO.setQty(request.getQty());
            mdPackageDetailDO.setPackUom(request.getPackUom());
            mdPackageDetailDO.setUpdateBy(userName);
            mdPackageDetailDO.setUpdateTime(new Date());
            mdPackageDetailDO.setUomDescr(String.format("%s(%s)", request.getPackUom(), request.getQty()));
            mdPackageDRepository.updateById(mdPackageDetailDO);
        } else {
            //新增
            MdPackageDDO mdPackageDetailDO = new MdPackageDDO();
            mdPackageDetailDO.setQty(request.getQty());
            mdPackageDetailDO.setPackUom(request.getPackUom());
            mdPackageDetailDO.setCreateBy(userName);
            mdPackageDetailDO.setPackageId(request.getPackageId());
            mdPackageDetailDO.setTenantId(tenantId);
            mdPackageDetailDO.setUomDescr(String.format("%s(%s)", request.getPackUom(), request.getQty()));
            mdPackageDRepository.save(mdPackageDetailDO);
        }
    }

    @Override
    public void deleteGoodsPackageItem(Long packageDetailId) {
        boolean removeFlag = mdPackageDRepository.removeById(packageDetailId);
        if (!removeFlag) {
            throw ExFactory.throwBusiness("根据商品包装明细信息ID删除明细失败，packageDetailId=" + packageDetailId);
        }

    }


}
