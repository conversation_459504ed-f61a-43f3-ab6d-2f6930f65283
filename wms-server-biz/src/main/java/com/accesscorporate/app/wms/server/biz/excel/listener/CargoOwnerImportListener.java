package com.accesscorporate.app.wms.server.biz.excel.listener;

import cn.hutool.core.util.StrUtil;
import com.accesscorporate.app.wms.server.biz.excel.model.CargoOwnerImportModel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.idanchuang.component.base.exception.common.ErrorCode;
import com.idanchuang.component.base.exception.core.ExFactory;

/**
 * 货主信息导入-监听器
 *
 * <AUTHOR>
 * @since 2025/2/24  10:18
 */
public class CargoOwnerImportListener extends AnalysisEventListener<CargoOwnerImportModel> {
    @Override
    public void invoke(CargoOwnerImportModel cargoOwnerImportModel, AnalysisContext analysisContext) {
        if (StrUtil.isBlank(cargoOwnerImportModel.getCargoOwnerCode())) {
            throw ExFactory.throwWith(ErrorCode.BUSINESS_ERROR,
                    String.format("第%s行[货主编码]为空，请核实", analysisContext.readRowHolder().getRowIndex() + 1));
        }


        if (StrUtil.isBlank(cargoOwnerImportModel.getCargoOwnerName())) {
            throw ExFactory.throwWith(ErrorCode.BUSINESS_ERROR,
                    String.format("第%s行[货主名称]为空，请核实", analysisContext.readRowHolder().getRowIndex() + 1));
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        if (exception instanceof ExcelDataConvertException) {
            CommonListenerFunc.onExceptionProcess(exception);
        } else if (exception instanceof RuntimeException) {
            throw exception;
        } else {
            super.onException(exception, context);
        }
    }
}
