package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.request.*;
import com.accesscorporate.app.wms.server.biz.params.response.ReplenishmentRuleDetailResponse;
import com.accesscorporate.app.wms.server.biz.params.response.ReplenishmentRuleResponse;
import com.idanchuang.component.base.page.PageData;

import java.util.List;

/**
 * 补货规则-Service
 *
 * <AUTHOR>
 * 2025/2/19  10:13
 */
public interface IReplenishmentRuleService {


    /**
     * 创建 补货规则
     *
     * @param request: 参数Req
     * @return Boolean
     * <AUTHOR>
     * 2025/2/19 14:14
     */
    Boolean createReplenishmentRule(ReplenishmentRuleCreateRequest request);


    /**
     * 更新 补货规则
     *
     * @param request: 参数Req
     * @return Boolean
     * <AUTHOR>
     * 2025/2/19 14:14
     */
    Boolean modifyReplenishmentRule(ReplenishmentRuleModifyRequest request);


    /**
     * 删除 补货规则
     *
     * @param id: 补货规则ID
     * @return Boolean
     * <AUTHOR>
     * 2025/2/19 14:14
     */
    Boolean removeReplenishmentRule(Long id);


    /**
     * 分页查询 补货规则列表
     *
     * @param request: 查询参数
     * @return PageData<ReplenishmentRuleResponse>
     * <AUTHOR>
     * 2025/2/19 10:14
     */
    PageData<ReplenishmentRuleResponse> queryReplenishmentRules(ReplenishmentRuleQueryRequest request);





    /*------------------------ 补货规则明细Service start...... --------------------------*/

    /**
     * 根据主规则ID  查询 补货规则明细
     *
     * @param ruleId: 补货规则ID (主规则！)
     * @return List<ReplenishmentRuleDetailResponse>
     * <AUTHOR>
     * 2025/2/19 10:17
     */
    List<ReplenishmentRuleDetailResponse> queryRuleDetails(Long ruleId);


    /**
     * 删除明细
     *
     * @param detailId: 明细ID
     * @return Boolean
     * <AUTHOR>
     * 2025/2/19 10:18
     */
    Boolean removeReplenishmentRuleDetail(Long detailId);


    /**
     * 补货规则明细新增
     *
     * @param request: 新增参数
     * @return Boolean
     * <AUTHOR>
     * 2025/2/21 11:25
     */
    Boolean createReplenishmentRuleDetail(ReplenishmentRuleDetailAddRequest request);


    /**
     * 补货规则明细编辑
     *
     * @param request: 编辑参数
     * @return Boolean
     * <AUTHOR>
     * 2025/2/21 11:25
     */
    Boolean modifyReplenishmentRuleDetail(ReplenishmentRuleDetailModifyRequest request);

}
