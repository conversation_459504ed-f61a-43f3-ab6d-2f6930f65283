package com.accesscorporate.app.wms.server.biz.utils;

public class PageConfig {
    
    public static String get(String key, Integer configLevel) {
        return Config.convert2String(Config.getConfig(key, Config.getLevel(configLevel)));
    }
    public static Integer getInt(String key, Integer configLevel) {
        return Config.convert2Int(Config.getConfig(key, Config.getLevel(configLevel)),null);
    }
    public static Integer getInt(String key, Integer configLevel,Integer nullDefaultValue) {
        return Config.convert2Int(Config.getConfig(key, Config.getLevel(configLevel)),nullDefaultValue);
    }
    public static boolean is(String key, Integer configLevel) {
        return Config.convert2Boolean(Config.getConfig(key, Config.getLevel(configLevel)), false);
    }
    
    public static boolean is(String key, Integer configLevel, Boolean nullDefaultValue) {
        return Config.convert2Boolean(Config.getConfig(key, Config.getLevel(configLevel)), nullDefaultValue);
    }
    

}