package com.accesscorporate.app.wms.server.biz.export;


import com.idanchuang.scm.excel.annotation.ExcelColumn;
import lombok.Data;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-08-06 15:44
 * Description:商品捡货位导出
 */
@Data
public class PickLocExcelExportVO {

    @ExcelColumn("商品捡货位记录行主键id")
    private Long pickLocId;

    @ExcelColumn("补货单位")
    private String uom;

    @ExcelColumn("商品名称，国外仓展示英文名称，国内仓")
    private String productCnameForI18n;

    @ExcelColumn("商品编码")
    private String productCode;

    @ExcelColumn("商品条码")
    private String ean13;

    @ExcelColumn("库位编码")
    private String locCode;

    @ExcelColumn("库区")
    private String partionCode;

    @ExcelColumn("库位类型")
    private String locTypeDesc;

    @ExcelColumn("下架方式")
    private String packageTypeDesc;

    @ExcelColumn("补货上限")
    private String upLimit;

    @ExcelColumn("补货下限")
    private String lowerLimit;

    @ExcelColumn("最小补货量")
    private String minimumRplQty;





}
