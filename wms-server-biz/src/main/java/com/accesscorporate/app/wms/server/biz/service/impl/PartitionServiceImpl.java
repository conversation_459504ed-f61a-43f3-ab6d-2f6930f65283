package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.manager.impl.PartitionManager;
import com.accesscorporate.app.wms.server.biz.service.PartitionService;
import com.accesscorporate.app.wms.server.biz.service.RegionService;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import com.accesscorporate.app.wms.server.dal.entity.Partition;
import com.accesscorporate.app.wms.server.dal.entity.Region;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.exception.core.ExFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

import static com.idanchuang.component.base.page.PageData.adaptLimit;
import static com.idanchuang.component.base.page.PageData.adaptPageNo;

@Service
public class PartitionServiceImpl implements PartitionService {
    @Autowired
    private RegionService regionService;
    @Autowired
    private PartitionManager partitionManager;
    @Override
    public List<Partition> list() {
        LambdaQueryWrapper<Partition> wrapper = new LambdaQueryWrapper<Partition>();
        wrapper.eq(Partition::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        return partitionManager.list(wrapper);
    }

    @Override
    public Partition getById(Long id) {
        return partitionManager.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(Partition partition) {
        LambdaQueryWrapper<Partition> wrapper = new LambdaQueryWrapper<Partition>();
        wrapper.eq(Partition::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        wrapper.eq(Partition::getPartitionCode, partition.getPartitionCode());
        List<Partition> list = partitionManager.list(wrapper);
        if (partition.getId() == null) {
            if(list.size() > 1|| (list.size()==1 && !list.get(0).getId().equals(partition.getPartitionCode()) )) {
                throw ExFactory.throwBusiness("{}分区编码已存在", partition.getPartitionCode());
            }
            partition.setWarehouseId(UserContextAssistant.getCurrentWarehouseId());
            return partitionManager.save(partition);
        }else{
            if(list.size() > 0) {
                throw ExFactory.throwBusiness("{}分区编码已存在", partition.getPartitionCode());
            }
            return partitionManager.updateById(partition);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        partitionManager.removeById(id);
        return true;
    }

    @Override
    public Page<Partition> page(Integer current, Integer size, String partitionCode, String description, String regionCode) {
        Region region = null;
        if(StringUtils.isNotBlank(regionCode)){
                LambdaQueryWrapper<Region> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(Region::getRegionCode, regionCode);
                List<Region> list = regionService.list(wrapper);
                if(list.size() > 0){
                    region = list.get(0);
                }
            }
        LambdaQueryWrapper<Partition> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Partition::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        wrapper.like(StringUtils.isNotBlank(description),Partition::getDescription,description);
        wrapper.eq(StringUtils.isNotBlank(partitionCode),Partition::getPartitionCode,partitionCode);
        if(Objects.nonNull(region)){
            wrapper.eq(Partition::getRegionId,region.getId());
        }
        return partitionManager.page(new Page<>(adaptPageNo(current), adaptLimit(size)), wrapper);
    }

}