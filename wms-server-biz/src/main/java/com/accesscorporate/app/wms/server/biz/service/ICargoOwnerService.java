package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.excel.model.CargoOwnerImportModel;
import com.accesscorporate.app.wms.server.biz.params.request.CargoOwnerPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.response.CargoOwnerInfoResponse;
import com.idanchuang.component.base.page.PageData;

import java.util.List;

/**
 * 货主管理-Service
 *
 * <AUTHOR>
 * 2025/2/8  17:22
 */
public interface ICargoOwnerService {


    /**
     * 分页查询-货主信息列表
     *
     * @param request: 查询参数
     * @return PageData<CargoOwnerInfoResponse>
     * <AUTHOR>
     * 2025/2/8 16:50
     */
    PageData<CargoOwnerInfoResponse> queryCargoOwnerPage(CargoOwnerPageQueryRequest request);


    /**
     * 导入货主信息
     *
     * @param models: 数据模型
     * @return Boolean
     * <AUTHOR>
     * @since 2025/2/24 10:34
     */
    Boolean importCargoOwnerProcessItems(List<CargoOwnerImportModel> models);


}
