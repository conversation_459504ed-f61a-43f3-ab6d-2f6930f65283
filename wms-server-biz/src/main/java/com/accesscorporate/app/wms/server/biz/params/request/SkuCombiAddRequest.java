package com.accesscorporate.app.wms.server.biz.params.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> liu<PERSON>liang
 * @project : wms-server
 * @company : 浙江单创品牌管理有限公司
 * @desc :
 * Copyright @ 2024 danchuang Technology Co.Ltd. – Confidential and Proprietary
 */
@Data
public class SkuCombiAddRequest implements Serializable {

    @Serial
    private static final long serialVersionUID = -3502173953986667379L;

    /**
     * 组合条码
     */
    @NotNull(message = "组合条码不能为空!")
    private String combiBarcode;

    /**
     * 库位编码
     */
    @NotNull(message = "库位编码不能为空!")
    private String locCode;

    @NotEmpty(message = "明细数据不能为空！")
    private List<SkuItem> items;

    @Data
    @Valid
    public static class SkuItem implements Serializable {

        @Serial
        private static final long serialVersionUID = 5401846651262509557L;

        /**
         * 货品条码
         */
        @NotNull(message = "货品条码不能为空!")
        private String barcode;

        /**
         * 数量
         */
        @NotNull(message = "货品数量不能为空!")
        private Integer quantity;

        /**
         * 货品等级
         */
        @NotNull(message = "货品等级不能为空!")
        private Integer goodsGrade;

        public String getUniqueKey() {
            return String.format("%s*%d*%d", barcode, goodsGrade, quantity);
        }
    }

    public boolean isCombiBarcodeDigit() {
        return combiBarcode.matches("\\d+");
    }

    public boolean isCombiBarcodeLengthMatchRule() {
        return combiBarcode.length() >= 1 && combiBarcode.length() <= 30;
    }

}
