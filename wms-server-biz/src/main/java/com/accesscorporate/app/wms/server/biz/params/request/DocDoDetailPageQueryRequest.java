package com.accesscorporate.app.wms.server.biz.params.request;

import com.idanchuang.component.base.page.PageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 发货单明细表分页查询请求体
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Tag(name = "发货单明细表分页查询请求体")
public class DocDoDetailPageQueryRequest extends PageDTO {

    @Schema(description = "订单头ID")
    private Long doHeaderId;

    @Schema(description = "明细状态")
    private String linestatus;

    @Schema(description = "商品ID")
    private Long skuId;

    @Schema(description = "包装ID")
    private Long packageId;

    @Schema(description = "包装明细ID")
    private Long packDetailId;

    @Schema(description = "单位")
    private String uom;

    @Schema(description = "供应商ID")
    private String lotatt04;

    @Schema(description = "批号")
    private String lotatt05;

    @Schema(description = "货主ID（商家）")
    private String lotatt06;

    @Schema(description = "制造商")
    private String lotatt08;

    @Schema(description = "po号")
    private String lotatt10;

    @Schema(description = "容器号")
    private String lotatt13;

    @Schema(description = "分配规则ID")
    private Long allocationRule;

    @Schema(description = "拣货区")
    private String pickzone;

    @Schema(description = "原始头ID")
    private String origHeaderId;

    @Schema(description = "原始明细ID")
    private String origDetailId;

    @Schema(description = "是否坏品")
    private Integer isDamaged;

    @Schema(description = "是否贵重品")
    private Integer isValueables;

    @Schema(description = "是否为酒类随附单")
    private Integer wineFlag;

    @Schema(description = "批次编码")
    private String lotNo;

    @Schema(description = "是否有处方药")
    private Integer haveCfy;

    @Schema(description = "销售等级")
    private Integer salesGrade;

    @Schema(description = "货品等级")
    private Integer goodsGrade;

    @Schema(description = "期望发货数量最小值")
    private BigDecimal expectedQtyMin;

    @Schema(description = "期望发货数量最大值")
    private BigDecimal expectedQtyMax;

    @Schema(description = "分配数量最小值")
    private BigDecimal allocatedQtyMin;

    @Schema(description = "分配数量最大值")
    private BigDecimal allocatedQtyMax;

    @Schema(description = "拣货数量最小值")
    private BigDecimal pickedQtyMin;

    @Schema(description = "拣货数量最大值")
    private BigDecimal pickedQtyMax;

    @Schema(description = "发货数量最小值")
    private BigDecimal shippedQtyMin;

    @Schema(description = "发货数量最大值")
    private BigDecimal shippedQtyMax;

    @Schema(description = "创建时间开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "最小失效日期开始")
    private LocalDate minExpStart;

    @Schema(description = "最小失效日期结束")
    private LocalDate minExpEnd;

    @Schema(description = "最大失效日期开始")
    private LocalDate maxExpStart;

    @Schema(description = "最大失效日期结束")
    private LocalDate maxExpEnd;
}
