package com.accesscorporate.app.wms.server.biz.job;

import com.accesscorporate.app.wms.server.biz.job.entity.XxlJobParamEntity;
import com.accesscorporate.app.wms.server.biz.mq.RabbitMqService;
import com.accesscorporate.app.wms.server.biz.mq.message.AllocateMessage;
import com.accesscorporate.app.wms.server.biz.service.AllocateService;
import com.accesscorporate.app.wms.server.biz.service.WarehouseService;
import com.accesscorporate.app.wms.server.biz.utils.Config;
import com.accesscorporate.app.wms.server.biz.utils.PageConfig;
import com.accesscorporate.app.wms.server.common.constant.MqConstant;
import com.accesscorporate.app.wms.server.common.enums.Keys;
import com.accesscorporate.app.wms.server.dal.entity.Warehouse;
import com.accesscorporate.app.wms.server.dal.entity.WarehouseDO;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.idanchuang.component.core.util.StringUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * 自动分配Do的定时任务,使用新框架重写
 * 只拉取数据发送rabbitmq，消费者监听消息处理分配任务
 *
 * <AUTHOR>
 */
@org.springframework.stereotype.Component
@JobHandler(value = "autoAllocateXxlJob")
@lombok.extern.slf4j.Slf4j
public class AutoAllocateXxlJob extends IJobHandler {

    @Resource
    private RabbitMqService rabbitMqService;
    @Resource
    private WarehouseService warehouseService;
    @Resource
    private AllocateService allocateService;
    private ObjectMapper mapper = new ObjectMapper();
    private static final String NULL = "null";
    private List<Long> getAlcHeaderIds(int batchAllocateNum,Long warehouseId) {
        return allocateService.queryNeedAllocateDoHeaderIds(batchAllocateNum,warehouseId);
    }
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        log.info("AutoAllocateXxlJob开始执行，参数：{}", param);
        try {

        List<Warehouse> whIdList = warehouseService.list();
        String cfg = PageConfig.get("job.except.wh.id", Config.ConfigLevel.GLOBAL.getValue());
        if (StringUtil.isNotEmpty(cfg)){
            for (String s : cfg.split(",")){
                whIdList.remove(Long.valueOf(s));
            }
        }

            int batchAllocateNum = Config.getInt(Keys.Delivery.allocate_batch_num, Config.ConfigLevel.GLOBAL, 20);
            if (!CollectionUtils.isEmpty(whIdList)) {
                for (Warehouse warehouse : whIdList) {
                    Long whId= warehouse.getId();
                    // 需要分配的alcHeaderId
                    List<Long> alcHeaderIds = getAlcHeaderIds(batchAllocateNum, whId);
                    if (!CollectionUtils.isEmpty(alcHeaderIds)) {
                        log.debug("alc header id size:" + alcHeaderIds.size());
                        for (Long alcId : alcHeaderIds) {
                            sendAllocateMessage(whId, alcId);
                        }
                    }
                }
            }
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("AutoAllocateXxlJob执行异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行失败：" + e.getMessage());
        }
    }

    /**
     * 发送分配消息到MQ
     */
    private void sendAllocateMessage(Long warehouseId,Long alcId) {
        try {
            AllocateMessage allocateMessage = new AllocateMessage();
            allocateMessage.setWarehouseId(warehouseId);
            allocateMessage.setDocId(alcId);

            String messageJson = JSON.toJSONString(allocateMessage);
            rabbitMqService.send(MqConstant.ALLOCATE_MESSAGE_EXCHANGE, messageJson);

            log.info("发送分配消息成功，仓库ID：{}，消息：{}", warehouseId, messageJson);
        } catch (Exception e) {
            log.error("发送分配消息失败，仓库ID：{}", warehouseId, e);
            throw e;
        }
    }
}