package com.accesscorporate.app.wms.server.biz.converter;

import com.accesscorporate.app.wms.server.biz.params.request.DocAlcDetailSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocAlcDetailResponse;
import com.accesscorporate.app.wms.server.dal.entity.DocAlcDetail;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface DocAlcDetailMapStruct {

    DocAlcDetailMapStruct INSTANCE = Mappers.getMapper(DocAlcDetailMapStruct.class);

    DocAlcDetailResponse convertToResponse(DocAlcDetail docAlcDetail);

    DocAlcDetail convertToEntity(DocAlcDetailResponse response);

    DocAlcDetail convertToEntity(DocAlcDetailSaveRequest saveRequest);
}
