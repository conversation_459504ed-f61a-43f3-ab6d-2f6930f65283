package com.accesscorporate.app.wms.server.biz.params.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 货主信息-响应Response
 *
 * <AUTHOR>
 * 2025/2/8  16:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "货主信息 View Object")
public class CargoOwnerInfoResponse {

    @Schema(title = "货主ID")
    private Long id;

    @Schema(title = "货主编码")
    private String cargoOwnerCode;

    @Schema(title = "货主名称")
    private String cargoOwnerName;

}
