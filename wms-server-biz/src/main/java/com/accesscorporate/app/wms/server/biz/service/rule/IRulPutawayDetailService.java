package com.accesscorporate.app.wms.server.biz.service.rule;

import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayDetailQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayDetailRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulPutawayDetailResponse;
import com.idanchuang.component.base.page.PageData;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
public interface IRulPutawayDetailService {

    /**
     * 分页查询上架规则明细列表
     */
     PageData<RulPutawayDetailResponse> queryPage(RulPutawayDetailQueryPageRequest request);

    /**
     * 查询上架规则明细详情
     */
    RulPutawayDetailResponse queryById(Long id);

    /**
     * 新增上架规则明细
     */
    Boolean save(RulPutawayDetailRequest request);


    /**
     * 删除上架规则明细
     */
    Boolean deleteById(Long id);

}
