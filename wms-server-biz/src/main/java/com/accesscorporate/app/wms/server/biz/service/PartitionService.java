package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.dal.entity.Partition;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public interface PartitionService {
    List<Partition> list();

    Partition getById(Long id);

    boolean save(Partition partition);

    boolean removeById(Long id);

    Page<Partition> page(Integer current, Integer size, String partitionCode, String description, String regionCode);
}
