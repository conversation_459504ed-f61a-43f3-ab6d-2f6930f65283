package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.params.request.AddressFilter;
import com.accesscorporate.app.wms.server.biz.service.AddressService;
import com.accesscorporate.app.wms.server.biz.service.dto.AddressDTO;
import com.accesscorporate.app.wms.server.biz.service.dto.AddressInfoDTO;
import com.accesscorporate.app.wms.server.common.enums.TransportWendyEnum;
import com.accesscorporate.app.wms.server.dal.dto.AddressFilterQuery;
import com.accesscorporate.app.wms.server.dal.entity.AddressDO;
import com.accesscorporate.app.wms.server.dal.mapper.AddressMapper;
import com.accesscorporate.app.wms.server.integration.wrap.IAddressWrap;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.danchuang.global.scm.address.client.dto.BaseAddressDTO;
import com.danchuang.global.scm.address.client.request.address.AddressQueryRequest;
import com.idanchuang.component.base.exception.common.ErrorCode;
import com.idanchuang.component.base.exception.core.ExFactory;
import com.idanchuang.component.base.page.PageData;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AddressServiceImpl implements AddressService {

    @Resource
    private AddressMapper addressMapper;

    @Resource
    private IAddressWrap addressWrap;

    @Override
    public PageData<AddressInfoDTO> queryAddressInfo(AddressFilter filter) {
        log.info("查询地址信息，filter：{}", filter);
        Page<AddressDO> page = new Page<>(filter.getCurrent(), filter.getSize());
        AddressFilterQuery addressFilterQuery = new AddressFilterQuery();
        addressFilterQuery.setCountryCode(filter.getCountryCode());
        addressFilterQuery.setProvinceCode(filter.getProvinceCode());
        IPage<AddressDO> result = addressMapper.queryByFilter(page, addressFilterQuery);
        List<AddressInfoDTO> voList = result.getRecords().stream()
                .map(this::convert2AddressInfoDTO)
                .collect(Collectors.toList());
        return PageData.of(voList, result.getCurrent(), result.getSize(), result.getTotal());
    }

    @Override
    public AddressInfoDTO getById(Long id) {
        AddressDO addressDO = addressMapper.selectById(id);
        return convert2AddressInfoDTO(addressDO);
    }

    @Override
    public List<AddressDTO> getAllCountry() {
        List<AddressDO> list = addressMapper.getListByParentCode("0");
        return list.stream().map(this::convert2AddressDTO).collect(Collectors.toList());
    }

    @Override
    public List<AddressDTO> getProvinceList(String countryCode) {
        List<AddressDO> list = addressMapper.getListByParentCode(countryCode);
        return list.stream().map(this::convert2AddressDTO).collect(Collectors.toList());
    }

    @Override
    public List<AddressDTO> findAddressListByParentCode(String parentCode) {
        List<AddressDO> list = addressMapper.getListByParentCode(parentCode);
        if (Objects.isNull(list)) {
            log.warn("地址信息不存在，parentCode：{}", parentCode);
            throw ExFactory.throwWith(ErrorCode.BUSINESS_ERROR, "地址信息不存在");
        }
        return list.stream().map(this::convert2AddressDTO).collect(Collectors.toList());

    }

    @Override
    public boolean saveOrUpdate(AddressDTO addressDTO) {
        AddressDO addressDO = convert2Address(addressDTO, null);
        if (addressDTO.getId() == null) {
            int row = addressMapper.insert(addressDO);
            log.info("新增地址信息：{}", addressDTO);
            return row > 0;
        } else {
            AddressDO addressDOInDb = addressMapper.selectById(addressDTO.getId());
            if (addressDOInDb == null) {
                log.warn("地址信息不存在，id：{}", addressDTO.getId());
                throw ExFactory.throwWith(ErrorCode.BUSINESS_ERROR, "地址信息不存在");
            }
            addressDOInDb.setType(addressDTO.getType());
            addressDOInDb.setSortName(addressDTO.getSortName());
            addressDOInDb.setIsOversea(addressDTO.getIsOversea());
            addressDOInDb.setTransportWendy(addressDTO.getTransportWendy());
            addressDOInDb.setName(addressDTO.getName());
            addressDOInDb.setParentCode(addressDTO.getParentCode());
            addressDOInDb.setSortName(addressDO.getSortName());
            int row = addressMapper.updateById(addressDOInDb);
            log.info("更新地址信息：{}", addressDTO);
            return row > 0;
        }
    }

    private void saveOrUpdateAddressDO(AddressDO address) {
        if (address.getId() == null) {
            addressMapper.insert(address);
            log.info("新增地址信息：{}", address);
        } else {
            addressMapper.updateById(address);
            log.info("更新地址信息：{}", address);
        }
    }

    @Override
    @Transactional
    public void save(String countryCode, String provinceCode, Integer transportWendy) {
        if (Objects.isNull(countryCode) || Objects.isNull(transportWendy)) {
            throw ExFactory.throwWith(ErrorCode.BUSINESS_ERROR, "保存地址时国家编码或运输温度不能为空");
        }
        if (TransportWendyEnum.getTransportWendyEnum(transportWendy) == null) {
            log.warn("运输温度不存在,countryCode:{},provinceCode:{}", countryCode, provinceCode);
            throw ExFactory.throwWith(ErrorCode.BUSINESS_ERROR, "运输温度不存在");
        }

        // 单省添加
        if (!Objects.isNull(provinceCode)) {
            // 校验该省是否存在
            AddressDO province = addressMapper.getByAddressCode(provinceCode);
            if (!Objects.isNull(province)) {
                log.info("该省/州名称:{} ,编码：{} 已存在, 执行更新操作!", province.getName(), provinceCode);
                // 对该省份的数据进行更新操作
                province.setTransportWendy(transportWendy);
                this.saveOrUpdateAddressDO(province);
                return;
            }

            // 判断该国家是否存在
            AddressDO country = addressMapper.getByAddressCode(countryCode);
            if (Objects.isNull(country)) {
                log.info("该国家编码：{} 不存在, 执行添加.", countryCode);
                // 不存在则添加
                country = convert2Address(findByAddressCode(countryCode), transportWendy);
                addressMapper.insert(country);
            }

            province = convert2Address(findByAddressCode(provinceCode), transportWendy);
            this.saveOrUpdateAddressDO(province);
            return;
        }

        // 批量添加
        // 查询当前国家下已存在的省份
        List<AddressDO> existAddressList = new ArrayList<>();
        List<AddressDO> notExistAddressList = new ArrayList<>();
        AddressDO country = addressMapper.getByAddressCode(countryCode);
        if (!Objects.isNull(country)) {
            existAddressList.addAll(addressMapper.getListByParentCode(countryCode));
        } else {
            notExistAddressList.add(convert2Address(findByAddressCode(countryCode), transportWendy));
        }

        List<String> existAddressCode = existAddressList.stream().map(AddressDO::getAddressCode).toList();
        // 找出当前国家下省份列表中不存在的省份列表
        List<BaseAddressDTO> addressListFromAddressService = findAllCountryByAddressLibrary(countryCode);
        List<AddressDO> provinceList = addressListFromAddressService.stream()
                .filter(province -> !existAddressCode.contains(province.getAddressCode()))
                .map(baseAddressDTO -> convert2Address(baseAddressDTO, transportWendy))
                .toList();
        notExistAddressList.addAll(provinceList);
        notExistAddressList.forEach(this::saveOrUpdateAddressDO);
    }

    private BaseAddressDTO findByAddressCode(String addressCode) {
        try {
            BaseAddressDTO baseAddressDTO = addressWrap.getByAddressCode(addressCode);
            if (baseAddressDTO == null) {
                log.warn("地址库中未查询到该地址信息! addressCode: {}", addressCode);
                throw ExFactory.throwWith(ErrorCode.BUSINESS_ERROR, "地址库中未查询到该地址信息!");
            }
            return baseAddressDTO;
        } catch (Exception e) {
            log.error("请求地址库服务异常! request: {}", addressCode, e);
            return null;
        }
    }

    private List<BaseAddressDTO> findAllCountryByAddressLibrary(String parentCode) {
        try {
            AddressQueryRequest request = new AddressQueryRequest();
            request.setParentCode(parentCode);
            request.setBusinessDomain("0");
            return addressWrap.queryByParentCode(request);
        } catch (Exception e) {
            log.error("请求地址库服务异常! parentCode: {}", parentCode, e);
            return Collections.emptyList();
        }
    }

    private AddressDO convert2Address(BaseAddressDTO baseAddressDTO, Integer transportWendy) {
        if (baseAddressDTO == null) {
            throw ExFactory.throwWith(ErrorCode.BUSINESS_ERROR, "基础数据不允许为空!");
        }

        AddressDO address = new AddressDO();
        address.setName(baseAddressDTO.getName());
        address.setAddressCode(baseAddressDTO.getAddressCode());
        address.setParentCode(baseAddressDTO.getParentCode());
        address.setType(baseAddressDTO.getType());
        address.setIsOversea(baseAddressDTO.getIsOversea());
        address.setSortName(baseAddressDTO.getSortName());
        address.setTransportWendy(transportWendy);
        address.setUpdateTime(new Date());
        address.setCreateTime(new Date());
        // TODO
        // address.setCreatedBy(ParamUtil.getCurrentLoginName());
        // address.setUpdatedBy(ParamUtil.getCurrentLoginName());
        // address.setTenantId(ParamUtil.getCurrentTenantId());
        return address;
    }

    private AddressDO convert2Address(AddressDTO addressDTO, Integer transportWendy) {
        if (addressDTO == null) {
            throw ExFactory.throwWith(ErrorCode.BUSINESS_ERROR, "基础数据不允许为空!");
        }

        AddressDO address = new AddressDO();
        address.setName(addressDTO.getName());
        address.setAddressCode(addressDTO.getAddressCode());
        address.setParentCode(addressDTO.getParentCode());
        address.setType(addressDTO.getType());
        address.setIsOversea(addressDTO.getIsOversea());
        address.setSortName(addressDTO.getSortName());
        if (transportWendy != null) {
            address.setTransportWendy(transportWendy);
        }
        address.setUpdateTime(new Date());
        address.setCreateTime(new Date());
        // TODO
        // address.setCreatedBy(ParamUtil.getCurrentLoginName());
        // address.setUpdatedBy(ParamUtil.getCurrentLoginName());
        // address.setTenantId(ParamUtil.getCurrentTenantId());
        return address;
    }

    @Override
    @Transactional
    public boolean deleteById(Long addressId) {
        int row = addressMapper.deleteById(addressId);
        log.info("逻辑删除地址信息，ID:{},row:{}", addressId, row);
        return row > 0;
    }

    @Override
    public boolean recoverById(Long addressId) {
        int row = addressMapper.recoverById(addressId);
        log.info("恢复逻辑删除的地址信息，ID:{},row:{}", addressId, row);
        return row > 0;
    }

    private AddressInfoDTO convert2AddressInfoDTO(AddressDO address) {
        AddressInfoDTO addressInfoDTO = new AddressInfoDTO();
        addressInfoDTO.setId(address.getId());
        AddressDO country = addressMapper.getByAddressCode(address.getParentCode());
        if (country != null) {
            addressInfoDTO.setCountry(convert2AddressDTO(country));
        }
        addressInfoDTO.setProvince(convert2AddressDTO(address));
        addressInfoDTO.setTransportWendy(addressInfoDTO.getProvince().getTransportWendyDesc());
        return addressInfoDTO;
    }

    private AddressDTO convert2AddressDTO(AddressDO address) {
        AddressDTO addressDTO = new AddressDTO();
        addressDTO.setId(address.getId());
        addressDTO.setAddressCode(address.getAddressCode());
        addressDTO.setProvinceId(address.getId());
        addressDTO.setName(address.getName());
        addressDTO.setParentCode(address.getParentCode());
        addressDTO.setType(address.getType());
        addressDTO.setSortName(address.getSortName());
        addressDTO.setIsOversea(address.getIsOversea());
        addressDTO.setCreateTime(address.getCreateTime());
        addressDTO.setCreateBy(address.getCreateBy());
        addressDTO.setUpdateTime(address.getUpdateTime());
        addressDTO.setUpdateBy(address.getUpdateBy());
        addressDTO.setVersion(address.getVersion());
        addressDTO.setTransportWendy(TransportWendyEnum.of(address.getTransportWendy()).getCode());
        addressDTO.setTransportWendyDesc(TransportWendyEnum.of(address.getTransportWendy()).getDesc());
        return addressDTO;
    }

}
