package com.accesscorporate.app.wms.server.biz.service.rule;

import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationDRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulAllocationDResponse;
import com.accesscorporate.app.wms.server.dal.entity.rule.RulAllocationDDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
public interface IRulAllocationDService {

    List<RulAllocationDResponse> queryByHId(Long hId);

    Boolean saveRulAllocationD(RulAllocationDRequest request);


//    RulAllocationDDO selectById(Long id);

}
