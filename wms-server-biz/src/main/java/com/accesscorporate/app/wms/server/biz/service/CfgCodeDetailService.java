package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.request.CfgCodeDetailPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.CfgCodeDetailSaveOrUpdateRequest;
import com.accesscorporate.app.wms.server.biz.params.response.CfgCodeDetailResponse;
import com.idanchuang.component.base.page.PageData;

public interface CfgCodeDetailService {

    PageData<CfgCodeDetailResponse> pageList(CfgCodeDetailPageRequest cfgCodeDetailPageRequest);

    void remove(String uuid);

    void save(CfgCodeDetailSaveOrUpdateRequest request);

    void update(CfgCodeDetailSaveOrUpdateRequest request);
}
