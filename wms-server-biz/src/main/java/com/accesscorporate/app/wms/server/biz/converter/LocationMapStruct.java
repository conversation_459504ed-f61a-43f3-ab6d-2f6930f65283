
package com.accesscorporate.app.wms.server.biz.converter;

import com.accesscorporate.app.wms.server.biz.params.request.LocationSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.LocationResponse;
import com.accesscorporate.app.wms.server.biz.service.PartitionService;
import com.accesscorporate.app.wms.server.dal.entity.Location;
import com.accesscorporate.app.wms.server.dal.entity.Partition;
import com.idanchuang.component.core.util.SpringUtil;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Objects;

@Mapper
public interface LocationMapStruct {

    LocationMapStruct INSTANCE = Mappers.getMapper(LocationMapStruct.class);
    @Mapping(source = "partitionId", target = "partitionName", qualifiedByName = "formatPartitionName")
    LocationResponse convertToResponse(Location location);

    Location convertToEntity(LocationResponse response);

    Location convertToEntity(LocationSaveRequest saveRequest);
    @Named("formatPartitionName")
    default String partitionName(Long partitionId){
        Partition partition = SpringUtil.getBean(PartitionService.class).getById(partitionId);
        if(Objects.isNull(partition)){
            return null;
        }
        return partition.getPartitionCode();
    }
}
