package com.accesscorporate.app.wms.server.biz.converter;

import com.accesscorporate.app.wms.server.biz.params.request.SkuCombiListRequest;
import com.accesscorporate.app.wms.server.biz.params.response.SkuCombiListResponse;
import com.accesscorporate.app.wms.server.biz.params.response.SkuCombiViewResponse;
import com.accesscorporate.app.wms.server.biz.service.dto.SkuCombiDetailDTO;
import com.accesscorporate.app.wms.server.biz.service.dto.SkuCombiHeaderDTO;
import com.accesscorporate.app.wms.server.dal.dto.SkuCombiListQuery;
import com.accesscorporate.app.wms.server.dal.entity.MdSkuCombiDetailDO;
import com.accesscorporate.app.wms.server.dal.entity.MdSkuCombiHeaderDO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liudongliang
 * @project : wms-server
 * @company : 浙江单创品牌管理有限公司
 * @desc :
 * Copyright @ 2024 danchuang Technology Co.Ltd. – Confidential and Proprietary
 */
public class SkuCombiConvert {

    public static SkuCombiListQuery convertToQuery(SkuCombiListRequest request) {
        SkuCombiListQuery skuCombiListQuery = new SkuCombiListQuery();
        skuCombiListQuery.setCombiBarcodeList(request.getCombiBarcodeList());
        skuCombiListQuery.setBarcodeList(request.getBarcodeList());
        skuCombiListQuery.setLocCodeList(request.getLocCodeList());
        skuCombiListQuery.setCreateTimeFm(request.getCreateTimeFm());
        skuCombiListQuery.setCreateTimeTo(request.getCreateTimeTo());
        return skuCombiListQuery;
    }

    public static List<SkuCombiListResponse> convertToResponseList(List<MdSkuCombiHeaderDO> items) {
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyList();
        }
        return items.stream().map(SkuCombiConvert::convertToResponse).collect(Collectors.toList());
    }

    public static SkuCombiListResponse convertToResponse(MdSkuCombiHeaderDO item) {
        SkuCombiListResponse skuCombiListResponse = new SkuCombiListResponse();
        skuCombiListResponse.setId(item.getId());
        skuCombiListResponse.setWarehouseId(item.getWarehouseId());
        skuCombiListResponse.setCombiBarcode(item.getCombiBarcode());
        skuCombiListResponse.setSign(item.getSign());
        skuCombiListResponse.setSkuCount(item.getSkuCount());
        skuCombiListResponse.setSkuTotal(item.getSkuTotal());
        skuCombiListResponse.setLocCode(item.getLocCode());
        skuCombiListResponse.setCreateTime(item.getCreateTime().getTime());
        skuCombiListResponse.setCreateBy(item.getCreateBy());
        return skuCombiListResponse;
    }

    public static MdSkuCombiHeaderDO convertToHeaderDO(SkuCombiHeaderDTO header) {
        MdSkuCombiHeaderDO mdSkuCombiHeaderDO = new MdSkuCombiHeaderDO();
        mdSkuCombiHeaderDO.setId(header.getId());
        mdSkuCombiHeaderDO.setCombiBarcode(header.getCombiBarcode());
        mdSkuCombiHeaderDO.setSign(header.getSign());
        mdSkuCombiHeaderDO.setSkuCount(header.getSkuCount());
        mdSkuCombiHeaderDO.setSkuTotal(header.getSkuTotal());
        mdSkuCombiHeaderDO.setLocCode(header.getLocCode());
        mdSkuCombiHeaderDO.setCreateTime(new Date());
        // TODO: 2024/11/11  获取当前线程上下文操作人
        //mdSkuCombiHeaderDO.setCreateBy();
        mdSkuCombiHeaderDO.setIsDeleted(0);
        mdSkuCombiHeaderDO.setWarehouseId(header.getWarehouseId());
        mdSkuCombiHeaderDO.setUpdateTime(new Date());
        return mdSkuCombiHeaderDO;
    }

    public static List<MdSkuCombiDetailDO> convertToDetails(List<SkuCombiDetailDTO> details) {
        return details.stream().map(SkuCombiConvert::convertToDetail).collect(Collectors.toList());
    }

    public static MdSkuCombiDetailDO convertToDetail(SkuCombiDetailDTO detail) {
        MdSkuCombiDetailDO mdSkuCombiDetailDO = new MdSkuCombiDetailDO();
        mdSkuCombiDetailDO.setId(detail.getId());
        mdSkuCombiDetailDO.setCombiBarcode(detail.getCombiBarcode());
        mdSkuCombiDetailDO.setSkuId(detail.getSkuId());
        mdSkuCombiDetailDO.setBarcode(detail.getBarcode());
        mdSkuCombiDetailDO.setGoodsGrade(detail.getGoodsGrade());
        mdSkuCombiDetailDO.setTotal(detail.getTotal());
        mdSkuCombiDetailDO.setCreateTime(new Date());
        // TODO: 2024/11/11  获取当前线程上下文操作人
        // mdSkuCombiDetailDO.setCreateBy();
        mdSkuCombiDetailDO.setIsDeleted(0);
        mdSkuCombiDetailDO.setWarehouseId(detail.getWarehouseId());
        mdSkuCombiDetailDO.setUpdateTime(new Date());
        return mdSkuCombiDetailDO;
    }

    public static List<SkuCombiViewResponse.SkuCombiItem> convertToViewItems(List<MdSkuCombiDetailDO> details) {
        return details.stream().map(SkuCombiConvert::convertToViewItem).collect(Collectors.toList());
    }

    public static SkuCombiViewResponse.SkuCombiItem convertToViewItem(MdSkuCombiDetailDO detail) {
        SkuCombiViewResponse.SkuCombiItem item = new SkuCombiViewResponse.SkuCombiItem();
        item.setBarcode(detail.getBarcode());
        // TODO: 2024/11/11 货品名称处理
        // item.setGoodsName();
        item.setQuantity(detail.getTotal());
        item.setGoodsGrade(detail.getGoodsGrade());
        // TODO: 2024/11/11 枚举翻译
        // item.setGoodsGradeDesc();
        return item;

    }

}
