package com.accesscorporate.app.wms.server.biz.manager;

import cn.hutool.core.util.StrUtil;
import com.accesscorporate.app.wms.server.dal.entity.RotationRuleHeaderDO;
import com.accesscorporate.app.wms.server.dal.mapper.IRotationRuleHeaderMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 补货规则-主规则-数据聚合层-manager
 *
 * <AUTHOR>
 * 2025/2/18  15:28
 */
@Component
@RequiredArgsConstructor
public class RotationRuleHeaderManager extends ServiceImpl<IRotationRuleHeaderMapper, RotationRuleHeaderDO> {


    public Page<RotationRuleHeaderDO> queryRulePage(String ruleCode, String description,
                                                    long current, long size) {
        return lambdaQuery()
                .eq(StrUtil.isNotBlank(ruleCode), RotationRuleHeaderDO::getRotationCode, ruleCode)
                .like(StrUtil.isNotBlank(description), RotationRuleHeaderDO::getRotationDesc, description)
                .eq(RotationRuleHeaderDO::getIsDeleted, 0)
                .page(new Page<>(current, size));
    }


    public List<RotationRuleHeaderDO> queryRulesByCode(String ruleCode) {
        return lambdaQuery()
                .eq(RotationRuleHeaderDO::getRotationCode, ruleCode)
                .eq(RotationRuleHeaderDO::getIsDeleted, 0)
                .list();
    }


    public void logicDeleteById(Long id) {
        lambdaUpdate()
                .eq(RotationRuleHeaderDO::getId, id)
                .set(RotationRuleHeaderDO::getIsDeleted, 1)
                .update();
    }


}
