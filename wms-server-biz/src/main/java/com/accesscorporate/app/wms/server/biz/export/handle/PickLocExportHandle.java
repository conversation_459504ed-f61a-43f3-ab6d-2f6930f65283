package com.accesscorporate.app.wms.server.biz.export.handle;

import com.accesscorporate.app.wms.server.biz.export.PickLocExcelExportVO;
import com.accesscorporate.app.wms.server.biz.params.request.pickloc.PickLocPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.response.pickloc.PickLocPageQueryResponse;
import com.accesscorporate.app.wms.server.biz.service.MdPickLocService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.idanchuang.component.base.page.PageData;
import com.idanchuang.component.core.util.CopyUtil;
import com.idanchuang.scm.excel.annotation.ExcelPropertyType;
import com.idanchuang.scm.excel.domain.ExcelRecordDTO;
import com.idanchuang.scm.excel.handler.ExcelPropertyHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-08-06 15:49
 * Description: 商品捡货位批量导出
 */
@Slf4j
@Service
@ExcelPropertyType("pickLocExportHandle")
public class PickLocExportHandle implements ExcelPropertyHandler<PickLocExcelExportVO> {

    @Autowired
    private MdPickLocService mdPickLocService;


    @Override
    public Class<PickLocExcelExportVO> getExcelHead(ExcelRecordDTO excelRecordDTO) {
        return PickLocExcelExportVO.class;
    }

    @Override
    public List<PickLocExcelExportVO> queryExcelData(int index, ExcelRecordDTO excelRecordDTO) {
        String param = excelRecordDTO.getData();
        PickLocPageQueryRequest originalQueryParam = JSON.parseObject(param, PickLocPageQueryRequest.class);
        originalQueryParam.setCurrent((long)index);
        originalQueryParam.setSize(500L);
        PageData<PickLocPageQueryResponse> pageData = mdPickLocService.pageQuery(originalQueryParam);
        if (CollectionUtils.isEmpty(pageData.getRecords())){
            return Lists.newArrayList();
        }
        return CopyUtil.copyList(pageData.getRecords(),PickLocExcelExportVO.class);
    }


}
