package com.accesscorporate.app.wms.server.biz.service.rule.impl;

import com.accesscorporate.app.wms.server.biz.converter.RulAllocationConverter;
import com.accesscorporate.app.wms.server.biz.manager.rule.RulAllocationDManager;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationDRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulAllocationDResponse;
import com.accesscorporate.app.wms.server.biz.service.rule.IRulAllocationDService;
import com.accesscorporate.app.wms.server.dal.entity.rule.RulAllocationDDO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RulAllocationDServiceImpl implements IRulAllocationDService {

    private final RulAllocationDManager rulAllocationDManager;

    @Override
    public List<RulAllocationDResponse> queryByHId(Long hId) {
        List<RulAllocationDDO> rulAllocationDDOS = rulAllocationDManager.queryByHId(hId);
        return rulAllocationDDOS.stream().map(RulAllocationConverter::toRulAllocationDResponse).toList();

    }

    @Override
    public Boolean saveRulAllocationD(RulAllocationDRequest request) {
        RulAllocationDDO rulAllocationDDO = RulAllocationConverter.toRulAllocationDDO(request);
        if (rulAllocationDDO.getId() != null) {
            return rulAllocationDManager.updateRulAllocationD(rulAllocationDDO);
        }
        return rulAllocationDManager.createRulAllocationD(rulAllocationDDO);
    }

}
