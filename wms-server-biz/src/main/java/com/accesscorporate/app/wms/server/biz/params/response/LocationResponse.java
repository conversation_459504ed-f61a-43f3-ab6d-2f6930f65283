package com.accesscorporate.app.wms.server.biz.params.response;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "库位列表信息响应体")
public class LocationResponse {
    private Long id; // id
    private String locCode; // 库位编码
    private String partitionName;// 库区
    private String locType; // 库位类型
    private Integer canMixProduct; // 产品混放
    private String aisle; // 行【通道】
    private String bay; // 贝【货架】
    private String lev; // 层【货架层】
    private String position; // 位置
    private String circleClass; // 周转需求类型
    private String packageType; // 下架方式
    private Integer notAutoRecmd; // 是否在上架计算临近货位时包括在内
    private Integer maxLpnQyt; // 最大 LPN 数量
    private BigDecimal volume; // 体积
    private BigDecimal weight; // 重量
    private BigDecimal length; // 长度
    private BigDecimal width; // 宽度
    private BigDecimal height; // 高度
    private BigDecimal axisX; // X 轴坐标
    private BigDecimal axisY; // Y 轴坐标
    private BigDecimal axisZ; // Z 轴坐标
    private BigDecimal fraction; // 容积率
    private Integer ignoreLpn; // 忽略 LPN
    private BigDecimal maxProdQty; // 最大产品数量
    private BigDecimal maxMixQty; // 最大混放数量
    private String putawaySeq; // 上架顺序
    private String pickSeq; // 拣货顺序
    private String countSeq; // 盘点顺序
    private Long merchantId; // 货主
    private Integer lockStatus; // 库位锁定状态
    private String chute; // 滑道号
    private Integer volumeType; // 体积类型
    private Integer lackFlag; // 疑似缺货标识
    private Integer useStatus; // 库位使用状态
    private Integer isEmpty; // 是否为空
}
