package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.service.RegionService;
import com.accesscorporate.app.wms.server.dal.entity.Region;
import com.accesscorporate.app.wms.server.dal.mapper.RegionMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idanchuang.component.base.exception.core.ExFactory;
import org.springframework.stereotype.Service;

@Service
public class RegionServiceImpl extends ServiceImpl<RegionMapper, Region> implements RegionService {
    @Override
    public boolean modify(Region region) {

        Region dbRecord = getBaseMapper().selectOne(new LambdaQueryWrapper<Region>().eq(Region::getRegionCode, region.getRegionCode()));
        if (dbRecord != null) {
            if( dbRecord.getId().equals(region.getId())) {
                dbRecord.setRegionCode(region.getRegionCode());
                dbRecord.setRegionName(region.getRegionName());
                dbRecord.setPipeline(region.getPipeline());
                getBaseMapper().updateById(dbRecord);
                return true;
            }else{
                ExFactory.throwBusiness("{} 同编码区域已存在", region.getRegionCode());
            }
        }
        getBaseMapper().insert(region);
        return true;
    }

}