package com.accesscorporate.app.wms.server.biz.utils;


import com.accesscorporate.app.wms.server.biz.service.ICfgConfigurationService;
import com.accesscorporate.app.wms.server.dal.entity.CfgConfigurationDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 配置工具类
 * 提供便捷的配置获取方法
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ConfigurationUtil {

    private static ICfgConfigurationService cfgConfigurationService;

    @Autowired
    public void setCfgConfigurationService(ICfgConfigurationService cfgConfigurationService) {
        ConfigurationUtil.cfgConfigurationService = cfgConfigurationService;
    }

    /**
     * 获取字符串配置值
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置值
     */
    public static String getString(String configNo, Long warehouseId, Long tenantId) {
        return getString(configNo, warehouseId, tenantId, null);
    }

    /**
     * 获取字符串配置值（带默认值）
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static String getString(String configNo, Long warehouseId, Long tenantId, String defaultValue) {
        try {
            return cfgConfigurationService.getStringValue(configNo, warehouseId, tenantId, defaultValue);
        } catch (Exception e) {
            log.error("获取字符串配置失败，configNo: {}, warehouseId: {}, tenantId: {}", 
                     configNo, warehouseId, tenantId, e);
            return defaultValue;
        }
    }

    /**
     * 获取整数配置值
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置值
     */
    public static Integer getInt(String configNo, Long warehouseId, Long tenantId) {
        return getInt(configNo, warehouseId, tenantId, null);
    }

    /**
     * 获取整数配置值（带默认值）
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static Integer getInt(String configNo, Long warehouseId, Long tenantId, Integer defaultValue) {
        try {
            return cfgConfigurationService.getIntValue(configNo, warehouseId, tenantId, defaultValue);
        } catch (Exception e) {
            log.error("获取整数配置失败，configNo: {}, warehouseId: {}, tenantId: {}", 
                     configNo, warehouseId, tenantId, e);
            return defaultValue;
        }
    }

    /**
     * 获取布尔配置值
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置值
     */
    public static Boolean getBoolean(String configNo, Long warehouseId, Long tenantId) {
        return getBoolean(configNo, warehouseId, tenantId, null);
    }

    /**
     * 获取布尔配置值（带默认值）
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static Boolean getBoolean(String configNo, Long warehouseId, Long tenantId, Boolean defaultValue) {
        try {
            return cfgConfigurationService.getBooleanValue(configNo, warehouseId, tenantId, defaultValue);
        } catch (Exception e) {
            log.error("获取布尔配置失败，configNo: {}, warehouseId: {}, tenantId: {}", 
                     configNo, warehouseId, tenantId, e);
            return defaultValue;
        }
    }

    /**
     * 获取配置对象
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置对象
     */
    public static CfgConfigurationDO getConfig(String configNo, Long warehouseId, Long tenantId) {
        try {
            return cfgConfigurationService.getConfig(configNo, warehouseId, tenantId);
        } catch (Exception e) {
            log.error("获取配置对象失败，configNo: {}, warehouseId: {}, tenantId: {}", 
                     configNo, warehouseId, tenantId, e);
            return null;
        }
    }

    /**
     * 根据配置类型获取配置列表
     * 
     * @param configType 配置类型
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置列表
     */
    public static List<CfgConfigurationDO> getConfigsByType(String configType, Long warehouseId, Long tenantId) {
        try {
            return cfgConfigurationService.listByConfigType(configType, warehouseId, tenantId);
        } catch (Exception e) {
            log.error("根据配置类型获取配置列表失败，configType: {}, warehouseId: {}, tenantId: {}", 
                     configType, warehouseId, tenantId, e);
            return null;
        }
    }

    /**
     * 根据配置级别获取配置列表
     * 
     * @param configLevel 配置级别
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置列表
     */
    public static List<CfgConfigurationDO> getConfigsByLevel(Integer configLevel, Long warehouseId, Long tenantId) {
        try {
            return cfgConfigurationService.listByConfigLevel(configLevel, warehouseId, tenantId);
        } catch (Exception e) {
            log.error("根据配置级别获取配置列表失败，configLevel: {}, warehouseId: {}, tenantId: {}", 
                     configLevel, warehouseId, tenantId, e);
            return null;
        }
    }

    /**
     * 检查配置是否存在
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 是否存在
     */
    public static Boolean configExists(String configNo, Long warehouseId, Long tenantId) {
        try {
            CfgConfigurationDO config = getConfig(configNo, warehouseId, tenantId);
            return config != null && config.getId() != null;
        } catch (Exception e) {
            log.error("检查配置是否存在失败，configNo: {}, warehouseId: {}, tenantId: {}", 
                     configNo, warehouseId, tenantId, e);
            return false;
        }
    }

    /**
     * 获取配置值并按分隔符分割为列表
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @param delimiter 分隔符
     * @return 字符串列表
     */
    public static List<String> getStringList(String configNo, Long warehouseId, Long tenantId, String delimiter) {
        String value = getString(configNo, warehouseId, tenantId);
        if (StringUtils.isNotBlank(value)) {
            return List.of(value.split(delimiter));
        }
        return List.of();
    }

    /**
     * 获取配置值并按逗号分割为列表
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 字符串列表
     */
    public static List<String> getStringList(String configNo, Long warehouseId, Long tenantId) {
        return getStringList(configNo, warehouseId, tenantId, ",");
    }

    /**
     * 配置级别枚举
     */
    public enum ConfigLevel {
        WAREHOUSE(0, "库房"),
        TENANT(1, "租户"),
        GLOBAL(2, "全局");

        private final Integer value;
        private final String description;

        ConfigLevel(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }
}
