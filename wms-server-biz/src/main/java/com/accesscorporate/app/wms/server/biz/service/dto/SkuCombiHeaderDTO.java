package com.accesscorporate.app.wms.server.biz.service.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> liudongliang
 * @project : wms-server
 * @company : 浙江单创品牌管理有限公司
 * @desc :
 * Copyright @ 2024 danchuang Technology Co.Ltd. – Confidential and Proprietary
 */
@Data
public class SkuCombiHeaderDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -6581159163687445995L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 组合条码
     */
    private String combiBarcode;

    /**
     * 组合特征 所有条码*数量*等级签名 用于确认组合唯一性
     */
    private String sign;

    /**
     * 品项数
     */
    private Integer skuCount;

    /**
     * 件数
     */
    private Integer skuTotal;

    /**
     * 库位编码
     */
    private String locCode;

    private Long warehouseId;

    private List<SkuCombiDetailDTO> details;

    public boolean sameBaseInfo(String combiBarcode, String locCode) {
        return Objects.equals(this.combiBarcode, combiBarcode) && Objects.equals(this.locCode, locCode);
    }
}
