package com.accesscorporate.app.wms.server.biz.utils;


import com.accesscorporate.app.wms.server.integration.redis.JedisDao;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * redis缓存实现
 * 
 * <AUTHOR>
 * @date 2021/9/6
 */
@Component
@Slf4j
public class RedisCacheProvider  {
   @Resource
    private JedisDao jedisDao;



    public JedisDao getDelegate() {
        return jedisDao;
    }

    public Object get(String region, String key) {
        return jedisDao.get(this.getKey(region, key));
    }

    /**
     *
     * @param region
     * @param key
     * @return 读取全部集合元素
     */
    public Set<Object> sget(String region, String key) {
        return jedisDao.sget(this.getKey(region, key));
    }

    /**
     *
     * @param region
     * @param key
     * @param field
     * @return 读取单个hash信息
     */
    public String hget(String region, String key,String field){
        return jedisDao.hget(this.getKey(region, key),field).toString();
    }

    /**
     *
     * @param region
     * @param key
     * @param value
     * @return 入参是否为集合元素
     */
    public Boolean isMember(String region, String key,Object value){
        return jedisDao.sismember(this.getKey(region, key),value);
    }
    /**
     * 批量查询k-v
     * 
     * @param region
     * @param keys
     * @return
     */
    public List batchGetObject(String region, List<String>keys){
        keys=keys.stream().map(key-> this.getKey(region,key)).collect(Collectors.toList());
        return jedisDao.batchGetObject(keys);
    }

    public void put(String region, String key, Object object) {
        jedisDao.set(this.getKey(region, key), object);
    }

    public void put(String region, String key, Object object,Integer expire) {
        jedisDao.set(this.getKey(region, key), object,expire);
    }

    public boolean putNx(String region, String key, Object object,Integer expire) {
        return jedisDao.setnx(this.getKey(region, key), object,expire);
    }

    /**
     * 批量插入缓存 k-v
     * @param region
     * @param entityMap
     */
    public void put(String region, Map<String, Object> entityMap) {
        if (MapUtils.isEmpty(entityMap)) {
            return;
        }
        final JedisDao finalJedisDao=jedisDao;
        Map<String, Object> cache = new HashMap(entityMap.size());
        entityMap.forEach((k, v) -> {
            cache.put(this.getKey(region, k), v);
        });
        finalJedisDao.batchSetString(cache);

    }

    public void remove(String region) {
        final JedisDao finalJedisDao=jedisDao;
        if(Objects.equals(region,"*")){
            finalJedisDao.delByPatternForTenant(region);
            return;
        }
        finalJedisDao.delByPattern("cache:" + region + "*");
    }

    public void remove(String region, String key) {
        if (key == "*") {
            this.remove(region);
        } else {
            jedisDao.del(this.getKey(region, key));
        }
    }

    public void clear() {
        jedisDao.delByPattern("cache*");
    }

    public <T> List<T> getAllCacheFromRegion(String region) {
        List<String> keys = jedisDao.getKeys(this.getKey(region, "*"));
        return (List<T>) jedisDao.batchGetObject(keys);
    }

    private String getKey(String region, String key) {
        return "cache:" + region + ":" + key;
    }
}
