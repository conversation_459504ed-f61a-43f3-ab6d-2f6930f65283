package com.accesscorporate.app.wms.server.biz.converter;

import com.accesscorporate.app.wms.server.biz.params.request.CfgCodeDetailSaveOrUpdateRequest;
import com.accesscorporate.app.wms.server.biz.params.response.CfgCodeDetailResponse;
import com.accesscorporate.app.wms.server.biz.params.response.CfgCodeMasterResponse;
import com.accesscorporate.app.wms.server.dal.entity.CfgCodeDetailDO;
import com.accesscorporate.app.wms.server.dal.entity.CfgCodeMasterDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CfgCodeDetailMapStruct {

    CfgCodeDetailMapStruct INSTANCE = Mappers.getMapper(CfgCodeDetailMapStruct.class);

    CfgCodeDetailResponse convertToResponse(CfgCodeDetailDO item);

    CfgCodeDetailDO convertToDO(CfgCodeDetailSaveOrUpdateRequest request);
}
