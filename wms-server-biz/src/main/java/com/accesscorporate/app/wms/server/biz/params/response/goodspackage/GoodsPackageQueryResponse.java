package com.accesscorporate.app.wms.server.biz.params.response.goodspackage;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-02-20 19:52
 * Description: 商品包装查询响应
 */
@Tag(name = "商品包装查询响应参数")
@Data
public class GoodsPackageQueryResponse {

    @Schema(title = "商品包装表主键id")
    private Long packageId;

    @Schema(title = "商品编码")
    private Long productCode;

    @Schema(title = "商品条码")
    private String barCode;

    @Schema(title = "商品名称")
    private String productCname;

    @Schema(title = "包装代码")
    private String pkgCode;

    @Schema(title = "包装描述")
    private String pkgDesc;



}
