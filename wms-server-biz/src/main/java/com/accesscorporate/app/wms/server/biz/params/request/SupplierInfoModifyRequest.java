package com.accesscorporate.app.wms.server.biz.params.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 供应商信息编辑 请求体
 *
 * <AUTHOR>
 * 2025/2/17  14:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "供应商信息编辑请求体")
public class SupplierInfoModifyRequest {

    @Schema(title = "供应商id")
    @NotNull(message = "供应商id不能为空")
    private Long id;

    @Schema(title = "供应商编码")
    private String supplierCode;

    @Schema(title = "供应商公司名称")
    private String supplierName;

    @Schema(title = "供应商类型")
    private Integer supplierType;

    @Schema(title = "送货方式")
    private Integer supplierDeliveryMethod;

    @Schema(title = "联系人")
    private String supplierContactManName;

    @Schema(title = "联系人手机")
    private String supplierContactMobile;

    @Schema(title = "联系人邮箱")
    private String supplierContactEmail;

    @Schema(title = "短信通知", description = "短信通知 0：不通知；1：通知")
    private Integer isSmsNoticeForPo;

    @Schema(title = "邮件通知", description = "邮件通知 0：不通知；1：通知")
    private Integer isEmailNoticeForPo;

    @Schema(title = "条码打印费")
    private BigDecimal barcodePrintFee;

    @Schema(title = "订货周期")
    private Integer poDays;

    @Schema(title = "是否提供发票", description = "是否提供发票(文本) 参考Enum")
    private Integer canInvoice;

    @Schema(title = "供应商来源", description = "（0：后台添加；1：供应商自己在网站注册）")
    private Integer supplierSource;

    @Schema(title = "最小订货金额")
    private BigDecimal minOrderAmount;

    @Schema(title = "付款日")
    private Integer paymentDay;

    @Schema(title = "税号")
    private String taxNumber;

    @Schema(title = "供应商状态", description = "0:申请中 1：正常")
    private Integer supplierStatus;


}
