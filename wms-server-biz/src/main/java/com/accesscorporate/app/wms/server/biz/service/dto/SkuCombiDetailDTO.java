package com.accesscorporate.app.wms.server.biz.service.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> liudongliang
 * @project : wms-server
 * @company : 浙江单创品牌管理有限公司
 * @desc :
 * Copyright @ 2024 danchuang Technology Co.Ltd. – Confidential and Proprietary
 */
@Data
public class SkuCombiDetailDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1760034752451990402L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 组合条码
     */
    private String combiBarcode;

    /**
     * 商品条码
     */
    private String barcode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 货品等级
     */
    private String goodsGrade;

    /**
     * 件数
     */
    private Integer total;

    private Long skuId;

    private Long warehouseId;
}
