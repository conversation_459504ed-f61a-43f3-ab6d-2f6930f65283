package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.manager.impl.DocAlcHeaderManager;
import com.accesscorporate.app.wms.server.biz.mq.message.AllocateMessage;
import com.accesscorporate.app.wms.server.biz.service.AllocateService;
import com.accesscorporate.app.wms.server.biz.service.DocDoHeaderService;
import com.accesscorporate.app.wms.server.biz.utils.Config;
import com.accesscorporate.app.wms.server.common.constant.Constants;
import com.accesscorporate.app.wms.server.common.enums.Keys;
import com.accesscorporate.app.wms.server.dal.entity.DocAlcHeader;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 自动分配服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class AllocateServiceImpl implements AllocateService {

    @Resource
    private DocAlcHeaderManager docAlcHeaderManager;
    @Resource
    private DocDoHeaderService deliveryOrderService;

    @Override
    public void processAllocate(AllocateMessage allocateMessage) {
//        log.info("开始处理分配任务，仓库ID:{}, 批次大小:{}",
//            allocateMessage.getWarehouseId(), allocateMessage.getBatchSize());
//
//        try {
//            // 执行具体的分配逻辑
//            executeAllocateByWarehouse(allocateMessage.getWarehouseId(), allocateMessage.getBatchSize());
//
//            log.info("分配任务处理完成，仓库ID:{}", allocateMessage.getWarehouseId());
//
//        } catch (Exception e) {
//            log.error("分配任务处理失败，仓库ID:{}", allocateMessage.getWarehouseId(), e);
//            throw e;
//        }
    }

    @Override
    public void executeAllocateByWarehouse(Long warehouseId, Integer batchSize) {
        log.info("执行仓库分配逻辑，仓库ID:{}, 批次大小:{}", warehouseId, batchSize);
        
        try {
            // TODO: 实现具体的分配业务逻辑
            // 1. 查询待分配的订单/出库单
            // 2. 根据分配规则进行库存分配
            // 3. 更新分配结果
            
            // 示例逻辑框架：
            processAllocateOrders(warehouseId, batchSize);
            
        } catch (Exception e) {
            log.error("执行仓库分配逻辑失败，仓库ID:{}", warehouseId, e);
            throw e;
        }
    }

    @Override
    public List<Long> queryNeedAllocateDoHeaderIds(int batchAllocateNum, Long warehouseId) {
        //按照订单的导入时间进行排序（先进先分配）
        Integer failMaxNum = this.getAllocateFailNum();
        Integer failCheck = this.getFailCheckConfig();
        LambdaQueryWrapper<DocAlcHeader> wrapper = new LambdaQueryWrapper<DocAlcHeader>();
        wrapper.eq(DocAlcHeader::getWarehouseId, warehouseId)
                .eq(DocAlcHeader::getIsDeleted, Boolean.FALSE)
                .eq(DocAlcHeader::getReleaseStatus, Constants.ReleaseStatus.RELEASE.getValue())
                .in(DocAlcHeader::getStatus, Lists.newArrayList(Constants.DoStatus.INITIAL.getValue(), Constants.DoStatus.PARTALLOCATED.getValue() ))
                .in(DocAlcHeader::getDoType, Lists.newArrayList(Constants.DoType.SELL.getValue(), Constants.DoType.WHOLESALE.getValue()))
                .eq(failCheck==1,DocAlcHeader::getAllocateFailNum, failMaxNum)
                .and(wrapper1 -> wrapper1
                        .eq(DocAlcHeader::getAllocTime, "0000-00-00 00:00:00")
                        .or()
                        .isNull(DocAlcHeader::getAllocTime).or().eq(DocAlcHeader::getReplStatus, Constants.DoReplStatus.COMPLETE.getValue()))
                .orderByAsc(DocAlcHeader::getDoCreateTime)
        ;

        List<Long> alcHeaderIds = docAlcHeaderManager.list(new Page<>(0, batchAllocateNum),wrapper)
                .stream().map(DocAlcHeader::getId).collect(Collectors.toList());
        return alcHeaderIds;
    }
    @Transactional
    @Override
    public Map<String, List<String>> autoAllocate(Long alcId, List<Long> detailIds) throws Exception {
        if (alcId == null) {
            return Maps.newHashMap();
        }
        DocAlcHeader alcHeader = docAlcHeaderManager.getById(alcId);
        if (alcHeader == null){
            return Maps.newHashMap();
        }
        //DO取消拦截
        if (deliveryOrderService.doCancelIntercept(alcHeader.getId())){
            Map<String, List<String>> map =  Maps.newHashMap();
            map.put(IDoAllocate.DO_CANCEL,Lists.<String>newArrayList());
            return map;
        }

        assignAutoCheck(alcHeader);

        alcHeader.setReplStatus(Constants.DoReplStatus.NONE.getValue());

        List<DoAllocateDetail> needFrozenAlcDetails = new ArrayList<DoAllocateDetail>();
        Map<String, List<String>> allocateResult = Maps.newHashMap();
        // 分配忽略区域, 默认全区域可分配
//        List<DoAllocateDetail> alcDetails = alcHeader.getDoAllocateDetails();
//        allocateDetail(ids, alcHeader, alcDetails, needFrozenAlcDetails, allocateResult, null);
//        checkDoStatusAfterAllocate(alcHeader);
//
//        if (!ListUtil.isNullOrEmpty(needFrozenAlcDetails)) {
//            autoFrozenOnAllocate(alcHeader.getId(), needFrozenAlcDetails, Constants.Reason.ALLOC_LACK.getValue(), ResourceUtils.getDispalyString("frozen.holdWho.default"));
//        }

        return allocateResult;
    }
    @Transactional
    @Override
    public Map<String, List<String>> autoAllocate(Long doId) throws Exception {
        return autoAllocate(doId, Lists.<Long>newArrayList());
    }

    public Integer getAllocateFailNum(){
        return Config.getInt(Keys.Delivery.auto_allocate_job_allocate_fail_num, Config.ConfigLevel.GLOBAL, 5);
    }
    public Integer getFailCheckConfig(){
        Integer failCheck = Config.getInt(Keys.Delivery.auto_allocate_job_allocate_check_num, Config.ConfigLevel.GLOBAL, 1);
        return failCheck;
    }
    /**
     * 处理分配订单
     * 
     * @param warehouseId 仓库ID
     * @param batchSize 批次大小
     */
    private void processAllocateOrders(Long warehouseId, Integer batchSize) {
        log.info("开始处理分配订单，仓库ID:{}, 批次大小:{}", warehouseId, batchSize);
        
        // TODO: 根据实际业务需求实现以下逻辑：
        
        // 1. 查询待分配的出库单/订单
        // List<OutboundOrder> pendingOrders = queryPendingAllocateOrders(warehouseId, batchSize);
        
        // 2. 遍历处理每个订单
        // for (OutboundOrder order : pendingOrders) {
        //     try {
        //         // 执行分配逻辑
        //         allocateOrderInventory(order);
        //         
        //         // 更新订单状态
        //         updateOrderStatus(order);
        //         
        //     } catch (Exception e) {
        //         log.error("处理订单分配失败，订单ID:{}", order.getId(), e);
        //         // 可以选择继续处理下一个订单或者抛出异常
        //     }
        // }
        
        // 临时实现：模拟处理逻辑
        simulateAllocateProcess(warehouseId, batchSize);
        
        log.info("分配订单处理完成，仓库ID:{}", warehouseId);
    }
    
    /**
     * 模拟分配处理过程
     * TODO: 替换为实际的业务逻辑
     */
    private void simulateAllocateProcess(Long warehouseId, Integer batchSize) {
        log.info("模拟分配处理，仓库ID:{}, 批次大小:{}", warehouseId, batchSize);
        
        // 模拟处理时间
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.info("模拟分配处理完成，仓库ID:{}", warehouseId);
    }
    
    // TODO: 实现以下方法
    
    /**
     * 查询待分配的订单
     */
    // private List<OutboundOrder> queryPendingAllocateOrders(Long warehouseId, Integer batchSize) {
    //     // 实现查询逻辑
    //     return Collections.emptyList();
    // }
    
    /**
     * 执行订单库存分配
     */
    // private void allocateOrderInventory(OutboundOrder order) {
    //     // 实现分配逻辑
    // }
    
    /**
     * 更新订单状态
     */
    // private void updateOrderStatus(OutboundOrder order) {
    //     // 实现状态更新逻辑
    // }
}
