package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.converter.WarehouseConverter;
import com.accesscorporate.app.wms.server.biz.params.response.TenantInfoResponse;
import com.accesscorporate.app.wms.server.biz.params.response.WarehouseBaseInfoResponse;
import com.accesscorporate.app.wms.server.biz.manager.WarehouseManager;
import com.accesscorporate.app.wms.server.biz.service.IAuthPermissionService;
import com.accesscorporate.app.wms.server.common.config.WmsAuthPermissionProperties;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import com.accesscorporate.app.wms.server.dal.entity.WarehouseDO;
import com.accesscorporate.app.wms.server.integration.dto.TenantResponse;
import com.accesscorporate.app.wms.server.integration.wrap.IUserPermissionWrap;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 认证权限--Service Impl
 *
 * <AUTHOR>
 * 2025/2/8  11:27
 */
@Service
@RequiredArgsConstructor
public class AuthPermissionServiceImpl implements IAuthPermissionService {

    private final WarehouseManager warehouseManager;

    private final IUserPermissionWrap iUserPermissionWrap;

    private final WmsAuthPermissionProperties wmsAuthPermissionProperties;

    @Override
    public List<TenantInfoResponse> queryUserTenantInfo() {
        List<TenantResponse> tenantResponses = iUserPermissionWrap.queryUserTenantPermission(UserContextAssistant.getUserId());
        if (tenantResponses.isEmpty()) {
            return List.of();
        }
        Map<Long, String> tenantMap = wmsAuthPermissionProperties.getTenantMap();
        List<Long> tenantIds = tenantResponses.stream().map(TenantResponse::getTenantId).distinct().toList();
        return tenantIds.stream()
                .map(tenantId -> {
                    String tenantName = tenantMap.getOrDefault(tenantId, "");
                    return new TenantInfoResponse(tenantId, tenantName);
                }).toList();
    }

    @Override
    public List<WarehouseBaseInfoResponse> queryUserBindWhBaseInfo() {

        List<String> warehouseCodes = iUserPermissionWrap.queryUserWarehousePermission(UserContextAssistant.getUserId());
        List<WarehouseDO> warehouseDOS = warehouseManager.queryWhByCodes(warehouseCodes);

        return warehouseDOS.stream()
                .map(WarehouseConverter::toWarehouseBaseInfoResponse)
                .toList();
    }


    @Override
    public List<WarehouseBaseInfoResponse> queryAllWhBaseInfo() {
        List<WarehouseDO> warehouseDOS = warehouseManager.queryAllWarehouseList();
        return warehouseDOS.stream()
                .map(WarehouseConverter::toWarehouseBaseInfoResponse)
                .toList();
    }

}
