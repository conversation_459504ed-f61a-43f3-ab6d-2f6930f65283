package com.accesscorporate.app.wms.server.biz.service.impl;

import cn.hutool.core.lang.Assert;
import com.accesscorporate.app.wms.server.biz.converter.StockCountRuleConverter;
import com.accesscorporate.app.wms.server.biz.manager.StockCountRuleManager;
import com.accesscorporate.app.wms.server.biz.params.request.StocktakingRuleCreateRequest;
import com.accesscorporate.app.wms.server.biz.params.request.StocktakingRuleModifyRequest;
import com.accesscorporate.app.wms.server.biz.params.request.StocktakingRulesQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.response.StocktakingRuleDetailResponse;
import com.accesscorporate.app.wms.server.biz.params.response.StocktakingRuleResponse;
import com.accesscorporate.app.wms.server.biz.service.IStocktakingRuleService;
import com.accesscorporate.app.wms.server.dal.entity.StockCountRuleDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.page.PageData;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 盘点规则 Service --实现类
 *
 * <AUTHOR>
 * 2025/2/14  13:06
 */
@Service
@RequiredArgsConstructor
public class StocktakingRuleServiceImpl implements IStocktakingRuleService {

    private final StockCountRuleManager stockCountRuleManager;


    @Override
    public PageData<StocktakingRuleResponse> queryStocktakingRules(StocktakingRulesQueryPageRequest request) {
        Page<StockCountRuleDO> stockCountRuleDOPage = stockCountRuleManager.queryStockCountRulePage(
                request.getRuleCode(),
                request.getRuleName(),
                request.getCurrent(),
                request.getSize()
        );

        List<StocktakingRuleResponse> resRecords = stockCountRuleDOPage.getRecords()
                .stream()
                .map(StockCountRuleConverter::toStocktakingRulesResponse)
                .toList();
        return PageData.of(resRecords, request.getCurrent(), request.getSize(), stockCountRuleDOPage.getTotal());
    }

    @Override
    public StocktakingRuleDetailResponse queryStocktakingRuleDetail(Long id) {
        StockCountRuleDO stockCountRuleDO = stockCountRuleManager.queryStockCountRuleDetailBy(id);
        Assert.notNull(stockCountRuleDO, "盘点规则不存在");
        return StockCountRuleConverter.toStocktakingRuleDetailResponse(stockCountRuleDO);

    }

    @Override
    public Boolean modifyStocktakingRule(StocktakingRuleModifyRequest request) {
        StockCountRuleDO stockCountRuleDO = StockCountRuleConverter.toStockCountRuleDO(request);
        return stockCountRuleManager.modifyStockCountRule(stockCountRuleDO);
    }

    @Override
    public Boolean createStocktakingRule(StocktakingRuleCreateRequest request) {
        StockCountRuleDO stockCountRuleDO = StockCountRuleConverter.toStockCountRuleDO(request);
        return stockCountRuleManager.createStockCountRule(stockCountRuleDO);
    }
}
