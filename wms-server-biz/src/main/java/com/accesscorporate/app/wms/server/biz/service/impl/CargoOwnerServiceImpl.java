package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.converter.CargoOwnerConverter;
import com.accesscorporate.app.wms.server.biz.excel.model.CargoOwnerImportModel;
import com.accesscorporate.app.wms.server.biz.manager.CargoOwnerManager;
import com.accesscorporate.app.wms.server.biz.params.request.CargoOwnerPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.response.CargoOwnerInfoResponse;
import com.accesscorporate.app.wms.server.biz.service.ICargoOwnerService;
import com.accesscorporate.app.wms.server.dal.entity.CargoOwnerDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.exception.core.ExFactory;
import com.idanchuang.component.base.page.PageData;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 货主管理-Service
 *
 * <AUTHOR>
 * 2025/2/8  17:23
 */
@Service
@RequiredArgsConstructor
public class CargoOwnerServiceImpl implements ICargoOwnerService {

    private final CargoOwnerManager cargoOwnerManager;


    @Override
    public PageData<CargoOwnerInfoResponse> queryCargoOwnerPage(CargoOwnerPageQueryRequest request) {
        //分页查询
        Page<CargoOwnerDO> cargoOwnerDOPage = cargoOwnerManager.queryCargoOwnerPage(request.getCargoOwnerCode(),
                request.getCargoOwnerName(),
                request.getCurrent(),
                request.getSize());

        List<CargoOwnerInfoResponse> resRecords = cargoOwnerDOPage.getRecords()
                .stream().map(CargoOwnerConverter::toCargoOwnerInfoResponse)
                .toList();

        return PageData.of(
                resRecords,
                request.getCurrent(),
                request.getSize(),
                cargoOwnerDOPage.getTotal()
        );
    }

    @Override
    public Boolean importCargoOwnerProcessItems(List<CargoOwnerImportModel> models) {
        this.importCheck(models);
        List<CargoOwnerDO> cargoOwnerDOS = models.stream().map(CargoOwnerConverter::toCargoOwnerDO).toList();
        return cargoOwnerManager.saveBatch(cargoOwnerDOS);
    }


    private void importCheck(List<CargoOwnerImportModel> models) {
        // 1. 检查重复项
        Map<String, Long> codeFrequency = models.stream()
                .map(CargoOwnerImportModel::getCargoOwnerCode)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

        List<String> duplicateCodes = codeFrequency.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (!duplicateCodes.isEmpty()) {
            ExFactory.throwBusiness("货主编码重复: {}", String.join(", ", duplicateCodes));
        }

        // 2. 检查货主编码是否已存在
        List<String> codesToCheck = models.stream()
                .map(CargoOwnerImportModel::getCargoOwnerCode)
                .collect(Collectors.toList());

        // 批量检查货主编码是否存在
        List<String> existingCodes = getExistingCargoOwnerCodes(codesToCheck);

        if (!existingCodes.isEmpty()) {
            ExFactory.throwBusiness("货主编码已存在: {}", String.join(", ", existingCodes));
        }

    }

    private List<String> getExistingCargoOwnerCodes(List<String> codesToCheck) {
        return cargoOwnerManager.lambdaQuery()
                .in(CargoOwnerDO::getMerchantCode, codesToCheck)
                .list()
                .stream()
                .map(CargoOwnerDO::getMerchantCode)
                .collect(Collectors.toList());
    }

}
