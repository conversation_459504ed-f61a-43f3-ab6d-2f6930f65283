package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.converter.SupplierConverter;
import com.accesscorporate.app.wms.server.biz.excel.model.SupplierImportModel;
import com.accesscorporate.app.wms.server.biz.manager.SupplierManager;
import com.accesscorporate.app.wms.server.biz.params.request.SupplierInfoModifyRequest;
import com.accesscorporate.app.wms.server.biz.params.request.SupplierPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.response.SupplierInfoResponse;
import com.accesscorporate.app.wms.server.biz.service.ISupplierService;
import com.accesscorporate.app.wms.server.dal.entity.SupplierDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.exception.core.ExFactory;
import com.idanchuang.component.base.page.PageData;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 供应商管理-Service-IMPL
 *
 * <AUTHOR>
 * 2025/2/8  16:49
 */
@Service
@RequiredArgsConstructor
public class SupplierServiceImpl implements ISupplierService {

    private final SupplierManager supplierManager;

    @Override
    public PageData<SupplierInfoResponse> querySupplierPage(SupplierPageQueryRequest request) {
        //分页查询
        Page<SupplierDO> supplierDOPage = supplierManager.querySupplierPage(request.getSupplierCode(),
                request.getSupplierName(),
                request.getCurrent(),
                request.getSize());

        List<SupplierInfoResponse> resRecords = supplierDOPage.getRecords()
                .stream().map(SupplierConverter::toSupplierInfoResponse)
                .toList();

        return PageData.of(
                resRecords,
                request.getCurrent(),
                request.getSize(),
                supplierDOPage.getTotal());
    }


    @Override
    public Boolean modifySupplier(SupplierInfoModifyRequest request) {
        SupplierDO supplierDO = SupplierConverter.toSupplierDO(request);
        return supplierManager.modifySupplier(supplierDO);
    }

    @Override
    public Boolean importSupplierProcessItems(List<SupplierImportModel> supplierImportModels) {
        // 前置校验
        this.importCheck(supplierImportModels);
        // 数据转换
        List<SupplierDO> supplierDOS = supplierImportModels.stream()
                .map(SupplierConverter::toSupplierDO)
                .toList();
        return supplierManager.saveBatch(supplierDOS);
    }


    private void importCheck(List<SupplierImportModel> models) {
        // 1. 检查重复项
        List<String> duplicateCodes = models.stream()
                .map(SupplierImportModel::getSupplierCode)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (!duplicateCodes.isEmpty()) {
            ExFactory.throwBusiness("供应商编码重复: {}", String.join(", ", duplicateCodes));
        }

        // 2. 检查货主编码是否已存在
        List<String> codesToCheck = models.stream()
                .map(SupplierImportModel::getSupplierCode)
                .collect(Collectors.toList());

        // 批量检查货主编码是否存在
        List<String> existingCodes = getExistingCargoOwnerCodes(codesToCheck);

        if (!existingCodes.isEmpty()) {
            ExFactory.throwBusiness("货主编码已存在: {}", String.join(", ", existingCodes));
        }

    }

    private List<String> getExistingCargoOwnerCodes(List<String> codesToCheck) {
        return supplierManager.lambdaQuery()
                .in(SupplierDO::getSupplierCode, codesToCheck)
                .list()
                .stream()
                .map(SupplierDO::getSupplierCode)
                .collect(Collectors.toList());
    }
}
