package com.accesscorporate.app.wms.server.biz.converter;

import com.accesscorporate.app.wms.server.biz.params.request.CfgSnRuleRequest;
import com.accesscorporate.app.wms.server.biz.params.response.CfgSnRuleResponse;
import com.accesscorporate.app.wms.server.dal.entity.SequenceRule;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/5 下午6:21 星期三
 */
@Mapper
public interface CfgSnRuleMapStruct {
    CfgSnRuleMapStruct INSTANCE = Mappers.getMapper(CfgSnRuleMapStruct.class);

    SequenceRule convertToDO(CfgSnRuleRequest item);

    CfgSnRuleResponse convertToResponse(SequenceRule item);
}
