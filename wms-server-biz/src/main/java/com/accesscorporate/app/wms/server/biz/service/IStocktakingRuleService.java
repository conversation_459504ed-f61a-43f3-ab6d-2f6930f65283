package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.request.StocktakingRuleCreateRequest;
import com.accesscorporate.app.wms.server.biz.params.request.StocktakingRuleModifyRequest;
import com.accesscorporate.app.wms.server.biz.params.request.StocktakingRulesQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.response.StocktakingRuleDetailResponse;
import com.accesscorporate.app.wms.server.biz.params.response.StocktakingRuleResponse;
import com.idanchuang.component.base.page.PageData;

/**
 * 盘点规则--Service
 *
 * <AUTHOR>
 * 2025/2/14  13:06
 */
public interface IStocktakingRuleService {


    /**
     * 盘点规则主列表查询
     *
     * @param request: 查询条件
     * @return PageData<StocktakingRulesResponse>
     * <AUTHOR>
     * 2025/2/14 16:32
     */
    PageData<StocktakingRuleResponse> queryStocktakingRules(StocktakingRulesQueryPageRequest request);


    /**
     * 盘点规则详情查询
     *
     * @param id: 主键
     * @return StocktakingRuleDetail
     * <AUTHOR>
     * 2025/2/14 16:32
     */
    StocktakingRuleDetailResponse queryStocktakingRuleDetail(Long id);


    /**
     * 修改盘点规则
     *
     * @param request: 修改参数
     * @return Boolean
     * <AUTHOR>
     * 2025/2/18 10:55
     */
    Boolean modifyStocktakingRule(StocktakingRuleModifyRequest request);


    /**
     * 新增盘点规则
     *
     * @param request: 新增参数
     * @return Boolean
     * <AUTHOR>
     * 2025/2/18 10:56
     */
    Boolean createStocktakingRule(StocktakingRuleCreateRequest request);
}
