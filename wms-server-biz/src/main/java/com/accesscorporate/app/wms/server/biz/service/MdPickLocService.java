package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.request.pickloc.PickLocPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.request.pickloc.PickLocSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.pickloc.PickLocPageQueryResponse;
import com.accesscorporate.app.wms.server.biz.service.dto.PickLocImportDTO;
import com.idanchuang.component.base.page.PageData;

import java.util.List;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-20 10:54
 * Description: 商品捡货位管理
 */
public interface MdPickLocService {

    /**
     * 商品捡货位列表查询
     */
    PageData<PickLocPageQueryResponse> pageQuery(PickLocPageQueryRequest queryParam);

    /**
     * 新增&编辑商品捡货位
     */
    void saveOrUpdatePickLoc(PickLocSaveRequest request);


    /**
     * 删除货品捡货位信息
     */
    void deletePickLocById(Long pickLocId);

    /**
     * 批量导入
     */
    void batchImportPickLoc(List<PickLocImportDTO> importList);


}
