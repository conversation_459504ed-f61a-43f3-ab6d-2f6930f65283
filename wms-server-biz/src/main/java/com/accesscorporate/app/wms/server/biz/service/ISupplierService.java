package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.excel.model.SupplierImportModel;
import com.accesscorporate.app.wms.server.biz.params.request.SupplierInfoModifyRequest;
import com.accesscorporate.app.wms.server.biz.params.request.SupplierPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.response.SupplierInfoResponse;
import com.idanchuang.component.base.page.PageData;

import java.util.List;

/**
 * 供应商管理-Service
 *
 * <AUTHOR>
 * 2025/2/8  16:48
 */
public interface ISupplierService {


    /**
     * 分页查询-供应商信息列表
     *
     * @param request: 查询参数
     * @return PageData<SupplierInfoResponse>
     * <AUTHOR>
     * 2025/2/8 16:50
     */
    PageData<SupplierInfoResponse> querySupplierPage(SupplierPageQueryRequest request);


    /**
     * 供应商信息修改
     *
     * @param request: 修改参数
     * @return Boolean
     * <AUTHOR>
     * 2025/2/17 16:26
     */
    Boolean modifySupplier(SupplierInfoModifyRequest request);


    /**
     * 供应商导入处理
     *
     * @param supplierImportModels:导入数据
     * @return Boolean
     * <AUTHOR>
     * 2025/2/25 12:58
     */
    Boolean importSupplierProcessItems(List<SupplierImportModel> supplierImportModels);
}
