package com.accesscorporate.app.wms.server.biz.params.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 补货规则新增-参数
 *
 * <AUTHOR>
 * 2025/2/19  14:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "补货规则新增-参数")
public class ReplenishmentRuleCreateRequest {

    @Schema(title = "规则编码")
    @NotBlank(message = "规则编码不能为空")
    private String ruleCode;

    @Schema(title = "规则描述")
    @NotBlank(message = "规则描述不能为空")
    private String ruleDesc;


    //TODO @guominghao ：单据类型这里需要取枚举
    @Schema(title = "单据类型", description = "这里是单据类型Code")
    private String orderType;

    @Schema(title = "是否效期控制")
    @NotNull(message = "是否效期控制必选")
    private Integer isExpirationDateControl;

    @Schema(title = "是否激活")
    private Integer isActivation;

    @Schema(title = "是否优先分配RETURN库区库存", description = "默认1")
    private Integer partitionCtrl = 1;


}
