package com.accesscorporate.app.wms.server.biz.params.request;

import com.idanchuang.component.base.page.PageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 盘点规则主列表响应-Request
 *
 * <AUTHOR>
 * 2025/2/14  15:05
 */

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "盘点规则主列表查询参数")
public class StocktakingRulesQueryPageRequest extends PageDTO {

    @Schema(title = "规则编码")
    private String ruleCode;

    @Schema(title = "规则名称", description = "规则名称,支持模糊查询")
    private String ruleName;

}
