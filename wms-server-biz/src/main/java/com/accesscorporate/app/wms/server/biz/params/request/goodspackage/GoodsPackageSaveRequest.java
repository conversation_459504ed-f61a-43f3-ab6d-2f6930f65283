package com.accesscorporate.app.wms.server.biz.params.request.goodspackage;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-02-20 10:52
 * Description: 保存包装信息请求参数
 */
@Tag(name = "保存包装信息请求参数")
@Data
public class GoodsPackageSaveRequest {

    @Schema(title = "商品包装表主键id")
    private Long packageId;

    @Schema(title = "商品编码")
    @NotBlank(message = "商品编码不能为空")
    private String productCode;

    @Schema(title = "包装代码")
    @NotBlank(message = "包装代码不能为空")
    private String pkgCode;

    @Schema(title = "包装描述")
    @NotBlank(message = "包装描述不能为空")
    private String pkgDesc;


}
