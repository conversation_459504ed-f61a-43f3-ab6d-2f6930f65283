package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.request.LocationSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.LocationResponse;
import com.accesscorporate.app.wms.server.dal.entity.Location;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface LocationService extends IService<Location> {
    List<LocationResponse> listAll();

    LocationResponse get(Long id);

    boolean save(LocationSaveRequest saveRequest);

    boolean removeById(Long id);

    Page<LocationResponse> page(Integer current, Integer size, String locCode, Long partitionId, String locType, Integer canMixProduct, Integer ignoreLpn, String aisleFrom, String aisleTo, String bayFrom, String bayTo, String levelFrom, String levelTo, String positionFrom, String positionTo);

    Location queryLocationByCodeAndWarehouseId(String locCode,Long warehouseId);


    List<Location> queryLocationByCodes(List<String> locCodes,Long warehouseId);



} 