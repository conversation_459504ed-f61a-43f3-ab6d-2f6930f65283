package com.accesscorporate.app.wms.server.biz.params.request;

import com.idanchuang.component.base.page.PageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 补货规则列表查询-参数
 *
 * <AUTHOR>
 * 2025/2/18  17:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "补货规则列表查询-参数")
public class ReplenishmentRuleQueryRequest extends PageDTO {

    @Schema(title = "规则编码")
    private String ruleCode;

    @Schema(title = "规则描述", description = "支持模糊查询")
    private String description;

}
