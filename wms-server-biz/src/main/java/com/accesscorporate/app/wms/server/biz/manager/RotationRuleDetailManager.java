package com.accesscorporate.app.wms.server.biz.manager;

import com.accesscorporate.app.wms.server.dal.entity.RotationRuleDetailDO;
import com.accesscorporate.app.wms.server.dal.mapper.IRotationRuleDetailMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 补货规则-明细-数据聚合层-manager
 *
 * <AUTHOR>
 * 2025/2/18  15:29
 */
@Component
@RequiredArgsConstructor
public class RotationRuleDetailManager extends ServiceImpl<IRotationRuleDetailMapper, RotationRuleDetailDO> {


    public void logicDeleteByRuleId(Long ruleId) {
        lambdaUpdate()
                .eq(RotationRuleDetailDO::getRHId, ruleId)
                .set(RotationRuleDetailDO::getIsDeleted, 1)
                .update();
    }


    public Boolean logicDeleteById(Long id) {
        return lambdaUpdate()
                .eq(RotationRuleDetailDO::getId, id)
                .set(RotationRuleDetailDO::getIsDeleted, 1)
                .update();
    }

    public List<RotationRuleDetailDO> queryByRuleId(Long ruleId) {
        return lambdaQuery()
                .eq(RotationRuleDetailDO::getRHId, ruleId)
                .eq(RotationRuleDetailDO::getIsDeleted, 0)
                .list();
    }


    public Boolean updateFieldsById(Long id, String batchAttr, String sortBy){
        return lambdaUpdate()
                .eq(RotationRuleDetailDO::getId, id)
                .set(RotationRuleDetailDO::getLotAttName, batchAttr)
                .set(RotationRuleDetailDO::getSortby, sortBy)
                .update();
    }
}
