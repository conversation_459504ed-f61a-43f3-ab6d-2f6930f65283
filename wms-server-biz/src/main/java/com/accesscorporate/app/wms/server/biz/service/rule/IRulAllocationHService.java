package com.accesscorporate.app.wms.server.biz.service.rule;

import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationHQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationHRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulAllocationHResponse;
import com.idanchuang.component.base.page.PageData;
import jakarta.validation.constraints.NotNull;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
public interface IRulAllocationHService {

    /**
     * 分页查询上架规则头列表
     */
    PageData<RulAllocationHResponse> queryPage(RulAllocationHQueryPageRequest request);

    /**
     * 查询上架规则头详情
     */
    RulAllocationHResponse queryById(Long id);

    /**
     * 新增上架规则头
     */
    Boolean save(RulAllocationHRequest request);


    /**
     * 删除上架规则头
     */
    Boolean deleteById(Long id);

    RulAllocationHResponse queryByAllocationId(Long allocationId);
}
