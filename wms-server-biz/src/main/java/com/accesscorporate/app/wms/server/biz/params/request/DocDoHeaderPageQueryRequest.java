package com.accesscorporate.app.wms.server.biz.params.request;

import com.idanchuang.component.base.page.PageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 发货单头表分页查询请求体
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Tag(name = "发货单头表分页查询请求体")
public class DocDoHeaderPageQueryRequest extends PageDTO {

    @Schema(description = "发货单号")
    private String doNo;

    @Schema(description = "订单状态")
    private String status;

    @Schema(description = "订单类型")
    private String doType;

    @Schema(description = "收货方")
    private String consigneeName;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "运单号")
    private String trackingNo;

    @Schema(description = "配送公司ID")
    private Long carrierId;

    @Schema(description = "波次ID")
    private Long waveId;

    @Schema(description = "原始ID")
    private String origId;

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "商家ID")
    private Long merchantId;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "企业客户ID")
    private Long businessCustomerId;

    @Schema(description = "原始SO编码")
    private String originalSoCode;

    @Schema(description = "订单子类型")
    private String orderSubType;

    @Schema(description = "订单来源系统")
    private String sourceSystem;

    @Schema(description = "渠道编码")
    private String channelCode;

    @Schema(description = "店铺编码")
    private String storeCode;

    @Schema(description = "创建时间开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "DO创建时间开始")
    private LocalDateTime doCreateTimeStart;

    @Schema(description = "DO创建时间结束")
    private LocalDateTime doCreateTimeEnd;

    @Schema(description = "发运时间开始")
    private LocalDateTime shipTimeStart;

    @Schema(description = "发运时间结束")
    private LocalDateTime shipTimeEnd;
}
