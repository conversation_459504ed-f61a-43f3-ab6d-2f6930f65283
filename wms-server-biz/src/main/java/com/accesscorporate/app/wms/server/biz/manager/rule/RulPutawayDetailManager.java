package com.accesscorporate.app.wms.server.biz.manager.rule;

import com.accesscorporate.app.wms.server.dal.entity.rule.RulPutawayDetailDO;
import com.accesscorporate.app.wms.server.dal.mapper.rule.RulPutawayDetailMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Component;

/**
 * @author: gao<PERSON>
 * @date: 2025-02-24 18:42
 * @desc:
 */
@Component
public class RulPutawayDetailManager extends ServiceImpl<RulPutawayDetailMapper, RulPutawayDetailDO> {

    public Page<RulPutawayDetailDO> selectPage(Long rulHeaderId,
                                               Long current,
                                               Long size) {
        return lambdaQuery()
                .eq(RulPutawayDetailDO::getRulHeaderId, rulHeaderId)
                .eq(RulPutawayDetailDO::getIsDeleted, 0)
                .page(new Page<>(current, size));
    }

    public RulPutawayDetailDO selectById(Long id) {
        return getById(id);
    }

    public Boolean createRulPutawayDetail(RulPutawayDetailDO rulPutawayDetailDO) {
        return save(rulPutawayDetailDO);
    }

    public Boolean updateRulPutawayDetail(RulPutawayDetailDO rulPutawayDetailDO) {
        return updateById(rulPutawayDetailDO);
    }


    public Boolean deleteById(Long id) {
        RulPutawayDetailDO rulPutawayDetailDO = new RulPutawayDetailDO()
                .setId(id)
                .setIsDeleted(1);
        return updateById(rulPutawayDetailDO);

    }

}
