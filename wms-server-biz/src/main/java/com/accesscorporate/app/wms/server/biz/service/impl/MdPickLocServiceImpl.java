package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.bo.PickLocCheckBO;
import com.accesscorporate.app.wms.server.biz.converter.PickLocConverter;
import com.accesscorporate.app.wms.server.biz.manager.PickLocManager;
import com.accesscorporate.app.wms.server.biz.params.request.pickloc.PickLocPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.request.pickloc.PickLocSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.pickloc.PickLocPageQueryResponse;
import com.accesscorporate.app.wms.server.biz.service.IMdSkuService;
import com.accesscorporate.app.wms.server.biz.service.LocationService;
import com.accesscorporate.app.wms.server.biz.service.MdPickLocService;
import com.accesscorporate.app.wms.server.biz.service.dto.PickLocImportDTO;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import com.accesscorporate.app.wms.server.dal.dto.PickLocQueryParam;
import com.accesscorporate.app.wms.server.dal.entity.Location;
import com.accesscorporate.app.wms.server.dal.entity.MdSku;
import com.accesscorporate.app.wms.server.dal.entity.pickloc.MdPickLocDO;
import com.accesscorporate.app.wms.server.dal.entity.pickloc.PickLocPageQueryResponseDO;
import com.accesscorporate.app.wms.server.dal.mapper.pickloc.MdPickLocMapper;
import com.accesscorporate.app.wms.server.dal.repository.MdPickLocRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.idanchuang.component.base.exception.core.ExFactory;
import com.idanchuang.component.base.exception.core.asserts.ExBusinessAssert;
import com.idanchuang.component.base.page.PageData;
import com.idanchuang.component.core.util.CopyUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-20 10:54
 * Description: 商品捡货位管理
 */
@Service
public class MdPickLocServiceImpl implements MdPickLocService {

    @Autowired
    private MdPickLocMapper mdPickLocMapper;

    @Autowired
    private PickLocManager pimLocManager;

    @Autowired
    private MdPickLocRepository mdPickLocRepository;

    @Autowired
    private IMdSkuService mdSkuService;

    @Autowired
    private LocationService locationService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    public PageData<PickLocPageQueryResponse> pageQuery(PickLocPageQueryRequest queryParam) {
        Page<PickLocPageQueryResponseDO> page = new Page<>(queryParam.getCurrent(), queryParam.getSize());
        Long currentWarehouseId = UserContextAssistant.getCurrentWarehouseId();
        if (Objects.isNull(currentWarehouseId)){
            currentWarehouseId=queryParam.getCurrentWarehouseId();
        }
        PickLocQueryParam pickLocQueryParam = CopyUtil.copy(queryParam, PickLocQueryParam.class);
        pickLocQueryParam.setWarehouseId(currentWarehouseId);
        IPage<PickLocPageQueryResponseDO> pageQueryResponse = mdPickLocMapper.pickLocPageQuery(page, pickLocQueryParam);
        List<PickLocPageQueryResponseDO> records = pageQueryResponse.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return PageData.empty();
        }
        List<PickLocPageQueryResponse> pickLocPageQueryResponses = PickLocConverter.converterToPageQueryResponse(records);
        return PageData.of(pickLocPageQueryResponses, queryParam.getCurrent(), queryParam.getSize(), pageQueryResponse.getTotal());
    }

    @Override
    public void saveOrUpdatePickLoc(PickLocSaveRequest request) {
        //参数检验
        String productCode = request.getProductCode();
        Long pickLocId = request.getPickLocId();
        String locCode = request.getLocCode();
        PickLocCheckBO pickLocCheckBO = pimLocManager.checkSkuAndLocation(productCode, locCode, pickLocId);
        Long currentWarehouseId = UserContextAssistant.getCurrentWarehouseId();
        String username = UserContextAssistant.getUsername();
        if (Objects.isNull(pickLocId)) {//新增
            MdPickLocDO mdPickLocDO = new MdPickLocDO();
            buildPickLocDO(request, pickLocCheckBO, mdPickLocDO);
            mdPickLocDO.setCreateBy(username);
            mdPickLocDO.setWarehouseId(currentWarehouseId);
            mdPickLocRepository.save(mdPickLocDO);
        } else {//编辑
            MdPickLocDO mdPickLocDO = mdPickLocRepository.getById(pickLocId);
            buildPickLocDO(request, pickLocCheckBO, mdPickLocDO);
            mdPickLocDO.setUpdateBy(username);
            mdPickLocDO.setWarehouseId(currentWarehouseId);
            mdPickLocRepository.updateById(mdPickLocDO);
        }
    }

    @Override
    public void deletePickLocById(Long pickLocId) {
        boolean remove = mdPickLocRepository.removeById(pickLocId);
        if (!remove) {
            throw ExFactory.throwBusiness("删除商品捡货位失败，主键ID:{}", pickLocId);
        }
    }

    @Override
    public void batchImportPickLoc(List<PickLocImportDTO> importList) {
        ExBusinessAssert.notEmpty(importList, "导入文件数据明细为空!请检查后重新上传!");
        // 前置校验检查
        pimLocManager.checkImportPickLoc(importList);
        Long currentWarehouseId = UserContextAssistant.getCurrentWarehouseId();
        String username = UserContextAssistant.getUsername();
        //获取所有货品编码
        List<String> productCodeList = importList.stream().map(PickLocImportDTO::getProductCode).distinct().
                collect(Collectors.toList());
        List<MdSku> mdSkus = mdSkuService.querySkuByGoodsCodes(productCodeList);
        Map<String, MdSku> mdSkuMap = mdSkus.stream().collect(Collectors.toMap(MdSku::getProductCode,
                Function.identity(), (k1, k2) -> k1));
        List<String> locCodeList = importList.stream().map(PickLocImportDTO::getLocCode).distinct().
                collect(Collectors.toList());
        List<Location> locations = locationService.queryLocationByCodes(locCodeList, currentWarehouseId);
        Map<String, Location> locationMap = locations.stream().collect(Collectors.toMap(Location::getLocCode,
                Function.identity(), (k1, k2) -> k1));
        //批量保存
        transactionTemplate.execute(status -> {
            List<List<PickLocImportDTO>> partition = Lists.partition(importList, 200);
            partition.forEach(importPartitions -> {
                List<MdPickLocDO> saveMdPickLocDOList = importPartitions.stream().map(importDTO -> {
                    MdPickLocDO mdPickLocDO = new MdPickLocDO();
                    String productCode = importDTO.getProductCode();
                    MdSku mdSku = mdSkuMap.get(productCode);
                    if (Objects.isNull(mdSku)) {
                        ExFactory.throwBusiness("通过货品编码未查询到货品信息，货品编码:{}", productCode);
                    }
                    mdPickLocDO.setSkuId(mdSku.getId());
                    String locCode = importDTO.getLocCode();
                    Location location = locationMap.get(locCode);
                    if (Objects.isNull(location)) {
                        ExFactory.throwBusiness("通过库位编码未查询到库位信息，库位编码:{}", locCode);
                    }
                    mdPickLocDO.setLocId(location.getId());
                    mdPickLocDO.setCreateBy(username);
                    mdPickLocDO.setWarehouseId(currentWarehouseId);
                    mdPickLocDO.setUplimit(BigDecimal.valueOf(importDTO.getUpLimit()));
                    mdPickLocDO.setLowerlimit(BigDecimal.valueOf(importDTO.getLowerLimit()));
                    mdPickLocDO.setMinimumRplQty(BigDecimal.valueOf(importDTO.getMinimumRplQty()));
                    mdPickLocDO.setUom(importDTO.getUom());
                    return mdPickLocDO;
                }).collect(Collectors.toList());
                mdPickLocRepository.batchInsertMdPickLoc(saveMdPickLocDOList);
            });
            return Boolean.TRUE;
        });
    }

    private void buildPickLocDO(PickLocSaveRequest request, PickLocCheckBO pickLocCheckBO, MdPickLocDO mdPickLocDO) {
        mdPickLocDO.setSkuId(pickLocCheckBO.getSkuId());
        mdPickLocDO.setLocId(pickLocCheckBO.getLocId());
        mdPickLocDO.setUplimit(BigDecimal.valueOf(request.getUpLimit()));
        mdPickLocDO.setLowerlimit(BigDecimal.valueOf(request.getLowerLimit()));
        mdPickLocDO.setMinimumRplQty(BigDecimal.valueOf(request.getMinimumRplQty()));
        mdPickLocDO.setUom(request.getUom());
    }






}
