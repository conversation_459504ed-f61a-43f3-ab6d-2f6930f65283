package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.manager.CfgCodeDetailCacheService;
import com.accesscorporate.app.wms.server.biz.params.request.CfgCodeDetailPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.CfgCodeDetailSaveOrUpdateRequest;
import com.accesscorporate.app.wms.server.biz.params.response.CfgCodeDetailResponse;
import com.accesscorporate.app.wms.server.biz.service.CfgCodeDetailService;
import com.accesscorporate.app.wms.server.biz.converter.CfgCodeDetailMapStruct;
import com.accesscorporate.app.wms.server.dal.entity.CfgCodeDetailDO;
import com.accesscorporate.app.wms.server.dal.mapper.CfgCodeDetailMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.idanchuang.component.base.exception.core.ExFactory;
import com.idanchuang.component.base.page.PageData;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class CfgCodeDetailServiceImpl implements CfgCodeDetailService {

    @Resource
    private CfgCodeDetailMapper cfgCodeDetailMapper;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private CfgCodeDetailCacheService cfgCodeDetailCacheService;

    @Override
    public PageData<CfgCodeDetailResponse> pageList(CfgCodeDetailPageRequest request) {
        PageDTO<CfgCodeDetailDO> page = new PageDTO<>(request.getCurrent(), request.getSize());
        LambdaQueryWrapper<CfgCodeDetailDO> queryWrapper =
                new LambdaQueryWrapper<CfgCodeDetailDO>().eq(CfgCodeDetailDO::getMasterCode, request.getMasterCode());
        IPage<CfgCodeDetailDO> cfgCodeDetailDOIPage = cfgCodeDetailMapper.selectPage(page, queryWrapper);
        List<CfgCodeDetailDO> records = cfgCodeDetailDOIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return PageData.empty();
        }
        List<CfgCodeDetailResponse> responses =
                records.stream().map(CfgCodeDetailMapStruct.INSTANCE::convertToResponse).collect(Collectors.toList());
        return PageData.of(responses, cfgCodeDetailDOIPage.getCurrent(), cfgCodeDetailDOIPage.getSize(), cfgCodeDetailDOIPage.getTotal());
    }

    @Override
    public void remove(String uuid) {
        LambdaQueryWrapper<CfgCodeDetailDO> queryWrapper =
                new LambdaQueryWrapper<CfgCodeDetailDO>().eq(CfgCodeDetailDO::getUuid, uuid);
        CfgCodeDetailDO cfgCodeDetailDO = cfgCodeDetailMapper.selectOne(queryWrapper);
        if (ObjectUtils.isEmpty(cfgCodeDetailDO)) {
            return;
        }
        transactionTemplate.execute((status) -> {
            cfgCodeDetailMapper.delete(queryWrapper);
            cfgCodeDetailCacheService.clearCache(cfgCodeDetailDO.getMasterCode());
            return true;
        });
    }

    @Override
    public void save(CfgCodeDetailSaveOrUpdateRequest request) {
        String uuid = request.getMasterCode() + "_" + request.getSeqNum();
        if (cfgCodeDetailMapper.exists(new LambdaQueryWrapper<CfgCodeDetailDO>().eq(CfgCodeDetailDO::getUuid, uuid))) {
            throw ExFactory.throwBusiness("序号重复");
        }

        CfgCodeDetailDO cfgCodeDetailDO = CfgCodeDetailMapStruct.INSTANCE.convertToDO(request);
        cfgCodeDetailDO.setUuid(uuid);
        transactionTemplate.execute((status) -> {
            cfgCodeDetailMapper.insert(cfgCodeDetailDO);
            cfgCodeDetailCacheService.clearCache(cfgCodeDetailDO.getMasterCode());
            return true;
        });
    }

    @Override
    public void update(CfgCodeDetailSaveOrUpdateRequest request) {
        if (request.getUuid() == null) {
            throw ExFactory.throwBusiness("编辑时uuid必填");
        }

        LambdaQueryWrapper<CfgCodeDetailDO> queryWrapper =
                new LambdaQueryWrapper<CfgCodeDetailDO>().eq(CfgCodeDetailDO::getUuid, request.getUuid());
        CfgCodeDetailDO cfgCodeDetailDO = CfgCodeDetailMapStruct.INSTANCE.convertToDO(request);
        cfgCodeDetailDO.setUuid(request.getMasterCode() + "_" + request.getSeqNum());
        transactionTemplate.execute((status) -> {
            cfgCodeDetailMapper.update(cfgCodeDetailDO, queryWrapper);
            cfgCodeDetailCacheService.clearCache(cfgCodeDetailDO.getMasterCode());
            return true;
        });
    }
}
