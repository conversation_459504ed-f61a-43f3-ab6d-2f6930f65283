package com.accesscorporate.app.wms.server.biz.service.rule;

import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayHQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayHRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulPutawayHResponse;
import com.idanchuang.component.base.page.PageData;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
public interface IRulPutawayHService {

    /**
     * 分页查询上架规则列表
     * @param request
     * @return
     */
    PageData<RulPutawayHResponse> queryPage(RulPutawayHQueryPageRequest request);

    /**
     * 查询上架规则详情
     */
    RulPutawayHResponse queryById(Long id);

    /**
     * 新增上架规则
     */
    Boolean save(RulPutawayHRequest request);


}
