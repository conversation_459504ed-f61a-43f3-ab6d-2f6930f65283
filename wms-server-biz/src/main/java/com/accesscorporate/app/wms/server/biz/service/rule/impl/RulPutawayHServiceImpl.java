package com.accesscorporate.app.wms.server.biz.service.rule.impl;

import com.accesscorporate.app.wms.server.biz.converter.RulPutawayConverter;
import com.accesscorporate.app.wms.server.biz.manager.rule.RulPutawayHManager;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayHQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayHRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulPutawayHResponse;
import com.accesscorporate.app.wms.server.biz.service.rule.IRulPutawayHService;
import com.accesscorporate.app.wms.server.dal.entity.rule.RulPutawayHDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.page.PageData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RulPutawayHServiceImpl implements IRulPutawayHService {

    private final RulPutawayHManager rulPutawayHManager;

    @Override
    public PageData<RulPutawayHResponse> queryPage(RulPutawayHQueryPageRequest request) {

        Page<RulPutawayHDO> page = rulPutawayHManager.selectPage(request.getPutawayCode(),
                request.getPutawayName(),
                request.getCurrent(),
                request.getSize());
        List<RulPutawayHResponse> responses = page.getRecords().stream().map(RulPutawayConverter::toRulPutawayHResponse).toList();

        return PageData.of(
                responses,
                request.getCurrent(),
                request.getSize(),
                page.getTotal());
    }

    @Override
    public RulPutawayHResponse queryById(Long id) {
        RulPutawayHDO rulPutawayHDO = rulPutawayHManager.selectById(id);
        if (rulPutawayHDO == null) {
            return null;
        }
        return RulPutawayConverter.toRulPutawayHResponse(rulPutawayHDO);
    }

    @Override
    public Boolean save(RulPutawayHRequest request) {
        RulPutawayHDO rulPutawayHDO = RulPutawayConverter.toRulPutawayHDO(request);
        if (rulPutawayHDO.getId() != null) {
            return rulPutawayHManager.updateRulPutawayH(rulPutawayHDO);
        }
        return rulPutawayHManager.createRulPutawayH(rulPutawayHDO);
    }


}
