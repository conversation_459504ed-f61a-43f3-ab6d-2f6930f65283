package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.params.request.SkuCombiAddRequest;
import com.accesscorporate.app.wms.server.biz.params.request.SkuCombiListRequest;
import com.accesscorporate.app.wms.server.biz.params.response.SkuCombiListResponse;
import com.accesscorporate.app.wms.server.biz.params.response.SkuCombiViewResponse;
import com.accesscorporate.app.wms.server.biz.service.SkuCombiService;
import com.accesscorporate.app.wms.server.biz.converter.SkuCombiConvert;
import com.accesscorporate.app.wms.server.biz.service.dto.SkuCombiDetailDTO;
import com.accesscorporate.app.wms.server.biz.service.dto.SkuCombiHeaderDTO;
import com.accesscorporate.app.wms.server.biz.service.dto.SkuCombiImportDTO;
import com.accesscorporate.app.wms.server.common.utils.EasyExcelUtils;
import com.accesscorporate.app.wms.server.common.utils.MD5Util;
import com.accesscorporate.app.wms.server.dal.dto.SkuCombiListQuery;
import com.accesscorporate.app.wms.server.dal.entity.MdSkuCombiDetailDO;
import com.accesscorporate.app.wms.server.dal.entity.MdSkuCombiHeaderDO;
import com.accesscorporate.app.wms.server.dal.mapper.MdSkuCombiDetailMapper;
import com.accesscorporate.app.wms.server.dal.mapper.MdSkuCombiHeaderMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.exception.core.ExFactory;
import com.idanchuang.component.base.exception.core.asserts.ExBusinessAssert;
import com.idanchuang.component.base.page.PageData;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liudongliang
 * @project : wms-server
 * @company : 浙江单创品牌管理有限公司
 * @desc :
 * Copyright @ 2024 danchuang Technology Co.Ltd. – Confidential and Proprietary
 */
@Slf4j
@Service
public class SkuCombiServiceImpl implements SkuCombiService {

    @Resource
    private MdSkuCombiHeaderMapper mdSkuCombiHeaderMapper;

    @Resource
    private MdSkuCombiDetailMapper mdSkuCombiDetailMapper;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    public PageData<SkuCombiListResponse> list(SkuCombiListRequest request) {
        SkuCombiListQuery query = SkuCombiConvert.convertToQuery(request);
        // TODO: 2024/11/6 仓库Id处理
        query.setWarehouseId(null);
        Page<MdSkuCombiHeaderDO> page = new Page<>(request.getCurrent(), request.getSize());
        IPage<MdSkuCombiHeaderDO> result = mdSkuCombiHeaderMapper.list(page, query);
        List<SkuCombiListResponse> response = SkuCombiConvert.convertToResponseList(result.getRecords());
        return PageData.of(response, result.getCurrent(), result.getSize(), result.getTotal());
    }

    @Override
    public Boolean add(SkuCombiAddRequest request) {
        // 前置检查
        SkuCombiHeaderDTO header = this.checkAndConvertBeforeAdd(request);
        transactionTemplate.execute((status) -> {
            // 主单新增
            mdSkuCombiHeaderMapper.insert(SkuCombiConvert.convertToHeaderDO(header));
            // 明细新增
            mdSkuCombiDetailMapper.batchInsert(SkuCombiConvert.convertToDetails(header.getDetails()));
            return Boolean.TRUE;
        });
        return Boolean.TRUE;
    }

    private SkuCombiHeaderDTO checkAndConvertBeforeAdd(SkuCombiAddRequest request) {
        // TODO: 2024/11/6 仓库Id处理
        Long warehouseId = null;
        // 组合条码格式校验 : 必须为纯数字 & 长度最大不超过30
        ExBusinessAssert.isTrue(request.isCombiBarcodeDigit(), "组合条码必须为纯数字!");
        // 组合条码长度校验
        ExBusinessAssert.isTrue(request.isCombiBarcodeLengthMatchRule(), "组合条码长度必须为1-30位!");
        // 组合条码唯一性
        List<MdSkuCombiHeaderDO> existList = mdSkuCombiHeaderMapper.querySkuCombiByCombiBarcode(request.getCombiBarcode(), warehouseId);
        ExBusinessAssert.isTrue(existList.isEmpty(), String.format("组合条码[%s]当前仓库已存在!", request.getCombiBarcode()));
        // 组合条码与商品条码重复性检查
        // TODO: 2024/11/6 对接商品条码查询能力
//        ExBusinessAssert.notEmpty(existList, String.format("组合条码[%s]与SKU条码重复!", request.getCombiBarcode()));
        // TODO: 2024/11/6 库位合法性检查
//        ExBusinessAssert.notEmpty(existList, String.format("库位编码[%s]不存在!请确认库存编码为有效库位!", request.getLocCode()));
        // 商品基础信息集合
        List<String> existsList = new ArrayList<>();
        // 明细数据检查
        request.getItems().forEach(item -> {
            // TODO: 2024/11/6 商品条码合法性检查
//            ExBusinessAssert.notEmpty(existList, String.format("明细数据中商品条码[%s]不存在!请确认商品条码为有效商品!", item.getBarcode()));
            // 明细行 条码 等级 数量 不能重复
            String key = item.getUniqueKey();
            if (existsList.contains(key)) {
                ExFactory.throwBusiness("明细数据重复!条码+等级+数量 需保持唯一!");
            } else {
                existsList.add(key);
            }
        });
        // 签名构建
        String signStr = existsList.stream().sorted().collect(Collectors.joining(", ", "[", "]"));
        String md5 = MD5Util.getMD5Hash(signStr);
        // 签名唯一性检查
        List<MdSkuCombiHeaderDO> signList = mdSkuCombiHeaderMapper.querySkuCombiBySign(md5, warehouseId);
        ExBusinessAssert.isTrue(signList.isEmpty(), "组合规则重复!");
        // 构建新增对象
        SkuCombiHeaderDTO header = new SkuCombiHeaderDTO();
        header.setWarehouseId(warehouseId);
        header.setCombiBarcode(request.getCombiBarcode());
        header.setSign(md5);
        // 商品数
        header.setSkuCount(request.getItems().stream().collect(Collectors.groupingBy(SkuCombiAddRequest.SkuItem::getBarcode)).size());
        header.setLocCode(request.getLocCode());
        // 商品总数量
        header.setSkuTotal(request.getItems().stream().map(SkuCombiAddRequest.SkuItem::getQuantity).reduce(Integer::sum).orElse(0));
        header.setDetails(this.buildGoodsItems(request, warehouseId));
        return header;
    }

    private List<SkuCombiDetailDTO> buildGoodsItems(SkuCombiAddRequest request,
                                                    Long warehouseId) {
        // TODO: 2024/11/11 商品信息带入
        List<SkuCombiDetailDTO> result = new ArrayList<>(request.getItems().size());
        for (SkuCombiAddRequest.SkuItem item : request.getItems()) {
            SkuCombiDetailDTO skuCombiDetailDTO = new SkuCombiDetailDTO();
            skuCombiDetailDTO.setCombiBarcode(request.getCombiBarcode());
            skuCombiDetailDTO.setBarcode(item.getBarcode());
//            skuCombiDetailDTO.setGoodsName(detail.getGoodsName());
            skuCombiDetailDTO.setGoodsName("MOCK");
            skuCombiDetailDTO.setGoodsGrade(String.valueOf(item.getGoodsGrade()));
            skuCombiDetailDTO.setTotal(item.getQuantity());
            skuCombiDetailDTO.setWarehouseId(warehouseId);
//            skuCombiDetailDTO.setSkuId(detail.getSkuId());
            skuCombiDetailDTO.setSkuId(10010L);
            result.add(skuCombiDetailDTO);
        }
        return result;
    }

    @Override
    public SkuCombiViewResponse view(Long id) {
        ExBusinessAssert.notNull(id, "组合绑码查看,入参ID不能为空!");
        // 查询主单
        MdSkuCombiHeaderDO header = mdSkuCombiHeaderMapper.selectByPrimaryKey(id);
        ExBusinessAssert.notNull(header, String.format("组合绑码查看,根据ID=[%d]查询数据不存在!", id));
        // 查询明细
        List<MdSkuCombiDetailDO> items = mdSkuCombiDetailMapper.selectByCombiBarcode(header.getCombiBarcode());
        ExBusinessAssert.notEmpty(items, String.format("组合绑码查看,根据组合条码=[%s]查询明细数据不存在!", header.getCombiBarcode()));
        // 构建结果
        SkuCombiViewResponse response = new SkuCombiViewResponse();
        response.setId(id);
        response.setCombiBarcode(header.getCombiBarcode());
        response.setLocCode(header.getLocCode());
        response.setItems(SkuCombiConvert.convertToViewItems(items));
        return response;
    }

    @Override
    public Boolean delete(Long id) {
        ExBusinessAssert.notNull(id, "组合绑码删除,入参ID不能为空!");
        // 查询主单
        MdSkuCombiHeaderDO header = mdSkuCombiHeaderMapper.selectByPrimaryKey(id);
        ExBusinessAssert.notNull(header, String.format("组合绑码删除,根据ID=[%d]查询数据不存在!", id));
        // 查询明细
        List<MdSkuCombiDetailDO> items = mdSkuCombiDetailMapper.selectByCombiBarcode(header.getCombiBarcode());
        ExBusinessAssert.isTrue(!items.isEmpty(), String.format("组合绑码删除,根据组合条码=[%s]查询明细数据不存在!", header.getCombiBarcode()));
        transactionTemplate.execute((status) -> {
            int headerCount = mdSkuCombiHeaderMapper.deleteByPrimaryKey(id);
            ExBusinessAssert.isTrue(headerCount == 1, "组合条码删除失败!");
            int itemCount = mdSkuCombiDetailMapper.deleteByCombiBarcode(header.getCombiBarcode());
            ExBusinessAssert.isTrue(itemCount == items.size(), "组合条码明细删除失败!");
            return Boolean.TRUE;
        });
        return Boolean.TRUE;
    }

    @Override
    public void importExcel(MultipartFile file) {
        // 导入文件解析
        List<SkuCombiImportDTO> importList = EasyExcelUtils.reader(file, SkuCombiImportDTO.class);
        ExBusinessAssert.notEmpty(importList, "导入文件数据明细为空!请检查后重新上传!");
        ExBusinessAssert.isTrue(importList.size() <= 500, "导入文件数据明细数量不能超过500条!");
        // 前置非空检查
        for (int i = 0; i < importList.size(); i++) {
            SkuCombiImportDTO importItem = importList.get(i);
            importItem.checkDataIsEmpty(i);
        }
        // 分组检查 : 按照组合条码分组
        Map<String, List<SkuCombiImportDTO>> groupMap = importList.stream().collect(Collectors.groupingBy(SkuCombiImportDTO::getCombiBarcode));
        // 检查组合条码和库位唯一性
        groupMap.values().forEach(record -> {
            Set<String> locCodeSet = record.stream().map(SkuCombiImportDTO::getLocCode).collect(Collectors.toSet());
            ExBusinessAssert.isTrue(locCodeSet.size() == 1,
                    String.format("导入文件数据明细中,组合条码为[%s]的组合条码和库位存在重复,请检查后重新上传!", record.get(0).getCombiBarcode()));
        });
        List<SkuCombiHeaderDTO> saveList = new ArrayList<>();
        // 执行新增前置校验并转化待新增对象
        List<SkuCombiAddRequest> requestList = buildAddRequest(groupMap);
        requestList.forEach(request -> {
            SkuCombiHeaderDTO header = this.checkAndConvertBeforeAdd(request);
            saveList.add(header);
        });
        // 批量保存数据
        transactionTemplate.execute((status) -> {
            saveList.forEach(record -> {
                // 主单新增
                mdSkuCombiHeaderMapper.insert(SkuCombiConvert.convertToHeaderDO(record));
                // 明细新增
                mdSkuCombiDetailMapper.batchInsert(SkuCombiConvert.convertToDetails(record.getDetails()));
            });
            return Boolean.TRUE;
        });
    }

    private List<SkuCombiAddRequest> buildAddRequest(Map<String, List<SkuCombiImportDTO>> groupMap) {
        List<SkuCombiAddRequest> result = new ArrayList<>();
        groupMap.forEach((combiBarcode, items) -> {
            SkuCombiAddRequest request = new SkuCombiAddRequest();
            request.setCombiBarcode(items.get(0).getCombiBarcode());
            request.setLocCode(items.get(0).getLocCode());
            List<SkuCombiAddRequest.SkuItem> skuItemList = new ArrayList<>();
            items.forEach(record -> {
                SkuCombiAddRequest.SkuItem item = new SkuCombiAddRequest.SkuItem();
                item.setBarcode(record.getBarcode());
                // TODO: 2024/12/2 翻译类型
//                item.setGoodsGrade(Integer.parseInt(record.getGoodsGrade()));
                item.setGoodsGrade(1);
                item.setQuantity(record.getTotal());
                skuItemList.add(item);
            });
            request.setItems(skuItemList);
            result.add(request);
        });
        return result;
    }

}
