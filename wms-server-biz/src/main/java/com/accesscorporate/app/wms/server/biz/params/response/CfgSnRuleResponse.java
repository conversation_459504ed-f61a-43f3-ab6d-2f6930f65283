package com.accesscorporate.app.wms.server.biz.params.response;

import com.accesscorporate.app.wms.server.common.enums.SLoopEnum;
import lombok.Data;

/**
 * @TableName cfg_sn_rule
 */
@Data
public class CfgSnRuleResponse {
    /**
     * 规则代码
     */
    private String ruleCode;

    /**
     *
     */
    private String ruleName;

    /**
     *
     */
    private String postfix;

    /**
     * 例子：WAVE{year}{month}{day}
     */
    private String prefix;

    /**
     *
     */
    private Integer sLength;

    /**
     *
     */
    private Integer sLoop;

    /**
     *
     */
    private String sqlText;

    /**
     *
     */
    private Integer isGlobal;

    /**
     *
     */
    private Integer isDeleted;

    /**
     * 乐观锁控制
     */
    private Long version;

    /**
     * 循环方式 0 日 1 月 2 年 3 不循环
     */
    private String sLoopDesc;

    public String getsLoopDesc() {
        return SLoopEnum.getValue(sLoop);
    }
}