package com.accesscorporate.app.wms.server.biz.params.response.rule;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @author: gao<PERSON>
 * @date: 2025-02-20 15:01
 * @desc:
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class RulPutawayHResponse {

    private Long id;

    /**
     *  规则code
     */
    private String putawayCode;

    /**
     * 规则描述
     */
    private String putawayName;

    /**
     * 是否激活0 未激活 1 激活
     */
    private String activeFlag;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
