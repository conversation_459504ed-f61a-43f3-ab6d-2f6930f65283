package com.accesscorporate.app.wms.server.biz.service.selector;

import com.accesscorporate.app.wms.server.biz.params.response.CommonSelectorResponse;
import com.accesscorporate.app.wms.server.common.enums.PickLocUomEnum;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-24 15:40
 * Description: 补货单位下拉框
 */
@Component
public class PickLocUomSelectorStrategy extends AbstractCommonSelectorStrategy{
    @Override
    protected SelectorTypeEnum getStrategyType() {
        return SelectorTypeEnum.PICK_LOC_UOM_SELECTOR;
    }

    @Override
    protected List<CommonSelectorResponse> queryStrategy() {
        return Arrays.stream(PickLocUomEnum.values()).map(uom->{
            String name = uom.name();
            return CommonSelectorResponse.builder()
                    .key(name)
                    .value(name)
                    .build();
        }).toList();
    }


}
