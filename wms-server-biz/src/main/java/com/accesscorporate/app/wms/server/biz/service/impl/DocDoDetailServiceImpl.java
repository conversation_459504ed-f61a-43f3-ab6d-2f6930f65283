package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.converter.DocDoDetailMapStruct;
import com.accesscorporate.app.wms.server.biz.manager.impl.DocDoDetailManager;
import com.accesscorporate.app.wms.server.biz.params.request.DocDoDetailPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.request.DocDoDetailSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocDoDetailResponse;
import com.accesscorporate.app.wms.server.biz.service.DocDoDetailService;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import com.accesscorporate.app.wms.server.dal.entity.DocDoDetail;
import com.accesscorporate.app.wms.server.dal.mapper.DocDoDetailMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 发货单明细表Service实现类
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
public class DocDoDetailServiceImpl extends ServiceImpl<DocDoDetailMapper, DocDoDetail> implements DocDoDetailService {

    @Autowired
    private DocDoDetailManager docDoDetailManager;

    @Override
    public List<DocDoDetailResponse> listAll() {
        LambdaQueryWrapper<DocDoDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        wrapper.eq(DocDoDetail::getIsDeleted, Boolean.FALSE);
        wrapper.orderByDesc(DocDoDetail::getCreateTime);
        return docDoDetailManager.list(wrapper).stream()
                .map(DocDoDetailMapStruct.INSTANCE::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public DocDoDetailResponse get(Long id) {
        DocDoDetail docDoDetail = super.getById(id);
        return DocDoDetailMapStruct.INSTANCE.convertToResponse(docDoDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(DocDoDetailSaveRequest request) {
        DocDoDetail docDoDetail = DocDoDetailMapStruct.INSTANCE.convertToEntity(request);
        docDoDetail.setWarehouseId(UserContextAssistant.getCurrentWarehouseId());
        return super.save(docDoDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        return super.removeById(id);
    }

    @Override
    public Page<DocDoDetailResponse> page(DocDoDetailPageQueryRequest request) {
        Page<DocDoDetail> page = new Page<>(request.getCurrent(), request.getSize());
        LambdaQueryWrapper<DocDoDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        wrapper.eq(DocDoDetail::getIsDeleted, Boolean.FALSE);

        // 订单头ID
        if (request.getDoHeaderId() != null) {
            wrapper.eq(DocDoDetail::getDoHeaderId, request.getDoHeaderId());
        }
        // 明细状态
        if (StringUtils.hasText(request.getLinestatus())) {
            wrapper.eq(DocDoDetail::getLinestatus, request.getLinestatus());
        }
        // 商品ID
        if (request.getSkuId() != null) {
            wrapper.eq(DocDoDetail::getSkuId, request.getSkuId());
        }
        // 包装ID
        if (request.getPackageId() != null) {
            wrapper.eq(DocDoDetail::getPackageId, request.getPackageId());
        }
        // 包装明细ID
        if (request.getPackDetailId() != null) {
            wrapper.eq(DocDoDetail::getPackDetailId, request.getPackDetailId());
        }
        // 单位
        if (StringUtils.hasText(request.getUom())) {
            wrapper.eq(DocDoDetail::getUom, request.getUom());
        }
        // 供应商ID
        if (StringUtils.hasText(request.getLotatt04())) {
            wrapper.eq(DocDoDetail::getLotatt04, request.getLotatt04());
        }
        // 批号
        if (StringUtils.hasText(request.getLotatt05())) {
            wrapper.like(DocDoDetail::getLotatt05, request.getLotatt05());
        }
        // 货主ID
        if (StringUtils.hasText(request.getLotatt06())) {
            wrapper.eq(DocDoDetail::getLotatt06, request.getLotatt06());
        }
        // 制造商
        if (StringUtils.hasText(request.getLotatt08())) {
            wrapper.like(DocDoDetail::getLotatt08, request.getLotatt08());
        }
        // po号
        if (StringUtils.hasText(request.getLotatt10())) {
            wrapper.like(DocDoDetail::getLotatt10, request.getLotatt10());
        }
        // 容器号
        if (StringUtils.hasText(request.getLotatt13())) {
            wrapper.like(DocDoDetail::getLotatt13, request.getLotatt13());
        }
        // 分配规则ID
        if (request.getAllocationRule() != null) {
            wrapper.eq(DocDoDetail::getAllocationRule, request.getAllocationRule());
        }
        // 拣货区
        if (StringUtils.hasText(request.getPickzone())) {
            wrapper.eq(DocDoDetail::getPickzone, request.getPickzone());
        }
        // 原始头ID
        if (StringUtils.hasText(request.getOrigHeaderId())) {
            wrapper.like(DocDoDetail::getOrigHeaderId, request.getOrigHeaderId());
        }
        // 原始明细ID
        if (StringUtils.hasText(request.getOrigDetailId())) {
            wrapper.like(DocDoDetail::getOrigDetailId, request.getOrigDetailId());
        }
        // 是否坏品
        if (request.getIsDamaged() != null) {
            wrapper.eq(DocDoDetail::getIsDamaged, request.getIsDamaged());
        }
        // 是否贵重品
        if (request.getIsValueables() != null) {
            wrapper.eq(DocDoDetail::getIsValueables, request.getIsValueables());
        }
        // 是否为酒类随附单
        if (request.getWineFlag() != null) {
            wrapper.eq(DocDoDetail::getWineFlag, request.getWineFlag());
        }
        // 批次编码
        if (StringUtils.hasText(request.getLotNo())) {
            wrapper.like(DocDoDetail::getLotNo, request.getLotNo());
        }
        // 是否有处方药
        if (request.getHaveCfy() != null) {
            wrapper.eq(DocDoDetail::getHaveCfy, request.getHaveCfy());
        }
        // 销售等级
        if (request.getSalesGrade() != null) {
            wrapper.eq(DocDoDetail::getSalesGrade, request.getSalesGrade());
        }
        // 货品等级
        if (request.getGoodsGrade() != null) {
            wrapper.eq(DocDoDetail::getGoodsGrade, request.getGoodsGrade());
        }

        // 期望发货数量范围
        if (request.getExpectedQtyMin() != null) {
            wrapper.ge(DocDoDetail::getExpectedQty, request.getExpectedQtyMin());
        }
        if (request.getExpectedQtyMax() != null) {
            wrapper.le(DocDoDetail::getExpectedQty, request.getExpectedQtyMax());
        }
        // 分配数量范围
        if (request.getAllocatedQtyMin() != null) {
            wrapper.ge(DocDoDetail::getAllocatedQty, request.getAllocatedQtyMin());
        }
        if (request.getAllocatedQtyMax() != null) {
            wrapper.le(DocDoDetail::getAllocatedQty, request.getAllocatedQtyMax());
        }
        // 拣货数量范围
        if (request.getPickedQtyMin() != null) {
            wrapper.ge(DocDoDetail::getPickedQty, request.getPickedQtyMin());
        }
        if (request.getPickedQtyMax() != null) {
            wrapper.le(DocDoDetail::getPickedQty, request.getPickedQtyMax());
        }
        // 发货数量范围
        if (request.getShippedQtyMin() != null) {
            wrapper.ge(DocDoDetail::getShippedQty, request.getShippedQtyMin());
        }
        if (request.getShippedQtyMax() != null) {
            wrapper.le(DocDoDetail::getShippedQty, request.getShippedQtyMax());
        }

        // 创建时间范围
        if (request.getCreateTimeStart() != null) {
            wrapper.ge(DocDoDetail::getCreateTime, request.getCreateTimeStart());
        }
        if (request.getCreateTimeEnd() != null) {
            wrapper.le(DocDoDetail::getCreateTime, request.getCreateTimeEnd());
        }
        // 最小失效日期范围
        if (request.getMinExpStart() != null) {
            wrapper.ge(DocDoDetail::getMinExp, request.getMinExpStart());
        }
        if (request.getMinExpEnd() != null) {
            wrapper.le(DocDoDetail::getMinExp, request.getMinExpEnd());
        }
        // 最大失效日期范围
        if (request.getMaxExpStart() != null) {
            wrapper.ge(DocDoDetail::getMaxExp, request.getMaxExpStart());
        }
        if (request.getMaxExpEnd() != null) {
            wrapper.le(DocDoDetail::getMaxExp, request.getMaxExpEnd());
        }

        wrapper.orderByDesc(DocDoDetail::getCreateTime);

        Page<DocDoDetail> docDoDetailPage = super.page(page, wrapper);
        List<DocDoDetailResponse> responses = docDoDetailPage.getRecords().stream()
                .map(DocDoDetailMapStruct.INSTANCE::convertToResponse)
                .collect(Collectors.toList());
        return new Page<DocDoDetailResponse>(docDoDetailPage.getCurrent(), docDoDetailPage.getSize(), docDoDetailPage.getTotal())
                .setRecords(responses);
    }

    @Override
    public List<DocDoDetail> queryByDoHeaderId(Long doHeaderId) {
        return lambdaQuery()
                .eq(DocDoDetail::getDoHeaderId, doHeaderId)
                .eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoDetail::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoDetail::getCreateTime)
                .list();
    }

    @Override
    public List<DocDoDetail> queryBySkuId(Long skuId) {
        return lambdaQuery()
                .eq(DocDoDetail::getSkuId, skuId)
                .eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoDetail::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoDetail::getCreateTime)
                .list();
    }

    @Override
    public List<DocDoDetail> queryByLinestatus(String linestatus) {
        return lambdaQuery()
                .eq(DocDoDetail::getLinestatus, linestatus)
                .eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoDetail::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoDetail::getCreateTime)
                .list();
    }

    @Override
    public List<DocDoDetail> queryByPackageId(Long packageId) {
        return lambdaQuery()
                .eq(DocDoDetail::getPackageId, packageId)
                .eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoDetail::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoDetail::getCreateTime)
                .list();
    }

    @Override
    public List<DocDoDetail> queryByOrigHeaderId(String origHeaderId) {
        return lambdaQuery()
                .eq(DocDoDetail::getOrigHeaderId, origHeaderId)
                .eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoDetail::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoDetail::getCreateTime)
                .list();
    }

    @Override
    public DocDoDetail queryByOrigDetailId(String origDetailId) {
        return lambdaQuery()
                .eq(DocDoDetail::getOrigDetailId, origDetailId)
                .eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoDetail::getIsDeleted, Boolean.FALSE)
                .one();
    }

    @Override
    public List<DocDoDetail> queryByLotNo(String lotNo) {
        return lambdaQuery()
                .eq(DocDoDetail::getLotNo, lotNo)
                .eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoDetail::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoDetail::getCreateTime)
                .list();
    }

    @Override
    public List<DocDoDetail> queryBySupplier(String supplierId) {
        return lambdaQuery()
                .eq(DocDoDetail::getLotatt04, supplierId)
                .eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoDetail::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoDetail::getCreateTime)
                .list();
    }

    @Override
    public List<DocDoDetail> queryByCargoOwner(String cargoOwnerId) {
        return lambdaQuery()
                .eq(DocDoDetail::getLotatt06, cargoOwnerId)
                .eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoDetail::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoDetail::getCreateTime)
                .list();
    }

    @Override
    public List<DocDoDetail> queryByPickzone(String pickzone) {
        return lambdaQuery()
                .eq(DocDoDetail::getPickzone, pickzone)
                .eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoDetail::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoDetail::getCreateTime)
                .list();
    }

    @Override
    public List<DocDoDetail> queryByDamaged(Integer isDamaged) {
        return lambdaQuery()
                .eq(DocDoDetail::getIsDamaged, isDamaged)
                .eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoDetail::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoDetail::getCreateTime)
                .list();
    }

    @Override
    public List<DocDoDetail> queryByValueables(Integer isValueables) {
        return lambdaQuery()
                .eq(DocDoDetail::getIsValueables, isValueables)
                .eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoDetail::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoDetail::getCreateTime)
                .list();
    }

    @Override
    public List<DocDoDetail> queryByHaveCfy(Integer haveCfy) {
        return lambdaQuery()
                .eq(DocDoDetail::getHaveCfy, haveCfy)
                .eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoDetail::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoDetail::getCreateTime)
                .list();
    }

    @Override
    public List<DocDoDetail> queryBySalesGrade(Integer salesGrade) {
        return lambdaQuery()
                .eq(DocDoDetail::getSalesGrade, salesGrade)
                .eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoDetail::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoDetail::getCreateTime)
                .list();
    }

    @Override
    public List<DocDoDetail> queryByGoodsGrade(Integer goodsGrade) {
        return lambdaQuery()
                .eq(DocDoDetail::getGoodsGrade, goodsGrade)
                .eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoDetail::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoDetail::getCreateTime)
                .list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(List<DocDoDetailSaveRequest> saveRequests) {
        List<DocDoDetail> docDoDetails = saveRequests.stream()
                .map(request -> {
                    DocDoDetail docDoDetail = DocDoDetailMapStruct.INSTANCE.convertToEntity(request);
                    docDoDetail.setWarehouseId(UserContextAssistant.getCurrentWarehouseId());
                    return docDoDetail;
                })
                .collect(Collectors.toList());
        return super.saveBatch(docDoDetails);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByDoHeaderId(Long doHeaderId) {
        LambdaQueryWrapper<DocDoDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocDoDetail::getDoHeaderId, doHeaderId);
        wrapper.eq(DocDoDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        return super.remove(wrapper);
    }
}
