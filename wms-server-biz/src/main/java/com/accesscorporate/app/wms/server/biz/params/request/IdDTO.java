package com.accesscorporate.app.wms.server.biz.params.request;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> liu<PERSON>liang
 * @project : wms-server
 * @company : 浙江单创品牌管理有限公司
 * @desc :
 * Copyright @ 2024 danchuang Technology Co.Ltd. – Confidential and Proprietary
 */
@Data
public class IdDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 5514513224118095434L;

    @NotNull(message = "ID不能为空!")
    private Long id;
}
