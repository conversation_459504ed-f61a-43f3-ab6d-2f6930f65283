package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.service.SExpSrvLogService;
import com.accesscorporate.app.wms.server.dal.entity.SExpSrvLogDO;
import com.accesscorporate.app.wms.server.dal.mapper.SExpSrvLogMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SExpSrvLogServiceImpl implements SExpSrvLogService {

    private final SExpSrvLogMapper sExpSrvLogMapper;

    @Override
    public SExpSrvLogDO getById(Long id) {
        return sExpSrvLogMapper.selectById(id);
    }


    @Override
    public int updateById(SExpSrvLogDO srvLog) {
        return sExpSrvLogMapper.updateById(srvLog);
    }
}
