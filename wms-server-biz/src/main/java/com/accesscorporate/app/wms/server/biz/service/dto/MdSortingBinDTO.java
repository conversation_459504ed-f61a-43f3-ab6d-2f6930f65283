package com.accesscorporate.app.wms.server.biz.service.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * md_sorting_bin 对象
 *
 * <AUTHOR>
 */
@Data
public class MdSortingBinDTO implements Serializable {
    private static final long serialVersionUID = -7462086750056853817L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 分拣区名称
     */
    private String sortingZoneName;

    /**
     * 分拣柜数量
     */
    private Integer sortingBinQty;

    /**
     * 是否可用0: 不可用1: 可用
     */
    private Integer isAvailable;

    /**
     * 分拣区号
     */
    private String sortingZoneNbr;

    /**
     * 分拣柜状态
     */
    private Integer sortingZoneStatus;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 创建人ID
     */
    private String createBy;

    /**
     * 最后修改日期
     */
    private Date updateTime;

    /**
     * 修改人ID
     */
    private String updateBy;

    /**
     * 是否删除0: 否1: 是
     */
    private Integer isDeleted;

    /**
     * 版本锁
     */
    private Long version;

    /**
     * 波次数量
     */
    private Integer waveQty;

    /**
     * 分拣柜类型
     */
    private String sortBinType;

    /**
     * 发票打印机ID
     */
    private Long invoicePrinterId;

    /**
     * 是否支持WCS
     */
    private Integer supportWcs;
}
