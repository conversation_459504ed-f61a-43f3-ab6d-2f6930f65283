package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.converter.DocAlcDetailMapStruct;
import com.accesscorporate.app.wms.server.biz.manager.impl.DocAlcDetailManager;
import com.accesscorporate.app.wms.server.biz.params.request.DocAlcDetailSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocAlcDetailResponse;
import com.accesscorporate.app.wms.server.biz.service.DocAlcDetailService;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import com.accesscorporate.app.wms.server.dal.entity.DocAlcDetail;
import com.accesscorporate.app.wms.server.dal.mapper.DocAlcDetailMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class DocAlcDetailServiceImpl extends ServiceImpl<DocAlcDetailMapper, DocAlcDetail> implements DocAlcDetailService {
    @Autowired
    private DocAlcDetailManager docAlcDetailManager;

    @Override
    public List<DocAlcDetailResponse> listAll() {
        LambdaQueryWrapper<DocAlcDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocAlcDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        return docAlcDetailManager.list(wrapper).stream()
                .map(DocAlcDetailMapStruct.INSTANCE::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public DocAlcDetailResponse get(Long id) {
        DocAlcDetail docAlcDetail = super.getById(id);
        return DocAlcDetailMapStruct.INSTANCE.convertToResponse(docAlcDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(DocAlcDetailSaveRequest request) {
        DocAlcDetail docAlcDetail = DocAlcDetailMapStruct.INSTANCE.convertToEntity(request);
        docAlcDetail.setWarehouseId(UserContextAssistant.getCurrentWarehouseId());
        return super.save(docAlcDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        return super.removeById(id);
    }

    @Override
    public Page<DocAlcDetailResponse> page(Integer current, Integer size, Long doHeaderId, Long skuId, String linestatus) {
        Page<DocAlcDetail> page = new Page<>(current, size);
        LambdaQueryWrapper<DocAlcDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocAlcDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        
        if (Objects.nonNull(doHeaderId)) {
            wrapper.eq(DocAlcDetail::getDoHeaderId, doHeaderId);
        }
        if (Objects.nonNull(skuId)) {
            wrapper.eq(DocAlcDetail::getSkuId, skuId);
        }
        if (StringUtils.hasText(linestatus)) {
            wrapper.eq(DocAlcDetail::getLinestatus, linestatus);
        }

        Page<DocAlcDetail> docAlcDetailPage = super.page(page, wrapper);
        List<DocAlcDetailResponse> responses = docAlcDetailPage.getRecords().stream()
                .map(DocAlcDetailMapStruct.INSTANCE::convertToResponse)
                .collect(Collectors.toList());
        return new Page<DocAlcDetailResponse>(docAlcDetailPage.getCurrent(), docAlcDetailPage.getSize(), docAlcDetailPage.getTotal())
                .setRecords(responses);
    }

    @Override
    public List<DocAlcDetailResponse> listByHeaderId(Long doHeaderId) {
        LambdaQueryWrapper<DocAlcDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocAlcDetail::getDoHeaderId, doHeaderId)
                .eq(DocAlcDetail::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocAlcDetail::getIsDeleted, Boolean.FALSE);
        return docAlcDetailManager.list(wrapper).stream()
                .map(DocAlcDetailMapStruct.INSTANCE::convertToResponse)
                .collect(Collectors.toList());
    }
}
