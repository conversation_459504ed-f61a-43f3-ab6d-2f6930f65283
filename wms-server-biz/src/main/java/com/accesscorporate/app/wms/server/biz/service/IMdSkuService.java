package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.dal.entity.MdSku;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 商品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-20
 */
public interface IMdSkuService extends IService<MdSku> {

    /**
     * 根据货品编码查询sku信息
     * @param goodsCode 货品编码
     * @return sku信息
     */
    MdSku querySkuByGoodsCode(String goodsCode);

    /**
     * 通过货品编码批量查询货品信息
     */
    List<MdSku> querySkuByGoodsCodes(List<String> goodsCodeList);



}
