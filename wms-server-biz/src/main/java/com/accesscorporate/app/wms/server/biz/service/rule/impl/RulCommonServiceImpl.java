package com.accesscorporate.app.wms.server.biz.service.rule.impl;

import com.accesscorporate.app.wms.server.biz.params.response.CfgCodeMasterResponse;
import com.accesscorporate.app.wms.server.biz.service.CfgCodeDetailService;
import com.accesscorporate.app.wms.server.biz.service.CfgCodeMasterService;
import com.accesscorporate.app.wms.server.biz.service.rule.IRulCommonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version JDK 17
 * @date 2025/2/26
 * @description
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RulCommonServiceImpl implements IRulCommonService {

    private final CfgCodeMasterService cfgCodeMasterService;

    private final CfgCodeDetailService cfgCodeDetailService;

    @Override
    public void getDirectionRule(String directionCode) {
        List<CfgCodeMasterResponse> codeMasters = cfgCodeMasterService.getCodeMasters(directionCode);
        if (codeMasters.isEmpty()) {
            log.warn("查询字典数据为空");
            return;
        }
//        new cfgCodeDetailPageRequest();
//        cfgCodeDetailService.pageList()
    }

}
