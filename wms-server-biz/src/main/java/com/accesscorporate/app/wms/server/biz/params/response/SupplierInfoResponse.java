package com.accesscorporate.app.wms.server.biz.params.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 供应商信息-响应Response
 *
 * <AUTHOR>
 * 2025/2/8  16:42
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "供应商信息 View Object")
public class SupplierInfoResponse {

    @Schema(title = "供应商ID")
    private Long id;

    @Schema(title = "供应商编码")
    private String supplierCode;

    @Schema(title = "供应商公司名称")
    private String supplierCompanyName;

    @Schema(title = "供应商类型")
    private Integer supplierType;

    @Schema(title = "送货方式")
    private Integer supplierDeliveryMethod;

    @Schema(title = "联系人")
    private String supplierContactManName;

    @Schema(title = "联系人手机")
    private String supplierContactMobile;

    @Schema(title = "联系人邮箱")
    private String supplierContactEmail;

    @Schema(title = "短信通知", description = "短信通知 0：不通知；1：通知")
    private Integer isSmsNoticeForPo;
    @Schema(title = "短信通知(文本)", description = "短信通知 0：不通知；1：通知")
    private String isSmsNoticeForPoStr;

    @Schema(title = "是否邮件通知", description = "邮件通知 0：不通知；1：通知")
    private Integer isEmailNoticeForPo;
    @Schema(title = "是否邮件通知(文本)", description = "邮件通知 0：不通知；1：通知")
    private String isEmailNoticeForPoStr;

    @Schema(title = "条码打印费")
    private BigDecimal barcodePrintFee;

    @Schema(title = "订货周期")
    private Integer poDays;

    @Schema(title = "是否提供发票")
    private Integer canInvoice;
    @Schema(title = "是否提供发票(文本)", description = "是否提供发票(文本) 参考Enum")
    private String canInvoiceStr;

    @Schema(title = "供应商来源", description = "（0：后台添加；1：供应商自己在网站注册）")
    private Integer supplierSource;
    @Schema(title = "供应商来源(文本)", description = "（0：后台添加；1：供应商自己在网站注册）")
    private Integer supplierSourceStr;

    @Schema(title = "最小订货金额")
    private BigDecimal minOrderAmount;

    @Schema(title = "付款日")
    private Integer paymentDay;

    @Schema(title = "税号")
    private String taxNumber;

    @Schema(title = "采购人员")
    private Long supplierPurchaserId;

    @Schema(title = "供应商状态", description = "0:申请中 1：正常")
    private Integer supplierStatus;
    @Schema(title = "供应商状态(文本)", description = "0:申请中 1：正常")
    private String supplierStatusStr;


}
