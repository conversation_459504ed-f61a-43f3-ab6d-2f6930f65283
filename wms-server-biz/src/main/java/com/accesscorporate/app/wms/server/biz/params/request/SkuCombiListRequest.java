package com.accesscorporate.app.wms.server.biz.params.request;

import com.idanchuang.component.base.page.PageDTO;
import com.idanchuang.component.core.util.DateUtil;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> liudongliang
 * @project : wms-server
 * @company : 浙江单创品牌管理有限公司
 * @desc :
 * Copyright @ 2024 danchuang Technology Co.Ltd. – Confidential and Proprietary
 */
@Data
public class SkuCombiListRequest extends PageDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -2400906788608618993L;

    /**
     * 组合条码集合
     */
    private List<String> combiBarcodeList;

    /**
     * 商品条码集合
     */
    private List<String> barcodeList;

    /**
     * 库位编码集合
     */
    private List<String> locCodeList;

    /**
     * 创建时间-起始(时间戳)
     */
    @DateTimeFormat(pattern = DateUtil.DF_YYYY_MM)
    private Date createTimeFm;

    /**
     * 创建时间-截止(时间戳)
     */
    @DateTimeFormat(pattern = DateUtil.DF_YYYY_MM)
    private Date createTimeTo;
}
