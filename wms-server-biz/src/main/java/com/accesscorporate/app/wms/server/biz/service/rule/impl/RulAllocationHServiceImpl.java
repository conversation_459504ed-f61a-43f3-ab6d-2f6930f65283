package com.accesscorporate.app.wms.server.biz.service.rule.impl;

import com.accesscorporate.app.wms.server.biz.converter.RulAllocationConverter;
import com.accesscorporate.app.wms.server.biz.manager.rule.RulAllocationHManager;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationHQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationHRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulAllocationHResponse;
import com.accesscorporate.app.wms.server.biz.service.rule.IRulAllocationHService;
import com.accesscorporate.app.wms.server.dal.entity.rule.RulAllocationHDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.page.PageData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-02-26
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RulAllocationHServiceImpl implements IRulAllocationHService {

    private final RulAllocationHManager rulAllocationHManager;


    @Override
    public PageData<RulAllocationHResponse> queryPage(RulAllocationHQueryPageRequest request) {
        Page<RulAllocationHDO> page = rulAllocationHManager.selectPage(request.getAllocationId(), request.getCurrent(), request.getSize());
        List<RulAllocationHResponse> responses = page.getRecords().stream().map(RulAllocationConverter::toRulAllocationHResponse).toList();

        return PageData.of(responses, request.getCurrent(), request.getSize(), page.getTotal());
    }

    @Override
    public RulAllocationHResponse queryById(Long id) {
        RulAllocationHDO rulAllocationHDO = rulAllocationHManager.selectById(id);
        if (rulAllocationHDO == null) {
            return null;
        }
        return RulAllocationConverter.toRulAllocationHResponse(rulAllocationHDO);
    }

    @Override
    public Boolean save(RulAllocationHRequest request) {
        RulAllocationHDO rulAllocationHDO = RulAllocationConverter.toRulAllocationHDO(request);
        if (rulAllocationHDO.getId() != null) {
            return rulAllocationHManager.updateRulAllocationH(rulAllocationHDO);
        }
        return rulAllocationHManager.createRulAllocationH(rulAllocationHDO);
    }

    @Override
    public Boolean deleteById(Long id) {
        return rulAllocationHManager.deleteRulAllocationH(id);
    }

    @Override
    public RulAllocationHResponse queryByAllocationId(Long allocationId) {

        RulAllocationHDO rulAllocationHDO = rulAllocationHManager.lambdaQuery()
                .eq(RulAllocationHDO::getAllocationId, allocationId)
                .eq(RulAllocationHDO::getIsDeleted, 0)
                .one();
        if (rulAllocationHDO == null) {
            return null;
        }
        return RulAllocationConverter.toRulAllocationHResponse(rulAllocationHDO);
    }
}
