package com.accesscorporate.app.wms.server.biz.manager;

import com.accesscorporate.app.wms.server.biz.bo.PickLocCheckBO;
import com.accesscorporate.app.wms.server.biz.service.IMdSkuService;
import com.accesscorporate.app.wms.server.biz.service.LocationService;
import com.accesscorporate.app.wms.server.biz.service.dto.PickLocImportDTO;
import com.accesscorporate.app.wms.server.common.enums.PickLocUomEnum;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import com.accesscorporate.app.wms.server.common.utils.ValidationUtils;
import com.accesscorporate.app.wms.server.dal.entity.Location;
import com.accesscorporate.app.wms.server.dal.entity.MdSku;
import com.accesscorporate.app.wms.server.dal.entity.pickloc.MdPickLocDO;
import com.accesscorporate.app.wms.server.dal.repository.MdPickLocRepository;
import com.idanchuang.component.base.exception.core.ExFactory;
import com.idanchuang.component.base.exception.core.asserts.ExBusinessAssert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-24 16:49
 * Description: 商品捡货位
 */
@Component
public class PickLocManager {

    @Autowired
    private IMdSkuService mdSkuService;

    @Autowired
    private MdPickLocRepository mdPickLocRepository;

    @Autowired
    private LocationService locationService;

    /**
     * 校验输入的条码、库位编码是否合法
     */
    public PickLocCheckBO checkSkuAndLocation(String productCode, String locCode, Long pickLocId) {
        //1、货品是否真实存在
        MdSku mdSku = mdSkuService.querySkuByGoodsCode(productCode);
        if (Objects.isNull(mdSku)) {
            throw ExFactory.throwBusiness("保存商品捡货位，货品编码查询结果为空，货品编码:{}", productCode);
        }
        Long currentWarehouseId = UserContextAssistant.getCurrentWarehouseId();
        //2、库位编码是否有效
        Location location = locationService.queryLocationByCodeAndWarehouseId(locCode, currentWarehouseId);
        if (Objects.isNull(location)) {
            throw ExFactory.throwBusiness("保存商品捡货位，库位编码查询结果为空，库位编码:{}", locCode);
        }
        Long locId = location.getId();
        Long skuId = mdSku.getId();
        //3、新增场景下，该货品和库位信息是否已经存在
        MdPickLocDO mdPickLocDO = mdPickLocRepository.queryPickLocBySkuIdAndWarehouseId(skuId, locId, currentWarehouseId);
        if (Objects.isNull(pickLocId)) {
            if (Objects.nonNull(mdPickLocDO)) {
                throw ExFactory.throwBusiness("新增商品捡货位，该货品和库位已经存在于捡货位列表，货品编码:{}，库位编码:{}"
                        , productCode, locCode);
            }
        } else {//编辑场景下
            if (Objects.nonNull(mdPickLocDO)) {
                Long dbLocDOId = mdPickLocDO.getId();
                if (!Objects.equals(pickLocId,dbLocDOId)){
                    throw ExFactory.throwBusiness("编辑商品捡货位，该货品和库位已经存在于捡货位列表，货品编码:{}，库位编码:{}"
                            , productCode, locCode);
                }
            }
        }
        return PickLocCheckBO.builder()
                .locId(locId)
                .skuId(skuId)
                .build();

    }

    /**
     * 检查导入的信息是否合法
     */
    public void checkImportPickLoc(List<PickLocImportDTO> importList){
        //同一个条码不允许出现两条
        List<String> repeatProductCodes = importList.stream()
                .map(PickLocImportDTO::getProductCode) // 提取 productCode
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting())) // 按 productCode 分组并统计数量
                .entrySet().stream() // 将 Map 转换为 Stream<Entry<String, Long>>
                .filter(entry -> entry.getValue() > 1) // 筛选出数量大于 1 的 productCode
                .map(Map.Entry::getKey) // 提取重复的 productCode
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(repeatProductCodes)){
            ExFactory.throwBusiness("存在重复的货品编码记录，货品编码集合:{}",repeatProductCodes);
        }
        //基础校验
        for (int i = 0; i < importList.size(); i++) {
            int rowNum = i + 1;
            PickLocImportDTO importItem = importList.get(i);
            ValidationUtils.validate(importItem, rowNum);
            //校验条码库位信息是否存在
            checkSkuAndLocation(importItem.getProductCode(), importItem.getLocCode(), null);
            //校验补货单位是否合法
            String uom = importItem.getUom();
            PickLocUomEnum pickLocUomEnum = PickLocUomEnum.getPickLocUomEnum(uom);
            ExBusinessAssert.notNull(pickLocUomEnum, "第{}行输入的补货单位不合法，输入的补货单位:{}", rowNum,uom);
            //补货上限需要大于补货下限
            Integer lowerLimit = importItem.getLowerLimit();
            Integer upLimit = importItem.getUpLimit();
            ExBusinessAssert.isTrue(lowerLimit<=upLimit,"第{}行输入补货上限小于补货下限,补货上限:{}，补货下限:{}",rowNum,upLimit,lowerLimit);
        }

    }





}
