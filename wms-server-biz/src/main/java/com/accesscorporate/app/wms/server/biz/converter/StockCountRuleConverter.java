package com.accesscorporate.app.wms.server.biz.converter;

import com.accesscorporate.app.wms.server.biz.params.request.StocktakingRuleCreateRequest;
import com.accesscorporate.app.wms.server.biz.params.request.StocktakingRuleModifyRequest;
import com.accesscorporate.app.wms.server.biz.params.response.StocktakingRuleDetailResponse;
import com.accesscorporate.app.wms.server.biz.params.response.StocktakingRuleResponse;
import com.accesscorporate.app.wms.server.common.enums.StocktakingRulePriorityEnum;
import com.accesscorporate.app.wms.server.dal.entity.StockCountRuleDO;

/**
 * 盘点规则转换器
 *
 * <AUTHOR>
 * 2025/2/14  16:37
 */
public class StockCountRuleConverter {


    public static StocktakingRuleResponse toStocktakingRulesResponse(StockCountRuleDO stockCountRuleDO) {
        return new StocktakingRuleResponse()
                .setId(stockCountRuleDO.getId())
                .setRuleCode(stockCountRuleDO.getCode())
                .setRuleName(stockCountRuleDO.getName())
                .setPriority(stockCountRuleDO.getPriority())
                .setPriorityStr(StocktakingRulePriorityEnum.getDescByCode(stockCountRuleDO.getPriority()))
                .setUpdater(stockCountRuleDO.getUpdateBy());
    }


    public static StocktakingRuleDetailResponse toStocktakingRuleDetailResponse(StockCountRuleDO stockCountRuleDO) {
        return new StocktakingRuleDetailResponse()
                .setProductionDate(stockCountRuleDO.getLotatt01())
                .setExpirationDate(stockCountRuleDO.getLotatt02())
                .setInboundDate(stockCountRuleDO.getLotatt03())
                .setSupplier(stockCountRuleDO.getLotatt04())
                .setLotNumber(stockCountRuleDO.getLotatt05())
                .setMerchant(stockCountRuleDO.getLotatt06())
                .setPackaging(stockCountRuleDO.getLotatt07())
                .setManufacturer(stockCountRuleDO.getLotatt08())
                .setPurchasePrice(stockCountRuleDO.getLotatt09())
                .setPurchaseOrderNumber(stockCountRuleDO.getLotatt10())
                .setBatch11(stockCountRuleDO.getLotatt11())
                .setId(stockCountRuleDO.getId())
                .setRuleCode(stockCountRuleDO.getCode())
                .setRuleName(stockCountRuleDO.getName())
                .setPriority(stockCountRuleDO.getPriority())
                .setPriorityStr(StocktakingRulePriorityEnum.getDescByCode(stockCountRuleDO.getPriority()));
    }

    public static StockCountRuleDO toStockCountRuleDO(StocktakingRuleModifyRequest stocktakingRuleModifyRequest) {
        return new StockCountRuleDO()
                .setId(stocktakingRuleModifyRequest.getId())
                .setCode(stocktakingRuleModifyRequest.getRuleCode())
                .setName(stocktakingRuleModifyRequest.getRuleName())
                .setPriority(stocktakingRuleModifyRequest.getPriority())
                .setLotatt01(stocktakingRuleModifyRequest.getProductionDate())
                .setLotatt02(stocktakingRuleModifyRequest.getExpirationDate())
                .setLotatt03(stocktakingRuleModifyRequest.getInboundDate())
                .setLotatt04(stocktakingRuleModifyRequest.getSupplier())
                .setLotatt05(stocktakingRuleModifyRequest.getLotNumber())
                .setLotatt06(stocktakingRuleModifyRequest.getMerchant())
                .setLotatt07(stocktakingRuleModifyRequest.getPackaging())
                .setLotatt08(stocktakingRuleModifyRequest.getManufacturer())
                .setLotatt09(stocktakingRuleModifyRequest.getPurchasePrice())
                .setLotatt10(stocktakingRuleModifyRequest.getPurchaseOrderNumber())
                .setLotatt11(stocktakingRuleModifyRequest.getBatch11());

    }


    public static StockCountRuleDO toStockCountRuleDO(StocktakingRuleCreateRequest stocktakingRuleCreateRequest) {
        return new StockCountRuleDO()
                .setCode(stocktakingRuleCreateRequest.getRuleCode())
                .setName(stocktakingRuleCreateRequest.getRuleName())
                .setPriority(stocktakingRuleCreateRequest.getPriority())
                .setLotatt01(stocktakingRuleCreateRequest.getProductionDate())
                .setLotatt02(stocktakingRuleCreateRequest.getExpirationDate())
                .setLotatt03(stocktakingRuleCreateRequest.getInboundDate())
                .setLotatt04(stocktakingRuleCreateRequest.getSupplier())
                .setLotatt05(stocktakingRuleCreateRequest.getLotNumber())
                .setLotatt06(stocktakingRuleCreateRequest.getMerchant())
                .setLotatt07(stocktakingRuleCreateRequest.getPackaging())
                .setLotatt08(stocktakingRuleCreateRequest.getManufacturer())
                .setLotatt09(stocktakingRuleCreateRequest.getPurchasePrice())
                .setLotatt10(stocktakingRuleCreateRequest.getPurchaseOrderNumber())
                .setLotatt11(stocktakingRuleCreateRequest.getBatch11());

    }



}
