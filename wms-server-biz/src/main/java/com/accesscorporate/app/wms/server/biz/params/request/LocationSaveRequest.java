package com.accesscorporate.app.wms.server.biz.params.request;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "库位保存请求体")
public class LocationSaveRequest {
    private Long partitionId;
    private String locType;
    private Integer canMixProduct;
    private Integer canMixBatch; // 批次混放
    private Integer ignoreLpn;
    private String aisleFrom;
    private String aisleTo;
    private String bayFrom;
    private String bayTo;
    private String levelFrom;
    private String levelTo;
    private String positionFrom;
    private String positionTo;
}
