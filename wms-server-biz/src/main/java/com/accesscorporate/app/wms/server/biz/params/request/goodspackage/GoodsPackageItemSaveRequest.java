package com.accesscorporate.app.wms.server.biz.params.request.goodspackage;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-18 15:30
 * Description: 保存包装明细信息请求参数
 */
@Tag(name = "保存包装明细信息请求参数")
@Data
public class GoodsPackageItemSaveRequest {

    @Schema(title = "商品包装明细id，新增没有值")
    private Long packageDetailId;

    @Schema(title = "数量")
    @NotNull(message = "数量不能为空")
    private BigDecimal qty;

    @Schema(title = "单位")
    @NotBlank(message = "单位不能为空")
    private String packUom;

    @Schema(title = "商品包装表主键id，编辑场景下没有值")
    private Long packageId;

}
