package com.accesscorporate.app.wms.server.biz.converter;

import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayDetailRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayHRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulPutawayDetailResponse;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulPutawayHResponse;
import com.accesscorporate.app.wms.server.dal.entity.rule.RulPutawayDetailDO;
import com.accesscorporate.app.wms.server.dal.entity.rule.RulPutawayHDO;

/**
 * @author: gaohao
 * @date: 2025-02-20 15:06
 * @desc:
 */
public class RulPutawayConverter {

    public static RulPutawayHResponse toRulPutawayHResponse(RulPutawayHDO rulPutawayHDO) {

        return new RulPutawayHResponse().setId(rulPutawayHDO.getId()).setPutawayCode(rulPutawayHDO.getPutawayCode())
                .setPutawayName(rulPutawayHDO.getDescr())
                .setActiveFlag(rulPutawayHDO.getActiveFlag())
                .setCreateTime(rulPutawayHDO.getCreateTime().toString())
                .setUpdateTime(rulPutawayHDO.getUpdateTime().toString());

    }

    public static RulPutawayHDO toRulPutawayHDO(RulPutawayHRequest request){
        return new RulPutawayHDO().setId(request.getId())
                .setPutawayCode(request.getPutawayCode())
                .setDescr(request.getPutawayName())
                .setActiveFlag(request.getActiveFlag());
    }

    public static RulPutawayDetailResponse toRulPutawayDetailResponse(RulPutawayDetailDO rulPutawayDetailDO) {

        return new RulPutawayDetailResponse().setId(rulPutawayDetailDO.getId())
                .setRulHeaderId(rulPutawayDetailDO.getRulHeaderId())
                .setDocTypes(rulPutawayDetailDO.getDocTypes())
                .setLocTypeOrigin(rulPutawayDetailDO.getLocTypeOrigin())
                .setSkuCatgIds(rulPutawayDetailDO.getSkuCatgIds())
                .setSupplierIds(rulPutawayDetailDO.getSupplierIds())
                .setMerchantIds(rulPutawayDetailDO.getMerchantIds())
                .setLocTypes(rulPutawayDetailDO.getLocTypes())
                .setEmptyLocOnly(rulPutawayDetailDO.getEmptyLocOnly())
                .setConcentrateType(rulPutawayDetailDO.getConcentrateType())
                .setDirectionRule(rulPutawayDetailDO.getDirectionRule())
                .setTargetLocs(rulPutawayDetailDO.getTargetLocs())
                .setTargetPartitionts(rulPutawayDetailDO.getTargetPartitionts())
                .setLatestOperTypes(rulPutawayDetailDO.getLatestOperTypes())
                .setActiveFlag(rulPutawayDetailDO.getActiveFlag())
                .setLineNo(rulPutawayDetailDO.getLineNo())
                .setPartitionRuleId(rulPutawayDetailDO.getPartitionRuleId());

    }

    public static RulPutawayDetailDO toRulPutawayDetailDO(RulPutawayDetailRequest request){
        return new RulPutawayDetailDO().setId(request.getId())
                .setRulHeaderId(request.getRulHeaderId())
                .setDocTypes(request.getDocTypes())
                .setLocTypeOrigin(request.getLocTypeOrigin())
                .setSkuCatgIds(request.getSkuCatgIds())
                .setSupplierIds(request.getSupplierIds())
                .setMerchantIds(request.getMerchantIds())
                .setLocTypes(request.getLocTypes())
                .setEmptyLocOnly(request.getEmptyLocOnly())
                .setConcentrateType(request.getConcentrateType())
                .setDirectionRule(request.getDirectionRule())
                .setTargetLocs(request.getTargetLocs())
                .setTargetPartitionts(request.getTargetPartitionts())
                .setLatestOperTypes(request.getLatestOperTypes())
                .setActiveFlag(request.getActiveFlag())
                .setLineNo(request.getLineNo())
                .setPartitionRuleId(request.getPartitionRuleId());
    }

}
