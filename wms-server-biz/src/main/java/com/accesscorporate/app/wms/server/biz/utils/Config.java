package com.accesscorporate.app.wms.server.biz.utils;

import cn.hutool.core.collection.ListUtil;
import com.accesscorporate.app.wms.server.biz.service.ICfgConfigurationService;
import com.accesscorporate.app.wms.server.common.context.UserContext;
import com.accesscorporate.app.wms.server.common.exceptions.SystemException;
import com.accesscorporate.app.wms.server.dal.entity.CfgConfigurationDO;
import com.accesscorporate.app.wms.server.dal.mapper.CfgConfigurationMapper;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.idanchuang.component.core.util.SpringUtil;
import com.idanchuang.component.core.util.StringUtil;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.ehcache.core.util.CollectionUtil;
import org.springframework.util.CollectionUtils;


import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


@lombok.extern.slf4j.Slf4j
public class Config {
    public static enum ConfigLevel {
        WAREHOUSE(0), //库房
        TENANT(1),// 租户
        GLOBAL(2);// 全局

        private final Integer value;

        ConfigLevel(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public static final String dictionaryCode() {
            return "CONFIG_LEVEL";
        }
    }

    public static final String DEFAULT_DELIMITER = ",";

    private static String CACHE_REGION = "sys_cfg";

    public static String get(Enum e, ConfigLevel configLevel) {
        return convert2String(getConfig(e, configLevel));
    }

    public static String get(Enum e, ConfigLevel configLevel, String defaultValue) {
        String s = convert2String(getConfig(e, configLevel));
        return StringUtil.isNotBlank(s) ? s : defaultValue;
    }

    public static Integer getInt(Enum e, ConfigLevel configLevel,Integer defalutValue) {
        return convert2Int(getConfig(e, configLevel),defalutValue);
    }

    /**
     * 没有配置的时候默认为True
     */
    public static Boolean isDefaultTrue(Enum e, ConfigLevel configLevel) {
        return is(e, configLevel, true);
    }

    /**
     * 没有配置的时候默认为False
     */
    public static Boolean isDefaultFalse(Enum e, ConfigLevel configLevel) {
        return is(e, configLevel, false);
    }

    public static Boolean is(Enum e, ConfigLevel configLevel, Boolean nullDefaultValue) {
        return convert2Boolean(getConfig(e, configLevel), nullDefaultValue);
    }

    public static boolean isFmJson(Enum e, ConfigLevel configLevel, String detailProperty) {
        String s = getFmJson(e, configLevel, detailProperty);
        return StringUtil.isNotEmpty(s) ? isIn(s.toUpperCase(), "Y", "1") : false;
    }

    public static String getFmJson(Enum e, ConfigLevel configLevel, String detailProperty) {
        String cfgValue = get(e, configLevel);
        if (StringUtil.isNotBlank(cfgValue)) {
            try {

                return JSONObject.parseObject(cfgValue).getString(detailProperty);
            } catch (JSONException e1) {
                return null;
            }
        }
        return "";
    }

    public static Integer getIntFmJson(Enum e, ConfigLevel configLevel, String detailProperty) {
        String cfgValue = getFmJson(e, configLevel, detailProperty);
        if (StringUtil.isNotBlank(cfgValue)) {
            return NumberUtils.createInteger(cfgValue);
        }
        return null;
    }

    /**
     * 配置项为：英文逗号间隔的字符串，比如：mailReceiver: <EMAIL>,<EMAIL>
     */
    public static List<String> getByDelimit(Enum e, ConfigLevel configLevel) {
        return getByDelimit(e, DEFAULT_DELIMITER, configLevel);
    }

    public static List<String> getByDelimit(Enum e, ConfigLevel configLevel,List<String> defalutValue) {
        return getByDelimit(e, DEFAULT_DELIMITER, configLevel,defalutValue);
    }

    /**
     * 配置项为：间隔符间隔的字符串，比如：mailReceiver: <EMAIL>,<EMAIL>
     */
    public static List<String> getByDelimit(Enum e, String delimited, ConfigLevel configLevel) {
        String cfgValue = get(e, configLevel);
        return StringUtil.isEmpty(cfgValue) ? new ArrayList<String>() :  Lists.newArrayList(cfgValue.split(delimited));
    }
    public static List<String> getByDelimit(Enum e, String delimited, ConfigLevel configLevel,List<String> defalutValue) {
        String cfgValue = get(e, configLevel);
        return StringUtil.isEmpty(cfgValue) ? (CollectionUtils.isEmpty(defalutValue)? new ArrayList<String>():  defalutValue)
                :  Lists.newArrayList(cfgValue.split(delimited));
    }

    public static List<String> getByDelimit(String key, String delimited, Integer configLevel) {
        String cfgValue = Config.convert2String(getConfig(key, Config.getLevel(configLevel)));
        return StringUtil.isEmpty(cfgValue) ? new ArrayList<String>() :  Lists.newArrayList(cfgValue.split(delimited));
    }

    public static boolean convert2Boolean(CfgConfigurationDO cfg, Boolean nullDefaultValue) {
        return (cfg != null && StringUtil.isNotEmpty(cfg.getValueString())) ? isIn(cfg.getValueString().toUpperCase(), "Y", "1") : nullDefaultValue;
    }

    public static String convert2String(CfgConfigurationDO cfg) {
        return cfg != null ? (null == cfg.getValueString() ? "" : cfg.getValueString()) : "";
    }

    public static Integer convert2Int(CfgConfigurationDO cfg,Integer defaultValue) {
        return (cfg != null && cfg.getValueInt() != null) ? cfg.getValueInt() : defaultValue;
    }

    public static CfgConfigurationDO getConfig(String key, ConfigLevel configLevel) {
        MutablePair<Long, Long> ids = getIds(configLevel);
        return getConfig(key, ids.getLeft(), ids.getRight());
    }

    public static String getConfigValue(String key, ConfigLevel configLevel) {
        return getConfigValue(key, configLevel, null);
    }

    public static String getConfigValue(String key, ConfigLevel configLevel, String defaultValue) {
        MutablePair<Long, Long> ids = getIds(configLevel);
        CfgConfigurationDO cfg = getConfig(key, ids.getLeft(), ids.getRight());
        return (cfg != null && cfg.getId() != null) ? cfg.getValueString() : defaultValue;
    }

    private static CfgConfigurationDO getConfig(Enum e, ConfigLevel configLevel) {
        MutablePair<Long, Long> ids = getIds(configLevel);

        return getConfig(e, ids.getLeft(), ids.getRight());
    }

    private static CfgConfigurationDO getConfig(Enum e, Long warehouseId, Long tenantId) {
        return getConfig(getKey(e), warehouseId, tenantId);
    }

    private static String getKey(Enum e) {
        String key = e.getClass().getName().toString().split("\\$")[1].toLowerCase() + "_" + e.name();
        return key.replace("_", ".");
    }

    private static MutablePair<Long, Long> getIds(ConfigLevel configLevel) {
        switch (configLevel) {
            case WAREHOUSE:
                Long warehouseId = UserContext.getCurrentWarehouseId();
                if (warehouseId == null) {
                    throw new SystemException(SystemException.WAREHOUSE_IS_NULL);
                }
                return new MutablePair(warehouseId, null);
            case TENANT:
                Long tenantId = UserContext.getTenantId();
                if (tenantId == null) {
                    throw new SystemException(SystemException.TENANG_IS_NULL);
                }
                return new MutablePair(null, tenantId);
            default:
                return new MutablePair(null, null);
        }
    }

    private static CfgConfigurationDO getConfig(String propertyName, Long warehouseId, Long tenantId) {
        String key = propertyName;
        if (warehouseId != null) {
            key = key + "-" + warehouseId + "-" + UserContext.getTenantId();
        } else if (tenantId != null) {
            key = key + "--" + tenantId;
        }
        RedisCacheProvider sysCacheProvider = SpringUtil.getBean(RedisCacheProvider.class);
        CfgConfigurationDO cfg = (CfgConfigurationDO) sysCacheProvider.get(CACHE_REGION, key);
        if (cfg != null) {
            return cfg;
        }
        ICfgConfigurationService configurationService = SpringUtil.getBean(ICfgConfigurationService.class);;
        cfg = configurationService.getConfig(propertyName, warehouseId, tenantId);
        if (cfg == null) {
            //如果数据库不存在该配置项，则将该新建一个空配置项缓存，避免再查数据库
            cfg = new CfgConfigurationDO();
            cfg.setConfigNo(propertyName);
        }
        sysCacheProvider.put(CACHE_REGION, key, cfg);
        return cfg;
    }
    public static Config.ConfigLevel getLevel(Integer configLevel) {
        if (configLevel.equals(Config.ConfigLevel.GLOBAL.getValue())) {
            return Config.ConfigLevel.GLOBAL;
        } else if (configLevel.equals(Config.ConfigLevel.TENANT.getValue())) {
            return Config.ConfigLevel.TENANT;
        } else {
            return Config.ConfigLevel.WAREHOUSE;
        }
    }
    /**
     * <pre>
     * 判断第一个参数是否等于后续的参数中的某一个
     * </pre>
     *
     * @param value
     * @param value1
     * @param strs
     * @return
     */
    public static boolean isIn(String value, String value1, String... strs) {
        if (value.equals(value1))
            return true;

        if (strs != null && strs.length > 0)
            for (String str : strs) {
                if (str.equals(value))
                    return true;
            }
        return false;
    }
}
