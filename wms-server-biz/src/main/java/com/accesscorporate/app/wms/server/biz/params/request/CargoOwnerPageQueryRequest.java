package com.accesscorporate.app.wms.server.biz.params.request;

import com.idanchuang.component.base.page.PageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 供应商列表-条件查询Req
 *
 * <AUTHOR>
 * 2025/2/8  16:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Tag(name = "货主列表查询参数")
public class CargoOwnerPageQueryRequest extends PageDTO {

    @Schema(title = "货主编码")
    private String cargoOwnerCode;

    @Schema(title = "货主名称", description = "支持模糊查询")
    private String cargoOwnerName;

}
