package com.accesscorporate.app.wms.server.biz.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 供应商信息导入--对象模型
 *
 * <AUTHOR>
 * @since 2025/2/24  10:55
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SupplierImportModel {

    @ExcelProperty(value = "供应商编码")
    private String supplierCode;

    @ExcelProperty(value = "供应商公司名称")
    private String supplierName;

    @ExcelProperty(value = "供应商类型")
    private Integer supplierType;

    @ExcelProperty(value = "送货方式")
    private Integer supplierDeliveryMethod;

    @ExcelProperty(value = "联系人")
    private String supplierContactManName;

    @ExcelProperty(value = "联系人手机")
    private String supplierContactMobile;

    @ExcelProperty(value = "联系人邮箱")
    private String supplierContactEmail;

    @ExcelProperty(value = "是否短信通知")
    private String isSmsNoticeForPo;

    @ExcelProperty(value = "是否邮件通知")
    private String isEmailNoticeForPo;

    @ExcelProperty(value = "条码打印费")
    private BigDecimal barcodePrintFee;

    @ExcelProperty(value = "订货周期")
    private Integer poDays;

    @ExcelProperty(value = "是否提供发票")
    private String canInvoice;

    @ExcelProperty(value = "供应商来源")
    private String supplierSource;

    @ExcelProperty(value = "最小订货金额")
    private BigDecimal minOrderAmount;

    @ExcelProperty(value = "付款日")
    private Integer paymentDay;

    @ExcelProperty(value = "税号")
    private String taxNumber;

    @ExcelProperty(value = "供应商状态")
    private String supplierStatus;

    @ExcelProperty(value = "采购员姓名")
    private String purchaserName;

    @ExcelProperty(value = "采购员电话")
    private String purchaserTel;

    @ExcelProperty(value = "供应商公司名")
    private String supplierCompanyName;

    @ExcelProperty(value = "商家样本URL")
    private String specimenUrl;

    @ExcelProperty(value = "仓库地址")
    private String warehouseAddress;

    @ExcelProperty(value = "拼音名称")
    private String pinyinName;


}
