package com.accesscorporate.app.wms.server.biz.manager.rule;

import com.accesscorporate.app.wms.server.dal.entity.rule.RulPutawayHDO;
import com.accesscorporate.app.wms.server.dal.mapper.rule.RulPutawayHMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: gao<PERSON>
 * @date: 2025-02-20 14:53
 * @desc:
 */
@Component
@RequiredArgsConstructor
public class RulPutawayHManager extends ServiceImpl<RulPutawayHMapper, RulPutawayHDO> {

    private final RulPutawayHMapper rulPutawayHMapper;

    public Page<RulPutawayHDO> selectPage(String putawayCode,
                                          String putawayName,
                                          Long current,
                                          Long size) {
        return lambdaQuery()
                .eq(StringUtils.isNotEmpty(putawayCode), RulPutawayHDO::getPutawayCode, putawayCode)
                .like(StringUtils.isNotEmpty(putawayName), RulPutawayHDO::getDescr, putawayName)
                .eq(RulPutawayHDO::getIsDeleted, 0)
                .page(new Page<>(current, size));
    }

    public RulPutawayHDO selectById(Long id) {
        return getById(id);
    }

    public Boolean updateRulPutawayH(RulPutawayHDO rulPutawayHDO) {
        return updateById(rulPutawayHDO);
    }

    public Boolean createRulPutawayH(RulPutawayHDO rulPutawayHDO) {
        return save(rulPutawayHDO);
    }

}
