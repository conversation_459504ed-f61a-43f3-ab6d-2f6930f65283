package com.accesscorporate.app.wms.server.biz.converter;

import com.accesscorporate.app.wms.server.biz.excel.model.SupplierImportModel;
import com.accesscorporate.app.wms.server.biz.params.request.SupplierInfoModifyRequest;
import com.accesscorporate.app.wms.server.biz.params.response.SupplierInfoResponse;
import com.accesscorporate.app.wms.server.common.enums.*;
import com.accesscorporate.app.wms.server.dal.entity.SupplierDO;

import java.util.Objects;

/**
 * 供应商管理-对象-转换器
 *
 * <AUTHOR>
 * 2025/2/8  17:02
 */
public class SupplierConverter {


    public static SupplierInfoResponse toSupplierInfoResponse(SupplierDO supplierDO) {
        return new SupplierInfoResponse()
                .setId(supplierDO.getId())
                .setSupplierCode(supplierDO.getSupplierCode())
                .setSupplierCompanyName(supplierDO.getSupplierCompanyName())
                .setSupplierType(supplierDO.getSupplierType())
                .setSupplierDeliveryMethod(supplierDO.getSupplierDeliveryMethod())
                .setSupplierContactManName(supplierDO.getSupplierContactManName())
                .setSupplierContactMobile(supplierDO.getSupplierContactMobile())
                .setSupplierContactEmail(supplierDO.getSupplierContactEmail())

                .setIsSmsNoticeForPo(supplierDO.getIsSmsNoticeForPo())
                .setIsSmsNoticeForPoStr(SupplierNoticeEnum.getDescByCode(supplierDO.getIsSmsNoticeForPo()))
                .setIsEmailNoticeForPo(supplierDO.getIsEmailNoticeForPo())
                .setIsEmailNoticeForPoStr(SupplierNoticeEnum.getDescByCode(supplierDO.getIsEmailNoticeForPo()))

                .setBarcodePrintFee(supplierDO.getBarcodePrintFee())
                .setPoDays(supplierDO.getPoDays())
                .setCanInvoice(supplierDO.getCanInvoice())
                .setCanInvoiceStr(SupplierCanInvoiceEnum.getDescByCode(supplierDO.getCanInvoice()))
                .setSupplierSource(supplierDO.getSupplierSource())
                .setMinOrderAmount(supplierDO.getMinOrderAmount())
                .setPaymentDay(supplierDO.getPaymentDay())
                .setTaxNumber(supplierDO.getTaxNumber())
                .setSupplierPurchaserId(supplierDO.getSupplierPurchaserId())

                .setSupplierStatus(supplierDO.getSupplierStatus())
                .setSupplierStatusStr(SupplierStatusEnum.getDescByCode(supplierDO.getSupplierStatus()));
    }


    public static SupplierDO toSupplierDO(SupplierInfoModifyRequest supplierInfoModifyRequest) {
        return new SupplierDO()
                .setId(supplierInfoModifyRequest.getId())
                .setSupplierCode(supplierInfoModifyRequest.getSupplierCode())
                .setSupplierCompanyName(supplierInfoModifyRequest.getSupplierName())
                .setSupplierType(supplierInfoModifyRequest.getSupplierType())
                .setSupplierDeliveryMethod(supplierInfoModifyRequest.getSupplierDeliveryMethod())
                .setSupplierContactManName(supplierInfoModifyRequest.getSupplierContactManName())
                .setSupplierContactMobile(supplierInfoModifyRequest.getSupplierContactMobile())
                .setSupplierContactEmail(supplierInfoModifyRequest.getSupplierContactEmail())
                .setIsSmsNoticeForPo(Objects.isNull(supplierInfoModifyRequest.getIsEmailNoticeForPo()) ? 0 : supplierInfoModifyRequest.getIsSmsNoticeForPo())
                .setIsEmailNoticeForPo(Objects.isNull(supplierInfoModifyRequest.getIsEmailNoticeForPo()) ? 0 : supplierInfoModifyRequest.getIsEmailNoticeForPo())
                .setBarcodePrintFee(supplierInfoModifyRequest.getBarcodePrintFee())
                .setPoDays(supplierInfoModifyRequest.getPoDays())
                .setCanInvoice(supplierInfoModifyRequest.getCanInvoice())
                .setSupplierSource(supplierInfoModifyRequest.getSupplierSource())
                .setMinOrderAmount(supplierInfoModifyRequest.getMinOrderAmount())
                .setPaymentDay(supplierInfoModifyRequest.getPaymentDay())
                .setTaxNumber(supplierInfoModifyRequest.getTaxNumber())
                .setSupplierStatus(supplierInfoModifyRequest.getSupplierStatus());
    }


    public static SupplierDO toSupplierDO(SupplierImportModel importModel) {
        return new SupplierDO()
                .setSupplierCode(importModel.getSupplierCode())
                .setSupplierCompanyName(importModel.getSupplierName())
                .setSupplierType(importModel.getSupplierType())
                .setSupplierDeliveryMethod(importModel.getSupplierDeliveryMethod())
                .setSupplierContactManName(importModel.getSupplierContactManName())
                .setSupplierContactMobile(importModel.getSupplierContactMobile())
                .setSupplierContactEmail(importModel.getSupplierContactEmail())
                .setIsSmsNoticeForPo(WhetherEnum.getCodeByDesc(importModel.getIsSmsNoticeForPo()))
                .setIsEmailNoticeForPo(WhetherEnum.getCodeByDesc(importModel.getIsEmailNoticeForPo()))
                .setBarcodePrintFee(importModel.getBarcodePrintFee())
                .setPoDays(importModel.getPoDays())
                .setCanInvoice(WhetherEnum.getCodeByDesc(importModel.getCanInvoice()))
                .setSupplierSource(SupplierSourceEnum.getCodeByDesc(importModel.getSupplierSource()))
                .setMinOrderAmount(importModel.getMinOrderAmount())
                .setPaymentDay(importModel.getPaymentDay())
                .setTaxNumber(importModel.getTaxNumber())
                .setSupplierStatus(SupplierStatusEnum.getCodeByDesc(importModel.getSupplierStatus()))
                .setPurchaserName(importModel.getPurchaserName())
                .setPurchaserTel(importModel.getPurchaserTel())
                .setSupplierCompanyName(importModel.getSupplierCompanyName())
                .setSpecimenUrl(importModel.getSpecimenUrl())
                .setWarehouseAddress(importModel.getWarehouseAddress())
                .setPinyinName(importModel.getPinyinName());

    }


}
