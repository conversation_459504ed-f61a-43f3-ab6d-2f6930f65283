package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.mq.message.AllocateMessage;

import java.util.List;
import java.util.Map;

/**
 * 自动分配服务接口
 * 
 * <AUTHOR>
 */
public interface AllocateService {
    /**
     * doDetail不存在
     */
    public static final String ALC_RESULT_NODO = "1";

    /**
     * 没有库存
     */
    public static final String ALC_RESULT_NOSTOCK = "2";

    /**
     * 分配成功
     */
    public static final String ALC_RESULT_SUCCESS = "3";

    /**
     * doDetail中产品没有默认拣货位
     */
    public static final String ALC_RESULT_NORULE = "4";

    /**
     * 补货任务已存在
     */
    public static final String REPL_TASK_IS_EXIST = "10";

    /**
     * 该商品没有默认拣货位
     */
    public static final String SKU_NO_DEFAULT_LOC = "21";

    /**
     * 库存已补货足够
     */
    public static final String STOCK_REPL_QTY_ENOUGH = "22";

    /**
     * 库存数量不足
     */
    public static final String NO_ENOUGH_STOCK_QTY = "23";

    /**
     * 补货成功
     */
    public static final String REP_SUCCESS = "24";

    /**
     * 没有补货规则
     */
    public static final String NO_REPL_RULE = "25";

    /**
     * 需要补货
     */
    public static final String NEED_REPL = "26";

    /**
     * 订单已经取消
     */
    public static final String DO_CANCEL = "27";
    /**
     * 处理分配任务
     * 
     * @param allocateMessage 分配消息
     */
    void processAllocate(AllocateMessage allocateMessage);
    
    /**
     * 根据仓库ID执行分配逻辑
     * 
     * @param warehouseId 仓库ID
     * @param batchSize 批次大小
     */
    void executeAllocateByWarehouse(Long warehouseId, Integer batchSize);

    List<Long> queryNeedAllocateDoHeaderIds(int batchAllocateNum, Long warehouseId);

    Map<String, List<String>> autoAllocate(Long id, List<Long> detailIds) throws Exception;

    Map<String, List<String>> autoAllocate(Long doId) throws Exception;

}
