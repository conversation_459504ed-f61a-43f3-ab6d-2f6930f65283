package com.accesscorporate.app.wms.server.biz.params.request.rule;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version JDK 17
 * @date 2025/2/26
 * @description
 */
@Data
@Accessors(chain = true)
public class RulAllocationRequest {

    /**
     * id
     */
    private Long id;

    /**
     * 规则编码
     */
    private String allocationCode;

    /**
     * 规则描述
     */
    private String allocationDescr;

    /**
     * 是否激活0 未激活 1 激活
     */
    private String activeFlag;
}
