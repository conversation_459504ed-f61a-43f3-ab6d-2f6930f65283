package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.request.AddressFilter;
import com.accesscorporate.app.wms.server.biz.service.dto.AddressDTO;
import com.accesscorporate.app.wms.server.biz.service.dto.AddressInfoDTO;
import com.accesscorporate.app.wms.server.dal.entity.AddressDO;
import com.idanchuang.component.base.page.PageData;

import java.util.List;

/**
 * 地址服务接口（替代原AddressService）
 */
public interface AddressService {

    /**
     * 分页查询地址信息（原queryAddressInfo逻辑）
     */
    PageData<AddressInfoDTO> queryAddressInfo(AddressFilter filter);

    /**
     * 根据ID获取地址（原getById逻辑）
     */
    AddressInfoDTO getById(Long id);

    /**
     * 获取所有国家（原getAllCountry逻辑）
     */
    List<AddressDTO> getAllCountry();

    List<AddressDTO> getProvinceList(String countryCode);

    /**
     * 根据父编码查询地址列表（原findAddressListByParentCode逻辑）
     */
    List<AddressDTO> findAddressListByParentCode(String parentCode);

    /**
     * 保存或更新地址（原save/update逻辑）
     */
    boolean saveOrUpdate(AddressDTO address);

    /**
     * 删除地址（原deleteById逻辑）
     */
    boolean deleteById(Long addressId);

    boolean recoverById(Long addressId);
    /**
     * 添加地址
     *
     * @param countryCode    国家id
     * @param provinceCode   省id
     * @param transportWendy 运输温度
     */
    void save(String countryCode, String provinceCode, Integer transportWendy);

}
