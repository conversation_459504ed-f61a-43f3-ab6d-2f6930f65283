package com.accesscorporate.app.wms.server.biz.params.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 发货单头表保存请求体
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "发货单头表保存请求体")
public class DocDoHeaderSaveRequest {

    @Schema(description = "发货单号")
    @NotBlank(message = "发货单号不能为空")
    private String doNo;

    @Schema(description = "订单状态")
    private String status;

    @Schema(description = "零散状态")
    private String pcsStatus;

    @Schema(description = "整件状态")
    private String unitStatus;

    @Schema(description = "发运时间")
    private LocalDateTime shipTime;

    @Schema(description = "订单类型")
    private String doType;

    @Schema(description = "订单冻结标识")
    private String releaseStatus;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "参考编号1")
    private String refNo1;

    @Schema(description = "参考编号2")
    private String refNo2;

    @Schema(description = "邮编")
    private String postCode;

    @Schema(description = "半日达普通/半日达大件/普通/普通大件/团购/一日三送/自提")
    private Integer specFlag;

    @Schema(description = "有无发票")
    private Integer invoiceFlag;

    @Schema(description = "订货数量")
    private BigDecimal expectedQty;

    @Schema(description = "发货数量")
    private BigDecimal shipQty;

    @Schema(description = "订货数量(每)")
    private BigDecimal expectedQtyEach;

    @Schema(description = "发货数量(每)")
    private BigDecimal shipQtyEach;

    @Schema(description = "毛重")
    private BigDecimal grossWt;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "省")
    private Integer province;

    @Schema(description = "市")
    private Integer city;

    @Schema(description = "县")
    private Integer county;

    @Schema(description = "区")
    private Integer disctrict;

    @Schema(description = "收货方")
    private String consigneeName;

    @Schema(description = "收货地址")
    private String address;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "电话")
    private String telephone;

    @Schema(description = "手机")
    private String mobile;

    @Schema(description = "自动标识")
    private Integer autoFlag;

    @Schema(description = "邮件")
    private String email;

    @Schema(description = "配送公司ID")
    private Long carrierId;

    @Schema(description = "运单号")
    private String trackingNo;

    @Schema(description = "发票数量")
    private Integer invoiceQty;

    @Schema(description = "支付方式")
    private String paymentMethodName;

    @Schema(description = "支付类型")
    private Integer paymentType;

    @Schema(description = "装箱数量")
    private BigDecimal packedQty;

    @Schema(description = "DO创建时间")
    private LocalDateTime doCreateTime;

    @Schema(description = "期望配送时间1")
    private LocalDateTime expectedArriveTime1;

    @Schema(description = "期望配送时间2")
    private LocalDateTime expectedArriveTime2;

    @Schema(description = "拣货开始时间")
    private LocalDateTime pkTimeStart;

    @Schema(description = "拣货完成时间")
    private LocalDateTime pkTimeEnd;

    @Schema(description = "包装开始时间")
    private LocalDateTime packTimeStart;

    @Schema(description = "包装完成时间")
    private LocalDateTime packTimeEnd;

    @Schema(description = "分拣结束时间")
    private LocalDateTime sortTime;

    @Schema(description = "分配时间")
    private LocalDateTime allocTime;

    @Schema(description = "取消时间")
    private LocalDateTime cancelTime;

    @Schema(description = "是否已跑波次")
    private Integer waveFlag;

    @Schema(description = "波次ID")
    private Long waveId;

    @Schema(description = "分拣格ID")
    private Integer sortGridNo;

    @Schema(description = "冻结人")
    private String holdWho;

    @Schema(description = "冻结时间")
    private LocalDateTime holdTime;

    @Schema(description = "冻结原因代码")
    private String holdCode;

    @Schema(description = "冻结原因")
    private String holdReason;

    @Schema(description = "用户自定义1")
    private String userdefine1;

    @Schema(description = "用户自定义2")
    private String userdefine2;

    @Schema(description = "用户自定义3")
    private String userdefine3;

    @Schema(description = "用户自定义4")
    private String userdefine4;

    @Schema(description = "备注")
    private String notes;

    @Schema(description = "EDI_1")
    private String edi1;

    @Schema(description = "EDI_2")
    private String edi2;

    @Schema(description = "是否需要取消")
    private Integer needCancel;

    @Schema(description = "期望收货时间")
    private String expectedReceiveTime;

    @Schema(description = "是否半日达")
    private Integer isHalfDayDelivery;

    @Schema(description = "发货单打印份数")
    private Integer printNum;

    @Schema(description = "订单总额")
    private BigDecimal orderAmount;

    @Schema(description = "货品总额")
    private BigDecimal productAmount;

    @Schema(description = "已收款")
    private BigDecimal accountPayable;

    @Schema(description = "总返利金额")
    private BigDecimal orderFrostRebate;

    @Schema(description = "运费")
    private BigDecimal orderDeliveryFee;

    @Schema(description = "是否要代收货款")
    private Integer thirdPartyBill;

    @Schema(description = "应收款")
    private BigDecimal receivable;

    @Schema(description = "换货标识")
    private Integer exchangeFlag;

    @Schema(description = "配送站ID")
    private Long stationId;

    @Schema(description = "订单同步标记（Backend）")
    private Integer ediSendFlag1;

    @Schema(description = "订单同步标记（TMS）")
    private Integer ediSendFlag2;

    @Schema(description = "订单同步标记（BACKEND UNLOCK）")
    private Integer ediSendFlag3;

    @Schema(description = "原始ID")
    private String origId;

    @Schema(description = "支付方式")
    private String paymentMethod;

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "临时纸箱标识")
    private Integer isTempCarton;

    @Schema(description = "补货状态")
    private Integer replStatus;

    @Schema(description = "称重毛重")
    private BigDecimal totalGrossWt;

    @Schema(description = "体积")
    private BigDecimal volume;

    @Schema(description = "商品等级")
    private Integer cycleClass;

    @Schema(description = "用户自定义5")
    private String userdefine5;

    @Schema(description = "用户自定义6")
    private String userdefine6;

    @Schema(description = "订单来源（旧）")
    private Integer orderSourceOld;

    @Schema(description = "异常状态")
    private Integer exceptionStatus;

    @Schema(description = "打印时是否显示金额")
    private Integer displayPrice;

    @Schema(description = "流程引擎")
    private Integer flowFlag;

    @Schema(description = "服务类型")
    private Integer serviceType;

    @Schema(description = "销售商家")
    private String lastDcName;

    @Schema(description = "调拨类型")
    private Integer tranType;

    @Schema(description = "是否贵重")
    private Integer isValuable;

    @Schema(description = "初始冻结原因代码")
    private String firstHoldCode;

    @Schema(description = "订单缺货状态")
    private String lackStatus;

    @Schema(description = "分拣柜")
    private Long sortingBinId;

    @Schema(description = "缺货暂存位号")
    private String doLackLocationCode;

    @Schema(description = "生鲜标识")
    private Integer isFresh;

    @Schema(description = "首次预计出库时间")
    private LocalDateTime doFinishTime;

    @Schema(description = "是否包含酒类随附单")
    private Integer inWine;

    @Schema(description = "酒类随附单单据号")
    private String wineNo;

    @Schema(description = "DO通道集")
    private String aisles;

    @Schema(description = "预计出库时间")
    private LocalDateTime planShipTime;

    @Schema(description = "预计出库时间止")
    private LocalDateTime planShipTimeEnd;

    @Schema(description = "标准预计出库时间")
    private LocalDateTime planShipTimeStd;

    @Schema(description = "订单来源系统")
    private String sourceSystem;

    @Schema(description = "登记标识")
    private Integer checkFlag;

    @Schema(description = "最近一次核检操作人")
    private String packedBy;

    @Schema(description = "最近一次分拣操作人")
    private String sortedBy;

    @Schema(description = "分拣开始时间")
    private LocalDateTime sortStartTime;

    @Schema(description = "预计发车时间")
    private String departureTime;

    @Schema(description = "打印代码")
    private String printCode;

    @Schema(description = "是否自动波次标识")
    private Integer isAutoWave;

    @Schema(description = "团购订单标识")
    private Integer isGroup;

    @Schema(description = "配送属性")
    private String deleveryFeature;

    @Schema(description = "优惠金额")
    private BigDecimal discountAmount;

    @Schema(description = "补货开始时间")
    private LocalDateTime replStartTime;

    @Schema(description = "补货完成时间")
    private LocalDateTime replEndTime;

    @Schema(description = "3PL站点名称")
    private String stationName;

    @Schema(description = "是否有处方药")
    private Integer haveCfy;

    @Schema(description = "核拣方式")
    private Integer recheckType;

    @Schema(description = "原始ID_HC")
    private Long origIdHc;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "订单来源")
    private Integer orderSource;

    @Schema(description = "原始SO编码")
    private String originalSoCode;

    @Schema(description = "买家备注")
    private String buyerRemark;

    @Schema(description = "卖家备注")
    private String sellerRemark;

    @Schema(description = "平台备注")
    private String platformRemark;

    @Schema(description = "体积类型")
    private Integer volumeType;

    @Schema(description = "企业客户ID")
    private Long businessCustomerId;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "区县名称")
    private String countyName;

    @Schema(description = "省份名称")
    private String provinceName;

    @Schema(description = "支付时间")
    private LocalDateTime payTime;

    @Schema(description = "失败次数")
    private Integer failedNumber;

    @Schema(description = "装载确认时间")
    private LocalDateTime loadConfimTime;

    @Schema(description = "最后称重时间")
    private LocalDateTime lastWeighTime;

    @Schema(description = "发货审核状态")
    private Integer orderCheckState;

    @Schema(description = "订单子类型")
    private String orderSubType;

    @Schema(description = "忽略失效日期")
    private Integer ignoreExpiryDate;

    @Schema(description = "配送服务费")
    private BigDecimal deliveryServiceFee;

    @Schema(description = "是否直接出库")
    private Integer isDirect;

    @Schema(description = "失败计数")
    private Integer failedCount;

    @Schema(description = "失败类型")
    private Integer failedType;

    @Schema(description = "是否越库")
    private Integer needCrossstock;

    @Schema(description = "ERP系统原入库单ID")
    private Long sourceAsnId;

    @Schema(description = "紧急标记")
    private Integer emergencyFlag;

    @Schema(description = "商家ID")
    private Long merchantId;

    @Schema(description = "打印标记")
    private Integer printFlag;

    @Schema(description = "是否允许缺发")
    private Integer lackShipFlag;

    @Schema(description = "运输温度")
    private Integer transportWendy;

    @Schema(description = "相似度明文")
    private String similarity;

    @Schema(description = "相似度密文")
    private String similaritySign;

    @Schema(description = "店铺编码")
    private String storeCode;

    @Schema(description = "称重标识")
    private Integer weightFlag;

    @Schema(description = "渠道编码")
    private String channelCode;
}
