package com.accesscorporate.app.wms.server.biz.converter;

import com.accesscorporate.app.wms.server.biz.params.response.CfgCodeMasterResponse;
import com.accesscorporate.app.wms.server.dal.entity.CfgCodeMasterDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CfgCodeMasterMapStruct {

    CfgCodeMasterMapStruct INSTANCE = Mappers.getMapper(CfgCodeMasterMapStruct.class);

    CfgCodeMasterResponse convertToResponse(CfgCodeMasterDO item);
}
