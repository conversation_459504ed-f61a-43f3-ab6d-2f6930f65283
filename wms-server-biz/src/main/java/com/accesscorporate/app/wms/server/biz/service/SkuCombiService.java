package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.request.SkuCombiAddRequest;
import com.accesscorporate.app.wms.server.biz.params.request.SkuCombiListRequest;
import com.accesscorporate.app.wms.server.biz.params.response.SkuCombiListResponse;
import com.accesscorporate.app.wms.server.biz.params.response.SkuCombiViewResponse;
import com.idanchuang.component.base.page.PageData;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> liudongliang
 * @project : wms-server
 * @company : 浙江单创品牌管理有限公司
 * @desc :
 * Copyright @ 2024 danchuang Technology Co.Ltd. – Confidential and Proprietary
 */
public interface SkuCombiService {

    PageData<SkuCombiListResponse> list(SkuCombiListRequest request);

    Boolean add(SkuCombiAddRequest request);

    SkuCombiViewResponse view(Long id);

    Boolean delete(Long id);

    void importExcel(MultipartFile file);
}
