package com.accesscorporate.app.wms.server.biz.service.rule;

import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulAllocationResponse;
import com.idanchuang.component.base.page.PageData;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
public interface IRulAllocationService {

    /**
     * 查询分配规则列表
     */
    PageData<RulAllocationResponse> queryPage(RulAllocationQueryPageRequest request);

    /**
     * 通过id查询分配规则
     */
    RulAllocationResponse queryById(Long id);


    /**
     * 分配规则
     *
     * @param request
     * @return
     */
    Boolean save(RulAllocationRequest request);


    /**
     * 分配规则删除
     *
     * @param id
     * @return
     */
    Boolean deleteById(Long id);

}
