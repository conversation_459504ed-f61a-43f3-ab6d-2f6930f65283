package com.accesscorporate.app.wms.server.biz.params.response;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> liu<PERSON>liang
 * @project : wms-server
 * @company : 浙江单创品牌管理有限公司
 * @desc :
 * Copyright @ 2024 danchuang Technology Co.Ltd. – Confidential and Proprietary
 */
@Data
public class SkuCombiViewResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 4871953015304384149L;

    private Long id;

    private String combiBarcode;

    private String locCode;

    private List<SkuCombiItem> items;

    @Data
    public static class SkuCombiItem implements Serializable {

        @Serial
        private static final long serialVersionUID = -7028637946732612641L;

        private String barcode;

        private String goodsName;

        private Integer quantity;

        private String goodsGrade;

        private String goodsGradeDesc;
    }

}
