package com.accesscorporate.app.wms.server.biz.params.response.pickloc;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-20 16:29
 * Description:商品捡货位查询响应参数
 */
@Tag(name = "商品包装列表分页查询响应参数")
@Data
public class PickLocPageQueryResponse {

    @Schema(title = "商品捡货位记录行主键id")
    private Long pickLocId;

    @Schema(title = "补货单位")
    private String uom;

    @Schema(title = "商品名称，国外仓展示英文名称，国内仓")
    private String productCnameForI18n;

    @Schema(title = "商品编码")
    private String productCode;

    @Schema(title = "商品条码")
    private String ean13;

    @Schema(title = "库位编码")
    private String locCode;

    @Schema(title = "库区")
    private String partionCode;

    @Schema(title = "库位类型")
    private String locTypeDesc;

    @Schema(title = "下架方式")
    private String packageTypeDesc;

    @Schema(title = "补货上限")
    private String upLimit;

    @Schema(title = "补货下限")
    private String lowerLimit;

    @Schema(title = "最小补货量")
    private String minimumRplQty;

}
