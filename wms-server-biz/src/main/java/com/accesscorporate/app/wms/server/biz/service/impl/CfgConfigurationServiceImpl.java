package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.manager.CfgConfigurationManager;
import com.accesscorporate.app.wms.server.biz.service.ICfgConfigurationService;
import com.accesscorporate.app.wms.server.dal.entity.CfgConfigurationDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 系统参数配置服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CfgConfigurationServiceImpl implements ICfgConfigurationService {

    private final CfgConfigurationManager cfgConfigurationManager;

    @Override
    public Page<CfgConfigurationDO> queryPage(String configType, String configNo, String descrC,
                                               Integer configLevel, Long warehouseId, Long tenantId,
                                               Long current, Long size) {
        return cfgConfigurationManager.selectPage(configType, configNo, descrC, 
                                                   configLevel, warehouseId, tenantId, 
                                                   current, size);
    }

    @Override
    public CfgConfigurationDO queryById(Long id) {
        return cfgConfigurationManager.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createConfig(CfgConfigurationDO config) {
        // 验证配置编号是否已存在
        if (cfgConfigurationManager.existsByConfigNoAndWarehouseId(
                config.getConfigNo(), config.getWarehouseId(), null)) {
            throw new RuntimeException("配置编号已存在: " + config.getConfigNo());
        }
        
        return cfgConfigurationManager.createConfig(config);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateConfig(CfgConfigurationDO config) {
        // 验证配置编号是否已存在（排除当前记录）
        if (cfgConfigurationManager.existsByConfigNoAndWarehouseId(
                config.getConfigNo(), config.getWarehouseId(), config.getId())) {
            throw new RuntimeException("配置编号已存在: " + config.getConfigNo());
        }
        
        return cfgConfigurationManager.updateConfig(config);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteConfig(Long id) {
        return cfgConfigurationManager.deleteConfig(id);
    }

    @Override
    public CfgConfigurationDO getConfig(String configNo, Long warehouseId, Long tenantId) {
        return cfgConfigurationManager.getConfigWithPriority(configNo, warehouseId, tenantId);
    }

    @Override
    public String getStringValue(String configNo, Long warehouseId, Long tenantId) {
        return getStringValue(configNo, warehouseId, tenantId, null);
    }

    @Override
    public String getStringValue(String configNo, Long warehouseId, Long tenantId, String defaultValue) {
        CfgConfigurationDO config = getConfig(configNo, warehouseId, tenantId);
        if (config != null && config.getId() != null) {
            return StringUtils.isNotBlank(config.getValueString()) ? config.getValueString() : defaultValue;
        }
        return defaultValue;
    }

    @Override
    public Integer getIntValue(String configNo, Long warehouseId, Long tenantId) {
        return getIntValue(configNo, warehouseId, tenantId, null);
    }

    @Override
    public Integer getIntValue(String configNo, Long warehouseId, Long tenantId, Integer defaultValue) {
        CfgConfigurationDO config = getConfig(configNo, warehouseId, tenantId);
        if (config != null && config.getId() != null) {
            return config.getValueInt() != null ? config.getValueInt() : defaultValue;
        }
        return defaultValue;
    }

    @Override
    public Boolean getBooleanValue(String configNo, Long warehouseId, Long tenantId) {
        return getBooleanValue(configNo, warehouseId, tenantId, null);
    }

    @Override
    public Boolean getBooleanValue(String configNo, Long warehouseId, Long tenantId, Boolean defaultValue) {
        CfgConfigurationDO config = getConfig(configNo, warehouseId, tenantId);
        if (config != null && config.getId() != null && StringUtils.isNotBlank(config.getValueString())) {
            String value = config.getValueString().toUpperCase();
            return "Y".equals(value) || "1".equals(value) || "TRUE".equals(value);
        }
        return defaultValue;
    }

    @Override
    public List<CfgConfigurationDO> listByConfigType(String configType, Long warehouseId, Long tenantId) {
        return cfgConfigurationManager.listByConfigType(configType, warehouseId, tenantId);
    }

    @Override
    public List<CfgConfigurationDO> listByConfigLevel(Integer configLevel, Long warehouseId, Long tenantId) {
        return cfgConfigurationManager.listByConfigLevel(configLevel, warehouseId, tenantId);
    }

    @Override
    public List<CfgConfigurationDO> listByLevelOne(String levelOne, Long warehouseId, Long tenantId) {
        return cfgConfigurationManager.listByLevelOne(levelOne, warehouseId, tenantId);
    }

    @Override
    public List<CfgConfigurationDO> listByLevelTwo(String levelTwo, Long warehouseId, Long tenantId) {
        return cfgConfigurationManager.listByLevelTwo(levelTwo, warehouseId, tenantId);
    }

    @Override
    public List<CfgConfigurationDO> listByConfigTags(String configTag1, String configTag2, 
                                                      Long warehouseId, Long tenantId) {
        return cfgConfigurationManager.listByConfigTags(configTag1, configTag2, warehouseId, tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchCreateConfig(List<CfgConfigurationDO> configList) {
        if (configList == null || configList.isEmpty()) {
            return true;
        }
        
        // 验证配置编号是否重复
        for (CfgConfigurationDO config : configList) {
            if (cfgConfigurationManager.existsByConfigNoAndWarehouseId(
                    config.getConfigNo(), config.getWarehouseId(), null)) {
                throw new RuntimeException("配置编号已存在: " + config.getConfigNo());
            }
        }
        
        int insertCount = cfgConfigurationManager.batchInsert(configList);
        return insertCount == configList.size();
    }

    @Override
    public Boolean existsByConfigNoAndWarehouseId(String configNo, Long warehouseId, Long excludeId) {
        return cfgConfigurationManager.existsByConfigNoAndWarehouseId(configNo, warehouseId, excludeId);
    }
}
