package com.accesscorporate.app.wms.server.biz.params.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 发货单明细表响应体
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "发货单明细表响应体")
public class DocDoDetailResponse {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "订单头ID")
    private Long doHeaderId;

    @Schema(description = "明细状态")
    private String linestatus;

    @Schema(description = "商品ID")
    private Long skuId;

    @Schema(description = "期望发货数量")
    private BigDecimal expectedQty;

    @Schema(description = "期望发货数量EACH")
    private BigDecimal expectedQtyEach;

    @Schema(description = "包装ID")
    private Long packageId;

    @Schema(description = "包装明细ID")
    private Long packDetailId;

    @Schema(description = "单位")
    private String uom;

    @Schema(description = "生产日期")
    private String lotatt01;

    @Schema(description = "失效日期")
    private String lotatt02;

    @Schema(description = "入库日期")
    private String lotatt03;

    @Schema(description = "供应商ID")
    private String lotatt04;

    @Schema(description = "批号")
    private String lotatt05;

    @Schema(description = "货主ID（商家）")
    private String lotatt06;

    @Schema(description = "批次包装")
    private String lotatt07;

    @Schema(description = "制造商")
    private String lotatt08;

    @Schema(description = "单价")
    private String lotatt09;

    @Schema(description = "po号")
    private String lotatt10;

    @Schema(description = "批次属性11")
    private String lotatt11;

    @Schema(description = "坏品原因")
    private String lotatt12;

    @Schema(description = "容器号")
    private String lotatt13;

    @Schema(description = "容器类型")
    private String lotatt14;

    @Schema(description = "批次属性15")
    private String lotatt15;

    @Schema(description = "批次属性16")
    private String lotatt16;

    @Schema(description = "批次属性17")
    private String lotatt17;

    @Schema(description = "分配规则ID")
    private Long allocationRule;

    @Schema(description = "体积")
    private BigDecimal volume;

    @Schema(description = "净重")
    private BigDecimal netweight;

    @Schema(description = "毛重")
    private BigDecimal grossweight;

    @Schema(description = "拣货区")
    private String pickzone;

    @Schema(description = "商品价值")
    private BigDecimal price;

    @Schema(description = "分配数量")
    private BigDecimal allocatedQty;

    @Schema(description = "分配数量EACH")
    private BigDecimal allocatedQtyEach;

    @Schema(description = "拣货数量")
    private BigDecimal pickedQty;

    @Schema(description = "原始拣货数量")
    private BigDecimal pickQty;

    @Schema(description = "拣货箱数量")
    private BigDecimal pickedQtyUnit;

    @Schema(description = "拣货数量EACH")
    private BigDecimal pickedQtyEach;

    @Schema(description = "分拣数量")
    private BigDecimal sortedQty;

    @Schema(description = "分拣包装数量")
    private BigDecimal sortedQtyUnit;

    @Schema(description = "核检数量")
    private BigDecimal packedQty;

    @Schema(description = "分拣数量EACH")
    private BigDecimal sortedQtyEach;

    @Schema(description = "核检数量EACH")
    private BigDecimal packedQtyEach;

    @Schema(description = "发货数量")
    private BigDecimal shippedQty;

    @Schema(description = "发货数量EACH")
    private BigDecimal shippedQtyEach;

    @Schema(description = "用户自定义1")
    private String userdefine1;

    @Schema(description = "用户自定义2")
    private String userdefine2;

    @Schema(description = "用户自定义3")
    private String userdefine3;

    @Schema(description = "用户自定义4")
    private String userdefine4;

    @Schema(description = "备注")
    private String notes;

    @Schema(description = "是否是促销产品")
    private Integer isPromote;

    @Schema(description = "本项商品返利金额")
    private BigDecimal orderItemRebate;

    @Schema(description = "出库单价")
    private BigDecimal retailPrice;

    @Schema(description = "父ID")
    private String parentId;

    @Schema(description = "是否是叶子结点")
    private Long isDoLeaf;

    @Schema(description = "D_EDI_1")
    private String dEdi1;

    @Schema(description = "D_EDI_2")
    private String dEdi2;

    @Schema(description = "原始头ID")
    private String origHeaderId;

    @Schema(description = "原始明细ID")
    private String origDetailId;

    @Schema(description = "需补货数量")
    private BigDecimal needReplQty;

    @Schema(description = "是否坏品")
    private Integer isDamaged;

    @Schema(description = "是否贵重品")
    private Integer isValueables;

    @Schema(description = "仓库ID")
    private Long warehouseId;

    @Schema(description = "是否为酒类随附单")
    private Integer wineFlag;

    @Schema(description = "商品行邮税总价")
    private BigDecimal cargoTotalPrice;

    @Schema(description = "批次编码")
    private String lotNo;

    @Schema(description = "商品总价（旧字段）")
    private BigDecimal cargoTotalPriceOld;

    @Schema(description = "药网药品监管码是否需要扫码")
    private Integer isValidationRequired;

    @Schema(description = "是否有处方药")
    private Integer haveCfy;

    @Schema(description = "处方药处理标识")
    private Integer cfyHandleFlag;

    @Schema(description = "最低有效天数")
    private Integer leastAvailableDay;

    @Schema(description = "核拣装箱弹出提示")
    private String recheckMessage;

    @Schema(description = "销售等级")
    private Integer salesGrade;

    @Schema(description = "货品等级")
    private Integer goodsGrade;

    @Schema(description = "最小失效日期")
    private LocalDate minExp;

    @Schema(description = "最大失效日期")
    private LocalDate maxExp;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "版本号")
    private Integer version;
}
