package com.accesscorporate.app.wms.server.biz.converter;

import com.accesscorporate.app.wms.server.biz.params.request.DocDoHeaderSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocDoHeaderResponse;
import com.accesscorporate.app.wms.server.dal.entity.DocDoHeader;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 发货单头表转换器
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Mapper
public interface DocDoHeaderMapStruct {

    DocDoHeaderMapStruct INSTANCE = Mappers.getMapper(DocDoHeaderMapStruct.class);

    /**
     * 实体转响应体
     */
    DocDoHeaderResponse convertToResponse(DocDoHeader docDoHeader);

    /**
     * 响应体转实体
     */
    @Mapping(target = "isDeleted", ignore = true)
    DocDoHeader convertToEntity(DocDoHeaderResponse response);

    /**
     * 保存请求转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "warehouseId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "updateBy", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    DocDoHeader convertToEntity(DocDoHeaderSaveRequest saveRequest);
}
