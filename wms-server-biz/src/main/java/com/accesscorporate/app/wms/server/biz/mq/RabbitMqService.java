package com.accesscorporate.app.wms.server.biz.mq;

/**
 * @<PERSON><PERSON>
 * @Date 2025/3/25 10:45 上午
 */
public interface RabbitMqService {
    void send(String exchange, String message, String messageId, Integer delay);

    void send(String exchange, String message, String messageId);

    void send(String exchange, String message, Long deliveryTag);

    void send(String exchange, String message);

    void sendPersistence(String exchange, String message);
}
