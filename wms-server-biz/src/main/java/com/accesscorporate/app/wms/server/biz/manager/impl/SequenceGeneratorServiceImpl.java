package com.accesscorporate.app.wms.server.biz.manager.impl;

import com.accesscorporate.app.wms.server.biz.manager.SequenceGenerator;
import com.accesscorporate.app.wms.server.biz.manager.SequenceGeneratorService;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import com.accesscorporate.app.wms.server.dal.entity.SequenceRule;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.remoting.rmi.RmiProxyFactoryBean;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/6 下午1:11 星期四
 */
@Service
@Slf4j
public class SequenceGeneratorServiceImpl implements SequenceGeneratorService {


    @Resource
    private RmiProxyFactoryBean rmiProxyFactoryBean;


    private SequenceGenerator sequenceGenerator;

    @PostConstruct
    public void initialize() {
        try {
            sequenceGenerator = (SequenceGenerator) rmiProxyFactoryBean.getObject();
        } catch (Exception e) {
            log.error("Error while initialize.", e);
        }
    }

    /**
     * @param sequenceName 序列号规则名称
     * @param warehouseId  仓库ID
     * @return
     */
    public String generateSequenceNo(String sequenceName, Long warehouseId) {
        return getSequenceGenerator().generateSequenceNo(sequenceName, warehouseId, UserContextAssistant.getTenantId());
    }

    public List<String> generateSequenceNo(String sequenceName, Integer seqNum, Long warehouseId) {
        return getSequenceGenerator().generateSequenceNo(sequenceName, seqNum, warehouseId, UserContextAssistant.getTenantId());
    }

    public Long generateSequenceId(String sequenceName) {
        return Long.valueOf(getSequenceGenerator().generateSequenceNo(sequenceName, null, UserContextAssistant.getTenantId()));
    }


    public List<String> generateSequenceId(String sequenceName, int size) {
        return getSequenceGenerator().generateSequenceNo(sequenceName, size, null, UserContextAssistant.getTenantId());
    }


    public SequenceGenerator getSequenceGenerator() {
        if (sequenceGenerator == null) {
            synchronized (SequenceGenerator.class) {
                if (sequenceGenerator == null) {
                    initialize();
                }
            }
        }
        return sequenceGenerator;
    }

    public void reconnectGenerator() {
        sequenceGenerator = null;
    }

    public void clearCache() {
        if (sequenceGenerator == null) {
            initialize();
        }
        sequenceGenerator.clearCache(UserContextAssistant.getTenantId());
    }

    public SequenceRule getRuleCache(String sequenceName, Long warehouseId) {
        if (sequenceGenerator == null) {
            initialize();
        }
        return sequenceGenerator.getRuleCahce(sequenceName, warehouseId, UserContextAssistant.getTenantId());
    }

    public void updateCache(SequenceRule rule, Long warehouseId) {
        SequenceRule cache = getRuleCache(rule.getRuleCode(), warehouseId);
//		cache.setSerialNo(rule.getSerialNo());
        sequenceGenerator.updateRuleCache(cache, warehouseId, UserContextAssistant.getTenantId());

    }
}
