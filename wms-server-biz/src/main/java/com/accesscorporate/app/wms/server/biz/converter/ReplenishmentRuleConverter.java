package com.accesscorporate.app.wms.server.biz.converter;

import cn.hutool.core.util.StrUtil;
import com.accesscorporate.app.wms.server.biz.params.request.ReplenishmentRuleCreateRequest;
import com.accesscorporate.app.wms.server.biz.params.request.ReplenishmentRuleDetailAddRequest;
import com.accesscorporate.app.wms.server.biz.params.request.ReplenishmentRuleModifyRequest;
import com.accesscorporate.app.wms.server.biz.params.response.ReplenishmentRuleDetailResponse;
import com.accesscorporate.app.wms.server.biz.params.response.ReplenishmentRuleResponse;
import com.accesscorporate.app.wms.server.common.enums.WhetherEnum;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import com.accesscorporate.app.wms.server.dal.entity.RotationRuleDetailDO;
import com.accesscorporate.app.wms.server.dal.entity.RotationRuleHeaderDO;

/**
 * 补货规则-转换器
 *
 * <AUTHOR>
 * 2025/2/19  13:52
 */
public class ReplenishmentRuleConverter {


    public static ReplenishmentRuleResponse toReplenishmentRuleResponse(RotationRuleHeaderDO entity) {
        return new ReplenishmentRuleResponse()
                .setId(entity.getId())
                .setRuleCode(entity.getRotationCode())
                .setRuleDesc(entity.getRotationDesc())
                .setOrderType(entity.getOrderType())
                .setPartitionCtrl(entity.getPartitionCtrl())
//                .setPartitionCtrlStr(entity.getPartitionCtrl() != null ? WhetherEnum.getDescByCode(entity.getPartitionCtrl()): WhetherEnum.NO.getDesc())
                .setPartitionCtrlStr(WhetherEnum.getDescByCode(entity.getPartitionCtrl()))
                .setIsExpirationDateControl(entity.getExpireControl())
                .setIsExpirationDateControlStr(WhetherEnum.getDescByCode(entity.getExpireControl()))
                .setIsActivation(StrUtil.isNotBlank(entity.getActiveFlag()) ? Integer.parseInt(entity.getActiveFlag()) : null)
                .setIsActivationStr(StrUtil.isNotBlank(entity.getActiveFlag()) ? WhetherEnum.getDescByCode(Integer.parseInt(entity.getActiveFlag())) : null);
    }


    public static RotationRuleHeaderDO toRotationRuleHeaderDO(ReplenishmentRuleCreateRequest createRequest) {
        return new RotationRuleHeaderDO()
                .setRotationCode(createRequest.getRuleCode())
                .setRotationDesc(createRequest.getRuleDesc())
                .setOrderType(createRequest.getOrderType())
                .setPartitionCtrl(createRequest.getPartitionCtrl())
                .setExpireControl(createRequest.getIsExpirationDateControl())
                .setActiveFlag(createRequest.getIsActivation() != null ? createRequest.getIsActivation().toString() : "0")
                .setCreateBy(UserContextAssistant.getAccount())
                .setUpdateBy(UserContextAssistant.getAccount())
                .setTenantId(UserContextAssistant.getTenantId());
    }

    public static RotationRuleHeaderDO toRotationRuleHeaderDO(ReplenishmentRuleModifyRequest modifyRequest) {
        return new RotationRuleHeaderDO()
                .setRotationCode(modifyRequest.getRuleCode())
                .setRotationDesc(modifyRequest.getRuleDesc())
                .setOrderType(modifyRequest.getOrderType())
                .setPartitionCtrl(modifyRequest.getPartitionCtrl())
                .setExpireControl(modifyRequest.getIsExpirationDateControl())
                .setActiveFlag(modifyRequest.getIsActivation() != null ? modifyRequest.getIsActivation().toString() : "0")
                .setUpdateBy(UserContextAssistant.getAccount())
                .setTenantId(UserContextAssistant.getTenantId());
    }


    public static ReplenishmentRuleDetailResponse toReplenishmentRuleDetailResponse(RotationRuleDetailDO rotationRuleDetailDO){
        return new ReplenishmentRuleDetailResponse()
                .setId(rotationRuleDetailDO.getId())
                .setBatchAttr(rotationRuleDetailDO.getLotAttName())
                .setSortBy(rotationRuleDetailDO.getSortby());
    }


    public static RotationRuleDetailDO toRotationRuleDetailDO(ReplenishmentRuleDetailAddRequest request){
        return new RotationRuleDetailDO()
                .setRHId(request.getRuleId())
                .setLotAttName(request.getBatchAttr())
                .setSortby(request.getSortBy())
                .setCreateBy(UserContextAssistant.getAccount())
                .setUpdateBy(UserContextAssistant.getAccount())
                .setTenantId(UserContextAssistant.getTenantId());
    }


}
