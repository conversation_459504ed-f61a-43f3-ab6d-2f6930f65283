package com.accesscorporate.app.wms.server.biz.manager;

import com.accesscorporate.app.wms.server.dal.entity.WarehouseDO;
import com.accesscorporate.app.wms.server.dal.mapper.IWarehouseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 仓--Manager
 *
 * <AUTHOR>
 * 2025/2/7  17:36
 */
@Component
@RequiredArgsConstructor
public class WarehouseManager extends ServiceImpl<IWarehouseMapper, WarehouseDO> {

    public List<WarehouseDO> queryAllWarehouseList() {
        return lambdaQuery()
                .eq(WarehouseDO::getIsDeleted, 0)
                .list();

    }

    public List<WarehouseDO> queryWhByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .eq(WarehouseDO::getIsDeleted, 0)
                .in(WarehouseDO::getWarehouseCode, codes)
                .list();
    }

}
