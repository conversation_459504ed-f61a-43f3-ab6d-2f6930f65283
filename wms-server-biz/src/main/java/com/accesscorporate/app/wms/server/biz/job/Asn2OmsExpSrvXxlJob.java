package com.accesscorporate.app.wms.server.biz.job;


import com.accesscorporate.app.wms.server.biz.service.Recheck2OmsExpService;
import com.accesscorporate.app.wms.server.biz.service.SExpSrvLogService;
import com.accesscorporate.app.wms.server.biz.service.SExpSrvMsgService;
import com.accesscorporate.app.wms.server.common.enums.MsgTypeEnum;
import com.accesscorporate.app.wms.server.dal.entity.SExpSrvLogDO;
import com.accesscorporate.app.wms.server.dal.entity.SExpSrvMsgDO;
import com.idanchuang.component.base.exception.core.ExFactory;
import com.idanchuang.component.base.exception.exception.BusinessException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@JobHandler(value = "asn2OmsExpSrvXxlJob")
@Slf4j
public class Asn2OmsExpSrvXxlJob extends IJobHandler {

    @Resource
    private SExpSrvMsgService sExpSrvMsgService;
    @Resource
    private SExpSrvLogService sExpSrvLogService;
    @Resource
    private Recheck2OmsExpService recheck2OmsExpService;


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("Asn2OmsExpSrvXxlJob execute");
        try {
            List<Long> typeList = List.of(1L, 2L, 3L);
            List<SExpSrvMsgDO> dataList = sExpSrvMsgService.queryByTypeAndInvokeCount(typeList, 0L, 100);
            for (SExpSrvMsgDO data : dataList) {
                SExpSrvLogDO serviceLog = null;
                if (null != data.getSrvLogId()) {
                    serviceLog = sExpSrvLogService.getById(data.getSrvLogId());
                }
                try {
                    this.dealExpSrvMsg(data, serviceLog);
                } catch (Exception e) {
                    log.error("Error while run.", e);
                }
            }
        } catch (Exception e) {
            log.error("Error while run.", e);
            ExFactory.throwBusiness("asn2OmsExpSrvXxlJob任务执行异常", e);
        }
        return SUCCESS;
    }

    private void dealExpSrvMsg(SExpSrvMsgDO data, SExpSrvLogDO serviceLog) {
        if (MsgTypeEnum.RECHECK_OMS.getValue().equals(data.getType())) {
            recheck2OmsExpService.msgJobSend(data, serviceLog);
        } else if (MsgTypeEnum.ASN_OMS_AUDIT.getValue().equals(data.getType())) {
//            ((Asn2OmsAuditExpSrvImpl)Component.getInstance(Asn2OmsAuditExpSrvImpl.class)).msgJobSend(data, serviceLog);
        } else if (MsgTypeEnum.MPS_OMS.getValue().equals(data.getType())) {
//            ((Mps2OmsExpSrvImpl)Component.getInstance(Mps2OmsExpSrvImpl.class)).msgJobSend(data, serviceLog);
        } else {
//            ((Asn2OmsExpSrvImpl)Component.getInstance(Asn2OmsExpSrvImpl.class)).msgJobSend(data, serviceLog);
        }

    }
}
