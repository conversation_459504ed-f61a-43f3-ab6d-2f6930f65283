package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.converter.LocationMapStruct;
import com.accesscorporate.app.wms.server.biz.manager.impl.LocationManager;
import com.accesscorporate.app.wms.server.biz.params.request.LocationSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.LocationResponse;
import com.accesscorporate.app.wms.server.biz.service.LocationService;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import com.accesscorporate.app.wms.server.dal.entity.Location;
import com.accesscorporate.app.wms.server.dal.mapper.LocationMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class LocationServiceImpl extends ServiceImpl<LocationMapper, Location> implements LocationService {
    @Autowired
    private LocationManager locationManager;

    @Override
    public List<LocationResponse> listAll() {
        LambdaQueryWrapper<Location> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Location::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        return locationManager.list(wrapper).stream()
                .map(LocationMapStruct.INSTANCE::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public LocationResponse get(Long id) {
        Location location = super.getById(id);
        return LocationMapStruct.INSTANCE.convertToResponse(location);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(LocationSaveRequest request) {
        Location location = LocationMapStruct.INSTANCE.convertToEntity(request);
        location.setWarehouseId(UserContextAssistant.getCurrentWarehouseId());
        return super.save(location);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        return super.removeById(id);
    }

    @Override
    public Page<LocationResponse> page(Integer current, Integer size, String locCode, Long partitionId, String locType, Integer canMixProduct, Integer ignoreLpn, String aisleFrom, String aisleTo, String bayFrom, String bayTo, String levelFrom, String levelTo, String positionFrom, String positionTo) {
        Page<Location> page = new Page<>(current, size);
        LambdaQueryWrapper<Location> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(locCode), Location::getLocCode, locCode);
        wrapper.eq(Location::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        wrapper.eq(partitionId != null, Location::getPartitionId, partitionId);
        wrapper.eq(locType != null, Location::getLocType, locType);
        wrapper.eq(canMixProduct != null, Location::getCanMixProduct, canMixProduct);
        wrapper.eq(ignoreLpn != null, Location::getIgnoreLpn, ignoreLpn);
        wrapper.ge(aisleFrom != null, Location::getAisle, aisleFrom);
        wrapper.le(aisleTo != null, Location::getAisle, aisleTo);
        wrapper.ge(bayFrom != null, Location::getBay, bayFrom);
        wrapper.le(bayTo != null, Location::getBay, bayTo);
        wrapper.ge(levelFrom != null, Location::getLev, levelFrom);
        wrapper.le(levelTo != null, Location::getLev, levelTo);
        wrapper.ge(positionFrom != null, Location::getPosition, positionFrom);
        wrapper.le(positionTo != null, Location::getPosition, positionTo);

        Page<Location> locationPage = super.page(page, wrapper);
        List<LocationResponse> responses = locationPage.getRecords().stream()
                .map(LocationMapStruct.INSTANCE::convertToResponse)
                .collect(Collectors.toList());
        return new Page<LocationResponse>(locationPage.getCurrent(), locationPage.getSize(), locationPage.getTotal())
                .setRecords(responses);
    }

    @Override
    public Location queryLocationByCodeAndWarehouseId(String locCode, Long warehouseId) {
        return lambdaQuery()
                .eq(Location::getLocCode,locCode)
                .eq(Location::getWarehouseId,warehouseId)
                .eq(Location::getIsDeleted,Boolean.FALSE)
                .one();
    }

    @Override
    public List<Location> queryLocationByCodes(List<String> locCodes, Long warehouseId) {
        if (CollectionUtils.isEmpty(locCodes)){
            return Lists.newArrayList();
        }
        return lambdaQuery()
                .in(Location::getLocCode,locCodes)
                .eq(Location::getWarehouseId,warehouseId)
                .eq(Location::getIsDeleted,Boolean.FALSE)
                .list();
    }




}
