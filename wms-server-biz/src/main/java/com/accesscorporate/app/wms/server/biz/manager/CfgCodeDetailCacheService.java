package com.accesscorporate.app.wms.server.biz.manager;

import com.accesscorporate.app.wms.server.biz.service.dto.SelectItem;
import com.accesscorporate.app.wms.server.common.enums.StatusEnum;
import com.accesscorporate.app.wms.server.dal.entity.CfgCodeDetailDO;
import com.accesscorporate.app.wms.server.dal.entity.CfgCodeMasterDO;
import com.accesscorporate.app.wms.server.dal.mapper.CfgCodeDetailMapper;
import com.accesscorporate.app.wms.server.dal.mapper.CfgCodeMasterMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.idanchuang.component.redis.util.RedisUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.MapUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/5 下午4:47 星期三
 */
@Service
public class CfgCodeDetailCacheService {
    @Resource
    private RedisUtil redisUtils;

    @Resource
    private CfgCodeDetailMapper cfgCodeDetailMapper;


    @Resource
    private CfgCodeMasterMapper cfgCodeMasterMapper;


    private static final String DICTIONARIES_KEY = "cache:dictionaries";
    private static final String DICTIONARIES_EN_KEY = "cache:dictionaries:en";
    private static final String SELECT_ITEMS_KEY = "cache:selectItems";


    public void clearCache(String masterCode) {
        redisUtils.delHash(DICTIONARIES_KEY, masterCode);
        redisUtils.delHash(DICTIONARIES_EN_KEY, masterCode);
        redisUtils.delHash(SELECT_ITEMS_KEY, masterCode);
    }

    /**
     * 根据数据字典名称取得数据字典
     *
     * @param masterCode
     * @return <p>
     * 返回数据字典中消息key值，若key值为空则返回字典项名称
     * </p>
     */
    public Map<String, String> getDictionary(String masterCode) {
        Map<String, String> dict = redisUtils.getHashObj(DICTIONARIES_KEY, masterCode);
        if (MapUtils.isNotEmpty(dict)) {
            return dict;
        }

        LambdaQueryWrapper<CfgCodeDetailDO> queryWrapper =
                new LambdaQueryWrapper<CfgCodeDetailDO>().eq(CfgCodeDetailDO::getMasterCode, masterCode);
        List<CfgCodeDetailDO> list = cfgCodeDetailMapper.selectList(queryWrapper);
        dict = Maps.newHashMap();
        for (CfgCodeDetailDO c : list) {
            if (StatusEnum.DISABLE.getKey().equals(c.getStatus())) {
                continue;
            }
            String text = c.getMessageKey();
            if (text == null || (text.trim().isEmpty())) {
                text = c.getCodeNameZh();
            }
            dict.put(c.getCodeValue().trim(), text);
        }
        redisUtils.setHashObj(DICTIONARIES_KEY, masterCode, dict);
        return dict;
    }

    /**
     * 获取英文数据字典
     *
     * @param masterCode
     * @return
     */
    public Map<String, String> getDictionaryEn(String masterCode) {
        Map<String, String> dict = redisUtils.getHashObj(DICTIONARIES_EN_KEY, masterCode);
        if (MapUtils.isNotEmpty(dict)) {
            return dict;
        }

        LambdaQueryWrapper<CfgCodeDetailDO> queryWrapper =
                new LambdaQueryWrapper<CfgCodeDetailDO>().eq(CfgCodeDetailDO::getMasterCode, masterCode);
        List<CfgCodeDetailDO> list = cfgCodeDetailMapper.selectList(queryWrapper);
        dict = Maps.newHashMap();
        for (CfgCodeDetailDO c : list) {
            if (StatusEnum.DISABLE.getKey().equals(c.getStatus())) {
                continue;
            }
            String text = c.getMessageKey();
            if (text == null || (text.trim().isEmpty())) {
                text = c.getCodeNameEn();
            }
            dict.put(c.getCodeValue().trim(), text);
        }
        redisUtils.setHashObj(DICTIONARIES_EN_KEY, masterCode, dict);
        return dict;
    }

    public String getDictionaryValue(String dictName, String key) {
        Map<String, String> dic = getDictionary(dictName);
        if (dic == null || dic.isEmpty()) {
            return null;
        } else {
            return dic.get(key);
        }
    }

    /**
     * 获取英文数据字典值
     *
     * @param dictName
     * @param key
     * @return
     */
    public String getDictionaryEnValue(String dictName, String key) {
        Map<String, String> dic = getDictionaryEn(dictName);
        if (dic == null || dic.isEmpty()) {
            return null;
        } else {
            return dic.get(key);
        }
    }

    /**
     * 取得下拉列表
     *
     * @param masterCode
     * @return
     */
    public List<SelectItem> getSelectItems(String masterCode, String... excludeValue) {
        Set excludeSet = Sets.newHashSet(excludeValue);
        List<SelectItem> list = redisUtils.getHashObj(SELECT_ITEMS_KEY, masterCode);
        if (list == null) {
            list = new ArrayList<SelectItem>();
        }
        if (list.isEmpty()) {
            LambdaQueryWrapper<CfgCodeDetailDO> queryWrapper =
                    new LambdaQueryWrapper<CfgCodeDetailDO>().eq(CfgCodeDetailDO::getMasterCode, masterCode);
            List<CfgCodeDetailDO> codeList = cfgCodeDetailMapper.selectList(queryWrapper);

            for (CfgCodeDetailDO c : codeList) {
                if (StatusEnum.DISABLE.getKey().equals(c.getStatus()) || excludeSet.contains(c.getCodeValue().trim())) {
                    continue;
                }

                SelectItem item = new SelectItem();
                item.setValue(c.getCodeValue().trim());
                String msgKey = c.getMessageKey();
                String text = null;
                if (msgKey == null || msgKey.trim().isEmpty()) {
                    text = c.getCodeNameZh();
                } else {
                    //TODO 资源文件映射key到中文
//                    text = ResourceUtils.getDispalyString(msgKey);
                }
                item.setLabel(text);
                item.setDescription(text);

                list.add(item);
            }
            redisUtils.setHashObj(SELECT_ITEMS_KEY, masterCode, list);
        }

        return list;
    }

    public List<CfgCodeDetailDO> findByCategory(String masterCode) {
        CfgCodeMasterDO codeMaster = cfgCodeMasterMapper.selectOne(
                new LambdaQueryWrapper<CfgCodeMasterDO>().eq(CfgCodeMasterDO::getMasterCode, masterCode));
        if (ObjectUtils.isEmpty(codeMaster)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CfgCodeDetailDO> queryWrapper =
                new LambdaQueryWrapper<CfgCodeDetailDO>().eq(CfgCodeDetailDO::getMasterCode, masterCode);
        return cfgCodeDetailMapper.selectList(queryWrapper);
    }


    public String findByCodeName(String dictName, String codeValue) {
        List<CfgCodeDetailDO> details = findByCategory(dictName);
        for (CfgCodeDetailDO detail : details) {
            if (StringUtil.endsWithIgnoreCase(detail.getCodeValue(), codeValue)) {
                return detail.getCodeNameZh();
            }
        }
        return null;
    }


}
