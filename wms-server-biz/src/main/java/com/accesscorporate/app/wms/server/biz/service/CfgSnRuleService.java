package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.request.CfgSnRulePageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.CfgSnRuleRequest;
import com.accesscorporate.app.wms.server.biz.params.response.CfgSnRuleResponse;
import com.idanchuang.component.base.page.PageData;

public interface CfgSnRuleService {
    void save(CfgSnRuleRequest cfgSnRuleRequest);

    void update(CfgSnRuleRequest cfgSnRuleRequest);

    PageData<CfgSnRuleResponse> page(CfgSnRulePageRequest cfgSnRulePageRequest);

    CfgSnRuleResponse edit(String ruleCode);
}
