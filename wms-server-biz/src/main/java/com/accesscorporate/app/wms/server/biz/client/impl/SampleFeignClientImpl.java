package com.accesscorporate.app.wms.server.biz.client.impl;

import com.accesscorporate.app.wms.server.api.client.SampleFeignClient;
import com.accesscorporate.app.wms.server.api.dto.MessageDTO;
import com.idanchuang.component.base.JsonResult;
import org.springframework.web.bind.annotation.RestController;

/**
 * 服务提供者对RPC接口实现样例
 *
 * 1.Rpc接口实现类名需要遵守 XxxFeignClientImpl 的规范
 *
 * <AUTHOR>
 * Created at 2020/5/14 12:59
 **/
@RestController
public class SampleFeignClientImpl implements SampleFeignClient {

    @Override
    public JsonResult<String> hello(MessageDTO msg) {
        return JsonResult.success(msg.getTitle());
    }

}
