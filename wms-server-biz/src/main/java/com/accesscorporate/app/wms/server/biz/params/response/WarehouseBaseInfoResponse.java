package com.accesscorporate.app.wms.server.biz.params.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 仓基础信息 Response
 *
 * <AUTHOR>
 * 2025/2/7  17:42
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "仓基础列表信息响应体")
public class WarehouseBaseInfoResponse {

    /**
     * 仓ID
     */
    @Schema(title = "仓ID")
    private Long warehouseId;

    /**
     * 仓名称
     */
    @Schema(title = "仓名称")
    private String warehouseName;

    /**
     * 仓库编码
     */
    @Schema(title = "仓库编码")
    private String warehouseCode;

}
