package com.accesscorporate.app.wms.server.biz.manager.rule;

import com.accesscorporate.app.wms.server.dal.entity.rule.RulAllocationDO;
import com.accesscorporate.app.wms.server.dal.mapper.rule.RulAllocationMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version JDK 17
 * @date 2025/2/26
 * @description
 */
@Component
public class RulAllocationManager extends ServiceImpl<RulAllocationMapper, RulAllocationDO> {

    public Page<RulAllocationDO> selectPage(String allocationCode,
                                            String allocationDescr,
                                            Long current,
                                            Long size) {
        return lambdaQuery()
                .eq(StringUtils.isNotEmpty(allocationCode), RulAllocationDO::getAllocationCode, allocationCode)
                .like(StringUtils.isNotEmpty(allocationDescr), RulAllocationDO::getAllocationDescr, allocationDescr)
                .eq(RulAllocationDO::getIsDeleted, 0)
                .page(new Page<>(current, size));
    }

    public RulAllocationDO selectById(Long id) {
        return getById(id);
    }

    public Boolean updateRulAllocation(RulAllocationDO rulAllocationDO) {
        return updateById(rulAllocationDO);
    }

    public Boolean createRulAllocation(RulAllocationDO rulAllocationDO) {
        return save(rulAllocationDO);
    }

    public Boolean deleteRulAllocation(Long id) {
        RulAllocationDO rulAllocationDO = new RulAllocationDO();
        rulAllocationDO.setId(id);
        rulAllocationDO.setIsDeleted(1);
        return updateById(rulAllocationDO);
    }

}
