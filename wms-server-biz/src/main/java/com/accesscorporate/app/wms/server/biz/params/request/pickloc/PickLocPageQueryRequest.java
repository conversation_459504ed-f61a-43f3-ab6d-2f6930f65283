package com.accesscorporate.app.wms.server.biz.params.request.pickloc;

import com.idanchuang.component.base.page.PageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-20 15:24
 * Description: 商品捡货位列表查询参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Tag(name = "商品捡货位列表查询参数")
public class PickLocPageQueryRequest extends PageDTO {

    @Schema(title = "商品编码")
    private String productCode;

    @Schema(title = "商品中文名称")
    private String productCname;

    @Schema(title = "商品条码")
    private String barCode;

    @Schema(title = "库位编码")
    private String locCode;

    @Schema(title = "库区主键ID")
    private String partitionId;

    @Schema(title = "当前仓库ID")
    private Long currentWarehouseId;


}
