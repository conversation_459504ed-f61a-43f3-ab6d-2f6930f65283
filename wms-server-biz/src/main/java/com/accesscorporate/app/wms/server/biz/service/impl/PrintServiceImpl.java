package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.params.request.PrinterFilter;
import com.accesscorporate.app.wms.server.biz.service.PrintService;
import com.accesscorporate.app.wms.server.biz.service.dto.PrintDTO;
import com.accesscorporate.app.wms.server.dal.dto.PrinterFilterQuery;
import com.accesscorporate.app.wms.server.dal.mapper.PrintMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.page.PageData;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.accesscorporate.app.wms.server.dal.entity.PrintDO;
import org.springframework.transaction.annotation.Transactional;

/**
 * 打印机实现类
 *
 * <AUTHOR>
 */
@Service
public class PrintServiceImpl implements PrintService {

    @Resource
    private PrintMapper printMapper;

    @Override
    public PrintDTO queryById(Long id) {
        PrintDO printDO = printMapper.selectById(id);
        // 将 PrintDO 转换为 PrintDTO
        if (printDO != null) {
            return convertToDTO(printDO);
        }
        return null;
    }

    private PrintDTO convertToDTO(PrintDO printDO) {
        if (printDO == null) {
            return null;
        }
        PrintDTO printDTO = new PrintDTO();
        printDTO.setId(printDO.getId());
        printDTO.setName(printDO.getName());
        printDTO.setIsPrintByName(printDO.getIsPrintByName());
        printDTO.setCode(printDO.getCode());
        printDTO.setInvoiceCode(printDO.getInvoiceCode());
        printDTO.setInvoiceNoCurrent(printDO.getInvoiceNoCurrent());
        printDTO.setWarehouseId(printDO.getWarehouseId());
        printDTO.setInvoiceNoFrom(printDO.getInvoiceNoFrom());
        printDTO.setInvoiceNoTo(printDO.getInvoiceNoTo());
        printDTO.setIsDeleted(printDO.getIsDeleted());
        printDTO.setStatus(printDO.getStatus());
        printDTO.setType(printDO.getType());
        return printDTO;
    }

    @Override
    public List<PrintDTO> selectAll() {
        List<PrintDO> list = printMapper.selectAll();
        return list.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public int insert(PrintDTO printDTO) {
        PrintDO printDO = convertToDO(printDTO);
        return printMapper.insert(printDO);
    }

    @Override
    public int updateById(PrintDTO printDTO) {
        PrintDO printDO = convertToDO(printDTO);
        return printMapper.updateById(printDO);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return printMapper.deleteByPrimaryKey(id);
    }

    private PrintDO convertToDO(PrintDTO printDTO) {
        PrintDO printDO = new PrintDO();
        printDO.setId(printDTO.getId());
        printDO.setName(printDTO.getName());
        printDO.setStatus(printDTO.getStatus());
        printDO.setCode(printDTO.getCode());
        printDO.setType(printDTO.getType());
        printDO.setInvoiceNoFrom(printDTO.getInvoiceNoFrom());
        printDO.setInvoiceNoTo(printDTO.getInvoiceNoTo());
        printDO.setInvoiceNoCurrent(printDTO.getInvoiceNoCurrent());
        printDO.setInvoiceCode(printDTO.getInvoiceCode());
        printDO.setIsDeleted(printDTO.getIsDeleted());
        printDO.setWarehouseId(printDTO.getWarehouseId());
        printDO.setIsPrintByName(printDTO.getIsPrintByName());
        return printDO;
    }

    @Override
    public PageData<PrintDTO> query(PrinterFilter filter, int startIndex, int pageSize) {
        Page<PrintDO> page = new Page<>(startIndex, pageSize);
        PrinterFilterQuery query = new PrinterFilterQuery();
        query.setCode(filter.getCode());
        query.setStatus(filter.getStatus());
        query.setWarehouseId(filter.getWarehouseId());
        IPage<PrintDO> printDOIPage = printMapper.selectByPage(page, query);
        PageData<PrintDTO> pageData = new PageData<>();
        pageData.setRecords(printDOIPage.getRecords().stream().map(this::convertToDTO).collect(Collectors.toList()));
        pageData.setTotal(printDOIPage.getTotal());
        pageData.setCurrent(page.getCurrent());
        pageData.setSize(page.getSize());
        return pageData;
    }

    @Override
    public PrintDTO get(Long id) {
        return queryById(id);
    }

    @Override
    @Transactional
    public void saveOrUpdate(PrintDTO printDTO) {
        if (printDTO.getId() == null) {
            insert(printDTO);
        } else {
            updateById(printDTO);
        }
    }

    @Override
    @Transactional
    public void remove(Long id) {
        deleteByPrimaryKey(id);
    }

    @Override
    public boolean isExists(PrintDTO printDTO) {
        PrintDO printDO = printMapper.selectByCode(printDTO.getCode(), printDTO.getWarehouseId());
        return printDO != null;
    }

    @Override
    public List<PrintDTO> getAvailablePrintersByType(String type) {
        Long warehouseId = 1L;
        List<PrintDO> list = printMapper.selectByType(type, warehouseId);
        if (CollectionUtils.isEmpty(list)) {
            return List.of();
        }
        return list.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public PrintDTO findPrinterByCode(String printerCode) {
        Long warehouseId = 1L;
        PrintDO printDO = printMapper.selectByCode(printerCode, warehouseId);
        return convertToDTO(printDO);
    }

}
