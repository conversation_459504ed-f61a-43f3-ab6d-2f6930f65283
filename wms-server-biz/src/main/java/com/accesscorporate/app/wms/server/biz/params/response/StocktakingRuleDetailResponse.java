package com.accesscorporate.app.wms.server.biz.params.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 盘点规则明细 响应-ViewObject
 *
 * <AUTHOR>
 * 2025/2/17  09:55
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "盘点规则明细 View Object")
public class StocktakingRuleDetailResponse {


    @Schema(title = "规则ID")
    private Long id;

    @Schema(title = "规则编码")
    private String ruleCode;

    @Schema(title = "规则名称")
    private String ruleName;

    @Schema(title = "优先级")
    private Integer priority;

    @Schema(title = "优先级(文本)")
    private String priorityStr;

    @Schema(title = "生产日期")
    private Boolean productionDate;

    @Schema(title = "失效日期")
    private Boolean expirationDate;

    @Schema(title = "入库日期")
    private Boolean inboundDate;

    @Schema(title = "供应商")
    private Boolean supplier;

    @Schema(title = "批号")
    private Boolean lotNumber;

    @Schema(title = "商家")
    private Boolean merchant;

    @Schema(title = "包装")
    private Boolean packaging;

    @Schema(title = "生产厂家")
    private Boolean manufacturer;

    @Schema(title = "进价")
    private Boolean purchasePrice;

    @Schema(title = "采购单号")
    private Boolean purchaseOrderNumber;

    @Schema(title = "批次11")
    private Boolean batch11;


}
