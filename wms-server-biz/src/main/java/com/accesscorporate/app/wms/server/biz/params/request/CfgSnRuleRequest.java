package com.accesscorporate.app.wms.server.biz.params.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CfgSnRuleRequest {
    /**
     * 规则代码
     */
    @NotBlank(message = "规则代码不能为空")
    private String ruleCode;

    /**
     * 规则名称
     */
    @NotBlank(message = "规则名称不能为空")
    private String ruleName;

    /**
     * 后缀
     */
    private String postfix;

    /**
     * 例子：WAVE
     * {
     * year
     * }
     * month
     * day
     */
    private String prefix;

    /**
     * 长度
     */
    @NotBlank(message = "流水号长度不能为空")
    private Integer sLength;

    /**
     * 循环方式 0 日 1 月 2 年 3 不循环
     */
    @NotNull(message = "流水号循环方式不能为空")
    private Integer sLoop;

    /**
     * 取最大单号的sql
     */
    private String sqlText;

    /**
     * 全局标记
     */
    private Integer isGlobal;

    /**
     * 是否删除 0:no 1:yes
     * 逻辑删除
     */
    private Integer isDeleted;

    /**
     * 版本锁
     * 版本
     * 乐观锁控制
     */
    private Long version;
}