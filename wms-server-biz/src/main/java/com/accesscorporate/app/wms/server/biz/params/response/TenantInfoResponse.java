package com.accesscorporate.app.wms.server.biz.params.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 租户信息-响应体Response
 *
 * <AUTHOR>
 * 2025/2/8  18:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "租户列表信息")
public class TenantInfoResponse {

    @Schema(title = "租户ID")
    private Long tenantId;

    @Schema(title = "租户名称")
    private String tenantName;

}
