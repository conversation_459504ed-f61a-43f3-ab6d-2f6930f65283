package com.accesscorporate.app.wms.server.biz.params.response.goodspackage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-18 13:49
 * Description: 商品包装明细
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PackageInfoDetailResponse {

    @Schema(title = "商品包装明细id")
    private Long packageDetailId;

    @Schema(title = "数量")
    private BigDecimal qty;

    @Schema(title = "单位")
    private String packUom;

    @Schema(title = "单位描述")
    private String uomDescr;

    @Schema(title = "是否主包装,0-否，1-是")
    private String mainFlag;


}
