package com.accesscorporate.app.wms.server.biz.converter;

import com.accesscorporate.app.wms.server.biz.params.request.DocDoDetailSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocDoDetailResponse;
import com.accesscorporate.app.wms.server.dal.entity.DocDoDetail;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 发货单明细表转换器
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Mapper
public interface DocDoDetailMapStruct {

    DocDoDetailMapStruct INSTANCE = Mappers.getMapper(DocDoDetailMapStruct.class);

    /**
     * 实体转响应体
     */
    DocDoDetailResponse convertToResponse(DocDoDetail docDoDetail);

    /**
     * 响应体转实体
     */
    @Mapping(target = "isDeleted", ignore = true)
    DocDoDetail convertToEntity(DocDoDetailResponse response);

    /**
     * 保存请求转实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "warehouseId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "updateBy", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    DocDoDetail convertToEntity(DocDoDetailSaveRequest saveRequest);
}
