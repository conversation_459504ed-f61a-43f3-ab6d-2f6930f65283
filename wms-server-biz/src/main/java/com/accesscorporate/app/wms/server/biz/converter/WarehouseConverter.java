package com.accesscorporate.app.wms.server.biz.converter;

import com.accesscorporate.app.wms.server.biz.params.response.WarehouseBaseInfoResponse;
import com.accesscorporate.app.wms.server.dal.entity.WarehouseDO;

/**
 * 仓 模型转换器
 *
 * <AUTHOR>
 * 2025/2/7  17:47
 */
public class WarehouseConverter {


    public static WarehouseBaseInfoResponse toWarehouseBaseInfoResponse(WarehouseDO warehouseDO) {
        return new WarehouseBaseInfoResponse(warehouseDO.getId(), warehouseDO.getWarehouseName(), warehouseDO.getWarehouseCode());
    }


}
