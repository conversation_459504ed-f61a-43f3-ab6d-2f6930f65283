package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.response.TenantInfoResponse;
import com.accesscorporate.app.wms.server.biz.params.response.WarehouseBaseInfoResponse;

import java.util.List;

/**
 * 认证权限--Service
 *
 * <AUTHOR>
 * 2025/2/8  11:26
 */
public interface IAuthPermissionService {


    /**
     * 查询当前用户关联的租户列表
     *
     * @return List<TenantInfoResponse>
     * <AUTHOR>
     * 2025/2/8 18:25
     */
    List<TenantInfoResponse> queryUserTenantInfo();


    /**
     * 查询当前用户绑定的仓-基础信息
     *
     * @return List<WarehouseBaseInfoResponse>
     * <AUTHOR>
     * 2025/2/8 11:28
     */
    List<WarehouseBaseInfoResponse> queryUserBindWhBaseInfo();


    /**
     * 查询仓-基础信息
     *
     * @return List<WarehouseBaseInfoResponse>
     * <AUTHOR>
     * 2025/2/7 17:49
     */
    List<WarehouseBaseInfoResponse> queryAllWhBaseInfo();

}
