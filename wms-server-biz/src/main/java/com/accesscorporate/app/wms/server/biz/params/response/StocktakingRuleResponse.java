package com.accesscorporate.app.wms.server.biz.params.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 盘点规则主列表响应-ViewObject
 *
 * <AUTHOR>
 * 2025/2/14  13:18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "盘点规则 View Object")
public class StocktakingRuleResponse {

    @Schema(title = "规则ID")
    private Long id;

    @Schema(title = "规则编码")
    private String ruleCode;

    @Schema(title = "规则名称")
    private String ruleName;

    @Schema(title = "优先级")
    private Integer priority;

    @Schema(title = "优先级(文本)")
    private String priorityStr;

    @Schema(title = "更新人")
    private String updater;

}
