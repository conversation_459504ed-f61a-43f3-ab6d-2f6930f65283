package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.service.SExpSrvMsgService;
import com.accesscorporate.app.wms.server.dal.entity.SExpSrvMsgDO;
import com.accesscorporate.app.wms.server.dal.mapper.SExpSrvMsgMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class SExpSrvMsgServiceImpl implements SExpSrvMsgService {

    private final SExpSrvMsgMapper sExpSrvMsgMapper;

    @Override
    public List<SExpSrvMsgDO> queryByTypeAndInvokeCount(List<Long> typeList, Long invokeCount, Integer maxRows) {
        return sExpSrvMsgMapper.queryByTypeAndInvokeCount(typeList, invokeCount, maxRows);
    }
}
