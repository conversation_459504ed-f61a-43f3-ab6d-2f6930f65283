package com.accesscorporate.app.wms.server.biz.params.request;

import com.idanchuang.component.base.page.PageDTO;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * MdSortingBinQueryRequest 查询类
 */
@Data
public class MdSortingBinQueryRequest extends PageDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -2400906788608618993L;

    /**
     * 分拣区名称
     */
    private String sortingZoneName;

    /**
     * 分拣区编号
     */
    private String sortingZoneNbr;

    /**
     * 是否可用
     */
    private Long isAvailable;

    /**
     * 分拣柜类型
     */
    private String sortBinType;

    /**
     * 仓库ID
     */
    private Long warehouseId;

}
