package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.params.request.MdSortingBinQueryRequest;
import com.accesscorporate.app.wms.server.biz.service.MdSortingBinService;
import com.accesscorporate.app.wms.server.biz.service.PrintService;
import com.accesscorporate.app.wms.server.biz.service.dto.MdSortingBinDTO;
import com.accesscorporate.app.wms.server.biz.service.dto.PrintDTO;
import com.accesscorporate.app.wms.server.common.enums.AutoWaveTypeEnum;
import com.accesscorporate.app.wms.server.common.enums.DoTypeEnum;
import com.accesscorporate.app.wms.server.common.enums.SortBinTypeEnum;
import com.accesscorporate.app.wms.server.dal.dto.MdSortingBinQuery;
import com.accesscorporate.app.wms.server.dal.entity.MdSortingBinDO;
import com.accesscorporate.app.wms.server.dal.mapper.MdSortingBinMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.exception.core.ExFactory;
import com.idanchuang.component.base.page.PageData;
import com.idanchuang.component.core.util.CopyUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service
public class MdSortingBinServiceImpl implements MdSortingBinService {

    @Resource
    private MdSortingBinMapper mdSortingBinMapper;

    @Resource
    private PrintService printService;

    /**
     * 根据主键ID查询分拣柜信息
     *
     * @param id 分拣柜主键ID
     * @return 分拣柜DTO对象，包含分拣柜详细信息，如果不存在则返回null
     */
    @Override
    public MdSortingBinDTO selectById(Long id) {
        MdSortingBinDO mdSortingBinDO = mdSortingBinMapper.selectById(id);
        if (mdSortingBinDO != null) {
            return CopyUtil.copy(mdSortingBinDO, MdSortingBinDTO.class);
        }
        return null;
    }

    /**
     * 根据条件查询MdSortingBinDO对象列表
     *
     * @param queryRequest 查询条件
     * @return MdSortingBinDO对象列表
     */
    @Override
    public PageData<MdSortingBinDTO> query(MdSortingBinQueryRequest queryRequest) {
        Page<MdSortingBinDO> page = new Page<>(queryRequest.getCurrent(), queryRequest.getSize());
        MdSortingBinQuery query = convertToMdSortingBinQuery(queryRequest);
        IPage<MdSortingBinDO> result = mdSortingBinMapper.selectByCondition(page, query);
        if (result != null && CollectionUtils.isNotEmpty(result.getRecords())) {
            List<MdSortingBinDTO> response = result.getRecords().stream().map(this::convertToMdSortingBinDTO)
                    .filter(Objects::nonNull).toList();
            return PageData.of(response, result.getCurrent(), result.getSize(), result.getTotal());
        }
        return new PageData<>();
    }

    private MdSortingBinQuery convertToMdSortingBinQuery(MdSortingBinQueryRequest queryRequest) {
        MdSortingBinQuery query = new MdSortingBinQuery();
        query.setWarehouseId(queryRequest.getWarehouseId());
        query.setSortingZoneName(queryRequest.getSortingZoneName());
        query.setIsAvailable(queryRequest.getIsAvailable());
        query.setSortingZoneNbr(queryRequest.getSortingZoneNbr());
        query.setSortBinType(queryRequest.getSortBinType());
        return query;
    }

    private MdSortingBinDTO convertToMdSortingBinDTO(MdSortingBinDO mdSortingBinDO) {
        if (mdSortingBinDO == null) {
            return null;
        }
        MdSortingBinDTO mdSortingBinDTO = new MdSortingBinDTO();
        mdSortingBinDTO.setId(mdSortingBinDO.getId());
        mdSortingBinDTO.setWarehouseId(mdSortingBinDO.getWarehouseId());
        mdSortingBinDTO.setSortingZoneName(mdSortingBinDO.getSortingZoneName());
        mdSortingBinDTO.setSortingBinQty(mdSortingBinDO.getSortingBinQty());
        mdSortingBinDTO.setIsAvailable(mdSortingBinDO.getIsAvailable());
        mdSortingBinDTO.setSortingZoneNbr(mdSortingBinDO.getSortingZoneNbr());
        mdSortingBinDTO.setSortingZoneStatus(mdSortingBinDO.getSortingZoneStatus());
        mdSortingBinDTO.setCreateTime(mdSortingBinDO.getCreateTime());
        mdSortingBinDTO.setCreateBy(mdSortingBinDO.getCreateBy());
        mdSortingBinDTO.setUpdateTime(mdSortingBinDO.getUpdateTime());
        mdSortingBinDTO.setUpdateBy(mdSortingBinDO.getUpdateBy());
        mdSortingBinDTO.setIsDeleted(mdSortingBinDO.getIsDeleted());
        mdSortingBinDTO.setVersion(mdSortingBinDO.getVersion());
        mdSortingBinDTO.setWaveQty(mdSortingBinDO.getWaveQty());
        mdSortingBinDTO.setSortBinType(mdSortingBinDO.getSortBinType());
        mdSortingBinDTO.setInvoicePrinterId(mdSortingBinDO.getInvoicePrinterId());
        mdSortingBinDTO.setSupportWcs(mdSortingBinDO.getSupportWcs());
        return mdSortingBinDTO;
    }


    /**
     * 插入MdSortingBinDO对象
     *
     * @param mdSortingBinDTO 要插入的对象
     * @return 插入成功的记录数
     */
    @Override
    public int add(MdSortingBinDTO mdSortingBinDTO) {
        if (mdSortingBinDTO == null) {
            return 0;
        }
        MdSortingBinDO mdSortingBinDO = CopyUtil.copy(mdSortingBinDTO, MdSortingBinDO.class);
        return mdSortingBinMapper.insert(mdSortingBinDO);
    }

    /**
     * 根据主键ID更新MdSortingBinDO对象
     *
     * @param mdSortingBinDTO 要更新的对象
     * @return 更新成功的记录数
     */
    @Override
    public int updateById(MdSortingBinDTO mdSortingBinDTO) {
        if (mdSortingBinDTO == null) {
            return 0;
        }
        if (mdSortingBinDTO.getId() == null) {
            return 0;
        }
        if (mdSortingBinDTO.getInvoicePrinterId() != null) {
            PrintDTO printDTO = printService.queryById(mdSortingBinDTO.getInvoicePrinterId());
            if (printDTO == null) {
                return 0;
            }
        }
        // 校验拒货柜编码是否存在
        mdSortingBinMapper.selectBySortingZoneNbrAndWarehouseIdAndId(mdSortingBinDTO.getSortingZoneNbr(),
                mdSortingBinDTO.getWarehouseId(), mdSortingBinDTO.getId());
        MdSortingBinDO sortingBinDOInDb = getUpdateMdSortingBin(mdSortingBinDTO);
        if (sortingBinDOInDb == null) {
            return 0;
        }
        return mdSortingBinMapper.updateById(sortingBinDOInDb);
    }

    /**
     * 获取更新的对象
     *
     * @param mdSortingBinDTO 提交待更新信息
     * @return MdSortingBinDO
     */
    private MdSortingBinDO getUpdateMdSortingBin(MdSortingBinDTO mdSortingBinDTO) {
        MdSortingBinDO sortingBinDOInDb = mdSortingBinMapper.selectById(mdSortingBinDTO.getId());
        if (sortingBinDOInDb == null) {
            return null;
        }
        if (mdSortingBinDTO.getSortBinType() != null) {
            sortingBinDOInDb.setSortBinType(mdSortingBinDTO.getSortBinType());
        }
        if (mdSortingBinDTO.getIsAvailable() != null) {
            sortingBinDOInDb.setIsAvailable(mdSortingBinDTO.getIsAvailable());
        }
        if (mdSortingBinDTO.getSortingBinQty() != null) {
            sortingBinDOInDb.setSortingBinQty(mdSortingBinDTO.getSortingBinQty());
        }
        if (mdSortingBinDTO.getSortingZoneNbr() != null) {
            sortingBinDOInDb.setSortingZoneNbr(mdSortingBinDTO.getSortingZoneNbr());
        }
        if (mdSortingBinDTO.getSortingZoneName() != null) {
            sortingBinDOInDb.setSortingZoneName(mdSortingBinDTO.getSortingZoneName());
        }
        if (mdSortingBinDTO.getSortingZoneStatus() != null) {
            sortingBinDOInDb.setSortingZoneStatus(mdSortingBinDTO.getSortingZoneStatus());
        }
        if (sortingBinDOInDb.getVersion() != null) {
            sortingBinDOInDb.setVersion(sortingBinDOInDb.getVersion() + 1);
        }
        if (mdSortingBinDTO.getWaveQty() != null) {
            sortingBinDOInDb.setWaveQty(mdSortingBinDTO.getWaveQty());
        }
        if (mdSortingBinDTO.getInvoicePrinterId() != null) {
            sortingBinDOInDb.setInvoicePrinterId(mdSortingBinDTO.getInvoicePrinterId());
        }
        if (mdSortingBinDTO.getUpdateBy() != null) {
            sortingBinDOInDb.setUpdateBy(mdSortingBinDTO.getUpdateBy());
        }
        return sortingBinDOInDb;
    }

    /**
     * 根据主键ID删除MdSortingBinDO对象
     *
     * @param id 主键ID
     * @return 删除成功的记录数
     */
    @Override
    public int deleteByPrimaryKey(Long id) {
        return mdSortingBinMapper.deleteByPrimaryKey(id);
    }

    @Override
    public MdSortingBinDTO querySortingBinByNo(String sortingBinNo) {
        MdSortingBinDO mdSortingBinDO = mdSortingBinMapper.selectBySortingBinNo(sortingBinNo);
        if (mdSortingBinDO != null) {
            return CopyUtil.copy(mdSortingBinDO, MdSortingBinDTO.class);
        }
        return null;
    }

    @Override
    public MdSortingBinDTO findSortingBinByNoIgnAvailable(String sortingBinNo) {
        Long warehouseId = 1L;// TODO
        List<MdSortingBinDO> sortingBins = mdSortingBinMapper.selectSortingBinByNo(sortingBinNo, warehouseId);
        //返回第一个
        if (CollectionUtils.isNotEmpty(sortingBins)) {
            MdSortingBinDO sortingBin = sortingBins.get(0);
            return CopyUtil.copy(sortingBin, MdSortingBinDTO.class);
        }
        return null;
    }

    @Override
    public Long getIdleSortingBin() {
        Long warehouseId = 1L;// TODO
        String sortBinType = SortBinTypeEnum.BIG.getCode();
        return mdSortingBinMapper.selectIdleSortingBin(sortBinType, warehouseId);
    }

    @Override
    @Transactional
    public void addWaveQty(Long id) {
        // TODO 加Apollo配置
        boolean isAllSortBin = true;// Config.isDefaultFalse(Keys.Delivery.wave_needAllSortBin, Config.ConfigLevel.WAREHOUSE);
        if (isAllSortBin) {
            MdSortingBinDO sortingBin = mdSortingBinMapper.selectById(id);
            Integer waveQty = sortingBin.getWaveQty();
            if (waveQty != null) {
                waveQty = waveQty + 1;
                sortingBin.setWaveQty(waveQty);
            }
            mdSortingBinMapper.updateById(sortingBin);
        }
    }

    @Override
    @Transactional
    public void subWaveQty(Long id) {
        boolean isAllSortBin = true;// Config.isDefaultFalse(Keys.Delivery.wave_needAllSortBin, Config.ConfigLevel.WAREHOUSE)
        if (isAllSortBin) {
            MdSortingBinDO sortingBin = mdSortingBinMapper.selectById(id);
            Integer waveQty = sortingBin.getWaveQty();
            if (waveQty != null) {
                waveQty = waveQty - 1;
                sortingBin.setWaveQty(waveQty);
            }
            mdSortingBinMapper.updateById(sortingBin);
        }
    }

    @Override
    public Long getIdleSortingBinByType(String sortBinType) {
        Long warehouseId = 1L;// TODO
        return mdSortingBinMapper.selectIdleSortingBinByType(sortBinType, warehouseId);
    }

    @Override
    public Long getSortingBinIdByNo(String sortingBinNo) {
        Long warehouseId = 1L;// TODO
        Long sortingBinId = mdSortingBinMapper.selectSortingBinIdByNo(sortingBinNo, warehouseId);
        if (null == sortingBinId) {
            ExFactory.throwBusiness("分拣柜" + sortingBinNo + "不存在或已停用");
        }
        return sortingBinId;
    }

    @Override
    @Transactional
    public void batchDisable(List<Long> sortingBinIdList) {
        mdSortingBinMapper.updateAvailableStatusByIds(sortingBinIdList);
    }

    @Override
    public Long assignSortBin(String doType, boolean isMinDO, Integer autoWaveType) {
        Long sortingBinId = null;
        //非正常do的订单全仓分配分拣柜
        if (!DoTypeEnum.SELL.getValue().equals(doType)) {
            return getIdleSortingBin();
        }

        if (isMinDO) {//全仓查找超小体积分拣柜
            sortingBinId = getIdleSortingBinByType(SortBinTypeEnum.SMALL.getCode());
        }

        if (AutoWaveTypeEnum.BIG_VOLUME.getValue().equals(autoWaveType)) {
            sortingBinId = getIdleSortingBinByType(SortBinTypeEnum.BIG.getCode());

            if (sortingBinId == null) {
                ExFactory.throwBusiness("不存在大体积分拣柜");
            }
        }

        if (sortingBinId == null) {//全仓查找
            sortingBinId = getIdleSortingBin();
        }
        return sortingBinId;
    }

}
