package com.accesscorporate.app.wms.server.biz.service.selector;

import com.accesscorporate.app.wms.server.biz.params.response.CommonSelectorResponse;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import com.accesscorporate.app.wms.server.dal.entity.Partition;
import com.accesscorporate.app.wms.server.dal.repository.PartitionRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-23 17:47
 * Description: 库区下拉框
 */
@Component
public class PartitionCommonSelectorStrategy extends AbstractCommonSelectorStrategy{
    @Autowired
    private PartitionRepository projectRepository;

    @Override
    protected SelectorTypeEnum getStrategyType() {
        return SelectorTypeEnum.PARTITION_SELECTOR;
    }

    @Override
    protected List<CommonSelectorResponse> queryStrategy() {
        Long currentWarehouseId = UserContextAssistant.getCurrentWarehouseId();
        List<Partition> partitionList = projectRepository.queryPartitionByWarehouseId(currentWarehouseId);
        if (CollectionUtils.isEmpty(partitionList)){
            return List.of();
        }
        return partitionList.stream().map(partition -> {
            CommonSelectorResponse selectorResponse = new CommonSelectorResponse();
            selectorResponse.setKey(partition.getId().toString());
            selectorResponse.setValue(partition.getPartitionCode());
            return selectorResponse;
        }).collect(Collectors.toList());
    }


}
