package com.accesscorporate.app.wms.server.biz.params.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 补货规则明细添加-参数
 *
 * <AUTHOR>
 * 2025/2/19  14:53
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "补货规则明细添加-参数")
public class ReplenishmentRuleDetailAddRequest {

    @Schema(title = "规则ID")
    private Long ruleId;

    @Schema(title = "批次属性")
    private String batchAttr;

    @Schema(title = "顺序")
    private String sortBy;


}
