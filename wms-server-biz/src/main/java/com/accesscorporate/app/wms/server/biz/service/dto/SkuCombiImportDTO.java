package com.accesscorporate.app.wms.server.biz.service.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.idanchuang.component.base.exception.core.asserts.ExBusinessAssert;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR> liudongliang
 * @project : wms-server
 * @company : 浙江单创品牌管理有限公司
 * @desc :
 * Copyright @ 2024 danchuang Technology Co.Ltd. – Confidential and Proprietary
 */
@Data
public class SkuCombiImportDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7872181754597897325L;

    @ExcelProperty("组合条码")
    private String combiBarcode;

    @ExcelProperty("组合品库位")
    private String locCode;

    @ExcelProperty("单品条码")
    private String barcode;

    @ExcelProperty("单品货品等级")
    private String goodsGrade;

    @ExcelProperty("单品数量")
    private Integer total;

    public void checkDataIsEmpty(int rowNum) {
        ExBusinessAssert.isTrue(StringUtils.isNotBlank(combiBarcode), String.format("导入文件第[%d]行,组合条码为空!", rowNum));
        ExBusinessAssert.isTrue(StringUtils.isNotBlank(locCode), String.format("导入文件第[%d]行,组合品库位为空!", rowNum));
        ExBusinessAssert.isTrue(StringUtils.isNotBlank(barcode), String.format("导入文件第[%d]行,单品条码为空!", rowNum));
        ExBusinessAssert.isTrue(StringUtils.isNotBlank(goodsGrade), String.format("导入文件第[%d]行,单品货品等级为空!", rowNum));
        ExBusinessAssert.isTrue(Objects.nonNull(total), String.format("导入文件第[%d]行,单品数量为空!", rowNum));
    }

}
