package com.accesscorporate.app.wms.server.biz.params.request.goodspackage;

import com.idanchuang.component.base.page.PageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-02-20 19:32
 * Description: 商品包装查询
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Tag(name = "商品包装列表查询参数")
public class GoodsPackageQueryRequest extends PageDTO {

    @Schema(title = "商品编码")
    private String productCode;

    @Schema(title = "商品名称")
    private String productCname;

    @Schema(title = "商品条码")
    private String barCode;


}
