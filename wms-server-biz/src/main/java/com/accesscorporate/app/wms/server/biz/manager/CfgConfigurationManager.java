package com.accesscorporate.app.wms.server.biz.manager;

import com.accesscorporate.app.wms.server.dal.entity.CfgConfigurationDO;
import com.accesscorporate.app.wms.server.dal.mapper.CfgConfigurationMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 系统参数配置 Manager
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class CfgConfigurationManager extends ServiceImpl<CfgConfigurationMapper, CfgConfigurationDO> {

    /**
     * 分页查询配置
     * 
     * @param configType 配置类型
     * @param configNo 配置编号
     * @param descrC 配置名称
     * @param configLevel 配置级别
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @param current 当前页
     * @param size 页大小
     * @return 分页结果
     */
    public Page<CfgConfigurationDO> selectPage(String configType, String configNo, String descrC,
                                                Integer configLevel, Long warehouseId, Long tenantId,
                                                Long current, Long size) {
        LambdaQueryWrapper<CfgConfigurationDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(configType), CfgConfigurationDO::getConfigType, configType)
                .like(StringUtils.isNotEmpty(configNo), CfgConfigurationDO::getConfigNo, configNo)
                .like(StringUtils.isNotEmpty(descrC), CfgConfigurationDO::getDescrC, descrC)
                .eq(configLevel != null, CfgConfigurationDO::getConfigLevel, configLevel)
                .eq(warehouseId != null, CfgConfigurationDO::getWarehouseId, warehouseId)
                .eq(tenantId != null, CfgConfigurationDO::getTenantId, tenantId)
                .eq(CfgConfigurationDO::getIsDeleted, 0)
                .orderByDesc(CfgConfigurationDO::getUpdateTime);
        
        return page(new Page<>(current, size), wrapper);
    }

    /**
     * 根据配置编号和仓库ID查询配置
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @return 配置信息
     */
    public CfgConfigurationDO getByConfigNoAndWarehouseId(String configNo, Long warehouseId) {
        return baseMapper.selectByConfigNoAndWarehouseId(configNo, warehouseId);
    }

    /**
     * 根据配置编号和租户ID查询配置
     * 
     * @param configNo 配置编号
     * @param tenantId 租户ID
     * @return 配置信息
     */
    public CfgConfigurationDO getByConfigNoAndTenantId(String configNo, Long tenantId) {
        return baseMapper.selectByConfigNoAndTenantId(configNo, tenantId);
    }

    /**
     * 根据配置编号查询全局配置
     * 
     * @param configNo 配置编号
     * @return 配置信息
     */
    public CfgConfigurationDO getGlobalByConfigNo(String configNo) {
        return baseMapper.selectGlobalByConfigNo(configNo);
    }

    /**
     * 根据配置级别查询配置列表
     * 
     * @param configLevel 配置级别
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置列表
     */
    public List<CfgConfigurationDO> listByConfigLevel(Integer configLevel, Long warehouseId, Long tenantId) {
        return baseMapper.selectByConfigLevel(configLevel, warehouseId, tenantId);
    }

    /**
     * 根据配置类型查询配置列表
     * 
     * @param configType 配置类型
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置列表
     */
    public List<CfgConfigurationDO> listByConfigType(String configType, Long warehouseId, Long tenantId) {
        return baseMapper.selectByConfigType(configType, warehouseId, tenantId);
    }

    /**
     * 根据一级分类查询配置列表
     * 
     * @param levelOne 一级分类
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置列表
     */
    public List<CfgConfigurationDO> listByLevelOne(String levelOne, Long warehouseId, Long tenantId) {
        return baseMapper.selectByLevelOne(levelOne, warehouseId, tenantId);
    }

    /**
     * 根据二级分类查询配置列表
     * 
     * @param levelTwo 二级分类
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置列表
     */
    public List<CfgConfigurationDO> listByLevelTwo(String levelTwo, Long warehouseId, Long tenantId) {
        return baseMapper.selectByLevelTwo(levelTwo, warehouseId, tenantId);
    }

    /**
     * 根据配置标签查询配置列表
     * 
     * @param configTag1 配置标签1
     * @param configTag2 配置标签2
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置列表
     */
    public List<CfgConfigurationDO> listByConfigTags(String configTag1, String configTag2, 
                                                      Long warehouseId, Long tenantId) {
        return baseMapper.selectByConfigTags(configTag1, configTag2, warehouseId, tenantId);
    }

    /**
     * 检查配置编号是否存在
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    public boolean existsByConfigNoAndWarehouseId(String configNo, Long warehouseId, Long excludeId) {
        int count = baseMapper.countByConfigNoAndWarehouseId(configNo, warehouseId, excludeId);
        return count > 0;
    }

    /**
     * 批量插入配置
     * 
     * @param configList 配置列表
     * @return 插入数量
     */
    public int batchInsert(List<CfgConfigurationDO> configList) {
        return baseMapper.batchInsert(configList);
    }

    /**
     * 创建配置
     * 
     * @param config 配置信息
     * @return 是否成功
     */
    public Boolean createConfig(CfgConfigurationDO config) {
        return save(config);
    }

    /**
     * 更新配置
     * 
     * @param config 配置信息
     * @return 是否成功
     */
    public Boolean updateConfig(CfgConfigurationDO config) {
        return updateById(config);
    }

    /**
     * 删除配置（逻辑删除）
     * 
     * @param id 配置ID
     * @return 是否成功
     */
    public Boolean deleteConfig(Long id) {
        CfgConfigurationDO config = new CfgConfigurationDO();
        config.setId(id);
        config.setIsDeleted(1);
        return updateById(config);
    }

    /**
     * 根据ID查询配置
     * 
     * @param id 配置ID
     * @return 配置信息
     */
    public CfgConfigurationDO selectById(Long id) {
        return getById(id);
    }

    /**
     * 根据多个条件查询配置
     * 支持优先级查询：仓库级别 > 租户级别 > 全局级别
     * 
     * @param configNo 配置编号
     * @param warehouseId 仓库ID
     * @param tenantId 租户ID
     * @return 配置信息
     */
    public CfgConfigurationDO getConfigWithPriority(String configNo, Long warehouseId, Long tenantId) {
        // 优先查询仓库级别配置
        if (warehouseId != null) {
            CfgConfigurationDO config = getByConfigNoAndWarehouseId(configNo, warehouseId);
            if (config != null && config.getId() != null) {
                return config;
            }
        }
        
        // 其次查询租户级别配置
        if (tenantId != null) {
            CfgConfigurationDO config = getByConfigNoAndTenantId(configNo, tenantId);
            if (config != null && config.getId() != null) {
                return config;
            }
        }
        
        // 最后查询全局配置
        return getGlobalByConfigNo(configNo);
    }
}
