package com.accesscorporate.app.wms.server.biz.params.request.pickloc;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-24 16:14
 * Description: 保存商品捡货位请求参数
 */
@Tag(name = "保存商品捡货位请求参数")
@Data
public class PickLocSaveRequest {

    @Schema(title = "商品捡货位记录行主键id，编辑场景下有值")
    private Long pickLocId;

    @Schema(title = "商品编码")
    @NotBlank(message = "商品编码不能为空")
    private String productCode;

    @Schema(title = "库位编码")
    @NotBlank(message = "库位编码不能为空")
    private String locCode;

    @Schema(title = "补货单位")
    @NotBlank(message = "补货单位不能为空")
    private String uom;

    @Schema(title = "补货上限")
    @NotNull(message = "补货上限不能为空")
    @Min(value = 1,message = "补货上限最小值为1")
    private Integer upLimit;

    @Schema(title = "补货下限")
    @NotNull(message = "补货下限不能为空")
    @Min(value = 1,message = "补货下限最小值为1")
    private Integer lowerLimit;

    @Schema(title = "最小补货量")
    @NotNull(message = "最小补货量不能为空")
    @Min(value = 0,message = "最小补货量最小值为0")
    private Integer minimumRplQty;





}
