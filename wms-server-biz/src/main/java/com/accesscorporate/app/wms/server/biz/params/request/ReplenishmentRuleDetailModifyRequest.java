package com.accesscorporate.app.wms.server.biz.params.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 补货规则明细修改保存-参数
 *
 * <AUTHOR>
 * 2025/2/21  11:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "补货规则明细修改保存-参数")
public class ReplenishmentRuleDetailModifyRequest {

    @Schema(title = "明细ID")
    private Long id;

    @Schema(title = "批次属性")
    private String batchAttr;

    @Schema(title = "顺序")
    private String sortBy;


}
