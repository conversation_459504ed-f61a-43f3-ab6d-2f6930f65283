package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.converter.ReplenishmentRuleConverter;
import com.accesscorporate.app.wms.server.biz.manager.RotationRuleDetailManager;
import com.accesscorporate.app.wms.server.biz.manager.RotationRuleHeaderManager;
import com.accesscorporate.app.wms.server.biz.params.request.*;
import com.accesscorporate.app.wms.server.biz.params.response.ReplenishmentRuleDetailResponse;
import com.accesscorporate.app.wms.server.biz.params.response.ReplenishmentRuleResponse;
import com.accesscorporate.app.wms.server.biz.service.IReplenishmentRuleService;
import com.accesscorporate.app.wms.server.dal.entity.RotationRuleDetailDO;
import com.accesscorporate.app.wms.server.dal.entity.RotationRuleHeaderDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.page.PageData;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;

/**
 * 补货规则-Service-Impl
 *
 * <AUTHOR>
 * 2025/2/19  13:46
 */
@Service
@RequiredArgsConstructor
public class ReplenishmentRuleServiceImpl implements IReplenishmentRuleService {

    private final RotationRuleHeaderManager rotationRuleHeaderManager;
    private final RotationRuleDetailManager rotationRuleDetailManager;
    private final TransactionTemplate transactionTemplate;

    @Override
    public Boolean createReplenishmentRule(ReplenishmentRuleCreateRequest request) {
        // 规则编码校验
        assert rotationRuleHeaderManager.queryRulesByCode(request.getRuleCode()).isEmpty() : "规则编码已存在!";
        RotationRuleHeaderDO rotationRuleHeaderDO = ReplenishmentRuleConverter.toRotationRuleHeaderDO(request);
        return rotationRuleHeaderManager.save(rotationRuleHeaderDO);
    }

    @Override
    public Boolean modifyReplenishmentRule(ReplenishmentRuleModifyRequest request) {
        // 规则编码校验
        assert rotationRuleHeaderManager.queryRulesByCode(request.getRuleCode()).isEmpty() : "规则编码已存在!";
        RotationRuleHeaderDO rotationRuleHeaderDO = ReplenishmentRuleConverter.toRotationRuleHeaderDO(request);
        return rotationRuleHeaderManager.updateById(rotationRuleHeaderDO);
    }

    @Override
    public Boolean removeReplenishmentRule(Long id) {
        // 规则 & 明细 「事务执行」
        transactionTemplate.execute((x) -> {
            rotationRuleHeaderManager.logicDeleteById(id);
            rotationRuleDetailManager.logicDeleteByRuleId(id);
            return true;
        });
        return true;
    }

    @Override
    public PageData<ReplenishmentRuleResponse> queryReplenishmentRules(ReplenishmentRuleQueryRequest request) {
        Page<RotationRuleHeaderDO> pageDO = rotationRuleHeaderManager.queryRulePage(request.getRuleCode(), request.getDescription(), request.getCurrent(), request.getSize());
        List<ReplenishmentRuleResponse> resRecords = pageDO.getRecords()
                .stream()
                .map(ReplenishmentRuleConverter::toReplenishmentRuleResponse)
                .toList();

        return PageData.of(resRecords, request.getCurrent(), request.getSize(), pageDO.getTotal());
    }

    @Override
    public List<ReplenishmentRuleDetailResponse> queryRuleDetails(Long ruleId) {
        return rotationRuleDetailManager.queryByRuleId(ruleId)
                .stream()
                .map(ReplenishmentRuleConverter::toReplenishmentRuleDetailResponse)
                .toList();

    }

    @Override
    public Boolean removeReplenishmentRuleDetail(Long detailId) {
        return rotationRuleDetailManager.logicDeleteById(detailId);
    }

    @Override
    public Boolean createReplenishmentRuleDetail(ReplenishmentRuleDetailAddRequest request) {
        RotationRuleDetailDO rotationRuleDetailDO = ReplenishmentRuleConverter.toRotationRuleDetailDO(request);
        return rotationRuleDetailManager.save(rotationRuleDetailDO);
    }

    @Override
    public Boolean modifyReplenishmentRuleDetail(ReplenishmentRuleDetailModifyRequest request) {
        return rotationRuleDetailManager.updateFieldsById(request.getId(), request.getBatchAttr(), request.getSortBy());
    }
}
