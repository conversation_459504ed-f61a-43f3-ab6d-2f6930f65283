package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.service.WarehouseService;
import com.accesscorporate.app.wms.server.dal.entity.Warehouse;
import com.accesscorporate.app.wms.server.dal.mapper.WarehouseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

@Service
public class WarehouseServiceImpl extends ServiceImpl<WarehouseMapper, Warehouse> implements WarehouseService {
    // 可以重写自定义方法
}