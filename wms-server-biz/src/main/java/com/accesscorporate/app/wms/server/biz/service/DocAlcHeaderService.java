package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.request.DocAlcHeaderSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocAlcHeaderResponse;
import com.accesscorporate.app.wms.server.dal.entity.DocAlcHeader;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface DocAlcHeaderService extends IService<DocAlcHeader> {
    List<DocAlcHeaderResponse> listAll();

    DocAlcHeaderResponse get(Long id);

    boolean save(DocAlcHeaderSaveRequest saveRequest);

    boolean removeById(Long id);

    Page<DocAlcHeaderResponse> page(Integer current, Integer size, String doNo, String status, String doType, String consigneeName);

    DocAlcHeader queryByDoNo(String doNo);
}
