package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.request.PrinterFilter;
import com.accesscorporate.app.wms.server.biz.service.dto.PrintDTO;
import com.idanchuang.component.base.page.PageData;

import java.util.List;

public interface PrintService {

    PrintDTO queryById(Long id);

    List<PrintDTO> selectAll();

    int insert(PrintDTO printDTO);

    int updateById(PrintDTO printDTO);

    // 新增补全的方法
    PageData<PrintDTO> query(PrinterFilter filter, int startIndex, int pageSize);

    PrintDTO get(Long id);

    void saveOrUpdate(PrintDTO printDTO);

    void remove(Long id);

    boolean isExists(PrintDTO printDTO);

    List<PrintDTO> getAvailablePrintersByType(String type);

    PrintDTO findPrinterByCode(String printerCode);

    int deleteByPrimaryKey(Long id);
}