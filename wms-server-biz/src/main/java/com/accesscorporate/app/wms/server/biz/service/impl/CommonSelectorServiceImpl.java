package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.params.response.CommonSelectorResponse;
import com.accesscorporate.app.wms.server.biz.service.CommonSelectorService;
import com.accesscorporate.app.wms.server.biz.service.selector.AbstractCommonSelectorStrategy;
import com.accesscorporate.app.wms.server.biz.service.selector.CommonSelectorStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-23 16:57
 * Description:
 */
@Service
@Slf4j
public class CommonSelectorServiceImpl implements CommonSelectorService {

    @Autowired
    private CommonSelectorStrategyFactory commonSelectorStrategyFactory;

    @Override
    public List<CommonSelectorResponse> queryCommonSelectors(String selectorType) {
        try {
            AbstractCommonSelectorStrategy queryStrategy = commonSelectorStrategyFactory.getQueryStrategy(selectorType);
            return queryStrategy.executeQuery();
        } catch (Exception e) {
            log.error("查询下拉框列表异常,下拉选择:{},异常信息:", selectorType, e);
            return List.of();
        }
    }


}
