package com.accesscorporate.app.wms.server.biz.params.response;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "订单分配头响应体")
public class DocAlcHeaderResponse {
    private Long id; // ID
    private String doNo; // 发货单号
    private String status; // 订单状态
    private LocalDateTime shipTime; // 发运时间
    private String doType; // 订单类型
    private String releaseStatus; // 订单冻结标识
    private Integer priority; // 优先级
    private String refNo1; // 参考编号1
    private String refNo2; // 参考编号2
    private String postCode; // 邮编
    private Integer specFlag; // 半日达普通/半日达大件/普通/普通大件/团购/一日三送/自提
    private Integer invoiceFlag; // 有无发票
    private BigDecimal expectedQty; // 订货数量
    private BigDecimal shipQty; // 发货数量
    private BigDecimal expectedQtyEach; // 订货数量
    private BigDecimal shipQtyEach; // 发货数量
    private BigDecimal grossWt; // 毛重
    private BigDecimal volume; // 体积
    private String country; // 国家
    private Integer province; // 省
    private Integer city; // 市
    private Integer county; // 县
    private Integer disctrict; // 区
    private String consigneeName; // 收货方
    private String address; // 收货地址
    private String contact; // 联系人
    private String telephone; // 电话
    private String mobile; // 手机
    private String email; // 邮件
    private Long carrierId; // 配送公司ID
    private String trackingNo; // 运单号
    private Integer invoiceQty; // 发票数量
    private String paymentMethodName; // 支付方式
    private Integer paymentType; // 支付类型
    private Integer packedQty; // 装箱数量
    private LocalDateTime doCreateTime; // DO创建时间
    private LocalDateTime expectedArriveTime1; // 期望配送时间1
    private LocalDateTime expectedArriveTime2; // 期望配送时间2
    private LocalDateTime pkTimeStart; // 拣货开始时间
    private LocalDateTime pkTimeEnd; // 拣货完成时间
    private LocalDateTime packTimeStart; // 包装开始时间
    private LocalDateTime packTimeEnd; // 包装完成时间
    private LocalDateTime sortTime; // 分拣时间
    private LocalDateTime allocTime; // 分配时间
    private LocalDateTime cancelTime; // 取消时间
    private Integer waveFlag; // 是否已经生成波次
    private Long waveId; // 波次ID
    private Integer sortGridNo; // 分拣格ID
    private String holdWho; // 冻结人
    private LocalDateTime holdTime; // 冻结时间
    private String holdCode; // 冻结原因代码
    private String holdReason; // 冻结原因
    private String userdefine1; // 用户自定义1
    private String userdefine2; // 用户自定义2
    private String userdefine3; // 用户自定义3
    private String userdefine4; // 用户自定义4
    private String userdefine5; // 用户自定义5
    private String userdefine6; // 用户自定义6
    private String notes; // 备注
    private String edi1; // EDI_1
    private String edi2; // EDI_2
    private Integer needCancel; // 是否需要取消
    private String expectedReceiveTime; // 期望收货时间
    private Integer isHalfDayDelivery; // 是否半日达
    private Integer printNum; // 发货单打印份数
    private BigDecimal orderAmount; // 订单总额
    private BigDecimal productAmount; // 货品总额
    private BigDecimal accountPayable; // 已收款
    private BigDecimal orderFrostRebate; // 总返利金额
    private BigDecimal orderDeliveryFee; // 运费
    private Integer thirdPartyBill; // 是否要代收货款
    private BigDecimal receivable; // 应收款
    private Integer exchangeFlag; // 换货标识
    private Long stationId; // 配送站ID
    private Integer ediSendFlag1; // 订单同步标记（Backend）
    private Integer ediSendFlag2; // 订单同步标记（TMS）
    private Integer ediSendFlag3; // 订单同步标记（BACKEND UNLOCK）
    private String origId; // 原始ID
    private String paymentMethod; // 支付方式
    private Integer replStatus; // 补货状态
    private Long supplierId; // 供应商ID
    private BigDecimal totalGrossWt; // 称重毛重
    private Integer cycleClass; // 商品等级
    private Integer ordersource; // 订单来源
    private Integer exceptionStatus; // 异常状态
    private Integer displayPrice; // 打印时是否显示金额
    private Integer tranType; // 调拨类型
    private Integer isFresh; // 生鲜标识
    private Integer isValuable; // 贵重标识
    private Long allocateFailNum; // 订单自动分配失败次数
    private LocalDateTime doFinishTime; // 首次预计出库时间
    private Integer flowFlag; // 流程引擎
    private String lastDcName; // 销售商家
    private Integer inWine; // 是否包含酒类随附单
    private String aisles; // 行
    private LocalDateTime planShipTime; // 预计出库时间
    private LocalDateTime planShipTimeEnd; // 预计出库时间止
    private LocalDateTime planShipTimeStd; // 标准预计出库时间
    private Integer checkFlag; // 登记标识
    private Integer isGroup; // 团购订单标识
    private Integer isAutoWave; // 是否自动波次标识
    private String partitionCodes; // 分配任务库区集合
    private String partitionCategory; // 分区类别
    private LocalDateTime replStartTime; // 补货开始时间
    private LocalDateTime replEndTime; // 补货完成时间
    private Integer waveFailNum; // 波次失败次数
    private String stationName; // 3PL站点名称
    private Integer haveCfy; // 是否有处方药
    private Integer needCancelAlloc; // 是否需要取消分配
    private Integer cancelAllocFailNum; // 自动取消分配失败次数
    private Long shopId; // 店铺ID
    private Integer volumeType; // 体积类型
    private Integer noStockFlag; // 无库存标识
    private Long businessCustomerId; // 企业客户ID
    private String originalSoCode; // 原始订单编码
    private String sellerRemark; // 卖家备注
    private String buyerRemark; // 买家备注
    private LocalDateTime loadConfimTime; // 装载确认时间
    private String provinceName; // 省份中文
    private String cityName; // 市地址中文
    private String countyName; // 区地址中文
    private BigDecimal pickedQtyEach; // 拣货数量
    private BigDecimal needReplQtyUnit; // 需要补货箱数
    private BigDecimal needReplQtyPcs; // 需要补货件数
    private Integer ignoreExpiryDate; // 忽略失效日期
    private String orderSubType; // 订单子类型
    private Integer lotStrategy; // 批次策略
    private Integer skuQty; // SKU数量
    private LocalDateTime payTime; // 支付时间
    private Integer isDirect; // 是否直发
    private Long merchantId; // 商家ID
    private Long replRegionId; // 补货区域ID
    private String similaritySign; // 相似度密文
    private String channelCode; // 渠道编码
    private Integer transportWendy; // 运输温度
}
