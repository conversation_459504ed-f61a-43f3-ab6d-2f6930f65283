package com.accesscorporate.app.wms.server.biz.params.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 补货规则-明细-响应体
 *
 * <AUTHOR>
 * 2025/2/19  09:44
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "补货规则明细 View Object")
public class ReplenishmentRuleDetailResponse {

    @Schema(title = "明细id")
    private Long id;

    @Schema(title = "批次属性")
    private String batchAttr;

    @Schema(title = "顺序")
    private String sortBy;

}
