package com.accesscorporate.app.wms.server.biz.service.impl;

import com.abmau.service.open.warehouse.api.client.IWmsAsnOrderReceiveClient;
import com.abmau.service.open.warehouse.api.param.dto.request.callback.awms.WmsAsnOrderReceiveRequestDTO;
import com.abmau.service.open.warehouse.api.param.dto.response.WmsAsnOrderReceiveResponseDTO;
import com.accesscorporate.app.wms.server.biz.service.ExpAsnHeaderService;
import com.accesscorporate.app.wms.server.biz.service.Recheck2OmsExpService;
import com.accesscorporate.app.wms.server.biz.service.SExpSrvLogService;
import com.accesscorporate.app.wms.server.biz.service.dto.ExpSrvDTO;
import com.accesscorporate.app.wms.server.common.enums.FlagEnum;
import com.accesscorporate.app.wms.server.common.enums.LogStatusEnum;
import com.accesscorporate.app.wms.server.dal.entity.ExpAsnHeaderDO;
import com.accesscorporate.app.wms.server.dal.entity.SExpSrvLogDO;
import com.accesscorporate.app.wms.server.dal.entity.SExpSrvMsgDO;
import com.accesscorporate.app.wms.server.dal.utils.ExpSrvUtil;
import com.alibaba.fastjson.JSON;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.exception.core.ExFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * 入库单签收通知
 * 涉及：
 * - 采购入库
 * - 调拨入库
 */
@Slf4j
@Service
public class Recheck2OmsExpServiceImpl implements Recheck2OmsExpService {

    @Resource
    private ExpAsnHeaderService expAsnHeaderService;
    @Resource
    private IWmsAsnOrderReceiveClient iWmsAsnOrderReceiveClient;
    @Resource
    private SExpSrvLogService sExpSrvLogService;

    @Override
    public void msgJobSend(SExpSrvMsgDO srvMsg, SExpSrvLogDO serviceLog) {
        process(srvMsg, serviceLog, true);
    }

    public void process(SExpSrvMsgDO srvMsg, SExpSrvLogDO serviceLog, boolean retryP) {
        ExpSrvDTO expSrvDTO = this.getExpSrvDTO(srvMsg, serviceLog);
        try {
            Integer retry = retryP ? FlagEnum.LOG_RETRY.getValue() : FlagEnum.MSG_JOB_RETRY.getValue();
            if (serviceLog == null) {
                serviceLog = createLog(expSrvDTO, retry);
            } else {
                newReq(serviceLog);
            }

            ExpAsnHeaderDO expAsnHeaderDO = expAsnHeaderService.getByIdAndWarehouseId(expSrvDTO.getRefId(), expSrvDTO.getWarehouseId());
            WmsAsnOrderReceiveRequestDTO requestDTO = new WmsAsnOrderReceiveRequestDTO();
            requestDTO.setOrderCode(expAsnHeaderDO.getAsnNo());
            String signTime = expAsnHeaderDO.getSignTime();//签收时间
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            long receiveDate = StringUtils.isNotBlank(signTime) ?
                    LocalDateTime.parse(signTime, formatter)
                            .atZone(ZoneId.systemDefault())
                            .toInstant().toEpochMilli() :
                    System.currentTimeMillis();
            requestDTO.setReceiveDate(receiveDate);
            requestDTO.setWarehouseId(String.valueOf(expAsnHeaderDO.getWarehouseId()));

            // 调用ZKT接口
            JsonResult<WmsAsnOrderReceiveResponseDTO> jsonResult = iWmsAsnOrderReceiveClient.doWmsAsnOrderReceive(requestDTO).mustSuccess();
            if (jsonResult.isSuccess()) {
                // 处理成功
                log.info("调用ZKT接口成功,request:{}", JSON.toJSONString(requestDTO));
            } else {
                // 处理失败
                log.warn("调用ZKT接口失败: {},request:{}", jsonResult.getMsg(), JSON.toJSONString(requestDTO));
            }
        } catch (Exception e) {
            if (null != serviceLog) {
                serviceLog.setResultMsg(e.getMessage());
                serviceLog.setStatus(LogStatusEnum.SYS_ERR.getValue());
                serviceLog.setResultCause(ExceptionUtils.getStackTrace(e) + "\n" + (e.getCause() == null ? "" : ExceptionUtils.getStackTrace(e.getCause())));
                log.error("asn2OmsExpSrvXxlJob任务执行异常", e);
                ExFactory.throwBusiness("asn2OmsExpSrvXxlJob任务执行异常", e);
            }
        } finally {
            if (null != serviceLog) {
                // TODO
                logHis(serviceLog);
                sExpSrvLogService.updateById(serviceLog);
            }
        }
    }

    public void logHis(SExpSrvLogDO serviceLog) {
        serviceLog.setInvokeCount(serviceLog.getInvokeCount() + 1);
        StringBuffer sb = new StringBuffer(null == serviceLog.getInvokeHis() ? "" : serviceLog.getInvokeHis()).append(serviceLog.getInvokeCount()).append(",")
                .append(this.dateToString(serviceLog.getInvokeTime(), "yyyy-MM-dd HH:mm:ss.SSSZ")).append(",")
                .append(serviceLog.getReqBase() + serviceLog.getReqPath()).append(",").append(serviceLog.getStatus()).append(",")
                .append(serviceLog.getInvokeCost()).append(",").append(serviceLog.getReqCost()).append(",").append(Integer.toBinaryString(serviceLog.getFlag()))
                .append("\r\n");

        serviceLog.setInvokeHis(sb.toString());
        serviceLog.setTotalInvokeCost(serviceLog.getTotalInvokeCost() + serviceLog.getInvokeCost());
        serviceLog.setTotalReqCost(serviceLog.getTotalReqCost() + serviceLog.getReqCost());
    }

    /** 根据时间变量返回时间字符串
     * @return 返回时间字符串
     * @param pattern 时间字符串样式
     * @param date 时间变量
     */
    public String dateToString(Date date, String pattern) {

        if (date == null) {

            return null;
        }

        try {

            SimpleDateFormat sfDate = new SimpleDateFormat(pattern);
            sfDate.setLenient(false);

            return sfDate.format(date);
        } catch (Exception e) {

            return null;
        }
    }

    protected SExpSrvLogDO createLog(ExpSrvDTO expSrvDTO, Integer retry) throws Exception {
        SExpSrvLogDO serviceLog = new SExpSrvLogDO();
        // serviceLog.setExpName(ExpServiceNames.RECHECK_OMS);
        serviceLog.setRefId(expSrvDTO.getRefId());
        serviceLog.setRefNo(expSrvDTO.getRefNo());
        serviceLog.setExpArgs(expSrvDTO.getExpArgs());
        serviceLog.setSysId(expSrvDTO.getWarehouseId() != null ? expSrvDTO.getWarehouseId().intValue() : null);// 仓ID
        serviceLog.setFlag(retry);
        serviceLog.setCreateTime(new Date());
        newReq(serviceLog);
        return serviceLog;
    }

    public void newReq(SExpSrvLogDO serviceLog) {
        serviceLog.setStatus(LogStatusEnum.INIT.getValue());
        serviceLog.setInvokeTime(Calendar.getInstance().getTime());
        serviceLog.setResultMsg(null);
        serviceLog.setResultCause(null);
    }


    public ExpSrvDTO getExpSrvDTO(SExpSrvMsgDO srvMsg, SExpSrvLogDO serviceLog) {
        ExpSrvDTO expSrvDTO = new ExpSrvDTO();
        // expSrvDTO.setExpName(ExpServiceNames.RECHECK_OMS);
        if (null != srvMsg) {
            String[] argsT = ExpSrvUtil.decodeArgs(srvMsg.getExpArgs(), 3);
            expSrvDTO.setRefId(srvMsg.getRefId());
            expSrvDTO.setRefNo(srvMsg.getRefNo());
            expSrvDTO.setExpArgs(srvMsg.getExpArgs());
            expSrvDTO.setWarehouseId(srvMsg.getWarehouseId());
            expSrvDTO.setSourceSyStem(argsT[2]);
        } else {
            String[] argsT = ExpSrvUtil.decodeArgs(serviceLog.getExpArgs(), 3);
            expSrvDTO.setRefId(serviceLog.getRefId());
            expSrvDTO.setRefNo(serviceLog.getRefNo());
            expSrvDTO.setExpArgs(serviceLog.getExpArgs());
            expSrvDTO.setWarehouseId(serviceLog.getSysId() != null ? Long.valueOf(serviceLog.getSysId()) : null);
            expSrvDTO.setSourceSyStem(argsT[2]);
        }
        return expSrvDTO;
    }

}
