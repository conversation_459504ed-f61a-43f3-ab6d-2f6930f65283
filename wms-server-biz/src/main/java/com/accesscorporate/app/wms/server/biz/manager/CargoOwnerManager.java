package com.accesscorporate.app.wms.server.biz.manager;

import com.accesscorporate.app.wms.server.dal.entity.CargoOwnerDO;
import com.accesscorporate.app.wms.server.dal.mapper.ICargoOwnerMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.idanchuang.component.core.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 货主-数据聚合层-manager
 *
 * <AUTHOR>
 * 2025/2/8  16:34
 */
@Component
@RequiredArgsConstructor
public class CargoOwnerManager extends ServiceImpl<ICargoOwnerMapper, CargoOwnerDO> {

    public Page<CargoOwnerDO> queryCargoOwnerPage(String cargoOwnerCode,
                                                   String cargoOwnerName,
                                                   Long current,
                                                   Long size) {

        return lambdaQuery()
                .eq(StringUtil.isNotBlank(cargoOwnerCode), CargoOwnerDO::getMerchantCode, cargoOwnerCode)
                .like(StringUtil.isNotBlank(cargoOwnerName), CargoOwnerDO::getDescrC, cargoOwnerName)
                .eq(CargoOwnerDO::getIsDeleted, 0)
                .page(new Page<>(current, size));
    }





}
