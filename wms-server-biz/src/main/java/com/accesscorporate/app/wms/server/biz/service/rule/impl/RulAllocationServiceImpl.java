package com.accesscorporate.app.wms.server.biz.service.rule.impl;

import com.accesscorporate.app.wms.server.biz.converter.RulAllocationConverter;
import com.accesscorporate.app.wms.server.biz.manager.rule.RulAllocationManager;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulAllocationResponse;
import com.accesscorporate.app.wms.server.biz.service.rule.IRulAllocationService;
import com.accesscorporate.app.wms.server.dal.entity.rule.RulAllocationDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.page.PageData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RulAllocationServiceImpl implements IRulAllocationService {

    private final RulAllocationManager rulAllocationManager;


    @Override
    public PageData<RulAllocationResponse> queryPage(RulAllocationQueryPageRequest request) {
        Page<RulAllocationDO> page = rulAllocationManager.selectPage(request.getAllocationCode(),
                request.getAllocationDescr(),
                request.getCurrent(),
                request.getSize());

        List<RulAllocationResponse> responses = page.getRecords().stream().map(RulAllocationConverter::toRulAllocationResponse).toList();

        return PageData.of(
                responses,
                request.getCurrent(),
                request.getSize(),
                page.getTotal());
    }

    @Override
    public RulAllocationResponse queryById(Long id) {
        RulAllocationDO rulAllocationDO = rulAllocationManager.selectById(id);
        if (rulAllocationDO == null) {
            return null;
        }
        RulAllocationResponse rulAllocationResponse = RulAllocationConverter.toRulAllocationResponse(rulAllocationDO);
        return rulAllocationResponse;
    }

    @Override
    public Boolean save(RulAllocationRequest request) {
        RulAllocationDO rulAllocationDO = RulAllocationConverter.toRulAllocationDO(request);
        if (rulAllocationDO.getId() != null) {
            return rulAllocationManager.updateRulAllocation(rulAllocationDO);
        }
        return rulAllocationManager.createRulAllocation(rulAllocationDO);
    }

    @Override
    public Boolean deleteById(Long id) {
        return rulAllocationManager.deleteRulAllocation(id);
    }
}
