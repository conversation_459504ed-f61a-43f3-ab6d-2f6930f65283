package com.accesscorporate.app.wms.server.biz.client.impl;

import com.accesscorporate.app.wms.server.api.client.SampleMappingFeignClient;
import com.accesscorporate.app.wms.server.api.dto.MessageDTO;
import com.idanchuang.component.base.JsonResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 服务提供者对RPC接口实现样例
 *
 * 1.Rpc接口实现类名需要遵守 XxxFeignClientImpl 的规范
 * 2.如果类上添加了@RequestMapping注册, 那么接口的@FeignClient注解上需要配置对应的path
 *
 * <AUTHOR>
 * Created at 2020/5/14 12:59
 **/
@RestController
@RequestMapping("/requestMapping")
public class SampleMappingFeignClientImpl implements SampleMappingFeignClient {

    @Override
    public JsonResult<String> hello(MessageDTO msg) {
        return JsonResult.success(msg.getTitle());
    }

}
