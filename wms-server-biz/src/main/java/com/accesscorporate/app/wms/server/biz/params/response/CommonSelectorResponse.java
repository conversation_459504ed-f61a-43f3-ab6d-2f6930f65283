package com.accesscorporate.app.wms.server.biz.params.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-23 16:33
 * Description: 公共下拉框内容响应
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonSelectorResponse {

    @Schema(title = "key")
    private String key;

    @Schema(title = "value")
    private String value;

}
