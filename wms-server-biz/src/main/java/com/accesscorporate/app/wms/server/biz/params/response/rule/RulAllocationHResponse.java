package com.accesscorporate.app.wms.server.biz.params.response.rule;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version JDK 17
 * @date 2025/2/26
 * @description
 */
@Data
@Accessors(chain = true)
public class RulAllocationHResponse {


    private Long id;

    /**
     * 序号
     */
    private Long lineno;

    /**
     * 分配规则id
     */
    private Long allocationId;

    /**
     * 规则描述
     */
    private String allocationDescr;

    /**
     * 订单类型
     */
    private String ordertype;

    /**
     * 是否优先分配RETURN库区库存，0：不优先分配，1：优先分配
     */
    private Integer partitionCtrl;

    /**
     * 是否允许分配存储库区库存，0：不允许，1：允许
     */
    private Integer storageCtrl;

    /**
     * 调拨类型，0：主动调拨，1：被动调拨
     */
    private Integer allotType;

    /**
     * 库存策略 1清空库位优先 2最少拣货点，满足订单需求数优先
     */
    private Integer stockStrategy;

    /**
     * 是否支持存储位
     */
    private Integer supportRs;

    /**
     * 是否效期控制
     */
    private Integer expireControl;

}
