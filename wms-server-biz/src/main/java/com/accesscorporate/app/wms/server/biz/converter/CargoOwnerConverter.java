package com.accesscorporate.app.wms.server.biz.converter;

import com.accesscorporate.app.wms.server.biz.excel.model.CargoOwnerImportModel;
import com.accesscorporate.app.wms.server.biz.params.response.CargoOwnerInfoResponse;
import com.accesscorporate.app.wms.server.dal.entity.CargoOwnerDO;

/**
 * 货主管理-对象-转换器
 *
 * <AUTHOR>
 * 2025/2/8  17:25
 */
public class CargoOwnerConverter {


    public static CargoOwnerInfoResponse toCargoOwnerInfoResponse(CargoOwnerDO cargoOwnerDO) {
        return new CargoOwnerInfoResponse()
                .setId(cargoOwnerDO.getId())
                .setCargoOwnerCode(cargoOwnerDO.getMerchantCode())
                .setCargoOwnerName(cargoOwnerDO.getDescrC());
    }

    public static CargoOwnerDO toCargoOwnerDO(CargoOwnerImportModel cargoOwnerImportModel) {
        return new CargoOwnerDO()
                .setMerchantCode(cargoOwnerImportModel.getCargoOwnerCode())
                .setDescrC(cargoOwnerImportModel.getCargoOwnerName())
                .setDescrE(cargoOwnerImportModel.getCargoOwnerEnName())
                .setContactEmail(cargoOwnerImportModel.getContactEmail())
                .setContactTel(cargoOwnerImportModel.getContactTel())
                .setContactName(cargoOwnerImportModel.getContactName())
                .setNote(cargoOwnerImportModel.getNote())
                .setContactFax(cargoOwnerImportModel.getContactFax())
                .setContactMo(cargoOwnerImportModel.getContactMo())
                .setPostcode(cargoOwnerImportModel.getPostcode())
                .setAddress(cargoOwnerImportModel.getAddress())
                .setTaxPayerName(cargoOwnerImportModel.getTaxPayerName())
                .setInvoiceInterfaceUrl(cargoOwnerImportModel.getInvoiceInterfaceUrl())
                .setInvoicePlatformCode(cargoOwnerImportModel.getInvoicePlatformCode())
                .setTaxPayerCode(cargoOwnerImportModel.getTaxPayerCode());
    }



}
