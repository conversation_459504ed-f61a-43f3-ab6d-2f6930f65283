package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.request.MdSortingBinQueryRequest;
import com.accesscorporate.app.wms.server.biz.service.dto.MdSortingBinDTO;
import com.idanchuang.component.base.page.PageData;

import java.util.List;


/**
 * MdSortingBinService 接口
 * 分拣柜管理
 *
 * <AUTHOR>
 */
public interface MdSortingBinService {

    /**
     * 根据主键ID查询MdSortingBinDO对象
     *
     * @param id 主键ID
     * @return MdSortingBinDO对象
     */
    MdSortingBinDTO selectById(Long id);

    /**
     * 根据条件查询MdSortingBinDO对象列表
     *
     * @param queryRequest 查询条件
     * @return MdSortingBinDO对象列表
     */
    PageData<MdSortingBinDTO> query(MdSortingBinQueryRequest queryRequest);

    /**
     * 插入MdSortingBinDTO对象
     *
     * @param mdSortingBinDTO 要插入的对象
     * @return 插入成功的记录数
     */
    int add(MdSortingBinDTO mdSortingBinDTO);

    /**
     * 根据主键ID更新MdSortingBinDO对象
     *
     * @param mdSortingBinDTO 要更新的对象
     * @return 更新成功的记录数
     */
    int updateById(MdSortingBinDTO mdSortingBinDTO);

    /**
     * 根据主键ID删除MdSortingBinDO对象
     *
     * @param id 主键ID
     * @return 删除成功的记录数
     */
    int deleteByPrimaryKey(Long id);

    MdSortingBinDTO querySortingBinByNo(String sortingBinNo);

    /**
     * 根据分拣柜编号查询分拣柜信息(忽略可用状态)
     * @param sortingBinNo 分拣柜编号
     * @return 分拣柜DTO
     */
    MdSortingBinDTO findSortingBinByNoIgnAvailable(String sortingBinNo);

    /**
     * 获取空闲的分拣柜ID
     * @return 分拣柜ID
     */
    Long getIdleSortingBin();

    /**
     * 增加波次数量
     * @param id 分拣柜ID
     */
    void addWaveQty(Long id);

    /**
     * 减少波次数量
     * @param id 分拣柜ID
     */
    void subWaveQty(Long id);

    /**
     * 根据类型获取空闲的分拣柜ID
     * @param sortBinType 分拣柜类型
     * @return 分拣柜ID
     */
    Long getIdleSortingBinByType(String sortBinType);

    /**
     * 根据分拣柜编号获取分拣柜ID
     * @param sortingBinNo 分拣柜编号
     * @return 分拣柜ID
     */
    Long getSortingBinIdByNo(String sortingBinNo);

    /**
     * 批量禁用分拣柜
     * @param sortingBinIdList 分拣柜ID列表
     */
    void batchDisable(List<Long> sortingBinIdList);

    /**
     * 分配分拣柜
     * @param doType 单据类型
     * @param isMinDO 是否最小单据
     * @param autoWaveType 自动波次类型
     * @return 分配的分拣柜ID
     */
    Long assignSortBin(String doType, boolean isMinDO, Integer autoWaveType);
}
