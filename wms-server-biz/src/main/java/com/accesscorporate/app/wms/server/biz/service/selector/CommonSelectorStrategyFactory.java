package com.accesscorporate.app.wms.server.biz.service.selector;

import com.accesscorporate.app.wms.server.common.enums.ErrorCodeEnum;
import com.accesscorporate.app.wms.server.common.utils.ThrowFactory;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-04-25 15:16
 * Description: 核注确认策略工厂
 */
@Component
public class CommonSelectorStrategyFactory {

    @Resource
    private List<AbstractCommonSelectorStrategy> queryStrategies;

    /**
     * 策略选择
     */
    public AbstractCommonSelectorStrategy getQueryStrategy(String selectorType) {
        Assert.notNull(selectorType, "下拉框选择类型不能为空!");
        return queryStrategies
                .stream()
                .filter(a -> Objects.equals(a.getStrategyType().getType(), selectorType))
                .findFirst().orElseThrow(() -> ThrowFactory.throwWith(ErrorCodeEnum.SYSTEM_ERROR,
                        "下拉框选择类型未匹配到执行策略:{}", selectorType));
    }


}
