package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.service.ExpAsnHeaderService;
import com.accesscorporate.app.wms.server.dal.entity.ExpAsnHeaderDO;
import com.accesscorporate.app.wms.server.dal.mapper.ExpAsnHeaderMapper;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ExpAsnHeaderServiceImpl implements ExpAsnHeaderService {

    @Resource
    private ExpAsnHeaderMapper expAsnHeaderMapper;

    @Override
    public ExpAsnHeaderDO getByIdAndWarehouseId(Long id, Long warehouseId) {
        return expAsnHeaderMapper.selectByIdAndWarehouseId(id, warehouseId);
    }
}