package com.accesscorporate.app.wms.server.biz.service.selector;

import com.accesscorporate.app.wms.server.biz.params.response.CommonSelectorResponse;

import java.util.List;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-05-20 16:59
 * Description: 2c订单未核注稽核
 */
public abstract class AbstractCommonSelectorStrategy {

    /**
     * 业务类型分配策略
     */
    protected abstract SelectorTypeEnum getStrategyType();

    /**
     * 不同类型业务单据处理逻辑
     */
    protected abstract List<CommonSelectorResponse> queryStrategy();

    public List<CommonSelectorResponse> executeQuery() {
        return queryStrategy();
    }


}
