package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.manager.SequenceGeneratorService;
import com.accesscorporate.app.wms.server.biz.params.request.CfgSnRulePageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.CfgSnRuleRequest;
import com.accesscorporate.app.wms.server.biz.params.response.CfgSnRuleResponse;
import com.accesscorporate.app.wms.server.biz.service.CfgSnRuleService;
import com.accesscorporate.app.wms.server.biz.converter.CfgSnRuleMapStruct;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import com.accesscorporate.app.wms.server.dal.entity.SequenceRule;
import com.accesscorporate.app.wms.server.dal.mapper.CfgSnRuleMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.idanchuang.component.base.exception.core.ExFactory;
import com.idanchuang.component.base.page.PageData;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/5 下午6:15 星期三
 */
@Service
public class CfgSnRuleServiceImpl implements CfgSnRuleService {

    @Resource
    private CfgSnRuleMapper cfgSnRuleMapper;

    @Resource
    private SequenceGeneratorService sequenceGeneratorService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    public void save(CfgSnRuleRequest cfgSnRuleRequest) {

        List<SequenceRule> sequenceRules = cfgSnRuleMapper.selectByRuleCode(cfgSnRuleRequest.getRuleCode());
        if (CollectionUtils.isNotEmpty(sequenceRules)) {
            throw ExFactory.throwBusiness(
                    String.format("规则代码[%s]关联的数据已存在，请尝试换个规则代码", cfgSnRuleRequest.getRuleCode()));
        }
        sequenceRules = cfgSnRuleMapper.selectByRuleName(cfgSnRuleRequest.getRuleName());
        if (CollectionUtils.isNotEmpty(sequenceRules)) {
            throw ExFactory.throwBusiness(
                    String.format("规则名称[%s]关联的数据已存在，请尝试换个规则代码", cfgSnRuleRequest.getRuleName()));
        }

        SequenceRule sequenceRule = CfgSnRuleMapStruct.INSTANCE.convertToDO(cfgSnRuleRequest);

        transactionTemplate.execute(status -> {
            sequenceGeneratorService.clearCache();
            cfgSnRuleMapper.insert(sequenceRule);
            return true;
        });

    }

    @Override
    public void update(CfgSnRuleRequest cfgSnRuleRequest) {
        LambdaQueryWrapper<SequenceRule> queryWrapper = new LambdaQueryWrapper<SequenceRule>()
                .eq(SequenceRule::getRuleCode, cfgSnRuleRequest.getRuleCode())
                .eq(SequenceRule::getRuleName, cfgSnRuleRequest.getRuleName());
        SequenceRule sequenceRule = cfgSnRuleMapper.selectOne(queryWrapper);
        if (ObjectUtils.isNotEmpty(sequenceRule)) {
            throw ExFactory.throwBusiness(
                    String.format("规则编码[%s],规则名称[%s]关联的数据已存在，请尝试换个规则代码",
                            cfgSnRuleRequest.getRuleCode(),
                            cfgSnRuleRequest.getRuleName()));
        }
        //根据规则编码更新
        transactionTemplate.execute(status -> {
            sequenceGeneratorService.updateCache(sequenceRule, UserContextAssistant.getCurrentWarehouseId());
            cfgSnRuleMapper.update(CfgSnRuleMapStruct.INSTANCE.convertToDO(cfgSnRuleRequest),
                    new LambdaQueryWrapper<SequenceRule>()
                            .eq(SequenceRule::getRuleCode, cfgSnRuleRequest.getRuleCode()));
            return true;
        });
    }

    @Override
    public PageData<CfgSnRuleResponse> page(CfgSnRulePageRequest request) {
        LambdaQueryWrapper<SequenceRule> lambdaQueryWrapper = new LambdaQueryWrapper<SequenceRule>()
                .eq(StringUtils.isNotBlank(request.getRuleCode()), SequenceRule::getRuleCode, request.getRuleCode())
                .eq(StringUtils.isNotBlank(request.getRuleName()), SequenceRule::getRuleName, request.getRuleName());
        PageDTO<SequenceRule> pageDTO = new PageDTO<>(request.getCurrent(), request.getSize());
        PageDTO<SequenceRule> sequenceRulePageDTO = cfgSnRuleMapper.selectPage(pageDTO, lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(sequenceRulePageDTO.getRecords())) {
            return PageData.empty();
        }

        List<CfgSnRuleResponse> collect = sequenceRulePageDTO.getRecords().stream()
                .map(CfgSnRuleMapStruct.INSTANCE::convertToResponse).toList();
        return PageData.of(collect, sequenceRulePageDTO.getCurrent(), sequenceRulePageDTO.getSize(), sequenceRulePageDTO.getTotal());
    }

    @Override
    public CfgSnRuleResponse edit(String ruleCode) {
        SequenceRule sequenceRule = cfgSnRuleMapper.selectOneByRuleCode(ruleCode);
        return CfgSnRuleMapStruct.INSTANCE.convertToResponse(sequenceRule);
    }

}
