package com.accesscorporate.app.wms.server.biz.manager;

import com.accesscorporate.app.wms.server.dal.entity.SequenceRule;

import java.util.List;

public interface SequenceGenerator {
    /**
     * 根据sequence rule code得到序列号
     *
     * @param ruleCode    序列号规则代码
     * @param warehouseId 仓库ID
     * @return
     */
    String generateSequenceNo(String ruleCode, Long warehouseId, Long tenantId);

    /**
     * 根据sequence rule code得到序列号
     *
     * @param ruleCode    序列号规则代码
     * @param warehouseId 仓库ID
     * @param seqNum      获取的seq的数量，比如一次性获取1000个
     * @return
     */
    List<String> generateSequenceNo(String ruleCode, Integer seqNum, Long warehouseId, Long tenantId);

    /**
     * 预览序列号，与generateSequenceNo不同的是，序列号值不会因此增长
     *
     * @param ruleCode
     * @return
     */
    String previewSequenceNo(String ruleCode, Long warehouseId, Long tenantId);

    /**
     * 当外部修改了序列规则时，需要清空缓存的序列号规则
     */
    void clearCache(Long tenantId);

    /**
     * 获取序列号缓存
     *
     * @param ruleName
     * @return
     */
    SequenceRule getRuleCahce(String ruleName, Long warehouseId, Long tenantId);

    /**
     * 更新序列号缓存
     *
     * @param rule
     */
    void updateRuleCache(SequenceRule rule, Long warehouseId, Long tenantId);
}
