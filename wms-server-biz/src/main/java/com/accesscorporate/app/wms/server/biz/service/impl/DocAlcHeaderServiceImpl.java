package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.converter.DocAlcHeaderMapStruct;
import com.accesscorporate.app.wms.server.biz.manager.impl.DocAlcHeaderManager;
import com.accesscorporate.app.wms.server.biz.params.request.DocAlcHeaderSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocAlcHeaderResponse;
import com.accesscorporate.app.wms.server.biz.service.DocAlcHeaderService;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import com.accesscorporate.app.wms.server.dal.entity.DocAlcHeader;
import com.accesscorporate.app.wms.server.dal.mapper.DocAlcHeaderMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class DocAlcHeaderServiceImpl extends ServiceImpl<DocAlcHeaderMapper, DocAlcHeader> implements DocAlcHeaderService {
    @Autowired
    private DocAlcHeaderManager docAlcHeaderManager;

    @Override
    public List<DocAlcHeaderResponse> listAll() {
        LambdaQueryWrapper<DocAlcHeader> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocAlcHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        return docAlcHeaderManager.list(wrapper).stream()
                .map(DocAlcHeaderMapStruct.INSTANCE::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public DocAlcHeaderResponse get(Long id) {
        DocAlcHeader docAlcHeader = super.getById(id);
        return DocAlcHeaderMapStruct.INSTANCE.convertToResponse(docAlcHeader);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(DocAlcHeaderSaveRequest request) {
        DocAlcHeader docAlcHeader = DocAlcHeaderMapStruct.INSTANCE.convertToEntity(request);
        docAlcHeader.setWarehouseId(UserContextAssistant.getCurrentWarehouseId());
        return super.save(docAlcHeader);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        return super.removeById(id);
    }

    @Override
    public Page<DocAlcHeaderResponse> page(Integer current, Integer size, String doNo, String status, String doType, String consigneeName) {
        Page<DocAlcHeader> page = new Page<>(current, size);
        LambdaQueryWrapper<DocAlcHeader> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocAlcHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        
        if (StringUtils.hasText(doNo)) {
            wrapper.like(DocAlcHeader::getDoNo, doNo);
        }
        if (StringUtils.hasText(status)) {
            wrapper.eq(DocAlcHeader::getStatus, status);
        }
        if (StringUtils.hasText(doType)) {
            wrapper.eq(DocAlcHeader::getDoType, doType);
        }
        if (StringUtils.hasText(consigneeName)) {
            wrapper.like(DocAlcHeader::getConsigneeName, consigneeName);
        }

        Page<DocAlcHeader> docAlcHeaderPage = super.page(page, wrapper);
        List<DocAlcHeaderResponse> responses = docAlcHeaderPage.getRecords().stream()
                .map(DocAlcHeaderMapStruct.INSTANCE::convertToResponse)
                .collect(Collectors.toList());
        return new Page<DocAlcHeaderResponse>(docAlcHeaderPage.getCurrent(), docAlcHeaderPage.getSize(), docAlcHeaderPage.getTotal())
                .setRecords(responses);
    }

    @Override
    public DocAlcHeader queryByDoNo(String doNo) {
        return lambdaQuery()
                .eq(DocAlcHeader::getDoNo, doNo)
                .eq(DocAlcHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocAlcHeader::getIsDeleted, Boolean.FALSE)
                .one();
    }
}
