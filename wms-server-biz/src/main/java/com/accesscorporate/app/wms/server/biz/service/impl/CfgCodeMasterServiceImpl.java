package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.params.response.CfgCodeMasterResponse;
import com.accesscorporate.app.wms.server.biz.service.CfgCodeMasterService;
import com.accesscorporate.app.wms.server.biz.converter.CfgCodeMasterMapStruct;
import com.accesscorporate.app.wms.server.dal.entity.CfgCodeMasterDO;
import com.accesscorporate.app.wms.server.dal.mapper.CfgCodeMasterMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/5 下午1:28 星期三
 */
@Service
public class CfgCodeMasterServiceImpl implements CfgCodeMasterService {

    @Resource
    private CfgCodeMasterMapper cfgCodeMasterMapper;

    @Override
    public List<String> findCodeCategoryGroupNames() {
        return cfgCodeMasterMapper.findCodeCategoryGroupNames();
    }

    @Override
    public List<CfgCodeMasterResponse> getCodeMasters(String groupName) {
        List<CfgCodeMasterDO> codeMasterDOS = cfgCodeMasterMapper.getCodeMasters(groupName);
        return codeMasterDOS.stream().map(CfgCodeMasterMapStruct.INSTANCE::convertToResponse).toList();
    }
}
