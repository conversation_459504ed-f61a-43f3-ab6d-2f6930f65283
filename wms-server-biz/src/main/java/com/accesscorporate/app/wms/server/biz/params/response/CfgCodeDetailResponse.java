package com.accesscorporate.app.wms.server.biz.params.response;

import com.accesscorporate.app.wms.server.common.enums.StatusEnum;
import lombok.Data;

/**
 * @TableName cfg_code_detail
 */
@Data
public class CfgCodeDetailResponse {
    /**
     *
     */
    private String uuid;

    /**
     *
     */
    private String masterCode;

    /**
     *
     */
    private String codeNameZh;

    /**
     *
     */
    private String codeNameEn;

    /**
     *
     */
    private String codeValue;

    /**
     *
     */
    private String memo;

    /**
     *
     */
    private String extendCol;

    /**
     *
     */
    private String messageKey;

    /**
     *
     */
    private Integer seqNum;

    /**
     *
     */
    private String status;

    /**
     *
     */
    private String statusDesc;

    /**
     * @param
     * @author: sxp
     * @date: 2025/2/5 下午3:35
     * @return: String
     **/
    public String getStatusDesc() {
        return StatusEnum.getValue(status);
    }
}