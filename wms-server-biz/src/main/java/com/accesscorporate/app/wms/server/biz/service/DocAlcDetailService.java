package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.request.DocAlcDetailSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocAlcDetailResponse;
import com.accesscorporate.app.wms.server.dal.entity.DocAlcDetail;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface DocAlcDetailService extends IService<DocAlcDetail> {
    List<DocAlcDetailResponse> listAll();

    DocAlcDetailResponse get(Long id);

    boolean save(DocAlcDetailSaveRequest saveRequest);

    boolean removeById(Long id);

    Page<DocAlcDetailResponse> page(Integer current, Integer size, Long doHeaderId, Long skuId, String linestatus);

    List<DocAlcDetailResponse> listByHeaderId(Long doHeaderId);
}
