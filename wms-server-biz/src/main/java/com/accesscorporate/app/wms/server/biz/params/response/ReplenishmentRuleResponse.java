package com.accesscorporate.app.wms.server.biz.params.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 补货规则-列表-响应体
 *
 * <AUTHOR>
 * 2025/2/18  17:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Tag(name = "补货规则 View Object")
public class ReplenishmentRuleResponse {

    @Schema(title = "ID")
    private Long id;

    @Schema(title = "规则编码")
    private String ruleCode;

    @Schema(title = "规则描述")
    private String ruleDesc;

    @Schema(title = "单据类型")
    private String orderType;

    @Schema(title = "是否优先分配RETURN库区库存")
    private Integer partitionCtrl;

    @Schema(title = "是否优先分配RETURN库区库存(文本)")
    private String partitionCtrlStr;

    @Schema(title = "是否效期控制")
    private Integer isExpirationDateControl;

    @Schema(title = "是否效期控制(文本)")
    private String isExpirationDateControlStr;

    @Schema(title = "是否激活")
    private Integer isActivation;

    @Schema(title = "是否激活(文本)")
    private String isActivationStr;


}
