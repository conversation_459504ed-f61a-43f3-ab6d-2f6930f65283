package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.common.enums.WhetherEnum;
import com.accesscorporate.app.wms.server.dal.entity.MdSku;
import com.accesscorporate.app.wms.server.dal.mapper.MdSkuMapper;
import com.accesscorporate.app.wms.server.biz.service.IMdSkuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * 商品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-20
 */
@Service
public class MdSkuServiceImpl extends ServiceImpl<MdSkuMapper, MdSku> implements IMdSkuService {

    @Override
    public MdSku querySkuByGoodsCode(String goodsCode) {
        return lambdaQuery()
                .eq(MdSku::getProductCode,goodsCode)
                .eq(MdSku::getIsDeleted, WhetherEnum.NO.getCode())
                .one();
    }

    @Override
    public List<MdSku> querySkuByGoodsCodes(List<String> goodsCodeList) {
        if (CollectionUtils.isEmpty(goodsCodeList)){
            return Lists.newArrayList();
        }
        return lambdaQuery()
                .eq(MdSku::getProductCode,goodsCodeList)
                .eq(MdSku::getIsDeleted, WhetherEnum.NO.getCode())
                .list();

    }


}
