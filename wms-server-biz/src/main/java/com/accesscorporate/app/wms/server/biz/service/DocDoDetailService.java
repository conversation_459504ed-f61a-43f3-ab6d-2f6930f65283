package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.request.DocDoDetailPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.request.DocDoDetailSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocDoDetailResponse;
import com.accesscorporate.app.wms.server.dal.entity.DocDoDetail;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 发货单明细表Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface DocDoDetailService extends IService<DocDoDetail> {

    /**
     * 查询所有发货单明细
     */
    List<DocDoDetailResponse> listAll();

    /**
     * 根据ID查询发货单明细
     */
    DocDoDetailResponse get(Long id);

    /**
     * 保存发货单明细
     */
    boolean save(DocDoDetailSaveRequest saveRequest);

    /**
     * 根据ID删除发货单明细
     */
    boolean removeById(Long id);

    /**
     * 分页查询发货单明细
     */
    Page<DocDoDetailResponse> page(DocDoDetailPageQueryRequest request);

    /**
     * 根据订单头ID查询明细
     */
    List<DocDoDetail> queryByDoHeaderId(Long doHeaderId);

    /**
     * 根据商品ID查询明细
     */
    List<DocDoDetail> queryBySkuId(Long skuId);

    /**
     * 根据明细状态查询
     */
    List<DocDoDetail> queryByLinestatus(String linestatus);

    /**
     * 根据包装ID查询明细
     */
    List<DocDoDetail> queryByPackageId(Long packageId);

    /**
     * 根据原始头ID查询明细
     */
    List<DocDoDetail> queryByOrigHeaderId(String origHeaderId);

    /**
     * 根据原始明细ID查询
     */
    DocDoDetail queryByOrigDetailId(String origDetailId);

    /**
     * 根据批次编码查询
     */
    List<DocDoDetail> queryByLotNo(String lotNo);

    /**
     * 根据供应商ID查询
     */
    List<DocDoDetail> queryBySupplier(String supplierId);

    /**
     * 根据货主ID查询
     */
    List<DocDoDetail> queryByCargoOwner(String cargoOwnerId);

    /**
     * 根据拣货区查询
     */
    List<DocDoDetail> queryByPickzone(String pickzone);

    /**
     * 根据是否坏品查询
     */
    List<DocDoDetail> queryByDamaged(Integer isDamaged);

    /**
     * 根据是否贵重品查询
     */
    List<DocDoDetail> queryByValueables(Integer isValueables);

    /**
     * 根据是否有处方药查询
     */
    List<DocDoDetail> queryByHaveCfy(Integer haveCfy);

    /**
     * 根据销售等级查询
     */
    List<DocDoDetail> queryBySalesGrade(Integer salesGrade);

    /**
     * 根据货品等级查询
     */
    List<DocDoDetail> queryByGoodsGrade(Integer goodsGrade);

    /**
     * 批量保存明细
     */
    boolean saveBatch(List<DocDoDetailSaveRequest> saveRequests);

    /**
     * 根据订单头ID删除所有明细
     */
    boolean removeByDoHeaderId(Long doHeaderId);
}
