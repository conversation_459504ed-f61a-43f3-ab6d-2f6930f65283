package com.accesscorporate.app.wms.server.biz.params.response.goodspackage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-18 11:04
 * Description: 编辑包装信息响应结果
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GoodsPackageEditResponse {

    @Schema(title = "商品编码")
    private String productCode;

    @Schema(title = "包装代码")
    private String pkgCode;

    @Schema(title = "包装描述")
    private String pkgDesc;


}
