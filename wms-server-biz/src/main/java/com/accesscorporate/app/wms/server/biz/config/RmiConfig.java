package com.accesscorporate.app.wms.server.biz.config;

import com.accesscorporate.app.wms.server.biz.manager.SequenceGenerator;
import com.idanchuang.component.config.apollo.ConfigService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.remoting.rmi.RmiProxyFactoryBean;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @description
 * @date 2025/2/6 下午1:28 星期四
 */
@Component
@Slf4j
public class RmiConfig {

    @Resource
    private ConfigService configService;

    private static final String RMI_SERVER_HOST = "sequence.server";

    @Bean
    public RmiProxyFactoryBean proxyFactoryBean() {
        String rmiHost = configService.getString(RMI_SERVER_HOST, null);
        log.info("RMI Host name is:{} ", rmiHost);
        RmiProxyFactoryBean proxy = new RmiProxyFactoryBean();
        proxy.setServiceInterface(SequenceGenerator.class);
        proxy.setServiceUrl(rmiHost);
        proxy.afterPropertiesSet();
        return proxy;
    }
}
