package com.accesscorporate.app.wms.server.biz.mq;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.core.ReturnedMessage;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @version JDK 17
 * @date 2025/3/25
 * @description
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RabbitMqServiceImpl implements RabbitMqService, RabbitTemplate.ReturnsCallback {

    private final RabbitTemplate rabbitTemplate;

    @PostConstruct
    public void init() {
        // 设置 ConfirmCallback（仍然有效）
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            log.info("correlationData: [{}], ack: [{}], cause: [{}]", correlationData, ack, cause);
            if (!ack) {
                log.warn("消息未成功发送，处理异常...");
            } else {
                log.info("消息发送成功！");
            }
        });
        // 设置 ReturnsCallback（新版本推荐写法）
        rabbitTemplate.setReturnsCallback(this);
    }

    @Override
    public void returnedMessage(ReturnedMessage returned) {
        log.warn("消息未被路由！ returnedMessage: {}, replyCode: {}, replyText: {}, exchange: {}, routingKey: {}",
                returned.getMessage(),
                returned.getReplyCode(),
                returned.getReplyText(),
                returned.getExchange(),
                returned.getRoutingKey());
    }

    @Override
    public void send(String exchange, String message, String messageId, Integer delay) {

        MessageProperties messageProperties = new MessageProperties();
        messageProperties.setMessageId(messageId);
        if (delay != null && delay > 0) {
            messageProperties.setHeader("x-delay", delay); // 使用 x-delay 插件头
        }
        Message amqpMessage = new Message(message.getBytes(StandardCharsets.UTF_8), messageProperties);
        rabbitTemplate.convertAndSend(exchange, "", amqpMessage);
    }

    @Override
    public void send(String exchange, String message, String messageId) {
        MessageProperties messageProperties = new MessageProperties();
        messageProperties.setMessageId(messageId);
        Message amqpMessage = new Message(message.getBytes(StandardCharsets.UTF_8), messageProperties);
        rabbitTemplate.convertAndSend(exchange, "", amqpMessage);
    }

    @Override
    public void send(String exchange, String message, Long deliveryTag) {
        MessageProperties messageProperties = new MessageProperties();
        messageProperties.setDeliveryTag(deliveryTag);
        Message amqpMessage = new Message(message.getBytes(StandardCharsets.UTF_8), messageProperties);
        rabbitTemplate.convertAndSend(exchange, "", amqpMessage);
    }

    /**
     * 发送消息(非持久化)
     *
     * @param exchange 交换机
     * @param message  消息体
     */
    @Override
    public void send(String exchange, String message) {
        log.info("消息发送交换机:{},消息内容:{}",exchange,message);
        MessageProperties messageProperties = new MessageProperties();
        messageProperties.setDeliveryMode(MessageDeliveryMode.NON_PERSISTENT);
        Message amqpMessage = new Message(message.getBytes(StandardCharsets.UTF_8), messageProperties);
        rabbitTemplate.convertAndSend(exchange, "", amqpMessage);
    }

    /**
     * 发送消息(持久化)
     *
     * @param exchange 交换机
     * @param message  消息体
     */
    @Override
    public void sendPersistence(String exchange, String message) {
        MessageProperties messageProperties = new MessageProperties();
        messageProperties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
        Message amqpMessage = new Message(message.getBytes(StandardCharsets.UTF_8), messageProperties);
        rabbitTemplate.convertAndSend(exchange, "", amqpMessage);
    }
}
