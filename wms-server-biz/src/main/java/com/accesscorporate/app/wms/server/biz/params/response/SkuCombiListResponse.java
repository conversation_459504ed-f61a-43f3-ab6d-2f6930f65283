package com.accesscorporate.app.wms.server.biz.params.response;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> liudongliang
 * @project : wms-server
 * @company : 浙江单创品牌管理有限公司
 * @desc :
 * Copyright @ 2024 danchuang Technology Co.Ltd. – Confidential and Proprietary
 */
@Data
public class SkuCombiListResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 7758780910969988677L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 组合条码
     */
    private String combiBarcode;

    /**
     * 组合标识
     */
    private String sign;

    /**
     * 组合数量
     */
    private Integer skuCount;

    /**
     * 组合总数量
     */
    private Integer skuTotal;

    /**
     * 库位
     */
    private String locCode;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 创建人
     */
    private String createBy;

}
