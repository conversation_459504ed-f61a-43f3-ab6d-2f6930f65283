package com.accesscorporate.app.wms.server.biz.job.entity;

import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * xxljob调度入参实体
 * <AUTHOR>
 * @date 2021/12/20
 */
@Data
public class XxlJobParamEntity {
    /**
     * 仓库集合 用于执行指定仓库的任务
     */
    private List<Long> warehouseIds;
    /**
     * 方法入参
     */
    private Object methodParams;
    /**
     * 分片参数 分片参数 {"nodeIndex":[warehouseId,...],...}
     */
    private Map<Integer, List<Long>> shardingMap=new HashMap<>();

}
