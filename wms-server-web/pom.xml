<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>wms-server</artifactId>
        <groupId>com.accesscorporate.app</groupId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>wms-server-web</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>2.13.3</version> <!-- 请根据需要检查最新版本 -->
        </dependency>
        <dependency>
            <groupId>com.accesscorporate.app</groupId>
            <artifactId>wms-server-biz</artifactId>
        </dependency>
        <dependency>
            <!-- (强制依赖)配置中心, 组件文档: http://git.acg.team/arch/spring-cloud-parent-all/tree/master/components/component-config-apollo -->
            <groupId>com.idanchuang.component</groupId>
            <artifactId>component-config-apollo</artifactId>
        </dependency>
        <dependency>
            <!-- (WEB服务强制依赖)统一接口异常处理, 组件文档: http://git.acg.team/arch/spring-cloud-parent-all/tree/master/components/component-just-web -->
            <groupId>com.idanchuang.component</groupId>
            <artifactId>component-just-web</artifactId>
        </dependency>
        <dependency>
            <!-- (推荐依赖)对接Sentinel流控后台, 组件文档: http://git.acg.team/arch/spring-cloud-parent-all/tree/master/components/component-sentinel -->
            <groupId>com.idanchuang.component</groupId>
            <artifactId>component-sentinel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.idanchuang.component</groupId>
            <artifactId>component-consumer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.idanchuang.component</groupId>
            <artifactId>component-i18n</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dc-easyexcel</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <!-- deploy 时排除此模块 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <!--开启filtering功能  -->
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>