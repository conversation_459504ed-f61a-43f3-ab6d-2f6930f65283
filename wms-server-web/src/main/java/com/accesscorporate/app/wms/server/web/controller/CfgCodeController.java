package com.accesscorporate.app.wms.server.web.controller;

import com.accesscorporate.app.wms.server.biz.manager.CfgCodeDetailCacheService;
import com.accesscorporate.app.wms.server.biz.params.request.CfgCodeDetailPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.CfgCodeDetailSaveOrUpdateRequest;
import com.accesscorporate.app.wms.server.biz.params.response.CfgCodeDetailResponse;
import com.accesscorporate.app.wms.server.biz.params.response.CfgCodeMasterResponse;
import com.accesscorporate.app.wms.server.biz.service.CfgCodeDetailService;
import com.accesscorporate.app.wms.server.biz.service.CfgCodeMasterService;
import com.accesscorporate.app.wms.server.biz.service.dto.SelectItem;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/5 下午5:24 星期三
 */
@RestController
@RequestMapping("/cfgCode/")
public class CfgCodeController {

    @Resource
    private CfgCodeDetailCacheService cfgCodeDetailCacheService;

    @Resource
    private CfgCodeMasterService cfgCodeMasterService;

    @Resource
    private CfgCodeDetailService cfgCodeDetailService;
    @Resource
    private CfgCodeDetailCacheService cacheService;

    @GetMapping("findCodeCategoryGroupNames")
    JsonResult<List<String>> findCodeCategoryGroupNames() {
        List<String> codeCategoryGroupNames = cfgCodeMasterService.findCodeCategoryGroupNames();
        return JsonResult.success(codeCategoryGroupNames);
    }

    @GetMapping("getCodeMasters")
    JsonResult<List<CfgCodeMasterResponse>> getCodeMasters(@RequestParam String groupName) {
        List<CfgCodeMasterResponse> codeMasters = cfgCodeMasterService.getCodeMasters(groupName);
        return JsonResult.success(codeMasters);
    }


    @PostMapping("pageList")
    JsonResult<PageData<CfgCodeDetailResponse>> pageList(@RequestBody @Valid CfgCodeDetailPageRequest cfgCodeDetailPageRequest) {
        PageData<CfgCodeDetailResponse> cfgCodeDetailResponsePageData = cfgCodeDetailService.pageList(cfgCodeDetailPageRequest);
        return JsonResult.success(cfgCodeDetailResponsePageData);
    }

    @GetMapping("remove")
    JsonResult<Boolean> remove(@RequestParam String uuid) {
        cfgCodeDetailService.remove(uuid);
        return JsonResult.success(true);
    }

    @PostMapping("save")
    JsonResult<Boolean> save(@RequestBody @Valid CfgCodeDetailSaveOrUpdateRequest request) {
        cfgCodeDetailService.save(request);
        return JsonResult.success(true);
    }

    @PostMapping("update")
    JsonResult<Boolean> update(@RequestBody @Valid CfgCodeDetailSaveOrUpdateRequest request) {
        cfgCodeDetailService.update(request);
        return JsonResult.success(true);
    }

    @PostMapping("clearCache")
    public JsonResult<Boolean> clearCache(@RequestParam String masterCode) {
        cfgCodeDetailCacheService.clearCache(masterCode);
        return JsonResult.success(true);
    }
    @PostMapping("dict")
    public JsonResult<List<SelectItem>> getSelectItems(@RequestParam String masterCode) {
        return JsonResult.success(cacheService.getSelectItems(masterCode));
    }
}
