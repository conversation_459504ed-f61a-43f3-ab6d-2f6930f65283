package com.accesscorporate.app.wms.server.web.controller;

import com.accesscorporate.app.wms.server.biz.params.request.StocktakingRuleCreateRequest;
import com.accesscorporate.app.wms.server.biz.params.request.StocktakingRuleModifyRequest;
import com.accesscorporate.app.wms.server.biz.params.request.StocktakingRulesQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.response.StocktakingRuleDetailResponse;
import com.accesscorporate.app.wms.server.biz.params.response.StocktakingRuleResponse;
import com.accesscorporate.app.wms.server.biz.service.IStocktakingRuleService;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 盘点规则-控制层
 *
 * <AUTHOR>
 * 2025/2/8  16:12
 */
@RestController
@RequestMapping("/stocktakingRule")
@RequiredArgsConstructor
@Tag(name = "盘点规则管理")
public class StocktakingRuleController {

    private final IStocktakingRuleService iStocktakingRuleService;


    @Operation(summary = "修改盘点规则", description = "修改盘点规则")
    @PutMapping
    @Validated
    public JsonResult<Boolean> modifyStocktakingRule(@RequestBody StocktakingRuleModifyRequest request){
        return JsonResult.success(iStocktakingRuleService.modifyStocktakingRule(request));
    }


    @Operation(summary = "新增盘点规则", description = "新增盘点规则")
    @PostMapping
    public JsonResult<Boolean> createStocktakingRule(@RequestBody StocktakingRuleCreateRequest request){
        return JsonResult.success(iStocktakingRuleService.createStocktakingRule(request));
    }



    @Operation(summary = "盘点规则列表查询", description = "供应商列表查询-分页-多条件")
    @PostMapping("/pageList")
    public JsonResult<PageData<StocktakingRuleResponse>> queryStocktakingRulesPage(@RequestBody StocktakingRulesQueryPageRequest request) {
        return JsonResult.success(iStocktakingRuleService.queryStocktakingRules(request));
    }


    /*------------------------ 盘点规则明细接口 start...... --------------------------*/


    @Operation(summary = "盘点规则详情查询", description = "盘点规则详情查询")
    @GetMapping("/detail/{id}")
    @Validated
    public JsonResult<StocktakingRuleDetailResponse> queryStocktakingRuleDetail(
            @NotNull(message = "id 不能为空") @PathVariable("id") Long id){
        return JsonResult.success(iStocktakingRuleService.queryStocktakingRuleDetail(id));
    }




}
