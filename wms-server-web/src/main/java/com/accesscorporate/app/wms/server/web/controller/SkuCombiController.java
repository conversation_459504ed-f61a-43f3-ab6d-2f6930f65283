package com.accesscorporate.app.wms.server.web.controller;

import com.accesscorporate.app.wms.server.biz.params.request.IdDTO;
import com.accesscorporate.app.wms.server.biz.params.request.SkuCombiAddRequest;
import com.accesscorporate.app.wms.server.biz.params.request.SkuCombiListRequest;
import com.accesscorporate.app.wms.server.biz.params.response.SkuCombiListResponse;
import com.accesscorporate.app.wms.server.biz.params.response.SkuCombiViewResponse;
import com.accesscorporate.app.wms.server.biz.service.SkuCombiService;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import com.idanchuang.component.just.web.base.BaseController;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> liudongliang
 * @project : wms-server
 * @company : 浙江单创品牌管理有限公司
 * @desc :   组合绑码控制层
 * Copyright @ 2024 danchuang Technology Co.Ltd. – Confidential and Proprietary
 */
@RestController
@RequestMapping("/skuCombi/")
public class SkuCombiController extends BaseController {

    @Resource
    private SkuCombiService skuCombiService;

    @PostMapping("list")
    public JsonResult<PageData<SkuCombiListResponse>> list(@RequestBody SkuCombiListRequest param) {
        return JsonResult.success(skuCombiService.list(param));
    }

    @PostMapping("add")
    public JsonResult<Boolean> add(@RequestBody @Valid SkuCombiAddRequest param) {
        return JsonResult.success(skuCombiService.add(param));
    }

    @PostMapping("view")
    public JsonResult<SkuCombiViewResponse> view(@RequestBody @Valid IdDTO idDTO) {
        return JsonResult.success(skuCombiService.view(idDTO.getId()));
    }

    @PostMapping("delete")
    public JsonResult<Boolean> delete(@RequestBody @Valid IdDTO idDTO) {
        return JsonResult.success(skuCombiService.delete(idDTO.getId()));
    }


    @PostMapping("import")
    public JsonResult<Boolean> importSkuCombi(@RequestParam("file") MultipartFile file) {
        skuCombiService.importExcel(file);
        return JsonResult.success(Boolean.TRUE);
    }

}
