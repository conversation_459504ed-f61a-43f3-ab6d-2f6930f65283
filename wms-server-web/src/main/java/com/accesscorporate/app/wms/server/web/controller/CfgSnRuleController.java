package com.accesscorporate.app.wms.server.web.controller;

import com.accesscorporate.app.wms.server.biz.params.request.CfgSnRulePageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.CfgSnRuleRequest;
import com.accesscorporate.app.wms.server.biz.params.response.CfgSnRuleResponse;
import com.accesscorporate.app.wms.server.biz.service.CfgSnRuleService;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/6 下午2:29 星期四
 */
@RestController
@RequestMapping("/cfgSnRule/")
public class CfgSnRuleController {
    @Resource
    private CfgSnRuleService cfgSnRuleService;

    @PostMapping("save")
    public JsonResult<Boolean> save(@RequestBody @Valid CfgSnRuleRequest cfgSnRuleRequest) {
        cfgSnRuleService.save(cfgSnRuleRequest);
        return JsonResult.success(true);
    }

    @PostMapping("update")
    public JsonResult<Boolean> update(@RequestBody @Valid CfgSnRuleRequest cfgSnRuleRequest) {
        cfgSnRuleService.update(cfgSnRuleRequest);
        return JsonResult.success(true);
    }

    @PostMapping("edit")
    public JsonResult<CfgSnRuleResponse> edit(@RequestParam String ruleCode) {
        CfgSnRuleResponse edit = cfgSnRuleService.edit(ruleCode);
        return JsonResult.success(edit);
    }

    @PostMapping("page")
    public JsonResult<PageData<CfgSnRuleResponse>> page(@RequestBody @Valid CfgSnRulePageRequest request) {
        PageData<CfgSnRuleResponse> page = cfgSnRuleService.page(request);
        return JsonResult.success(page);
    }

}
