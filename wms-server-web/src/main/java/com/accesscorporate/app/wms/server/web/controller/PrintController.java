package com.accesscorporate.app.wms.server.web.controller;

import com.accesscorporate.app.wms.server.biz.service.PrintService;
import com.accesscorporate.app.wms.server.biz.service.dto.PrintDTO;
import com.idanchuang.component.base.JsonResult;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/print")
public class PrintController {

    @Resource
    private PrintService printService;

    @GetMapping("/{id}")
    public JsonResult<PrintDTO> getPrintById(@PathVariable Long id) {
        PrintDTO printDTO = printService.queryById(id);
        return JsonResult.success(printDTO);
    }

    @GetMapping("/all")
    public JsonResult<List<PrintDTO>> getAllPrints() {
        List<PrintDTO> printDTOList = printService.selectAll();
        return JsonResult.success(printDTOList);
    }

    @PostMapping("/")
    public JsonResult<Integer> insertPrint(@RequestBody PrintDTO printDTO) {
        int result = printService.insert(printDTO);
        return JsonResult.success(result);
    }

    @PutMapping("/{id}")
    public JsonResult<Integer> updatePrint(@PathVariable Long id, @RequestBody PrintDTO printDTO) {
        printDTO.setId(id);
        int result = printService.updateById(printDTO);
        return JsonResult.success(result);
    }

    @DeleteMapping("/{id}")
    public JsonResult<Integer> deletePrint(@PathVariable Long id) {
        int result = printService.deleteByPrimaryKey(id);
        return JsonResult.success(result);
    }
}