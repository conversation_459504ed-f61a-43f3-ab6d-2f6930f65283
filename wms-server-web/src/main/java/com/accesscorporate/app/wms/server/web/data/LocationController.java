package com.accesscorporate.app.wms.server.web.data;

import com.accesscorporate.app.wms.server.biz.params.request.LocationSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.LocationResponse;
import com.accesscorporate.app.wms.server.biz.service.LocationService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/locations")
public class LocationController {

  @Autowired
  private LocationService locationService;

  @GetMapping
  public JsonResult<List<LocationResponse>> getAllLocations() {
    return JsonResult.success(locationService.listAll());
  }

  @GetMapping("/{id}")
  public JsonResult<LocationResponse> getLocationById(@PathVariable Long id) {
    return JsonResult.success(locationService.get(id));
  }

  @PostMapping("/save")
  public JsonResult<Boolean> saveLocation(@RequestBody LocationSaveRequest location) {
    return JsonResult.success(locationService.save(location));
  }

  @DeleteMapping("/{id}")
  public JsonResult<Boolean> deleteLocation(@PathVariable Long id) {
    return JsonResult.success(locationService.removeById(id));
  }

  /**
   * 分页查询库位
   *
   * @param current       当前页
   * @param size          一页数量
   * @param locCode       库位编码
   * @param partitionId   关联的分区 ID
   * @param locType       库位类型
   * @param canMixProduct 是否允许混放商品
   * @param ignoreLpn     是否忽略 LPN
   * @param aisleFrom     行 From
   * @param aisleTo       行 To
   * @param bayFrom       贝 From
   * @param bayTo         贝 To
   * @param levelFrom     层 From
   * @param levelTo       层 To
   * @param positionFrom  位置 From
   * @param positionTo    位置 To
   * @return JsonResult<PageData<Location>>
   */
  @GetMapping("/page")
  public JsonResult<PageData<LocationResponse>> page(
      Integer current,
      Integer size,
      String locCode,
      Long partitionId,
      String locType,
      Integer canMixProduct,
      Integer ignoreLpn,
      String aisleFrom,
      String aisleTo,
      String bayFrom,
      String bayTo,
      String levelFrom,
      String levelTo,
      String positionFrom,
      String positionTo) {

    Page<LocationResponse> page = locationService.page(current, size, locCode, partitionId, locType, canMixProduct, ignoreLpn,
        aisleFrom, aisleTo, bayFrom, bayTo, levelFrom, levelTo, positionFrom, positionTo);
    PageData<LocationResponse> pageData = PageData.of(page.getRecords(), page.getSize(), page.getTotal());
    return JsonResult.success(pageData);
  }
}