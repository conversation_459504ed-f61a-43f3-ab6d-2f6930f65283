package com.accesscorporate.app.wms.server.web.data;

import com.accesscorporate.app.wms.server.biz.params.request.DocDoDetailPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.request.DocDoDetailSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocDoDetailResponse;
import com.accesscorporate.app.wms.server.biz.service.DocDoDetailService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 发货单明细表Controller
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@RestController
@RequestMapping("/api/doc-do-detail")
@Tag(name = "发货单明细表管理")
public class DocDoDetailController {

    @Autowired
    private DocDoDetailService docDoDetailService;

    @GetMapping("/list")
    @Operation(summary = "查询所有发货单明细")
    public JsonResult<List<DocDoDetailResponse>> listDocDoDetails() {
        return JsonResult.success(docDoDetailService.listAll());
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询发货单明细")
    public JsonResult<DocDoDetailResponse> getDocDoDetail(@PathVariable Long id) {
        return JsonResult.success(docDoDetailService.get(id));
    }

    @PostMapping
    @Operation(summary = "新增发货单明细")
    public JsonResult<Boolean> saveDocDoDetail(@Valid @RequestBody DocDoDetailSaveRequest request) {
        return JsonResult.success(docDoDetailService.save(request));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "根据ID删除发货单明细")
    public JsonResult<Boolean> deleteDocDoDetail(@PathVariable Long id) {
        return JsonResult.success(docDoDetailService.removeById(id));
    }

    @PostMapping("/page")
    @Operation(summary = "分页查询发货单明细")
    public JsonResult<PageData<DocDoDetailResponse>> pageDocDoDetails(@Valid @RequestBody DocDoDetailPageQueryRequest request) {
        Page<DocDoDetailResponse> page = docDoDetailService.page(request);
        return JsonResult.success(PageData.of(page.getRecords(), page.getSize(), page.getTotal()));
    }

    @GetMapping("/by-do-header-id/{doHeaderId}")
    @Operation(summary = "根据订单头ID查询明细")
    public JsonResult<List<DocDoDetailResponse>> getByDoHeaderId(@PathVariable Long doHeaderId) {
        var docDoDetails = docDoDetailService.queryByDoHeaderId(doHeaderId);
        List<DocDoDetailResponse> responses = docDoDetails.stream()
                .map(docDoDetail -> docDoDetailService.get(docDoDetail.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @GetMapping("/by-sku-id/{skuId}")
    @Operation(summary = "根据商品ID查询明细")
    public JsonResult<List<DocDoDetailResponse>> getBySkuId(@PathVariable Long skuId) {
        var docDoDetails = docDoDetailService.queryBySkuId(skuId);
        List<DocDoDetailResponse> responses = docDoDetails.stream()
                .map(docDoDetail -> docDoDetailService.get(docDoDetail.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @GetMapping("/by-linestatus/{linestatus}")
    @Operation(summary = "根据明细状态查询")
    public JsonResult<List<DocDoDetailResponse>> getByLinestatus(@PathVariable String linestatus) {
        var docDoDetails = docDoDetailService.queryByLinestatus(linestatus);
        List<DocDoDetailResponse> responses = docDoDetails.stream()
                .map(docDoDetail -> docDoDetailService.get(docDoDetail.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @GetMapping("/by-package-id/{packageId}")
    @Operation(summary = "根据包装ID查询明细")
    public JsonResult<List<DocDoDetailResponse>> getByPackageId(@PathVariable Long packageId) {
        var docDoDetails = docDoDetailService.queryByPackageId(packageId);
        List<DocDoDetailResponse> responses = docDoDetails.stream()
                .map(docDoDetail -> docDoDetailService.get(docDoDetail.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @GetMapping("/by-orig-header-id/{origHeaderId}")
    @Operation(summary = "根据原始头ID查询明细")
    public JsonResult<List<DocDoDetailResponse>> getByOrigHeaderId(@PathVariable String origHeaderId) {
        var docDoDetails = docDoDetailService.queryByOrigHeaderId(origHeaderId);
        List<DocDoDetailResponse> responses = docDoDetails.stream()
                .map(docDoDetail -> docDoDetailService.get(docDoDetail.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @GetMapping("/by-orig-detail-id/{origDetailId}")
    @Operation(summary = "根据原始明细ID查询")
    public JsonResult<DocDoDetailResponse> getByOrigDetailId(@PathVariable String origDetailId) {
        var docDoDetail = docDoDetailService.queryByOrigDetailId(origDetailId);
        if (docDoDetail == null) {
            return JsonResult.success(null);
        }
        return JsonResult.success(docDoDetailService.get(docDoDetail.getId()));
    }

    @GetMapping("/by-lot-no/{lotNo}")
    @Operation(summary = "根据批次编码查询")
    public JsonResult<List<DocDoDetailResponse>> getByLotNo(@PathVariable String lotNo) {
        var docDoDetails = docDoDetailService.queryByLotNo(lotNo);
        List<DocDoDetailResponse> responses = docDoDetails.stream()
                .map(docDoDetail -> docDoDetailService.get(docDoDetail.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @GetMapping("/by-supplier/{supplierId}")
    @Operation(summary = "根据供应商ID查询")
    public JsonResult<List<DocDoDetailResponse>> getBySupplier(@PathVariable String supplierId) {
        var docDoDetails = docDoDetailService.queryBySupplier(supplierId);
        List<DocDoDetailResponse> responses = docDoDetails.stream()
                .map(docDoDetail -> docDoDetailService.get(docDoDetail.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @GetMapping("/by-cargo-owner/{cargoOwnerId}")
    @Operation(summary = "根据货主ID查询")
    public JsonResult<List<DocDoDetailResponse>> getByCargoOwner(@PathVariable String cargoOwnerId) {
        var docDoDetails = docDoDetailService.queryByCargoOwner(cargoOwnerId);
        List<DocDoDetailResponse> responses = docDoDetails.stream()
                .map(docDoDetail -> docDoDetailService.get(docDoDetail.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @GetMapping("/by-pickzone/{pickzone}")
    @Operation(summary = "根据拣货区查询")
    public JsonResult<List<DocDoDetailResponse>> getByPickzone(@PathVariable String pickzone) {
        var docDoDetails = docDoDetailService.queryByPickzone(pickzone);
        List<DocDoDetailResponse> responses = docDoDetails.stream()
                .map(docDoDetail -> docDoDetailService.get(docDoDetail.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @GetMapping("/by-damaged/{isDamaged}")
    @Operation(summary = "根据是否坏品查询")
    public JsonResult<List<DocDoDetailResponse>> getByDamaged(@PathVariable Integer isDamaged) {
        var docDoDetails = docDoDetailService.queryByDamaged(isDamaged);
        List<DocDoDetailResponse> responses = docDoDetails.stream()
                .map(docDoDetail -> docDoDetailService.get(docDoDetail.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @GetMapping("/by-valueables/{isValueables}")
    @Operation(summary = "根据是否贵重品查询")
    public JsonResult<List<DocDoDetailResponse>> getByValueables(@PathVariable Integer isValueables) {
        var docDoDetails = docDoDetailService.queryByValueables(isValueables);
        List<DocDoDetailResponse> responses = docDoDetails.stream()
                .map(docDoDetail -> docDoDetailService.get(docDoDetail.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @GetMapping("/by-have-cfy/{haveCfy}")
    @Operation(summary = "根据是否有处方药查询")
    public JsonResult<List<DocDoDetailResponse>> getByHaveCfy(@PathVariable Integer haveCfy) {
        var docDoDetails = docDoDetailService.queryByHaveCfy(haveCfy);
        List<DocDoDetailResponse> responses = docDoDetails.stream()
                .map(docDoDetail -> docDoDetailService.get(docDoDetail.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @GetMapping("/by-sales-grade/{salesGrade}")
    @Operation(summary = "根据销售等级查询")
    public JsonResult<List<DocDoDetailResponse>> getBySalesGrade(@PathVariable Integer salesGrade) {
        var docDoDetails = docDoDetailService.queryBySalesGrade(salesGrade);
        List<DocDoDetailResponse> responses = docDoDetails.stream()
                .map(docDoDetail -> docDoDetailService.get(docDoDetail.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @GetMapping("/by-goods-grade/{goodsGrade}")
    @Operation(summary = "根据货品等级查询")
    public JsonResult<List<DocDoDetailResponse>> getByGoodsGrade(@PathVariable Integer goodsGrade) {
        var docDoDetails = docDoDetailService.queryByGoodsGrade(goodsGrade);
        List<DocDoDetailResponse> responses = docDoDetails.stream()
                .map(docDoDetail -> docDoDetailService.get(docDoDetail.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @PostMapping("/batch")
    @Operation(summary = "批量保存明细")
    public JsonResult<Boolean> saveBatch(@Valid @RequestBody List<DocDoDetailSaveRequest> requests) {
        return JsonResult.success(docDoDetailService.saveBatch(requests));
    }

    @DeleteMapping("/by-do-header-id/{doHeaderId}")
    @Operation(summary = "根据订单头ID删除所有明细")
    public JsonResult<Boolean> removeByDoHeaderId(@PathVariable Long doHeaderId) {
        return JsonResult.success(docDoDetailService.removeByDoHeaderId(doHeaderId));
    }
}
