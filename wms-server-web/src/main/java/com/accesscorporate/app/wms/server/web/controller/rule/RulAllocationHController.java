package com.accesscorporate.app.wms.server.web.controller.rule;


import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationHQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationHRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulAllocationHResponse;
import com.accesscorporate.app.wms.server.biz.service.rule.IRulAllocationHService;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@RestController
@RequestMapping("/rule/allocation/header")
@RequiredArgsConstructor
public class RulAllocationHController {

    private final IRulAllocationHService rulAllocationHService;

    @PostMapping("/list")
    public JsonResult<PageData<RulAllocationHResponse>> list(@RequestBody RulAllocationHQueryPageRequest request) {
        return JsonResult.success(rulAllocationHService.queryPage(request));
    }

    @GetMapping("/queryById")
    public JsonResult<RulAllocationHResponse> queryById(@RequestParam("id") @NotNull Long id) {
        RulAllocationHResponse rulAllocationHResponse = rulAllocationHService.queryById(id);
        if (rulAllocationHResponse == null) {
            return JsonResult.failure("通过id查询分配规则头不存在");
        }
        return JsonResult.success(rulAllocationHResponse);
    }


    @GetMapping("/queryByAllocationId")
    public JsonResult<RulAllocationHResponse> queryByAllocationId(@RequestParam("allocationId") @NotNull Long allocationId) {
        RulAllocationHResponse rulAllocationHResponse = rulAllocationHService.queryByAllocationId(allocationId);
        if (rulAllocationHResponse == null) {
            return JsonResult.failure("通过allocationId查询分配规则头不存在");
        }
        return JsonResult.success(rulAllocationHResponse);
    }

    @PostMapping("/save")
    public JsonResult<Boolean> save(@RequestBody RulAllocationHRequest request) {
        return JsonResult.success(rulAllocationHService.save(request));
    }

    @PostMapping("/deleteById")
    public JsonResult<Boolean> deleteById(@RequestParam("id") @NotNull Long id) {
        return JsonResult.success(rulAllocationHService.deleteById(id));
    }

}
