package com.accesscorporate.app.wms.server.web.controller;

import com.accesscorporate.app.wms.server.biz.params.request.MdSortingBinQueryRequest;
import com.accesscorporate.app.wms.server.biz.service.MdSortingBinService;
import com.accesscorporate.app.wms.server.biz.service.PrintService;
import com.accesscorporate.app.wms.server.biz.service.dto.MdSortingBinDTO;
import com.accesscorporate.app.wms.server.biz.service.dto.PrintDTO;
import com.accesscorporate.app.wms.server.common.enums.PrinterTypeEnum;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import com.idanchuang.component.just.web.base.BaseController;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;


/**
 * MdSortingBinController 类
 * 分拣柜管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mdSortingBin")
public class MdSortingBinController extends BaseController {

    @Resource
    private MdSortingBinService mdSortingBinService;
    @Resource
    private PrintService printService;

    /**
     * 根据主键ID查询MdSortingBinDO对象
     *
     * @param id 主键ID
     * @return MdSortingBinDO对象
     */
    @GetMapping("/{id}")
    public JsonResult<MdSortingBinDTO> getMdSortingBinById(@PathVariable Long id) {
        MdSortingBinDTO mdSortingBinDTO = mdSortingBinService.selectById(id);
        return JsonResult.success(mdSortingBinDTO);
    }

    /**
     * 根据条件分页查询MdSortingBinDO对象列表
     *
     * @param queryRequest 查询条件
     * @return MdSortingBinDO对象列表
     */
    @PostMapping("/search")
    public JsonResult<PageData<MdSortingBinDTO>> query(@RequestBody MdSortingBinQueryRequest queryRequest) {
        PageData<MdSortingBinDTO> sortingBinDTOPageData = mdSortingBinService.query(queryRequest);
        return JsonResult.success(sortingBinDTOPageData);
    }

    /**
     * 插入MdSortingBinDO对象
     *
     * @param mdSortingBinDTO 要插入的对象
     * @return 插入成功的记录数
     */
    @PostMapping("/add")
    public JsonResult<Integer> add(@RequestBody MdSortingBinDTO mdSortingBinDTO) {
        int result = mdSortingBinService.add(mdSortingBinDTO);
        return JsonResult.success(result);
    }

    /**
     * 根据主键ID更新MdSortingBinDO对象
     *
     * @param id              主键ID
     * @param mdSortingBinDTO 要更新的对象
     * @return 更新成功的记录数
     */
    @PutMapping("/edit/{id}")
    public JsonResult<Integer> saveSortingBin(@PathVariable Long id, @RequestBody MdSortingBinDTO mdSortingBinDTO) {
        mdSortingBinDTO.setId(id);
        int result = mdSortingBinService.updateById(mdSortingBinDTO);
        return JsonResult.success(result);
    }

    /**
     * 根据主键ID删除MdSortingBinDO对象
     *
     * @param id 主键ID
     * @return 删除成功的记录数
     */
    @DeleteMapping("/delete/{id}")
    public JsonResult<Integer> deleteMdSortingBin(@PathVariable Long id) {
        int result = mdSortingBinService.deleteByPrimaryKey(id);
        return JsonResult.success(result);
    }

    /**
     * 批量停用分拣柜
     *
     * @param ids List of sorting bin IDs to disable
     * @return Result indicating success or failure
     */
    @PutMapping("/batchDisable")
    public JsonResult<Void> batchDisable(@RequestBody List<Long> ids) {
        mdSortingBinService.batchDisable(ids);
        return JsonResult.success(null);
    }

    /**
     * 获取可用的卷式发票打印机列表
     *
     * @return List of available roll-type printers
     */
    @GetMapping("/invoicePrinters")
    public JsonResult<List<PrintDTO>> getInvoicePrinterList() {
        List<PrintDTO> printers = printService.getAvailablePrintersByType(PrinterTypeEnum.ROLL.getDisplayName());
        return JsonResult.success(Objects.requireNonNullElseGet(printers, List::of));
    }
}