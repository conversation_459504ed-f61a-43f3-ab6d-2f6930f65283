package com.accesscorporate.app.wms.server.web.controller.rule;


import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulAllocationResponse;
import com.accesscorporate.app.wms.server.biz.service.rule.IRulAllocationService;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@RestController
@RequestMapping("/rule/allocation")
@RequiredArgsConstructor
@Validated
public class RulAllocationController {

    private final IRulAllocationService rulAllocationService;

    @PostMapping("/list")
    public JsonResult<PageData<RulAllocationResponse>> list(@RequestBody RulAllocationQueryPageRequest request) {
        return JsonResult.success(rulAllocationService.queryPage(request));
    }

    @GetMapping("/queryById")
    public JsonResult<RulAllocationResponse> queryById(@RequestParam("id") @NotNull Long id){
        RulAllocationResponse rulAllocationResponse = rulAllocationService.queryById(id);
        if (rulAllocationResponse == null) {
            return JsonResult.failure("通过id查询分配规则不存在");
        }
        return JsonResult.success(rulAllocationResponse);
    }

    @PostMapping("/save")
    public JsonResult<Boolean> save(@RequestBody RulAllocationRequest request) {
        return JsonResult.success(rulAllocationService.save(request));
    }


    @PostMapping("/deleteById")
    public JsonResult<Boolean> deleteById(@RequestParam("id") @NotNull Long id) {
        return JsonResult.success(rulAllocationService.deleteById(id));
    }
}
