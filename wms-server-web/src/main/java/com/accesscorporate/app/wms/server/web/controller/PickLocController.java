package com.accesscorporate.app.wms.server.web.controller;

import com.accesscorporate.app.wms.server.biz.params.request.pickloc.PickLocPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.request.pickloc.PickLocSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.pickloc.PickLocPageQueryResponse;
import com.accesscorporate.app.wms.server.biz.service.MdPickLocService;
import com.accesscorporate.app.wms.server.biz.service.dto.PickLocImportDTO;
import com.accesscorporate.app.wms.server.common.duplicateSubmit.PreventDuplicateSubmit;
import com.accesscorporate.app.wms.server.common.utils.EasyExcelUtils;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-20 10:51
 * Description: 货品捡货位管理
 */
@Tag(name = "货品捡货位管理")
@RestController
@RequestMapping(value = "/goods/pickLoc")
public class PickLocController {
    @Autowired
    private MdPickLocService MdPickLocService;

    @Operation(summary = "捡货位列表查询")
    @PostMapping("/page/query")
    public JsonResult<PageData<PickLocPageQueryResponse>> pageQuery(@RequestBody @Valid PickLocPageQueryRequest request) {
        return JsonResult.success(MdPickLocService.pageQuery(request));
    }

    @Operation(summary = "新增&编辑商品捡货位信息")
    @PostMapping("/saveOrUpdate")
    @PreventDuplicateSubmit
    public JsonResult<Void> saveOrUpdatePickLoc(@RequestBody @Valid PickLocSaveRequest request) {
        MdPickLocService.saveOrUpdatePickLoc(request);
        return JsonResult.success();
    }

    @Operation(summary = "删除商品捡货位信息")
    @GetMapping("/delete")
    public JsonResult<Void> removePickLoc(@RequestParam Long pickLocId) {
        MdPickLocService.deletePickLocById(pickLocId);
        return JsonResult.success();
    }

    @Operation(summary = "捡货位批量导入")
    @PostMapping("/import")
    public JsonResult<Void> batchImportPickLoc(@RequestParam("file") MultipartFile file){
        List<PickLocImportDTO> importList = EasyExcelUtils.reader(file, PickLocImportDTO.class);
        MdPickLocService.batchImportPickLoc(importList);
        return JsonResult.OK;
    }















}


