package com.accesscorporate.app.wms.server.web.controller;

import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.exception.common.ErrorCode;
import com.idanchuang.component.just.web.base.BaseController;
import com.idanchuang.component.just.web.dto.LoginSsoUserDTO;
import com.idanchuang.component.just.web.dto.LoginUserDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 聚合层接口样例
 *
 * <AUTHOR>
 * Created at 2020/5/14 13:03
 **/
@RestController
public class IndexController extends BaseController {

    @Value("${spring.application.name}")
    private String appId;

    /**
     * 简单接口
     * @return ..
     */
    @GetMapping("hello")
    public JsonResult<String> hello() {
        String message = "hello, i am " + appId;
        return JsonResult.success(message);
    }

    /**
     * 获取当前App登入用户信息的案例
     *
     * 前提:
     * 1.已接入网关并启用了登入拦截 (网关接入文档: https://access.feishu.cn/wiki/wikcnf2uMhO4VlEnmrm82puoXuf )
     *
     * @return 当前登入用户信息
     */
    @GetMapping("currentAppUser")
    public JsonResult<LoginUserDTO> currentAppUser() {
        // 如果没有继承BaseController, 可通过 RequestUtils.getUserDTO(request); 获取
        LoginUserDTO dto = getUserDTO();
        if (dto == null) {
            return JsonResult.failure(ErrorCode.UNAUTHORIZED, "登入信息不存在");
        }
        return JsonResult.success(dto);
    }

    /**
     * 获取当前后台登入用户信息的案例
     *
     * 前提:
     * 1.已接入网关并启用了登入拦截 (网关接入文档: https://access.feishu.cn/wiki/wikcnf2uMhO4VlEnmrm82puoXuf )
     *
     * @return 当前登入用户信息
     */
    @GetMapping("currentSsoUser")
    public JsonResult<LoginSsoUserDTO> currentSsoUser() {
        // 如果没有继承BaseController, 可通过 RequestUtils.getSsoUserDTO(request); 获取
        LoginSsoUserDTO dto = getSsoUserDTO();
        if (dto == null) {
            return JsonResult.failure(ErrorCode.UNAUTHORIZED, "登入信息不存在");
        }
        return JsonResult.success(dto);
    }

}
