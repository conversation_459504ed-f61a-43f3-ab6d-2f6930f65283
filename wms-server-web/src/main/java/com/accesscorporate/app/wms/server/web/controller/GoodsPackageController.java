package com.accesscorporate.app.wms.server.web.controller;

import com.accesscorporate.app.wms.server.biz.params.request.goodspackage.GoodsPackageItemSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.request.goodspackage.GoodsPackageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.request.goodspackage.GoodsPackageSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.goodspackage.GoodsPackageEditResponse;
import com.accesscorporate.app.wms.server.biz.params.response.goodspackage.GoodsPackageQueryResponse;
import com.accesscorporate.app.wms.server.biz.params.response.goodspackage.PackageInfoDetailResponse;
import com.accesscorporate.app.wms.server.biz.service.GoodsPackageService;
import com.accesscorporate.app.wms.server.common.duplicateSubmit.PreventDuplicateSubmit;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-02-20 10:38
 * Description:货品包装信息管理
 */
@Tag(name = "货品包装信息管理")
@RestController
@RequestMapping(value = "/goods/package")
public class GoodsPackageController {
    @Autowired
    private GoodsPackageService goodsPackageService;

    @Operation(summary = "新增&编辑包装信息")
    @PostMapping("/saveOrUpdate/package")
    @PreventDuplicateSubmit
    public JsonResult<Void> saveOrUpdateGoodsPackage(@RequestBody @Valid GoodsPackageSaveRequest request) {
        goodsPackageService.saveOrUpdateGoodsPackage(request);
        return JsonResult.success();
    }

    @Operation(summary = "包装信息列表查询")
    @PostMapping("/page/query")
    public JsonResult<PageData<GoodsPackageQueryResponse>> pageQuery(@RequestBody @Valid GoodsPackageQueryRequest request) {
        return JsonResult.success(goodsPackageService.pageQuery(request));
    }


    @Operation(summary = "点击列表包装代码，编辑包装信息")
    @GetMapping("/edit/goodsPackage")
    public JsonResult<GoodsPackageEditResponse> editGoodsPackage(@RequestParam Long packageId) {
        GoodsPackageEditResponse goodsPackageEditResponse = goodsPackageService.editGoodsPackage(packageId);
        return JsonResult.success(goodsPackageEditResponse);
    }

    @Operation(summary = "点击列表商品编码，编辑包装明细信息响应体")
    @GetMapping("/queryItems")
    public JsonResult<List<PackageInfoDetailResponse>> editGoodsPackageQueryItems(@RequestParam Long packageId) {
        List<PackageInfoDetailResponse> packageInfoDetailResponses = goodsPackageService.editGoodsPackageQueryItems(packageId);
        return JsonResult.success(packageInfoDetailResponses);
    }

    @Operation(summary = "新增&编辑包装信息明细")
    @PostMapping("/saveOrUpdate/package/item")
    @PreventDuplicateSubmit
    public JsonResult<Void> saveOrUpdateGoodsPackageItem(@RequestBody @Valid GoodsPackageItemSaveRequest request) {
        goodsPackageService.saveOrUpdateGoodsPackageItem(request);
        return JsonResult.success();
    }

    @Operation(summary = "删除包装明细信息")
    @GetMapping("/delete/goodsPackageItem")
    public JsonResult<Void> deleteGoodsPackageItem(@RequestParam Long packageDetailId) {
        goodsPackageService.deleteGoodsPackageItem(packageDetailId);
        return JsonResult.success();
    }



}
