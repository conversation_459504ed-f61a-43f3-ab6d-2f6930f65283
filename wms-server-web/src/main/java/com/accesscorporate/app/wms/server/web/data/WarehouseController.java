package com.accesscorporate.app.wms.server.web.data;

import com.accesscorporate.app.wms.server.biz.service.WarehouseService;
import com.accesscorporate.app.wms.server.dal.entity.Warehouse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.idanchuang.component.base.page.PageData.adaptLimit;
import static com.idanchuang.component.base.page.PageData.adaptPageNo;

@RestController
@RequestMapping("/warehouses")
public class WarehouseController {

    @Autowired
    private WarehouseService warehouseService;

    @GetMapping("page")
    public JsonResult<PageData<Warehouse>> page(Integer current, Integer size,String warehouseCode,String warehouseName) {
        LambdaQueryWrapper<Warehouse> wrapper = new LambdaQueryWrapper();
        wrapper.like(StringUtils.isNotBlank(warehouseName),Warehouse::getWarehouseName,warehouseName)
                .like(StringUtils.isNotBlank(warehouseCode),Warehouse::getWarehouseCode,warehouseCode);
        Page<Warehouse> page = warehouseService.page(new Page<>(adaptPageNo(current), adaptLimit(size)), wrapper);
        PageData<Warehouse> pageData = PageData.of(page.getRecords(), page.getSize(), page.getTotal());
        return JsonResult.success(pageData);
    }
}