package com.accesscorporate.app.wms.server.web.controller;

import com.accesscorporate.app.wms.server.biz.excel.listener.CargoOwnerImportListener;
import com.accesscorporate.app.wms.server.biz.excel.model.CargoOwnerImportModel;
import com.accesscorporate.app.wms.server.biz.params.request.CargoOwnerPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.response.CargoOwnerInfoResponse;
import com.accesscorporate.app.wms.server.biz.service.ICargoOwnerService;
import com.alibaba.excel.EasyExcel;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 货主管理-控制层
 *
 * <AUTHOR>
 * 2025/2/8  16:14
 */
@RestController
@RequestMapping("/cargoOwner")
@RequiredArgsConstructor
@Tag(name = "货主管理")
@Slf4j
public class CargoOwnerController {

    private final ICargoOwnerService iSupplierService;


    @Operation(summary = "货主列表查询", description = "货主列表查询-分页-多条件")
    @PostMapping("/pageList")
    public JsonResult<PageData<CargoOwnerInfoResponse>> queryCargoOwnerPage(@RequestBody CargoOwnerPageQueryRequest request) {
        return JsonResult.success(iSupplierService.queryCargoOwnerPage(request));
    }

    @PostMapping("/import")
    @Operation(summary = "货主信息导入", description = "导入货主信息,OBS模版链接:[https://zkt-pro-bucket.obs.cn-east-3.myhuaweicloud.com/AWMS/AWMS-货主导入模版.xlsx]")
    public JsonResult<Boolean> importCargoOwnerInfo(@Parameter(name = "file", description = "excel文件")
                                                    @RequestParam("file") MultipartFile file) {

        CargoOwnerImportListener listener = new CargoOwnerImportListener();
        try {

            List<CargoOwnerImportModel> cargoOwnerImportModels = EasyExcel.read(file.getInputStream(), CargoOwnerImportModel.class, listener)
                    .sheet() // 默认读取第一个工作表
                    .doReadSync();

            return JsonResult.success(iSupplierService.importCargoOwnerProcessItems(cargoOwnerImportModels));
        } catch (Exception e) {
            log.error("导入失败{}", e.getMessage());
            return JsonResult.failure("导入失败" + e.getMessage());
        }
    }


}
