package com.accesscorporate.app.wms.server.web.controller.rule;

import com.accesscorporate.app.wms.server.biz.service.rule.IRulCommonService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version JDK 17
 * @date 2025/2/26
 * @description
 */
@RestController
@RequestMapping("/rule/common")
@RequiredArgsConstructor
@Validated
public class RuleCommonController {

    private final IRulCommonService rulCommonService;

    // 规则公共接口
    @GetMapping
    public void getDirectionRule(){

    }
}
