package com.accesscorporate.app.wms.server.web;

import cn.hutool.extra.spring.EnableSpringUtil;
import com.idanchuang.component.core.ServiceInfo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * <AUTHOR>
 * Created at 2020/5/14 12:45
 **/
@SpringBootApplication
@ComponentScan(value = {"com.accesscorporate.app.wms.*",
})
@EnableFeignClients({
        "com.idanchuang",
        "com.accesscorporate",
        "com.idanchuang.sso.external.facade",
        "com.danchuang.global.scm.address.client.*"
})
@EnableSpringUtil
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);

        System.out.println("Open http://" + ServiceInfo.address() + "/doc.html ");
    }

}
