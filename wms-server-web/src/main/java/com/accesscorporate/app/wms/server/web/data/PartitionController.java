package com.accesscorporate.app.wms.server.web.data;

import com.accesscorporate.app.wms.server.biz.service.PartitionService;
import com.accesscorporate.app.wms.server.dal.entity.Partition;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/partitions")
public class PartitionController {

    @Autowired
    private PartitionService partitionService;

    @GetMapping
    public JsonResult<List<Partition>> getAllPartitions() {
        return JsonResult.success(partitionService.list());
    }

    @GetMapping("/{id}")
    public JsonResult<Partition> getPartitionById(@PathVariable Long id) {
        return JsonResult.success(partitionService.getById(id));
    }

    @PostMapping("/save")
    public JsonResult<Boolean> savePartition(@RequestBody Partition partition) {
        return JsonResult.success(partitionService.save(partition));
    }


    @DeleteMapping("/{id}")
    public JsonResult<Boolean> deletePartition(@PathVariable Long id) {
        return JsonResult.success( partitionService.removeById(id));
    }

    /**
     *
     * @param current 当前页
     * @param size 一页数量
     * @param partitionCode 库区编码
     * @param description 描述
     * @param regionCode 所属区域编码
     * @return
     */
    @GetMapping("page")
    public JsonResult<PageData<Partition>> page(Integer current, Integer size, String partitionCode,String description,String regionCode) {
        Page<Partition> page = partitionService.page(current,size,partitionCode,description,regionCode);
        PageData<Partition> pageData = PageData.of(page.getRecords(), page.getSize(), page.getTotal());
        return JsonResult.success(pageData);
    }
}