package com.accesscorporate.app.wms.server.web.controller.rule;


import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayHQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayHRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulPutawayHResponse;
import com.accesscorporate.app.wms.server.biz.service.rule.IRulPutawayHService;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@RestController
@RequestMapping("/rule/putawayH")
@RequiredArgsConstructor
@Validated
public class RulPutawayHController {

    private final IRulPutawayHService rulPutawayHService;


    @PostMapping("/list")
    public JsonResult<PageData<RulPutawayHResponse>> list(@RequestBody RulPutawayHQueryPageRequest request) {
        return JsonResult.success(rulPutawayHService.queryPage(request));
    }

    @GetMapping("/queryById")
    public JsonResult<RulPutawayHResponse> queryById(@RequestParam("id") @NotNull Long id) {
        RulPutawayHResponse rulPutawayHResponse = rulPutawayHService.queryById(id);
        if (rulPutawayHResponse == null) {
            return JsonResult.failure("通过id查询上架规则不存在");
        }
        return JsonResult.success(rulPutawayHResponse);
    }

    @PostMapping("/save")
    public JsonResult<Boolean> save(@RequestBody RulPutawayHRequest request) {
        return JsonResult.success(rulPutawayHService.save(request));
    }


}
