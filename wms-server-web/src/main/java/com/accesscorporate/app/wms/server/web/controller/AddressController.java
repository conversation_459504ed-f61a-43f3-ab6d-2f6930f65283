package com.accesscorporate.app.wms.server.web.controller;

import com.accesscorporate.app.wms.server.biz.params.request.AddressFilter;
import com.accesscorporate.app.wms.server.biz.service.AddressService;
import com.accesscorporate.app.wms.server.biz.service.dto.AddressDTO;
import com.accesscorporate.app.wms.server.biz.service.dto.AddressInfoDTO;
import com.accesscorporate.app.wms.server.biz.service.dto.SelectItem;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 地址管理控制器（替代原AddressAction）
 */
@RestController
@RequestMapping("/wms/data/address")
public class AddressController {

    @Resource
    private AddressService addressService;

    /**
     * 分页查询地址信息
     */
    @GetMapping("/list")
    @Operation(summary = "分页查询地址信息")
    public JsonResult<PageData<AddressInfoDTO>> query(@ModelAttribute AddressFilter filter) {
        PageData<AddressInfoDTO> page = addressService.queryAddressInfo(filter);
        return JsonResult.success(page);
    }

    /**
     * 新增/修改地址及运输温度
     */
    @PostMapping("/edit")
    @Operation(summary = "新增/修改地址及运输温度")
    public JsonResult<Boolean> save(@RequestBody AddressDTO address) {
        addressService.save(address.getParentCode(), address.getAddressCode(), address.getTransportWendy());
        return JsonResult.success(true);
    }

    /**
     * 新增/修改地址
     */
    @PostMapping("/saveOrUpdate")
    @Operation(summary = "直接新增/修改地址,字段传空也会更新DB")
    public JsonResult<Boolean> saveOrUpdate(@RequestBody AddressDTO address) {
        return JsonResult.success(addressService.saveOrUpdate(address));
    }

    /**
     * 删除地址
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "逻辑删除地址")
    public JsonResult<Boolean> delete(@PathVariable Long id) {
        return JsonResult.success(addressService.deleteById(id));
    }

    /**
     * 恢复被逻辑删除的地址
     */
    @PutMapping("/recover/{id}")
    @Operation(summary = "恢复被逻辑删除的地址")
    public JsonResult<Boolean> recover(@PathVariable Long id) {
        return JsonResult.success(addressService.recoverById(id));
    }

    /**
     * 根据ID查询地址信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询地址信息")
    public JsonResult<AddressInfoDTO> getById(@PathVariable Long id) {
        return JsonResult.success(addressService.getById(id));
    }

    /**
     * 获取所有国家（原findCountryList逻辑）
     */
    @GetMapping("/country/list")
    @Operation(summary = "获取所有国家")
    public JsonResult<List<AddressDTO>> getAllCountry() {
        return JsonResult.success(addressService.getAllCountry());
    }

    /**
     * 根据国家编码获取省份列表
     */
    @GetMapping("/province/list")
    @Operation(summary = "根据国家编码获取省份列表")
    public JsonResult<List<AddressDTO>> getProvinceList(@RequestParam("countryCode") String countryCode) {
        return JsonResult.success(addressService.getProvinceList(countryCode));
    }

    /**
     * 根据父编码查询地址列表（原changeListForProvince逻辑）
     */
    @GetMapping("/listByParentCode")
    @Operation(summary = "根据父编码查询地址列表")
    public JsonResult<List<AddressDTO>> listByParentCode(@RequestParam("parentCode") String parentCode) {
        return JsonResult.success(addressService.findAddressListByParentCode(parentCode));
    }

    /**
     * 查询指定地址Code下的地址库列表
     */
    @GetMapping("/address/list/{addressCode}")
    @Operation(summary = "查询指定地址Code下的地址库列表")
    public JsonResult<List<SelectItem>> findLibCountryList(@PathVariable("addressCode") String addressCode) {
        List<AddressDTO> countries = addressService.findAddressListByParentCode(addressCode);
        List<SelectItem> result = countries.stream()
                .map(dto -> {
                    SelectItem item = new SelectItem();
                    item.setLabel(dto.getName());
                    item.setValue(dto.getAddressCode());
                    return item;
                }).toList();
        return JsonResult.success(result);
    }
}