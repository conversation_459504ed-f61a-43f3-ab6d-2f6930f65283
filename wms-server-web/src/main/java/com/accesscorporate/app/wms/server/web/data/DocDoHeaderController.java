package com.accesscorporate.app.wms.server.web.data;

import com.accesscorporate.app.wms.server.biz.params.request.DocDoHeaderPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.request.DocDoHeaderSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocDoHeaderResponse;
import com.accesscorporate.app.wms.server.biz.service.DocDoHeaderService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 发货单头表Controller
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@RestController
@RequestMapping("/api/doc-do-header")
@Tag(name = "发货单头表管理")
public class DocDoHeaderController {

    @Autowired
    private DocDoHeaderService docDoHeaderService;

    @GetMapping("/list")
    @Operation(summary = "查询所有发货单")
    public JsonResult<List<DocDoHeaderResponse>> listDocDoHeaders() {
        return JsonResult.success(docDoHeaderService.listAll());
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询发货单")
    public JsonResult<DocDoHeaderResponse> getDocDoHeader(@PathVariable Long id) {
        return JsonResult.success(docDoHeaderService.get(id));
    }

    @PostMapping
    @Operation(summary = "新增发货单")
    public JsonResult<Boolean> saveDocDoHeader(@Valid @RequestBody DocDoHeaderSaveRequest request) {
        return JsonResult.success(docDoHeaderService.save(request));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "根据ID删除发货单")
    public JsonResult<Boolean> deleteDocDoHeader(@PathVariable Long id) {
        return JsonResult.success(docDoHeaderService.removeById(id));
    }

    @PostMapping("/page")
    @Operation(summary = "分页查询发货单")
    public JsonResult<PageData<DocDoHeaderResponse>> pageDocDoHeaders(@Valid @RequestBody DocDoHeaderPageQueryRequest request) {
        Page<DocDoHeaderResponse> page = docDoHeaderService.page(request);
        return JsonResult.success(PageData.of(page.getRecords(), page.getSize(), page.getTotal()));
    }

    @GetMapping("/by-do-no/{doNo}")
    @Operation(summary = "根据发货单号查询")
    public JsonResult<DocDoHeaderResponse> getByDoNo(@PathVariable String doNo) {
        var docDoHeader = docDoHeaderService.queryByDoNo(doNo);
        if (docDoHeader == null) {
            return JsonResult.success(null);
        }
        return JsonResult.success(docDoHeaderService.get(docDoHeader.getId()));
    }

    @GetMapping("/by-orig-id/{origId}")
    @Operation(summary = "根据原始ID查询")
    public JsonResult<DocDoHeaderResponse> getByOrigId(@PathVariable String origId) {
        var docDoHeader = docDoHeaderService.queryByOrigId(origId);
        if (docDoHeader == null) {
            return JsonResult.success(null);
        }
        return JsonResult.success(docDoHeaderService.get(docDoHeader.getId()));
    }

    @GetMapping("/by-tracking-no/{trackingNo}")
    @Operation(summary = "根据运单号查询")
    public JsonResult<List<DocDoHeaderResponse>> getByTrackingNo(@PathVariable String trackingNo) {
        var docDoHeaders = docDoHeaderService.queryByTrackingNo(trackingNo);
        List<DocDoHeaderResponse> responses = docDoHeaders.stream()
                .map(docDoHeader -> docDoHeaderService.get(docDoHeader.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @GetMapping("/by-mobile/{mobile}")
    @Operation(summary = "根据手机号查询")
    public JsonResult<List<DocDoHeaderResponse>> getByMobile(@PathVariable String mobile) {
        var docDoHeaders = docDoHeaderService.queryByMobile(mobile);
        List<DocDoHeaderResponse> responses = docDoHeaders.stream()
                .map(docDoHeader -> docDoHeaderService.get(docDoHeader.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @GetMapping("/by-wave-id/{waveId}")
    @Operation(summary = "根据波次ID查询")
    public JsonResult<List<DocDoHeaderResponse>> getByWaveId(@PathVariable Long waveId) {
        var docDoHeaders = docDoHeaderService.queryByWaveId(waveId);
        List<DocDoHeaderResponse> responses = docDoHeaders.stream()
                .map(docDoHeader -> docDoHeaderService.get(docDoHeader.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @GetMapping("/by-status/{status}")
    @Operation(summary = "根据状态查询")
    public JsonResult<List<DocDoHeaderResponse>> getByStatus(@PathVariable String status) {
        var docDoHeaders = docDoHeaderService.queryByStatus(status);
        List<DocDoHeaderResponse> responses = docDoHeaders.stream()
                .map(docDoHeader -> docDoHeaderService.get(docDoHeader.getId()))
                .toList();
        return JsonResult.success(responses);
    }

    @GetMapping("/by-do-type/{doType}")
    @Operation(summary = "根据订单类型查询")
    public JsonResult<List<DocDoHeaderResponse>> getByDoType(@PathVariable String doType) {
        var docDoHeaders = docDoHeaderService.queryByDoType(doType);
        List<DocDoHeaderResponse> responses = docDoHeaders.stream()
                .map(docDoHeader -> docDoHeaderService.get(docDoHeader.getId()))
                .toList();
        return JsonResult.success(responses);
    }
}
