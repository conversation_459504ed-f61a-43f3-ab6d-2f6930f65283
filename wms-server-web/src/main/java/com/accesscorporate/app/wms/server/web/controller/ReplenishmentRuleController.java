package com.accesscorporate.app.wms.server.web.controller;

import com.accesscorporate.app.wms.server.biz.params.request.*;
import com.accesscorporate.app.wms.server.biz.params.response.ReplenishmentRuleDetailResponse;
import com.accesscorporate.app.wms.server.biz.params.response.ReplenishmentRuleResponse;
import com.accesscorporate.app.wms.server.biz.service.IReplenishmentRuleService;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 补货规则-控制层
 *
 * <AUTHOR>
 * 2025/2/8  16:09
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "补货规则管理")
@RequestMapping("/replenishmentRule")
public class ReplenishmentRuleController {

    private final IReplenishmentRuleService iReplenishmentRuleService;

    @Operation(summary = "补货规则新增")
    @PostMapping
    @Validated
    public JsonResult<Boolean> createReplenishmentRule(@RequestBody ReplenishmentRuleCreateRequest request) {
        return JsonResult.success(iReplenishmentRuleService.createReplenishmentRule(request));
    }

    @Operation(summary = "补货规则更新")
    @PutMapping
    @Validated
    public JsonResult<Boolean> modifyReplenishmentRule(@RequestBody ReplenishmentRuleModifyRequest request) {
        return JsonResult.success(iReplenishmentRuleService.modifyReplenishmentRule(request));
    }

    @Operation(summary = "补货规则删除")
    @DeleteMapping("/{id}")
    public JsonResult<Boolean> removeReplenishmentRule(
            @NotNull(message = "id 不能为空") @PathVariable("id") Long id) {
        return JsonResult.success(iReplenishmentRuleService.removeReplenishmentRule(id));
    }

    @Operation(summary = "补货规则列表查询")
    @PostMapping("/pageList")
    public JsonResult<PageData<ReplenishmentRuleResponse>> queryReplenishmentRulePage(@RequestBody ReplenishmentRuleQueryRequest request) {
        return JsonResult.success(iReplenishmentRuleService.queryReplenishmentRules(request));
    }

    /*------------------------ 补货规则明细接口 start...... --------------------------*/

    @Operation(summary = "补货规则明细查询", description = "根据主规则的ID查询补货规则明细")
    @GetMapping("/detail/{ruleId}")
    @Validated
    public JsonResult<List<ReplenishmentRuleDetailResponse>> queryReplenishmentRuleDetails(
            @NotNull(message = "ruleId 不能为空") @PathVariable("ruleId") Long ruleId) {
        return JsonResult.success(iReplenishmentRuleService.queryRuleDetails(ruleId));
    }

    @Operation(summary = "补货规则明细删除", description = "根据明细ID进行")
    @DeleteMapping("/detail/{id}")
    @Validated
    public JsonResult<Boolean> removeReplenishmentRuleDetail(
            @NotNull(message = "id 不能为空") @PathVariable("id") Long id) {
        return JsonResult.success(iReplenishmentRuleService.removeReplenishmentRuleDetail(id));
    }

    @Operation(summary = "补货规则明细新增", description = "规则明细新增")
    @PostMapping("/detail")
    @Validated
    public JsonResult<Boolean> createReplenishmentRuleDetail(@RequestBody ReplenishmentRuleDetailAddRequest request) {
        return JsonResult.success(iReplenishmentRuleService.createReplenishmentRuleDetail(request));
    }

    @Operation(summary = "补货规则明细编辑", description = "规则明细编辑")
    @PutMapping("/detail")
    @Validated
    public JsonResult<Boolean> modifyReplenishmentRuleDetail(@RequestBody ReplenishmentRuleDetailModifyRequest request) {
        return JsonResult.success(iReplenishmentRuleService.modifyReplenishmentRuleDetail(request));
    }


}
