package com.accesscorporate.app.wms.server.web.controller.common;

import com.accesscorporate.app.wms.server.biz.params.response.CommonSelectorResponse;
import com.accesscorporate.app.wms.server.biz.service.CommonSelectorService;
import com.idanchuang.component.base.JsonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Author: tianKun
 * Company: 浙江单创品牌管理有限公司
 * CreateTime: 2025-06-23 16:28
 * Description: 通用调用
 */
@Tag(name = "通用接口调用")
@RestController
@RequestMapping(value = "/common")
public class CommonSelectorController {

    @Autowired
    private CommonSelectorService commonSelectorService;

    @Operation(summary = "统一下拉框列表入口")
    @GetMapping("/selector")
    public JsonResult<List<CommonSelectorResponse>> queryCommonSelectorList(@RequestParam String selectorType) {
        List<CommonSelectorResponse> commonSelectorResponses = commonSelectorService.queryCommonSelectors(selectorType);
        return JsonResult.success(commonSelectorResponses);
    }




}
