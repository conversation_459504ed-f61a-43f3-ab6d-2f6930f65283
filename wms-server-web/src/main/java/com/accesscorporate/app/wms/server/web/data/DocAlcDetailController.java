package com.accesscorporate.app.wms.server.web.data;

import com.accesscorporate.app.wms.server.biz.params.request.DocAlcDetailSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocAlcDetailResponse;
import com.accesscorporate.app.wms.server.biz.service.DocAlcDetailService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/doc-alc-details")
public class DocAlcDetailController {

  @Autowired
  private DocAlcDetailService docAlcDetailService;

  @GetMapping
  public JsonResult<List<DocAlcDetailResponse>> getAllDocAlcDetails() {
    return JsonResult.success(docAlcDetailService.listAll());
  }

  @GetMapping("/{id}")
  public JsonResult<DocAlcDetailResponse> getDocAlcDetailById(@PathVariable Long id) {
    return JsonResult.success(docAlcDetailService.get(id));
  }

  @PostMapping("/save")
  public JsonResult<Boolean> saveDocAlcDetail(@RequestBody DocAlcDetailSaveRequest docAlcDetail) {
    return JsonResult.success(docAlcDetailService.save(docAlcDetail));
  }

  @DeleteMapping("/{id}")
  public JsonResult<Boolean> deleteDocAlcDetail(@PathVariable Long id) {
    return JsonResult.success(docAlcDetailService.removeById(id));
  }

  @GetMapping("/page")
  public JsonResult<PageData<DocAlcDetailResponse>> pageDocAlcDetails(
      @RequestParam(defaultValue = "1") Integer current,
      @RequestParam(defaultValue = "10") Integer size,
      @RequestParam(required = false) Long doHeaderId,
      @RequestParam(required = false) Long skuId,
      @RequestParam(required = false) String linestatus) {
    Page<DocAlcDetailResponse> page = docAlcDetailService.page(current, size, doHeaderId, skuId, linestatus);
    return JsonResult.success(PageData.of(page.getRecords(), page.getSize(), page.getTotal()));
  }

  @GetMapping("/by-header/{doHeaderId}")
  public JsonResult<List<DocAlcDetailResponse>> getByHeaderId(@PathVariable Long doHeaderId) {
    return JsonResult.success(docAlcDetailService.listByHeaderId(doHeaderId));
  }
}
