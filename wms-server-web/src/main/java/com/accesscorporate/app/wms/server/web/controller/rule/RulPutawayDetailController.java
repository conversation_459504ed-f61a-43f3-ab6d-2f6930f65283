package com.accesscorporate.app.wms.server.web.controller.rule;


import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayDetailQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayDetailRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulPutawayDetailResponse;
import com.accesscorporate.app.wms.server.biz.service.rule.IRulPutawayDetailService;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@RestController
@RequestMapping("/rule/putawayDetail")
@RequiredArgsConstructor
public class RulPutawayDetailController {

    private final IRulPutawayDetailService rulPutawayDetailService;

    @PostMapping("/list")
    public JsonResult<PageData<RulPutawayDetailResponse>> list(@RequestBody RulPutawayDetailQueryPageRequest request) {
        return JsonResult.success(rulPutawayDetailService.queryPage(request));
    }

    @GetMapping("/queryById")
    public JsonResult<RulPutawayDetailResponse> queryById(@RequestParam("id") @NotNull Long id) {
        RulPutawayDetailResponse rulPutawayDetailResponse = rulPutawayDetailService.queryById(id);
        if (rulPutawayDetailResponse == null) {
            return JsonResult.failure("通过id查询上架规则明细不存在");
        }
        return JsonResult.success(rulPutawayDetailResponse);
    }

    @PostMapping("/save")
    public JsonResult<Boolean> save(@RequestBody RulPutawayDetailRequest request) {
        return JsonResult.success(rulPutawayDetailService.save(request));
    }

    @GetMapping("/deleteById")
    public JsonResult<Boolean> deleteById(@RequestParam("id") @NotNull Long id) {
        return JsonResult.success(rulPutawayDetailService.deleteById(id));
    }

}
