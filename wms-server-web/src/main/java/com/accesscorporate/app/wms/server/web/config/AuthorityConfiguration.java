package com.accesscorporate.app.wms.server.web.config;

import cn.hutool.core.util.StrUtil;
import com.accesscorporate.app.wms.server.common.ErrorCode;
import com.accesscorporate.app.wms.server.common.context.UserContext;
import com.accesscorporate.app.wms.server.integration.dto.UserDetailResponse;
import com.accesscorporate.app.wms.server.integration.wrap.IUserInfoWrap;
import com.alibaba.csp.sentinel.util.AssertUtil;
import com.idanchuang.component.base.exception.core.ExFactory;
import com.idanchuang.component.config.apollo.util.SpringContextUtil;
import com.idanchuang.component.core.util.StringUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.Nullable;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Optional;

@Configuration
public class AuthorityConfiguration implements WebMvcConfigurer {


    private static final String TOKEN_KEY = "token";


    @Bean
    public AuthorityInterceptor authorityInterceptor() {
        return new AuthorityInterceptor();
    }

    @Bean
    public AuthoritySpecialInterceptor authoritySpecialInterceptor() {
        return new AuthoritySpecialInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authorityInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns("/authPermission/**");

        // 此拦截器主要是针对 --经过Token校验但是没有进行选择 当前仓-当前租户的API接口场景
        registry.addInterceptor(authoritySpecialInterceptor())
                .addPathPatterns("/authPermission/**");
    }

    public static class AuthoritySpecialInterceptor implements HandlerInterceptor {
        @Override
        public boolean preHandle(@Nullable HttpServletRequest request, @Nullable HttpServletResponse response, @Nullable Object handler) {
            if (request == null) {
                throw new IllegalStateException("HttpServletRequest is null");
            }
            IUserInfoWrap iUserInfoWrap = SpringContextUtil.getBean(IUserInfoWrap.class);
            String token = request.getHeader(TOKEN_KEY);
            if (token == null) {
                token = request().getParameter(TOKEN_KEY);
            }

            UserDetailResponse userInfoByToken = iUserInfoWrap.getUserInfoByToken(token);
            UserContext.setUserId(userInfoByToken.getUserId());
            UserContext.setUsername(userInfoByToken.getUserName());
            UserContext.setAccount(userInfoByToken.getAccount());
            return true;
        }

        @Override
        public void postHandle(@Nullable HttpServletRequest request, @Nullable HttpServletResponse response, @Nullable Object handler, ModelAndView modelAndView) {
            UserContext.removeAll();
        }
    }


    public static class AuthorityInterceptor implements HandlerInterceptor {

        @Override
        public boolean preHandle(@Nullable HttpServletRequest request, @Nullable HttpServletResponse response, @Nullable Object handler) {
            if (request == null) {
                throw new IllegalStateException("HttpServletRequest is null");
            }
            UserDetailResponse userInfo = getUserInfo(request);

            UserContext.setUserId(Optional.ofNullable(userInfo).map(UserDetailResponse::getUserId).orElse(null));
            UserContext.setUsername(Optional.ofNullable(userInfo).map(UserDetailResponse::getUserName).orElse(""));
            UserContext.setAccount(Optional.ofNullable(userInfo).map(UserDetailResponse::getAccount).orElse(""));

            String twTag = request.getHeader("twTag");
            AssertUtil.isTrue(StrUtil.isNotBlank(twTag), "租户&仓 标志符为空");
            String[] split = twTag.split("_");
            if (split.length != 2 || StrUtil.isBlank(split[0]) || StrUtil.isBlank(split[1])) {
                ExFactory.throwBusiness(ErrorCode.ILLEGAL_HEADER, "租户&仓 标志符格式错误");
            }
            UserContext.setTenantId(Long.valueOf(split[0]));
            UserContext.setCurrentWarehouseId(Long.valueOf(split[1]));
            return true;
        }

        @Override
        public void postHandle(@Nullable HttpServletRequest request, @Nullable HttpServletResponse response, @Nullable Object handler,
                               @Nullable ModelAndView modelAndView) {
            UserContext.removeAll();
        }


        /**
         * 获取用户信息
         *
         * @return vo
         */
        public static UserDetailResponse getUserInfo(HttpServletRequest request) {
            IUserInfoWrap iUserInfoWrap = SpringContextUtil.getBean(IUserInfoWrap.class);
            String token = request.getHeader(TOKEN_KEY);
            if (token == null) {
                token = request().getParameter(TOKEN_KEY);
            }
            if (StringUtil.isEmpty(token)) {
                throw ExFactory.throwWith(ErrorCode.FORBIDDEN_REQUEST, "请先登录");
            }
            return iUserInfoWrap.getUserInfoByToken(token);
        }
    }

    private static HttpServletRequest request() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        AssertUtil.notNull(servletRequestAttributes, "request is null");
        return servletRequestAttributes.getRequest();
    }

}
