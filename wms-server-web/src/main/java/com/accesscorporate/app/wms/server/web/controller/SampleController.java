package com.accesscorporate.app.wms.server.web.controller;

import com.accesscorporate.app.wms.server.biz.service.SampleService;
import com.accesscorporate.app.wms.server.dal.entity.Sample;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.exception.common.ErrorCode;
import com.idanchuang.component.base.page.PageData;
import com.idanchuang.component.just.web.base.BaseController;
import com.idanchuang.component.just.web.dto.LoginSsoUserDTO;
import com.idanchuang.component.just.web.dto.LoginUserDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

/**
 * 聚合层接口样例
 *
 * <AUTHOR>
 * Created at 2020/5/14 13:03
 **/
@RestController
@RequestMapping("sample")
public class SampleController extends BaseController {

    @Autowired
    private SampleService sampleService;

    @PostMapping("add")
    public JsonResult<Long> add(@RequestBody Sample sample) {
        sampleService.save(sample);
        return JsonResult.success(sample.getId());
    }

    @GetMapping("page")
    public JsonResult<PageData<Sample>> page(Integer pageNo, Integer limit) {
        Page<Sample> page = sampleService.page(new Page<>(adaptPageNo(pageNo), adaptLimit(limit)));
        PageData<Sample> pageData = PageData.of(page.getRecords(), page.getSize(), page.getTotal());
        return JsonResult.success(pageData);
    }

}
