package com.accesscorporate.app.wms.server.web.controller;

import com.accesscorporate.app.wms.server.biz.excel.listener.SupplierImportImportListener;
import com.accesscorporate.app.wms.server.biz.excel.model.SupplierImportModel;
import com.accesscorporate.app.wms.server.biz.params.request.SupplierInfoModifyRequest;
import com.accesscorporate.app.wms.server.biz.params.request.SupplierPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.response.SupplierInfoResponse;
import com.accesscorporate.app.wms.server.biz.service.ISupplierService;
import com.alibaba.excel.EasyExcel;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 供应商管理-控制层
 *
 * <AUTHOR>
 * 2025/2/8  16:14
 */
@RestController
@RequestMapping("/supplier")
@RequiredArgsConstructor
@Tag(name = "供应商管理")
@Slf4j
public class SupplierController {


    private final ISupplierService iSupplierService;


    @Operation(summary = "供应商修改", description = "供应商修改")
    @PutMapping
    @Validated
    public JsonResult<Boolean> modifySupplier(@RequestBody SupplierInfoModifyRequest request) {
        Boolean resBool = iSupplierService.modifySupplier(request);
        return JsonResult.success(resBool);
    }


    @Operation(summary = "供应商列表查询", description = "供应商列表查询-分页-多条件")
    @PostMapping("/pageList")
    public JsonResult<PageData<SupplierInfoResponse>> querySupplierPage(@RequestBody SupplierPageQueryRequest request) {
        return JsonResult.success(iSupplierService.querySupplierPage(request));
    }


    @Operation(summary = "供应商信息批量导入", description = "供应商信息批量导入")
    @PostMapping("/import")
    public JsonResult<Boolean> importSupplierInfo(@Parameter(name = "file", description = "excel文件")
                                                  @RequestParam("file") MultipartFile file) {


        SupplierImportImportListener listener = new SupplierImportImportListener();
        try {
            List<SupplierImportModel> supplierImportModels = EasyExcel.read(file.getInputStream(), SupplierImportModel.class, listener)
                    .sheet() // 默认读取第一个工作表
                    .doReadSync();
            return JsonResult.success(iSupplierService.importSupplierProcessItems(supplierImportModels));
        } catch (Exception e) {
            log.error("导入失败{}", e.getMessage());
            return JsonResult.failure("导入失败" + e.getMessage());
        }
    }


}
