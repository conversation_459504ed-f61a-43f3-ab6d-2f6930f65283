package com.accesscorporate.app.wms.server.web.data;

import com.accesscorporate.app.wms.server.biz.service.RegionService;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import com.accesscorporate.app.wms.server.dal.entity.Region;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.idanchuang.component.base.page.PageData.adaptLimit;
import static com.idanchuang.component.base.page.PageData.adaptPageNo;

@RestController
@RequestMapping("/regions")
public class RegionController {

    @Autowired
    private RegionService regionService;

    @PostMapping("save")
    public JsonResult<Boolean> save(@RequestBody Region region) {
        region.setWarehouseId(UserContextAssistant.getCurrentWarehouseId());
        return JsonResult.success(regionService.modify(region));
    }

    @GetMapping("/{id}")
    public JsonResult<Region> getById(@PathVariable Integer id) {
        return JsonResult.success(regionService.getById(id));
    }

    @GetMapping("page")
    public JsonResult<PageData<Region>> page(Integer current, Integer size, String regionCode) {
        LambdaQueryWrapper<Region> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Region::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        wrapper.like(StringUtils.isNotBlank(regionCode), Region::getRegionName, regionCode);
        Page<Region> page = regionService.page(new Page<>(adaptPageNo(current), adaptLimit(size)), wrapper);
        PageData<Region> pageData = PageData.of(page.getRecords(), page.getSize(), page.getTotal());
        return JsonResult.success(pageData);
    }

    @GetMapping("/list")
    public JsonResult<List<Region>> getAllPartitions() {
        LambdaQueryWrapper<Region> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Region::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        return JsonResult.success(regionService.list(wrapper));
    }
}