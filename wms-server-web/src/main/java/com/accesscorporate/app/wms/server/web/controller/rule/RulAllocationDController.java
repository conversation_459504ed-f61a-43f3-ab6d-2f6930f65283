package com.accesscorporate.app.wms.server.web.controller.rule;


import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationDRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulAllocationDResponse;
import com.accesscorporate.app.wms.server.biz.service.rule.IRulAllocationDService;
import com.idanchuang.component.base.JsonResult;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@RestController
@RequestMapping("/rule/allocation/detail")
@RequiredArgsConstructor
public class RulAllocationDController {

    private final IRulAllocationDService rulAllocationDService;

    @GetMapping("/queryByHId")
    public JsonResult<List<RulAllocationDResponse>> list(@RequestParam("hId") @NotNull Long hId) {
        return JsonResult.success(rulAllocationDService.queryByHId(hId));
    }

    @PostMapping("/save")
    public JsonResult<Boolean> save(@RequestBody RulAllocationDRequest request) {
        return JsonResult.success(rulAllocationDService.saveRulAllocationD(request));
    }

}
