package com.accesscorporate.app.wms.server.web.controller;

import com.accesscorporate.app.wms.server.biz.params.response.TenantInfoResponse;
import com.accesscorporate.app.wms.server.biz.params.response.WarehouseBaseInfoResponse;
import com.accesscorporate.app.wms.server.biz.service.IAuthPermissionService;
import com.idanchuang.component.base.JsonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 认证鉴权 WMS 接口
 *
 * <AUTHOR>
 * 2025/2/8  11:21
 */
@RestController
@RequestMapping("/authPermission")
@RequiredArgsConstructor
@Tag(name = "认证鉴权 WMS 接口")
public class AuthPermissionController {


    private final IAuthPermissionService iAuthPermissionService;


    @GetMapping("/userTenantList")
    @Operation(summary = "获取用户关联租户列表", description = "获取用户关联租户列表")
    public JsonResult<List<TenantInfoResponse>> queryTenantList() {
        return JsonResult.success(iAuthPermissionService.queryUserTenantInfo());
    }


    @GetMapping("/userWarehouseInfo")
    @Operation(summary = "获取当前用户授权绑定的仓基础信息", description = "获取当前用户授权绑定的仓信息列表「SSO数据权限」")
    public JsonResult<List<WarehouseBaseInfoResponse>> queryUserWarehouseInfo() {
        return JsonResult.success(iAuthPermissionService.queryUserBindWhBaseInfo());
    }


    @GetMapping("/queryAllWarehouseList")
    @Operation(summary = "获取WMS ALL仓基础信息", description = "获取WMS全仓基础信息")
    public JsonResult<List<WarehouseBaseInfoResponse>> queryAllWarehouseList() {
        return JsonResult.success(iAuthPermissionService.queryAllWhBaseInfo());
    }


}
