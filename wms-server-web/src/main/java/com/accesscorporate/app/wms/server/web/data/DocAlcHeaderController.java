package com.accesscorporate.app.wms.server.web.data;

import com.accesscorporate.app.wms.server.biz.params.request.DocAlcHeaderSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocAlcHeaderResponse;
import com.accesscorporate.app.wms.server.biz.service.DocAlcHeaderService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/doc-alc-headers")
public class DocAlcHeaderController {

  @Autowired
  private DocAlcHeaderService docAlcHeaderService;

  @GetMapping
  public JsonResult<List<DocAlcHeaderResponse>> getAllDocAlcHeaders() {
    return JsonResult.success(docAlcHeaderService.listAll());
  }

  @GetMapping("/{id}")
  public JsonResult<DocAlcHeaderResponse> getDocAlcHeaderById(@PathVariable Long id) {
    return JsonResult.success(docAlcHeaderService.get(id));
  }

  @PostMapping("/save")
  public JsonResult<Boolean> saveDocAlcHeader(@RequestBody DocAlcHeaderSaveRequest docAlcHeader) {
    return JsonResult.success(docAlcHeaderService.save(docAlcHeader));
  }

  @DeleteMapping("/{id}")
  public JsonResult<Boolean> deleteDocAlcHeader(@PathVariable Long id) {
    return JsonResult.success(docAlcHeaderService.removeById(id));
  }

  @GetMapping("/page")
  public JsonResult<PageData<DocAlcHeaderResponse>> pageDocAlcHeaders(
      @RequestParam(defaultValue = "1") Integer current,
      @RequestParam(defaultValue = "10") Integer size,
      @RequestParam(required = false) String doNo,
      @RequestParam(required = false) String status,
      @RequestParam(required = false) String doType,
      @RequestParam(required = false) String consigneeName) {
    Page<DocAlcHeaderResponse> page = docAlcHeaderService.page(current, size, doNo, status, doType, consigneeName);
    return JsonResult.success(PageData.of(page.getRecords(), page.getSize(), page.getTotal()));
  }

  @GetMapping("/by-do-no/{doNo}")
  public JsonResult<DocAlcHeaderResponse> getByDoNo(@PathVariable String doNo) {
    return JsonResult.success(docAlcHeaderService.get(docAlcHeaderService.queryByDoNo(doNo).getId()));
  }
}
