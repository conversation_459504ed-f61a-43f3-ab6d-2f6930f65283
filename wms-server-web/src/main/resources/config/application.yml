# 该配置项由upgrade17.sh脚本所添加，目的是为了解决循环依赖的问题
spring.main.allow-circular-references: true
# appId
spring:
  application:
    name: wms-server
  main:
    allow-bean-definition-overriding: true

# apollo appId
app.id: ${spring.application.name}

apollo:
  # dev (部署时不需要修改此配置来切换环境, 发布平台会统一通过启动参数来覆盖此配置)
  meta: http://apollo-configservice-test.idanchuang.vpc
  bootstrap:
    enabled: true
    namespaces: application,springcloud,sentinel_dashboard,error_code,ribbon_resolve,xxl_job
