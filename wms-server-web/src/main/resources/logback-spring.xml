<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="10 seconds">

    <!-- 应用名称 -->
    <property name="APP_NAME" value="wms-server" />
    <!--通用变量-日志目录，请勿修改-->
    <property name="LOG_HOME" value="${ACG_LOG_RT:-${HOME:-/data}}/logs/${ACG_APP_NAME:-${APP_NAME}}"/>
    <property name="DEBUG_LOG_FILE_NAME" value="debug"/>
    <property name="INFO_LOG_FILE_NAME" value="info"/>
    <property name="ERROR_LOG_FILE_NAME" value="error"/>
    <property name="WARN_LOG_FILE_NAME" value="warn"/>
    <!--文件日志格式-->
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss} [ %level ] [ %appid ] [ %version ] [ %group ] [ %host:%port ] [ %tid ] [ %thread ] [ %F:%L ] [ %method ] [ %url ] [ %duration ] [ %keyword ] - %msg%n"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.idanchuang.component.logback.CustomPatternLogbackLayout">
                <pattern>${LOG_PATTERN}</pattern>
            </layout>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="com.idanchuang.component.logback.filter.ConsoleLogFilter"/>
        <filter class="com.idanchuang.component.logback.filter.StandardLogFilter"/>
    </appender>

    <appender name="DEBUG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${DEBUG_LOG_FILE_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${DEBUG_LOG_FILE_NAME}.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
            <maxFileSize>128MB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>16GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.idanchuang.component.logback.CustomPatternLogbackLayout">
                <pattern>${LOG_PATTERN}</pattern>
            </layout>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="com.idanchuang.component.logback.filter.StandardLogFilter"/>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${INFO_LOG_FILE_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${INFO_LOG_FILE_NAME}.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
            <maxFileSize>128MB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>16GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.idanchuang.component.logback.CustomPatternLogbackLayout">
                <pattern>${LOG_PATTERN}</pattern>
            </layout>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="com.idanchuang.component.logback.filter.StandardLogFilter"/>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${ERROR_LOG_FILE_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${ERROR_LOG_FILE_NAME}.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
            <maxFileSize>128MB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>16GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.idanchuang.component.logback.CustomPatternLogbackLayout">
                <pattern>${LOG_PATTERN}</pattern>
            </layout>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="com.idanchuang.component.logback.filter.StandardLogFilter"/>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="WARN_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${WARN_LOG_FILE_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${WARN_LOG_FILE_NAME}.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
            <maxFileSize>128MB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>16GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.idanchuang.component.logback.CustomPatternLogbackLayout">
                <pattern>${LOG_PATTERN}</pattern>
            </layout>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="com.idanchuang.component.logback.filter.StandardLogFilter"/>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 对接Sentry, 将ERROR日志上报 -->
    <appender name="SENTRY" class="io.sentry.logback.SentryAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <filter class="com.idanchuang.component.logback.filter.StandardLogFilter"/>
        <filter class="com.idanchuang.component.logback.filter.SentryLogFilter"/>
    </appender>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="DEBUG_FILE"/>
        <appender-ref ref="INFO_FILE"/>
        <appender-ref ref="ERROR_FILE"/>
        <appender-ref ref="WARN_FILE"/>
        <appender-ref ref="SENTRY"/>
    </root>

</configuration>
