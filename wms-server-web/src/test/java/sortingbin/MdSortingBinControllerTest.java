package sortingbin;

import com.accesscorporate.app.wms.server.biz.service.MdSortingBinService;
import com.accesscorporate.app.wms.server.biz.service.dto.MdSortingBinDTO;
import com.accesscorporate.app.wms.server.web.Application;
import com.accesscorporate.app.wms.server.web.controller.MdSortingBinController;
import com.idanchuang.component.base.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

@SpringBootTest(classes = Application.class)
@WebMvcTest(MdSortingBinController.class)
@Slf4j
class MdSortingBinControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private MdSortingBinService mdSortingBinService;

    @Test
    public void testDeleteMdSortingBin() throws Exception {
        // 模拟 ID
        Long id = 1L;

        // 发送 DELETE 请求进行测试
        mockMvc.perform(MockMvcRequestBuilders.delete("/mdSortingBin/{id}", id)
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().isOk());
    }
}

