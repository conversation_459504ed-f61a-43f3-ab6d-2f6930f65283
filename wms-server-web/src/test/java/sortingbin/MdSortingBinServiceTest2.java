package sortingbin;

import com.accesscorporate.app.wms.server.biz.service.MdSortingBinService;
import com.accesscorporate.app.wms.server.biz.service.dto.MdSortingBinDTO;
import com.accesscorporate.app.wms.server.web.Application;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = Application.class)
class MdSortingBinServiceTest {

    @Autowired
    private MdSortingBinService mdSortingBinService;

    @Test
    public void testDeleteByPrimaryKey() {
        // 假设我们有一个已知的主键ID
        Long id = 1L;

        // 首先插入一条记录，以便我们可以删除它
        MdSortingBinDTO mdSortingBinDO = new MdSortingBinDTO();
        mdSortingBinDO.setId(id);
        mdSortingBinDO.setSortingZoneName("Some Value"); // 设置其他必要的字段
        mdSortingBinService.add(mdSortingBinDO);

        // 调用deleteByPrimaryKey方法删除记录
        int result = mdSortingBinService.deleteByPrimaryKey(id);

        // 断言删除操作成功
        assertEquals(1, result);

        // 尝试再次查询该记录，应该返回null
        MdSortingBinDTO deletedMdSortingBinDO = mdSortingBinService.selectById(id);
        assertNull(deletedMdSortingBinDO);
    }
}