package com.idanchuang.wms.cfg;

import com.accesscorporate.app.wms.server.biz.manager.CfgCodeDetailCacheService;
import com.accesscorporate.app.wms.server.biz.service.dto.SelectItem;
import com.idanchuang.component.core.json.Jackson;
import com.idanchuang.wms.BaseTest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/8 上午10:57 星期六
 */
@Slf4j
public class CfgCodeTest extends BaseTest {
    @Resource
    private CfgCodeDetailCacheService cfgCodeDetailCacheService;

    @Test
    public void test() {
        Map<String, String> wmsStockType = cfgCodeDetailCacheService.getDictionary("CYCLE_CLASS");
        log.info("wmsStockType:{}", wmsStockType);
        List<SelectItem> cycleClass = cfgCodeDetailCacheService.getSelectItems("CYCLE_CLASS");
        log.info("wmsStockType1:{}", Jackson.toJsonString(cycleClass));
    }
}
