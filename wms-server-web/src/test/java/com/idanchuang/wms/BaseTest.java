package com.idanchuang.wms;

import com.accesscorporate.app.wms.server.biz.manager.CfgCodeDetailCacheService;
import com.accesscorporate.app.wms.server.web.Application;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/8 上午10:28 星期六
 */
@SpringBootTest(classes = {Application.class})
// 指定启动类
@Slf4j
@RunWith(SpringRunner.class)
@ContextConfiguration
public class BaseTest {

}
