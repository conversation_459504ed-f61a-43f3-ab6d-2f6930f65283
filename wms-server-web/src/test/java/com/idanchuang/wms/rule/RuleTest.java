package com.idanchuang.wms.rule;


import com.accesscorporate.app.wms.server.biz.params.request.CfgCodeDetailPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationDRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulAllocationRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayDetailQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayDetailRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayHQueryPageRequest;
import com.accesscorporate.app.wms.server.biz.params.request.rule.RulPutawayHRequest;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulAllocationDResponse;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulAllocationResponse;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulPutawayDetailResponse;
import com.accesscorporate.app.wms.server.biz.params.response.rule.RulPutawayHResponse;
import com.accesscorporate.app.wms.server.web.controller.CfgCodeController;
import com.accesscorporate.app.wms.server.web.controller.rule.RulAllocationController;
import com.accesscorporate.app.wms.server.web.controller.rule.RulAllocationDController;
import com.accesscorporate.app.wms.server.web.controller.rule.RulPutawayDetailController;
import com.accesscorporate.app.wms.server.web.controller.rule.RulPutawayHController;
import com.idanchuang.component.base.JsonResult;
import com.idanchuang.component.base.page.PageData;
import com.idanchuang.wms.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @author: gaohao
 * @date: 2025-02-24 18:03
 * @desc:
 */

@Slf4j
public class RuleTest extends BaseTest {

    @Autowired
    RulPutawayHController rulPutawayHController;

    @Autowired
    RulPutawayDetailController rulPutawayDetailController;

    @Autowired
    RulAllocationController rulAllocationController;

    @Autowired
    CfgCodeController cfgCodeController;

    @Autowired
    RulAllocationDController rulAllocationDController;

    @Test
    public void rulPutawayHControllerList() {
        JsonResult<PageData<RulPutawayHResponse>> list = rulPutawayHController.list(new RulPutawayHQueryPageRequest());
        System.out.println(list);
    }

    @Test
    public void rulPutawayHControllerQueryById() {
        JsonResult<RulPutawayHResponse> rulPutawayHResponseJsonResult = rulPutawayHController.queryById(4L);
        System.out.println(rulPutawayHResponseJsonResult);
    }

    @Test
    public void rulPutawayHControllerSave() {
        RulPutawayHRequest request = new RulPutawayHRequest();
        request.setPutawayCode("AAAA");
        request.setPutawayName("测试保存AAAA");
        request.setActiveFlag("1");
        rulPutawayHController.save(request);
    }

    @Test
    public void rulPutawayHControllerUpdate() {
        RulPutawayHRequest updateRequest = new RulPutawayHRequest();
        updateRequest.setId(4L);
        updateRequest.setPutawayName("测试修改BBBB");
        updateRequest.setPutawayCode("BBBB");
    }

    @Test
    public void rulPutawayDetailControllerList() {
        RulPutawayDetailQueryPageRequest request = new RulPutawayDetailQueryPageRequest();
        request.setRulHeaderId(14L);
        JsonResult<PageData<RulPutawayDetailResponse>> list = rulPutawayDetailController.list(request);
        System.out.println(list);
    }

    @Test
    public void rulPutawayDetailControllerQueryById() {
        JsonResult<RulPutawayDetailResponse> rulPutawayDetailResponseJsonResult = rulPutawayDetailController.queryById(4L);
        System.out.println(rulPutawayDetailResponseJsonResult);
    }

    @Test
    public void rulPutawayDetailControllerUpdate() {
        JsonResult<RulPutawayDetailResponse> rulPutawayDetailResponseJsonResult = rulPutawayDetailController.queryById(4L);
        RulPutawayDetailResponse data = rulPutawayDetailResponseJsonResult.getData();
        RulPutawayDetailRequest request = new RulPutawayDetailRequest();
        BeanUtils.copyProperties(data, request);
        request.setLocTypes("EA,RS");
    }


    @Test
    public void rulPutawayDetailControllerCreate() {
        JsonResult<RulPutawayDetailResponse> rulPutawayDetailResponseJsonResult = rulPutawayDetailController.queryById(4L);
        RulPutawayDetailResponse data = rulPutawayDetailResponseJsonResult.getData();
        RulPutawayDetailRequest request = new RulPutawayDetailRequest();
        BeanUtils.copyProperties(data, request);
        request.setLocTypes("EA");
        rulPutawayDetailController.save(request);
    }

    /**
     * 获取上架规则下拉框
     */
    @Test
    public void CfgCodeControllerList() {
        CfgCodeDetailPageRequest request = new CfgCodeDetailPageRequest();
        request.setMasterCode("DIRECTION_RULE");
//        JsonResult<PageData<CfgCodeDetailResponse>> pageDataJsonResult = cfgCodeController.pageList(request);
//        System.out.println(pageDataJsonResult);
    }

    @Test
    public void rulAllocationControllerList() {
        RulAllocationQueryPageRequest request = new RulAllocationQueryPageRequest();
        JsonResult<PageData<RulAllocationResponse>> list = rulAllocationController.list(request);
        System.out.println("list方法" + list);
        RulAllocationResponse rulAllocationResponse = list.getData().getRecords().get(0);
        JsonResult<RulAllocationResponse> rulAllocationResponseJsonResult = rulAllocationController.queryById(rulAllocationResponse.getId());
        System.out.println("queryById方法" + rulAllocationResponseJsonResult);
        RulAllocationRequest request1 = new RulAllocationRequest();
        BeanUtils.copyProperties(rulAllocationResponse, request1);
        request1.setAllocationDescr(request1.getAllocationDescr() + "测试2025");

    }

    @Test
    public void rulAllocationDController() {

        JsonResult<List<RulAllocationDResponse>> list = rulAllocationDController.list(37L);
        System.out.println(list);
        RulAllocationDResponse rulAllocationDResponse = list.getData().get(0);
        RulAllocationDRequest rulAllocationDRequest = new RulAllocationDRequest();
        BeanUtils.copyProperties(rulAllocationDResponse,rulAllocationDRequest);
        rulAllocationDRequest.setLotattname(rulAllocationDRequest.getLotattname()+"1");
        System.out.println("更新代码执行结果="+rulAllocationDController.save(rulAllocationDRequest));

    }


}
