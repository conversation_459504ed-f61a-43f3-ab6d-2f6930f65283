package com.accesscorporate.app.wms.server.biz;

import com.accesscorporate.app.wms.server.BaseTest;
import com.accesscorporate.app.wms.server.biz.params.response.TenantInfoResponse;
import com.accesscorporate.app.wms.server.biz.params.response.WarehouseBaseInfoResponse;
import com.accesscorporate.app.wms.server.biz.service.IAuthPermissionService;
import com.accesscorporate.app.wms.server.common.context.UserContext;
import com.accesscorporate.app.wms.server.integration.dto.UserDetailResponse;
import com.accesscorporate.app.wms.server.integration.wrap.IUserInfoWrap;
import com.alibaba.fastjson.JSON;
import com.idanchuang.component.core.json.Jackson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.List;

/**
 * 认证&权限模块 测试类
 *
 * <AUTHOR>
 * 2025/2/13  10:21
 */
@Slf4j
public class AuthPermissionServiceTest extends BaseTest {

    @Resource
    private IAuthPermissionService authPermissionService;

    @Resource
    private IUserInfoWrap userInfoWrap;

    @Test
    public void queryUserTenantInfo() {
        UserContext.setUserId(10000170L);
        List<TenantInfoResponse> tenantInfoResponses = authPermissionService.queryUserTenantInfo();
        log.info("tenantInfoResponses:::::{}", Jackson.toJsonString(tenantInfoResponses));
        List<WarehouseBaseInfoResponse> warehouseBaseInfoResponses = authPermissionService.queryUserBindWhBaseInfo();
        log.info("warehouseBaseInfoResponses:::::{}", Jackson.toJsonString(warehouseBaseInfoResponses));
        UserContext.removeAll();
    }

    @Test
    public void testQueryUserInfoByToken() {
        String token="eyJhbGciOiJIUzI1NiJ9.eyJyZWFsTmFtZSI6IuWxoOWkqeWdpCIsInN1YiI6InRrIiwiZW52aXJvbm1lbnQiOiJ0ZXN0Iiwibmlja25hbWUiOiIiLCJpZCI6MTAwMDAwMzQsImV4cCI6MTc1MDI3NjYyNiwiaWF0IjoxNzUwMjQwNjI2LCJhY2NvdW50IjoidGsiLCJqdGkiOiIxNzUwMjQwNjI2NTgyIiwidGltZXN0YW1wIjoxNzUwMjQwNjI2fQ.1uCYouvHxNAemV8iWOhzc78-SPOIjTkSvnQoxfHe0qc";
        UserDetailResponse userInfoByToken = userInfoWrap.getUserInfoByToken(token);
        System.out.println(JSON.toJSON(userInfoByToken));
    }




}
