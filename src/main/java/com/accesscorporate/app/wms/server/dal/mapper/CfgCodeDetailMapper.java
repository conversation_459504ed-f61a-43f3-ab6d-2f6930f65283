package com.accesscorporate.app.wms.server.dal.mapper;

import com.accesscorporate.app.wms.server.dal.entity.CfgCodeDetailDO;

public interface CfgCodeDetailMapper {
    int deleteByPrimaryKey(String uuid);

    int insert(CfgCodeDetailDO record);

    int insertSelective(CfgCodeDetailDO record);

    CfgCodeDetailDO selectByPrimaryKey(String uuid);

    int updateByPrimaryKeySelective(CfgCodeDetailDO record);

    int updateByPrimaryKey(CfgCodeDetailDO record);
}