<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.accesscorporate.app.wms.server.dal.mapper.CfgCodeDetailCfgCodeDetailMapper">
  <resultMap id="BaseResultMap" type="com.accesscorporate.app.wms.server.dal.entity.CfgCodeDetailDO">
    <id column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="master_code" jdbcType="VARCHAR" property="masterCode" />
    <result column="code_name_zh" jdbcType="VARCHAR" property="codeNameZh" />
    <result column="code_name_en" jdbcType="VARCHAR" property="codeNameEn" />
    <result column="code_value" jdbcType="VARCHAR" property="codeValue" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="extend_col" jdbcType="VARCHAR" property="extendCol" />
    <result column="message_key" jdbcType="VARCHAR" property="messageKey" />
    <result column="seq_num" jdbcType="INTEGER" property="seqNum" />
    <result column="status" jdbcType="VARCHAR" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    uuid, master_code, code_name_zh, code_name_en, code_value, memo, extend_col, message_key, 
    seq_num, `status`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cfg_code_detail
    where uuid = #{uuid,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from cfg_code_detail
    where uuid = #{uuid,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" keyColumn="uuid" keyProperty="uuid" parameterType="com.accesscorporate.app.wms.server.dal.entity.CfgCodeDetailDO" useGeneratedKeys="true">
    insert into cfg_code_detail (master_code, code_name_zh, code_name_en, 
      code_value, memo, extend_col, 
      message_key, seq_num, `status`
      )
    values (#{masterCode,jdbcType=VARCHAR}, #{codeNameZh,jdbcType=VARCHAR}, #{codeNameEn,jdbcType=VARCHAR}, 
      #{codeValue,jdbcType=VARCHAR}, #{memo,jdbcType=VARCHAR}, #{extendCol,jdbcType=VARCHAR}, 
      #{messageKey,jdbcType=VARCHAR}, #{seqNum,jdbcType=INTEGER}, #{status,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="uuid" keyProperty="uuid" parameterType="com.accesscorporate.app.wms.server.dal.entity.CfgCodeDetailDO" useGeneratedKeys="true">
    insert into cfg_code_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="masterCode != null">
        master_code,
      </if>
      <if test="codeNameZh != null">
        code_name_zh,
      </if>
      <if test="codeNameEn != null">
        code_name_en,
      </if>
      <if test="codeValue != null">
        code_value,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="extendCol != null">
        extend_col,
      </if>
      <if test="messageKey != null">
        message_key,
      </if>
      <if test="seqNum != null">
        seq_num,
      </if>
      <if test="status != null">
        `status`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="masterCode != null">
        #{masterCode,jdbcType=VARCHAR},
      </if>
      <if test="codeNameZh != null">
        #{codeNameZh,jdbcType=VARCHAR},
      </if>
      <if test="codeNameEn != null">
        #{codeNameEn,jdbcType=VARCHAR},
      </if>
      <if test="codeValue != null">
        #{codeValue,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="extendCol != null">
        #{extendCol,jdbcType=VARCHAR},
      </if>
      <if test="messageKey != null">
        #{messageKey,jdbcType=VARCHAR},
      </if>
      <if test="seqNum != null">
        #{seqNum,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.accesscorporate.app.wms.server.dal.entity.CfgCodeDetailDO">
    update cfg_code_detail
    <set>
      <if test="masterCode != null">
        master_code = #{masterCode,jdbcType=VARCHAR},
      </if>
      <if test="codeNameZh != null">
        code_name_zh = #{codeNameZh,jdbcType=VARCHAR},
      </if>
      <if test="codeNameEn != null">
        code_name_en = #{codeNameEn,jdbcType=VARCHAR},
      </if>
      <if test="codeValue != null">
        code_value = #{codeValue,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="extendCol != null">
        extend_col = #{extendCol,jdbcType=VARCHAR},
      </if>
      <if test="messageKey != null">
        message_key = #{messageKey,jdbcType=VARCHAR},
      </if>
      <if test="seqNum != null">
        seq_num = #{seqNum,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where uuid = #{uuid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.accesscorporate.app.wms.server.dal.entity.CfgCodeDetailDO">
    update cfg_code_detail
    set master_code = #{masterCode,jdbcType=VARCHAR},
      code_name_zh = #{codeNameZh,jdbcType=VARCHAR},
      code_name_en = #{codeNameEn,jdbcType=VARCHAR},
      code_value = #{codeValue,jdbcType=VARCHAR},
      memo = #{memo,jdbcType=VARCHAR},
      extend_col = #{extendCol,jdbcType=VARCHAR},
      message_key = #{messageKey,jdbcType=VARCHAR},
      seq_num = #{seqNum,jdbcType=INTEGER},
      `status` = #{status,jdbcType=VARCHAR}
    where uuid = #{uuid,jdbcType=VARCHAR}
  </update>
</mapper>